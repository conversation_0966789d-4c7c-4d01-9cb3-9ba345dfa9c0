#pragma once
#if _WINDOWS
#    pragma warning(disable : 4251)
#    pragma warning(disable : 4275)
#    ifdef BudgetStreaming_EXPORTS
#        define BudgetStreaming_API __declspec(dllexport)
#    else
#        define BudgetStreaming_API __declspec(dllimport)
#    endif
#else
#    ifdef __GNUC__
#        define BudgetStreaming_API __attribute__((visibility("default")))
#    else
#        define BudgetStreaming
#    endif
#endif
#include "core/modules/imodule.h"
namespace cross
{
static const char kModuleBugdetStreaming[] = "BudgetStreaming";
class BudgetStreamingModule : public gbf::IModule
{
public:
    BudgetStreamingModule();
    gbf::ModuleCallReturnStatus Init() override;
    gbf::ModuleCallReturnStatus Start() override;
    gbf::ModuleCallReturnStatus Update() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Stop() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Release() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    void Free() override;
};
}   // namespace cross
