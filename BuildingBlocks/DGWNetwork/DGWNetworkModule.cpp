#include "DGWNetworkModule.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
#pragma warning(push)
#pragma warning(disable : 4100)
void DGWNetworkRegister();
void SystemRegisterR();
void SystemRegisterG();

namespace cross::scripts {
void CodeGenRegisterGeneratedClass();
}
namespace cross {
DGWNetworkModule::DGWNetworkModule()
{
    DGWNetworkRegister();
    SystemRegisterG();
    SystemRegisterR();
    scripts::CodeGenRegisterGeneratedClass();
}
}   // namespace cross
#pragma warning(pop)
MAKE_MODULE(cross::DGWNetworkModule, DGWNetwork)