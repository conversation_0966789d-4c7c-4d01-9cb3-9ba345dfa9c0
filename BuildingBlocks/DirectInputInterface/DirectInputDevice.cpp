#include "DirectInputDevice.h"

namespace cross
{
static const std::unordered_map<int, std::string> POVButtonNamePrefixByIndex = {
    {0, "Joystick_POV_Up_"},
    {1, "Joystick_POV_UpRight_"},
    {2, "Joystick_POV_Right_"},
    {3, "Joystick_POV_DownRight_"},
    {4, "Joystick_POV_Down_"},
    {5, "Joystick_POV_DownLeft_"},
    {6, "Joystick_POV_Left_"},
    {7, "Joystick_POV_UpLeft_"},
};

BOOL CALLBACK EnumObjectsCallback(const DIDEVICEOBJECTINSTANCE* pdidoi, VOID* pContext) noexcept;

DirectInputDevice::DirectInputDevice(IApplicationMessageHandler* InMessageHandler, LPDIRECTINPUTDEVICE8 inDevice, const std::string& inName, const std::string& inGuid)
    : MessageHandler(InMessageHandler)
    , device(inDevice)
    , mName(inName)
    , mGuid(inGuid)
    , IInputDevice()
{
    mDeviceInfo.DeviceID = IInputDevice::GenerateDeviceID();
    mDeviceInfo.InterfaceName = "DirectInput";
    mDeviceInfo.DeviceName = mName;
    mDeviceInfo.DeviceType = cross::input::InputDeviceType::Gamepad;
    mDeviceInfo.SerialNumber = mGuid;

    // InitDirectInput();
    memset(buttonState, 0, sizeof(buttonState));
    memset(povState, 0, sizeof(povState));
    InitialButtonRepeatDelay = 0.008f;   // Set polling / report rate to 125Hz, same as XBOX controller
    ButtonRepeatDelay = 0.008f;          // Set polling / report rate to 125Hz, same as XBOX controller
    buttonNum = 0;
    povNum = 0;
    axisNum = 0;
    lastPressedPovButtonIndex = -1;

    device->EnumObjects(EnumObjectsCallback, this, DIDFT_ALL);

    stateInfo["name"] = mName;
    stateInfo["guid"] = mGuid;
    stateInfo["axisCount"] = axisNum;
    stateInfo["buttonCount"] = buttonNum;
    stateInfo["povCount"] = povNum;
}

void DirectInputDevice::Tick(float DeltaTime)
{
    stateInfo.RemoveMember("buttonState");
    stateInfo.RemoveMember("axisValues");
    stateInfo.RemoveMember("povValues");
    stateInfo.RemoveMember("povState");

    HRESULT hr = device->Poll();
    if (FAILED(hr))
    {
        hr = device->Acquire();
        while (hr == DIERR_INPUTLOST)
            hr = device->Acquire();

        return;
    }

    DIJOYSTATE2 js;
    hr = device->GetDeviceState(sizeof(DIJOYSTATE2), &js);

    MessageHandler->OnControllerAnalog(("Joystick_X_" + mName).data(), AxisValToNormal(js.lX));
    MessageHandler->OnControllerAnalog(("Joystick_Y_" + mName).data(), AxisValToNormal(js.lY));
    MessageHandler->OnControllerAnalog(("Joystick_Z_" + mName).data(), AxisValToNormal(js.lZ));
    MessageHandler->OnControllerAnalog(("Joystick_RX_" + mName).data(), AxisValToNormal(js.lRx));
    MessageHandler->OnControllerAnalog(("Joystick_RY_" + mName).data(), AxisValToNormal(js.lRy));
    MessageHandler->OnControllerAnalog(("Joystick_RZ_" + mName).data(), AxisValToNormal(js.lRz));
    MessageHandler->OnControllerAnalog(("Joystick_Slider_1_" + mName).data(), AxisValToNormal(js.rglSlider[0]));
    MessageHandler->OnControllerAnalog(("Joystick_Slider_2_" + mName).data(), AxisValToNormal(js.rglSlider[1]));

    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lX));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lY));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lZ));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lRx));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lRy));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.lRz));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.rglSlider[0]));
    stateInfo["axisValues"].PushBack(AxisValToNormal(js.rglSlider[1]));

    const double currentTime = std::chrono::duration<double>(std::chrono::high_resolution_clock::now().time_since_epoch()).count();

    // Ref: [https://learn.microsoft.com/en-us/previous-versions/windows/desktop/ee418260(v=vs.85)]
    // Treat POV as buttons here, to align with console D-Pad
    if (js.rgdwPOV[0] != 0xFFFFFFFF)
    {
        int pressedIndex = js.rgdwPOV[0] / DI_DEGREES / 45;
        // NOTE: In some situation (disconnected?) index may be an invalid number and causes array out of bound
        if (pressedIndex >= 0 && pressedIndex < MAX_NUM_JOYSTICK_POV)
        {
            // LOG_DEBUG("POV {} -> {}", lastPressedPovButtonIndex, pressedIndex);
            if (lastPressedPovButtonIndex != pressedIndex)
            {
                if (lastPressedPovButtonIndex != -1)
                {
                    // LOG_DEBUG("POV {} released", lastPressedPovButtonIndex);
                    povState[lastPressedPovButtonIndex] = 0;
                    MessageHandler->OnControllerButtonReleased((POVButtonNamePrefixByIndex.at(lastPressedPovButtonIndex) + mName).data(), false);
                }
                lastPressedPovButtonIndex = pressedIndex;

                // LOG_DEBUG("POV {} pressed", pressedIndex);
                povState[pressedIndex] = 1;
                MessageHandler->OnControllerButtonPressed((POVButtonNamePrefixByIndex.at(pressedIndex) + mName).data(), false);
                nextPovRepeatTime[pressedIndex] = currentTime + InitialButtonRepeatDelay;
            }
            else if (nextPovRepeatTime[pressedIndex] <= currentTime)
            {
                // LOG_DEBUG("POV {} pressed", pressedIndex);
                MessageHandler->OnControllerButtonPressed((POVButtonNamePrefixByIndex.at(pressedIndex) + mName).data(), true);
                nextPovRepeatTime[pressedIndex] = currentTime + ButtonRepeatDelay;
            }
        }
    }
    else if (lastPressedPovButtonIndex != -1 && nextPovRepeatTime[lastPressedPovButtonIndex] <= currentTime)
    {
        // LOG_DEBUG("POV {} -> -1", lastPressedPovButtonIndex);
        // LOG_DEBUG("POV {} released", lastPressedPovButtonIndex);
        povState[lastPressedPovButtonIndex] = 0;
        MessageHandler->OnControllerButtonReleased((POVButtonNamePrefixByIndex.at(lastPressedPovButtonIndex) + mName).data(), false);
        lastPressedPovButtonIndex = -1;
    }

    stateInfo["povValues"].PushBack(static_cast<int>(js.rgdwPOV[0]));
    stateInfo["povValues"].PushBack(static_cast<int>(js.rgdwPOV[1]));
    stateInfo["povValues"].PushBack(static_cast<int>(js.rgdwPOV[2]));
    stateInfo["povValues"].PushBack(static_cast<int>(js.rgdwPOV[3]));

    for (int index = 0; index < MAX_NUM_JOYSTICK_POV; index++)
    {
        stateInfo["povState"].PushBack(povState[index]);
    }

    for (int i = 0; i < buttonNum; i++)
    {
        // Button name index starts with 1 to match Windows joy.cpl settings
        std::string buttonName = "Joystick_Button_" + std::to_string(i + 1) + "_" + mName;
        if (js.rgbButtons[i] != buttonState[i])
        {
            if (js.rgbButtons[i] & 0x80)
            {
                MessageHandler->OnControllerButtonPressed(buttonName.data(), false);
                nextButtonRepeatTime[i] = currentTime + InitialButtonRepeatDelay;
            }
            else
            {
                MessageHandler->OnControllerButtonReleased(buttonName.data(), false);
            }
        }
        else if (js.rgbButtons[i] & 0x80 && nextButtonRepeatTime[i] <= currentTime)
        {
            MessageHandler->OnControllerButtonPressed(buttonName.data(), true);

            nextButtonRepeatTime[i] = currentTime + ButtonRepeatDelay;
        }

        if (buttonState[i])
        {
            stateInfo["buttonState"].PushBack(1);
        }
        else
        {
            stateInfo["buttonState"].PushBack(0);
        }

        buttonState[i] = js.rgbButtons[i];
    }
}

BOOL CALLBACK EnumObjectsCallback(const DIDEVICEOBJECTINSTANCE* pdidoi, VOID* pContext) noexcept
{
    DirectInputDevice* mDevice = reinterpret_cast<DirectInputDevice*>(pContext);

    /*Button Info*/
    if (pdidoi->guidType == GUID_Button)
    {
        mDevice->buttonNum++;
    }
    if (pdidoi->guidType == GUID_POV)
    {
        mDevice->povNum++;
    }

    /*Axes Info*/
    if (pdidoi->guidType == GUID_XAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_YAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_ZAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_RxAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_RyAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_RzAxis)
    {
        mDevice->axisNum++;
    }
    if (pdidoi->guidType == GUID_Slider)
    {
        mDevice->axisNum++;
    }

    return DIENUM_CONTINUE;
}

bool DirectInputDevice::IsGamepadAttached() const
{
    return true;
}

}   // namespace cross

