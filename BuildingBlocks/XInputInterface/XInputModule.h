#pragma once
#if CROSSENGINE_WIN
#    pragma warning(disable : 4251)
#    pragma warning(disable : 4275)
#    ifdef XInputInterface_EXPORTS
#        define XInputInterface_API __declspec(dllexport)
#    else
#        define XInputInterface_API __declspec(dllimport)
#    endif
#else
#    ifdef __GNUC__
#        define XInputInterface_API __attribute__((visibility("default")))
#    else
#        define XInputInterface_API
#    endif
#endif
#include "core/modules/imodule.h"
#include "XInputDevice.h"

namespace cross
{

static const char kModuleCEGamePlay[] = "XInput";
class XInputModule : public gbf::IModule
{
public:
    XInputModule();

    // method from IModule
    gbf::ModuleCallReturnStatus Init() override;
    gbf::ModuleCallReturnStatus Start() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Update() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Stop() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Release() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    void Free() override
    {
        delete this;
    }

    // TODO(hendrikwang): Should seperate up to 4 XInput devices?
    std::shared_ptr<XInputDevice> mXInputDevice;
};
    
} // namespace cross
