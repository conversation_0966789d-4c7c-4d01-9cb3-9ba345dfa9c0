#pragma compute DepthValue<PERSON>lear
#pragma compute DepthReduce
#pragma compute CalculateFrustum

#define THREADGROUP_SIZEX 16
#define THREADGROUP_SIZEY 16
#define THREADGROUP_TOTALSIZE (THREADGROUP_SIZEX * THREADGROUP_SIZEY)
#define MAX_UINT 4294967295
#define MIN_UINT 0

Texture2D<float> depthTex : register(space0);
RWBuffer<uint> depthMinMaxBuffer : register(space0);

struct CascadeShadowFrustum 
{
    //float4x4 shadowMatrices[4];
    float4 shadowSplitDatas;
};
RWStructuredBuffer<CascadeShadowFrustum> cascadeShadowFrustumBuffer : register(space0);

cbuffer _cbCommon
{
    float4 _DepthParams;//min max lerp
}

#define _MinShadowDepth _DepthParams.x
#define _MaxShadowDepth _DepthParams.y
#define _LerpFactor     _DepthParams.z

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void DepthValueClear(
	uint3 DispatchThreadID : SV_DISPATCHTHREADID)
{
	if (DispatchThreadID.x == 0 && DispatchThreadID.y ==0)
	{
		depthMinMaxBuffer[0] = MAX_UINT;
		depthMinMaxBuffer[1] = MIN_UINT;
	}
}

// Atomic reduce
groupshared uint IntegerTileMinZ;
groupshared uint IntegerTileMaxZ;

[numthreads(THREADGROUP_SIZEX, THREADGROUP_SIZEY, 1)]
void DepthReduce(
	uint3 DispatchThreadID : SV_DISPATCHTHREADID,
	uint3 GroupThreadID : SV_GROUPTHREADID)
{
	// Compute Min/Max depth value within this thread
    uint ThreadIndex = GroupThreadID.y * THREADGROUP_SIZEX + GroupThreadID.x;
	
    if (ThreadIndex == 0) 
	{
        IntegerTileMinZ = MAX_UINT;     
        IntegerTileMaxZ = MIN_UINT;
    }

    GroupMemoryBarrierWithGroupSync();

	uint2 PixelPos = DispatchThreadID.xy;
	float Depth = depthTex[PixelPos];

	InterlockedMin(IntegerTileMinZ, (uint)(Depth * MAX_UINT));
	InterlockedMax(IntegerTileMaxZ, (uint)(Depth * MAX_UINT));

	GroupMemoryBarrierWithGroupSync();

	if(ThreadIndex == 0)
	{
		InterlockedMin(depthMinMaxBuffer[0], IntegerTileMinZ);
		InterlockedMax(depthMinMaxBuffer[1], IntegerTileMaxZ);
	}
}

[numthreads(1, 1, 1)]
void CalculateFrustum(
	uint3 DispatchThreadID : SV_DISPATCHTHREADID,
	uint3 GroupThreadID : SV_GROUPTHREADID)
{
	const uint SplitNum = 4;
	const float Lambda = _LerpFactor;

	// // Get Min/ Max value
	// float MinDepthZ = (float)(depthMinMaxBuffer[0]);//far
	// float MaxDepthZ = (float)(depthMinMaxBuffer[1]);//near
	// float maxDis = _MaxShadowDepth * MAX_UINT;
	// float tempMinDis = _MinShadowDepth * MAX_UINT;
	
	// MinDepthZ = MinDepthZ < tempMinDis ? tempMinDis : MinDepthZ;
	// MaxDepthZ = MaxDepthZ > maxDis ? maxDis : MaxDepthZ;

	// float DepthRange_uni = (MaxDepthZ - MinDepthZ) / float(SplitNum);
	// float DepthRange_log = pow(MaxDepthZ / MinDepthZ, 1.0 / float(SplitNum));

	// // Calculate frustum by log partition
	// for(int i = 1; i < SplitNum; i++)
	// {
	// 	// Calc split
	// 	float C_uni = MinDepthZ + i * DepthRange_uni;
	// 	float C_log = MinDepthZ * pow(DepthRange_log, i);
		
	// 	cascadeShadowFrustumBuffer[0].shadowSplitDatas[i] = lerp(C_uni, C_log, Lambda);

	// 	// Calc crop matrix

	// }

	// Get Min/ Max value
	float farDepthZ = (float)(MAX_UINT - depthMinMaxBuffer[0]);//far
	float nearDepthZ = (float)(MAX_UINT - depthMinMaxBuffer[1]);//near
	float farDis = (1.0-_MaxShadowDepth) * MAX_UINT;
	float tempNearDis = (1.0-_MinShadowDepth) * MAX_UINT;
	
	nearDepthZ = nearDepthZ > tempNearDis ? nearDepthZ : tempNearDis;
	farDepthZ = farDis < farDepthZ ? farDis : farDepthZ;

    float C_interval =  (farDepthZ - nearDepthZ);
	float DepthRange_uni = C_interval / float(SplitNum);
	float DepthRange_log = farDepthZ / nearDepthZ;

	cascadeShadowFrustumBuffer[0].shadowSplitDatas[0] = (float)(depthMinMaxBuffer[1])/(float)(MAX_UINT);//posV.z;

	// Calculate frustum by log partition
	for(int i = 1; i < SplitNum; i++)
	{
		// Calc split
		float C_uni = nearDepthZ + i * DepthRange_uni;
		float C_log = nearDepthZ * pow(DepthRange_log, i/SplitNum);
		
		cascadeShadowFrustumBuffer[0].shadowSplitDatas[i] = (lerp(C_uni, C_log, Lambda)-nearDepthZ)/C_interval;

		// Calc crop matrix
	}
}