#pragma compute downsample4mip

Texture2D<float4> toDownSample_texture : register(space0);
RWTexture2D<float4> Mip1 : register(space0);
RWTexture2D<float4> Mip2 : register(space0);
RWTexture2D<float4> Mip3 : register(space0);
RWTexture2D<float4> Mip4 : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer _cbCommon
{
    float2 TexSize;
}
float Brightness(float3 color)
{
    return color.x * 0.21 + color.y * 0.02 + color.z * 0.77;
}
float4 Box4(float4 p0, float4 p1, float4 p2, float4 p3)
{
    return (p0 + p1 + p2 + p3) * 0.25f;
}
groupshared float4 g_Tile[64];   // 8x8 input pixels
//8x8 five mips generated
//Generate last 4 mip of bloom postprocess
[numthreads(8, 8, 1)]  
 void downsample4mip(uint2 dispatchThreadID : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex) 
 {
    uint parity = dispatchThreadID.x | dispatchThreadID.y;
    //Mip1
    float2 centerUV = (float2(dispatchThreadID.xy) * 2.0f + 1.0f) * TexSize;
    float4 c0 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(-2, -2));
    float4 c1 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(0, -2));
    float4 c2 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(2, -2));
    float4 c3 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(-1, -1));
    float4 c4 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(1, -1));
    float4 c5 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(-2, 0));
    float4 c6 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0);
    float4 c7 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(2, 0));
    float4 c8 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(-1, 1));
    float4 c9 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(1, 1));
    float4 c10 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(-2, 2));
    float4 c11 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(0, 2));
    float4 c12 = toDownSample_texture.SampleLevel(ce_Sampler_Clamp, centerUV, 0, int2(2, 2));
    float4 avgPixel = Box4(c0, c1, c5, c6) * 0.125f + Box4(c1, c2, c6, c7) * 0.125f + Box4(c5, c6, c10, c11) * 0.125f + Box4(c6, c7, c11, c12) * 0.125f + Box4(c3, c4, c8, c9) * 0.5f;
    g_Tile[groupIndex] = avgPixel;
    Mip1[dispatchThreadID.xy] = avgPixel;

    GroupMemoryBarrierWithGroupSync();
    if ((parity & 1) == 0)
    {
        avgPixel = 0.25f * (avgPixel + g_Tile[groupIndex + 1] + g_Tile[groupIndex + 8] + g_Tile[groupIndex + 9]);
        g_Tile[groupIndex] = avgPixel;
        Mip2[dispatchThreadID.xy >> 1] = avgPixel;
    }

    GroupMemoryBarrierWithGroupSync();

    // Downsample and store the 2x2 block
    if ((parity & 3) == 0)
    {
        avgPixel = 0.25f * (avgPixel + g_Tile[groupIndex + 2] + g_Tile[groupIndex + 16] + g_Tile[groupIndex + 18]);
        g_Tile[groupIndex] = avgPixel;
        Mip3[dispatchThreadID.xy >> 2] = avgPixel;
    }

    GroupMemoryBarrierWithGroupSync();

    // Downsample and store the 1x1 block
    if ((parity & 7) == 0)
    {
        avgPixel = 0.25f * (avgPixel + g_Tile[groupIndex + 4] + g_Tile[groupIndex + 32] + g_Tile[groupIndex + 36]);
        Mip4[dispatchThreadID.xy >> 3] = avgPixel;
    }
 }