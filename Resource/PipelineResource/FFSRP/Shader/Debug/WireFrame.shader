#pragma vertex VSMain
#pragma geometry GeoMain
#pragma pixel PSMain

// #pragma keyword INSTANCING
#pragma keyword CE_USE_DOUBLE_TRANSFORM

#define ADDITIONAL_MTL_PARAM \
float4 WireFrameColor; \

#include "../ShaderLibrary/GlobalModelVariables.hlsl"
// #define VERTEX_TYPE VertexType_Vegetation
#include "../ShaderLibrary/Vertex.hlsl"

struct g2f 
{
	float4 pos : SV_POSITION;
	float3 dist : TEXCOORD1;
};

VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}

[maxvertexcount(6)]
void GeoMain(triangle VSOutput IN[3], inout TriangleStream<g2f> triStream)
{
	float2 WIN_SCALE = float2(ce_ScreenParams.x / 2.0, ce_ScreenParams.y / 2.0);

	//frag position
	float2 p0 = WIN_SCALE * IN[0].positionNDC.xy / IN[0].positionNDC.w;
	float2 p1 = WIN_SCALE * IN[1].positionNDC.xy / IN[1].positionNDC.w;
	float2 p2 = WIN_SCALE * IN[2].positionNDC.xy / IN[2].positionNDC.w;

	//barycentric position
	float2 v0 = p2-p1;
	float2 v1 = p2-p0;
	float2 v2 = p1-p0;
	//triangles area
	float area = abs(v1.x*v2.y - v1.y * v2.x);

	g2f OUT;
	OUT.pos = IN[0].positionNDC;
	OUT.pos.y *= -1;
	OUT.dist = float3(area/length(v0),0,0);
	triStream.Append(OUT);

	OUT.pos = IN[1].positionNDC;
	OUT.pos.y *= -1;
	OUT.dist = float3(0,area/length(v1),0);
	triStream.Append(OUT);

	OUT.pos = IN[2].positionNDC;
	OUT.pos.y *= -1;
	OUT.dist = float3(0,0,area/length(v2));
	triStream.Append(OUT);
}

float4 PSMain(g2f input) : SV_TARGET
{
	// return WireFrameColor;
	//distance of frag from triangles center
	float d = min(input.dist.x, min(input.dist.y, input.dist.z));
	//fade based on dist from center
	float I = exp2(-4.0*d*d);

	return lerp(float4(0, 0, 0, 0), WireFrameColor, I);	
}