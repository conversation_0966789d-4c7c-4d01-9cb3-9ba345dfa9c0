#pragma compute ConvertVelocityCS
#pragma compute MergeCloudDepthCS
#pragma compute GetSeparateTranslucencyAlpha
#include "../../ShaderLibrary/MotionVector.hlsl"
Texture2D<float>           InputDepth               : register(space0);
Texture2D<float4>           InputVelocity               : register(space0); 
RWTexture2D<float2>           OutputTexture               : register(space0);

// MergeCloudDepthCS resources
Texture2D<float>           SceneDepth               : register(space0);
Texture2D<float4>          CloudDepth               : register(space0); 
RWTexture2D<float>         MergedDepth              : register(space0);


// MergeReactiveCS resources
Texture2D ReactiveTexture;
RWTexture2D<float4> RWReactiveTexture;

// GetSeparateTranslucencyAlpha resources
Texture2D SeparateTranslucency;
RWTexture2D<float>SeparateTranslucencyAlpha;

cbuffer Constant
{
    uint2       ViewSize;
    float2      ViewInvSize;
    float4x4 fReprojectionMat;
}


[numthreads(8, 8, 1)]
void ConvertVelocityCS(uint3 DispatchThreadId : SV_DispatchThreadID)
{
    uint Width = ViewSize.x;
    uint Height = ViewSize.y;

	if (Width > DispatchThreadId.x && Height > DispatchThreadId.y)
	{
		float Depth = InputDepth[DispatchThreadId.xy].x;
		float2 Velocity = ComputeStaticVelocity(DispatchThreadId.xy, fReprojectionMat, ViewSize, Depth);

		float2 EncodedVelocity = InputVelocity[DispatchThreadId.xy].xy;
        if ( EncodedVelocity.x > 0.0 )
        {
            Velocity = DecodeVelocityFromTexture(EncodedVelocity).xy;
        }
		OutputTexture[DispatchThreadId.xy] = Velocity * float2(0.5, -0.5);
	}
}

[numthreads(8, 8, 1)]
void MergeCloudDepthCS(uint3 DTid : SV_DispatchThreadID)
{
    if (DTid.x >= ViewSize.x || DTid.y >= ViewSize.y)
        return;
        
    float sceneDepth = SceneDepth[DTid.xy];
    // CloudDepth is float4, where y component stores the average ndc depth
    float cloudDepth = CloudDepth[DTid.xy].y;
    
    // Using reverse-Z, so larger values are closer
    float mergedDepth = max(sceneDepth, cloudDepth);
    
    MergedDepth[DTid.xy] = mergedDepth;
}


[numthreads(8, 8, 1)] 
void MergeReactiveCS(uint3 LocalThreadId : SV_GroupThreadID, uint3 WorkGroupId : SV_GroupID, uint3 DispatchThreadId : SV_DispatchThreadID)
{
    uint2 pixelPos = DispatchThreadId.xy;
    RWReactiveTexture[pixelPos].xy = max(RWReactiveTexture[pixelPos].xy, ReactiveTexture[pixelPos].xy);
}

[numthreads(8, 8, 1)] 
void GetSeparateTranslucencyAlpha(uint3 LocalThreadId : SV_GroupThreadID, uint3 WorkGroupId : SV_GroupID, uint3 DispatchThreadId : SV_DispatchThreadID)
{
    uint2 pixelPos = DispatchThreadId.xy;
    SeparateTranslucencyAlpha[pixelPos].x = max(SeparateTranslucency[pixelPos].w, 0.0f);
}