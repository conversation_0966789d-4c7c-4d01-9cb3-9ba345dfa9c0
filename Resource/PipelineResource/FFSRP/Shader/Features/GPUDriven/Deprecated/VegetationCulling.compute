#pragma compute SetupMapData
#pragma compute FoliageCulling
#pragma compute CopyLightInstanceBuffer
#pragma compute UpdateLightShadowIndex
#pragma compute DrawCompaction
// #pragma enable debug_symbol

#include "../../Lighting/LightDefinition.hlsl"
#include "../../ShaderLibrary/Common.hlsl"
#include "../../ShaderLibrary/CommonStruct.hlsl"
#include "../../ShaderLibrary/Random.hlsl"

// #pragma keyword LIGHT_CULLING
SHADER_CONST(bool, LIGHT_CULLING, false);

#define FIXED_LOD_NUM 5
#define MAX_UPDATE_BOUNDS 10
#define BASE_SCREEN_HEIGHT 1080

cbuffer _cbCommon
{
    matrix ce_Projection;
    matrix ce_View;
    matrix shadowCamProjection;
    matrix shadowCamView;
    matrix lightToCamera;           // clip space from shadow camera to render camera
	float4 ce_ScreenParams;
	float4 ce_HZBParams;
    float4 displayScreenSize;
    float4 _Frustums[6];
    float4 shadowFrustums[6];
    float4 hizUVFactorAndInv;
    float3 ce_CameraPos;
    float3 ce_CameraTilePosition;
    float3 ce_ShadowCameraTilePosition;
    float _DrawCMD_Count;
    float _DrawUnitCount;
    float _BatchDrawUnitCount;
    float2 _Camera_Range;
    bool useHiz;
    bool reverseZ;
    bool isShadowPass;
    bool useDoubleTransform;
    bool shadowVolumeCulling;
    float drawCountOffset;
    float lodScaleFactor;
    float copyLightInstanceCount;
    float tileBasedCullHeight;
    uint _VisibleObjectCommandBufferMaxNum;
    uint _SourceBufferShadowIndexCount;
    float _RandomCullingMin;
    float _RandomCullingMax;

    // Voxel Pass Culling
    bool isClipmapCulling;
    int aabbCount;
    float4 aabbCenters[MAX_UPDATE_BOUNDS];
    float4 aabbExtents[MAX_UPDATE_BOUNDS];
}

struct VisibleObjectCommand
{
    uint objectIndex;
    uint indirectArgIndex;
};

struct LODInfo
{
    uint indexCount;
	uint firstIndex;
	int vertexOffset;
    float screenReleativeTransitionHeight;
};

struct BatchDrawUnitInfo
{
    uint countBufferOffset;
    uint instanceCount;
    uint startSection;
    uint sectionCount;
    uint separateLOD;
};

struct InstanceSection      // also drawUnitInfo
{
    float4 boundingSphere;
    uint batchDrawUnitIndex;
    uint instanceOffset;
    uint drawCountOffset;
    float culledHeight;
    int isPCGFoliage;
    uint startLodOffset;
    uint lodCount;
    uint startInstance;
    uint instanceCount;
};


struct DrawUnitInfo
{
    float4 boundingSphere;
    float culledHeight;
    float maxRandomCulling;
    uint startLodOffset;
    uint lodCount;
    uint lodBias;
    uint startInstance;
    uint instanceCount;
    uint indirectArgIndexOffset;
};

struct CompactDrawCMD
{
    uint indexCount;
	uint instanceCount;
	uint firstIndex;
	int vertexOffset;
	uint firstInstance;
};

struct MapData
{
    uint drawUnitId;
    uint firstInstance;
};

StructuredBuffer<FoliageEntityData> _FoliageEntityBuffer;
StructuredBuffer<FoliageCompactSceneData> _InstanceBuffer;
RWStructuredBuffer<InstanceLightType> _InstanceBufferLight;
StructuredBuffer<InstanceLightType> _SourceBufferLight;
StructuredBuffer<int> _SourceBufferShadowIndex;        // 2 * n = instanceID, 2 * n + 1 = shadowIndex
StructuredBuffer<LODInfo> _SrcDrawBuffer;               // store lod info of different meshes
StructuredBuffer<DrawUnitInfo> _DrawUnitInfoBuffer;
StructuredBuffer<uint> _IndirectArgIndexBuffer;
RWStructuredBuffer<uint> _CountBuffer;                  // _CountBuffer[0] represent total drawCount. _CountBuffer[1], _CountBuffer[2], ... represent drawCount for specified drawunit.
RWStructuredBuffer<uint> _InstanceCount;
RWStructuredBuffer<uint> _TilebasedLightID;             // [0] represent total count, the following is the light id
RWStructuredBuffer<uint> _MapData;                      // store drawUnit id and instanceID mapping
RWStructuredBuffer<CompactDrawCMD> _DstDrawBuffer;
RWStructuredBuffer<CompactDrawCMD> _OutDrawIndirectArgs;
RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
Texture2D<float> hzb;
Texture2D<float> hzbShadowVolume;
SamplerState ce_Sampler_Clamp;
SamplerState ce_Sampler_Point;

#include "../Cluster/CullingCommon.hlsl"

bool CullingFunc(float4 boundingSphere, matrix world, float3 tilePosition, out float4 viewSphere)
{
    float3 center = boundingSphere.xyz;
    float4 world_ceneter = mul(world, float4(center, 1));
    if (useDoubleTransform)
    {
        if (isShadowPass)
        {
            world_ceneter.xyz = GetLargeCoordinateReltvPosition(world_ceneter.xyz, tilePosition, ce_ShadowCameraTilePosition);
        }
        else
        {
            world_ceneter.xyz = GetLargeCoordinateReltvPosition(world_ceneter.xyz, tilePosition, ce_CameraTilePosition);
        }
    }
    float scale = max(max(length(world[0].xyz), length(world[1].xyz)), length(world[2].xyz));
    viewSphere = mul(world_ceneter, isShadowPass ? transpose(shadowCamView) : transpose(ce_View));
    float4 worldSphere = float4(world_ceneter.xyz, boundingSphere.w * scale);
    viewSphere.w = worldSphere.w;
    bool frustumVisible = true;
    if (isClipmapCulling)
    {
        bool visible = false;
        for (int i = 0; i < aabbCount; i++)
        {
            if (AABBTestVisible(worldSphere, aabbCenters[i].xyz, aabbExtents[i].xyz))
            {
                visible = true;
                break;
            }
        }
        return visible;
    }
    else
    {
        if (isShadowPass)
        {
            frustumVisible = FrustumTestVisible(worldSphere, shadowFrustums);

            // float4 scaleWorldSphere = float4(worldSphere.xyz, worldSphere.w * 1.3);
            // frustumVisible &= FrustumTestVisible(scaleWorldSphere, _Frustums);         // render camera culling, may have misculling result in boundary
        }
        else
        {
            frustumVisible = FrustumTestVisible(worldSphere, _Frustums);
        }

        bool occlusionVisible = true;
        if (frustumVisible && useHiz)
        {
            occlusionVisible = OcclussioCulling(worldSphere, ce_View, ce_Projection, true);
            // occlusionVisible = OcclussionTestVisible(viewSphere, isShadowPass ? shadowCamProjection : ce_Projection, _Camera_Range, reverseZ, isShadowPass, shadowVolumeCulling);
        }

        // frustum culling and occlusion culling
        bool visible = frustumVisible && occlusionVisible;
        return visible;
    }
}

float GetScreenSizeY(float3 viewSphereCenter, float radius, matrix proj)
{
    float dist = length(viewSphereCenter);
    float screenSize = max(proj._m00, proj._m11) * 0.5;
    float screenRadius = screenSize * radius / max(1.0, dist);
    return 2 * screenRadius;
}

#define GROUP_SIZE 64

[numthreads(64, 1, 1)]
void CopyLightInstanceBuffer(
    uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    uint TotalCommandId = Gid.x * 64 + GI;
    if (TotalCommandId >= copyLightInstanceCount)
        return;
    
    InstanceLightType instanceData = _SourceBufferLight[TotalCommandId];
    uint instanceID = instanceData.instanceID;
    _InstanceBufferLight[instanceID] = instanceData;
}

[numthreads(64, 1, 1)]
void UpdateLightShadowIndex(
    uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    uint TotalCommandId = Gid.x * 64 + GI;
    if (TotalCommandId >= _SourceBufferShadowIndexCount)
        return;
    
    uint instanceID = _SourceBufferShadowIndex[2 * TotalCommandId];
    int shadowDataIndex = _SourceBufferShadowIndex[2 * TotalCommandId + 1];
    _InstanceBufferLight[instanceID].shadowDataIndex = shadowDataIndex;
}

groupshared uint temp[8192];
[numthreads(1, 1, 1)]
void DrawCompaction(
    uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    uint sum = 0;
    int i = 0;
    uint drawOffset = drawCountOffset;

    for (i = 0; i < _DrawCMD_Count; ++i)
    {
        temp[i] = sum;
        sum += _DstDrawBuffer[i + drawOffset].instanceCount;
    }

    for (i = 0; i < _DrawCMD_Count; ++i)
    {
        if (_DstDrawBuffer[i + drawOffset].instanceCount == 1)
        {
            _DstDrawBuffer[temp[i] + drawOffset] = _DstDrawBuffer[i + drawOffset];
        }
    }
}

[numthreads(GROUP_SIZE, 1, 1)]
void SetupMapData(
    uint GI:SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID)
{
    uint TotalCommandId = Gid.x * GROUP_SIZE + GI;

    if (TotalCommandId >= _DrawCMD_Count)
        return;
    
    int curInstanceCount = 0;
    uint curDrawUnitId = 0;
    uint instanceID = 0;
    for (int i = 0; i < _DrawUnitCount; ++i)
    {
        DrawUnitInfo info = _DrawUnitInfoBuffer[i];
        int nextInstanceCount = curInstanceCount + info.instanceCount;
        if (TotalCommandId < nextInstanceCount)
        {
            instanceID = TotalCommandId - curInstanceCount;
            curDrawUnitId = i;
            break;
        }
        else
        {
            curInstanceCount = nextInstanceCount;
        }
    }
    _MapData[2 * TotalCommandId] = curDrawUnitId;
    _MapData[2 * TotalCommandId + 1] = instanceID;
}

[numthreads(64, 1, 1)]
void FoliageCulling(
	uint GI : SV_GroupIndex,
	uint3 Gid : SV_GroupID,
	uint3 GTid : SV_GroupThreadID,
	uint3 DTid : SV_DispatchThreadID
)
{
    uint TotalCommandId = Gid.x * 64 + GI;

    if(TotalCommandId >= _DrawCMD_Count)
        return;

    int curInstanceCount = 0;
    uint curDrawUnitId = 0;
    uint firstInstance = 0;
    uint instanceID = 0;
    
    curDrawUnitId = _MapData[2 * TotalCommandId];
    instanceID = _MapData[2 * TotalCommandId + 1];

    uint baseDrawUnitId = FIXED_LOD_NUM * curDrawUnitId;
    DrawUnitInfo drawUnitInfo = _DrawUnitInfoBuffer[curDrawUnitId];
    firstInstance = drawUnitInfo.startInstance + instanceID;
    uint startLodOffset = drawUnitInfo.startLodOffset;
    // uint instanceOffset = drawUnitInfo.instanceOffset;
    // uint drawCountOffset = drawUnitInfo.drawCountOffset;
    // int isPCGFoliage = drawUnitInfo.isPCGFoliage;
    // bool pcgVisible = (!isPCGFoliage || firstInstance - instanceOffset < _InstanceCount[0]);

    float4 boundingSphere = drawUnitInfo.boundingSphere;
    matrix world;
    float3 tilePosition;
    if (LIGHT_CULLING)
    {
        InstanceLightType instanceData = _InstanceBufferLight[firstInstance];
        world = instanceData.world;
        tilePosition = instanceData.lightTilePos.xyz;
    }
    else
    {
        FoliageCompactSceneData compactData = _InstanceBuffer[firstInstance];
        FoliageObjectSceneData instanceData;
        DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], instanceData);
        world = instanceData.world;
        tilePosition = instanceData.tilePosition;
    }
    float4 viewSphere;
    bool cullingVisible = CullingFunc(boundingSphere, world, tilePosition, viewSphere);

    uint lodIndex = 0;
    uint lodCount = drawUnitInfo.lodCount;
    uint startLod = min(drawUnitInfo.lodBias, lodCount -1);
    
    if (cullingVisible)
    {
        // select proper lod index
        float screenSizeY = GetScreenSizeY(viewSphere.xyz, viewSphere.w, ce_Projection);
        float sizeFactor = 1.0 * sqrt(displayScreenSize.y / BASE_SCREEN_HEIGHT);
        float random = drawUnitInfo.maxRandomCulling>0?lerp(_RandomCullingMin, _RandomCullingMax * drawUnitInfo.maxRandomCulling, pow(PerInstanceRandom(firstInstance), 2.0)):1.0;

        for (int i = startLod; i < lodCount; ++i)
        {
            lodIndex = i;
            LODInfo lod = _SrcDrawBuffer[startLodOffset + lodIndex];
            // culledHeight test
            if (screenSizeY < drawUnitInfo.culledHeight * random)
            {
                cullingVisible = false;
                break;
            }

            if (isShadowPass)
            {
                sizeFactor *= 0.8;
            }
            if (screenSizeY * sizeFactor * lodScaleFactor > lod.screenReleativeTransitionHeight)
            {
                break;
            }
        }

        if (LIGHT_CULLING && cullingVisible && screenSizeY > tileBasedCullHeight)
        {
            // record light id and use in tilebased rendering
            cullingVisible = false;
            uint writeIndex;
            InterlockedAdd(_TilebasedLightID[0], 1, writeIndex);
            _TilebasedLightID[writeIndex + 1] = firstInstance;
        }
    }

    uint drawUnitId = startLodOffset + lodIndex;
    LODInfo lodInfo = _SrcDrawBuffer[drawUnitId];
    bool visible = lodInfo.indexCount > 0 && cullingVisible;

    if (visible) 
    {
        uint indirectArgIndex = _IndirectArgIndexBuffer[drawUnitInfo.indirectArgIndexOffset + lodIndex];
        InterlockedAdd(_OutDrawIndirectArgs[indirectArgIndex].instanceCount, 1);

        VisibleObjectCommand visibleObjectCommand;
        visibleObjectCommand.objectIndex = firstInstance;
        visibleObjectCommand.indirectArgIndex = indirectArgIndex;

        uint visibleObjectCommandOutputOffset = 0u;
        InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
        // if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
        {
            _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
        }
    }
}