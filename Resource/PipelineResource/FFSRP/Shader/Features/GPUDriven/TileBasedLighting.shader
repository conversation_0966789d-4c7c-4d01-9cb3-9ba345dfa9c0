#pragma vertex VSMain
#pragma pixel PSMain
#pragma keyword CE_USE_DOUBLE_TRANSFORM
// #pragma enable debug_symbol

#define DEFERRED_SHADING
#define ENABLE_VIEW_MODE_VISUALIZE

#include "../../ShaderLibrary/Common.hlsl"

#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
// #include "../../Material/Lit/LitUEVariables.hlsl"

#include "../../Material/Material.hlsl"
#include "../../Lighting/Lighting.hlsl"

// #include "../../Material/Lit/Lit.hlsl"
// #include "../../Lighting/LightLoop/IndirectDiffuse.hlsl"
#include "../../Lighting/LightDefinition.hlsl"
#include "../../Lighting/LightLoop/LightLoopDef.hlsl"
#include "../../Lighting/SurfaceShading.hlsl"

SHADER_CONST(bool, USE_SIMPLE_SPOT, false);

Texture2D<float4> _HiZBuffer : register(space0);

struct LightLoopOutput
{
	float3 diffuseLighting;
	float3 specularLighting;
	float3 indirectDiffuseLighting;
};

struct VSInput
{
	float4 position : POSITION;
	float2 uv : TEXCOORD0;
};

struct PSInput
{
	float4 positionNDC : SV_POSITION;
};

#include "../../Lighting/Shadow/MassiveLightsShadow/MassiveLightsShadowCommon.hlsl"

RWStructuredBuffer<uint> _TilebasedLightID;             // [0] represent total count, the following is the light id
StructuredBuffer<InstanceLightType> _InstanceBufferLight;

LightLoopOutput LightLoop(float3 V, PositionInputs posInput, BSDFData bsdfData, BuiltinData builtinData)
{
	LightLoopOutput output = (LightLoopOutput)0;
    float depth = posInput.deviceDepth;
    float sceneDepth = ConvertFromDeviceZ(depth, ce_Projection);
    float3 positionWS = posInput.positionWS;
    uint2 positionSS = posInput.positionSS;
    float3 normalWS = bsdfData.normalWS;
    uint GridIndex = ComputeLightGridCellIndex(posInput.positionSS, sceneDepth,
        float3(_LightGridZParamsB, _LightGridZParamsO, _LightGridZParamsS), 
        uint3(_CulledGridSizeX, _CulledGridSizeY, _CulledGridSizeZ), _LightGridPixelSizeShift);
    uint NumGrid = _CulledGridSizeX * _CulledGridSizeY * _CulledGridSizeZ;
    uint NumCulledEntryIndex = (GridIndex + NumGrid) * 2;
    uint NumCulledLocalLights = min(_NumCulledLightsGrid[NumCulledEntryIndex + 0], _TilebasedLightID[0]);
    uint DataStartIndex = _NumCulledLightsGrid[NumCulledEntryIndex + 1];

    bool shadowed = false;

    for (uint TileLightIndex = 0; TileLightIndex < NumCulledLocalLights; TileLightIndex++)
    {
        shadowed = false;

        uint lightIndex = _CulledLightsDataGrid[DataStartIndex + TileLightIndex];

        uint instanceID = lightIndex;//_TilebasedLightID[lightIndex];
	    InstanceLightType lightData = _InstanceBufferLight[instanceID];
        int shadowDataIndex = lightData.shadowDataIndex;
        float4 lightDirPos = lightData.lightDirPos;
        float4 lightTilePos = lightData.lightTilePos;
        float4 lightColor = lightData.lightColor;
        float3 lightSpotDir = lightData.lightSpotDirection.xyz;
        float fadeInten = lightData.lightSpotDirection.w;
        float lightSpotDirLen = length(lightSpotDir);
        float4 lightAttenuation = lightData.lightAttenuation;

        // early return
        float range = lightDirPos.w;
        float3 lightWorldPos = lightDirPos.xyz;
    #ifdef CE_USE_DOUBLE_TRANSFORM
        lightWorldPos = GetLargeCoordinateReltvPosition(lightWorldPos, lightTilePos.xyz, ce_CameraTilePosition);
    #endif
        float3 L = lightWorldPos - positionWS;
        float distance = length(L);
        if (distance > range)
        {
            continue;
        }

        if (lightColor.w >= 0.0)
        {
            matrix rotMat = GetTransMatrixInverseFromEulerAndPos(lightSpotDir.xyz, float3(0, 0, 0));
            float3 lightForward = mul(float4(0, 0, 1.0, 1.0), rotMat).xyz;
            L = normalize(L);
            float minAttenuation = min(min(min(lightAttenuation.x, lightAttenuation.y), lightAttenuation.z), lightAttenuation.w);
            float cos_vertex = dot(-L, lightForward);
            if (cos_vertex < minAttenuation)
            {
                continue;
            }
        }

        bool shadowed = MassiveLightsShadow_LoadShadowBit(positionWS, positionSS, instanceID);

        if (lightColor.w < 0.0)
        {
            // Point Light
            PunctualLightData light = (PunctualLightData)0;
            light.positionWS = lightDirPos.xyz;
            light.color = lightColor.rgb;
            light.attenuation = lightAttenuation;
            light.tilePosition = lightTilePos.xyz;

            float shadowTransmission = 1.0;
            float shadowValue = MassiveLightsShadow_CalculateShadowValue(posInput, positionSS, normalWS, shadowed, light.positionWS, lightData, OUT_ARG shadowTransmission);

            DirectLighting directLighting = ShadeSurface_Point_MassiveLights(V, posInput, bsdfData, builtinData, light, lightIndex, shadowDataIndex, shadowValue, shadowTransmission);

            directLighting.diffuse *= shadowValue;
            directLighting.specular *= shadowValue;

            if (MASSIVE_LIGHTS_SHADOW_DEBUG)
            {
                float4 debugColor = MassiveLightsShadow_GetDebugColor(positionWS);
                directLighting.diffuse *= debugColor.xyz;
                directLighting.specular *= debugColor.xyz;
            }

            output.diffuseLighting += directLighting.diffuse;
            output.specularLighting += directLighting.specular;
        }
        else
        {
            // Spot Light
            PunctualLightData light = (PunctualLightData)0;
            light.positionWS = lightDirPos.xyz;
            light.forward = lightSpotDir;
            light.color = lightColor.rgb;
            light.attenuation = lightAttenuation;
            light.tilePosition = lightTilePos.xyz;
            float range = lightDirPos.w;
            float fadeIntensity = fadeInten;
            float overflowLength = lightTilePos.w;
            float distExp = lightColor.w;

            float shadowTransmission = 1.0;
            float shadowValue = MassiveLightsShadow_CalculateShadowValue(posInput, positionSS, normalWS, shadowed, light.positionWS, lightData, OUT_ARG shadowTransmission);

            DirectLighting directLighting = (DirectLighting)0;
            if (USE_SIMPLE_SPOT)
            {
                directLighting = ShadeSurface_Spot_Simple_MassiveLights(
                    V, posInput,
                    bsdfData, builtinData,
                    light, range,
                    fadeIntensity, overflowLength, distExp,
                    lightIndex, shadowDataIndex, shadowValue, shadowTransmission);
            }
            else
            {
                directLighting = ShadeSurface_Spot_MassiveLights(
                    V, posInput, 
                    bsdfData, builtinData, 
                    light, range, 
                    fadeIntensity, overflowLength, distExp,
                    lightIndex, shadowDataIndex,
                    shadowValue, shadowTransmission);
            }

            if (MASSIVE_LIGHTS_SHADOW_DEBUG)
            {
                float4 debugColor = MassiveLightsShadow_GetDebugColor(positionWS);
                directLighting.diffuse *= debugColor.xyz;
                directLighting.specular *= debugColor.xyz;
            }            

            output.diffuseLighting += directLighting.diffuse;
            output.specularLighting += directLighting.specular;
        }
    }

	return output;
}

PSInput VSMain(VSInput input)
{
	PSInput output = (PSInput)0;
	float4 position = input.position;
	position.xy = -1.0f + 2.0f * position.xy;
	position.xy *= float2(1, -1);
	output.positionNDC = position;

	return output;
}

float3 LinearToSrgb(float3 lin) 
{
	lin = max(6.10352e-5, lin); // minimum positive non-denormal (fixes black problem on DX11 AMD and NV)
	return min(lin * 12.92, pow(max(lin, 0.00313067), 1.0/2.4) * 1.055 - 0.055);
}

float4 PSMain(PSInput input) : SV_TARGET
{
	float2 screenUV = (input.positionNDC.xy ) * ce_ScreenParams.zw;

	float depth = SampleCameraDepth(screenUV);

	PositionInputs posInput = GetPositionInput(screenUV, depth, ce_InvViewProjMatrix, ce_View);
    posInput.positionSS = input.positionNDC.xy;

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	BSDFData bsdfData;
	BuiltinData builtinData;
	DecodeFromGBuffer(posInput.uv, bsdfData, builtinData);

	LightLoopOutput lightLoopOutput = LightLoop(V, posInput, bsdfData, builtinData);	
	
    return float4((lightLoopOutput.diffuseLighting + lightLoopOutput.specularLighting + lightLoopOutput.indirectDiffuseLighting), 1.0);
}
