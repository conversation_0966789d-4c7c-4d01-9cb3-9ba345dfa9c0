#pragma compute ScatterByteCopy

cbuffer cbPass
{
    uint _ScatterCount;
}

StructuredBuffer<uint4> _ScatterBuffer;
RWStructuredBuffer<float> _UploadBuffer;
RWStructuredBuffer<float> _DstBuffer;

[numthreads(64, 1, 1)]
void ScatterByteCopy(uint3 dispatchThreadID : SV_DispatchThreadID)
{
    uint scatterIndex = dispatchThreadID.x;

    if (scatterIndex < _ScatterCount)
    {
        uint3 scatterData = _ScatterBuffer[scatterIndex];

        uint uploadBufferOffset = scatterData.x;
        uint sizeInFloat = scatterData.y;
        uint dstBufferOffset = scatterData.z;

        for (uint i = 0; i < sizeInFloat; i++)
        {
            _DstBuffer[dstBufferOffset + i] = _UploadBuffer[uploadBufferOffset + i];
        }
    }
}