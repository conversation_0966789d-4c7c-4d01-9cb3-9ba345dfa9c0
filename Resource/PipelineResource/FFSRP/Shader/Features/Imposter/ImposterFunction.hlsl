#ifndef IMPOSTER_FUNCTION
#define IMPOSTER_FUNCTION

struct ImposterInfo
{
    float3 position;
    float defaultSize;
    float scaledSize;
};

float3 GetObjectScaleXYZ(float4x4 world)
{
    float objScaleX = length(mul((float3x3)world, float3(1, 0, 0)));
    float objScaleY = length(mul((float3x3)world, float3(0, 1, 0)));
    float objScaleZ = length(mul((float3x3)world, float3(0, 0, 1)));

    float3 objScaleXYZ = float3(objScaleX, objScaleY, objScaleZ);
    return objScaleXYZ;
}

#ifdef CE_INSTANCING

ImposterInfo GetImposterInfo(float4x4 worldMat, float3 tilePosition, float3 cameraTilePosition)
{
    ImposterInfo info = (ImposterInfo)0;
    float3 objScaleXYZ = GetObjectScaleXYZ(worldMat);
    float3 originPos = 0;
    float3 originPosWS = mul(worldMat, float4(originPos, 1)).xyz;

    float4 pivotOffset = _PivotOffset;
    float3 pivotOffsetWS = mul((float3x3)worldMat, pivotOffset.xyz).xyz;
    // float3 pivotOffsetWS = 2 * pivotOffset.xyz;

    info.position = originPosWS + pivotOffsetWS;
#ifdef CE_USE_DOUBLE_TRANSFORM
	info.position = GetLargeCoordinateReltvPosition(info.position, tilePosition, cameraTilePosition);
#endif
    float defaultSize = _DefaultMeshSize;
    info.defaultSize = defaultSize;
    // info.scaledSize = info.defaultSize * objScaleXYZ.x;
    info.scaledSize = info.defaultSize;

    return info;
}

float3 ImposterGridToVector(float2 gridValue, bool fullSphere)
{
    float3 outVec = 0;
    if (fullSphere)
    {
        outVec = _OctahedronToUnitVector(gridValue);
    }
    else
    {
        outVec = _HemiOctahedronToUnitVector(gridValue);
    }
    return outVec;
}

float2 ImposterVectorToGrid(float3 vec, bool fullSphere)
{
    float2 outGridValue = 0;
    if (fullSphere)
    {
        outGridValue = _UnitVectorToOctahedron(vec);
    }
    else
    {
        vec = float3(vec.x, vec.y, max(vec.z, 0.001));
        outGridValue = _UnitVectorToHemiOctahedron(vec);
    }
    return outGridValue;
}

void ImposterSpriteProjection(VSInput vIn, float2 octahedron, float3 vec, float xyFrames, float2 size, bool fullSphere, out float3 outVec)
{
    float frames = (xyFrames - 1);
    float2 gridValue = floor(frames * octahedron) / frames;
    gridValue = (gridValue - 0.5) * 2;
    float3 vecOverride = ImposterGridToVector(gridValue, fullSphere);

    float3 vecZ = normalize(vec);
    float3 vecX = normalize(cross(vecZ, float3(0, 1, 0)));
    // float3 vecX = normalize(float3(vecZ.y, vecZ.x * -1, 0));
    float3 vecY = normalize(cross(vecZ, vecX));

    float2 uv = vIn.uv * 10;
    uv = (uv * xyFrames - 0.5) * 2;
    size = 0.5 * size;

    outVec = size.x * uv.x * vecX + size.y * uv.y * vecY;
}

float3 SetUpImposterFrameTransform(float2 frame, float2 xyFrames, bool fullSphere)
{
    float2 gridValue = frame / (xyFrames - 1);
    gridValue = (gridValue - 0.5) * 2;
    float3 worldZ = normalize(ImposterGridToVector(gridValue, fullSphere));
    worldZ = float3(worldZ.x, worldZ.z, -worldZ.y);

    return worldZ;
}

float3 InverseTransformMatrix(float3 vecToTransform, float3 basisX, float3 basisY, float3 basisZ)
{
    float x = dot(vecToTransform, basisX);
    float y = dot(vecToTransform, basisY);
    float z = dot(vecToTransform, basisZ);

    return float3(x, y ,z);
}

void TriangleInterpolator(float2 uv, out float3 result, out float mask)
{
    float2 _uv = frac(uv);
    float2 x = (1 - _uv);
    float y = dot(float2(1, -1), _uv);
    result = float3(min(x.x, x.y), y, min(_uv.x, _uv.y));
    mask = saturate(ceil(_uv.x - _uv.y));
}

void ImposterFrameTransform(float3 vec, float3 zVec, out float3 localZVec, out float3 worldX, out float3 worldY, out float3 worldZ)
{
    // worldX = float3(zVec.y, -zVec.x, 0);
    worldX = float3(zVec.z, 0, -zVec.x);
    worldX = normalize(worldX);
    worldY = normalize(cross(worldX, zVec));
    worldZ = zVec;
    
    localZVec = InverseTransformMatrix(-vec, worldX, worldY, worldZ);
    localZVec = normalize(localZVec);
}

void ImposterVirtualPlaneCoordinates(float3 planeNormalAxis, float3 planeXAxis, float3 planeYAxis, float3 planeCenter, float2 uvScale, float3 rayDirLocal, float3 rayOriginLocal, out float2 outUV)
{
    // create third orthogonal vector
    float3 vec2 = float3(0, 0.00015, -1);
    float3 outVec1 = normalize(planeNormalAxis);
    float3 outVec3 = normalize(cross(outVec1, normalize(vec2)));

    float planeDistance = dot(planeNormalAxis, rayOriginLocal) - dot(planeNormalAxis, planeCenter);
    float vectorDotPlaneNormal = dot(planeNormalAxis, rayDirLocal);
    float intersectionTime = -planeDistance / vectorDotPlaneNormal;

    float3 intersectionPosition = (intersectionTime * rayDirLocal) + rayOriginLocal;
    float3 worldPosCenter = intersectionPosition - planeCenter;

    if (intersectionTime > 0)
    {
        float2 uv = float2(dot(worldPosCenter, -planeXAxis), dot(worldPosCenter, -planeYAxis));
        outUV = uv / uvScale + 0.5;
    }
    else
    {
        outUV = 0.5;
    }
}

float3 GetWPOImpl(float3 Cur_or_Prev_worldPosition, inout VSOutput vOut, in VSInput vIn, in FoliageObjectSceneData instance, bool previousFrame)
{
    float3 cameraPos = ce_CameraPos;
    float4x4 worldMat = instance.world;
    float3 cameraTilePosition = ce_CameraTilePosition;
    float3 tilePosition = instance.tilePosition;
    if (previousFrame)
    {
        cameraPos = ce_CameraPos;
        worldMat = instance.preWorld;
        // cameraTilePosition = ce_PrevCameraTilePosition;
        tilePosition = instance.preTilePosition;
    }
    ImposterInfo imposterInfo = GetImposterInfo(worldMat, tilePosition, cameraTilePosition);
    float3 dir = normalize(cameraPos - imposterInfo.position);
    float3 dirLS = normalize(mul((float3x3)instance.invWorld, dir));

    // dirLS = float3(dirLS.x, -dirLS.z, dirLS.y);
    float2 outGridValue = ImposterVectorToGrid(float3(dirLS.x, -dirLS.z, dirLS.y), _FullSphere);
    // float2 outGridValue = ImposterVectorToGrid(dirLS, _FullSphere);
    outGridValue = (outGridValue + 1) * 0.5;
    outGridValue = clamp(outGridValue, 0, 1);
    
    float xyFrames = _FramesXY;
    float3 outProjVec;
    ImposterSpriteProjection(vIn, outGridValue, dirLS, xyFrames, imposterInfo.scaledSize, _FullSphere, outProjVec);
    float3 outProjVecWS = mul((float3x3)worldMat, outProjVec);
    // float3 outProjVecWS = mul(worldMat, float4(outProjVec, 1)).xyz;

    float3 dirProj = normalize(cameraPos - (outProjVecWS + imposterInfo.position));
    float defaultMeshSize = _DefaultMeshSize;
    float3 objScaleXYZ = GetObjectScaleXYZ(worldMat);

    float3 offsetPos = outProjVecWS + 0.5 * defaultMeshSize * objScaleXYZ.x * dirProj;
    float3 wpo = offsetPos + imposterInfo.position - Cur_or_Prev_worldPosition;

    if (!previousFrame)
    {
        // vsout
        vOut.imposterUV = (xyFrames - 1) * outGridValue;
        float3 viewDir = cameraPos - (_InterpolatePosition * outProjVecWS + imposterInfo.position);
        // viewDir = float3(viewDir.x, -viewDir.z, viewDir.y);
        float3 viewDirLS = normalize(mul((float3x3)instance.invWorld, viewDir));

        float2 imposterUVFloor = floor(vOut.imposterUV);
        float3 inWorldZ = SetUpImposterFrameTransform(imposterUVFloor, xyFrames, _FullSphere);
        float3 localZVec, worldX, worldY, worldZ;
        ImposterFrameTransform(viewDirLS, inWorldZ, localZVec, worldX, worldY, worldZ);

        float2 virtualPlaneUV;
        float2 uvScale = _TanScaleFix * imposterInfo.scaledSize;
        float3 rayDirLocal = (imposterInfo.position + outProjVecWS) - cameraPos;
        // rayDirLocal = float3(rayDirLocal.x, -rayDirLocal.z, rayDirLocal.y);
        rayDirLocal = mul((float3x3)instance.invWorld, rayDirLocal);
        float3 rayOriginLocal = (cameraPos - imposterInfo.position);
        // rayOriginLocal = float3(rayOriginLocal.x, -rayOriginLocal.z, rayOriginLocal.y);
        rayOriginLocal = mul((float3x3)instance.invWorld, rayOriginLocal);
        ImposterVirtualPlaneCoordinates(worldZ, worldX, -worldY, 0, uvScale, rayDirLocal, rayOriginLocal, virtualPlaneUV);
        vOut.virtualPlaneUV = virtualPlaneUV / xyFrames;
    }

	return wpo + Cur_or_Prev_worldPosition;
}

float4 FrameBlendWeights(float4 R, float4 G, float4 B, float3 weights)
{
    float4 result = weights.x * R + weights.y * G + weights.z * B;
    return result;
}

void TextureFrameBlendWeights(Texture2D<float4> tex, float2 uvR, float2 uvG, float2 uvB, float3 weights, float2 derivativeUV, out float4 result, out float4 resultR)
{
    // ddx(derivativeUV);
    // ddy(derivativeUV);

    float4 R = tex.Sample(ce_Sampler_Clamp, uvR);
    float4 G = tex.Sample(ce_Sampler_Clamp, uvG);
    float4 B = tex.Sample(ce_Sampler_Clamp, uvB);

    result = FrameBlendWeights(R, G, B, weights);
    resultR = R;
}
#endif
#endif