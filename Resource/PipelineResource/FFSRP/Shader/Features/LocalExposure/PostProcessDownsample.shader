#pragma vertex VSMain
#pragma pixel MainPS

#include "PostProcessDownsample.hlsl"

float4 VSMain(uint vertexID : SV_VertexID, out float2 UV : TEXCOORD0) : SV_POSITION
{
    float2 uv = float2((vertexID << 1U) & 2U, vertexID & 2U);
    UV = uv;
    return float4(uv * float2(2.f, -2.f) + float2(-1.f, 1.f), 0.f, 1.f);
}

// pixel shader entry point
void MainPS(float4 SvPosition : SV_POSITION, in noperspective float2 UV : TEXCOORD0, out float4 OutColor : SV_Target0)
{
	// const float2 UV = SvPosition.xy * Output_ExtentInverse;

	OutColor = DownsampleCommon(UV);
}
