
#include "common.h"

Texture2D VelocityTileTexture;
SamplerState ce_Sampler_Clamp : register(space0);

// Neighbor Max Filter
float4 NeighborMaxFilter(uint2 PixelPos) 
{
    const float cw = 1.01f; // center weight tweak

    int4 d = int4(1, 1, -1, 0);

    float4 v1 = VelocityTileTexture[PixelPos - d.xy];
    float4 v2 = VelocityTileTexture[PixelPos - d.wy];
    float4 v3 = VelocityTileTexture[PixelPos - d.zy];

    float4 v4 = VelocityTileTexture[PixelPos - d.xw];
    float4 v5 = VelocityTileTexture[PixelPos       ] * cw;
    float4 v6 = VelocityTileTexture[PixelPos + d.xw];

    float4 v7 = VelocityTileTexture[PixelPos + d.zy];
    float4 v8 = VelocityTileTexture[PixelPos + d.wy];
    float4 v9 = VelocityTileTexture[PixelPos + d.xy];

    float4 va = VMax(v1, VMax(v2, v3));
    float4 vb = VMax(v4, VMax(v5, v6));
    float4 vc = VMax(v7, VMax(v8, v9));

    return VMax(va, VMax(vb, vc)) / cw;
}
