// #pragma enable debug_symbol
#pragma compute CSCoC
#pragma compute CSPrefilter
#pragma compute CSBlur
#pragma compute CSPostBlur
#pragma compute CSCombine

#define GROUP_SIZE_X 32
#define GROUP_SIZE_Y 32

SamplerState ce_Sampler_Point : register(space0);//ce_Sampler_Point
SamplerState ce_Sampler_Clamp : register(space0);
Texture2D<float4> _DepthMap : register(space0);
Texture2D<float4> _MainMap : register(space0);//sceneTex
Texture2D<float> _CoCMap : register(space0);//[0-1] as alpha
Texture2D<float4> _DOFMap : register(space0);
Texture2D<float4> _DOFTempMap : register(space0);
RWTexture2D<float> _CoCDst : register(space0);//[0-1] as alpha
RWTexture2D<float4> _DOFDst : register(space0);
RWTexture2D<float4> _DOFTempDst : register(space0);
RWTexture2D<float4> _OutputDst : register(space0);

cbuffer _cbCommon
{
    float4 _ZBufferParams;               //x = 1 - f / n      y = f/n     z = 1/f - 1/n     w = 1/n
    float4 _MainTex_TexelSize;              //x:1/texWidth       y:1/texHeight   z:-1/texWidth      w:-1/texHeight
    //Mtl
    float _CoCRadiusInPixels;//10 pixel Height

    float _FocalLength;//[1,300]:51.47687
    float _Aperture;//N:2.136895
    float _FocusDistance;//m:7.346432
    float _FilmHeight;//0.02046667
    float _CocInfinityRadius;

    float4x4 _ProjMat;
}

#define F              _FocalLength // mm
#define FocusDistance  _FocusDistance // cm
#define S1             max(_FocusDistance * 10,F)
// #define LensCoeff      F*F/(_Aperture*(S1-F)*_FilmHeight*2)

#define _MaxCoC        min(0.1,_CoCRadiusInPixels*_MainTex_TexelSize.y)
#define _RcpMaxCoC     1.0/_MaxCoC
#define _RcpAspect     _MainTex_TexelSize.x/_MainTex_TexelSize.y

#define PI 3.1415926

float LinearEyeDepth(float z)
{
    return 1.0 / (_ZBufferParams.z * z + _ZBufferParams.w);
}

float LinearEyeDepth(float depth, float4x4 projMatrix)
{
    return (projMatrix[2][3] / (depth - projMatrix[2][2]));
}

float SmoothStep(float x, float y, float t) {
    float u = -2.f * t * t * t + 3.f * t * t;
    return y * u + x * (1.f - u);
}

float Max3(float a, float b, float c)
{
    return max(max(a, b), c);
}

float Min3(float a, float b, float c)
{
    return min(min(a, b), c);
}

static const int kSampleCount = 22;
static const float2 kDiskKernel[kSampleCount] = {
    float2(0,0),
    float2(0.53333336,0),
    float2(0.3325279,0.4169768),
    float2(-0.11867785,0.5199616),
    float2(-0.48051673,0.2314047),
    float2(-0.48051673,-0.23140468),
    float2(-0.11867763,-0.51996166),
    float2(0.33252785,-0.4169769),
    float2(1,0),
    float2(0.90096885,0.43388376),
    float2(0.6234898,0.7818315),
    float2(0.22252098,0.9749279),
    float2(-0.22252095,0.9749279),
    float2(-0.62349,0.7818314),
    float2(-0.90096885,0.43388382),
    float2(-1,0),
    float2(-0.90096885,-0.43388376),
    float2(-0.6234896,-0.7818316),
    float2(-0.22252055,-0.974928),
    float2(0.2225215,-0.9749278),
    float2(0.6234897,-0.7818316),
    float2(0.90096885,-0.43388376),
};

//===CoC calculation===                      
//INPUT:_DepthMap
//OUTPUT:_CocMap
[numthreads(GROUP_SIZE_X, GROUP_SIZE_Y, 1)]
void CSCoC(uint3 id : SV_DISPATCHTHREADID)
{
    float2 uv = float2((float)id.x + 0.5, (float)id.y + 0.5)* _MainTex_TexelSize.xy;
    float depth = LinearEyeDepth(_DepthMap.SampleLevel(ce_Sampler_Clamp, uv, 0.0).r, _ProjMat);
    //fg:coc<0  bg coc>0
    float coc = (depth - _FocusDistance) * _CocInfinityRadius / max(depth, 1e-5);
    _CoCDst[id.xy] = coc;
}

//===Prefilter: downsampling and premultiplying===
//INPUT:_CocMap,_MainMap
//OUTPUT:_DOFMap
[numthreads(GROUP_SIZE_X, GROUP_SIZE_Y, 1)]
void CSPrefilter(uint3 id : SV_DISPATCHTHREADID)
{
    float2 uv = float2((float)id.x + 0.5, (float)id.y + 0.5) * _MainTex_TexelSize.xy;

    float3 duv = _MainTex_TexelSize.xyx * float3(0.5,0.5,-0.5);
    float2 uv0 = uv - duv.xy;//-0.5,-0.5
    float2 uv1 = uv - duv.zy;//+0.5,-0.5
    float2 uv2 = uv + duv.zy;//-0.5,+0.5
    float2 uv3 = uv + duv.xy;//+0.5,+0.5

    //sample scene color
    float3 c0 = _MainMap.SampleLevel(ce_Sampler_Point, uv0, 0.0).rgb;
    float3 c1 = _MainMap.SampleLevel(ce_Sampler_Point, uv1, 0.0).rgb;
    float3 c2 = _MainMap.SampleLevel(ce_Sampler_Point, uv2, 0.0).rgb;
    float3 c3 = _MainMap.SampleLevel(ce_Sampler_Point, uv3, 0.0).rgb;

    //sample coc
    float coc0 = _CoCMap.SampleLevel(ce_Sampler_Point, uv0, 0.0).r;
    float coc1 = _CoCMap.SampleLevel(ce_Sampler_Point, uv1, 0.0).r;
    float coc2 = _CoCMap.SampleLevel(ce_Sampler_Point, uv2, 0.0).r;
    float coc3 = _CoCMap.SampleLevel(ce_Sampler_Point, uv3, 0.0).r;

    //reduce bleeding and flicking, heigher weight when darker
    float w0 = abs(coc0) / (Max3(c0.r, c0.g, c0.b) + 1.0);
    float w1 = abs(coc1) / (Max3(c1.r, c1.g, c1.b) + 1.0);
    float w2 = abs(coc2) / (Max3(c2.r, c2.g, c2.b) + 1.0);
    float w3 = abs(coc3) / (Max3(c3.r, c3.g, c3.b) + 1.0);

    //average color according to weight of coc
    float3 avg = (c0 * w0 + c1 * w1 + c2 * w2 + c3 * w3) / max(w0 + w1 + w2 + w3, 1e-5);

    float coc_min = min(coc0, Min3(coc1, coc2, coc3));
    float coc_max = max(coc0, Max3(coc1, coc2, coc3));
    float coc = (-coc_min > coc_max ? coc_min : coc_max) * _MainTex_TexelSize.y;

    //premultiply coc
    avg *= smoothstep(0, _MainTex_TexelSize.y * 2, abs(coc));

    _DOFDst[id.xy] =  float4(avg, coc);
}


//===Bokeh Filter(Medium Blur kernel)===
//INPUT:_DOFMap
//OUTPUT:_DOFTempMap
[numthreads(GROUP_SIZE_X, GROUP_SIZE_Y, 1)]
void CSBlur(uint3 id : SV_DISPATCHTHREADID)
{
    float2 uv = float2((float)id.x + 0.5, (float)id.y + 0.5) * _MainTex_TexelSize.xy;

    float4 sampleCenter = _DOFMap.SampleLevel(ce_Sampler_Point, uv, 0.0);

    float4 bgAcc = 0.0; // Background: far field bokeh
    float4 fgAcc = 0.0; // Foreground: near field bokeh

    for (int i = 0; i < kSampleCount; i++)
    {
        float2 disp = kDiskKernel[i] * _MaxCoC;
        float dist = length(disp);

        float2 duv = float2(disp.x * _RcpAspect, disp.y);
        float4 samp = _DOFMap.SampleLevel(ce_Sampler_Point, uv + duv, 0.0);

        // BG: Compare CoC of the current sample and the center sample
        // and select smaller one.
        float bgCoC = max(min(sampleCenter.a, samp.a), 0.0);

        // Compare the CoC to the sample distance.
        // Add a small margin to smooth out.
        const float margin = _MainTex_TexelSize.y * 2;
        float bgWeight = saturate((bgCoC - dist + margin) / margin);
        float fgWeight = saturate((-samp.a - dist + margin) / margin);

        // Cut influence from focused areas because they're darkened by CoC
        // premultiplying. This is only needed for near field.
        fgWeight *= step(_MainTex_TexelSize.y, -samp.a); //step(a,x)=(x>a?1:0)

        // Accumulation
        bgAcc += float4(samp.rgb, 1.0) * bgWeight;
        fgAcc += float4(samp.rgb, 1.0) * fgWeight;
    }

    // Get the weighted average.
    bgAcc.rgb /= bgAcc.a + (bgAcc.a == 0.0); // zero-div guard
    fgAcc.rgb /= fgAcc.a + (fgAcc.a == 0.0);

    // BG: Calculate the alpha value only based on the center CoC.
    // This is a rather aggressive approximation but provides stable results.
    bgAcc.a = smoothstep(_MainTex_TexelSize.y, _MainTex_TexelSize.y * 2.0, sampleCenter.a);

    // FG: Normalize the total of the weights.
    fgAcc.a *= PI / kSampleCount;

    // Alpha premultiplying
    float alpha = saturate(fgAcc.a);
    float3 rgb = lerp(bgAcc.rgb, fgAcc.rgb, alpha);

    _DOFTempDst[id.xy] = float4(rgb, alpha);
}

//===Postfilter blur===
//INPUT:_DOFTempMap
//OUTPUT:_DOFMap
[numthreads(GROUP_SIZE_X, GROUP_SIZE_Y, 1)]
void CSPostBlur(uint3 id : SV_DISPATCHTHREADID)
{
    float2 uv = float2((float)id.x + 0.5, (float)id.y + 0.5) * _MainTex_TexelSize.xy;
    // 9 tap tent filter with 4 bilinear samples
    const float4 duv = _MainTex_TexelSize.xyxy * float4(0.5, 0.5, -0.5, 0);
    float4 acc;
    acc = _DOFTempMap.SampleLevel(ce_Sampler_Point, uv - duv.xy, 0.0);
    acc += _DOFTempMap.SampleLevel(ce_Sampler_Point, uv - duv.zy, 0.0);
    acc += _DOFTempMap.SampleLevel(ce_Sampler_Point, uv + duv.zy, 0.0);
    acc += _DOFTempMap.SampleLevel(ce_Sampler_Point, uv + duv.xy, 0.0);
    _DOFDst[id.xy] = acc / 4.0;
}

//===Combine with source===
//INPUT:_DOFMap,_CoCTex,_MainTex
//OUTPUT:res
[numthreads(GROUP_SIZE_X, GROUP_SIZE_Y, 1)]
void CSCombine(uint3 id : SV_DISPATCHTHREADID)
{
    float2 uv = float2((float)id.x + 0.5, (float)id.y + 0.5) * _MainTex_TexelSize.xy;
    float4 dof = _DOFMap.SampleLevel(ce_Sampler_Point,uv, 0.0);
    float coc = _CoCMap.SampleLevel(ce_Sampler_Point, uv, 0.0).r;

    // Convert CoC to far field alpha value.
    float ffa = smoothstep(2.0, 4.0, abs(coc));

    float4 color = _MainMap.SampleLevel(ce_Sampler_Point, uv, 0.0);

    float alpha = Max3(dof.r, dof.g, dof.b);

    // lerp(lerp(color, dof, ffa), dof, dof.a)
    color = lerp(color, float4(dof.rgb, alpha), ffa);

    _OutputDst[id.xy] = color;
}
