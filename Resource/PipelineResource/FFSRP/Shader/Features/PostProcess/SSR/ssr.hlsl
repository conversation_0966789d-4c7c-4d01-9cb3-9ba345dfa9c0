
#define SSR_MINIMUM_ATTENUATION 0.275
#define SSR_ATTENUATION_SCALE (1.0-SSR_MINIMUM_ATTENUATION)
#define SSR_VIGNETTE_SMOOTHNESS 5.0
#define SSR_COLOR_NEIGHBORHOOD_SAMPLE_SPREAD 1.0
#define SSR_FINAL_BLEND_STATIC_FACTOR 0.95
#define SSR_FINAL_BLEND_DYNAMIC_FACTOR 0.7

#define SSR_ENABLE_CONTACTS 0
#define SSR_KILL_FIREFLIES 0

#include"cg.hlsl"

struct Ray
{
    float3 origin;
    float3 dir;
};

struct Segment
{
    float3 start;
    float3 end;
    float3 dir;
};

struct Result
{
    bool isHit;
    float2 uv;
    int iterCount;
};

//Texture2D<float> _SSRMaskMap : register(space0);
//Texture2D<float4> ce_Scene_Depth : register(space0);

Texture2D<float4> _MainMap : register(space0);
Texture2D<float4> _NormalDepthMap : register(space0);
Texture2D<float4> _SpecularRMap : register(space0);
Texture2D<float4> _MarchTestMap : register(space0);
Texture2D<float4> _ResolveMap : register(space0);
TextureCube<float4> _ReflectionCubemap : register(space0);
//Texture2D<float4> _DiffuseMap : register(space0);
//Texture2D<float4> _SpecularMap : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);
SamplerState ce_Sampler_Point : register(space0);
SamplerState texture_sampler : register(space0);

cbuffer para : register(space0)
{
    matrix _InverseProjectionMatrix;
    matrix _InverseViewMatrix;
    //matrix _ScreenSpaceProjectionMatrix;
    matrix _ProjectionMatrix;
    //matrix _ViewMatrix;
    float4 _ProjectionParams;         // x: 1 (-1 flipped), y: near,     z: far,       w: 1/far
    float4 _MarchTest_TexelSize;      //1/width, 1/height, width, height
    float4 _MainTex_TexelSize;
	float3 _Params;                   // x: _AspectRatio,   y: _NoiseTiling  z:_BlurPyramidLODCount
    float3 _CameraPosWS;
    float  _CurrentMipLevel;
}

#define _AspectRatio _Params.x
#define _NoiseTiling _Params.y
#define _LODCount _Params.z

cbuffer mtl : register(space1)
{
	float _Attenuation;//0.25
	float _VignetteIntensity;
	float _DistanceFade;
	float _MaximumMarchDistance;
    float _Bandwidth;
    float _MaximumIterCount;
    float _Epsilon;
    float _BlurRadius;
    float _BlurGaussianSigma;
    float _SSRResolveIntensity;
    float _SSRThickness;
}
Texture2D<float4> _NoiseMap : register(space1);

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};


float LinearEyeDepth(float vDepth)
{
    /*float zParamZ = 1.0 / _ProjectionParams.z - 1.0 / _ProjectionParams.y;
    float zParamW = 1.0 / _ProjectionParams.y;
    return 1.0 / (zParamZ * z + zParamW);*/
    float u_Near = _ProjectionParams.y;
    float u_Far = _ProjectionParams.z;
    float z = vDepth * 2.0 - 1.0;
    return (2.0 * u_Near * u_Far) / (u_Far + u_Near - z * (u_Far - u_Near));
}

float LinearEyeDepth(float depth, float4x4 projMatrix)
{
    return (projMatrix[2][3] / (depth - projMatrix[2][2]));
}

float Linear01Depth(float z)
{
    return (LinearEyeDepth(z) - _ProjectionParams.y) / (_ProjectionParams.z - _ProjectionParams.y);
}

float SpecularStrength(float3 specular)
{
//#if (SHADER_TARGET < 30)
    // SM2.0: instruction count limitation
    // SM2.0: simplified SpecularStrength
    return specular.r; // Red channel - because most metals are either monocrhome or with redish/yellowish tint
//#else
    //return max(max(specular.r, specular.g), specular.b);
//#endif
}

//center:largest  corner:smallest
float Attenuate(float2 uv)
{
    float offset = min(1.0 - max(uv.x, uv.y), min(uv.x, uv.y));

    float result = offset / (SSR_ATTENUATION_SCALE * _Attenuation + SSR_MINIMUM_ATTENUATION);
    result = saturate(result);

    return pow(result, 0.5);
}

float Vignette(float2 uv)
{
    float2 k = abs(uv - 0.5) * _VignetteIntensity;
    k.x *= _MainTex_TexelSize.y * _MainTex_TexelSize.z;
    return pow(saturate(1.0 - dot(k, k)), _VignetteIntensity);
}

float3 GetViewSpacePosition(float2 uv, float depth)
{
    float4 result = mul(_InverseProjectionMatrix, float4(2.0 * uv - 1.0, depth, 1.0));
    return result.xyz / result.w;
}

float GetSquaredDistance(float2 first, float2 second)
{
    first -= second;
    return dot(first, first);
}

//float4 ProjectToScreenSpace(float3 position)
//{
//    return float4(
//        _ScreenSpaceProjectionMatrix[0][0] * position.x + _ScreenSpaceProjectionMatrix[0][2] * position.z,
//        _ScreenSpaceProjectionMatrix[1][1] * position.y + _ScreenSpaceProjectionMatrix[1][2] * position.z,
//        _ScreenSpaceProjectionMatrix[2][2] * position.z + _ScreenSpaceProjectionMatrix[2][3],
//        _ScreenSpaceProjectionMatrix[3][2] * position.z
//        );
//}

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

// Heavily adapted from McGuire and Mara's original implementation
// http://casual-effects.blogspot.com/2014/08/screen-space-ray-tracing.html
Result March(Ray ray, VS2PS input)
{
    Result result = (Result)0;
    result.isHit = false;

    float3 V0 = ray.origin;
    float magnitude = (ray.origin.z + ray.dir.z * _MaximumMarchDistance) < _ProjectionParams.y ? (_ProjectionParams.y - ray.origin.z) / ray.dir.z : _MaximumMarchDistance;
    float3 V1 = ray.origin + ray.dir * magnitude;

    float4 H0 = mul(_ProjectionMatrix,float4(V0, 1.0));
    float4 H1 = mul(_ProjectionMatrix,float4(V1, 1.0));

    float k0 = 1.0 / H0.w;
    float k1 = 1.0 / H1.w;

    float3 Q0 = V0 * k0;
    float3 Q1 = V1 * k1;

    //NDC
    float2 P0 = H0.xy * k0;
    float2 P1 = H1.xy * k1;

    //ScreenSpace
    P0 = (P0 + 1.0) / 2.0;
    P0.y = 1.0 - P0.y;
    P0*=_MarchTest_TexelSize.zw;
    P1 = (P1 + 1.0) / 2.0; 
    P1.y = 1.0 - P1.y;
    P1*=_MarchTest_TexelSize.zw;

    P1 += GetSquaredDistance(P0, P1) < 0.0001 ? max(_MarchTest_TexelSize.x, _MarchTest_TexelSize.y) : 0.0;
    float2 Delta = P1 - P0;

    bool isPermuted = false;
    if (abs(Delta.x) < abs(Delta.y))
    {
        isPermuted = true;
        Delta = Delta.yx; P0 = P0.yx; P1 = P1.yx;
    }

    //P:screenspace Q:viewspace
    float stepDir = sign(Delta.x);
    float invdx = stepDir / Delta.x;
    float3 dQ = (Q1 - Q0) * invdx;
    float dk = (k1 - k0) * invdx;
    float2 dP = float2(stepDir, Delta.y * invdx);
    float stride = 1.0 - min(1.0, ray.origin.z / _MaximumMarchDistance);
    float2 uv = input.UV * _NoiseTiling;
    uv.y *= _AspectRatio;
    float jitter = _NoiseMap.Sample(texture_sampler, uv + _CameraPosWS.xz, 0).r;
    stride = stride * jitter* _Bandwidth;
    
    dP *= stride;
    dQ *= stride;
    dk *= stride;

    P0 += dP;
    Q0 += dQ;
    k0 += dk;

    int step = 0;
    float k = k0;
    float3 Q = Q0;
    float prevZMaxEstimate = V0.z;

    for (float2 P = P0; step < _MaximumIterCount; step++, P += dP, Q.z += dQ.z, k += dk)
    {
        result.uv = (isPermuted ? P.yx : P)* _MarchTest_TexelSize.xy;
        if (any(result.uv < 0.0) || any(result.uv > 1.0))
        {
            result.uv = float2(0.8, 0.8);
            result.iterCount = step + 1;
            result.isHit = false;
            return result;
        }
        float2 depths;
        depths.x = prevZMaxEstimate;
        depths.y = (dQ.z * 0.5 + Q.z) / (dk*0.5 + k);

        if (depths.y > _MaximumMarchDistance)
        {
            result.uv = float2(0.3, 0.3);
            result.iterCount = step + 1;
            result.isHit = false;
            return result;
        }

        prevZMaxEstimate = depths.y;
        if (depths.x < depths.y)
            depths.xy = depths.yx;

        ////fix
        //float scale = (prevZMaxEstimate - V0.z) / ray.dir.z;
        //float3 target = V0 + ray.dir * scale;
        //float4 tH = mul(_ProjectionMatrix, float4(target, 1.0));
        //float2 tUV = tH.xy / tH.w;
        //tUV = (tUV + 1.0) / 2.0;
        //tUV.y = 1.0 - tUV.y;
        //result.uv = tUV;

        float4 enc = _NormalDepthMap.Sample(ce_Sampler_Point, result.uv);
        float d; float3 n;
        DecodeDepthNormal(enc, d, n);
        //float d = ce_Scene_Depth.Sample(ce_Sampler_Point, result.uv).r;
        float linearD = LinearEyeDepth(d, _ProjectionMatrix);// +_Epsilon * (_ProjectionParams.z - _ProjectionParams.y);
        //result.isHit = (depths.y <= linearD) && (depths.x >= linearD) && (step > 0);
        result.isHit = (depths.x - _SSRThickness <= linearD) && (depths.x >= linearD) && (step > 0);
        if (result.isHit) {
            result.iterCount = step + 1;
            break;
        }
    }
    return result;
}

float InterleavedGradientNoise(float2 pixCoord, int frameCount)
{
    const float3 magic = float3(0.06711056f, 0.00583715f, 52.9829189f);
    float2 frameMagicScale = float2(2.083f, 4.867f);
    pixCoord += frameCount * frameMagicScale;
    return frac(magic.z * frac(dot(pixCoord, magic.xy)));
}

//Input:_NormalDepthMap
//Output:_MarchTestMap
float4 PSMarchTest(VS2PS input) : SV_TARGET
{
   /* float mask = _SSRMaskMap.Sample(ce_Sampler_Point, input.UV).r;
    if (mask < 1.0)
    {
        return 0.0;
    }*/

    float4 enc = _NormalDepthMap.Sample(ce_Sampler_Point, input.UV);
    float depth; float3 normal;
    /*normal = enc.xyz;
    depth = enc.w;*/
    DecodeDepthNormal(enc, depth, normal);
    normal = normalize(normal);
    //normal = mul(_ViewMatrix,float4(normal,0)).xyz;

    //view space
    Ray ray = (Ray)0;
    ray.origin = GetViewSpacePosition(float2(input.UV.x,1.0-input.UV.y), depth);
    ray.origin.z = LinearEyeDepth(depth, _ProjectionMatrix);
    ray.origin += InterleavedGradientNoise(input.UV, 0) * _Epsilon * normal;

    if (ray.origin.z > _MaximumMarchDistance)
        return 0.0;

    ray.dir = normalize(reflect(normalize(ray.origin), normal));

    /*if (ray.dir.z > 0.0)
        return 0.0;*/

    Result result = March(ray, input);
    float confidence = (float)result.iterCount / (float)_MaximumIterCount;
    return float4(result.uv, confidence, (float)result.isHit);   
}

half3 FresnelLerpFast(half3 F0, half3 F90, half cosA)
{
    half factor = 1 - cosA;
    half t = factor * factor* factor* factor;
    return lerp(F0, F90, t);
}

//input: _MarchTestMap, _MainMap
//output: _ResolveMap
float4 PSResolve(VS2PS input) : SV_TARGET
{
    float4 marchRes = _MarchTestMap.Sample(ce_Sampler_Point, input.UV);
    if (marchRes.w == 0.0)
    {
        return float4(0, 0, 0, 1);
    }

    float4 color = _MainMap.Sample(texture_sampler, marchRes.xy);
    float confidence = Attenuate(marchRes.xy)* Vignette(marchRes.xy);
    color.rgb *= confidence;
    color.rgb *= _SSRResolveIntensity;
    color.a = marchRes.z;
    //return color;
    //float4 color = _MainMap.Sample(texture_sampler, marchRes.xy);
    return color;
}

static const int kSampleCount = 16;
static const float2 kDiskKernel[kSampleCount] = {
    float2(0,0),
    float2(0.54545456,0),
    float2(0.16855472,0.5187581),
    float2(-0.44128203,0.3206101),
    float2(-0.44128197,-0.3206102),
    float2(0.1685548,-0.5187581),
    float2(1,0),
    float2(0.809017,0.58778524),
    float2(0.30901697,0.95105654),
    float2(-0.30901703,0.9510565),
    float2(-0.80901706,0.5877852),
    float2(-1,0),
    float2(-0.80901694,-0.58778536),
    float2(-0.30901664,-0.9510566),
    float2(0.30901712,-0.9510565),
    float2(0.80901694,-0.5877853),
};


//high resolution texelSize
float4 PSMipmap(VS2PS input) : SV_TARGET
{
    float4 specularR = _SpecularRMap.Sample(texture_sampler,input.UV);
    float2 duv = _MarchTest_TexelSize.xy;
    duv = duv * pow(2.0, _CurrentMipLevel);
    float4 resolveColor = 0;
    for (int index = 0; index <= kSampleCount; index++)
    {
        resolveColor += _ResolveMap.Sample(texture_sampler, input.UV + duv.xy * kDiskKernel[index]* specularR.a * 2.0 * _BlurRadius);
    }
    resolveColor /= kSampleCount;
    return resolveColor;
    //return _ResolveMap.Sample(texture_sampler, input.UV);
}

float4 PSCombine(VS2PS input) : SV_TARGET
{
    float4 enc = _NormalDepthMap.Sample(ce_Sampler_Point, input.UV);
    if (enc.r == 0.0 && enc.g == 0.0 && enc.b == 1.0 && enc.a == 0.0)
    {
        return _MainMap.Sample(texture_sampler, input.UV);
    }
    float depth; float3 normal;
    DecodeDepthNormal(enc, depth, normal);

    float3 position = GetViewSpacePosition(float2(input.UV.x,1.0-input.UV.y),depth);
    float3 eye = mul((float3x3)_InverseViewMatrix, normalize(position));//视线方向：WS
    normal = normalize(mul(_InverseViewMatrix, float4(normal, 0)));
    float nv = saturate(dot(normal, -eye));
    float4 specularR = _SpecularRMap.Sample(texture_sampler,input.UV);
    float3 specular = specularR.rgb;// 0.5;
    float smoothness = 1.0-specularR.a;// 0.5;
    float oneMinusReflectivity = 1.0 - SpecularStrength(specular);
    half grazingTerm = (half)saturate(smoothness + (1 - oneMinusReflectivity));

    float2 duv = _MarchTest_TexelSize.xy;// xy* float4(1.0, 1.0, -1.0, -1.0);
    float4 resolve=0;
    float lodLevel = specularR.a*(_LODCount-1)+1.0;
    duv = duv*pow(2.0, lodLevel);

    //resolve = _ResolveMap.SampleLevel(texture_sampler, input.UV + duv.zw, lodLevel) * 0.0947416;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + duv.xw, lodLevel) * 0.0947416;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + duv.zy, lodLevel) * 0.0947416;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + duv.xy, lodLevel) * 0.0947416;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + float2(duv.x, 0.0), lodLevel) * 0.118318;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + float2(duv.z, 0.0), lodLevel) * 0.118318;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + float2(0.0, duv.y), lodLevel) * 0.118318;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + float2(0.0, duv.w), lodLevel) * 0.118318;
    //resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV, lodLevel) * 0.147864;

    int radius = min((int)_BlurRadius* specularR.a,1);
    float sigma = _BlurGaussianSigma;
    float pi = 3.1415926;
    float sum = 0.0;
    float doubleSig2 = 2.0f * sigma * sigma;
    if (doubleSig2 == 0.0)doubleSig2 = 1.0;
    for (int x_i = -radius; x_i <= radius; x_i++)
    {
        for (int y_i = -radius; y_i <= radius; y_i++)
        {
            float weight = 1.0f / (doubleSig2 * pi) * exp(-(x_i * x_i + y_i * y_i) / doubleSig2);
            resolve += _ResolveMap.SampleLevel(texture_sampler, input.UV + duv.xy*float2(x_i, y_i), lodLevel) * weight;
            sum += weight;
        }
    }
    resolve /= sum;
    float3 dir = normalize(reflect(normalize(eye), normal));
    float4 cubemapColor = _ReflectionCubemap.Sample(texture_sampler,dir);
    float3 reflectColor = resolve.rgb + cubemapColor.rgb * (1.0 - resolve.a);
    /*half ssrMask = pow(resolve.a, 2);
    half4 cubemapColor = 0;
    half4 sceneColor = _MainMap.Sample(texture_sampler, input.UV);
    sceneColor.rgb = max(1e-5, sceneColor.rgb - cubemapColor.rgb);
    half4 reflectionColor = cubemapColor * (1 - ssrMask) + resolve * ssrMask;
    return sceneColor + reflectionColor;*/
    return _MainMap.Sample(texture_sampler, input.UV) + float4(reflectColor * (1.0-_DistanceFade),0.0);
}

float4 PSCombineComplex(VS2PS input) : SV_TARGET
{
    float4 enc = _NormalDepthMap.Sample(ce_Sampler_Clamp, input.UV);
    float depth; float3 normal;
    DecodeDepthNormal(enc, depth, normal);
    if (Linear01Depth(depth) > 0.999)
        return _MainMap.Sample(texture_sampler, input.UV);

    //TODO:--sample gbuffer
    //float3 diffuse = _DiffuseMap.Sample(texture_sampler, input.UV).rgb;
    //float3 specular = _SpecularMap.Sample(texture_sampler, input.UV).rgb;
    float3 specular = 0.1;
    float oneMinusReflectivity = 1.0 - SpecularStrength(specular);

    float3 position = GetViewSpacePosition(float2(input.UV.x,1.0 - input.UV.y),depth);

    float3 eye = mul((float3x3)_InverseViewMatrix, normalize(position));//视线方向：WS
    normal = normalize(mul(_InverseViewMatrix, float4(normal, 0)));
    position = mul(_InverseViewMatrix, float4(position, 1.0)).xyz;//像素位置坐标点世界位置

    //TODO:--sample gbuffer
    float smoothness = 0.8;
    //TODO:---根据smoothness采样resolve
    float lodLevel = 3.0;
    float4 resolve = _ResolveMap.SampleLevel(texture_sampler, input.UV, lodLevel);
    float perceptualRoughness = 1.0 - smoothness;
    float roughness = perceptualRoughness * perceptualRoughness;

    float nv = saturate(dot(normal, -eye));

    //Gamma space
    float surfaceReduction = 0.28;
    surfaceReduction = 1.0 - roughness * perceptualRoughness * surfaceReduction;

    half grazingTerm = (half)saturate(smoothness + (1 - oneMinusReflectivity));
    resolve.rgb = surfaceReduction * resolve.rgb * FresnelLerpFast(specular, half3(grazingTerm, grazingTerm, grazingTerm), nv);

    //TODO:--采样probe
    //float4 reflectionProbes = _CameraReflectionsTexture.Sample(sampler_CameraReflectionsTexture, input.UV);
    float4 reflectionProbes = _MainMap.Sample(texture_sampler, input.UV);
    float4 color = _MainMap.Sample(texture_sampler, input.UV);
    color.rgb = max(0.0, color.rgb - reflectionProbes.rgb);

    resolve.a *= 2. * resolve.a; // 2 and 1.5 are quite important for the correct ratio of 3:2 distribution
    float fade = 1.0 - saturate(1.5 * resolve.a * smoothstep(0.5, 1.0, 1.5 * resolve.a) * _DistanceFade);

    //dot(reflect dir, eye dir) is larger, confidence is larger
    float confidence = saturate(2.0 * dot(-eye, normalize(reflect(-eye, normal))));
    resolve.rgb = lerp(reflectionProbes.rgb, resolve.rgb, confidence * fade);
    color.rgb += resolve.rgb;

    return color;

    //return  _ResolveMap.SampleLevel(texture_sampler, input.UV, 0.0);
}