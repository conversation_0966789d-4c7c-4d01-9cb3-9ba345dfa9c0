#include "../../ShaderLibrary/Common.hlsl"
#include "../../ShaderLibrary/MotionVector.hlsl"

#pragma vertex VSMain
#pragma pixel PSMain

#define USE_YCOCG 1

SHADER_CONST(bool, MOTION_VECTOR, true);
SHADER_CONST(bool, ENABLE_AUTO_EXPOSURE, false);

Texture2D<float4> SceneColor : register(space0);
Texture2D<float> DepthMap : register(space0);
Texture2D<float2> VelocityBuffer : register(space0);
Texture2D<float4> HistoryBuffer : register(space0);
Texture2D<float4> ExposureTexture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);
SamplerState ce_Sampler_Point : register(space0);

cbuffer PassConstants : register(space0)
{
	float4x4 ReprojectionMat;
	float2 JitterOffset;
	float FrameTime;
	float CurrentFrameWeight;
}

struct PSInput
{
    float4 position : SV_Position;
    float2 uv : TexCoord0;
};

float3 Min(float3 a, float3 b)
{
	return float3(min(a.x, b.x), min(a.y, b.y), min(a.z, b.z));
}

float3 Max(float3 a, float3 b)
{
	return float3(max(a.x, b.x), max(a.y, b.y), max(a.z, b.z));
}

float3 RGBToYCoCg(float3 rgb)
{
	float y  = dot(rgb, float3(  1.f, 2.f,  1.f ));
	float co = dot(rgb, float3(  2.f, 0.f, -2.f ));
	float cg = dot(rgb, float3( -1.f, 2.f, -1.f ));
	return float3(y, co, cg);
}

float3 YCoCgToRGB(float3 yCoCg)
{
	float y  = yCoCg.x * .25f;
	float co = yCoCg.y * .25f;
	float cg = yCoCg.z * .25f;

	float r = y + co - cg;
	float g = y + cg;
	float b = y - co - cg;

	return float3(r, g, b);
}

float Luma(float3 color)
{
	return dot(float3(0.2126f, 0.7152f, 0.0722f), color);
}

float HdrColorWeight(float luma, float exposure)
{
	return rcp(luma * exposure + 1.f);
}

float HdrColorWeight(float3 color, float exposure)
{
#if USE_YCOCG
	return rcp(color.x * exposure + 1.f);
#else
	return rcp(Luma(color) * exposure + 1.f);
#endif
}

float ClipHistory(float3 history, float3 filtered, float3 neighborMin, float3 neighborMax)
{
	float3 boxMin = neighborMin;
	float3 boxMax = neighborMax;

	float3 rayOrigin = history;
	float3 rayDir = filtered - history;
	rayDir = select(abs(rayDir) < (1.f / 65536.f) , (1.f / 65536.f) , rayDir);
	float3 invRayDir = rcp(rayDir);

	float3 minIntersect = (boxMin - rayOrigin) * invRayDir;
	float3 maxIntersect = (boxMax - rayOrigin) * invRayDir;
	float3 enterIntersect = min(minIntersect, maxIntersect);
	return max(enterIntersect.x, max(enterIntersect.y, enterIntersect.z));
}

float2 LerpWeightedFactors(float weightA, float weightB, float blend)
{
	float blendA = (1.f - blend) * weightA;
	float blendB =        blend  * weightB;
	float rcpBlend = rcp(blendA + blendB);
	blendA *= rcpBlend;
	blendB *= rcpBlend;
	return float2(blendA, blendB);
}

float ApplyTimeConstant(float blend)
{
	return 1.f - exp(-FrameTime * rcp(saturate(1.f - blend)));
}

float4 SampleCatmullRom(Texture2D<float4> texture, SamplerState linearSampler, float2 uv, float2 texSize)
{
    float2 samplePos = uv * texSize;
    float2 texPos1 = floor(samplePos - 0.5f) + 0.5f;

    float2 f = samplePos - texPos1;
    float2 f2 = f * f;
    float2 f3 = f2 * f;

    float2 w0 = (1.0f / 6.0f) * (-3.0f * f3 +  6.0f * f2 - 3.0f * f);
    float2 w1 = (1.0f / 6.0f) * ( 9.0f * f3 - 15.0f * f2 + 6.0f);
    float2 w2 = (1.0f / 6.0f) * (-9.0f * f3 + 12.0f * f2 + 3.0f * f);
    float2 w3 = (1.0f / 6.0f) * ( 3.0f * f3 -  3.0f * f2);

    float2 w12 = w1 + w2;
    float2 offset12 = w2 / (w1 + w2);

    float2 texPos0 = texPos1 - 1.f;
    float2 texPos3 = texPos1 + 2.f;
    float2 texPos12 = texPos1 + offset12;

    texPos0 /= texSize;
    texPos3 /= texSize;
    texPos12 /= texSize;

    float4 result0 = texture.SampleLevel(linearSampler, float2(texPos0.x,  texPos0.y), 0.f);
	float weight0 = w0.x * w0.y * rcp(Luma(result0.rgb) + 1.f);

    float4 result1 = texture.SampleLevel(linearSampler, float2(texPos12.x, texPos0.y), 0.f);
	float weight1 = w12.x * w0.y * rcp(Luma(result1.rgb) + 1.f);

    float4 result2 = texture.SampleLevel(linearSampler, float2(texPos3.x,  texPos0.y), 0.f);
	float weight2 = w3.x  * w0.y * rcp(Luma(result2.rgb) + 1.f);

    float4 result3 = texture.SampleLevel(linearSampler, float2(texPos0.x,  texPos12.y), 0.f);
	float weight3 = w0.x  * w12.y * rcp(Luma(result3.rgb) + 1.f);

    float4 result4 = texture.SampleLevel(linearSampler, float2(texPos12.x, texPos12.y), 0.f);
	float weight4 = w12.x * w12.y * rcp(Luma(result4.rgb) + 1.f);

    float4 result5 = texture.SampleLevel(linearSampler, float2(texPos3.x,  texPos12.y), 0.f);
	float weight5 = w3.x  * w12.y * rcp(Luma(result5.rgb) + 1.f);

    float4 result6 = texture.SampleLevel(linearSampler, float2(texPos0.x,  texPos3.y), 0.f);
	float weight6 = w0.x  * w3.y * rcp(Luma(result6.rgb) + 1.f);

    float4 result7 = texture.SampleLevel(linearSampler, float2(texPos12.x, texPos3.y), 0.f);
	float weight7 = w12.x * w3.y * rcp(Luma(result7.rgb) + 1.f);

    float4 result8 = texture.SampleLevel(linearSampler, float2(texPos3.x,  texPos3.y), 0.f);
	float weight8 = w3.x  * w3.y * rcp(Luma(result8.rgb) + 1.f);

	float4 result = 0.f;
	float weightSum = 0.f;
	result += result0 * weight0;
	result += result1 * weight1;
	result += result2 * weight2;
	result += result3 * weight3;
	result += result4 * weight4;
	result += result5 * weight5;
	result += result6 * weight6;
	result += result7 * weight7;
	result += result8 * weight8;
	weightSum += weight0;
	weightSum += weight1;
	weightSum += weight2;
	weightSum += weight3;
	weightSum += weight4;
	weightSum += weight5;
	weightSum += weight6;
	weightSum += weight7;
	weightSum += weight8;
	result /= weightSum;

    return abs(result);
}

float3 TransformSceneColor(float3 linearSceneColor)
{
#if USE_YCOCG
	return RGBToYCoCg(linearSceneColor.rgb);
#else
	return linearSceneColor;
#endif
}

float3 TransformBackToLinearSceneColor(float3 sceneColor)
{
#if USE_YCOCG
	return YCoCgToRGB(sceneColor.xyz);
#else
	return sceneColor;
#endif
}

float GetSceneColorLuma(float3 sceneColor)
{
#if USE_YCOCG
	return sceneColor.x;
#else
	return Luma(sceneColor);
#endif	
}

float GetSceneColorHdrWeight(float luma)
{
	float exposure = ENABLE_AUTO_EXPOSURE ? ExposureTexture.Load(0).x : 1.f;
	return HdrColorWeight(luma, exposure);
}

float GetSceneColorHdrWeight(float3 sceneColor)
{
	float exposure = ENABLE_AUTO_EXPOSURE ? ExposureTexture.Load(0).x : 1.f;
	return HdrColorWeight(sceneColor, exposure);
}

float3 ClampHistory(float3 filtered, float3 historyColor, float3 neighborMin, float3 neighborMax)
{
	float clipBlend = ClipHistory(historyColor, filtered, neighborMin, neighborMax);
	return lerp(historyColor, filtered, saturate(clipBlend));
}

PSInput VSMain(uint vertexID : SV_VertexID)
{
    PSInput output = (PSInput)0;

    output.uv = float2((vertexID << 1U) & 2U, vertexID & 2U);
	output.position = float4(output.uv * float2(2.f, -2.f) + float2(-1.f, 1.f), 0.f, 1.f);

    return output;
}

float4 PSMain(PSInput input) : SV_TARGET
{
	float4 textureSize;
	SceneColor.GetDimensions(textureSize.x, textureSize.y);
	textureSize.zw = rcp(textureSize.xy);

	float4 sceneColor = SampleCatmullRom(SceneColor, ce_Sampler_Clamp, input.uv + JitterOffset * .5f, textureSize.xy);
	//sceneColor = SceneColor.SampleLevel(ce_Sampler_Point, input.uv, 0.f);
	float3 transformedSceneColor = TransformSceneColor(sceneColor.rgb);
	float3 minColor = transformedSceneColor.rgb;
	float3 maxColor = transformedSceneColor.rgb;

	//return sceneColor;

	float3 m1 = 0.f;
	float3 m2 = 0.f;
	[unroll]
	for (int j = -1; j <= 1; j++)
	{
		[unroll]
		for (int i = -1; i <= 1; i++)
		{
			float3 color = SceneColor.SampleLevel(ce_Sampler_Point, input.uv + float2(i, j) * textureSize.zw, 0.f).rgb;
			color = TransformSceneColor(color);
			minColor = Min(minColor, color);
			maxColor = Max(maxColor, color);

			m1 += color;
			m2 += color * color;
		}
	}

	float3 mu = m1 / 9.f;
	float3 sigma = sqrt(abs(m2 / 9.f - mu * mu));
	float3 minc = mu - sigma * 1.25f;
	float3 maxc = mu + sigma * 1.25f;

	minColor = float3(max(minColor.x, minc.x), max(minColor.y, minc.y), max(minColor.z, minc.z));
	maxColor = float3(min(maxColor.x, maxc.x), min(maxColor.y, maxc.y), min(maxColor.z, maxc.z));

	float depth = DepthMap.SampleLevel(ce_Sampler_Point, input.uv, 0.f);
	float minDepth = depth;
	float maxDepth = depth;
	float2 depthSampleOffset = 0.f;
	[unroll]
	for (int j = -1; j <= 1; j++)
	{
		[unroll]
		for (int i = -1; i <= 1; i++)
		{
			if (j != 0 || i != 0)
			{
				float neighborDepth = DepthMap.SampleLevel(ce_Sampler_Point, input.uv + float2(i, j) * textureSize.zw, 0.f);
				if (depth < neighborDepth)
				{
					depth = neighborDepth;
					depthSampleOffset = float2(i, j);
				}

				minDepth = min(minDepth, depth);
				maxDepth = max(maxDepth, depth);
			}
		}
	}

	float2 lastUV;
	float2 lastNDC;
	float2 motionVector;

	float2 encodeV = VelocityBuffer.SampleLevel(ce_Sampler_Point, input.uv + depthSampleOffset * textureSize.zw, 0.f);
	if (MOTION_VECTOR && encodeV.x > 0)
	{
		motionVector = DecodeVelocityFromTexture(encodeV) * 0.5;
		motionVector *= float2(0.5, -0.5);
		lastUV = input.uv + motionVector;
		lastNDC = 2.f * lastUV - 1.f;
	}
	else
	{
		float2 velocityUV = input.position.xy * textureSize.zw;
		float4 positionNDC = float4((2.f * velocityUV - 1.f) * float2(1.f, -1.f), depth, 1.f);
		float4 lastPositionCS = mul(ReprojectionMat, positionNDC);
		lastNDC = lastPositionCS.xy / lastPositionCS.w;
		lastUV = lastNDC * float2(.5f, -.5f) + .5f;
		motionVector = lastUV - input.uv;
	}

	float4 historyColor;
	if (any(1.f < abs(lastNDC.xy)))
	{
		historyColor = float4(transformedSceneColor, 1.f);
	}
	else
	{
		historyColor = SampleCatmullRom(HistoryBuffer, ce_Sampler_Clamp, lastUV, textureSize.xy);
		//historyColor = HistoryBuffer.SampleLevel(ce_Sampler_Point, lastUV, 0.f);
		historyColor.rgb = TransformSceneColor(historyColor.rgb);
	}

	float lumaHistory = GetSceneColorLuma(historyColor.rgb);

	float2 back = motionVector * textureSize.xy;
	float speed = sqrt(dot(back, back));
	float3 clampedHistory = ClampHistory(transformedSceneColor, historyColor.xyz, minColor, maxColor);

	float lumaFiltered = GetSceneColorLuma(transformedSceneColor);
	float blendFactor = max(lerp(CurrentFrameWeight, .2f, speed / 40.f), saturate(.01f * lumaHistory / abs(lumaFiltered - lumaHistory)));
	float filterWeight = GetSceneColorHdrWeight(transformedSceneColor);
	float historyWeight = GetSceneColorHdrWeight(clampedHistory);
	float2 weights = LerpWeightedFactors(historyWeight, filterWeight, blendFactor);
	float3 blendedColor =  clampedHistory * weights.x + transformedSceneColor * weights.y;
	blendedColor = TransformBackToLinearSceneColor(blendedColor);

	blendedColor = -min(-blendedColor, 0.f);
	return float4(blendedColor, speed);
}
