Texture2D<float4> highRes_texture : register(space0);
Texture2D<float4> lowRes_texture: register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

[[vk::constant_id(0)]]
bool NeedHighColor = true;

cbuffer para : register(space0)
{
	// bool NeedHighColor;
	float2 TexSizeLow;
	float SampleScale;
	float KernelRadius;
	float Sigma;
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float GaussianWeight(float x, float sigma)
{
    float weight = 0.39894228 / sigma; // equivalent of "1 / sqrt(2 * π * sigma^2)"
    weight *= exp(-(x * x) / (2.0f * sigma * sigma));
    return weight;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
	
    float3 colorHigh = (float3)0;
	if (NeedHighColor)
	{
		colorHigh = highRes_texture.Sample(ce_Sampler_Clamp, input.UV).xyz;
	}

	float weight_sum = 0;
	float4 color_sum = (float4)0;

    for(int i = -KernelRadius; i <= KernelRadius; i++)
	{
		float weight = GaussianWeight(i, Sigma);
		weight_sum += weight;

		float2 d = TexSizeLow * i * SampleScale;
		color_sum += weight * lowRes_texture.Sample(ce_Sampler_Clamp, input.UV + d);
	}

	float3 colorLow = color_sum.rgb / weight_sum;

    return float4(colorHigh + colorLow, 1.f);
}