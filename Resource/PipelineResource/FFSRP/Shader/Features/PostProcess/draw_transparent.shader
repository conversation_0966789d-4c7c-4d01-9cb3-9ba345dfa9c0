Texture2D<float4> src_texture : register(space0);
Texture2D<float4> transparent : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);

cbuffer cbPass : register(space0)
{
	float4 src_dimension;
    //xy: direction, z: depth influnce
    float3 blur_dir;

}

cbuffer cbMtl:register(space1)
{
    float blur_depth_weight;
    float blur_strength;
}

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;
	return ret;
}

float4 PSMain(VS2PS input) : SV_TARGET
{
	float4 scene = src_texture.Sample(ce_Sampler_Clamp, input.UV);
	float4 trans = transparent.Sample(ce_Sampler_Clamp, input.UV);
    float3 color = lerp(scene.xyz, trans.xyz, trans.w);
	return float4(color, 1.f);
}
