Texture2D<float4> toSMAA_texture : register(space0);
SamplerState ce_Sampler_Clamp : register(space0);
SamplerState ce_Sampler_Point : register(space0);

cbuffer para : register(space0)
{
    float4 SMAA_RT_METRICS;
	bool useLuma;
	bool useColor;
}

#define SMAA_HLSL_4
#define SMAA_PRESET_HIGH
#include "SMAA.hlsl"

struct VS2PS
{
	float2 UV : 		TEXCOORD0;
	float4 Pos : 		SV_POSITION;
	float4 offset[3] :  TEXCOORD1;
};

void DrawRectangle(
	float4 InPosition,
	float2 InTexCoord,
	out float4 OutPosition,
	out float2 OutTexCoord)
{
	OutPosition = InPosition;
	OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
	OutPosition.xy *= float2(1, -1);
	OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
	VS2PS ret;
	float4 Outpos;
	float2 Outuv;
	DrawRectangle(
		Pos,
		uv,
		Outpos,
		Outuv);
	ret.Pos = Outpos;
	ret.UV = Outuv;

	ret.offset[0] = mad(SMAA_RT_METRICS.xyxy, float4(-1.0, 0.0, 0.0, -1.0), Outuv.xyxy);
    ret.offset[1] = mad(SMAA_RT_METRICS.xyxy, float4( 1.0, 0.0, 0.0,  1.0), Outuv.xyxy);
    ret.offset[2] = mad(SMAA_RT_METRICS.xyxy, float4(-2.0, 0.0, 0.0, -2.0), Outuv.xyxy);
	return ret;
}


float4 PSMain(VS2PS input) : SV_TARGET
{
	if (useLuma == 0)
		return float4(SMAALumaEdgeDetectionPS(input.UV, input.offset, toSMAA_texture), 0.0, 0.0);
	else if (useColor == 1)
		return float4(SMAAColorEdgeDetectionPS(input.UV, input.offset, toSMAA_texture), 0.0, 0.0);
	else 
		return float4(SMAADepthEdgeDetectionPS(input.UV, input.offset, toSMAA_texture), 0.0, 0.0);
}