// Copyright Epic Games, Inc. All Rights Reserved.
#ifndef SKYATMOSPHERE_COMMON
#define SKYATMOSPHERE_COMMON
#define RAYDPOS 0.00001f

#define CM2KM 1e-5f;
#define KM2CM 1e5f;

//#ifndef GROUND_GI_ENABLED
//#define GROUND_GI_ENABLED 0
//#endif
//#ifndef TRANSMITANCE_METHOD
//#define TRANSMITANCE_METHOD 2
//#endif
//#ifndef MULTISCATAPPROX_ENABLED 
//#define MULTISCATAPPROX_ENABLED 0 
//#endif
//#ifndef GAMEMODE_ENABLED 
//#define GAMEMODE_ENABLED 0 
//#endif
//#ifndef COLORED_TRANSMITTANCE_ENABLED 
//#define COLORED_TRANSMITTANCE_ENABLED 0 
//#endif
//#ifndef FASTSKY_ENABLED 
//#define FASTSKY_ENABLED 0 
//#endif
//#ifndef FASTAERIALPERSPECTIVE_ENABLED 
//#define FASTAERIALPERSPECTIVE_ENABLED 0 
//#endif
//#ifndef SHADOWMAP_ENABLED 
//#define SHADOWMAP_ENABLED 0 
//#endif
//#ifndef MEAN_ILLUM_MODE 
//#define MEAN_ILLUM_MODE 0 
//#endif

#define INDEX_NONE -1

#if 1
#define USE_CornetteShanks
#define MIE_PHASE_IMPORTANCE_SAMPLING 0
#else
// Beware: untested, probably faulty code path.
// Mie importance sampling is only used for multiple scattering. Single scattering is fine and noise only due to sample selection on view ray.
// A bit more expenssive so off for now.
#define MIE_PHASE_IMPORTANCE_SAMPLING 1
#endif

#define PLANET_RADIUS_OFFSET 0.005f

#ifndef LENGTH_PER_TILE
#define LENGTH_PER_TILE 65536.0

float3 GetLargeCoordinateReltvPosition(float3 posOffsetInModelTile, float3 modelTileInLargeWorld, float3 orgTileInLargeWorld)
{
    return posOffsetInModelTile + (modelTileInLargeWorld - orgTileInLargeWorld) * LENGTH_PER_TILE;
}

float3 GetLargeCoordinateModelPosition(float3 posOffsetInOrgTile, float3 modelTileInLargeWorld, float3 orgTileInLargeWorld)
{
    return posOffsetInOrgTile + (orgTileInLargeWorld - modelTileInLargeWorld) * LENGTH_PER_TILE;
}

float3 GetLargeCoordinateAbsolutePosition(float3 RelativePosition, float3 TileIndex)
{
    return RelativePosition + TileIndex * LENGTH_PER_TILE;
}

float2 GetLargeCoordinateAbsolutePosition(float2 RelativePosition, float2 TileIndex)
{
    return RelativePosition + TileIndex * LENGTH_PER_TILE;
}
#endif

struct RaySA
{
	float3 o;
	float3 d;
};

float3 ToUEVec(float3 ceVec)
{
	return float3(ceVec.x, -ceVec.z, ceVec.y);
}
float3 ToCEVec(float3 ueVec)
{
	return float3(ueVec.x, ueVec.z, -ueVec.y);
}

#define AP_SLICE_COUNT CAMERA_VOLUME_LUT_DEPTH
#define AP_KM_PER_SLICE 6.0f

float AerialPerspectiveDepthToSlice(float depth)
{
	return depth * (1.0f / AP_KM_PER_SLICE);
}
float AerialPerspectiveSliceToDepth(float slice)
{
	return slice * AP_KM_PER_SLICE;
}

RaySA createRay(in float3 p, in float3 d)
{
    RaySA r;
	r.o = p;
	r.d = d;
	return r;
}

/**
 * Returns near intersection in x, far intersection in y, or both -1 if no intersection.
 * RayDirection does not need to be unit length.
 */
float2 RayIntersectSphere(float3 RayOrigin, float3 RayDirection, float4 Sphere)
{
    float3 LocalPosition = RayOrigin - Sphere.xyz;
    float LocalPositionSqr = dot(LocalPosition, LocalPosition);

    float3 QuadraticCoef;
    QuadraticCoef.x = dot(RayDirection, RayDirection);
    QuadraticCoef.y = 2 * dot(RayDirection, LocalPosition);
    QuadraticCoef.z = LocalPositionSqr - Sphere.w * Sphere.w;

    float Discriminant = QuadraticCoef.y * QuadraticCoef.y - 4 * QuadraticCoef.x * QuadraticCoef.z;

    float2 Intersections = -1;

    // Only continue if the ray intersects the sphere
    if (Discriminant >= 0)
    {
        float SqrtDiscriminant = sqrt(Discriminant);
        Intersections = (-QuadraticCoef.y + float2(-1, 1) * SqrtDiscriminant) / (2 * QuadraticCoef.x);
    }

    return Intersections;
}

// - r0: ray origin
// - rd: normalized ray direction
// - s0: sphere center
// - sR: sphere radius
// - Returns distance from r0 to first intersecion with sphere,
//   or -1.0 if no intersection.
float raySphereIntersectNearest(float3 r0, float3 rd, float3 s0, float sR)
{
    float2 Sol = RayIntersectSphere(r0, rd, float4(s0, sR));
    float Sol0 = Sol.x;
    float Sol1 = Sol.y;
    if (Sol0 < 0.0f && Sol1 < 0.0f)
    {
        return -1.0f;
    }
    if (Sol0 < 0.0f)
    {
        return max(0.0f, Sol1);
    }
    else if (Sol1 < 0.0f)
    {
        return max(0.0f, Sol0);
    }
    return max(0.0f, min(Sol0, Sol1));
}

/**
 * Returns near intersection in x, far intersection in y, or both -1 if no intersection.
 * RayDirection does not need to be unit length.
 */
float2 rayIntersectSphere(float3 RayOrigin, float3 RayDirection, float4 Sphere)
{
	float3 LocalPosition = RayOrigin - Sphere.xyz;
	float LocalPositionSqr = dot(LocalPosition, LocalPosition);

	float3 QuadraticCoef;
	QuadraticCoef.x = dot(RayDirection, RayDirection);
	QuadraticCoef.y = 2 * dot(RayDirection, LocalPosition);
	QuadraticCoef.z = LocalPositionSqr - Sphere.w * Sphere.w;

	float Discriminant = QuadraticCoef.y * QuadraticCoef.y - 4 * QuadraticCoef.x * QuadraticCoef.z;

	float2 Intersections = -1;

	// Only continue if the ray intersects the sphere
	
	if (Discriminant >= 0)
	{
		float SqrtDiscriminant = sqrt(Discriminant);
		Intersections = (-QuadraticCoef.y + float2(-1, 1) * SqrtDiscriminant) / (2 * QuadraticCoef.x);
	}

	return Intersections;
}

void LutTransmittanceParamsToUv(in AtmosphereParametersUE Atmosphere, in float viewHeight, in float viewZenithCosAngle, out float2 uv)
{
	float H = sqrt(max(0.0f, Atmosphere.TopRadius * Atmosphere.TopRadius - Atmosphere.BottomRadius * Atmosphere.BottomRadius));
	float rho = sqrt(max(0.0f, viewHeight * viewHeight - Atmosphere.BottomRadius * Atmosphere.BottomRadius));

	float discriminant = viewHeight * viewHeight * (viewZenithCosAngle * viewZenithCosAngle - 1.0) + Atmosphere.TopRadius * Atmosphere.TopRadius;
	float d = max(0.0, (-viewHeight * viewZenithCosAngle + sqrt(discriminant))); // Distance to atmosphere boundary

	float d_min = Atmosphere.TopRadius - viewHeight;
	float d_max = rho + H;
	float x_mu = (d - d_min) / (d_max - d_min);
	float x_r = rho / H;

	uv = float2(x_mu, x_r);
	//uv = float2(fromUnitToSubUvs(uv.x, TRANSMITTANCE_TEXTURE_WIDTH), fromUnitToSubUvs(uv.y, TRANSMITTANCE_TEXTURE_HEIGHT)); // No real impact so off
}

// We should precompute those terms from resolutions (Or set resolution as #defined constants)
float fromUnitToSubUvs(float u, float resolution) { return (u + 0.5f / resolution) * (resolution / (resolution + 1.0f)); }
float fromSubUvsToUnit(float u, float resolution) { return (u - 0.5f / resolution) * (resolution / (resolution - 1.0f)); }

void UvToLutTransmittanceParams(in AtmosphereParametersUE Atmosphere, out float viewHeight, out float viewZenithCosAngle, in float2 uv)
{
	//uv = float2(fromSubUvsToUnit(uv.x, TRANSMITTANCE_TEXTURE_WIDTH), fromSubUvsToUnit(uv.y, TRANSMITTANCE_TEXTURE_HEIGHT)); // No real impact so off
	float x_mu = uv.x;
	float x_r = uv.y;

	float H = sqrt(Atmosphere.TopRadius * Atmosphere.TopRadius - Atmosphere.BottomRadius * Atmosphere.BottomRadius);
	float rho = H * x_r;
	viewHeight = sqrt(rho * rho + Atmosphere.BottomRadius * Atmosphere.BottomRadius);

	float d_min = Atmosphere.TopRadius - viewHeight;
	float d_max = rho + H;
	float d = d_min + x_mu * (d_max - d_min);
	viewZenithCosAngle = d == 0.0 ? 1.0f : (H * H - rho * rho - d * d) / (2.0 * viewHeight * d);
	viewZenithCosAngle = clamp(viewZenithCosAngle, -1.0, 1.0);
}

#define NONLINEARSKYVIEWLUT 1
void UvToSkyViewLutParams(in AtmosphereParametersUE Atmosphere, out float3 ViewDir, in float viewHeight, in float2 uv)
{
	// Constrain uvs to valid sub texel range (avoid zenith derivative issue making LUT usage visible)
	uv = float2(fromSubUvsToUnit(uv.x, 192.0f), fromSubUvsToUnit(uv.y, 108.0f));

	float Vhorizon = sqrt(viewHeight * viewHeight - Atmosphere.BottomRadius * Atmosphere.BottomRadius);
	float CosBeta = Vhorizon / viewHeight;				// GroundToHorizonCos
	float Beta = acos(CosBeta);
	float ZenithHorizonAngle = PI_SA - Beta;

	float viewZenithAngle;
	if (uv.y < 0.5f)
	{
		float coord = 2.0*uv.y;
		coord = 1.0 - coord;
#if NONLINEARSKYVIEWLUT
		coord *= coord;
#endif
		coord = 1.0 - coord;
		viewZenithAngle = (ZenithHorizonAngle * coord);
	}
	else
	{
		float coord = uv.y*2.0 - 1.0;
#if NONLINEARSKYVIEWLUT
		coord *= coord;
#endif
		viewZenithAngle = (ZenithHorizonAngle + Beta * coord);
	}

	float CosViewZenithAngle = cos(viewZenithAngle);
	float SinViewZenithAngle = sqrt(1.0 - CosViewZenithAngle * CosViewZenithAngle) * (viewZenithAngle > 0.0f ? 1.0f : -1.0f); // Equivalent to sin(ViewZenithAngle)

	float LongitudeViewCosAngle = uv.x * 2.0f * PI_SA;

	// Make sure those values are in range as it could disrupt other math done later such as sqrt(1.0-c*c)
	float CosLongitudeViewCosAngle = cos(LongitudeViewCosAngle);
	float SinLongitudeViewCosAngle = sqrt(1.0 - CosLongitudeViewCosAngle * CosLongitudeViewCosAngle) * (LongitudeViewCosAngle <= PI_SA ? 1.0f : -1.0f); // Equivalent to sin(LongitudeViewCosAngle)
	ViewDir = float3(
		SinViewZenithAngle * CosLongitudeViewCosAngle,
		SinViewZenithAngle * SinLongitudeViewCosAngle,
		CosViewZenithAngle
		);
}

void SkyViewLutParamsToUv(in AtmosphereParametersUE Atmosphere, in bool IntersectGround, in float viewZenithCosAngle, in float3 ViewDir, in float viewHeight, out float2 uv)
{
	float Vhorizon = sqrt(viewHeight * viewHeight - Atmosphere.BottomRadius * Atmosphere.BottomRadius);
	float CosBeta = Vhorizon / viewHeight;				// GroundToHorizonCos
	float Beta = acos(CosBeta);
	float ZenithHorizonAngle = PI_SA - Beta;
    float ViewZenithAngle = acos(viewZenithCosAngle);

	if (!IntersectGround)
	{
		float coord = ViewZenithAngle / ZenithHorizonAngle;
		coord = saturate(1.0 - coord);
#if NONLINEARSKYVIEWLUT
		coord = sqrt(coord);
#endif
		coord = 1.0 - coord;
		uv.y = coord * 0.5f;
	}
	else
	{
		float coord = saturate((ViewZenithAngle - ZenithHorizonAngle) / Beta);
#if NONLINEARSKYVIEWLUT
		coord = sqrt(coord);
#endif
		uv.y = coord * 0.5f + 0.5f;
	}

	{
		uv.x = (atan2(-ViewDir.y, -ViewDir.x) + PI_SA) / (2.0f * PI_SA);
	}

	// Constrain uvs to valid sub texel range (avoid zenith derivative issue making LUT usage visible)
	uv = float2(fromUnitToSubUvs(uv.x, 192.0f), fromUnitToSubUvs(uv.y, 108.0f));
}


////////////////////////////////////////////////////////////
// Participating media
////////////////////////////////////////////////////////////



float getAlbedo(float scattering, float extinction)
{
	return scattering / max(0.001, extinction);
}
float3 getAlbedo(float3 scattering, float3 extinction)
{
	return scattering / max(0.001, extinction);
}


struct MediumSampleRGB
{
	float3 scattering;
	float3 absorption;
	float3 extinction;

	float3 scatteringMie;
	float3 absorptionMie;
	float3 extinctionMie;

	float3 scatteringRay;
	float3 absorptionRay;
	float3 extinctionRay;

	float3 scatteringOzo;
	float3 absorptionOzo;
	float3 extinctionOzo;

	float3 albedo;
};

MediumSampleRGB sampleMediumRGB(in float3 WorldPos, in AtmosphereParametersUE Atmosphere)
{
    const float SampleHeight = max(0.0, (length(WorldPos) - Atmosphere.BottomRadius));

    const float DensityMie = exp(Atmosphere.MieDensityExpScale * SampleHeight);

    const float DensityRay = exp(Atmosphere.RayleighDensityExpScale * SampleHeight);

    const float DensityOzo = SampleHeight < Atmosphere.AbsorptionDensity0LayerWidth ?
        saturate(Atmosphere.AbsorptionDensity0LinearTerm * SampleHeight + Atmosphere.AbsorptionDensity0ConstantTerm) :	// We use saturate to allow the user to create plateau, and it is free on GCN.
        saturate(Atmosphere.AbsorptionDensity1LinearTerm * SampleHeight + Atmosphere.AbsorptionDensity1ConstantTerm);

    MediumSampleRGB s;

    s.scatteringMie = DensityMie * Atmosphere.MieScattering.rgb;
    s.absorptionMie = DensityMie * Atmosphere.MieAbsorption.rgb;
    s.extinctionMie = DensityMie * Atmosphere.MieExtinction.rgb;

    s.scatteringRay = DensityRay * Atmosphere.RayleighScattering.rgb;
    s.absorptionRay = 0.0f;
    s.extinctionRay = s.scatteringRay + s.absorptionRay;

    s.scatteringOzo = 0.0f;
    s.absorptionOzo = DensityOzo * Atmosphere.AbsorptionExtinction.rgb;
    s.extinctionOzo = s.scatteringOzo + s.absorptionOzo;

    s.scattering = s.scatteringMie + s.scatteringRay + s.scatteringOzo;
    s.absorption = s.absorptionMie + s.absorptionRay + s.absorptionOzo;
    s.extinction = s.extinctionMie + s.extinctionRay + s.extinctionOzo;
    s.albedo = getAlbedo(s.scattering, s.extinction);

    return s;
}



////////////////////////////////////////////////////////////
// Sampling functions
////////////////////////////////////////////////////////////



// Generates a uniform distribution of directions over a sphere.
// Random zetaX and zetaY values must be in [0, 1].
// Top and bottom sphere pole (+-zenith) are along the Y axis.
float3 getUniformSphereSample(float zetaX, float zetaY)
{
	float phi = 2.0f * 3.14159f * zetaX;
	float theta = 2.0f * acos(sqrt(1.0f - zetaY));
	float3 dir = float3(sin(theta)*cos(phi), cos(theta), sin(theta)*sin(phi));
	return dir;
}

// Generate a sample (using importance sampling) along an infinitely long path with a given constant extinction.
// Zeta is a random number in [0,1]
float infiniteTransmittanceIS(float extinction, float zeta)
{
	return -log(1.0f - zeta) / extinction;
}
// Normalized PDF from a sample on an infinitely long path according to transmittance and extinction.
float infiniteTransmittancePDF(float extinction, float transmittance)
{
	return extinction * transmittance;
}

// Same as above but a sample is generated constrained within a range t,
// where transmittance = exp(-extinction*t) over that range.
float rangedTransmittanceIS(float extinction, float transmittance, float zeta)
{
	return -log(1.0f - zeta * (1.0f - transmittance)) / extinction;
}

float RayleighPhase(float cosTheta)
{
	float factor = 3.0f / (16.0f * PI_SA);
	return factor * (1.0f + cosTheta * cosTheta);
}

// [chopperlin] We restrict the denom's minimum in case g = 1 will cause nan problem in volumetric cloud
float CornetteShanksMiePhaseFunction(float g, float cosTheta)
{
	float k = 3.0 / (8.0 * PI_SA) * (1.0 - g * g) / (2.0 + g * g);
	return k * (1.0 + cosTheta * cosTheta) / pow(max(1.0 + g * g - 2.0 * g * -cosTheta, 1e-8), 1.5);
}
float hgPhase(float g, float cosTheta)
{
// #ifdef USE_CornetteShanks
//     return CornetteShanksMiePhaseFunction(g, cosTheta);
// #else
	// Reference implementation (i.e. not schlick approximation). 
	// See http://www.pbr-book.org/3ed-2018/Volume_Scattering/Phase_Functions.html
	float numer = 1.0f - g * g;
    float denom = 1.0f + g * g + 2.0f * g * cosTheta;
	return numer / (4.0f * PI_SA * denom * sqrt(denom));
// #endif
}

float dualLobPhase(float PhaseG, float PhaseG2, float w, float cosTheta)
{
    PhaseG = clamp(PhaseG, -0.999f, 0.999f);
    PhaseG2 = clamp(PhaseG2, -0.999f, 0.999f);
    // ref to UE's VolumetricCloud.usf line 329-338
    return lerp(hgPhase(PhaseG, -cosTheta), hgPhase(PhaseG2, -cosTheta), w);
}

float uniformPhase()
{
	return 1.0f / (4.0f * PI_SA);
}



////////////////////////////////////////////////////////////
// Misc functions
////////////////////////////////////////////////////////////



// From http://jcgt.org/published/0006/01/01/
void CreateOrthonormalBasis(in float3 n, out float3 b1, out float3 b2)
{
	float sign = n.z >= 0.0f ? 1.0f : -1.0f; // copysignf(1.0f, n.z);
	const float a = -1.0f / (sign + n.z);
	const float b = n.x * n.y * a;
	b1 = float3(1.0f + sign * n.x * n.x * a, sign * b, -sign * n.x);
	b2 = float3(b, sign + n.y * n.y * a, -n.y);
}

float mean(float3 v)
{
	return dot(v, float3(1.0f / 3.0f, 1.0f / 3.0f, 1.0f / 3.0f));
}

float whangHashNoise(uint u, uint v, uint s)
{
	uint seed = (u * 1664525u + v) + s;
	seed = (seed ^ 61u) ^ (seed >> 16u);
	seed *= 9u;
	seed = seed ^ (seed >> 4u);
	seed *= uint(0x27d4eb2d);
	seed = seed ^ (seed >> 15u);
	float value = float(seed) / (4294967296.0);
	return value;
}

bool MoveToTopAtmosphere(inout float3 WorldPos, in float3 WorldDir, in float AtmosphereTopRadius)
{
    float ViewHeight = length(WorldPos);
    if (ViewHeight > AtmosphereTopRadius)
    {
        float TTop = raySphereIntersectNearest(WorldPos, WorldDir, float3(0.0f, 0.0f, 0.0f), AtmosphereTopRadius);
        if (TTop >= 0.0f)
        {
            float3 UpVector = WorldPos / ViewHeight;
            float3 UpOffset = UpVector * -PLANET_RADIUS_OFFSET;
            WorldPos = WorldPos + WorldDir * TTop + UpOffset;
        }
        else
        {
            // Ray is not intersecting the atmosphere
            return false;
        }
    }
    return true; // ok to start tracing
}

float3 GetMultipleScattering(in AtmosphereParametersUE Atmosphere, float3 scattering, float3 extinction, float3 worlPos, float viewZenithCosAngle)
{
	float2 uv = saturate(float2(viewZenithCosAngle*0.5f + 0.5f, (length(worlPos) - Atmosphere.BottomRadius) / (Atmosphere.TopRadius - Atmosphere.BottomRadius)));
	uv = float2(fromUnitToSubUvs(uv.x, MultiScatteringLUTRes), fromUnitToSubUvs(uv.y, MultiScatteringLUTRes));

	float3 multiScatteredLuminance = _MultiScatTextureReadOnly.SampleLevel(ce_Sampler_Clamp, uv, 0).rgb;
	return multiScatteredLuminance;
}

float getShadow(in AtmosphereParametersUE Atmosphere, float3 P)
{
	//// First evaluate opaque shadow
	//float4 shadowUv = mul(gShadowmapViewProjMat, float4(P + float3(0.0, 0.0, -Atmosphere.BottomRadius), 1.0));
	////shadowUv /= shadowUv.w;	// not be needed as it is an ortho projection
	//shadowUv.x = shadowUv.x*0.5 + 0.5;
	//shadowUv.y = -shadowUv.y*0.5 + 0.5;
	//if (all(shadowUv.xyz >= 0.0) && all(shadowUv.xyz < 1.0))
	//{
	//	return ShadowmapTexture.SampleCmpLevelZero(samplerShadow, shadowUv.xy, shadowUv.z);
	//}
	return 1.0f;
}

float3x3 GetSkyViewLocalReferential(in float3 cameraForward, in float3 cameraPosition)
{
	float3 Up = normalize(cameraPosition);
	float3 Forward = cameraForward;
	float3 Left = normalize(cross(Forward, Up));

	const float DotMainDir = abs(dot(Up, Forward));
	if(DotMainDir > 0.999f)
	{		
		const float Sign = Up.z >= 0.0f ? 1.0f : -1.0f;
		const float a = -1.0f / (Sign + Up.z);
		const float b = Up.x * Up.y * a;
		Forward = float3( 1 + Sign * a * pow(Up.x, 2.0f), Sign * b, -Sign * Up.x );
		Left = float3(b,  Sign + a * pow(Up.y, 2.0f), -Up.y );
	}
	else
	{
		// This is better as it should be more stable with respect to camera forward.
		Forward = normalize(cross(Up, Left));
	}

    // return transpose(float3x3(
    //     0.9354, 0.3534, 0,
    //     0.3534, -0.9354, 0.001,
    //     0, 0.001, 1
    // ));
	
	return (float3x3(Forward, Left, Up));
}
#endif