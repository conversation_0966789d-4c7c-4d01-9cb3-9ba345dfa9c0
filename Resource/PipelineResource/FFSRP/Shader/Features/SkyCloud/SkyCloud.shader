#pragma keyword CE_USE_DOUBLE_TRANSFORM
#define DEBUG_STEP_CNT false

// #pragma enable debug_symbol

#include "SkyCloud_Template_Begin.hlsl"


cbuffer cbCloudCustom : register(space0)
{
    // Customized Uniforms
}

// [Not work now]Helper function to project 3D point onto a plane
float2 ProjectPointOntoPlane(float3 posMeter, float3 up)
{
    // Normalize the up vector
    up = normalize(up);
    
    // Create two perpendicular vectors in the plane
    float3 right = normalize(cross(up, float3(0, 0, 1)));
    if (all(right == 0)) // Handle case when up is parallel to (0,0,1)
        right = normalize(cross(up, float3(0, 1, 0)));
    float3 forward = cross(right, up);
    
    // Project the point onto these vectors to get 2D coordinates
    float2 projected;
    projected.x = dot(posMeter, right);
    projected.y = dot(posMeter, forward);
    
    return projected;
}

///  Template Volume Cloud Shader 
///  A template for Cloud Attribution Shader
//   The sampling point posMeter may exist in many cloud layers/thunderstorms, so we can't determine it's cloudLayerAtrribute before
// TODO we still need to align our shape with
CloudAttributeOut SampleCloudAttribute(in float3 posMeter, in CloudLayerAttribute cloudLayerAttrib, bool isThunderStorm, in float mip, in int cloud_layer_index = 0, in bool isShadow = false)
{
    CloudAttributeOut cloud_attrib_out = (CloudAttributeOut)0;

    CloudData cloudData = clouds_params[cloud_layer_index];

#if 0
    cloud_attrib_out.Density = 0.1f;
    return cloud_attrib_out;
#endif

    // TODO(yazhenyuan)
    float3 CamPosInKm = GetTiledCameraPosInKm();

    float3 posKm = posMeter * 0.001;

    // shadow's mip level should be the same with cloud ray marching
    float cosAngle = dot(normalize(CamPosInKm), normalize(posKm-CamPosInKm));
    float sinAngle = sqrt(1-cosAngle*cosAngle);
    float horizonDist = length(posKm-CamPosInKm) * sinAngle * 1000.f; // to unit m

    // Apply Cloud Wind Flow 
    posMeter = ApplyCloudWindSpeed(posMeter);

    // base cloud distortion but it seems not working
    //float3 base_dist_uv = posMeter * cloudData.base_curl_distortion_scale * float3(1, 1, 2);
    //posMeter += (TEXTURE_BIAS(ce_Sampler_Linear_Point_Wrap, TEX_CURL, (base_dist_uv), 0.f).xyz * 2.0f - 1.0f) * cloudData.base_cloud_distortion;

    // TODO wind skew


    float normalizeHeight = cloudLayerAttrib.SampleNormAltitudeInLayer;
    float kCoverage = cloudData.coverage;
    float cloudHeightRate = cloudLayerAttrib.CloudHeight;

    float3 uvScale  = float3(1,1,1);
    float4 nowUVW = CalculateUVW_EdgeFade(posMeter, uvScale);
    float2 sampleUv = nowUVW.xz;

#if 0 // not good too much noise!
    float2 projectedUv = ProjectPointOntoPlane(posMeter, normalize(CamPosInKm));
    sampleUv = projectedUv;
#endif


    // sample coverage
    float4 coverage = 0.xxxx;
#if 0
    if (isThunderStorm)
    {
        coverage.r = SampleStormDensity(posMeter, 0);
        coverage.g = cloudData.coverage_cloudness; // coverage_cloudness
        coverage.b = 1.f;
        coverage.r = pow(coverage.r, 0.5); // this is from curves
        
    } else
#endif    
    {
        if (fix_tiling)
        {
            // [Reduce aliasing of farview] Use 2 UV for further sampling, lerp according ray marching distance, just like 2-level LOD
            float3 uvScale2 = float3(cloudData.mip_uv_far_scale, 1, cloudData.mip_uv_far_scale);
            float4 nowUVW2 = CalculateUVW_EdgeFade(posMeter, uvScale2);
            float2 sampleUv2 = nowUVW2.xz;
            float2 gapDist = float2(cloudData.mip_near_ratio * CloudCutOffDistance, cloudData.mip_far_ratio * CloudCutOffDistance);
            float2 samplePara = (horizonDist < gapDist.y) ? sampleUv : sampleUv2;
            // In fact, inWeatherTexture will be replaced by static_coverage texture from component
            coverage = inWeatherTexture.SampleLevel(ce_Sampler_Repeat, samplePara, 0.f); // sample Cloud density
            if (horizonDist > gapDist.x && horizonDist < gapDist.y)
            {
                float4 coverage2 = inWeatherTexture.SampleLevel(ce_Sampler_Repeat, sampleUv2, mip);
                coverage = lerp(coverage, coverage2, remap(horizonDist, gapDist.x, gapDist.y, 0, 1));
            }
        }
        else
        {
            coverage = inWeatherTexture.SampleLevel(ce_Sampler_Repeat, sampleUv, 0.f);
        }    

        coverage.r = pow(coverage.r, kCoverage);
        coverage.g *= cloudData.coverage_cloudness;
        coverage.b = lerp(cloudData.coverage_height_remap, 1.0, coverage.b);
        coverage.rgb *= 1.f; //alpha, useful for 
        coverage.rgb *= 1.f; //region_mask

        coverage.g *= cloudData.coverage_cloudness;

    }

    coverage.b = lerpOne(coverage.b, coverage.g * 0.6f);

    float coverage_height_gradient = 1;
#if 0    
    if (!isThunderStorm)
#endif    
    {
        coverage_height_gradient = gradient4(normalizeHeight, lerp3(SG_CLOUD_NOISE_0, SG_CLOUD_NOISE_1, SG_CLOUD_NOISE_2, coverage.b));
    }

    if (coverage_height_gradient * coverage.r < 1e-6f)
        return cloud_attrib_out;

    // shadow density don't sample noise to reduce L2 burden
#if 0
     cloud_attrib_out.Density = coverage_height_gradient * coverage.r;
     return cloud_attrib_out;
#endif


    float noise = 0.f;

    float uvOffset = ce_Time.x * CloudNoiseFlow;

#if 0
    if (!isThunderStorm)
#endif
    float4 gradient_scalar = TEXTURE_CURVE_ATLAS(0, normalizeHeight);
    {
        // Attention: magic code from Unigine, don't try to change it
        cloud_attrib_out.hint = lerpFixed(0.0f, 0.8f, pow(coverage.r * coverage_height_gradient, 0.5f));

        float3 baseUVW = posKm * cloudData.base_size + WindDirection * uvOffset; //CalculateUVW_EdgeFade(posKm * 0.42 + WindDirection * uvOffset, 1.xxx);

        float4 low_noises = 0.xxxx;

        low_noises = TEX_BASE.SampleLevel(ce_Sampler_Linear_Point_Wrap, baseUVW, max(mip, 0.f));
        
        noise = dot(low_noises * gradient_scalar, 0.25f.xxxx) * coverage_height_gradient;

        noise = smoothstep(cloudData.noise_threshold, cloudData.noise_threshold + cloudData.noise_threshold_extent, noise);

        noise = saturate(noise - 1.0f + coverage.r) * coverage.r;

        // Attention: magic code from Unigine, don't try to change it
        if (cloud_attrib_out.hint > 0.5)
            cloud_attrib_out.hint += lerpFixed(0.0f, 0.2f, pow(noise, 0.5f));

    }
#if 0    
    else
    {
       
        // thunderstorm
        noise = coverage.r;
        // const is enough, thunderstorm noise threshold is different with cloud
        noise = smoothstep(0.67/*data.noise_threshold*/, 0.67 + 0.1/*data.noise_threshold + data.noise_threshold_extent*/, noise);

        // Attention: magic code from Unigine, don't try to change it
        cloud_attrib_out.hint = lerpFixed(0.0f, 1.0f, pow(noise - smoothstep(1.0f, 0.0f, noise) * cloudData.extra_noise_intensity * 0.5f, 0.1f));
        
    }
#endif    
   
    if (isShadow)
    {
        cloud_attrib_out.Density = noise;
        return cloud_attrib_out;
    }

    // extra noise here(optional)
    if (cloudData.extra_noise_size > 0.f)
    {
        float4 gradient_scalar2 = gradient_scalar; //TEXTURE_CURVE_ATLAS(0, normalizeHeight);
        float3 noise_sample_uvw2 = posKm * cloudData.extra_noise_size + WindDirection * uvOffset;
        float noise2 = dot((TEXTURE_BIAS(ce_Sampler_Linear_Point_Wrap, TEX_BASE, noise_sample_uvw2, 0.f)) * gradient_scalar2, 0.25f.xxxx) * coverage_height_gradient;

        noise2 = smoothstep(cloudData.extra_noise_threshold, cloudData.extra_noise_threshold + cloudData.extra_noise_threshold_extent, noise2);
        
        noise -= smoothstep(1.0f, 0.0f, noise) * cloudData.extra_noise_intensity * (1.0 - noise2);
    }

    if (noise < EPSILON)
        return cloud_attrib_out;
        
    if (noise > EPSILON)
    {
        // detail noise here
        float3 detailUVW = posKm * cloudData.detail_size + WindDirection * uvOffset;

        float3 dist_uv = posKm * cloudData.curl_distortion_scale * float3(1, 1, 2);
        
        float3 noise_detail_sample_uvw = posKm * cloudData.detail_size;// + (TEXTURE_BIAS(ce_Sampler_Linear_Point_Wrap, TEX_CURL, dist_uv, 0).xyz * 2.0f - 1.0f) * cloudData.cloud_distortion * normalizeHeight;

        float3 detail_sample = TEX_DETAIL.SampleLevel(ce_Sampler_Linear_Point_Wrap, noise_detail_sample_uvw, 0).xyz;

        // float3 detail_sample = TEXTURE_BIAS(TEX_DETAIL, detailUVW, 0).xyz;
        float detail_noise = dot(detail_sample, 0.333333f.xxxx);
        float detail_modifier = saturate(lerp(detail_noise, 1.0  - detail_noise, saturate(normalizeHeight * cloudData.detail_wispy_billowy_gradient)));
        
        noise -= smoothstep(1.0f, 0.0f, noise) * 0.5f * detail_modifier * cloudData.detail_affect * (1.f - coverage.g);
    }
#if 0
    // TODO: need to improve this
    if (!isThunderStorm)
#endif
        noise = saturate(noise * smoothstep(0.0, cloudData.cloud_bottom_fade * (1.0f - 0.6f * coverage.g), normalizeHeight)); 


    cloud_attrib_out.coverage = coverage;

    float cloud_density_scale = isThunderStorm ?  _StormData[0].Params.y : cloudData.density;
    cloud_attrib_out.Density = noise * cloud_density_scale;
    // mip pow to reduce density in far view
    float remap_mip_contrast = remap(horizonDist, cloudData.mip_far_ratio * CloudCutOffDistance, CloudCutOffDistance, 1, cloudData.mip_contrast);
    cloud_attrib_out.Density = saturate(pow(cloud_attrib_out.Density, remap_mip_contrast));

    cloud_attrib_out.hint = saturate(cloud_attrib_out.hint);

    return cloud_attrib_out;
}


CloudMaterial SampleCloudMaterial(in float3 posMeter, in CloudAttributeOut cloudAttrib , in CloudLayerAttribute cloudLayerAttrib, bool isForShadow)
{
    CloudMaterial cloudMat;
    cloudMat.Emission = float3(0.0, 0.0, 0.0);
    cloudMat.Albedo = float3(1.0f, 1.0f, 1.0f);
    cloudMat.Extinction = 1.0f;
    cloudMat.AO = 1.0f;

    return cloudMat;
}



/////////////
#include "RayMarching.hlsl"
#include "SkyCloud_Template_End.hlsl"


