#include "../../ShaderLibrary/Common.hlsl"
#include "../../ShaderLibrary/GlobalModelVariables.hlsl"

//#include "../SkyAtmosphere/UE/Interfaces.hlsl"

#include "../../Material/Lit/LitCommonStruct.hlsl"

#include "../../Features/Fog/FogCommon.hlsl"

cbuffer cbCloudMtl : register(space0)
{
    float4 RayMarchingTexSize;
    uint TempDownRatio;

    // for lightning lighting
    bool LightningEnabled;
    float3 LightningPosition;
    float3 LightningTilePosition;

    bool EnableSFog;
}

Texture2D<float>  DownsampleDepth        : register(space0);
Texture2D<float4> RayMarchingIdxAndDepth : register(space0);

#include "SkyCloudCommon.hlsl"
#include "SkyCloudMtl.hlsl"
