struct VS2PS
{
    float2 UV : TEXCOORD0;
    float4 Pos : SV_POSITION;
};

void DrawRectangle(
    float4 InPosition,
    float2 InTexCoord,
    out float4 OutPosition,
    out float2 OutTexCoord)
{
    OutPosition = InPosition;
    OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
    OutPosition.xy *= float2(1, -1);
    OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
    VS2PS ret;
    float4 Outpos;
    float2 Outuv;
    DrawRectangle(
    Pos,
    uv,
    Outpos,
    Outuv);
    ret.Pos = Outpos;
    ret.UV = Outuv;
    return ret;
}

struct PSOutput
{
    float4 colorTarget : SV_Target0;
    float2 depthTarget : SV_Target1;
};


PSOutput PSMain(VS2PS input)
{
    PSOutput psOutput;

     // cam pos to meter
    float3 camPos = ce_CameraPos.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
    camPos = GetLargeCoordinateModelPosition(camPos, float3(0.0, 0.0, 0.0), ce_CameraTilePosition);
#endif
    camPos *= 0.01f;

    int2 screen_coord = int2((input.UV) * RayMarchingTexSize.xy);
    int2 output_screen_coord = screen_coord;
    
    // 2 : temporal 4 frames
    if (TempDownRatio == 2)
    {
        //const uint2 s_interleaved_samples[4] = {uint2(0, 0), uint2(1, 1), uint2(0, 1), uint2(1, 0)};
        const uint INTERLEAVED_SIZE = TempDownRatio;
        float interleaved_frame_count = INTERLEAVED_SIZE * INTERLEAVED_SIZE;
        float ray_placement_noise = BlueNoiseTex2D.Load(int3(fmod(screen_coord, 256), 0)).x;
        const uint frame_selector = uint(ceFrameNumber + int(min(ray_placement_noise * interleaved_frame_count, interleaved_frame_count - 1.0f))) % int(interleaved_frame_count);

        screen_coord = screen_coord * INTERLEAVED_SIZE + s_interleaved_samples_2x2[frame_selector];
    } else if (TempDownRatio == 4)
    {
        const uint INTERLEAVED_SIZE = TempDownRatio;
        float interleaved_frame_count = INTERLEAVED_SIZE * INTERLEAVED_SIZE;
        float ray_placement_noise = BlueNoiseTex2D.Load(int3(fmod(screen_coord, 256), 0)).x;
        const uint frame_selector = uint(ceFrameNumber + int(min(ray_placement_noise * interleaved_frame_count, interleaved_frame_count - 1.0f))) % int(interleaved_frame_count);

        screen_coord = screen_coord * INTERLEAVED_SIZE + s_interleaved_samples_4x4[frame_selector];
    }


    float2 uv = float2(screen_coord) / float2(RayMarchingTexSize.xy * TempDownRatio - 1.xx);

    float scene_native_depth = DownsampleDepth.SampleLevel(ce_Sampler_Clamp, uv, 0).r;
    // In large world you should use GetWorldPositionFromUVDepth instead of GetLinearDepth, GetLinearDepth didn't consider double transform, will cause errors
    // when you move up camera, cloud nearby will disappear
    float3 posWorld = GetWorldPositionFromUVDepth(uv, scene_native_depth).xyz;

    float3 rayDir = posWorld - camPos;
    float rayLength = length(rayDir);

    float scene_depth = scene_native_depth != 0.0 ? rayLength : CloudCutOffDistance; //GetLinearDepth(scene_native_depth, uv) : CloudCutOffDistance;

    // sun lighting
    int dirLightIdx = GetDirectLightIdx();
    float3 sunDirection = normalize(-ce_Lights[dirLightIdx].LightDirPos.xyz);

    float3 s_lighting_samples[LIGHTING_NUM_SAMPLES];
    // TODO cone-sampled lighting not good enough
    GenerateConeSamples(sunDirection, s_lighting_samples);

    Ray ray;
    ray.pos = camPos;
    ray.dir = -screenUVToViewDirection(uv);

    // view direction and sunDirection is correct
    if (Debug_RayMarching_Option == Debug_ViewDirection)
    {
        float dotVL = dot(ray.dir, sunDirection);
        float dotVL_contrast = dotVL < 0.999 ? 0.f : dotVL;
        psOutput.colorTarget = float4(dotVL_contrast, 0, 0, 1);
        psOutput.depthTarget = float2(0, 0);
        return psOutput;
    }

    AtmosphereParametersUE atmosphere = GetAtmosphereParametersUE(ATMOSPHERE);
    float2 cloudDepth = 0.xx;
    float4 cloudResult =  RayMarchingMainAdaptiveSteps_New(ray, uv, cloudDepth, atmosphere, scene_depth, scene_native_depth, s_lighting_samples, sunDirection);   // unigine scene_depth is rayLength here
    psOutput.colorTarget = cloudResult;
    //save linear depth
    psOutput.depthTarget = cloudDepth;


    return psOutput;
}