#pragma compute UnderCloudTestCS
#include "./SkyCloudCommon.hlsl"

#include "../../Lighting/Shadow/CloudShadows.hlsl"

#define CE_USE_DOUBLE_TRANSFORM

RWStructuredBuffer<float> _OutUnderCloudTestResult;

cbuffer cbUnderCloudTest
{
    float3 worldPos;
}


// return 0~1, 0 for not under cloud, 1 for under cloud
// pos in meter
float UnderCloudTest(float3 pos, float3 upDir)
{
    return 1 - GetLowCloudShadow(pos, upDir);
}

[numthreads(1, 1, 1)]
void UnderCloudTestCS(uint3 id : SV_DISPATCHTHREADID)
{
    float3 pos = worldPos;
#ifdef CE_USE_DOUBLE_TRANSFORM
    pos.xyz = GetLargeCoordinateModelPosition(pos.xyz, float3(0.0, 0.0, 0.0), ce_CameraTilePosition);
#endif
    pos.xyz *= 0.01f;
    float3 upDir = normalize(pos);

    _OutUnderCloudTestResult[0] = max(UnderCloudTest(pos, upDir), UnderStormTest(pos, upDir));
    return;
}