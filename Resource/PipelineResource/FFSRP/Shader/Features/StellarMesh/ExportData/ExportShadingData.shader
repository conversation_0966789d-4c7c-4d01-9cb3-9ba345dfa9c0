#pragma vertex VSMain
#pragma pixel PSMain

#include "../Rasterize/VisibilityBuffer.hlsl"
#include "../SceneDataStellarMesh.hlsl"

struct VS2PS
{
    float2 UV  : TEXCOORD0;
    float4 Pos : SV_POSITION;
};

void DrawRectangle(
    float4 InPosition,
    float2 InTexCoord,
    out float4 OutPosition,
    out float2 OutTexCoord)
{
    OutPosition = InPosition;
    OutPosition.xy = -1.0f + 2.0f * InPosition.xy;
    OutPosition.y  = -OutPosition.y;
    OutTexCoord.xy = InTexCoord.xy;
}

VS2PS VSMain(float4 Pos : POSITION, float2 uv : TEXCOORD0)
{
    VS2PS ret;
    DrawRectangle(Pos, uv, ret.Pos, ret.UV);
    return ret;
}

struct PSOutput
{
    uint  shadingData : SV_Target0;
    // float depth       : SV_Depth;
};

StructuredBuffer<Cluster>          clusters;
StructuredBuffer<StellarMeshScene> instances;
StructuredBuffer<InstancedCluster> visibleClusters;
StructuredBuffer<uint>             materialSlots;
StructuredBuffer<StellarMeshMaterialScene> materialInstances;
Texture2D<uint> visibilityBuffer;
// Texture2D<float> depthBuffer;

MaterialSlot LoadMaterialSlot(uint materialOffset, uint materialIndexInCluster)
{
    uint data = materialSlots[materialOffset + materialIndexInCluster];
    MaterialSlot res;
    res.mShadingBin = data >> 16u;
    res.mRasterBin  = data  & 0xFFFFu;
    return res;
} 

uint GetMaterialShadingBin(Cluster cluster, uint instanceIndex, uint triangleIndex)
{
    uint materialIndexInCluster = GetMaterialIndexInCluster(cluster, triangleIndex);
    StellarMeshMaterialScene materialInfo = materialInstances[instanceIndex];
    return LoadMaterialSlot(materialInfo.mMaterialOffset, min(materialIndexInCluster, materialInfo.mMaterialCount - 1)).mShadingBin;
}

PSOutput PSMain(VS2PS input)
{
    PSOutput output;

    uint2 pixelPos = uint2(input.Pos.xy);
    uint visibilityPixel = visibilityBuffer[pixelPos];
    // float depth = depthBuffer[pixelPos];
    uint2 visibleClusterIndex_triangleIndex = DecodeClusterTriangleIndex(visibilityPixel);
    if (visibleClusterIndex_triangleIndex.x != 0xFFFFFFFF)
    {
        InstancedCluster visibleCluster = visibleClusters[visibleClusterIndex_triangleIndex.x];
        Cluster cluster = clusters[visibleCluster.mClusterIndex];
        StellarMeshScene instanceData = instances[visibleCluster.mInstanceIndex];
        uint shadingBin = GetMaterialShadingBin(cluster, visibleCluster.mInstanceIndex, visibleClusterIndex_triangleIndex.y);
        bool bReceiveDecal = (instanceData.ce_Flags & RECEIVE_DECAL) != 0;
        output.shadingData = PackShadingData(shadingBin, bReceiveDecal);
        // output.depth = 1.0f;
    }
    else
    {
        output.shadingData = 0;
        // output.depth = 0.0f;
        discard;
    }
    
    return output;
}
