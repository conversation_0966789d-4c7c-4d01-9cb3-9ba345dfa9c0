#ifndef STELLAR_MESH_UTIL_HLSL
#define STELLAR_MESH_UTIL_HLSL

#define STELLARMESH_DISPATCH_GROUP_SIZE_WARPPED 128
#define STELLARMESH_DISPATCH_THREAD_SIZE_X 64
#define STELLARMESH_DISPATCH_THREAD_SIZE_Y 1
#define STELLARMESH_DISPATCH_THREAD_SIZE_Z 1

uint GetGroupIndex(uint3 groupID)
{
    return groupID.x + (groupID.y + groupID.z * STELLARMESH_DISPATCH_GROUP_SIZE_WARPPED) * STELLARMESH_DISPATCH_GROUP_SIZE_WARPPED;
}

uint GetThreadIndex(uint3 threadID)
{
    return threadID.x + (threadID.y + threadID.z * STELLARMESH_DISPATCH_THREAD_SIZE_Y) * STELLARMESH_DISPATCH_THREAD_SIZE_X;
}

uint GetDispatchThreadIndex(uint3 groupID, uint3 threadID, uint groupSize)
{
    uint groupIndex = GetGroupIndex(groupID);
    uint threadIndex = GetThreadIndex(threadID);
    return groupIndex * groupSize + threadIndex;
}

#endif