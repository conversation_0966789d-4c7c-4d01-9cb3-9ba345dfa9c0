#ifndef PARTICLE_DATA_GETTERS_MATERIAL_EDITOR
#define PARTICLE_DATA_GETTERS_MATERIAL_EDITOR

float4 GetParticleColor(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_Color;
#else
	return 1;
#endif
}

float4 GetParticleUVScale(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_UVScale;
#else
	return 1;
#endif
}

float3 GetParticleAnimatedVelocity(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_AnimatedVelocity;
#else
	return 1;
#endif
}

float3 GetParticlePosition(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_Position;
#else
	return 1;
#endif
}

float3 GetParticleRotation(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_Rotation;
#else
	return 1;
#endif
}

float3 GetParticleSizeScale(in ObjectSceneData data)
{
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE) || defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	return data.particle_SizeScale;
#else
	return 1;
#endif
}

#endif