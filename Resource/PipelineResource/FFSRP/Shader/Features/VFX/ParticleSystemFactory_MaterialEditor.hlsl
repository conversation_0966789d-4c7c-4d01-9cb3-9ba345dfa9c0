#ifndef PARTICLE_SYSTEM_FACTORY_MATERIAL_EDITOR
#define PARTICLE_SYSTEM_FACTORY_MATERIAL_EDITOR

#define PARTICLE 1
#ifdef PARTICLE
    #undef INSTANCING
#endif

#define ENABLE_VIEW_MODE_VISUALIZE
#include "../../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../../Material/Lit/LitUEVariables.hlsl"

#define ALIGN_MODE_UNALIGNED			0
#define ALIGN_MODE_VELOCITY				1
#define ALIGN_MODE_CUSTOM				2

#define FACING_MODE_CAMERA_PLANE 		0
#define FACING_MODE_CAMERA_POSITION 	1
#define FACING_MODE_CUSTOM 				2
#define FACING_MODE_CAMERA_BLEND		3

SHADER_CONST(bool, ENABLE_LARGE_COORDINATE, false);

float3 RotateMeshParticle(float3 euler, float3 pos)
{
	float sx, cx;
	sincos(euler.x, sx, cx);
	float sy, cy;
	sincos(euler.y, sy, cy);
	float sz, cz;
	sincos(euler.z, sz, cz);
	float3x3 rotMat;
	rotMat[0].xyz = float3(cz * cy, cz * sy * sx - sz * cx, cz * sy * cx + sz * sx);
	rotMat[1].xyz = float3(sz * cy, cz * cx + sz * sy * sx, sz * sy * cx - sx * cz);
	rotMat[2].xyz = float3(-sy, cy * sx, cy * cx);

	float3 output = mul(rotMat, pos);
	return output;
}

#define eps 0.001
float3 FacingMode(PrimitiveSceneData primData, float3 pos, float3 center, float3 tilePos, float3 customFacingDirection, out float3 normal, out float3 tangent)
{
	float3 axisZ = float3(ce_View._m20, ce_View._m21, ce_View._m22);
	float3 axisX = float3(ce_View._m00, ce_View._m01, ce_View._m02);
	float3 axisY = -float3(ce_View._m10, ce_View._m11, ce_View._m12);

	const float3 cameraVector = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePos) - center;
	if (primData.particle_RenderFacingMode != FACING_MODE_CAMERA_PLANE)
	{
		const float3 planeX = axisX;
		const float3 planeY = axisY;
		float3 facingDirection = lerp(cameraVector, customFacingDirection, primData.particle_RenderFacingMode == FACING_MODE_CUSTOM);
		if (length(facingDirection) < eps)
			facingDirection = float3(1, 0, 0);
		float3 up = float3(0, 1, 0);
		if (ENABLE_LARGE_COORDINATE)
			up = normalize(GetLargeCoordinateAbsolutePosition(ce_CameraPos, ce_CameraTilePosition));
		up = -up;
		axisZ = normalize(facingDirection);
		axisX = cross(up, axisZ);
		if (length(axisX) < eps)
		{
			axisX = GetPerpendicularVector(axisZ);
		}
		axisX = normalize(axisX);
		axisY = normalize(cross(axisZ, axisX));

		if (primData.particle_RenderFacingMode == FACING_MODE_CAMERA_BLEND)
		{
			const float cameraDistanceSq = dot(cameraVector, cameraVector);
			const float interp = saturate(cameraDistanceSq * primData.particle_RenderFacingBlend.y - primData.particle_RenderFacingBlend.z);
			axisX = lerp(axisX, planeX, interp);
			axisY = lerp(axisY, planeY, interp);
			//axisZ = normalize(cross(axisX, axisY));
		}
	}

	float3 centerOffs = pos.xyz - center;
    float3 localPos = center + axisX * centerOffs.x + axisY * centerOffs.y;
	normal = cross(axisX, axisY);
	tangent = axisX;
	return localPos;
}

float3 Billboard(float3 input, matrix worldToObject, float3 center)
{
    float3 viewer = mul(worldToObject, float4(ce_CameraPos, 1.0)).xyz;

    float3 zPivot = normalize(viewer - center);
    float3 xPivot = cross(float3(0, 1, 0), zPivot);
	if (length(xPivot) < eps)
	{
		if (abs(zPivot.x) <= abs(zPivot.y) && abs(zPivot.x) <= abs(zPivot.z))
			xPivot = float3(0, -zPivot.z, zPivot.y);
		else if (abs(zPivot.y) <= abs(zPivot.x) && abs(zPivot.y) <= abs(zPivot.z))
			xPivot = float3(-zPivot.z, 0, zPivot.x);
		else
			xPivot = float3(-zPivot.y, zPivot.x, 0);
	}
	xPivot = normalize(xPivot);
    float3 yPivot = normalize(cross(zPivot, xPivot));

    // Use the three vectors to rotate the quad
    float3 centerOffs = input.xyz - center;
    float3 localPos = center + xPivot * centerOffs.x + yPivot * centerOffs.y + zPivot * centerOffs.z;

	return localPos;
}

float3 FaceRenderer(float3 input, float3 faceDirection, float3 center)
{
	float3 zPivot = normalize(faceDirection);
	float3 xPivot = normalize(cross(center, zPivot));
	float3 yPivot = normalize(cross(zPivot, xPivot));

	float3 centerOffs = input.xyz - center;
    float3 localPos = center + xPivot * centerOffs.x + yPivot * centerOffs.y + zPivot * centerOffs.z;
	return localPos;
}

float3 AlignRenderer(in PrimitiveSceneData primData, float3 input, float3 align, float3 center, float3 tilePos, float3 customFacingDirection, out float3 normal, out float3 tangent)
{
	align = -align;
	const float3 cameraVector = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePos) - center;
	float3 facingDirection = lerp(cameraVector, customFacingDirection, primData.particle_RenderFacingMode == FACING_MODE_CUSTOM);
	if (length(align) < eps)
		align = float3(1, 0, 0);
	if (length(facingDirection) < eps)
		facingDirection = float3(0, 0, 1);
	align = normalize(align);
    float3 zPivot = normalize(facingDirection);
	float3 xPivot = cross(align, zPivot);
	if (length(xPivot) < eps)
	{
		xPivot = GetPerpendicularVector(align);
	}
	xPivot = normalize(xPivot);
	float3 yPivot = align;
	//zPivot = normalize(cross(xPivot, yPivot));
	yPivot = lerp(yPivot, cross(zPivot, xPivot), primData.particle_RenderFacingMode == FACING_MODE_CUSTOM);

	float3 centerOffs = input.xyz - center;
    float3 localPos = center + xPivot * centerOffs.x + yPivot * centerOffs.y;
	normal = cross(xPivot, yPivot);
	tangent = xPivot;
	return localPos;
}

bool CullParticle(inout ObjectSceneData objData, inout PrimitiveSceneData primData)
{
	bool isCulled = false;

	// Cull by camera distance
	if (objData.particle_CullMask & (1 << 0))
	{
		float minDistance = primData.particle_CameraDistanceCullRange.x;
		float maxDistance = primData.particle_CameraDistanceCullRange.y;
		float3 particle_Position = mul(primData.ce_World, float4(objData.particle_Position, 1.0)).xyz;
		float3 tilePosition;
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
		tilePosition = objData.ce_TilePosition;
#else
		tilePosition = primData.ce_TilePosition;
#endif
		float3 camera_Position = GetLargeCoordinateReltvPosition(ce_CameraPos, ce_CameraTilePosition, tilePosition);
		float camDistSq = distance(particle_Position, camera_Position);
		if (camDistSq < minDistance || camDistSq > maxDistance)
		{
			isCulled = true;
		}
	}

	return isCulled;
}

void ProcessSpriteParticle(in ObjectSceneData objData, in PrimitiveSceneData primData, inout VSInput vertex)
{
	if (CullParticle(objData, primData))
	{
		vertex.position.xyz = 0.0f;
		return;
	}
	float4 particle_Position = mul(primData.ce_World, float4(objData.particle_Position, 1.0));
    // scale particle's position
	const float2 scale = objData.particle_SizeScale.xy;
	float4x4 scaleMat;
	scaleMat[0].xyzw = float4(scale.x, 0, 0, 0);
	scaleMat[1].xyzw = float4(0, scale.y, 0, 0);
	scaleMat[2].xyzw = float4(0, 0, 1, 0);
	scaleMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(scaleMat, vertex.position);
	float2 pivotOffset = (primData.particle_DefaultPivot - 0.5.xx) * abs(vertex.position.xy) * 2;
	vertex.position -= float4(pivotOffset, 0.0, 0.0);
	// rotate particle
    float sinRotation;
	float cosRotation;
	sincos(objData.particle_Rotation.z, sinRotation, cosRotation);
	float4x4 rotMat;
	rotMat[0].xyzw = float4(cosRotation, -sinRotation, 0, 0);
	rotMat[1].xyzw = float4(sinRotation,  cosRotation, 0, 0);
	rotMat[2].xyzw = float4(0, 0, 1, 0);
	rotMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(rotMat, vertex.position);
	vertex.position.xyz += particle_Position;

	// float3 pivotOffset = float3(particle_DefaultPivot * scale.xy * 100, 0.0);
	// float3 anchorPoint = particle_Position + pivotOffset;

	// float4 rotatedPivot = mul(rotMat, float4(anchorPoint, 1.0));
	// float3 inverseOffset = anchorPoint - rotatedPivot.xyz;
	// vertex.position.xyz += (inverseOffset - pivotOffset);
	float3 normal, tangent;
	float3 tilePosition;
#if defined(USED_WITH_GLOBAL_SPACE_PARTICLE)
	tilePosition = objData.ce_TilePosition;
#else
	tilePosition = primData.ce_TilePosition;
#endif
	float3 customFacingDirection = float3(0, 0, 0), customAlignmentDirection = float3(0, 0, 0);

#if IS_NPARTICLE_PASS
	customFacingDirection = lerp(customFacingDirection, objData.particle_SpriteFacing, primData.particle_RenderFacingMode == FACING_MODE_CUSTOM);
	customAlignmentDirection = lerp(customAlignmentDirection, objData.particle_SpriteAlignment, primData.particle_RenderAlignMode == ALIGN_MODE_CUSTOM);
#else
	customFacingDirection = lerp(customFacingDirection, primData.particle_RenderFacingDirection, primData.particle_RenderFacingMode == FACING_MODE_CUSTOM);
	customAlignmentDirection = lerp(customAlignmentDirection, primData.particle_RenderAlignDirection, primData.particle_RenderAlignMode == ALIGN_MODE_CUSTOM);
#endif

#if defined(USED_WITH_LOCAL_SPACE_PARTICLE)
	customFacingDirection = mul(primData.ce_World, float4(customFacingDirection, 0)).xyz;
	customAlignmentDirection = mul(primData.ce_World, float4(customAlignmentDirection, 0)).xyz;
#endif

    // apply particle's orientation depend on align mode or facing mode
	if (primData.particle_RenderAlignMode == ALIGN_MODE_UNALIGNED)
	{
		vertex.position.xyz = FacingMode(primData, vertex.position.xyz, particle_Position, tilePosition, customFacingDirection, normal, tangent);
	}
	else if (primData.particle_RenderAlignMode == ALIGN_MODE_VELOCITY)
	{
		float4 velocity = float4(objData.particle_AnimatedVelocity, 0);
		velocity = mul(primData.ce_World, velocity);
		vertex.position.xyz = AlignRenderer(primData, vertex.position.xyz, velocity.xyz, particle_Position, tilePosition, customFacingDirection, normal, tangent);
	}
	else
	{
		vertex.position.xyz = AlignRenderer(primData, vertex.position.xyz, customAlignmentDirection, particle_Position, tilePosition, customFacingDirection, normal, tangent);
	}
    vertex.position.w = 1.0;

#ifdef VERTEX_NEED_NORMAL
	vertex.normal = float4(normal, 0);
#endif

#ifdef VERTEX_NEED_TANGENT
	vertex.tangent = float4(tangent, 0);
#endif

#ifdef VERTEX_NEED_UV
    // particle's uv depend on SubUV
    float tileWidth = objData.particle_UVScale.z;
	float tileHeight = objData.particle_UVScale.w;
	vertex.uv = float2((1 - vertex.uv.x) * tileWidth + objData.particle_UVScale.x, vertex.uv.y * tileHeight + objData.particle_UVScale.y);
#endif
}

void ProcessMeshParticle(in ObjectSceneData objData, in PrimitiveSceneData primData, inout VSInput vertex)
{
	if (CullParticle(objData, primData))
	{
		vertex.position.xyz = 0.0f;
		return;
	}

    const float3 scale = objData.particle_SizeScale;
	float4x4 scaleMat;
	scaleMat[0].xyzw = float4(scale.x, 0, 0, 0);
	scaleMat[1].xyzw = float4(0, scale.y, 0, 0);
	scaleMat[2].xyzw = float4(0, 0, scale.z, 0);
	scaleMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(scaleMat, vertex.position);

	float sx, cx;
	sincos(objData.particle_Rotation.x, sx, cx);
	float sy, cy;
	sincos(objData.particle_Rotation.y, sy, cy);
	float sz, cz;
	sincos(objData.particle_Rotation.z, sz, cz);
	float4x4 rotMat;
	rotMat[0].xyzw = float4(cz * cy, cz * sy * sx - sz * cx, cz * sy * cx + sz * sx, objData.particle_Position.x);
	rotMat[1].xyzw = float4(sz * cy, cz * cx + sz * sy * sx, sz * sy * cx - sx * cz, objData.particle_Position.y);
	rotMat[2].xyzw = float4(-sy, cy * sx, cy * cx, objData.particle_Position.z);
	rotMat[3].xyzw = float4(0, 0, 0, 1);
	vertex.position = mul(rotMat, vertex.position);

    vertex.position = float4(vertex.position.xyz, 1.0);
	vertex.position = mul(primData.ce_World, vertex.position);

	float3x3 model = transpose(inverse(primData.ce_World));

#ifdef VERTEX_NEED_NORMAL
	vertex.normal = mul(rotMat, vertex.normal);
	vertex.normal.xyz = mul(model, vertex.normal.xyz);
#endif

#ifdef VERTEX_NEED_TANGENT
	vertex.tangent = mul(rotMat, vertex.tangent);
	vertex.tangent.xyz = mul(model, vertex.tangent.xyz);
	vertex.tangent.w = 0;
#endif
}

#endif