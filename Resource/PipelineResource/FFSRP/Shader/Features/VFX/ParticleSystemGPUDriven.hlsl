#ifndef PARTICLE_SYSTEM_GPU_DRIVEN
#define PARTICLE_SYSTEM_GPU_DRIVEN
//#pragma enable debug_symbol

#include "../../ShaderLibrary/Math/RandomUtils.hlsl"
#include "ParticleSystemUtils.hlsl"

/* -----------------------------------------------------------------
 * GPU simulation utility functions
 * -----------------------------------------------------------------
 */

static uint3 GDispatchThreadId;		// SV_DispatchThreadId
static uint3 GGroupId;				// SV_GroupdId
static uint3 GGroupThreadId;		// SV_GroupThreadId
static uint  GGroupIndex;			// SV_GroupIndex
static uint GLinearThreadId;
static uint GEmitterTickCounter = 0;
static uint GEmitterRandomSeed = 0;
static uint GRandomSeedOffset = 0;

struct MinMaxCurve
{
    uint  mode;
    float scaler;
    float floatMin;
    float floatMax;
	uint  curveEvalMode;
	uint  curveIdMin;
	uint  curveIdMax;
	float padding;
};

struct DynamicVector3
{
    MinMaxCurve x;
    MinMaxCurve y;
    MinMaxCurve z;
};

struct ColorCurve
{
	float4 scaler;
	uint4 curveIdRGBA;
};

struct MinMaxGradient
{
	float3 padding;
	uint mode;
	float4 colorMin;
	float4 colorMax;
	ColorCurve gradientMin;
	ColorCurve gradientMax;
};

struct ParticleInitInfo
{
    MinMaxCurve startSpeed;
    DynamicVector3 originVelocity;
    MinMaxCurve lifetime;
    MinMaxCurve scaler;
    MinMaxCurve sizeX;
    MinMaxCurve sizeY;
    MinMaxCurve sizeZ;
    MinMaxCurve rotationX;
    MinMaxCurve rotationY;
    MinMaxCurve rotationZ;
    float4 uvScale;
    MinMaxGradient color;
	float3 padding;
	uint enableOriginVelocity;
};

struct LocationShapeInfo
{
    uint shapeType;
    float3 positionOffset;
    uint emitFrom;
    float3 positionOffsetScaler;
    float radius;
    float3 box;
	float uDistribution;
	float diskCoverage;
	float uniformSpiralAmount;
	float uniformSpiralFalloff;
	uint distributionType;
	uint meshSurfaceEmitType;
	float length;
	float angle;
	float innerAngle;
	float3 axis;
	float arc;
	float3 padding;

	//for custom usage
	DynamicVector3 positionCurve;
	DynamicVector3 boxCurve;
};

struct SizeScaleInfo
{
	MinMaxCurve sizeScale;
	MinMaxCurve sizeScaleX;
	MinMaxCurve sizeScaleY;
	MinMaxCurve sizeScaleZ;

	uint separateAxis;
	uint scaleWithCamera;
	uint scaleWithVelocity;
	uint sampleScaleFactorCurve;

	float4 cameraRemapScale;
	float3 minScaleFactor;
	float padding0;
	float3 maxScaleFactor;
	float velocityThreshold;

	MinMaxCurve scaleFactorCurve;
};

struct ColorScaleInfo
{
	MinMaxGradient colorScale;
	uint scaleWithCamera;
	float3 padding;
	float4 scaleChannel;
	float4 cameraRemapScale;
};

struct SpriteRotationRateInfo
{
	MinMaxCurve spriteRotationRate;
};

struct VelocityInfo
{
	DynamicVector3 vector;
	DynamicVector3 offset;
};

struct VectorNoiseInfo
{
	DynamicVector3 frequency;
	DynamicVector3 strength;
	DynamicVector3 positionOffset;
	float scaler;
	uint separateAxis;
	uint damping;
	uint octaveCount;
	float octaveScale;
	float octaveMultiplier;
	float2 padding;
};

struct GravityInfo
{
	DynamicVector3 gravityVector;
};

struct ForceInfo
{
	DynamicVector3 forceVector;
	uint space;
	float scaler;
	float2 padding;
};

struct VortexForceInfo
{
	DynamicVector3 vortexAxis;
	DynamicVector3 vortexOriginOffset;
	MinMaxCurve vortexForceAmout;
	MinMaxCurve originPullAmount;
	uint space;
	float3 padding;
};

struct PointAttractionForceInfo
{
	DynamicVector3 attractorOffset;
	MinMaxCurve attractionRadius;
	MinMaxCurve attractionStrength;
	MinMaxCurve killRadius;
	float3 attractorPosition;
	uint useFalloff;
	float falloffExponent;
	float killRadiusOvershootCorrection;
	uint useAttractorOffset;
	float padding;
};

struct SolveForceVelocityInfo
{
	MinMaxCurve speedScale;
	MinMaxCurve speedLimit;
	MinMaxCurve drag;
	uint dragEnable;
	uint enableSpeedLimit;
	float2 padding;
};

struct SubUVInfo
{
	uint tileX;
	uint tileY;
	uint animationMode;
	uint cycles;

	MinMaxCurve startFrame;
	MinMaxCurve frame;

	uint rowMode;
	uint rowIndex;
	float playRate;
	float padding;
};

struct ParticleStateInfo
{
	float3 	volume;
	uint 	killTrigger;
	float3 	volumePosition;
	float	padding0;
	float3 	volumeRotation;
	uint 	space;
};

struct ParticleSpawnDashboard
{
    ParticleInitInfo particleInit;
    LocationShapeInfo locationShape;
};

struct ParticleUpdateDashboard
{
	SizeScaleInfo sizeScale;
	ColorScaleInfo colorScale;
	SubUVInfo subuv;
	SpriteRotationRateInfo spriteRotationRate;
	VelocityInfo velocity;
	GravityInfo gravity;
	ForceInfo force;
	VortexForceInfo vortexForce;
	PointAttractionForceInfo pointAttractionForce;
	VectorNoiseInfo vectorNoise;
	SolveForceVelocityInfo solveForceVelocity;
	ParticleStateInfo particleState;
};

cbuffer cbSystemView
{
	matrix _SystemRelativeMatrix;
	matrix _SystemInverseRelativeMatrix;
	matrix _SystemRelativeMatrixNoScale;
	float3 _SystemTilePosition;
	float3 ce_CameraPos;
    float3 ce_CameraTilePosition;
}

cbuffer cbEmitterView
{
	uint  _GPUSceneBufferOffset;
	uint  _EmitterIndex;
    uint  _EmitterMaxNum;
    uint  _EmitterAliveNum;
	uint  _EmitterTickCounter;
	uint  _EmitterRandomSeed;
	uint  _EmitterIsLocalSpace;
	float _EmitterDeltaTime;
	float _EmitterAge;
	float _EmitterNormalizedAge;
	uint4 _EmitterDashboardMask;
	uint  _EmitterLocationMeshTriangleCount;
	uint  _EmitterLocationMeshVertexCount;
	uint  _EmitterLocationMeshIs16BitIndex;
	uint  _EmitterSimulationState;
	float _EmitterVectorNoiseModuleScrollOffset;
}

struct ParticleGPUOriginState
{
    uint id;
    uint dead;
    uint seed;
    float offset;

    float lifetime;
    float subuvId;
    float startSpeed;
	float padding;

    float4 startSize;
    float4 startRotation;
    //float4 animatedVelocity;
    float4 startColor;
};

struct ParticleGPUSimulationState
{
	matrix particle_RelativeMatrix;
	float3 particle_TilePosition;
	float  subuvId;
    float3 particle_Position;
    float  age;
    float3 particle_AnimatedVelocity;
	uint   particle_CullMask;
    float3 particle_SizeScale;
	float  padding1;
    float3 particle_Rotation;
	float  padding2;
	float4 particle_UVScale;
    float4 particle_Color;
};

struct ParticleGPUSimulationContext
{
	ParticleGPUOriginState particleOriginState;
	ParticleGPUSimulationState particleSimulationState;
	float3 particleForce;
};

static int gOutputIndex = 0;
static int gSpawnStartIndex = 0, gSpawnCount = 0;
static ParticleGPUSimulationContext context;
groupshared ParticleSpawnDashboard spawnParams;
groupshared ParticleUpdateDashboard updateParams;
groupshared matrix localRelativeMatrix;	//only in the spawn stage can it be applied to a position
groupshared matrix worldToLocalMatrix;
RWStructuredBuffer<ParticleGPUOriginState>  _InputOriginStateBuffer;
RWStructuredBuffer<ParticleGPUSimulationState>  _InputSimulationStateBuffer;
RWStructuredBuffer<ParticleGPUOriginState>  _OutputOriginStateBuffer;
RWStructuredBuffer<ParticleGPUSimulationState>  _OutputSimulationStateBuffer;
StructuredBuffer<ParticleSpawnDashboard> _ParticleSpawnDashboard;
StructuredBuffer<ParticleUpdateDashboard> _ParticleUpdateDashboard;
RWStructuredBuffer<float> _CurveLuts;
RWStructuredBuffer<int> _ParticleInstanceCounterUAV;
StructuredBuffer<uint> _ParticleIndirectArgsSRV;
StructuredBuffer<uint> _EmitterLocationMeshIndexBuffer;
StructuredBuffer<float3> _EmitterLocationMeshPositionBuffer;
StructuredBuffer<float3> _EmitterLocationMeshNormalBuffer;

void LoadEmitterParams(const int emitterIndex)
{
	spawnParams = _ParticleSpawnDashboard[emitterIndex];
	updateParams = _ParticleUpdateDashboard[emitterIndex];
}

void LoadParticleState(int index)
{
	context.particleOriginState = _InputOriginStateBuffer[index];
	context.particleSimulationState = _InputSimulationStateBuffer[index];
}

void InitWorldTransform()
{
	[branch] if (_EmitterIsLocalSpace)
    {
        context.particleSimulationState.particle_RelativeMatrix = _SystemRelativeMatrix;
        context.particleSimulationState.particle_TilePosition = _SystemTilePosition;
    }
    else
    {
        context.particleSimulationState.particle_RelativeMatrix = matrix(   1, 0, 0, 0,
                                                                            0, 1, 0, 0,
                                                                            0, 0, 1, 0,
                                                                            0, 0, 0, 1);
        context.particleSimulationState.particle_TilePosition = _SystemTilePosition;
    }
}

void UpdateWorldTransform()
{
	[branch] if (_EmitterIsLocalSpace)
	{
		context.particleSimulationState.particle_RelativeMatrix = _SystemRelativeMatrix;
        context.particleSimulationState.particle_TilePosition = _SystemTilePosition;
	}
}

//When it's in local space, the model transform will be applied in the shading pass,
//otherwise, the model transform will be applied in the simulation stage.
void SetLocalRelativeMatrix()
{
	[branch] if (_EmitterIsLocalSpace)
	{
		localRelativeMatrix = matrix(	1, 0, 0, 0,
										0, 1, 0, 0,
										0, 0, 1, 0,
										0, 0, 0, 1);
		worldToLocalMatrix = matrix(	1, 0, 0, 0,
										0, 1, 0, 0,
										0, 0, 1, 0,
										0, 0, 0, 1);
	}
	else
	{
		localRelativeMatrix = _SystemRelativeMatrix;
		worldToLocalMatrix = _SystemInverseRelativeMatrix;
	}
}


//RNG
// static uint rngSeed = 0;
// uint RandPcg()
// {
//     uint state = rngSeed;
//     rngSeed = rngSeed * 747796405u + 2891336453u;
//     uint word = ((state >> ((state >> 28u) + 4u)) ^ state) * 277803737u;
//     return (word >> 22u) ^ word;
// }
// void SetSeed(uint seed)
// {
//     rngSeed = seed;
// }

// Returns 4 random normalized floats based on 4 explicit integer seeds
float4 rand4(int Seed1, int Seed2, int Seed3, int Seed4)
{
	int4 v = int4(Seed4, Seed1, Seed2, Seed3) * 1664525 + 1013904223;

	v.x += v.y*v.w;
	v.y += v.z*v.x;
	v.z += v.x*v.y;
	v.w += v.y*v.z;
	v.x += v.y*v.w;
	v.y += v.z*v.x;
	v.z += v.x*v.y;
	v.w += v.y*v.z;

	// We can use 24 bits of randomness, as all integers in [0, 2^24] 
	// are exactly representable in single precision floats.
	// We use the upper 24 bits as they tend to be higher quality.

	// The divide is often folded with the range scale in the rand functions
	return float4((v >> 8) & 0x00ffffff) / 16777216.0; // 0x01000000 == 16777216
	// return float4((v >> 8) & 0x00ffffff) * (1.0/16777216.0); // bugged, see UE-67738
}

// float3 specialization of the above:
// 
// Returns 3 random normalized floats based on 4 explicit integer seeds.
// 
// All bits of the first and second seeds are used, while only 
// the lower 16 bits of the third and fourth seeds are used.
float3 rand3(int Seed1, int Seed2, int Seed3, int Seed4)
{
	int3 v = int3(Seed1, Seed2, Seed4 | (Seed3 << 16)) * 1664525 + 1013904223;

	v.x += v.y*v.z;
	v.y += v.z*v.x;
	v.z += v.x*v.y;
	v.x += v.y*v.z;
	v.y += v.z*v.x;
	v.z += v.x*v.y;

	return float3((v >> 8) & 0x00ffffff) / 16777216.0; // 0x01000000 == 16777216
}

// Internal counter used to generate a different sequence of random numbers for each call
static int RandomCounterDeterministic = 0;

// Cost using rand4: 6 imad, 1 itof, 1 ishr, 1 add, 2 mul
float rand(float x, int Seed1, int Seed2, int Seed3)
{
	RandomCounterDeterministic += 1;
	return rand3(Seed1, Seed2, Seed3, RandomCounterDeterministic).x * x;
}

// Cost using rand4: 7 imad, 1 itof, 1 ishr, 1 add, 2 mul
float2 rand(float2 x, int Seed1, int Seed2, int Seed3)
{
	RandomCounterDeterministic += 1;
	return rand3(Seed1, Seed2, Seed3, RandomCounterDeterministic).xy * x;
}

// Cost using rand4: 8 imad, 1 itof, 1 ishr, 1 add, 2 mul
float3 rand(float3 x, int Seed1, int Seed2, int Seed3)
{
	RandomCounterDeterministic += 1;
	return rand3(Seed1, Seed2, Seed3, RandomCounterDeterministic).xyz * x;
}

// Cost using rand4: 9 imad, 1 itof, 1 ishr, 1 and, 2 mul
float4 rand(float4 x, int Seed1, int Seed2, int Seed3) 
{
	RandomCounterDeterministic += 1;
	return rand4(Seed1, Seed2, Seed3, RandomCounterDeterministic).xyzw * x;
}

// Cost using rand4: 6 imad, 2 itof, 1 ishr, 1 add, 2 mul, 1 ftoi
int rand(int x, int Seed1, int Seed2, int Seed3)
{
	// Scaling a uniform float range provides better distribution of numbers than using %.
	// Inclusive! So [0, x] instead of [0, x)
	RandomCounterDeterministic += 1;
	return int(rand3(Seed1, Seed2, Seed3, RandomCounterDeterministic).x * (x+1));
}

// This simply calls the deterministic random number functions from the Seeded RNG section, 
// but uses non-deterministic seeds as input. 

// This could perhaps be optimized by using slightly cheaper functions, but the difference is likely negligible. 

// Internal counter used to generate a different sequence of random numbers for each call
// We need to keep this separate from the Deterministic version so that non-deterministic 
// calls do not interfere with the deterministic ones. 
static int RandomCounterNonDeterministic = -1;

float rand(float x)
{
    RandomCounterNonDeterministic -= 1;
    return rand4(GLinearThreadId, _EmitterTickCounter, GLinearThreadId, RandomCounterNonDeterministic).x * x;
}

float2 rand(float2 x)
{
    RandomCounterNonDeterministic -= 1;
    return rand4(GLinearThreadId, _EmitterTickCounter, GLinearThreadId, RandomCounterNonDeterministic).xy * x;
}

float3 rand(float3 x)
{
    RandomCounterNonDeterministic -= 1;
    return rand4(GLinearThreadId, _EmitterTickCounter, GLinearThreadId, RandomCounterNonDeterministic).xyz * x;
}

float4 rand(float4 x) 
{
    RandomCounterNonDeterministic -= 1;
    return rand4(GLinearThreadId, _EmitterTickCounter, GLinearThreadId, RandomCounterNonDeterministic).xyzw * x;
}

// Integer randoms are INCLUSIVE, i.e. includes both the upper and lower limits
int rand(int x)
{
    RandomCounterNonDeterministic -= 1;
    return int(rand4(GLinearThreadId, _EmitterTickCounter, GLinearThreadId, RandomCounterNonDeterministic).x * (x+1));
}

// Small changes in the input bits should propagate to a lot of output bits, so the resulting hash is not periodic.
// This is important because the hash inputs are often things like particle ID, but the output should be pseudo-random. 
int hash_single(int a)
{
    int x = (a ^ 61) ^ (a >> 16);
	x += x << 3;
	x ^= x >> 4;
	x *= 0x27d4eb2d;
	x ^= x >> 15;
	return x;
}

int hash(int a, int b)
{
	return hash_single(a) ^ hash_single(b * 31);
}

float hash_float(int a, int b)
{
	return (hash(a, b) & 0x00ffffff) / 16777216.0;
}

// this is used when chaining calls from variable number of inputs, e.g. hash_float(hash_float(a, b), c)
float hash_float(float a, int b)
{
	return (hash(a * 16777216.0, b) & 0x00ffffff) / 16777216.0;
}

// Explicit non-deterministic random overrides used by Random Float/Integer and Seeded Random Float/Integer op nodes
float rand_float(float x)
{
	return rand(x.x);
}

float2 rand_float(float2 x)
{
	return float2(rand_float(x.x), rand_float(x.y));
}

float3 rand_float(float3 x)
{
	return float3(rand_float(x.x), rand_float(x.y), rand_float(x.z));
}

float4 rand_float(float4 x) 
{
	return float4(rand_float(x.x), rand_float(x.y), rand_float(x.z), rand_float(x.w));
}

int rand_int(int x)
{
	// Going through the float function also give us a better distribution than using modulo 
	// to get an integer range.
	// This will not include the upper range as rand_float returns [0, max), not [0, max].
	return (int) rand_float(x.x);
}

// Explicit deterministic random overrides used by Random Float/Integer and Seeded Random Float/Integer op nodes
float rand_float(float x, int Seed1, int Seed2, int Seed3)
{
	return rand(x.x, Seed1, Seed2, Seed3);
}

float2 rand_float(float2 x, int Seed1, int Seed2, int Seed3)
{
	return rand(x, Seed1, Seed2, Seed3);
}

float3 rand_float(float3 x, int Seed1, int Seed2, int Seed3)
{
	return rand(x, Seed1, Seed2, Seed3);
}

float4 rand_float(float4 x, int Seed1, int Seed2, int Seed3) 
{
	return rand(x, Seed1, Seed2, Seed3);
}

int rand_int(int x, int Seed1, int Seed2, int Seed3)
{
	// This will not include the upper range as rand_float returns [0, max), not [0, max]
	// The deterministic rand call will include the upper range, so we subtract a one to compensate
	return rand(x.x-1, Seed1, Seed2, Seed3);
}

////////////////////////////////////////////////////////////////////////////////////
// Random number functions
struct ParticleRandInfo
{
	int Seed1;
	int Seed2;
	int Seed3;
};

ParticleRandInfo MakeRandInfo()
{
	GRandomSeedOffset += 1664525u;

	ParticleRandInfo randInfo;
	randInfo.Seed1 = context.particleOriginState.seed;
	randInfo.Seed2 = GRandomSeedOffset;
	randInfo.Seed3 = GEmitterTickCounter;
	return randInfo;
}

// MASSIVE HACK - Tracked in JIRA UE-69298
// Hardcoded random function accessible from inner part of node implementation.
// It works for now at least and avoids exposing every random needed in the UI. 
// Temporary solution, it will be replaced when a design is validated.
float GenerateInternalNoise(uint u, uint v, uint s)
{
    uint Seed = (u * 1664525u + v) + s + GRandomSeedOffset;
    GRandomSeedOffset += Seed;
    return float(Rand3DPCG32(int3(u,v,Seed)).x) / 4294967296.0f;
}

float GetNoiseFloat(ParticleRandInfo randInfo)
{
	return uint(randInfo.Seed3) == 0xffffffff ? GenerateInternalNoise(uint(randInfo.Seed1), uint(randInfo.Seed2), uint(randInfo.Seed3)) : rand(1.0f, uint(randInfo.Seed1), uint(randInfo.Seed2), uint(randInfo.Seed3));
}

int GetNoiseInt(ParticleRandInfo randInfo, int range)
{
	float T = GetNoiseFloat(randInfo);
	return int(floor(float(range) * T));
}

float3 GetRandomBaryCoord(ParticleRandInfo randInfo)
{
	float2 r = float2(GetNoiseFloat(randInfo), GetNoiseFloat(randInfo));
	float sqrt0 = sqrt(r.x);
	float sqrt1 = sqrt(r.y);
	return float3(1.0f - sqrt0, sqrt0 * (1.0 - r.y), r.y * sqrt0);
}

float GetRandomFloat()
{
    // return RandPcg() * (1.0 / 4294967296.0);
    int Constant24 = -246450169;
    return rand(1.0f, context.particleOriginState.seed, Constant24, GEmitterRandomSeed);
}

int GetRandomInt(int range = 1)
{
    float T = GetRandomFloat();
    return int(floor(float(range) * T));
}

float RangedRandom(float rangeMin, float rangeMax)
{
    // float t = GetRandomFloat(MakeRandInfo());
    float t = GetRandomFloat();
    t = rangeMin * t + (1.0 - t) * rangeMax;
    return t;
}

float4 RangedRandom(float4 rangeMin, float4 rangeMax)
{
    float t = GetRandomFloat();
    return lerp(rangeMin, rangeMax, t);
}

float SampleCurveLut(uint curveId, float normalizedTime)
{
	uint index = (round((normalizedTime) * CURVE_LUT_WIDTH_MINUS_ONE));
	index += curveId;
	return _CurveLuts[index];
}

float SampleMinMaxCurve(MinMaxCurve value, float time = 0.0f)
{
	float sampledResult = 0.0f;
	float sampledValueMin, sampledValueMax;

	[branch]
    switch (value.mode)
    {
        case 0: // CONSTANT
        case 4: // CONSTANT_WITH_SCALER
		{
            sampledResult = value.floatMax;
		}
		break;
        case 1: // TWO_CONSTANTS
        case 5: // TWO_CONSTANTS_WITH_SCALER
		{
            sampledResult = RangedRandom(value.floatMin, value.floatMax);
		}
        break;
        case 2: // CURVE
        case 6: // CURVE_WITH_SCALER
		{
            sampledResult = SampleCurveLut(value.curveIdMax, time);
		}
        break;
        case 3: // TWO_CURVES
        case 7: // TWO_CURVES_WITH_SCALER
		{
            float sampledValueMin = SampleCurveLut(value.curveIdMin, time);
            float sampledValueMax = SampleCurveLut(value.curveIdMax, time);
            sampledResult = RangedRandom(sampledValueMin, sampledValueMax);
		}
        break;
    }

	if (value.mode >= 4)
	{
		sampledResult *= value.scaler;
	}

	return sampledResult;
}

float4 SampleColorCurve(in ColorCurve input, float time = 0.0f)
{
	float4 result = float4(0, 0, 0, 0);
	result.r = SampleCurveLut(input.curveIdRGBA.r, time);
	result.g = SampleCurveLut(input.curveIdRGBA.g, time);
	result.b = SampleCurveLut(input.curveIdRGBA.b, time);
	result.a = SampleCurveLut(input.curveIdRGBA.a, time);
	return result;
}

float4 SampleMinMaxGradient(in MinMaxGradient input, float time = 0.0f)
{
	float4 result = float4(0, 0, 0, 0);
	[branch] switch (input.mode)
	{
		case 0: //COLOR
			result = input.colorMax;
			break;
		case 1: //GRADIENT
			result = SampleColorCurve(input.gradientMax, time);
			break;
		case 2: //TWOCOLORS
			result = RangedRandom(input.colorMin, input.colorMax);
			break;
		case 3: //TWOGRADIENTS
			float4 colorMin, colorMax;
			colorMin = SampleColorCurve(input.gradientMin, time);
			colorMax = SampleColorCurve(input.gradientMax, time);
			result = RangedRandom(colorMin, colorMax);
			break;
		case 4: //RANDOMCOLOR
			result = SampleColorCurve(input.gradientMax, GetRandomFloat());
			break;
	}
	return result;
}

float GetNormalizedAge(float age, float lifetime)
{
	return clamp(age / max(0.00001f, lifetime), 0.0f, 1.0f);
}

#endif // PARTICLE_SYSTEM_GPU_DRIVEN