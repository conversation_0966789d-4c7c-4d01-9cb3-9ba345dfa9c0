#ifndef CLOUD_SHADOW
#define CLOUD_SHADOW

#include "../../Features/SkyCloud/SkyCloudCommon.hlsl"

#define OvercastBias 0.8f

// Mainly for AP calculation : not only AP affect cloud, cloud also affect AP
float GetAvgCloudShadow(float3 pos)
{
    return 1.f;
    // float avgCloudShadow = 0.f;
    // float cloudThicknessBase  = CloudShadowThicknessBase / (TopHeight - BottomHeight);
    // float cloudThicknessScale = 1.f;

    // float lowCloudShadow = 0.f;
    // if (LowCloudEnable)
    // {
    //     // if (LowCloudCover >= CloudShadowOvercastThres)
    //     // {
    //     //     lowCloudShadow = clamp(CloudShadowOvercastVal * CloudShadowScale * cloudThicknessScale, 0.2f, 1.f);
    //     // } else
    //     {
    //         cloudThicknessScale = (LowCloudVRange.y - LowCloudVRange.x) / cloudThicknessBase;
    //         lowCloudShadow = LowCloudCover * cloudThicknessScale * CloudShadowForAPScale;
    //     }
    // }
    // float midCloudShadow = 0.f;
    // if (MidCloudEnable)
    // {
    //     // if (MidCloudCover >= CloudShadowOvercastThres)
    //     // {
    //     //     midCloudShadow = clamp(CloudShadowOvercastVal * CloudShadowScale * cloudThicknessScale, 0.2f, 1.f);
    //     // } else
    //     {
    //         cloudThicknessScale = (MidCloudVRange.y - MidCloudVRange.x) / cloudThicknessBase;
    //         midCloudShadow = MidCloudCover * cloudThicknessScale * CloudShadowForAPScale;
    //     }
    // }
    // avgCloudShadow = max(lowCloudShadow, midCloudShadow);

    // // Only 1 case is special : mid scatter low overcast (but this situation will not happen in FFS)
    // // This case should consider position
    // bool bIsSpecialCase = LowCloudEnable && LowCloudCover >= CloudShadowOvercastThres && MidCloudEnable && MidCloudCover < CloudShadowOvercastThres;
    // if (bIsSpecialCase)
    // {
    //     // Pos in which cloud layer
    //     // ------------- mid cloud ---------------
    //     // -------------     pos   ---------------
    //     // ------------- low cloud ---------------
    //     // -------------     pos   ---------------
    //     // Should always consider the correct position
    //     pos = GetLargeCoordinateAbsolutePosition(pos, ce_CameraTilePosition);
    //     pos *= 0.01f;
    //     float4 posUvw = CalculateUVW_EdgeFade(pos);
    //     float posNormalizeHeight = posUvw.y;
    //     if (posNormalizeHeight <= LowCloudVRange.y)
    //     {
    //         return saturate(1 - lowCloudShadow);
    //     }
    //     else if (posNormalizeHeight <= MidCloudVRange.y)
    //     {
    //         return saturate(1 - midCloudShadow);
    //     }
    //     return 1.f;
    // }

    // // Storm does not affect AP NOW
    // return saturate(1 - avgCloudShadow);
}

float GetLowCloudShadow(float3 posW, float3 lightDir)
{
    if (!LowCloudEnable) return 1.f;

    float3 ret = SolveRayIntersecHeight_Wgs84(posW, lightDir, LowCloudBottomHeight + LowCloudHeight * CloudShadowBias);
    if (ret.x >= 0.f && ret.y > 0.f)
    {
        float tMin = ret.y; // since y >= z
        float3 cloudIntersectPos = posW + lightDir * tMin;
        // Apply Cloud Rotation 
        //cloudIntersectPos = ApplyCloudWindSpeed(cloudIntersectPos);
        float4 uvwFade = CalculateUVW_EdgeFade(cloudIntersectPos);
        float3 nowUVW = uvwFade.xyz;
        return saturate(cloudMapShadowNew(cloudIntersectPos, lightDir, clouds_params[0]));
    }
    else return 1.f;
}


// float GetStormShadow(float3 posW, float3 lightDir)
// {
//     if (ThunderstormEnable){
//         RayMarchingResult emptyResult = (RayMarchingResult)0;
//         return saturate(1.f - SampleAllStormShadow(posW, lightDir));
//     }
//     return 1.f;
// }

// More precise CloudShadow should consider ray marching, ref : https://www.shadertoy.com/view/WdXSRj
// 1 : no shadow, 0 : full shadow
// Only bottom/mid cloud will cast cloud shadow
float GetCloudShadow(float3 pos, float3 lightDir)
{
    if (!CloudCastShadow) 
        return 1.f;
    lightDir = -normalize(lightDir);

#ifdef CE_USE_DOUBLE_TRANSFORM
    pos.xyz = GetLargeCoordinateModelPosition(pos.xyz, float3(0.0, 0.0, 0.0), ce_CameraTilePosition);
#endif

    // to meter
    pos.xyz *= 0.01f;
    
    float shadow = 1.f;
    shadow = min(shadow, GetLowCloudShadow(pos, lightDir));

    return shadow;
}

#endif