#pragma compute CullPerLightDrawUnitsStable
#pragma compute CullPerLightDrawUnitsFast
#pragma compute AllocateCommandInstanceOutputSpace
#pragma compute OutputCommandInstanceLists

SamplerState ce_Sampler_Point : register(space0);

#include "../VirtualShadowMap/SceneData.hlsl"
#include "../../../Features/InstanceCulling/HZBCull.hlsl"
#include "../../../ShaderLibrary/Common.hlsl"
#include "LocalLightShadowCommon.hlsl"

#define NUM_THREADS_PER_GROUP 64
#define INDIRECT_ARGS_NUM_WORDS 5
#define GROUP_SIZE 1024

cbuffer cbPass
{
    uint4 _UIntParams0;
}

#define _LightViewCount                     _UIntParams0.x
#define _ObjectCount                        _UIntParams0.y
#define _VisibleObjectCommandBufferMaxNum   _UIntParams0.z
#define _IndirectArgCount                   _UIntParams0.w
#define _MaxLightPerObject                  _UIntParams0.w   //in CullPerLightDrawUnits kernel

struct VisibleObjectCommand
{
    uint lightIndex;
    uint objectIndex;
    uint indirectArgIndex;
};

StructuredBuffer<PrimitiveCullingData> _GPUScenePrimitiveCullingDatas;
StructuredBuffer<ObjectCullingData> _GPUSceneObjectCullingDatas;
StructuredBuffer<ObjectPayloadData> _ObjectPayloadDatas;
StructuredBuffer<LocalLightViewData> _LocalLightViewDatas;

RWStructuredBuffer<VisibleObjectCommand> _OutVisibleObjectCommands;
RWStructuredBuffer<uint> _OutVisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutDrawIndirectArgs;

bool WriteCommand(uint viewIndex, uint objectIndex, uint indirectArgIndex)
{
    VisibleObjectCommand visibleObjectCommand;
    visibleObjectCommand.objectIndex = objectIndex;
    visibleObjectCommand.indirectArgIndex = indirectArgIndex;
    visibleObjectCommand.lightIndex = viewIndex;

    uint visibleObjectCommandOutputOffset = 0u;
    InterlockedAdd(_OutVisibleObjectCommandCount[0], 1u, visibleObjectCommandOutputOffset);
    if (visibleObjectCommandOutputOffset < _VisibleObjectCommandBufferMaxNum)
    {
        _OutVisibleObjectCommands[visibleObjectCommandOutputOffset] = visibleObjectCommand;
        return true;
    }

    _OutVisibleObjectCommandCount[0] = _VisibleObjectCommandBufferMaxNum;
    return false;
}

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void CullPerLightDrawUnitsFast(uint dispatchThreadId : SV_DispatchThreadID)
{
    if (dispatchThreadId >= _ObjectCount)
    {
        return;
    }

    ObjectPayloadData objectPayloadData = _ObjectPayloadDatas[dispatchThreadId];

    int objectCullingGUID = objectPayloadData.objectCullingGUID;
    int objectIndex = objectPayloadData.objectIndex;

    if (objectCullingGUID == -1)
    {
        return;
    }

    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

    uint visibleCount = 0;
    bool fullCmd = false;
    for (uint viewIdx = 0; viewIdx < _LightViewCount; viewIdx++)
    {
        if (fullCmd) break;
        LocalLightViewData viewData = _LocalLightViewDatas[viewIdx];
        matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, viewData.lightViewProjMatrix, primitiveCullingData.tilePosition, viewData.lightTileAndRange);
        FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
        if (cull.isVisible)
        {
            fullCmd = !WriteCommand(viewIdx, objectIndex, objectPayloadData.indirectArgIndex);
            if (!fullCmd)
            {
                ++visibleCount;
            }
        }
    }
    InterlockedAdd(_OutDrawIndirectArgs[objectPayloadData.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], visibleCount); 
}

[numthreads(GROUP_SIZE, 1, 1)]
void CullPerLightDrawUnitsStable(uint dispatchThreadId : SV_DispatchThreadID)
{
    uint objBatchSize = (_ObjectCount + GROUP_SIZE - 1) / GROUP_SIZE;
    bool fullCmd = false;

    for (uint viewIdx = 0; viewIdx < _LightViewCount; viewIdx++)
    {
        LocalLightViewData viewData = _LocalLightViewDatas[viewIdx];

        for (uint objBatchIdx = 0; objBatchIdx < objBatchSize; objBatchIdx++)
        {
            uint objThreadIdx = dispatchThreadId * objBatchSize + objBatchIdx;

            if (objThreadIdx < _ObjectCount)
            {
                ObjectPayloadData objectPayloadData = _ObjectPayloadDatas[objThreadIdx];
                int objectCullingGUID = objectPayloadData.objectCullingGUID;
                int objectIndex = objectPayloadData.objectIndex;
                if (objectCullingGUID != -1)
                {
                    ObjectCullingData objectCullingData = _GPUSceneObjectCullingDatas[objectCullingGUID];
                    PrimitiveCullingData primitiveCullingData = _GPUScenePrimitiveCullingDatas[objectCullingData.primitiveCullingGUID];

                    matrix localToClip = CombineTranslationMatrix(objectCullingData.worldMatrix, viewData.lightViewProjMatrix, primitiveCullingData.tilePosition, viewData.lightTileAndRange.xyz);
                    FrustumCullData cull = BoxCullFrustum(primitiveCullingData.localBoundsCenter, primitiveCullingData.localBoundsExtent, localToClip, true);
                    if (!fullCmd && cull.isVisible)
                    {
                        fullCmd = !WriteCommand(viewIdx, objectIndex, objectPayloadData.indirectArgIndex);
                        if (!fullCmd) 
                            InterlockedAdd(_OutDrawIndirectArgs[objectPayloadData.indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1], 1); 
                    }
                }
            }
        } 
        AllMemoryBarrierWithGroupSync();
    }
}

StructuredBuffer<uint> _DrawIndirectArgs;
RWStructuredBuffer<uint> _OutOffsetBufferCount;
RWStructuredBuffer<uint> _OutObjectIndexOffsetBuffer;
RWStructuredBuffer<uint> _OutTempObjectIndexOffsetBuffer;
 
[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void AllocateCommandInstanceOutputSpace(uint indirectArgIndex : SV_DispatchThreadID)
{
    if (indirectArgIndex < _IndirectArgCount)
    {
        uint objectCommandCount = _DrawIndirectArgs[indirectArgIndex * INDIRECT_ARGS_NUM_WORDS + 1];
        uint objectCommandOffset = 0u;
        if (objectCommandCount > 0u)
        {
            InterlockedAdd(_OutOffsetBufferCount[0], objectCommandCount, objectCommandOffset);
        }
        _OutObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
        _OutTempObjectIndexOffsetBuffer[indirectArgIndex] = objectCommandOffset;
    }
}

StructuredBuffer<VisibleObjectCommand> _VisibleObjectCommands;
StructuredBuffer<uint> _VisibleObjectCommandCount;
RWStructuredBuffer<uint> _OutObjectIndexBuffer;
RWStructuredBuffer<uint> _OutLightInfoBuffer;

[numthreads(NUM_THREADS_PER_GROUP, 1, 1)]
void OutputCommandInstanceLists(uint visibleObjectCommandIndex : SV_DispatchThreadID)
{
    uint visibleObjectCommandCount = _VisibleObjectCommandCount[0];

    if (visibleObjectCommandIndex < visibleObjectCommandCount)
    {
        VisibleObjectCommand visibleObjectCommand = _VisibleObjectCommands[visibleObjectCommandIndex];

        uint objectIndexOutputOffset = 0u;
        InterlockedAdd(_OutTempObjectIndexOffsetBuffer[visibleObjectCommand.indirectArgIndex], 1u, objectIndexOutputOffset);

        _OutObjectIndexBuffer[objectIndexOutputOffset] = visibleObjectCommand.objectIndex;
        _OutLightInfoBuffer[objectIndexOutputOffset] = visibleObjectCommand.lightIndex;
    }
}