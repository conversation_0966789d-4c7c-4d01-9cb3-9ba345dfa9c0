#pragma compute ClearCS

cbuffer cbPass
{
    uint4 _ClearParams; // x:minBound, y:maxBound, z:clearValue
}

RWStructuredBuffer<uint> _ClearResource;

// clear RWStructuredBuffer<uint> [minBound, maxBound) = clearValue
[numthreads(64, 1, 1)]
void ClearCS(uint3 dispatchThreadId : SV_DispatchThreadID)
{
    uint minBound = _ClearParams.x;
    uint maxBound = _ClearParams.y;
    uint clearValue = _ClearParams.z;

    uint index = dispatchThreadId.x + minBound;

    if (index < maxBound)
    { 
        _ClearResource[index] = clearValue;
    }
}