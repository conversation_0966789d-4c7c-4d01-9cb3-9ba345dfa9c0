#ifndef VIRTUAL_SHADOW_MAP_PROJECTION_COMMON_HLSL
#define VIRTUAL_SHADOW_MAP_PROJECTION_COMMON_HLSL

#include "ShaderLibrary/Common.hlsl"
#include "VirtualShadowMapPageAccessCommon.hlsl"
#include "VirtualShadowMapProjectionStructs.hlsl"

#define VIRTUAL_SHADOW_MAP_MIN_OCCLUDER_DISTANCE 1e-6f

float CalcClipmapLevelFloat(VirtualShadowMapProjectionData data, float3 positionWS)
{
    float distanceToClipmapOrigin = length(positionWS - data.clipmapWorldOrigin);
    float log2Distance = data.resolutionLodBias + log2(distanceToClipmapOrigin);
    return log2Distance;
}

int CalcClipmapLevel(VirtualShadowMapProjectionData data, float3 positionWS)
{
    return int(floor(CalcClipmapLevelFloat(data, positionWS)));
}

struct VirtualShadowMapSampleResult
{
	float shadowFactor;
	float occluderDistance;
	uint assetGUID;
};

float ComputeOccluderDistanceOrtho(float4x4 shadowViewToClip, float occluderDepth, float receiverDepth)
{
	float occluderViewZ = (occluderDepth - shadowViewToClip._34) / shadowViewToClip._33;
	float receiverViewZ = (receiverDepth - shadowViewToClip._34) / shadowViewToClip._33;

	// No perspective projection, so simple difference gets us the distance
	float result = receiverViewZ - occluderViewZ;
	return max(VIRTUAL_SHADOW_MAP_MIN_OCCLUDER_DISTANCE, result);
}

// High quality integer hash - this mixes bits almost perfectly
uint StrongIntegerHash(uint x)
{
	// From https://github.com/skeeto/hash-prospector
	// bias = 0.16540778981744320
	x ^= x >> 16;
	x *= 0xa812d533;
	x ^= x >> 15;
	x *= 0xb278e4ad;
	x ^= x >> 17;
	return x;
}

float Rand(inout uint seed)
{
	// Counter based PRNG -- safer than most small-state PRNGs since we use the random values directly here.
	seed += 1;
	uint output = StrongIntegerHash(seed);
	// take low 24 bits
	return (output & 0xFFFFFF) * 5.96046447754e-08; // * 2^-24
}

float4 GenerateSample4D(uint2 pixelCoord, uint sampleIndex, uint timeSeed, uint maxSamples)
{
    uint positionSeed = pixelCoord.x + pixelCoord.y * 65536;
    timeSeed = timeSeed * maxSamples + sampleIndex;
    uint sampleSeed = StrongIntegerHash(positionSeed + StrongIntegerHash(timeSeed));

    float4 result;
	result.x = Rand(sampleSeed);
	result.y = Rand(sampleSeed);
	result.z = Rand(sampleSeed);
	result.w = Rand(sampleSeed);
	return result;
}

// Returns a point in the unit circle
float2 UniformSampleDiskConcentric_VSM(float2 E)
{
	float2 p = 2 * E - 1;
	float2 a = abs(p);
	float Lo = min(a.x, a.y);
	float Hi = max(a.x, a.y);
	float epsilon = 5.42101086243e-20; // 2^-64 (this avoids 0/0 without changing the rest of the mapping)
	float phi = (M_PI / 4) * (Lo / (Hi + epsilon) + 2 * float(a.y >= a.x));
	float radius = Hi;
	// copy sign bits from p
	const uint signMask = 0x80000000;
	float2 disk = asfloat((asuint(float2(cos(phi), sin(phi))) & ~signMask) | (asuint(p) & signMask));
    return disk * radius;
}

// TEMP------------------------------------------------------------

// [0, 1)
// ~10 ALU operations (2 frac, 5 *, 3 mad)
float RandFast_Temp(uint2 pixelPos, float magic = 3571.0)
{
    // add % to avoid overflow
	float2 random2 = (1.0 / 4320.0) * (pixelPos % 4320)+ float2(0.25, 0.0);
	float random = frac(dot(random2 * random2, magic));
	random = frac(random * random * (2 * magic));
	return random;
}

float3 RandomColor(uint value)
{
    return float3(RandFast_Temp(value.xx), RandFast_Temp((value + 1).xx), RandFast_Temp((value + 2).xx));
}

#endif