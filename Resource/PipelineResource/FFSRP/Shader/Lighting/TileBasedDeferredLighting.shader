#pragma vertex VSMain
#pragma pixel PSMain
#pragma keyword CE_USE_DOUBLE_TRANSFORM

#define DEFERRED_SHADING
#define ENABLE_VIEW_MODE_VISUALIZE

#include "../ShaderLibrary/Common.hlsl"

#include "../ShaderLibrary/GlobalModelVariables.hlsl"
#include "../Material/Lit/LitUEVariables.hlsl"

#include "TileBasedDeferredVertex.hlsl"

#include "../Material/Material.hlsl"
#include "../Lighting/Lighting.hlsl"

#include "../Material/Lit/Lit.hlsl"
#include "LightLoop/TileBasedDeferredLightLoop.hlsl"
#include "LightLoop/IndirectDiffuse.hlsl"

Texture2D<float4> _HiZBuffer : register(space0);

PSInput VSMain(VSInput input)
{
	return VSInputToPSInput(input);
}

float3 LinearToSrgb(float3 lin) 
{
	lin = max(6.10352e-5, lin); // minimum positive non-denormal (fixes black problem on DX11 AMD and NV)
	return min(lin * 12.92, pow(max(lin, 0.00313067), 1.0/2.4) * 1.055 - 0.055);
}

float4 PSMain(PSInput input) : SV_TARGET
{
	float2 screenUV = (input.positionNDC.xy ) * ce_ScreenParams.zw;

	float depth = SampleCameraDepth(screenUV);

	PositionInputs posInput = GetPositionInput(screenUV, depth, ce_InvViewProjMatrix, ce_View);
    posInput.positionSS = input.positionNDC.xy;

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	BSDFData bsdfData;
	BuiltinData builtinData;
	DecodeFromGBuffer(posInput.uv, bsdfData, builtinData);

	
    if(bsdfData.materialType == MaterialType_Unlit)
    {
        return float4(0,0,0,0);
    }

	LightLoopOutput lightLoopOutput = LightLoop(V, posInput, bsdfData, builtinData);	
	
    return float4((lightLoopOutput.diffuseLighting + lightLoopOutput.specularLighting + lightLoopOutput.indirectDiffuseLighting), 1.0);
}
