#ifndef __STENCIL_DEFERRED_VERTEX__
#define __STENCIL_DEFERRED_VERTEX__

struct VSInput
{
	float4 position : POSITION;
	float2 uv : TEXCOORD0;
};

struct PSInput
{
	float4 positionNDC : SV_POSITION;
};

PSInput VSInputToPSInput(VSInput input)
{
	PSInput output = (PSInput)0;
	float4 position = input.position;
	position.xy = -1.0f + 2.0f * position.xy;
	position.xy *= float2(1, -1);
	output.positionNDC = position;

	return output;
}

#endif