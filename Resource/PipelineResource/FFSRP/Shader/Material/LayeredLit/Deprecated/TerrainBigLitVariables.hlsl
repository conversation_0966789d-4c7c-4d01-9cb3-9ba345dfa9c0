#ifndef LAYERED_LIT_VARIBALES_HLSL
#define LAYERED_LIT_VARIBALES_HLSL

[[vk::constant_id(0)]]
int MATERIAL_TYPE = 0;

[[vk::constant_id(1)]]
bool DOUBLE_SIDED = false;

[[vk::constant_id(2)]]
bool USE_LM_DIRECTIONALITY = false;

[[vk::constant_id(3)]]
bool ENABLE_LM_AO = false;

[[vk::constant_id(4)]]
bool NO_INVERTUV = false;

[[vk::constant_id(5)]]
bool DISABLE_LM_GI = false;

[[vk::constant_id(6)]]
bool DEBUG_LM_GI_ONLY = false;

[[vk::constant_id(7)]]
bool DEBUG_LM_COLOR_ONLY = false;

[[vk::constant_id(8)]]
bool ENABLE_LIGHT_PROBE = false;

[[vk::constant_id(9)]]
bool ENABLE_VOLUMETRIC_LIGHT_MAP = false;

[[vk::constant_id(10)]]
bool USE_SKY_OCCLUSION = false;

[[vk::constant_id(11)]]
bool DISABLE_BAKE_SHADOW = false;

[[vk::constant_id(12)]]
bool DISABLE_BAKE_NORMAL = false;

[[vk::constant_id(13)]]
bool SHOW_DEBUG_LM_UV = false;

[[vk::constant_id(14)]]
bool SHOW_DEBUG_NORMAL = false;

[[vk::constant_id(15)]]
bool ENABLE_LIGHTMAP_PRT_API = false;

[[vk::constant_id(16)]]
bool ENABLE_LOCAL_LM_PRT = false;

cbuffer cbMtl : register(space1) 
{
	float4 _BaseColor0;
	float4 _BaseColor1;
	float4 _BaseColor2;

	float _Metallic;
    float _Roughness;
    float _Specular;

	float _yanshiMap0_ST;//100
	float _caodi1_ST;//100
	float _alpha2_ST;//15

    float _AO_Intensity;
    float _ReflectionProbeIntensity;
    float _LightSpecIntensity;
	float _VLMReflectionProbeIntensity;
	float _VLMReflectionProbeAOIntensity;
}

Buffer<float4> _ShapeParamsAndMaxScatterDists;
Buffer<float4> _WorldScalesAndFilterRadiiAndThicknessRemaps;

Texture2D<float4> _BaseColorMap0 : register(space1);
Texture2D<float4> _BaseColorMap1 : register(space1);
Texture2D<float4> _BaseColorMap2 : register(space1);
Texture2D<float4> _NormalMap : register(space1);
Texture2D<float4> _EnvBRDFLutMap : register(space1);

#endif