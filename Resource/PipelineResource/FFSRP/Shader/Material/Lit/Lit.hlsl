#ifndef LIT_HLSL
#define LIT_HLSL

#include "LitCommonStruct.hlsl"
#include "../NormalBuffer.hlsl"
#include "Lighting/Shadow/CloudShadows.hlsl"
#include "../../ShaderLibrary/MotionVector.hlsl"

SHADER_CONST(bool, MATERIAL_ERROR_FLAG, true);

#ifndef DEFERRED_SHADING

#ifndef SHADOW_PASS
#if !defined(USED_WITH_LOCAL_SPACE_PARTICLE) && !defined(USED_WITH_GLOBAL_SPACE_PARTICLE) && !defined(PARTICLE_PASS)

#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
#include "Lighting/LightMap.hlsl"
#else
#include "Lighting/IndirectLightingCommon.hlsl"
#endif
#include "Lighting/LightLoop/IndirectDiffuse.hlsl"

SHADER_CONST(bool, SPLIT_REFLECTION_INDIRECT, false);
SHADER_CONST(bool, FORWARD_DEAL, false);
SHADER_CONST(bool, DISABLE_FORWARD_SSR, false);
//Temp code

float GetPrecomputedShadowMasks(float3 positionWS, float3 normalWS, half2 uvStaticLightmap, ObjectSceneData objectData)
{
    #if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    {
        if(!ENABLE_LIGHTMAP_PRT_API)
        {
            BakeShadowMap lm_shadow_map;
            lm_shadow_map.isSDF = true;
            lm_shadow_map.shadowTexture = ce_lmShadowTexture;
            lm_shadow_map.shadowSampler = texture_sampler;
            lm_shadow_map.invPenumbraSize = objectData.ce_lmInvPenumbraSize;
            float4 bake_shadow = lm_shadow_map.GetShadow(uvStaticLightmap, objectData.ce_lmUVScale, objectData.ce_lmUVBias);
            return bake_shadow.r;
        }
    }
    #elif !defined(DEFERRED_SHADING) && defined(ENABLE_VOLUMETRIC_LIGHT_MAP) && ENABLE_VOLUMETRIC_LIGHT_MAP == 1
            VolumetricLightmapData vlmData;
            vlmData.VolumetricLightmapIndirectionTexture = ce_vlmIndirectionTexture;
            vlmData.VolumetricLightmapBrickSkyBentNormal = ce_vlmSkyBentNormal;
            vlmData.VolumetricLightmapWorldToUVScale = objectData.ce_vlmWorldToUVScale;
            vlmData.VolumetricLightmapWorldToUVAdd = objectData.ce_vlmWorldToUVAdd;
            vlmData.VolumetricLightmapIndirectionTextureSize = objectData.ce_vlmIndirectionTextureSize;
            vlmData.VolumetricLightmapBrickTexelSize = objectData.ce_vlmBrickTexelSize;
            vlmData.VolumetricLightmapBrickSize = objectData.ce_vlmBrickSize;
            vlmData.vlmSampler = texture_sampler;

            //float3 BrickTextureUVs = vlmData.ComputeVolumetricLightmapBrickTextureUVs(Float3CE2UE(positionWS));
            float3 BrickTextureUVs = ComputeVolumetricLightmapBrickTextureUVs(Float3CE2UE(positionWS), vlmData);

            float vlmShadow = 1.0f;
            if(USE_SKY_OCCLUSION)
            {
                //vlmShadow = vlmData.GetVolumetricLightmapDirectionalLightShadowing(BrickTextureUVs);
                vlmShadow = GetVolumetricLightmapDirectionalLightShadowing(BrickTextureUVs, vlmData);
            }
            return vlmShadow;
    #endif
    return 1.0f;
}
#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
float3 GetBakedIndirectLighting(float3 positionWS, float3 normalWS, float3 F0, float roughness, ObjectSceneData objectData)
{
    float3 viewDirW = normalize(ce_CameraPos.xyz - positionWS.xyz);
    float3 refleDirW = normalize(reflect(-viewDirW, normalWS));
    float2 envBRDFLutColor = _EnvBRDFLutMap.SampleLevel(ce_Sampler_Clamp, float2(saturate(dot(normalWS, viewDirW)), (1.0 - roughness)), 0).rg;
    float3 envBRDF = saturate(F0 * envBRDFLutColor.r + envBRDFLutColor.g);
    half3 rpColor = half3(0, 0, 0);
    for (uint i = 0; i < objectData.ce_ReflectionProbeCount; i++)
    {
        if (objectData.ce_ReflectionProbeIndex[i] == 0)
        {
            refleDirW = mul(GetRotMatrixFromEuler(ce_ReflectionProbeEulerRot[0].xyz), refleDirW);
            if (ce_ReflectionProbeBoxMin[0].w > 0) {
                float3 posWS = positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
                posWS = GetLargeCoordinateModelPosition(posWS, ce_ReflectionProbeTilePos[0].xyz, ce_CameraTilePosition);
#endif
                refleDirW = BoxProjectedCubemapDirection(refleDirW, posWS, ce_ReflectionProbePosDistance[0].xyz, ce_ReflectionProbeBoxMin[0].xyz, ce_ReflectionProbeBoxMax[0].xyz);
            }
            float3 rpPos = ce_ReflectionProbePosDistance[0].xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
            rpPos = GetLargeCoordinateReltvPosition(rpPos, ce_ReflectionProbeTilePos[0].xyz, ce_CameraTilePosition);
#endif
            float distance = length(positionWS.xyz - rpPos);
            float maxD = max(ce_RPExtentMipmapCount[0].x, max(ce_RPExtentMipmapCount[0].y, ce_RPExtentMipmapCount[0].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[0].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[0].w), 0.0, 1.0);
            //rpColor += ce_ReflectionProbeMap1.SampleLevel(texture_sampler, refleDirW, (ce_RPExtentMipmapCount[0].w - 1) * roughness).rgb * objectData.ce_ReflectionProbeWeight[i] * blendWeight;
            rpColor += ce_ReflectionProbeMap1.SampleLevel(texture_sampler, refleDirW, ComputeReflectionCaptureMipFromRoughness(roughness, ce_RPExtentMipmapCount[0].w)).rgb * objectData.ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (objectData.ce_ReflectionProbeIndex[i] == 1)
        {
            refleDirW = mul(GetRotMatrixFromEuler(ce_ReflectionProbeEulerRot[1].xyz), refleDirW);
            if (ce_ReflectionProbeBoxMin[1].w > 0) {
                float3 posWS = positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
                posWS = GetLargeCoordinateModelPosition(posWS, ce_ReflectionProbeTilePos[1].xyz, ce_CameraTilePosition);
#endif
                refleDirW = BoxProjectedCubemapDirection(refleDirW, posWS, ce_ReflectionProbePosDistance[1].xyz, ce_ReflectionProbeBoxMin[1].xyz, ce_ReflectionProbeBoxMax[1].xyz);
            }
            float3 rpPos = ce_ReflectionProbePosDistance[1].xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
            rpPos = GetLargeCoordinateReltvPosition(rpPos, ce_ReflectionProbeTilePos[1].xyz, ce_CameraTilePosition);
#endif
            float distance = length(positionWS.xyz - rpPos);
            float maxD = max(ce_RPExtentMipmapCount[1].x, max(ce_RPExtentMipmapCount[1].y, ce_RPExtentMipmapCount[1].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[1].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[1].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap2.SampleLevel(texture_sampler, refleDirW, ComputeReflectionCaptureMipFromRoughness(roughness, ce_RPExtentMipmapCount[1].w)).rgb * objectData.ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (objectData.ce_ReflectionProbeIndex[i] == 2)
        {
            refleDirW = mul(GetRotMatrixFromEuler(ce_ReflectionProbeEulerRot[2].xyz), refleDirW);
            if (ce_ReflectionProbeBoxMin[2].w > 0) {
                float3 posWS = positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
                posWS = GetLargeCoordinateModelPosition(posWS, ce_ReflectionProbeTilePos[2].xyz, ce_CameraTilePosition);
#endif
                refleDirW = BoxProjectedCubemapDirection(refleDirW, posWS, ce_ReflectionProbePosDistance[2].xyz, ce_ReflectionProbeBoxMin[2].xyz, ce_ReflectionProbeBoxMax[2].xyz);
            }
            float3 rpPos = ce_ReflectionProbePosDistance[2].xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
            rpPos = GetLargeCoordinateReltvPosition(rpPos, ce_ReflectionProbeTilePos[2].xyz, ce_CameraTilePosition);
#endif
            float distance = length(positionWS.xyz - rpPos);
            float maxD = max(ce_RPExtentMipmapCount[2].x, max(ce_RPExtentMipmapCount[2].y, ce_RPExtentMipmapCount[2].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[2].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[2].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap3.SampleLevel(texture_sampler, refleDirW, ComputeReflectionCaptureMipFromRoughness(roughness, ce_RPExtentMipmapCount[2].w)).rgb * objectData.ce_ReflectionProbeWeight[i] * blendWeight;
        }
        else if (objectData.ce_ReflectionProbeIndex[i] == 3)
        {
            refleDirW = mul(GetRotMatrixFromEuler(ce_ReflectionProbeEulerRot[3].xyz), refleDirW);
            if (ce_ReflectionProbeBoxMin[3].w > 0) {
                float3 posWS = positionWS.xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
                posWS = GetLargeCoordinateModelPosition(posWS, ce_ReflectionProbeTilePos[3].xyz, ce_CameraTilePosition);
#endif
                refleDirW = BoxProjectedCubemapDirection(refleDirW, posWS, ce_ReflectionProbePosDistance[3].xyz, ce_ReflectionProbeBoxMin[3].xyz, ce_ReflectionProbeBoxMax[3].xyz);
            }
            float3 rpPos = ce_ReflectionProbePosDistance[3].xyz;
#ifdef CE_USE_DOUBLE_TRANSFORM
            rpPos = GetLargeCoordinateReltvPosition(rpPos, ce_ReflectionProbeTilePos[3].xyz, ce_CameraTilePosition);
#endif
            float distance = length(positionWS.xyz - rpPos);
            float maxD = max(ce_RPExtentMipmapCount[3].x, max(ce_RPExtentMipmapCount[3].y, ce_RPExtentMipmapCount[3].z));
            float blendWeight = distance < ce_ReflectionProbePosDistance[3].w ? 1.0 : clamp((maxD - distance) / (maxD - ce_ReflectionProbePosDistance[3].w), 0.0, 1.0);
            rpColor += ce_ReflectionProbeMap4.SampleLevel(texture_sampler, refleDirW, ComputeReflectionCaptureMipFromRoughness(roughness, ce_RPExtentMipmapCount[3].w)).rgb * objectData.ce_ReflectionProbeWeight[i] * blendWeight;
        }
    }

    float vlmAO = SampleBakedSkyVisibility(objectData, positionWS, refleDirW);
    half3 vlmrpColor = (SampleBakedSpecGI(objectData, positionWS, refleDirW, _VLMReflectionProbeAOIntensity) * _VLMReflectionProbeIntensity);
    vlmrpColor *= lerp(1.0f, vlmAO, _VLMReflectionProbeAOIntensity);
    float3 res = vlmrpColor * envBRDF + vlmAO * rpColor * envBRDF;
    return res;
}
#endif
//-----------------------------------------------------------------------------
// GetBuiltinData
//-----------------------------------------------------------------------------

BuiltinData GetBuiltinData(float3 V, PSInput psInput, bool isFrontFace, SurfaceData surfaceData, ObjectSceneData objectData)
{
    BuiltinData builtinData = (BuiltinData)0;

    float3 diffuseColor = ComputeDiffuseColor(surfaceData.baseColor, surfaceData.metallic);
    float3 fresnel0 = ComputeFresnel0(surfaceData.baseColor, surfaceData.metallic, surfaceData.specular);

    // Lightmap
    float3 lightmapLight = 0;
    float2 lightmapUV = 0;

    bool use_baked_skylight = false;

#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    use_baked_skylight = true;
#else
    if(ENABLE_LIGHT_PROBE)
    {
        use_baked_skylight = true;
    }
#endif

#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    lightmapUV = psInput.uv1;
#endif
#if defined(DISABLE_LM_GI) && (DISABLE_LM_GI)
    lightmapLight = float3(0, 0, 0);
#else

#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    if(use_baked_skylight)
    {
        lightmapLight = diffuseColor * SampleBakedGI(objectData, psInput.positionWS, surfaceData.normalWS, lightmapUV, 1, 1, 1);
        builtinData.indirectLighting += lightmapLight * surfaceData.ambientOcclusion;
    }
    else
#endif
    {
        if (FORWARD_DEAL)
        {
            float screenSpaceAo = 1.0; // to do
            float3 BentNormal = surfaceData.normalWS;
            builtinData.indirectLighting += GetSkySHOnly(surfaceData, V, surfaceData.normalWS, ce_UE4AmbientProbeSH, ce_SkyLightColor * ce_SkyLightIntensity, /*_SkyLight_Intensity*/1.0,
            screenSpaceAo, BentNormal, diffuseColor, fresnel0, ENABLE_SKY_LIGHT_REALTIME_CAPTURE);
        }
    }
#endif
#if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
    if (!SPLIT_REFLECTION_INDIRECT)
    {
        if (FORWARD_DEAL)//FORWARD
        {
            //SSR
            float4 ssr = 0;
            if (!DISABLE_FORWARD_SSR)
            {
                float3 viewDirWS = normalize(ce_CameraPos.xyz - psInput.positionWS.xyz);
                float3 refleDirW = normalize(reflect(-viewDirWS, normalize(surfaceData.normalWS)));
                float linearDepth = LinearEyeDepth(psInput.positionWS, ce_View);
                ssr = GetSSRTracing(
                    ce_FurthestHZBTexture,
                    ce_Sampler_Point,
                    ce_PrevSceneColorTexture,
                    ce_Sampler_Point,
                    ce_DepthTexture,
                    psInput.positionWS,
                    linearDepth,
                    ce_ForwardSSRParams.x,
                    (uint)(ce_ForwardSSRParams.y),
                    surfaceData.roughness,
                    refleDirW,
                    psInput.positionNDC.xy * ce_ScreenParams.zw,
                    ce_ScreenParams,
                    ce_HZBUvFactorAndInvFactor,
                    ce_View,
                    mul(ce_Projection, ce_View),
                    ce_Projection,
                    ce_InvViewProjMatrix,
                    ce_PreViewMatrix,
                    ce_PreProjMatrix,
                    ce_ClipToPrevClipMat,
                    (uint)(ce_ForwardSSRParams.z),
                    (uint)(ce_ForwardSSRParams.w)
                );
            }
            float3 specularIBL = GetBakedIndirectLighting(psInput.positionWS.xyz, surfaceData.normalWS, fresnel0, surfaceData.roughness, objectData);
            float3 envBRDF; 
            specularIBL += ReflectionEnviroment(surfaceData, V, 1.0, surfaceData.normalWS, fresnel0, envBRDF);
            specularIBL = specularIBL.rgb * (1 - ssr.a) + ssr.rgb * envBRDF;
            builtinData.indirectLighting += specularIBL.rgb;
        }
        else//DEFER
        {
            // ReflectionProbe
            builtinData.indirectLighting += GetBakedIndirectLighting(psInput.positionWS.xyz, surfaceData.normalWS, fresnel0, surfaceData.roughness, objectData);

            // note the bentNormal should consider the baked bentNormal  (todo timllpan)
            float3 envBRDF;
            builtinData.indirectLighting += ReflectionEnviroment(surfaceData, V, 1.0, surfaceData.normalWS, fresnel0, envBRDF);
        }
    }
    else
#endif
    {
        if (FORWARD_DEAL)
        {
            float4 ssr = 0;
            float3 viewDirWS = normalize(ce_CameraPos.xyz - psInput.positionWS.xyz);
            float3 refleDirW = normalize(reflect(-viewDirWS, normalize(surfaceData.normalWS)));
            float linearDepth = LinearEyeDepth(psInput.positionWS, ce_View);
            if (!DISABLE_FORWARD_SSR)
            {
                ssr = GetSSRTracing(
                    ce_FurthestHZBTexture,
                    ce_Sampler_Point,
                    ce_PrevSceneColorTexture,
                    ce_Sampler_Point,
                    ce_DepthTexture,
                    psInput.positionWS,
                    linearDepth,
                    ce_ForwardSSRParams.x,
                    (uint)(ce_ForwardSSRParams.y),
                    surfaceData.roughness,
                    refleDirW,
                    psInput.positionNDC.xy * ce_ScreenParams.zw,
                    ce_ScreenParams,
                    ce_HZBUvFactorAndInvFactor,
                    ce_View,
                    mul(ce_Projection, ce_View),
                    ce_Projection,
                    ce_InvViewProjMatrix,
                    ce_PreViewMatrix,
                    ce_PreProjMatrix,
                    ce_ClipToPrevClipMat,
                    (uint)(ce_ForwardSSRParams.z),
                    (uint)(ce_ForwardSSRParams.w)
                );
            }

            float sceneLinearDepth = ConvertFromDeviceZ(psInput.positionNDC.z, ce_Projection);
            uint GridIndex = ComputeLightGridCellIndex(psInput.positionNDC.xy, sceneLinearDepth, float3(_LightGridZParamsB, _LightGridZParamsO, _LightGridZParamsS), uint3(_CulledGridSizeX, _CulledGridSizeY, _CulledGridSizeZ), _LightGridPixelSizeShift);
            uint NumCulledEntryIndex = GridIndex * 2;
#if !defined(VOXELIZE_PASS) || VOXELIZE_PASS == 0
            uint NumCulledReflectionCaptures = min(_NumCulledLightsGrid[NumCulledEntryIndex + 0], _NumReflectionCaptures);
            uint DataStartIndex = _NumCulledLightsGrid[NumCulledEntryIndex + 1];
#else
            uint NumCulledReflectionCaptures = 0;
            uint DataStartIndex = 0;
#endif
            float indirectIrradiance = 0;
            uint shadingModelID = 0;
            float NoV = max(0.0001, saturate(dot(surfaceData.normalWS, viewDirWS)));
            float3 transformedV = viewDirWS;
            float3 transformedN = surfaceData.normalWS;
            float3 transformedR = 2 * dot(transformedV, transformedN) * transformedN - transformedV;
            float transformedNoV = max(0.0001, saturate(dot(transformedN, transformedV)));
            transformedR = GetOffSpecularPeakReflectionDir(transformedN, transformedR, surfaceData.roughness);
            float specularOcclusion = GetSpecularOcclusion(transformedNoV, surfaceData.roughness * surfaceData.roughness, 1.0);
            //Combine
            float compositeAlpha = 1.0 - ssr.a;
            if (ENABLE_SKY_LIGHT_REALTIME_CAPTURE)
            {
                transformedR = float3(transformedR.x, -transformedR.z, transformedR.y);
            }
#if !defined(VOXELIZE_PASS) || VOXELIZE_PASS == 0
            float3 specularIBL = GatherRadiance(_CulledLightsDataGrid, _CaptureReflectionCubemaps, texture_sampler, _ReflectionCapturesMipNum,
                ce_RPExtentMipmapCount, ce_ReflectionProbePosDistance, ce_ReflectionProbeEulerRot,
#ifdef CE_USE_DOUBLE_TRANSFORM
                ce_CameraTilePosition,
                ce_ReflectionProbeTilePos,
#endif
                ce_SkyLightTexture,
                ce_PreSkyLightTexture,
                0.f,  // 0 for disable blend
                texture_sampler,
                ce_SkyLightColor,
                ce_SkyLightIntensity,
                compositeAlpha, psInput.positionWS, refleDirW, transformedR, surfaceData.roughness, indirectIrradiance, shadingModelID, NumCulledReflectionCaptures, DataStartIndex, specularOcclusion);
#else
            float3 specularIBL = float3(1, 1, 1);
#endif
            specularIBL = (specularIBL + ssr.rgb) * EnvBRDFApprox(fresnel0, surfaceData.roughness, NoV);
            builtinData.indirectLighting += specularIBL.rgb;
        }
    }

    //BakeShadow
    float bakeShadow = GetPrecomputedShadowMasks(psInput.positionWS.xyz, psInput.normalWS.xyz, lightmapUV, objectData);
    if (DISABLE_BAKE_SHADOW)
    {
        bakeShadow = 1.0f;
    }
    builtinData.bakeShadow = bakeShadow;

    // MotionVector
#if !WRITES_VELOCITY_TO_GBUFFER
        builtinData.motionVector = 0;
#else
        float2 velocity = psInput.prePositionNDC.xy / psInput.prePositionNDC.w - psInput.nowPositionNDC.xy / psInput.nowPositionNDC.w;
        builtinData.motionVector = EncodeVelocityToTexture(velocity);
#endif
    return builtinData;
}
#else // Process particle's transparent rendering
#include "Lighting/IndirectLightingCommon.hlsl"
#include "Lighting/LightLoop/IndirectDiffuse.hlsl"
#include "Material/BSDF.hlsl"
BuiltinData GetBuiltinData(float3 V, PSInput psInput, bool isFrontFace, SurfaceData surfaceData, ObjectSceneData objectData)
{
    BuiltinData builtinData = (BuiltinData)0;
    float3 diffuseColor = ComputeDiffuseColor(surfaceData.baseColor, surfaceData.metallic);
    float3 fresnel0 = ComputeFresnel0(surfaceData.baseColor, surfaceData.metallic, surfaceData.specular);

    float3 viewDirWS = normalize(ce_CameraPos.xyz - psInput.positionWS.xyz);
    float NoV = max(0.0001, saturate(dot(surfaceData.normalWS, viewDirWS)));
    float3 refleDirW = normalize(reflect(-viewDirWS, normalize(surfaceData.normalWS)));
    uint NumCulledReflectionCaptures = 0;
    uint DataStartIndex = 0;
    float indirectIrradiance = 0;
    uint shadingModelID = 0;
    float3 transformedV = viewDirWS;
    float3 transformedN = surfaceData.normalWS;
    float3 transformedR = 2 * dot(transformedV, transformedN) * transformedN - transformedV;
    float transformedNoV = max(0.0001, saturate(dot(transformedN, transformedV)));
    transformedR = GetOffSpecularPeakReflectionDir(transformedN, transformedR, surfaceData.roughness);
    float specularOcclusion = GetSpecularOcclusion(transformedNoV, surfaceData.roughness * surfaceData.roughness, 1.0);
    float compositeAlpha = 1.0;
    float screenSpaceAo = 1.0; // to do
    float3 BentNormal = surfaceData.normalWS;
    builtinData.indirectLighting += GetSkySHOnly(surfaceData, V, surfaceData.normalWS, ce_UE4AmbientProbeSH, ce_SkyLightColor * ce_SkyLightIntensity, /*_SkyLight_Intensity*/1.0,
    screenSpaceAo, BentNormal, diffuseColor, fresnel0, ENABLE_SKY_LIGHT_REALTIME_CAPTURE);

    if (ENABLE_SKY_LIGHT_REALTIME_CAPTURE)
    {
        transformedR = float3(transformedR.x, -transformedR.z, transformedR.y);
    }
    float3 specularIBL = GatherRadiance(_CulledLightsDataGrid, _CaptureReflectionCubemaps, texture_sampler, _ReflectionCapturesMipNum,
                ce_RPExtentMipmapCount, ce_ReflectionProbePosDistance, ce_ReflectionProbeEulerRot,
#ifdef CE_USE_DOUBLE_TRANSFORM
                ce_CameraTilePosition,
                ce_ReflectionProbeTilePos,
#endif
                ce_SkyLightTexture,
                ce_PreSkyLightTexture,
                0.f,  // 0 for disable blend
                texture_sampler,
                ce_SkyLightColor,
                ce_SkyLightIntensity,
                compositeAlpha, psInput.positionWS, refleDirW, transformedR, surfaceData.roughness, indirectIrradiance, shadingModelID, NumCulledReflectionCaptures, DataStartIndex, specularOcclusion);
    specularIBL = specularIBL * EnvBRDFApprox(fresnel0, surfaceData.roughness, NoV);
    builtinData.indirectLighting += specularIBL.rgb;

    builtinData.motionVector = 0;

    return builtinData;
}

#endif // end PARTICLE_PASS
#endif // end SHADOW_PASS

#ifndef TRANSFER_CUSTOM_DATA
void TransferCustomDataSurface(in PSInput psInput, inout SurfaceInput surfaceInput)
{
}
#endif

//-----------------------------------------------------------------------------
// GetObjectSceneData
//-----------------------------------------------------------------------------

#ifndef SHADOW_PASS
// for GPass
ObjectSceneData GetObjectSceneData(uint instanceID)
{
#ifdef PARTICLE_PASS
    return ce_PerObject[instanceID];
#elif defined(USED_WITH_TERRAIN)
    uint objectIndex = _ObjectIndexBuffer[instanceID];
    return ce_PerObject[objectIndex];
#elif defined(CE_INSTANCING)
    #if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Vegetation
        if (USE_FOLIAGE_INSTANCE)
        {
            FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[instanceID];
            FoliageObjectSceneData foliageObjectData;
            DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], foliageObjectData);

            ObjectSceneData objectData = (ObjectSceneData)0;
            objectData.ce_World = foliageObjectData.world;
            objectData.ce_PreWorld = foliageObjectData.preWorld;
            #ifdef CE_USE_DOUBLE_TRANSFORM
                objectData.ce_TilePosition = foliageObjectData.tilePosition;
            #endif
            objectData.ce_InvTransposeWorld = foliageObjectData.invTransposeWorld;
            objectData.ce_InvWorld = foliageObjectData.invWorld;

            // not support yet
            objectData.ce_ReflectionProbeCount = 0;
            objectData.ce_ReflectionProbeIndex = 0;
            objectData.ce_ReflectionProbeWeight = 0;

            return objectData;
        }
        else
        {
            return ce_PerObject[instanceID];
        }
    #else
        // get objectData from GPUScene
        return ce_PerObject[instanceID];
    #endif
#else
    // get objectData from ConstBuffer
    ObjectSceneData objectData;

    #if defined(INSTANCING) && INSTANCING == 1
    #else
        objectData.ce_World = ce_World;
        objectData.ce_PreWorld = ce_PreWorld;
        objectData.ce_InvWorld = ce_InvWorld;
    #endif

        // objectData.ce_LODIndex = ce_LODIndex;
        // objectData.ce_ReflectionProbeCount = ce_ReflectionProbeCount;
        // objectData.ce_ReflectionProbeIndex = ce_ReflectionProbeIndex;
        // objectData.ce_ReflectionProbeWeight = ce_ReflectionProbeWeight;

    #if defined(LIGHT_MAP_ENABLE) && LIGHT_MAP_ENABLE == 1
        objectData.ce_lmUVBias = ce_lmUVBias;
        objectData.ce_lmUVScale = ce_lmUVScale;
        objectData.ce_lmColorScale = ce_lmColorScale;
        objectData.ce_lmColorAdd = ce_lmColorAdd;
        objectData.ce_lmDirectionScale = ce_lmDirectionScale;
        objectData.ce_lmDirectionAdd = ce_lmDirectionAdd;
        objectData.ce_lmInvPenumbraSize = ce_lmInvPenumbraSize;
        objectData.ce_lmTransferScale = ce_lmTransferScale;
        objectData.ce_lmTransferAdd = ce_lmTransferAdd;
        objectData.ce_localLMUVBias = ce_localLMUVBias;
        objectData.ce_localLMUVScale = ce_localLMUVScale;
        objectData.ce_localLMTransferScale = ce_localLMTransferScale;
        objectData.ce_localLMTransferAdd = ce_localLMTransferAdd;
        objectData.ce_localLightGroupColor = ce_localLightGroupColor;
    #elif defined(ENABLE_VOLUMETRIC_LIGHT_MAP) && ENABLE_VOLUMETRIC_LIGHT_MAP == 1
        objectData.ce_LightProbeSH = ce_LightProbeSH;

        objectData.ce_vlmWorldToUVScale = ce_vlmWorldToUVScale;
        objectData.ce_vlmWorldToUVAdd = ce_vlmWorldToUVAdd;
        objectData.ce_vlmIndirectionTextureSize = ce_vlmIndirectionTextureSize;
        objectData.ce_vlmBrickTexelSize = ce_vlmBrickTexelSize;
        objectData.ce_vlmBrickSize = ce_vlmBrickSize;
    #endif

    return objectData;
#endif
}
#else
// for ShadowPass
ObjectSceneData GetObjectSceneData(uint instanceID)
{
#ifdef CE_INSTANCING
    #if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Vegetation
        FoliageCompactSceneData compactData = _FoliageObjectSceneDatas[instanceID];
        FoliageObjectSceneData foliageObjectData;
        DecodeFoliageCompactSceneData(compactData, _FoliageEntityBuffer[compactData.entityIndex], foliageObjectData);

        ObjectSceneData objectData = (ObjectSceneData)0;
        objectData.ce_World = transpose(foliageObjectData.world);
        objectData.ce_PreWorld = transpose(foliageObjectData.preWorld);
        #ifdef CE_USE_DOUBLE_TRANSFORM
            objectData.ce_TilePosition = foliageObjectData.tilePosition;
        #endif

        return objectData;
    #else
        // get objectData from GPUScene
        return ce_PerObject[instanceID];
    #endif
#else
    // get objectData from ConstBuffer
    ObjectSceneData objectData = (ObjectSceneData)0;

    #if defined(INSTANCING) && INSTANCING == 1
    #else
        objectData.ce_PreWorld = ce_PreWorld;
        objectData.ce_World = ce_World;
    #endif
    return objectData;
#endif
}
#endif // end SHADOW_PASS

//-----------------------------------------------------------------------------
// GetSurfaceAndBuiltinData
//-----------------------------------------------------------------------------

void GetSurfaceAndBuiltinData(float3 V, PSInput psInput, bool isFrontFace, uint instanceID, out SurfaceData surfaceData, out BuiltinData builtinData)
{
    // get object param
    ObjectSceneData objectData = GetObjectSceneData(instanceID);

    // GetSurfaceData
    SurfaceInput surfaceInput = (SurfaceInput)0;
    surfaceInput.V = V;

#ifdef VERTEX_NEED_VERTEX_COLOR
	surfaceInput.vertexColor = psInput.vertexColor;
#endif

#ifdef USE_PARTICLE_COLOR
    surfaceInput.particleColor = psInput.color;
#endif

#if NUM_MATERIAL_TEXCOORDS > 0
    surfaceInput.uv = psInput.uv;
#ifdef MATERIAL_EDITOR
    surfaceInput.uvs = psInput.uvs;
#endif
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
    surfaceInput.uv1 = psInput.uv1;
#endif
#if NUM_MATERIAL_TEXCOORDS > 2
    surfaceInput.uv2 = psInput.uv2;
#endif
#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
    surfaceInput.uvIdx = psInput.uvIdx;
#else
    surfaceInput.uvIdx = float4(0, 0, 0, 0);
#endif
    surfaceInput.screenUV = psInput.screenUV;
    surfaceInput.positionWS = psInput.positionWS;
    surfaceInput.positionNDC = psInput.positionNDC.xyz;
    surfaceInput.normalWS = psInput.normalWS;
    surfaceInput.tangentWS = psInput.tangentWS;
    surfaceInput.binormalWS = psInput.binormalWS;
    surfaceInput.instanceID = instanceID;
    surfaceInput.isFrontFace = isFrontFace;

#if defined(USE_VERTEX_COLOR)
surfaceInput.vertexColor = psInput.color;
#endif

#ifdef SET_CUSTOM_SURFACE_INPUT
    SET_CUSTOM_SURFACE_INPUT
#endif

    TransferCustomDataSurface(psInput, surfaceInput);

    float3x3 tangentToWorld = float3x3(surfaceInput.tangentWS, surfaceInput.binormalWS, surfaceInput.normalWS);
    surfaceInput.TangentToWorld = tangentToWorld;


#ifdef ENABLE_VSM
	PageInfo pageInfo = UnpackPageInfo(psInput.packedPageInfo);
	VirtualShadowMapViewData viewData = _VirtualShadowMapViewDatas[pageInfo.viewId];
    surfaceInput.CameraTilePosition = viewData.tilePosition;
#elif defined(USE_MULTI_VIEWPORT) && defined(CE_INSTANCING)
    LocalLightViewData viewData = _LocalLightViewDatas[psInput.lightViewIndex];
    surfaceInput.CameraTilePosition = viewData.lightTileAndRange.xyz;
#else
    surfaceInput.CameraTilePosition = ce_CameraTilePosition;
#endif

#ifdef MATERIAL_EDITOR
    PrimitiveSceneData primitiveData = ce_PerPrimitive[objectData.ce_PrimitiveIndex];
    surfaceData = GetSurfaceData(primitiveData, objectData, surfaceInput);
#else
    #ifdef CE_INSTANCING
        surfaceData = GetSurfaceData(objectData, surfaceInput);
    #else
        surfaceData = GetSurfaceData(surfaceInput);
    #endif
#endif

#ifdef MATERIAL_EDITOR
    MaterialData matData = ce_PerMaterial[primitiveData.ce_MaterialIndex];
#endif

    // Clip
#if defined(MATERIAL_EDITOR) && defined(RAY_TRACING_PASS)
    if (matData.ALPHA_CLIPPING)
#else
    if (ALPHA_CLIPPING)
#endif
    {
        clip(surfaceData.opacityMask - surfaceData.opacityMaskClip);
    }

    // Clamp roughness
    surfaceData.roughness = MakeRoughnessSafe(surfaceData.roughness, M_RoughnessFloatMin);
    
#ifndef SHADOW_PASS
    if (surfaceData.materialType == MaterialType_Unlit)
    {
        builtinData = (BuiltinData)0;
    }
    else
    {
        builtinData = GetBuiltinData(V, psInput, isFrontFace, surfaceData, objectData);
    }
#else
    builtinData = (BuiltinData)0;
#endif
    
    // geomNormalWS + normalWS
    surfaceData.geomNormalWS = psInput.normalWS;
    float3 normalTS = surfaceData.normalTS;
    if (DOUBLE_SIDED && !isFrontFace)
    {
        normalTS.x *= builtinData._DoubleSidedConstants.x;
        normalTS.y *= builtinData._DoubleSidedConstants.y;
        normalTS.z *= builtinData._DoubleSidedConstants.z;
    }
#ifndef USE_WORLDSPACE_NORMALMAP
    surfaceData.normalWS = normalize(mul(normalTS, tangentToWorld)); 
#else
    surfaceData.normalWS = normalTS;
#endif

#ifdef MATERIAL_EDITOR
    builtinData.ForceDisableExponentialFog = matData.ForceDisableExponentialFog;
    builtinData.ForceDisableVolumetricFog = matData.ForceDisableVolumetricFog;
    builtinData.ForceDisableCloudFog = matData.ForceDisableCloudFog;
    builtinData._DoubleSidedConstants = matData._DoubleSidedConstants;

    surfaceData.roughness = GeometricSpecularAntiAliasing(surfaceData.roughness, surfaceData.normalWS, matData._SpecularAntiAliasingVariance, matData._SpecularAntiAliasingThreshold);
#else
    builtinData.ForceDisableExponentialFog = ForceDisableExponentialFog;
    builtinData.ForceDisableVolumetricFog = ForceDisableVolumetricFog;
    builtinData.ForceDisableCloudFog = ForceDisableCloudFog;
    builtinData._DoubleSidedConstants = _DoubleSidedConstants;

    surfaceData.roughness = GeometricSpecularAntiAliasing(surfaceData.roughness, surfaceData.normalWS, _SpecularAntiAliasingVariance, _SpecularAntiAliasingThreshold);
#endif

    if (MATERIAL_ERROR_FLAG){
#ifdef IS_GPASS
        surfaceData.baseColor = float4(1.f, 0.f, 1.f, 1.f);
#else if defined(IS_FORWARD)
        surfaceData.emissiveColor = float3(1.f, 0.f, 1.f);
#endif
    }

    // Set objCullingGUID for SmartSurfel usage
#ifdef MATERIAL_EDITOR
    surfaceData.objCullingGUID = objectData.ce_ObjectCullingGUID;
#else
    surfaceData.objCullingGUID = 0;
#endif
}

//-----------------------------------------------------------------------------
// ConvertSurfaceDataToBSDFData
//-----------------------------------------------------------------------------

BSDFData ConvertSurfaceDataToBSDFData(SurfaceData surfaceData)
{
    BSDFData bsdfData = (BSDFData)0;

    bsdfData.normalWS = surfaceData.normalWS;
    bsdfData.geomNormalWS = surfaceData.geomNormalWS;
    bsdfData.roughness = surfaceData.roughness;
    bsdfData.materialType = surfaceData.materialType;
    bsdfData.metallic = surfaceData.metallic;

    if (bsdfData.materialType == MaterialType_Unlit)
    {
        bsdfData.subsurfaceColor = surfaceData.subsurfaceColor;
    }

    bsdfData.diffuseColor = ComputeDiffuseColor(surfaceData.baseColor, surfaceData.metallic);
    bsdfData.fresnel0 = ComputeFresnel0(surfaceData.baseColor, surfaceData.metallic, surfaceData.specular);
    bsdfData.ambientOcclusion = surfaceData.ambientOcclusion;

    return bsdfData;
}

#endif

float GetSphereSinAlpha(float SourceRadius, float roughness, float InvDist)
{
    const float a = Pow2(roughness);
    return SourceRadius * InvDist * (1.f - a);
}

float GetSphereSinAlphaSoft(float SoftSourceRadius, float InvDist)
{
    return SoftSourceRadius * InvDist;
}

float SphereHorizonCosWrap( float NoL, float SinAlphaSqr )
{
    float SinAlpha = sqrt( SinAlphaSqr );
    if( NoL < SinAlpha )
    {
        NoL = max( NoL, -SinAlpha );
        NoL = Pow2( SinAlpha + NoL ) / ( 4 * SinAlpha );
    }
    return NoL;
}

struct BxDFContext
{
    float NoV;
    float NoL;
    float VoL;
    float NoH;
    float VoH;
    // not used now
    float XoV;
    float XoL;
    float XoH;
    float YoV;
    float YoL;
    float YoH;
};

void Init( inout BxDFContext Context, float3 N, float3 V, float3 L )
{
    Context.NoL = dot(N, L);
    Context.NoV = dot(N, V);
    Context.VoL = dot(V, L);
    float InvLenH = rsqrt( 2 + 2 * Context.VoL );
    Context.NoH = saturate( ( Context.NoL + Context.NoV ) * InvLenH );
    Context.VoH = saturate( InvLenH + InvLenH * Context.VoL );

    Context.XoV = 0.0f;
    Context.XoL = 0.0f;
    Context.XoH = 0.0f;
    Context.YoV = 0.0f;
    Context.YoL = 0.0f;
    Context.YoH = 0.0f;
}

void SphereMaxNoH( inout BxDFContext Context, float SinAlpha, bool bNewtonIteration )
{
    if( SinAlpha > 0 )
    {
        float CosAlpha = sqrt( 1 - Pow2( SinAlpha ) );
        float RoL = 2 * Context.NoL * Context.NoV - Context.VoL;
        if( RoL >= CosAlpha )
        {
            Context.NoH = 1;
            Context.XoH = 0;
            Context.YoH = 0;
            Context.VoH = abs( Context.NoV );
        }
        else
        {
            float rInvLengthT = SinAlpha * rsqrt( 1 - RoL*RoL );
            float NoTr = rInvLengthT * ( Context.NoV - RoL * Context.NoL );
            float VoTr = rInvLengthT * ( 2 * Context.NoV*Context.NoV - 1 - RoL * Context.VoL );
            if (bNewtonIteration)
            {
                float NxLoV = sqrt( saturate( 1 - Pow2(Context.NoL) - Pow2(Context.NoV) - Pow2(Context.VoL) + 2 * Context.NoL * Context.NoV * Context.VoL ) );
                float NoBr = rInvLengthT * NxLoV;
                float VoBr = rInvLengthT * NxLoV * 2 * Context.NoV;
                float NoLVTr = Context.NoL * CosAlpha + Context.NoV + NoTr;
                float VoLVTr = Context.VoL * CosAlpha + 1   + VoTr;
                float p = NoBr   * VoLVTr;
                float q = NoLVTr * VoLVTr;
                float s = VoBr   * NoLVTr;
                float xNum = q * ( -0.5 * p + 0.25 * VoBr * NoLVTr );
                float xDenom = p*p + s * (s - 2*p) + NoLVTr * ( (Context.NoL * CosAlpha + Context.NoV) * Pow2(VoLVTr) + q * (-0.5 * (VoLVTr + Context.VoL * CosAlpha) - 0.5) );
                float TwoX1 = 2 * xNum / ( Pow2(xDenom) + Pow2(xNum) );
                float SinTheta = TwoX1 * xDenom;
                float CosTheta = 1.0 - TwoX1 * xNum;
                NoTr = CosTheta * NoTr + SinTheta * NoBr;
                VoTr = CosTheta * VoTr + SinTheta * VoBr;
            }
            Context.NoL = Context.NoL * CosAlpha + NoTr; 
            Context.VoL = Context.VoL * CosAlpha + VoTr;
            float InvLenH = rsqrt( 2 + 2 * Context.VoL );
            Context.NoH = saturate( ( Context.NoL + Context.NoV ) * InvLenH );
            Context.VoH = saturate( InvLenH + InvLenH * Context.VoL );
        }
    }
}

//-----------------------------------------------------------------------------
// EvaluateBSDF
//-----------------------------------------------------------------------------

CBSDF EvaluateBSDF(float3 V, float3 L, BSDFData bsdfData,
    BxDFContext Context = (BxDFContext)0,
    float Falloff = 1.0,
    float SphereSinAlpha = 0.f,
    float SphereSinAlphaSoft = 0.f,
    float LineCosSubtended = 1.f)
{
    CBSDF cbsdf = (CBSDF)0;

    if (bsdfData.materialType == MaterialType_Unlit)
    {
        return cbsdf;
    }

    float3 N = bsdfData.normalWS;
    
    float imposterNdotL = Context.NoL;
    if (bsdfData.materialType == MaterialType_ImposterFoliage)
    {
        //imposterNdotL = clamp(NdotL, 0.3, 0.5);
        //imposterNdotL *= 2;
        imposterNdotL = clamp(Context.NoL, 0, 1);
        imposterNdotL -= 0.5;
        imposterNdotL = imposterNdotL / 2 + 0.5;
        Context.NoL = imposterNdotL;

        //imposterNdotL = 0.5;
        //NdotL = NdotL * NdotL * NdotL * NdotL * 2;
    }
    float clampedNdotV = SafeSaturate(Context.NoV);
    float clampedNdotL = SafeSaturate(Context.NoL);

    float LdotV = Context.VoL;
    float NdotH = Context.NoH;
    float LdotH = Context.VoH;
    
    float a2 = Pow4(bsdfData.roughness);
    // LdotH = VdotH
    float Energy = EnergyNormalization(a2, LdotH, SphereSinAlpha, SphereSinAlphaSoft, LineCosSubtended);
    float3 F = F_Schlick(bsdfData.fresnel0, LdotH);
    float D = D_GGX(a2, NdotH) * Energy;
    float Vis = Vis_SmithJointApprox(a2, clampedNdotV, clampedNdotL);

    float3 diffTerm = Diffuse_Lambert();
    float3 specTerm = F * D * Vis;

    cbsdf.diffR = bsdfData.diffuseColor * diffTerm * clampedNdotL * Falloff;

    if (bsdfData.materialType == MaterialType_TwosidedFoliage || bsdfData.materialType == MaterialType_ImposterFoliage)
    {
        // http://blog.stevemcauley.com/2011/12/03/energy-conserving-wrapped-diffuse/
        float wrap = 0.5;
        float wrapNdotL = saturate((-Context.NoL + wrap) / Square(1 + wrap));

        // Scatter distribution
        float scatter = D_GGX(0.6 * 0.6, saturate(-LdotV));

        cbsdf.diffT = wrapNdotL * scatter * bsdfData.subsurfaceColor * Falloff;
    }
    else if (bsdfData.materialType == MaterialType_Subsurface)
    {
        // to get an effect when you see through the material
    	// hard coded pow constant
        float inScatter = pow(saturate(-LdotV), 12) * lerp(3, 0.1, bsdfData.opacity);
        // wrap around lighting, /(PI*2) to be energy consistent (hack do get some view dependnt and light dependent effect)
	    // Opacity of 0 gives no normal dependent lighting, Opacity of 1 gives strong normal contribution
        float normalContribution = saturate(NdotH * bsdfData.opacity + 1 - bsdfData.opacity);
	    float backScatter = bsdfData.ambientOcclusion * normalContribution / (M_PI * 2);

        cbsdf.diffT = lerp(backScatter, 1, inScatter) * bsdfData.subsurfaceColor * Falloff;
    }

    cbsdf.specR = specTerm * clampedNdotL * Falloff;
    cbsdf.specT = 0;

    return cbsdf;
}

#include "./GbufferEncoderDecoder.hlsl"

#endif