#ifndef LIT_COMMON_STRUCT_HLSL
#define LIT_COMMON_STRUCT_HLSL

#define MaterialType_Standard 0
#define MaterialType_Unlit 1
#define MaterialType_TwosidedFoliage 2
#define MaterialType_ImposterFoliage 3
#define MaterialType_Subsurface 4

//-----------------------------------------------------------------------------
// SurfaceInput
//-----------------------------------------------------------------------------
  
struct SurfaceInput
{
	float3 V;
	float4 vertexColor;
#if NUM_MATERIAL_TEXCOORDS > 0
	float2 uv;
	float4 uvs[(NUM_MATERIAL_TEXCOORDS + 1) >> 1] : TEXCOORD0;
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
	float2 uv1;
#endif
#if NUM_MATERIAL_TEXCOORDS > 2
	float2 uv2;
#endif
	float4 uvIdx;
	float2 screenUV;
	float3 positionWS;
	float3 normalWS;
	float3 tangentWS;
	float3 binormalWS;
	float3 positionNDC; // x,y:[0, screenPixelCount), z:[0, 1]
	uint instanceID;
	bool isFrontFace;

	float3x3 TangentToWorld;
	float3 CameraTilePosition;

#ifdef USE_PARTICLE_COLOR
	float4 particleColor;
#endif

#ifdef USED_WITH_TERRAIN
	uint slotIndex;
#endif

#if defined(VERTEX_NEED_PER_INSTANCE_PARAMS)
	// x = per-instance fade out amount, y = hide/show flag, z dither fade cutoff, w - CustomData index
	float4  perInstanceParams;
#endif

#ifdef ADD_SURFACE_INPUT
	ADD_SURFACE_INPUT
#endif
};

//-----------------------------------------------------------------------------
// SurfaceData
//-----------------------------------------------------------------------------

#ifdef RAY_TRACING_PASS
#include "../RayTracing/RayTracingPayloadPack.hlsl"
#endif

struct SurfaceData
{
#ifdef RAY_TRACING_PASS
    uint normalWS;
    uint baseColor;
    float opacity;
    float opacityMask;
    
    float opacityMaskClip;
    float roughness;
    float ambientOcclusion;
    float metallic;
    
    uint emissiveColor;
    uint subsurfaceColor;
    float specular;
    uint normalTS;

    uint materialType;
#else
    uint materialType;
    float3 baseColor;
    
    float3 normalWS;
    float opacity;
    
    float opacityMask;
    float opacityMaskClip;
    float roughness;
    float ambientOcclusion;

    float3 emissiveColor;
    float metallic;

    float3 subsurfaceColor;
    float specular;

    float3 normalTS;
    
    uint objCullingGUID;
    
    float3 debugColor;
    float outDepth;
    
    float3 geomNormalWS;
    float2 temporalReactive;
#endif
};

#ifdef RAY_TRACING_PASS
    void EncodeBaseColor(inout SurfaceData surfaceData, float3 color)
    {
        surfaceData.baseColor = PackPayloadUNormFloat3(color);
    }
    float3 DecodeBaseColor(in SurfaceData surfaceData)
    {
        return UnpackPayloadUNormFloat3(surfaceData.baseColor);
    }

    void EncodeEmissiveColor(inout SurfaceData surfaceData, float3 color)
    {
        surfaceData.emissiveColor = PackPayloadUNormFloat3(color);
    }
    float3 DecodeEmissiveColor(in SurfaceData surfaceData)
    {
        return UnpackPayloadUNormFloat3(surfaceData.emissiveColor);
    }
        
    void EncodeSubsurfaceColor(inout SurfaceData surfaceData, float3 color)
    {
        surfaceData.subsurfaceColor = PackPayloadUNormFloat3(color);
    }
    float3 DecodeSubsurfaceColor(in SurfaceData surfaceData)
    {
        return PackPayloadUNormFloat3(surfaceData.subsurfaceColor);
    }

    void EncodeNormalTS(inout SurfaceData surfaceData, float3 n)
    {
        surfaceData.normalTS = PackPayloadNormal(n);
    }
    float3 DecodeNormalTS(in SurfaceData surfaceData)
    {
        return PackPayloadNormal(surfaceData.normalTS);
    }

    void EncodeNormalWS(inout SurfaceData surfaceData, float3 n)
    {
        surfaceData.normalWS = PackPayloadNormal(n);
    }
    float3 DecodeNormalWS(in SurfaceData surfaceData)
    {
        return PackPayloadNormal(surfaceData.normalWS);
    }
#endif

float GetPerInstanceFadeAmount(in SurfaceInput input)
{
#if defined(VERTEX_NEED_PER_INSTANCE_PARAMS)
	return float(input.perInstanceParams.x);
#else
	return float(1.0);
#endif
}

SurfaceData GetDefaultSurfaceData()
{
	SurfaceData surfacedata= (SurfaceData)0;

	surfacedata.opacity = 1.0;
	surfacedata.opacityMask = 1.0;
	surfacedata.opacityMaskClip = 1.0;

	surfacedata.roughness = 0.5;
	surfacedata.ambientOcclusion = 1.0;
	surfacedata.metallic = 0.0;
	surfacedata.specular = 0.5;

#ifdef RAY_TACING_PASS
    EncodeBaseColor(surfacedata, float3(1.0, 1.0 ,1.0));
    EncodeNormalTS(surfacedata, float3(0, 0, 1));
    EncodeEmissiveColor(surfacedata, float3(0, 0, 0));
    EncodeSubsurfaceColor(surfacedata, float3(0, 0, 0));
#else
    surfacedata.baseColor = float3(1.0, 1.0 ,1.0);
    surfacedata.normalTS = float3(0, 0, 1);
	surfacedata.emissiveColor = float3(0, 0, 0);
	surfacedata.subsurfaceColor = float3(0, 0, 0);
#endif

#ifndef RAY_TRACING_PASS
	surfacedata.temporalReactive = float2(0, 0);
	surfacedata.objCullingGUID = 0;
#endif

	return surfacedata;
}

//-----------------------------------------------------------------------------
// BuiltinData
//-----------------------------------------------------------------------------

struct BuiltinData
{
	float3 indirectLighting;
    float bakeShadow;
	float2 motionVector;
	float coverage;
    bool ForceDisableExponentialFog;
    bool ForceDisableVolumetricFog;
    bool ForceDisableCloudFog;
    float4 _DoubleSidedConstants;
};

//-----------------------------------------------------------------------------
// BSDFData
//-----------------------------------------------------------------------------

struct BSDFData
{
	uint materialType;
	float3 diffuseColor;
	float3 fresnel0;
	float3 normalWS;
	float3 geomNormalWS;
	float roughness;
	float3 subsurfaceColor;
	float opacity;
	float2 lm_uv;
	float ambientOcclusion;
	float metallic;
};


#endif