#ifndef LIT_DATA_TERRAIN_HLSL
#define LIT_DATA_TERRAIN_HLSL

#include "LitCommonStruct.hlsl"
#include "../MaterialUtilities.hlsl"
#include "../../ShaderLibrary/Common.hlsl"

#define NUM_MAX_TERRAIN_BLEND_LAYERS 8U
#define NUM_EFFECTIVE_TERRAIN_BLEND_LAYERS 8U

float4 SampleHeightmapData(float2 UV, uint Index)
{
#ifdef TERRAIN_USE_INSTANCING
    return _TerrainHeightmapStore.Sample(ce_Sampler_Clamp, float3(UV, Index >> 16U));
#else
    return Heightmap.Sample(ce_Sampler_Clamp, UV);
#endif
}

float3 SampleAlbedoTexture(float2 UV, uint Index)
{
#ifdef TERRAIN_USE_INSTANCING
    return _TerrainAlbedoTextureStore.Sample(ce_Sampler_TerrainAlbedo, float3(UV, Index >> 16U));
#else
    return AlbedoTexture.Sample(ce_Sampler_TerrainAlbedo, UV);
#endif
}

#ifndef DEFERRED_SHADING

#define USE_SIMPLE_TEXTURE_SAMPLE true
#if USE_SIMPLE_TEXTURE_SAMPLE

float3 FlattenNormal(float3 Normal, float Fraction)
{
    return lerp(Normal, float3(0, 0, 1), Fraction);
}

float4 SampleLayerTexture(Texture2D<float4> LayerTexture, float2 TexCoord, float2 UVScale, float Weight, float TexSampleBias)
{
    float4 Albedo = LayerTexture.SampleBias(ce_Sampler_Anisotropic, TexCoord * UVScale, TexSampleBias);
    return Albedo;
}

float3 SampleNormalTexture(Texture2D<float4> NormalTexture, float2 TexCoord, float2 UVScale, float Weight, float TexSampleBias, float NormalStrength)
{
    float4 NormalColor = NormalTexture.SampleBias(ce_Sampler_Anisotropic, TexCoord * UVScale, TexSampleBias);
    float3 Normal = UnpackNormalmapRGorAG(NormalColor, 1.0);
    Normal = FlattenNormal(Normal, NormalStrength);
    return Normal;
}

void GetTerrainBlendWeights(float4 Weights0, float4 Weights1, float4 Heights0, float4 Heights1, out float OutWeights[NUM_MAX_TERRAIN_BLEND_LAYERS])
{
    float4 OutWeights0 = Weights0;
    float4 OutWeights1 = Weights1;

    //OutWeights0 = float4(1.0, 0.0, 0.0, 0.0);
    //OutWeights1 = float4(0.0, 0.0, 0.0, 0.0);

    OutWeights[0] = OutWeights0.x;
    OutWeights[1] = OutWeights0.y;
    OutWeights[2] = OutWeights0.z;
    OutWeights[3] = OutWeights1.x;
    OutWeights[4] = OutWeights1.y;
    OutWeights[5] = OutWeights1.z;
    OutWeights[6] = 0.f;
    OutWeights[7] = 0.f;
}

#else

float InterleavedGradientNoise(float2 TexCoord)
{
    return frac(sin(dot(TexCoord, float2(12.9898f, 78.233f))) * 43758.5453123f);
}

// temporary code without performance consideration in mind
float4 CalculateSampleOffset(float2 Dim, float2 TexCoord, float2 UVScale, float4 PatchOffsetAndScale, out float Blend)
{
    float2 Resolution = GridDim.xy * Dim / PatchSize;
    float2 UVPosition = (PatchOffsetAndScale.xz / PatchSize + TexCoord) * Dim;
    float2 Scale = Dim / UVScale * 1.247f;
    float2 SPI = floor(UVPosition / Scale);
    float2 SPF = frac(UVPosition / Scale);

    float K0 = InterleavedGradientNoise((SPI + float2(0.f, 0.f)) * Scale / Resolution);
    float K1 = InterleavedGradientNoise((SPI + float2(1.f, 0.f)) * Scale / Resolution);
    float K2 = InterleavedGradientNoise((SPI + float2(0.f, 1.f)) * Scale / Resolution);
    float K3 = InterleavedGradientNoise((SPI + float2(1.f, 1.f)) * Scale / Resolution);
    float Kx0 = lerp(K0, K1, smoothstep(0.f, 1.f, SPF.x));
    float Kx1 = lerp(K2, K3, smoothstep(0.f, 1.f, SPF.x));
    float K = lerp(Kx0, Kx1, smoothstep(0.f, 1.f, SPF.y));

    float Index = K * 8.f;
    float F = frac(Index);
    
    float I0 = floor(Index);
    float I1 = I0 + 1.f;

    Blend = smoothstep(0.f, 1.f, F);
    return sin(float4(3.f, 7.f, 3.f, 7.f) * float4(I0, I0, I1, I1));
}

// temporary code without performance consideration in mind
float4 SampleLayerTexture(Texture2D<float4> LayerTexture, float2 TexCoord, float2 UVScale, float4 PatchOffsetAndScale, float Weight)
{
    if (Weight == 0.f)
    {
        return 0.f;
    }
    else
    {
        float2 Dim;
        LayerTexture.GetDimensions(Dim.x, Dim.y);

        float Blend;
        float4 Offset = CalculateSampleOffset(Dim, TexCoord, UVScale, PatchOffsetAndScale, Blend);

        TexCoord *= UVScale * PatchOffsetAndScale.w;
        float2 DuvDx = ddx(TexCoord);
        float2 DuvDy = ddy(TexCoord);
        
        float4 Color0 = LayerTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord + Offset.xy, DuvDx, DuvDy);
        float4 Color1 = LayerTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord + Offset.zw, DuvDx, DuvDy);

        if (ENABLE_TERRAIN_BLEND_ANTI_TILING)
        {
            return lerp(Color0, Color1, Blend);
        }
        else
        {
            return LayerTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord, DuvDx, DuvDy);
        }
    }
}

// temporary code without performance consideration in mind
float3 SampleNormalTexture(Texture2D<float3> NormalTexture, float2 TexCoord, float2 UVScale, float4 PatchOffsetAndScale, float Weight, float NormalStrength)
{
    if (Weight == 0.f)
    {
        return 0.f;
    }
    else
    {    
        float2 Dim;
        NormalTexture.GetDimensions(Dim.x, Dim.y);

        float Blend;
        float4 Offset = CalculateSampleOffset(Dim, TexCoord, UVScale, PatchOffsetAndScale, Blend);

        TexCoord *= UVScale * PatchOffsetAndScale.w;
        float2 DuvDx = ddx(TexCoord);
        float2 DuvDy = ddy(TexCoord);

        float3 Normal0 = 2.f * NormalTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord + Offset.xy, DuvDx, DuvDy) - 1.f;
        float3 Normal1 = 2.f * NormalTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord + Offset.zw, DuvDx, DuvDy) - 1.f;

        if (ENABLE_TERRAIN_BLEND_ANTI_TILING)
        {
            return lerp(Normal0, Normal1, Blend);
        }
        else
        {
            return 2.f * NormalTexture.SampleGrad(ce_Sampler_Anisotropic, TexCoord, DuvDx, DuvDy) - 1.f;
        }
    }
}

void GetTerrainBlendWeights(float4 Weights0, float4 Weights1, float4 Heights0, float4 Heights1, out float OutWeights[NUM_MAX_TERRAIN_BLEND_LAYERS])
{
    float4 OutWeights0 = Weights0;
    float4 OutWeights1 = Weights1;
    float TotalWeight = 0.f;

    float MaxHeight0 = max(max(Heights0.x, Heights0.y), max(Heights0.z, Heights0.w));
    float MaxHeight1 = max(max(Heights1.x, Heights1.y), max(Heights1.z, Heights1.w));
    float4 HeightsMask0 = 1.f - saturate((MaxHeight0 - Heights0) * _HeightBlendStrength);
    float4 HeightsMask1 = 1.f - saturate((MaxHeight1 - Heights1) * _HeightBlendStrength);

    OutWeights0 = (OutWeights0 + saturate(HeightsMask0 * _HeightBlendStrength)) * HeightsMask0 * BlendLayerMask0;
    OutWeights1 = (OutWeights1 + saturate(HeightsMask1 * _HeightBlendStrength)) * HeightsMask1 * BlendLayerMask1;
    TotalWeight = rcp(dot(OutWeights0, 1.f) + dot(OutWeights1, 1.f));

    OutWeights0 *= TotalWeight;
    OutWeights1 *= TotalWeight;

    OutWeights[0] = OutWeights0.x;
    OutWeights[1] = OutWeights0.y;
    OutWeights[2] = OutWeights0.z;
    OutWeights[3] = OutWeights0.w;
    OutWeights[4] = OutWeights1.x;
    OutWeights[5] = OutWeights1.y;
    OutWeights[6] = OutWeights1.z;
    OutWeights[7] = OutWeights1.w;

#if NUM_EFFECTIVE_TERRAIN_BLEND_LAYERS == 6U
    OutWeights[0] = OutWeights0.x;
    OutWeights[1] = OutWeights0.y;
    OutWeights[2] = OutWeights0.z;
    OutWeights[3] = OutWeights1.x;
    OutWeights[4] = OutWeights1.y;
    OutWeights[5] = OutWeights1.z;
    OutWeights[6] = 0.f;
    OutWeights[7] = 0.f;
#endif
}

#endif

#ifdef TERRAIN_OCEAN
SurfaceData GetOceanSurfaceData(ObjectSceneData objectData, SurfaceInput psInput)
{
	SurfaceData surfaceData = GetDefaultSurfaceData();

    // float2 psInput.uv = psInput.positionWS.yz * (10 / 65536);
    float3 subsurfaceColor = _SSColor;
	float4 color = _BaseMapOcean.Sample(ce_Sampler_Repeat, psInput.uv) *_BaseColor;// 采样BaseMap
	float4 MROcolor = _MROTex.Sample(ce_Sampler_Repeat, psInput.uv) ;//
	float4 TileBreakerValue = _TileBreaker.Sample(ce_Sampler_Repeat, psInput.uv*_TileBreakerTile) ;
    // float3 normalTS = 0.5*(_NormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_NrmlTexTile + ce_Time.w/10).rgb + _NormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_NrmlTexTile/7.283 + ce_Time.w/10).rgb);
    float3 normalTS0 = _NormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_NrmlTexTile + ce_Time.w/1).rgb;
    float3 normalTS1 = _NormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_NrmlTexTile/3 - ce_Time.w/3).rgb;
    float3 sndnormalTS = _sndNormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_sndNrmlTexTile - ce_Time.w/5).rgb;
    float3 trdnormalTS = _trdNormalTex.Sample(ce_Sampler_Repeat, psInput.uv*_trdNrmlTexTile - ce_Time.w/4).rgb;
    float4 foamUtility = _FoamTex.Sample(ce_Sampler_Repeat, psInput.uv*200 - ce_Time.w);


    normalTS0 = UnpackNormalmapRGorAG(float4(normalTS0, 1), _NormalScale);
    normalTS1 = UnpackNormalmapRGorAG(float4(normalTS1, 1), _NormalScale);
    sndnormalTS = UnpackNormalmapRGorAG(float4(sndnormalTS, 1), _sndNormalScale);
    trdnormalTS = UnpackNormalmapRGorAG(float4(trdnormalTS, 1), _trdNormalScale);


    // normalTS1 = normalTS1 * 2 - 1;
    // normalTS1.xy *= _NormalScale;
    // normalTS1.z = sqrt(1 - dot(normalTS1.xy, normalTS1.xy));
    normalTS0 = normalize(float3(-normalTS0.y, normalTS0.x, normalTS0.z));
    normalTS1 = normalize(float3(-normalTS1.y, normalTS1.x, normalTS1.z));
    sndnormalTS = normalize(float3(-sndnormalTS.y, sndnormalTS.x, sndnormalTS.z));
    trdnormalTS = normalize(float3(-trdnormalTS.y, trdnormalTS.x, trdnormalTS.z));
    

    normalTS0 = 1 * normalTS0 + 1 * normalTS1;
    float depthInterpolate = smoothstep(_SplitDepth - 0.5*_SplitTransition, _SplitDepth + 0.5*_SplitTransition, psInput.positionNDC.z);
	sndnormalTS = lerp( sndnormalTS, normalTS0, min(1,depthInterpolate));
	trdnormalTS = lerp(sndnormalTS , trdnormalTS, log(TileBreakerValue.x * 2) ); 
    // trdnormalTS.z = 1;


    // normalTS0 = normalize(1 * normalTS0 + 1 * normalTS1);
    // float depthInterpolate = smoothstep(_SplitDepth - 0.5*_SplitTransition, _SplitDepth + 0.5*_SplitTransition, psInput.positionNDC.z);
	// sndnormalTS.xy = lerp( sndnormalTS.xy * _sndNormalScale, normalTS0.xy * _NormalScale ,min(1,depthInterpolate));
	// trdnormalTS.xy = lerp(sndnormalTS.xy , trdnormalTS.xy * _trdNormalScale , log(TileBreakerValue.x * 2) );
	// trdnormalTS.z = 1; 
	

	// surfaceData.baseColor	 = max(color.rgb, foamUtility.rgb) ;
    surfaceData.baseColor = _BaseColor;
	if (color.a < 0.333)
		discard;
//
	float3x3 tangentToWorld = float3x3(psInput.tangentWS, psInput.binormalWS, psInput.normalWS); 
	float3x3 tangentToLocal = float3x3(mul(objectData.ce_InvTransposeInvWorld , psInput.tangentWS).xyz , mul( objectData.ce_InvTransposeInvWorld , psInput.binormalWS ).xyz, mul( objectData.ce_InvTransposeInvWorld ,psInput.normalWS).xyz); 
	float3x3 tangentToLocal0 = float3x3(mul( psInput.tangentWS , objectData.ce_InvTransposeInvWorld ).xyz , mul( psInput.binormalWS , objectData.ce_InvTransposeInvWorld  ).xyz, mul( psInput.normalWS , objectData.ce_InvTransposeInvWorld  ).xyz); 
//	float3x3 tangentToLocalNew = float3x3(psInput.tangent, psInput.binormal, psInput.normal);

	// float3 PixelNormalLocal = normalize(mul(tangentToLocal , trdnormalTS ));
	float3 PixelNormalLocal = normalize(mul( trdnormalTS , tangentToLocal  ));

// pxnm subcolor:
    float3 N = normalize(psInput.normalWS);
    // N = psInput.normalWS;
	float3 V = normalize(ce_CameraPos.xyz - psInput.positionWS.xyz);
	float3 CamVec = normalize(psInput.positionWS.xyz - ce_CameraPos.xyz);
	float NdotV = saturate(dot(N, V));
	float SubThickness = max(0 , dot((mul( objectData.ce_InvTransposeInvWorld , ce_Lights[0].LightDirPos ).xyz) , float3( PixelNormalLocal.x, PixelNormalLocal.y /max(0.01 , _DetailScatterCurvature) , PixelNormalLocal.z)) );
	float3 SubColor = lerp(float3(0,0,0) , subsurfaceColor * 30 * _DetailScatterIntensity, SubThickness * 0.00001 ); 

	float3 ligtDirLocal = normalize(mul( objectData.ce_InvTransposeInvWorld , ce_Lights[0].LightDirPos  ).xyz ); 
// light dir mask:
	float lightDirMask = max(0,dot( float3(ligtDirLocal.x , _VerticleScatterDistrbt , ligtDirLocal.z )  ,  normalize(mul(  objectData.ce_InvTransposeInvWorld , -CamVec ).xyz )) );
	// lightDirMask = pow(lightDirMask , 1/max(0.03 ,_ScatterRange));
// tmp fresnelMask 
	float FresnelMask = pow((1 - NdotV),  2)* _ScatterFresnelRate ;
	// float SubColorMask = saturate(FresnelMask * lightDirMask);
    float SubColorMask = saturate(FresnelMask * 1);


	//float3 EmissiveColor = lerp(float3(0,0,0) , SubColor , SubColorMask);
	float3 EmissiveColor = lerp(SubColorMask * subsurfaceColor *_DeepScatterIntensity , SubColor , SubColorMask) *  ce_Lights[0].LightColor * _ScatterIntensity;

    //float3 NormalTS = trdnormalTS.x * normalize(psInput.tangentWS) + trdnormalTS.z * normalize(psInput.positionWS) + trdnormalTS.y * normalize(psInput.binormalWS);
    float3 NormalTS = trdnormalTS.y * normalize(psInput.tangentWS) + trdnormalTS.z * normalize(psInput.positionWS) + trdnormalTS.x * normalize(psInput.binormalWS);

    // surfaceData.normalTS = NormalTS; 
	// surfaceData.metallic = 0;// ;
	// surfaceData.roughness = MROcolor.g * _RoughnessScale;
	// surfaceData.emissiveColor = EmissiveColor;
	// surfaceData.materialType = MATERIAL_TYPE;
    // surfaceData.specular = 0.5;
	// surfaceData.debugColor = lightDirMask;

    //surfaceData.geomNormalWS = float3(0,0,1);
    surfaceData.normalTS = trdnormalTS;
    //surfaceData.normalWS = float3(0,0,1);
	surfaceData.metallic = 0;// ;
	surfaceData.roughness = MROcolor.g * _RoughnessScale;
    surfaceData.emissiveColor = EmissiveColor;
	surfaceData.materialType = MATERIAL_TYPE;
    surfaceData.specular = 0.5;
	surfaceData.debugColor = lightDirMask;

	return surfaceData;
}
#endif

#ifndef GET_SURFACE_DATA_CALLBACK
SurfaceData GetSurfaceDataCallBack(SurfaceData primary, SurfaceInput input)
{
    return primary;
}
#endif

#ifdef CE_INSTANCING
SurfaceData GetSurfaceData(ObjectSceneData objectData, inout SurfaceInput Input)
#else
SurfaceData GetSurfaceData(inout SurfaceInput Input)
#endif
{
	SurfaceData Data = (SurfaceData)0;

#ifdef TERRAIN_OCEAN
#ifdef CE_INSTANCING
    Data = GetOceanSurfaceData(objectData,Input);
    return Data;
#endif
    //float3 Normal = Input.normalWS;
#else
    //float4 HeightmapData = SampleHeightmapData(Input.uv, Input.instanceID);
    //float4 NormalAndHeight = DecodeHeightmap(HeightmapData, SURFACE_TYPE);
    //float3 Normal = normalize(NormalAndHeight.xyz) * float3(1.f, sign(Input.normalWS.y), 1.f);
    //float3 Normal = NormalAndHeight.x * normalize(Input.tangentWS) + NormalAndHeight.y * normalize(Input.positionWS) + NormalAndHeight.z * normalize(Input.binormalWS);
    float3 Normal = normalize(Input.normalWS);

    float3 Albedo;
    float3 NormalTS;
    float3 MRA;

#if USE_SIMPLE_TEXTURE_SAMPLE
    float2 UV = (GetLargeCoordinateAbsolutePosition(Input.positionWS.xz, ce_CameraTilePosition.xz) - WorldTranslation.xz) / WorldScale.xz * 0.002;
#else
    float2 UV = Input.uv;
#endif
    float Specular = 0.0;

    if (USE_WEIGHT_BLEND)
    {
        float TexSampleBias = (1.0 - pow(saturate(length(ce_CameraPos.xyz - Input.positionWS.xyz) / _TexSampleBiasCriticleDistance), _TexSampleBiasCurveParam)) * _TextureSampleBias;

        // float4 HMRA0 = _HMRA0 * SampleLayerTexture(HMRATexture0, UV, _LayerTextureUVScale0, 1.f, TexSampleBias);
        // float4 HMRA1 = _HMRA1 * SampleLayerTexture(HMRATexture1, UV, _LayerTextureUVScale1, 1.f, TexSampleBias);
        // float4 HMRA2 = _HMRA2 * SampleLayerTexture(HMRATexture2, UV, _LayerTextureUVScale2, 1.f, TexSampleBias);
        // float4 HMRA3 = _HMRA3 * SampleLayerTexture(HMRATexture3, UV, _LayerTextureUVScale3, 1.f, TexSampleBias);
        // float4 HMRA4 = _HMRA4 * SampleLayerTexture(HMRATexture4, UV, _LayerTextureUVScale4, 1.f, TexSampleBias);
        // float4 HMRA5 = _HMRA5 * SampleLayerTexture(HMRATexture5, UV, _LayerTextureUVScale5, 1.f, TexSampleBias);
        // float4 HMRA6 = _HMRA6 * SampleLayerTexture(HMRATexture6, UV, _LayerTextureUVScale6, 1.f, TexSampleBias);
        // float4 HMRA7 = _HMRA7 * SampleLayerTexture(HMRATexture7, UV, _LayerTextureUVScale7, 1.f, TexSampleBias);

        //float Weights[NUM_MAX_TERRAIN_BLEND_LAYERS];
        float4 Weights0 = WeightTexture0.Sample(ce_Sampler_Clamp, Input.uv) * BlendLayerMask0;
        float4 Weights1 = WeightTexture1.Sample(ce_Sampler_Clamp, Input.uv) * BlendLayerMask1;
        //GetTerrainBlendWeights(Weights0, Weights1, float4(HMRA0.x, HMRA1.x, HMRA2.x, HMRA3.x), float4(HMRA4.x, HMRA5.x, HMRA6.x, HMRA7.x), Weights);

        float4 BaseColor0 = _BaseColor0 * SampleLayerTexture(BaseColorTexture0, UV, _LayerTextureUVScale0, Weights0.r, TexSampleBias);
        float4 BaseColor1 = _BaseColor1 * SampleLayerTexture(BaseColorTexture1, UV, _LayerTextureUVScale1, Weights0.g, TexSampleBias);
        float4 BaseColor2 = _BaseColor2 * SampleLayerTexture(BaseColorTexture2, UV, _LayerTextureUVScale2, Weights0.b, TexSampleBias);
        float4 BaseColor3 = _BaseColor3 * SampleLayerTexture(BaseColorTexture3, UV, _LayerTextureUVScale3, Weights0.a, TexSampleBias);
        float4 BaseColor4 = _BaseColor4 * SampleLayerTexture(BaseColorTexture4, UV, _LayerTextureUVScale4, Weights1.r, TexSampleBias);
        float4 BaseColor5 = _BaseColor5 * SampleLayerTexture(BaseColorTexture5, UV, _LayerTextureUVScale5, Weights1.g, TexSampleBias);
        float4 BaseColor6 = _BaseColor6 * SampleLayerTexture(BaseColorTexture6, UV, _LayerTextureUVScale6, Weights1.b, TexSampleBias);
        float4 BaseColor7 = _BaseColor7 * SampleLayerTexture(BaseColorTexture7, UV, _LayerTextureUVScale7, Weights1.a, TexSampleBias);

        // float3 Normal0 = SampleNormalTexture(NormalTexture0, UV, _LayerTextureUVScale0, Weights[0], TexSampleBias, 0.0).xyz;
        // float3 Normal1 = SampleNormalTexture(NormalTexture1, UV, _LayerTextureUVScale1, Weights[1], TexSampleBias, 0.0).xyz;
        // float3 Normal2 = SampleNormalTexture(NormalTexture2, UV, _LayerTextureUVScale2, Weights[2], TexSampleBias, 0.0).xyz;
        // float3 Normal3 = SampleNormalTexture(NormalTexture3, UV, _LayerTextureUVScale3, Weights[3], TexSampleBias, 0.0).xyz;
        // float3 Normal4 = SampleNormalTexture(NormalTexture4, UV, _LayerTextureUVScale4, Weights[4], TexSampleBias, 0.0).xyz;
        // float3 Normal5 = SampleNormalTexture(NormalTexture5, UV, _LayerTextureUVScale5, Weights[5], TexSampleBias, -1.2).xyz;
        // float3 Normal6 = SampleNormalTexture(NormalTexture6, UV, _LayerTextureUVScale6, Weights[6], TexSampleBias, 0.0).xyz;
        // float3 Normal7 = SampleNormalTexture(NormalTexture7, UV, _LayerTextureUVScale7, Weights[7], TexSampleBias, 0.0).xyz;

        // Albedo = BaseColor0.rgb * Weights[0] + BaseColor1.rgb * Weights[1] + BaseColor2.rgb * Weights[2] + BaseColor3.rgb * Weights[3] + BaseColor4.rgb * Weights[4] + BaseColor5.rgb * Weights[5] + BaseColor6.rgb * Weights[6] + BaseColor7.rgb * Weights[7];
        // NormalTS = Normal0 * Weights[0] + Normal1 * Weights[1] + Normal2 * Weights[2] + Normal3 * Weights[3] + Normal4 * Weights[4] + Normal5 * Weights[5] + Normal6 * Weights[6] + Normal7 * Weights[7];
        // MRA = HMRA0.yzw * Weights[0] + HMRA1.yzw * Weights[1] + HMRA2.yzw * Weights[2] + HMRA3.yzw * Weights[3] + HMRA4.yzw * Weights[4] + HMRA5.yzw * Weights[5] + HMRA6.yzw * Weights[6] + HMRA7.yzw * Weights[7];
        // Specular = _Specular0 * Weights[0] + _Specular1 * Weights[1] + _Specular2 * Weights[2] + _Specular3 * Weights[3] + _Specular4 * Weights[4] + _Specular5 * Weights[5] + _Specular6 * Weights[6] + _Specular7 * Weights[7];

        MRA = float3(0, 1, 0);
        Albedo = Weights0.r * BaseColor0.rgb + Weights0.g * BaseColor1.rgb + Weights0.b * BaseColor2.rgb + Weights0.a * BaseColor3.rgb +
                 Weights1.r * BaseColor4.rgb + Weights1.g * BaseColor5.rgb + Weights1.b * BaseColor6.rgb + Weights1.a * BaseColor7.rgb;
        NormalTS = normalize(NormalTS);
        NormalTS = Normal;
    }
    else
    {
        Albedo = SampleAlbedoTexture(Input.uv, Input.instanceID);
        NormalTS = Normal;
        MRA = float3(0.0, 1.0, 1.0);
    }

    if (TUNE_COLOR)
    {
        Albedo = pow(saturate(Albedo * _TuneColor_DiffuseBrightness), _TuneColor_DiffuseContrast);
        Albedo = lerp(Albedo, dot(Albedo, float3(.3f, .59f, .11f)), saturate(_TuneColor_DesaturationFraction));
    }

	Data.materialType = MATERIAL_TYPE;
    Data.baseColor = Albedo * _BaseColor.rgb;
    Data.opacityMask = 1.f;
    Data.roughness = MRA.y;
    Data.ambientOcclusion = MRA.z;
    Data.metallic = MRA.x;
    Data.specular = Specular;
    Data.normalWS = Normal;
    Data.geomNormalWS = Normal;
    Data.normalTS = NormalTS;

    #ifdef GET_SURFACE_DATA_CALLBACK
    Data = GetSurfaceDataCallBack(Data, Input);
    #endif
#endif//terrain ocean

    //Data.baseColor = float3(1.f, 1.f, 1.f);

    // float2 VEdge = step(abs(Input.uv - .5f), .495f);
    // if (VEdge.x * VEdge.y == 0.f)
    // {
    //     Data.baseColor = float3(1.f, 0.f, 0.f);
    // }

    return Data;
}
#endif

#endif
