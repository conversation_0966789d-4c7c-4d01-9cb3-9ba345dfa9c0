#include "LitCommonStruct.hlsl"
#include "../MaterialUtilities.hlsl"
#include "../NormalBuffer.hlsl"
#include "../../ShaderLibrary/Common.hlsl"

#ifdef OPEN_VT
#include "../../Features/VirtualTexture/VirtualTextureCommon.hlsl"
#endif

#ifndef DEFERRED_SHADING
#if defined(CE_INSTANCING)
SurfaceData GetSurfaceData(ObjectSceneData objectData, SurfaceInput input)
{
	SurfaceData surfaceData = (SurfaceData)0;

	// BaseColor OpacityMask
    float texSampleBias = 0;
    texSampleBias = (1.0 - pow(saturate(length(ce_CameraPos.xyz - input.positionWS.xyz) / _TexSampleBiasCriticleDistance), _TexSampleBiasCurveParam)) * _TextureSampleBias;

	float4 color;
    if (ANISO_SAMPLE)
    {
        color = _BaseMap.SampleBias(ce_Sampler_Anisotropic, input.uv, _TextureSampleBias);
    }
	else
	{
        color = _BaseMap.SampleBias(texture_sampler, input.uv, _TextureSampleBias);
	}
	surfaceData.baseColor = _BaseColor.xyz * color.xyz;
	surfaceData.opacity = surfaceData.opacityMask = _BaseColor.w * color.w;
	surfaceData.opacityMaskClip = _AlphaClip;

#ifdef USE_VERTEX_COLOR
    surfaceData.baseColor *= input.vertexColor.rgb;
#endif

    if (TUNE_COLOR)
    {
        float3 desaturationInColor = pow(clamp(surfaceData.baseColor * _TuneColor_DiffuseBrightness, 0, 1), _TuneColor_DiffuseContrast);
        surfaceData.baseColor = DesaturateColor(desaturationInColor, 1.0 - _TuneColor_DesaturationFraction);
    }

	// Normal
	float4 channels;
    if (ANISO_SAMPLE)
    {

        channels = _NormalMap.SampleBias(ce_Sampler_Anisotropic, input.uv, texSampleBias);
    }
	else
	{
        channels = _NormalMap.SampleBias(texture_sampler, input.uv, texSampleBias);
	}

	surfaceData.normalTS = UnpackNormalmapRGorAG(channels, _NormalScale);
	//surfaceData.normalTS = UnpackNormalUE(channels, _NormalScale);

	// Mask
	float4 mask;
	if(CHANNEL_PACKING)
	{
		mask.r = 1.0f;
		mask.g = channels.b;
		mask.b = 1.0f;
	}
	else
	{
		// Roughness Metallic
		mask = _MaskMap.SampleBias(texture_sampler, input.uv, texSampleBias);
	}
	
	float ambientOcclusion;

	// Quixel Dispalcement , Roughness map
	if (DPR_MAP) 
	{
		surfaceData.metallic = _Metallic;
		ambientOcclusion = 1.0;
	}
	else
	{
		surfaceData.metallic = mask.r * _Metallic;
		ambientOcclusion = mask.b;
	}
	// ambientOcclusion *= ao_texture.Sample(texture_sampler, input.screenUV).r;
	// surfaceData.ambientOcclusion = (1.0 - _AO_Intensity * (1.0 - ambientOcclusion));
    surfaceData.ambientOcclusion = 1.0;

	// smoothness = roughness;
	float smoothness = mask.g * _Smoothness;
    smoothness = lerp(_SmoothnessRemapMin, _SmoothnessRemapMax, smoothness);
	surfaceData.roughness = smoothness;	

	// Specular
	surfaceData.specular = _Specular;

	// Emissive
	surfaceData.emissiveColor = _EmissiveColor.xyz;
	
	// MaterialType
	surfaceData.materialType = MATERIAL_TYPE;

	// SubsurfaceColor
	surfaceData.subsurfaceColor = _SubsurfaceColor.xyz;

	// DebugColor
	surfaceData.debugColor = (ce_TemporalSampleIndex / 16.0).xxx;
	
	surfaceData.temporalReactive = float2(0, 0);
	
	return surfaceData;
}
#else
SurfaceData GetSurfaceData(SurfaceInput input)
{
	SurfaceData surfaceData = (SurfaceData)0;

	// BaseColor OpacityMask
    float texSampleBias = 0;
    texSampleBias = (1.0 - pow(saturate(length(ce_CameraPos.xyz - input.positionWS.xyz) / _TexSampleBiasCriticleDistance), _TexSampleBiasCurveParam)) * _TextureSampleBias;

	float4 color;
    if (ANISO_SAMPLE)
    {
        color = _BaseMap.SampleBias(ce_Sampler_Anisotropic, input.uv, _TextureSampleBias);
    }
	else
	{
        color = _BaseMap.SampleBias(texture_sampler, input.uv, _TextureSampleBias);
	}
	surfaceData.baseColor = _BaseColor.xyz * color.xyz;
	surfaceData.opacity = surfaceData.opacityMask = _BaseColor.w * color.w;
	surfaceData.opacityMaskClip = _AlphaClip;

#ifdef USE_VERTEX_COLOR
    surfaceData.baseColor *= input.vertexColor.rgb;
#endif

    if (TUNE_COLOR)
    {
        float3 desaturationInColor = pow(clamp(surfaceData.baseColor * _TuneColor_DiffuseBrightness, 0, 1), _TuneColor_DiffuseContrast);
        surfaceData.baseColor = DesaturateColor(desaturationInColor, 1.0 - _TuneColor_DesaturationFraction);
    }

	// Normal
	float4 channels;
    if (ANISO_SAMPLE)
    {
        channels = _NormalMap.SampleBias(ce_Sampler_Anisotropic, input.uv, texSampleBias);
    }
	else
	{
        channels = _NormalMap.SampleBias(texture_sampler, input.uv, texSampleBias);
	}
	surfaceData.normalTS = UnpackNormalmapRGorAG(channels, _NormalScale);
	//surfaceData.normalTS = UnpackNormalUE(channels, _NormalScale);

	// Mask
	float4 mask;
	if(CHANNEL_PACKING)
	{
		mask.r = 1.0f;
		mask.g = channels.b;
		mask.b = 1.0f;
	}
	else
	{
		// Roughness Metallic
		mask = _MaskMap.SampleBias(texture_sampler, input.uv, texSampleBias);
	}
	
	float ambientOcclusion;

	// Quixel Dispalcement , Roughness map
	if (DPR_MAP) 
	{
		surfaceData.metallic = _Metallic;
		ambientOcclusion = 1.0;
	}
	else
	{
		surfaceData.metallic = mask.r * _Metallic;
		ambientOcclusion = mask.b;
	}
	// ambientOcclusion *= ao_texture.Sample(texture_sampler, input.screenUV).r;
	// surfaceData.ambientOcclusion = (1.0 - _AO_Intensity * (1.0 - ambientOcclusion));
    surfaceData.ambientOcclusion = 1.0;

	// smoothness = roughness;
	float smoothness = mask.g * _Smoothness;
    smoothness = lerp(_SmoothnessRemapMin, _SmoothnessRemapMax, smoothness);
	surfaceData.roughness = smoothness;	

	// Specular
	surfaceData.specular = _Specular;

	// Emissive
	surfaceData.emissiveColor = _EmissiveColor.xyz;

	// MaterialType
	surfaceData.materialType = MATERIAL_TYPE;

	// SubsurfaceColor
	surfaceData.subsurfaceColor = _SubsurfaceColor.xyz;

	// DebugColor
	surfaceData.debugColor = (ce_TemporalSampleIndex / 16.0).xxx;
	
	// Fsr reactive mask
#ifdef USED_WITH_SKELETAL_MESH    
    float3 normalInView = mul(ce_View, float4(input.normalWS.xyz, 0.0));
	normalInView = normalize(normalInView);

	float reactiveFactor = min(0.0, dot(normalInView, float3(0, 0, 1))); // -1.0 -- 0.0
	reactiveFactor = step(_Reactivemask.x, 1.0 - abs(reactiveFactor)) * (1.0 - abs(reactiveFactor)) * _Reactivemask.y;
    surfaceData.temporalReactive = reactiveFactor;
#else
    surfaceData.temporalReactive = float2(0, 0);
#endif

	return surfaceData;
}
#endif
#endif