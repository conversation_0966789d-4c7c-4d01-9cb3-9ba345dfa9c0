#include "LitCommonStruct.hlsl"
#include "../../ShaderLibrary/Common.hlsl"

Texture2D<float>           	_SceneDepth               : register(space0);   // input, material ID in w

cbuffer cbDecal : register(space2)
{
	matrix ce_WorldOnlyRotation;
}

float GetSceneDepth(float2 uv)
{
	return _SceneDepth.SampleLevel(ce_Sampler_Point, uv, 0);
}

float3 GetWorldFromDepthAndUV(float depth, float2 uv)
{
    float4 ndc = float4(uv.x * 2 - 1, (1 - uv.y) * 2 - 1, depth, 1);
    float4 view = mul(ce_InvProjection, ndc);
    view /= view.w;
    float4 world = mul(ce_InvView, view);
#ifdef CE_USE_DOUBLE_TRANSFORM
	world.xyz = GetLargeCoordinateReltvPosition(world.xyz, ce_CameraTilePosition, ce_TilePosition);
#endif
    return world.xyz;
}

float3 GetCamTileSpaceWorldFromDepthAndUV(float depth, float2 uv)
{
    float4 ndc = float4(uv.x * 2 - 1, (1 - uv.y) * 2 - 1, depth, 1);
    float4 view = mul(ce_InvProjection, ndc);
    view /= view.w;
    float4 world = mul(ce_InvView, view);
    return world.xyz;
}

float3 ConvertNormalFromTangentToWorld(in float3 normalTS)
{
	return normalize(mul(ce_WorldOnlyRotation, float4(normalize(normalTS), 0)).xyz);
}
SurfaceData GetSurfaceData(SurfaceInput input)
{
	SurfaceData surfaceData = GetDefaultSurfaceData();
	;
	float depth = GetSceneDepth(input.screenUV);
	float3 worldPos = GetWorldFromDepthAndUV(depth, input.screenUV);

	float4 DecalVectorHom = mul(ce_InvWorld, float4(worldPos,1));
	float3 OSPosition = DecalVectorHom.xyz;// / DecalVectorHom.w;
	clip(OSPosition.xyz + 1.0f);
	clip(1.0f - OSPosition.xyz);

	// projection from +z to -z
	float3 DecalVector =  -OSPosition * 0.5f + 0.5f;
	float2 DecalUVs = DecalVector.xy;

	input.positionWS = GetCamTileSpaceWorldFromDepthAndUV(depth, input.screenUV);

	DecalData decalData = GetDecalMaterialData(input, DecalUVs);
	float alpha = 1.0;
	if(Decal_Opacity)
	{
		alpha = decalData.Opacity;
	}
	
	surfaceData.opacity = alpha;

	if(Decal_BaseColor_Blend)
	{
		surfaceData.baseColor = decalData.BaseColor;
	}

	if(Decal_Normal_Blend)
	{
		// Normal Blend in Lit.hlsl
		surfaceData.normalTS = ConvertNormalFromTangentToWorld(decalData.Normal);
	}

	if(Decal_Metallic_Blend)
	{
		surfaceData.metallic = decalData.Metallic;
	}

	if(Decal_Specular_Blend)
	{
		surfaceData.specular = decalData.Specular;
	}

	if(Decal_Roughness_Blend)
	{
		surfaceData.roughness = decalData.Roughness;
	}

	if(Decal_Emissive_Blend)
	{
		surfaceData.emissiveColor = decalData.EmissiveColor;
	}

	return surfaceData;
}