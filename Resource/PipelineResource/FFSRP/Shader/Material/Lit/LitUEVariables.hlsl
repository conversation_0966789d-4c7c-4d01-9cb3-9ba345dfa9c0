#ifndef LIT_VARIBALES_HLSL
#define LIT_VARIBALES_HLSL
#include "../../ShaderLibrary/Common.hlsl"

#ifdef OPEN_VT
#define MAX_VT_TEXTURE_LAYERS 4
#endif

SHADER_CONST(int, MATERIAL_TYPE, 0);
SHADER_CONST(bool, ALPHA_CLIPPING, false);
SHADER_CONST(bool, _NORMALMAP_TANGENT_SPACE, true);
SHADER_CONST(bool, DOUBLE_SIDED, false);
SHADER_CONST(bool, ENABLE_SSS, false);
SHADER_CONST(bool, LIGHE_MAP_ENABLE, false);
SHADER_CONST(bool, USE_LM_DIRECTIONALITY, false);
SHADER_CONST(bool, ENABLE_LM_AO, false);
SHADER_CONST(bool, NO_INVERTUV, false);
SHADER_CONST(bool, DPR_MAP, false);
SHADER_CONST(bool, DISABLE_LM_GI, false);
SHADER_CONST(bool, DEBUG_LM_GI_ONLY, false);
SHADER_CONST(bool, DEBUG_LM_COLOR_ONLY, false);
SHADER_CONST(bool, ENABLE_LIGHT_PROBE, false);
SHADER_CONST(bool, ENABLE_VOLUMETRIC_LIGHT_MAP, false);
SHADER_CONST(bool, USE_SKY_OCCLUSION, false);
SHADER_CONST(int, ALPHA_CLIPPING_TYPE, 0);
SHADER_CONST(bool, DISABLE_BAKE_SHADOW, false);
SHADER_CONST(bool, DISABLE_BAKE_NORMAL, false);
SHADER_CONST(bool, SHOW_DEBUG_LM_UV, false);
SHADER_CONST(bool, SHOW_DEBUG_NORMAL, false);
SHADER_CONST(bool, FLIP_NORMAL_Y, true);
SHADER_CONST(bool, TUNE_COLOR, true);
SHADER_CONST(bool, CHANNEL_PACKING, false);
SHADER_CONST(bool, ENABLE_LIGHTMAP_PRT_API, false);
SHADER_CONST(bool, ENABLE_LOCAL_LM_PRT, false);
SHADER_CONST(bool, ANISO_SAMPLE, false);
SHADER_CONST(bool, ENABLE_GEOMETRIC_SPECULAR_AA, false);
SHADER_CONST(int, SURFACE_TYPE, 0);
SHADER_CONST(int, MATERIAL_BLEND_MODE, 0);

#define MATERIAL_BLEND_MODE_OPAQUE 0
#define MATERIAL_BLEND_MODE_MASKED 1
#define MATERIAL_BLEND_MODE_TRANSLUCENT 2
#define MATERIAL_BLEND_MODE_ADDITIVE 3
#define MATERIAL_BLEND_MODE_MODULATE 4
#define MATERIAL_BLEND_MODE_ALPHA_COMPOSITE 5

#include "../MaterialDefine.hlsl"

Texture2D<float4> _BaseMap : register(space1);
Texture2D<float4> _NormalMap : register(space1);
//Texture2D<float4> _NormalMapOS : register(space1);
Texture2D<float4> _MaskMap : register(space1);
Texture2D<float4> _EnvBRDFLutMap : register(space1);
//Texture2D<float4> _GrassOcclusionMap: register(space1);

// VT
#ifdef OPEN_VT
    Texture2D<uint4> _VTPageTable : register(space1);
#endif

float normalFiltering(float perceptualRoughness, const float3 worldNormal, float variance, float threshold);
float GeometricSpecularAntiAliasing(float perceptualRoughness, const float3 worldNormal, float _SpecularAntiAliasingVariance, float _SpecularAntiAliasingThreshold)
{
    if (ENABLE_GEOMETRIC_SPECULAR_AA)
    {
        return normalFiltering(perceptualRoughness, worldNormal, _SpecularAntiAliasingVariance, _SpecularAntiAliasingThreshold);
    }
    return perceptualRoughness;
}

#endif
