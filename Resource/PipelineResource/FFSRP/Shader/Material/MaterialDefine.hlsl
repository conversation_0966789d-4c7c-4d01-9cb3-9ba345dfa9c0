/*
 * Material Definition for both Graphics and RayTracing Shaders
 * Use Material StructuredBuffer for MaterialEditor generated shaders
 * Use Constant Buffer for Legacy
 */
 
#ifndef MATERIAL_DEFINE_HLSL
#define MATERIAL_DEFINE_HLSL

struct MaterialData
{
    float4 _DoubleSidedConstants;

    float _AO_Intensity;
    float _ReflectionProbeIntensity;
    float _LightSpecIntensity;
    float _VLMReflectionProbeIntensity;
    float _VLMReflectionProbeAOIntensity;

    float _TextureSampleBias;
    float _TexSampleBiasCurveParam;
    float _TexSampleBiasCriticleDistance;

    // VT
#ifdef OPEN_VT
    float4 _VTPageTableUniform0;
    float4 _VTPageTableUniform1;
    float4 _VTPageTableUniform2;
    float4 _VTPackedUniform;
    float4   _VTLayerRemap;
#endif

#ifdef ADDITIONAL_MTL_PARAM
    ADDITIONAL_MTL_PARAM
#endif
    
    float2 _Reactivemask;
    
    float4 GridDim;
    float4 WorldTranslation;
    float4 WorldScale;
    uint PatchSize;
    float4 BlendLayerMask0;
    float4 BlendLayerMask1;

    // for ENABLE_GEOMETRIC_SPECULAR_AA
    float _SpecularAntiAliasingVariance;
    float _SpecularAntiAliasingThreshold;

    bool ForceDisableExponentialFog;
    bool ForceDisableVolumetricFog;
    bool ForceDisableCloudFog;
    
    int MATERIAL_TYPE;
    int MATERIAL_BLEND_MODE;
};

#ifdef MATERIAL_EDITOR

// for ray tracing and graphics shaders
StructuredBuffer<MaterialData> ce_PerMaterial : register(space1);

// for graphics shaders
cbuffer cbMtl : register(space1)
{
    uint ce_MaterialIndex;
};
#endif

#endif
