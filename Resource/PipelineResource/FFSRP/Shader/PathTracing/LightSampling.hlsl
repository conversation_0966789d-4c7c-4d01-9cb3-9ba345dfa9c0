#ifndef LIGHT_SAMPLING_HLSL
#define LIGHT_SAMPLING_HLSL

#include "Utils/SamplingUtils.hlsl"
#include "PathTracingLight.hlsl"

namespace CE_PathTracing
{
    static const int MAX_LIGHT_PER_GRID = 32;  // TODO(scolu): LightGrid, As small as possible, large will cause local memory stall and waste registers
    static const float MAX_TRACE_DISTANCE = 1e10f;

    // ----------------------------------------------Light Sample--------------------------------------------
    struct LightSample
    {
        float3 Direction;
        float Pdf;
        float3 RadianceOverPdf;
        float Distance;
    };

    struct LightHit
    {
        float3 Radiance;
        float Pdf;
        float HitT;

        bool IsHit()
        {
            return HitT > 0.f;
        }
    };

    LightSample SampleDirectionalLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal)
    {
        LightSample lightSample = (LightSample)0;

        float sinThetaMax = Light.SourceRadius;
        float4 dirAndPdf = UniformSampleConeRobust(Rnd2, sinThetaMax * sinThetaMax);
        lightSample.Direction = LocalToWorld(dirAndPdf.xyz, -Light.DirOrPos);
        lightSample.Pdf = dirAndPdf.w;
        /*
        * NOTE: For Directional Light, Lo / Pdf = light.Color * light.Intensity
        */
        lightSample.RadianceOverPdf = Light.LightColor;
        lightSample.Distance = MAX_TRACE_DISTANCE;

        return lightSample;
    }

    LightSample SamplePointLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal)
    {
        LightSample lightSample = (LightSample)0;
        
        float falloff, lightMask;
        Light.GetAttenuation(Pos, falloff, lightMask);

        float3 toLight = Light.DirOrPos - Pos;
        float dis2 = dot(toLight, toLight);
        float radius = max(Light.SourceRadius, CE_PATH_TRACING_SMALL_NUMBER);
        float radius2 = pow(radius, 2);
        float sinThetaMax2 = saturate(radius2 / dis2);
        float4 sample = UniformSampleConeRobust(Rnd2, sinThetaMax2);
        lightSample.Direction = LocalToWorld(sample.xyz, normalize(toLight));
        lightSample.Pdf = sample.w;

        float cosTheta = sample.z;
        float sinTheta2 = 1.f - pow(cosTheta, 2);
        lightSample.Distance = sqrt(dis2) * (cosTheta - sqrt(max(sinThetaMax2 - sinTheta2, 0.f)));

        float3 power = Light.LightColor * falloff * lightMask;
        float3 radiance = power / (M_PI * radius2);
        lightSample.RadianceOverPdf = sinTheta2 < 1e-3f ? power : radiance / lightSample.Pdf;

        return lightSample;
    }

    LightSample SampleSpotLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal)
    {
        return SamplePointLight(Light, Rnd2, Pos, Normal);
    }

    LightSample SampleRectLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal)
    {
        LightSample lightSample = (LightSample)0;

        return lightSample;
    }

    LightSample SampleSkyLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal)
    {
        LightSample lightSample = (LightSample)0;
        float4 sample = UniformSampleSphere(Rnd2);
        lightSample.Direction = sample.xyz;
        lightSample.Pdf = sample.w;
        float3 lightColor = ce_SkyLightTexture.SampleLevel(texture_sampler, ToUEVec(sample.xyz), 0).rgb * ce_SkyLightColor * ce_SkyLightIntensity;
        lightSample.RadianceOverPdf = lightColor / lightSample.Pdf;
        lightSample.Distance = MAX_TRACE_DISTANCE;

        return lightSample;
    }

    LightSample SampleLight(in PathTracingLight Light, in float2 Rnd2, in float3 Pos, in float3 Normal) 
    {
        switch (Light.LightType) 
        {
            case (LightType_Directional):
                return SampleDirectionalLight(Light, Rnd2, Pos, Normal);
            case (LightType_Point):
                return SamplePointLight(Light, Rnd2, Pos, Normal);
            case (LightType_Spot):
                return SampleSpotLight(Light, Rnd2, Pos, Normal);
            case (LightType_Rect):
                return SampleRectLight(Light, Rnd2, Pos, Normal);
            case (LightType_Sky):
                return SampleSkyLight(Light, Rnd2, Pos, Normal);
            default:
                return (LightSample)0;
        }
    }
    

    // ----------------------------------------------Light Estimate--------------------------------------------
    float EstimateDirectionalLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        return Luminance(Light.LightColor);
    }

    float EstimatePointLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        float falloff, lightMask;
        Light.GetAttenuation(Pos, falloff, lightMask);
        float3 power = Luminance(Light.LightColor);
        float3 L = normalize(Light.DirOrPos - Pos);
        float NoL = max(0.f, dot(Normal, L));
        float outIrradiance = power * falloff * lightMask * NoL;

        return outIrradiance;
    }

    float EstimateSpotLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        return EstimatePointLight(Light, Pos, Normal);
    }

    float EstimateRectLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        return 0.f;
    }

    float EstimateSkyLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        return 4 * M_PI;
    }

    // Estimate Light Pick Pdf(power based/irradiance)
    float EstimateLight(in PathTracingLight Light, in float3 Pos, in float3 Normal)
    {
        switch (Light.LightType)
        {
            case LightType_Directional:
                return EstimateDirectionalLight(Light, Pos, Normal);
            case LightType_Point:
                return EstimatePointLight(Light, Pos, Normal);
            case LightType_Spot:
                return EstimateSpotLight(Light, Pos, Normal);
            case LightType_Rect:
                return EstimateRectLight(Light, Pos, Normal);
            case LightType_Sky:
                return EstimateSkyLight(Light, Pos, Normal);
            default:
                return 0.f;
        }
    }

    // Select light via cdf
    void SelectLight(in float Rnd, in uint LightCount, inout float LightPickCDF[MAX_LIGHT_PER_GRID], out uint LightIndex, out float LightPickPdf)
    {
        LightIndex = 0;
        for (int count = LightCount; count > 0;)
        {
            int step = count / 2;
            int iter = LightIndex + step;
            if (Rnd < LightPickCDF[iter])
            {
                count = step;
            }
            else
            {
                LightIndex = iter + 1;
                count -= step + 1;
            }
        }
        
        LightPickPdf = LightPickCDF[LightIndex] - (LightIndex > 0 ? LightPickCDF[LightIndex - 1] : 0.f);
    }

    // ----------------------------------------------Light Trace--------------------------------------------
    LightHit TraceDirectionalLight(in RayDesc Ray, in PathTracingLight Light)
    {
        LightHit hitSample = (LightHit)0;

        float sinThetaMax = Light.SourceRadius;
        float sinThetaMax2 = pow(sinThetaMax, 2);
        float oneMinusCosThetaMax = sinThetaMax2 < 1e-2f ? sinThetaMax2 * (.5f + .125f * sinThetaMax2) : 1 - sqrt(1 - sinThetaMax2);
        float cosTheta = saturate(dot(Ray.Direction, -Light.DirOrPos));

        // If the ray intersect the directional light cone
        if (1 - cosTheta < oneMinusCosThetaMax)
        {
            hitSample.HitT = MAX_TRACE_DISTANCE;
            float solidAngle = 2 * M_PI * oneMinusCosThetaMax;
            hitSample.Pdf = rcp(solidAngle);
            hitSample.Radiance = Light.LightColor * hitSample.Pdf;
        }

        return hitSample;
    }

    LightHit TracePointLight(in RayDesc Ray, in PathTracingLight Light)
    {
        LightHit hitSample = (LightHit)0;

        return hitSample;
    }

    LightHit TraceSpotLight(in RayDesc Ray, in PathTracingLight Light)
    {
        return TracePointLight(Ray, Light);
    }

    LightHit TraceRectLight(in RayDesc Ray, in PathTracingLight Light)
    {
        LightHit hitSample = (LightHit)0;

        return hitSample;
    }

    LightHit TraceSkyLight(in RayDesc Ray, in PathTracingLight Light)
    {
        LightHit hitSample = (LightHit)0;

        hitSample.Pdf = rcp(4.f * M_PI);
        float3 sampleDir = ToUEVec(Ray.Direction);
        hitSample.Radiance = ce_SkyLightTexture.SampleLevel(texture_sampler, sampleDir, 0).rgb * ce_SkyLightColor * ce_SkyLightIntensity;
        hitSample.HitT = MAX_TRACE_DISTANCE;

        return hitSample;
    }

    LightHit TraceLight(in RayDesc Ray, in PathTracingLight Light)
    {
        switch (Light.LightType)
        {
            case LightType_Directional:
                return TraceDirectionalLight(Ray, Light);
            case LightType_Point:
                return TracePointLight(Ray, Light);
            case LightType_Spot:
                return TraceSpotLight(Ray, Light);
            case LightType_Rect:
                return TraceRectLight(Ray, Light);
            case LightType_Sky:
                return TraceSkyLight(Ray, Light);
            default:
                return (LightHit)0;
        }
    }


}  // namespace CE_PathTracing

#endif
