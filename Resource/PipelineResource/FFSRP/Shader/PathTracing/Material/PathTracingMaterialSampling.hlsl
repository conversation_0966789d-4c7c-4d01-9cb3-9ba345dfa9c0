#ifndef MATERIAL_SAMPLING_HLSL
#define MATERIAL_SAMPLING_HLSL

#include "PathTracingDiffuseMaterial.hlsl"
#include "PathTracingCEStandardMaterial.hlsl"

namespace CE_PathTracing
{
    #define DIFFUSE_ONLY 0

    MaterialEval EvalMaterial(in float3 V, in float3 L, in RayTracingPayload Payload)
    {
#if DIFFUSE_ONLY
        return EvalDiffuse<PERSON><PERSON>bert(V, L, Payload);
#endif

        return EvalCEStandardMaterial(V, L, Payload);
    }

    MaterialSample SampleMaterial(in float3 V, in RayTracingPayload Payload, in float3 Rnd)
    {
#if DIFFUSE_ONLY
        return SampleDiffuse<PERSON><PERSON>bert(V, Payload, Rnd);
#endif

        return SampleCEStandardMaterial(V, Payload, Rnd);
    }
}  // namespace CE_PathTracing

#endif
