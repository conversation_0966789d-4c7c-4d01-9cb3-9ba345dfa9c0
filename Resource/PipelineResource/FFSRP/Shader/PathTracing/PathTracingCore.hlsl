#ifndef PATH_TRACING_CORE_HLSL
#define PATH_TRACING_CORE_HLSL

#include "LightSampling.hlsl"
#include "Material/PathTracingMaterialSampling.hlsl"

namespace CE_PathTracing
{
    struct PathTracingPayload
    {
        float3 Radiance;
        bool IsHit;
        bool HitSky;
        float3 Debug;
    };

    struct DeferredTraceMaterialRayState 
    {
        uint LightType;
        float3 Radiance;
        float Distance;
    };

    PathTracingPayload PathTracingCore(in float3 Origin, in float3 Direction, in bool UseMIS, in uint LightCount, 
        in bool hasSkyLight, in bool ENABLE_SKY_LIGHT_REALTIME_CAPTURE, 
        inout uint Seed, in uint MaxBounce, in float MinRayBias, in float MaxTraceDistance,
        in bool ClampFirefly, in float FireflyClampThreshold)
    {
        PathTracingPayload ptPayload = (PathTracingPayload)0;

        RayDesc ray;
        ray.Origin = Origin;
        ray.Direction = Direction;
        ray.TMin = MinRayBias;
        ray.TMax = MaxTraceDistance;

        RayTracingPayload payload;
        payload.Seed = Seed;
        float3 radiance = 0.xxx;
        float3 pathThp = 1.xxx;
        float lightPickCDF[MAX_LIGHT_PER_GRID];
        uint lightCountNEE = hasSkyLight ? LightCount + 1 : LightCount;
        uint skyLightIndex = hasSkyLight ? lightCountNEE - 1 : lightCountNEE;
        lightCountNEE = max(MAX_LIGHT_PER_GRID, lightCountNEE);

        const bool deferredTraceMaterialRay = true;
        DeferredTraceMaterialRayState traceMaterialRayState[MAX_LIGHT_PER_GRID];
        uint numValidTraceLightState = 0;

        [[loop]]
        for (uint bounce = 0; bounce < MaxBounce; bounce++)
        {
            bool isFirstBounce = (bounce == 0);
            bool isLastBounce = (bounce == MaxBounce - 1);

            TraceRay(ce_AccelerationStructure, RAY_FLAG_NONE, 0xff, 0, 1, 0, ray, payload);

            if (UseMIS && deferredTraceMaterialRay)
            {
                for (int idx = 0; idx < numValidTraceLightState; idx++)
                {
                    DeferredTraceMaterialRayState currState = traceMaterialRayState[idx];
                    if (!payload.IsHit() || payload.HitT >= currState.Distance)
                    {
                        radiance += currState.Radiance;
                    }
                }
                numValidTraceLightState = 0;
            }

            if (!payload.IsHit())
            {
                if (isFirstBounce)
                {
                    ptPayload.HitSky = true;
                    float3 sampleDir = ENABLE_SKY_LIGHT_REALTIME_CAPTURE ? ToUEVec(ray.Direction) : ray.Direction;
                    radiance += pathThp * ce_SkyLightTexture.SampleLevel(texture_sampler, sampleDir, 0).rgb * ce_SkyLightColor * ce_SkyLightIntensity;
                }
                break;
            }
            else
            {
                ptPayload.IsHit = true;
            }

            ptPayload.Debug = payload.SurfaceData.opacity.xxx;
            

            // Hit object with default hit group, just break
            if (payload.Seed == 0xffffffff)
            {
                // Return pink debug color for material with no valid ray tracing shaders
                if (bounce == 0)
                {
                    radiance = payload.SurfaceData.DecodeBaseColor();
                    break;
                }
                break;
            }

            SurfaceData surfaceData = payload.SurfaceData;
            radiance += pathThp * surfaceData.DecodeEmissiveColor();  /* surfaceData.opacity */

            // ---------------------------------------------NEE--------------------------------------------
            float lightPickCDFSum = 0.f;
            float3 pos = payload.GetHitPos(ray);
            float3 normal = surfaceData.GetNormalWS();
            for (uint lightIndex = 0; lightIndex < lightCountNEE; lightIndex++)
            {
                PathTracingLight light = ConvertToPathTracingLight(ce_Lights[lightIndex]);
                if (lightIndex == skyLightIndex) 
                {
                    light.LightType = LightType_Sky;
                }
                lightPickCDFSum += EstimateLight(light, pos, normal);
                lightPickCDF[lightIndex] = lightPickCDFSum;
            }

            if (lightPickCDFSum > 0.f)
            {
                uint selectLightIndex;
                float selectLightPdf;
                SelectLight(rnd(Seed) * lightPickCDFSum, lightCountNEE, lightPickCDF, selectLightIndex, selectLightPdf);
                selectLightPdf /= lightPickCDFSum;

                PathTracingLight selectLight = ConvertToPathTracingLight(ce_Lights[selectLightIndex]);
                if (selectLightIndex == skyLightIndex)
                {
                    selectLight.LightType = LightType_Sky;
                }
                LightSample lightSample = SampleLight(selectLight, rnd2(Seed), pos, normal);
                lightSample.RadianceOverPdf /= selectLightPdf;
                lightSample.Pdf *= selectLightPdf;

                if (lightSample.Pdf > 0.f)
                {
                    RayDesc shadowRay;
                    shadowRay.Origin = pos;
                    shadowRay.Direction = lightSample.Direction;
                    shadowRay.TMin = MinRayBias;
                    shadowRay.TMax = lightSample.Distance;

                    RayTracingPayload shadowRayPayload;
                    shadowRayPayload.Seed = Seed;
                    TraceRay(ce_AccelerationStructure, RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH, 0xff, 0, 1, 0, shadowRay, shadowRayPayload);
                    float visibility = shadowRayPayload.HitT > 0.f ? 0.f : 1.f;
                    lightSample.RadianceOverPdf *= visibility;
                }

                if (any(lightSample.RadianceOverPdf > 0.f)) 
                {
                    MaterialEval matEval = EvalMaterial(-ray.Direction, lightSample.Direction, payload);
                    float3 lightContrib = pathThp * lightSample.RadianceOverPdf * matEval.Weight * matEval.Pdf;
                    if (UseMIS)
                    {
                        lightContrib *= MISWeightPower(lightSample.Pdf, matEval.Pdf);
                    }
                    radiance += lightContrib;
                }
            }
            
            // --------------------------------------Sample material------------------------------------
            MaterialSample matSample = SampleMaterial(-ray.Direction, payload, rnd3(Seed));
            if (matSample.Pdf <= CE_PATH_TRACING_SMALL_NUMBER || asuint(matSample.Pdf) > 0x7f800000)
            {
                break;
            }

            // ------------------------------------------RR-----------------------------------------
            float3 nextPathThp = pathThp * matSample.Weight;
            float continueProb = sqrt(max(nextPathThp) / max(pathThp));
            if (continueProb < 1.f)
            {
                if (rnd(Seed) >= continueProb)
                {
                    break;
                }
                pathThp = nextPathThp / continueProb;
            }
            else 
            {
                pathThp = nextPathThp;
            }

            // Update ray
            ray.Origin = payload.GetHitPos(ray);
            ray.Direction = matSample.Direction;
            ray.TMin = MinRayBias;
            ray.TMax = MaxTraceDistance;

            if (UseMIS)
            {
                for (uint lightIndex = 0; lightIndex < lightCountNEE; lightIndex++)
                {
                    PathTracingLight light = ConvertToPathTracingLight(ce_Lights[lightIndex]);
                    if (lightIndex == skyLightIndex) 
                    {
                        light.LightType = LightType_Sky;
                    }

                    LightHit hitSample = TraceLight(ray, light);

                    if (!hitSample.IsHit())
                    {
                        continue;
                    }
                    
                    float3 lightContrib = pathThp * hitSample.Radiance;
                    if (lightPickCDFSum > 0.f)
                    {
                        float lightPickPdf = (lightPickCDF[lightIndex] - (lightIndex > 0 ? lightPickCDF[lightIndex - 1] : 0.f)) / lightPickCDFSum;
                        lightContrib *= MISWeightPower(matSample.Pdf, hitSample.Pdf * lightPickPdf);
                        if (any(lightContrib > 0.f))
                        {
                            if (deferredTraceMaterialRay)
                            {
                                DeferredTraceMaterialRayState currState;
                                currState.LightType = light.LightType;
                                currState.Radiance = lightContrib;
                                currState.Distance = hitSample.HitT;
                                traceMaterialRayState[numValidTraceLightState++] = currState;
                            }
                            else 
                            {
                                RayDesc shadowRay = ray;
                                shadowRay.TMax = hitSample.HitT;
                                RayTracingPayload shadowRayPayload;
                                shadowRayPayload.Seed = Seed;
                                TraceRay(ce_AccelerationStructure, RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH, 0xff, 0, 1, 0, shadowRay, shadowRayPayload);
                                float visibility = shadowRayPayload.HitT > 0.f ? 0.f : 1.f;
                                lightContrib *= visibility;
                                radiance += lightContrib;
                            }
                        }
                    }
                }
            }
        }

        float lum = Luminance(radiance);
        if(UseMIS && ClampFirefly && lum > FireflyClampThreshold)
        {
            radiance *= FireflyClampThreshold / lum;
        }
        ptPayload.Radiance = radiance;

        return ptPayload;
    }

}  // namespace CE_PathTracing

#endif
