#ifndef RAY_TRACING_COMMON_STRUCT_HLSL
#define RAY_TRACING_COMMON_STRUCT_HLSL

#define RAY_TRACING_PASS

#define ALPHA_MODE_OPAQUE 0
#define ALPHA_MODE_MASK 1
#define ALPHA_MODE_TRANSPARENT 2

#include "../Material/Lit/LitCommonStruct.hlsl"
#include "../ShaderLibrary/BindlessCommon.hlsl"

struct RayTracingPayload
{
    SurfaceData SurfaceData;

    float2 Bary;
    uint Seed;
    float HitT;

    bool IsHit()
    {
        return HitT > 0.f;
    }

    float3 GetHitPos(in RayDesc Ray)
    {
        return Ray.Origin + HitT * Ray.Direction;
    }
};

struct RayTracingAttributes
{
    float2 bary : SV_Barycentrics;
};

#endif
