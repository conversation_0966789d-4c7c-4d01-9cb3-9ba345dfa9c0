#ifndef SHADER_PASS_ANY_HIT_HLSL
#define SHADER_PASS_ANY_HIT_HLSL

#include "../Material/Lit/Lit.hlsl"
#include "../RayTracing/RayTracingCommonResource.hlsl"
#include "../PathTracing/PathTracingRandom.hlsl"

PSInput RayTracingGetPSInput_AnyHit(in SubInstanceData subInstanceData,
    in uint triangleIndex,
    in float2 bary)
{
    PSInput input = (PSInput)0;

    input.positionWS = WorldRayDirection() + WorldRayDirection() * RayTCurrent();
    input.positionNDC = 0.f;  // Not used for ray tracing?
    
    uint3 indices = GetIndices(subInstanceData, triangleIndex);
    float3 localNormal = GetInterpolatedNormal(subInstanceData, indices, bary);
    input.normalWS = mul((float3x3)WorldToObject4x3(), localNormal);
    float3 localBinormal = GetInterpolatedBinormal(subInstanceData, indices, bary);
    input.binormalWS = mul((float3x3)WorldToObject4x3(), localBinormal);
    float3 localTangent = GetInterpolatedTangent(subInstanceData, indices, bary);
    input.tangentWS = mul((float3x3)WorldToObject4x3(), localTangent);
    
    input.vertexColor = 1.f;  // Not used for ray tracing?

    // TODO(scolu): Only support uv1 for ray tracing now
#if NUM_MATERIAL_TEXCOORDS > 0
    input.uv = GetInterpolatedUV(subInstanceData, indices, bary);
    input.uvs[0].xy = GetInterpolatedUV(subInstanceData, indices, bary);
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
    input.uv1 = GetInterpolatedUV1(subInstanceData, indices, bary);
#endif

    input.screenUV = 0.f;
    input.instanceID = subInstanceData.ObjectIndex;  // Used to query ObjectSceneData, but not used for ray tracing, since we store ObjectIndex in SubInstanceData
    input.packedPageInfo = 0u;  // Fot VT
    input.lightViewIndex = 0u;  // Not used for ray tracing

    // CUSTOM_PS_INPUT, not used for ray tracing
    
    return input;
}

[shader("anyhit")]
void anyhit(inout RayTracingPayload payload, in RayTracingAttributes attrib)
{
    uint subInstanceIndex = InstanceID() + GeometryIndex();
    uint triangleIndex = PrimitiveIndex();
    SubInstanceData subInstanceData = ce_SubInstanceData[subInstanceIndex];

    MaterialData matData = ce_PerMaterial[subInstanceData.MaterialIndex];
    switch (matData.MATERIAL_BLEND_MODE)
    {
        case MATERIAL_BLEND_MODE_MASKED:
        {
            PSInput input = RayTracingGetPSInput_AnyHit(subInstanceData, triangleIndex, attrib.bary);
            PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
            float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);
            BuiltinData builtInData;
            GetSurfaceAndBuiltinData(V, input, true, subInstanceData, payload.SurfaceData, builtInData);
            if (payload.SurfaceData.opacityMask <= payload.SurfaceData.opacityMaskClip)
            {
                IgnoreHit();
            }
            break;
        }
        case MATERIAL_BLEND_MODE_TRANSLUCENT:
        {
            PSInput input = RayTracingGetPSInput_AnyHit(subInstanceData, triangleIndex, attrib.bary);
            PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
            float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);
            BuiltinData builtInData;
            GetSurfaceAndBuiltinData(V, input, true, subInstanceData, payload.SurfaceData, builtInData);
            float random = CE_PathTracing::rnd(payload.Seed);
            if (random > payload.SurfaceData.opacity)
            {
                IgnoreHit();
            }
            break;
        }
    }
}

#endif
