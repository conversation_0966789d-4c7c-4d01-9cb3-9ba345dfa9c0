#ifndef SHADER_PASS_CLOSEST_HIT_HLSL
#define SHADER_PASS_CLOSEST_HIT_HLSL

#define CUSTOM_VS_OUTPUT
#define CUSTOM_PS_INPUT

#include "../RayTracing/RayTracingCommonStruct.hlsl"
#include "../Material/Lit/Lit.hlsl"
#include "../ShaderLibrary/MaterialEditor/VertexLayout.hlsl"

StructuredBuffer<SubInstanceData> ce_SubInstanceData : register(space0);
StructuredBuffer<ObjectSceneData> ce_PerObject : register(space2);
StructuredBuffer<PrimitiveSceneData> ce_PerPrimitive : register(space2);

PSInput RayTracingGetPSInput_ClosestHit(in SubInstanceData subInstanceData,
    in uint triangleIndex,
    in float2 bary)
{
    PSInput input = (PSInput)0;

    input.positionWS = WorldRayDirection() + WorldRayDirection() * RayTCurrent();
    input.positionNDC = 0.f;  // Not used for ray tracing?
    
    uint3 indices = GetIndices(subInstanceData, triangleIndex);
    float3 localNormal = GetInterpolatedNormal(subInstanceData, indices, bary);
    input.normalWS = mul((float3x3)WorldToObject4x3(), localNormal);
    float3 localTangent = GetInterpolatedTangent(subInstanceData, indices, bary);
    input.tangentWS = mul((float3x3)WorldToObject4x3(), localTangent);
    
    input.vertexColor = 1.f;  // Not used for ray tracing?

    // TODO(scolu): Only support uv1 for ray tracing now
#if NUM_MATERIAL_TEXCOORDS > 0
    input.uv = GetInterpolatedUV(subInstanceData, indices, bary);
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
    input.uv1 = GetInterpolatedUV1(subInstanceData, indices, bary);
#endif

    input.screenUV = 0.f;
    input.instanceID = subInstanceData.ObjectIndex;  // Used to query ObjectSceneData, but not used for ray tracing, since we store ObjectIndex in SubInstanceData
    input.packedPageInfo = 0u;  // Fot VT
    input.lightViewIndex = 0u;  // Not used for ray tracing

    // CUSTOM_PS_INPUT, not used for ray tracing
    
    return input;
}

[shader("closesthit")]
void closesthit(inout RayTracingPayload payload, in RayTracingAttributes attrib) 
{
    uint subInstanceIndex = InstanceID() + GeometryIndex();
    uint triangleIndex = PrimitiveIndex();
    SubInstanceData subInstanceData = ce_SubInstanceData[subInstanceIndex];
    PSInput input = RayTracingGetPSInput_ClosestHit(subInstanceData, triangleIndex, attrib.bary);

    PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);
    float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);
    GetSurfaceAndBuiltInData(V, input, subInstanceData.ObjectIndex, payload.SurfaceData, payload.BuiltInData);

    payload.HitT = RayTCurrent();
    payload.Bary = attrib.bary;
    payload.Seed = 0;  // TODO(scolu)
}

#endif
