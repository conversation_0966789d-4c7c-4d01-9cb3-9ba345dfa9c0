#ifndef SHADER_PASS_DEPTH_FOR_SHADOW
#define SHADER_PASS_DEPTH_FOR_SHADOW

#include "../Material/MaterialUtilities.hlsl"
#include "../Material/Lit/Lit.hlsl"
#include "../Lighting/Shadow/VirtualShadowMap/VirtualShadowMapPageAccessCommon.hlsl"
#include "../Lighting/Shadow/VirtualShadowMap/VirtualShadowMapProjectionCommon.hlsl"

#ifndef ENABLE_VSM
void ApplyShadowBias(inout VSOutput vsOutput)
{
#ifdef PUNCTUAL_LIGHT
    float4 posView = mul(ce_InvProjection, vsOutput.positionNDC);
    posView /= posView.w;
    const float3 viewDirection = -normalize(posView.xyz);
    const float3 viewNormal = mul(ce_View, float4(vsOutput.normalWS.xyz, 0.0f));
    const float NoL = abs(dot(viewDirection, viewNormal));
#else
    const float NoL = abs(dot(float3(ce_View[2].xyz), vsOutput.normalWS));
#endif

    const float maxSlopeDepthBias = ce_ShadowParams.z;
    const float slope = clamp(abs(NoL) > 0 ? sqrt(saturate(1 - NoL * NoL)) / NoL : maxSlopeDepthBias, 0, maxSlopeDepthBias);

    const float slopeDepthBias = ce_ShadowParams.y;
    const float slopeBias = slopeDepthBias * slope;

    const float constantDepthBias = ce_ShadowParams.x;

    const float depthBias = constantDepthBias + slopeBias;

    float adjustedDepth = vsOutput.positionNDC.z + depthBias;
    vsOutput.positionNDC.z = adjustedDepth;
}
#endif

VSOutput VSMain(VSInput input)
{
	VSOutput output = VSInputToVSOutput(input);

#ifndef ENABLE_VSM
    #ifndef PUNCTUAL_LIGHT
    ApplyShadowBias(output);
    #endif
#endif

    return output;
}

#ifdef PUNCTUAL_LIGHT

    void PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace, out float depth : SV_Depth)
    {
        PSInput input = VSOutputToPSInput(vsoutput);
        PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

        #if defined(USE_MULTI_VIEWPORT) && defined(CE_INSTANCING)
                LocalLightViewData viewData = _LocalLightViewDatas[vsoutput.lightViewIndex];
                depth = length(posInput.positionWS.xyz - viewData.lightPosition.xyz) / viewData.lightTileAndRange.w;
        #else // not USE_MULTI_VIEWPORT
                depth = length(posInput.positionWS.xyz - ce_CameraPos.xyz) / _NormalizeFactor;
        #endif // USE_MULTI_VIEWPORT
    }

#elif defined(ENABLE_VSM)

    void PSMain(VSOutput vsOutput, bool isFrontFace : SV_IsFrontFace)
    {
        PSInput input = VSOutputToPSInput(vsOutput);
        PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

#ifdef VSM_ALPHACLIP
        float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

        SurfaceData surfaceData;
        BuiltinData builtinData;
    #if defined(INSTANCING) && INSTANCING == 1
        GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
    #else
        GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
    #endif
#endif

        PageInfo pageInfo = UnpackPageInfo(input.packedPageInfo);
        VirtualShadowMapViewData viewData = _VirtualShadowMapViewDatas[pageInfo.viewId];

        const uint2 vAddress = uint2(input.positionNDC.xy);
        const float depth = input.positionNDC.z;

        const uint pageTableValue = _PageTable[CalcPageOffset(viewData.virtualShadowMapId, viewData.levelIndex, vAddress >> VSM_LOG2_PAGE_SIZE)];
        ShadowPhysicalPage page = ShadowDecodePageTable(pageTableValue);

        if (page.isThisLODValid)
        {
            uint2 pAddress = page.physicalAddress * VSM_PAGE_SIZE + (vAddress & VSM_PAGE_SIZE_MASK);
            InterlockedMax(_PhysicalPagePool[pAddress], asuint(depth));
        }
    }
#else // Dirtional Light CSM
    void PSMain(VSOutput vsOutput, bool isFrontFace : SV_IsFrontFace)
    {
        PSInput input = VSOutputToPSInput(vsOutput);
        PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

        //float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

 //       SurfaceData surfaceData;
 //       BuiltinData builtinData;
 //   #if defined(INSTANCING) && INSTANCING == 1
 //       GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
 //   #else
 //       GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
 //   #endif
    }
#endif

#endif