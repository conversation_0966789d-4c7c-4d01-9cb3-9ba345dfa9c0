#ifndef SHADER_PASS_G_BUFFER
#define SHADER_PASS_G_BUFFER

#include "../Material/MaterialUtilities.hlsl"
#include "../Material/Lit/Lit.hlsl"



#ifdef TESSELLATION_ENABLE
// #if defined(TESSELLATION) && TESSELLATION
// --------------------------------------------------------------------
// Tessellation Stage
// --------------------------------------------------------------------
struct ConstantHSOut
{
	float TessLevelOuter[3] : SV_TessFactor; 
	float TessLevelInner : SV_InsideTessFactor; 
};

float4 CalculateCompositeTessellationFactors(float3 Control0, float3 Control1, float3 Control2)
{
// #if USE_ADAPTIVE_TESSELLATION_FACTOR
// #if 1
// 	half MaxDisplacement = GetMaterialMaxDisplacement();

// 	// Frustum cull
// 	int3 ClipFlag = 0;
// 	ClipFlag  = GetClipFlag( Control0, MaxDisplacement );
// 	ClipFlag |= GetClipFlag( Control1, MaxDisplacement );
// 	ClipFlag |= GetClipFlag( Control2, MaxDisplacement );
// 	if( any( ClipFlag != 3 ) )
// 	{
// 		return 0;
// 	}
// #endif

	float3 Edge0 = ( Control0 - Control1 );
	float3 Edge1 = ( Control1 - Control2 );
	float3 Edge2 = ( Control2 - Control0 );

	float3 ToMidpoint0 = 0.5 * ( Control0 + Control1 ) - ce_CameraPos.xyz;
	float3 ToMidpoint1 = 0.5 * ( Control1 + Control2 ) - ce_CameraPos.xyz;
	float3 ToMidpoint2 = 0.5 * ( Control2 + Control0 ) - ce_CameraPos.xyz;

	// float d0 = dot( ToMidpoint1, ToMidpoint1 );
	// float d1 = dot( ToMidpoint2, ToMidpoint2 );
	// float d2 = dot( ToMidpoint1, ToMidpoint1 );

	// Use spherical projection instead of planer
	float4 CompositeFactors = float4(
		sqrt( dot( Edge1, Edge1 ) / dot( ToMidpoint1, ToMidpoint1 ) ),
		sqrt( dot( Edge2, Edge2 ) / dot( ToMidpoint2, ToMidpoint2 ) ),
		sqrt( dot( Edge0, Edge0 ) / dot( ToMidpoint0, ToMidpoint0 ) ),
		1 );
	CompositeFactors.w = 0.333 * ( CompositeFactors.x + CompositeFactors.y + CompositeFactors.z );

	// The adaptive tessellation factor is 0.5 * ResolvedView.ViewToClip[1][1] * ViewSizeY / PixelsPerEdge and CompositeFactors is 2 * PercentageOfScreen.  
	// return View.AdaptiveTessellationFactor * CompositeFactors;
	return CompositeFactors * 20;
// #else
// 	return float4( 1.0,1.0,1.0,1.0 );
// #endif
}

VSInput VSMain(VSInput input)
{
	VSInput I = (VSInput)0;
	I = input;
	return I;
}

ConstantHSOut ConstantHS(InputPatch<VSInput, 3> patch, uint patchID : SV_PrimitiveID)
{
	ConstantHSOut chsout = (ConstantHSOut)0;

    // Adaptively adjust the tessellation factor based on camera distance
    // Tessellate the patch based on distance from the eye such that
	// the tessellation is 0 if d >= _Distance1 and _TessLevel if d <= _Distance0.  The interval
	// [_Distance0, _Distance1] defines the range we tessellate in.

	// float _Distance0 = 20;
	// float _Distance1 = 10000;
	// float _TessLevel = 64;

    // float4 centerL = 0.33f*(patch[0].position + patch[1].position + patch[2].position);  //tri need barycentric coord
	// float4 centerW = mul(ce_World,centerL);

    // float d = distance(ce_CameraPos.xyz , centerW.xyz);
    // //float tessf = max(1.0, _TessLevel * saturate((_Distance1-d)/(_Distance1-_Distance0)));
	// float tessf = max(1.0, _TessLevel * saturate((_Distance1-d)));

	// chsout.TessLevelOuter[0] = tessf;
	// chsout.TessLevelOuter[1] = tessf;
	// chsout.TessLevelOuter[2] = tessf;
	
	// chsout.TessLevelInner = tessf;

	// float3 centerLS = 0.33f*(patch[0].position + patch[1].position + patch[2].position);  //tri need barycentric coord
	// float3 centerWS = mul(ce_World,centerLS);

    // float d = distance(ce_CameraPos.xyz , centerWS);
	// // in full tessellation range 
	// float range = max(FullTessRange, 100);
	// if(d < range)
	// {
	// 	float tessf = clamp(TessLevel, 1, 15);
	// 	chsout.TessLevelOuter[0] = tessf;
	// 	chsout.TessLevelOuter[1] = tessf;
	// 	chsout.TessLevelOuter[2] = tessf;
	
	// 	chsout.TessLevelInner = tessf;
	// }
	// else
	// {
	// 	float3 WorldPositon0 = mul(ce_World, patch[0].position);
	// 	float3 WorldPositon1 = mul(ce_World, patch[1].position);
	// 	float3 WorldPositon2 = mul(ce_World, patch[2].position);

	// 	float4 CompositeTessFactors = TessLevel * CalculateCompositeTessellationFactors(WorldPositon0, WorldPositon1, WorldPositon2);
	// 	CompositeTessFactors = clamp(CompositeTessFactors , 1 , 15);
	
	// 	chsout.TessLevelOuter[0] = CompositeTessFactors.x;
	// 	chsout.TessLevelOuter[1] = CompositeTessFactors.y;
	// 	chsout.TessLevelOuter[2] = CompositeTessFactors.z;
	// 	chsout.TessLevelInner = CompositeTessFactors.w;
	// }

	float3 WorldPositon0 = mul(ce_World, patch[0].position);
	float3 WorldPositon1 = mul(ce_World, patch[1].position);
	float3 WorldPositon2 = mul(ce_World, patch[2].position);

	float4 CompositeTessFactors = TessLevel * CalculateCompositeTessellationFactors(WorldPositon0, WorldPositon1, WorldPositon2);
	CompositeTessFactors = clamp(CompositeTessFactors , 1 , 15);
	
	chsout.TessLevelOuter[0] = CompositeTessFactors.x;
	chsout.TessLevelOuter[1] = CompositeTessFactors.y;
	chsout.TessLevelOuter[2] = CompositeTessFactors.z;
	chsout.TessLevelInner = CompositeTessFactors.w;
	
	return chsout;
}

[domain("tri")]  
[partitioning("fractional_odd")]  
[outputtopology("triangle_cw")]  
[outputcontrolpoints(3)]  
[patchconstantfunc("ConstantHS")]  
[maxtessfactor(15)] 

VSInput HSMain(InputPatch<VSInput, 3> p, uint i : SV_OutputControlPointID, uint patchId : SV_PrimitiveID)
{
	VSInput I = (VSInput)0;
	I = p[i];
	return I;
}


[domain("tri")]  
VSOutput DSMain(ConstantHSOut HSConstantData, float3 BarycentricCoords : SV_DomainLocation, const OutputPatch<VSInput, 3> I)
{
	VSInput input = (VSInput)0;

	// get the BarycentricCoords
	float U = BarycentricCoords.x;
	float V = BarycentricCoords.y;
	float W = BarycentricCoords.z;

#if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain
    input.position = I[0].position * U + I[1].position * V + I[2].position * W;
	#ifdef TERRAIN_USE_INSTANCING
    	input.slotIndex = (uint)(I[0].slotIndex * U + I[1].slotIndex * V + I[2].slotIndex * W);
	#endif

#else // defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain

#ifdef USE_VERTEX_COLOR
    input.color = I[0].color * U + I[1].color * V + I[2].color * W;
#endif

#ifdef USED_WITH_SKELETAL_MESH
	input.prePosition = I[0].prePosition * U + I[1].prePosition * V + I[2].prePosition * W;
#endif
	// Interpolate attributes according to our tessellation scheme
	input.position = I[0].position * U + I[1].position * V + I[2].position * W;
	input.uv = I[0].uv * U + I[1].uv * V + I[2].uv * W;
	input.uv1 = I[0].uv1 * U + I[1].uv1 * V + I[2].uv1 * W;

	input.normal = normalize(I[0].normal * U + I[1].normal * V + I[2].normal * W);
	input.tangent = normalize( I[0].tangent * U + I[1].tangent * V + I[2].tangent * W);

#ifdef VEGATATION_AO_UV
	input.uv2 = I[0].uv2 * U + I[1].uv2 * V + I[2].uv2 * W;
#endif
    
#if defined(INSTANCING) && INSTANCING == 1
	input.instanceID = (uint)(I[0].instanceID * U + I[1].instanceID * V + I[2].instanceID * W);

	
#endif

#endif // defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain

	return VSInputToVSOutput(input);
}

#else // defined(TESSELLATION) && TESSELLATION
VSOutput VSMain(VSInput input)
{
	return VSInputToVSOutput(input);
}
#endif // defined(TESSELLATION) && TESSELLATION


struct PSOutput
{
	float4 gBuffer0 : SV_Target0;
	float4 gBuffer1 : SV_Target1;
	float4 gBuffer2 : SV_Target2;
	float4 gBuffer3 : SV_Target3;
	float4 gBuffer4 : SV_Target4;
    float4 gBuffer5 : SV_Target5;
    float4 gBuffer6 : SV_Target6;
};



PSOutput PSMain(VSOutput vsoutput, bool isFrontFace : SV_IsFrontFace, uint coverage : SV_Coverage)
{
    PSInput input = VSOutputToPSInput(vsoutput);
	PositionInputs posInput = GetPositionInput(input.positionWS, input.screenUV);

	float3 V = GetWorldSpaceNormalizeViewDir(posInput.positionWS);

	SurfaceData surfaceData;
	BuiltinData builtinData;
#ifdef TERRAIN_USE_INSTANCING
	GetSurfaceAndBuiltinData(V, input, isFrontFace, input.slotIndex, surfaceData, builtinData);
#elif defined(INSTANCING) && INSTANCING == 1
	GetSurfaceAndBuiltinData(V, input, isFrontFace, input.instanceID, surfaceData, builtinData);
#else
	GetSurfaceAndBuiltinData(V, input, isFrontFace, 0, surfaceData, builtinData);
#endif

	builtinData.coverage = float(coverage);

	// VT FeedbackBuffer
	//if(_TextureEnableVT)
#ifdef OPEN_VT
	{
		FVirtualTextureFeedbackParams FeedbackParams = {surfaceData.virtualTextureFeedbackRequest, 0};
		uint FrameNumber = 1;
		float Opacity = 1.0;
		FinalizeVirtualTextureFeedback(FeedbackParams, input.positionNDC, Opacity, FrameNumber, VTFeedbackBuffer);
	}
#endif						

    PSOutput output = (PSOutput)0;

	// Always use 6-parameter version, C++ side controls which targets are allocated
	EncodeIntoGBuffer(surfaceData, builtinData, output.gBuffer0, output.gBuffer1, output.gBuffer2, output.gBuffer3, output.gBuffer4, output.gBuffer5, output.gBuffer6);
	return output;
}

#endif