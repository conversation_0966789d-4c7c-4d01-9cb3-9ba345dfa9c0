#ifndef BINDLESS_COMMON_HLSL
#define BINDLESS_COMMON_HLSL

// ------------------------buffers--------------------------
Buffer<uint4> ce_BindlessUIntBuffers[]  : register(space3);
Buffer<float4> ce_BindlessFloatBuffers[] : register(space4);
Buffer<int4> ce_BindlessIntBuffers[] : register(space5);
Texture2D<float4> ce_BindlessTextures[] : register(space6);
SamplerState ce_Samplers[] : register(space7);

#define CE_INVALID_BINDLESS_INDEX 0xffffffff

// --------------------------------------RayTracing-----------------------------------------
struct SubInstanceData
{
    uint PosBufferIndex;
    uint ColorBufferIndex;
    uint NormalBufferIndex;
    uint TangentBufferIndex;
    
    uint BinormalBufferIndex;
    uint UVBufferIndex;
    uint UV1BufferIndex;
    uint IndexBufferIndex;
    
    uint ObjectIndex;
    uint MaterialIndex;
    uint VertexStart;
    uint IndexStart;
};

uint3 GetIndices(in SubInstanceData SubInstanceData, in uint PrimitiveIndex)
{
    uint3 Indices = (uint3)0;
    if (SubInstanceData.IndexBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        Buffer<uint4> IndexBuffer = ce_BindlessUIntBuffers[NonUniformResourceIndex(SubInstanceData.IndexBufferIndex)];
        uint FirstTriangleIndexQuery = SubInstanceData.IndexStart + PrimitiveIndex * 3;
        Indices = uint3(
            IndexBuffer[FirstTriangleIndexQuery].x,
            IndexBuffer[FirstTriangleIndexQuery + 1].x,
            IndexBuffer[FirstTriangleIndexQuery + 2].x
        ) + SubInstanceData.VertexStart;
    }
    return Indices;
}

float3 GetVertexPosition(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float3 Pos = (float3)0;
    if (SubInstanceData.PosBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        Pos = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.PosBufferIndex)][VertexIndex].xyz;
    }
    return Pos;
}

float3 GetVertexNormal(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float3 Normal = (float3)0;
    if (SubInstanceData.NormalBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        Normal = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.NormalBufferIndex)][VertexIndex];
    }
    return normalize(Normal);
}

float3 GetVertexTangent(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float3 Tangent = (float3)0;
    if (SubInstanceData.TangentBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        Tangent = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.TangentBufferIndex)][VertexIndex].xyz;
    }
    return Tangent;
}

float3 GetVertexBinormal(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float3 Binormal = (float3)0;
    if (SubInstanceData.BinormalBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        Binormal = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.BinormalBufferIndex)][VertexIndex].xyz;
    }
    return Binormal;
}

float2 GetVertexUV(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float2 UV = (float2)0;
    if (SubInstanceData.UVBufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        UV = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.UVBufferIndex)][VertexIndex].xy;
    }
    return UV;
}

float2 GetVertexUV1(in SubInstanceData SubInstanceData, in uint VertexIndex)
{
    float2 UV1 = (float2)0;
    if (SubInstanceData.UV1BufferIndex != CE_INVALID_BINDLESS_INDEX)
    {
        UV1 = ce_BindlessFloatBuffers[NonUniformResourceIndex(SubInstanceData.UV1BufferIndex)][VertexIndex].xy;
    }
    return UV1;
}

float3 GetInterpolatedNormal(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float3 Normal0 = GetVertexNormal(SubInstanceData, VertexIndices.x);
    float3 Normal1 = GetVertexNormal(SubInstanceData, VertexIndices.y);
    float3 Normal2 = GetVertexNormal(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * Normal0 + BaryFloat3.y * Normal1 + BaryFloat3.z * Normal2;
}

float3 GetInterpolatedPosition(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float3 Pos0 = GetVertexPosition(SubInstanceData, VertexIndices.x);
    float3 Pos1 = GetVertexPosition(SubInstanceData, VertexIndices.y);
    float3 Pos2 = GetVertexPosition(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * Pos0 + BaryFloat3.y * Pos1 + BaryFloat3.z * Pos2;
}

float3 GetInterpolatedTangent(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float3 Tangent0 = GetVertexTangent(SubInstanceData, VertexIndices.x);
    float3 Tangent1 = GetVertexTangent(SubInstanceData, VertexIndices.y);
    float3 Tangent2 = GetVertexTangent(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * Tangent0 + BaryFloat3.y * Tangent1 + BaryFloat3.z * Tangent2;
}

float3 GetInterpolatedBinormal(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float3 Binormal0 = GetVertexBinormal(SubInstanceData, VertexIndices.x);
    float3 Binormal1 = GetVertexBinormal(SubInstanceData, VertexIndices.y);
    float3 Binormal2 = GetVertexBinormal(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * Binormal0 + BaryFloat3.y * Binormal1 + BaryFloat3.z * Binormal2;
}

float2 GetInterpolatedUV(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float2 UV0 = GetVertexUV(SubInstanceData, VertexIndices.x);
    float2 UV1 = GetVertexUV(SubInstanceData, VertexIndices.y);
    float2 UV2 = GetVertexUV(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * UV0 + BaryFloat3.y * UV1 + BaryFloat3.z * UV2;
}

float2 GetInterpolatedUV1(in SubInstanceData SubInstanceData, in uint3 VertexIndices, in float2 bary)
{
    float3 BaryFloat3 = float3(1.f - bary.x - bary.y, bary.x, bary.y);
    float2 UV10 = GetVertexUV1(SubInstanceData, VertexIndices.x);
    float2 UV11 = GetVertexUV1(SubInstanceData, VertexIndices.y);
    float2 UV12 = GetVertexUV1(SubInstanceData, VertexIndices.z);
    return BaryFloat3.x * UV10 + BaryFloat3.y * UV11 + BaryFloat3.z * UV12;
}

// // ------------------------textures--------------------------
// [[vk::binding(0, 3)]]
// Texture2D<float4> BindlessTextures[];

// [[vk::binding(0, 3)]]
// Texture2DArray<float4> ;

// [[vk::binding(0, 3)]]
// Texture1D<float4> ;

// [[vk::binding(0, 3)]]
// Texture1DArray<float4> ;

// [[vk::binding(0, 3)]]
// Texture3D<float4> ;

// [[vk::binding(0, 3)]]
// TextureCube<float4> ;

// [[vk::binding(0, 3)]]
// TextureCubeArray<float4> ;

#endif
