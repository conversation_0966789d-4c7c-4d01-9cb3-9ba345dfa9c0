#ifndef DECAL_HLSL
#define DECAL_HLSL

SHADER_CONST(bool, Decal_BaseColor_Blend, true);
SHADER_CONST(bool, Decal_Normal_Blend, false);
SHADER_CONST(bool, Decal_Metallic_Blend, false);
SHADER_CONST(bool, Decal_Specular_Blend, false);
SHADER_CONST(bool, Decal_Roughness_Blend, false);
SHADER_CONST(bool, Decal_Emissive_Blend, false);
SHADER_CONST(bool, Decal_Opacity, true);

cbuffer cbDecalTile : register(space2)
{
	float3 ce_ParentTilePosition;
}

//-----------------------------------------------------------------------------
// Decal Data, some property may not used
//-----------------------------------------------------------------------------

struct DecalData
{
	float3 BaseColor;
	float Metallic;
	float Specular;
	float Roughness;
	float3 EmissiveColor;
	float Opacity;
	float3 Normal;
	float3 WorldPositionOffset; // Not used
	float3 AmbientOcclusion; // Not Used
};

void DecalOutput(SurfaceData surfaceData, out float4 DBuffer0, out float4 DBuffer1, out float4 DBuffer2, out float4 Emisive)
{
	float surfaceAlpha = surfaceData.opacity;
	float Alpha0 = 0.0, Alpha1 = 0.0, Alpha2 = 0.0, Alpha3 = 0.0;

	if(Decal_BaseColor_Blend)
	{
		Alpha0 = surfaceAlpha;
	}
    if(Decal_Normal_Blend)
	{
		Alpha1 = surfaceAlpha;
	}
    if(Decal_Metallic_Blend || Decal_Specular_Blend || Decal_Roughness_Blend)
	{
		Alpha2 = surfaceAlpha;
	}
    if(Decal_Emissive_Blend)
	{
		Alpha3= 1.0;
	}

	DBuffer0 = float4(surfaceData.baseColor, Alpha0);
	// surfaceData.normalTS is used as world space normal
	DBuffer1 = float4(surfaceData.normalTS * 0.5f + 128.0f/255.0f, Alpha1);
	// default value is (0.5, 0, 0.5)
	DBuffer2 = float4(surfaceData.roughness, surfaceData.metallic, surfaceData.specular, Alpha2);

	Emisive = float4(surfaceData.emissiveColor, Alpha3);
}
/*

struct SurfaceData
{
	uint materialType;
	float3 baseColor;
	float opacity;
	float opacityMask;
	float opacityMaskClip;
	float3 normalTS;
	float roughness;
	float ambientOcclusion;
	float metallic;
	float specular;
	float3 emissiveColor;
	float3 subsurfaceColor;
	float3 debugColor;

	// System
	uint virtualTextureFeedbackRequest;
	float3 normalWS;
	float3 geomNormalWS;
};
*/

void ApplyDBufferData(float4 DBuffer0, float4 DBuffer1, float4 DBuffer2, inout SurfaceData surfaceData)
{
	surfaceData.baseColor = surfaceData.baseColor * DBuffer0.a + DBuffer0.xyz;

	float3 preMulNormal = DBuffer1.xyz * 2 - (256.0 / 255.0);
	surfaceData.normalWS = normalize(surfaceData.normalWS * DBuffer1.a + preMulNormal);

	surfaceData.roughness = surfaceData.roughness * DBuffer2.a + DBuffer2.x;
	surfaceData.metallic = surfaceData.metallic * DBuffer2.a + DBuffer2.y;
	surfaceData.specular = surfaceData.specular * DBuffer2.a + DBuffer2.z;
}

#endif