#ifndef MATERIAL_EDITOR_COMMON_HLSL
#define MATERIAL_EDITOR_COMMON_HLSL

#include "ShaderLibrary/MaterialEditor/SobolRandom.hlsl"
#include "ShaderLibrary/MaterialEditor/SkyAtmosphere.hlsl"
#include "ShaderLibrary/Math/LargeWorldCoordinates.hlsl"

/** Rotates Position about the given axis by the given angle, in radians, and returns the offset to Position. */
float3 RotateAboutAxis(float4 NormalizedRotationAxisAndAngle, float3 PositionOnAxis, float3 Position)
{
	// Project Position onto the rotation axis and find the closest point on the axis to Position
	float3 ClosestPointOnAxis = PositionOnAxis + NormalizedRotationAxisAndAngle.xyz * dot(NormalizedRotationAxisAndAngle.xyz, Position - PositionOnAxis);
	// Construct orthogonal axes in the plane of the rotation
	float3 UAxis = Position - ClosestPointOnAxis;
	float3 VAxis = cross(NormalizedRotationAxisAndAngle.xyz, UAxis);
	float CosAngle;
	float SinAngle;
	sincos(NormalizedRotationAxisAndAngle.w, SinAngle, CosAngle);
	// Rotate using the orthogonal axes
	float3 R = UAxis * CosAngle + VAxis * SinAngle;
	// Reconstruct the rotated world space position
	float3 RotatedPosition = ClosestPointOnAxis + R;
	// Convert from position to a position offset
	return RotatedPosition - Position;
}

#define LWCADDRESSMODE_CLAMP 0u
#define LWCADDRESSMODE_WRAP 1u
#define LWCADDRESSMODE_MIRROR 2u

float DFApplyAddressModeWrap(FDFScalar V)
{
	// Hacky and certainly not flawless, but reasonably fast.
	// The resulting value is not in [0;1] so the texture unit apply the final coordinate wrapping, which will allow derivatives to work correctly unless cross the modulo boundary.
    const float Divisor = 0x1000; // Chosen somewhat arbitrarily. Large enough to not have seams everywhere, but small enough to have plenty of fractional precision in the result.
    return DFFmodByPow2Demote(V, Divisor);
}

float DFApplyAddressModeMirror(FDFScalar V)
{
	// Unclear what the best option is for MIRROR
	// We can apply the mirror logic directly, but that will break derivatives
	// We can use similar logic as WRAP, but in that case results will actually be incorrect at tile boundaries (not just wrong derivatives)
	// Or we can convert to float and accept precision loss for large values (but otherwise correct)
	// TODO - something better?

    float a = DFFracDemote(DFDivideByPow2(V, 2));
    float b = 2.0 * a;
    float c = 2.0 * (1.0 - a);
    return min(b, c); //DF_TODO: derivatives? Maybe use the same logic from Wrap instead?
}

float DFApplyAddressModeClamp(FDFScalar V)
{
	// For the CLAMP case, a simple DFDemote() is sufficient.  This will lose a ton of precision for large values, but we don't care about this since the GPU will clamp to [0,1) anyway
	// It's possible certain GPUs might need a special case if the ToFloat() conversion overflows
    return DFDemote(V);
}

float DFApplyAddressMode(FDFScalar v, uint AddressMode)
{
    if (AddressMode == LWCADDRESSMODE_WRAP)
        return DFApplyAddressModeWrap(v);
    else if (AddressMode == LWCADDRESSMODE_MIRROR)
        return DFApplyAddressModeMirror(v);
    else
        return DFApplyAddressModeClamp(v);
}

float2 DFApplyAddressMode(FDFVector2 UV, uint AddressX, uint AddressY)
{
    return float2(DFApplyAddressMode(DFGetX(UV), AddressX), DFApplyAddressMode(DFGetY(UV), AddressY));
}

float3 DFApplyAddressMode(FDFVector3 UV, uint AddressX, uint AddressY, uint AddressZ)
{
    return float3(DFApplyAddressMode(DFGetX(UV), AddressX), DFApplyAddressMode(DFGetY(UV), AddressY), DFApplyAddressMode(DFGetZ(UV), AddressZ));
}

#endif