#ifndef MOTIONVECTOR_HLSL
#define MOTIONVECTOR_HLSL

// for velocity rendering, motionblur and temporal AA
// since the input is preNDC - nowNDC, thus its range is [-2, 2]
// velocity needs to support -2..2 screen space range for x and y
// texture is 16bit 0..1 range per channel
float2 EncodeVelocityToTexture(float2 V)
{  
    // Avoid using the full range to use the clear color (0,0) as special value
    // Allow for a range of -2..2 instead of -1..1 for really fast motions for temporal AA
    V = V * (0.499f * 0.5f) + 32767.0f / 65535.0f; 
    return V;
}

// decode from [0, 1] to [-2, 2], since it's NDC, -1 means left down and 1 means right up
float2 DecodeVelocityFromTexture(float2 EncodedV)
{
    const float InvDiv = 1.0f / (0.499f * 0.5f);
    return EncodedV * InvDiv - 32767.0f / 65535.0f * InvDiv; 
}

float2 ComputeStaticVelocity(float4 PixelNDC, float4x4 fReprojectionMat)
{
    float4 fPositionNDC = PixelNDC;
    float4 fLastPositionView = mul(fReprojectionMat, fPositionNDC);
    float2 fLastPositionNDC = fLastPositionView.xy / fLastPositionView.w;
    float2 fSrcMotionVector = fLastPositionNDC - fPositionNDC.xy;

    return fSrcMotionVector;
}

float2 ComputeStaticVelocity(float2 PixelUV, float4x4 fReprojectionMat, float DeviceZ)
{
    float2 fPositionUV = PixelUV;
    float4 fPositionNDC = float4(fPositionUV.x * 2 - 1, 1 - fPositionUV.y * 2, DeviceZ, 1);
    	
	return ComputeStaticVelocity(fPositionNDC, fReprojectionMat);
}

float2 ComputeStaticVelocity(uint2 PixelPos, float4x4 fReprojectionMat, uint2 ScreenSize, float DeviceZ)
{
    float2 fPositionUV = (PixelPos + 0.5) / ScreenSize;

	return ComputeStaticVelocity(fPositionUV, fReprojectionMat, DeviceZ);
}

#endif
