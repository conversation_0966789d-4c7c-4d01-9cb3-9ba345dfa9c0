#ifndef QTANGENTS_HLSL
#define QTANGENTS_HLSL

float3 xAxis( float4 qQuat )
{
  float fTy  = 2.0 * qQuat.y;
  float fTz  = 2.0 * qQuat.z;
  float fTwy = fTy * qQuat.w;
  float fTwz = fTz * qQuat.w;
  float fTxy = fTy * qQuat.x;
  float fTxz = fTz * qQuat.x;
  float fTyy = fTy * qQuat.y;
  float fTzz = fTz * qQuat.z;

  return float3( 1.0-(fTyy+fTzz), fTxy+fTwz, fTxz-fTwy );
}

float3 yAxis( float4 qQuat )
{
  float fTx  = 2.0 * qQuat.x;
  float fTy  = 2.0 * qQuat.y;
  float fTz  = 2.0 * qQuat.z;
  float fTwx = fTx * qQuat.w;
  float fTwz = fTz * qQuat.w;
  float fTxx = fTx * qQuat.x;
  float fTxy = fTy * qQuat.x;
  float fTyz = fTz * qQuat.y;
  float fTzz = fTz * qQuat.z;

  return float3( fTxy-fTwz, 1.0-(fTxx+fTzz), fTyz+fTwx );
}


void decode_QTangent_to_tangent(in float4 in_qtangent, out float3 normal, out float4 tangent)
{
    float4 qtangent = normalize(in_qtangent); //Needed because 16-bit quantization
    normal = xAxis(qtangent);
    tangent.xyz = yAxis(qtangent);
    tangent.w = sign(in_qtangent.w); //ensured qtangent.w != 0
}

#endif
