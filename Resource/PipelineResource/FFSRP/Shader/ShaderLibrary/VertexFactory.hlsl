#ifndef VERTEX_FACTORY_HLSL
#define VERTEX_FACTORY_HLSL

struct VSInput
{
#ifdef USE_VERTEX_COLOR
    float4 color : COLOR;
#endif

	float4 position : POSITION;

#ifdef USED_WITH_SKELETAL_MESH
	float4 prePosition : POSITIONT;
#endif


#ifndef QTANGENT
	float3 normal : NORMAL;
	float4 tangent : TANGENT;
#else
	float4 Qtangent : QUATTAN;
#endif

#if NUM_MATERIAL_TEXCOORDS > 0
	float2 uv : TEXCOORD0;
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	float2 uv1 : TEXCOORD1;
#endif

	uint instanceID : SV_InstanceID;

#if defined(INSTANCING) && INSTANCING == 1
    matrix ce_World : INSTANCE_ce_World;
	matrix ce_PreWorld: INSTANCE_ce_PreWorld;
	#ifdef CE_USE_DOUBLE_TRANSFORM
		float3 ce_TilePosition : INSTANCE_ce_TilePosition;
		float3 ce_PreTilePosition : INSTANCE_ce_PreTilePosition;
	#endif

	matrix ce_InvTransposeWorld : INSTANCE_ce_InvTransposeWorld;
	int4 ce_LightIndex : INSTANCE_ce_LightIndex;
	int ce_LightCount : INSTANCE_ce_LightCount;

	uint instanceIDOffset : INSTANCE_ce_InstanceIDOffset;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	float4 uvIdx : NORMAL2;
#endif

#if defined(PARTICLE) && PARTICLE == 1
	uint   particle_CullMask         : INSTANCE_PARTICLE_CULLMASK;
    float3 particle_Position         : INSTANCE_PARTICLE_POSITION;
    float3 particle_AnimatedVelocity : INSTANCE_PARTICLE_ANIMATED_VELOCITY;
    float3 particle_SizeScale        : INSTANCE_PARTICLE_SIZE_SCALE;
    float3 particle_Rotation         : INSTANCE_PARTICLE_ROTATION;
	float4 particle_UVScale          : INSTANCE_PARTICLE_UV_SCALE;
    float4 particle_Color            : INSTANCE_PARTICLE_COLOR;
#endif

#ifdef ADD_VERTEX_INPUT
       ADD_VERTEX_INPUT
#endif
};

struct VSOutput
{
#ifdef USE_VERTEX_COLOR
    float4 color : COLOR0;
#endif

    float3 normalWS : NORMAL0;
	float4 tangentWS : TANGENT0;
#if NUM_MATERIAL_TEXCOORDS
	float4 uvs[(NUM_MATERIAL_TEXCOORDS + 1) >> 1] : TEXCOORD0;
#endif
	float4 positionNDC : SV_POSITION;

#if WRITES_VELOCITY_TO_GBUFFER
    float4 prePositionNDC : NORMAL1;
    float4 nowPositionNDC : TANGENT1;
#endif

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	float4 vertexAerialPerspective : TEXCOORD12;
#endif
#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	float4 uvIdx : NORMAL2;
#endif
    #ifdef ADD_VERTEX_OUTPUT
          ADD_VERTEX_OUTPUT
   #endif
};

struct PSInput
{
#ifdef USE_VERTEX_COLOR
    float4 color : COLOR0;
#endif
	float3 normalWS : NORMAL0;
	float3 tangentWS : TANGENT0;
	float3 binormalWS : BINORMAL0;
#if NUM_MATERIAL_TEXCOORDS > 0
	float2 uv : TEXCOORD0;
	float4 uvs[(NUM_MATERIAL_TEXCOORDS + 1) >> 1];
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
	float2 uv1 : TEXCOORD1;
#endif
	float2 screenUV : TEXCOORD2;
	float3 positionWS : TEXCOORD3;
	float4 positionNDC : SV_POSITION;

#if WRITES_VELOCITY_TO_GBUFFER
    float4 prePositionNDC : NORMAL1;
    float4 nowPositionNDC : TANGENT1;
#endif

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	float4 vertexAerialPerspective : TEXCOORD12;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	float4 uvIdx : NORMAL2;
#endif

#ifdef ADD_PIXEL_INPUT
	ADD_PIXEL_INPUT
#endif
};



#endif