#ifndef VERTEX_FACTORY_HEADER_HLSL
#define VERTEX_FACTORY_HEADER_HLSL

#define ENABLE_VIEW_MODE_VISUALIZE

#include "ShaderLibrary/GlobalModelVariables.hlsl"

#if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain
     #include "Material/Lit/LitVariablesTerrain.hlsl"
#else
     #include "Material/Lit/LitUEVariables.hlsl"
#endif

#if defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Vegetation
     #ifdef CE_INSTANCING
		#include "VertexFoliageFactory.hlsl" 
	#else
          #include "VertexVegetationFactory.hlsl"
     #endif  
#elif defined(VERTEX_TYPE) && VERTEX_TYPE == VertexType_Terrain
     #include "VertexTerrainFactory.hlsl"
#else
     #include "VertexFactory.hlsl"
#endif



#endif