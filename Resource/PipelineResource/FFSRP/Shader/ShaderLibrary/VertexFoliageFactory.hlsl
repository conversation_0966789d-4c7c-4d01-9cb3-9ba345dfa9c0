#ifndef VERTEX_FOLIAGE_FACTORY_HLSL
#define VERTEX_FOLIAGE_FACTORY_HLSL

#include "CommonStruct.hlsl"

struct VSInput
{
	float4 position : POSITION;
#if NUM_MATERIAL_TEXCOORDS > 0
	float2 uv : TEXCOORD0;
#endif

#ifdef USED_WITH_SKELETAL_MESH
	float4 prePosition : POSITIONT;
#endif

#ifndef QTANGENT
	float3 normal : NORMAL;
	float4 tangent : TANGENT;
#else
	float4 Qtangent : QUATTAN;
#endif

#ifdef USE_VERTEX_COLOR
	float4 color : COLOR;
#endif

#if NUM_MATERIAL_TEXCOORDS > 1
	float2 uv1 : TEXCOORD2;
#endif

#ifdef VEGATATION_AO_UV
	float2 uv2 : TEXCOORD1;
#endif

	uint instanceID : SV_InstanceID;
	uint instanceIDOffset : INSTANCE_ce_InstanceIDOffset;

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	int4 uvIdx : NORMAL2;
#endif
};

struct VSOutput
{
#ifdef USE_VERTEX_COLOR
    float4 color : COLOR;
#endif

	float3 normalWS : NORMAL0;
	float3 tangentWS : TANGENT;
	float3 binormalWS : BINORMAL;
#if NUM_MATERIAL_TEXCOORDS 
	float4 uv[(NUM_MATERIAL_TEXCOORDS + 1) >> 1] : TEXCOORD0;
#endif

#ifdef VEGATATION_AO_UV
    float2 uv2 : TEXCOORD2;
#endif

	float4 positionNDC : SV_POSITION;

	uint instanceID : NORMAL1;
#if WRITES_VELOCITY_TO_GBUFFER
    float4 prePositionNDC : COLOR1;
    float4 nowPositionNDC : COLOR2;
#endif

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	float4 vertexAerialPerspective : TEXCOORD12;
#endif

#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	int4 uvIdx : NORMAL2;
#endif
#ifdef ADD_VERTEX_OUTPUT
	ADD_VERTEX_OUTPUT
#endif
};

struct PSInput
{
#ifdef USE_VERTEX_COLOR
    float4 color : COLOR;
#endif
	float3 normalWS : NORMAL0;
	float3 tangentWS : TANGENT;
	float3 binormalWS : BINORMAL;
#if NUM_MATERIAL_TEXCOORDS > 0
	float2 uv : TEXCOORD0;
#endif
#if NUM_MATERIAL_TEXCOORDS > 1
	float2 uv1 : TEXCOORD1;
#endif
#ifdef VEGATATION_AO_UV
    float2 uv2 : TEXCOORD2;
#endif
	float2 screenUV : TEXCOORD4;
	float3 positionWS : TEXCOORD5; // stay out of shadow_pass, incase of hashed alpha
	float4 positionNDC : SV_POSITION;

	uint instanceID : NORMAL1;
    float4 prePositionNDC;
    float4 nowPositionNDC;

#ifdef USE_VERTEX_AERIAL_PERSPECTIVE
	float4 vertexAerialPerspective : TEXCOORD12;
#endif
#if defined(TEXTURE_ARRAY_ENABLE) && TEXTURE_ARRAY_ENABLE == 1
	int4 uvIdx : NORMAL2;
#endif

#ifdef ADD_PIXEL_INPUT
	ADD_PIXEL_INPUT
#endif
};

#endif