#ifndef VERTEX_TERRAIN_HLSL
#define VERTEX_TERRAIN_HLSL

//#include "../../../../EngineResource/Shader/TerrainCommon.hlsl"
#include "VertexTerrainFactory.hlsl"

#define SURFACE_FLAT        0
#define SURFACE_SPHERICAL   1
#define SURFACE_WGS84       2
static const float TERRAIN_Y_SCALE = 1.f / 128.0f;

struct TerrainData
{
    float4 GridDim;
    float4 WorldTranslation;
    float4 WorldScale;
    float4 PatchOffsetAndScale;
    uint SlotIndex;
    uint LodInfo;
    uint PatchSize;
};

struct TerrainVSToPS
{
    float4 Position;
    float4 Normal;
    float4 Tangent;
    float4 Bitangent;
    float2 TexCoord;
};

float4 GetPatchOffsetAndScale(uint Index)
{
#ifdef TERRAIN_USE_INSTANCING
    return _TerrainInstanceBufferStore[Index & 0xFFFFU].PatchOffsetAndScale;
#else
    return PatchOffsetAndScale;
#endif
}

uint GetLodInfo(uint Index)
{
#ifdef TERRAIN_USE_INSTANCING
    return _TerrainInstanceBufferStore[Index & 0xFFFFU].LodInfo.x;
#else
    return LodInfo;
#endif
}

float4 GetHeightmapData(uint2 Location, uint Mip, uint Index)
{
#if defined(TERRAIN_USE_INSTANCING) && !defined(CUSTOM_TERRAIN)
    return _TerrainHeightmapStore.Load(uint4(Location, Index >> 16U, Mip));
#else
    return Heightmap.Load(uint3(Location, Mip));
#endif
}

float4 DecodeHeightmap(float4 Value, uint SurfaceType)
{
    Value.xy *= 255.f;
    Value.zw *= 2.f;
    Value.zw -= 1.f;
    float Height = (Value.y * 256.f + Value.x);
    Height = Height == 0.f ? 0.f / 0.f : Height - 32768.f;
    if (SurfaceType != SURFACE_WGS84)
    {
        Height *= TERRAIN_Y_SCALE;
    }
    return float4(Value.z, sqrt(1.f - saturate(dot(Value.zw, Value.zw))), Value.w, Height);
}

void GetWorldTBN(float3 P, float3 N, out float4 Tangent, out float4 Bitangent, out float4 Normal)
{
    Normal = float4(normalize(P), 0.f);
    Tangent = float4(normalize(cross(Normal, float3(0.f, 1.f, 0.f))), 0.f);
    Bitangent = float4(normalize(cross(Tangent, Normal)), 0.f);

#ifdef TERRAIN_USE_WORLD_NORMAL
    Normal = float4(N * float3(1.f, sign(P.y), 1.f), 0.f);
#else
    Normal = Tangent * N.x + Normal * N.y + Bitangent * N.z;
#endif
}

float2 CalculateLodTransition(float2 PositionXZ, uint LodInfo, uint PatchSize)
{
    int HalfPatchSize = PatchSize >> 1U;
    int2 Coord = PositionXZ;
    int2 Neighbor = HalfPatchSize < PositionXZ;
    int2 Index = (Neighbor.yx << 1) + int2(0, 1);
    int2 CoordOffset = HalfPatchSize - abs(Coord - HalfPatchSize);
    int2 LoDOffset = (LodInfo >> 8 * Index) & 0xFF;
    int2 Mask = (1U << max(LoDOffset - CoordOffset.yx, 0)) - 1;
    return Coord & ~Mask;
}

TerrainVSToPS GetTerrainVSToPS(float3 Position, TerrainData Input, uint SurfaceType)
{
    TerrainVSToPS Output = (TerrainVSToPS)0;

#ifndef CUSTOM_TERRAIN
    Position.xz = CalculateLodTransition(Position.xz, Input.LodInfo, Input.PatchSize);
#endif

#ifndef CUSTOM_TERRAIN
    Output.Position = float4(mad(Input.PatchOffsetAndScale.w, Position, Input.PatchOffsetAndScale.xyz), 1.f);
#else
    float2 LocalPosition = Position.xz * Input.PatchOffsetAndScale.zw + Input.PatchOffsetAndScale.xy;
    Output.Position = float4(LocalPosition.x, Position.y, LocalPosition.y, 1.f);
#endif

#ifdef TERRAIN_OCEAN
    // test begin: replace this with actual ocean code
    //const float K = 2.f, L = 1.3f;
    //float OceanY = Input.WorldScale.y * (sin(K * Output.Position.x + ce_Time) + sin(L * Output.Position.z + ce_Time));
    //float DX = Input.WorldScale.y * K * cos(K * Output.Position.x + ce_Time);
    //float DZ = Input.WorldScale.y * L * cos(L * Output.Position.z + ce_Time);
    //float3 OceanT = float3(1.f, -DX, 0.f);
    //float3 OceanB = float3(0.f, DZ, 1.f);
    // test end
    //float4 NormalAndHeight = float4(normalize(cross(OceanB, OceanT)), OceanY);
    //float4 NormalAndHeight = float4(0.f, 1.f, 0.f, 0.f);
#endif
    float4 NormalAndHeight = DecodeHeightmap(GetHeightmapData(Position.xz, 0U, Input.SlotIndex), SurfaceType);
    NormalAndHeight.w *= Input.WorldScale.y;
    
#ifdef CUSTOM_TERRAIN
    // const float2 Blocks = float2(18.f, 16.f);
    // float2 TexCoord = Position.xz / (float2(Input.PatchSize & 0xFFFFU, Input.PatchSize >> 16U) - 1.f);
    // TexCoord = TexCoord * float2(1.f, -1.f) + float2(0.f, 1.f);
    // TexCoord *= Blocks;
    // uint BlockIndex = trunc(TexCoord.y) * Blocks.x + trunc(TexCoord.x);
    // const uint UDim = 10;
    // Output.TexCoord.x = BlockIndex % UDim + frac(TexCoord.x);
    // Output.TexCoord.y = BlockIndex / UDim + 1.f - frac(TexCoord.y);
    Output.TexCoord = (Position.xz + .5f) / (float2(Input.PatchSize & 0xFFFFU, Input.PatchSize >> 16U) - 1.f);
#elif defined(TERRAIN_OCEAN)
    Output.TexCoord = float2((Output.Position.x + .5f) * Input.GridDim.z, (Output.Position.z + .5f) * Input.GridDim.w);
#else
    //float HalfPatchSize = Input.PatchSize * .5f;
    //float2 Distance = Position.xz - HalfPatchSize;
    //Output.TexCoord = (Position.xz + .5f * (1.f + step(HalfPatchSize, abs(Distance)) * sign(Distance))) / (Input.PatchSize + 1.f);
    Output.TexCoord = Position.xz / Input.PatchSize;
#endif

    if (SurfaceType == SURFACE_FLAT)
    {
        Output.Position.xyz = mad(Output.Position.xyz, Input.WorldScale.xyz, Input.WorldTranslation.xyz);
        Output.Position.y = NormalAndHeight.w;
        Output.Normal.xyz = NormalAndHeight.xyz;
    }

    if (SurfaceType == SURFACE_SPHERICAL)
    {
        float Phi = M_PI * (1.f - Output.Position.z * Input.GridDim.w);
        float Theta = 2.f * M_PI * Output.Position.x * Input.GridDim.z;
        float Radius = Input.WorldScale.x + NormalAndHeight.w;
        Output.Position.xyz = float3(sin(Phi) * cos(Theta), cos(Phi), sin(Phi) * sin(Theta)) * Radius;
        Output.Position.xyz += Input.WorldTranslation.xyz;
        Output.Position.w = 1.f;
        Output.Normal = float4(NormalAndHeight.xyz * float3(1.f, sign(.5f * M_PI - Phi), 1.f), 0.f);
    }

    if (SurfaceType == SURFACE_WGS84)
    {
        const float A = 1.f;
        const float B = .9966471893f;
        const float B2 = B * B;
        const float E2 = 1.f - B2;

        float Longitude = M_PI * (2.f * Output.Position.x * Input.GridDim.z - 1.f);
        float Latitude = .5f * M_PI * (2.f * Output.Position.z * Input.GridDim.w - 1.f);
        float Radius = Input.WorldScale.x;
        float SinLat = sin(Latitude);
        float CosLat = cos(Latitude);

        float N = Radius * rsqrt(1.f - E2 * SinLat * SinLat);
        float Height = NormalAndHeight.w;
        float NH = N + Height;

        Output.Position.xyz = float3(NH * CosLat * sin(Longitude), (B2 * N + Height) * SinLat, -NH * CosLat * cos(Longitude));

#ifdef TERRAIN_OCEAN
        // float3 ON = normalize(Output.Position.xyz); // not true normal
        // float3 OB = normalize(cross(float3(0.f, 1.f, 0.f), ON));
        // float3 OT = normalize(cross(ON, OB));
        // float3x3 TBN = transpose(float3x3(OB, ON, OT));
        // Output.Normal = float4(mul(TBN, NormalAndHeight.xyz), 0.f);
        // Output.Normal = float4(ON, 0.f);
#endif
        GetWorldTBN(Output.Position.xyz, NormalAndHeight.xyz, Output.Tangent, Output.Bitangent, Output.Normal);
#ifdef TERRAIN_OCEAN
        Output.Normal.xyz = normalize(Output.Position.xyz);
#endif
        Output.Position.xyz += Input.WorldTranslation.xyz;
        Output.Position.w = 1.f;
    }

    return Output;
}

VSOutput VSInputToVSOutput(VSInput Input)
{
    VSOutput Output = (VSOutput)0;

#if 1 /*TERRAIN_USE_INSTANCING*/
#ifdef CE_INSTANCING
    uint instanceIndex = Input.instanceIDOffset + Input.instanceID;
    uint objectIndex = _ObjectIndexBuffer[instanceIndex];
    ObjectSceneData objectSceneData = ce_PerObject[objectIndex];
    uint SlotIndex = objectSceneData.SlotIndex;
#else
    uint SlotIndex = Input.slotIndex;
#endif
#else
    uint SlotIndex = 0U;
#endif

    TerrainData Data;
    Data.GridDim = GridDim;
    Data.WorldTranslation = WorldTranslation;
    Data.WorldScale = WorldScale;
#ifdef CE_INSTANCING
    Data.PatchOffsetAndScale = objectSceneData.PatchOffsetAndScale;
#else
    Data.PatchOffsetAndScale = GetPatchOffsetAndScale(SlotIndex);
#endif
    Data.SlotIndex = SlotIndex;
#ifdef CE_INSTANCING
    Data.LodInfo = objectSceneData.LodInfo;
#else
    Data.LodInfo = GetLodInfo(SlotIndex);
#endif    
    Data.PatchSize = PatchSize;

    TerrainVSToPS TerrainOutput = GetTerrainVSToPS(Input.position, Data, SURFACE_TYPE);

    float4 prevPosition = TerrainOutput.Position;
#ifdef CE_USE_DOUBLE_TRANSFORM
    TerrainOutput.Position.xyz = GetLargeCoordinateReltvPosition(TerrainOutput.Position.xyz, float3(0.0, 0.0, 0.0), ce_CameraTilePosition); 
    prevPosition.xyz = GetLargeCoordinateReltvPosition(prevPosition.xyz, float3(0.0, 0.0, 0.0), ce_PrevCameraTilePosition); 
#endif

    Output.positionWS = TerrainOutput.Position.xyz;
    Output.normalWS = TerrainOutput.Normal;
    Output.tangentWS = TerrainOutput.Tangent;
    Output.binormalWS = TerrainOutput.Bitangent;
    Output.uv = TerrainOutput.TexCoord;

#ifdef TERRAIN_USE_INSTANCING
    Output.slotIndex = SlotIndex;
#endif

	float3 reservedPositionWS = Output.positionWS;
    Output = ExecuteVertexOut(Output, Input);
	Output.positionWS = GetWorldPositionOffset(reservedPositionWS, Output, Input);
	VSOutput reservedVSOut = Output;
	reservedVSOut.positionWS = reservedPositionWS;

	#ifdef WORLD_POSITION_OFFSET_PREVIOUS
		prevPosition.xyz = GetPreviousWorldPositionOffset(prevPosition.xyz, reservedVSOut, Input);
	#else
		prevPosition.xyz = GetWorldPositionOffset(prevPosition.xyz, reservedVSOut, Input);
	#endif

	matrix ce_ProjectionNoJitter=ce_Projection;
	ce_ProjectionNoJitter[0][2]=ce_ProjMatrixJitter[0];
	ce_ProjectionNoJitter[1][2]=ce_ProjMatrixJitter[1];

	Output.prePositionNDC=mul(ce_PreProjMatrix, mul(ce_PreViewMatrix, prevPosition));
    Output.nowPositionNDC=mul(ce_ProjectionNoJitter, mul(ce_View, float4(Output.positionWS.xyz, 1.0f)));
	Output.nowPositionNDC.z = distance(Output.positionWS.xyz, prevPosition.xyz);

    Output.positionNDC = mul(ce_Projection, mul(ce_View, float4(Output.positionWS, 1.0)));

    return Output;
}

PSInput VSOutputToPSInput(VSOutput Input)
{
    PSInput Output = (PSInput)0;

    Output.normalWS = Input.normalWS;
    Output.texCoord = Input.uv;
    Output.screenUV = Input.positionNDC.xy * ce_ScreenParams.zw;
    Output.uv = Input.uv;
    Output.positionWS = Input.positionWS;
    Output.positionNDC = Input.positionNDC;
    Output.uv = Input.uv;
    Output.binormalWS = Input.binormalWS;
    Output.tangentWS = Input.tangentWS;
	Output.prePositionNDC=Input.prePositionNDC;
    Output.nowPositionNDC=Input.nowPositionNDC;
#ifdef TERRAIN_USE_INSTANCING
    Output.slotIndex = Input.slotIndex;
#endif
    return Output;
}

#endif
