#pragma compute SpatialFilteringCS
#pragma compute TemporalFilteringCS
//#pragma enable debug_symbol

#include "FinalGatherCommon.hlsl"
#include "ScreenTextures.hlsl"
#include "SHCommon.hlsl"
SHADER_CONST(bool, PROBE_TEMPORAL_FILTER_WITH_HIT_DISTANCE, false);

RWTexture2D<float4> RWScreenProbeRadiance;
Texture2D<float4> ScreenProbeRadiance;

cbuffer _cbCommon
{
	// Temporal	
	float4 HistoryScreenPositionScaleBias;
	float4 HistoryUVMinMax;
	float ProbeTemporalFilterHistoryWeight;
	float HistoryDistanceThreshold;
	float PrevInvPreExposure;

	// Spatial
	float SpatialFilterMaxRadianceHitAngle;
	float SpatialFilterPositionWeightScale;
	int SpatialFilterHalfKernelSize;
}

Texture2D<uint> HistoryScreenProbeSceneDepth;
Texture2D<float4> HistoryScreenProbeRadiance;
Texture2D<float4> HistoryScreenProbeTranslatedWorldPosition;

Texture2D<float> HistoryScreenProbeHitDistance;
Texture2D<float> ScreenProbeHitDistance;

[numthreads(PROBE_THREADGROUP_SIZE_2D, PROBE_THREADGROUP_SIZE_2D, 1)]
void TemporalFilteringCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint2 ScreenProbeAtlasCoord = DispatchThreadId.xy / _ScreenProbeGatherOctahedronResolution;
	uint2 ProbeTexelCoord = DispatchThreadId.xy - ScreenProbeAtlasCoord * _ScreenProbeGatherOctahedronResolution;
	uint ScreenProbeIndex = ScreenProbeAtlasCoord.y * _ScreenProbeAtlasViewSize.x + ScreenProbeAtlasCoord.x;

	uint2 ScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
	uint2 ScreenTileCoord = GetScreenTileCoord(ScreenProbeScreenPosition);

	if (ScreenProbeIndex < GetNumScreenProbes() && ScreenProbeAtlasCoord.x < _ScreenProbeAtlasViewSize.x)
	{
		float SceneDepth = GetScreenProbeDepth(ScreenProbeAtlasCoord);

		if (SceneDepth > 0.0f)
		{
			float2 ScreenUV = GetScreenUVFromScreenProbePosition(ScreenProbeScreenPosition);
			float3 WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, SceneDepth);
			float3 SceneNormal = GetScreenProbeNormal(ScreenProbeAtlasCoord);
			float4 ScenePlane = float4(SceneNormal, dot(WorldPosition, SceneNormal));

			float HitDistance = DecodeProbeHitDistanceForFiltering(ScreenProbeHitDistance.Load(int3(DispatchThreadId.xy, 0)).x);
			float ScreenProbeTracesMoving = GetScreenProbeMoving(ScreenProbeAtlasCoord);

			float2 ProbeTexelCenter = float2(0.5, 0.5);
			float2 ProbeUV = (ProbeTexelCoord + ProbeTexelCenter) / (float)_ScreenProbeGatherOctahedronResolution;
			float3 WorldConeDirection = OctahedronToUnitVector(ProbeUV * 2.0 - 1.0);

			float2 ScreenPosition = (ScreenUV - _View_ScreenPositionScaleBias.wz) / _View_ScreenPositionScaleBias.xy;
			float3 HistoryScreenPosition = GetHistoryScreenPosition(ScreenPosition, ScreenUV, ConvertToDeviceZ(SceneDepth));
			float2 HistoryScreenUV = HistoryScreenPosition.xy * HistoryScreenPositionScaleBias.xy + HistoryScreenPositionScaleBias.wz;
			bool bHistoryWasOnscreen = all(HistoryScreenUV < HistoryUVMinMax.zw) && all(HistoryScreenUV > HistoryUVMinMax.xy);

			float3 TotalHistoryRadiance = 0;
			float TotalHistoryWeight = 0.0f;

			uint HistoryTemporalIndex = 0;
			// disable jitter, cost from 4 jittered samples to 9 uniform samples
			// HistoryTemporalIndex = (FixedJitterIndex < 0 ? ((int)View.StateFrameIndexMod8) % 8 : FixedJitterIndex);
			float2 UnclampedHistoryScreenProbeCoord = GetScreenTileCoordFromScreenUV(HistoryScreenUV, HistoryTemporalIndex /*SCREEN_TEMPORAL_INDEX*/);

			if(bHistoryWasOnscreen)
			{
				for (float Y = -1; Y <= 1; Y++)
				for (float X = -1; X <= 1; X++)
				{
					uint2 NeighborHistoryScreenProbeCoord = UnclampedHistoryScreenProbeCoord + float2(X, Y);
					uint2 ClampedHistoryScreenProbeCoord = clamp(UnclampedHistoryScreenProbeCoord + float2(X, Y), float2(0, 0), _ScreenProbeViewSize-float2(1, 1));

					float NeighborHistoryDepth = GetScreenProbeDepth(NeighborHistoryScreenProbeCoord, HistoryScreenProbeSceneDepth);

					if (NeighborHistoryDepth > 0.0f && !any(ClampedHistoryScreenProbeCoord-NeighborHistoryScreenProbeCoord))
					{
						float3 NeighborWorldPosition = HistoryScreenProbeTranslatedWorldPosition[NeighborHistoryScreenProbeCoord].rgb - LWCHackToFloat(_View_PrevPreViewTranslation);

						float PlaneDistance = abs(dot(float4(NeighborWorldPosition, -1), ScenePlane));
						float RelativeDepthDifference = PlaneDistance / SceneDepth;

						float PositionWeight = exp2(-100.0f * abs(RelativeDepthDifference));
						PositionWeight = PlaneDistance < HistoryDistanceThreshold ? PositionWeight : 0.0f;

						uint2 HistoryRadianceCoord = NeighborHistoryScreenProbeCoord * _ScreenProbeGatherOctahedronResolution + ProbeTexelCoord;

						float AngleWeight = 0.0f;
						if(PROBE_TEMPORAL_FILTER_WITH_HIT_DISTANCE)
						{
							float HistoryRadianceDepth = DecodeProbeHitDistanceForFiltering(HistoryScreenProbeHitDistance.Load(int3(HistoryRadianceCoord, 0)).x);
							if (HistoryRadianceDepth >= 0)
							{
								// Increase spatial filtering when temporal filter will be reduced
								if (ScreenProbeTracesMoving <= .01f)
								{
									// Clamp neighbor's hit distance to our own.  This helps preserve contact shadows, as a long neighbor hit distance will cause a small NeighborAngle and bias toward distant lighting.
									if (HitDistance >= 0)
									{
										HistoryRadianceDepth = min(HistoryRadianceDepth, HitDistance);
									}
									float3 NeighborHitPosition = NeighborWorldPosition + WorldConeDirection * HistoryRadianceDepth;
									float3 ToNeighborHit = NeighborHitPosition - WorldPosition;
									float NeighborAngle = acosFast(dot(ToNeighborHit, WorldConeDirection) / length(ToNeighborHit));
									AngleWeight = 1.0f - saturate(NeighborAngle / SpatialFilterMaxRadianceHitAngle);
								}
							}
						}
						else
						{
							AngleWeight = 1.0f;
						}

						float Weight = PositionWeight * AngleWeight * saturate(bHistoryWasOnscreen ? 1.0f : 0.0f);

						//@todo spatial weight
						//@todo - need smarter angle weighting as spatial filter
						TotalHistoryRadiance += HistoryScreenProbeRadiance.Load(int3(HistoryRadianceCoord, 0)).xyz * (PrevInvPreExposure * _View_PreExposure * Weight);
						TotalHistoryWeight += Weight;
					}
				}
			}

			if (TotalHistoryWeight > 0.0f)
			{
				TotalHistoryRadiance /= TotalHistoryWeight;
			}

			float3 NewRadiance = ScreenProbeRadiance.Load(int3(DispatchThreadId.xy, 0)).xyz;

			float3 UpdatedRadiance = lerp(NewRadiance, TotalHistoryRadiance, TotalHistoryWeight > 0.0f ? ProbeTemporalFilterHistoryWeight : 0.0f);

			RWScreenProbeRadiance[DispatchThreadId.xy] = float4(UpdatedRadiance, 1.0);
		}
		else
		{
			RWScreenProbeRadiance[DispatchThreadId.xy] = 0.0f;
		}
	}
}

float GetFilterPositionWeight(float ProbeDepth, float SceneDepth)
{
	float DepthDifference = abs(ProbeDepth - SceneDepth);
	float RelativeDepthDifference = DepthDifference / SceneDepth;
	return ProbeDepth >= 0 ? exp2(-SpatialFilterPositionWeightScale * (RelativeDepthDifference * RelativeDepthDifference)) : 0;
}

void GatherNeighborRadiance(
	int2 NeighborScreenTileCoord,
	uint2 ProbeTexelCoord,
	float3 WorldPosition,
	float3 WorldConeDirection,
	float SceneDepth, 
	float HitDistance, 
	float ScreenProbeTracesMoving,
	inout float3 TotalRadiance, 
	inout float TotalWeight)
{
    if (all(and(NeighborScreenTileCoord >= 0, NeighborScreenTileCoord < (int2)_ScreenProbeViewSize)))
    {
        uint2 NeighborScreenProbeAtlasCoord = NeighborScreenTileCoord;
        uint2 NeighborScreenProbeScreenPosition = GetUniformScreenProbeScreenPosition(NeighborScreenProbeAtlasCoord);
        float NeighborSceneDepth = GetScreenProbeDepth(NeighborScreenProbeAtlasCoord);
        float PositionWeight = GetFilterPositionWeight(NeighborSceneDepth, SceneDepth);

#define FILTER_SEARCH_ADAPTIVE_PROBES 0
#if FILTER_SEARCH_ADAPTIVE_PROBES
		if (PositionWeight <= 0.0f)
		{
			uint NumAdaptiveProbes = ScreenTileAdaptiveProbeHeader[NeighborScreenTileCoord];

			for (uint AdaptiveProbeListIndex = 0; AdaptiveProbeListIndex < NumAdaptiveProbes; AdaptiveProbeListIndex++)
			{
				uint2 AdaptiveProbeCoord = GetAdaptiveProbeCoord(NeighborScreenTileCoord, AdaptiveProbeListIndex);
				uint AdaptiveProbeIndex = ScreenTileAdaptiveProbeIndices[AdaptiveProbeCoord];
				uint ScreenProbeIndex = AdaptiveProbeIndex + NumUniformScreenProbes;

				uint2 NewNeighborScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
				uint2 NewNeighborScreenProbeAtlasCoord = uint2(ScreenProbeIndex % ScreenProbeAtlasViewSize.x, ScreenProbeIndex / ScreenProbeAtlasViewSize.x);
				float NewNeighborSceneDepth = GetScreenProbeDepth(NewNeighborScreenProbeAtlasCoord);
				float NewPositionWeight = GetFilterPositionWeight(NewNeighborSceneDepth, SceneDepth);

				if (NewPositionWeight > PositionWeight)
				{
					PositionWeight = NewPositionWeight;
					NeighborScreenProbeAtlasCoord = NewNeighborScreenProbeAtlasCoord;
					NeighborScreenProbeScreenPosition = NewNeighborScreenProbeScreenPosition;
					NeighborSceneDepth = NewNeighborSceneDepth;
				}
			}
		}
#endif
		
        if (PositionWeight > 0.0f)
        {
            uint2 NeighborTraceCoord = NeighborScreenProbeAtlasCoord * _ScreenProbeGatherOctahedronResolution + ProbeTexelCoord;
            float NeighborRadianceDepth = DecodeProbeHitDistanceForFiltering(ScreenProbeHitDistance.Load(int3(NeighborTraceCoord, 0)).x);

            if (NeighborRadianceDepth >= 0)
            {
                float AngleWeight = 1.0f;

				// Increase spatial filtering when temporal filter will be reduced
                if (ScreenProbeTracesMoving <= .01f)
                {
					// Clamp neighbor's hit distance to our own.  This helps preserve contact shadows, as a long neighbor hit distance will cause a small NeighborAngle and bias toward distant lighting.
                    if (HitDistance >= 0)
                    {
                        NeighborRadianceDepth = min(NeighborRadianceDepth, HitDistance);
                    }
                    float2 NeighborScreenUV = (NeighborScreenProbeScreenPosition + .5f) * _View_BufferSizeAndInvSize.zw;
                    float3 NeighborWorldPosition = GetWorldPositionFromScreenUV(NeighborScreenUV, NeighborSceneDepth);
                    float3 NeighborHitPosition = NeighborWorldPosition + WorldConeDirection * NeighborRadianceDepth;
                    float3 ToNeighborHit = NeighborHitPosition - WorldPosition;
                    float NeighborAngle = acosFast(dot(ToNeighborHit, WorldConeDirection) / length(ToNeighborHit));
                    AngleWeight = 1.0f - saturate(NeighborAngle / SpatialFilterMaxRadianceHitAngle);
                }

                float Weight = PositionWeight * AngleWeight;
                TotalRadiance += ScreenProbeRadiance.Load(int3(NeighborTraceCoord, 0)).xyz * Weight;
                TotalWeight += Weight;
            }
        }
    }
}

[numthreads(PROBE_THREADGROUP_SIZE_2D, PROBE_THREADGROUP_SIZE_2D, 1)]
void SpatialFilteringCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint2 ScreenProbeAtlasCoord = DispatchThreadId.xy / _ScreenProbeGatherOctahedronResolution;
	uint2 ProbeTexelCoord = DispatchThreadId.xy - ScreenProbeAtlasCoord * _ScreenProbeGatherOctahedronResolution;
	uint ScreenProbeIndex = ScreenProbeAtlasCoord.y * _ScreenProbeAtlasViewSize.x + ScreenProbeAtlasCoord.x;

	uint2 ScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
	uint2 ScreenTileCoord = GetScreenTileCoord(ScreenProbeScreenPosition);

	if (ScreenProbeIndex < GetNumScreenProbes() && ScreenProbeAtlasCoord.x < _ScreenProbeAtlasViewSize.x)
	{
		float SceneDepth = GetScreenProbeDepth(ScreenProbeAtlasCoord);

		if (SceneDepth > 0.0f)
		{
			float ScreenProbeTracesMoving = GetScreenProbeMoving(ScreenProbeAtlasCoord);

			float2 ScreenUV = GetScreenUVFromScreenProbePosition(ScreenProbeScreenPosition);
			float3 WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, SceneDepth);

			float2 ProbeTexelCenter = float2(0.5, 0.5);
			float2 ProbeUV = (ProbeTexelCoord + ProbeTexelCenter) / (float)_ScreenProbeGatherOctahedronResolution;
			float3 WorldConeDirection = OctahedronToUnitVector(ProbeUV * 2.0 - 1.0);
			float HitDistance = DecodeProbeHitDistanceForFiltering(ScreenProbeHitDistance.Load(int3(DispatchThreadId.xy, 0)).x);
			float3 TotalRadiance = 0;
			float TotalWeight = 0;

			{
				TotalRadiance = ScreenProbeRadiance.Load(int3(DispatchThreadId.xy, 0)).xyz;
				TotalWeight = 1.0f;
			}

			int2 Offsets[4];
			Offsets[0] = int2(-1, 0);
			Offsets[1] = int2(1, 0);
			Offsets[2] = int2(0, -1);
			Offsets[3] = int2(0, 1);

			LOOP
			for (uint OffsetIndex = 0; OffsetIndex < 4; OffsetIndex++)
			{
				GatherNeighborRadiance(ScreenTileCoord + Offsets[OffsetIndex], ProbeTexelCoord, WorldPosition, WorldConeDirection, SceneDepth, HitDistance, ScreenProbeTracesMoving, TotalRadiance, TotalWeight);
			}

			// Increase spatial filtering when temporal filter will be reduced
			if (ScreenProbeTracesMoving > .01f)
			{
				int2 Offsets[8];
				Offsets[0] = int2(-2, 0);
				Offsets[1] = int2(2, 0);
				Offsets[2] = int2(0, -2);
				Offsets[3] = int2(0, 2);
				Offsets[4] = int2(-1, 1);
				Offsets[5] = int2(1, 1);
				Offsets[6] = int2(-1, -1);
				Offsets[7] = int2(1, -1);

				LOOP
				for (uint OffsetIndex = 0; OffsetIndex < 8; OffsetIndex++)
				{
					GatherNeighborRadiance(ScreenTileCoord + Offsets[OffsetIndex], ProbeTexelCoord, WorldPosition, WorldConeDirection, SceneDepth, HitDistance, ScreenProbeTracesMoving, TotalRadiance, TotalWeight);
				}
			}

			if (TotalWeight > 0)
			{
				TotalRadiance /= TotalWeight;
			}

			RWScreenProbeRadiance[DispatchThreadId.xy] = float4(TotalRadiance, 1.0);
		}
		else
		{
			RWScreenProbeRadiance[DispatchThreadId.xy] = 0.0;
		}
	}
}