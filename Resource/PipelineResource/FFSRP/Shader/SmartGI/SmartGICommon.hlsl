#ifndef SMARTGI_SMARTGICOMMON_HLSL
#define SMARTGI_SMARTGICOMMON_HLSL
#include "../ShaderLibrary/Common.hlsl"

#ifndef SNORM
#if COMPILER_HLSLCC || PS4_PROFILE
	#define SNORM 
	#define UNORM
#else
	#define SNORM snorm
	#define UNORM unorm
#endif
#endif

#ifndef FLATTEN
#define FLATTEN [flatten]
#endif

#ifndef LOOP
#define LOOP [loop]
#endif

#ifndef UNROLL
#define UNROLL [unroll]
#endif

#ifndef UNROLL_N
#define UNROLL_N(N) [unroll(N)]
#endif

#ifndef BRANCH
#define BRANCH [branch]
#endif

//const static float PI = M_PI;

#define PROBE_THREADGROUP_SIZE_2D 8 
#define PROBE_THREADGROUP_SIZE_1D 64

#define LWCHackToFloat(A) A

// if input A is float4x4, what should we return?
#define LWCToFloat3x3(A) (float3x3)A

#define MUL_UE(vec, mat) mul(mat, vec)

float3 LWCMultiplyVector(float3 Vector, matrix InMatrix)
{
    return mul(Vector, (float3x3) InMatrix);
}

struct FScreenProbeGBuffer
{
	float3 WorldNormal;
	float SceneDepth; // Clip Space
	float Roughness;
	bool bLit;
};

#endif