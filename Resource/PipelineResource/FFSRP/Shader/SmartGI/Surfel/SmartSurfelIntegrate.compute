#pragma compute SurfelIntegrate

#ifndef LIGHTING_ONLY_MODE
#define LIGHTING_ONLY_MODE 0
#endif


#define VF_SUPPORTS_PRIMITIVE_SCENE_DATA 1
#include "../ScreenTextures.hlsl"

#define OCTAHEDRAL_COMMON 1
#include "../SmartGICommon.hlsl"
#include "SmartSurfelCommon.hlsl"
#include "../../ShaderLibrary/CommonStruct.hlsl"

#include "../Common.hlsl"


static const float WEIGHT_EPSILON = 0.0001f;

ByteAddressBuffer SurfelStatsBuffer;
StructuredBuffer<FSurfelGridCell> SurfelGridBuffer;
StructuredBuffer<uint> SurfelCellBuffer;
StructuredBuffer<uint> SurfelAliveBuffer;
StructuredBuffer<FSurfelRayDataPacked> SurfelRayBuffer;
StructuredBuffer<FSurfel> SurfelBuffer;

//Texture2D<float2> SurfelMomentsTexturePrev;
//Texture2D<float> SurfelRayGuideTexturePrev;
//SamplerState SurfelMomentsTexturePrevSampler;

RWStructuredBuffer<FSurfelData> SurfelDataBuffer;
RWStructuredBuffer<FSurfelEstimatorData> SurfelEstimatorDataBuffer;
//RWTexture2D<float2> SurfelMomentsTexture;
//RWTexture2D<float> SurfelRayGuideTexture;

static const uint THREADCOUNT = 4;
static const uint CACHE_SIZE = THREADCOUNT * THREADCOUNT;
groupshared FSurfelRayData RayDataSharedCache[CACHE_SIZE];
groupshared float4 IrradianceSharedCache[CACHE_SIZE];

/**
Module:
	Lighting Injection - Pass2_2


Description:
	Gather IndirectLighting by result of many rays and Gather DirectLighting.

Input:
	ByteAddressBuffer SurfelStatsBuffer;
	StructuredBuffer<FSurfelGridCell> SurfelGridBuffer;
	StructuredBuffer<uint> SurfelCellBuffer;
	StructuredBuffer<uint> SurfelAliveBuffer;
	StructuredBuffer<FSurfelRayDataPacked> SurfelRayBuffer;
	StructuredBuffer<FSurfel> SurfelBuffer;
	Texture2D<float4> SceneColor;
	groupshared SurfelRayData ray_cache[CACHE_SIZE];
	groupshared float4 result_cache[CACHE_SIZE];

Output:
	RWStructuredBuffer<FSurfelData> SurfelDataBuffer;
	RWStructuredBuffer<FSurfelEstimatorData> SurfelEstimatorDataBuffer;
*/

void AccmulateRaysRadianceAndComputeAverageRaysDepth(uint NumRays, float3 SurfelNormal, float SurfelRadius, inout float4 IndirectLighting, inout float2 ResultDepth, inout float TotalWeight)
{
	for (uint Index = 0; Index < NumRays; ++Index)
	{
		FSurfelRayData Ray = RayDataSharedCache[Index];
		float MaxLighting = max(max(Ray.radiance.x, Ray.radiance.y), Ray.radiance.z);
		IndirectLighting += float4(Ray.radiance * saturate(dot(SurfelNormal, Ray.direction)), 1);	// accumulate ray radiance

		/* ----moment texture---- */
		/*
		float Depth;
		if (Ray.depth > 0)														// 
			Depth = clamp(Ray.depth, 0, SurfelRadius);							// if ray hit something, clamp to surfel radius
		else
			Depth = SurfelRadius;												// if ray is infinity, set to surfel max radius
			
		float Weight = max(0.01, dot(TexelDirection, Ray.direction));			// if ray direction, match octahedron dir, then the ray depth contribute 100% to moment texture
		Weight = pow(Weight, 32);												// sharpen the fall off
		if (Weight > WEIGHT_EPSILON)											// weight large enough
		{
			ResultDepth += float2(Depth, Square(Depth)) * Weight;				// add depth
			TotalWeight += Weight;												// save total weight
		}
		*/
	}
}

/*
void UpdateMomentTextureByLerpWithAverageDepth(uint2 MomentsTopLeft, float TotalWeight, float2 MomentTexelCoord, float Life, inout float2 ResultDepth)
{
	if (TotalWeight > WEIGHT_EPSILON && MomentTexelCoord.x < SMART_SURFEL_MOMENT_RESOLUTION && MomentTexelCoord.y < SMART_SURFEL_MOMENT_RESOLUTION)
	{
		ResultDepth /= TotalWeight;
		uint2 MomentsCoord = MomentsTopLeft + 1 + MomentTexelCoord;
		if (Life > 0)
		{
			const float2 PrevMoment = SurfelMomentsTexturePrev[MomentsCoord];
			ResultDepth = lerp(PrevMoment, ResultDepth, 0.02);
		}
		RWSurfelMomentsTexture[MomentsCoord] = ResultDepth;
	}
}
*/

void ComputeNeighbourSurfelIrradianceContribution(float3 SurfelWorldPosition, float3 SurfelNormal, float SurfelRadius, uint MomentTexelIndex, inout float4 IndirectLighting)
{
	uint CellIndex = SurfelCellIndex(SurfelCell(SurfelWorldPosition));
	FSurfelGridCell Cell = SurfelGridBuffer[CellIndex];

	for (uint i = 0; i < Cell.GetCount(); i += THREADCOUNT * THREADCOUNT)
	{
		uint AdjSurfelIndex = SurfelCellBuffer[Cell.GetOffSet() + i];
		FSurfel AdjSurfel = SurfelBuffer[AdjSurfelIndex];
		
		const float SumRadius = AdjSurfel.GetRadius() + SurfelRadius;

		float3 L = SurfelWorldPosition - AdjSurfel.GetWorldPosition();
		float Dist2 = dot(L, L);
		
		if (Dist2 < Square(SumRadius))
		{
			float3 AdjSurfelNormal = AdjSurfel.GetWorldNormal();
			float dotN = dot(SurfelNormal, AdjSurfelNormal);
			if (dotN > 0)
			{
				float Dist = sqrt(Dist2);
				float Contribution = 1;
				/////////////////////////////////////////
				// !! contribution: angle between surrounding normal and center surfel normal, cos(theta)
				Contribution *= saturate(dotN);
				/////////////////////////////////////////
				// !! contribution: distance between  surrounding normal and center surfel normal, r
				Contribution *= saturate(1 - Dist / SumRadius);
				/////////////////////////////////////////
				// !! smooth:
				Contribution = smoothstep(0, 1, Contribution);
				/////////////////////////////////////////
				// !! contribution: if surrounding surfel is in shadow 0
				/* ----moment texture---- */
				//float2 Moments = SurfelMomentsTexturePrev.SampleLevel(LinearSampler, SurfelMomentUV(AdjSurfelIndex, AdjSurfelNormal, L / Dist), 0);
				//Contribution *= SurfelMomentWeight(Moments, Dist);

				float3 AdjSurfelIndirectLighting = SurfelEstimatorDataBuffer[AdjSurfelIndex].GetMean();
				IndirectLighting += float4(AdjSurfelIndirectLighting, 1) * Contribution;
			}
		}
	}
	IrradianceSharedCache[MomentTexelIndex] = IndirectLighting;
}

void SumUpNeighbourSurfelIrradianceContribution(inout float4 IndirectLighting)
{
	IndirectLighting = 0;
	for (uint c = 0; c < CACHE_SIZE; ++c)
	{
		IndirectLighting += IrradianceSharedCache[c];
	}
}

void UpdateSurfelData(float3 SurfelWorldPosition, float3 SurfelNormal, uint SurfelObjCullingGUID, inout FSurfelData SurfelData)
{
	//SurfelWorldPosition += 10 * surfel_worldposition_offsets[View.StateFrameIndex % 27];
    float3 camWorldPos = GetLargeCoordinateAbsolutePosition(ce_CameraPos, ce_CameraTilePosition);
    float3 TranslatedWorldPosition = SurfelWorldPosition - LWCHackToFloat(camWorldPos);
    float4 ClipPosition = mul(float4(TranslatedWorldPosition, 1), mul(ce_View, ce_Projection));
	float3 ScreenPosition = float3(ClipPosition.xyz / ClipPosition.w);
    float2 ScreenUV = ScreenPosition.xy * _View_ScreenPositionScaleBias.xy + _View_ScreenPositionScaleBias.wz;

	// Update Local Normal & Albedo & Emissive when Surfel in screen
	if (all(and(ScreenUV > 0, ScreenUV < 1)))
	{
        float DeviceZ = _DepthMap.SampleLevel(clampSampler, ScreenUV, 0);

		float SceneDepth = ConvertFromDeviceZ(DeviceZ, ce_Projection);
		float SurfelSceneDepth = ConvertFromDeviceZ(ScreenPosition.z, ce_Projection);

		if(abs(SurfelSceneDepth - SceneDepth) < 0.0001f * max(min(SceneDepth, SurfelSceneDepth), 0.0001f))
		{
			//uint2 PixelCoord = ScreenUV * _View_BufferSizeAndInvSize.xy + _View_RectMin.xy + .5f;
            FGBufferData GBufferData = GetGBufferData(ScreenUV);
			const float3 SceneNormal = GBufferData.WorldNormal;
            const float3 diffuseColor = GBufferData.DiffuseColor;
			//@TODO: GBufferD no longer has emissive
			//float3 Emissive = GetGBufferEmissive(GBufferData);
			float3 Emissive = float3(0, 0, 0);

            uint ObjectCullingGUID = GBufferData.ObjectCullingGUID;
            ObjectCullingData objCullingData = _GPUSceneObjectCullingDatas[ObjectCullingGUID];
            float4x4 worldToLocal = MakeInverseMatrix(objCullingData.worldMatrix, 1.xxx);
			
			float3 NewNormal = normalize(lerp(SceneNormal, SurfelNormal, 0.95f));
            float3 LocalNormal = LWCMultiplyVector(NewNormal, worldToLocal);

			SurfelData.SetLocalNormal(LocalNormal);
		// BaseColor is (1,1,1) in GBuffer when LightingOnly Mode, it's not correct.
		#if (LIGHTING_ONLY_MODE == 0)
			SurfelData.SetDiffuseColor(lerp(diffuseColor, SurfelData.GetDiffuseColor(), 0.95f));
		#endif
			SurfelData.SetEmissive(Emissive);
		}
	}
}

[numthreads(THREADCOUNT, THREADCOUNT, 1)]
void SurfelIntegrate(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID,
	uint GroupThreadIndex : SV_GroupIndex)
{
	uint SurfelCount = SurfelStatsBuffer.Load(SMART_SURFEL_STATS_OFFSET_COUNT);
	if (GroupId.x >= SurfelCount)
		return;

	//////////////////////////////////////////////////////////////////
	// !! Cache Surfel Information
	uint SurfelIndex = SurfelAliveBuffer[GroupId.x];
	uint2 MomentTexelCoord = GroupThreadId.xy;
	uint MomentTexelIndex = GroupThreadIndex;
	FSurfel Surfel = SurfelBuffer[SurfelIndex];
	FSurfelData SurfelData = SurfelDataBuffer[SurfelIndex];
	FSurfelEstimatorData SurfelEstimatorData = SurfelEstimatorDataBuffer[SurfelIndex];
	float SurfelRadius = Surfel.GetRadius();
	float3 SurfelNormal = Surfel.GetWorldNormal();
	float3 SurfelWorldPosition = Surfel.GetWorldPosition();
	uint SurfelObjCullingGUID = Surfel.GetObjCullingGUID();

	//////////////////////////////////////////////////////////////////
	// !! The Texel Direction that we are processing
	//float3 TexelDirection = HemiOctahedronToUnitVector(((MomentTexelCoord + 0.5) / (float2) SMART_SURFEL_MOMENT_RESOLUTION) * 2 - 1);
	//TexelDirection = mul(TexelDirection, GetTangentBasis(SurfelNormal));
	//TexelDirection = normalize(TexelDirection);

	//////////////////////////////////////////////////////////////////
	// !! Process All Ray of this surfel
	float4 IndirectLighting = 0;
	float2 ResultDepth = 0;
	float TotalWeight = 0;
	uint SurfelRayCount = Surfel.GetRayCount();
	uint RayIndexOffset = Surfel.GetRayOffset();

	// Gather IndirectLighting (multi-bounce Irradiance) by Rays' Result
#if SURFEL_MULTIBOUNCE_ENABLE
	// IMPROVEMENT: Integrate Rays in batch way
	while (SurfelRayCount > 0)
	{
		/////////////////////////////////////////////////////////////////
		// !! Each Thread Fill Ray to Cache and 
		uint NumRays = min(CACHE_SIZE, SurfelRayCount);
		if (MomentTexelIndex < NumRays)
		{
			RayDataSharedCache[MomentTexelIndex] = SurfelRayBuffer[RayIndexOffset + MomentTexelIndex].load();
		}

		GroupMemoryBarrierWithGroupSync();

		//////////////////////////////////////////////////////////////////
		// !! Accmulate Rays Radiance And Compute Average Rays Depth For Current TexelDirection
		AccmulateRaysRadianceAndComputeAverageRaysDepth(NumRays, SurfelNormal, SurfelRadius, IndirectLighting, ResultDepth, TotalWeight);

		GroupMemoryBarrierWithGroupSync();

		//////////////////////////////////////////////////////////////////
		// !! Next batch
		SurfelRayCount -= NumRays;
		RayIndexOffset += NumRays;
	}

	//////////////////////////////////////////////////////////////////
	// !! Update Moment Texture By Lerp With Average Depth
	//uint2 MomentsTopLeft = unflatten2D(SurfelIndex, SQRT_SMART_SURFEL_CAPACITY) * SMART_SURFEL_MOMENT_TEXELS;
	//UpdateMomentTextureByLerpWithAverageDepth(MomentsTopLeft, TotalWeight, MomentTexelCoord, Life, ResultDepth);


#ifdef SMART_SURFEL_ENABLE_IRRADIANCE_SHARING
	////////////////////////////////////////////////////////////////// 
	// !! Compute Surfel Neighbour Irradiance Contribution
	// !! IMPROVEMENT: Irradiance Sharing
	ComputeNeighbourSurfelIrradianceContribution(SurfelWorldPosition, SurfelNormal, SurfelRadius, MomentTexelIndex, IndirectLighting);
#endif // SMART_SURFEL_ENABLE_IRRADIANCE_SHARING


	/*
	////////////////////////////////////////////////////////////////// 
	// !! Copy moment borders
	AllMemoryBarrierWithGroupSync();
	for (uint i = MomentTexelCoord.x; i < SMART_SURFEL_MOMENT_TEXELS; i += THREADCOUNT)
	{
		for (uint j = MomentTexelCoord.y; j < SMART_SURFEL_MOMENT_TEXELS; j += THREADCOUNT)
		{
			uint2 PixelWrite = MomentsTopLeft + uint2(i, j);
			uint2 PixelRead = clamp(PixelWrite, MomentsTopLeft + 1, MomentsTopLeft + 1 + SMART_SURFEL_MOMENT_RESOLUTION - 1);
			RWSurfelMomentsTexture[PixelWrite] = RWSurfelMomentsTexture[PixelRead];
		}
	}

	////////////////////////////////////////////////////////////////// 
	// !! Ray Guide Texture
	// for (uint i = GTid.x; i < SURFEL_MOMENT_TEXELS; i += THREADCOUNT)
	{
		// for (uint j = GTid.y; j < SURFEL_MOMENT_TEXELS; j += THREADCOUNT)
		if(all(GTid.xy < uint2(SURFEL_MOMENT_RESOLUTION,SURFEL_MOMENT_RESOLUTION)))
		{
			uint2 pixel_write = moments_topleft + GTid.xy + uint2(1,1);
			// surfelRayGuideTexture[pixel_write] = texel_direction;
			float last_result_radiance = surfelRayGuideTexturePrev[pixel_write];
			float target_result_radiance = lerp(last_result_radiance, Luminance(result_radiance), 0.02);
			surfelRayGuideTexture[pixel_write] = target_result_radiance;
		}
	}
	*/

	if (MomentTexelIndex > 0)
		return;

#ifdef SMART_SURFEL_ENABLE_IRRADIANCE_SHARING
	//////////////////////////////////////////////////////////////////
	// !! Sum Up Surfel Neighbour Surfel Irradiance Contribution
	// !! IMPROVEMENT: Irradiance Sharing
	SumUpNeighbourSurfelIrradianceContribution(IndirectLighting);
#endif // SMART_SURFEL_ENABLE_IRRADIANCE_SHARING

	//////////////////////////////////////////////////////////////////
	// !! Final Result Variance
	// !! IMPROVEMENT: Multiscale Mean Estimator
	if (IndirectLighting.a > 0)
	{
		IndirectLighting.rgb /= IndirectLighting.a;
		MultiscaleMeanEstimator(IndirectLighting.rgb, SurfelEstimatorData, 0.08f);
	}
#else // SURFEL_MULTIBOUNCE_ENABLE
	SurfelEstimatorData.SetMean(0);
#endif // SURFEL_MULTIBOUNCE_ENABLE

	//////////////////////////////////////////////////////////////////
	// !! Update SurfelData for Local Normal & Albedo & Emissive
#define SURFEL_UPDATE_NORMAL_ALBEDO_EMISSIVE 1
#if SURFEL_UPDATE_NORMAL_ALBEDO_EMISSIVE
    UpdateSurfelData(SurfelWorldPosition, SurfelNormal, SurfelObjCullingGUID, SurfelData);
#endif

	//////////////////////////////////////////////////////////////////
	// !! Gather DirectLighting, may use VSM/CSM
	float3 DirectLighting = 0;
    // TODO(chopperlin) to be completed
	// DirectLighting = GatherDirectLighting(SurfelWorldPosition, SurfelNormal, DispatchThreadId);
	
	float3 HistoryDirectLighting = SurfelData.GetDirectLighting();
	SurfelData.SetDirectLighting(lerp(HistoryDirectLighting, DirectLighting, 0.95f));	// temporal blend

	//////////////////////////////////////////////////////////////////
	// !! Update Data: Output SurfelEsimatorData and SurfelData
	SurfelEstimatorDataBuffer[SurfelIndex] = SurfelEstimatorData;
	SurfelDataBuffer[SurfelIndex] = SurfelData;
}