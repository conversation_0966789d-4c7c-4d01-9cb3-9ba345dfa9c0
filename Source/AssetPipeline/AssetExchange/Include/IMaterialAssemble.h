#pragma once

#include <cstdint>
#include <type_traits>
#include <functional>
#include "AssetExchangeDefinitions.h"
#include "ITextureAssemble.h"

namespace CEAssetExchange {

constexpr auto CDefaultMaterial = "EngineResource/Material/DefaultMaterial.nda";

constexpr auto CDefaultTexture = "EngineResource/Texture/DefaultTexture.nda";

struct IMaterialAssemble
{
    virtual void SaveToFile() = 0;

    virtual void SetParent(const char* matParent) = 0;

    virtual void AddBool(const char* Name, const bool data) = 0;

    virtual void AddScalar(const char* uniformName, const float data) = 0;

    virtual void AddVector(const char* uniformName, const float data[4]) = 0;

    virtual void AddVectorDouble(const char* uniformName, const double data[4]) = 0;

    virtual void AddTexture(const char* uniformName, const char* textureRP) = 0;

    virtual bool HasBeenDestroyed() = 0;

    virtual void EndAssemble() = 0;

    virtual bool HasEndAssemble() = 0;
};
struct IFxAssemble
{
    virtual void SaveToFile() = 0;

    virtual bool HasBeenDestroyed() = 0;

    virtual void EndAssemble() = 0;

    virtual bool HasEndAssemble() = 0;

    virtual const char* GetCompileErrorMessage() = 0;
    enum class Type
    {
        Unknown,
        Fx,
        Material,
        MaterialFunction
    };
    virtual void SetupFromJsonString(const char* jsonStr, Type type) = 0;

    virtual void AddDependencies(const char* textureRP) = 0;
};
}   // namespace CEAssetExchange
