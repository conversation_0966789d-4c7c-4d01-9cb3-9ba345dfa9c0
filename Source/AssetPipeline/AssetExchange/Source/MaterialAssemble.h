#pragma once

#include <string>
#include <memory>
#include <set>
#include <map>
#include "Object.h"
#include "IMaterialAssemble.h"
#include "material_instance_editor.h"
#include "CrossBase/Math/CrossMath.h"
#include "crude/runtime/material/material_editor.h"

namespace CEAssetExchange {

class MaterialEditorCallbackForAssemble : public cross::MaterialEditorCallback
{
public:
    std::string mErrorMessage;

    void AddWarningMessage(const char* msg) override
    {
        mErrorMessage.append(msg);
    }
};

class FxAssemble : virtual public IFxAssemble, virtual public Object
{
public:
    explicit FxAssemble(std::filesystem::path& relative, std::filesystem::path& staging, std::string& guid);

    virtual void Finish() override { SaveToFile(); }

    virtual bool HasBeenDestroyed() override
    {
        assert(0);   // this api for proxy only
        return false;
    }

    virtual void EndAssemble() override { mEndAssembled = true; }

    virtual bool HasEndAssemble() override { return mEndAssembled; }

    virtual void SaveToFile() override;

    virtual void SetupFromJsonString(const char* jsonStr, Type type) override;

    virtual const char* GetCompileErrorMessage() override;
    virtual void AddDependencies(const char* textureRP) override;

private:
    Type mType = Type::Unknown;

    MaterialEditorCallbackForAssemble mCallback{};

    std::string mJsonStr;
    std::set<std::string> mDependencies;
};
}   // namespace CEAssetExchange
