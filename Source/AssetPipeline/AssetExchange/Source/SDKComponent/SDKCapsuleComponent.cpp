#include "SDKComponent/SDKCapsuleComponent.h"

#include "GameFramework/Components/CapsuleComponent.h"
#include <string>

namespace cesdk::cegf {
SDKCapsuleComponent::SDKCapsuleComponent(::cegf::GameObjectComponent* CapsuleComponentInstance)
    : SDKPhysicsComponent(CapsuleComponentInstance)
{
    // auto comp = static_cast<::cegf::CapsuleComponent*>(componentInstance);
    // comp->Init();
}

void SDKCapsuleComponent::SetCapsuleSize(float radius, float halfHeight)
{
    auto comp = static_cast<::cegf::CapsuleComponent*>(componentInstance);
    comp->SetCapsuleSize(radius, halfHeight);
}
void SDKCapsuleComponent::SetOffset(float x, float y, float z)
{
    auto comp = static_cast<::cegf::CapsuleComponent*>(componentInstance);
    comp->SetOffset({x, y, z});
}
}   // namespace cesdk::cegf
