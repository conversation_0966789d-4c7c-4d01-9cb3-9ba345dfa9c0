#include "AssetPipeline/AssetExchange/Include/SDKComponent/SDKPhysicsComponent.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CrossPhysics/PhysicsEngine/PhysicsCooker.h"
#include "GameFramework/Components/PhysicsComponent.h"

namespace cesdk { 
	namespace cegf {
		SDKPhysicsComponent::SDKPhysicsComponent(::cegf::GameObjectComponent* physicsComponentInstance): 
			SDKGameObjectComponent(physicsComponentInstance){
		}

		void SDKPhysicsComponent::SetUseMeshCollision(bool useMeshCollision) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetUseMeshCollision(useMeshCollision);
            }
		}
        void SDKPhysicsComponent::SetCollisionType(CollisionType type)
        {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetCollisionType(static_cast<::cross::CollisionType>(type));
            }
		}
        void SDKPhysicsComponent::SetCollisionMask(uint16_t mask)
        {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetCollisionMask(::cross::CollisionMask(mask));
            }
        }

        void SDKPhysicsComponent::SetIsDynamic(bool isDynamic) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetIsDynamic(isDynamic);
            }
        }

        void SDKPhysicsComponent::SetIsKinematic(bool isKinematic) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetIsKinematic(isKinematic);
            }
        }

        void SDKPhysicsComponent::SetIsTrigger(bool isTrigger) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                physicsComp->SetIsTrigger(isTrigger);
            }
        }
        void SDKPhysicsComponent::AddBoxCollision(const CEAssetExchange::BoxCollision* data, const std::uintmax_t size) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                for (auto i = 0; i < size; ++i)
                {
                    ::cross::PhysicsGeometryBox box(::cross::Float3(data[i].center),::cross::Quaternion(data[i].rotation),::cross::Float3(data[i].extent));
                    physicsComp->AddExtraBoxShape(box);
                }
            }
        }

        void SDKPhysicsComponent::AddSphereCollision(const CEAssetExchange::SphereCollision* data, const std::uintmax_t size) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                for (auto i = 0; i < size; ++i)
                {
                    ::cross::PhysicsGeometrySphere sphere(::cross::Float3(data[i].center), data[i].radius);
                    physicsComp->AddExtraSphereShape(sphere);
                }
            }
        }

        void SDKPhysicsComponent::AddCapsuleCollision(const CEAssetExchange::CapsuleCollision* data, const std::uintmax_t size) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                for (auto i = 0; i < size; ++i)
                {
                    ::cross::PhysicsGeometryCapsule capsule(::cross::Float3(data[i].center), ::cross::Quaternion(data[i].rotation), data[i].radius, data[i].halfHeight);
                    physicsComp->AddExtraCapsuleShape(capsule);
                }
            }
        }
        void SDKPhysicsComponent::AddConvexCollision(const CEAssetExchange::ConvexCollision* data) {
            auto* physicsComp = static_cast<::cegf::PhysicsComponent*>(componentInstance);
            if (physicsComp)
            {
                ::cross::PhysicsCooker* cooker = ::cross::EngineGlobal::GetPhysicsEngine()->GetCooker();
                std::shared_ptr<::cross::PhysicsConvexMesh> convexMesh = cooker->BuildConvexMesh(data->vertexData, data->vertexSize, data->vertexStride, data->indexData, data->indexSize, data->indexStride);
                ::cross::Float3 position(data->center);
                ::cross::Quaternion rotation(data->rotation);
                physicsComp->AddExtraConvexShape({position, rotation, convexMesh});
            }
        }
}}