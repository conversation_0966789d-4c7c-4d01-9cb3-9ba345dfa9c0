#include "SDKComponent/SDKTerrainComponent.h"
#include "GameFramework/Components/TerrainComponent.h"

namespace cesdk::cegf {

    static ::cegf::TerrainComponent* GetTerrainComponent(const SDKTerrainComponent* component)
    {
        if (!component || !component->componentInstance)
        {
            return nullptr;
        }

        return static_cast<::cegf::TerrainComponent*>(component->componentInstance);
    }

    void SDKTerrainComponent::SetTerrainPath(const char* terrainPath)
    {
        ::cegf::TerrainComponent* terrainComponent = GetTerrainComponent(this);
        if (terrainComponent)
        {
            terrainComponent->SetTerrainPath(terrainPath);
        }
    }

} // namespace cesdk::cegf
