#include <fstream>
#include "WorldAssemble.h"

#include "CrossBase/Serialization/ResourceMetaHeader.h"
#include "Resource/TerrainResource.h"
#include "Runtime/GameWorld/SkyAtmosphereSystemG.h"
#include "PostProcessVolumeSetting.h"

namespace CEAssetExchange {

extern std::string TryGetGUID(const std::string& normPath, bool fallbackToRelativePath);
extern std::string TryGetRefference(const std::string& normPath);

Entity::Entity(const char* n)
    : mName{n}
{
    mEuid = cross::CrossUUID::GenerateCrossUUID();
}

const char* _GetEntityUUID(IEntity* entity)
{
    if (!entity)
        return "";

    static std::string retain = "";
    retain = reinterpret_cast<Entity*>(entity)->mEuid.ToString();
    return retain.c_str();
}

const char* WorldAssemble::GetEntityUUID(IEntity* entity)
{
    return _GetEntityUUID(entity);
}

cross::SerializeNode serializeRenderProperty(bool castShadow)
{
    using namespace cross::WorldSerializeConst;
    auto hash = cross::HashString("cross::RenderPropertyComponentG");
    cross::SerializeNode node;
    {
        node[ComponentHash] = hash.GetHash32();
        node["mCullingProperty"] = 1;
        node["RenderEffect"] = {cross::_N("RuntimeEffectMask", castShadow ? 0x80000 : 0)};
    }
    return node;
}

cross::SerializeNode LocalComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    auto& local = *this;
    cross::SerializeNode ComponentN;
    ComponentN[ComponentHash] = TransformCmpntHashStr.GetHash32();

    //// hierarchy
    // cross::SerializeNode hierarchyN;
    //{
    //    hierarchyN[Parent] = local.mParent ? local.mParent->mEuid.ToString() : CVoidEuid;
    //    hierarchyN[FirstChild] = local.mChildren.size() ? local.mChildren.back()->mEuid.ToString() : CVoidEuid;
    //    hierarchyN[NextSibling] = local.mNextSibling ? local.mNextSibling->mEuid.ToString() : CVoidEuid;
    //    hierarchyN[PrevSibling] = local.mSibling ? local.mSibling->mEuid.ToString() : CVoidEuid;
    //    hierarchyN["mDepth"] = local.mDepth;
    //}
    // ComponentN[cross::WorldSerializeConst::Hierarchy] = std::move(hierarchyN);

    // TRS
    cross::SerializeNode TRS_N;
    {
        auto& t = local.mTransform.mTranslation;
        TRS_N["mTranslation"] = {cross::_N("x", t[0]), cross::_N("y", t[1]), cross::_N("z", t[2])};

        auto& q = local.mTransform.mQuarternion;
        TRS_N["mRotation"] = {cross::_N("x", q[0]), cross::_N("y", q[1]), cross::_N("z", q[2]), cross::_N("w", q[3])};

        auto& s = local.mTransform.mScale;
        TRS_N["mScale"] = {cross::_N("x", s[0]), cross::_N("y", s[1]), cross::_N("z", s[2])};

        TRS_N["mTRSFlag"] = 3;
        TRS_N["mUnesedPadding"] = 0;
        TRS_N["mVersion"] = 0;
    }
    ComponentN[TRS] = std::move(TRS_N);

    return ComponentN;
}

cross::SerializeNode ModelComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::ModelComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        cross::SerializeNode modelsN;
        {
            for (size_t i = 0; i < mModels.size(); i++)
            {
                auto& model = mModels[i];
                auto& subModels = mModels[i].mSubModels;
                cross::SerializeNode modelN;
                modelN["mVisible"] = true;

                std::string Asset = model.mData.mAsset_A ? AssembleProxy::GetAssetReference(model.mData.mAsset_A) : model.mAsset;
                modelN["mAsset"] = TryGetRefference(Asset);
                mDependencies.emplace(modelN["mAsset"].AsString());

                if (!subModels.empty())
                    modelN["mSubModelProperties"] = cross::SerializeNode{};

                for (size_t j = 0; j < subModels.size(); j++)
                {
                    auto& subModel = subModels[j];
                    auto& lodMaterials = subModels[j].mLODMaterials;
                    cross::SerializeNode subModelN;
                    subModelN["mVisible"] = true;

                    std::string material = CDefaultMaterial;
                    if (subModel.mData.mMaterial_A)
                    {
                        material = AssembleProxy::GetAssetReference(subModel.mData.mMaterial_A);
                    }
                    else if (!subModel.mMaterial.empty())
                    {
                        material = subModel.mMaterial;
                    }
                    subModelN["mMaterial"] = TryGetRefference(material);
                    mDependencies.emplace(subModelN["mMaterial"].AsString());

                    if (!lodMaterials.empty())
                        subModelN["mLODMaterials"] = cross::SerializeNode{};

                    for (size_t k = 0; k < lodMaterials.size(); k++)
                    {
                        std::string key = std::to_string(k + 1);
                        subModelN["mLODMaterials"][key] = TryGetRefference(lodMaterials[k]);
                        mDependencies.emplace(subModelN["mLODMaterials"][key].AsString());
                    }

                    modelN["mSubModelProperties"].PushBack(std::move(subModelN));
                }

                modelsN.PushBack(std::move(modelN));
            }
        }
        ComponentN["mModels"] = std::move(modelsN);
    }
    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

cross::SerializeNode LightComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    if (mData.mType == LightType::SkyLight)
    {
        auto hash = cross::HashString("cross::SkyLightComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();
        ComponentN["LightColor"] = {cross::_N("x", mData.mColor[0]), cross::_N("y", mData.mColor[1]), cross::_N("z", mData.mColor[2])};
        ComponentN["SkyLightIntensity"] = mData.mIntensity;
    }
    else
    {
        auto hash = cross::HashString("cross::LightComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        // light data
        cross::SerializeNode lightN;
        {
            lightN["mType"] = mData.mType;
            lightN["mColor"] = {cross::_N("x", mData.mColor[0]), cross::_N("y", mData.mColor[1]), cross::_N("z", mData.mColor[2])};
            lightN["mIntensity"] = mData.mIntensity;
            lightN["mPrtIntensity"] = mData.mPrtIntensity;
            lightN["mVolumetricFactor"] = mData.mVolumetricFactor;
            lightN["mRange"] = mData.mRange;
            lightN["mInnerConeAngle"] = mData.mInnerConeAngle;
            lightN["mOuterConeAngle"] = mData.mOuterConeAngle;
            lightN["mVersion"] = 1;
            lightN["mCastShadow"] = mData.mCastShadow;
            lightN["mCastScreenSpaceShadow"] = mData.mCastScreenSpaceShadow;
            lightN["mSourceWidth"] = mData.mSourceWidth;
            lightN["mSourceHeight"] = mData.mSourceHeight;
            lightN["mBarnDoorAngle"] = mData.mBarnDoorAngle;
            lightN["mBarnDoorLength"] = mData.mBarnDoorLength;
        }
        ComponentN["mLightData"] = std::move(lightN);
        ComponentN["mEnable"] = mData.mEnable;
        ComponentN["mMode"] = mData.mMode;
        ComponentN["mPriority"] = mData.mPriority;
        ComponentN["mMask"] = mData.mMask;
        ComponentN["mShadowType"] = static_cast<UInt8>(mData.mShadowType);
        ComponentN["mDepthBias"] = mData.mDepthBias;
        ComponentN["mNormalBias"] = mData.mNormalBias;
    }
    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

cross::SerializeNode FoliageComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::FoliageComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        ComponentN["mPrimaryMeshAsset"] = TryGetRefference(mInstancedFoliage.mPrimaryMeshAsset);
        ComponentN["mPrimaryMaterial"] = TryGetRefference(mInstancedFoliage.mPrimaryMaterial);

        mDependencies.insert(ComponentN["mPrimaryMeshAsset"].AsString());
        mDependencies.insert(ComponentN["mPrimaryMaterial"].AsString());

        cross::SerializeNode Lod;
        {
            for (auto& LodSection : mInstancedFoliage.mLoDSections)
            {
                cross::SerializeNode LodSection_N;
                LodSection_N["mDefaultMaterial"] = TryGetRefference(LodSection.mDefaultMaterial);
                mDependencies.insert(LodSection_N["mDefaultMaterial"].AsString());

                cross::SerializeNode SubSectionMaterials_N;
                for (auto& m : LodSection.mSubSectionMaterials)
                {
                    std::string mRef = TryGetRefference(m);
                    SubSectionMaterials_N.PushBack(mRef);
                    mDependencies.insert(mRef);
                }
                LodSection_N["mSubSectionMaterials"] = std::move(SubSectionMaterials_N);
                Lod.PushBack(std::move(LodSection_N));
            }
        }
        ComponentN["mLoDSections"] = std::move(Lod);

        cross::SerializeNode Instance;
        {
            for (auto& pose : mInstancedFoliage.mInstanceData)
            {
                cross::SerializeNode TRS_N;
                {
                    auto& t = pose.mTranslation;
                    TRS_N["mTranslation"] = {cross::_N("x", t[0]), cross::_N("y", t[1]), cross::_N("z", t[2])};

                    auto& q = pose.mQuarternion;
                    TRS_N["mRotation"] = {cross::_N("x", q[0]), cross::_N("y", q[1]), cross::_N("z", q[2]), cross::_N("w", q[3])};

                    auto& s = pose.mScale;
                    TRS_N["mScale"] = {cross::_N("x", s[0]), cross::_N("y", s[1]), cross::_N("z", s[2])};
                }
                cross::SerializeNode Trans;
                Trans["mTransform"] = std::move(TRS_N);
                Instance.PushBack(std::move(Trans));
            }
        }
        ComponentN["mInstanceData"] = std::move(Instance);
    }
    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

cross::SerializeNode Entity::serialize(Entity* root)
{
    using namespace cross::WorldSerializeConst;

    cross::SerializeNode entityN;
    {
        if (mPrefab)
        {
            entityN[PrefabID] = mPrefab->mPrefabId;
            entityN[PrefabEUID] = mPrefab->GetRoot()->mEuid.ToString();
            mDependencies.emplace(mPrefab->GetRefference());
        }
        else
        {
            entityN[PrefabID] = "";
            entityN[PrefabEUID] = "";
        }
        entityN[Euid] = mEuid.ToString();
        entityN[Name] = mName;
        entityN[Hide] = mHide;
        entityN[PrototypeHash] = mType;

        cross::SerializeNode componentsN;
        {
            // local
            componentsN[TransformComponent] = mLocal.serialize();
            mDependencies.merge(std::set<std::string>(mLocal.mDependencies));

            // world
            auto worldHash = cross::HashString("cross::WorldTransformComponentG");
            componentsN["cross::WorldTransformComponentG"] = {cross::_N(ComponentHash, worldHash.GetHash32())};

            // model
            if (mType == EntityType::Model)
            {
                componentsN["cross::AABBComponentG"] = {cross::_N(ComponentHash, cross::HashString("cross::AABBComponentG").GetHash32())};
                componentsN["cross::ModelComponentG"] = std::move(mModel.serialize());
                componentsN["cross::RenderPropertyComponentG"] = std::move(serializeRenderProperty(mModel.mCastShadow));
                mDependencies.merge(std::set<std::string>(mModel.mDependencies));
            }

            // light
            if (mType == EntityType::Light)
            {
                componentsN["cross::LightComponentG"] = std::move(mLight.serialize());
                mDependencies.merge(std::set<std::string>(mLight.mDependencies));
            }

            // Foliage
            if (mType == EntityType::Foliage)
            {
                componentsN["cross::AABBComponentG"] = {cross::_N(ComponentHash, cross::HashString("cross::AABBComponentG").GetHash32())};
                componentsN["cross::FoliageComponentG"] = std::move(mFoliage.serialize());
                componentsN["cross::RenderPropertyComponentG"] = std::move(serializeRenderProperty(mFoliage.mCastShadow));
                mDependencies.merge(std::set<std::string>(mFoliage.mDependencies));
            }

            // Camera
            if (mType == EntityType::Camera)
            {
                componentsN["cross::CameraComponentG"] = std::move(mCamera.serialize());
                componentsN["cross::ScriptComponentG"] = std::move(mCamera.serializeController());

                if (mCamera.mHasControllabeUnit)   // extra controllable unit
                {
                    componentsN["cross::ControllableUnitComponentG"] = std::move(mControllabeUnit.serialize());
                    mDependencies.merge(std::set<std::string>(mControllabeUnit.mDependencies));
                }

                mDependencies.merge(std::set<std::string>(mCamera.mDependencies));
            }

            // Controllable Unit
            if (mType == EntityType::ControllableUnit)
            {
                componentsN["cross::ControllableUnitComponentG"] = std::move(mControllabeUnit.serialize());
                mDependencies.merge(std::set<std::string>(mControllabeUnit.mDependencies));
            }

            // SkyLight
            if (mType == EntityType::SkyLight)
            {
                componentsN["cross::SkyLightComponentG"] = std::move(mLight.serialize());
                mDependencies.merge(std::set<std::string>(mLight.mDependencies));
            }

            // SkyAtmosphere
            if (mType == EntityType::SkyAtmosphere)
            {
                componentsN["cross::SkyAtmosphereComponentG"] = std::move(mSkyAtmosphere.serialize());
                mDependencies.merge(std::set<std::string>(mSkyAtmosphere.mDependencies));
            }

            // PostProcessVolume
            if (mType == EntityType::PostProcessVolume)
            {
                componentsN["cross::PostProcessVolumeComponentG"] = std::move(mPostProcessVolume.serialize());
                mDependencies.merge(std::set<std::string>(mPostProcessVolume.mDependencies));
            }

            // Terrain
            if (mType == EntityType::Terrain)
            {
                componentsN["cross::TerrainComponentG"] = std::move(mTerrain.serialize());
                componentsN["cross::RenderPropertyComponentG"] = std::move(serializeRenderProperty(true));
                mDependencies.merge(std::set<std::string>(mTerrain.mDependencies));
            }
        }
        entityN[ComponentList] = std::move(componentsN);

        cross::SerializeNode childrenN;
        {
            for (auto& child : mLocal.mChildren)
            {
                childrenN.PushBack(child->mEuid.ToString());
            }
        }
        entityN[Children] = std::move(childrenN);

        if (root)
            entityN[RootParent] = root->mEuid.ToString();
    }
    assert(mDependencies.find("") == mDependencies.end());
    return entityN;
};

WorldAssemble::WorldAssemble(std::filesystem::path& relative, std::filesystem::path& staging, std::string& guid)
    : Object(relative, staging, guid)
{
    auto root = std::make_unique<Entity>("Root");
    mRoot = root.get();
    mEntities[root.get()] = std::move(root);
    mPrefabId = mReference;
}

void WorldAssemble::SaveToFile()
{
    if (mSaved)
        return;

    if (mFile.empty())
        return;

    if (!mOverwrite && std::filesystem::exists(mFile))
    {
        mSaved = true;
        return;
    }

    std::filesystem::create_directories(mFile.parent_path());

    if (!mSettings.mAsPrefab)
    {
        // world
        {
            cross::SerializeNode worldN;
            {
                worldN["mMapSize"]["x"] = mSettings.mMapSize[0];
                worldN["mMapSize"]["y"] = mSettings.mMapSize[1];
                worldN["mBlockSize"]["x"] = mSettings.mBlockSize[0];
                worldN["mBlockSize"]["y"] = mSettings.mBlockSize[1];
                worldN["mBlockSize"]["z"] = mSettings.mBlockSize[2];
                worldN["mOrigin"]["x"] = mSettings.mOrigin[0];
                worldN["mOrigin"]["y"] = mSettings.mOrigin[1];
                worldN["mOrigin"]["z"] = mSettings.mOrigin[2];
                worldN["mPolicy"] = mSettings.mPolicy;
            }
            std::string worldStr = worldN.FormatToJson();

            std::stringstream ss;
            ss.write(worldStr.c_str(), worldStr.length());

            std::filesystem::path filePath{mFile};
            filePath.replace_extension("world");

            std::string ndaStr = ss.str();
            std::ofstream ostrm(filePath, std::ios::out | std::ios::binary);
            ostrm.write(ndaStr.c_str(), ndaStr.length());
        }

        // block preload
        {
            std::set<std::string> dependencies;
            cross::SerializeNode blockN;
            {
                auto* root = GetRoot();
                auto&& ecsN = blockN["ecs"];
                ecsN["RootNode"] = {cross::_N("euid", root->mEuid.ToString())};
                ecsN["entities"] = {cross::_N(root->mEuid.ToString(), std::move(root->serialize(nullptr)))};

                dependencies.merge(std::set<std::string>(root->mDependencies));
            }
            const std::string blockStr = blockN.FormatToJson();

            cross::SerializeNode metaHeader;
            {
                metaHeader["Version"] = 5;

                std::filesystem::path reference{mReference};
                reference.replace_filename(reference.stem().string() + "_Preload.block");
                std::string GUID = TryGetGUID(reference.string(), false);
                if (GUID.empty())
                {
                    metaHeader["GuidL"] = 0;
                    metaHeader["GuidH"] = 0;
                }
                else
                {
                    metaHeader["Guid"] = GUID;
                }

                metaHeader["ClassID"] = ClassID(WorldBlock);
                metaHeader["DataSize"] = static_cast<SInt32>(blockStr.length());
                metaHeader["ContentType"] = static_cast<UInt32>(cross::CONTENT_TYPE::CONTENT_TYPE_JSON);

                cross::SerializeNode dependenciesNode;
                for (auto beg = dependencies.begin(); beg != dependencies.end(); beg++)
                    dependenciesNode.PushBack(beg->data());
                metaHeader["Dependency"] = std::move(dependenciesNode);
            }
            const std::string headerStr = metaHeader.FormatToJson();

            std::stringstream ss;
            UInt32 magicNum = ASSET_MAGIC_NUMBER_JMETA;
            ss.write(reinterpret_cast<char*>(&magicNum), sizeof(magicNum));
            ss << '\n';
            ss.write(headerStr.c_str(), headerStr.length());
            ss << '\n';
            ss.write(blockStr.c_str(), blockStr.length());

            std::filesystem::path filePath{mFile};
            std::cout << filePath.stem().string() << std::endl;
            filePath.replace_filename(filePath.stem().string() + "_Preload.block");

            std::string ndaStr = ss.str();
            std::ofstream ostrm(filePath, std::ios::out | std::ios::binary);
            ostrm.write(ndaStr.c_str(), ndaStr.length());
        }
    }

    SaveBlockToFile();

    mSaved = true;
}

void WorldAssemble::SaveBlockToFile()
{
    std::filesystem::create_directories(mFile.parent_path());

    std::filesystem::path filePath{mFile};

    std::set<std::string> dependencies;
    cross::SerializeNode blockN;
    std::string GUID = mGUID;

    if (mSettings.mAsPrefab)
    {
        auto* root = GetRoot();
        auto&& ecsN = blockN["ecs"];
        ecsN["RootNode"] = {cross::_N("euid", root->mEuid.ToString())};

        for (auto beg = mEntities.begin(); beg != mEntities.end(); beg++)
        {
            auto* entity = beg->first;
            ecsN["entities"][entity->mEuid.ToString()] = entity->serialize(entity != root ? root : nullptr);
            dependencies.merge(std::set<std::string>(entity->mDependencies));
        }

        filePath.replace_extension("prefab");
    }
    else
    {
        auto* root = GetRoot();
        auto&& ecsN = blockN["ecs"];
        // ecsN["RootNode"] = {cross::_N("euid", root->mEuid.ToString())};

        for (auto beg = mEntities.begin(); beg != mEntities.end(); beg++)
        {
            auto* entity = beg->first;
            if (entity != root)
            {
                ecsN["entities"][entity->mEuid.ToString()] = entity->serialize(root);
                dependencies.merge(std::set<std::string>(entity->mDependencies));
            }
        }

        filePath.replace_filename(filePath.stem().string() + "_L0_X0_Y0_Z0.block");

        std::filesystem::path reference{mReference};
        reference.replace_filename(reference.stem().string() + "_L0_X0_Y0_Z0.block");
        GUID = TryGetGUID(reference.string(), false);
    }

    const std::string blockStr = blockN.FormatToJson();
    cross::SerializeNode metaHeader;
    {
        metaHeader["Version"] = 5;

        if (GUID.empty())
        {
            if (mSettings.mAsPrefab)
            {
                metaHeader["Guid"] = GetRoot()->mEuid.ToString();
            }
            else
            {
                metaHeader["GuidL"] = 0;
                metaHeader["GuidH"] = 0;
            }
        }
        else
        {
            metaHeader["Guid"] = GUID;
        }

        metaHeader["ClassID"] = mSettings.mAsPrefab ? ClassID(PrefabResource) : ClassID(WorldBlock);
        metaHeader["DataSize"] = static_cast<SInt32>(blockStr.length());
        metaHeader["ContentType"] = static_cast<UInt32>(cross::CONTENT_TYPE::CONTENT_TYPE_JSON);

        cross::SerializeNode dependenciesNode;
        for (auto beg = dependencies.begin(); beg != dependencies.end(); beg++)
            dependenciesNode.PushBack(beg->data());
        metaHeader["Dependency"] = std::move(dependenciesNode);
    }
    const std::string headerStr = metaHeader.FormatToJson();

    std::stringstream ss;
    UInt32 magicNum = ASSET_MAGIC_NUMBER_JMETA;
    ss.write(reinterpret_cast<char*>(&magicNum), sizeof(magicNum));
    ss << '\n';
    ss.write(headerStr.c_str(), headerStr.length());
    ss << '\n';
    ss.write(blockStr.c_str(), blockStr.length());

    std::string ndaStr = ss.str();
    std::ofstream ostrm(filePath, std::ios::out | std::ios::binary);
    ostrm.write(ndaStr.c_str(), ndaStr.length());
}

void WorldAssemble::SetSettings(WorldSettings* settings)
{
    assert(!mEndAssembled);
    memcpy(&mSettings, settings, sizeof(WorldSettings));

    if (mSettings.mAutoChangeExtension)
    {
        mFile.replace_extension(mSettings.mAsPrefab ? "prefab" : "world");
        {
            std::filesystem::path reference{mReference};
            reference.replace_extension(mSettings.mAsPrefab ? "prefab" : "world");
            mReference = reference.string();
            mGUID = TryGetGUID(mReference, false);
        }
    }
}

IEntity* WorldAssemble::AddEntity(const char* name, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto entityAutoPtr = std::make_unique<Entity>(name);
    auto* entity = entityAutoPtr.get();

    Transform trans{};
    Entity* parent = GetRoot();

    if (hierarchy)
    {
        if (hierarchy->mTransform)
            memcpy(&trans, hierarchy->mTransform, sizeof(decltype(trans)));

        if (hierarchy->mParent)
            parent = static_cast<Entity*>(hierarchy->mParent);
    }

    // Hierarchy
    {
        auto getSlibing = [](std::vector<Entity*>& entities) -> Entity* { return entities.size() > 0 ? entities.back() : nullptr; };

        entity->mLocal.mParent = parent;

        auto prevSlibing = getSlibing(parent->mLocal.mChildren);
        entity->mLocal.mSibling = prevSlibing;

        if (prevSlibing)
            prevSlibing->mLocal.mNextSibling = entity;

        parent->mLocal.mChildren.emplace_back(entity);

        parent->mLocal.mChildren.front()->mLocal.mSibling = entity;
        entity->mLocal.mNextSibling = parent->mLocal.mChildren.front();

        entity->mLocal.mDepth = parent->mLocal.mDepth + 1;

        memcpy(&entity->mLocal.mTransform, &trans, sizeof(decltype(trans)));
    }

    mEntities[entity] = std::move(entityAutoPtr);
    return entity;
}

IEntity* WorldAssemble::AddModel(const char* name, ModelData* model, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::Model;

        if (!model || !model->mModelsNum)
            return entityI;

        entity->mModel.mCastShadow = model->mCastShadow;

        for (uint32_t i = 0; i < model->mModelsNum; i++)
        {
            auto& modelI = entity->mModel.mModels.emplace_back();
            memcpy(&modelI.mData, &model->mModels[i], sizeof(Model));
            modelI.mAsset = model->mModels[i].mAsset;

            const auto* subModels = model->mModels[i].mSubModels;
            const auto subModelsNum = model->mModels[i].mSubModelsNum;
            for (uint32_t j = 0; j < subModelsNum; j++)
            {
                auto& subModelI = modelI.mSubModels.emplace_back();
                memcpy(&subModelI.mData, &subModels[j], sizeof(SubModel));
                subModelI.mMaterial = subModels[j].mMaterial;

                const auto lodMatsNum = subModels[j].mLODMaterialsNum;
                if (subModels[j].mLODMaterials_A)
                {
                    auto* lodMats = subModels[j].mLODMaterials_A;
                    for (uint32_t k = 0; k < lodMatsNum; k++)
                    {
                        subModelI.mLODMaterials[k] = AssembleProxy::GetAssetReference(&lodMats[k]);
                    }
                }
                else if (subModels[j].mLODMaterials)
                {
                    auto* lodMats = subModels[j].mLODMaterials;
                    for (uint32_t k = 0; k < lodMatsNum; k++)
                    {
                        subModelI.mLODMaterials.emplace_back(std::string{lodMats[k]});
                    }
                }
            }
        }
    }
    return entityI;
}

IEntity* WorldAssemble::AddLight(const char* name, LightData* light, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = light->mType == LightType::SkyLight ? EntityType::SkyLight : EntityType::Light;

        memcpy(&entity->mLight.mData, light, sizeof(decltype(*light)));
    }
    return entityI;
}

IEntity* WorldAssemble::AddPrefab(const char* name, IWorldAssemble* prefab, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mPrefab = dynamic_cast<WorldAssemble*>(prefab);
    }
    return entityI;
}

IEntity* WorldAssemble::AddInstancedFoliage(const char* name, InstancedFoliage* foliage, Hierarchy* hierarchy)
{
    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::Foliage;

        if (!foliage || !foliage->mInstanceData)
            return entityI;

        entity->mFoliage.mCastShadow = foliage->mCastShadow;

        auto& foliageData = entity->mFoliage.mInstancedFoliage;

        foliageData.mPrimaryMeshAsset = foliage->mPrimaryMeshAsset_A ? AssembleProxy::GetAssetReference(foliage->mPrimaryMeshAsset_A) : foliage->mPrimaryMeshAsset;
        foliageData.mPrimaryMaterial = foliage->mPrimaryMaterial_A ? AssembleProxy::GetAssetReference(foliage->mPrimaryMaterial_A) : foliage->mPrimaryMaterial;

        foliageData.mInstanceData.resize(foliage->mInstanceData->mInstanceDataNum);
        for (uint32_t i = 0; i < foliage->mInstanceData->mInstanceDataNum; i++)
        {
            memcpy(&foliageData.mInstanceData[i], &foliage->mInstanceData->mInstanceData[i], sizeof(Transform));
        }

        foliageData.mLoDSections.resize(foliage->mLoDSectionsNum);
        for (uint32_t i = 0; i < foliage->mLoDSectionsNum; i++)
        {
            auto* lod = foliage->mLoDSections + i;
            foliageData.mLoDSections[i].mDefaultMaterial = lod->mDefaultMaterial_A ? AssembleProxy::GetAssetReference(lod->mDefaultMaterial_A) : lod->mDefaultMaterial;

            foliageData.mLoDSections[i].mSubSectionMaterials.resize(lod->mSubSectionMaterialsNum);

            if (lod->mSubSectionMaterials_A)
            {
                for (uint32_t j = 0; j < lod->mSubSectionMaterialsNum; j++)
                    foliageData.mLoDSections[i].mSubSectionMaterials[j] = AssembleProxy::GetAssetReference(lod->mSubSectionMaterials_A + j);
            }
            else
            {
                for (uint32_t j = 0; j < lod->mSubSectionMaterialsNum; j++)
                    foliageData.mLoDSections[i].mSubSectionMaterials[j] = std::string{lod->mSubSectionMaterials[j]};
            }
        }
    }
    return entityI;
}

IEntity* WorldAssemble::AddControllableUnit(const char* name, ControllableUnitInfo* cu, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::ControllableUnit;

        memcpy(&entity->mControllabeUnit.mControllableUnitInfo.mInfo, cu, sizeof(decltype(*cu)));
        entity->mControllabeUnit.mControllableUnitInfo.mCurveCtrResPath = cu->mCurveCtrResPath ? cu->mCurveCtrResPath : "";
    }
    return entityI;
}

IEntity* WorldAssemble::AddCamera(const char* name, CameraInfo* camera, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::Camera;

        memcpy(&entity->mCamera.mCamera, camera, sizeof(decltype(*camera)));

        if (camera->mControllableUnitInfo)
        {
            entity->mCamera.mHasControllabeUnit = true;

            memcpy(&entity->mControllabeUnit.mControllableUnitInfo.mInfo, camera->mControllableUnitInfo, sizeof(decltype(*(camera->mControllableUnitInfo))));
            entity->mControllabeUnit.mControllableUnitInfo.mCurveCtrResPath = camera->mControllableUnitInfo->mCurveCtrResPath ? camera->mControllableUnitInfo->mCurveCtrResPath : "";

            if (!entity->mControllabeUnit.mControllableUnitInfo.mInfo.mEntity)
                entity->mControllabeUnit.mControllableUnitInfo.mInfo.mEntity = entity;
        }
    }
    return entityI;
}

cross::SerializeNode CameraComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::CameraComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        cross::SerializeNode mCameraInfoN;
        {
            mCameraInfoN["mAspectRatio"] = mCamera.mAspectRatio;
            mCameraInfoN["mHorizontalFov"] = mCamera.mHorizontalFov;
            mCameraInfoN["mFov"] = mCamera.mFov;
            mCameraInfoN["mFarPlane"] = mCamera.mFarPlane;
            mCameraInfoN["mNearPlane"] = mCamera.mNearPlane;
            mCameraInfoN["mCurrentFocalLength"] = mCamera.mCurrentFocalLength;
            mCameraInfoN["mMinFocalLength"] = mCamera.mMinFocalLength;
            mCameraInfoN["mMaxFocalLength"] = mCamera.mMaxFocalLength;
            mCameraInfoN["mSensorWidth"] = mCamera.mSensorWidth;
            mCameraInfoN["mSensorHeight"] = mCamera.mSensorHeight;

            mCameraInfoN["mWidth"] = mCamera.mWidth;
            mCameraInfoN["mHeight"] = mCamera.mHeight;
            mCameraInfoN["mOrthNearPlane"] = mCamera.mOrthNearPlane;
            mCameraInfoN["mOrthFarPlane"] = mCamera.mOrthFarPlane;

            mCameraInfoN["mNormalBias"] = mCamera.mNormalBias;
            mCameraInfoN["mDepthBias"] = mCamera.mDepthBias;
        }
        ComponentN["mCameraInfo"] = std::move(mCameraInfoN);
        ComponentN["mRenderPipelineTag"] = "";
        ComponentN["mEnable"] = false;
        ComponentN["mProjectionMode"] = mCamera.mProjectionMode;
        ComponentN["mRenderToTargetCam"] = 0;
        ComponentN["mTargetWidth"] = mCamera.mTargetWidth;
        ComponentN["mTargetHeight"] = mCamera.mTargetHeight;
    }
    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

cross::SerializeNode CameraComponent::serializeController()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::ScriptComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();
        ComponentN["ScriptPath"] = "EngineResource/lua/CameraController.lua";
        ComponentN["ScriptProperties"] = cross::SerializeNode::EmptyObject();
        mDependencies.insert("EngineResource/lua/CameraController.lua");
    }
    return ComponentN;
}

cross::SerializeNode ControllableUnitComponent::serialize()
{
    using namespace cross;
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::ControllableUnitComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();
        ComponentN["Type"] = mControllableUnitInfo.mInfo.mControllableUnitType;
        cross::SerializeNode ControllerN;
        {
            ControllerN["Entity"] = _GetEntityUUID(mControllableUnitInfo.mInfo.mEntity);
            ControllerN["SpawnSpot"] = "";
            ControllerN["RotationOffset"] = SerializeNode{"x"_k = 0, "y"_k = 0, "z"_k = 0, "w"_k = 1};
            ControllerN["TranslationOffset"] = SerializeNode{"x"_k = 0, "y"_k = 0, "z"_k = 0};
            ControllerN["mPlayOnStart"] = mControllableUnitInfo.mInfo.mPlayOnStart;
            ControllerN["mRetargetCurveBindings"] = mControllableUnitInfo.mInfo.mRetargetCurveBindings;
            ControllerN["mPlaySpeed"] = mControllableUnitInfo.mInfo.mPlaySpeed;
            ControllerN["CurveCtrResPath"] = mControllableUnitInfo.mCurveCtrResPath;

            if (mControllableUnitInfo.mCurveCtrResPath != "")
                mDependencies.insert(mControllableUnitInfo.mCurveCtrResPath);
        }
        ComponentN["Controller"] = ControllerN.FormatToJson();
    }
    return ComponentN;
}

IEntity* WorldAssemble::AddPostProcessVolume(const char* name, PostProcessVolumeInfo* ppv, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);
    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::PostProcessVolume;
        memcpy(&entity->mPostProcessVolume.mPostProcessVolume, ppv, sizeof(decltype(*ppv)));
    }
    return entityI;
}

cross::SerializeNode PostProcessVolumeComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::PostProcessVolumeComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        cross::PostProcessVolumeSetting settings;

        settings.mType = cross::PostProcessVolumeType::Global; // TODO not exactly
        settings.BlendSettings.Priority = mPostProcessVolume.mPriority;

        // bloom
        auto& bloomDst = settings.mPostProcessBloomSetting;
        auto& bloomSrc = mPostProcessVolume.mBloom;
        bloomDst.BloomIntensity = bloomSrc.mIntensity;
        bloomDst.BloomThreshold = bloomSrc.mThreshold;
        bloomDst.BloomTint = {bloomSrc.mTint[0], bloomSrc.mTint[1], bloomSrc.mTint[2]};
        bloomDst.BloomScale = bloomSrc.mScale;
        bloomDst.BloomLuminanceClamp = bloomSrc.mLuminanceClamp;

        // exposure
        settings.mPostProcessExposureSetting.enable = true;
        settings.mPostProcessExposureSetting.mExposureType = cross::ExposureType::Histogram;
        auto& autoExposureDst = settings.mPostProcessExposureSetting.mHistogramExposureSettings;
        auto& autoExposureSrc = mPostProcessVolume.mExposure;
        autoExposureDst.enable = autoExposureSrc.mEnable;
        autoExposureDst.MinBrightness = autoExposureSrc.mMinBrightness;
        autoExposureDst.MaxBrightness = autoExposureSrc.mMaxBrightness;
        autoExposureDst.SpeedUp = autoExposureSrc.mSpeedUp;
        autoExposureDst.SpeedDown = autoExposureSrc.mSpeedDown;
        autoExposureDst.AutoExposureBias = autoExposureSrc.mAutoExposureBias;
        autoExposureDst.HistogramLogMin = autoExposureSrc.mHistogramLogMin;
        autoExposureDst.HistogramLogMax = autoExposureSrc.mHistogramLogMax;
        autoExposureDst.HighPercent = autoExposureSrc.mHighPercent;

        // Tonemapper only ACES?
        auto& tonemapDst = settings.mTonemapSettings.ACECureveSetting;
        auto& tonemapSrc = mPostProcessVolume.mTonemapper;
        tonemapDst.EnableToneMapping = tonemapSrc.mEnable;
        tonemapDst.FilmShoulder = tonemapSrc.mFilmShoulder;
        tonemapDst.FilmSlope = tonemapSrc.mFilmSlope;
        tonemapDst.FilmToe = tonemapSrc.mFilmToe;
        tonemapDst.FilmBlackClip = tonemapSrc.mFilmBlackClip;
        tonemapDst.FilmWhiteClip = tonemapSrc.mFilmWhiteClip;
        tonemapDst.ToneCurveAmount = tonemapSrc.mToneCurveAmount;

        // lens flare
        settings.mPostProcessLensFlareSetting.enable = false;

        // color correction (we don't have this in CE)

        // Depth of Field
        settings.mDepthOfFieldSetting.enable = false;
        
        settings.mPostProcessExposureSetting.mManualExposureSettings.enable = false;

        settings.mMotionBlurSetting.enable = false;

        cross::SerializeContext context;
        ComponentN["PostProcessVolumeSettings"] = settings.Serialize(context);
    }

    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

IEntity* WorldAssemble::AddSkyAtmosphere(const char* name, SkyAtmosphere* Sky, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto* entityI = AddEntity(name, hierarchy);
    {
        auto* entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::SkyAtmosphere;

        memcpy(&entity->mSkyAtmosphere.mSkyAtmosphere, Sky, sizeof(decltype(*Sky)));
    }
    return entityI;
}

cross::SerializeNode SkyAtmosphereComponent::serialize()
{
    using namespace cross::WorldSerializeConst;
    cross::SerializeNode ComponentN;
    {
        auto hash = cross::HashString("cross::SkyAtmosphereComponentG");
        ComponentN[ComponentHash] = hash.GetHash32();

        cross::SkyAtmosphereConfig settings;
        settings.MiePhase = mSkyAtmosphere.MiePhase;
        settings.MieScattCoeff = {mSkyAtmosphere.MieScattCoeff[0], mSkyAtmosphere.MieScattCoeff[1], mSkyAtmosphere.MieScattCoeff[2]};
        settings.MieScattScale = mSkyAtmosphere.MieScattScale;
        settings.MieAbsorCoeff = {mSkyAtmosphere.MieAbsorCoeff[0], mSkyAtmosphere.MieAbsorCoeff[1], mSkyAtmosphere.MieAbsorCoeff[2]};
        settings.MieAbsorScale = mSkyAtmosphere.MieAbsorScale;
        settings.MieScaleHeight = mSkyAtmosphere.MieScaleHeight;
        settings.RayScattCoeff = {mSkyAtmosphere.RayScattCoeff[0], mSkyAtmosphere.RayScattCoeff[1], mSkyAtmosphere.RayScattCoeff[2]};
        settings.RayScattScale = mSkyAtmosphere.RayScattScale;
        settings.RayScaleHeight = mSkyAtmosphere.RayScattHeight;
        settings.AbsorptiCoeff = {mSkyAtmosphere.AbsorptiCoeff[0], mSkyAtmosphere.AbsorptiCoeff[1], mSkyAtmosphere.AbsorptiCoeff[2]};
        settings.AbsorptiScale = mSkyAtmosphere.AbsorptiScale;
        settings.PlanetRadius = mSkyAtmosphere.PlanetRadius;
        settings.AtmosHeight = mSkyAtmosphere.AtmosHeight;
        settings.GroundAlbedo3 = {mSkyAtmosphere.GroundAlbedo3[0], mSkyAtmosphere.GroundAlbedo3[1], mSkyAtmosphere.GroundAlbedo3[2]};
        settings.SFogMieScattScale = mSkyAtmosphere.SFogMieScattScale;

        cross::SerializeContext context;
        ComponentN["config"] = settings.Serialize(context);

        cross::SkyAtmosphereOuterParam outerParams;
        outerParams.APScale = mSkyAtmosphere.APScale;
        outerParams.ColorTransmittance = mSkyAtmosphere.ColorTransmittance;
        ComponentN["outerParam"] = outerParams.Serialize(context);

    }
    assert(mDependencies.find("") == mDependencies.end());
    return ComponentN;
}

IEntity* WorldAssemble::AddTerrain(const char* name, TerrainDesc* desc, Hierarchy* hierarchy)
{
    assert(!mEndAssembled);

    auto entityI = AddEntity(name, hierarchy);
    {
        auto entity = static_cast<Entity*>(entityI);
        entity->mType = EntityType::Terrain;

        memcpy(&entity->mTerrain.mTerrainDesc, desc, sizeof(decltype(*desc)));
    }
    return entityI;
}

cross::SerializeNode TerrainComponent::serialize()
{
    using namespace cross::WorldSerializeConst;

    auto terrainResource = gResourceMgr.CreateResourceAs<cross::resource::TerrainResource>();
    {
        terrainResource->mTerrainInfo.mSurfaceType = cross::TerrainSurfaceType::Flat;
        terrainResource->mTerrainInfo.mGridSizeX = mTerrainDesc.mGridSizeX;
        terrainResource->mTerrainInfo.mGridSizeY = mTerrainDesc.mGridSizeY;
        terrainResource->mTerrainInfo.mBlockSize = mTerrainDesc.mBlockSize;
        terrainResource->mTerrainInfo.mTileSize = mTerrainDesc.mTileSize;
        terrainResource->mTerrainInfo.mTexelDensity = mTerrainDesc.mTexelDensity;
        terrainResource->mTerrainInfo.mRootDataPath = TryGetRefference(mTerrainDesc.mRootDataPath);
        terrainResource->mTerrainInfo.mHeightmapPrefix = TryGetRefference(mTerrainDesc.mHeightmapPrefix);
        terrainResource->mTerrainInfo.mAlbedoTexturePrefix = TryGetRefference(mTerrainDesc.mAlbedoTexturePrefix);
        terrainResource->mTerrainInfo.mWeightTexturePrefix = TryGetRefference(mTerrainDesc.mWeightTexturePrefix);
        terrainResource->mTerrainInfo.mMaterialPath = TryGetRefference(mTerrainDesc.mMaterialPath);
        terrainResource->mTerrainInfo.mDefaultHeightmapPath = TryGetRefference(mTerrainDesc.mDefaultHeightmapPath);
        terrainResource->mTerrainInfo.mDefaultAlbedoTexturePath = TryGetRefference(mTerrainDesc.mDefaultAlbedoTexturePath);

        for (auto i = 0; i != cross::NumMaxTerrainBlendLayers; i++)
        {
            if (auto baseColorTexture = mTerrainDesc.mBaseColorTextures[i])
            {
                terrainResource->mTerrainInfo.mBaseColorTextures.emplace_back(TryGetRefference(baseColorTexture));
            }
        }
        //terrainResource->mTerrainInfo.mNormalTextureNameList = mTerrainDesc.mNormalTextures;
        //terrainResource->mTerrainInfo.mHMRATextureNameList = mTerrainDesc.mHMRATextures;

        //cross::SerializeNode node;
        //if (!terrainResource->HasAsset())
        //{
        //    terrainResource->CreateAsset(mTerrainDesc.mTerrainPath);
        //}

        //cross::SerializeContext context;
        //node["mTerrainInfo"] = std::move(terrainResource->mTerrainInfo.Serialize(context));
        //terrainResource->Serialize(std::move(node), mTerrainDesc.mTerrainPath);

        auto terrainPath = TryGetRefference(mTerrainDesc.mTerrainPath);
        terrainResource->Serialize(terrainPath);
    }

    cross::SerializeNode componentN;
    {
        auto hash = cross::HashString("cross::TerrainComponentG");
        componentN[ComponentHash] = hash.GetHash32();
        componentN["mTerrainPath"] = mTerrainDesc.mTerrainPath;
        componentN["mEnabled"] = true;
    }

    assert(mDependencies.find("") == mDependencies.end());
    return componentN;
}

}   // namespace CEAssetExchange
