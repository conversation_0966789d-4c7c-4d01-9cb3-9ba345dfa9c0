#include "AssetPipeline/Cook/CookRecord.h"
#include "CrossBase/Format.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/ifile.h"
#include "FileSystem/filesystem.h"
#include "CrossBase/MD5/MD5.h"
#include "CrossBase/String/StringHelper.h"
namespace cross::editor {
std::string GetFileMd5(filesystem::IFilePtr fp) {
    auto fileSize = fp->GetSize();
    UInt8* data = new UInt8[fileSize];
    fp->Read(reinterpret_cast<char*>(data), fileSize);
    auto md5Str = MD5::GetCode(data, fileSize);
    delete[] data;
    return md5Str;
}

SerializeNode AssetInfo::Serialize()
{
    SerializeNode node;
    node["ModTS"] = mModTS;
    node["Size"]  = mSize;
    node["MD5"]   = mMD5;
    return node;
}

bool AssetInfo::Deserialize(const DeserializeNode& node)
{
    if (node.HasMember("ModTS"))
        mModTS = node["ModTS"].AsInt64();
    if (node.HasMember("Size"))
        mSize = node["Size"].AsInt64();
    if (node.HasMember("MD5"))
        mMD5 = node["MD5"].AsString();
    return true;
}

SerializeNode AssetCookRecord::Serialize()
{
    SerializeNode node;
    node["Src"] = std::move(mSrcInfo.Serialize());
    node["Des"] = std::move(mDesInfo.Serialize());
    return node;
}

bool AssetCookRecord::Deserialize(const DeserializeNode& node)
{
    if (node.HasMember("Src"))
        mSrcInfo.Deserialize(node["Src"]);
    if (node.HasMember("Des"))
        mDesInfo.Deserialize(node["Des"]);
    return true;
}

AssetCookRecordManager::AssetCookRecordManager() {
}

AssetCookRecordManager::~AssetCookRecordManager() {
    Clear();
}

void AssetCookRecordManager::Load(std::string path) {
    Clear();
    auto absolutePath = PathHelper::GetAbsolutePath(path);
    if (!PathHelper::IsFileExist(absolutePath))
        return;
    // read file
    filesystem::IFilePtr file = EngineGlobal::GetFileSystem()->Open(absolutePath);
    std::string fileContent = file->GetBuffer();
    // parse global
    DeserializeNode rootNode = DeserializeNode::ParseFromJson(fileContent);
    if (rootNode.IsObject())
    {
        if (rootNode.HasMember("Version"))
        {
            mVersion = rootNode["Version"].AsUInt32() + 1;
        }
        if (rootNode.HasMember("FileList"))
        {
            DeserializeNode fileListNode = rootNode["FileList"];
            for (auto it = fileListNode.begin(); it != fileListNode.end(); it++)
            {
                std::string filePath = it.Key().data();
                if (!PathHelper::IsFileExist(PathHelper::Combine(mDesAssetPath.c_str(), filePath.c_str())))
                {
                    mIsChanged = true;
                    continue;
                }
                if (mCookRecordMap.find(filePath) == mCookRecordMap.end())
                    mCookRecordMap.emplace(filePath, new AssetCookRecord());
                AssetCookRecord* record = mCookRecordMap[filePath];
                record->Deserialize(it.Value());
            }
        }
    }
    file->Close();
}

void AssetCookRecordManager::Save(std::string path) {
    if (!mIsChanged)
        return;
    auto dir = PathHelper::GetDirectoryFromAbsolutePath(path);
    if (!PathHelper::IsDirectoryExist(dir))
        PathHelper::CheckDirectory(dir);
    auto absolutePath = PathHelper::GetAbsolutePath(path);
    // cache old file list
    if (PathHelper::IsFileExist(absolutePath))
    {
        std::string suffix = Format("_V{}.json", mVersion - 1);
        std::string oldPath = StringHelper::Replace(absolutePath, ".json", suffix.c_str());
        int ret = ::rename(absolutePath.c_str(), oldPath.c_str());
        if (ret < 0)
        {
            LOG_EDITOR_ERROR("Rename Old FileList Failed");
        }
    }
    // save file list
    SerializeNode rootNode;
    rootNode["Version"] = mVersion;
    SerializeNode fileListNode;
    for (auto& it : mCookRecordMap)
    {
        fileListNode[it.first] = std::move(it.second->Serialize());
    }
    rootNode["FileList"] = std::move(fileListNode);
    std::string fileContent = rootNode.FormatToJson();
    EngineGlobal::GetFileSystem()->Save(absolutePath, fileContent.c_str(), fileContent.size());
}

void AssetCookRecordManager::AddRecord(std::string srcFile, std::string desFile) {
    std::string key = PathHelper::GetRelativePath(srcFile);
    if (mCookRecordMap.find(key) == mCookRecordMap.end())
        mCookRecordMap.emplace(key, new AssetCookRecord());
    AssetCookRecord* record = mCookRecordMap[key];
    record->mSrcInfo = GetFileAssetInfo(srcFile);
    record->mDesInfo = GetFileAssetInfo(desFile);
    mIsChanged = true;
}

bool AssetCookRecordManager::CheckRecord(std::string srcFile, std::string desFile, bool checkMd5)
{
    std::string key = PathHelper::GetRelativePath(srcFile);
    // 1 no record
    if (mCookRecordMap.find(key) == mCookRecordMap.end())
        return false;
    // 2 no des file
    if (!PathHelper::IsFileExist(desFile))
        return false;
    auto fileSys = EngineGlobal::GetFileSystem();
    AssetCookRecord* record = mCookRecordMap[key];
    // 3  time
    if (record->mDesInfo.mModTS != fileSys->GetTimeStamp(desFile))
        return false;
    if (record->mSrcInfo.mModTS != fileSys->GetTimeStamp(srcFile))
        return false;
    // 4 size
    auto desFp = fileSys->Open(desFile, false);
    if (!desFp || record->mDesInfo.mSize != desFp->GetSize())
    {
        desFp->Close();
        return false;
    }
    auto srcFp = fileSys->Open(srcFile, false);
    if (!srcFp || record->mSrcInfo.mSize != srcFp->GetSize())
    {
        srcFp->Close();
        desFp->Close();
        return false;
    }
    // md5
    if (checkMd5)
    {
        if (GetFileMd5(desFp) != record->mDesInfo.mMD5)
        {
            srcFp->Close();
            desFp->Close();
            return false;
        }
        desFp->Close();
        if (GetFileMd5(srcFp) != record->mSrcInfo.mMD5)
        {
            srcFp->Close();
            return false;
        }
    }
    else
    {
        desFp->Close();
    }
    srcFp->Close();
    return true;
}

void AssetCookRecordManager::Clear() {
    mIsChanged = false;
    for (auto& it : mCookRecordMap)
    {
        if (it.second)
            delete it.second;
    }
    mCookRecordMap.clear();
}

AssetInfo AssetCookRecordManager::GetFileAssetInfo(std::string path)
{
    AssetInfo info;
    // modify time
    auto fileSys = EngineGlobal::GetFileSystem();
    info.mModTS = fileSys->GetTimeStamp(path);
    // size
    auto fp = fileSys->Open(path, false);
    if (fp)
    {
        info.mSize = fp->GetSize();
        info.mMD5 = GetFileMd5(fp);
        fp->Close();
    }
    return info;
}
}   // namespace cross::editor