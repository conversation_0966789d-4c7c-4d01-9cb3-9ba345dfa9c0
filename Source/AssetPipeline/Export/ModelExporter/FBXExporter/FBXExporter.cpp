#include "FBXExporter.h"
#include <flatbuffers/minireflect.h>
#include <Resource/resourceasset.h>
#include <filesystem>
#include <iostream>
#include <fstream>

namespace cross::editor {
FBXExporter::FBXExporter()
    : m_pFbxSdkManager(nullptr)
    , m_pFbxScene(nullptr)
    , m_pcszDiffuseElementName("DiffuseUV")
    , m_pcszLightMapElementName("LightMapUV")
    , m_bUseCustomMaterial(false)
{}

FBXExporter::~FBXExporter() {}

bool FBXExporter::ExportSelectModelsAsFBX(const char* pcszFBXFilename, IEntityBuffer* pEntityBuffer)
{
    bool bResult = false;

    FBX_ASSERT(pcszFBXFilename);
    FBX_ASSERT(pEntityBuffer);

    strcpy(m_pszFBXFilename, pcszFBXFilename);

    if (pEntityBuffer)
    {
        // Prepare the FBX SDK.
        InitializeFbxSdkObjects();

        // Create the scene.
        bResult = CreateScene(pEntityBuffer);
        if (!bResult)
        {
            FBXSDK_printf("\n\nAn error occurred while creating the scene...\n");
            DestroyFbxSdkObjects(m_pFbxSdkManager, bResult);
            return false;
        }

        // Save the scene.
        bResult = SaveScene();
        if (!bResult)
        {
            FBXSDK_printf("\n\nAn error occurred while saving the scene...\n");
            DestroyFbxSdkObjects(m_pFbxSdkManager, bResult);
            return false;
        }

        // Destroy all objects created by the FBX SDK.
        DestroyFbxSdkObjects(m_pFbxSdkManager, bResult);
    }

    Clear();

    return true;
}

bool FBXExporter::ExportSelectModelsAsFBX(const char* inputNdaFilename, const char* outputFbxFilename)
{
    bool bResult = false;
    // Prepare the FBX SDK.
    InitializeFbxSdkObjects();
    // Create the scene.
    CrossSchema::ImportMeshAssetDataT importSet;

    if (!CheckNdaIsValid(inputNdaFilename)) 
    {
        return false;
    }
    MeshAssetData::DeSerializeToFlatbufferFile(inputNdaFilename, importSet);
    std::vector<MeshStructureInfo> info;
    MeshAssetDataTToMeshStructure(importSet, info);

    const std::size_t meshCount = importSet.fmeshpartinfo.size();
    mFbxMeshs.resize(meshCount);
    meshNodes.resize(meshCount);

    // Create mesh
    for (UInt32 meshindex = 0; meshindex < meshCount; meshindex++)
    {
        meshNodes[meshindex] = FbxNode::Create(m_pFbxScene, std::string("SingleNode" + std::to_string(meshindex)).c_str());
        ExportSingleMesh(info, meshindex);
        FbxEndMesh(meshindex);
    }
    
    // Save the scene.
    bResult = SaveScene(outputFbxFilename, 0, false);
    if (!bResult)
    {
        LOG_EDITOR_ERROR("An error occurred while saving the scene...\n");
    }
    // Destroy all objects created by the FBX SDK.
    DestroyFbxSdkObjects(m_pFbxSdkManager, bResult);
    Clear();
    return true;
}

void FBXExporter::ExportSingleMesh(std::vector<MeshStructureInfo>& info, int meshId)
{
    /*int size = 0;
    for (int i = 1; i < info.size(); i++)
    {
        size = static_cast<int>(info[0].vertices.size());
        info[0].vertices.insert(info[0].vertices.end(), info[i].vertices.begin(), info[i].vertices.end());
        info[0].normals.insert(info[0].normals.end(), info[i].normals.begin(), info[i].normals.end());
        info[0].binormals.insert(info[0].binormals.end(), info[i].binormals.begin(), info[i].binormals.end());
        info[0].tangents.insert(info[0].tangents.end(), info[i].tangents.begin(), info[i].tangents.end());
        for (int j = 0; j < info[i].indices.size(); j++)
        {
            info[i].indices[j] = info[i].indices[j] + size;
        }
        for (int j = 0; j < info[0].texCoords.size(); j++)
        {
            info[0].texCoords[j].insert(info[0].texCoords[j].end(), info[i].texCoords[j].begin(), info[i].texCoords[j].end());
        }
    }*/

    mFbxMeshs[meshId] = FbxMesh::Create(m_pFbxScene, std::string("Mesh" + std::to_string(meshId)).c_str());
    FbxAddVertices(info[meshId].vertices, meshId);
    FbxAddNormals(info[meshId].normals, meshId);
    FbxAddBiNormals(info[meshId].binormals, meshId);
    FbxAddTangents(info[meshId].tangents, meshId);

    for (int j = 0; j < info[meshId].texCoords.size(); j++)
    {
        FbxAddTexCoords(info[meshId].texCoords[j], j, "uv" + j, meshId);
    }
    // add material
    FbxAddMaterial(meshId);
    //for (int w = 0; w < info.size(); w++)
    {
        int materialId = FbxCreateDefaultMaterial(meshId);
        FbxAddIndices(info[meshId].indices, meshId, materialId);
    }
}

bool FBXExporter::MergeSelectModelsAsFBX(std::vector<std::string> fbxPath, std::vector<cross::Float3> fbxPosition, std::vector<cross::Float3> fbxRotation, std::vector<cross::Float3> fbxScale, const char* fbxpath)
{
    bool bResult = false;
    for (int i = 0; i < fbxPath.size(); i++) 
    {
        if (!CheckNdaIsValid(fbxPath[i].c_str())) 
        {
            return false;
        }
    }
    // Prepare the FBX SDK.
    InitializeFbxSdkObjects();
    std::size_t allMeshSize = 0;
    std::map<int, std::size_t> importSetMap;
    std::map<int, std::vector<MeshStructureInfo>> infoMap;
    for (int i = 0; i < fbxPath.size(); i++)
    {
        CrossSchema::ImportMeshAssetDataT importSet;
        MeshAssetData::DeSerializeToFlatbufferFile(fbxPath[i], importSet);
        infoMap[i] = {};
        MeshAssetDataTToMeshStructure(importSet, infoMap[i]);
        importSetMap[i] = importSet.fmeshpartinfo.size();
        allMeshSize += importSet.fmeshpartinfo.size();
    }
    mFbxMeshs.resize(allMeshSize);
    meshNodes.resize(allMeshSize);
    // Create the scene.
    for (int i = 0; i < fbxPath.size(); i++)
    {
        for (int meshindex = 0; meshindex < static_cast<int>(importSetMap[i]); meshindex++)
        {
            meshNodes[meshindex] = FbxNode::Create(m_pFbxScene, std::string("MultiNode" + std::to_string(i * 100 + meshindex)).c_str());
            ExportSingleMesh(infoMap[i], meshindex);
            // FbxEndMeshMultiNode(i, Float3(fbxPosition[i].x, fbxPosition[i].y, -fbxPosition[i].z));
            FbxEndMesh(meshindex, Float3(fbxPosition[i].x, fbxPosition[i].y, -fbxPosition[i].z), fbxRotation[i], fbxScale[i]);
        }
    }
    // Save the scene.
    bResult = SaveScene(fbxpath, 0, false);
    if (!bResult)
    {
        LOG_EDITOR_ERROR("An error occurred while saving the scene...\n");
    }
    // Destroy all objects created by the FBX SDK.
    DestroyFbxSdkObjects(m_pFbxSdkManager, bResult);
    Clear();
    return true;
}
bool FBXExporter::CheckNdaIsValid(const char* inputNdaFilename)
{
    const auto typedPath = std::filesystem::path{inputNdaFilename};
    std::ifstream ndaFile;
    ndaFile.open(typedPath, std::ios::binary | std::ios::in);
    if (ndaFile)
    {
        cross::resource::LoadNDAFileInfo info;
        gResourceAssetMgr.GetLoadNDAInfo(inputNdaFilename, info);

        ndaFile.seekg(0, std::ios::end);
        const auto bufSize = ndaFile.tellg();
        const auto headerSize = info.HasMetaHeader() ? info.GetMetaHeader().mJsonStringLength : 0;
        const auto dataSize = bufSize - std::streampos{headerSize};

        const auto buffer = std::make_unique<char[]>(dataSize);

        ndaFile.seekg(headerSize, std::ios::beg);
        ndaFile.read(buffer.get(), dataSize);
        ndaFile.close();

        flatbuffers::Verifier verifier(reinterpret_cast<UInt8*>(buffer.get()), dataSize);
        if (!CrossSchema::VerifyResourceAssetBuffer(verifier))
        {
            return false;
        }
        return true;
    }
    return false;
}
void FBXExporter::MeshAssetDataTToMeshStructure(CrossSchema::ImportMeshAssetDataT& dataT, std::vector<MeshStructureInfo>& info)
{
    auto& meshparts = dataT.fmeshpartinfo;
    info.resize(meshparts.size());

    for (std::size_t meshpartId = 0; meshpartId != meshparts.size(); meshpartId++)
    {
        // index
        int indexCount = meshparts[meshpartId]->findexcount;
        info[meshpartId].indices.resize(indexCount);

        if (dataT.findexstream->fis16bitindex)
        {
            const auto head = reinterpret_cast<const std::uint16_t*>(dataT.findexstream->fdata.data() + meshparts[meshpartId]->findexstart * sizeof(std::uint16_t));
            for (std::size_t i = 0; i < indexCount; i++)
            {
                info[meshpartId].indices[i] = *(head + i);
                Assert(info[meshpartId].indices[i] >= 0);
            }
        }
        else
        {
            const auto head = reinterpret_cast<const int*>(dataT.findexstream->fdata.data() + meshparts[meshpartId]->findexstart * sizeof(std::uint32_t));
            memcpy(info[meshpartId].indices.data(), head, indexCount * sizeof(int));
        }

        int texCoordId = 0;

        for (std::size_t i = 0; i < dataT.fvertexchanneldata.size(); i++)
        {
            auto vertexChannel = dataT.fvertexchanneldata[i].get();
            if (vertexChannel->fvertexchannel >= (uint32_t)cross::VertexChannel::TexCoord0 && vertexChannel->fvertexchannel <= (uint32_t)cross::VertexChannel::TexCoord4)
            {
                texCoordId++;
            }
        }
        info[meshpartId].texCoords.resize(texCoordId);
        texCoordId = 0;

        uint32_t verticesCount = meshparts[meshpartId]->fvertexcount;
        // about vertex
        for (std::size_t j = 0; j < dataT.fvertexchanneldata.size(); j++)
        {
            auto vertexChannel = dataT.fvertexchanneldata[j].get();
            if (vertexChannel->fvertexchannel == (uint32_t)cross::VertexChannel::Position0)
            {
                // uint32_t verticesCount = (uint32_t)(vertexChannel->fdata.size() / vertexChannel->fstride);
                info[meshpartId].vertices.resize(verticesCount);
                memcpy(info[meshpartId].vertices.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
            }
            else if (vertexChannel->fvertexchannel == (uint32_t)cross::VertexChannel::Normal0)
            {
                // uint32_t normalCount = (uint32_t)(vertexChannel->fdata.size() / vertexChannel->fstride);
                if (vertexChannel->fstride == sizeof(Short3))
                {
                    std::vector<Short3> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].normals.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].normals[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].normals[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].normals[q].z);
                    }
                }
                else
                {
                    std::vector<Char3> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].normals.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].normals[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].normals[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].normals[q].z);
                    }
                }
            }
            else if (vertexChannel->fvertexchannel >= (uint32_t)cross::VertexChannel::TexCoord0 && vertexChannel->fvertexchannel <= (uint32_t)cross::VertexChannel::TexCoord4)
            {
                if (vertexChannel->fstride == sizeof(CrossSchema::float2))
                {
                    info[meshpartId].texCoords[texCoordId].resize(verticesCount);
                    memcpy(info[meshpartId].texCoords[texCoordId].data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                }
                else
                {
                    if (meshparts[meshpartId]->fvertexstart * vertexChannel->fstride >= vertexChannel->fdata.size())
                        continue;
                    std::vector<UShort2> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].texCoords[texCoordId].resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        cross::HalfToFloat(normalizeData[q].x, info[meshpartId].texCoords[texCoordId][q].x);
                        cross::HalfToFloat(normalizeData[q].y, info[meshpartId].texCoords[texCoordId][q].y);
                    }
                }
                texCoordId++;
            }
            else if (vertexChannel->fvertexchannel == (uint32_t)cross::VertexChannel::Tangent0)
            {
                // uint32_t tangentCount = (uint32_t)(vertexChannel->fdata.size() / vertexChannel->fstride);
                if (vertexChannel->fstride == sizeof(Short4))
                {
                    std::vector<Short4> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].tangents.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].tangents[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].tangents[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].tangents[q].z);
                        SNormToFloat(normalizeData[q].w, info[meshpartId].tangents[q].w);
                    }
                }
                else
                {
                    std::vector<Char4> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].tangents.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].tangents[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].tangents[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].tangents[q].z);
                        SNormToFloat(normalizeData[q].w, info[meshpartId].tangents[q].w);
                    }
                }
            }
            else if (vertexChannel->fvertexchannel == (uint32_t)cross::VertexChannel::BiNormal0)
            {
                // uint32_t binormalCount = (uint32_t)(vertexChannel->fdata.size() / vertexChannel->fstride);
                if (vertexChannel->fstride == sizeof(Short4))
                {
                    std::vector<Short4> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].tangents.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].binormals[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].binormals[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].binormals[q].z);
                        SNormToFloat(normalizeData[q].w, info[meshpartId].binormals[q].w);
                    }
                }
                else
                {
                    std::vector<Char4> normalizeData;
                    normalizeData.resize(verticesCount);
                    memcpy(normalizeData.data(), vertexChannel->fdata.data() + meshparts[meshpartId]->fvertexstart * vertexChannel->fstride, verticesCount * vertexChannel->fstride);
                    info[meshpartId].tangents.resize(verticesCount);
                    for (std::size_t q = 0; q < verticesCount; q++)
                    {
                        SNormToFloat(normalizeData[q].x, info[meshpartId].binormals[q].x);
                        SNormToFloat(normalizeData[q].y, info[meshpartId].binormals[q].y);
                        SNormToFloat(normalizeData[q].z, info[meshpartId].binormals[q].z);
                        SNormToFloat(normalizeData[q].w, info[meshpartId].binormals[q].w);
                    }
                }
            }
        }
    }
}

void FBXExporter::FbxAddVertices(std::vector<AssetMath::Vector3f>& vertices, int index)
{
    mFbxMeshs[index]->InitControlPoints(static_cast<int>(vertices.size()));

    FbxVector4* ControlPoints = mFbxMeshs[index]->GetControlPoints();
    for (std::size_t i = 0; i < vertices.size(); i++)
    {
        ControlPoints[i] = FbxVector4(vertices[i].x, vertices[i].y, -vertices[i].z);
    }
}

void FBXExporter::FbxAddNormals(std::vector<AssetMath::Vector3f>& normals, int index)
{
    FbxLayer* layer = mFbxMeshs[index]->GetLayer(0);
    if (layer == nullptr)
    {
        mFbxMeshs[index]->CreateLayer();
        layer = mFbxMeshs[index]->GetLayer(0);
    }

    FbxLayerElementNormal* layerElementNormal = FbxLayerElementNormal::Create(mFbxMeshs[index], "");
    layerElementNormal->SetMappingMode(FbxLayerElement::eByControlPoint);
    layerElementNormal->SetReferenceMode(FbxLayerElement::eDirect);

    for (std::size_t i = 0; i < normals.size(); i++)
    {
        layerElementNormal->GetDirectArray().Add(FbxVector4(normals[i].x, normals[i].y, -normals[i].z));
    }
    layer->SetNormals(layerElementNormal);
}

void FBXExporter::FbxAddTangents(std::vector<AssetMath::Vector4f>& tangents, int index)
{
    FbxLayer* layer = mFbxMeshs[index]->GetLayer(0);
    if (layer == nullptr)
    {
        mFbxMeshs[index]->CreateLayer();
        layer = mFbxMeshs[index]->GetLayer(0);
    }

    FbxLayerElementTangent* layerElementTangent = FbxLayerElementTangent::Create(mFbxMeshs[index], "");
    layerElementTangent->SetMappingMode(FbxLayerElement::eByControlPoint);
    layerElementTangent->SetReferenceMode(FbxLayerElement::eDirect);

    for (std::size_t i = 0; i < tangents.size(); i++)
    {
        layerElementTangent->GetDirectArray().Add(FbxVector4(tangents[i].x, tangents[i].y, -tangents[i].z));
    }
    layer->SetTangents(layerElementTangent);
}

void FBXExporter::FbxAddBiNormals(std::vector<AssetMath::Vector4f>& binormals, int index)
{
    FbxLayer* layer = mFbxMeshs[index]->GetLayer(0);
    if (layer == nullptr)
    {
        mFbxMeshs[index]->CreateLayer();
        layer = mFbxMeshs[0]->GetLayer(0);
    }

    FbxLayerElementBinormal* layerElementBinormal = FbxLayerElementBinormal::Create(mFbxMeshs[index], "");
    layerElementBinormal->SetMappingMode(FbxLayerElement::eByControlPoint);
    layerElementBinormal->SetReferenceMode(FbxLayerElement::eDirect);

    for (std::size_t i = 0; i < binormals.size(); i++)
    {
        layerElementBinormal->GetDirectArray().Add(FbxVector4(binormals[i].x, binormals[i].y, -binormals[i].z));
    }
    layer->SetBinormals(layerElementBinormal);
}

void FBXExporter::FbxAddMaterial(int index)
{
    FbxLayer* layer = mFbxMeshs[index]->GetLayer(0);
    if (layer == nullptr)
    {
        mFbxMeshs[index]->CreateLayer();
        layer = mFbxMeshs[index]->GetLayer(0);
    }
    FbxLayerElementMaterial* matLayer = FbxLayerElementMaterial::Create(mFbxMeshs[index], "");
    matLayer->SetMappingMode(FbxLayerElement::eByPolygon);
    matLayer->SetReferenceMode(FbxLayerElement::eIndexToDirect);

    layer->SetMaterials(matLayer);
}

int FBXExporter::FbxCreateDefaultMaterial(int index)
{
    FbxSurfaceMaterial* fbxMaterial = m_pFbxScene->GetMaterial(const_cast<char*>("Fbx Default Material"));

    if (!fbxMaterial)
    {
        fbxMaterial = FbxSurfaceLambert::Create(m_pFbxScene, "Fbx Default Material");
        (static_cast<FbxSurfaceLambert*>(fbxMaterial))->Diffuse.Set(FbxDouble3(0.72, 0.72, 0.72));
    }

    //FbxNode* newMeshNode = FbxNode::Create(m_pFbxScene, "");
    int result = meshNodes[index]->AddMaterial(fbxMaterial);
    return result;
}

void FBXExporter::FbxAddTexCoords(std::vector<AssetMath::Vector2f>& uvs, int uvLayer, const char* channelName, int index)
{
    FbxLayer* Layer = mFbxMeshs[index]->GetLayer(uvLayer);

    if (Layer == nullptr)
    {
        int layers = mFbxMeshs[index]->GetLayerCount();
        int toCreate = uvLayer - layers;
        for (int i = 0; i <= toCreate; i++)
        {
            mFbxMeshs[index]->CreateLayer();
        }
        Layer = mFbxMeshs[index]->GetLayer(uvLayer);
    }

    FbxLayerElementUV* UVsLayer = FbxLayerElementUV::Create(mFbxMeshs[index], channelName);
    UVsLayer->SetMappingMode(FbxLayerElement::eByControlPoint);
    UVsLayer->SetReferenceMode(FbxLayerElement::eDirect);   // eIndexToDirect

    for (int i = 0; i < uvs.size(); i++)
    {
        UVsLayer->GetDirectArray().Add(FbxVector2(uvs[i].x, 1 - uvs[i].y));
    }
    Layer->SetUVs(UVsLayer);
}

void FBXExporter::FbxAddIndices(std::vector<UInt32>& indices, int index, int Material, int size)
{
    for (std::size_t i = 0; i < indices.size() - 2; i += 3)
    {
        mFbxMeshs[index]->BeginPolygon(Material);
        for (std::size_t j = 0; j < 3; j+=3)
        {
            mFbxMeshs[index]->AddPolygon(indices[i + j + 2]);
            mFbxMeshs[index]->AddPolygon(indices[i + j + 1]);
            mFbxMeshs[index]->AddPolygon(indices[i + j ]);
            //mFbxMeshs[index]->AddPolygon(indices[i + j ]);
        }
        mFbxMeshs[index]->EndPolygon();
    }
    mFbxMeshs[index]->mPolygons;
}

Float3 FBXExporter::CalLeftToRightRotation(Float3 rotation)
{
    Double4x4 s_z;
    s_z.m33 = -1;
    cross::Quaternion64 rot = Quaternion64::EulerToQuaternion64(Double3(rotation.x, rotation.y, rotation.z).ToRadian());
    rot.y = -rot.y;
    rot.z = -rot.z;
    Double3 end = Quaternion64::Quaternion64ToEuler(rot).ToDegree();
    return Float3(static_cast<float>(end.x), static_cast<float>(end.y), static_cast<float>(end.z));
}

void FBXExporter::FbxEndMesh(int index, Float3 position, Float3 rotation, Float3 scale)
{
    //FbxNode* meshNode = FbxNode::Create(m_pFbxScene, "");
    meshNodes[index]->SetNodeAttribute(mFbxMeshs[index]);
    meshNodes[index]->SetShadingMode(FbxNode::eTextureShading);
    // set the cube position
    meshNodes[index]->LclTranslation.Set(FbxVector4(position.x, position.y, position.z));
    Float3 result = CalLeftToRightRotation(rotation);
    meshNodes[index]->LclRotation.Set(FbxVector4(result.x, result.y, result.z));
    meshNodes[index]->LclScaling.Set(FbxVector4(scale.x, scale.y, scale.z));
    m_pFbxScene->GetRootNode()->AddChild(meshNodes[index]);
}

void FBXExporter::FbxEndMeshMultiNode(int index, Float3 position)
{
    FbxNode* newMeshNode = FbxNode::Create(m_pFbxScene, "");

    newMeshNode->SetNodeAttribute(mFbxMeshs[index]);
    newMeshNode->SetShadingMode(FbxNode::eTextureShading);
    // set the cube position
    newMeshNode->LclTranslation.Set(FbxVector4(position.x, position.y, position.z));
    m_pFbxScene->GetRootNode()->AddChild(newMeshNode);
}

bool FBXExporter::SaveScene(const char* exportFbxFilename, int pFileFormat, bool pEmbedMedia)
{
    int lMajor, lMinor, lRevision;
    bool lStatus = true;

    if (pFileFormat < 0 || pFileFormat >= m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatCount())
    {
        // Write in fall back format if pEmbedMedia is true
        pFileFormat = m_pFbxSdkManager->GetIOPluginRegistry()->GetNativeWriterFormat();

        if (!pEmbedMedia)
        {
            // Try to export in ASCII if possible
            int lFormatIndex, lFormatCount = m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatCount();

            for (lFormatIndex = 0; lFormatIndex < lFormatCount; lFormatIndex++)
            {
                if (m_pFbxSdkManager->GetIOPluginRegistry()->WriterIsFBX(lFormatIndex))
                {
                    FbxString lDesc = m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatDescription(lFormatIndex);
                    if (lDesc.Find("ascii") >= 0)
                    {
                        pFileFormat = lFormatIndex;
                        break;
                    }
                }
            }
        }
    }

    // Initialize the exporter by providing a filename.
    if (mExporter->Initialize(exportFbxFilename, pFileFormat, m_pFbxSdkManager->GetIOSettings()) == false)
    {
        LOG_EDITOR_ERROR("Call to FbxExporter::Initialize() failed.");
        LOG_EDITOR_ERROR("Error returned: %s", mExporter->GetStatus().GetErrorString());
        return false;
    }

    FbxManager::GetFileFormatVersion(lMajor, lMinor, lRevision);
    LOG_EDITOR_ERROR("FBX version number for this FBX SDK is {}.{}.{}", lMajor, lMinor, lRevision);

    if (m_pFbxSdkManager->GetIOPluginRegistry()->WriterIsFBX(pFileFormat))
    {
        // Export options determine what kind of data is to be imported.
        // The default (except for the option eEXPORT_TEXTURE_AS_EMBEDDED)
        // is true, but here we set the options explicitly.
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_MATERIAL, true);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_TEXTURE, true);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_EMBEDDED, pEmbedMedia);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_SHAPE, true);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_GOBO, true);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_ANIMATION, true);
        (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_GLOBAL_SETTINGS, true);
    }

    // Export the scene.
    lStatus = mExporter->Export(m_pFbxScene);

    return lStatus;
}

void FBXExporter::InitializeFbxSdkObjects()
{
    // The first thing to do is to create the FBX Manager which is the object allocator for almost all the classes in the SDK
    m_pFbxSdkManager = FbxManager::Create();
    if (!m_pFbxSdkManager)
    {
        FBXSDK_printf("Error: Unable to create FBX Manager!\n");
        exit(1);
    }
    else
    {
        FBXSDK_printf("Autodesk FBX SDK version %s\n", m_pFbxSdkManager->GetVersion());
    }

    // Create an IOSettings object. This object holds all import/export settings.
    FbxIOSettings* ios = FbxIOSettings::Create(m_pFbxSdkManager, IOSROOT);
    m_pFbxSdkManager->SetIOSettings(ios);

    // Load plugins from the executable directory (optional)
    /*FbxString lPath = FbxGetApplicationDirectory();
    m_pFbxSdkManager->LoadPluginsDirectory(lPath.Buffer());*/

    // Create an FBX scene. This object holds most objects imported/exported from/to files.
    m_pFbxScene = FbxScene::Create(m_pFbxSdkManager, "NeoX Scene");
    if (!m_pFbxScene)
    {
        FBXSDK_printf("Error: Unable to create FBX scene!\n");
        exit(1);
    }
    // Create an exporter.
    mExporter = FbxExporter::Create(m_pFbxSdkManager, "");

    /*meshNode = FbxNode::Create(m_pFbxScene, "");*/
    AxisSystemConvert();
}

bool FBXExporter::CreateScene([[maybe_unused]] IEntityBuffer* pEntityBuffer)
{
    return true;
}

bool FBXExporter::SaveScene(int nFileFormat /* = -1 */, bool bEmbedMedia /* = false */)
{
    bool bStatus = true;
    int nMajor, nMinor, nRevision;

    // Create an exporter.
    FbxExporter* pFbxExporter = FbxExporter::Create(m_pFbxSdkManager, "");

    if (nFileFormat < 0 || nFileFormat >= m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatCount())
    {
        // Write in fall back format in less no ASCII format found
        nFileFormat = m_pFbxSdkManager->GetIOPluginRegistry()->GetNativeWriterFormat();

        // Try to export in ASCII if possible
        int nFormatCount = m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatCount();

        for (int nFormatIndex = 0; nFormatIndex < nFormatCount; nFormatIndex++)
        {
            if (m_pFbxSdkManager->GetIOPluginRegistry()->WriterIsFBX(nFormatIndex))
            {
                FbxString strDescription = m_pFbxSdkManager->GetIOPluginRegistry()->GetWriterFormatDescription(nFormatIndex);
                const char* pcszASCII = "ascii";
                if (strDescription.Find(pcszASCII) >= 0)
                {
                    nFileFormat = nFormatIndex;
                    break;
                }
            }
        }
    }

    // Set the export states. By default, the export states are always set to
    // true except for the option eEXPORT_TEXTURE_AS_EMBEDDED. The code below
    // shows how to change these states.
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_MATERIAL, true);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_TEXTURE, true);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_EMBEDDED, bEmbedMedia);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_SHAPE, true);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_GOBO, true);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_ANIMATION, true);
    (*(m_pFbxSdkManager->GetIOSettings())).SetBoolProp(EXP_FBX_GLOBAL_SETTINGS, true);

    // Initialize the exporter by providing a filename.
    if (pFbxExporter->Initialize(m_pszFBXFilename, nFileFormat, m_pFbxSdkManager->GetIOSettings()) == false)
    {
        FBXSDK_printf("Call to FBXExporter::Initialize() failed.\n");
        FBXSDK_printf("Error returned: %s\n\n", pFbxExporter->GetStatus().GetErrorString());
        return false;
    }

    FbxManager::GetFileFormatVersion(nMajor, nMinor, nRevision);
    FBXSDK_printf("FBX file format version %d.%d.%d\n\n", nMajor, nMinor, nRevision);

    // Export the scene.
    bStatus = pFbxExporter->Export(m_pFbxScene);

    // Destroy the exporter.
    pFbxExporter->Destroy();
    return bStatus;
}

void FBXExporter::DestroyFbxSdkObjects(FbxManager* fbxSdkManager, bool bExitStatus)
{
    // Delete the FBX Manager. All the objects that have been allocated using the FBX Manager and that haven't been explicitly destroyed are also automatically destroyed.
    if (fbxSdkManager)
    {
        fbxSdkManager->Destroy();
    }
    if (mFbxMeshs.size() != 0)
    {
        mFbxMeshs.~vector();
    }
    // Destroy the exporter.
    if (mExporter)
    {
        mExporter = nullptr;
    }

    if (bExitStatus)
    {
        // FBXSDK_printf("Program Success!\n");
    }
}

void FBXExporter::Clear()
{
    m_pFbxSdkManager = nullptr;
    m_pFbxScene = nullptr;
    mExporter = nullptr;
    mFbxMeshs.~vector();
    m_mapTextureToSubMeshInfo.clear();
}

void FBXExporter::AxisSystemConvert()
{
    // Convert Axis System, if needed
    FbxAxisSystem fbxAxisSystem = m_pFbxScene->GetGlobalSettings().GetAxisSystem();
    FbxAxisSystem engineAxisSystem(FbxAxisSystem::eYAxis, FbxAxisSystem::eParityOdd, FbxAxisSystem::eRightHanded);
    if (fbxAxisSystem != engineAxisSystem)
    {
        engineAxisSystem.ConvertScene(m_pFbxScene);
    }
}

void FBXExporter::ExtractModelInfo() {}

void FBXExporter::ExportNode() {}

void FBXExporter::SetGeometryInfo([[maybe_unused]] FbxMesh* pFbxMesh, [[maybe_unused]] SubMeshInfo& subMeshInfo) {}

void FBXExporter::CreateDefaultMaterial(FbxMesh* pFbxMesh, FbxNode* pFbxNode, FbxSurfacePhong* pFbxMaterial, int nGroupIndex, const char* pcszTextureFilename)
{
    FBX_ASSERT(pFbxMesh);
    FBX_ASSERT(pFbxNode);

    if (pFbxMesh && pFbxNode)
    {
        FbxString strMaterialName = std::to_string(nGroupIndex).c_str();
        FbxString strShadingName = "Phong";
        FbxDouble3 dBlackColor(0.0, 0.0, 0.0);
        FbxDouble3 dRedColor(1.0, 0.0, 0.0);
        FbxDouble3 dDiffuseColor(0.75, 0.75, 0.0);

        FbxLayer* pFbxLayer = pFbxMesh->GetLayer(0);
        if (pFbxLayer)
        {
            // Create a layer element material to handle proper mapping.
            FbxLayerElementMaterial* pFbxLayerElementMaterial = FbxLayerElementMaterial::Create(pFbxMesh, strMaterialName.Buffer());
            if (pFbxLayerElementMaterial)
            {
                // This allows us to control where the materials are mapped.  Using eAllSame
                // means that all faces/polygons of the mesh will be assigned the same material.
                pFbxLayerElementMaterial->SetMappingMode(FbxLayerElement::eAllSame);
                pFbxLayerElementMaterial->SetReferenceMode(FbxLayerElement::eIndexToDirect);

                // Add an index to the pFbxLayerElementMaterial. Since we have only one, and are using eAllSame mapping mode,
                // we only need to add one.
                pFbxLayerElementMaterial->GetIndexArray().Add(0);
            }

            // Save the material on the layer
            pFbxLayer->SetMaterials(pFbxLayerElementMaterial);
        }

        FbxFileTexture* pFbxTexture = FbxFileTexture::Create(m_pFbxScene, "Diffuse Texture");
        if (pFbxTexture)
        {
            // Set texture properties.
            pFbxTexture->SetFileName(pcszTextureFilename);   // Resource file is in current directory.
            pFbxTexture->SetTextureUse(FbxTexture::eStandard);
            pFbxTexture->SetMappingType(FbxTexture::eUV);
            pFbxTexture->SetMaterialUse(FbxFileTexture::eModelMaterial);
            pFbxTexture->SetSwapUV(false);
            pFbxTexture->SetTranslation(0.0, 0.0);
            pFbxTexture->SetScale(1.0, 1.0);
            pFbxTexture->SetRotation(0.0, 0.0);
            pFbxTexture->UVSet.Set(FbxString(m_pcszDiffuseElementName));   // Connect texture to the proper UV
        }

        pFbxMaterial = FbxSurfacePhong::Create(m_pFbxScene, strMaterialName.Buffer());
        if (pFbxMaterial)
        {
            // Generate primary and secondary colors.
            pFbxMaterial->Emissive.Set(dBlackColor);
            pFbxMaterial->Ambient.Set(dRedColor);
            pFbxMaterial->AmbientFactor.Set(1.0);
            pFbxMaterial->Diffuse.Set(dDiffuseColor);
            pFbxMaterial->DiffuseFactor.Set(1.0);
            pFbxMaterial->TransparencyFactor.Set(0.4);
            pFbxMaterial->ShadingModel.Set(strShadingName);
            pFbxMaterial->Shininess.Set(0.5);
            pFbxMaterial->Specular.Set(dBlackColor);
            pFbxMaterial->SpecularFactor.Set(0.3);

            // connect the texture to the corresponding property of the material
            pFbxMaterial->Diffuse.ConnectSrcObject(pFbxTexture);

            pFbxNode->AddMaterial(pFbxMaterial);
        }
    }
}

void FBXExporter::CreateCustomMaterial([[maybe_unused]] FbxNode* pFbxNode) {}

void FBXExporter::CreateTableEntry(FbxBindingTable* pBindingTable, FbxProperty& pProperty)
{
    // create entry
    FbxBindingTableEntry& bindingTableEntry = pBindingTable->AddNewEntry();

    // set src to the fbx property, fbx properties have the same name with shader parameters
    FbxPropertyEntryView srcPropertyEntryView(&bindingTableEntry, true, true);
    srcPropertyEntryView.SetProperty(pProperty.GetHierarchicalName());

    // set dst to the shader parameter
    FbxSemanticEntryView dstSemanticEntryView(&bindingTableEntry, false, true);
    dstSemanticEntryView.SetSemantic(pProperty.GetName());
}

void FBXExporter::SetNodeTransform(FbxNode* pFbxNode, [[maybe_unused]] SubMeshInfo& subMeshInfo)
{
    FBX_ASSERT(pFbxNode);
}
}   // namespace cross::editor