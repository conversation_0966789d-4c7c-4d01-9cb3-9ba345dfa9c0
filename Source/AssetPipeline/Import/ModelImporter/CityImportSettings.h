#pragma once
#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "Resource/MeshAssetDataInfo.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"
#include "AssetPipeline/Model/ModelChange.h"

namespace cross::editor
{
//#pragma pack(push, 1)

    struct ASSET_API CityImportSettings : ImportSetting
    {
        CE_Virtual_Serialize_Deserialize;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Create New Material"))
        bool CreateNewMaterial = false;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsResource", ToolTips = "City Material"))
        std::string CityMaterial = "Contents/Library/City_Materials/city01.mtl.nda";

        <PERSON>Meta(<PERSON><PERSON><PERSON>, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "City Resource Lib Path"))
        std::string CityResLibPath = "Contents/Library/City_Materials";

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsResource",
                                   ToolTips = "Selected custom fx for create material, which texture name from fx must include one of these keywords: color, diffuse, base, albedo, normal, specular, emissive",
                                   FileTypeDescriptor = "Fx resource#nda", ObjectClassID1 = ClassIDType.CLASS_Fx))
        std::string MaterialFx{"EngineResource/Shader/DefaultShader.nda"};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Use Exist Texture"))
        bool UseExistTexture = true;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Texture Size"))
        ImportTexureSize TextureSize = ImportTexureSize::SQUARE_512;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Base Map Compression"))
        TextureCompression BaseMapCompression = TextureCompression::BC1;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Normal Map Compression."))
        TextureCompression NormalMapCompression = TextureCompression::BC5;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Mask Map Compression."))
        TextureCompression MaskMapCompression = TextureCompression::BC1;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Emissive Map Compression"))
        TextureCompression EmissiveMapCompression = TextureCompression::BC1;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ffs mesh curvation, pointcloud only!"))
        bool UseMeshCurvation{false};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Split Model"))
        bool SplitModel = false;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable VT Streaming"))
        ModelAsMode SplitSubModelAsMode = ModelAsMode::ModelAsEntity;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Split Model Tile Size (cm)"))
        Float2 SplitModelTileSize = {200000.0, 200000.0};

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Enable VT Streaming"))
        bool EnableVTStreaming = false;

        CEMeta(Serialize, Editor)
        CECSAttribute(PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "City Src Resource Lib Path"))
        std::string CitySrcResLibPath = "F:/FFS_PCG_Assets/XT_visual/Assets";

        

        static CityImportSettings gCityImporterSetting;

        void SetEngineImportSettingImp() { gCityImporterSetting = *this; }
    };
    //#pragma pack(pop)
}
