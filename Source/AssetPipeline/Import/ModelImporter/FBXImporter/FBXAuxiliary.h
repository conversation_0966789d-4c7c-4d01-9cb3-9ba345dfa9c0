#pragma once
#include <stdlib.h>
#include <iostream>
#include <fbxsdk.h>
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
#include <vector> 
#include <map> 
#include "string"
#include <assert.h>

#include "FBXNewImporter.h"
#include "FBXConversion.h"
#include "FBXAnimation.h"
#include "FBXConstraints.h"
#include "FBXImportUtility.h"
#include "External/Tristripper/Adjacency.h"
#include "External/mikktspace/mikktspace.h"
#include "FBXLayerConvert.h"
#include "CrossBase/String/Word.h"
#if CROSSENGINE_OSX
#include <string>
#include <locale>
#include <codecvt>
#endif
//#define LOG_EDITOR_ERROR(...) printf(__VA_ARGS__)
namespace cross
{
    namespace editor
    {

        //help class for GuaranteeUniqueNodeNames 
        class SortingFbxNode
        {
            std::string m_Path;
            int m_Depth;
            FBXImportNode *m_Node;
        
            SortingFbxNode(std::string path, int depth, FBXImportNode* node)
                : m_Path(path)
                , m_Depth(depth)
                , m_Node(node)
            {}

            friend bool operator <(const SortingFbxNode &a, const SortingFbxNode &b)
            {
                if (a.m_Node->name > b.m_Node->name)
                    return false;
                if (a.m_Node->name < b.m_Node->name)
                    return true;
                if (a.m_Depth > b.m_Depth)
                    return false;
                if (a.m_Depth < b.m_Depth)
                    return true;
                if (a.m_Path > b.m_Path)
                    return false;
                if (a.m_Path < b.m_Path)
                    return true;
                return false;
            }
            typedef std::vector<SortingFbxNode> SortingNodes;
            static void FillSortingNodes(FBXImportNode &node, int depth, std::string path, SortingNodes &nodes)
            {
                SortingFbxNode sort(path, depth, &node);
                nodes.push_back(sort);

                for (decltype(node.children)::iterator i = node.children.begin(); i != node.children.end(); i++)
                {
                    FillSortingNodes(*i, depth + 1, path + "/" + node.name, nodes);
                }
            }
        public:
            static void GuaranteeUniqueNodeNames(FBXImportScene& scene)
            {
                bool foundDupes = true;
                while (foundDupes)
                {
                    foundDupes = false;
                    std::vector<SortingFbxNode> nodes;
                    for (decltype(scene.nodes)::iterator i = scene.nodes.begin(); i != scene.nodes.end(); i++)
                        FillSortingNodes(*i, 0, "", nodes);

                    std::sort(nodes.begin(), nodes.end());

                    std::string lastName;
                    int sameNames = 0;
                    for (int i = 0; i < nodes.size(); i++)
                    {
                        std::string name = nodes[i].m_Node->name;
                        if (name == lastName)
                        {
                            sameNames++;
                            nodes[i].m_Node->name += Format(" %d", sameNames);
                            foundDupes = true;
                        }
                        else
                        {
                            sameNames = 0;
                        }
                        lastName = name;
                    }
                }
            }
        };

        class FBXAuxiliary
        {
        public:
        
            FBXAuxiliary()
            {}
            
            ~FBXAuxiliary()
            {}

            static void buildFBNode(ImportNode& root, CrossSchema::ImportNodeT& fbroot)

            {
                fbroot.name = root.Name;
                fbroot.meshindex = root.MeshIndex;
                fbroot.boneindexinimportmesh = root.BoneIndexInImportMesh;
                fbroot.isrootinimportmesh = root.IsRootInImportMesh;
                fbroot.localtransform = std::vector<float>(root.LocalTransform.data(), root.LocalTransform.data() + 16);
                if (!root.Children.size())
                    return;

                for (auto& ch : root.Children)
                {
                    std::unique_ptr<CrossSchema::ImportNodeT> newnode(new CrossSchema::ImportNodeT);
                    fbroot.children.push_back(std::move(newnode));
                    buildFBNode(ch, *fbroot.children.back());
                }
            }

        };    
        
         //-------------------------------------------
        class ConvertMaterials
        {
        public:
            ConvertMaterials()
            {}
            ~ConvertMaterials()
            {}

            static ConvertMaterials& Get()
            {
                static ConvertMaterials* c = new ConvertMaterials();
                return *c;
            }

        public:
            void PrintGenericInvalidList(const std::string& nameStr, const UInt32 maxIndexCount, const int totalCount, const std::vector<int>& indices, const std::string& messageText)
            {
                std::ostringstream oss;
                oss << nameStr << " has " << indices.size()
                    << " (out of " << totalCount << ") " << messageText << ". The list of vertices: ";

                for (int i = 0, size = std::min<int>(maxIndexCount, static_cast<int>(indices.size())); i < size; ++i)
                {
                    if (i != 0)
                    {
                        oss << ", ";
                    }
                    oss << indices[i];
                }

                if (indices.size() > maxIndexCount)
                {
                    oss << " and so on...";
                }

                oss << std::endl;

                ReportWarning("%s", oss.str().c_str());
            }

            void PrintInvalidList(const std::string& meshName, const UInt32 maxIndexCount, const int totalCount, const std::vector<int>& indices, const std::string& messageText)
            {
                PrintGenericInvalidList("Mesh '" + meshName + "'", maxIndexCount, totalCount, indices, messageText);
            }

            bool IsValidVertex(const AssetMath::Vector2f& v)
            {
                return !v.IsNaN();
            }

            bool IsValidVertex(const AssetMath::Vector3f& v)
            { 
                return !v.IsNaN();
            }

            void ValidateUVs(FBXImportMesh& mesh, const std::string& meshName, int uvMapIndex)
            {
                std::vector<AssetMath::Vector2f>& uvs = mesh.uvs[uvMapIndex];

                std::vector<int> invalidUVs;
                for (UInt32 i = 0, size = (UInt32)uvs.size(); i < size; ++i)
                {
                    // case 370153: UVs in fbx can contain NaNs, so we set these vertices to (0,0,0)
                    if (!IsValidVertex(uvs[i]))
                    {
                        uvs[i] = AssetMath::Vector2f::Zero();
                        invalidUVs.push_back(i);
                    }
                }

                if (!invalidUVs.empty())
                {
                    PrintInvalidList(meshName, 10, static_cast<int>(uvs.size()), invalidUVs, "invalid UVs (NaNs) in UVMap #" + IntToString(uvMapIndex) + ". They will be assigned value (0,0)");
                }
            }

            void ValidateUVs(FBXImportMesh& mesh, const std::string& meshName)
            {
                ValidateUVs(mesh, meshName, 0);
                ValidateUVs(mesh, meshName, 1);
            }

            FbxVector4 ConvertToGlobalCoordinates(FbxNode& node, FbxVector4 vector)
            {
                FbxAMatrix globalMatrix = node.EvaluateGlobalTransform(0);
                FbxVector4 result = globalMatrix.MultT(vector);

                //LOG_EDITOR_WARN("before:{}  {}  {}  {}  after: {} {} {} {}\n", vector.mData[0], vector.mData[1], vector.mData[2], vector.mData[3],
                //                                                        result.mData[0], result.mData[1], result.mData[2], result.mData[3]);
                return result;
            }

            // referenceVertices are used for blendshapes
            void ImportVerticesPosition(FbxNode& node, const FbxGeometryBase& fbx, const std::vector<AssetMath::Vector3f>* referenceVertices, std::vector<AssetMath::Vector3f>& vertices, std::vector<int>& invalidVertices)
            {
                bool cnIsSkinned = false;
                FbxMesh* pFbxMesh = node.GetMesh();
                if (pFbxMesh != nullptr)
                {
                    auto pSkin = reinterpret_cast<FbxSkin*>(pFbxMesh->GetDeformer(0, FbxDeformer::eSkin));
                    if (pSkin != nullptr)
                        cnIsSkinned = true;
                }

                int vertexCount = fbx.GetControlPointsCount();
                vertices.resize(vertexCount);
                FbxVector4* controlPoints = fbx.GetControlPoints();
                for (int i = 0; i < vertexCount; i++)
                {
                    FbxVector4 t = cnIsSkinned ? controlPoints[i] : ConvertToGlobalCoordinates(node, controlPoints[i]);

                    vertices[i] = FBXPointToVector3Remap(t);


                    if (!IsValidVertex(vertices[i]))
                    {
                        vertices[i] = referenceVertices ? (*referenceVertices)[i] : AssetMath::Vector3f::Zero();
                        invalidVertices.push_back(i);
                    }
                }
            }

            // referenceVertices are used for blendshapes
            void ImportVertices(const FbxGeometryBase& fbx, const std::vector<AssetMath::Vector3f>* referenceVertices, std::vector<AssetMath::Vector3f>& vertices, std::vector<int>& invalidVertices)
            {
                int vertexCount = fbx.GetControlPointsCount();
                vertices.resize(vertexCount);
                FbxVector4* controlPoints = fbx.GetControlPoints();
                for (int i = 0; i < vertexCount; i++)
                {
                    vertices[i] = FBXPointToVector3Remap(controlPoints[i]);

                    // vertices in fbx can contain NaNs, so we set these vertices to (0,0,0) for meshes and to referenceVertices for blendShapes
                    if (!IsValidVertex(vertices[i]))
                    {
                        vertices[i] = referenceVertices ? (*referenceVertices)[i] : AssetMath::Vector3f::Zero();
                        invalidVertices.push_back(i);
                    }
                }
            }

            template<class T, class T2, class GetValueAt>
            void ExtractWedgeLayerData1([[maybe_unused]] FbxMesh* fbxMesh, FbxLayerElementTemplate<T>& element, std::vector<T2>& output, int* indices, int indexCount, 
                UInt32* polygonSizes, int nPolygonCount, int vertexCount, const char* layerName, const std::string& meshName)
            {
                GetValueAt input(element);
                if (!input.IsValid())
                    return;

                output.resize(indexCount);

                const FbxLayerElement::EMappingMode mappingMode = element.GetMappingMode();

                if (mappingMode == FbxLayerElement::eByControlPoint)
                {
                    // TODO(maxwan): I'm not sure this makes sense when eIndexToDirect is used
                    if (input.GetDirectArraySize() != vertexCount)
                    {
                        output.clear();
                        ReportError("The mesh %s has invalid %s. Try cleaning and triangulating %s in your 3D modeller before importing it in engine.\nThis is a failure in the fbx exporter of the tool which exported the fbx file.", 
                            meshName.c_str(), layerName, meshName.c_str(), layerName);
                        return;
                    }

                    for (int f = 0; f < indexCount; f++)
                    {
                        int index = indices[f];
                        input.GetValue(index, output[f]);
                    }
                }
                else if (mappingMode == FbxLayerElement::eByPolygonVertex)
                {
                    for (int f = 0; f < indexCount; ++f)
                    {
                        input.GetValue(f, output[f]);
                    }
                }
                else if (mappingMode == FbxLayerElement::eByPolygon)
                {
                    int wedgeIndex = 0;
                    for (int f = 0; f < nPolygonCount; f++)
                    {
                        for (UInt32 e = 0; e < polygonSizes[f]; e++, wedgeIndex++)
                        {
                            input.GetValue(f, output[wedgeIndex]);
                        }
                    }
                }
                else if (mappingMode == FbxLayerElement::eAllSame)
                {
                    T2 value;
                    if (input.GetValue(0, value))
                    {
                        for (int f = 0; f < indexCount; f++)
                        {
                            output[f] = value;
                        }
                    }
                }
                else
                {
                    LOG_INFO("Unsupported wedge mapping {}", (int)mappingMode);
                    LOG_EDITOR_ERROR("Unsupported wedge mapping mode type. Please report this bug.\n");
                }
            }

            template<class T, class T2>
            void ExtractWedgeLayerData(FbxMesh* fbxMesh, FbxLayerElementTemplate<T>& element, std::vector<T2>& output, int* indices, int indexCount, 
                UInt32* polygonSizes, int nPolygonCount, int vertexCount, const char* layerName, const std::string& meshName)
            {

#if 1
                FBXLayerData<T> src;
                src.mFbxMesh = nullptr;
                src.mElement = &element;
                src.mIndices = indices;
                src.mIndexCount = indexCount;
                src.mPolygonSizes = polygonSizes;
                src.mPolygonCount = nPolygonCount;
                src.mVertexCount = vertexCount;
                src.mLayerName = layerName;
                src.mMeshName = &meshName;

                FBXLayerConvert<T2, T> dst(output);
                dst << src;

#else

                const FbxLayerElement::EReferenceMode referenceMode = element.GetReferenceMode();

                if (referenceMode == FbxLayerElement::eDirect)
                {
                    ExtractWedgeLayerData1<T, T2, GetValueAtDirect<T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
                }
                else if (referenceMode == FbxLayerElement::eIndexToDirect)
                {
                    ExtractWedgeLayerData1<T, T2, GetValueAtIndex <T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
                }
                else
                {
                    LOG_EDITOR_ERROR("Unsupported wedge reference mode type. Please report this bug.\n");
                }

#endif
                /*
#if 1
                std::vector<T2> testvec;
                FBXLayerData<T> src;
                src.mFbxMesh = nullptr;
                src.mElement = &element;
                src.mIndices = indices;
                src.mIndexCount = indexCount;
                src.mPolygonSizes = polygonSizes;
                src.mPolygonCount = nPolygonCount;
                src.mVertexCount = vertexCount;
                src.mLayerName = layerName;
                src.mMeshName = &meshName;

                FBXLayerConvert<T2, T> dst(testvec);
                dst << src;

#else
                
                const FbxLayerElement::EReferenceMode referenceMode = element.GetReferenceMode();

                if (referenceMode == FbxLayerElement::eDirect)
                {
                    ExtractWedgeLayerData1<T, T2, GetValueAtDirect<T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
                }
                else if (referenceMode == FbxLayerElement::eIndexToDirect)
                {
                    ExtractWedgeLayerData1<T, T2, GetValueAtIndex <T, T2> >(fbxMesh, element, output, indices, indexCount, polygonSizes, nPolygonCount, vertexCount, layerName, meshName);
                }
                else
                {
                    LOG_EDITOR_ERROR("Unsupported wedge reference mode type. Please report this bug.\n");
                }

                if (output.size() != testvec.size())
                {
                    LOG_EDITOR_ERROR("bug.\n");
                }
                else
                {
                    for(int i = 0;i< testvec.size();i++)
                    { 
                        if (testvec[i] != output[i])
                        {
                            LOG_EDITOR_ERROR("bug.\n");
                        }
                    }
                }
#endif
*/
            }

            int ImportUVs(FbxMesh& fbxMesh, FBXImportMesh& mesh, int* indices, int nPolygonIndexCount, UInt32* polygonSizes, int nPolygonCount, int nVertexCount, const std::string& strMeshName)
            {
                // Import up to 2 UV sets
                int uvSetIndex = 0;

                // First just try importing diffuse UVs from separate layers
                // (Maya exports that way)
                FbxLayerElementUV* firstUVSet = nullptr;
                FbxLayer* firstUVLayer = nullptr;
                for (int i = 0; i < fbxMesh.GetLayerCount(); i++)
                {
                    FbxLayer* lay = fbxMesh.GetLayer(i);
                    if (!lay)
                        continue;
                    FbxLayerElementUV* uvs = lay->GetUVs();
                    if (!uvs)
                        continue;

                    //printf_console( "GetUVSetCount: %d\n", lay->GetUVSetCount() );
                    //FbxArray<FbxLayerElement::ELayerElementType> setChannels = lay->GetUVSetChannels();
                    //printf_console( "UVSetChannels: %d\n", setChannels.GetCount() );

                    if (!firstUVSet)
                    {
                        firstUVSet = uvs;
                        firstUVLayer = lay;
                    }

                    //printf_console("UV Set: %d DIRECT ARRAY\n", uvsetIndex);
                    //for (int i = 0; i < uvs->GetDirectArray().GetCount(); i++)
                    //{
                    //printf_console("uv %d: %f %f\n", i, uvs->GetDirectArray().GetAt(i)[0], uvs->GetDirectArray().GetAt(i)[1]);
                    //}

                    ExtractWedgeLayerData(nullptr, *uvs, mesh.uvs[uvSetIndex], indices, nPolygonIndexCount, polygonSizes, nPolygonCount, nVertexCount, "UV coordinates", strMeshName);

                    //printf_console("UV Set: %d AFTER CONVERSION\n", uvsetIndex);
                    //for (int i = 0; i < mesh.uvs[uvsetIndex].size(); i++)
                    //{
                    //printf_console("uv %d: %f %f\n", i, mesh.uvs[uvsetIndex][i].x, mesh.uvs[uvsetIndex][i].y);
                    //}

                    uvSetIndex++;
                    if (uvSetIndex == 2)
                        break;
                }

                // If we have received one UV set, check whether the same layer contains an emissive UV set
                // that is different from diffuse UV set.
                // 3dsmax FBX exporters don't export UV sets as different layers, instead for lightmapping usually
                // a material is set up to have lightmap (2nd UV set) as self-illumination slot, and main texture
                // (1st UV set) as diffuse slot.
                if (uvSetIndex == 1 && firstUVSet)
                {
                    FbxLayerElementUV* secondaryUVs = nullptr;
                    for (int i = FbxLayerElement::eTextureEmissive; i < FbxLayerElement::eTypeCount; i++)
                    {
                        secondaryUVs = firstUVLayer->GetUVs((FbxLayerElement::EType)i);
                        if (secondaryUVs)
                            break;
                    }

                    if (secondaryUVs)
                    {
                        ExtractWedgeLayerData(nullptr, *secondaryUVs, mesh.uvs[uvSetIndex], indices, nPolygonIndexCount, polygonSizes, nPolygonCount, nVertexCount, "UV coordinates", strMeshName);
                        uvSetIndex++;
                    }
                }

                ValidateUVs(mesh, strMeshName);
                return uvSetIndex;
            }
#if CROSSENGINE_OSX
            std::string UnicodeToUTF8(const std::wstring& wstr)
            {
                std::string ret;
                try {
                    std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
                    ret = wcv.to_bytes(wstr);
                } catch (const std::exception & e) {
                    std::cerr << e.what() << std::endl;
                }
                return ret;
            }
            std::wstring UTF8ToUnicode(const std::string & str)
             {
                 std::wstring ret;
                 try {
                     std::wstring_convert<std::codecvt_utf8<wchar_t>> wcv;
                     ret = wcv.from_bytes(str);
                 } catch (const std::exception & e) {
                     std::cerr << e.what() << std::endl;
                 }
                 return ret;
             }
             std::string UnicodeToANSI(const std::wstring & wstr)
             {
                 std::string ret;
                 std::mbstate_t state = {};
                 const wchar_t *src = wstr.data();
                 size_t len = std::wcsrtombs(nullptr, &src, 0, &state);
                 if (static_cast<size_t>(-1) != len) {
                     std::unique_ptr< char [] > buff(new char[len + 1]);
                     len = std::wcsrtombs(buff.get(), &src, len, &state);
                     if (static_cast<size_t>(-1) != len) {
                         ret.assign(buff.get(), len);
                     }
                 }
                 return ret;
             }
             std::wstring ANSIToUnicode(const std::string & str)
             {
                 std::wstring ret;
                 std::mbstate_t state = {};
                 const char *src = str.data();
                 size_t len = std::mbsrtowcs(nullptr, &src, 0, &state);
                 if (static_cast<size_t>(-1) != len) {
                     std::unique_ptr< wchar_t [] > buff(new wchar_t[len + 1]);
                     len = std::mbsrtowcs(buff.get(), &src, len, &state);
                     if (static_cast<size_t>(-1) != len) {
                         ret.assign(buff.get(), len);
                     }
                 }
                 return ret;
             }
             std::string UTF8ToANSI(const std::string & str)
             {
                 return UnicodeToANSI(UTF8ToUnicode(str));
             }
             std::string ANSIToUTF8(const std::string & str)
             {
                 return UnicodeToUTF8(ANSIToUnicode(str));
             }
             
             bool AsciiToUTF8(std::string& name)
             {
                 std::string t = ANSIToUTF8(name);
                 name.clear();
                 name.append(t.c_str());
                 return true;
             }
#else
            
            bool AsciiToUTF8(std::string& name)
            {
                if (name.empty())
                    return true;

                bool result = false;
                int bufferSize = static_cast<int>(name.size()) * 4 + 1;
                wchar_t* wideBuffer = new wchar_t[bufferSize];
                if (MultiByteToWideChar(CP_ACP, 0, name.c_str(), -1, wideBuffer, bufferSize))
                {
                    char* buffer = new char[bufferSize];
                    if (WideCharToMultiByte(CP_UTF8, 0, wideBuffer, -1, buffer, bufferSize, nullptr, nullptr))
                    {
                        name = buffer;
                        result = true;
                    }
                    SAFE_DELETE_ARRAY(buffer);
                }
                SAFE_DELETE_ARRAY(wideBuffer);
                return result;
            }
#endif
            std::string ConvertAsciiToUTF8(const std::string& str)
            {
                std::string s = str;
                if (!AsciiToUTF8(s))
                {
                    ReportWarning("Failed to convert string '%s' to UTF8.\n", str.c_str());
                    s = "";
                }

                return s;
            }

            void Replace(std::string& str, char findChar, char newChar)
            {
                for (std::string::iterator i = str.begin(); i != str.end(); ++i)
                {
                    if (*i == findChar)
                    {
                        *i = newChar;
                    }
                }
            }

            void ConvertFBXTexture(FbxTexture* pBaseTexture, std::string& strMaterialName, FBXImportTexture& FBXImportTexture)
            {
                FbxFileTexture* pFbxFileTexture = FbxCast<FbxFileTexture>(pBaseTexture);

                if (pFbxFileTexture)
                {
                    if (strMaterialName.empty())
                    {
                        strMaterialName = pFbxFileTexture->GetNameWithoutNameSpacePrefix();
                    }

                    FBXImportTexture.path = !pFbxFileTexture->GetFileName() ? "" : ConvertAsciiToUTF8(pFbxFileTexture->GetFileName());
                    FBXImportTexture.relativePath = !pFbxFileTexture->GetRelativeFileName() ? "" : ConvertAsciiToUTF8(pFbxFileTexture->GetRelativeFileName());

                    // The file has fucked up mac style path names (Lightwave seems to export those!)
                    // fix them up
                    if (FBXImportTexture.path.find(':') != std::string::npos && FBXImportTexture.path.find('/') == std::string::npos)
                    {
                        Replace(FBXImportTexture.path, ':', '/');
                        Replace(FBXImportTexture.relativePath, ':', '/');
                    }

                    // TODO(maxwan) : this seems to be a result of the loop above...
                    // The file has fucked up win style path names.
                    if (FBXImportTexture.path.find('\\') != std::string::npos && (FBXImportTexture.path.find('/') == std::string::npos || FBXImportTexture.path.rfind('/') == 1))
                    {
                        Replace(FBXImportTexture.path, '\\', '/');
                        Replace(FBXImportTexture.relativePath, '\\', '/');
                    }

                    ConvertSeparators(FBXImportTexture.path);
                    ConvertSeparators(FBXImportTexture.relativePath);

                    FBXImportTexture.offset.x = static_cast<float>(pFbxFileTexture->GetTranslationU());
                    FBXImportTexture.offset.y = static_cast<float>(pFbxFileTexture->GetTranslationV());
                    FBXImportTexture.scale.x = static_cast<float>(pFbxFileTexture->GetScaleU());
                    FBXImportTexture.scale.y = static_cast<float>(pFbxFileTexture->GetScaleV());
                }
            }

            void SetAlpha(FbxPropertyT<FbxDouble> value, FBXImportMaterial& material)
            {
                if (value.IsValid())
                {
                    float alpha = 1 - static_cast<float>(value.Get());

                    // HACK: we do this, because it looks like a bunch of old software write bogus information into 
                    // transparency factor, so we need to treat it as opaque. Also I've seen same HACK in examples by other people...
                    if (alpha == 0)
                    {
                        alpha = 1;
                    }

                    material.diffuse.a = material.ambient.a = alpha;
                }
            }

            int ConvertFBXToFBXImportMaterial(FBXMaterialInfo fbxMaterialInfo, FBXImportScene& scene, FBXMaterialLookup& fbxMaterialLookup)
            {
                FBXMaterialLookup::iterator found = fbxMaterialLookup.find(fbxMaterialInfo);
                if (found != fbxMaterialLookup.end())
                    return found->second;

                fbxMaterialLookup[fbxMaterialInfo] = static_cast<int>(scene.materials.size());
                scene.materials.push_back(FBXImportMaterial());
                FBXImportMaterial& material = scene.materials.back();

                material.fbxMaterial = std::make_shared<FBXMaterial>();
                
                if (fbxMaterialInfo.pFbxSurfaceMaterial)
                {
                    material.fbxMaterial->SetMaterial(fbxMaterialInfo.pFbxSurfaceMaterial);
                    material.name = fbxMaterialInfo.pFbxSurfaceMaterial->GetNameWithoutNameSpacePrefix();

                    if (fbxMaterialInfo.pFbxSurfaceMaterial->GetClassId().Is(FbxSurfaceLambert::ClassId))
                    {
                        FbxSurfaceLambert* pLambertMaterial = reinterpret_cast<FbxSurfaceLambert*>(fbxMaterialInfo.pFbxSurfaceMaterial);
                        ConvertFBXColorProperty(pLambertMaterial->Diffuse, material.diffuse);
                        ConvertFBXColorProperty(pLambertMaterial->Ambient, material.ambient);
                    }
                    else if (fbxMaterialInfo.pFbxSurfaceMaterial->GetClassId().Is(FbxSurfacePhong::ClassId))
                    {
                        FbxSurfacePhong* pPhongMaterial = reinterpret_cast<FbxSurfacePhong*>(fbxMaterialInfo.pFbxSurfaceMaterial);
                        ConvertFBXColorProperty(pPhongMaterial->Diffuse, material.diffuse);
                        ConvertFBXColorProperty(pPhongMaterial->Ambient, material.ambient);
                    }
                    else
                    {
                        // Unknown material
                        FbxProperty DiffuseColorProp = fbxMaterialInfo.pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sDiffuse);
                        FbxProperty AmbientColorProp = fbxMaterialInfo.pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sAmbient);
                        ConvertFBXColorProperty(DiffuseColorProp, material.diffuse);
                        ConvertFBXColorProperty(AmbientColorProp, material.ambient);
                    }

                    // Assigning alpha from transparencyFactor
                    FbxProperty propTransaparencyFactor = fbxMaterialInfo.pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sTransparencyFactor);
                    if (propTransaparencyFactor.IsValid())
                    {
                        SetAlpha(propTransaparencyFactor, material);
                    }

                    FbxProperty propTransaparency = fbxMaterialInfo.pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sTransparentColor);
                    if (material.diffuse.a == 1 && propTransaparency.IsValid())
                    {
                        SetAlpha(propTransaparency, material);
                    }
                }

                ConvertFBXTexture(fbxMaterialInfo.pFbxTexture, material.name, material.texture);
                ConvertFBXTexture(fbxMaterialInfo.pNormalMap, material.name, material.normalMap);
                material.hasTransparencyTexture = fbxMaterialInfo.bHasTransparencyTexture;

                return static_cast<int>(scene.materials.size()) - 1;
            }

            void SetMaterial(FBXMaterialInfo& fbxMaterialInfo, FbxSurfaceMaterial* pNewFbxSurfaceMaterial, bool& bHasMaterialClashes)
            {
                if (!fbxMaterialInfo.pFbxSurfaceMaterial)
                {
                    fbxMaterialInfo.pFbxSurfaceMaterial = pNewFbxSurfaceMaterial;
                }
                else
                {
                    bHasMaterialClashes = true;
                    //ReportWarning("Material is already set to '%s'; new material '%s'.\n", wrapper.material->GetName(), newMaterial ? newMaterial->GetName() : "<null>");
                }
            }

            FbxFileTexture* GetTexture(const FbxProperty& propDiffuse)
            {
                FbxFileTexture* pResTexture = nullptr;

                const int lLayeredTextureCount = propDiffuse.GetSrcObjectCount<FbxLayeredTexture>();
                if (lLayeredTextureCount > 0)
                {
                    FbxLayeredTexture* pLayeredTexture = propDiffuse.GetSrcObject<FbxLayeredTexture>();

                    //std::string layeredTextureName = lLayeredTexture->GetNameWithoutNameSpacePrefix();

                    const int cnNbTextures = pLayeredTexture->GetSrcObjectCount<FbxFileTexture>();
                    //printf_console( "lNbTextures = %d\n", lNbTextures );
                    if (cnNbTextures > 0)
                    {
                        //let's say I want the blendmode of that layered texture:
                        //FbxLayeredTexture::EBlendMode lBlendMode;
                        //lLayeredTexture->GetTextureBlendMode(0, lBlendMode);
                        pResTexture = pLayeredTexture->GetSrcObject<FbxFileTexture>();

                        //std::string textureName = lTexture->GetRelativeFileName ();

                        // TODO(maxwan): Add support for blending multiple textures
                    }
                }
                else
                {
                    //Get first texture connected to property. Anyway, there shouldn't be more than one.
                    FbxFileTexture* pFbxFileTexture = propDiffuse.GetSrcObject<FbxFileTexture>();
                    if (pFbxFileTexture)
                    {
                        pResTexture = pFbxFileTexture;
                    }
                }

                return pResTexture;
            }

            void ConvertFBXMeshMaterials(FbxManager* pFbxSdkManager, FbxScene& fbxScene, FbxNode& fbxNode, FbxMesh& fbxMesh, const std::string& meshName, 
                FBXImportMesh& mesh, FBXImportScene& scene, int nPolygonCount, FBXMaterialLookup& fbxMaterialLookup)
            {
                // setup material import
                std::vector<FBXMaterialInfo> vecMaterials;
                vecMaterials.resize(nPolygonCount);

                bool bHasMaterialClashes = false;

                for (int nLayerIndex = 0, size = fbxMesh.GetLayerCount(); nLayerIndex < size; ++nLayerIndex)
                {
                    FbxLayer* pFbxLayer = fbxMesh.GetLayer(nLayerIndex);

                    // Assign fbx materials to the per face material list
                    FbxLayerElementMaterial* materialLayer = pFbxLayer ? pFbxLayer->GetMaterials() : nullptr;

                    if (materialLayer)
                    {
                        if (materialLayer->GetMappingMode() == FbxLayerElement::eByPolygon)
                        {
                            // Per polygon -> global index lookups
                            if (materialLayer->GetReferenceMode() == FbxLayerElement::eIndex)
                            {
                                for (int i = 0; i < nPolygonCount; i++)
                                {
                                    SetMaterial(vecMaterials[i], fbxScene.GetMaterial(materialLayer->GetIndexArray().GetAt(i)), bHasMaterialClashes);
                                }
                            }
                            // Per polygon index -> material ptrs lookup
                            else if (materialLayer->GetReferenceMode() == FbxLayerElement::eIndexToDirect)
                            {
                                int lNbMaterial = fbxNode.GetSrcObjectCount<FbxSurfaceMaterial>();
                                int matIndexArraySize = materialLayer->GetIndexArray().GetCount();
                                int clampSize = (std::min)(nPolygonCount, matIndexArraySize);
                                for (int i = 0; i < clampSize; ++i)
                                {
                                    int index = materialLayer->GetIndexArray().GetAt(i);
                                    if (index >= 0 && index < lNbMaterial)
                                    {
                                        SetMaterial(vecMaterials[i], fbxNode.GetMaterial(index), bHasMaterialClashes);
                                    }
                                }
                            }
                            // Per polygon direct material lookup
                            else if (materialLayer->GetReferenceMode() == FbxLayerElement::eDirect)
                            {
                                mesh.materials.resize(nPolygonCount);
                                int clampCount = (std::min)(nPolygonCount, fbxNode.GetMaterialCount());
                                for (int i = 0; i < clampCount; i++)
                                {
                                    //vecMaterials[i].material = (FbxSurfaceMaterial*)fbxNode.GetSrcObject(FbxSurfaceMaterial::ClassId, i);
                                    SetMaterial(vecMaterials[i], fbxNode.GetMaterial(i), bHasMaterialClashes);
                                }
                            }
                            else
                            {
                                ReportError("Unsupported material reference mode!\n");
                            }
                        }
                        else if (materialLayer->GetMappingMode() == FbxLayerElement::eAllSame)
                        {
                            // Per object -> global material index lookup
                            if (materialLayer->GetReferenceMode() == FbxLayerElement::eIndex)
                            {
                                if (materialLayer->GetIndexArray().GetCount())
                                {
                                    //FbxSurfaceMaterial* mat = (FbxSurfaceMaterial*)fbxNode.GetSrcObject(FbxSurfaceMaterial::ClassId, materialLayer->GetIndexArray().GetFirst());
                                    FbxSurfaceMaterial* mat = fbxScene.GetMaterial(materialLayer->GetIndexArray().GetFirst());
                                    for (int i = 0; i < nPolygonCount; i++)
                                    {
                                        SetMaterial(vecMaterials[i], mat, bHasMaterialClashes);
                                    }
                                }
                            }
                            // Per object -> index to texture ptrs lookup
                            else if (materialLayer->GetReferenceMode() == FbxLayerElement::eIndexToDirect)
                            {
                                if (materialLayer->GetIndexArray().GetCount() && fbxNode.GetMaterialCount())
                                {
                                    //FbxSurfaceMaterial* mat = (FbxSurfaceMaterial*)fbxNode.GetSrcObject(FbxSurfaceMaterial::ClassId, materialLayer->GetIndexArray().GetFirst());
                                    FbxSurfaceMaterial* mat = fbxNode.GetMaterial(materialLayer->GetIndexArray().GetFirst());
                                    for (int i = 0; i < nPolygonCount; i++)
                                    {
                                        SetMaterial(vecMaterials[i], mat, bHasMaterialClashes);
                                    }
                                }
                            }
                            // Per object -> direct texture ptrs
                            else if (materialLayer->GetReferenceMode() == FbxLayerElement::eDirect)
                            {
                                if (fbxNode.GetMaterialCount())
                                {
                                    //FbxSurfaceMaterial* mat = (FbxSurfaceMaterial*)fbxNode.GetSrcObject(FbxSurfaceMaterial::ClassId, 0);
                                    FbxSurfaceMaterial* mat = fbxNode.GetMaterial(0);
                                    for (int i = 0; i < nPolygonCount; i++)
                                    {
                                        SetMaterial(vecMaterials[i], mat, bHasMaterialClashes);
                                    }
                                }
                            }
                            else
                            {
                                ReportError("Unsupported material reference mode! Please Report this bug.\n");
                            }
                        }
                        else if (materialLayer->GetMappingMode() != FbxLayerElement::eNone)
                        {
                            ReportError("Unsupported material wedge reference mode type! Please Report this bug.\n");
                            //printf_console ("unsupported mapping mode material layer %d\n", textureLayer->GetMappingMode ());
                        }
                    }

                    // TODO(maxwan) : this doesn't seem to have anything todo with polygons, so we should gather all unique materials first and then extract necessary data
                    // Grab textures for vecMaterials
                    for (int i = 0; i < nPolygonCount; i++)
                    {
                        FbxSurfaceMaterial* pFbxSurfaceMaterial = vecMaterials[i].pFbxSurfaceMaterial;
                        if (pFbxSurfaceMaterial)
                        {
                            FbxProperty propDiffuse = pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sDiffuse);
                            if (propDiffuse.IsValid())
                            {
                                vecMaterials[i].pFbxTexture = GetTexture(propDiffuse);
                            }

                            FbxProperty propNormalMap = pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sNormalMap);
                            if (propNormalMap.IsValid())
                            {
                                vecMaterials[i].pNormalMap = GetTexture(propNormalMap);
                            }

                            /*if (!vecMaterials[i].pNormalMap)
                            {
                                FbxProperty propBump = lMaterial->FindProperty(FbxSurfaceMaterial::sBump);
                                if (propBump.IsValid())
                                {
                                    vecMaterials[i].pNormalMap = GetTexture(propBump);
                                }
                            }*/

                            FbxProperty propTransaparency = pFbxSurfaceMaterial->FindProperty(FbxSurfaceMaterial::sTransparentColor);
                            if (propTransaparency.IsValid())
                            {
                                vecMaterials[i].bHasTransparencyTexture = (GetTexture(propTransaparency) != nullptr);
                            }
                        }
                    }
                }

                mesh.materials.resize(nPolygonCount, -1);
                for (UInt32 i = 0; i < vecMaterials.size(); i++)
                {
                    mesh.materials[i] = ConvertFBXToFBXImportMaterial(vecMaterials[i], scene, fbxMaterialLookup);
                }

                if (bHasMaterialClashes)
                {
                    ReportWarning("Mesh '%s' has overlapping vecMaterials on two layers. Material(s) will be taken from lower layer(s).\n", meshName.c_str());
                }
            }
        };


        /** Returns lower value in a generic way */
        template< class T >
        static T Min(const T A, const T B)
        {
            return (A <= B) ? A : B;
        }

        template< class T >
        class CArray
        {
        public:
            CArray()
            {}
            ~CArray()
            {}
            std::vector<T> mVec;

            void Add(T v)
            {
                AddUnique(v);
            }
            void AddUnique(T v)
            {
                mVec.push_back(v);
            }

            void Swap(int v1,int v2)
            {
                T vTmp   = mVec[v1];
                mVec[v1] = mVec[v2];
                mVec[v2] = vTmp;
            }
            int Num()
            {
                return (int)mVec.size();
            }

            void AddZeroed(int size)
            {
                mVec.clear();
                mVec.resize(size);
            }

            int Find(T v)
            {
                for (int i = 0; i < mVec.size(); i++)
                {
                    if (v == mVec[i])
                        return i;
                }
                return -1;
            }

            void Empty()
            {
                mVec.clear();
            }

            T& operator[](int i)
            {
                if (i >= 0 && i < mVec.size())
                {
                    return mVec[i];
                }

                assert(false);
                return mVec[0];
            }
        };    

        //UE4 CODE ---------- START ------------------------------------------------
        // Wraps some common code useful for multiple fbx import code path
        struct FbxUvs
        {
            // constructor
            FbxUvs(FbxMesh* Mesh)
                : UniqueUVCount(0)
            {
                //
                //store the UVs in arrays for fast access in the later looping of triangles 
                //
                // mapping from UVSets to Fbx LayerElementUV
                // Fbx UVSets may be duplicated, remove the duplicated UVSets in the mapping 
                int LayerCount = Mesh->GetLayerCount();
                if (LayerCount > 0)
                {
                    int UVLayerIndex;
                    for (UVLayerIndex = 0; UVLayerIndex < LayerCount; UVLayerIndex++)
                    {
                        FbxLayer* lLayer = Mesh->GetLayer(UVLayerIndex);
                        int UVSetCount = lLayer->GetUVSetCount();
                        if (UVSetCount)
                        {
                            FbxArray<FbxLayerElementUV const*> EleUVs = lLayer->GetUVSets();
                            for (int UVIndex = 0; UVIndex < UVSetCount; UVIndex++)
                            {
                                FbxLayerElementUV const* ElementUV = EleUVs[UVIndex];
                                if (ElementUV)
                                {
                                    const char* UVSetName = ElementUV->GetName();
                                    std::string LocalUVSetName = UVSetName;
                                    if (LocalUVSetName.size() == 0)
                                    {
                                        LocalUVSetName = "UVmap_" + std::to_string(UVLayerIndex);
                                    }

                                    UVSets.AddUnique(LocalUVSetName);
                                }
                            }
                        }
                    }
                }


                // If the the UV sets are named using the following format (UVChannel_X; where X ranges from 1 to 4)
                // we will re-order them based on these names.  Any UV sets that do not follow this naming convention
                // will be slotted into available spaces.
                if (UVSets.Num())
                {
                    for (int ChannelNumIdx = 0; ChannelNumIdx < 4; ChannelNumIdx++)
                    {
                        std::string ChannelName = "UVChannel_" + std::to_string(ChannelNumIdx + 1);
                        int SetIdx = UVSets.Find(ChannelName);

                        // If the specially formatted UVSet name appears in the list and it is in the wrong spot,
                        // we will swap it into the correct spot.
                        if (SetIdx != -1 && SetIdx != ChannelNumIdx)
                        {
                            // If we are going to swap to a position that is outside the bounds of the
                            // array, then we pad out to that spot with empty data.
                            for (int ArrSize = UVSets.Num(); ArrSize < ChannelNumIdx + 1; ArrSize++)
                            {
                                UVSets.Add("");
                            }
                            //Swap the entry into the appropriate spot.
                            UVSets.Swap(SetIdx, ChannelNumIdx);
                        }
                    }
                }
            }

            void Phase2(FbxMesh* Mesh)
            {
                //
                //store the UVs in arrays for fast access in the later looping of triangles 
                //
                UniqueUVCount = UVSets.Num();
                if (UniqueUVCount > 0)
                {
                    LayerElementUV.AddZeroed(UniqueUVCount);
                    UVReferenceMode.AddZeroed(UniqueUVCount);
                    UVMappingMode.AddZeroed(UniqueUVCount);
                }
                for (int UVIndex = 0; UVIndex < UniqueUVCount; UVIndex++)
                {
                    LayerElementUV[UVIndex] = NULL;
                    for (int UVLayerIndex = 0, LayerCount = Mesh->GetLayerCount(); UVLayerIndex < LayerCount; UVLayerIndex++)
                    {
                        FbxLayer* lLayer = Mesh->GetLayer(UVLayerIndex);
                        int UVSetCount = lLayer->GetUVSetCount();
                        if (UVSetCount)
                        {
                            FbxArray<FbxLayerElementUV const*> EleUVs = lLayer->GetUVSets();
                            for (int FbxUVIndex = 0; FbxUVIndex < UVSetCount; FbxUVIndex++)
                            {
                                FbxLayerElementUV const* ElementUV = EleUVs[FbxUVIndex];
                                if (ElementUV)
                                {
                                    const char* UVSetName = ElementUV->GetName();
                                    std::string LocalUVSetName = UVSetName;
                                    if (LocalUVSetName.size() == 0)
                                    {
                                        LocalUVSetName = "UVmap_" + std::to_string(UVLayerIndex);
                                    }
                                    if (LocalUVSetName == UVSets[UVIndex])
                                    {
                                        LayerElementUV[UVIndex] = ElementUV;
                                        UVReferenceMode[UVIndex] = ElementUV->GetReferenceMode();
                                        UVMappingMode[UVIndex] = ElementUV->GetMappingMode();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }

                if (UniqueUVCount > 8)
                {
                    //REPORT "Error_TooMuchUVChannel", "Reached the maximum number of UV Channels for a Static Mesh({0}) - discarding {1} UV Channels"
                }

                UniqueUVCount = Min<int>(UniqueUVCount, 8);
            }

            int FindLightUVIndex()
            {
                // See if any of our UV set entry names match LightMapUV.
                for (int UVSetIdx = 0; UVSetIdx < UVSets.Num(); UVSetIdx++)
                {
                    if (UVSets[UVSetIdx] == "LightMapUV")
                    {
                        return UVSetIdx;
                    }
                }

                // not found
                return -1;
            }

            // @param FaceCornerIndex usually TriangleIndex * 3 + CornerIndex but more complicated for mixed n-gons
            int ComputeUVIndex(int UVLayerIndex, int lControlPointIndex, int FaceCornerIndex)
            {
                int UVMapIndex = (UVMappingMode[UVLayerIndex] == FbxLayerElement::eByControlPoint) ? lControlPointIndex : FaceCornerIndex;

                int Ret;

                if (UVReferenceMode[UVLayerIndex] == FbxLayerElement::eDirect)
                {
                    Ret = UVMapIndex;
                }
                else
                {
                    FbxLayerElementArrayTemplate<int>& Array = LayerElementUV[UVLayerIndex]->GetIndexArray();
                    Ret = Array.GetAt(UVMapIndex);
                }

                return Ret;
            }

            // todo: is that needed? could the dtor do it?
            void Cleanup()
            {
                //
                // clean up.  This needs to happen before the mesh is destroyed
                //
                LayerElementUV.Empty();
                UVReferenceMode.Empty();
                UVMappingMode.Empty();
            }

            CArray<std::string> UVSets;
            CArray<FbxLayerElementUV const*> LayerElementUV;
            CArray<FbxLayerElement::EReferenceMode> UVReferenceMode;
            CArray<FbxLayerElement::EMappingMode> UVMappingMode;
            int UniqueUVCount;
        };      
    }
}
