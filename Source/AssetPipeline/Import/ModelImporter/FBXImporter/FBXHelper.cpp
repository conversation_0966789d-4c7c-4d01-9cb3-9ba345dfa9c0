#include <algorithm>

#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXHelper.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImportData.h"
#include "AssetPipeline/Import/ModelImporter/FBXImporter/FBXImportUtility.h"
#include "AssetPipeline/Import/ModelImporter/MeshDescription.h"

namespace cross { namespace editor {

    bool FBXHelper::AreVerticesEqual(MeshBuildVertex const& v1, MeshBuildVertex const& v2, float comparisonThreshold)
    {
        if (!PointsEqual(v1.Position, v2.Position, comparisonThreshold) ||
            !NormalsEqual(v1.Normal, v2.Normal) ||
            !TangentEqual(v1.Tangent, v2.Tangent) ||
            v1.Color != v2.Color)
        {
            return false;
        }

        for (auto i = 0; i < MAX_UV_SUPPORT; ++i)
        {
            if (!UVsEqual(v1.UVs[i], v2.UVs[i]))
            {
                return false;
            }
        }

        return true;
    }

    bool FBXHelper::AreVerticesPosEqual(MeshBuildVertex const& v1, MeshBuildVertex const& v2, float comparisonThreshold)
    {
        if (!PointsEqual(v1.Position, v2.Position, comparisonThreshold))
        {
            return false;
        }

        return true;
    }

    void FBXHelper::FindOverlappingCorners(const MeshDescription& meshDescription, float comparisonThreshold, OverlappingCorners& outputCorners)
    {
        const UInt32 triangleNum = meshDescription.GetTriangleNum();
        UInt32 wedgeNum = triangleNum * 3;

        std::vector<IndexAndZ> vertIndexAndZ;
        vertIndexAndZ.reserve(wedgeNum);

        const auto& triangleInstances = meshDescription.GetTriangleInstances();
        for (const auto& triangleView : triangleInstances)
        {
            for (auto corner = 0u; corner < 3; ++corner)
            {
                MeshDescription::VertexInstanceID vertexInstanceID = triangleView[corner];
                MeshDescription::VertexID vertexID = meshDescription.GetVertexInstanceVertex(vertexInstanceID);
                const auto& position = meshDescription.GetRemappedVertexPosition(vertexID);

                vertIndexAndZ.emplace_back(vertexInstanceID, position, vertexID);
            }
        }

        std::sort(vertIndexAndZ.begin(), vertIndexAndZ.end());

        for (auto i = 0u; i < vertIndexAndZ.size(); ++i)
        {
            for (auto j = i + 1; j < vertIndexAndZ.size(); ++j)
            {
                if (MathUtils::Abs(vertIndexAndZ[j].Z - vertIndexAndZ[i].Z) > comparisonThreshold)
                {
                    break;
                }

                const Float3& positionA = *(vertIndexAndZ[i].OriginVector);
                const Float3& positionB = *(vertIndexAndZ[j].OriginVector);

                if (PointsEqual(positionA, positionB, comparisonThreshold))
                {
                    outputCorners.Add(vertIndexAndZ[i].Index, vertIndexAndZ[j].Index);
                    outputCorners.Add(vertIndexAndZ[j].Index, vertIndexAndZ[i].Index);
                }
            }
        }
    }

    void FBXHelper::ValidateSkin(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName)
    {
        ValidateIdenticalWeights(influences, boneCount, meshName);
        ValidateBoneWeightRanges(influences, boneCount, meshName);
    }

    // checks if all indices and weights are the same
    void FBXHelper::ValidateIdenticalWeights(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName)
    {
        if (influences.empty())
            return;

        const SkinBuildVertex& firstBI = influences[0];

        // checking if weights are the same on first bone
        for (int j = 1; j < 4; ++j)
        {
            if (firstBI.Weights[0] != firstBI.Weights[j])
                return;
        }

        // checking if weights and indices on other bones match the ones on the first bone
        for (int i = 1, size = static_cast<int>(influences.size()); i < size; ++i)
        {
            const SkinBuildVertex& bi = influences[i];
            for (int j = 0; j < 4; ++j)
            {
                if (bi.BoneIDs[j] != firstBI.BoneIDs[j] || bi.Weights[j] != firstBI.Weights[j])
                    return;
            }
        }

        ReportWarning("All vertices are affected by same bones (%d, %d, %d, %d) and same weights (%f, %f, %f, %f) on mesh '%s'. Bone count: %d; vertex count: %d.\n",
                      firstBI.BoneIDs[0],
                      firstBI.BoneIDs[1],
                      firstBI.BoneIDs[2],
                      firstBI.BoneIDs[3],
                      firstBI.Weights[0],
                      firstBI.Weights[1],
                      firstBI.Weights[2],
                      firstBI.Weights[3],
                      meshName,
                      boneCount,
                      influences.size());
    }

    // checks if all indices and weigths are in range
    void FBXHelper::ValidateBoneWeightRanges(const std::vector<SkinBuildVertex>& influences, const int boneCount, const char* meshName)
    {
        const int kMaxInvalidCount = 10;
        int invalidCount = 0;
        std::ostringstream oss;

        // checking that weights and indices are in range
        for (int i = 0, size = static_cast<int>(influences.size()); i < size; ++i)
        {
            const SkinBuildVertex& bi = influences[i];

            std::ostringstream ossBI;
            for (int j = 0; j < 4; ++j)
            {
                // TODO(maxwan) : this useless, we never run in debug mode!
                // Assert(!IsNAN(bi.weight[j]));
                // Assert(bi.weight[j] >= 0 && bi.weight[j] <= 1);
                // Assert(bi.boneIndex[j] >= 0 && bi.boneIndex[j] < boneCount);

                if (!IsFinite(bi.Weights[j]) || bi.BoneIDs[j] < 0 || bi.BoneIDs[j] > (UInt32)boneCount || bi.Weights[j] < 0 || bi.Weights[j] > 1)
                {
                    if (j > 0)
                    {
                        ossBI << ", ";
                    }

                    ossBI << "(" << bi.BoneIDs[j] << "; " << bi.Weights[j] << ")";
                }
            }

            const std::string invalidBI = ossBI.str();
            if (!invalidBI.empty())
            {
                if (invalidCount < kMaxInvalidCount)
                {
                    if (invalidCount > 0)
                    {
                        oss << "\n";
                    }

                    oss << invalidCount << ": (" << invalidBI << ")";
                }
                ++invalidCount;
            }
        }

        if (invalidCount > 0)
        {
            if (invalidCount > kMaxInvalidCount)
            {
                oss << "\nand so on..";
            }

            ReportWarning("Mesh '%s' (bone count: %d) has invalid %d (out of %d) BoneWeights (bone index; weight): \n%s.\n", meshName, boneCount, invalidCount, influences.size(), oss.str().c_str());
        }
    }

    bool FBXHelper::HasBadNTB(const MeshDescription& meshDescription)
    {
        const SInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();
        if (vertexCount < 1)
        {
            return false;
        }

        bool badTangents = false;
        for (auto i = 0; i < vertexCount; ++i)
        {
            const auto& vertex = meshDescription.GetMeshBuildVertex(i);
            if (vertex.Normal.IsNaN() || vertex.Normal.IsNearlyZero())
            {
                badTangents = true;
            }
            const auto& tangent = vertex.Tangent.XYZ();
            if (tangent.IsNaN() || tangent.IsNearlyZero())
            {
                badTangents = true;
            }
        }

        return badTangents;
    }

    bool FBXHelper::HasNormal(const MeshDescription& meshDescription)
    {
        const SInt32 vertexCount = meshDescription.GetMeshBuildVertexCount();
        if (vertexCount < 1)
        {
            return false;
        }
        return meshDescription.GetMeshBuildVertex(0).Channels & ImportVertexChannel::NORMAL;
    }

    void FBXHelper::GenerateSmoothNormals(const MeshDescription& meshDescription, const std::vector<int> smoothingInfo, std::vector<Float3>& output) 
    {
        const auto& rawIndices = meshDescription.GetRawIndices();

        std::unordered_map<UInt32, SmoothVertex> smoothNormals;

        output.resize(rawIndices.size());

        for (UInt32 faceIndex = 0, offset = 0; faceIndex < meshDescription.GetTriangleNum(); ++faceIndex)
        {
            for (UInt32 cornerIndex = 0; cornerIndex < 3; ++cornerIndex)
            {
                int prev = (cornerIndex > 0 ? cornerIndex - 1 : 2);
                int next = (cornerIndex < 2 ? cornerIndex + 1 : 0);

                UInt32 curIndex = offset + cornerIndex;
                UInt32 preIndex = offset + prev;
                UInt32 postIndex = offset + next;

                Float3 v0 = meshDescription.GetRemappedVertexPosition(rawIndices[preIndex]);
                Float3 v1 = meshDescription.GetRemappedVertexPosition(rawIndices[curIndex]);
                Float3 v2 = meshDescription.GetRemappedVertexPosition(rawIndices[postIndex]);

                Float3 normal = (v0 - v1).Cross(v2 - v1).Normalized();

                auto iter = smoothNormals.find(rawIndices[curIndex]);
                if (iter != smoothNormals.end())
                {
                    iter->second.Add(smoothingInfo[curIndex], normal);
                }
                else
                {
                    SmoothVertex smoothVertex;
                    smoothVertex.Add(smoothingInfo[curIndex], normal);
                    smoothNormals.try_emplace(rawIndices[curIndex], smoothVertex);
                }

                // we will use it if it has no smoothing group at all
                output[curIndex] = normal;
            }

            offset += 3;
        }

        for (auto& kv : smoothNormals)
        {
            kv.second.Normalize();
        }

        for (UInt32 faceIndex = 0, offset = 0; faceIndex < meshDescription.GetTriangleNum(); ++faceIndex)
        {
            for (UInt32 cornerIndex = 0; cornerIndex < 3; ++cornerIndex)
            {
                UInt32 realIndex = offset + cornerIndex;
                if (smoothingInfo[realIndex] != 0)
                {
                    UInt32 vertexID = rawIndices[realIndex];
                    output[offset + cornerIndex] = smoothNormals[vertexID].FindNormal(smoothingInfo[realIndex]);
                }
            }

            offset += 3;
        }
    }

}}   // namespace cross::editor