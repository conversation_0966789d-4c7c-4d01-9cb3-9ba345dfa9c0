#pragma once
#if 0

#include <fbxsdk.h>
#include "FBXImportData.h"
#include "FBXImportUtility.h"
#include "AssetPipeline/Import/ModelImporter/ModelImporter.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
#include "AssetPipeline/Protocol/Model/ImportMeshAssetData.h"

struct FBXImportScene;

namespace cross {
    namespace editor
    {
        class FBXNewImporter : public ModelImporter
        {
        public:
            FBXNewImporter();
            ~FBXNewImporter() = default;

            bool ImportAsset(const std::string& assetFilename, const ModelImportSettings& settings, const std::string& ndaSavePath) override;
            bool CheckAssetName(const char* name) const override;

        private:
            //create a FbxScene from a FBX file
            fbxsdk::FbxScene* CreateFbxScene(const std::string& assetFilename) const;

            /*These several functions process the FBX scene, extracting pointers to mesh and cluster and storing them to different arrays,
            so we don't need to do a DFS each time importing one type of asset*/
            void ClearDataArrays();
            void FillDataArrays(fbxsdk::FbxScene* pFbxScene);
            void FillMeshArray(fbxsdk::FbxScene* pFbxScene);
            void FillClusterArray();

            bool IsChildrenContain(fbxsdk::FbxNodeAttribute::EType searchType, fbxsdk::FbxNode* pRoot) const;
            bool IsNodeAnimated(FbxNode const* pNode, FbxAnimLayer const* pAnimLayer) const;

            //functions to import and serialize skeleton data
            void ImportSkeletonData(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton);
            void ImportSkeletonHierarchy(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton);
            void ImportSkeletonBindpose(fbxsdk::FbxScene* pFbxScene, SkeletonDesc& outSkeleton);

            //functions to import and serialize animation data
            void ImportAllAnimations(fbxsdk::FbxScene* pFbxScene, const SkeletonDesc& skeleton, std::vector<AnimationDesc>& outAnimations);
            std::vector<std::vector<AssetMath::Matrix4x4f>> ConvertBonesRefTransToLocal(std::vector<std::vector<AssetMath::Matrix4x4f>>& allBonesRefTransformationsInAnim, const SkeletonDesc& skeleton);
            void SplitBonesLocalTransformationsToTracks(std::vector<std::vector<AssetMath::Matrix4x4f>>& allBonesLocalTransformationsInAnim, std::vector<AnimationDesc::Track>& trackForAllBones);

            void ImportSkin(
                FbxMesh* pFbxMesh,
                FBXImportMesh& importMesh,
                FBXImportScene& scene,
                const std::map<FbxNode*, FBXImportNode*>& fbxNodeMap,
                const FBXMeshToSharedMeshInfoMap& fbxMeshToInfoMap);

            // functions to import blend shape
            void ImportBlendShapes(FBXMeshToSharedMeshInfoMap& meshMap, FBXImportScene& scene);

            void ImportAssetData(const std::string& assetFilename, ImportScene& iScene);
            auto ImportFBXSceneData(std::string filename, FBXImportScene* fbxImportScene) -> std::pair<FbxScene*, FbxImporter*>;
            void ResetInvalid3dsMaxValue(FbxNode* node);
            void ProcessAnimationCurveAndPivot(FbxScene* scene);
            void ConvertNurbsAndPatchRecursively(FbxManager& mgr, FbxNode* node);
            void ConvertToFBXImportScene(FbxScene& fbx, FBXImportScene& scene);
            void ImportNodesRecursively(
                FbxScene& pFbxScene,
                FbxNode* pFbxNode,
                FBXImportNode& outNode,
                FBXImportScene& scene,
                FBXMeshToSharedMeshInfoMap& fbxMeshToSharedMeshInfoMap,
                FBXMaterialLookup& fbxMaterialLookup, int parentBone = -1);

            void ExtractSceneInfo(FbxImporter& importer, FbxScene& fbxScene, FBXImportInfo& sceneInfo);
            void ConvertFBXMesh(
                FbxManager* pFbxSdkManager,
                FbxScene& fbxScene,
                FbxNode& fbxNode,
                FBXImportMesh& mesh,
                FBXImportScene& scene,
                const bool cbImportNormals,
                const bool cbImportTangets,
                FBXMaterialLookup& fbxMaterialLookup);

            void SplitMesh(const FBXImportMesh& mesh, FBXImportMesh& splitMesh, float splitAngle = 1.0f);
            void RemapVertex(FBXImportMesh& mesh, std::vector<FBXImportMesh>& meshes);
            bool Triangulate(const FBXImportMesh& input, FBXImportMesh& output, bool allowQuads);
            void RemoveDegenerateFaces(FBXImportMesh& mesh);
            void GenerateMikkTSpace(FBXImportMesh& mesh);

            void ImportMaterial(const std::string& assetFilename, const std::string& ndaSavePath);

            bool SplitMeshByMaterial(
                FBXImportScene& scene,
                const FBXImportNode& in_node,
                std::vector<FBXImportNode>& subNodes,
                std::map<int, int>& materialID);

            void MergeMeshWithMaterial(
                ImportNode& rootNode,
                CrossSchema::ImportMeshesT& meshes,
                const std::map<int, int>& materialID,
                int materialCount);

            void InOneFbxNode(
                CrossSchema::ImportMeshesT& meshes,
                std::map<int, int>& sceneMeshMaterialMap,
                std::map<int, int> &meshMaterialMap,
                ImportScene& iScene,
                std::vector<ImportNode>& slibingVector,
                ImportNode& node,
                const FBXImportNode& sceneNode);


            bool SubmeshNameIsPhysicsCollisionBox(const std::string& name);
            bool SubmeshNameIsPhysicsCollisionSphere(const std::string& name);
            bool SubmeshNameIsPhysicsCollisionCapsule(const std::string& name);
            bool SubmeshNameIsPhysicsCollisionConvex(const std::string& name);
            bool SubmeshNameIsPhysicsCollision(const std::string& name);
        private:
            FbxManager* mFbxSdkManager;
            std::string mFbxImportErrors;
            std::string mFbxImportWarnings;
            std::map<FbxNode*, FBXImportNode*> mFBXNodeMap;
            FbxArray<FbxNode*> mFbxNodeArray;
            FBXMeshToSharedMeshInfoMap mFbxMeshToInfoMap;
            std::shared_ptr<MeshAssetData>   mMeshAssetData;

        private:
            std::string mAssetFileName;
            std::string mNdaSavePath;
            std::unordered_map<fbxsdk::FbxNode*, int> mBoneNodeToIDMap;
            std::vector<fbxsdk::FbxMesh*> mMeshArray;
            std::vector<fbxsdk::FbxCluster*> mClusterArray;
            std::vector<fbxsdk::FbxNode*> mSkeletonArray;
            std::unique_ptr<FBXImportScene> mFbxImportScene;
        };
    }
}
#endif
