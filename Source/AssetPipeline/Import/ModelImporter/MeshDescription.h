#pragma once

#include <array>
#include <vector>
#include <unordered_map>
#include "CrossBase/Math/CrossMath.h"
#include "Resource/MeshAssetData.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/ModelImporter/SkinDescription.h"
#include "AssetPipeline/Import/ModelImporter/BlendShapeDescription.h"

namespace cross { namespace editor {

    #define MAX_UV_SUPPORT 4

    struct MeshBuildVertex
    {
        MeshBuildVertex(const Float3& inPosition)
        {
            Position = inPosition;
            std::fill_n(UVs, MAX_UV_SUPPORT, Float2(0.f, 0.f));
        }

        // Raw control point index in Fbx for this MeshBuildVertex
        UInt32 ControlPointID = 0;
        UInt32 UVChannelNum = 0;
        ImportVertexChannel Channels = ImportVertexChannel::POSITION;

        Float3 Position;
        Float3 Normal = Float3::Zero();
        Float4 Tangent = Float4::Zero();
        Float2 UVs[MAX_UV_SUPPORT];
        SInt32 MatID;
        ColorRGBA32 Color;
        Float4 RawColor = Float4::Zero();
    };

    struct VertexNormalGroup
    {
        int smoothingGroup;
        Float3 normal;
        int vertexCount;

        VertexNormalGroup()
            : smoothingGroup(0)
            , normal(Float3::Zero())
            , vertexCount(0)
        {}

        void Add(int sg, const Float3& n, int vc)
        {
            smoothingGroup |= sg;
            normal += n;
            vertexCount += vc;
        }

        void Add(int sg, const Float3& n)
        {
            Add(sg, n, 1);
        }
        
        void Add(const VertexNormalGroup& g)
        {
            Add(g.smoothingGroup, g.normal, g.vertexCount);
        }

        void Normalize()
        {
            normal.Normalize();
        }
    };

    struct SmoothVertex
    {
        std::vector<VertexNormalGroup> groups;

        void Add(int smoothingGroup, const Float3& normal)
        {
            bool found = false;

            for (size_t i = 0; i < groups.size(); ++i)
            {
                if (groups[i].smoothingGroup & smoothingGroup)
                {
                    const bool hasExtraGroups = (smoothingGroup & ~groups[i].smoothingGroup) != 0;

                    if (hasExtraGroups)
                    {
                        for (size_t j = i + 1; j < groups.size(); ++j)
                        {
                            if (groups[j].smoothingGroup & smoothingGroup)
                            {
                                groups[i].Add(groups[j]);
                                groups.erase(groups.begin() + j);
                                --j;
                            }
                        }
                    }

                    groups[i].Add(smoothingGroup, normal);

                    found = true;
                    break;
                }
            }

            if (!found)
            {
                VertexNormalGroup group;
                group.Add(smoothingGroup, normal);

                groups.push_back(group);
            }
        }

        void Normalize()
        {
            for (size_t i = 0; i < groups.size(); ++i)
            {
                groups[i].Normalize();
            }
        }

        Float3 FindNormal(const int smoothingGroup)
        {
            for (size_t i = 0; i < groups.size(); ++i)
            {
                if (groups[i].smoothingGroup & smoothingGroup)
                {
                    for (size_t j = i + 1; j < groups.size(); ++j)
                    {
                        AssertIf(groups[j].smoothingGroup & smoothingGroup);
                    }

                    return groups[i].normal;
                }
            }

            AssertMsg(false, "Couldn't find normal for smoothingGroup {}", smoothingGroup);
            return Float3::Zero();
        }
    };

    class ASSET_API MeshDescription
    {
    public:
        // Actually raw control point's index in Fbx, stored in mVertexInstanceVertices and mRawIndices. Used to index raw control point.
        using VertexID = UInt32;
        // Used to index mVertexInstanceVertices array
        using VertexInstanceID = UInt32;
        using PolygonID = UInt32;
        using TriangleID = UInt32;

        struct TriangleView
        {
            TriangleView(UInt32 id) : TriangleID(id) {}

            const VertexInstanceID& operator [] (UInt32 cornerIndex) const
            {
                Assert(cornerIndex >= 0 && cornerIndex < 3);
                return Data[cornerIndex];
            }

            VertexInstanceID& operator[](UInt32 cornerIndex)
            {
                Assert(cornerIndex >= 0 && cornerIndex < 3);
                return Data[cornerIndex];
            }

            UInt32 TriangleID{ 0 };
            std::array<VertexInstanceID, 3> Data{ 0, 0, 0 };
        };

        MeshDescription(const std::string& name) : mName(name) {};
        ~MeshDescription() = default;

        void Init(UInt32 polygonNums);

        void GenerateMikkTSpace();
        void GenerateMikkTSpaceForTargetShape(ShapeDesc& targetShape);

    public:
        inline void CreateRemappedVertex(const VertexID vertexID, const Float3& remapVertex)
        {
            mVertexPositions.try_emplace(vertexID, remapVertex);
        }

        inline void SetVertexInstanceVertex(const VertexInstanceID instanceID, VertexID vertexID)
        {
            mVertexInstanceVertices[instanceID] = vertexID;
        }

        inline SInt32 AddMeshBuildVertex(const MeshBuildVertex& vertex)
        {
            mMeshBuildVertices.emplace_back(vertex);
            return static_cast<SInt32>(mMeshBuildVertices.size()) - 1;
        }

        inline void ReserveNewVertices(const UInt32 nums)
        {
            mVertexPositions.reserve(nums);
        }

        inline void ReserveNewVertexInstances(const UInt32 nums)
        {
            mVertexInstanceVertices.resize(nums);
        }

        inline void ReserveTriangleInstances(const UInt32 nums)
        {
            mTriangleVertexInstances.reserve(nums);
        }

        inline UInt32 GetTriangleNum() const
        {
            return static_cast<UInt32>(mTriangleVertexInstances.size());
        }

        inline std::vector<TriangleView>& GetTriangleInstancesRef()
        {
            return mTriangleVertexInstances;
        }

        inline const std::vector<TriangleView>& GetTriangleInstances() const
        {
            return mTriangleVertexInstances;
        }

        inline const VertexInstanceID GetTriangleVertexInstance(const TriangleID id, const UInt32 index) const
        {
            Assert(id >= 0 && id < mTriangleVertexInstances.size());
            Assert(index >= 0 && index < 3);
            const TriangleView& triangleView = mTriangleVertexInstances[id];
            return triangleView.Data[index];
        }

        inline const Float3& GetRemappedVertexPosition(const VertexID id) const
        {
            static Float3 InvalidPosition{ -99999.0, -99999.0, -99999.0 };

            mVertexPositions.find(id)->second;
            const auto iter = mVertexPositions.find(id);
            return iter != mVertexPositions.end() ? iter->second : InvalidPosition;
        }

        inline const std::unordered_map<VertexID, Float3>& GetRemappedVertexPositions() const
        {
            return mVertexPositions;
        }

        inline std::unordered_map<VertexID, Float3>& GetRemappedVertexPositionsRef()
        {
            return mVertexPositions;
        }

        inline UInt32 GetVertexCount() const
        {
            return static_cast<UInt32>(mVertexPositions.size());
        }

        inline VertexID GetVertexInstanceVertex(const VertexInstanceID vertexInstanceID) const
        {
            Assert(vertexInstanceID >= 0 && vertexInstanceID < mVertexInstanceVertices.size());
            return mVertexInstanceVertices[vertexInstanceID];
        }

        inline VertexID GetVertexInstanceVertex(TriangleID id, UInt32 corner) const
        {
            VertexInstanceID instanceID = GetTriangleVertexInstance(id, corner);
            return GetVertexInstanceVertex(instanceID);
        }

        inline const VertexID* GetVertexInstanceVertexData() const
        {
            return mVertexInstanceVertices.data();
        }

        inline const std::vector<VertexID>& GetVertexInstanceVertices() const
        {
            return mVertexInstanceVertices;
        }

        inline MeshBuildVertex& GetMeshBuildVertex(VertexID id)
        {
            Assert(id >= 0 && id < mMeshBuildVertices.size());
            return mMeshBuildVertices[id];
        }

        inline const MeshBuildVertex& GetMeshBuildVertex(VertexID id) const
        {
            Assert(id >= 0 && id < mMeshBuildVertices.size());
            return mMeshBuildVertices[id];
        }

        inline MeshBuildVertex& GetMeshBuildVertex(TriangleID id, UInt32 corner)
        {
            VertexID vertexID = GetVertexInstanceVertex(id, corner);
            return mMeshBuildVertices[vertexID];
        }

        inline const MeshBuildVertex& GetMeshBuildVertex(TriangleID id, UInt32 corner) const
        {
            VertexID vertexID = GetVertexInstanceVertex(id, corner);
            return mMeshBuildVertices[vertexID];
        }

        inline const UInt32 GetMeshBuildVertexCount() const
        {
            return static_cast<UInt32>(mMeshBuildVertices.size());
        }

        inline std::vector<MeshBuildVertex>& GetMeshBuildVerticesRef()
        {
            return mMeshBuildVertices;
        }

        inline const std::vector<MeshBuildVertex>& GetMeshBuildVerticesConstRef() const
        {
            return mMeshBuildVertices;
        }

        inline const std::string& GetName() const
        {
            return mName;
        }

        inline void RecordRawIndices(int* data, UInt32 count)
        {
            mRawIndices.reserve(count);
            mRawIndices.insert(mRawIndices.end(), data, data + count);
        }

        inline const std::vector<UInt32>& GetRawIndices() const
        {
            return mRawIndices;
        }

        inline bool IsSkinnedMesh() const
        {
            return mSkinDescription.HasSkin();
        }

        inline void ReserveSkinVertices()
        {
            mSkinDescription.Init(mMeshBuildVertices);
        }

        inline const SkinDescription& GetSkinDescriptionConstRef() const
        {
            return mSkinDescription;
        }

        inline SkinDescription& GetSkinDescriptionRef()
        {
            return mSkinDescription;
        }

        inline BlendShapeDeformer& GetBlendShapeDeformerRef()
        {
            return mBlendShapeDeformer;
        }

        inline const BlendShapeDeformer& GetBlendShapeDeformerConstRef() const
        {
            return mBlendShapeDeformer;
        }

        inline ShapeDesc& GetTargetShape(UInt32 channelIdx, UInt32 shapeIdx)
        {
            Assert(channelIdx >= 0 && channelIdx < mBlendShapeDeformer.Channels.size());
            Assert(shapeIdx >= 0 && shapeIdx < mBlendShapeDeformer.Channels[channelIdx].TargetShapes.size());

            return mBlendShapeDeformer.Channels[channelIdx].TargetShapes[shapeIdx];
        }

        inline DeltaShapeDesc& GetDeltaShape(UInt32 channelIdx, UInt32 shapeIdx)
        {
            Assert(channelIdx >= 0 && channelIdx < mBlendShapeDeformer.Channels.size());
            Assert(shapeIdx >= 0 && shapeIdx < mBlendShapeDeformer.Channels[channelIdx].DeltaShapes.size());

            return mBlendShapeDeformer.Channels[channelIdx].DeltaShapes[shapeIdx];
        }

        inline bool HasBlendShape() const
        {
            return mBlendShapeDeformer.HasBlendShape();
        }

        inline bool HasVertexColor() const
        {
            return mHasVertexColor;
        }

        inline void RecordVertexColor(bool hasVertexColor)
        {
            mHasVertexColor |= hasVertexColor;
        }

        inline void SetMaterialPath(const std::string& path)
        {
            mMaterialPath = path;
        }

        inline const std::string& GetMaterialPath() const
        {
            return mMaterialPath;
        }

        void mergeMeshDescription(MeshDescription& meshDescription)
        {
            auto vertexNum = mVertexPositions.size();
            auto rawIndecesNum = mRawIndices.size();
            auto triangeNum = mTriangleVertexInstances.size();
            auto meshBuildVertexNum = mMeshBuildVertices.size();
            for (SInt32 be = 0; be < meshDescription.mTriangleVertexInstances.size(); be++)
            {
                TriangleView mTriangle = meshDescription.mTriangleVertexInstances[be];
                mTriangle.TriangleID += static_cast<UInt32>(triangeNum);
                mTriangle.Data[0] += static_cast<VertexInstanceID>(rawIndecesNum);
                mTriangle.Data[1] += static_cast<VertexInstanceID>(rawIndecesNum);
                mTriangle.Data[2] += static_cast<VertexInstanceID>(rawIndecesNum);
                this->mTriangleVertexInstances.push_back(mTriangle);
            }
            for (SInt32 be = 0; be < meshDescription.mRawIndices.size(); be++)
            {
                VertexID tmp = meshDescription.mRawIndices[be];
                tmp += static_cast<VertexID>(meshBuildVertexNum);
                this->mRawIndices.push_back(tmp);
            }
            for (auto iter = meshDescription.mVertexPositions.begin(); iter != meshDescription.mVertexPositions.end(); ++iter)
            {
                this->mVertexPositions.insert(std::pair<VertexID, Float3>(iter->first + static_cast<VertexID>(vertexNum), iter->second));
            }
            for (SInt32 be = 0; be < meshDescription.mVertexInstanceVertices.size(); be++)
            {
                VertexID tmp = meshDescription.mVertexInstanceVertices[be];
                tmp += static_cast<VertexID>(meshBuildVertexNum);
                this->mVertexInstanceVertices.push_back(tmp);
            }
            for (SInt32 be = 0; be < meshDescription.mMeshBuildVertices.size(); be++)
            {
                MeshBuildVertex tmp = meshDescription.mMeshBuildVertices[be];
                tmp.ControlPointID += static_cast<UInt32>(vertexNum);
                this->mMeshBuildVertices.push_back(tmp);
            }
        }
    private:
        std::string mName;
        // Three vertices's index for each triangle
        std::vector<TriangleView> mTriangleVertexInstances;
        // Raw index buffer in Fbx
        std::vector<VertexID> mRawIndices;
        // Converted vertex position array, size and order match control point in Fbx
        //std::vector<Float3> mVertexPositions;
        std::unordered_map<VertexID, Float3> mVertexPositions;
        // Index buffer( CCW vertex indices for each triangle )
        std::vector<VertexID> mVertexInstanceVertices; // match indices data, index[VertexInstanceID], value[VetexID]
        // Vertex buffer
        std::vector<MeshBuildVertex> mMeshBuildVertices;

        std::string mMaterialPath;

        // For skinned mesh
        SkinDescription mSkinDescription;

        // For blend shape
        BlendShapeDeformer mBlendShapeDeformer;

        bool mHasVertexColor{ false };
    };

}}   // namespace cross::editor