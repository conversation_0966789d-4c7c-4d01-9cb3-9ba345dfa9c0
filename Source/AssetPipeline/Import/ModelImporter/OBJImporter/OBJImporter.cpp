#include "OBJImporter.h"
#include "EnginePrefix.h"

#include <fstream>
#include <iostream>
#include <queue>

#include "AssetPipeline/Utils/AssetMath.h"
#include "External/Tristripper/Adjacency.h"
#include "External/mikktspace/mikktspace.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "FileSystem/filesystem.h"
#include "CrossBase/String/Word.h"
namespace cross::editor {

    OBJImporter::OBJImporter()
    {
        //mImportSettings = setting;
    }

    void OBJImporter::ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath, ImportSetting* setting)
    {
        mImportSettings = static_cast<ModelImportSettings*>(setting);
        std::string extension = ToLower(GetPathNameExtension(assetFilename));
        if (extension != "ceobj")
        {
            mImportResult = AssetImportState::ExtensionError;
            return;
        }

        mAssetFileName = assetFilename;
        mNdaSavePath = ndaSavePath;
        PathHelper::Normalize(mAssetFileName);
        PathHelper::Normalize(mNdaSavePath);

        mName = assetFilename.substr(0, assetFilename.length() - 4);
        auto tmpIdx = mName.find_last_of('/');
        if (tmpIdx != -1)
            mName = mName.substr(tmpIdx + 1);
        tmpIdx = mName.find_last_of('\\');
        if (tmpIdx != -1)
            mName = mName.substr(tmpIdx + 1);

        mIdxLoad.clear();
        mVtxLoad.clear();
        mVcolLoad.clear();
        mNmlLoad.clear();
        mUVLoad.clear();
        mUV2Load.clear();
        mUV3Load.clear();
        mUV4Load.clear();
        mTanLoad.clear();

        mSubMeshCnt = -1;
        mSubMeshMode = false;
        mIdxOffset.clear();
        mIdxBuffer.clear();
        mVtxBuffer.clear();
        mVcolBuffer.clear();
        mNmlBuffer.clear();
        mUVBuffer.clear();
        mUV2Buffer.clear();
        mUV3Buffer.clear();
        mUV4Buffer.clear();
        mTanBuffer.clear();

        mMeshPartLodStartIndex.clear();
        mMeshPartLodParams.clear();

        if (!LoadObjAssetDataFromFile())
        {
            mImportResult = AssetImportState::OpenFileFail;
        }

        if (!AssembleImportMeshAssetDataT())
        {
            mImportResult = AssetImportState::ImportFail;
        }

        if (!ExportNdaFile())
        {
            mImportResult = AssetImportState::WriteNdaFail;
        }
    }

    bool OBJImporter::CheckAssetName(const char* name) const
    {
        return HasExtension(name, ".ceobj");
    }

    bool OBJImporter::IfExistChannel(VertexChannel channel)
    {
        // return mVertexChannelSemanticMask & mask;
        for (int i = 0; i < mVertexChannelData.size(); i++)
        {
            if (mVertexChannelData[i].mVertexChannel == (UInt32)channel)
                return true;
        }
        return false;
    }

    UInt32 OBJImporter::GetVertexCount(VertexChannel posChannel)
    {
        if (!IfExistChannel(posChannel))
            return 0;
        VertexChannelAssetData& vertexChannel = GetChannelAssetData(posChannel);
        return (UInt32)vertexChannel.mData.size() / (UInt32)vertexChannel.mStride;
    }

    bool OBJImporter::LoadObjAssetDataFromFile()
    {
        std::map<uint32_t, size_t> dict;
        std::ifstream              fin(mAssetFileName);
        if (!fin)
            return false;

        std::string tmpStr;

        int      subMeshIdx = -1;
        uint32_t nowOffset = 0;

        while (getline(fin, tmpStr))
        {
            std::stringstream sstream;
            sstream << tmpStr;
            std::string inType;
            sstream >> inType;

            bool vt2Flag = false;
            bool vt3Flag = false;

            if (inType == "v")
            {
                float x, y, z;
                sstream >> x >> y >> z;
                mVtxLoad.push_back(AssetMath::Vector3f(x, y, z));
            }
            else if (inType == "vt")
            {
                float x, y;
                sstream >> x >> y;
                mUVLoad.push_back(AssetMath::Vector2f(x, y));
            }
            else if (inType == "#vt2" && mImportSettings->ImportSecondaryUV)
            {
                float x, y;
                sstream >> x >> y;
                mUV2Load.push_back(AssetMath::Vector2f(x, y));
                vt2Flag = true;
            }
            else if (inType == "#vt3")
            {
                float x, y, z, w;
                sstream >> x >> y >> z >> w;
                mUV3Load.push_back(AssetMath::Vector4f(x, y, z, w));
                vt3Flag = true;
            }
            else if (inType == "#vt4")
            {
                uint32_t x0, y0, z0;
                sstream >> x0 >> y0 >> z0;
                float x = reinterpret_cast<float&>(x0);
                float y = reinterpret_cast<float&>(y0);
                float z = reinterpret_cast<float&>(z0);
                mUV4Load.push_back(AssetMath::Vector3f(x, y, z));
            }
            else if (inType == "#vc")
            {
                float x, y, z, w;
                sstream >> x >> y >> z >> w;
                mVcolLoad.push_back(AssetMath::Vector4f(x, y, z, w));
            }
            else if (inType == "vn" && mImportSettings->ImportNormals)
            {
                float x, y, z;
                sstream >> x >> y >> z;
                mNmlLoad.push_back(AssetMath::Vector3f(x, y, z));
            }
            else if (inType == "#t" && mImportSettings->ImportTangents)
            {
                float x, y, z, w;
                sstream >> x >> y >> z >> w;
                mTanLoad.push_back(AssetMath::Vector4f(x, y, z, w));
            }
            else if (inType == "g")
            {
                if (mIdxBuffer.empty() || !mIdxBuffer.back().empty())
                {
                    dict.clear();
                    mIdxBuffer.push_back(std::vector<UInt32>(0));
                    mVtxBuffer.push_back(std::vector<AssetMath::Vector3f>(0));
                    mVcolBuffer.push_back(std::vector<AssetMath::Vector4f>(0));
                    mNmlBuffer.push_back(std::vector<AssetMath::Vector3f>(0));
                    mUVBuffer.push_back(std::vector<AssetMath::Vector2f>(0));
                    mUV2Buffer.push_back(std::vector<AssetMath::Vector2f>(0));
                    mUV3Buffer.push_back(std::vector<AssetMath::Vector4f>(0));
                    mUV4Buffer.push_back(std::vector<AssetMath::Vector3f>(0));
                    mTanBuffer.push_back(std::vector<AssetMath::Vector4f>(0));
                    mIdxOffset.push_back(nowOffset);
                    mSubMeshMode = true;
                    subMeshIdx++;
                }
            }
            else if (inType == "f")
            {
                for (uint32_t v = 0; v < 3; v++)
                {
                    std::string faceStr;
                    sstream >> faceStr;
                    std::vector<uint32_t> indices(0);
                    {
                        // fetch indices of v/vt/vn
                        uint32_t now = 0;
                        for (uint32_t i = 0; i < faceStr.length(); i++)
                        {
                            if (faceStr[i] != '/')
                            {
                                now = now * 10 + faceStr[i] - '0';
                            }
                            else
                            {
                                indices.push_back(now);
                                now = 0;
                            }
                        }
                        indices.push_back(now);
                    }

                    if (mSubMeshMode)
                    {
                        auto   it = dict.find(indices[0] - 1);
                        size_t useIdx;
                        if (it != dict.end())
                        {
                            useIdx = it->second;
                        }
                        else
                        {
                            useIdx = dict.size();
                            dict[indices[0] - 1] = useIdx;
                            nowOffset++;
                            mVtxBuffer[subMeshIdx].push_back(mVtxLoad[indices[0] - 1]);
                            mUVBuffer[subMeshIdx].push_back(mUVLoad[indices[0] - 1]);
                            if (mVcolLoad.size() != 0)
                            {
                                mVcolBuffer[subMeshIdx].push_back(mVcolLoad[indices[0] - 1]);
                            }
                            if (mImportSettings->ImportSecondaryUV && mUV2Load.size() != 0)
                            {
                                mUV2Buffer[subMeshIdx].push_back(mUV2Load[indices[0] - 1]);
                            }
                            if (mUV3Load.size() != 0)
                            {
                                mUV3Buffer[subMeshIdx].push_back(mUV3Load[indices[0] - 1]);
                            }
                            if (mUV4Load.size() != 0)
                            {
                                mUV4Buffer[subMeshIdx].push_back(mUV4Load[indices[0] - 1]);
                            }
                            if (mImportSettings->ImportNormals)
                            {
                                mNmlBuffer[subMeshIdx].push_back(mNmlLoad[indices[0] - 1]);
                            }
                            if (mImportSettings->ImportTangents)
                            {
                                mTanBuffer[subMeshIdx].push_back(mTanLoad[indices[0] - 1]);
                            }
                        }
                        // mIdxBuffer[subMeshIdx].push_back(UInt32(useIdx + mIdxOffset[subMeshIdx]));
                        mIdxBuffer[subMeshIdx].push_back(UInt32(useIdx));
                    }
                    else
                    {
                        mIdxLoad.push_back(UInt32(indices[0] - 1));
                    }
                }
            }
            else if (inType == "#lod")
            {
                MeshLODParam param{};
                sstream >> param.screenRelativeTransitionHeight >> param.fadeTransitionWidth;
                mMeshPartLodStartIndex.emplace_back(static_cast<UInt32>(mIdxOffset.size()));
                mMeshPartLodParams.emplace_back(param);
            }
        }

        if (!mSubMeshMode)
        {
            mIdxBuffer.push_back(mIdxLoad);
            mVtxBuffer.push_back(mVtxLoad);
            if (mVcolLoad.size() != 0)
            {
                mVcolBuffer.push_back(mVcolLoad);
            }
            mUVBuffer.push_back(mUVLoad);
            if (mImportSettings->ImportSecondaryUV && mUV2Load.size() != 0)
            {
                mUV2Buffer.push_back(mUV2Load);
            }
            if (mUV3Load.size() != 0)
            {
                mUV3Buffer.push_back(mUV3Load);
            }
            if (mUV4Load.size() != 0)
            {
                mUV4Buffer.push_back(mUV4Load);
            }
            if (mImportSettings->ImportNormals)
            {
                mNmlBuffer.push_back(mNmlLoad);
            }
            if (mImportSettings->ImportTangents)
            {
                mTanBuffer.push_back(mTanLoad);
            }
        }

        mVertexCount = UInt32(mVtxLoad.size());
        mPrimitiveCount = UInt32(mIdxLoad.size() / 3);
        mIndexStream.mIs16BitIndex = false;
        return true;
    }

    bool OBJImporter::ToImportMeshAssetDataT()
    {
        // mAABB
        std::unique_ptr<CrossSchema::MeshBoundT> aabb = std::make_unique<CrossSchema::MeshBoundT>();
        aabb->fmax = std::make_unique<CrossSchema::float3>(mAABB.Max.x, mAABB.Max.y, mAABB.Max.z);
        aabb->fmin = std::make_unique<CrossSchema::float3>(mAABB.Min.x, mAABB.Min.y, mAABB.Min.z);
        mMeshAssetDataT.faabb = std::move(aabb);

        // mIndexStream
        mMeshAssetDataT.findexstream = std::make_unique<CrossSchema::IndexStreamAssetDataT>();
        mMeshAssetDataT.findexstream->fcount = mIndexStream.mCount;
        mMeshAssetDataT.findexstream->fdata.resize(mIndexStream.mData.size());
        std::copy(mIndexStream.mData.begin(),
            mIndexStream.mData.end(),
            mMeshAssetDataT.findexstream->fdata.begin());
        mMeshAssetDataT.findexstream->fis16bitindex = mIndexStream.mIs16BitIndex;

        // mMaterialNames
        for (int i = 0; i < mMaterialNames.size(); i++)
        {
            mMeshAssetDataT.fmaterialnames.push_back(mMaterialNames[i]);
        }

        // mMeshPartInfo
        mMeshAssetDataT.fmeshpartinfo.clear();
        mMeshAssetDataT.fmeshpartinfo.resize(0);
        for (int i = 0; i < mMeshPartInfo.size(); i++)
        {
            std::unique_ptr<CrossSchema::ImportMeshPartAssetInfoT> partAssetInfoT =
                std::make_unique<CrossSchema::ImportMeshPartAssetInfoT>();
            partAssetInfoT->fbindinginfo = std::make_unique<CrossSchema::MeshBoundT>();
            partAssetInfoT->fbindinginfo->fmax = std::make_unique<CrossSchema::float3>(mMeshPartInfo[i].mMeshBound.Max.x, mMeshPartInfo[i].mMeshBound.Max.y, mMeshPartInfo[i].mMeshBound.Max.z);
            partAssetInfoT->fbindinginfo->fmin = std::make_unique<CrossSchema::float3>(mMeshPartInfo[i].mMeshBound.Min.x, mMeshPartInfo[i].mMeshBound.Min.y, mMeshPartInfo[i].mMeshBound.Min.z);
            partAssetInfoT->fcustomattributedata.resize(mMeshPartInfo[i].mCustomAttributeData.size());
            std::copy(partAssetInfoT->fcustomattributedata.begin(),
                partAssetInfoT->fcustomattributedata.end(),
                mMeshPartInfo[i].mCustomAttributeData.begin());
            partAssetInfoT->fcustomattributeinfo =
                std::make_unique<CrossSchema::CustomAttributeInfoT>();
            partAssetInfoT->fcustomattributeinfo->fdataflag =
                mMeshPartInfo[i].mCustomAttributeInfo.mDataFlag;
            partAssetInfoT->fcustomattributeinfo->fdataoffset =
                mMeshPartInfo[i].mCustomAttributeInfo.mDataOffset;
            partAssetInfoT->fcustomattributeinfo->fdatasizeinbyte =
                mMeshPartInfo[i].mCustomAttributeInfo.mDataSizeInByte;
            partAssetInfoT->fcustomattributeinfo->fkeynamehash =
                mMeshPartInfo[i].mCustomAttributeInfo.mKeyNameHash;

            partAssetInfoT->findexcount = mMeshPartInfo[i].mIndexCount;
            partAssetInfoT->findexstart = mMeshPartInfo[i].mIndexStart;
            partAssetInfoT->fmaterialindex = mMeshPartInfo[i].mMaterialIndex;
            partAssetInfoT->fmiscflag = mMeshPartInfo[i].mMiscFlag;
            partAssetInfoT->fnameindex = mMeshPartInfo[i].mNameIndex;
            partAssetInfoT->fprimitivecount = mMeshPartInfo[i].mPrimitiveCount;
            partAssetInfoT->fprimitivetype = mMeshPartInfo[i].mPrimitiveType;
            partAssetInfoT->frenderpriority = mMeshPartInfo[i].mRenderPriority;
            partAssetInfoT->fshadowbias = mMeshPartInfo[i].mShadowBias;
            partAssetInfoT->fshadownormalbias = mMeshPartInfo[i].mShadowNormalBias;
            partAssetInfoT->fvertexcount = mMeshPartInfo[i].mVertexCount;
            partAssetInfoT->fvertexstart = mMeshPartInfo[i].mVertexStart;

            for (int j = 0; j < this->mMeshPartInfo[i].mCollisionTree.size(); j++)
            {
                std::unique_ptr<CrossSchema::CollisionNodeT> c =
                    std::make_unique<CrossSchema::CollisionNodeT>();
                c->index = this->mMeshPartInfo[i].mCollisionTree[j].Index;
                c->leftindex = this->mMeshPartInfo[i].mCollisionTree[j].LeftIndex;
                c->rightindex = this->mMeshPartInfo[i].mCollisionTree[j].RightIndex;
                c->maxpos.clear();
                c->maxpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Max.x);
                c->maxpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Max.y);
                c->maxpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Max.z);
                c->minpos.clear();
                c->minpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Min.x);
                c->minpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Min.y);
                c->minpos.push_back(this->mMeshPartInfo[i].mCollisionTree[j].Bound.Min.z);

                c->trianglelist.resize(this->mMeshPartInfo[i].mCollisionTree[j].TriangleList.size());
                std::copy(this->mMeshPartInfo[i].mCollisionTree[j].TriangleList.begin(),
                    this->mMeshPartInfo[i].mCollisionTree[j].TriangleList.end(),
                    c->trianglelist.begin());

                partAssetInfoT->fcollisiontree.push_back(std::move(c));
            }

            mMeshAssetDataT.fmeshpartinfo.push_back(std::move(partAssetInfoT));
        }

        // mMeshPartNames
        for (int i = 0; i < mMeshPartNames.size(); i++)
        {
            mMeshAssetDataT.fmeshpartnames.push_back(mMeshPartNames[i]);
        }

        mMeshAssetDataT.fmeshpartlodstartindex = mMeshPartLodStartIndex;

        // mName
        mMeshAssetDataT.fname = mName.length() <= 0 ? "unknow" : mName;

        // mPrimitiveCount
        mMeshAssetDataT.fprimitivecount = mPrimitiveCount;

        // mVersion
        mMeshAssetDataT.fversion = mVersion;

        // mVertexChannelData
        for (int i = 0; i < mVertexChannelData.size(); i++)
        {
            std::unique_ptr<CrossSchema::VertexChannelAssetDataT> v =
                std::make_unique<CrossSchema::VertexChannelAssetDataT>();
            v->fdata.resize(mVertexChannelData[i].mData.size());
            std::copy(mVertexChannelData[i].mData.begin(),
                mVertexChannelData[i].mData.end(),
                v->fdata.begin());

            v->fdataformat = mVertexChannelData[i].mDataFormat;
            v->ffrequency = mVertexChannelData[i].mFrequency;
            v->fmiscflag = (uint16_t)mVertexChannelData[i].mMiscFlag;
            v->freserve0 = (uint16_t)mVertexChannelData[i].mReserve0;
            v->freserve1 = (uint16_t)mVertexChannelData[i].mReserve1;
            v->fstream = mVertexChannelData[i].mStream;
            v->fstreamoffset = mVertexChannelData[i].mStreamOffset;
            v->fstride = mVertexChannelData[i].mStride;
            v->fvertexchannel = mVertexChannelData[i].mVertexChannel;

            mMeshAssetDataT.fvertexchanneldata.push_back(std::move(v));
        }

        // mVertexChannelSemanticMask
        mMeshAssetDataT.fvertexchannelsemanticmask = mVertexChannelSemanticMask;

        // mVertexCount
        mMeshAssetDataT.fvertexcount = mVertexCount;

        return true;
    }

    void OBJImporter::CalculateWholeAABB()
    {
        VertexChannelAssetData& vertpos = GetChannelAssetData(VertexChannel::Position0);
        UInt32                  count = (UInt32)vertpos.mData.size() / (UInt32)vertpos.mStride;
        for (UInt32 i = 0; i < count; i++)
        {
            Float3 point;
            memcpy(point.data(), vertpos.mData.data() + i * vertpos.mStride, vertpos.mStride);

            mAABB.Encapsulate(point);
        }
    }

    void OBJImporter::AddSubMeshBegin()
    {
        mMeshPartInfo.emplace_back();
        MeshPartAssetInfo& meshPartInfo = mMeshPartInfo.back();

        std::stringstream meshNameSS;
        meshNameSS << "meshPart" << static_cast<int>(mMeshPartInfo.size() - 1);
        std::string meshPartName;
        meshNameSS >> meshPartName;
        mMeshPartNames.push_back(meshPartName.c_str());
        meshPartInfo.mNameIndex = (SInt16)(mMeshPartNames.size() - 1);

        mMaterialNames.push_back(mName.c_str());
        meshPartInfo.mMaterialIndex = (SInt16)(mMaterialNames.size() - 1);

        // TODO(imzzhang): only support Position0, not support Position1,2,3
        meshPartInfo.mVertexStart = GetVertexCount(VertexChannel::Position0);

        meshPartInfo.mIndexStart = mIndexStream.GetCount();
        meshPartInfo.mPrimitiveType = (UInt32)PrimitiveTopology::TriangleList;
    }

    void OBJImporter::AddSubMeshEnd()
    {
        MeshPartAssetInfo& meshPartInfo = mMeshPartInfo.back();

        meshPartInfo.mVertexCount =
            GetVertexCount(VertexChannel::Position0) - meshPartInfo.mVertexStart;
        meshPartInfo.mIndexCount = mIndexStream.GetCount() - meshPartInfo.mIndexStart;

        // TODO(imzzhang): attention maybe not TriangleList
        meshPartInfo.mPrimitiveCount = meshPartInfo.mIndexCount / 3;

        // TODO(imzzhang): calculate AABB and CollisionTree
        VertexChannelAssetData& vertpos = GetChannelAssetData(VertexChannel::Position0);
        for (UInt32 i = 0; i < meshPartInfo.mVertexCount; i++)
        {
            Float3 point;
            memcpy(point.data(), vertpos.mData.data() + ((meshPartInfo.mVertexStart + i) * vertpos.mStride), vertpos.mStride);

            meshPartInfo.mMeshBound.Encapsulate(point);
        }
        CalculateWholeAABB();

        auto& collisionNode = meshPartInfo.mCollisionTree.emplace_back();
        collisionNode.Index = 0;
        collisionNode.LeftIndex = 0;
        collisionNode.RightIndex = 0;
        collisionNode.Bound.Min = mAABB.Min;
        collisionNode.Bound.Max = mAABB.Max;
        collisionNode.TriangleList.resize(meshPartInfo.mIndexCount);
        for (UInt32 j = 0; j < meshPartInfo.mPrimitiveCount; ++j)
        {
            collisionNode.TriangleList[j] = j;
        }
    }

    bool OBJImporter::AssembleImportMeshAssetDataT()
    {
        for (size_t i = 0; i < mIdxBuffer.size(); i++)
        {
            AddSubMeshBegin();

            mIndexStream.AddIndices(mIdxBuffer[i]);

            if (!AddChannelData<AssetMath::Vector3f, data::Vec3f>(
                SemanticPosition, VertexChannel::Position0, VertexFormat::Float3, mVtxBuffer[i]))
                return false;

            if (mVcolLoad.size() != 0)
            {
                if (!AddChannelData<AssetMath::Vector4f, data::Vec4f>(
                    SemanticColor, VertexChannel::Color0, VertexFormat::Float4, mVcolBuffer[i]))
                    return false;
            }

            if (!AddChannelData<AssetMath::Vector2f, data::Vec2f>(
                SemanticTexCoord, VertexChannel::TexCoord0, VertexFormat::Float2, mUVBuffer[i]))
                return false;

            if (mImportSettings->ImportSecondaryUV && mUV2Load.size() != 0)
            {
                if (!AddChannelData<AssetMath::Vector2f, data::Vec2f>(SemanticTexCoord,
                    VertexChannel::TexCoord1,
                    VertexFormat::Float2,
                    mUV2Buffer[i]))
                    return false;
            }

            if (mUV3Load.size() != 0)
            {
                if (!AddChannelData<AssetMath::Vector4f, data::Vec4f>(SemanticTexCoord,
                    VertexChannel::TexCoord2,
                    VertexFormat::Float4,
                    mUV3Buffer[i]))
                    return false;
            }

            if (mUV4Load.size() != 0)
            {
                if (!AddChannelData<AssetMath::Vector3f, data::Vec3f>(SemanticTexCoord,
                    VertexChannel::TexCoord3,
                    VertexFormat::Float3,
                    mUV4Buffer[i]))
                    return false;
            }

            if (mImportSettings->ImportNormals)
            {
                if (!AddChannelData<AssetMath::Vector3f, data::Vec3f>(
                    SemanticNormal, VertexChannel::Normal0, VertexFormat::Float3, mNmlBuffer[i]))
                    return false;
            }

            if (mImportSettings->ImportTangents)
            {
                if (!AddChannelData<AssetMath::Vector4f, data::Vec4f>(
                    SemanticTangent, VertexChannel::Tangent0, VertexFormat::Float4, mTanBuffer[i]))
                    return false;
            }

            AddSubMeshEnd();
        }

        if (!ToImportMeshAssetDataT())
            return false;

        return true;
    }

    bool OBJImporter::ExportNdaFile()
    {
        std::string fileName(std::move(mNdaSavePath));
        PathHelper::MakePathIllegal(fileName);
        auto dir = PathHelper::GetDirectoryFromAbsolutePath(fileName);
        if (!PathHelper::IsDirectoryExist(dir))
        {
            // create all the folders needed
            PathHelper::CheckDirectory(dir);
        }


        filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
        Assert(fileSystem);
        filesystem::IFilePtr file = fileSystem->OpenForWrite(fileName);
        Assert(file);


        resource::AssetFileHeader h;

        auto classID = ClassID(NullType);
        classID = ClassID(MeshAssetDataResource);

        h.SetMagicNumber(ASSET_MAGIC_NUMBER);
        h.SetVersion(4);
        h.SetGUID(0x0001, 0x0002);
        h.SetClassID(classID);
        h.SetContenType((UInt32)CONTENT_TYPE::CONTENT_TYPE_FLATBUFFER);
        for (auto& lod : mMeshPartLodParams)
        {
            h.AddLOD(lod.screenRelativeTransitionHeight, lod.fadeTransitionWidth);
        }

        cross::FileArchive             archive{ file };
        cross::SimpleSerializer        serializer{ archive };
        CrossSchema::ResourceHeader    header(ASSET_MAGIC_NUMBER, 0, classID, 0, 0);
        flatbuffers::FlatBufferBuilder builder(4096);

        auto mloc = CrossSchema::CreateImportMeshAssetData(builder, &mMeshAssetDataT);
        auto name = PathHelper::GetBaseFileName(mNdaSavePath);
        auto mloc2 = CrossSchema::CreateResourceAsset(builder,
            &header,
            builder.CreateString(name),
            CrossSchema::ResourceType::ImportMeshAssetData,
            mloc.Union());
        FinishResourceAssetBuffer(builder, mloc2);

        h.SetDataSize(builder.GetSize());

        std::string headerString;
        h.Serialize(headerString);

        std::string fileContent;
        fileContent.resize(headerString.size() + builder.GetSize());

        std::copy(headerString.begin(), headerString.end(), fileContent.data());
        std::copy(builder.GetBufferPointer(), builder.GetBufferPointer() + builder.GetSize(), fileContent.data() + headerString.size());

        fileSystem->Save(fileName, fileContent.data(), fileContent.size());

        return true;
    }

}
