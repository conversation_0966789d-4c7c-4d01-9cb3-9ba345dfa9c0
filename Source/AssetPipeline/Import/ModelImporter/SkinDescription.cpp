#include "SkinDescription.h"
#include "AssetPipeline/Import/ModelImporter/MeshDescription.h"

namespace cross::editor {

void SkinDescription::Init(const std::vector<MeshBuildVertex>& vertices)
{
    const UInt32 vertexCount = static_cast<UInt32>(vertices.size());
    mSkinVertices.reserve(vertexCount);

    for (auto i = 0u; i < vertexCount; ++i)
    {
        mSkinVertices.emplace_back(vertices[i].ControlPointID);
    }
}

}