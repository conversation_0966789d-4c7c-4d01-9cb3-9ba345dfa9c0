#pragma once
#include "AssetPipeline/Import/PCGResourceImportSetting.h"

namespace cross::editor {
struct ASSET_API OSMImportSetting : PCGResourceImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serial<PERSON>, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "PCG Resource type", bReadOnly = true))
    PCGResourceType mResouceType = PCGResourceType::OSM;

    
    static OSMImportSetting gOSMImportSetting;

    void SetEngineImportSettingImp() { gOSMImportSetting = *this; }
};
}

