#pragma once

#include "Resource/RuntimePCG/PCGResource.h"
#include "AssetPipeline/Import/AssetImporter.h"
#include "AssetPipeline/Import/SHPImporter/SHPImportSetting.h"

namespace cross::editor {
class SHPImporter : public AssetImporter
{
public:
    SHPImporter();
    ~SHPImporter();

public:
    virtual void ImportAsset(const std::string& assetFilename, const std::string& ndaSavePath = "", ImportSetting* setting = nullptr) override;
    virtual bool CheckAssetName(const char* name) const override;

private:
    bool LoadShpFile(const std::string& filename, PCGMap& map);
    bool LoadDbfFile(const std::string& filename, PCGMap& map);
    bool SaveToNda(const PCGMap& map, const std::string& savePath);
    UInt32  GetValueByBig(UInt32 bigValue);
    Double2 LonLatTo2D(double lon, double lat) { return mCDTransfer.WGS84_To_2D(lon, lat); }

private:
    CoordinateTransfer  mCDTransfer;
};
}   // namespace cross::editor