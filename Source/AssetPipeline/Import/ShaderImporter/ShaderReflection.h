#pragma once
#include "PlatformDefs.h"
#include "dxc/dxcapi.h"
#if CROSSENGINE_WIN
#include <atlbase.h>
#include <d3d12shader.h>
#else
#include "dxc/Support/WinAdapter.h"
#endif
#include "spirv_cross.hpp"
#include "spirv_glsl.hpp"
#include "spirv_msl.hpp"
#include "CrossSchema/ShaderAsset_generated.h"

namespace cross::editor
{
bool ReflectForDXIL(
	IDxcBlob* binary, 
	CrossSchema::ShaderCodeT* code, 
	CrossSchema::ShaderLayoutT* layout, 
    CrossSchema::uint3* groupSize);


bool ReflectForOtherFormat(
	CrossSchema::ShaderCodeT* code,
	const CrossSchema::ShaderVersion& version,
	CrossSchema::ShaderLayoutT* pHLSLLayout, 
	CrossSchema::ShaderLayoutT* layout,
	CrossSchema::uint3* groupSize);

bool MergeShaderLayouts(const std::vector<std::shared_ptr<CrossSchema::ShaderLayoutT>>& srcLayouts, CrossSchema::ShaderLayoutT* dstLayout, CrossSchema::ShaderVersion version);

}
