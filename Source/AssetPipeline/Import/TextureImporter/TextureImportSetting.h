#pragma once
#include "EnginePrefix.h"
#include "CrossBase/Math/ColorSpace.h"
#include "AssetPipeline/Import/AssetImportSetting.h"
#include "CECommon/Common/TextureDefines.h"

namespace cross::editor
{
enum CEMeta(Editor) TextureCompression : UInt32
{
    Uncompressed,
    CompressedBasisHQ,
    CompressedBasisLQ,
    BC1,
    BC2,
    BC3,
    BC4,
    BC5,
    BC6H,
    BC7,
    ASTC,
    PVRTC,
    ETC2
};

enum CEMeta(Editor) ImportColorSpace : UInt32
{
    Linear,
    SRGB,
    Count
};

enum class CEMeta(Editor) ImportTexureSize : UInt32
{
    None = 0,
    SQUARE_64   = 1 << 6,
    SQUARE_128  = 1 << 7,
    SQUARE_256  = 1 << 8,
    SQUARE_512  = 1 << 9,
    SQUARE_1024 = 1 << 10,
    SQUARE_2048 = 1 << 11,
    SQUARE_4096 = 1 << 12,
    SQUARE_8192 = 1 << 13,
};

inline ImportColorSpace GetSpace(const std::string& name)
{
    static std::unordered_map<std::string, ImportColorSpace> name_mapping = {
        {"Linear", Linear}, {"SRGB", SRGB}
    };
    return name_mapping.count(name) ? name_mapping[name] : ImportColorSpace::Count;
}

inline TextureCompression GetCompression(const std::string& name)
{
    static std::unordered_map<std::string, TextureCompression> name_mapping = {
        {"Uncompressed", Uncompressed},
        {"CompressedBasisHQ", CompressedBasisHQ},
        {"CompressedBasisLQ", CompressedBasisLQ},
        {"BC7", BC7},
        {"BC6H", BC6H},
        {"BC4", BC4},
        {"BC3", BC3},
        {"BC1", BC1},
    };

    return name_mapping.count(name) ? name_mapping[name] : Uncompressed;
}

enum class CEMeta(Editor) ImportTextureGroup : UInt32
{
    Default = 0,
    Heightmap,
    UI,
    Character,
    VFX,
    Lightmap,
    Terrain,
};

struct ASSET_API TextureImportSetting : ImportSetting
{
    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Auto import")) 
    bool AutoImport = true;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Texture type"))
    TextureType Type = TextureType::ImageTexture;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "ColorSpace"))
    ImportColorSpace ColorSpace = SRGB;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Compression Type"))
    TextureCompression Compression = BC3;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate mip maps"))
    bool GenerateMipmap = true;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate radiance prefilter mip maps for cubemap"))
    bool GeneratePrefilterMipmap = true;
    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether generate radiance prefilter mip maps for cubemap"))
    int GlossBias = 1;

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether flip Y")) 
    bool FlipUV = false;

    CEMeta(Serialize, Editor)
    bool OpenGLESCompatible = true;

    CEMeta(Serialize, Editor, ToolTips = "whether use stream file.")
    bool IsStreamFile = false;

    CEMeta(Editor, ToolTips = "Save raw image data for later usage.")
    bool SaveRawData = true;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "whether cook"))
    bool IsCook = false;

    CEMeta(Serialize, Editor) 
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "support virtual texture streaming.")) 
    bool VirtualTextureStreaming = false;
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Is Udim texture.")) 
    bool isUDIM = false;

    CEMeta(Serialize, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Tile XY"))
    Float2 TileXY = Float2(0, 0);

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "texture change size.")) 
    ImportTexureSize ImportSize = ImportTexureSize::None;
    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Texture Group.")) 
    ImportTextureGroup ImportTextureGroup = ImportTextureGroup::Default;

    CEMeta(Serialize, Editor) CECSAttribute(PropertyInfo(PropertyType = "Auto", ToolTips = "Is Auto Recognition.", bHide = true)) 
    bool isAutoRecognition = false;

    TextureImportSetting() = default;
    TextureImportSetting(TextureType Type, ImportColorSpace ColorSpace, TextureCompression Compression, bool GenerateMipmap, bool OpenGLESCompatible, bool IsStreamFile, bool SaveRawData, bool IsCook, bool VirtualTextureStreaming)
        : Type(Type)
        , ColorSpace(ColorSpace)
        , Compression(Compression)
        , GenerateMipmap(GenerateMipmap)
        , OpenGLESCompatible(OpenGLESCompatible)
        , IsStreamFile(IsStreamFile)
        , SaveRawData(SaveRawData)
        , IsCook(IsCook)
        , VirtualTextureStreaming(VirtualTextureStreaming)
    {}

    static TextureImportSetting gTextureImportSetting;

    void SetEngineImportSettingImp() { 
        gTextureImportSetting = *this;
    }
};
}