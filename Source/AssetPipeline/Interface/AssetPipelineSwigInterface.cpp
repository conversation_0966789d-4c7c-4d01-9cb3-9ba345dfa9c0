#include "AssetPipeline/Interface/AssetPipelineSwigInterface.h"
#include "AssetPipeline/Import/ModelImporter/ModelImportSettings.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"
#include "AssetPipeline/Import/FontImporter/FontImportSetting.h"
#include "AssetPipeline/Import/OSMImporter/OSMImportSetting.h"
#include "AssetPipeline/Import/SHPImporter/SHPImportSetting.h"
#include "AssetPipeline/Import/TMapImporter/TMapImportSetting.h"
#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Import/ModelImporter/CityImportSettings.h"
namespace cross::editor 
{
    bool UpdateTerrainHeightmap(const std::string& terrainNdaPath, const std::string& updateDir)
    {
        static auto & assetImporterMgr = AssetImporterManager::Instance();
        return assetImporterMgr.UpdateTerrainHeightmap(terrainNdaPath, updateDir);
    }

    bool UpdateTerrainWeightTexture(const std::string& terrainNdaPath, const std::string& updateDir)
    {
        static auto& assetImporterMgr = AssetImporterManager::Instance();
        return assetImporterMgr.UpdateTerrainWeightTexture(terrainNdaPath, updateDir);
    }

    TextureResourceInfo GetTextureInfoFromResource(cross::Resource* resource)
    {
        TextureType type = TextureType::ImageTexture;
        if (const auto& importSetting = resource->GetAsset()->GetHeader().mImportSet; !importSetting.empty())
        {
            editor::TextureImportSetting setting;
            setting.DeserializeFromString(importSetting);
            type = setting.Type;
        }

        TextureResourceInfo info{type, ::TextureDimension::Tex2D, ::TextureFormat::TextureFormat_None, ColorSpace::Linear, TextureSize::None, 0, 0, 0, 0, false};
#if CROSSENGINE_EDITOR
        if (resource->GetClassID() == ClassID(Texture2D) || resource->GetClassID() == ClassID(Texture3D) || resource->GetClassID() == ClassID(TextureCube) || resource->GetClassID() == ClassID(Texture2DVirtual))
        {
            auto textureres = TYPE_CAST(resource::Texture*, resource);
            textureres->GetTextureData()->mResourceInfo.Type = type;
            return textureres->GetTextureData()->mResourceInfo;
        }
#endif
        return info;
    }
}
