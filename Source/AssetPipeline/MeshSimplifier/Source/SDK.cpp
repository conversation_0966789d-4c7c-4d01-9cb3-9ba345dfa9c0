#include "SDK.h"

CEMeshSimplifier::IMeshSimplifier* MeshSimplifierSDK_Create(const char* staging)
{
    return CEMeshSimplifier::CreateMeshSimplifierSDK(staging);
}

void MeshSimplifierSDK_Release(CEMeshSimplifier::IMeshSimplifier* ptr)
{
    return ptr->Release();
}

int MeshSimplifierSDK_MeshReduce(CEMeshSimplifier::IMeshSimplifier* ptr, const char* inputFile, float percentTriangles, float percentVertices, const char* outputFile)
{
    CEMeshSimplifier::MeshReduceInfo info{};
    info.mInputFile = inputFile;
    info.mPercentTriangles = percentTriangles;
    info.mPercentVertices = percentVertices;
    info.mOutputFile = outputFile;
    return ptr->MeshReduce(&info);
}
