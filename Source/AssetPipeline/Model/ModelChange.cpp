#include "ModelChange.h"
#include "Resource/AssetStreaming.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/Material.h"
#include "Resource/Texture/Texture2D.h"
#include "Resource/Texture/Texture2DArray.h"
#include "Resource/Prefab/PrefabResource.h"
#include "CECommon/Common/WorldConst.h"
#include "FileSystem/filesystem.h"
#include "Runtime/GameWorld/FFSWGS84SystemG.h"
namespace cross::editor {
SerializeNode LoadJsonFile(std::string filename)
{
    std::string fileContent;
    filesystem::IFilePtr file = EngineGlobal::GetFileSystem()->Open(filename);
    size_t fileSize = file->GetSize();
    fileContent.resize(fileSize);
    size_t readFileSize = file->Read(fileContent.data(), fileSize);
    Assert(fileSize == readFileSize);
    return SerializeNode::Parse<PERSON><PERSON><PERSON><PERSON>(fileContent);
}

void ModelChange::CreateModel(const std::vector<std::string>& meshs, const std::vector<std::vector<std::vector<std::string>>>& mats, const std::string& outName, ModelAsMode mam)
{
    auto fileSys = EngineGlobal::GetFileSystem();
    std::string modelPath = outName + ".model";
    // gen prefab
    auto prefab = gResourceMgr.CreateResourceAs<resource::PrefabResource>();
    prefab->CreateAsset(modelPath);
    prefab->GetAsset()->EnsureGuid();
    // root entity
    SerializeNode entitiesNode;
    std::string rootEuid;
    // entities
    UInt32 entityCount = static_cast<UInt32>(meshs.size());
    SerializeNode entityTemplate = LoadJsonFile("EngineResource/Model/ModelTemplate.json");
    if (entityCount == 1)
    {
        rootEuid = CrossUUID::GenerateCrossUUID().ToString();

        SerializeNode entityNode = entityTemplate.Clone();
        entityNode[WorldSerializeConst::Euid] = rootEuid;
        entityNode[WorldSerializeConst::Name] = PathHelper::GetBaseFileName(meshs[0]);

        auto&& modelNode = entityNode[WorldSerializeConst::ComponentList]["cross::ModelComponentG"]["mModels"][0];
        modelNode["mAsset"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(meshs[0]));

        SerializeNode subModelPropertiesNode = SerializeNode::EmptyArray();
        if (mats.size() > 0)
        {
            for (auto lodmats : mats[0])
            {
                if (lodmats.size() <= 0)
                    break;
                SerializeNode subModelProp;
                subModelProp["mMaterial"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[0]));
                subModelProp["mVisible"] = true;
                if (lodmats.size() > 1)
                {
                    for (size_t i = 0; i < lodmats.size(); i++)
                    {
                        subModelProp["mLODMaterials"][std::to_string(i + 1)] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[i]));
                    }
                }
                subModelPropertiesNode.PushBack(std::move(subModelProp));
            }
        }
        modelNode["mSubModelProperties"] = std::move(subModelPropertiesNode);
        entityNode[WorldSerializeConst::ComponentList]["cross::ModelComponentG"]["mEnableSubModelCulling"] = mam == ModelAsMode::ModelAsMeshPart;

        entitiesNode[rootEuid] = std::move(entityNode);
    }
    else if (mam == ModelAsMode::ModelAsSubModel)
    {
        rootEuid = CrossUUID::GenerateCrossUUID().ToString();

        SerializeNode entityNode = entityTemplate.Clone();
        entityNode[WorldSerializeConst::Euid] = rootEuid;
        entityNode[WorldSerializeConst::Name] = PathHelper::GetBaseFileName(meshs[0]);

        SerializeNode modelsNode = SerializeNode::EmptyArray();
        for (UInt32 idx = 0; idx < entityCount; idx++)
        {
            SerializeNode modelNode = SerializeNode::EmptyObject();
            modelNode["mAsset"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(meshs[idx]));
            SerializeNode subModelPropertiesNode = SerializeNode::EmptyArray();
            if (mats.size() > idx)
            {
                for (auto lodmats : mats[idx])
                {
                    if (lodmats.size() <= 0)
                        break;
                    SerializeNode subModelProp;
                    subModelProp["mMaterial"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[0]));
                    subModelProp["mVisible"] = true;
                    if (lodmats.size() > 1)
                    {
                        for (size_t i = 1; i < lodmats.size(); i++)
                        {
                            subModelProp["mLODMaterials"][std::to_string(i)] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[i]));
                        }
                    }
                    subModelPropertiesNode.PushBack(std::move(subModelProp));
                }
            }
            modelNode["mSubModelProperties"] = std::move(subModelPropertiesNode);
            modelsNode.PushBack(std::move(modelNode));
        }
        entityNode[WorldSerializeConst::ComponentList]["cross::ModelComponentG"]["mModels"] = std::move(modelsNode);
        entityNode[WorldSerializeConst::ComponentList]["cross::ModelComponentG"]["mEnableSubModelCulling"] = true;
        entitiesNode[rootEuid] = std::move(entityNode);
    }
    else
    {
        SerializeNode entityRoot = LoadJsonFile("EngineResource/Model/ModelEmpty.json");
        entityRoot[WorldSerializeConst::Name] = PathHelper::GetBaseFileName(outName);
        rootEuid = entityRoot[WorldSerializeConst::Euid].AsString();
        SerializeNode&& rootChildrenNode = entityRoot[WorldSerializeConst::Children];
        entitiesNode[rootEuid] = std::move(entityRoot);

        for (UInt32 idx = 0; idx < entityCount; idx++)
        {
            std::string euid = CrossUUID::GenerateCrossUUID().ToString();
            rootChildrenNode.PushBack(euid);

            SerializeNode entityNode = entityTemplate.Clone();
            entityNode[WorldSerializeConst::Euid] = euid;
            entityNode[WorldSerializeConst::Name] = PathHelper::GetBaseFileName(meshs[idx]);

            auto&& modelNode = entityNode[WorldSerializeConst::ComponentList]["cross::ModelComponentG"]["mModels"][0];
            modelNode["mAsset"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(meshs[idx]));

            SerializeNode subModelPropertiesNode = SerializeNode::EmptyArray();
            if (mats.size() > idx)
            {
                for (auto lodmats : mats[idx])
                {
                    if (lodmats.size() <= 0)
                        break;
                    SerializeNode subModelProp;
                    subModelProp["mMaterial"] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[0]));
                    subModelProp["mVisible"] = true;
                    if (lodmats.size() > 1)
                    {
                        for (size_t i = 1; i < lodmats.size(); i++)
                        {
                            subModelProp["mLODMaterials"][std::to_string(i)] = gResourceMgr.ConvertPathToGuid(fileSys->GetRelativePath(lodmats[i]));
                        }
                    }
                    subModelPropertiesNode.PushBack(std::move(subModelProp));
                }
            }
            modelNode["mSubModelProperties"] = std::move(subModelPropertiesNode);

            entitiesNode[euid] = std::move(entityNode);
        }
    }

    SerializeNode rootNode;
    rootNode[WorldSerializeConst::Euid] = rootEuid;
    SerializeNode ecsNode;
    ecsNode[WorldSerializeConst::RootNode] = std::move(rootNode);
    ecsNode[WorldSerializeConst::Entites] = std::move(entitiesNode);
    SerializeNode prefabJson;
    //prefabJson[WorldSerializeConst::PrefabID] = prefab->GetGuid_Str();
    prefabJson[WorldSerializeConst::ECSDict] = std::move(ecsNode);
    // save
    gResourceMgr.AddResourceDependencies(prefabJson, TypeCast<Resource>(prefab));
    prefab->DelReferenceResource(prefab->GetGuid_Str());
    prefab->Serialize(std::move(prefabJson), modelPath);
}

ModelSplitErrorCode ModelChange::ModelSplit(const std::string& mesh, const std::vector<std::string>& mats, Float2 center, Float2 blockSize, ModelAsMode mam)
{
    std::string modelName = gResourceMgr.ConvertGuidToPath(mesh);
    auto meshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(mesh, false));
    std::vector<MeshAssetDataResourcePtr> outMeshAssets;
    int ret = meshAsset->SeparateTo(center, blockSize, outMeshAssets, mam == ModelAsMode::ModelAsMeshPart);
    if (ret >= 0)
    {
        std::vector<std::string> meshs(outMeshAssets.size());
        std::vector<std::vector<std::vector<std::string>>> matss(outMeshAssets.size());
        for (UInt32 idx = 0; idx < outMeshAssets.size(); idx++)
        {
            outMeshAssets[idx]->Serialize(outMeshAssets[idx]->GetName());
            meshs[idx] = outMeshAssets[idx]->GetName();
            UInt32 LodMeshPartCount = outMeshAssets[idx]->GetAssetData()->GetMeshPartCount(0);
            matss[idx] = std::vector<std::vector<std::string>>(LodMeshPartCount, mats);
        }
        modelName.replace(modelName.length() - 4, modelName.length(), ".split");
        CreateModel(meshs, matss, modelName, mam);
        return ModelSplitErrorCode::ERR_OK;
    }
    return ModelSplitErrorCode::ERR_PARAMETERS;
}

Float3 ModelChange::ChangeModelCenter(const std::string& meshPath)
{
    auto meshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(meshPath, false));
    if (!meshAsset)
        return Float3::Zero();
    auto localCenter = meshAsset->GetAssetData()->ChangeMeshCenterToPointsCenter();
    auto assetName = meshAsset->GetName();
    meshAsset->Serialize(assetName);
    return localCenter;
}

void ModelChange::BuildingSplit(const std::string& mesh, const std::vector<std::string>& mats, ModelAsMode mam)
{
    std::string modelName = gResourceMgr.ConvertGuidToPath(mesh);
    auto meshAsset = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(mesh));
    auto meshData = meshAsset->GetAssetData();
    auto& posChannelData = meshData->GetChannelAssetData(VertexChannel::Position0);
    auto posData = reinterpret_cast<Float3*>(posChannelData.mData.data());
    auto& indexData = meshData->GetIndexStream();

    struct Float3Hash
    {
        size_t operator()(const Float3& key) const
        {
            const UInt32* data = reinterpret_cast<const UInt32*>(key.data());
            return HashRange(data, data + 3);
        }

        bool operator()(const Float3& key1, const Float3& key2) const
        {
            return key1 == key2;
        }
    };

    UInt32 primitivePointCount = indexData.mCount / meshData->GetPrimitiveCount();
    std::unordered_map<UInt32, UInt32> pointIDMap;
    std::unordered_map<UInt32, UInt32> primBuildingMap;
    std::unordered_map<Float3, std::vector<UInt32>, Float3Hash> samePosToIndexMap;
    std::unordered_map<UInt32, std::vector<UInt32>> samePointIDToIndexMap;
    std::unordered_map<UInt32, std::set<UInt32>> sameBuildingIDToPrimMap;
    pointIDMap.reserve(indexData.mCount);
    primBuildingMap.reserve(meshData->GetPrimitiveCount());
    UInt32 pointID = 0;
    UInt32 buildingID = 0;

    auto GetVertexStartByIdx = [meshData](UInt32 idx) {
        for (UInt32 mIdx = 0; mIdx < meshData->GetAllLodMeshPartCount(); mIdx++)
        {
            const auto& meshPart = meshData->GetMeshPartInfo(mIdx);
            if (meshPart.mIndexStart + meshPart.mIndexCount > idx)
                return meshPart.mVertexStart;
        }
        return 0u;
    };

    for (UInt32 idx = 0; idx < indexData.mCount; idx++)
    {
        UInt32 primIdx = idx / primitivePointCount;
        UInt32 index = indexData.mIs16BitIndex ? static_cast<UInt32>(reinterpret_cast<UInt16*>(indexData.mData.data())[idx]) : reinterpret_cast<UInt32*>(indexData.mData.data())[idx];
        index += GetVertexStartByIdx(idx);
        const auto& pos = posData[index];
        UInt32 samePointID = 0;
        if (samePosToIndexMap.find(pos) == samePosToIndexMap.end())
        {
            for (const auto& [p, indexs] : samePosToIndexMap)
            {
                if (Float2::Distance({pos.x, pos.z}, {p.x, p.z}) < 10.f)
                {
                    samePointID = pointIDMap[indexs[0]];
                    break;
                }
            }
            samePointID = samePointID > 0 ? samePointID : ++pointID;
        }
        else
        {
            samePointID = pointIDMap[samePosToIndexMap[pos][0]];
        }
        pointIDMap[idx] = samePointID;
        samePosToIndexMap[pos].push_back(idx);

        UInt32 sameBuildingID = 0;
        if (primBuildingMap.find(primIdx) != primBuildingMap.end())
        {
            UInt32 oldBuildingID = primBuildingMap[primIdx];
            if (samePointIDToIndexMap.find(samePointID) != samePointIDToIndexMap.end())
            {
                UInt32 tempIdx = samePointIDToIndexMap[samePointID][0];
                UInt32 tempPrimIdx = tempIdx / primitivePointCount;
                sameBuildingID = primBuildingMap[tempPrimIdx];
            }
            else
            {
                sameBuildingID = oldBuildingID;
            }
            if (oldBuildingID != sameBuildingID)
            {
                for (UInt32 oldBuildingPIdx : sameBuildingIDToPrimMap[oldBuildingID])
                {
                    primBuildingMap[oldBuildingPIdx] = sameBuildingID;
                    sameBuildingIDToPrimMap[sameBuildingID].insert(oldBuildingPIdx);
                }
                sameBuildingIDToPrimMap.erase(oldBuildingID);
            }
        }
        else
        {
            if (samePointIDToIndexMap.find(samePointID) != samePointIDToIndexMap.end())
            {
                UInt32 tempIdx = samePointIDToIndexMap[samePointID][0];
                UInt32 tempPrimIdx = tempIdx / primitivePointCount;
                sameBuildingID = primBuildingMap[tempPrimIdx];
            }
            else
            {
                sameBuildingID = ++buildingID;
            }
            sameBuildingIDToPrimMap[sameBuildingID].insert(primIdx);
        }
        primBuildingMap[primIdx] = sameBuildingID;
        samePointIDToIndexMap[samePointID].push_back(idx);
    }

    std::vector<MeshAssetDataResourcePtr> outMeshAssets;
    UInt32 partID = 0;
    for (const auto& [primBuildingID, prims] : sameBuildingIDToPrimMap)
    {
        MeshAssetDataResourcePtr buildingRes = gResourceMgr.CreateResourceAs<resource::MeshAssetDataResource>();
        cross::MeshAssetData* buildingMeshData = buildingRes->GetAssetData();
        buildingMeshData->GetIndexStream().mIs16BitIndex = indexData.mIs16BitIndex;
        UInt32 newVIndex = 0;
        std::unordered_map<UInt32, UInt32> oldVIndexToVIndexMap;
        MeshBound aabb;
        for (const auto& prim : prims)
        {
            for (UInt32 idx = 0; idx < primitivePointCount; idx++)
            {
                UInt32 rIdx = prim * primitivePointCount + idx;
                UInt32 index = indexData.mIs16BitIndex ? static_cast<UInt32>(reinterpret_cast<UInt16*>(indexData.mData.data())[rIdx]) : reinterpret_cast<UInt32*>(indexData.mData.data())[rIdx];
                UInt32 vIndex = index + GetVertexStartByIdx(rIdx);
                if (oldVIndexToVIndexMap.find(vIndex) == oldVIndexToVIndexMap.end())
                {
                    aabb.Encapsulate(posData[vIndex]);
                    for (UInt32 cIdx = 0; cIdx < meshData->GetVertexChannelCount(); cIdx++)
                    {
                        auto channelData = meshData->GetVertexChannelDataByIndex(cIdx);
                        buildingMeshData->AddChannelData(channelData->mVertexChannel, channelData->mDataFormat, channelData->mStride, channelData->mData.data() + vIndex * channelData->mStride, 1);
                    }
                    oldVIndexToVIndexMap[vIndex] = newVIndex;
                    buildingMeshData->AddIndexData(newVIndex);
                    buildingMeshData->AddVertexCount(1);
                    newVIndex++;
                }
                else
                {
                    buildingMeshData->AddIndexData(oldVIndexToVIndexMap[vIndex]);
                }
            }
            buildingMeshData->AddPrimitiveCount(1);
        }
        buildingMeshData->MergeAABB(aabb);
        std::string meshName = meshData->GetName();
        buildingMeshData->SetName(meshName);
        buildingMeshData->AddMeshPartName(meshName + "part");
        buildingMeshData->AddMaterialName(meshName + "part_mat");
        buildingMeshData->AddMeshLod();
        // add mesh part
        auto& meshPart = buildingMeshData->AddMeshPart();
        meshPart.mNameIndex = 0;
        meshPart.mMaterialIndex = 0;
        meshPart.mPrimitiveType = PrimitiveTopology::TriangleList;
        meshPart.mIndexCount = buildingMeshData->GetIndexStream().mCount;
        meshPart.mVertexCount = buildingMeshData->GetVertexCount();
        meshPart.mPrimitiveCount = buildingMeshData->GetPrimitiveCount();
        meshPart.mMeshBound = buildingMeshData->GetAABB();

        std::string resPath = meshAsset->GetName();
        auto last_pos = resPath.rfind('/') + 1;
        std::string filename = resPath.substr(last_pos, resPath.rfind('.') - last_pos);
        resPath.replace(last_pos, resPath.length(), filename + "_parts/" + filename + ".part" + std::to_string(++partID) + ".nda");
        *buildingRes->GetRawLODSetting() = *meshAsset->GetRawLODSetting();
        buildingRes->CreateAsset(resPath);
        outMeshAssets.emplace_back(MeshAssetDataResourcePtr(buildingRes));
    }

    std::vector<std::string> meshs(outMeshAssets.size());
    std::vector<std::vector<std::vector<std::string>>> matss(outMeshAssets.size());
    for (UInt32 idx = 0; idx < outMeshAssets.size(); idx++)
    {
        outMeshAssets[idx]->Serialize(outMeshAssets[idx]->GetName());
        meshs[idx] = outMeshAssets[idx]->GetName();
        UInt32 LodMeshPartCount = outMeshAssets[idx]->GetAssetData()->GetMeshPartCount(0);
        matss[idx] = std::vector<std::vector<std::string>>(LodMeshPartCount, mats);
    }
    modelName.replace(modelName.length() - 4, modelName.length(), ".split");
    CreateModel(meshs, matss, modelName, mam);
}

int ModelChange::ModelLodCombineAndSplit(std::vector<std::vector<std::string>> meshs, const std::vector<std::string>& mats, bool enableSplit, Float2 blockSize, const std::string& outName, ModelAsMode mam)
{
    std::vector<MeshAssetDataResourcePtr> lodMeshAssets;
    for (size_t i = 0; i < meshs.size(); i++)
    {
        std::string lodMeshName = outName + (i == 0 ? "" : "_lod" + std::to_string(i));

        auto combineCount = meshs[i].size();
        std::vector<Float4x4> trans(combineCount);
        std::vector<MeshAssetDataResourcePtr> meshAssets(combineCount);
        for (size_t j = 0; j < combineCount; j++)
        {
            meshAssets[j] = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(meshs[i][j]));
        }
        if (meshAssets.size() <= 0)
            continue;
        if (meshAssets.size() > 1)
        {
            auto lodMeshAsset = gResourceMgr.CreateResourceAs<resource::MeshAssetDataResource>();
            lodMeshAsset->CreateAsset(lodMeshName + ".nda");
            auto ret = static_cast<ModelCombineErrorCode>(resource::MeshAssetDataResource::Combine(meshAssets, trans, {}, lodMeshAsset));
            if (ret != ModelCombineErrorCode::ERR_OK)
                return static_cast<int>(ret);
            lodMeshAssets.emplace_back(lodMeshAsset);
            // lodMeshAssets[i]->Serialize(lodMeshAssets[i]->GetName());
        }
        else
        {
            lodMeshAssets.emplace_back(meshAssets[0]);
        }
    }
    for (size_t i = 1; i < lodMeshAssets.size(); i++)
    {
        auto ret = lodMeshAssets[0]->GetAssetData()->AddLodMeshData(lodMeshAssets[i]->GetAssetData());
        if (ret != EditMeshLodError::ERR_EDITOR_SUCCESS)
            return static_cast<int>(ret);
    }
    lodMeshAssets[0]->GetAsset()->SetName((outName + ".nda").c_str());
    if (enableSplit)
    {
        std::vector<MeshAssetDataResourcePtr> outMeshAssets;
        Float3 center = lodMeshAssets[0]->GetAssetData()->GetBoundingBox().GetCenter();
        int ret = lodMeshAssets[0]->SeparateTo(Float2(center.x, center.z), blockSize, outMeshAssets, mam == ModelAsMode::ModelAsMeshPart);
        if (ret >= 0)
        {
            std::vector<std::string> outMeshs(outMeshAssets.size());
            std::vector<std::vector<std::vector<std::string>>> matss(outMeshAssets.size());
            for (UInt32 idx = 0; idx < outMeshAssets.size(); idx++)
            {
                auto* lodSeting = outMeshAssets[idx]->GetRawLODSetting();
                for (UInt32 lod = 0; lod < lodMeshAssets.size(); lod++)
                {
                    lodSeting->mLevelSettings[lod].mScreenReleativeTransitionHeight = static_cast<float>(0.5 / std::pow(2, lod));
                }
                lodSeting->mCulledHeight = 0.0001f;
                outMeshAssets[idx]->Serialize(outMeshAssets[idx]->GetName());
                outMeshs[idx] = outMeshAssets[idx]->GetName();
                UInt32 LodMeshPartCount = outMeshAssets[idx]->GetAssetData()->GetMeshPartCount(0);
                matss[idx] = std::vector<std::vector<std::string>>(LodMeshPartCount, mats);
            }
            CreateModel(outMeshs, matss, outName + ".split", mam);
            return 0;
        }
        return ret;
    }
    else
    {
        lodMeshAssets[0]->Serialize(lodMeshAssets[0]->GetName());
        return 0;
    }
}

ModelCombineErrorCode ModelChange::ModelCombine(const std::vector<std::string>& meshs, const std::vector<std::string>& mats, const std::vector<Float4x4>& trans, const std::string& outName, ModelAsMode mam)
{
    if (mam == ModelAsMode::ModelAsEntity)
    {
        std::vector<std::vector<std::vector<std::string>>> matss(meshs.size());
        for (size_t i = 0; i < meshs.size(); i++)
        {
            matss[i] = std::vector<std::vector<std::string>>{{mats[i]}};
        }
        CreateModel(meshs, matss, outName, mam);
        return ModelCombineErrorCode::ERR_OK;
    }

    if (meshs.size() != mats.size())
        return ModelCombineErrorCode::ERR_PARAMETERS;

    UInt32 combineCount = static_cast<UInt32>(meshs.size());
    std::vector<MeshAssetDataResourcePtr> meshAssets(combineCount);
    std::vector<MaterialPtr> matAssets(combineCount);
    for (UInt32 idx = 0; idx < combineCount; idx++)
    {
        meshAssets[idx] = TypeCast<resource::MeshAssetDataResource>(gAssetStreamingManager->LoadSynchronously(meshs[idx]));
        matAssets[idx] = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously(mats[idx]));
    }
    NameID openVTProperty = "OPEN_VT";
    bool openVT = false;
    if (const auto& openVrProp = matAssets[0]->GetProperty(openVTProperty))
    {
        openVT = *std::get_if<bool>(openVrProp);
    }

    NameID flagProperty = openVT ? "_EnableVTFlags" : "_EnableMapArrayFlags";
    std::vector<std::pair<NameID, NameID>> texture_properties;
    if (openVT)
        texture_properties = {{"_BaseMap", "_BaseMap_VT_0"}, {"_NormalMap", "_NormalMap_VT_1"}, {"_MaskMap", "_MaskMap_VT_2"}, {"_EmissiveMap", "_EmissiveMap_VT_3"}};
    else
        texture_properties = {{"_BaseMap", "_BaseMapArray"}, {"_NormalMap", "_NormalMapArray"}, {"_MaskMap", "_MaskMapArray"}, {"_EmissiveMap", "_EmissiveMapArray"}};
    
    // check
    auto fx = matAssets[0]->GetFx();
    std::vector<float> standFlag{-1.f, -1.f, -1.f, -1.f};
    if (auto standFlagProp = matAssets[0]->GetProperty(flagProperty))
    {
        standFlag = *std::get_if<std::vector<float>>(standFlagProp);
    }

    for (UInt32 idx = 1; idx < combineCount; idx++)
    {
        const auto& matAsset = matAssets[idx];
        if (matAsset->GetFx()->GetGuid_Str() != fx->GetGuid_Str())
            return ModelCombineErrorCode::ERR_FX;

        std::vector<float> tempFlag{-1.f, -1.f, -1.f, -1.f};
        if (auto flaglagProp = matAssets[idx]->GetProperty(flagProperty))
        {
            tempFlag = *std::get_if<std::vector<float>>(flaglagProp);
        }
        if (tempFlag != standFlag)
            return ModelCombineErrorCode::ERR_TEX_PROPERTY;
    }
    bool hasTextureArray = std::any_of(standFlag.begin(), standFlag.end(), [](float i){return i > 0.f;});

    auto matCombine = resource::Material::CreateMaterialTempInstance(fx);
    matCombine->CreateAsset(outName + ".mtl.nda");
    matCombine->SetProp("TEXTURE_ARRAY_ENABLE", true);

    std::vector<float> enableMapArrayFlags(4, -1.f);
    std::vector<std::vector<std::array<int, 4>>> textureIndexs(combineCount);
    std::vector<std::vector<UInt64>> textureCounts(combineCount);
    for (UInt32 pIdx = 0; pIdx < texture_properties.size(); pIdx++)
    {
        if (hasTextureArray && standFlag[pIdx] <= 0)
            continue;
        const auto& texProp = texture_properties[pIdx].first;
        const auto& texArrayProp = texture_properties[pIdx].second;
        auto texArrayRes = gResourceMgr.CreateResourceAs<resource::Texture2DArray>();
        texArrayRes->CreateAsset(outName + "." + texArrayProp.GetName() + ".nda");
        for (UInt32 idx = 0; idx < combineCount; idx++)
        {
            const auto& matAsset = matAssets[idx];
            if (standFlag[pIdx] > 0)
            {
                auto texProperty = matAsset->GetProperty(texArrayProp);
                if (!texProperty)
                {
                    texArrayRes.reset();
                    break;
                }
                auto texRes = TypeCast<resource::Texture2DArray>(*std::get_if<TexturePtr>(texProperty));
                // add texture
                const auto& textures = texRes->GetTextures();
                textureIndexs[idx].resize(textures.size());
                for (UInt32 texIdx = 0; texIdx < textures.size(); texIdx++)
                {
                    auto& texIndexs = textureIndexs[idx][texIdx];
                    if (pIdx == 0)
                    {
                        if (idx == 0)
                        {
                            bool ret = texArrayRes->AddTexture(textures[texIdx]);
                            Assert(ret);
                            texIndexs[pIdx] = static_cast<int>(texArrayRes->GetTextureCount()) - 1;
                        }
                        else if (auto oldTexIdx = texArrayRes->GetTextureIndex(textures[texIdx]); oldTexIdx >= 0)
                        {
                            texIndexs[pIdx] = oldTexIdx;
                        }
                        else
                        {
                            bool ret = texArrayRes->AddTexture(textures[texIdx]);
                            Assert(ret);
                            texIndexs[pIdx] = static_cast<int>(texArrayRes->GetTextureCount()) - 1;
                        }
                        textureCounts[idx].emplace_back(texArrayRes->GetTextureCount());
                    }
                    else
                    {
                        if (textureCounts[idx][texIdx] > texArrayRes->GetTextureCount())
                        {
                            texArrayRes->AddTexture(textures[texIdx]);
                        }
                        texIndexs[pIdx] = texIndexs[0];
                    }
                }
            }          
            else
            {
                auto texProperty = matAsset->GetProperty(texProp);
                if (!texProperty)
                {
                    texArrayRes.reset();
                    break;
                }
                auto& texIndexs = textureIndexs[idx].size() <= 0 ? textureIndexs[idx].emplace_back() : textureIndexs[idx][0];
                auto texRes = TypeCast<resource::Texture2D>(*std::get_if<TexturePtr>(texProperty));
                if (pIdx == 0)
                {
                    if (auto oldTexIdx = texArrayRes->GetTextureIndex(texRes->GetGuid_Str()); oldTexIdx >= 0)
                    {
                        texIndexs[pIdx] = oldTexIdx;
                    }
                    else
                    {
                        bool ret = texArrayRes->AddTexture(texRes->GetGuid_Str());
                        Assert(ret);
                        texIndexs[pIdx] = static_cast<int>(texArrayRes->GetTextureCount()) - 1;
                    }
                    textureCounts[idx].emplace_back(texArrayRes->GetTextureCount());
                }
                else
                {
                    if (textureCounts[idx][0] > texArrayRes->GetTextureCount())
                    {
                        texArrayRes->AddTexture(texRes->GetGuid_Str());
                    }
                    texIndexs[pIdx] = texIndexs[0];
                }
            }
        }
        if (texArrayRes && texArrayRes->GetTextureCount() > 0)
        {
            if (texArrayRes->GetTextureCount() > 1)
            {
                enableMapArrayFlags[pIdx] = 1.f;
                texArrayRes->Serialize();
                matCombine->SetTextureProp(texArrayProp, TypeCast<resource::Texture>(texArrayRes));
            }
            else
            {
                auto texRes = gResourceMgr.GetResource(gResourceMgr.ConvertGuidToPath(texArrayRes->GetTextures()[0]).c_str());
                matCombine->SetTextureProp(texProp, TypeCast<resource::Texture>(texRes));
            }
        }
    }
    matCombine->SetProp(flagProperty, enableMapArrayFlags);
    matCombine->Serialize({}, matCombine->GetName());

    MeshAssetDataResourcePtr meshCombine = gResourceMgr.CreateResourceAs<resource::MeshAssetDataResource>();
    meshCombine->CreateAsset(outName + ".nda");
    ModelCombineErrorCode ret = static_cast<ModelCombineErrorCode>(resource::MeshAssetDataResource::Combine(meshAssets, trans, textureIndexs, meshCombine));
    if (ret == ModelCombineErrorCode::ERR_OK)
    {
        meshCombine->Serialize(meshCombine->GetName());
        CreateModel({meshCombine->GetName()}, {{{matCombine->GetName()}}}, outName, mam);
    }
    return ret;
}
}   // namespace cross::editor
