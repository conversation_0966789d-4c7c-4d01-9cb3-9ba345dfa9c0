#pragma once

#include "AssetPipeline/Protocol/Model/ImportAnimation.h"
#include "AssetPipeline/Protocol/Model/ImportSkeleton.h"
#include "acl/compression/compress.h"
#include "acl/core/ansi_allocator.h"
#include "acl/decompression/decompress.h"

namespace cross::animcompress {
using DecompressContext = acl::decompression_context<acl::default_transform_decompression_settings>;
using DecompressWriter = acl::track_writer;
using DecompressTransform = rtm::qvvf;

struct MyTrackWriter : DecompressWriter
{
    MyTrackWriter(DecompressTransform* transforms, UInt32 transformsNum)
        : mTransforms(transforms)
        , mTransformsNum(transformsNum)
    {
        Assert(transforms != nullptr && "Transforms array cannot be null");
        Assert(transformsNum != 0 && "Transforms array cannot be empty");
    }

    void RTM_SIMD_CALL write_rotation(UInt32 trackIndex, rtm::quatf_arg0 rotation)
    {
        Assert(trackIndex < mTransformsNum && "Invalid track index.");
        mTransforms[trackIndex].rotation = rotation;
    }

    void RTM_SIMD_CALL write_translation(UInt32 trackIndex, rtm::vector4f_arg0 translation)
    {
        Assert(trackIndex < mTransformsNum && "Invalid track index.");
        mTransforms[trackIndex].translation = translation;
    }

    void RTM_SIMD_CALL write_scale(UInt32 trackIndex, rtm::vector4f_arg0 scale)
    {
        Assert(trackIndex < mTransformsNum && "Invalid track index.");
        mTransforms[trackIndex].scale = scale;
    }

    DecompressTransform* mTransforms;
    UInt32 mTransformsNum;
};

/*
 * This function iterates all keys between iterator "begin" and "end" and collect iterator of keys which can be
 * linearly interpolate from their neighbors with a error tolerance "threshold". Return value is a vector of iterators
 * pointing to keys that are redundant.
 */
template<typename It, typename RatioFunc, typename LinearInterpolateFunc, typename DistanceFunc>
std::vector<It> LinearKeyReduction(It begin, It end, float threshold, RatioFunc Ratio, LinearInterpolateFunc LinearInterpolate, DistanceFunc Distance)
{
    using KeyType = typename std::iterator_traits<It>::value_type;

    static_assert(std::is_invocable_r_v<float, RatioFunc, KeyType const&> && "Wrong Ratio function!");
    static_assert(std::is_invocable_r_v<KeyType, LinearInterpolateFunc, KeyType const&, KeyType const&, float> && "Wrong interpolate function!");
    static_assert(std::is_invocable_r_v<float, DistanceFunc, KeyType const&, KeyType const&> && "Wrong distance function!");

    std::vector<It> result;
    if (end - begin <= 2)
        return result;
    It lastNotredundant = begin;
    for (auto it = begin + 1; it != end - 1; it++)
    {
        float alpha = (Ratio(*lastNotredundant) - Ratio(*it)) / (Ratio(*lastNotredundant) - Ratio(*(it + 1)));
        auto interpResult = LinearInterpolate(*lastNotredundant, *(it + 1), alpha);
        if (Distance(interpResult, *it) <= threshold)
        {
            result.push_back(it);
        }
        else
        {
            lastNotredundant = it;
        }
    }
    return result;
}

/*
 * This function takes in keys and redundant key iterators to erase all redundant keys. Keys will be modified
 * After calling this method. This function can be overload to accept containers other than vector in the future.
 */
template<typename T>
void EraseRedundantAnimPropertyKeys(std::vector<T>& keys, std::vector<typename std::vector<T>::iterator> const& redundantKeyIts)
{
    std::vector<bool> redundantIndicators(keys.size(), false);
    for (auto const& it : redundantKeyIts)
    {
        redundantIndicators[it - keys.begin()] = true;
    }
    std::vector<T> result;
    for (int i = 0; i < redundantIndicators.size(); i++)
    {
        if (redundantIndicators[i] == false)
        {
            result.push_back(std::move(keys[i]));
        }
    }
    keys = result;
}

}   // namespace cross::animcompress

namespace cross::editor {
enum class AnimCompressionType
{
    LinearKeyReducion = 0,
    UniformSample = 1,
    Raw = 2,
    Default = LinearKeyReducion
};

struct AnimCompressionSetting
{
    float TranslationThreshold = 0.0001f;
    float RotationThreshold = 0.0001f;
    float ScaleThreshold = 0.0001f;
    AnimCompressionType Algoirthm = AnimCompressionType::LinearKeyReducion;
};

// Compress animation sequence using LinearKeyReduction
void CompressAnimation_Lkr(AnimationDesc& anim, AnimCompressionSetting const& setting = {});

// Compress animation sequence using Acl
acl::compressed_tracks* CompressAnimation_Acl(acl::iallocator& allocator, const AnimationDesc& animation, const SkeletonDesc& skeleton);

// Check acl compression error
float CompressAnimation_CheckAclError(acl::iallocator& allocator, acl::compressed_tracks* pCompressedAclAnim, const AnimationDesc& rawAnim);

}   // namespace cross::editor
