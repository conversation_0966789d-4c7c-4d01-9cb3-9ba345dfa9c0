#pragma once
#include <set>
#include <array>
#include "AnimBase.h"
#include "ImportAnimation_generated.h"

namespace cross::anim {
struct AnimTrack;
class AnimSectionedTrack;
class AnimReferenceSecTrack;

class IExecutableAnim;
using AnimExecPtr = std::shared_ptr<IExecutableAnim>;
using AnimSeqPtr = std::shared_ptr<IAnimSequence>;
}   // namespace cross::anim

namespace cross::anim::sync 
{
using AdvanceSyncAnimLeaderFunction = std::function<void(float deltaTime, float& curTime, bool const loop)>;
using AdvanceSyncAnimFollowerFunction = std::function<void(float deltaTime, float& curTime, bool const loop, SyncMarkerUpdateRecord& record, SyncMarkerUpdateContext& context)>;

struct Animation_API AnimSyncMarkerTrack
{
    std::vector<sync::AnimSyncMarker> MarkerTrack;

    /* Track data holding in sequence deserialized by flat buffer */
    void Deserialize(const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimSyncMarker>>* fbNotifyEventPtr);


    /* Sync Markers holding in sequence & serialized by flat buffer only while no json markers exists */
    void Serialize(std::vector<std::unique_ptr<CrossSchema::ImportAnimSyncMarkerT>>& csMarkers) const;


    constexpr operator std::vector<sync::AnimSyncMarker>&() noexcept { return MarkerTrack; }
};

struct Animation_API AnimSyncMarkerData
{
public:
    bool RefreshDataFromAnimTrack(AnimReferenceSecTrack& animTrack);
    bool RefreshDataFromAnimTrack(const AnimTrack& animTrack);
    bool RefreshDataFromSingleSeq(const AnimSeqPtr animSeq);

    const CENameSet& GetUniqueMarkerNames() const { return mUniqueMarkerNames; }

    // Given a marker pair( prev SyncMarker & next SyncMarker ) and a SyncMarkerBasedAnimPos,
    // return current time on anim sequence.
    float GetCurrentTimeFromMarkers(SyncMarkerUpdateRecord& markerUpdateRecord, const SyncMarkerBasedAnimPos& syncPosition) const;

    // Given a current time on an anim sequence, return its prev SyncMarker & next SyncMarker if exists.
    void GetMarkerIndicesForTime(SyncMarkerUpdateRecord& markerUpdateRecord, const float currentTime, const bool bLooping, const CENameSet& validMarkerNames) const;

    // Given a SyncMarkerBasedAnimPos on an anim sequence, return its prev SyncMarker & next SyncMarker if exists.
    void GetMarkerIndicesForSyncMarkerBasedPos(SyncMarkerUpdateRecord& markerUpdateRecord, float& outCurTime, const bool bLooping, const SyncMarkerBasedAnimPos& syncPosition) const;

    // Get SyncMarkerBasedAnimPos according to current time and corresponding prevMarker & nextMarker from an ordered SyncMarkerList.
    SyncMarkerBasedAnimPos GetSyncMarkerBasedPosFromMarkerIndicies(SyncMarkerUpdateRecord& markerUpdateRecord, float currentTime) const;

    // Given a SyncMarkerBasedPos, return first matching pos on anim
    float GetFirstMatchingPosFromSyncMarkerBasedPos(const SyncMarkerBasedAnimPos& inSyncPosition) const;
    float GetPrevMatchingPosFromSyncMarkerBasedPos(const SyncMarkerBasedAnimPos& inSyncPosition, const float startingPosition) const;
    float GetNextMatchingPosFromSyncMarkerBasedPos(const SyncMarkerBasedAnimPos& inSyncPosition, const float startingPosition) const;

    void AdvanceMarkerPhaseAsLeader(float& currentTime, SyncMarkerUpdateRecord& markerUpdateRecord, SyncMarkerUpdateContext& markerUpdateContext, const bool bLooping, float moveDelta) const;

    void AdvanceMarkerPhaseAsFollower(float& currentTime, SyncMarkerUpdateRecord& markerUpdateRecord, SyncMarkerUpdateContext& markerUpdateContext, const bool bLooping, float deltaRemaining) const;

private:
    void CollectUniqueNames();

    void CollectMarkersInRange(float prevPos, float newPos, std::vector<PassedMarkerInfo>& outMarkersPassedThisTick, float moveDelta) const;

    void ValidateCurrentSyncPosition(float& inOutCurTime, SyncMarkerUpdateRecord& markerUpdateRecord, const bool bPlayingFwd, const bool bLooping, const SyncMarkerBasedAnimPos& inSyncPos) const;

    void AdvanceDefTrackAsLeader(float inDeltaTime, float& inOutCurTime, bool const inLoop, AnimTrack const& inTrack);
    void AdvanceSecTrackAsLeader(float inDeltaTime, float& inOutCurTime, bool const inLoop, AnimReferenceSecTrack& inTrack);
    void AdvanceSequenceAsLeader(float inDeltaTime, float& inOutCurTime, bool const inLoop, IAnimSequence* inSequence);

    void AdvanceDefTrackAsFollower(float inDeltaTime, float& inOutCurTime, bool const inLoop, AnimTrack const& inTrack, SyncMarkerUpdateRecord& record, SyncMarkerUpdateContext& context);
    void AdvanceSecTrackAsFollower(float inDeltaTime, float& inOutCurTime, bool const inLoop, AnimReferenceSecTrack const& inTrack, SyncMarkerUpdateRecord& record, SyncMarkerUpdateContext& context);
    void AdvanceSequenceAsFollower(float inDeltaTime, float& inOutCurTime, bool const inLoop, IAnimSequence* inSequence, SyncMarkerUpdateRecord& record, SyncMarkerUpdateContext& context);

public:
    std::vector<AnimSyncMarker> SyncMarkers;

private:
    // set of Unique marker names in this animation sequence
    CENameSet mUniqueMarkerNames;

    float mAnimRunStart = 0.f;
    float mAnimRunEnd = 0.f;

    SInt32 mValidMarkerStartIndex = -1;
    SInt32 mValidMarkerEndIndex = -1;

    AdvanceSyncAnimLeaderFunction mAdvanceSyncAnimAsLeader = nullptr;
    AdvanceSyncAnimFollowerFunction mAdvanceSyncAnimAsFollower = nullptr;
};

struct Animation_API AnimExecUpdateRecord
{
    IExecutableAnim* InstancePtr{nullptr};

    float FinalWeight{0.f};

    // marker sync related data
    SyncMarkerUpdateRecord* MarkerUpdateRecord{nullptr};

    // default is false, this may change when AnimSyncGroupInstance::Prepare() called
    bool bCanUseMarkerSync{false};

    float LeaderScore{0.f};

    CEName SlotName = AnimSlotGroup::sDefaultSlotName;

public:
    AnimExecUpdateRecord();

    AnimExecUpdateRecord(IExecutableAnim* instancePtr, float inFinalWeight, CEName const& inSlotName = AnimSlotGroup::sDefaultSlotName);

    // for update record sorting
    inline bool operator<(const AnimExecUpdateRecord& other) const { return LeaderScore > other.LeaderScore; }
};

// This structure is used to either advance or synchronize animation players
struct AnimExecUpdateContext
{
public:
    AnimExecUpdateContext(float inDeltaTime, const CENameSet& inValidMarkers)
        : DeltaTime(inDeltaTime)
        , mIsLeader(true)
        , mLeaderDelta(0.f)
        , mPrevAnimLengthRatio(0.f)
        , mCurAnimLengthRatio(0.f)
        , mCanUseMarkerSync(!inValidMarkers.empty())
        , MarkerUpdateContext(inValidMarkers)
    {}

    AnimExecUpdateContext(float inDeltaTime)
        : DeltaTime(inDeltaTime)
        , mIsLeader(true)
        , mLeaderDelta(0.f)
        , mPrevAnimLengthRatio(0.f)
        , mCurAnimLengthRatio(0.f)
        , mCanUseMarkerSync(false)
    {}

    // are we the leader of our sync group (or ungrouped)?
    bool IsLeader() const { return mIsLeader; }

    bool IsFollower() const { return !mIsLeader; }

    void ConvertToFollower() { mIsLeader = false; }

    void SetLeaderDelta(float inLeaderDelta) { mLeaderDelta = inLeaderDelta; }

    float GetLeaderDelta() const { return mLeaderDelta; }

    void SetPrevAnimLengthRatio(float ratio) { mPrevAnimLengthRatio = ratio; }

    void SetCurAnimLengthRatio(float ratio) { mCurAnimLengthRatio = ratio; }

    // Returns the previous synchronization point (normalized time; only legal to call if ticking a follower)
    float GetPrevAnimLengthRatio() const
    {
        Assert(IsFollower());
        return mPrevAnimLengthRatio;
    }

    // Returns the synchronization point (normalized time; only legal to call if ticking a follower)
    float GetCurAnimLengthRatio() const
    {
        Assert(IsFollower());
        return mCurAnimLengthRatio;
    }

    bool CanUseMarkerSync() const { return mCanUseMarkerSync; }

    void InvalidateMarkerSync() { mCanUseMarkerSync = false; }

    float DeltaTime{0.f};

    SyncMarkerUpdateContext MarkerUpdateContext;

private:
    bool mIsLeader{true};

    float mLeaderDelta{0.f};

    // Float in 0 - 1 range representing how far through an animation we were before ticking
    float mPrevAnimLengthRatio{0.f};

    // Float in 0 - 1 range representing how far through an animation we are
    float mCurAnimLengthRatio{0.f};

    bool mCanUseMarkerSync{false};
};


struct AnimSyncGroupInstance
{
public:
    // The list of anim exec in this group which are going to be evaluated this frame
    std::vector<AnimExecUpdateRecord> ActiveAnimExecs;

    // The current group leader
    // @note : before ticking, this is invalid
    // after ticking, this should contain the real leader
    // during ticket, this list gets sorted by LeaderScore of AnimExecTickRecord,
    // and it starts from 0 index, but if that fails due to invalid position, it will go to the next available leader
    SInt32 GroupLeaderIndex{0};

    // Valid marker names for this sync group
    CENameSet ValidMarkers;

    // Can we use sync markers for ticking this sync group
    bool CanUseMarkerSync{false};

    SyncMarkerUpdateContext MarkerUpdateContext;

public:
    AnimSyncGroupInstance()
        : GroupLeaderIndex(-1)
        , CanUseMarkerSync(false)
    {}

    void Reset()
    {
        GroupLeaderIndex = -1;
        ActiveAnimExecs.clear();
        CanUseMarkerSync = false;
        MarkerUpdateContext = SyncMarkerUpdateContext();
    }

    // Checks the last update record in the ActiveAnimExecs array to see if it's a better leader than the current candidate.
    // This should be called once for each record added to ActiveAnimExecs, after the record is setup.
    void CheckUpdateRecordForLeadership(AnimSyncGroupRole::Type membershipType);

    void UpdateGroup(const AnimSyncGroupInstance* inLastFrameGroupInstance, float deltaTime);

private:
    // Called after all update records have been added but before animExec are actually updated
    void Prepare(const AnimSyncGroupInstance* inLastFrameGroupInstance);

    // Called after leader has been updated and decided
    void Finalize(const AnimSyncGroupInstance* inLastFrameGroupInstance);
};

class Animation_API AnimSync final
{
    // <SyncGroupName, SyncGroupInstance>
    using SyncGroupMap = CENameMap<CEName, AnimSyncGroupInstance>;

public:
    AnimSync() = default;
    ~AnimSync() = default;

    void Reset();

    void AddAnimExecUpdateRecord(const AnimExecUpdateRecord& inUpdateRecord);

    void UpdateAnimations(float deltaTime);

    // flip sync group read/write indices
    void TickSyncGroupWriteIndex() { mSyncGroupWriteIndex = GetSyncGroupReadIndex(); }

    // Gets the sync group we should be reading from
    SInt32 GetSyncGroupReadIndex() const { return 1 - mSyncGroupWriteIndex; }

    // Gets the sync group we should be writing to
    SInt32 GetSyncGroupWriteIndex() const { return mSyncGroupWriteIndex; }

private:
    // The list of animation assets which are going to be evaluated this frame and need to be ticked (ungrouped)
    std::array<std::vector<AnimExecUpdateRecord>, 2> mUngroupedActiveAnimExecs;

    // The set of tick groups for this anim instance */
    std::array<SyncGroupMap, 2> mSyncGroupMaps;

    // Current sync group buffer index
    SInt32 mSyncGroupWriteIndex{0};
};
}   // namespace cross::anim::sync
