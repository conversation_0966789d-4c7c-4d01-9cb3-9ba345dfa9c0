#include "EnginePrefix.h"
#include "CEAnimation/Animator/AnimatorParameter.h"
#include "ECS/Develop/Framework/Types.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"


namespace cross::anim {
Parameter::TypeRegistryMap const Parameter::sTypeRegistry = {{typeid(bool), ParamMode::Bool},
                                                             {typeid(int), ParamMode::Int},
                                                             {typeid(float), ParamMode::Float},
                                                             {typeid(std::string), ParamMode::String},
                                                             {typeid(Float2), ParamMode::Vector2},
                                                             {typeid(Float3), ParamMode::Vector3},
                                                             {typeid(Float4), ParamMode::Vector4},
                                                             {typeid(NodeTransform), ParamMode::Transform},
                                                             {typeid(void*), ParamMode::Customized}};

/////////////////////////////////////////////
// Parameter
//
/////////////////////////////////////////////
ParamGetResult Parameter::Extract(Parameter const* inParameter, void* outData)
{
    if (inParameter == nullptr || outData == nullptr)
        return ParamGetResult::CannotConvert;

    auto re = inParameter->ConvertFromType(outData);
    return re.first != ParamMode::Type::None ? ParamGetResult::Converted : ParamGetResult::CannotConvert;
}

/////////////////////////////////////////////
// ParameterWithSerializer
//
/////////////////////////////////////////////

static Float2 ExtractFloat2(DeserializeNode const& s)
{
    return Float2(s["X"].AsFloat(), s["Y"].AsFloat());
}
static Float3 ExtractFloat3(DeserializeNode const& s)
{
    return Float3(s["X"].AsFloat(), s["Y"].AsFloat(), s["Z"].AsFloat());
}
static Float4 ExtractFloat4(DeserializeNode const& s)
{
    return Float4(s["X"].AsFloat(), s["Y"].AsFloat(), s["Z"].AsFloat(), s["W"].AsFloat());
}
static Quaternion ExtractQuaternion(DeserializeNode const& s)
{
    return Quaternion(s["X"].AsFloat(), s["Y"].AsFloat(), s["Z"].AsFloat(), s["W"].AsFloat());
}

Parameter* ParameterWithSerializer::Deserialize(DeserializeNode const& s) const
{
    auto const& param_type = s[ANIMGRAPH_NODE_TYPE].AsString();
    auto const& param_name = s[ANIMGRAPH_NAME].AsString();
    auto const& param_value = s["Value"];
    Parameter* parameter = nullptr;

    switch (ParamMode::Operate(param_type))
    {
    case ParamMode::Bool:
        parameter = TYPE_CAST(Parameter*, new BoolParameter(CEName(param_name.c_str()), param_value.AsBoolean()));
        break;
    case ParamMode::Int:
        parameter = TYPE_CAST(Parameter*, new IntParameter(CEName(param_name.c_str()), param_value.AsInt32()));
        break;
    case ParamMode::Float:
        parameter = TYPE_CAST(Parameter*, new FloatParameter(CEName(param_name.c_str()), param_value.AsFloat()));
        break;
    case ParamMode::String:
        parameter = TYPE_CAST(Parameter*, new StringParameter(CEName(param_name.c_str()), param_value.AsString()));
        break;
    case ParamMode::Vector2:
        parameter = TYPE_CAST(Parameter*, new Vector2Parameter(param_name.c_str(), ExtractFloat2(param_value)));
        break;
    case ParamMode::Vector3:
        parameter = TYPE_CAST(Parameter*, new Vector3Parameter(param_name.c_str(), ExtractFloat3(param_value)));
        break;
    case ParamMode::Vector4:
        parameter = TYPE_CAST(Parameter*, new Vector4Parameter(param_name.c_str(), ExtractFloat4(param_value)));
        break;
    case ParamMode::Transform:
    {
        NodeTransform t(ExtractFloat3(param_value["Scale"]), ExtractQuaternion(param_value["Quaternion"]), ExtractFloat3(param_value["Translation"]));
        parameter = TYPE_CAST(Parameter*, new TransformParameter(param_name.c_str(), t));
        break;
    }
    case ParamMode::Customized:
    {
        void* default_value = nullptr;
        parameter = TYPE_CAST(Parameter*, new CustomizedParameter(param_name.c_str(), default_value));
        break;
    }
    default:
        AssertMsg(false, "Unknown parameter type");
        break;
    }
    return parameter;
}

Parameter* ParameterWithSerializer::Clone(const Parameter* inParam) const
{
    Parameter* out = nullptr;
    switch (inParam->GetParameterType())
    {
    case ParamMode::Type::Bool:
    {
        out = TYPE_CAST(Parameter*, new BoolParameter(inParam->Name(), reinterpret_cast<const BoolParameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Int:
    {
        out = TYPE_CAST(Parameter*, new IntParameter(inParam->Name(), reinterpret_cast<const IntParameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Float:
    {
        out = TYPE_CAST(Parameter*, new FloatParameter(inParam->Name(), reinterpret_cast<const FloatParameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::String:
    {
        out = TYPE_CAST(Parameter*, new StringParameter(inParam->Name(), reinterpret_cast<const StringParameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Vector2:
    {
        out = TYPE_CAST(Parameter*, new Vector2Parameter(inParam->Name(), reinterpret_cast<const Vector2Parameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Vector3:
    {
        out = TYPE_CAST(Parameter*, new Vector3Parameter(inParam->Name(), reinterpret_cast<const Vector3Parameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Vector4:
    {
        out = TYPE_CAST(Parameter*, new Vector4Parameter(inParam->Name(), reinterpret_cast<const Vector4Parameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Transform:
    {
        out = TYPE_CAST(Parameter*, new TransformParameter(inParam->Name(), reinterpret_cast<const TransformParameter*>(inParam)->GetValue()));
        break;
    }
    case ParamMode::Type::Customized:
    {
        out = TYPE_CAST(Parameter*, new CustomizedParameter(inParam->Name(), reinterpret_cast<const CustomizedParameter*>(inParam)->GetValue()));
        break;
    }
    default:
        AssertMsg(false, "Unknown parameter type");
        break;
    }
    return out;
}
}   // namespace cross::anim