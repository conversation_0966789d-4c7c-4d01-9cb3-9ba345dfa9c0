#include "EnginePrefix.h"
#include "CEAnimation/AnimRuntime.h"
#include "CEAnimation/Composite/AnimSectionedTrack.h"

namespace cross::anim
{
    const CEName sDefaultSectionName = "DefaultSection";

    /////////////////////////////////////////////
    // AnimSection
    //
    /////////////////////////////////////////////
    AnimSegment const* AnimSection::GetSegmentAtTime(TrackUnWrapperH inTrackTime) const
    {
        Assert(IsInRange(inTrackTime) || std::abs(inTrackTime - TrackEndPos()) < 0.001f);

        // Segments overlap on a single frame occurs between segments.
        // So last frame of Segment1 overlaps first frame of Segment2.
        // But in that case we want Segment2 to win.
        // So we iterate through these segments in reverse 
        // and return the first match with an inclusive range check.
        for (SInt32 idx = (SInt32)Segments.size() - 1; idx >= 0; --idx)
        {
            const AnimSegment* segment = Segments[idx];
            if (segment->IsInRange(inTrackTime))
                return segment;
        }

        return nullptr;
    }

    AnimSegment const* AnimSection::GetSegmentAtTime(SectionUnwrapperH inSectionTime) const
    {
        TrackUnWrapperH cursor = { inSectionTime + TrackStartPos() };
        return GetSegmentAtTime(cursor);
    }

    /////////////////////////////////////////////
    // AnimReferenceSection
    //
    /////////////////////////////////////////////
    std::optional<AnimReferenceSection> AnimReferenceSection::Next() const
    {
        if (SectionPtr->NextSectionH == SectionHandle::InvalidHandle())
            return std::nullopt;

        auto nextSection = mOwnerPtr->GetNextSection(SectionPtr);

        if (nextSection != nullptr)
            return AnimReferenceSection(nextSection, mOwnerPtr);
        else
            return std::nullopt;
    }

    AnimSegment const* AnimReferenceSection::GetActiveSegment() const
    {
        return SectionPtr->GetSegmentAtTime(mCursor);
    }

    /////////////////////////////////////////////
    // AnimSectionedTrack
    //
    /////////////////////////////////////////////
    bool AnimSectionedTrack::Deserialize(const SlotTrackRes& slotTrack, const std::vector<AnimSeqPtr>& inAnimSeqs, const std::vector<AnimNotifyEvent *>& NotifiesInTrack)
    {
        Assert(slotTrack.IsAnySectionExists());
        AnimTrack::Deserialize(slotTrack, inAnimSeqs, NotifiesInTrack);

        // instance section here
        AnimSections.clear();
        const auto& sectionDescs = slotTrack.SectionsDesc;
        for (auto sectionIndex = 0; sectionIndex < sectionDescs.size(); ++sectionIndex)
        {
            auto const& sectionDesc = sectionDescs[sectionIndex];

            AnimSection curSection;

            curSection.Name = sectionDesc.Name;
            curSection.Loop = sectionDesc.Loop;
            curSection.NextSectionH = sectionDesc.NextIndex;

            // grab all exec_anims holding in current section
            SInt32 SegmentEndIndex = static_cast<SInt32>(std::min(static_cast<size_t>(sectionDesc.SegmentEndIndex), AnimSegments.size()));
            for (auto segment = sectionDesc.SegmentStartIndex; segment < SegmentEndIndex; segment++)
            {
                Assert(AnimSegments.size() > segment);

                curSection.Segments.push_back(&AnimSegments[segment]);
                
                // forbid for temporary 
                //curSection.PriorityDeltas.push_back(sectionDesc.PriorityDeltas[segment - sectionDesc.SegmentStartIndex]);
            }

            // grab all notifies holding in current section
            if (!curSection.Segments.empty())
            {
                for (auto* notify : NotifiesInTrack)
                {
                    if (curSection.IsInRange({notify->GetTriggerTime()}))
                    {
                        curSection.AllNotifies.push_back(notify);
                        
                        if (notify->IsInstantNotify())
                            curSection.InstantNotifies.push_back(notify);
                    }
                }

                AnimSections.push_back(std::move(curSection));
            }
        }

        // sort sections by section start pos in track
        auto sort_section = [](const AnimSection& lhs, const AnimSection& rhs) { return lhs.TrackStartPos() < rhs.TrackStartPos(); };
        std::sort(AnimSections.begin(), AnimSections.end(), sort_section);

        // grab each section's sync markers offset
        SInt32 curAccumlateMarkersCount = 0;
        for (auto sectionIndex = 0; sectionIndex < AnimSections.size(); ++sectionIndex)
        {
            auto& section = AnimSections[sectionIndex];
            section.mSyncMarkerIndexStart = curAccumlateMarkersCount;

            for (auto segmentIndex = 0; segmentIndex < section.Segments.size(); segmentIndex++)
            {
                auto& segment = section.Segments[segmentIndex];
                const auto& syncMarkers = segment->SequencePtr->GetSyncMarkers();

                curAccumlateMarkersCount += static_cast<SInt32>(syncMarkers.size());
            }

            section.mSyncMarkerIndexEnd = curAccumlateMarkersCount;
        }

        return true;
    }

    SectionHandle AnimSectionedTrack::GetSectionIndexAtTime(TrackUnWrapperH inTime) const
    {
        if (inTime > GetRunLength())
            return SectionHandle::InvalidHandle();

        // check segmenets range from END to START
        auto itr = std::find_if(AnimSections.rbegin(), AnimSections.rend(), [inTime](auto& elem) {
            if (elem.TrackStartPos() <= inTime && elem.TrackEndPos() >= inTime)
                return true;
            return false;
            });

        if (itr != AnimSections.rend())
        {
            auto index = std::distance(std::begin(AnimSections), itr.base()) - 1; 
            return {static_cast<UInt32>(index)};
        }
        return SectionHandle::InvalidHandle();
    }

    AnimSection const* AnimSectionedTrack::GetSectionAtTime(TrackUnWrapperH inTime) const
    {
        SectionHandle sectionH = GetSectionIndexAtTime(inTime);
        return sectionH == SectionHandle::InvalidHandle() ? nullptr : &AnimSections[sectionH];
    }

    AnimSection* AnimSectionedTrack::GetSectionAtTime(TrackUnWrapperH inTime)
    {
        return const_cast<AnimSection*>(const_cast<const AnimSectionedTrack*>(this)->GetSectionAtTime(inTime));
    }

    AnimSection const* AnimSectionedTrack::GetSectionAtHandle(SectionHandle inHandle) const
    {
        Assert(inHandle < AnimSections.size());
        return &AnimSections[inHandle];
    }

    SectionHandle AnimSectionedTrack::GetSectionHandleByName(CEName sectionName) const
    {
        for (auto sectionIndex = 0; sectionIndex < AnimSections.size(); ++sectionIndex)
        {
            if (AnimSections[sectionIndex].Name == sectionName)
                return SectionHandle::From(sectionIndex);
        }
        return SectionHandle::InvalidHandle();
    }

    /////////////////////////////////////////////
    // AnimReferenceSecTrack
    //
    /////////////////////////////////////////////
    AnimReferenceSecTrack::AnimReferenceSecTrack(AnimSectionedTrack const& inTrack)
        : mReference(inTrack)
        , mActiveRefSection(&inTrack.AnimSections[0], this)
    {
        mTrackMarkersData.RefreshDataFromAnimTrack(inTrack);

        mNextSections.resize(mReference.AnimSections.size());
        mPrevSections.resize(mNextSections.size());
        for (int i = 0; i < mReference.AnimSections.size(); i++)
        {
            mNextSections[i] = SectionHandle::InvalidHandle();
            mPrevSections[i] = SectionHandle::InvalidHandle();
            mSectionHandleMap[mReference.AnimSections[i].Name] = SectionHandle::From(i);
        }
    }

    void AnimReferenceSecTrack::GetPose(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& extractContext) const
    {
        auto contextWithCurTime = extractContext.Move(mCurPos);

        // cursor should be in the range of active section
        //Assert(mActiveRefSection.SectionPtr == mReference.GetSectionAtTime(contextWithCurTime.CurrentTime()));

        // grab active segment from section 
        auto activeSegment = mActiveRefSection.GetActiveSegment();
        Assert(activeSegment != nullptr);

        return activeSegment->GetPose(outPose, contextWithCurTime);
    }

    void AnimReferenceSecTrack::ExtractAnimCurves(AnimCurveData& outAnimCurves) const
    {
        // cursor should be in the range of active section
        //Assert(mActiveRefSection.SectionPtr == mReference.GetSectionAtTime(mCurPos));

        // grab active segment from section
        auto activeSegment = mActiveRefSection.GetActiveSegment();
        Assert(activeSegment != nullptr);

        return activeSegment->ExtractAnimCurves(mCurPos, outAnimCurves);
    }

    AnimReferenceSection const& AnimReferenceSecTrack::GetActiveSection() const
    {
        if (mActiveRefSection.SectionPtr->IsInRange(mCurPos))
            return mActiveRefSection;

        time::TrackUnWrapperH newCurPos = mCurPos;

        auto curSection = mActiveRefSection.SectionPtr;
        auto preSection = curSection;
        curSection = GetNextSection(curSection);
        while ( curSection != nullptr)
        {
            newCurPos.mVal = newCurPos.mVal - preSection->TrackEndPos().mVal + curSection->TrackStartPos().mVal;
            if (curSection->IsInRange(newCurPos))
            {
                break;
            }
            preSection = curSection;
            curSection = GetNextSection(curSection);
        }
        mCurPos = newCurPos;
        if (curSection)
            mActiveRefSection = AnimReferenceSection(curSection, this);
        else
            mActiveRefSection = AnimReferenceSection(preSection, this);
        if (!curSection)
        {
            mIsComplete = true;
        }
        return mActiveRefSection;
    }

    AnimReferenceSection& AnimReferenceSecTrack::GetActiveSection()
    {
        auto& ConstSection = const_cast<const AnimReferenceSecTrack*>(this)->GetActiveSection();
        return const_cast<AnimReferenceSection&>(ConstSection);
    }

    bool AnimReferenceSecTrack::IsCompleted(float inBlendTime /*= 0.001f*/, bool isLoopAllowed /*= false*/) const
    {
        if (mIsComplete == true)
            return true;

        mIsComplete = std::abs(mCurPos - mReference.GetRunLength()) < inBlendTime
            && mActiveRefSection.SectionPtr->Loop == false;
        return mIsComplete;
    }

    AdvanceAnim::Type AnimReferenceSecTrack::Advance(float deltaTime, bool isLoopAllowed /*= false*/)
    {
        // initialize if first advance
        if (mCurPos == TrackUnWrapperH::InvalidHandle())
            mCurPos = { 0 };

        // grab active section here
        auto* currentSection = &GetActiveSection();
        Assert(currentSection != nullptr);

        // advance prepos here
        mPrePos = mCurPos;

        // advance current section here
        auto tempCursor = mCurPos;
        auto advancedRe = AnimRuntime::AdvanceTime(
            currentSection->SectionPtr->Loop,
            deltaTime,
            tempCursor,
            currentSection->SectionPtr->TrackStartPos(),
            currentSection->SectionPtr->TrackEndPos());

        // advance will stop until no more section be crossed 
        float leftDeltaTime = deltaTime - (tempCursor - mCurPos);

        int maxLoopCount = 2;
        while (advancedRe == AdvanceAnim::Type::AA_Finished)
        {
            if (leftDeltaTime < 0.001f)
                break;

            if (--maxLoopCount <= 0)
                break;
            
            // move part of delta time here
            mCurPos = tempCursor;

            // got next section here, failed means meet end
            auto nextSection = &GetActiveSection();

            // advance next section happened here
            advancedRe = AnimRuntime::AdvanceTime(
                nextSection->SectionPtr->Loop,
                leftDeltaTime,
                tempCursor,
                nextSection->SectionPtr->TrackStartPos(),
                nextSection->SectionPtr->TrackEndPos());

            // grab part of delta time left to move further 
            leftDeltaTime = leftDeltaTime - (tempCursor - mCurPos);

            if (advancedRe == AdvanceAnim::AA_Looped)
                leftDeltaTime += GetActiveSection().SectionPtr->RunLength();
        }

        // move part of delta time here
        mCurPos = tempCursor;

        // got absolute next section here, failed means meet end
        auto newCurSection = &GetActiveSection();
        if (newCurSection != currentSection)
        {
            currentSection = newCurSection;
            currentSection->SetCursor(mCurPos);
            advancedRe = AdvanceAnim::Type::AA_CrossOver;
        }

        // calculate delta
        if (mCurPos != mPrePos)
        {
            // figure out delta time
            mDeltaTime = mCurPos - mPrePos;

            // if we went against play rate, this means looping happened.
            if ((deltaTime * mDeltaTime) < 0.f)
                mDeltaTime += GetActiveSection().SectionPtr->RunLength();
        }

        return advancedRe;
    }

    void AnimReferenceSecTrack::RatioScaleCursor(float preRatio, float curRatio)
    {
        auto& section = GetActiveSection();
        auto length = section.SectionPtr->RunLength();

        mPrePos = { preRatio * length + section.SectionPtr->TrackStartPos() };
        mCurPos = { curRatio * length + section.SectionPtr->TrackStartPos() };
    }

    bool AnimReferenceSecTrack::MoveNext() const
    { 
        auto& curSection = GetActiveSection();
        auto nextSection = curSection.Next();

        if (nextSection)
        {
            // GetActiveSection will move forward automatically by mCurPos 
            mCurPos = nextSection->GetCursor();

            mActiveRefSection = GetActiveSection();
            return true;
        }

        return false;
    }

    bool AnimReferenceSecTrack::SetNextSection(CEName currentSectionName, CEName nextSectionName)
    {
        auto curSectionHandle = mReference.GetSectionHandleByName(currentSectionName);
        auto nextSectionHandle = mReference.GetSectionHandleByName(nextSectionName);

        return SetNextSection(curSectionHandle, nextSectionHandle);
    }

    bool AnimReferenceSecTrack::SetNextSection(SectionHandle curSectionHandle, SectionHandle nextSectionHandle)
    {
        if (curSectionHandle >= mNextSections.size() || nextSectionHandle >= mNextSections.size())
        {
            return false;
        }

        if (curSectionHandle && nextSectionHandle)
        {
            if (mNextSections[curSectionHandle])
                mPrevSections[mNextSections[curSectionHandle]] = SectionHandle::InvalidHandle();
            if (mPrevSections[nextSectionHandle])
                mNextSections[mPrevSections[nextSectionHandle]] = SectionHandle::InvalidHandle();
            mNextSections[curSectionHandle] = nextSectionHandle;
            mPrevSections[nextSectionHandle] = curSectionHandle;
            return true;
        }

        return false;
    }

    const AnimSection* AnimReferenceSecTrack::GetNextSection(const AnimSection* Section) const
    {
        auto curSectionHandle = mReference.GetSectionHandleByName(Section->Name);
        if (mNextSections[curSectionHandle])
        {
            auto* nextSection = mReference.GetSectionAtHandle(mNextSections[curSectionHandle]);
            return nextSection;
        }
        return nullptr;
    }
}   // namespace cross::anim
