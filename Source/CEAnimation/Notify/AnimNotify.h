#pragma once
#include "ImportAnimation_generated.h"
#include "CEAnimation/AnimBase.h"

namespace cross
{
class IGameWorld;
}

namespace cross::anim {

    class IAnimator
    {
    public:
        virtual IGameWorld* GetWorld() const  = 0;
    };

    namespace NotifyGetResult
    {
        enum Type
        {
            None,
            JumpSection
        };
    };

    class IAnimSequence;
    class Animator;
    class AnimNotifyEvent;    
    class AnimReferenceTrackBase;

    // Function pointer that takes a stream and produces an notify object by type name
    using NotifyProduceWithFb = std::function<std::unique_ptr<AnimNotifyEvent>(CrossSchema::ImportAnimNotifyEvent const* fbNotifyEventPtr)>;
    using NotifyProduceWithJson = std::function<std::unique_ptr<AnimNotifyEvent>(const DeserializeNode& node)>;

    /*
     * Triggers an animation notify.  Each AnimNotifyEvent contains an AnimNotify object
     * which has its Notify method called and passed to the animation.
     */
    class Animation_API AnimNotifyEvent : public AnimLinkableElement
    {
    public:
        /** Animation's Notify trigger while its Blend weight great than <PERSON><PERSON><PERSON><PERSON> otherwise ignore cur Notify **/
        DEFRES_VARIABLE_TO_STRING(float, TriggerWeightThreshold, 0.f)
    
        /** Animation's Notify trigger while skeltal mesh LOD less than Threshold otherwise ignore cur Notify **/
        DEFRES_VARIABLE_TO_STRING(UInt32, NotifyLODThreshold, 0)
        
        virtual ~AnimNotifyEvent() {}

        /*  */
        virtual NotifyGetResult::Type Broadcast(IAnimator const* inAnimator, IExecutableAnim const* inExecAnim, CEName const& inSlotName, cross::IGameWorld* inGameWorld) const = 0;
        
        /* Notify data holding in sequence deserialized by flat buffer */
        virtual void Deserialize(CrossSchema::ImportAnimNotifyEvent const* fbNotifyEventPtr);
        /* Notify data holding NOT in sequence deserialized by json no matter type */
        virtual void Deserialize(const DeserializeNode& node);


        /* Notify data holding in sequence serialized by flat buffer */
        virtual void Serialize(std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>& csNotifyEvent) const;
        /* Notify data holding NOT in sequence serialized by json no matter type */
        virtual void Serialize(SerializeNode& node) const;


        /** Returns true if this trigger is called instantly, less performance */
        virtual bool IsInstantNotify() const { return false; }
        /** Returns true if this trigger is handled centralized by AnimQueue, high performance */
        inline bool IsQueuedNotify() const { return !IsInstantNotify(); }
        /** Returns true after notify processed & discard the left notifies for jump section, less performance */
        virtual bool IsBranchingPointNotify() const { return false; }

        /** Returns the actual trigger time for this notify. In some cases this may be different to the DisplayTime that has been set */
        inline float GetTriggerTime() const { return TriggerTimeInSec; }
    
        bool operator<(const AnimNotifyEvent& other) const { return TriggerTimeInSec < other.TriggerTimeInSec; }   
        bool operator==(const AnimNotifyEvent& other) const { return Name == other.Name; }
  
    protected:   
        // Static: One and the same instance for all function calls.
        static std::map<CEName, NotifyProduceWithFb>& GetRegistryFb()
        {
            static std::map<CEName, NotifyProduceWithFb> registry;
            return registry;
        }

        static std::map<CEName, NotifyProduceWithJson>& GetRegistryJson();

        friend struct AnimNotifyTrack;

        friend class AnimNotifyTrackModifier;

    };

    template<class T>
    class AnimNotifyEventRegister : public AnimNotifyEvent
    {
    public:
        // sRegistered needs to be assigned from the very beginning
        virtual ~AnimNotifyEventRegister()
        {
            if (!sRegistered)
            {
                std::string_view fullname = NameDetailPretty<T>();
                LOG_ERROR("bad register for {}", fullname);
            }
        }   

        static inline CEName GetTypeName()
        {
            std::string_view fullname = NameDetailPretty<T>();
            size_t start = fullname.find_last_of(':') + 1;
            fullname.remove_prefix(start);
            return CEName{fullname.data(), (UInt32)fullname.size()};
        }

        static inline bool RegisterType()
        {
            const auto&& ceName = GetTypeName();

            auto& registeryFb = AnimNotifyEvent::GetRegistryFb();
            registeryFb[ceName] = std::bind(&AnimNotifyEventRegister::ProduceByFb, std::placeholders::_1);

            auto& registeryJson = AnimNotifyEvent::GetRegistryJson();
            registeryJson[ceName] = std::bind(&AnimNotifyEventRegister::ProduceByJson, std::placeholders::_1);

            return true;
        }


        /* Acculmate type info into json */
        virtual void Serialize(SerializeNode& node) const override
        {
            AnimNotifyEvent::Serialize(node);
            // Force assign type variable which got no reltv class member
            ASGK_TYPE_TO_STRING(node, T);
        }


    protected:       
        static std::unique_ptr<AnimNotifyEvent> ProduceByFb(CrossSchema::ImportAnimNotifyEvent const* fbNotifyEventPtr)
        { 
            std::unique_ptr<AnimNotifyEvent> instance = std::make_unique<T>();            
            Assert(fbNotifyEventPtr != nullptr);
            instance->Deserialize(fbNotifyEventPtr); 
            return instance;
        }

        static std::unique_ptr<AnimNotifyEvent> ProduceByJson(const DeserializeNode& node)
        {
            std::unique_ptr<AnimNotifyEvent> instance = std::make_unique<T>();            
            Assert(!node.IsNull());
            instance->Deserialize(node);
            return instance;
        }

        static const inline bool sRegistered = RegisterType();
    };

    struct Animation_API AnimNotifyTrack
    {
    public:
        virtual ~AnimNotifyTrack();
    
        /* Track data holding in sequence deserialized by flat buffer */
        bool Deserialize(const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportAnimNotifyEvent>>* fbNotifyEventPtr);
        /* Track data holding NOT in sequence deserialized by json no matter type */
        bool Deserialize(const DeserializeNode& node);
    

        /* Track data holding in sequence serialized by flat buffer */
        void Serialize(std::vector<std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>>& csNotifies) const;
        /* Track data holding NOT in sequence serialized by json no matter type */
        void Serialize(SerializeNode& node) const;
        SerializeNode Serialize() const;

    public:
        std::string Name;
        std::vector<AnimNotifyEvent*> Notifies;
    };

    struct Animation_API AnimNotifyEventReference
    {
        AnimNotifyEventReference() = default;
    
        AnimNotifyEventReference(AnimNotifyEventReference&& rhs) = default;
    
        AnimNotifyEventReference& operator=(const AnimNotifyEventReference& rhs) = default;
    
        AnimNotifyEventReference(const AnimNotifyEventReference& rhs)
            : mNotify(rhs.mNotify)
            , mSlotName(rhs.mSlotName)
            , mNotifySourceExecAnim(rhs.mNotifySourceExecAnim)
            , mOwner(rhs.mOwner)
        {}
    
        AnimNotifyEventReference(AnimNotifyEvent const* inNotify)
            : mNotify(inNotify)
        {}
    
        AnimNotifyEvent const* GetNotify() const { return mNotify; }
    
        inline void SetOwner(IAnimator const* inOwner) { mOwner = inOwner; }
    
        inline void SetExecInstance(IExecutableAnim const* inExecInstance, CEName const& inSlotName = "") 
        {
            mNotifySourceExecAnim = inExecInstance; 
            mSlotName = inSlotName;
        }
    
        NotifyGetResult::Type Broadcast() const;
    
        friend bool operator==(const AnimNotifyEventReference& lhs, const AnimNotifyEventReference& rhs) { return lhs.mNotify == rhs.mNotify; }
    
        friend bool operator==(const AnimNotifyEventReference& lhs, const AnimNotifyEvent& rhs);
    
    private:
        const AnimNotifyEvent* mNotify{nullptr};
        
        const IExecutableAnim* mNotifySourceExecAnim{nullptr};
    
        /* The mesh we're currently triggering a AnimNotify for (so we can retrieve per instance information) */
        const IAnimator* mOwner{nullptr};

        CEName mSlotName = "";
    };
}   // namespace cross::anim
