#include "EnginePrefix.h"
#include "CEAnimation/Skeleton/FilteredSkeleton.h"
#include "Resource/MeshAssetData.h"
#include <memory>

namespace cross::anim {
FilteredSkeleton::FilteredSkeleton(const std::vector<SkBoneHandle>& inUsedBoneIndices, const Skeleton* inSkeleton)
    : mChosenBoneIndicesArray(inUsedBoneIndices)
    , mRefSkeletonForFilter(nullptr)
{
    ResetBoneIndices(inUsedBoneIndices, inSkeleton);
}

FilteredSkeleton::FilteredSkeleton(const std::vector<SkBoneHandle>& inUsedBoneIndices, const MeshAssetData* inSkeletalMesh)
    : mChosenBoneIndicesArray(inUsedBoneIndices)
    , mRefSkeletonForFilter(nullptr)
{
    ResetBoneIndices(inUsedBoneIndices, inSkeletalMesh);
}

void FilteredSkeleton::ResetBoneIndices(const std::vector<SkBoneHandle>& inUsedBoneIndices, const Skeleton* inSkeleton)
{
    mChosenBoneIndicesArray = inUsedBoneIndices;
    mRunSkeleton = inSkeleton;
    mRunSkeltMesh = nullptr;

    Assert(mRunSkeleton != nullptr);
    Initialize();
}

void FilteredSkeleton::ResetBoneIndices(const std::vector<SkBoneHandle>& inUsedBoneIndices, const MeshAssetData* inSkeletalMesh)
{
    mChosenBoneIndicesArray = inUsedBoneIndices;
    mRunSkeleton = nullptr;
    mRunSkeltMesh = inSkeletalMesh;

    Assert(mRunSkeltMesh != nullptr);
    Initialize();
}

void FilteredSkeleton::ResetPose(std::vector<NodeTransform>& outNodes) const
{
    const auto maxBoneNum = static_cast<UInt32>(mRefSkeletonForFilter->GetRawBoneNum());
    const auto usedBoneNum = static_cast<UInt32>(mChosenBoneIndicesArray.size());
    outNodes.resize(usedBoneNum);

    // Assign FilteredRefPose Transform Array by RefSkeletonForFilter bindPose.
    auto const& refPoseScale = mRefSkeletonForFilter->GetRawRefBonePoseScale();
    auto const& refPoseRot = mRefSkeletonForFilter->GetRawRefBonePoseRotate();
    auto const& refPoseTrans = mRefSkeletonForFilter->GetRawRefBonePoseTranslate();

    for (size_t index = 0; index < usedBoneNum; ++index)
    {
        const SkBoneHandle boneIndex = mChosenBoneIndicesArray[index];
        Assert(boneIndex < maxBoneNum);

        outNodes[index] = NodeTransform(refPoseScale[boneIndex], refPoseRot[boneIndex], refPoseTrans[boneIndex]);
    }
}

void FilteredSkeleton::Initialize()
{
    if (mRunSkeltMesh == nullptr)
        mRefSkeletonForFilter = &mRunSkeleton->GetReferenceSkeleton();
    else
        mRefSkeletonForFilter = mRunSkeltMesh->GetRefSkeleton();

    const auto maxBoneNum = static_cast<UInt32>(mRefSkeletonForFilter->GetRawBoneNum());
    const auto usedBoneNum = static_cast<UInt32>(mChosenBoneIndicesArray.size());
    mBoneMasks.resize(maxBoneNum, false);

    for (UInt32 index = 0; index < usedBoneNum; ++index)
    {
        const SkBoneHandle boneIndex = mChosenBoneIndicesArray[index];
        Assert(boneIndex < maxBoneNum);
        mBoneMasks[boneIndex] = true;
    }

    BuildLinkUp();
}

void FilteredSkeleton::BuildLinkUp()
{
    mSkeletonToBoneIndicesArray.resize(mBoneMasks.size(), POSE_BONE_INDEX_NONE);
    for (int index = 0; index < mChosenBoneIndicesArray.size(); ++index)
    {
        SkBoneHandle chosenBoneIndex = mChosenBoneIndicesArray[index];
        Assert(chosenBoneIndex.mVal < mBoneMasks.size());
        mSkeletonToBoneIndicesArray[chosenBoneIndex] = PoseBoneHandle::From(index);
    }

    mParentSubscriptIndicesArray.resize(mChosenBoneIndicesArray.size());
    for (int index = 0; index < mChosenBoneIndicesArray.size(); ++index)
    {
        SkBoneHandle chosenBoneIndex = mChosenBoneIndicesArray[index];
        SkBoneHandle parentBoneIndex = mRefSkeletonForFilter->GetRawBoneParentIndex(chosenBoneIndex);

        if (parentBoneIndex == SK_BONE_INDEX_NONE)
        {
            Assert(index == 0);
            mParentSubscriptIndicesArray[index] = POSE_BONE_INDEX_NONE;
        }
        else
        {
            Assert(parentBoneIndex < mBoneMasks.size() && mBoneMasks[parentBoneIndex] == true);
            mParentSubscriptIndicesArray[index] = mSkeletonToBoneIndicesArray[parentBoneIndex];
        }
    }
}

}   // namespace cross::anim
