#include "CECommon/Common/CmdSettings.h"
namespace cross
{
#define DeclNameObjectW(cmd, defaultV, container) container.SetKeyValue(cmd, defaultV);

    static CmdSettings sCmdSetting;

    CmdSettings& CmdSettings::Inst()
    {
        return sCmdSetting;
    }

    void ExtensibleSetting::ParseExtensibleSetting(argparse::ArgumentParser& argsparser, std::vector<std::string>& args)
    {
        if (args.size() > 1)
        {
            for (auto& itr : mCmdValues.mContext)
            {
                {
                    auto key = itr.first.GetCString();
                    std::visit(
                        [this, key, &itr, &argsparser](auto&& arg) {
                            using V = std::decay_t<decltype(arg)>;
                            if constexpr (std::is_same_v<V, bool*>)
                                *std::get<bool*>(itr.second) = argsparser.get<bool>(key);
                            else if constexpr (std::is_same_v<V, int*>)
                                *std::get<int*>(itr.second) = argsparser.get<int>(key);
                            else if constexpr (std::is_same_v<V, float*>)
                                *std::get<float*>(itr.second) = argsparser.get<float>(key);
                            else if constexpr (std::is_same_v<V, std::uint32_t*>)
                                *std::get<std::uint32_t*>(itr.second) = argsparser.get<std::uint32_t>(key);
                            // comment due to cannot pass CI, always report c++ error in server build, need some c++ expert
                            // else
                            //    static_assert(std::always_false_v<V>, "non-exhaustive visitor!");
                        },
                        itr.second);
                }
            }
        }
    }
    bool ExtensibleSetting::HasProp(const char* classname, const char* name)    
    {
        auto cls = gbf::reflection::query_meta_class_by_name(classname);
        if (cls)
        {
            return cls->HasProperty(name);
        }
        return false;
    }
    }
