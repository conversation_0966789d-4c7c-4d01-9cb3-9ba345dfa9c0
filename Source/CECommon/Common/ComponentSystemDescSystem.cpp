#include "EnginePrefix.h"
#include "ComponentSystemDescSystem.h"

namespace cross {
SystemDesc* ComponentSystemDescSystem::GetGameSystemDescByName(const char* Name) 
{
    HashString key(Name);
    return mGameSystemDescMap[key.GetHash32()];
}
SystemDesc* ComponentSystemDescSystem::GetRenderSystemDescByName(const char* Name)
{
    HashString key(Name);
    return mRenderSystemDescMap[key.GetHash32()];
}
}   // namespace cross