#pragma once
#include "CrossBase/CEMetaMacros.h"
#include "CECommon/Common/CECommonForward.h"
namespace cross {
class PhysicsEngine;
class FrameParamManager;
class RenderEngine;
class SettingsManager;
class CrossEngine;
class ECSConfig;
class EngineModuleConfig;
class WorldHelper;
class GlobalSystemDescSystem;
class ComponentSystemDescSystem;
class InputManager;
class MemBlockPool;
class RenderPipelineConfigure;
class PrefabManager;
class ModuleManagerSystem;
class InGameTerminalManager;
class AudioEngine;
#if NCNN_ASSEMBLE
class InferenceEngine;
#endif

namespace filesystem {
    class FileSystem;
}

namespace ecs {
    class Framework;
}

class CEMeta(Puerts) EngineGlobal
{
public:
    CECOMMON_API static void InitEngineGlobal();

    CECOMMON_API static EngineGlobal& Inst();

    static CrossEngine* GetEngine()
    {
        return Inst().mEngine;
    }

    static RenderEngine* GetRenderEngine()
    {
        return Inst().mRenderEngine;
    }

    static ECSConfig* GetECSConfig()
    {
        return Inst().mECSConfig;
    }
    
    CECOMMON_API CEFunction(ScriptCallable)
    static SettingsManager* GetSettingMgr()
    {
        return Inst().mSettingMgr;
    }

    // static RenderPipelineConfigure* GetPipelineConfigure() { return Inst().mPipelineConfigure; }

    static FrameParamManager* GetFrameParamMgr()
    {
        return Inst().mFrameParamMgr;
    }

    static GlobalSystemDescSystem* GetGlobalSystemDescSystem()
    {
        return Inst().mGlobalDescSystem;
    }

    static ComponentSystemDescSystem* GetCompSystemDescSystem()
    {
        return Inst().mCSDescSystem;
    }

    static PhysicsEngine* GetPhysicsEngine()
    {
        return Inst().mPhysicsEngine;
    }

    static InputManager* GetInputManager()
    {
        return Inst().mInputManager;
    }

	static AudioEngine* GetAudioEngine()
	{
		return Inst().mAudioEngine;
	}

    static ecs::Framework& GetECSFramework()
    {
        return *Inst().mECSFramework;
    }

    static filesystem::FileSystem* GetFileSystem()
    {
        return Inst().mFileSystem;
    }

    static MemBlockPool* GetMemoryBlockPool()
    {
        return Inst().mMemBlockPool;
    }

    static PrefabManager* GetPrefabMgr()
    {
        return Inst().mPrefabMgr;
    };

    static InGameTerminalManager* GetTerminalManager()
    {
        return Inst().mTerminalManager;
    }
    static EngineModuleConfig* GetEngineModuleConfig()
    {
        return Inst().mEngineModuleConfig;
    }
#if NCNN_ASSEMBLE
    static InferenceEngine* GetInferenceEngine()
    {
        return Inst().mInferenceEngine;
    }
#endif
public:
    EngineGlobal(){};
    ~EngineGlobal() = default;
    EngineGlobal(const EngineGlobal&) = delete;
    EngineGlobal& operator=(EngineGlobal const&) = delete;

public:
    void SetCrossEngine(CrossEngine* engine)
    {
        mEngine = engine;
    }

    void SetRenderEngine(RenderEngine* engine)
    {
        mRenderEngine = engine;
    }

    void SetECSFramework(ecs::Framework* ecsFramework)
    {
        mECSFramework = ecsFramework;
    }

    void SetSystemPriorityConfig(ECSConfig* systemPriorityConfig)
    {
        mECSConfig = systemPriorityConfig;
    }
    void SetEngineModuleConfig(EngineModuleConfig* moduleConfig)
    {
        mEngineModuleConfig = moduleConfig;
    }
    void SetSettingManager(SettingsManager* mgr)
    {
        mSettingMgr = mgr;
    }

    // void SetPipelineConfigure(RenderPipelineConfigure* configure) { mPipelineConfigure = configure; }

    void SetFrameParamManager(FrameParamManager* mgr)
    {
        mFrameParamMgr = mgr;
    }

    void SetGlobalSystemDescSystem(GlobalSystemDescSystem* descSystem)
    {
        mGlobalDescSystem = descSystem;
    }

    void SetCompSystemDescSystem(ComponentSystemDescSystem* csSystem)
    {
        mCSDescSystem = csSystem;
    }

	void SetAudioEngine(AudioEngine* audioEngine)
	{
		mAudioEngine = audioEngine;
	}

    void SetPhysicsEngine(PhysicsEngine* physicsEngine)
    {
        mPhysicsEngine = physicsEngine;
    }

    void SetInputManager(InputManager* inputManager)
    {
        mInputManager = inputManager;
    }

    void SetFileSystem(filesystem::FileSystem* inFileSystem)
    {
        mFileSystem = inFileSystem;
    }

    void SetMemoryBlockPool(MemBlockPool* eventPool)
    {
        mMemBlockPool = eventPool;
    }

    void SetPrefabMgr(PrefabManager* prefabMgr)
    {
        mPrefabMgr = prefabMgr;
    }
    void SetTerminalManager(InGameTerminalManager* terminalmanager)
    {
        mTerminalManager = terminalmanager;
    }
#if NCNN_ASSEMBLE
    void SetInferenceEngine(InferenceEngine* infEng)
    {
        mInferenceEngine = infEng;
    }
#endif
protected:
    CrossEngine* mEngine{nullptr};
    RenderEngine* mRenderEngine{nullptr};
    ecs::Framework* mECSFramework{nullptr};
    ECSConfig* mECSConfig{nullptr};
    EngineModuleConfig* mEngineModuleConfig{nullptr};
    SettingsManager* mSettingMgr{nullptr};
    FrameParamManager* mFrameParamMgr{nullptr};
    GlobalSystemDescSystem* mGlobalDescSystem{nullptr};
    ComponentSystemDescSystem* mCSDescSystem{nullptr};
    PhysicsEngine* mPhysicsEngine{nullptr};
    InputManager* mInputManager{nullptr};
	AudioEngine* mAudioEngine{nullptr};
    filesystem::FileSystem* mFileSystem{nullptr};
    MemBlockPool* mMemBlockPool{nullptr};
    // RenderPipelineConfigure*   mPipelineConfigure{nullptr};
    PrefabManager* mPrefabMgr{nullptr};
    InGameTerminalManager* mTerminalManager{nullptr};
#if NCNN_ASSEMBLE
    InferenceEngine* mInferenceEngine{nullptr};
#endif

private:
    friend class CrossEngine;
};
}   // namespace cross
