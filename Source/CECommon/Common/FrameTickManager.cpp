#include "PlatformPrefix.h"
#include "FrameTickManager.h"
#include <stack>
#include <Runtime/Reflection/TypeGet.h>
#include "reflection/meta/meta_class.hpp"
DECLARE_CPU_TIMING_GROUP(GroupFrameTick);

namespace cross
{
    void IFrameTick::Init()
    {
        auto name = GetFrameTickName().GetCString();
        
        {
			auto& baseBuildFunc = gbf::reflection::query_meta_class_by_name("cross::WorldInternalSystem")->GetFunction("OnBuildPreUpdateTasks");
           
			gbf::reflection::IFuncCaller* buildPtr = nullptr;
            bool isOverride = false;
			if (gbf::reflection::query_meta_class_by_name(name)->HasFunction("OnBuildPreUpdateTasks"))
			{
                auto& buildFunc = gbf::reflection::query_meta_class_by_name(name)->GetFunction("OnBuildPreUpdateTasks");
                buildPtr = buildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);
                isOverride = buildFunc.call_type() == gbf::reflection::FuncCallType::OverloadObjectCall;
			}
			
            auto baseBuildPtr = baseBuildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);

            mIsValid[static_cast<UInt32>(FrameTickStage::PreUpdate)] = buildPtr && isOverride;
        }

        {
            auto& baseBuildFunc = gbf::reflection::query_meta_class_by_name("cross::WorldInternalSystem")->GetFunction("OnBuildUpdateTasks");
            bool isOverride = false;
            gbf::reflection::IFuncCaller* buildPtr = nullptr;
            if (gbf::reflection::query_meta_class_by_name(name)->HasFunction("OnBuildUpdateTasks"))
            {
                auto& buildFunc = gbf::reflection::query_meta_class_by_name(name)->GetFunction("OnBuildUpdateTasks");
                buildPtr = buildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);
                isOverride = buildFunc.call_type() == gbf::reflection::FuncCallType::OverloadObjectCall;
            }

            auto baseBuildPtr = baseBuildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);

            mIsValid[static_cast<UInt32>(FrameTickStage::Update)] = buildPtr && isOverride;
        }

		{
			auto& baseBuildFunc = gbf::reflection::query_meta_class_by_name("cross::WorldInternalSystem")->GetFunction("OnBuildPostUpdateTasks");
            bool isOverride = false;
			gbf::reflection::IFuncCaller* buildPtr = nullptr;
			if (gbf::reflection::query_meta_class_by_name(name)->HasFunction("OnBuildPostUpdateTasks"))
			{
                auto& buildFunc = gbf::reflection::query_meta_class_by_name(name)->GetFunction("OnBuildPostUpdateTasks");
                buildPtr = buildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);
                isOverride = buildFunc.call_type() == gbf::reflection::FuncCallType::OverloadObjectCall;
			}
			
            auto baseBuildPtr = baseBuildFunc.GetOneCaller(gbf::reflection::FuncLanguageType::AsCxx);

            mIsValid[static_cast<UInt32>(FrameTickStage::PostUpdate)] = buildPtr && isOverride;
        }
    }

	void IFrameTick::InitializeTasks(FrameTickStage stage)
	{
		mTasks[int32_t(stage)].clear();
		mSortedTasks[int32_t(stage)].clear();
	}

	void IFrameTick::FinalizeTasks(FrameTickStage stage)
	{
		std::stack<TaskFunction*> stack;
		for (const auto& task : mTasks[int32_t(stage)])
		{
			if (!task->mOutDegrees)
			{
				stack.push(task.get());
			}
		}

		while (!stack.empty())
		{
			auto top = stack.top();
			stack.pop();
			for (const auto& task : top->GetSuccessors())
			{
				if (task->mOutDegrees-- == 1)
				{
					stack.push(task);
				}
			}
			mSortedTasks[int32_t(stage)].push_back(top);
		}
	}

	void FrameTickManager::AddSystemDependency(IFrameTick* predecessor, IFrameTick* succcessor)
	{
		Assert(succcessor);
		//remove the override logic which deletes render system dependencies 
		auto succcessorDpd = CreateOrGetDependency(succcessor);
        if (predecessor)
		{
			auto predecessorDpd = CreateOrGetDependency(predecessor);
			predecessorDpd->mSuccessors.push_back(succcessorDpd);
			succcessorDpd->mPredecessors.push_back(predecessorDpd);
		}
	}

    void FrameTickManager::AddSystemDependency(const DependencyGroup& predecessors, const DependencyGroup& succcessors)
    {
        if (predecessors.size() == 0)
        {
            for (auto succcessor : succcessors)
            {
				if (succcessor)
				{
                    AddSystemDependency(nullptr, succcessor);
				}
            }
        }
        else
        {
            for (auto predecessor : predecessors)
            {
                for (auto succcessor : succcessors)
                {
                    AddSystemDependency(predecessor, succcessor);
                }
            }
        }
    }

    void FrameTickManager::BuildExecutionGraph(FrameParam* frameParam)
	{
		SCOPED_CPU_TIMING(GroupFrameTick, "BuildFrameTask");
        {
            std::stack<Dependency*> stack;

            for (int32_t i = 0; i != mDependencies.size(); i++)
            {
                mDependencies[i]->mOutDegrees = static_cast<int32_t>(mDependencies[i]->mPredecessors.size());

                if (mDependencies[i]->mPredecessors.empty())
                {
                    stack.push(mDependencies[i].get());
                }
            }

            mSortedDependencies.clear();
            while (!stack.empty())
            {
                auto top = stack.top();
                stack.pop();
                for (auto successor : top->mSuccessors)
                {
                    if (successor->mOutDegrees-- == 1)
                    {
                        stack.push(successor);
                    }
                }
                mSortedDependencies.push_back(top);
            }

            // Fail if there exists a circular dependency.
            Assert(mSortedDependencies.size() == mDependencies.size());
        }

		for (int32_t stage = 0; stage != int32_t(FrameTickStage::NumStages); stage++)
		{
			mBeginTasks[stage].Reset();
			mEndTasks[stage].Reset();

            for (auto dependency : mSortedDependencies)
            {
                dependency->mSystem->InitializeTasks(FrameTickStage(stage));
                auto enable = dependency->mSystem->GetEnableTick();
                if (std::string(dependency->mSystem->GetFrameTickName().GetCString()).ends_with("G"))
                {
                    enable &= dependency->mSystem->IsValidStage(stage);
                }

                if (enable)
				{
                    dependency->mSystem->BuildTasks(FrameTickStage(stage), frameParam);
				}
                dependency->mSystem->FinalizeTasks(FrameTickStage(stage));
			}

			for (auto dependency : mSortedDependencies)
			{
				if (dependency->mPredecessors.empty())
				{
					dependency->mBeginEvent = threading::DispatchAndHold([](auto) {});
				}
				else
				{
					threading::TaskEventArray taskEventArray;
					for (auto predecessor : dependency->mPredecessors)
					{
						taskEventArray.Add(predecessor->mEndEvent);
					}

					dependency->mBeginEvent = threading::Dispatch(taskEventArray, [](auto) {});
				}

				if (dependency->mSystem->GetSortedTasks(FrameTickStage(stage)).empty())
				{
					dependency->mEndEvent = threading::Dispatch({ dependency->mBeginEvent }, [](auto) {});
				}
				else
				{
					class FrameTickTask
					{
					public:
						FrameTickTask(TaskFunction* taskFunction) : mTaskFunction(taskFunction) {}

						void Execute(const threading::TaskEventPtr& selfTaskEvent)
						{
							SCOPED_CPU_TIMING(GroupDispatch, "FrameTickTask");
							mTaskFunction->Execute();
						}

						threading::ThreadID GetThreadToExecuteOn() const
						{
							return mTaskFunction->GetThreadToExecuteOn();
						}

                        threading::Priority GetTaskPriority() const
                        {
                            return mTaskFunction->GetTaskPriority();
                        }

					private:
						TaskFunction* mTaskFunction;
					};

					threading::TaskEventArray endTaskEventArray;
					for (auto task : dependency->mSystem->GetSortedTasks(FrameTickStage(stage)))
					{
						if (!task->GetNumPredecessors())
						{
							task->mTaskEvent = threading::TTask<FrameTickTask>::Create({ dependency->mBeginEvent }).DispatchTask(task);
						}
						else
						{
							threading::TaskEventArray taskEventArray;
							for (auto predecessor : task->GetPredecessors())
							{
								taskEventArray.Add(predecessor->mTaskEvent);
							}

							task->mTaskEvent = threading::TTask<FrameTickTask>::Create({ taskEventArray }).DispatchTask(task);
						}

						if (!task->GetNumSuccessors())
						{
							endTaskEventArray.Add(task->mTaskEvent);
						}
					}

					dependency->mEndEvent = threading::Dispatch(endTaskEventArray, [](auto) {});
				}
			}

			for (auto dependency : mSortedDependencies)
			{
				if (dependency->mPredecessors.empty())
				{
					mBeginTasks[stage].Add(dependency->mBeginEvent);
				}
			}

			for (auto dependency : mSortedDependencies)
			{
				if (dependency->mSuccessors.empty())
				{
					mEndTasks[stage].Add(dependency->mEndEvent);
				}
			}
		}
	}

	void FrameTickManager::Tick()
	{
		SCOPED_CPU_TIMING(GroupFrameTick, "FrameTaskTick");

		{
			SCOPED_CPU_TIMING(GroupFrameTick, "PreUpdateStage");

			mBeginTasks[0].ReleaseTasks();
			mEndTasks[0].WaitForCompletion();
		}

		{
			SCOPED_CPU_TIMING(GroupFrameTick, "UpdateStage");

			mBeginTasks[1].ReleaseTasks();
			mEndTasks[1].WaitForCompletion();
		}

		{
			SCOPED_CPU_TIMING(GroupFrameTick, "PostUpdateStage");

			mBeginTasks[2].ReleaseTasks();
			mEndTasks[2].WaitForCompletion();
		}
	}

	std::string FrameTickManager::GenerateDependencyGraphviz()
	{
		std::string result;
		result = "digraph Dependency {\n";

		for (int32_t stage = 0; stage != int32_t(FrameTickStage::NumStages); stage++)
		{
			result += "\tsubgraph cluster_" + std::to_string(stage) + " {\n";

			for (const auto dependency : mSortedDependencies)
			{
				for (const auto predecessors : dependency->mPredecessors)
				{
					result += "\t\t";
                    result += "\"S" + std::to_string(stage) + "_" + std::string(predecessors->mSystem->GetFrameTickName().GetCString()) + "\"";
					result += " -> ";
                    result += "\"S" + std::to_string(stage) + "_" + std::string(dependency->mSystem->GetFrameTickName().GetCString()) + "\"";
					result += ";\n";
				}
                result += "\t\t";
                result += "\"S" + std::to_string(stage) + "_" + std::string(dependency->mSystem->GetFrameTickName().GetCString()) + "\"";
                result += ";\n";
			}

			result += "\t}\n";
		}

		result += "}\n";
		return result;
	}

	FrameTickManager::Dependency* FrameTickManager::CreateOrGetDependency(IFrameTick* system)
	{
		Assert(system);

		auto iter = mMap.find(system);
		if (iter == mMap.end())
		{
			mDependencies.push_back(std::make_unique<Dependency>());

			auto dependency = mDependencies.back().get();
			dependency->mSystem = system;
            system->Init();
			mMap.emplace(system, dependency);

			return dependency;
		}
		else
		{
			return iter->second;
		}
	}

    CECOMMON_API PostTickUpdates* PostTickUpdates::Get()
    {
        static PostTickUpdates instance;
        return &instance;
    }
}
