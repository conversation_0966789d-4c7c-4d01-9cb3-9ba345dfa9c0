
#include "PlatformPrefix.h"
#include "CrossBase/Hash/Hash.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "Log.h"

namespace cross
{

const GlobalSystemDesc* GlobalSystemDescSystem::CreateOrGetGlobalSystemDesc(const char* name, bool isGameSystem)
{
	if (!name || name[0] == '\0')
	{
		LOG_ERROR("Invalid global system desc name!!!");
		return nullptr;
	}

	StringHash32 hash = HashFunction::HashString32(name);

	std::lock_guard<std::mutex> lock(mMtx);

	if (isGameSystem)
	{
		auto it = mGloalSystemDescMapG.find(hash);
		if (it != mGloalSystemDescMapG.end())
		{
			LOG_ERROR("Create global system with same name: {}, very suspicious!!", name);
			return it->second;
		}
		else
		{
			auto* desc = new GlobalSystemDesc(mGlobalDescCountG++, name, isGameSystem);
			mGloalSystemDescMapG.emplace(hash, desc);
			return desc;
		}
	}
	else
	{
		auto it = mGloalSystemDescMapR.find(hash);
		if (it != mGloalSystemDescMapR.end())
		{
			LOG_ERROR("Create global system with same name: {}, very suspicious!!", name);
			return it->second;
		}
		else
		{
			auto* desc = new GlobalSystemDesc(mGlobalDescCountR++, name, isGameSystem);
			mGloalSystemDescMapR.emplace(hash, desc);
			return desc;
		}
	}

	
}

}