#pragma once
#include <vector>
#include "CECommonForward.h"
#include "CECommon/Common/SystemEvent.h"

namespace cross
{
enum class SystemCatalog
{
    SystemCatalogGameGlobal,
    SystemCatalogRenderGlobal,
    SystemCatalogGame,
    SystemCatalogRender,
};

enum RunningStage : UInt8
{
    RunningStage_StartFrame = 0,
    RunningStage_BeginFrame,
    RunningStage_FirstUpdate,
    RunningStage_PreUpdate,
    RunningStage_Update,
    RunningStage_PostUpdate,
    RunningStage_EndFrame,
    RunningState_FinishFrame,
};

struct OnSystemEnableChangedEventData
{
    enum class ChangeFlag
    {
        EnableChanged,
    };

    ChangeFlag mChangeFlag{ ChangeFlag::EnableChanged };
};

using OnSystemEnableChangedEvent = SystemEvent<OnSystemEnableChangedEventData>;

class SystemCommon
{
};
}