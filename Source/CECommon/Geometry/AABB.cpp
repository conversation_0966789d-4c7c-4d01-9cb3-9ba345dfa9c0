#include "EnginePrefix.h"
#include "AABB.h"

namespace cross
{
void CalculateClosestPoint(const Float3& rkPoint, const AABB_deperecated& rkBox, Float3& outPoint, float& outSqrDistance)
    {
        // compute coordinates of point in box coordinate system
        Float3 kClosest = rkPoint - rkBox.GetCenter();

        // project test point onto box
        float fSqrDistance = 0.0f;
        float fDelta;

        for (int i = 0; i < 3; i++)
        {
            if (kClosest[i] < -rkBox.GetExtent(i))
            {
                fDelta = kClosest[i] + rkBox.GetExtent(i);
                fSqrDistance += fDelta * fDelta;
                kClosest[i] = -rkBox.GetExtent(i);
            }
            else if (kClosest[i] > rkBox.GetExtent(i))
            {
                fDelta = kClosest[i] - rkBox.GetExtent(i);
                fSqrDistance += fDelta * fDelta;
                kClosest[i] = rkBox.GetExtent(i);
            }
        }

        // Inside
        if (fSqrDistance == 0.0F)
        {
            outPoint = rkPoint;
            outSqrDistance = 0.0F;
        }
        // Outside
        else
        {
            outPoint = kClosest + rkBox.GetCenter();
            outSqrDistance = fSqrDistance;
        }
    }

    bool allFinite(const Float3& val)
    {
        {
            if (isnan(val.x) || isnan(val.y) || isnan(val.z) || isinf(val.x) || isinf(val.y) || isinf(val.z))
            {
                return false;
            }
            else
            {
                return true;
            }
        }
    }

    // Sphere-AABB distance, Arvo's algorithm
    float CalculateSqrDistance(const Float3& rkPoint, const AABB_deperecated& rkBox)
    {
        Float3 closest = rkPoint - rkBox.GetCenter();
        float sqrDistance = 0.0f;

        for (int i = 0; i < 3; ++i)
        {
            float clos = closest[i];
            float ext = rkBox.GetExtent(i);
            if (clos < -ext)
            {
                float delta = clos + ext;
                sqrDistance += delta * delta;
                closest[i] = -ext;
            }
            else if (clos > ext)
            {
                float delta = clos - ext;
                sqrDistance += delta * delta;
                closest[i] = ext;
            }
        }

        return sqrDistance;
    }

    void AABB_deperecated::GetVertices(Float3* outVertices) const
    {
        outVertices[0] = mCenter + Float3{-mExtent.x, -mExtent.y, -mExtent.z};
        outVertices[1] = mCenter + Float3{+mExtent.x, -mExtent.y, -mExtent.z};
        outVertices[2] = mCenter + Float3{-mExtent.x, +mExtent.y, -mExtent.z};
        outVertices[3] = mCenter + Float3{+mExtent.x, +mExtent.y, -mExtent.z};

        outVertices[4] = mCenter + Float3{-mExtent.x, -mExtent.y, +mExtent.z};
        outVertices[5] = mCenter + Float3{+mExtent.x, -mExtent.y, +mExtent.z};
        outVertices[6] = mCenter + Float3{-mExtent.x, +mExtent.y, +mExtent.z};
        outVertices[7] = mCenter + Float3{+mExtent.x, +mExtent.y, +mExtent.z};
    }

    void MinMaxAABB::GetVertices(Float3 outVertices[8]) const
    {
        //    7-----6
        //   /     /|
        //  3-----2 |
        //  | 4   | 5
        //  |     |/
        //  0-----1
        outVertices[0] = { m_Min.x, m_Min.y, m_Min.z };
        outVertices[1] = { m_Max.x, m_Min.y, m_Min.z };
        outVertices[2] = { m_Max.x, m_Max.y, m_Min.z };
        outVertices[3] = { m_Min.x, m_Max.y, m_Min.z };
        outVertices[4] = { m_Min.x, m_Min.y, m_Max.z };
        outVertices[5] = { m_Max.x, m_Min.y, m_Max.z };
        outVertices[6] = { m_Max.x, m_Max.y, m_Max.z };
        outVertices[7] = { m_Min.x, m_Max.y, m_Max.z };
    }


    bool AABB_deperecated::IsInside(const Float3& inPoint) const
    {
        if (inPoint[0] < mCenter[0] - mExtent[0])
            return false;
        if (inPoint[0] > mCenter[0] + mExtent[0])
            return false;

        if (inPoint[1] < mCenter[1] - mExtent[1])
            return false;
        if (inPoint[1] > mCenter[1] + mExtent[1])
            return false;

        if (inPoint[2] < mCenter[2] - mExtent[2])
            return false;
        if (inPoint[2] > mCenter[2] + mExtent[2])
            return false;

        return true;
    }

    void AABB_deperecated::Encapsulate(const Float3& inPoint)
    {
        MinMaxAABB temp = *this;
        temp.Encapsulate(inPoint);
        FromMinMaxAABB(temp);
    }

	void AABB_deperecated::ScaleExtent(const Float3& scale)
	{
		mExtent[0] *= scale.x;
		mExtent[1] *= scale.y;
		mExtent[2] *= scale.z;
	}

    bool MinMaxAABB::IsInside(const Float3& inPoint) const
    {
        if (inPoint[0] < m_Min[0])
            return false;
        if (inPoint[0] > m_Max[0])
            return false;

        if (inPoint[1] < m_Min[1])
            return false;
        if (inPoint[1] > m_Max[1])
            return false;

        if (inPoint[2] < m_Min[2])
            return false;
        if (inPoint[2] > m_Max[2])
            return false;

        return true;
    }

    MinMaxAABB AddAABB(const MinMaxAABB& lhs, const MinMaxAABB& rhs)
    {
        MinMaxAABB minMax;
        if (lhs.IsValid())
            minMax = lhs;

        if (rhs.IsValid())
        {
            minMax.Encapsulate(rhs.GetMax());
            minMax.Encapsulate(rhs.GetMin());
        }

        return minMax;
    }


    inline Float3 RotateExtents(const Float3& extents, const Float4x4& rotation)
    {
        Float3 newExtents{};
        for (int i = 0; i < 3; i++)
        {
            newExtents[i] = Abs(rotation(i, 0) * extents.x) + Abs(rotation(i, 1) * extents.y) + Abs(rotation(i, 2) * extents.z);
        }
        return newExtents;
    }

    void TransformAABB(const AABB_deperecated& aabb, const Float3& position, const Quaternion& rotation, AABB_deperecated& result)
    {
        //Matrix3x3f m = rotation.;

        //Vector3f extents = RotateExtents(aabb.GetExtent(), m);
        //Vector3f center = m * aabb.GetCenter();
        //center += position;
        //result.SetCenterAndExtent(center, extents);
    }

    void TransformAABB(const AABB_deperecated& aabb, const Float4x4& transform, AABB_deperecated& result)
    {
        //Vector3f extents = RotateExtents(aabb.GetExtent(), transform);
        ////Vector3f center = transform.MultiplyPoint3(aabb.GetCenter());
        //Transform3f t{ transform };
        //Vector3f center = t * aabb.GetCenter();
        //result.SetCenterAndExtent(center, extents);
    }

    void TransformAABBSlow(const AABB_deperecated& aabb, const Float4x4& transform, AABB_deperecated& result)
    {
        //MinMaxAABB transformed;

        //Transform3f t{ transform };
        //Vector3f v[8];
        //aabb.GetVertices(v);
        //for (int i = 0; i < 8; i++)
        //{
        //    //Vector3f point = transform.MultiplyPoint3(v[i]);
        //    Vector3f point = t * v[i];
        //    transformed.Encapsulate(point);
        //}

        //result = transformed;
    }

    void InverseTransformAABB(const AABB_deperecated& aabb, const Float3& position, const Quaternion& rotation, AABB_deperecated& result)
    {
        ///*Matrix3x3f m = rotation.inverse().toRotationMatrix();

        //Vector3f extents = RotateExtents(aabb.GetExtent(), m);
        //Vector3f center = aabb.GetCenter() - position;
        //center = m * center;

        //result.SetCenterAndExtent(center, extents);
    }

    bool IsContainedInAABB(const AABB_deperecated& inside, const AABB_deperecated& bigBounds)
    {
        bool outside = false;
        outside |= inside.mCenter[0] - inside.mExtent[0] < bigBounds.mCenter[0] - bigBounds.mExtent[0];
        outside |= inside.mCenter[0] + inside.mExtent[0] > bigBounds.mCenter[0] + bigBounds.mExtent[0];

        outside |= inside.mCenter[1] - inside.mExtent[1] < bigBounds.mCenter[1] - bigBounds.mExtent[1];
        outside |= inside.mCenter[1] + inside.mExtent[1] > bigBounds.mCenter[1] + bigBounds.mExtent[1];

        outside |= inside.mCenter[2] - inside.mExtent[2] < bigBounds.mCenter[2] - bigBounds.mExtent[2];
        outside |= inside.mCenter[2] + inside.mExtent[2] > bigBounds.mCenter[2] + bigBounds.mExtent[2];

        return !outside;
    }
}