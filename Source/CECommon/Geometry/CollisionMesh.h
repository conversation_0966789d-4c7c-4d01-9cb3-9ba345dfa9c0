#pragma once

#include "CECommon/Geometry/AABB.h"
#include "CECommon/Geometry/Ray.h"
#include "Ray_DoublePrecision.h"
#include "CECommon/Geometry/Plane.h"
#include "CECommon/Common/CECommonForward.h"
#include "Resource/MeshAssetData.h"

#define COLLISION_MAX_DISTANCE FLT_MAX

namespace cross
{
    class CollisionVertex
    {
        public:
            MeshAssetData& mMeshAssetData;
            VertexChannelAssetData* vertAssetData;
            int                   mAllSubMeshIndex;
            CollisionVertex(MeshAssetData& meshAssetData,int allSubMeshIndex) : mMeshAssetData(meshAssetData)
            {
                VertexChannelAssetData& vertpos = meshAssetData.GetChannelAssetData(VertexChannel::Position0);
                vertAssetData = &vertpos;
            }


            UInt32 Size()
            {
                return (UInt32)(vertAssetData->mData.size() / vertAssetData->mStride);
            }

            void CopyToVertexList(std::vector<Float3>& mVertexList, std::vector<int>& mIndexList)
            {
                mVertexList.clear();
                mVertexList.resize(Size());
                for (UInt32 i = 0; i < Size(); i++)
                {
                    memcpy(reinterpret_cast<void*>(mVertexList[i].data()), vertAssetData->mData.data() + i * vertAssetData->mStride, vertAssetData->mStride);
                }

                mIndexList.clear();
                const IndexStreamAssetData& assetIndexStream = mMeshAssetData.GetIndexStream();
                mIndexList.resize(assetIndexStream.mCount);
                for (UInt32 i = 0; i < assetIndexStream.mCount; i++)
                {
                    UInt32 t = 0;
                    if (assetIndexStream.mIs16BitIndex)
                    {
                        const UInt16* mData16 = reinterpret_cast<const UInt16*>(assetIndexStream.mData.data());
                        t =  (UInt32)mData16[i];
                    }
                    else
                    {
                        const UInt32* mData32 = reinterpret_cast<const UInt32*>(assetIndexStream.mData.data());
                        t =  (UInt32)mData32[i];
                    }

                    mIndexList[i] = (int)t;
                }
            }
    };

    class CollisionNode
    {
    public:
        BoundingBox mAABB;
        std::vector<int> mTriangleList;
        CollisionNode* mLeft;
        CollisionNode* mRight;

        
    public:
        CollisionNode();
        ~CollisionNode();
    };
#pragma warning(push)
#pragma warning(disable : 4324)
    class CECOMMON_API CollisionMesh
    {
    private:
        std::vector<Float3> mVertexList;
        std::vector<int> mIndexList;
        std::vector<Float3> mCenteroidList;
        CollisionNode* mRoot;

        std::shared_ptr<CollisionVertex> mCollisionVertexPtr;
    private:
        Ray mRay;
        float mMinDistance;
        Float4x4A mWorldMatrix;
        Float4x4A mTransform;
        Float3A mRayStart;
        Float3A mFaceNormal;

        Ray_DoublePrecision mRayDouble;
        double mMinDistanceDouble;
        Double4x4 mWorldMatrixDouble;
        Double4x4 mTransformDouble;
        Double3 mRayStartDouble;
        Double3 mFaceNormalDouble;

        UInt32 mCount;
    private:
        Plane2* mPlanes;
        int mPlaneCount;

    public:
        CollisionMesh();
        ~CollisionMesh();

    public:
        void Clear();

    public:
        std::vector<Float3> &GetVertexList();
        std::vector<int> &GetIndexList();
        std::vector<Float3> const &GetVertexList() const;
        std::vector<int> const &GetIndexList() const;

    public:
        CollisionNode* GetRoot();
        Float3A GetFaceNormal();   
        Double3 GetFaceNormalDouble();
        UInt32 GetIntersectCount();
    public:
        void InitCollisionMesh(MeshAssetData &meshAssetData,int allMeshPartIndex);
        void LoadCollisionTree(MeshAssetData &meshAssetData, int allMeshPartIndex);

    private:
        void CalculateAABB_Vertices(CollisionNode* node);

    public:
        bool RayIntersects(Ray* ray, float* minDistance, Float4x4& worldMatrix);
        bool RayIntersectsDouble(Ray_DoublePrecision* ray, double* minDistance, Double4x4& worldMatrix);
        bool RayIntersectsNode(CollisionNode* node);
        bool RayIntersectsNodeDouble(CollisionNode* node);

    public:
        bool FrustumContains(Plane2 *planes, int planeCount, Float4x4& worldMatrix);
        bool FrustumContainsNode(CollisionNode* node);

    private:
        bool PointInAllPlanes(Float3 &point);
    };
#pragma warning(pop)
    }
