#include "Triangulation.h"
#include "CrossBase/Math/DList.h"
#include <unordered_set>

namespace cross::Triangulation 
{
//------------- Inner Functions -----------------------
double CrossProduct(const Double2& p1, const Double2& p2, const Double2& p3)
{
    return (p2.x - p1.x) * (p3.y - p1.y) - (p2.y - p1.y) * (p3.x - p1.x);
}

bool CheckInTriangle(const Double2& p, const Double2& tp1, const Double2& tp2, const Double2& tp3)
{
    if (CrossProduct(tp1, tp2, tp3) < 0)
        return CrossProduct(tp1, tp3, p) > 0 && CrossProduct(tp3, tp2, p) > 0 && CrossProduct(tp2, tp1, p) > 0;
    else
        return CrossProduct(tp1, tp2, p) > 0 && CrossProduct(tp2, tp3, p) > 0 && CrossProduct(tp3, tp1, p) > 0;
}

bool CheckIsConvex(const std::vector<Double2>& points, DNode<UInt32>* chckNode)
{
    auto curIdx = chckNode->data;
    auto preIdx = chckNode->lLink->data;
    auto posIdx = chckNode->rLink->data;
    auto v1 = points[preIdx] - points[curIdx];
    auto v2 = points[posIdx] - points[curIdx];
    return v1.Cross(v2) > 0;
}

bool CheckIsEar(const std::vector<Double2>& points, DNode<UInt32>* chckNode)
{
    auto preNode = chckNode->lLink;
    auto posNode = chckNode->rLink;
    auto tempNode = posNode->rLink;
    while (tempNode != preNode)
    {
        if (CheckInTriangle(points[tempNode->data], points[preNode->data], points[chckNode->data], points[posNode->data]))
            return false;
        tempNode = tempNode->rLink;
    }
    return true;
}

bool CaculateCrossPoint(const Double2& p, const Double2& lp1, const Double2& lp2, Double2& crossPoint)
{
    auto v1 = lp2 - lp1;
    double a1 = v1.x == 0 ? 1.0 : v1.y / v1.x;
    double b1 = v1.x == 0 ? 0.0 : 1.0;
    double c1 = lp1.x;
    auto v2 = Double2(-v1.y, v1.x);
    double a2 = v2.x == 0 ? 1.0 : v2.y / v2.x;
    double b2 = v2.x == 0 ? 0.0 : 1.0;
    double c2 = lp1.x;
    double d = a2 * b1 - a1 * b2;
    if (d == 0)
        return false;
    crossPoint.x = (b1 * c2 - b2 * c1) / d;
    crossPoint.y = (a2 * c1 - a1 * c2) / d;
    bool outLine = crossPoint.x > lp1.x && crossPoint.x > lp2.x;
    outLine |= crossPoint.x < lp1.x && crossPoint.x < lp2.x;
    outLine |= crossPoint.y > lp1.y && crossPoint.y > lp2.y;
    outLine |= crossPoint.y < lp1.y && crossPoint.y < lp2.y;
    return outLine;
}

void MergeInnerOnePolyon(std::vector<Double2>& outPolygon, const std::vector<std::vector<Double2>>& innerPolygons, const std::vector<UInt32>& innerPolygonOffsets, std::vector<UInt32>& pIndexMap, std::unordered_set<UInt32>& mergedPoygons)
{
    // find max x inner polygon
    UInt32 polygonIdx = 0, polygonPointIdx = 0;
    for (UInt32 i = 0; i < static_cast<UInt32>(innerPolygons.size()); i++)
    {
        if (mergedPoygons.find(i) != mergedPoygons.end())
            continue;
        for (UInt32 j = 0; j < static_cast<UInt32>(innerPolygons[i].size()); j++)
        {
            if (i == j == 0 || innerPolygons[i][j].x > innerPolygons[polygonIdx][polygonPointIdx].x)
            {
                polygonIdx = i;
                polygonPointIdx = j;
            }
        }
    }
    // find nearest edge
    const auto& mergePoint = innerPolygons[polygonIdx][polygonPointIdx];
    UInt32 pCount = static_cast<UInt32>(outPolygon.size());
    Double2 crossPoint, edgePoint;
    double minDis = -1;
    UInt32 linkIndx = 0;
    for (UInt32 i = 0; i < pCount; i++)
    {
        const auto& lp1 = outPolygon[(i + pCount - 1) % pCount];
        const auto& lp2 = outPolygon[i];
        if (CaculateCrossPoint(mergePoint, lp1, lp2, crossPoint))
        {
            auto dis = Double2::Distance(mergePoint, crossPoint);
            if (minDis < 0 || minDis > dis)
            {
                linkIndx = i;
                minDis = dis;
                edgePoint = lp1.x > lp2.x ? lp1 : lp2;
            }
        }
    }
    // check 
    Double2 xDir = {1, 0};
    double maxCos = 0;
    for (UInt32 i = 0; i < pCount; i++)
    {
        if (CheckInTriangle(outPolygon[i], mergePoint, crossPoint, edgePoint))
        {
            Double2 checkDir = outPolygon[i] - mergePoint;
            checkDir.Normalize();
            double checkCos = checkDir.Dot(xDir);
            if (checkCos > maxCos)
            {
                maxCos = checkCos;
                linkIndx = i;
            }
        }
    }
    auto polygonCount = static_cast<UInt32>(innerPolygons[polygonIdx].size());
    for (UInt32 i = 0; i < polygonCount; i++)
    {
        auto idx = (i + polygonPointIdx) % polygonCount;
        outPolygon.insert(outPolygon.begin() + linkIndx + idx, innerPolygons[polygonIdx][idx]);
        pIndexMap.insert(pIndexMap.begin() + linkIndx + idx, innerPolygonOffsets[polygonIdx] + idx);
    }
    outPolygon.insert(outPolygon.begin() + linkIndx + polygonCount, innerPolygons[polygonIdx][polygonPointIdx]);
    pIndexMap.insert(pIndexMap.begin() + linkIndx + polygonCount, innerPolygonOffsets[polygonIdx] + polygonPointIdx);
    outPolygon.insert(outPolygon.begin() + linkIndx + polygonCount + 1, outPolygon[linkIndx]);
    pIndexMap.insert(pIndexMap.begin() + linkIndx + polygonCount, pIndexMap[linkIndx]);
    mergedPoygons.insert(polygonIdx);
}

void RemoveEar(std::vector<UInt32>& triangles, const std::vector<Double2>& points, DList<UInt32>& polygon, DList<UInt32>& ears, UInt32 earIndex)
{
    auto removeNode = polygon.Search(earIndex);
    auto preNode = removeNode->lLink;
    auto posNode = removeNode->rLink;
    polygon.Remove(removeNode);
    // add to triangles
    triangles.push_back(preNode->data);
    triangles.push_back(earIndex);
    triangles.push_back(posNode->data);
    // remove check node
    ears.Remove(preNode->data);
    ears.Remove(posNode->data);
    // check
    if (polygon.Length() >= 3)
    {
        if (CheckIsConvex(points, posNode) && CheckIsEar(points, posNode))
            ears.PushFront(posNode->data);
        if (CheckIsConvex(points, preNode) && CheckIsEar(points, preNode))
            ears.PushBack(preNode->data);
    }
    else
    {
        ears.MakeEmpty();
    }
}

//------------- Out Functions -----------------------
std::vector<UInt32> TriangulateByEarClip(const std::vector<Double2>& outPolygon)
{
    std::vector<UInt32> triangles;
    DList<UInt32> polygon;
    DList<UInt32> ears;
    // build
    UInt32 pCount = static_cast<UInt32>(outPolygon.size());
    for (UInt32 idx = 0; idx < pCount; idx++)
    {
        polygon.PushBack(idx);
    }
    for (UInt32 idx = 0; idx < pCount; idx++)
    {
        auto dnode = polygon.Search(idx);
        Assert(dnode->data == idx);
        if (CheckIsConvex(outPolygon, dnode) && CheckIsEar(outPolygon, dnode))
        {
            ears.PushBack(idx);
        }
    }
    while (!ears.IsEmpty())
    {
        UInt32 removeIdx;
        ears.PopFront(removeIdx);
        RemoveEar(triangles, outPolygon, polygon, ears, removeIdx);
    }
    return triangles;
}

std::vector<UInt32> TriangulateByEarClip(const std::vector<Double2>& outPolygon, std::vector<std::vector<Double2>> innerPolygons)
{
    auto newPCount = static_cast<UInt32>(outPolygon.size());
    auto innerPolygonCount = static_cast<UInt32>(innerPolygons.size());
    // caculate new size
    std::vector<UInt32> polygonOffsets(innerPolygonCount, newPCount);
    for (UInt32 i = 0; i < innerPolygonCount; i++)
    {
        newPCount += static_cast<UInt32>(innerPolygons[i].size()) + 2;
        if (i > 0)
        {
            polygonOffsets[i] = static_cast<UInt32>(innerPolygons[i - 1].size()) + polygonOffsets[i - 1];
        }
    }
    // init new point and index map
    std::vector<Double2> newPoints;
    newPoints.reserve(newPCount);
    std::vector<UInt32> pointIndexMap;
    pointIndexMap.reserve(newPCount);
    for (UInt32 i = 0; i < static_cast<UInt32>(outPolygon.size()); i++)
    {
        pointIndexMap.emplace_back(i);
    }
    newPoints.insert(newPoints.begin(), outPolygon.begin(), outPolygon.end());

    std::unordered_set<UInt32> mergeInnerPolygons;
    while (mergeInnerPolygons.size() < innerPolygons.size())
    {
        MergeInnerOnePolyon(newPoints, innerPolygons, polygonOffsets, pointIndexMap, mergeInnerPolygons);
    }
    auto trangles = TriangulateByEarClip(newPoints);
    for (UInt32 i = 0; i < static_cast<UInt32>(trangles.size()); i++)
    {
        trangles[i] = pointIndexMap[trangles[i]];
    }
    return trangles;
}
}