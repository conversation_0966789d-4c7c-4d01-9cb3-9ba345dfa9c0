#pragma once
#include "CECommon/Common/CECommonForward.h"
#include "CrossSchema/ShaderAsset_generated.h"
#include <map>

namespace cross
{

struct CECOMMON_API GPUProtoType
{
    using ID = UInt32;
    using DataKey = std::vector<uint32_t>;

    inline static ID InvalidID = UINT32_MAX;
    inline static ID AnyID = UINT32_MAX - 1;

    static GPUProtoType* GetInstance();

    ID CreateID(const CrossSchema::ShaderStructType* typePacked);
    ID CreateID(const CrossSchema::ShaderStructTypeT* typeUnPacked);

    void FreeID(ID id);

private:
    ID CreateID(GPUProtoType::DataKey dataKey);

    struct DataHasher
    {
        size_t operator()(const DataKey& key) const { return HashRange(key.data(), key.data() + key.size()); }

        bool operator() (const DataKey& key1, const DataKey& key2) const { return key1 == key2; }
    };

    std::mutex mMutex;

    ID mGlobalID = 0;

    struct ProtoTypeID
    {
        ID ID;
        SInt32 RefCount;
    };

    // robin hood hash map的元素和迭代器都不是稳定的，unordered map的元素是稳定的，但迭代起不是
    using ProtoTypeMap = std::map<DataKey, ProtoTypeID>;
    ProtoTypeMap mProtoType2ID;

    std::map<ID, ProtoTypeMap::iterator> mID2ProtoType;
};

}