#ifndef __RANDOM_H__
#define __RANDOM_H__

namespace cross
{
	/*
	Some random generator timings:
	MacBook Pro w/ Core 2 Duo 2.4GHz. Times are for gcc 4.0.1 (OS X 10.6.2) / VS2008 SP1 (Win XP SP3),
	in milliseconds for this loop (4915200 calls):

	 for (int j = 0; j < 100; ++j)
	   for (int i = 0; i < 128*128*3; ++i)
		 data[i] = (rnd.get() & 0x3) << 6;

					  gcc   vs2008    Size
	C's rand():       57.0  109.3 ms     1
	Mersenne Twister: 56.0   37.4 ms  2500
	Unity 2.x LCG:    11.1    9.2 ms     4
	Xorshift 128:     15.0   17.8 ms    16
	Xorshift 32:      20.6   10.7 ms     4
	WELL 512:         43.6   55.1 ms    68
	*/

	struct RandState
	{
		UInt32 x, y, z, w;

		//template<class TransferFunc>
		//void Transfer(TransferFunc& transfer)
		//{
		//	TRANSFER(x);
		//	TRANSFER(y);
		//	TRANSFER(z);
		//	TRANSFER(w);
		//}
	};


	// Xorshift 128 implementation
	// Xorshift paper: http://www.jstatsoft.org/v08/i14/paper
	// Wikipedia: http://en.wikipedia.org/wiki/Xorshift
	class Rand
	{
	public:
		Rand(UInt32 seed = 0)
		{
			SetSeed(seed);
		}

		UInt32 Get()
		{
			UInt32 t;
			t = state.x ^ (state.x << 11);
			state.x = state.y; state.y = state.z; state.z = state.w;
			return state.w = (state.w ^ (state.w >> 19)) ^ (t ^ (t >> 8));
		}

		UInt64 Get64()
		{
			UInt64 hi = Get();
			UInt64 lo = Get();
			return (hi << 32) | lo;
		}

		inline static float GetFloatFromInt(UInt32 value)
		{
			// take 23 bits of integer, and divide by 2^23-1
			return float(value & 0x007FFFFF) * (1.0f / 8388607.0f);
		}

		inline static UInt8 GetByteFromInt(UInt32 value)
		{
			// take the most significant byte from the 23-bit value
			return UInt8(value >> (23 - 8));
		}

		// random number between 0.0 and 1.0
		float GetFloat()
		{
			return GetFloatFromInt(Get());
		}

		// random number between -1.0 and 1.0
		float GetSignedFloat()
		{
			return GetFloat() * 2.0f - 1.0f;
		}

		void SetSeed(UInt32 seed)
		{
			// std::mt19937::initialization_multiplier = 1812433253U
			state.x = seed;
			state.y = state.x * 1812433253U + 1;
			state.z = state.y * 1812433253U + 1;
			state.w = state.z * 1812433253U + 1;
		}

		UInt32 GetSeed() const { return state.x; }

		const RandState& GetState() const { return state; }
		void SetState(RandState value) { state = value; }

		void RandomizeState();

		//template<class TransferFunc>
		//void TransferState(TransferFunc& transfer)
		//{
		//	transfer.SetVersion(2);

		//	if (transfer.IsVersionSmallerOrEqual(1))
		//	{
		//		transfer.Transfer(state.x, "x");
		//		if (transfer.IsReading())
		//			SetSeed(state.x);
		//	}
		//	else
		//	{
		//		TRANSFER(state);
		//	}
		//}

		static Rand GetUniqueGenerator()
		{
			Rand result;
			result.RandomizeState();
			return result;
		}

		inline bool operator<(const Rand& rhs) const
		{
			return (memcmp(this, &rhs, sizeof(rhs)) < 0);
		}

		inline bool operator==(const Rand& rhs) const
		{
			return (memcmp(this, &rhs, sizeof(rhs)) == 0);
		}

	private:
		RandState state;    // RNG state (128 bit)
	};

	inline float RangedRandom(Rand& r, const float inclusiveMin, const float inclusiveMax)
	{
		float t = r.GetFloat();
		t = inclusiveMin * t + (1.0f - t) * inclusiveMax;
		return t;
	}

	inline float Random01(Rand& r)
	{
		return r.GetFloat();
	}

	template<class T>
	T RangedRandomImplInt32(Rand& r, T inclusiveMin, T exclusiveMax)
	{
		if (inclusiveMin < exclusiveMax)
		{
			T dif = exclusiveMax - inclusiveMin;
			T t = r.Get() % dif;
			t += inclusiveMin;
			return t;
		}
		else if (exclusiveMax < inclusiveMin)
		{
			T dif = inclusiveMin - exclusiveMax;
			T t = r.Get() % dif;
			t = inclusiveMin - t;
			return t;
		}
		else
		{
			return inclusiveMin;
		}
	}

	inline SInt32 RangedRandom(Rand& r, SInt32 inclusiveMin, SInt32 exclusiveMax)
	{
		return RangedRandomImplInt32(r, inclusiveMin, exclusiveMax);
	}

	inline UInt32 RangedRandom(Rand& r, UInt32 inclusiveMin, UInt32 exclusiveMax)
	{
		return RangedRandomImplInt32(r, inclusiveMin, exclusiveMax);
	}

	template<class T>
	T RangedRandomImplInt64(Rand& r, T inclusiveMin, T exclusiveMax)
	{
		if (inclusiveMin < exclusiveMax)
		{
			T dif = exclusiveMax - inclusiveMin;
			T t = r.Get64() % dif;
			t += inclusiveMin;
			return t;
		}
		else if (exclusiveMax < inclusiveMin)
		{
			T dif = inclusiveMin - exclusiveMax;
			T t = r.Get64() % dif;
			t = inclusiveMin - t;
			return t;
		}
		else
		{
			return inclusiveMin;
		}
	}

	inline SInt64 RangedRandom(Rand& r, SInt64 inclusiveMin, SInt64 exclusiveMax)
	{
		return RangedRandomImplInt64(r, inclusiveMin, exclusiveMax);
	}

	inline UInt64 RangedRandom(Rand& r, UInt64 inclusiveMin, UInt64 exclusiveMax)
	{
		return RangedRandomImplInt64(r, inclusiveMin, exclusiveMax);
	}

	inline Float3 RandomUnitVector(Rand& rand)
	{
		float z = RangedRandom(rand, -1.0f, 1.0f);
		float a = RangedRandom(rand, 0.0f, 2.0f * PI);

		float r = Sqrt(1.0f - z * z);

		float x = r * std::cos(a);
		float y = r * std::sin(a);

		return Float3{x, y, z};
	}
	
	inline Float2 RandomUnitVector2(Rand& rand)
	{
		float a = RangedRandom(rand, 0.0f, 2.0f * PI);

		float x = std::cos(a);
		float y = std::sin(a);

		return Float2{x, y};
	}

	inline Float3 RangedRandom(Rand& r, const Float3& inclusiveMin, const Float3& inclusiveMax)
	{
        Float3 outVector;
		outVector.x = RangedRandom(r, inclusiveMin.x, inclusiveMax.x);
		outVector.y = RangedRandom(r, inclusiveMin.y, inclusiveMax.y);
		outVector.z = RangedRandom(r, inclusiveMin.z, inclusiveMax.z);
		return outVector;
	}
	/*
	inline Quaternionf RandomQuaternion(Rand& rand)
	{
		Quaternionf q;
		q.x = RangedRandom(rand, -1.0f, 1.0f);
		q.y = RangedRandom(rand, -1.0f, 1.0f);
		q.z = RangedRandom(rand, -1.0f, 1.0f);
		q.w = RangedRandom(rand, -1.0f, 1.0f);
		q = NormalizeSafe(q);
		if (Dot(q, Quaternionf::identity()) < 0.0f)
			return -q;
		else
			return q;
	}

	inline Quaternionf RandomQuaternionUniformDistribution(Rand& rand)
	{
		const float two_pi = 2.0F * PI;

		// Employs Hopf fibration to uniformly distribute quaternions
		float u1 = RangedRandom(rand, 0.0f, 1.0f);
		float theta = RangedRandom(rand, 0.0f, two_pi);
		float rho = RangedRandom(rand, 0.0f, two_pi);

		float i = Sqrt(1.0f - u1);
		float j = Sqrt(u1);

		// We do not need to normalize the generated quaternion, because the probability density corresponds to the Haar measure.
		// This means that a random rotation is obtained by picking a point at random on S^3, and forming the unit quaternion.
		Quaternionf q(i * std::sin(theta), i * std::cos(theta), j * std::sin(rho), j * std::cos(rho));

		if (Dot(q, Quaternionf::identity()) < 0.0f)
			return -q;
		else
			return q;
	}
	
	inline Vector3f RandomPointInsideCube(Rand& r, const Vector3f& extents)
	{
		return Vector3f{ RangedRandom(r, -extents.x, extents.x),
			RangedRandom(r, -extents.y, extents.y),
			RangedRandom(r, -extents.z, extents.z) };
	}

	inline Vector3f RandomPointInsideUnitSphere(Rand& r)
	{
		Vector3f v = RandomUnitVector(r);
		v *= pow(Random01(r), 1.0F / 3.0f);
		return v;
	}

	inline Vector3f RandomPointInsideEllipsoid(Rand& r, const Vector3f& extents)
	{
		return Scale(RandomPointInsideUnitSphere(r), extents);
	}

	inline Vector2f RandomPointInsideUnitCircle(Rand& r)
	{
		Vector2f v = RandomUnitVector2(r);
		// As the volume of the circle increases (x^2) over an interval we have to increase range as well with x^(1/2), (equivalent to sqrt(x))
		v *= Sqrt(RangedRandom(r, 0.0f, 1.0f));
		return v;
	}

	inline Vector3f RandomPointBetweenEllipsoid(Rand& r, const Vector3f& maxExtents, float minRange)
	{
		Vector3f v = Scale(RandomUnitVector(r), maxExtents);
		// As the volume of the sphere increases (x^3) over an interval we have to increase range as well with x^(1/3)
		float range = pow(RangedRandom(r, minRange, 1.0f), 1.0f / 3.0f);
		return v * range;
	}

	/// Builds a random Barycentric coordinate which can be used to generate random points on a triangle:
	/// Vector3f point = v0 * barycentric.x + v1 * barycentric.y + v2 * barycentric.z;
	inline Vector3f RandomBarycentricCoord(Rand& rand)
	{
		// Was told that this leads to bad distribution because of the 1.0F - s
		//  float s = gRand.GetFloat ();
		//  float t = RangedRandom (gRand, 0.0F, 1.0F - s);
		//  float r = (1.0F - s - t);
		//  Vector3f positionOnMesh = r * vertices[face.v1] + s * vertices[face.v2] + t * vertices[face.v3];
		//  return positionOnMesh;
		float u = rand.GetFloat();
		float v = rand.GetFloat();
		if (u + v > 1.0f)
		{
			u = 1.0f - u;
			v = 1.0f - v;
		}
		float w = 1.0f - u - v;
		return Vector3f(u, v, w);
	}
	*/
}

#endif