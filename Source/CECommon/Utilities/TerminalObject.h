#pragma once
#include "CECommon/Common/CECommonForward.h"
#include "CrossBase/Template/TypeDemangle.hpp"
#include "CrossBase/Template/Functional.hpp"
#include "CrossBase/String/UniqueString.h"
#include "CECommon/Common/SystemEvent.h"
#include "CECommon/GameWorld/IGameWorld.h"
#include "CrossBase/String/StringID.h"

namespace cross {
    struct TerminalCommandData
    {
        std::string mCommandName;
        bool bRenderWorldCommand {false};
    };
    using TerminalCommandEvent = SystemEvent<TerminalCommandData>;

    using TerminalVar = std::variant<bool, UInt32, float, std::string>;
    using TerminalVarRef = std::variant<bool*, UInt32*, float*, std::string*, void*>;
    enum class TerminalVarSetType
    {
        GameThread,  
        RenderThread
    };
    struct TerminalVarData
    {
    public:
        TerminalVarData() = default;
        std::string mCVarName;
        TerminalVarRef mValueRef;
        TerminalVar mSetValue;
        TerminalVarSetType mSetType;
        std::function<void(TerminalVar var)> mCallBack;
    };
    using TerminalVarEvent = SystemEvent<TerminalVarData>;

    class CECOMMON_API TerminalCommand final : public SystemEventManager<TerminalCommandEvent>
    {
    public:
        TerminalCommand(const char* name, bool isRenderCommand)
        {
            mData.mCommandName = name;
            mData.bRenderWorldCommand = isRenderCommand;
        }
        void OnTrigger();
    private: 
        TerminalCommandData mData;
    };

 class CECOMMON_API TerminalVarCommand 
 {
     public:
        template<class T, class TLambda = std::function<void(TerminalVar var)>>
        TerminalVarCommand(const char* name, T& refvalue, TLambda&& f)
        {
            mData.mCVarName = name;
            mData.mValueRef = &refvalue;
            mData.mSetValue = refvalue;
            mData.mCallBack = f;
        }                     
        template<class TLambda = std::function<void(TerminalVar var)>>
        TerminalVarCommand(const char* name, TLambda&& f)
        {
            mData.mCVarName = name;
            mData.mValueRef = static_cast<void*>(nullptr);
            mData.mCallBack = f;
        }        
        auto GetType() 
        {
            auto  variant_is_not_null = [](const TerminalVarRef& ValueRef)
            {
                    bool value = false;
                    std::visit([&value, &ValueRef](auto&& varRef) {
                        using V = std::decay_t<decltype(varRef)>;
                        if constexpr (std::is_same_v<V, bool*>)
                            value = std::get<bool*>(ValueRef) != nullptr;
                        else if constexpr (std::is_same_v<V, std::uint32_t*>)
                            value = std::get<std::uint32_t*>(ValueRef) != nullptr;
                        else if constexpr (std::is_same_v<V, float*>)
                            value = std::get<float*>(ValueRef) != nullptr;
                        else if constexpr (std::is_same_v<V, std::string*>)
                            value = std::get<std::string*>(ValueRef) != nullptr;
                        else if constexpr (std::is_same_v<V, void*>)
                            value = std::get<void*>(ValueRef) != nullptr;
                        }, ValueRef);
                    return value;
            };

            if (variant_is_not_null(mData.mValueRef))
            {
                return mData.mValueRef.index();
            }

            return mData.mSetValue.index();
        }

        void OnChange(UInt32 Value);
        void OnChange(float Value);
        void OnChange(std::string Value);
        void OnChange(bool Value);
    private:
        TerminalVarData mData;
    };

}   // namespace cross