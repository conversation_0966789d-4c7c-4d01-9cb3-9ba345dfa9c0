#if CROSSENGINE_EDITOR
#include "EnginePrefix.h"
#include "EditorAnimInterface.h"
#include <typeinfo>
#include "Runtime/Animation/Interface/AnimInterface.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "CECommon/Common/GameSystemBase.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/SkeltSocketSystemG.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "EditorMsaPreviewSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/MeshBlendShapeSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Resource/AssetStreaming.h"
#include "CECommon/Animation/Curve/FloatCurve.h"
namespace cross 
{
bool AnimationEditorUtil::RunSkelt_ReplaceRunSkelt(cross::IGameWorld* world, UInt64 entity, cross::skeleton::SkeletonResource* skeltResPtr)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;

    auto [skeletonComp, msaComp] = gworld->GetComponent<cross::SkeletonComponentG, cross::MsaPreviewComponentG>(entityId);

    if (!skeletonComp.IsValid() || !msaComp.IsValid())
        return false;

    const SkeletonSystemG::SkeletonCompWriter& skHandle = skeletonComp.Write();
    auto skeltSystem = gworld->GetGameSystem<SkeletonSystemG>();
    if (!skeltSystem->SetSkeleton(skHandle, skeltResPtr->GetName()))
        return false;

    const EditorMsaPreviewSystemG::MsaCompWriter& handle = msaComp.Write();
    if (handle->Context == nullptr)
    {
        handle->Context = std::make_unique<cross::MsaPreviewContext>();
    }

    handle->Context->RunSkeletonPtr = skHandle->RunSkelt.get();
    auto msaPreviewSys = gworld->GetGameSystem<EditorMsaPreviewSystemG>();
    if (!msaPreviewSys->OnReplaceRunSkeleton(handle))
        return false;

    return true;
}

void AnimationEditorUtil::RunSkelt_SetBoneRetargetingMode(cross::IGameWorld* world, UInt64 entity, int boneIndex, cross::skeleton::BoneTranslateRetargetingMode mode)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    auto skeletonSys = gworld->GetGameSystem<cross::SkeletonSystemG>();

    // modify asset happened here
    skeletonHandle.Write()->SkeletonAssetPtr->SetBoneRetargetingMode({static_cast<UInt32>(boneIndex)}, mode);
    // modify runtime inst shared by above asset happened here
    skeletonHandle.Write()->RunSkelt->SetBoneRetargetingMode({static_cast<UInt32>(boneIndex)}, mode);
}

void AnimationEditorUtil::RunSkelt_SetBoneMirrorBoneName(cross::IGameWorld* world, UInt64 entity, int boneIndex, const cross::skeleton::CEName& mirrorBoneName)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    auto skeletonSys = gworld->GetGameSystem<cross::SkeletonSystemG>();

    auto mirrorBone = skeletonHandle.Read()->SkeletonAssetPtr->GetStreamingSkeleton().GetReferenceSkeleton().FindRawBoneIndex(mirrorBoneName);
    // modify asset happened here
    skeletonHandle.Write()->SkeletonAssetPtr->SetMirrorBoneInfo({static_cast<UInt32>(boneIndex)}, mirrorBone);
    // modify runtime inst shared by above asset happened here
    skeletonHandle.Write()->RunSkelt->SetBoneMirrorBoneInfo({static_cast<UInt32>(boneIndex)}, mirrorBone);
}

cross::skeleton::BoneTranslateRetargetingMode AnimationEditorUtil::SkeletonRes_GetBoneRetargetingMode(cross::skeleton::SkeletonResource* skeltResPtr, int boneIndex)
{
    return skeltResPtr->GetBoneRetargetingMode({static_cast<UInt32>(boneIndex)});
}

void AnimationEditorUtil::SkeletonRes_SetBoneRetargetingMode(cross::skeleton::SkeletonResource* skeltResPtr, int boneIndex, cross::skeleton::BoneTranslateRetargetingMode mode)
{
    skeltResPtr->SetBoneRetargetingMode({static_cast<UInt32>(boneIndex)}, mode);
}

cross::skeleton::CEName AnimationEditorUtil::SkeletonRes_GetMirrorBoneName(cross::skeleton::SkeletonResource* skeltResPtr, int boneIndex)
{
    return skeltResPtr->GetMirrorBoneName({static_cast<UInt32>(boneIndex)});
}

void AnimationEditorUtil::SkeletonRes_SetMirrorBoneName(cross::skeleton::SkeletonResource* skeltResPtr, int boneIndex, const cross::skeleton::CEName& mirrorBoneName)
{
    skeltResPtr->SetMirrorBoneName({static_cast<UInt32>(boneIndex)}, mirrorBoneName);
}

#define CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, previewReturn)                                                                                                                                                                  \
    cross::ecs::EntityID entityId{entity};                                                                                                                                                                                                     \
    cross::GameWorld* gworld = (cross::GameWorld*)world;                                                                                                                                                                                       \
    auto [skeletonComp, msaComp] = gworld->GetComponent<cross::SkeletonComponentG, cross::MsaPreviewComponentG>(entityId);                                                                                                                     \
    cross::anim::SkeltPosePtr skPosePtr = skeletonComp.Read()->PosePtr;                                                                                                                                                                        \
    if (skPosePtr == nullptr)                                                                                                                                                                                                                  \
        return 0;                                                                                                                                                                                                                              \
    const EditorMsaPreviewSystemG::MsaCompWriter& handle = msaComp.Write();                                                                                                                                                                    \
    if (handle->Context == nullptr)                                                                                                                                                                                                            \
    {                                                                                                                                                                                                                                          \
        handle->Context = std::make_unique<cross::MsaPreviewContext>();                                                                                                                                                                        \
        handle->Context->RunSkeletonPtr = skeletonComp.Read()->RunSkelt.get();                                                                                                                                                                 \
    }                                                                                                                                                                                                                                          \
    EditorMsaPreviewSystemG* system = gworld->GetGameSystem<EditorMsaPreviewSystemG>();                                                                                                                                                        \
    if (system->IsPreviewing(handle, seqPtr->GetName().c_str()) == previewReturn)                                                                                                                                                              \
        return 0;

bool AnimationEditorUtil::AnimSeqRes_MoveCursor(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, float position)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false);

    skPosePtr->ResetToRefPose();

    // Extract pose into local space from previewer
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    previewer->GetPose(*skPosePtr.get(), position);

    // Convert into root space immediately
    skPosePtr->ConvertAllBoneToRootSpace();

    // Extract anim curves
    previewer->ExtractAnimCurves(position);
    // Apply anim curves for blend shape
    auto meshBlendShapeSystem = gworld->GetGameSystem<MeshBlendShapeSystemG>();
    auto modelSystem = gworld->GetGameSystem<ModelSystemG>();
    meshBlendShapeSystem->AutoBlendShapeFromAnimCurves(modelSystem, entity, previewer->GetCurveData());

    return true;
}

bool AnimationEditorUtil::AnimSeqRes_AddPreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, true)
    return system->Previewing<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
}

bool AnimationEditorUtil::AnimSeqRes_RemovePreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)
    return system->RemovePreviewing(handle, seqPtr->GetName().c_str());
}

int AnimationEditorUtil::AnimSeqRes_GetSyncMarkersCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto syncTrackModifier = previewer->GetSyncMarkerModifier();

    int outCount = static_cast<int>(syncTrackModifier.Count());
    return outCount;
}

std::string AnimationEditorUtil::AnimSeqRes_GetSyncMarkerData(cross::IGameWorld* world, UInt64 entity, AnimSequenceRes* seqPtr, UInt32 index)
{
    std::string outMarkerJson;
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto& syncTrackModifier = previewer->GetSyncMarkerModifier();
  
    syncTrackModifier.ModifyMarker(index, [&outMarkerJson](AnimSyncMarker& curSyncMarker) {
        SerializeNode json;
        curSyncMarker.Serialize(json);
        outMarkerJson = json.FormatToJson();
    });
    return outMarkerJson;
}

bool AnimationEditorUtil::AnimSeqRes_SetSyncMarkerData(cross::IGameWorld* world, UInt64 entity, AnimSequenceRes* seqPtr, UInt32 index, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto& syncTrackModifier = previewer->GetSyncMarkerModifier();

    // grab json node holding sync marker
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    syncTrackModifier.ModifyMarker(index, [&inNode](AnimSyncMarker& curSyncMarkerPtr) { curSyncMarkerPtr.Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimSeqRes_SetSyncMarkerDataByName(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& inMarkerName, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto& syncTrackModifier = previewer->GetSyncMarkerModifier();

    // grab json node holding sync marker
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    syncTrackModifier.ModifyMarkerByCond([inMarkerName](AnimSyncMarker& curSyncMarkerPtr) { return curSyncMarkerPtr.MarkerName == inMarkerName.c_str(); }, [&inNode](AnimSyncMarker& curSyncMarker) { curSyncMarker.Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimSeqRes_AddSyncMarkerData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& addMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto& syncTrackModifier = previewer->GetSyncMarkerModifier();

    // grab json node holding sync marker
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(addMarkerJson, &success);
    if (success == false)
        return false;

    AnimSyncMarker tMarker;
    if (tMarker.Deserialize(inNode) == false)
        return false;

    return syncTrackModifier.Add(tMarker);
}

bool AnimationEditorUtil::AnimSeqRes_RemoveSyncMarkerData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& removeMarkerName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab sync marker track modifier from previewer
    auto& syncTrackModifier = previewer->GetSyncMarkerModifier();

    CEName removeName = removeMarkerName.c_str();
    return syncTrackModifier.Remove([removeName](AnimSyncMarker& marker) { return marker.MarkerName == removeName; });
}

int AnimationEditorUtil::AnimSeqRes_GetNotifiesCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    int outCount = static_cast<int>(modifier.Count());
    return outCount;
}

std::string AnimationEditorUtil::AnimSeqRes_GetNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, UInt32 index)
{
    std::string outNotifyJson;
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    modifier.ModifyMarker(index, [&outNotifyJson](AnimNotifyEvent* curNotifyPtr) {
        SerializeNode json;
        curNotifyPtr->Serialize(json);
        outNotifyJson = json.FormatToJson();
    });
    return outNotifyJson;
}

bool AnimationEditorUtil::AnimSeqRes_SetNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, UInt32 index, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    modifier.ModifyMarker(index, [&inNode](AnimNotifyEvent* curNotifyPtr) { curNotifyPtr->Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimSeqRes_SetNotifyDataByName(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& inMarkerName, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    modifier.ModifyMarkerByCond([inMarkerName](AnimNotifyEvent* marker) { return marker->Name == inMarkerName.c_str(); }, [&inNode](AnimNotifyEvent* curNotifyPtr) { curNotifyPtr->Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimSeqRes_AddNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& addMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(addMarkerJson, &success);
    if (success == false)
        return false;

    return modifier.Add(inNode);
}

bool AnimationEditorUtil::AnimSeqRes_RemoveNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimSequenceRes* seqPtr, std::string const& removeMarkerName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)

    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->GetNotifyTrackModifier();

    CEName removeName = removeMarkerName.c_str();
    return modifier.Remove([removeName](AnimNotifyEvent* curNotifyPtr) { return curNotifyPtr->Name == removeName; });
}

bool AnimationEditorUtil::AnimSeqRes_AddCurveTrack(IGameWorld* world, UInt64 entity, anim::AnimSequenceRes* seqPtr, const FloatCurveTrack& inCurveData)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)
    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab curve modifier from previewer
    auto& modifier = previewer->GetCurveListModifier();

    return modifier.AddTrack(inCurveData);
}

bool AnimationEditorUtil::AnimSeqRes_RemoveCurveTrack(IGameWorld* world, UInt64 entity, anim::AnimSequenceRes* seqPtr, const CEName& inCurveDataName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)
    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab curve modifier from previewer
    auto& modifier = previewer->GetCurveListModifier();

    return modifier.RemoveTrack(inCurveDataName);
}

bool AnimationEditorUtil::AnimSeqRes_SetCurveTrack(IGameWorld* world, UInt64 entity, anim::AnimSequenceRes* seqPtr, const FloatCurveTrack& inCurveData)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, seqPtr, false)
    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab curve modifier from previewer
    auto& modifier = previewer->GetCurveListModifier();

    return modifier.SetTrack(inCurveData);
}

FloatCurveList AnimationEditorUtil::AnimSeqRes_GetCurveList(IGameWorld* world, UInt64 entity, anim::AnimSequenceRes* seqPtr)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto [skeletonComp, msaComp] = gworld->GetComponent<cross::SkeletonComponentG, cross::MsaPreviewComponentG>(entityId);
    cross::anim::SkeltPosePtr skPosePtr = skeletonComp.Read()->PosePtr;
    if (skPosePtr == nullptr)
        return FloatCurveList();
    const EditorMsaPreviewSystemG::MsaCompWriter& handle = msaComp.Write();
    if (handle->Context == nullptr)
    {
        handle->Context = std::make_unique<cross::MsaPreviewContext>();
        handle->Context->RunSkeletonPtr = skeletonComp.Read()->RunSkelt.get();
    }
    EditorMsaPreviewSystemG* system = gworld->GetGameSystem<EditorMsaPreviewSystemG>();
    if (system->IsPreviewing(handle, seqPtr->GetName().c_str()) == false)
        return FloatCurveList();
    // grab sequence previewer from msa context
    auto previewer = system->GetPreviewer<AnimSequencePreviewer>(handle, seqPtr->GetName().c_str());
    // grab curve modifier from previewer
    auto& modifier = previewer->GetCurveListModifier();

    FloatCurveList outCurveSet = modifier.GetCurveList();
    return outCurveSet;
}

cross::anim::CEName AnimationEditorUtil::AnimCmpRes_GetSlotName(cross::anim::AnimCompositeRes* cmpResPtr)
{
    return cmpResPtr->GetSlotTrack().SlotName;
}
cross::Resource* AnimationEditorUtil::AnimationCreateAnimator(const char* animatorContentStr, const char* resourceRefStr)
{
    auto animatorResPtr = gResourceMgr.CreateResourceAs<cross::anim::AnimatorRes>();
    animatorResPtr->SetStoryBoardContent(animatorContentStr);
    animatorResPtr->SetResourceRef(resourceRefStr);

    //animatorResPtr->IncreaseRefCount();
    return animatorResPtr.get();
}
bool AnimationEditorUtil::Animation_CookMotionMatchData(cross::anim::MotionDataAsset* dataset) 
{
    dataset->InitializeDatabase();
    dataset->GenerateStandardDeviationWeights(WeightControl::GetDefaultWeights(), 0.5f);

    return true;
}

void AnimationEditorUtil::Animator_SetAnimatorEnable(cross::IGameWorld* world, UInt64 entity, bool enable)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto animatorHandle = gworld->GetComponent<cross::AnimatorComponentG>(entityId);
    gworld->GetGameSystem<cross::AnimatorSystemG>()->SetAnimatorEnable(animatorHandle.Write(), enable);
}

void AnimationEditorUtil::Skeleton_SetSkeletonCompEnable(cross::IGameWorld* world, UInt64 entity, bool enable)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    gworld->GetGameSystem<cross::SkeletonSystemG>()->SetSkeletonCompEnable(skeletonHandle.Write(), enable);
}

void AnimationEditorUtil::Skeleton_ResetRuntimeSkeleton(cross::IGameWorld* world, UInt64 entity)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    gworld->GetGameSystem<cross::SkeletonSystemG>()->ResetRuntimeSkeleton(skeletonHandle.Write());
}

void AnimationEditorUtil::Skeleton_ResetSkeletonPhysics(cross::IGameWorld* world, UInt64 entity)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto skeletonHandle = gworld->GetComponent<cross::SkeletonComponentG>(entityId);
    gworld->GetGameSystem<cross::SkeletonSystemG>()->ResetSkeletonPhysics(skeletonHandle.Write());
}
cross::Resource* AnimationEditorUtil::Animation_CreateMotionMatchData(const char* animation_sequence)
    {
    auto motion_match_data = gResourceMgr.CreateResourceAs<cross::anim::MotionDataAsset>();

    motion_match_data->AddMotionMatchSequencePath(animation_sequence);

    //motion_match_data->IncreaseRefCount();
    return motion_match_data.get();
}
bool AnimationEditorUtil::AnimCmpRes_MoveCursor(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, float position)
    {
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false);

    skPosePtr->ResetToRefPose();

    if (cmpPtr->GetSlotTrack().GetSegmentCount() <= 0)
        return true;

    // extract pose into local space from previewer
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    previewer->GetPose(*skPosePtr.get(), position);

    // convert into root space immediately
    skPosePtr->ConvertAllBoneToRootSpace();

    // Extract anim curves
    previewer->ExtractAnimCurves(position);
    // Apply anim curves for blend shape
    auto meshBlendShapeSystem = gworld->GetGameSystem<MeshBlendShapeSystemG>();
    auto modelSystem = gworld->GetGameSystem<ModelSystemG>();
    meshBlendShapeSystem->AutoBlendShapeFromAnimCurves(modelSystem, entity, previewer->GetCurveData());

    return true;
}

bool AnimationEditorUtil::AnimCmpRes_AddPreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, true)
    return system->Previewing<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
}

bool AnimationEditorUtil::AnimCmpRes_RemovePreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)
    return system->RemovePreviewing(handle, cmpPtr->GetName().c_str());
}

bool AnimationEditorUtil::AnimCmpRes_InsertSegment(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, std::string const& inMarkerJson, int preIndexInTrack)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab composite track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    // grab json node holding segment data
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    return system->AnimTrackModify_InsertSegment(modifier.ModifierForAnim, skeletonComp.Read()->RunSkelt.get(), inNode, preIndexInTrack);
}

bool AnimationEditorUtil::AnimCmpRes_RemoveSegment(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, int indexInTrack)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab composite track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    return system->AnimTrackModify_RemoveSegment(modifier.ModifierForAnim, indexInTrack);
}

bool AnimationEditorUtil::AnimCmpRes_SetSegmentData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, std::string const& inMarkerJson, int indexInTrack)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab composite track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    // grab json node holding segment data
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    return system->AnimTrackModify_SetSegment(modifier.ModifierForAnim, skeletonComp.Read()->RunSkelt.get(), inNode, indexInTrack);
}

int AnimationEditorUtil::AnimCmpRes_GetSegmentCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab composite track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;
    int outCount = 0;
    system->AnimTrackModify_GetSegmentCount(modifier.ModifierForAnim, outCount);
    return outCount;
}

std::string AnimationEditorUtil::AnimCmpRes_GetSegmentData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 indexInTrack)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab composite track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    std::string outMarkerJson;
    SerializeNode outNode;
    if (system->AnimTrackModify_GetSegment(modifier.ModifierForAnim, outNode, indexInTrack))
    {
        outMarkerJson = outNode.FormatToJson();
        return outMarkerJson;
    }

    return "";
}
int AnimationEditorUtil::AnimCmpRes_GetNotifyTrackCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)
    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier;

    return static_cast<int>(modifier.size());
}

int AnimationEditorUtil::AnimCmpRes_GetNotifiesCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];
    return static_cast<int>(modifier.Count());
}

std::string AnimationEditorUtil::AnimCmpRes_GetNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex, UInt32 index)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];
    std::string outNotify = "";
    modifier.ModifyMarker(index, [&outNotify](AnimNotifyEvent* curNotifyPtr) {
        SerializeNode json;
        curNotifyPtr->Serialize(json);
        outNotify = json.FormatToJson();
    });
    return outNotify;
}

bool AnimationEditorUtil::AnimCmpRes_SetNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex, UInt32 index, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    modifier.ModifyMarker(index, [&inNode](AnimNotifyEvent* curNotifyPtr) { curNotifyPtr->Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimCmpRes_SetNotifyDataByName(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex, std::string const& inMarkerName, std::string const& inMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(inMarkerJson, &success);
    if (success == false)
        return false;

    modifier.ModifyMarkerByCond([inMarkerName](AnimNotifyEvent* marker) { return marker->Name == inMarkerName.c_str(); }, [&inNode](AnimNotifyEvent* curNotifyPtr) { curNotifyPtr->Deserialize(inNode); });
    return true;
}

bool AnimationEditorUtil::AnimCmpRes_AddNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex, std::string const& addMarkerJson)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];

    // grab json node holding notify
    bool success = true;
    auto inNode = DeserializeNode::ParseFromJson(addMarkerJson, &success);
    if (success == false)
        return false;

    return modifier.Add(inNode);
}

bool AnimationEditorUtil::AnimCmpRes_RemoveNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, UInt32 trackIndex, std::string const& removeMarkerName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];

    CEName removeName = removeMarkerName.c_str();
    return modifier.Remove([removeName](AnimNotifyEvent* curNotifyPtr) { return curNotifyPtr->Name == removeName; });
}

bool AnimationEditorUtil::AnimCmpRes_AddCurveTrack(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, const FloatCurveTrack& inCurveData)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->CurveListModifier;

    return modifier.AddTrack(inCurveData);
}

bool AnimationEditorUtil::AnimCmpRes_RemoveCurveTrack(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, const CEName& inCurveDataName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->CurveListModifier;

    return modifier.RemoveTrack(inCurveDataName);
}

bool AnimationEditorUtil::AnimCmpRes_SetCurveTrack(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr, const FloatCurveTrack& inCurveData)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, cmpPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->CurveListModifier;

    return modifier.SetTrack(inCurveData);
}

FloatCurveList AnimationEditorUtil::AnimCmpRes_GetCurveList(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimCompositeRes* cmpPtr)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto [skeletonComp, msaComp] = gworld->GetComponent<cross::SkeletonComponentG, cross::MsaPreviewComponentG>(entityId);
    cross::anim::SkeltPosePtr skPosePtr = skeletonComp.Read()->PosePtr;
    if (skPosePtr == nullptr)
        return FloatCurveList();
    const EditorMsaPreviewSystemG::MsaCompWriter& handle = msaComp.Write();
    if (handle->Context == nullptr)
    {
        handle->Context = std::make_unique<cross::MsaPreviewContext>();
        handle->Context->RunSkeletonPtr = skeletonComp.Read()->RunSkelt.get();
    }
    EditorMsaPreviewSystemG* system = gworld->GetGameSystem<EditorMsaPreviewSystemG>();
    if (system->IsPreviewing(handle, cmpPtr->GetName().c_str()) == false)
        return FloatCurveList();

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimCompositePreviewer>(handle, cmpPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->CurveListModifier;

    FloatCurveList outCurveSet = modifier.GetCurveList();
    return outCurveSet;
}

bool AnimationEditorUtil::AnimatrixRes_MoveCursor(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, std::string slotName, float position)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false);

    skPosePtr->ResetToRefPose();

    if (animtrixPtr->GetSlotTracks().empty() || animtrixPtr->GetSlotTracks()[0].GetSegmentCount() <= 0)
        return true;

    // extract pose into local space from previewer
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());

    CEName SlotName = AnimSlotGroup::sDefaultSlotName;
    if (slotName.empty())
    {
        if (!animtrixPtr->GetSlotTracks().empty())
            SlotName = animtrixPtr->GetSlotTracks().begin()->SlotName;
    }
    else
    {
        SlotName = CEName(slotName.c_str());
    }

    previewer->GetPose(*skPosePtr.get(), position, SlotName);

    // convert into root space immediately
    skPosePtr->ConvertAllBoneToRootSpace();

    // Extract anim curves
    previewer->ExtractAnimCurves(position);
    // Apply anim curves for blend shape
    auto meshBlendShapeSystem = gworld->GetGameSystem<MeshBlendShapeSystemG>();
    auto modelSystem = gworld->GetGameSystem<ModelSystemG>();
    meshBlendShapeSystem->AutoBlendShapeFromAnimCurves(modelSystem, entity, previewer->GetCurveData());

    return true;
}

bool AnimationEditorUtil::AnimatrixRes_AddPreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, true)
    return system->Previewing<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
}

bool AnimationEditorUtil::AnimatrixRes_RemovePreview(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)
    return system->RemovePreviewing(handle, animtrixPtr->GetName().c_str());
}

int AnimationEditorUtil::AnimatrixRes_GetSlotNamesCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    return static_cast<int>(modifier.TrackNames.size());
}

std::string AnimationEditorUtil::AnimatrixRes_GetSlotName(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, UInt32 index)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;
    if (index < modifier.TrackNames.size()) {
        return modifier.TrackNames[index];
    }
    return "";
}

int AnimationEditorUtil::AnimatrixRes_GetNotifyTrackCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr) {
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)
    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    return static_cast<int>(previewer->NotifyTrackModifier.size());
}

int AnimationEditorUtil::AnimatrixRes_GetNotifiesCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, UInt32 trackIndex)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];

    return static_cast<int>(modifier.Count());
}

std::string AnimationEditorUtil::AnimatrixRes_GetNotifyData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, UInt32 trackIndex, UInt32 index)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->NotifyTrackModifier[trackIndex];
    std::string outNotify = "";
    modifier.ModifyMarker(index, [&outNotify](AnimNotifyEvent* curNotifyPtr) {
        SerializeNode json;
        curNotifyPtr->Serialize(json);
        outNotify = json.FormatToJson();
    });
    return outNotify;
}

int AnimationEditorUtil::AnimatrixRes_GetSegmentCount(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, std::string slotName)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;
    std::string name = std::string(slotName);
    if (auto it = modifier.ModifierForActivatedTracks.find(name); it != modifier.ModifierForActivatedTracks.end())
    {
        return it->second.GetSegmentsCount();
    }
    return 0;
}

std::string AnimationEditorUtil::AnimatrixRes_GetSegmentData(cross::IGameWorld* world, UInt64 entity, cross::anim::AnimatrixRes* animtrixPtr, std::string slotName, UInt32 indexInTrack)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, animtrixPtr, false)

    // grab composite previewer from msa context
    auto previewer = system->GetPreviewer<AnimatrixPreviewer>(handle, animtrixPtr->GetName().c_str());
    // grab notify track modifier from previewer
    auto& modifier = previewer->AnimTrackModifier;

    if (auto it = modifier.ModifierForActivatedTracks.find(slotName); it != modifier.ModifierForActivatedTracks.end())
    {
        std::string outMarkerJson;
        SerializeNode outNode;
        if (system->AnimTrackModify_GetSegment(it->second, outNode, indexInTrack))
        {
            outMarkerJson = outNode.FormatToJson();
            return outMarkerJson;
        }
    }
    return "";
}

bool AnimationEditorUtil::AnimMMRes_MoveCursor(cross::IGameWorld* world, UInt64 entity, cross::anim::MotionDataAsset* mmPtr, std::string animName, float position)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, mmPtr, false)

    skPosePtr->ResetToRefPose();

    // extract pose into local space from previewer
    auto previewer = system->GetPreviewer<AnimMotionMatchPreviewer>(handle, mmPtr->GetName().c_str());

    if (!previewer->IsAnimationLoaded(animName.c_str()))
    {
        previewer->LoadSequence(system->GetAnimFactory(), handle->Context->RunSkeletonPtr, animName.c_str());
    }

    previewer->GetPose(*skPosePtr.get(), position, animName.c_str());

    // convert into root space immediately
    skPosePtr->ConvertAllBoneToRootSpace();
    return true;
}

bool AnimationEditorUtil::AnimMMRes_AddPreview(cross::IGameWorld* world, UInt64 entity, cross::anim::MotionDataAsset* mmPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, mmPtr, true)
    return system->Previewing<AnimMotionMatchPreviewer>(handle, mmPtr->GetName().c_str());
}

bool AnimationEditorUtil::AnimMMRes_RemovePreview(cross::IGameWorld* world, UInt64 entity, cross::anim::MotionDataAsset* mmPtr)
{
    CHECK_PREVIEWING_FROM_MSASYSTEM(world, entity, mmPtr, false)
    return system->RemovePreviewing(handle, mmPtr->GetName().c_str());
}

void AnimationEditorUtil::AnimStbDebug_GetDebugItem(IGameWorld* world, UInt64 entity, StbDebugData& outData)
{
    cross::ecs::EntityID entityId{entity};
    cross::GameWorld* gworld = (cross::GameWorld*)world;
    auto animator = gworld->GetComponent<cross::AnimatorComponentG>(entityId);
    auto animSys = gworld->GetGameSystem<cross::AnimatorSystemG>();
    animSys->GetStbDebugData(animator.Read(), outData);
}

}   // namespace cross
#endif