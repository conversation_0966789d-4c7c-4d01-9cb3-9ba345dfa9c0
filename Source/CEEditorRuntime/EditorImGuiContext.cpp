#include "EditorImGuiContext.h"
#include "imgui.h"
#include "imgui_internal.h"

#pragma warning(push)
#pragma warning(disable : 4244)
#pragma warning(disable : 4267)
namespace cross {
EditorImGuiContext::EditorImGuiContext()
    : m_ShowUI(false)
    , mC<PERSON>back(nullptr)
{}

EditorImGuiContext::EditorImGuiContext(EditorImGuiCallback* callback)
    : mC<PERSON>back(callback)
    , m_ShowUI(true)
{
    mGuiContext = ImGui::CreateContext();
    ImGui::SetCurrentContext(mGuiContext);
    auto& io = ImGui::GetIO();

    io.IniFilename = nullptr;
    io.LogFilename = nullptr;
    io.BackendPlatformName = "imgui_impl_CrossEngine";
    io.BackendFlags |= ImGuiBackendFlags_RendererHasVtxOffset;

    io.KeyMap[ImGuiKey_Tab] = ToUnderlying(EditorKey::Tab);
    io.KeyMap[ImGuiKey_LeftArrow] = ToUnderlying(EditorKey::Left);
    io.KeyMap[ImGuiKey_RightArrow] = ToUnderlying(EditorKey::Right);
    io.KeyMap[ImGuiKey_UpArrow] = ToUnderlying(EditorKey::Up);
    io.KeyMap[ImGuiKey_DownArrow] = ToUnderlying(EditorKey::Down);
    io.KeyMap[ImGuiKey_PageUp] = ToUnderlying(EditorKey::PageUp);
    io.KeyMap[ImGuiKey_PageDown] = ToUnderlying(EditorKey::PageDown);
    io.KeyMap[ImGuiKey_Home] = ToUnderlying(EditorKey::Home);
    io.KeyMap[ImGuiKey_End] = ToUnderlying(EditorKey::End);
    // io.KeyMap[ImGuiKey_Insert] = ToUnderlying(EditorKey::Insert);
    io.KeyMap[ImGuiKey_Delete] = ToUnderlying(EditorKey::Delete);
    io.KeyMap[ImGuiKey_Backspace] = ToUnderlying(EditorKey::Backspace);
    io.KeyMap[ImGuiKey_Space] = ToUnderlying(EditorKey::Space);
    io.KeyMap[ImGuiKey_Enter] = ToUnderlying(EditorKey::Enter);
    io.KeyMap[ImGuiKey_Escape] = ToUnderlying(EditorKey::Escape);
    io.KeyMap[ImGuiKey_A] = ToUnderlying(EditorKey::A);
    io.KeyMap[ImGuiKey_C] = ToUnderlying(EditorKey::C);
    io.KeyMap[ImGuiKey_V] = ToUnderlying(EditorKey::V);
    io.KeyMap[ImGuiKey_X] = ToUnderlying(EditorKey::X);
    io.KeyMap[ImGuiKey_Y] = ToUnderlying(EditorKey::Y);
    io.KeyMap[ImGuiKey_Z] = ToUnderlying(EditorKey::Z);

    io.UserData = this;
    io.ImeSetInputScreenPosFn = [](int x, int y) { reinterpret_cast<EditorImGuiContext*>(ImGui::GetIO().UserData)->OnSetInputScreenPos(x, y); };

    ImGui::StyleColorsDark();

    RecreateFontAtlas();
}

EditorImGuiContext::~EditorImGuiContext()
{
    if (m_ShowUI)
    {
        EditorUICanvasInterface::Instance().DestroyIMGUIImage(mFontImage);
        if (ImGui::GetCurrentContext() == mGuiContext)
        {
            ImGui::SetCurrentContext(nullptr);
        }
        ImGui::DestroyContext(mGuiContext);
    }
}

void EditorImGuiContext::OnMouseMoveEvent(int mouseX, int mouseY)
{
    auto& io = mGuiContext->IO;
    io.MousePos.x = static_cast<float>(mouseX);
    io.MousePos.y = static_cast<float>(mouseY);
}

void EditorImGuiContext::OnMouseWheelEvent(int mouseDeltaZ)
{
    auto& io = mGuiContext->IO;
    io.MouseWheel += mouseDeltaZ;
}

void EditorImGuiContext::OnKeyEvent(EditorKey btn, bool isDown)
{
    auto& io = mGuiContext->IO;
    switch (btn)
    {
    case EditorKey::LeftButton:
        io.MouseDown[0] = isDown;
        break;
    case EditorKey::RightButton:
        io.MouseDown[1] = isDown;
        break;
    case EditorKey::MiddleButton:
        io.MouseDown[2] = isDown;
        break;
    case EditorKey::Tab:
    case EditorKey::Left:
    case EditorKey::Right:
    case EditorKey::Up:
    case EditorKey::Down:
    case EditorKey::PageUp:
    case EditorKey::PageDown:
    case EditorKey::Home:
    case EditorKey::End:
    // case EditorKey::Insert:
    case EditorKey::Delete:
    case EditorKey::Backspace:
    case EditorKey::Space:
    case EditorKey::Enter:
    case EditorKey::Escape:
    case EditorKey::A:
    case EditorKey::C:
    case EditorKey::V:
    case EditorKey::X:
    case EditorKey::Y:
    case EditorKey::Z:
        io.KeysDown[ToUnderlying(btn)] = isDown;
        break;
    case EditorKey::Control:
        io.KeyCtrl = isDown;
        break;
    case EditorKey::Shift:
        io.KeyShift = isDown;
        break;
    case EditorKey::Alt:
        io.KeyAlt = isDown;
        break;
    case EditorKey::Window:
        io.KeyAlt = isDown;
        break;
    default:
        break;
    }
}

void EditorImGuiContext::OnInputCharacter(ImWchar16 c)
{
    mGuiContext->IO.AddInputCharacterUTF16(c);
}

void EditorImGuiContext::OnActivate(bool active)
{
    if (!active)
    {
        ImGuiIO& io = mGuiContext->IO;
        for (int n = 0; n < IM_ARRAYSIZE(io.KeysDown); n++)
        {
            io.KeysDown[n] = false;
        }
    }
}

void EditorImGuiContext::OnSetInputScreenPos(int x, int y)
{
    mCallback->OnSetInputScreenPosition(x, y);
}

void EditorImGuiContext::Update(const Float2& offset, const Float2& size)
{
    if (m_ShowUI)
    {
        ImGui::SetCurrentContext(mGuiContext);

        auto& io = ImGui::GetIO();
        io.DisplaySize.x = size.x;
        io.DisplaySize.y = size.y;
        io.DeltaTime = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetDeltaTime();

        // Update OS mouse cursor with the cursor requested by imgui
        ImGuiMouseCursor mouse_cursor = io.MouseDrawCursor ? ImGuiMouseCursor_None : ImGui::GetMouseCursor();
        if (mLastMouseCursor != mouse_cursor)
        {
            mLastMouseCursor = mouse_cursor;
            UpdateMouseCursor();
        }

        ImGui::NewFrame();

        OnUpdate(offset, size);

        ImGui::Render();
    }
}

void EditorImGuiContext::Paint(const Float2& offset, const Float2& size, EditorUIRenderInterface& renderInterface)
{
    ImGui::SetCurrentContext(mGuiContext);

    auto* drawData = ImGui::GetDrawData();

    auto ortho = Float4x4::CreateOrthographicOffCenter(0, renderInterface.GetSize().x, renderInterface.GetSize().y, 0, -1, 1, GetNGIDevice().GetPlatform() == NGIPlatform::OpenGLES3);
    auto trans = Float4x4::CreateTranslation(offset.x, offset.y, 0);

    for (SInt32 i = 0; i < drawData->CmdListsCount; ++i)
    {
        renderInterface.DrawIMGUI(drawData->CmdLists[i], gLayout, trans * ortho, offset, size);
    }
}

bool EditorImGuiContext::UpdateMouseCursor()
{
    ImGuiIO& io = mGuiContext->IO;
    if (io.ConfigFlags & ImGuiConfigFlags_NoMouseCursorChange)
        return false;

    ImGuiMouseCursor imgui_cursor = ImGui::GetMouseCursor();
    if (imgui_cursor == ImGuiMouseCursor_None || io.MouseDrawCursor)
    {
        // Hide OS mouse cursor if imgui is drawing it or if it wants no cursor
        mCallback->OnSetCursor(ImGuiMouseCursor_None);
    }
    else
    {
        // Show OS mouse cursor
        mCallback->OnSetCursor(imgui_cursor);
    }
    return true;
}

void EditorImGuiContext::RecreateFontAtlas()
{
    ImGui::SetCurrentContext(mGuiContext);
    ImGuiIO& io = ImGui::GetIO();

    IM_DELETE(io.Fonts);
    io.Fonts = IM_NEW(ImFontAtlas);

    static auto LoadFont = [](std::string_view fontName) {
        const auto fontPath = fmt::format("{}/EngineResource/Font/{}", PathHelper::GetEngineResourceDirectoryPath(), fontName);
        std::ifstream ifs{fontPath, std::ios::binary | std::ios::ate};
        std::vector<UInt8> fontDate(ifs.tellg());
        ifs.seekg(0, std::ios::beg);
        ifs.read(reinterpret_cast<char*>(fontDate.data()), fontDate.size());
        return fontDate;
    };

    mEnglishFontDate = LoadFont("consola.ttf");
    mChineseFontDate = LoadFont("PingFangSCRegular.ttf");

    ImFontConfig config;
    config.OversampleH = 4;
    config.OversampleV = 4;
    config.FontDataOwnedByAtlas = false;

    ImFontConfig mergeFontConfig;
    mergeFontConfig.OversampleH = 4;
    mergeFontConfig.OversampleV = 4;
    mergeFontConfig.FontDataOwnedByAtlas = false;
    mergeFontConfig.MergeMode = true;

    m_DefaultFont = io.Fonts->AddFontFromMemoryTTF(mEnglishFontDate.data(), mEnglishFontDate.size(), 16.0f, &config, io.Fonts->GetGlyphRangesDefault());
    io.Fonts->AddFontFromMemoryTTF(mChineseFontDate.data(), mChineseFontDate.size(), 20.0f, &mergeFontConfig, io.Fonts->GetGlyphRangesChineseSimplifiedCommon());

    m_SubTitleFont = io.Fonts->AddFontFromMemoryTTF(mEnglishFontDate.data(), mEnglishFontDate.size(), 13.0f, &config, io.Fonts->GetGlyphRangesDefault());
    io.Fonts->AddFontFromMemoryTTF(mChineseFontDate.data(), mChineseFontDate.size(), 20.0f, &mergeFontConfig, io.Fonts->GetGlyphRangesChineseSimplifiedCommon());

    io.Fonts->Build();

    unsigned char* pixels;
    int width, height;
    io.Fonts->GetTexDataAsRGBA32(&pixels, &width, &height);

    if (mFontImage)
    {
        EditorUICanvasInterface::Instance().DestroyIMGUIImage(mFontImage);
    }

    mFontImage = EditorUICanvasInterface::Instance().CreateIMGUIImage(width, height);
    EditorUICanvasInterface::Instance().UploadIMGUIImage(mFontImage, reinterpret_cast<UInt32*>(pixels));

    io.Fonts->TexID = mFontImage;
}
}   // namespace cross
#pragma warning(pop)