#include "EnginePrefix.h"
#include "EditorInput/EditorCursor.h"

namespace cross {

#if _MSC_VER && !__INTEL_COMPILER
EditorCursor::EditorCursor()
{
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::ARROW)] = LoadCursor(NULL, IDC_ARROW);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::IBEAM)] = LoadCursor(NULL, IDC_IBEAM);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::WAIT)] = LoadCursor(NULL, IDC_WAIT);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::CROSSHAIR)] = LoadCursor(NULL, IDC_CROSS);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::RESIZE_NWSE)] = LoadCursor(NULL, IDC_SIZENWSE);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::RESIZE_NESW)] = LoadCursor(NULL, IDC_SIZENESW);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::RESIZE_WE)] = LoadCursor(NULL, IDC_SIZEWE);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::RESIZE_NS)] = LoadCursor(NULL, IDC_SIZENS);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::UNAVAILABLE)] = LoadCursor(NULL, IDC_NO);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::HAND)] = LoadCursor(NULL, IDC_HAND);
    mCursors[static_cast<uint32_t>(CURSOR_SHAPE::SIZE_ALL)] = LoadCursor(NULL, IDC_SIZEALL);
}
#endif

Float2 EditorCursor::GetPosition() const
{
    Assert(mGetMousePosition);

    float mouseX = 0.f, mouseY = 0.f;
    mGetMousePosition(&mouseX, &mouseY);

    return {mouseX, mouseY};
}

void EditorCursor::SetPosition(const SInt32 x, const SInt32 y)
{
}

void EditorCursor::Show(bool bShow)
{
    Assert(mShowMouseCursor);
    mShowMouseCursor(bShow);
}

void EditorCursor::Lock(Float2 const inOrigin, Float2 const inSize)
{
    Assert(mLockMouseCursor);
    mLockMouseCursor(inOrigin.x, inOrigin.y, inSize.x, inSize.y);
}

void EditorCursor::UnLock()
{
    Assert(mUnLockMouseCursor);
    mUnLockMouseCursor();
}

void EditorCursor::GetSize(SInt32& outWidth, SInt32& outHeight) const
{
}

#if _MSC_VER && !__INTEL_COMPILER
void EditorCursor::SetShape(CURSOR_SHAPE const shape)
{
    if(shape != CURSOR_SHAPE::NUM_CURSOR_SHAPES)
        mCursorShape = shape;
    Assert(mCursorShape < CURSOR_SHAPE::NUM_CURSOR_SHAPES);

    ::SetCursor(mCursors[static_cast<uint32_t>(mCursorShape)]);
}
#endif

void EditorCursor::SetFetchMousePosCallBack(EditorFetchMousePosCallBack callBack)
{
    mGetMousePosition = callBack;
}

void EditorCursor::SetShowMouseCursorCallBack(EditorShowMouseCursorCallBack callBack)
{
    mShowMouseCursor = callBack;
}

void EditorCursor::SetLockMouseCursorCallBack(EditorLockMouseCursorCallBack callBack)
{
    mLockMouseCursor = callBack;
}

void EditorCursor::SetUnLockMouseCursorCallBack(EditorGeneralCallBack callBack)
{
    mUnLockMouseCursor = callBack;
}

}

