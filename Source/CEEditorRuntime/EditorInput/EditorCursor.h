#pragma once
#include "Runtime/Input/Core/PlatformCursor.h"
#include "Runtime/Editor/EditorCallback.h"

namespace cross {

class EditorCursor : public IPlatformCursor
{
#if _MSC_VER && !__INTEL_COMPILER
    using CursorHandle = HCURSOR;
#endif
public:
#if _MSC_VER && !__INTEL_COMPILER
    EditorCursor();
#endif

    virtual Float2 GetPosition() const override;

    virtual void SetPosition(const SInt32 x, const SInt32 y) override;

    virtual void Show(bool bShow) override;

    // origin point's coordinate = upper-left corner of the screen.
    virtual void Lock(Float2 const inOrigin, Float2 const inSize) override;

    virtual void UnLock() override;

    virtual void GetSize(SInt32& outWidth, SInt32& outHeight) const override;
    
#if _MSC_VER && !__INTEL_COMPILER
    virtual void SetShape(CURSOR_SHAPE const shape) override;
#else
    virtual void SetShape(CURSOR_SHAPE const shape) override {}
#endif
public:
    void SetFetchMousePosCallBack(EditorFetchMousePosCallBack callBack);

    void SetShowMouseCursorCallBack(EditorShowMouseCursorCallBack callBack);

    void SetLockMouseCursorCallBack(EditorLockMouseCursorCallBack callBack);

    void SetUnLockMouseCursorCallBack(EditorGeneralCallBack callBack);

private:
    EditorFetchMousePosCallBack mGetMousePosition{nullptr};
    EditorShowMouseCursorCallBack mShowMouseCursor{nullptr};
    EditorLockMouseCursorCallBack mLockMouseCursor{nullptr};
    EditorGeneralCallBack mUnLockMouseCursor{nullptr};

#if _MSC_VER && !__INTEL_COMPILER
    CursorHandle mCursors[static_cast<uint32_t>(CURSOR_SHAPE::NUM_CURSOR_SHAPES)] = {NULL};
#endif
    CURSOR_SHAPE mCursorShape = CURSOR_SHAPE::ARROW;
};

}   // namespace cross