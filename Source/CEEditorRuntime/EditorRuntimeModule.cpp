#include "EnginePrefix.h"
#include "EditorRuntimeModule.h"
#include "GizmoManager.h"
#include "EditorUIRenderSystemG.h"
#include "Runtime/Interface/CrossEngineImp.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CrossBase/Globals/Globals.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
void CEEditorRuntimeRegister();
void SystemRegisterR();
void SystemRegisterG();
namespace cross::scripts {
void CodeGenRegisterGeneratedClass();
}   // namespace cross::scripts

namespace cross {

CEEditorRuntimeModule::CEEditorRuntimeModule()
{
    CEEditorRuntimeRegister();
    SystemRegisterG();
    SystemRegisterR();
    /// Global System
    if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == AppStartUpTypeCrossEditor || EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeThumbnailProcessor)
    {
        auto* editorUIRenderSystem = EditorUIRenderSystemG::CreateInstance();
        editorUIRenderSystem->GetDesc().mBeginFramePriority = 3000;
        editorUIRenderSystem->GetDesc().mEndFramePriority = 3000;
        editorUIRenderSystem->GetDesc().mUpdatePriority = 3000;
        cross::EngineGlobal::GetEngine()->AddGlobalSystem(editorUIRenderSystem);
    }
}
CEEditorRuntimeModule& CEEditorRuntimeModule::Instance()
{
    auto moduleptr = cross::GameFramework()->QueryModule(cross::kModuleEditorRuntime);
    return *(dynamic_cast<CEEditorRuntimeModule*>(moduleptr));
}

void CEEditorRuntimeModule::Free()
{
    delete this;
}

CEEditorRuntimeModule::~CEEditorRuntimeModule()
{
#if CROSSENGINE_EDITOR
    GizmoManager::GetInstance()->Destroy();
#endif
}

gbf::ModuleCallReturnStatus CEEditorRuntimeModule::Init()
{
    return gbf::ModuleCallReturnStatus::Succeed;
}

gbf::ModuleCallReturnStatus CEEditorRuntimeModule::Start()
{
    return gbf::ModuleCallReturnStatus::Succeed;
}
}   // namespace cross
MAKE_MODULE(cross::CEEditorRuntimeModule, CEEditorRuntime)