#pragma once
#include "CrossBase/Platform/PlatformConfigMacro.h"

#if CROSSENGINE_WIN
#    pragma warning(disable : 4251)
#    pragma warning(disable : 4275)
#    ifdef CEEditorRuntime_EXPORTS
#        define CEEditorRuntime_API __declspec(dllexport)
#    else
#        define CEEditorRuntime_API __declspec(dllimport)
#    endif
#else
#    ifdef __GNUC__
#        define CEEditorRuntime_API __attribute__((visibility("default")))
#    else
#        define CEEditorRuntime_API
#    endif
#endif
#include "Runtime/Interface/CrossEngine.h"
#include "EditorUICanvas.h"
#include "core/modules/imodule.h"
namespace cross {
static const char kModuleEditorRuntime[] = "CEEditorRuntime";
class CEEditorRuntime_API CEEditorRuntimeModule : public gbf::IModule
{
public:
    CEEditorRuntimeModule();
    ~CEEditorRuntimeModule();
    // method from IModule
    gbf::ModuleCallReturnStatus Init() override;
    gbf::ModuleCallReturnStatus Start() override;
    gbf::ModuleCallReturnStatus Update() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Stop() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    gbf::ModuleCallReturnStatus Release() override
    {
        return gbf::ModuleCallReturnStatus::Succeed;
    }
    void Free() override;
    static CEEditorRuntimeModule& Instance();
};
}   // namespace cross
