#pragma once
#include "EditorRuntimeModule.h"
#include "Runtime/Editor/EditorCallback.h"
#include "Runtime/GameWorld/EcotopeMap.h"
#include "ECS/Develop/Framework/Types.h"
#include "Resource/MaterialInterface.h"
#include "imgui.h"
#ifndef ImTextureID         
typedef void* ImTextureID;  
#endif
namespace cross
{
    struct UIVertex
    {
        Float3 vPos;
        UInt32 uColor;
        Float2 vUV0;
        Float2 vUV1;
    };

    struct CEMeta(Editor, Cli) UserParam
    {
        CEProperty(Editor, Cli)
        bool buttonR = false;
        CEProperty(Editor, Cli) 
        bool buttonG = false;
        CEProperty(Editor, Cli) 
        bool buttonB = false;
        CEProperty(Editor, Cli) 
        bool buttonA = false;
        CEProperty(Editor, Cli) 
        bool isRGBA = false;
        CEProperty(Editor, Cli) 
        bool isSRGB = false;
        CEProperty(Editor, Cli) 
        bool isMipmap;
        CEProperty(Editor, Cli) 
        bool isUnPack;
        CEProperty(Editor, Cli) 
        SInt8 MipLevel = 0;

        bool operator==(const UserParam& other) const
        {
            return buttonR == other.buttonR && buttonG == other.buttonG && buttonB == other.buttonB && buttonA == other.buttonA && 
                isRGBA == other.isRGBA && isSRGB == other.isSRGB && isMipmap == other.isMipmap &&
                   isUnPack == other.isUnPack && MipLevel == other.MipLevel;
        }
    };

    struct UITexture
    {
        std::string mImageFilename;
        int mWidth;
        int mHeight;
        MaterialInterfacePtr mImageMaterial;
        resource::Texture* mTexture;
    };
    struct UIGridVtx
    {
        Float3 vPos;
        Float2 vUV0;
    };
    using UITexturePtr = std::shared_ptr<UITexture>;

    class EditorUIRenderInterface;

    class CEEditorRuntime_API CEMeta(Cli) EditorUICanvasInterface
    {
        friend class EditorUIRenderInterface;
    public:
        EditorUICanvasInterface() = default;
        ~EditorUICanvasInterface() = default;

        CEMeta(Editor, Cli)
        static EditorUICanvasInterface& Instance();

        CEMeta(Editor, Cli)
        void Initialize(EditorGeneralCallBack delegate);
        CEMeta(Editor, Cli)
        int FindUIImage(const char *filename);
        CEMeta(Editor, Cli)
        int LoadUIImage(const char *filename);
        CEMeta(Editor, Cli)
        int ReloadUIImage(const char *filename);
        CEMeta(Editor, Cli) 
        int LoadUIImageSRGB(const char* filename);
        CEMeta(Editor, Cli) 
        int LoadUIImageRGBA(const char* filename, bool cR, bool cG, bool cB, bool cA);
        // this interface makesure reload the texture and use the parameters to create new material
        CEMeta(Editor, Cli) 
        int LoadUIImageUserDefined(const char* filename, UserParam parameters);
        CEMeta(Editor, Cli)
        int LoadUIImageEcotopeMap(cross::EcotopeMap* map);
        CEMeta(Editor, Cli)
        bool ReleaseUIImageUserDefined(const char* filename, UserParam parameters);
        CEMeta(Editor, Cli)
        int CreateUIImage(int width, int height);
        CEMeta(Editor, Cli) 
        int CreateUIImageByResource(cross::Resource* resource);
        CEMeta(Editor, Cli)
        int CreateSceneImage(cross::GameWorld* world,int width, int height);
        CEMeta(Editor, Cli) 
        int CreateSceneCameraImage(cross::GameWorld* world, ecs::EntityID cameraEntity, int width, int height);
        CEMeta(Editor, Cli)
        void ResizeSceneImage(cross::GameWorld* world,int image,int width, int height);
        CEMeta(Editor, Cli) 
        void ResizeSceneCameraImage(cross::GameWorld* world, ecs::EntityID cameraEntity, int image, int width, int height);
        CEMeta(Editor, Cli) 
        void SaveImage(IRenderWindow* window, int image, const char* filename, bool saveAlpha);
        CEMeta(Editor, Cli)
        void ClearReadBackTask();
        CEMeta(Editor)
        void UploadImage(int image, UInt32 *data);
        CEMeta(Editor)
        void UploadImageRect(int image, UInt32 *data, int x, int y, int width, int height);
        CEMeta(Editor, Cli)
		int GetImageWidth(int image);
        CEMeta(Editor, Cli)
        int GetImageHeight(int image);
        CEMeta(Editor, Cli)
        void SetFilter(int image, int textureFilter);
        CEMeta(Editor)
        int CreateREDVisualizerImage();

        CEMeta(Editor)
        void OnREDVisualizerMouseEvent(const MouseEvent& e);

        CEMeta(Editor)
        void OnREDVisualizerKeyboardEvent(const ButtonEvent& e);

        CEMeta(Editor) 
        void OnREDVisualizerClosed();

        CEMeta(Editor)
        void OnREDVisualizerPaint(SInt32 image, UInt32 width, UInt32 height);

        ImTextureID CreateIMGUIImage(UInt32 width, UInt32 height);

        ImTextureID CreateIMGUIImage(const std::string& texturePath);

        UInt2 GetIMGUIImageSize(ImTextureID image);

        void UploadIMGUIImage(ImTextureID image, void* data);

        void DestroyIMGUIImage(ImTextureID image);

        void DestroyImage(int image);

    protected:
        void OnFirstUpdate();
        EditorGeneralCallBack mDelegate = nullptr;
        std::vector<EditorUIRenderInterface*> mRenderInterfaces;

    private:
        MaterialInterfacePtr CreateGridMaterial(int gridID);
        MaterialInterfacePtr CreateLineMaterial();
        MaterialInterfacePtr CreateGradientMaterial();
        int AddImage(const char* filename, int width, int height, MaterialInterfacePtr material, resource::Texture* texture);
        MaterialInterfacePtr CreateUIMaterial(bool alphaBlend);
        MaterialInterfacePtr CreateUISRGBMaterial(bool alphaBlend);
        MaterialInterfacePtr CreateTextureRGBAMaterial(bool cR, bool cG, bool cB, bool cA);
        MaterialInterfacePtr CreateTextureRelatedMaterial(UserParam userParam);
        VertexStreamLayout mUIVertexLayout;
        VertexStreamLayout mGridVertexLayout;
        VertexStreamLayout mLineVertexLayout;

        struct UserDefinedUIKey
        {
            std::string mFileName;
            UserParam   mUserParam;

           
        };


        struct UseDefineUIKeyHasher
        {
            std::size_t operator()(const UserDefinedUIKey & s) const {

                size_t hash_value = 0;
                hash_combine(hash_value, std::hash<std::string>{}(s.mFileName));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.buttonR));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.buttonG));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.buttonB));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.buttonA));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.isRGBA));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.isSRGB));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.isMipmap));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.isUnPack));
                hash_combine(hash_value, robin_hood::hash_int(s.mUserParam.MipLevel));

                return hash_value;
            }
            bool operator()(const UserDefinedUIKey lhs, const UserDefinedUIKey& other) const { return lhs.mFileName == other.mFileName && lhs.mUserParam == other.mUserParam; }
        };


        std::unordered_map<int, UITexturePtr> mUITextures;
        std::unordered_map<UserDefinedUIKey, int, UseDefineUIKeyHasher, UseDefineUIKeyHasher> mUIUserDefinedFileNameToHandle;

        cross::MaterialInterfacePtr mLineMaterial;
        cross::MaterialInterfacePtr mGradientMaterial;
        std::unordered_map<int, MaterialInterfacePtr> mGridMaterials;

        std::set<UITexturePtr> mIMGUIImages;
    };
}