
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "EditorUIRenderSystemR.h"
#include "ECS/Develop/Framework/Types.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/RenderEngine.h"
//#include "RenderEngine/RenderWindow.h"
//#include "RenderEngine/DrawUnit.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/WindowSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "Threading/RenderingThread.h"
#include "IWindowR.h"
#include "RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross {
const GlobalSystemDesc& EditorUIRenderSystemR::GetDesc()
{
    static const GlobalSystemDesc* sDesc = nullptr;
    if (!sDesc)
    {
        auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
        sDesc = descSystem->CreateOrGetGlobalSystemDesc("EditorUIRender", false);
    }
    return *sDesc;
}

EditorUIRenderSystemR* EditorUIRenderSystemR::CreateInstance()
{
    return new EditorUIRenderSystemR();
}

void EditorUIRenderSystemR::Release()
{
    delete this;
}

void EditorUIRenderSystemR::OnFirstUpdate(FrameParam* frameParam)
{
    AllocateDensityMapTexture();
}

void EditorUIRenderSystemR::OnUpdate(FrameParam* frameParam)
{
    for (auto& info : mCanvasRenderInfos)
    {
        auto& mVertexData = info.mVertexData;
        auto& mIndexData = info.mIndexData;
        auto& mUIInfo = info.mUIInfo;
        auto& mGeopakInfo = info.mGeopakInfo;
        auto& mGeopakVec = info.mGeopakVec;
        auto& mDrawUnits = info.mDrawUnits;

        if (!mVertexData || !mIndexData || !mUIInfo || !mGeopakInfo || !(mVertexData->GetSize()))
            continue;

        auto& renderFactory = RenderFactory::Instance();
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

        UInt32 vbSize = mVertexData->GetSize();
        auto vertexBufferWrap = rendererSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::VertexBuffer, vbSize);
        vertexBufferWrap.MemWriteContainer(mVertexData, vbSize, 0);

        UInt32 ibSize = mIndexData->GetSize();
        auto indexBufferWrap = rendererSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::IndexBuffer, ibSize);
        indexBufferWrap.MemWriteContainer(mIndexData, ibSize, 0);

        if (mGeopakVec.size() < mGeopakInfo->GetSize())
        {
            mGeopakVec.resize(mGeopakInfo->GetSize());
        }

        for (UInt32 i = 0; i < mGeopakInfo->GetSize(); i++)
        {
            auto& geopakInfo = mGeopakInfo->At(i);
            auto& geopakPtr = mGeopakVec[i];

            if (!geopakPtr)
            {
                geopakPtr = renderFactory.CreateGeometryPacket();
            }

            geopakPtr->AddVertexStream(vertexBufferWrap.GetNGIBuffer(), geopakInfo.mVertexCount * geopakInfo.mLayout.GetVertexStride(), (UInt32)vertexBufferWrap.GetNGIOffset() + geopakInfo.mVertexOffsetInByte, geopakInfo.mLayout);
            geopakPtr->SetIndexStream(indexBufferWrap.GetNGIBuffer(), geopakInfo.mIndexCount * sizeof(UInt32), geopakInfo.mIndexCount, (UInt32)indexBufferWrap.GetNGIOffset() + geopakInfo.mIndexOffsetInByte);
        }

        mDrawUnits = std::move(FrameStdVector<REDRawDrawUnit>(rendererSystem->GetRenderingExecutionDescriptor()->GetREDFrameAllocator()));
        mDrawUnits.reserve(mUIInfo->GetSize());

        NGIViewport* curViewport = nullptr;
        NGIScissor* curScissor = nullptr;

        for (UInt32 i = 0; i < mUIInfo->GetSize(); i++)
        {
            auto& uiInfo = mUIInfo->At(i);
            if ((uiInfo.mMiscFlag & (EditorUIFlagViewport | EditorUIFlagScissor)) == 0)
            {
                auto& drawUnit = mDrawUnits.emplace_back();
                auto& geometryPacket = mGeopakVec[uiInfo.mGeopakIndex];

                drawUnit.mGeometry = RenderGeometry{geometryPacket.get(),
                                                    uiInfo.mGeometry.mVertexCount,
                                                    uiInfo.mGeometry.mVertexStart,
                                                    uiInfo.mGeometry.mIndexCount,
                                                    uiInfo.mGeometry.mIndexStart,
                                                    uiInfo.mGeometry.mPrimitiveCount,
                                                    uiInfo.mGeometry.mPrimitiveType,
                                                    (UInt16)i};
                drawUnit.mMaterial = uiInfo.mMaterial;
                drawUnit.mViewport = curViewport;
                drawUnit.mScissor = curScissor;

                if (auto colorTex = dynamic_cast<RenderTextureR*>(uiInfo.mMaterial->GetTexture(NAME_ID("color_texture"))); colorTex)
                {
                    mSceneViewImages.emplace_back(colorTex->GetREDTexture());
                }
            }
            else
            {
                if (uiInfo.mMiscFlag & EditorUIFlagViewport)
                {
                    curViewport = frameParam->GetFrameAllocator()->Allocate<NGIViewport>(1, FrameStage::FRAME_STAGE_RENDER);
                    *curViewport = {
                        static_cast<float>(uiInfo.mViewport.x),
                        static_cast<float>(uiInfo.mViewport.y),
                        static_cast<float>(uiInfo.mViewport.width),
                        static_cast<float>(uiInfo.mViewport.height),
                        0,
                        1,
                    };
                }
                else if (uiInfo.mMiscFlag & EditorUIFlagScissor)
                {
                    curScissor = frameParam->GetFrameAllocator()->Allocate<NGIScissor>(1, FrameStage::FRAME_STAGE_RENDER);
                    *curScissor = {uiInfo.mScissor.x, uiInfo.mScissor.y, uiInfo.mScissor.width, uiInfo.mScissor.height};
                }
            }
        }
    }

    AssembleEditorUIPass();
}

void EditorUIRenderSystemR::OnBeginFrame(FrameParam* frameParam) 
{
}

void EditorUIRenderSystemR::OnEndFrame(FrameParam* frameParam)
{
    for (auto& info : mCanvasRenderInfos)
    {
        for (auto& geopak : info.mGeopakVec)
        {
            geopak->Clear();
        }
    }
    mCanvasRenderInfos.clear();
    mSceneViewImages.clear();
}

void EditorUIRenderSystemR::DrawUIElementBatch(IWindowR* window, EditorUIElementBatchFrameVec* uiInfo, EditorUIGeopakInfoFrameVec* geoInfo, DynamicSizeFrameDataLot* vertexData, DynamicSizeFrameDataLot* indexData)
{
    mCanvasRenderInfos.push_back({ window, uiInfo, geoInfo, vertexData, indexData });
}

void EditorUIRenderSystemR::AllocateDensityMapTexture()
{
    auto renderersystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    if (renderersystem->mDensityMapTexture_NGI.get() == nullptr)
    {
        UInt32 width = 1024;
        UInt32 height = 1024;
        NGITextureDesc sceneViewTexDesc{
            GraphicsFormat::R8G8B8A8_UNorm,
            NGITextureType::Texture2D,
            1,
            1,
            width,
            height,
            1,
            1,
            NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
        };
        renderersystem->mDensityMapTexture_NGI.reset(GetNGIDevice().CreateTexture(sceneViewTexDesc, "EditorUI DensityMapTexture"));

        NGITextureViewDesc sceneViewTexViewDesc{NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource,
                                                GraphicsFormat::R8G8B8A8_UNorm,
                                                NGITextureType::Texture2D,
                                                {
                                                    NGITextureAspect::Color,
                                                    0,
                                                    1,
                                                    0,
                                                    1,
                                                }};
        renderersystem->mDensityMapTextureView_NGI.reset(GetNGIDevice().CreateTextureView(renderersystem->mDensityMapTexture_NGI.get(), sceneViewTexViewDesc));

        NGITextureBarrier barrier{
            renderersystem->mDensityMapTexture_NGI.get(),
            0,
            NGIResourceState::Undefined,
            NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask,
        };
        renderersystem->mImCmdListPre->ResourceBarrier(0, nullptr, 1, &barrier);
    }
}

void EditorUIRenderSystemR::AssembleEditorUIPass()
{
    auto renderersystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto RED = renderersystem->GetRenderingExecutionDescriptor();

    if (auto* REDVisViewTarget = renderersystem->GetREDVisualizer()->GetTextureView(); REDVisViewTarget)
    {
        REDColorTargetDesc desc{
            REDVisViewTarget,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            {{0, 0, 0, 1}},
        };
        RED->BeginRenderPass(gREDVisualizerPassName, 1, &desc, nullptr);
        auto colorIndex = NGIRenderPassTargetIndex::Target0;
        auto* pass = RED->AllocateSubRenderPass(gREDVisualizerPassName, 0, nullptr, 1, &colorIndex, REDPassFlagBit{0});
        pass->Execute([=](REDPass* pass, NGIBundleCommandList* bundleCmdList) { renderersystem->GetREDVisualizer()->Draw(pass, bundleCmdList); });
        RED->EndRenderPass();
    }
   

    for (auto sceneViewImage : mSceneViewImages)
    {
        RED->FlushState(sceneViewImage, NGIResourceState::PixelShaderShaderResource);
    }
    if (auto* REDVisViewSource = renderersystem->GetREDVisualizer()->GetTexture(); REDVisViewSource)
    {
        RED->FlushState(REDVisViewSource, NGIResourceState::PixelShaderShaderResource);
    }

    const static NameID gEditorUI = "EditorUI";

    for (auto& info : mCanvasRenderInfos)
    {
        auto* backbuffer = info.mWindow->PrepareBackbuffer(renderersystem->GetCMDList());
        NGITextureViewDesc RTVDesc
        {
            NGITextureUsage::RenderTarget,
            backbuffer->mDesc.Format,
            NGITextureType::Texture2D,
            {
                NGITextureAspect::Color,
                0,
                1,
                0,
                1,
            },
        };
        auto* renderTarget = RED->AllocateTextureView(backbuffer, RTVDesc);

        REDColorTargetDesc desc{
            renderTarget,
            NGILoadOp::Clear,
            NGIStoreOp::Store,
            {{0, 0, 0, 0}},
        };

        RED->BeginRenderPass("EditorUI", 1, &desc, nullptr);
        auto colorIndex = NGIRenderPassTargetIndex::Target0;
        auto* editorPass = RED->AllocateSubRenderPass("EditorUI", 0, nullptr, 1, &colorIndex, REDPassFlagBit{0});
        editorPass->RenderRawDrawUnits(gEditorUI, info.mDrawUnits);
        RED->EndRenderPass();
    }
}

}   // namespace cross
