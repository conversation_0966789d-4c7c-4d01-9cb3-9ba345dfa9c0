#pragma once
#include <vector>
#include <CrossBase/Math/CrossMath.h>
namespace cross {
class ExtrudeShape
{
private:
    std::vector<Float2> vertices;
    std::vector<Float2> normals;
    std::vector<int> lines;
    std::vector<float> uCoord;
    float shapeLength;

public:
    ExtrudeShape();
    ExtrudeShape(std::vector<Float2> vertices, std::vector<int> lines);

    static ExtrudeShape GetSimpleRoadShape()
    {
        std::vector<Float2> vertices;
        vertices.push_back(Float2(-10.0f, 0.0f));
        vertices.push_back(Float2(0.0f, 0.0f));
        vertices.push_back(Float2(10.0f, 0.0f));

        std::vector<int> lines = {0, 1, 1, 2};
        ExtrudeShape shape = ExtrudeShape(vertices, lines);
        return shape;
    }

    std::vector<Float2> GetVertices() const
    {
        return vertices;
    };

    std::vector<Float2> GetNormals() const
    {
        return normals;
    };

    std::vector<int> GetLines() const
    {
        return lines;
    }

    std::vector<float> GetUCood() const
    {
        return uCoord;
    }

    float GetShapeLength()
    {
        return shapeLength;
    }
};

}   // namespace cross