#include "EnginePrefix.h"
#include "SplineMesh.h"
#include "RenderEngine/PrimitiveGenerator.h"

namespace cross {
UInt32 SplineMesh::sColorWhite = 0xffffffff;

SplineMesh::SplineMesh(ExtrudeShape value)
{
    shape = value;
}

void SplineMesh::GenerateMesh(PrimitiveData* outData, std::vector<Float3> points)
{
    if (points.size() == 0)
        return;
    float distance = Float3::Distance(points[0], points[3]);
    int edgeLoops = /*static_cast<int>(distance * 5) */ 20;
    if (edgeLoops == 0)
        return;
    int segments = edgeLoops - 1;
    int vertsInShape = static_cast<int>(shape.GetVertices().size());
    int vertCount = vertsInShape * edgeLoops;
    int triCount = static_cast<int>(shape.GetLines().size() * segments * 3);
    std::vector<Float3> vertices(vertCount);
    std::vector<Float3> normals(vertCount);
    std::vector<Float2> uv(vertCount);
    std::vector<int> triangles(triCount);

    for (int i = 0; i < edgeLoops; i++)
    {
        float t = i / static_cast<float>(edgeLoops - 1);
        Float3 position = GetSplinePoint(points, t);
        Float3 tangent = GetTangent(points, t);
        Float3 up = Float3(0.0f, 1.0f, 0.0f);
        /*up.Cross(tangent);
        up.Normalized();*/
        Float3 binormal = up.Cross(tangent).Normalized();
        Float3 normal = tangent.Cross(binormal);
        Double3 tangentDouble3 = Double3(tangent.x, tangent.y, tangent.z);
        Double3 normalDouble3 = Double3(normal.x, normal.y, normal.z);
        Double3 forward = Double3(0.0f, 0.0f, 1.0f);
        Quaternion64 rotation = Quaternion64::CreateFrom2Vectors(forward, tangentDouble3, normalDouble3);

        int offset = i * vertsInShape;
        for (int j = 0; j < vertsInShape; j++)
        {
            int index = offset + j;
            vertices[index] = position + (Float3)rotation.Double3Rotate(Double3(shape.GetVertices()[j].x, shape.GetVertices()[j].y, 0.0));
            // vertices[index].y = 0.0;
            normals[index] = (Float3)rotation.Double3Rotate(Double3(shape.GetNormals()[j].x, shape.GetNormals()[j].y, 0.0));
            // normals[index] = Float3(0.0, 1.0, 0.0);
            uv[index] = Float2(shape.GetUCood()[j], t * distance / 20);
        }
    }

    int ti = 0;
    for (int i = 0; i < segments; i++)
    {
        int offset = i * vertsInShape;
        for (int j = 0; j < shape.GetLines().size(); j += 2)
        {
            int a = offset + shape.GetLines()[j] + vertsInShape;
            int b = offset + shape.GetLines()[j];
            int c = offset + shape.GetLines()[j + 1];
            int d = offset + shape.GetLines()[j + 1] + vertsInShape;
            triangles[ti] = a;
            ti++;
            triangles[ti] = c;
            ti++;
            triangles[ti] = b;
            ti++;
            triangles[ti] = a;
            ti++;
            triangles[ti] = d;
            ti++;
            triangles[ti] = c;
            ti++;
        }
    }

    //
    UInt16 vertexCount = static_cast<UInt16>(vertices.size());
    UInt16 indexCount = static_cast<UInt16>(triangles.size());
    VertexStreamLayout&& layout = cross::PrimitiveGenerator::GetStandardTriangleLayout();
    outData->Resize(vertexCount, indexCount, indexCount / 3, layout, false);
    outData->SetTopology(cross::PrimitiveTopology::TriangleList);

    // Set vertex
    UInt32 offset = 0;
    UInt32 stride = layout.GetVertexStride();
    auto AddVertex = [vertexData = outData->GetVertexData(), &offset, stride, vertexCount](Float3* position, Float3* normal, Float2* uv) {
        Assert(stride * static_cast<UInt32>(vertexCount));
        memcpy(static_cast<void*>(vertexData + offset), position, 12);
        memcpy(static_cast<void*>(vertexData + offset + 12), normal, 12);
        memcpy(static_cast<void*>(vertexData + offset + 24), uv, 8);
        memcpy(static_cast<void*>(vertexData + offset + 32), &sColorWhite, 4);
        offset += stride;
    };

    for (size_t i = 0; i < vertices.size(); ++i)
    {
        AddVertex(&vertices[i], &normals[i], &uv[i]);
    }

    auto indexArray = outData->GetIndexArray();
    for (UInt8 i = 0; i < triangles.size(); i += 3)
    {
        indexArray[i] = static_cast<UInt16>(triangles[i]);
        indexArray[i + 1] = static_cast<UInt16>(triangles[i + 1]);
        indexArray[i + 2] = static_cast<UInt16>(triangles[i + 2]);
    }
}

cross::Float3 SplineMesh::GetSplinePoint(std::vector<Float3> points, float t)
{
    float omt = 1.0f - t;
    float omt2 = omt * omt;
    float t2 = t * t;
    return points[0] * (omt2 * omt) + points[1] * (3.0f * omt2 * t) + points[2] * (3.0f * omt * t2) + points[3] * (t2 * t);
}
cross::Float3 SplineMesh::GetTangent(std::vector<Float3> points, float t)
{
    float omt = 1.0f - t;
    float omt2 = omt * omt;
    float t2 = t * t;
    Float3 tangent = points[0] * (-omt2) + points[1] * (3.0f * omt2 - 2 * omt) + points[2] * (-3.0f * t2 + 2 * t) + points[3] * (t2);
    return tangent.Normalized();
}
}   // namespace cross