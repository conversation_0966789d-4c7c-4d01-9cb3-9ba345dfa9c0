#include "AudioComponent.h"
#include <CECommon/Common/EngineGlobal.h>

//static cross::AudioEngine::ObjectID constexpr DEFAULT_LISTENER = 0;

namespace cegf {
AudioComponent::AudioComponent()
{
    cross::AudioEngine::ObjectID const object_id = GetObjectID();
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    audio_engine->ObjectRegister(object_id);
    //audio_engine->ObjectSetListeners(object_id, &DEFAULT_LISTENER, 1);
}

AudioComponent::~AudioComponent()
{
    cross::EngineGlobal::Inst().GetAudioEngine()->ObjectUnregister(GetObjectID());
}

void AudioComponent::Serialize(SerializeNode& node, SerializeContext& context) const
{
    GameObjectComponent::Serialize(node, context);
    node["IsSpatialListener"] = mIsSpatialListener;
    node["Bank"] = mBank;
}

bool AudioComponent::Deserialize(const DeserializeNode& in, SerializeContext& context)
{
    if(in.HasMember("IsSpatialListener"))
        mIsSpatialListener = in["IsSpatialListener"].AsBoolean();
    else
        mIsSpatialListener = false;
    if(in.HasMember("Bank"))
        mBank = in["Bank"].AsString();
    else
        mBank = "";
    return GameObjectComponent::Deserialize(in, context);
}

void AudioComponent::StartGame()
{
    GameObjectComponent::StartGame();
    if (mBank.length())
        if (0 != cross::EngineGlobal::Inst().GetAudioEngine()->BankLoad(mBank.c_str()))
            LOG_ERROR("failed to load bank {}", mBank);
}

void AudioComponent::EndGame()
{
    if (mBank.length())
        if(0 != cross::EngineGlobal::Inst().GetAudioEngine()->BankUnload(mBank.c_str()))
            LOG_ERROR("failed to unload bank {}", mBank);
    GameObjectComponent::EndGame();
}

bool AudioComponent::GetIsSpatialListener() const
{
    return mIsSpatialListener;
}

void AudioComponent::SetIsSpatialListener(bool value)
{
    mIsSpatialListener = value;
}

std::string AudioComponent::GetBank() const
{
    return mBank;
}

void AudioComponent::SetBank(std::string const& value)
{
    mBank = value;
}

cross::AudioEngine::ObjectID AudioComponent::GetObjectID() const
{
    return reinterpret_cast<cross::AudioEngine::ObjectID>(this);
}

bool AudioComponent::AddListener(AudioComponent* listener) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine::ObjectID const other = listener->GetObjectID();

    cross::AudioEngine * const audioEngine = cross::EngineGlobal::Inst().GetAudioEngine();
    if(listener->GetIsSpatialListener())
    {
        if(0 != audioEngine->SpatialSetListener(other))
            return false;
        ++listener->mSpatialListenerRefCount;
    }
    return 0 == audioEngine->ObjectAddListener(self, other);
}

bool AudioComponent::RemoveListener(AudioComponent* listener) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine::ObjectID const other = listener->GetObjectID();

    cross::AudioEngine * const audioEngine = cross::EngineGlobal::Inst().GetAudioEngine();
    if(listener->GetIsSpatialListener())
    {
        --listener->mSpatialListenerRefCount;
        if(listener->mSpatialListenerRefCount <= 0)
            (0 != audioEngine->SpatialUnsetListener(other));
    }
    return 0 == audioEngine->ObjectRemoveListener(self, other);
}

bool AudioComponent::SetVolume(AudioComponent * listener, float const volume) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine::ObjectID const other = listener->GetObjectID();

    cross::AudioEngine * const audioEngine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audioEngine->ObjectSetVolume(self, other, volume);
}

bool AudioComponent::Move(cross::Float3 const& position, cross::Float3 const& forward, cross::Float3 const& up) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->ObjectSetPose(self, position, forward, up);
}

bool AudioComponent::Move(cross::Double3 const& position, cross::Double3 const& forward, cross::Double3 const& up) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->ObjectSetPose(self, position, forward, up);
}

cross::AudioEngine::EventPtr AudioComponent::Post(std::string const& event_name
    , cross::AudioEngine::EndCallback const & end_callback
    , cross::AudioEngine::MarkerCallback const & marker_callback) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Post(audio_engine->StringToID(event_name.c_str()), end_callback, marker_callback);
}

cross::AudioEngine::EventPtr AudioComponent::Post(cross::AudioEngine::EventID const & event_id
    , cross::AudioEngine::EndCallback const & end_callback
    , cross::AudioEngine::MarkerCallback const & marker_callback) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine::EventPtr event_ptr = 0;
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    if (audio_engine->EventPost(self, event_id, &event_ptr, end_callback, marker_callback))
        return 0;
    return static_cast<unsigned int>(event_ptr);
}

bool AudioComponent::Stop(cross::AudioEngine::EventPtr const event_ptr, int const duration_ms, AudioCurveInterpolation const curve) const
{
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->EventStop(event_ptr, duration_ms, Convert(curve));
}

bool AudioComponent::StopAll() const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    return 0 == cross::EngineGlobal::Inst().GetAudioEngine()->EventStopAll(self);
}

bool AudioComponent::Set(std::string const& param_name, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Set(audio_engine->StringToID(param_name.c_str()), value, duration_ms, curve);
}

bool AudioComponent::Set(cross::AudioEngine::ParamID const & param_id, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->ParamSet(self, param_id, value, duration_ms, Convert(curve));
}

bool AudioComponent::Set(cross::AudioEngine::EventPtr const event_ptr, std::string const& param_name, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Set(event_ptr, audio_engine->StringToID(param_name.c_str()), value, duration_ms, curve);
}

bool AudioComponent::Set(cross::AudioEngine::EventPtr const event_ptr, cross::AudioEngine::ParamID const & param_id, float const value, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->ParamSet(static_cast<cross::AudioEngine::EventPtr>(event_ptr), param_id, value, duration_ms, Convert(curve));
}

bool AudioComponent::Reset(std::string const& param_name, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Reset(audio_engine->StringToID(param_name.c_str()), duration_ms, curve);
}

bool AudioComponent::Reset(cross::AudioEngine::ParamID const & param_id, int const duration_ms, AudioCurveInterpolation const curve) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->ParamReset(self, param_id, duration_ms, Convert(curve));
}

bool AudioComponent::Switch(std::string const& switch_name, std::string const& switch_value) const
{
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return Switch(audio_engine->StringToID(switch_name.c_str()), audio_engine->StringToID(switch_value.c_str()));
}

bool AudioComponent::Switch(cross::AudioEngine::SwitchID const & switch_id, cross::AudioEngine::SwitchValueID const & switch_value) const
{
    cross::AudioEngine::ObjectID const self = GetObjectID();
    cross::AudioEngine* const audio_engine = cross::EngineGlobal::Inst().GetAudioEngine();
    return 0 == audio_engine->SwitchSet(self, switch_id, switch_value);
}
}   // namespace cegf
