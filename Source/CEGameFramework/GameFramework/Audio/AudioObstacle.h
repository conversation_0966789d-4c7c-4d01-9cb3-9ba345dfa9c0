#pragma once
#include "GameFramework/Components/PrimitiveComponent.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include <Runtime/Audio/AudioEngine.h>
#include <CECommon/Geometry/CollisionMesh.h>

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, <PERSON>uerts, Cli) AudioObstacle : public PrimitiveComponent
{
public:
    StaticMetaClassName(AudioObstacle);
    CEMeta(Reflect)
    AudioObstacle();
    virtual ~AudioObstacle();
    virtual void Serialize(SerializeNode& node, SerializeContext& context) const override;
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    virtual void Init() override;
    virtual void Uninit(bool bShouldNotifyECS) override;
    virtual void Tick(float deltaTime) override; // !
    virtual void Draw() const override; // !
    virtual void StartGame() override;
    virtual void EndGame() override;
    virtual void BeginDestroy() override;
    virtual void Activate() override; // !
    virtual void Deactivate() override; // !
    virtual void SetOwner(GameObject* owner) override;
    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask & bitMask) const override;

    cross::AudioEngine::ObjectID GetObjectID() const;

public: // script and editor
    CEFunction(Cli, ScriptCallable) bool GetIsRoom() const;
    CEFunction(Cli, ScriptCallable) void SetIsRoom(bool value);
    CEFunction(Cli, ScriptCallable) std::string GetTexture() const;
    CEFunction(Cli, ScriptCallable) void SetTexture(std::string value);
    CEFunction(Cli, ScriptCallable) bool GetUseForReflectionAndDiffraction() const;
    CEFunction(Cli, ScriptCallable) void SetUseForReflectionAndDiffraction(bool value);
    CEFunction(Cli, ScriptCallable) bool GetBypassPortalSubtraction() const;
    CEFunction(Cli, ScriptCallable) void SetBypassPortalSubtraction(bool value);
    CEFunction(Cli, ScriptCallable) bool GetIsSolid() const;
    CEFunction(Cli, ScriptCallable) void SetIsSolid(bool value);
    CEFunction(Cli, ScriptCallable) std::string GetReverbAuxBus() const;
    CEFunction(Cli, ScriptCallable) void SetReverbAuxBus(std::string value);
    CEFunction(Cli, ScriptCallable) float GetReverbLevel() const;
    CEFunction(Cli, ScriptCallable) void SetReverbLevel(float value);
    CEFunction(Cli, ScriptCallable) float GetTransmissionLoss() const;
    CEFunction(Cli, ScriptCallable) void SetTransmissionLoss(float value);
    CEFunction(Cli, ScriptCallable) float GetAuxSendLevelToSelf() const;
    CEFunction(Cli, ScriptCallable) void SetAuxSendLevelToSelf(float value);
    CEFunction(Cli, ScriptCallable) float GetPriority() const;
    CEFunction(Cli, ScriptCallable) void SetPriority(float value);

private:
    bool BuildGeometry(cross::CollisionMesh const* const collisionMesh);
    void BuildMesh(GameObject const* const gameObject);
    void BuildTraverseChildren(GameObject const* const gameObject);
    void ClearGeometryAndObstacles();

private:
    bool mIsRoom = false;
    std::string mTexture = "Brick";
    bool mUseForReflectionAndDiffraction = true;
    bool mBypassPortalSubtraction = false;
    bool mIsSolid = false;
    std::string mReverbAuxBus = "Room";
    float mReverbLevel = 1.0f;
    float mTransmissionLoss = 1.0f;
    float mAuxSendLevelToSelf = 1.0f;
    float mPriority = 100.0f;

private:
    bool mDrawn = false;
    std::vector<cross::CollisionMesh const*> mCollisionMeshes;
    std::vector<GameObject const*> mGameObjects;
};
}
