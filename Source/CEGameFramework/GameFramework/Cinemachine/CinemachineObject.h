#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Cinemachine/CinemachineComponent.h"

namespace cegf {

class GAMEFRAMEWORK_API CEMeta(Reflect, WorkflowType, <PERSON>li, Puerts) CinemachineObject : public GameObject
{
public:
    CEGameplayInternal();
    StaticMetaClassName(CinemachineObject)
    CinemachineObject();

    virtual ~CinemachineObject();

    virtual void InitializeComponents() override;

    CinemachineComponent* GetCinemachineComponent() const { return mCinemachineComponent; }

protected:
    CinemachineComponent* mCinemachineComponent = nullptr;
};

using CinemachineObjectPtr = std::shared_ptr<CinemachineObject>;

}   // namespace cegf
