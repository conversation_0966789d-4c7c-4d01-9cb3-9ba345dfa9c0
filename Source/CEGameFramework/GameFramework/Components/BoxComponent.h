#pragma once
#include "GameFramework/GameFrameworkGlobals.h"
#include "GameFramework/Components/BasicComponent.h"
#include "CrossPhysics/PhysicsEngine/PhysicsGeometry.h"
#include "GameFramework/Components/PhysicsComponent.h"
namespace cegf {

class GAMEFRAMEWORK_API CEMeta(Cli, Reflect, Puerts) BoxComponent : public PhysicsComponent
{
public:
    StaticMetaClassName(BoxComponent);
    CEMeta(Reflect)
    BoxComponent();

    virtual void Init() override;

    CEMeta(Cli, Puerts)
    const cross::Float3* GetBoxExtents() const;

    CEMeta(Cli, Puerts)
    void SetBoxExtents(const cross::Float3& inExtents);

protected:
    cross::PhysicsGeometryBox mBox;
};

}   // namespace cegf
