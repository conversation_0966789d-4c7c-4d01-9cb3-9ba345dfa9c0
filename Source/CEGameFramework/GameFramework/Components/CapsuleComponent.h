#pragma once
#include "GameFramework/Components/BasicComponent.h"
#include "GameFramework/GameFrameworkGlobals.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "GameFramework/Components/PhysicsComponent.h"

namespace cegf {

class GAMEFRAMEWORK_API CEMeta(Cli, Reflect, Puerts) CapsuleComponent : public PhysicsComponent
{
public:
    StaticMetaClassName(CapsuleComponent);
    CEMeta(Reflect)
    CapsuleComponent();

    virtual ~CapsuleComponent();

    virtual void Init() override;

    CEMeta(Cli)
    void SetCapsuleSize(float radius, float halfHeight);

    void SetOffset(const cross::Float3& offset);

    CEMeta(Cli)
    float GetCapsuleRadius() const;

    CEMeta(Cli)
    float GetCapsuleHalfHeight() const;
protected:
    cross::PhysicsGeometryCapsule mCapsule;
};

}   // namespace cegf
