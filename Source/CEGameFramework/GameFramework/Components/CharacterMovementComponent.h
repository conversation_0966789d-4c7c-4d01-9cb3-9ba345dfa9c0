#pragma once
#include "GameFramework/Components/PawnMovementComponent.h"
#include "CEGameplay/CharacterMovementSystemG.h"
#include "CEGameplay/CharacterMovementComponent.h"

namespace cegf
{

class Character;

class GAMEFRAMEWORK_API CEMeta(Reflect,Puerts) CharacterMovementComponent : public PawnMovementComponent
{
public:
    StaticMetaClassName(CharacterMovementComponent);
    CEMeta(Reflect) 
    CharacterMovementComponent();
    virtual ~CharacterMovementComponent();

    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const override;

    void SetOwnerCharacter(Character* ownerCharacter) { mOwner<PERSON>haracter = ownerCharacter; }
    
    Character* GetOwnerCharacter() const { return mOwnerCharacter; }
    CEFunction(ScriptCallable)
    virtual void Jump();
    CEFunction(ScriptCallable)
    virtual void StopJumping();
    CEFunction(ScriptCallable)
    cross::Float3 GetCurrentVelocityInWorldSpace() const;
    CEFunction(ScriptCallable)
    cross::Float3 GetCurrentVelocityInRootSpace() const;
    CEFunction(ScriptCallable)
    void SetMaxWalkSpeed(float maxSpeed);
    CEFunction(ScriptCallable)
    cross::Float3A GetAngleBetweenAimForwardAndCurrentVelocity() const;
    CEFunction(ScriptCallable)
    void SetAngleBetweenActorForwardAndCurrentVelocity(float yawOffset);
    CEFunction(ScriptCallable)
    void Crouch();
    CEFunction(ScriptCallable)
    void UnCrouch();
    CEFunction(ScriptCallable)
    void SetAimGameObject(GameObject* aim);

    CEFunction(ScriptCallable)
    cross::Float3 GetLastInputVector();

    CEFunction(ScriptCallable)
    void SetRotationMode(int RotationMode);
    CEFunction(ScriptCallable)
    void InputForward(float value);
    CEFunction(ScriptCallable)
    void InputRight(float value);

protected:
    //owner character
    Character* mOwnerCharacter = nullptr;

    cross::MovementMode MovementMode;

};

}
