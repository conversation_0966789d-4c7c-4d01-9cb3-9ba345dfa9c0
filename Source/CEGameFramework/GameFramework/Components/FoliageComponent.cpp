#include "FoliageComponent.h"

#include "Runtime/GameWorld/FoliageSystemG.h"

namespace cegf
{
void FoliageComponent::SetIntersection(bool enable)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetIntersection(comp.Write(), enable);
    }
}

bool FoliageComponent::GetIntersection() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetIntersection(comp.Read());
    }
    return {};
}

void FoliageComponent::SetEnable(bool enable)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetEnable(comp.Write(), enable);
    }
}

bool FoliageComponent::GetEnable() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetEnable(comp.Read());
    }
    return {};
}

void FoliageComponent::SetGlobalScale(float globalScale)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetGlobalScale(comp.Write(), globalScale);
    }
}

float FoliageComponent::GetGlobalScale() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetGlobalScale(comp.Read());
    }
    return {};
}

void FoliageComponent::SetGlobalRangeScale(float globalScale)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetGlobalRangeScale(comp.Write(), globalScale);
    }
}

float FoliageComponent::GetGlobalRangeScale() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetGlobalRangeScale(comp.Read());
    }
    return {};
}

void FoliageComponent::SetMaxRandomCulling(float maxRandomculling)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetMaxRandomCulling(comp.Write(), maxRandomculling);
    }
}

float FoliageComponent::GetMaxRandomCulling() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetMaxRandomCulling(comp.Read());
    }
    return {};
}

void FoliageComponent::SetDensity(float density)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetDensity(comp.Write(), density);
    }
}

float FoliageComponent::GetDensity() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetDensity(comp.Read());
    }
    return {};
}

void FoliageComponent::SetPCGReservedCapacity(UInt32 count)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetPCGReservedCapacity(comp.Write(), count);
    }
}

UInt32 FoliageComponent::GetPCGReservedCapacity() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetPCGReservedCapacity(comp.Read());
    }
    return {};
}

bool FoliageComponent::GetSubmeshVisible(UInt32 submeshIndex)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetSubmeshVisible(comp.Read(), submeshIndex);
    }
    return {};
}

void FoliageComponent::SetSubmeshVisible(UInt32 submeshIndex, bool visible)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetSubmeshVisible(comp.Write(), submeshIndex, visible);
    }
}

void FoliageComponent::SetLightCastShadow(bool castShadow)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetLightCastShadow(comp.Write(), castShadow);
    }
}

bool FoliageComponent::GetLightCastShadow() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetLightCastShadow(comp.Read());
    }
    return {};
}

void FoliageComponent::SetEditorPrefabResource(const std::string& prefabPath)
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        sys->SetEditorPrefabResource(comp.Write(), prefabPath);
    }
}

size_t FoliageComponent::GetInstanceCount() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetInstanceCount(comp.Read());
    }
    return {};
}

size_t FoliageComponent::GetInstanceLightCount() const
{
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetInstanceLightCount(comp.Read());
    }
    return {};
}
cross::FoliageGenerationType FoliageComponent::GetFoliageGenerationType() const {
    cross::FoliageSystemG* sys = GetSystem<cross::FoliageSystemG>();
    if (sys)
    {
        auto comp = GetECSComponent<cross::FoliageComponentG>();
        return sys->GetFoliageGenerationType(comp.Read());
    }
    return {};
}

void FoliageComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
{
    GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
    bitMask.Set(cross::FoliageComponentG::GetDesc()->GetMaskBitIndex(), true);
}
} // namespace cegf
