#include "InstancedStaticModelComponent.h"
#include "Runtime/GameWorld/InstancedStaticModelSystemG.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"
#include "Runtime/GameWorld/AABBSystemG.h"

namespace cegf
{

    class InstancedStaticModelComponentImpl : public IInstancedStaticModelComponent
    {
    public:
        InstancedStaticModelComponentImpl(GameObjectComponent* comp) :mParent(comp) {}

#pragma region Instanced Model Component 
        bool SetModelAssetPath(const std::string& assetpath)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->SetModelAssetPath(comp.Write(), assetpath);
        }
        std::string GetModelAssetPath() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetModelAssetPath(comp.Read());
        }
        UInt32 GetModelAssetLODCount() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetModelAssetLODCount(comp.Read());
        }
        bool SetModelMaterialPath(const std::string& assetpath, SInt32 subModelIndex, SInt32 lodIndex)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->SetModelMaterialPath(comp.Write(), assetpath, subModelIndex, lodIndex);
        }
        std::string GetModelMaterialPath(UInt32 subModelIndex, UInt32 lodIndex) const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetModelMaterialPath(comp.Read(), subModelIndex, lodIndex);
        }
        void SetModelEnityDistanceCulling(const cross::EntityDistanceCulling& entityCulling)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetModelEnityDistanceCulling(comp.Write(), entityCulling);
        }
        cross::EntityDistanceCulling GetModelEnityDistanceCulling()
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetModelEnityDistanceCulling(comp.Read());
        }
        bool IsModelVisible() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->IsModelVisible(comp.Read());
        }
        void SetModelVisible(bool isVisible)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetModelVisible(comp.Write(), isVisible);
        }
        bool IsSubModelVisible(UInt32 lodIndex, UInt32 subModelIndex) const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->IsSubModelVisible(comp.Read(), lodIndex, subModelIndex);
        }
        void SetSubModelVisible(bool isVisible, UInt32 lodIndex, UInt32 subModelIndex)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetSubModelVisible(comp.Write(), isVisible, lodIndex, subModelIndex);
        }
        void SetModelReceiveDecals(bool value)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetModelReceiveDecals(comp.Write(), value);
        }
        bool GetModelReceiveDecals() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetModelReceiveDecals(comp.Read());
        }
        void SetModelDirty()
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetModelDirty(comp.Write());
        }
        UInt32 GetSubModelCount(UInt32 lodIndex) const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetSubModelCount(comp.Read(), lodIndex);
        }
        void SetIntersection(bool enable)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            sys->SetIntersection(comp.Write(), enable);
        }
        bool GetIntersection() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetIntersection(comp.Read());
        }
        bool IsModelAssetStreamable() const
        {
            return false;
        }
        void SetModelAssetStreamable(bool enabled)
        {

        }
#pragma endregion Model Component 

#pragma region Instanced StaticModel Component 
        bool SetInstanceDataResourcePath(const std::string& resourcePath)
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->SetInstanceDataResourcePath(comp.Write(), resourcePath);
        }
        const std::string& GetInstanceDataResourcePath() const
        {
            cross::InstancedStaticModelSystemG* sys = mParent->GetSystem<cross::InstancedStaticModelSystemG>();
            auto comp = mParent->GetECSComponent<cross::InstancedStaticModelComponentG>();
            return sys->GetInstanceDataResourcePath(comp.Read());
        }
#pragma endregion Instanced StaticModel Component
    private:
        GameObjectComponent* mParent;
    };

    InstancedStaticModelComponent::InstancedStaticModelComponent()
    {
        InitInstancedStaticModelComponentImpl(std::make_shared<InstancedStaticModelComponentImpl>(this));
    }

    void InstancedStaticModelComponent::InitInstancedStaticModelComponentImpl(std::shared_ptr<IInstancedStaticModelComponent> Impl)
    {
        InitModelComponentImpl(Impl);
        mInstancedStaticModelComponentImpl = Impl;
    }

    bool InstancedStaticModelComponent::SetInstanceDataResourcePath(const std::string& resourcePath)
    {
        return mInstancedStaticModelComponentImpl->SetInstanceDataResourcePath(resourcePath);
    }

    const std::string& InstancedStaticModelComponent::GetInstanceDataResourcePath() const
    {
        return mInstancedStaticModelComponentImpl->GetInstanceDataResourcePath();
    }

    void InstancedStaticModelComponent::GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask& bitMask) const
    {
        GameObjectComponent::GetRelatedECSComponentBitMask(bitMask);
        bitMask.Set(cross::InstancedStaticModelComponentG::GetDesc()->GetMaskBitIndex(), true);
        bitMask.Set(cross::AABBComponentG::GetDesc()->GetMaskBitIndex(), true);
        bitMask.Set(cross::RenderPropertyComponentG::GetDesc()->GetMaskBitIndex(), true);
    }
} // namespace cegf
