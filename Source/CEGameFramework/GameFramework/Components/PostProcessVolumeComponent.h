#pragma once
#include "RenderEngine/PostProcessVolumeSetting.h"

#include "GameFramework/Components/Component.h"
#include "RenderEngine/RenderPipeline/Effects/VolumetricFog.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, Cli, WorkflowType, Puerts) PostProcessVolumeComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(PostProcessVolumeComponent);
    CEMeta(Reflect) PostProcessVolumeComponent() = default;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableHistogramExposure(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledHistogramExposure() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableManualExposure(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnalbedManualExposure() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableLocalExposure(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledLocalExposure() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableBloom(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledBloom() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableChromaticAberration(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledChromaticAberration() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableVignette(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledVignette() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableTonemapSetting(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledTonemapSetting() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableLensFlare(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledLensFlare() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableDOF(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledDOF() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableMotionBlur(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledMotionBlur() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableScreenBlur(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledScreenBlur() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableRain(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledRain() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableWind(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledWind() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnableSnow(bool val);
    CEFunction(Reflect, Cli, ScriptCallable) bool IsEnabledSnow() const;
    CEFunction(Reflect, Cli, ScriptCallable) void SetEnable(bool bEnable);

    CEFunction(Reflect, Cli, ScriptCallable)
    void SetPropertyPostProcessVolumeSettings(const cross::PostProcessVolumeSetting& val);
    CEFunction(Reflect, Cli, ScriptCallable)
    cross::PostProcessVolumeSetting GetPropertyPostProcessVolumeSettings();


    virtual void GetRelatedECSComponentBitMask(cross::ecs::ComponentBitMask & bitMask) const override;
};
} // namespace cegf
