#pragma once
#include "GameFramework/Components/Component.h"

namespace cegf
{

class GAMEFRAMEWORK_API CEMeta(Reflect, Cli) PrimitiveComponent : public GameObjectComponent
{
public:
    StaticMetaClassName(PrimitiveComponent);
    CEMeta(Reflect, Cli)
    PrimitiveComponent() = default;

    virtual void Init() override;

    virtual void Uninit(bool bShouldNotifyECS = false);

    virtual void Tick(float deltaTime) override;

    virtual void Draw() const override {};
    
    virtual void Serialize(SerializeNode & node, SerializeContext & context) const override;

    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;

    virtual void GetReferenceResource(cross::ResourcePtr resource) const {}

    //void MarkRenderStateDirty();
    virtual void BeginDestroy() override;

    void MarkForNeedEndOfFrameDraw();

    void MarkForNoNeedEndOfFrameDraw();

    //virtual void CreateRenderState() override;

    //bool ShouldComponentAddToScene() const;
};

}

