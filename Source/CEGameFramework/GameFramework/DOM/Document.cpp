#include "Document.h"
#include "Element.h"
#include "RmlUI/Core/Context.h"
#include "RmlUI/Core/ElementDocument.h"
#include "RmlUI/Core/EventListener.h"

namespace cegf
{
    Document::Document(Rml::ElementDocument* document)
    {
        mDocument = document;
    }

    Document::~Document()
    {
        Clear();
    }

    void Document::Clear()
    {
        for(ElementMap::iterator it = mElementMap.begin(); it != mElementMap.end(); ++it)
            delete it->second;
        mElementMap.clear();
    }

    void Document::SetDocument(Rml::ElementDocument* const document)
    {
        if(mDocument != document)
        {
            Clear();
            mDocument = document;
        }
    }

    Element * Document::getElementById(std::string const & id)
    {
		if(mDocument == nullptr) return nullptr;
        Rml::Element * const element = mDocument->GetElementById(id);
        if(nullptr == element) return nullptr;

        ElementMap::iterator it = mElementMap.find(element);
        if(it != mElementMap.end())
            return it->second;
        else
        {
            Element * newElement = new Element(element);
            mElementMap.insert(std::make_pair(element, newElement));
            return newElement;
        }
        return nullptr;
    }
}
