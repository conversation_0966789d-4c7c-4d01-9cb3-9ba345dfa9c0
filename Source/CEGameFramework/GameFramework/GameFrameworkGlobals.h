#pragma once
#include "GameFramework/Objects/ObjectBase.h"
#include "reflection/runtime/cxx_runtime.hpp"
#include "CrossBase/Template/TypeTraits.hpp"

namespace cegf
{

using ObjectMetaClass = gbf::reflection::MetaClass;

template<typename T>
const ObjectMetaClass* QueryMetaClass()
{
    return gbf::reflection::query_meta_class<T>();
}

inline const ObjectMetaClass* QueryMetaClass(const std::string& className)
{
    return __type_of_name(className);
}

template<typename T, typename... Args>
std::enable_if_t<std::is_base_of_v<ObjectBase, T>, std::shared_ptr<T>> NewObject(Args&&... args)
{
    return gbf::reflection::make_shared_with_rtti<T>(std::forward<Args>(args)...);
}

template<typename... Args>
ObjectBasePtr NewObject(const ObjectMetaClass* metaClass, Args&&... args)
{
    if (metaClass == nullptr)
    {
        LOG_ERROR("new object failed, meta class is null");
        return nullptr;
    }

    auto userObject = gbf::reflection::cxx::CreateShared(*metaClass, std::forward<Args>(args)...);
    if (!userObject)
    {
        LOG_ERROR("new object failed, create user object failed {}", metaClass->name());
        return nullptr;
    }

    cegf::ObjectBasePtr basePtr;
    bool ret = gbf::reflection::__unbox(userObject, basePtr);
    if (!ret)
    {
        LOG_ERROR("new object failed, unbox from userobject to object base failed {}", metaClass->name());
        return nullptr;
    }
    return basePtr;
}

template<typename... Args>
ObjectBasePtr NewObject(const std::string& className, Args&&... args)
{
    auto metaClass = __type_of_name(className);
    if (!metaClass)
    {
        LOG_ERROR("new object failed, meta class is nullptr {}", className);
        return nullptr;
    }
    return NewObject(metaClass, std::forward<Args>(args)...);
}

// TODO(hendrikwang): To export functions with container return type to Cli, we need a simple wrapper struct as proposed below or
// the return type will be recognized as UnknowKeeper^. But current code generation does not fully support template class :(

// template<class T>
// concept Iterable = cross::TIsContainerV<T>;
//
// template<Iterable T>
// class GAMEFRAMEWORK_API CEMeta(Cli) StlContainerProxy
//{
// public:
//     CEMeta(Cli)
//     StlContainerProxy() = default;
//     CEMeta(Cli)
//     StlContainerProxy(T && other)
//         : mStorage(std::move(other))
//     {}
//
//     CEMeta(Cli)
//     T mStorage;
// };
//
// template<class T>
// using VectorProxy = StlContainerProxy<std::vector<T>>;
//
// template<class T>
// using ListProxy = StlContainerProxy<std::list<T>>;
//
// template<class T>
// using DequeProxy = StlContainerProxy<std::deque<T>>;
//
// template<class T>
// using SetProxy = StlContainerProxy<std::set<T>>;
//
// template<class T>
// using MultiSetProxy = StlContainerProxy<std::multiset<T>>;
//
// template<class K, class V>
// using MapProxy = StlContainerProxy<std::map<K, V>>;
//
// template<class K, class V>
// using HashMapProxy = StlContainerProxy<std::unordered_map<K, V>>;
//
// template<class K, class V>
// using MultiMapProxy = StlContainerProxy<std::multimap<K, V>>;

} // namespace cegf
