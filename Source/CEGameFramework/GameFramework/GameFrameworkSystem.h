#pragma once
#include "CECommon/Common/GlobalSystemBase.h"
#include "GameEngine.h"
#include "GameFramework/Tests/GameFrameworkTest.h"

namespace cross {

struct GOSerializerComponentG;
}
namespace cegf {

class GAMEFRAMEWORK_API GameFrameworkSystem : public cross::GlobalGameSystemBase
{
public:
    CEFunction(Reflect)
    static GameFrameworkSystem* CreateInstance();
    CEFunction(Reflect)
    static const cross::GlobalSystemDesc& GetDesc();

    virtual const cross::GlobalSystemDesc& GetSystemDesc() const { return GetDesc(); }

    // Release should be the same as calling destructor
    virtual void Release() override;

    // This function will be invoked before Release()
    virtual void NotifyShutdownEngine() override;

    virtual cross::GlobalRenderSystemBase* GetRenderSystem() override { return nullptr; }

    virtual void NotifyAddRenderSystemToRenderEngine() override {}

    virtual void NotifyEvent(const cross::SystemEventBase& eventBase, UInt32& flag) override;

    GameEnginePtr GetGameEngine() { return mGameEngine; }

    bool AddEventDispatcher(evt::GOEventDispatcherPtr inDispatcher);

protected:
    GameFrameworkSystem();

    virtual ~GameFrameworkSystem();
    
    virtual void OnBeforeBeginFrame(cross::FrameParam* frameParam);
    
    virtual void OnBeginFrame(cross::FrameParam* frameParam);
    
    virtual void OnFirstUpdate(cross::FrameParam* frameParam);
    
    virtual void OnPreUpdate(cross::FrameParam* frameParam);
    
    virtual void OnUpdate(cross::FrameParam* frameParam);
    
    virtual void OnPostUpdate(cross::FrameParam* frameParam);
    
    virtual void OnEndFrame(cross::FrameParam* frameParam);

    void Init();

private:
    GameEnginePtr mGameEngine;

    //test
    GameFrameworkTestPtr mFrameworkTest;

    bool createObj = false;

    friend struct cross::GOSerializerComponentG;
};

}// namespace cegf

namespace cross {

struct GOSerializerComponentG : ecs::IComponent
{
    CEComponentInternal(SystemType = cegf::GameFrameworkSystem)
    CEFunction(Reflect)
    static GAMEFRAMEWORK_API cross::ecs::ComponentDesc* GetDesc();

    static SerializeNode SerializeGOSComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeGOSComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeGOSComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void UpdateDeserializeGOSComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
    static void GetResourceGOSComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource);

    static inline const std::string SK_GAMEOBJECT = "GameObject";

    const cegf::GameObject* GetGameObject() const
    {
        return mGameObject;
    }
     
private:
    cegf::GameObject* mGameObject = nullptr;

    friend class cegf::GameObject;
    CE_Serialize_Deserialize;
};
}   // namespace cross