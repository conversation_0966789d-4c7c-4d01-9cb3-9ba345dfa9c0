#pragma once
#include "CrossBase/Platform/PlatformTypes.h"

#if CROSSENGINE_WIN
#    pragma warning(disable : 4251)
#    ifdef GameFramework_EXPORTS
#        define GAMEFRAMEWORK_API __declspec(dllexport)
#    else
#        define GAMEFRAMEWORK_API __declspec(dllimport)
#    endif
#else
#    if defined(__GNUC__)
#        define GAMEFRAMEWORK_API __attribute__((visibility("default")))
#    else
#        define GAMEFRAMEWORK_API
#    endif
#endif

enum class GameWorldBlockType : UInt8
{
    LevelBlock,
    PreBlock,
    CustomBlock
};