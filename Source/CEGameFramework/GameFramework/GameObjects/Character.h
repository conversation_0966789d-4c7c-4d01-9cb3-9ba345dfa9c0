#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "GameFramework/Components/CharacterMovementComponent.h"
#include "GameFramework/GameObjects/Pawn.h"
#include "GameFramework/Components/AnimationComponent.h"

namespace cegf
{
class ModelComponent;
class CapsuleComponent;
class CharacterMovementComponent;
class AnimationComponent;

class GAMEFRAMEWORK_API CEMeta(Reflect, WorkflowType, Puerts) Character : public Pawn
{
public:
    CEGameplayInternal() 
    StaticMetaClassName(Character)
	Character();

	virtual ~Character();

	virtual void InitializeComponents() override;

	virtual void Destroyed() override;

	ModelComponent* GetModelComponent() const { return mModelComponent; }

	CharacterMovementComponent* GetCharacterMovementComponent() const { return mCharacterMovementComponent; }

	CapsuleComponent* GetCapsuleComponent() const { return mCapsuleComponent; }
	
    CEFunction(WorkflowExecutable, PuertsCallable)
    void Jump();

	CEFunction(WorkflowExecutable)
    void StopJumping();

	CEFunction(WorkflowExecutable)
    void SetSkeletonAnimation(std::string _SkeletonPath, std::string _AnimatorResourcePath);

	CEFunction(WorkflowExecutable)
	std::string GetAnimatorAssetPath();

    CEFunction(WorkflowExecutable)
    std::string GetAnimatorName();

    CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamInt(std::string paramName, int value){
        mAnimationComponent->SetParameter<IntParameter, int>(paramName, value);
    }

	CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamFloat(std::string paramName, float value){
        mAnimationComponent->SetParameter<FloatParameter, float>(paramName, value);
    }

	CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamBool(std::string paramName, bool value){
        mAnimationComponent->SetParameter<BoolParameter, bool>(paramName, value);
    }

	CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamString(std::string paramName, std::string value){
        mAnimationComponent->SetParameter<StringParameter, std::string>(paramName, value);
    }

    CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamVec2(std::string paramName, cross::Float2A value){
        mAnimationComponent->SetParameter<Vector2Parameter, cross::Float2A>(paramName, value);
    }

    CEFunction(WorkflowExecutable, ScriptCallable)
    void SetParamVec3(std::string paramName, cross::Float3A value){
        mAnimationComponent->SetParameter<Vector3Parameter, cross::Float3A>(paramName, value);
    }

	CEFunction(WorkflowExecutable)
    void SetParamVec4(std::string paramName, cross::Float4A value){
        mAnimationComponent->SetParameter<Vector4Parameter, cross::Float4A>(paramName, value);
    }

protected:
	ModelComponent* mModelComponent;

	CapsuleComponent* mCapsuleComponent;

	CharacterMovementComponent* mCharacterMovementComponent;

	AnimationComponent* mAnimationComponent;
};

using CharacterPtr = std::shared_ptr<Character>;
}

