#include "GameFramework/GameObjects/GameObject.h"

#include "Components/WorkFlowComponent.h"
#include "CrossBase/Log.h"
#include "Resource/Resource.h"
#include "GameFramework/GameWorld.h"
#include "GameFramework/GameBlock.h"
#include "GameFramework/GameEngine.h"
#include "GameFramework/Components/Component.h"
#include "GameFramework/Camera/CameraComponent.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/RenderPropertySystemG.h"

#include "GameFramework/GameFrameworkSystem.h"
#include "GameFramework/GeneratedCode/GameFramework.script_ts.h"
#include "GameFramework/Event/Listeners.h"
#include <ranges>

std::string extractBetweenSpaceAndAbc(const std::string& input)
{
    size_t posSpace = input.find_last_of(' ');
    if (posSpace == std::string::npos)
    {
        return "No space found";
    }

    size_t posAbc = input.find("::abc");
    if (posAbc == std::string::npos)
    {
        return "No '::abc' found";
    }

    if (posSpace + 1 < posAbc)
    {
        return input.substr(posSpace + 1, posAbc - posSpace - 1);
    }

    return "Invalid position";
}


namespace cegf {

GameObject::GameObject()
{
    mTickFunction = std::make_shared<GameObjectTickFunction>();
    mTickFunction->bCanEverTick = false;
    mTickFunction->bCanTickInEditorViewport = false;
    mTickFunction->TickGroup = TickingGroup::TG_0;
    mTickFunction->SetTickFunctionEnable(true);

    mScriptTickFunction = std::make_shared<TypeScriptTickFunction>();
    mScriptTickFunction->bCanEverTick = true;
    mScriptTickFunction->bCanTickInEditorViewport = false;
    mScriptTickFunction->TickGroup = TG_0;
    mScriptTickFunction->SetTickFunctionEnable(true);

    mScriptListener = new evt::ScriptListener(this);
}

GameObject::~GameObject()
{
    if (mScriptListener)
    {
        delete mScriptListener;
    }
}

void GameObject::Tick(float deltaTime)
{
}

void GameObject::Serialize(SerializeNode& node, SerializeContext& context) const
{
    
    auto goInWorld = GetWorld()->GetGameObject(mObjectEntityID);
    if (goInWorld != this)
    {
        Assert(false);
    }

    // Serialize Meta and name
    node[SK_METACLASSTYPE] = GetMetaClass()->name();
    node[SK_OBJECTNAME] = GetName();
    if (mTsResource)
    {
        node[SCRIPT_PATH] = mTsResource->GetName();
    }
    else
    {
        node[SCRIPT_PATH] = "";
    }
    node[SK_SCRIPTEDITORFIELDS] = std::move(SerializeNode::ParseFromJson(mScriptEditorFieldsJson));

    SerializeNode componentsJson;
    // Serialize all components
    for (auto& component : mOwnedComponents)
    {
        SerializeNode json;
        component->Serialize(json, context);

        std::string componentType = component->GetMetaClass()->name();
        json[SK_COMPONENTTYPE] = componentType;

        componentsJson.PushBack(std::move(json));
    }

    node[SK_COMPONENTS] = std::move(componentsJson);
}

void GameObject::GetReferenceResource(cross::ResourcePtr resource) const
{
    for (auto&& comp : mOwnedComponents)
    {
        comp->GetReferenceResource(resource);
    }

    if (mTSObject)
    {
        cross::TypeScriptModule::Instance()->GetReferenceResource(mTSObject, resource->GetGuid_Str());
    }
}
void GameObject::SetScriptPath(const std::string& path)
{
    auto resource = gAssetStreamingManager->GetResource(path.c_str());
    if (!resource)
    {
        LOG_WARN("Invalid Script Resoruce: {}.", path);
    }
    mTsResource = TypeCast<cross::resource::TsResource>(resource);
    BindScriptObject();
    RegisterScriptTickFunction();
}

std::string GameObject::GetScriptPath()
{
    if (mTsResource)
    {
        return mTsResource->GetName();
    }
    else
    {
        return "";
    }
}

void ScriptJsonProperty::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    mValueNodes = inNode.Clone();
}

bool GameObject::Deserialize(const DeserializeNode& in, SerializeContext& context)
    {
    bool ret = true;
    if (!mHasInitialized)
    {
        Assert(false);
        return false;
    }

    // Deserialize and name       
    SetName(in[SK_OBJECTNAME].AsString());
    
    // Deserialize rts
    if (in.HasMember(SCRIPT_PATH) && !in[SCRIPT_PATH].AsString().empty())
    {
        auto path = in[SCRIPT_PATH].AsString();
        auto resource = gAssetStreamingManager->GetResource(path.c_str());
        if (!resource)
        {
            LOG_WARN("Invalid Script Resource: {}.", path);   
            return true;
        }
        mTsResource = TypeCast<cross::resource::TsResource>(resource);

        // Old version in[SK_SCRIPTEDITORFIELDS] is a string
        if (in.HasMember(SK_SCRIPTEDITORFIELDS) && in[SK_SCRIPTEDITORFIELDS].IsString())
        {
            UpdateLoadedScriptJsonProperty(in[SK_SCRIPTEDITORFIELDS].AsString());
        } // version 25.07.31 in[SK_SCRIPTEDITORFIELDS] is a json object
        else if (in.HasMember(SK_SCRIPTEDITORFIELDS)&& in[SK_SCRIPTEDITORFIELDS].IsObject())
        {
            UpdateLoadedScriptJsonProperty(in[SK_SCRIPTEDITORFIELDS].FormatToJson());
        }
    }

    // root components has been created when object initializing
    // create other components
    const DeserializeNode& components = in[SK_COMPONENTS];
    for (size_t i = 0; i < components.Size(); i++)
    {
        const DeserializeNode& component = components[i];
        const auto& componentType = component[SK_COMPONENTTYPE].AsString();
        auto* gameComp = GetComponentByMetaClassName(componentType); 
        if (gameComp == nullptr)
        {
            gameComp = AddComponent(componentType);
        }

        gameComp->Deserialize(component, context);
    }

    return true;
}

void GameObjectTickFunction::ExecTick(float DeltaTime, LevelTick TickType)
{
    if (mTargetObject && mTargetObject->GetWorld() && !mTargetObject->IsPendingDestroy())
    {
        if (TickType != LevelTick::LEVELTICK_ViewportsOnly || bCanTickInEditorViewport)
        {
            mTargetObject->Tick(DeltaTime);
        }
    }
}

void TypeScriptTickFunction::ExecTick(float DeltaTime, LevelTick TickType)
{
    if (mTargetObject && mTargetObject->GetWorld() && !mTargetObject->IsPendingDestroy())
    {

        if (TickType == LevelTick::LEVELTICK_ViewportsOnly && bCanTickInEditorViewport)
        {
            SCOPED_CPU_TIMING_DYNAMIC(TypeScript, fmt::format("{} {}", mTargetObject->GetName(), BuiltInEditorScriptFuncNames::Editor_OnTick));
            mTargetObject->GetTsObject().Action(BuiltInEditorScriptFuncNames::Editor_OnTick, DeltaTime);
        }

        if (TickType != LevelTick::LEVELTICK_ViewportsOnly)
        {
            SCOPED_CPU_TIMING_DYNAMIC(TypeScript, fmt::format("{} {}", mTargetObject->GetName(), BuiltInScriptFuncNames::OnTick));
            mTargetObject->GetTsObject().Action(BuiltInScriptFuncNames::OnTick, DeltaTime);
        }
    }
}

void GameObject::RegisterScriptTickFunction()
{
    if (mScriptTickFunction->bCanEverTick && (GetTsObject().IsFunction(BuiltInScriptFuncNames::OnTick) || GetTsObject().IsFunction(BuiltInEditorScriptFuncNames::Editor_OnTick)))
    {
        mScriptTickFunction->mTargetObject = this;
        auto world = GetWorld();
        mScriptTickFunction->RegisterTickFunction(world);
    }
}

void GameObject::UnRegisterScriptTickFunction()
{
    mScriptTickFunction->UnRegisterTickFunction();
}

void GameObject::RegisterEventListeners()
{
    GetWorld()->RegisterEventListener(mScriptListener);

    // register component listeners
    for (auto& component : mOwnedComponents)
    {
        if (component)
        {
            component->RegisterEventListeners();
        }
    }
}

void GameObject::UnRegisterEventListeners()
{
    GetWorld()->UnregisterEventListener(mScriptListener);

    // unregister component ticks
    for (auto& component : mOwnedComponents)
    {
        if (component)
        {
            component->UnRegisterEventListeners();
        }
    }
}

void GameObject::RegisterTickFunction()
{
    if (mTickFunction->bCanEverTick)
    {
        mTickFunction->mTargetObject = this;
        auto world = GetWorld();
        mTickFunction->RegisterTickFunction(world);
    }

    RegisterScriptTickFunction();

    // register component ticks
    for (auto& component : mOwnedComponents)
    {
        if (component)
        {
            component->RegisterTickFunction();
        }
    }
}

void GameObject::UnRegisterTickFunction()
{
    mTickFunction->UnRegisterTickFunction();

    UnRegisterScriptTickFunction();

    // register component ticks
    for (auto& component : mOwnedComponents)
    {
        if (component)
        {
            component->UnRegisterTickFunction();
        }
    }
}

void GameObject::StartGame()
{
    mHasStarted = true;
    for (auto& component : mOwnedComponents)
    {
        if (component->IsRegistered() && !component->HasStarted())
        {
            component->StartGame();
        }
    }
    mTSObject.Action(BuiltInScriptFuncNames::OnStartGame);
    RegisterTickFunction();

    RegisterEventListeners();
}

void GameObject::EndGame()
{
    mHasStarted = false;

    UnRegisterEventListeners();

    UnRegisterTickFunction();

    for (auto& component : mOwnedComponents)
    {
        if (component->HasStarted())
        {
            component->EndGame();
        }
    }
    mTSObject.Action(BuiltInScriptFuncNames::OnEndGame);
}

void GameObject::OnSpawn()
{
    
}

const std::string& GameObject::GetScriptEditorFieldsJson() const
{
    return mScriptEditorFieldsJson;
}
void GameObject::RebindScriptWithEditorFieldsJson(const std::string& inJson)
{
    mScriptEditorFieldsJson = inJson;
    // update script bind first, so contruction can get correct value;
    BindScriptObject();
}

void GameObject::BeginDestroy()
{
    for (auto& component : mOwnedComponents)
    {
        // unregister all components
        if (component->IsRegistered())
        {
            component->Unregister();
        }

        component->BeginDestroy();
    }

    UnRegisterTickFunction();
    ObjectBase::BeginDestroy();
}

void GameObject::Destroyed()
{
    if (mHasStarted)
    {
        EndGame();
    }

    //uninit all components
    for (auto& component : mOwnedComponents)
    {
        if (component->HasBeenInit())
        {
            component->Uninit();
        }

        component->Destroyed();
    }

    mOwnedComponents.clear();

    OnDestroyed.Broadcast(this);

    ObjectBase::Destroyed();
}

GameWorld* GameObject::GetWorld() const
{
    if (mBlock)
    {
        return mBlock->GetOwningWorld();
    }
    else
    {
        return gGameEngine->GetGameWorld(mObjectEntityID.GetWorldID());
    }
}

const std::string& GameObject::GetName() const
{
    auto meta = GetCrossGameWorld()->GetComponent<cross::ecs::EntityMetaComponentG>(GetObjectEntityID());
    auto metaSys = GetCrossGameWorld()->GetGameSystem<cross::EntityMetaSystem>();
    auto& name = metaSys->GetName(meta.Read());
    return name;
}

GameWorldBlock* GameObject::GetWorldBlock() const
{
    return mBlock;
}

GameObject* GameObject::GetParent() const
{
    cross::GameWorld* crossGameWorld = GetCrossGameWorld();
    auto transSys = crossGameWorld->GetGameSystem<cross::TransformSystemG>();
    cross::ecs::EntityID parentEntity = transSys->GetEntityParent(mObjectEntityID);

    auto ret = GetWorld()->GetGameObject(parentEntity);
    //Assert(ret == GetParent0());
    return ret;
}

GameObject* GameObject::GetChild(size_t index) const
{
    // TODO optimize
    auto children = GetChildren();
    if (index >= children.size())
        return nullptr;
    return children[index];
}

GameObject* GameObject::FindChild(const char* childName) const
{
    // TODO optimize
    auto children = GetChildren();
    auto it = std::find_if(children.begin(), children.end(), [childName](GameObject* obj) { return obj->GetName() == childName; });
    if (it == children.end())
        return nullptr;
    return *it;
}

GameObject* GameObject::FindInWorld(const char* name)
{
    return GetWorld()->FindGameObject(name);
}

GameObject* GameObject::FindChildRecursive(const char* childName) const
{
    // find in children
    if (GameObject* found = FindChild(childName))
    {
        return found;
    }

    // find in children of children recursively
    for (GameObject* child : GetChildren())
    {
        if (GameObject* found = child->FindChildRecursive(childName))
        {
            return found;
        }
    }

    return nullptr;
}

size_t GameObject::GetChildNum() const
{
    // TODO optimize
    return GetChildren().size();
}

std::vector<GameObject*> GameObject::GetChildren() const
{
    std::vector<GameObject*> ret;
    cross::GameWorld* crossGameWorld = GetCrossGameWorld();
    auto transSys = crossGameWorld->GetGameSystem<cross::TransformSystemG>();
    auto childrenEntity = transSys->GetEntityChildren(mObjectEntityID);
    ret.reserve(childrenEntity.size());

    for (auto child : childrenEntity)
    {
        auto childObj = GetWorld()->GetGameObject(child);
        if (childObj)
        {
            ret.emplace_back(childObj);
        }
    }
    return ret;
}

GameObject* GameObject::GetFirstChild() const{
    auto* transSys = GetSystem<cross::TransformSystemG>();
    cross::ecs::EntityID firstChild = transSys->GetEntityFirstChild(mObjectEntityID);
    return GetWorld()->GetGameObject(firstChild);
}
GameObject* GameObject::GetLastChild() const
{
    auto* transSys = GetSystem<cross::TransformSystemG>();
    cross::ecs::EntityID lastChild = transSys->GetEntityLastChild(mObjectEntityID);
    return GetWorld()->GetGameObject(lastChild);
}
GameObject* GameObject::GetNextSiblings() const
{
    auto* transSys = GetSystem<cross::TransformSystemG>();
    cross::ecs::EntityID nextSibling = transSys->GetEntityNextSibling(mObjectEntityID);
    auto gameobject = GetWorld()->GetGameObject(nextSibling);
    return gameobject;
}

GameObject* GameObject::GetPreSiblings() const
{
     auto* transSys = GetSystem<cross::TransformSystemG>();
    cross::ecs::EntityID nextSibling = transSys->GetEntityPreSibling(mObjectEntityID);
    return GetWorld()->GetGameObject(nextSibling);
}

GameObject* GameObject::FindSiblingByName(const std::string& name) const
{
    GameObject* siblingObj = nullptr;
    auto* transSys = GetSystem<cross::TransformSystemG>();

    // search backward
    cross::ecs::EntityID currentEntity = transSys->GetEntityPreSibling(mObjectEntityID);
    while (currentEntity != cross::ecs::EntityID::InvalidHandle() && currentEntity != mObjectEntityID)
    {
        GameObject* obj = GetWorld()->GetGameObject(currentEntity);
        if (obj && obj->GetName() == name)
        {
            return obj;
        }

        currentEntity = transSys->GetEntityPreSibling(currentEntity);
    }

    return nullptr;
}
std::string GameObject::GetEUID() {
    
    auto metaComp = GetECSComponent<cross::ecs::EntityMetaComponentG>();
    auto* metaSys = GetSystem<cross::EntityMetaSystem>();
    return metaSys->GetEUID(metaComp.Read()).GetString();
}
void GameObject::Joint(GameObject* Parent)
{
    auto* transSys = GetSystem<cross::TransformSystemG>();
    transSys->Joint(mObjectEntityID, Parent->GetObjectEntityID());
}
bool GameObject::GetVisible()
{
    auto* renderPropertySystem = GetSystem<cross::RenderPropertySystemG>();
    auto* entityMetaSystem = GetSystem<cross::EntityMetaSystem>();
    auto renderPropertyH = GetCrossGameWorld()->GetComponent<cross::RenderPropertyComponentG>(mObjectEntityID);
    if (renderPropertyH.IsValid())
    {
        return !renderPropertySystem->IsHide(renderPropertyH.Read());
    }
    return entityMetaSystem->GetEntityVisibility(mObjectEntityID);
}
void GameObject::SetVisible(bool visible) {
    auto* renderPropertySystem = GetSystem<cross::RenderPropertySystemG>();
    auto* entityMetaSystem = GetSystem<cross::EntityMetaSystem>();
    ;
    auto renderPropertyH = GetWorld()->GetCrossGameWorld()->GetComponent<cross::RenderPropertyComponentG>(mObjectEntityID);
    if (renderPropertyH.IsValid())
    {
        renderPropertySystem->SetHide(renderPropertyH.Write(), !visible);
    }
    entityMetaSystem->SetEntityVisibility(mObjectEntityID, visible);
}
void GameObject::SetVisibleHierarchy(bool visible)
{
    auto* renderPropertySystem = GetSystem<cross::RenderPropertySystemG>();
    auto* entityMetaSystem = GetSystem<cross::EntityMetaSystem>();
    auto* crossGameWorld = GetWorld()->GetCrossGameWorld();
    crossGameWorld->GetGameSystem<cross::TransformSystemG>()->TraverseHierarchyBreadth(mObjectEntityID, [visible, crossGameWorld,renderPropertySystem, entityMetaSystem](cross::ecs::EntityID traversedEntity) {
        auto renderPropertyH = crossGameWorld->GetComponent<cross::RenderPropertyComponentG>(traversedEntity);
        if (renderPropertyH.IsValid())
        {
            renderPropertySystem->SetHide(renderPropertyH.Write(), !visible);
        }
        entityMetaSystem->SetEntityVisibility(traversedEntity, visible);
    });
}
void GameObject::PostCreatingInitialize(GameWorld* world, const std::string& name, const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters)
{

    // create related entity
    cross::GameWorld* crossGameWorld = world->GetCrossGameWorld();
    assert(crossGameWorld);

    bool bNewEntityID = false;
    if (mObjectEntityID == mObjectEntityID.InvalidHandle())
    {
        mObjectEntityID = crossGameWorld->CreateEntityID();
        bNewEntityID = true;
    }

    if (mOwnedComponents.size() == 0)
    {
        AddComponent<GameObjectComponent>();
    }
    // init component
    PreInitializeComponents();

    InitializeComponents();

    // set root component
    if (mRootComponent == nullptr)
    {
        for (auto& comp : mOwnedComponents)
        {
            GameObjectComponent* rootComp = TYPE_CAST(GameObjectComponent*, comp.get());
            if (rootComp)
            {
                SetRootComponent(rootComp);
                break;
            }
        }
    }

    // set game object name
    SetName(name);
    auto transSys = crossGameWorld->GetGameSystem<cross::TransformSystemG>();
    if (bNewEntityID)
    {
        // set parent and joint parent entity
        if (Parameters.mParent)
        {
            mParent = Parameters.mParent;
            mIsJointed = transSys->Joint(mObjectEntityID, mParent->GetObjectEntityID());
        }
        else
        {
            // get root entity related root game object in CEFrameworkModule
            mParent = world->GetGameObject(transSys->GetRootEntity());
            if (!mParent)
            {
                LOG_WARN("post creating initialize can not find root game object {}", transSys->GetRootEntity().GetValue());
            }
            mIsJointed = transSys->Joint(mObjectEntityID, transSys->GetRootEntity());
        }

        auto metaComp = crossGameWorld->GetComponent<cross::ecs::EntityMetaComponentG>(mObjectEntityID);
        auto metaSys = crossGameWorld->GetGameSystem<cross::EntityMetaSystem>();
        if (metaSys && metaComp)
        {
            metaSys->SetName(metaComp.Write(), name);
        }

        SyncGOSerializer();

        // dispatch create entity event
        cross::ecs::ComponentBitMask newBitMask;
        for (auto& comp : mOwnedComponents)
        {
            comp->GetRelatedECSComponentBitMask(newBitMask);
        }
        if (!newBitMask.IsZero())
        {
            crossGameWorld->DispatchEntityCreateEvent(cross::EntityLifeCycleEventFlag::CreateComponent, mObjectEntityID, newBitMask);
        }
    }
    else
    {
        mIsJointed = true;
        // get root entity related root game object in CEFrameworkModule
        mParent = Parameters.mParent;
    }

    if (!mIsJointed)
    {
        LOG_WARN("post creating initialize joint entity failed {}", name);
    }


    // gameobject should have all of its components created and registered now;
    // do any collision checking and handling that we need to do
    // set game object transfrom and rotation
    if (Parameters.InitTransform)
    {
        SetLocalTranslation(localLocation);
        SetLocalRotation(localRotation);
        SetLocalScale(localScale);
    }

    PostInitializeComponents();
}

void GameObject::FinishCreating()
{
    //assert(mBlock != nullptr);

    BindScriptObject();

    RunConstructionScript();

    OnSpawn();

    if (GetWorld())
    {
        // editor view port
        if (cross::EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && GetWorld()->GetWorldType() == cross::WorldTypeTag::DefaultWorld)
        {
            RegisterTickFunction();

            RegisterEventListeners();

        }
    }
}

void GameObject::PreInitializeComponents()
{
    for (auto& comp : mOwnedComponents)
    {
        if (!comp->IsRegistered())
        {
            comp->Register();
        }
    }
}

void GameObject::InitializeComponents()
{
    for (auto& component : mOwnedComponents)
    {
        if (component->IsRegistered())
        {
            if (!component->IsActive())
            {
                component->Activate();
            }
            if (!component->HasBeenInit())
            {
                component->Init();
            }
        }
    }

}

void GameObject::PostInitializeComponents()
{
    //here, all components have been associated with ECS components
    mHasInitialized = true;
}

bool GameObject::HasComponent(GameObjectComponent* comp) const
{
    for (auto& ownedComp : mOwnedComponents)
    {
        if (comp == ownedComp.get())
        {
            return true;
        }
    }
    return false;
}

void GameObject::SetCanEverTick(bool enable)
{
    mTickFunction->bCanEverTick = enable;
}

bool GameObject::GetCanEverTick() const
{
    return mTickFunction->bCanEverTick;
}

void GameObject::SetTickGroup(TickingGroup tickGroup)
{
    mTickFunction->TickGroup = tickGroup;
    mScriptTickFunction->TickGroup = tickGroup;
}

TickingGroup GameObject::GetTickGroup() const
{
    return mTickFunction->TickGroup;
}


void GameObject::SetCanTickInEditorViewport(bool enable)
{
    // make sure all is same
    mTickFunction->bCanTickInEditorViewport = enable;

    mScriptTickFunction->bCanTickInEditorViewport = enable;

}

bool GameObject::GetCanTickInEditorViewport() const
{
    return mTickFunction->bCanTickInEditorViewport;
}

bool GameObject::CreateComponentByClassName(const std::string& compName)
{
    auto compPtr = GetComponentByMetaClassName(compName);
    if (compPtr != nullptr)
    {
        return false;
    }
    compPtr = AddComponent(compName);

    if (compPtr)
    {
        compPtr->RegisterTickFunction();
    }
    return compPtr != nullptr;
}

bool GameObject::RemoveComponentByClassName(const std::string& compName)
{
    auto compPtr = GetComponentByMetaClassName(compName);
    if (compPtr == nullptr)
    {
        return false;
    }

    RemoveComponent(compPtr);
    return true;
}
GameObjectComponent* GameObject::GetComponentByMetaClass(const gbf::reflection::MetaClass* metaClass) const
{
    if (metaClass == nullptr)
    {
        return nullptr;
    }

    for (auto& comp : mOwnedComponents)
    {
        if (comp->GetMetaClass()->IsTypeMatch(metaClass->id()))
        {
            return comp.get();
        }
    }
    return nullptr;
}

GameObjectComponent* GameObject::GetComponentByMetaClassName(const std::string& className) const
{
    auto metaClass = gbf::reflection::query_meta_class_by_name(className);
    return GetComponentByMetaClass(metaClass);
}

GameObjectComponent* GameObject::GetComponentByName(const std::string& compName) const
{
    for (auto& comp : mOwnedComponents)
    {
        if (comp->GetName() == compName)
        {
            return comp.get();
        }
    }
    return nullptr;
}
GameObjectComponent* GameObject::GetComponentByNameScript(const std::string& compName) const
{
    for (auto& comp : mOwnedComponents)
    {
        if (comp->GetMetaClass()->name() == compName)
        {
            return comp.get();
        }
    }
    return nullptr;
}
void GameObject::SetRootComponent(GameObjectComponent* comp)
{
    mRootComponent = comp;
}

GameObjectComponent* GameObject::AddComponent(const std::string& className)
{
    //see NewObject, The parameter list cannot be custom object 
    //GameObjectComponentPtr objectComp = TYPE_CAST_SHARD_PTR(GameObjectComponent, NewObject(className, this));
    GameObjectComponentPtr objectComp = TYPE_CAST_SHARD_PTR(GameObjectComponent, NewObject(className));
    
    //game object name
    const ObjectMetaClass* metaClass = QueryMetaClass(className);
    if (metaClass == nullptr)
    {
        LOG_ERROR("metaClass(className:{}) not found", className);
        return nullptr;
    }
    objectComp->SetName(MakeUniqueObjectName(metaClass));
    objectComp->SetOwner(this);
    objectComp->Register();

    if (mHasInitialized)
    {
        //after game object initialized, add component need to notify ecs
        cross::GameWorld* crossWorld = GetCrossGameWorld();
        cross::ecs::ComponentBitMask newBitMask;
        objectComp->GetRelatedECSComponentBitMask(newBitMask);
        if (!newBitMask.IsZero())
        {
            crossWorld->DispatchEntityMoveEvent(cross::EntityLifeCycleEventFlag::MoveComponent, mObjectEntityID);
        }
    }
    mOwnedComponents.emplace_back(objectComp);
    return objectComp.get();
}

void GameObject::RemoveComponent(GameObjectComponent* component)
{
    if (component == nullptr)
    {
        return;
    }
    
    if (IsPendingDestroy())
    {
        return;
    }

    auto FindComp = [&, comp = component]() {
        return std::find_if(mOwnedComponents.begin(), mOwnedComponents.end(), [&](const GameObjectComponentPtr& ownedComp) { return ownedComp.get() == comp; });
    };

    //check whether contains this component
    auto iter = FindComp();
    if (iter == mOwnedComponents.end())
    {
        return;
    }

    //uninit, notify ecs remove component
    component->Uninit(true);

    // check again
    iter = FindComp();
    if (iter == mOwnedComponents.end())
    {
        return;
    }

    if (component->HasRelatedECSComponentExcludeOwner())
    {
        //wait ecs destroy component event notify, call destroyed
        component->BeginDestroy();
    }
    else
    {
        component->BeginDestroy();
        component->Destroyed();
        mOwnedComponents.erase(iter);
    }
}

void GameObject::OnDestroyComponent(const cross::ecs::ComponentBitMask& changedMaskBit)
{
    for (auto iter = mOwnedComponents.begin(); iter != mOwnedComponents.end();)
    {
        const GameObjectComponentPtr& objComp = *iter;
        cross::ecs::ComponentBitMask compBitMask;
        objComp->GetRelatedECSComponentBitMaskExcludeOwner(compBitMask);
        auto testBits = compBitMask & changedMaskBit;
        if (!testBits.IsZero() && testBits == compBitMask)
        {
            if (objComp->IsPendingDestroy())
            {
                objComp->Destroyed();
            }
            else
            {
                objComp->Uninit(false);
                objComp->BeginDestroy();
                objComp->Destroyed();
            }
            iter = mOwnedComponents.erase(iter);
        }
        else
        {
            ++iter;
        }
    }
}

void GameObject::EndViewTarget(PlayerController* controller)
{
    
}

void GameObject::BecomeViewTarget(PlayerController* controller)
{
    
}

int GameObject::TestFunc(int param)
{
    LOG_INFO("GameObject::TestFunc {} name = {}", param, GetName());
    return param;
}

int GameObject::TestFunc2(int param, GameObject* other)
{
    LOG_INFO("GameObject::TestFunc2 {} name = {}", param, GetName());
    return param;
}

int GameObject::TestFuncGameObject(int param)
{
    LOG_INFO("GameObject::TestFuncGameObject {} name = {}", param, GetName());
    TestGameObjectPuertsImplableNoReturn(param);
    return TestGameObjectPuertsImplable(param);
}

void GameObject::CalcCamera(float deltaTime, ViewTargetInfo& outTargetInfo)
{
    //look for the first active camera component and use that for the view
    for (const GameObjectComponentPtr& comp : mOwnedComponents)
    {
        if (comp->CheckCast<CameraComponent>() && comp->IsActive())
        {
            CameraComponent* cameraComp = TYPE_CAST(CameraComponent*, comp.get());
            cameraComp->GetCameraView(deltaTime, outTargetInfo);
            return;
        }
    }
    //if game object has not CameraComponent, using game object transform
    outTargetInfo.SetViewRotation(GetWorldRotation());
    outTargetInfo.SetViewTranslation(GetWorldTranslation());
}

void GameObject::RunConstructionScript()
{
    if (auto comp = GetComponent<WorkFlowComponent>(); comp)
    {
        comp->ImmediateEvent({"ConstructionScript"});
    }
}

cross::GameWorld* GameObject::GetCrossGameWorld() const
{
    GameWorld* world = GetWorld();
    return world ? world->GetCrossGameWorld() : nullptr;
}

const gbf::logic::UBlueprintGraphGroup* GameObject::GetBlueprint() const
{
    if (GameObjectComponent* comp = GetComponent<WorkFlowComponent>(); comp)
    {
        return static_cast<WorkFlowComponent*>(comp)->GetBlueprint();
    }
    return nullptr;
}

void GameObject::PrintKey(cross::input::CEKey key)
{
}

Pawn* GameObject::GetInstigator() const
{
    return Instigator.get();
}

void GameObject::PostEditChangeProperty(PropertyChangedEvent& PropertyChangedEvent)
{
    RunConstructionScript();
}

void GameObject::SyncGOSerializer()
{
    auto crossGameWorld = GetCrossGameWorld();
    // create important GOSerializerComponentG!!!
    if (crossGameWorld && crossGameWorld->HasComponent<cross::GOSerializerComponentG>(mObjectEntityID))
    {
        crossGameWorld->GetComponent<cross::GOSerializerComponentG>(mObjectEntityID).Write()->mGameObject = this;
    }
    else
    {
        Assert(false);
    }
}

void GameObject::BindScriptObject()
{
    auto GetTypeScriptModuleName = [](cross::TsPtr tsPtr) {
        // mTsResource file is Contents/TypeScript/**/*.mts or Contents/JavaScript/**/*.mjs
        auto module_name = tsPtr->GetName();
        module_name = cross::StringHelper::Replace(module_name, "Contents/JavaScript/", "");
        module_name = cross::StringHelper::Replace(module_name, "Contents/TypeScript/", "");

        // Resource/EngineResource/Puerts/TypeScript to Resource/EngineResource/Puerts/JavaScript for build in typescript
        module_name = cross::StringHelper::Replace(module_name, "/TypeScript/", "/JavaScript/");

        return cross::StringHelper::Replace(module_name, ".mts", ".mjs");
    };

    if (mTsResource)
    {
        mTSObject = cross::TypeScriptModule::Instance()->BindScriptObject(GetTypeScriptModuleName(mTsResource), PUERTS_NAMESPACE::DynamicTypeId<GameObject>::get(this), this, mScriptEditorFieldsJson);

        UpdateLoadedScriptJsonProperty(cross::TypeScriptModule::Instance()->GetScriptEditorFieldsJson(mTSObject));
    }
    else
    {
        mTSObject = cross::TypeScriptObject{};
        mScriptEditorFieldsJson = "";
    }
}

void GameObject::UpdateLoadedScriptJsonProperty(std::string originalScriptEditorFieldsJson)
{
    mScriptEditorFieldsJson = originalScriptEditorFieldsJson;
}

cross::TRSMatrixType GameObject::GetWorldMatrix() const
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetWorldMatrixT(comp.Read());
}

cross::TRSFloat3ReturnType GameObject::GetTilePosition() const
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetWorldTranslationTile(comp.Read());
}

cross::TRSVector3Type GameObject::GetWorldTranslation() const
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetWorldTranslationT(comp.Read());
}

cross::TRSQuaternionType GameObject::GetWorldRotation() const
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetWorldRotationT(comp.Read());
}

cross::TRSVector3Type GameObject::GetWorldScale() const
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetWorldScaleT(comp.Read());
}

void GameObject::SetWorldTranslation(const cross::TRSVector3Type& translation)
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetWorldTranslationT(comp.Write(), translation);
}

void GameObject::SetWorldRotation(const cross::TRSQuaternionType& rotation)
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetWorldRotationT(comp.Write(), rotation);
}

void GameObject::SetWorldScale(const cross::TRSVector3Type& scale)
{
    auto comp = GetECSComponent<cross::WorldTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetWorldScaleT(comp.Write(), scale);
}

cross::TRSVector3Type GameObject::GetLocalTranslation() const
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetLocalTranslationT(comp.Read());
}

cross::TRSQuaternionType GameObject::GetLocalRotation() const
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetLocalRotationT(comp.Read());
}

cross::TRSVector3Type GameObject::GetLocalScale() const
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->GetLocalScaleT(comp.Read());
}

void GameObject::SetLocalTranslation(const cross::TRSVector3Type& translation)
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetLocalTranslationT(comp.Write(), translation);
}

void GameObject::SetLocalRotation(const cross::TRSQuaternionType& rotation)
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetLocalRotationT(comp.Write(), rotation);
}

void GameObject::SetLocalScale(const cross::TRSVector3Type& scale)
{
    auto comp = GetECSComponent<cross::LocalTransformComponentG>();
    auto transSys = GetSystem<cross::TransformSystemG>();
    return transSys->SetLocalScaleT(comp.Write(), scale);
}

}   // namespace cegf


