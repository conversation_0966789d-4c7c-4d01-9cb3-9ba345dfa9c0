#pragma once
#include "GameObjects/GameObject.h"
#include "GameFramework/Components/ModelComponent.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, <PERSON><PERSON>ts, Cli) StaticModel : public GameObject{
public:
    CEGameplayInternal()
    StaticMetaClassName(StaticModel)
    StaticModel();

    virtual ~StaticModel();
    virtual void Serialize(SerializeNode & node, SerializeContext & context) const override;
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;
    virtual void InitializeComponents() override;
    virtual void PostInitializeComponents() override;
    virtual void Tick(float deltaTime) override;

private:
    ModelComponent* mModelComponent;
};

}
