#pragma once
#include "GameObjects/GameObject.h"
#include "GameFramework/Components/UIComponent.h"

namespace cegf
{
class GAMEFRAMEWORK_API CEMeta(Reflect, <PERSON><PERSON>ts, Cli) UICanvas : public GameObject{
public:
    CEGameplayInternal() 
    StaticMetaClassName(UICanvas)
    UICanvas();

    virtual ~UICanvas();
    virtual void Serialize(SerializeNode & node, SerializeContext & context) const override;
    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context) override;
    virtual void InitializeComponents() override;
    virtual void PostInitializeComponents() override;
    virtual void Tick(float deltaTime) override;

private:
    UIComponent* CanvasComponent;
};

}
