#pragma once
#include "Runtime/GameWorld/GameWorld.h"
#include "CECommon/Common/SystemEvent.h"
#include "GameFramework/Audio/AudioManager.h"
#include "GameFrameworkGlobals.h"
#include "GameFrameworkTypes.h"
#include "core/modules/time_manager.h"
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Ticking/TickManager.h"
#include "GameFramework/Event/Listeners.h"
#include "GameFramework/Event/Dispatchers.h"
#include "TimerManager/TimerManager.h"

namespace cegf {

class GameWorld;
using GameWorldPtr = std::shared_ptr<GameWorld>;
using GameWorldWeakPtr = std::weak_ptr<GameWorld>;
using GameWorldMap = std::unordered_map<UInt32, GameWorldPtr>;
using GameWorldList = std::vector<GameWorldPtr>;

class GameWorldBlock;
using GameWorldBlockPtr = std::shared_ptr<GameWorldBlock>;
using GameWorldBlockMap = std::unordered_map<UInt64, GameWorldBlockPtr>;

class GameWorldEventProcessor;
using GameWorldEventProcessorPtr = std::shared_ptr<GameWorldEventProcessor>;

using GameObjectMap = std::unordered_map<cross::ecs::EntityID, GameObjectPtr>;
using GameObjectSet = std::unordered_set<GameObjectPtr>;


class Controller;
class PlayerController;
using ControllerList = std::vector<Controller*>;
using PlayerControllerList = std::vector<PlayerController*>;

using GameObjectCreatedFunc = std::function<void(GameObject*, GameWorld *)>;

struct GAMEFRAMEWORK_API CreateGameObjectParameters
{
    // a name to assign as the name of the game object being created.
    // if no value is specified, the name of created game object will be automatically generated using the form [Class]_[Number].
    std::string mGameObjectName;

    // created game object will joint the parent object. if no value is specified, will joint the root game object
    GameObject* mParent = nullptr;

    cross::ecs::EntityID mLocalEntity = cross::ecs::EntityID::InvalidHandle();

    GameObjectCreatedFunc OnGameObjectCreated = [](GameObject* obj, GameWorld* gameWorld) {
        if (obj)
        {
            //obj->FinishCreating();
        }
    };

    // Sometimes we can not init transform when create game object
    bool InitTransform = true;
};

class GAMEFRAMEWORK_API CEMeta(Puerts) GameWorld : public ObjectBase
{
    friend class GameWorldEventProcessor;

public:
    GameWorld();

    virtual ~GameWorld();

    void Init(cross::GameWorld * crossWorld);

    virtual void BeginDestroy() override;

    virtual void Destroyed() override;

    virtual void OnBeginFrame(float deltaTime);

    void PreTick(float deltaTime);
    void Tick(float deltaTime);
    void PostTick(float deltaTime);

    void StartGame();

    void EndGame();

    bool HasStarted() const
    {
        return mHasStarted;
    }

    bool IsLoaded() const
    {
        return mCrossGameWorld->IsLoaded();
    }

    bool Save(const char* savePath) const;

    UInt32 GetRuntimeID() const
    {
        return mWorldRuntimeID;
    }

    const std::string& GetWorldPath()
    {
        return mCrossGameWorld->GetWorldPath();
    }

    const cross::HashString& GetName()
    {
        return mCrossGameWorld->GetName();
    }

    cross::WorldTypeTag GetWorldType() const
    {
        return mCrossGameWorld->GetWorldType();
    }

    cross::GameWorld* GetCrossGameWorld() const
    {
        return mCrossGameWorld;
    }

    GameWorldBlock* GetWorldBlock(UInt64 blockId) const;

    GameObject* GetGameObject(const cross::ecs::EntityID& entityId) const;

    CEFunction(ScriptCallable)
    GameObject* GetGameObjectByUUID(const std::string& entity_uuid) const;

    CEFunction(ScriptCallable)
    GameObject* FindGameObject(const std::string& name) const;

    auto GetRuntimeCreatedGameObjectCount() const
    {
        return mRuntimeCreatingObjects.size();
    }

    GameObject* CreateRootGameObject() const;

    CEFunction(ScriptCallable)
    GameObject* GetRootGameObject() const;
    CEFunction(ScriptCallable)
    GameObject* CreateGameObject(const std::string& name, GameObject* parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale);
    CEFunction(ScriptCallable)
    GameObject* CreateGameObjectByMetaClassName(const std::string& name, const std::string& metaClassName, GameObject* parent, const cross::TRSVector3Type& localLocation, const cross::TRSQuaternionType& localRotation, const cross::TRSVector3Type& localScale);

    template<typename T>
    std::shared_ptr<T> CreateGameObject(const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters = CreateGameObjectParameters());

    GameObjectPtr CreateGameObject(const std::string& className, const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters = CreateGameObjectParameters());

    GameObjectPtr CreateGameObject(
        const std::string& className, const cross::TRSVector3AType* localLocation = nullptr, const cross::TRSQuaternionAType* localRotation = nullptr, const cross::TRSVector3AType* localScale = nullptr, const CreateGameObjectParameters& Parameters = CreateGameObjectParameters());

    GameObjectPtr CreateGameObject(const ObjectMetaClass* metaClass, const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters = CreateGameObjectParameters());

    CEFunction(ScriptCallable)
    bool DestroyGameObject(GameObject * gameObj);

    template<class Func>
    void TraverseGameObjects(Func func)
    {
        std::shared_lock lock(mCreatingObjectsMutex);
        for (auto [entity, obj] : mRuntimeCreatingObjects)
        {
            func(obj);
        }
    }

    void AddController(Controller * controller);

    void RemoveController(Controller * controller);

    UInt32 GetNumControllers() const
    {
        return static_cast<UInt32>(mControllerList.size());
    }

    UInt32 GetNumPlayerControllers() const
    {
        return static_cast<UInt32>(mPlayerControllerList.size());
    }

    TickTaskLevel* GetTickTaskLevel()
    {
        return mTickTaskLevel;
    }

    void SetLevelTick(LevelTick in_level_tick)
    {
        mLevelTick = in_level_tick;
    }

    bool HasEndOfFrameDraw() const;

    void EndOfFrameDraw() const;

    void MarkComponentForNeededEndOfFrameDraw(GameObjectComponent * component);

    void MarkComponentForNoNeedEndOfFrameDraw(GameObjectComponent * component);

    CEFunction(ScriptCallable)
    GameObject* GetMainCamera() const;
    
    CEFunction(ScriptCallable)
    AudioManager const * GetAudioManager() const;

    void PendingAddComponentByMask(GameObject* Object, const cross::ecs::ComponentBitMask& mask);

    // Net

    /** Returns true if we are currently recording a replay */
    bool IsRecordingReplay() const
    {
        return false;
    }

    /*
     * Events
     */
public:
    bool AddEventDispatcher(evt::GOEventDispatcherPtr inDispatcher);

    void RegisterEventListener(evt::Listener* inListener);
    void UnregisterEventListener(evt::Listener * inListener);

private:

    // create and add engine event dispatchers
    void CreateEngineEventDispatchers();

    // init dispatchers, include engine dispatchers and other project event dispatchers
    void InitEventDispatchers();

    // uninit dispatchers, include engine dispatchers and other project event dispatchers
    void UnInitEventDispatchers();

    evt::GOEventDispatcherList mEventDispatchers;

public:
    void OnLevelBlockLoaded(UInt64 blockId);

    void OnLevelBlockUnloaded(UInt64 blockId);

    void OnWorldConfigLoaded();

    void OnWorldLoaded();

    void OnCreateGameObject(const cross::ecs::EntityID& entityID, const cross::ecs::ComponentBitMask& compBitMask);

    void OnChangeBelongedBlock(const std::vector<cross::ecs::EntityID>& entities, UInt64 sourceBlockId, UInt64 targetBlockId);

    void OnDestroyGameObject(const cross::ecs::EntityID& entityID, UInt64 blockId);

    void OnDestroyComponent(const cross::ecs::EntityID& entityID, const cross::ecs::ComponentBitMask& changedComponentMask);

public:
    float GetDeltaSeconds() const
    {
        return mDeltaTimeSeconds;
    }

    double GetTimeSeconds() const
    {
        return mTimeSeconds;
    }

    double TimeSince(double Time) const
    {
        return GetTimeSeconds() - Time;
    }

    FTimerManager& GetTimerManager()
    {
        return mTimerManager;
    }

protected:
    GameWorldBlockPtr CreateWorldBlock(UInt64 blockId, GameWorldBlockType blockType);

private:
    void RuntimeCreatedGOStartGame();
    void RuntimeCreatedGOEndGame();

protected:
    GameWorldEventProcessorPtr mEventProcessor;

    // cross game world
    cross::GameWorld* mCrossGameWorld = nullptr;

    // saved the global entities and information
    GameWorldBlockPtr mPreWorldBlock;

    // include all level blocks, custom blocks and PreWorldBlock
    GameWorldBlockMap mWorldBlocks;

    mutable std::shared_mutex mCreatingObjectsMutex;
    GameObjectMap mRuntimeCreatingObjects;

    mutable std::mutex mAwakeObjectMutex;
    GameObjectSet mNewlyCreateObjects;

    using PendingComponentsMapType = std::unordered_map<GameObject*, cross::ecs::ComponentBitMask>;
    mutable std::mutex mPendingComponentsMutex;
    PendingComponentsMapType mPendingComponentsMap;

    // List of all the controllers in the world
    ControllerList mControllerList;

    // List of all the player controllers in the world
    PlayerControllerList mPlayerControllerList;

    // Tick task level
    TickTaskLevel* mTickTaskLevel{nullptr};

    LevelTick mLevelTick{LEVELTICK_All};

    UInt32 mWorldRuntimeID = 0;

    bool mHasStarted = false;

    std::unordered_set<GameObjectComponent*> mComponentsNeedEndOfFrameDraw;

    std::unordered_set<GameObjectComponent*> mComponentsNeedEndOfFrameDrawOnlyOnEditor;

    // Audio Manager
    AudioManager mAudioManager;

    // Time
    FTimerManager mTimerManager;
    /** Frame delta time in seconds adjusted by e.g. time dilation. */
    float mDeltaTimeSeconds = 0.0f;
    /**  Time in seconds since level began play, but IS paused when the game is paused, and IS dilated/clamped. */
    double mTimeSeconds = 0.0f;

    friend class GameWorldBlock;
};

template<typename T>
std::shared_ptr<T> GameWorld::CreateGameObject(const cross::TRSVector3AType& localLocation, const cross::TRSQuaternionAType& localRotation, const cross::TRSVector3AType& localScale, const CreateGameObjectParameters& Parameters)
{
    if (IsPendingDestroy())
    {
        LOG_ERROR("create game object failed, game world is pending destroy");
        return nullptr;
    }

    // check parent is valid
    if (Parameters.mParent)
    {
        const cross::ecs::EntityID& parentEntityID = Parameters.mParent->GetObjectEntityID();
        if (!parentEntityID)
        {
            LOG_ERROR("create game object failed, invalid parent entity id {}", parentEntityID.GetValue());
            return nullptr;
        }

        GameWorld* parentWorld = Parameters.mParent->GetWorld();
        if (parentWorld != this)
        {
            LOG_ERROR("create game object failed, parent world {} is not current world {}", parentWorld->GetRuntimeID(), mWorldRuntimeID);
            return nullptr;
        }
    }

    // game object name
    const ObjectMetaClass* metaClass = QueryMetaClass<T>();
    std::string newObjectName = Parameters.mGameObjectName;
    if (newObjectName.empty())
    {
        newObjectName = MakeUniqueObjectName(metaClass);
    }

    // new object
    auto gameObj = TYPE_CAST_SHARD_PTR(T, NewObject<T>());
    if (!gameObj)
    {
        LOG_ERROR("create game object failed, new object failed {}", metaClass->name());
        return nullptr;
    }

    gameObj->PostCreatingInitialize(this, newObjectName, localLocation, localRotation, localScale, Parameters);

    if (gameObj->IsJointed())
    {
        std::unique_lock lock(mCreatingObjectsMutex);
        mRuntimeCreatingObjects.emplace(gameObj->GetObjectEntityID(), gameObj);
    }

    {
        std::unique_lock lock(mAwakeObjectMutex);
        mNewlyCreateObjects.emplace(gameObj);
    }

    return TYPE_CAST_SHARD_PTR(T, gameObj);
}
}   // namespace cegf
