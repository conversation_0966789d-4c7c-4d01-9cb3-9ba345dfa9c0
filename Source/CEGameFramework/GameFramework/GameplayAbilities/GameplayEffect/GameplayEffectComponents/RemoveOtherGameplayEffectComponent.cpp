// Copyright Epic Games, Inc. All Rights Reserved.

#include "RemoveOtherGameplayEffectComponent.h"
#include "GameplayAbilities/AbilitySystemComponent.h"
namespace cegf {

void URemoveOtherGameplayEffectComponent::OnGameplayEffectApplied(FActiveGameplayEffectsContainer& ActiveGEContainer, FGameplayEffectSpec& GESpec, FPredictionKey& PredictionKey) const
{
    if (!ActiveGEContainer.OwnerIsNetAuthority)
    {
        return;
    }

    FGameplayEffectQuery FindOwnerQuery;
    FindOwnerQuery.EffectDefinition = GetOwner() ? GetOwner()->GetMetaClass() : nullptr;

    // We need to keep track to ensure we never remove ourselves
    std::vector<FActiveGameplayEffectHandle> ActiveGEHandles = ActiveGEContainer.GetActiveEffects(FindOwnerQuery);

    constexpr int32 RemoveAllStacks = -1;
    for (const FGameplayEffectQuery& RemoveQuery : RemoveGameplayEffectQueries)
    {
        if (!RemoveQuery.IsEmpty())
        {
            // If we have an ActiveGEHandle, make sure we never remove ourselves.
            // If we don't, there's no need to make a copy.
            if (ActiveGEHandles.empty())
            {
                // Faster path: No copy needed
                ActiveGEContainer.RemoveActiveEffects(RemoveQuery, RemoveAllStacks);
            }
            else
            {
                FGameplayEffectQuery MutableRemoveQuery = RemoveQuery;
                MutableRemoveQuery.IgnoreHandles = std::move(ActiveGEHandles);

                ActiveGEContainer.RemoveActiveEffects(MutableRemoveQuery, RemoveAllStacks);
            }
        }
    }
}

#if WITH_EDITOR
EDataValidationResult URemoveOtherGameplayEffectComponent::IsDataValid(FDataValidationContext& Context) const
{
    EDataValidationResult Result = Super::IsDataValid(Context);

    if (GetOwner()->DurationPolicy != EGameplayEffectDurationType::Instant)
    {
        if (GetOwner()->Period.Value > 0.0f)
        {
            Context.AddError(FText::FormatOrdered(LOCGAS_TEXT("PeriodicEffectError", "GE is Periodic. Remove {0} and use TagRequirements (Ongoing) instead."), FText::FromString(GetClass()->GetName())));
            Result = EDataValidationResult::Invalid;
        }
    }

    return Result;
}
#endif   // WITH_EDITOR


}   // namespace cegf