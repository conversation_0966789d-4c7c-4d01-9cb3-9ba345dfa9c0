#pragma once
#include "GameFrameworkTypes.h"
#include "GameplayTaskTypes.h"
#include "Log.h"
namespace cegf {
    class GameObject;
    class UGameplayTask;
    class UGameplayTasksComponent;

    class IGameplayTaskOwnerInterface
    {
    public:
        virtual ~IGameplayTaskOwnerInterface() = default;
        /** Finds tasks component for given GameplayTask, Task.GetGameplayTasksComponent() may not be initialized at this point! */
        GAMEFRAMEWORK_API virtual UGameplayTasksComponent* GetGameplayTasksComponent(const UGameplayTask& Task) const = 0;

        /** Get owner of a task or default one when task is null */
        GAMEFRAMEWORK_API virtual GameObject* GetGameplayTaskOwner(const UGameplayTask* Task) const = 0;

        /** Get "body" of task's owner / default, having location in world (e.g. Owner = AIController, Avatar = Pawn) */
        virtual GameObject* GetGameplayTaskAvatar(const UGameplayTask* Task) const { return GetGameplayTaskOwner(Task); }

        /** Get default priority for running a task */
        virtual uint8_t GetGameplayTaskDefaultPriority() const { return FGameplayTasks::DefaultPriority; }

        /** Notify called after GameplayTask finishes initialization (not active yet) */
        virtual void OnGameplayTaskInitialized(UGameplayTask& Task) {}

        /** Notify called after GameplayTask changes state to Active (initial activation or resuming) */
        virtual void OnGameplayTaskActivated(UGameplayTask& Task) {}

        /** Notify called after GameplayTask changes state from Active (finishing or pausing) */
        virtual void OnGameplayTaskDeactivated(UGameplayTask& Task) {}
    };
}
