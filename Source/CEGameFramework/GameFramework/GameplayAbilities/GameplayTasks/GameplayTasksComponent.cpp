#include "GameplayTasksComponent.h"


namespace cegf {

    UGameplayTasksComponent::FEventLock::FEventLock(UGameplayTasksComponent* InOwner) : Owner(InOwner)
    {
        if (Owner)
        {
            Owner->EventLockCounter++;
        }
    }

    UGameplayTasksComponent::FEventLock::~FEventLock()
    {
        if (Owner)
        {
            Owner->EventLockCounter--;

            if (Owner->TaskEvents.size() && Owner->CanProcessEvents())
            {
                Owner->ProcessTaskEvents();
            }
        }
    }

    UGameplayTasksComponent::UGameplayTasksComponent()
    {
        //PrimaryComponentTick.TickGroup = TG_DuringPhysics;
        //PrimaryComponentTick.bStartWithTickEnabled = false;
        //PrimaryComponentTick.bCanEverTick = true;

        //SetIsReplicatedByDefault(true);
        bInEventProcessingInProgress = false;
        TopActivePriority = 0;
    }

    void UGameplayTasksComponent::AddTaskReadyForActivation(UGameplayTask& NewTask)
    {
        //UE_VLOG(this, LogGameplayTasks, Log, GAS_TEXT("AddTaskReadyForActivation %s"), *NewTask.GetName());

        Assert(NewTask.RequiresPriorityOrResourceManagement() == true);

        TaskEvents.push_back(FGameplayTaskEventData(EGameplayTaskEvent::Add, NewTask));
        // trigger the actual processing only if it was the first event added to the list
        if (TaskEvents.size() == 1 && CanProcessEvents())
        {
            ProcessTaskEvents();
        }
    }

    void UGameplayTasksComponent::ProcessTaskEvents()
    {
        static const int32_t MaxIterations = 16;
        bInEventProcessingInProgress = true;

        int32_t IterCounter = 0;
        while (TaskEvents.size() > 0)
        {
            IterCounter++;
            if (IterCounter > MaxIterations)
            {
                //UE_VLOG(this, LogGameplayTasks, Error, GAS_TEXT("UGameplayTasksComponent::ProcessTaskEvents has exceeded allowes number of iterations. Check your GameplayTasks for logic loops!"));
                TaskEvents.clear();
                break;
            }

            for (int32_t EventIndex = 0; EventIndex < TaskEvents.size(); ++EventIndex)
            {
                //UE_VLOG(this, LogGameplayTasks, Verbose, GAS_TEXT("UGameplayTasksComponent::ProcessTaskEvents: %s event %s")
                    //, *TaskEvents[EventIndex].RelatedTask.GetName(), GetGameplayTaskEventName(TaskEvents[EventIndex].Event));

                if (!IsValid(TaskEvents[EventIndex].RelatedTask))
                {
                    //UE_VLOG(this, LogGameplayTasks, Verbose, GAS_TEXT("%s is invalid"), *TaskEvents[EventIndex].RelatedTask.GetName());
                    // we should ignore it, but just in case run the removal code.
                    RemoveTaskFromPriorityQueue(*TaskEvents[EventIndex].RelatedTask);
                    continue;
                }

                switch (TaskEvents[EventIndex].Event)
                {
                case EGameplayTaskEvent::Add:
                    if (TaskEvents[EventIndex].RelatedTask->TaskState != EGameplayTaskState::Finished)
                    {
                        AddTaskToPriorityQueue(*TaskEvents[EventIndex].RelatedTask);
                    }
                    else
                    {
                        //UE_VLOG(this, LogGameplayTasks, Error, GAS_TEXT("UGameplayTasksComponent::ProcessTaskEvents trying to add a finished task to priority queue!"));
                    }
                    break;
                case EGameplayTaskEvent::Remove:
                    RemoveTaskFromPriorityQueue(*TaskEvents[EventIndex].RelatedTask);
                    break;
                default:
                    AssertMsg(false, "Enclosing block should never be called");
                    break;
                }
            }

            TaskEvents.clear();
            UpdateTaskActivations();

            // task activation changes may create new events, loop over to check it
        }

        bInEventProcessingInProgress = false;
    }

    bool UGameplayTasksComponent::GetShouldTick() const
    {
        return TickingTasks.size() > 0;
    }

    void UGameplayTasksComponent::OnGameplayTaskActivated(UGameplayTask& Task)
    {
        // process events after finishing all operations
        FEventLock ScopeEventLock(this);
        std::shared_ptr<UGameplayTask> TaskPtr = std::dynamic_pointer_cast<UGameplayTask>(Task.shared_from_this());
        KnownTasks.push_back(TaskPtr);

        if (Task.IsTickingTask())
        {
            Assert(std::find(TickingTasks.begin(), TickingTasks.end(), TaskPtr) == TickingTasks.end());
            TickingTasks.push_back(TaskPtr);

            // If this is our first ticking task, set this component as active so it begins ticking
            if (TickingTasks.size() == 1)
            {
                UpdateShouldTick();
            }
        }

        if (Task.IsSimulatedTask())
        {
            const bool bWasAdded = AddSimulatedTask(&Task);
            Assert(bWasAdded == true);
        }

        IGameplayTaskOwnerInterface* TaskOwner = Task.GetTaskOwner();
        if (!Task.IsOwnedByTasksComponent() && TaskOwner)
        {
            TaskOwner->OnGameplayTaskActivated(Task);
        }
    }

    void UGameplayTasksComponent::OnGameplayTaskDeactivated(UGameplayTask& Task)
    {
        // process events after finishing all operations
        FEventLock ScopeEventLock(this);
        const bool bIsFinished = (Task.GetState() == EGameplayTaskState::Finished);

        if (Task.GetChildTask() && bIsFinished)
        {
            if (Task.HasOwnerFinished())
            {
                Task.GetChildTask()->TaskOwnerEnded();
            }
            else
            {
                Task.GetChildTask()->EndTask();
            }
        }

        if (Task.IsTickingTask())
        {
            // If we are removing our last ticking task, set this component as inactive so it stops ticking
            // TickingTasks.RemoveSingleSwap(&Task);
            auto it = std::find(TickingTasks.begin(), TickingTasks.end(), std::dynamic_pointer_cast<UGameplayTask>(Task.shared_from_this()));
            TickingTasks.erase(it);
        }

        if (bIsFinished)
        {
            // using RemoveSwap rather than RemoveSingleSwap since a Task can be added
            // to KnownTasks both when activating as well as unpausing
            // while removal happens only once. It's cheaper to handle it here.
            //KnownTasks.RemoveSwap(&Task);
            KnownTasks.erase(
                std::remove(KnownTasks.begin(), KnownTasks.end(), std::dynamic_pointer_cast<UGameplayTask>(Task.shared_from_this())),
                KnownTasks.end());
        }

        if (Task.IsSimulatedTask())
        {
            RemoveSimulatedTask(&Task);
        }

        // Resource-using task
        if (Task.RequiresPriorityOrResourceManagement() && bIsFinished)
        {
            OnTaskEnded(Task);
        }

        IGameplayTaskOwnerInterface* TaskOwner = Task.GetTaskOwner();
        if (!Task.IsOwnedByTasksComponent() && !Task.HasOwnerFinished() && TaskOwner)
        {
            TaskOwner->OnGameplayTaskDeactivated(Task);
        }

        UpdateShouldTick();
    }

    void UGameplayTasksComponent::ReadyForReplication()
    {
        // TODO ActorComponent overrides
        //Super::ReadyForReplication();

        //REDIRECT_TO_VLOG(GetOwner());

        //if (IsUsingRegisteredSubObjectList())
        //{
        //    for (UGameplayTask* SimulatedTask : SimulatedTasks)
        //    {
        //        if (SimulatedTask)
        //        {
        //            AddReplicatedSubObject(SimulatedTask, COND_SkipOwner);
        //        }
        //    }
        //}
    }

    void UGameplayTasksComponent::UpdateTaskActivations()
    {
        FGameplayResourceSet ResourcesClaimed;
        bool bHasNulls = false;

        if (TaskPriorityQueue.size() > 0)
        {
            std::vector<UGameplayTask*> ActivationList;
            ActivationList.reserve(TaskPriorityQueue.size());

            FGameplayResourceSet ResourcesBlocked;
            for (int32_t TaskIndex = 0; TaskIndex < TaskPriorityQueue.size(); ++TaskIndex)
            {
                if (TaskPriorityQueue[TaskIndex])
                {
                    const FGameplayResourceSet RequiredResources = TaskPriorityQueue[TaskIndex]->GetRequiredResources();
                    const FGameplayResourceSet ClaimedResources = TaskPriorityQueue[TaskIndex]->GetClaimedResources();
                    if (RequiredResources.GetOverlap(ResourcesBlocked).IsEmpty())
                    {
                        // postpone activations, it's some tasks (like MoveTo) require pausing old ones first
                        ActivationList.push_back(TaskPriorityQueue[TaskIndex].get());
                        ResourcesClaimed.AddSet(ClaimedResources);
                    }
                    else
                    {
                        TaskPriorityQueue[TaskIndex]->PauseInTaskQueue();
                    }

                    ResourcesBlocked.AddSet(ClaimedResources);
                }
                else
                {
                    bHasNulls = true;

                    //UE_VLOG(this, LogGameplayTasks, Warning, GAS_TEXT("UpdateTaskActivations found null entry in task queue at index:%d!"), TaskIndex);
                }
            }

            for (int32_t Idx = 0; Idx < ActivationList.size(); Idx++)
            {
                // check if task wasn't already finished as a result of activating previous elements of this list
                if (IsValid(ActivationList[Idx])
                    && ActivationList[Idx]->IsFinished() == false)
                {
                    ActivationList[Idx]->ActivateInTaskQueue();
                }
            }
        }

        SetCurrentlyClaimedResources(ResourcesClaimed);

        // remove all null entries after processing activation changes
        if (bHasNulls)
        {
            TaskPriorityQueue.erase(
                std::remove_if(TaskPriorityQueue.begin(), TaskPriorityQueue.end(),
                    [](std::shared_ptr<UGameplayTask> task) {return task == nullptr; }),
                TaskPriorityQueue.end());
            //TaskPriorityQueue.RemoveAll([](UGameplayTask* Task) { return Task == nullptr; });
        }
    }

    void UGameplayTasksComponent::AddTaskToPriorityQueue(UGameplayTask& NewTask)
    {
        if ((NewTask.GetResourceOverlapPolicy() == ETaskResourceOverlapPolicy::RequestCancelAndStartOnTop)
            || (NewTask.GetResourceOverlapPolicy() == ETaskResourceOverlapPolicy::RequestCancelAndStartAtEnd))
        {
            const FGameplayResourceSet NewClaimedResources = NewTask.GetClaimedResources();
            std::vector<UGameplayTask*> CancelList;

            for (std::shared_ptr<UGameplayTask> Task : TaskPriorityQueue)
            {
                if (Task != nullptr
                    && Task->GetPriority() <= NewTask.GetPriority()
                    && Task->GetClaimedResources().HasAnyID(NewClaimedResources))
                {
                    // Postpone cancelling, as cancel can call EndTask() and may alter the TaskPriorityQueue.  
                    CancelList.push_back(Task.get());
                }
            }

            for (UGameplayTask* Task : CancelList)
            {
                Task->ExternalCancel();
            }
        }

        const bool bStartOnTopOfSamePriority = (NewTask.GetResourceOverlapPolicy() == ETaskResourceOverlapPolicy::StartOnTop)
            || (NewTask.GetResourceOverlapPolicy() == ETaskResourceOverlapPolicy::RequestCancelAndStartOnTop);
        int32_t InsertionPoint = INDEX_NONE;

        for (int32_t Idx = 0; Idx < TaskPriorityQueue.size(); ++Idx)
        {
            if (TaskPriorityQueue[Idx] == nullptr)
            {
                continue;
            }

            if ((bStartOnTopOfSamePriority && TaskPriorityQueue[Idx]->GetPriority() <= NewTask.GetPriority())
                || (!bStartOnTopOfSamePriority && TaskPriorityQueue[Idx]->GetPriority() < NewTask.GetPriority()))
            {
                TaskPriorityQueue.insert(TaskPriorityQueue.begin() + Idx, std::dynamic_pointer_cast<UGameplayTask>(NewTask.shared_from_this()));
                //TaskPriorityQueue.Insert(&NewTask, Idx);
                InsertionPoint = Idx;
                break;
            }
        }

        if (InsertionPoint == INDEX_NONE)
        {
            TaskPriorityQueue.push_back(std::dynamic_pointer_cast<UGameplayTask>(NewTask.shared_from_this()));
        }

    }

    void UGameplayTasksComponent::RemoveTaskFromPriorityQueue(UGameplayTask& Task)
    {
        TaskPriorityQueue.erase(
            std::find(
                TaskPriorityQueue.begin(),
                TaskPriorityQueue.end(),
                std::dynamic_pointer_cast<UGameplayTask>(Task.shared_from_this())
            )
        );
        //const int32_t RemovedTaskIndex = TaskPriorityQueue.Find(&Task);
        //if (RemovedTaskIndex != INDEX_NONE)
        //{
        //    TaskPriorityQueue.RemoveAt(RemovedTaskIndex, EAllowShrinking::No);
        //}
        //else
        //{
        //    // take a note and ignore
        //    UE_VLOG(this, LogGameplayTasks, Verbose, GAS_TEXT("RemoveTaskFromPriorityQueue for %s called, but it's not in the queue. Might have been already removed"), *Task.GetName());
        //}
    }

    bool UGameplayTasksComponent::AddSimulatedTask(UGameplayTask* NewTask)
    {
        if (NewTask == nullptr)
        {
            return false;
        }

        std::shared_ptr<UGameplayTask> NewTaskPtr = std::dynamic_pointer_cast<UGameplayTask>(NewTask->shared_from_this());

        if (std::ranges::find(SimulatedTasks, NewTaskPtr) == SimulatedTasks.end())
        {
            SimulatedTasks.push_back(NewTaskPtr);
            SetSimulatedTasksNetDirty();

            //if (IsUsingRegisteredSubObjectList() && IsReadyForReplication())
            //{
            //    AddReplicatedSubObject(NewTask, COND_SkipOwner);
            //}

            return true;
        }

        return false;
    }

    void UGameplayTasksComponent::RemoveSimulatedTask(UGameplayTask* NewTask)
    {
        std::shared_ptr<UGameplayTask> NewTaskPtr = std::dynamic_pointer_cast<UGameplayTask>(NewTask->shared_from_this());

        auto it = std::ranges::find(SimulatedTasks, NewTaskPtr);
        if (it != SimulatedTasks.end())
        {
            SimulatedTasks.erase(it);
            SetSimulatedTasksNetDirty();

            //if (IsUsingRegisteredSubObjectList())
            //{
            //    RemoveReplicatedSubObject(NewTask);
            //}
        }
    }

    void UGameplayTasksComponent::UpdateShouldTick()
    {
        const bool bShouldTick = GetShouldTick();
        if (IsActive() != bShouldTick)
        {
            // TODO
            //SetActive(bShouldTick);
        }
    }

    void UGameplayTasksComponent::SetCurrentlyClaimedResources(FGameplayResourceSet NewClaimedSet)
    {
        if (CurrentlyClaimedResources != NewClaimedSet)
        {
            FGameplayResourceSet ReleasedResources = FGameplayResourceSet(CurrentlyClaimedResources).RemoveSet(NewClaimedSet);
            FGameplayResourceSet ClaimedResources = FGameplayResourceSet(NewClaimedSet).RemoveSet(CurrentlyClaimedResources);
            CurrentlyClaimedResources = NewClaimedSet;
            OnClaimedResourcesChange.Broadcast(ClaimedResources, ReleasedResources);
        }
    }

    void UGameplayTasksComponent::OnTaskEnded(UGameplayTask& Task)
    {
        Assert(Task.RequiresPriorityOrResourceManagement() == true);
        RemoveResourceConsumingTask(Task);
    }


    void UGameplayTasksComponent::RemoveResourceConsumingTask(UGameplayTask& Task)
    {
        //UE_VLOG(this, LogGameplayTasks, Log, GAS_TEXT("RemoveResourceConsumingTask %s"), *Task.GetName());

        TaskEvents.push_back(FGameplayTaskEventData(EGameplayTaskEvent::Remove, Task));
        // trigger the actual processing only if it was the first event added to the list
        if (TaskEvents.size() == 1 && CanProcessEvents())
        {
            ProcessTaskEvents();
        }
    }

    void UGameplayTasksComponent::SetSimulatedTasksNetDirty()
    {
        //MARK_PROPERTY_DIRTY_FROM_NAME(UGameplayTasksComponent, SimulatedTasks, this);
    }
}