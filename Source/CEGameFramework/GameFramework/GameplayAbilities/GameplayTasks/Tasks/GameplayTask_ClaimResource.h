// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#include "GameplayAbilities/GameplayTasks/GameplayTaskOwnerInterface.h"
#include "GameplayAbilities/GameplayTasks/GameplayTask.h"
#include "GameplayAbilities/GameplayTasks/GameplayTaskResource.h"
#include "GameplayAbilities/GameplayTasks/GameplayTask.h"

namespace cegf {

    //UCLASS(BlueprintType, MinimalAPI)
    class UGameplayTask_ClaimResource : public cegf::UGameplayTask
    {
        //GENERATED_BODY()
    public:
        GAMEFRAMEWORK_API UGameplayTask_ClaimResource();

        //UFUNCTION(BlueprintCallable, Category = "AI|Tasks", meta = (AdvancedDisplay = "Priority, TaskInstanceName"))
        static GAMEFRAMEWORK_API std::shared_ptr<UGameplayTask_ClaimResource> ClaimResource(IGameplayTaskOwnerInterface* InTaskOwner, TSubclassOf<UGameplayTaskResource> ResourceClass, const uint8_t Priority = 192, const cross::UniqueString TaskInstanceName = "");

        //UFUNCTION(BlueprintCallable, Category = "AI|Tasks", meta = (AdvancedDisplay = "Priority, TaskInstanceName"))
        static GAMEFRAMEWORK_API std::shared_ptr<UGameplayTask_ClaimResource> ClaimResources(IGameplayTaskOwnerInterface* InTaskOwner, std::vector<TSubclassOf<UGameplayTaskResource> > ResourceClasses, const uint8_t Priority = 192, const cross::UniqueString TaskInstanceName = "");

        static GAMEFRAMEWORK_API std::shared_ptr<UGameplayTask_ClaimResource> ClaimResource(IGameplayTaskOwnerInterface& InTaskOwner, const TSubclassOf<UGameplayTaskResource> ResourceClass, const uint8_t Priority = FGameplayTasks::DefaultPriority, const cross::UniqueString TaskInstanceName = "");
        static GAMEFRAMEWORK_API std::shared_ptr<UGameplayTask_ClaimResource> ClaimResources(IGameplayTaskOwnerInterface& InTaskOwner, const std::vector<TSubclassOf<UGameplayTaskResource> >& ResourceClasses, const uint8_t Priority = FGameplayTasks::DefaultPriority, const cross::UniqueString TaskInstanceName = "");
    };
}
