// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once
#include "GameFramework/GameFrameworkTypes.h"
#include "GameFramework/GameplayAbilities/GameplayTasks/GameplayTask.h"
namespace cegf {
    //UCLASS(MinimalAPI)
    class GAMEFRAMEWORK_API UGameplayTask_WaitDelay : public UGameplayTask
    {
        //GENERATED_BODY()

        DECLARE_DYNAMIC_MULTICAST_DELEGATE(FTaskDelayDelegate);

    public:
        UGameplayTask_WaitDelay();

        //UPROPERTY(BlueprintAssignable)
        FTaskDelayDelegate OnFinish;

        virtual void Activate() override;

        /** Return debug string describing task */
        virtual std::string GetDebugString() const override;

        /** Wait specified time. This is functionally the same as a standard Delay node. */
        //UFUNCTION(BlueprintCallable, Category = "GameplayTasks", meta = (AdvancedDisplay = "TaskOwner, Priority", DefaultToSelf = "TaskOwner", BlueprintInternalUseOnly = "TRUE"))
        static std::shared_ptr<UGameplayTask_WaitDelay> TaskWaitDelay(IGameplayTaskOwnerInterface* TaskOwner, float Time, const uint8_t Priority = 192);

        static std::shared_ptr<UGameplayTask_WaitDelay> TaskWaitDelay(IGameplayTaskOwnerInterface& InTaskOwner, float Time, const uint8_t Priority = FGameplayTasks::DefaultPriority);

    private:

        void OnTimeFinish();

        double Time;
        double TimeStarted;
    };
}