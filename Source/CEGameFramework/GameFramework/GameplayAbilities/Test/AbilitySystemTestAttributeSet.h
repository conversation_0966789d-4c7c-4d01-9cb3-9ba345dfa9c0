// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once
#include "GameplayAbilities/AttributeSet/AttributeSet.h"
#include "GameplayAbilities/GameplayEffect/GameplayEffect.h"
#include "GameplayAbilities/Utils/GASMacros.h"

namespace cegf {

class GAMEPLAYABILITIES_API CEMeta(Reflect, Script) UAbilitySystemTestAttributeSet : public UAttributeSet
{
    GENERATED_UCLASS_BODY()
    StaticMetaClassName(UAbilitySystemTestAttributeSet);
    CEMeta(Reflect, Script)
    UAbilitySystemTestAttributeSet(GameObject* owner);
    /**
     *	NOTE ON MUTABLE:
     *	This is only done so that UAbilitySystemTestAttributeSet can be initialized directly in GameplayEffectsTest.cpp without doing a const_cast in 100+ places.
     *	Mutable is not required and should never be used on normal attribute sets.
     */

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest", meta = (HideFromModifiers))   // You can't make a GameplayEffect modify Health Directly (go through)

    CEMeta(<PERSON>fle<PERSON>, Script)
    mutable float MaxHealth;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest", meta = (HideFromModifiers))   // You can't make a GameplayEffect modify Health Directly (go through)

    CEMeta(Reflect, Script)
    mutable float Health;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable FGameplayAttributeData Mana;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float MaxMana;

    /** This Damage is just used for applying negative health mods. Its not a 'persistent' attribute. */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AttributeTest")   // You can't make a GameplayEffect 'powered' by Damage (Its transient)

    CEMeta(Reflect, Script)
    mutable float Damage;

    /** This Attribute is the actual spell damage for this actor. It will power spell based GameplayEffects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float SpellDamage;

    /** This Attribute is the actual physical damage for this actor. It will power physical based GameplayEffects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float PhysicalDamage;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float CritChance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float CritMultiplier;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float ArmorDamageReduction;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float DodgeChance;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float LifeSteal;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float Strength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float StackingAttribute1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float StackingAttribute2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AttributeTest")
    CEMeta(Reflect, Script)
    mutable float NoStackAttribute;

    virtual bool PreGameplayEffectExecute(struct FGameplayEffectModCallbackData& Data) override;
    virtual void PostGameplayEffectExecute(const struct FGameplayEffectModCallbackData& Data) override;
};
}   // namespace cegf