#pragma once
#include "String/UniqueString.h"
#include <string_view>
#include <cctype>  // for std::isdigit
#include <algorithm> // for std::transform
namespace CE::ComparisonUtility {

/** Compare the two names, correctly ordering any numeric suffixes they may have */
int32_t CompareWithNumericSuffix(std::string_view A, std::string_view B);

int32_t CompareWithNumericSuffix(cross::UniqueString A, cross::UniqueString B);
}   // namespace UE::ComparisonUtility
