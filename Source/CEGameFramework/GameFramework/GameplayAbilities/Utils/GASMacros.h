#pragma once
#define GAMEPLAYTAGS_API GAMEFRAMEWORK_API 
#define GAMEPL<PERSON>YABILITIES_API GAMEFRAMEWORK_API
#define UENUM(...)	
#define USTRUCT(...)	
#define UFUNCTION(...)	
#define UMETA(...)
#define UINTERFACE(...)
#define GENERATED_USTRUCT_BODY(...) public:	
#define GENERATED_UCLASS_BODY(...)  public:	
#define GENERATED_BODY(...)	
#define GENERATED_UINTERFACE_BODY(...) public:
#define GENERATED_IINTERFACE_BODY(...) public:
#define UPROPERTY(...)	
#define UINTERFACE(...)
#define UE_DEPRECATED(...)	
#define UE_DEPRECATED_FORGAME(...)
#define UCLASS(...)
#define FString std::string	
#define FName   cross::UniqueString
#define GAS_NAME_None ""
#define GAS_TEXT 

#ifndef IN
#define IN
#endif

#ifndef OUT
#define OUT
#endif

#ifdef __clang__
template<typename T >
auto ArrayCountHelper(T& t) -> char (&)[sizeof(t) / sizeof(t[0]) + 1];
#else
template<typename T, int N>
char (&ArrayCountHelper(const T (&)[N]))[N + 1];
#endif

// Number of elements in an array.
#define GAS_ARRAY_COUNT(array) (sizeof(ArrayCountHelper(array)) - 1)

#include "MSVCPlatformCompilerPreSetup.h"
#include "CEMetaMacros.h"