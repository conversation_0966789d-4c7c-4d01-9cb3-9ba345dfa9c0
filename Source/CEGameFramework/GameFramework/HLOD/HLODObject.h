#pragma once
#include "GameFramework/GameObjects/GameObject.h"
#include "GameFramework/Components/ModelComponent.h"

namespace cegf
{

class HLODProxyObject;

// An instance of an autogenerated StaticMesh Actors by Hierarchical LOD System
// This is essentially just StaticMeshActor that you can't move or edit, but it contains multiple actors reference
class GAMEFRAMEWORK_API CEMeta(Reflect, <PERSON>li, Puerts) HLODObject : public GameObject
{
public:
    CEGameplayInternal()
    StaticMetaClassName(HLODObject)
    
    static HLODObject* AsHLODObject(GameObject* object);

    CEMeta(Cli)
    HLODObject();
    virtual ~HLODObject() = default;

    virtual void Tick(float deltaTime) override;

    virtual void InitializeComponents() override;

    virtual void PostInitializeComponents() override;

    virtual bool Deserialize(const DeserializeNode& in, SerializeContext& context);

    virtual void BeginDestroy() override;

    // Tick implementation
    void Update(bool fullUpdate = false, bool forceHide = false);

    CEMeta(Cli, ScriptCallable)
    int GetLODLevel() const;

    CEMeta(Cli, ScriptCallable)
    void AddSubObject(GameObject * object);

    CEMeta(Cli, ScriptCallable)
    void RemoveSubObject(GameObject * object);

    CEMeta(Cli, ScriptCallable)
    void ClearSubObjects();

    CEMeta(Cli, ScriptCallable)
    bool HasSubObjects() const;

    float GetDrawDistanceWithOverride() const;

    float CalculateDrawDistance(GameObject * object, float screenSize);

    void SetTransitionScreenSize(float screenSize);

    void SetDrawDistance(float distance);

    void SetOverrideMaterial(const std::string& materialPath);

    void SetForcedLODLevel(int value);

    CEMeta(Cli, EditorPropertyInfo(PropertyType = "Auto"))
    bool DebugBoundingSphere = false;

    CEMeta(Cli, EditorPropertyInfo(PropertyType = "Auto", bReadOnly = true))
    int LODLevel = -1;

private:
    void SetCullingDistance(GameObject* object, float min, float max);
    bool GetObjectVisibility(GameObject * object) const;
    void SetObjectVisibility(GameObject* object, bool visibility);
    float GetCurrentDrawDistance();
    float GetCurrentScreenSize();
    void DrawDebugBoundingSphere();
    HLODProxyObject* GetProxy() const;
    bool IsValidForUpdate(GameObject* object) const;

private:
    float mDrawDistance = 0.0f;
    float mTransitionScreenSize = 0.0f;
    int mForcedLODLevel = -1;

    // Cached result for streaming purpose
    bool mVisible = false;

	ModelComponent* mModelComponent;
    std::vector<GameObject*> mSubObjects;
    std::weak_ptr<GameObject> mParent;

    mutable std::mutex mMutex;
};

using HLODObjectPtr = std::shared_ptr<HLODObject>;

} // namespace cegf