#include "GameFramework/Input/InputActionMapping/InputActionMappingRegistry.h"
#include "GameFramework/Input/InputModifiers.h"
#include "GameFramework/Input/InputTriggers.h"

namespace cegf {

InputActionMappingRegistry& InputActionMappingRegistry::Instance()
{
    static InputActionMappingRegistry instance;
    return instance;
}

void InputActionMappingRegistry::RegisterInputActionMappingClasses()
{
    // Modifiers
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyDefaultModifier>(), true);
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyNegativeModifier>());
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyScaleModifier>());
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyDeltaModifier>());
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyDeadZoneModifier>());
    RegisterInputModifier(gbf::reflection::query_meta_class<cegf::InputKeyExponentModifier>());

    // Triggers
    RegisterInputTrigger(gbf::reflection::query_meta_class<cegf::InputKeyDefaultTrigger>(), true);
    RegisterInputTrigger(gbf::reflection::query_meta_class<cegf::InputKeyChordActionTrigger>());
}

void InputActionMappingRegistry::RegisterInputModifier(const gbf::reflection::MetaClass* meta, bool updateDefault)
{
    if (!meta || !meta->IsTypeMatch(gbf::reflection::query_meta_class<cross::InputKeyModifierItem>()->id()))
    {
        return;
    }

    bool successful = mInputModifierMetaClasses.try_emplace(meta->name(), meta).second;
    if (successful)
    {
        Modifiers.emplace_back(meta->name());
        if (updateDefault)
        {
            DefaultInputModifier = meta->name();
        }
    }
}

void InputActionMappingRegistry::RegisterInputTrigger(const gbf::reflection::MetaClass* meta, bool updateDefault)
{
    if (!meta || !meta->IsTypeMatch(gbf::reflection::query_meta_class<cross::InputKeyTriggerItem>()->id()))
    {
        return;
    }

    bool successful = mInputTriggerMetaClasses.try_emplace(meta->name(), meta).second;
    if (successful)
    {
        Triggers.emplace_back(meta->name());
        if (updateDefault)
        {
            DefaultInputTrigger = meta->name();
        }
    }
}

}   // namespace cegf
