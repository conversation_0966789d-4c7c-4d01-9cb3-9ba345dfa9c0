#pragma once
#pragma warning(disable : 4100)

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/node/blueprint_action_node.h"
#include "GameFramework/Objects/ObjectBase.h"
namespace gbf { namespace logic {
    template<typename T>
    class UBlueprintNode_CastTo : public UBlueprintActionNode
    {
    public:
        CEMeta(Reflect)
        UBlueprintNode_CastTo()
            : UBlueprintActionNode(BlueprintSlotAvailableFlag::NodeVarIn, "")
        {
            m_title = std::string{"Cast To "} + std::string{MetatypeHash::NamePretty<T>()};
            InitializeSlotsImpl();
        }

    protected:
        void InitializeSlotsImpl()
        {
            // ExecSlot
            AddExecSlot(BlueprintLinkDirection::In, 0, "");
            AddExecSlot(BlueprintLinkDirection::Out, 0, "Succeed");   // In data slot
            AddExecSlot(BlueprintLinkDirection::Out, 1, "CastFailed");    // In data slot
            AddDataSlot(BlueprintLinkDirection::In, 0, machine::VValueKind::kUser, "Object", reflection::query_meta_class<cegf::ObjectBase>()->id());
            AddDataSlot(BlueprintLinkDirection::Out, 0, machine::VValueKind::kUser, std::string{MetatypeHash::NamePretty<T>()}, reflection::query_meta_class<T>()->id());
        }

        ProcessingInfo RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot) override
        {
            machine::MemoryScope* local = coroutine_->GetCurrentMemoryScope();
            machine::NamedMemoryScope* global = coroutine_->GetGlobalMemoryScope();
            UBlueprintActionNode::ProcessingInfo info;
            // prepare param
            const reflection::UserObject& delfult_value = reflection::make_raw_pointer_user_object(nullptr);
            const reflection::UserObject& p = RtTryGetDataSlotValue(BlueprintLinkDirection::In, 0, local, global, delfult_value);
            reflection::RttiBase* Target = reflection::get_raw_pointer_user_object<reflection::RttiBase>(p);
            if (Target && dynamic_cast<T*>(Target))
            {

                auto ret = dynamic_cast<T*>(Target);

                auto slot_out = GetDataSlotOut(0);
                auto out_slot_id = slot_out->GetFullSlotId();
                auto out = local->QueryValue(out_slot_id);

                if (!out)
                    out = local->CreateValue(out_slot_id, slot_out->GetDefaultValue());

                *out = reflection::make_raw_pointer_user_object(ret);
                info.State = UBlueprintActionNode::LogicState::Ok;
                RtActiveOuputLink(coroutine_, 0);
            }
            else
            {
                info.State = UBlueprintActionNode::LogicState::Ok;
                RtActiveOuputLink(coroutine_, 1);
            }
            return {};
        }
    };

    template<typename T>
    class CEMeta(Reflect) UBlueprintNode_GetClass : public UBlueprintNode
    {
    public:
        CEMeta(Reflect)
        UBlueprintNode_GetClass()
            : UBlueprintNode(BlueprintNodeType::Variable, BlueprintSlotAvailableFlag::Var,"")
        {
            m_title = std::string{"GetClass: "} + std::string{MetatypeHash::NamePretty<T>()};
            InitializeSlotsImpl();
        }

    protected:
        int target_slot_id = 0;
        int paramName_slot_id = 0;
        int value_slot_id = 0;

        void InitializeSlotsImpl()
        {
            AddDataSlot(BlueprintLinkDirection::Out, 0, machine::VValueKind::kUser, "Class* ", reflection::query_meta_class<gbf::reflection::MetaClass>()->id());
            AddDataSlot(BlueprintLinkDirection::Out, 1, machine::VValueKind::kString, "Class FullName");
        }

        machine::VValuePtr RtPullResult(UBlueprintDataSlot * slot, machine::MemoryScope * local, machine::NamedMemoryScope * global) override
        {
            auto meta_class = reflection::query_meta_class<T>();
            auto out_slot_id = slot->GetFullSlotId();
            auto out = local->QueryValue(out_slot_id);

            if (!out)
                out = local->CreateValue(out_slot_id, slot->GetDefaultValue());

            if (meta_class == nullptr)
            {
                return out;
            }

            if (slot == GetDataSlotOut(0))
            {
                *out = reflection::make_raw_pointer_user_object(const_cast<void*>(static_cast<const void*>(meta_class)));
            }
            else   // if (slot == GetDataSlotOut(1))
            {
                *out = meta_class->name();
            }
            return out;
        }
    };

}}