#pragma once

#include <functional>
#include <memory>
#include <unordered_map>
#include <vector>
#include <atomic>

namespace cegf
{
    // 定时器句柄，用于标识和管理定时器
    struct FTimerHandle
    {
        FTimerHandle() : Id(0) {}
        explicit FTimerHandle(uint64_t InId) : Id(InId) {}

        bool IsValid() const { return Id != 0; }
        void Invalidate() { Id = 0; }

        bool operator==(const FTimerHandle& Other) const { return Id == Other.Id; }
        bool operator!=(const FTimerHandle& Other) const { return Id != Other.Id; }

        uint64_t GetId() const { return Id; }

    private:
        uint64_t Id;
    };
}

// 为FTimerHandle提供hash支持，用于unordered_map
namespace std
{
    template <>
    struct hash<cegf::FTimerHandle>
    {
        size_t operator()(const cegf::FTimerHandle& Handle) const
        {
            return hash<uint64_t>()(Handle.GetId());
        }
    };
}

namespace cegf
{
    // 定时器委托类型
    using FTimerDelegate = std::function<void()>;

    // 定时器数据结构
    struct FTimerData
    {
        FTimerDelegate Delegate;           // 回调函数
        float Rate;                        // 执行间隔（秒）
        bool bLoop;                        // 是否循环
        bool bPaused;                      // 是否暂停
        float ElapsedTime;                 // 已经过的时间
        float NextExecutionTime;           // 下次执行时间
        double StartTime; // 开始时间

        FTimerData()
            : Rate(0.0f)
            , bLoop(false)
            , bPaused(false)
            , ElapsedTime(0.0f)
            , NextExecutionTime(0.0f)
            , StartTime(0.0)
        {
        }
    };

    /**
     * 定时器管理器
     * 提供类似Unreal Engine FTimerManager的功能
     */
    class FTimerManager
    {
    public:
        FTimerManager();
        ~FTimerManager();

        // 禁用拷贝构造和赋值
        FTimerManager(const FTimerManager&) = delete;
        FTimerManager& operator=(const FTimerManager&) = delete;

        /**
         * 设置定时器
         * @param InOutHandle 定时器句柄（输出参数）
         * @param InDelegate 回调函数
         * @param InRate 执行间隔（秒）
         * @param bInLoop 是否循环执行
         * @param InFirstDelay 首次执行延迟时间（默认-1表示使用InRate）
         */
        void SetTimer(FTimerHandle& InOutHandle, FTimerDelegate&& InDelegate,
            float InRate, bool bInLoop = false, float InFirstDelay = -1.0f);

        // todo add other SetTimer impl

        /**
         * 设置在下一个Tick执行的定时器
         * @param InDelegate 回调函数
         * @return 定时器句柄
         */
        FTimerHandle SetTimerForNextTick(FTimerDelegate&& InDelegate);

        /**
         * 清除定时器
         * @param InHandle 要清除的定时器句柄
         */
        void ClearTimer(FTimerHandle& InHandle);

        /**
         * 清除所有定时器
         */
        void ClearAllTimers();

        /**
         * 暂停定时器
         * @param InHandle 要暂停的定时器句柄
         */
        void PauseTimer(const FTimerHandle& InHandle);

        /**
         * 恢复定时器
         * @param InHandle 要恢复的定时器句柄
         */
        void UnPauseTimer(const FTimerHandle& InHandle);

        /**
         * 检查定时器是否激活
         */
        bool IsTimerActive(const FTimerHandle& InHandle) const;

        /**
         * 检查定时器是否暂停
         */
        bool IsTimerPaused(const FTimerHandle& InHandle) const;

        /**
         * 获取定时器剩余时间
         */
        float GetTimerRemaining(const FTimerHandle& InHandle) const;

        /**
         * 获取定时器已运行时间
         */
        float GetTimerElapsed(const FTimerHandle& InHandle) const;

        /**
         * 获取定时器执行间隔
         */
        float GetTimerRate(const FTimerHandle& InHandle) const;

        /**
         * 检查定时器是否存在
         */
        bool TimerExists(const FTimerHandle& InHandle) const;

        /**
         * 更新所有定时器（需要在主循环中调用）
         * @param DeltaTime 时间步长（秒）
         */
        void Tick(float DeltaTime);

        /**
         * 获取当前激活的定时器数量
         */
        int32_t GetActiveTimerCount() const;

    private:
        // 生成新的定时器句柄
        FTimerHandle GenerateHandle();

        // 查找定时器数据
        FTimerData* FindTimer(const FTimerHandle& InHandle);
        const FTimerData* FindTimer(const FTimerHandle& InHandle) const;

        // 执行定时器回调
        void ExecuteTimer(const FTimerHandle& InHandle, FTimerData& TimerData);

        // 定时器映射表
        std::unordered_map<FTimerHandle, std::unique_ptr<FTimerData>> Timers;

        // 待删除的定时器句柄列表
        std::vector<FTimerHandle> TimersToRemove;

        // 句柄生成器
        std::atomic<uint64_t> NextHandleId;

        // 当前时间
        double CurrentTime;

        // 是否正在执行Tick
        bool bIsTickingTimers;
    };
}