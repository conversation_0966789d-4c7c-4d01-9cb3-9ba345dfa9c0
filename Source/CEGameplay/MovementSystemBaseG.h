#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/GameSystemBase.h"
#include "WorldCollision.h"

namespace cross
{
#define MOVEMENT_SMALL_NUMBER (0.0001f)

/*
 * Define functionality for moving an entity based on physics.
 * Base functionality includes:
 *    - Utility functions for special handling of collision results (SlideAlongSurface(), ComputeSlideVector(), TwoSurfaceAdjust()).
 *    - Utility functions for moving when there may be initial penetration (SafeMovement(), ResolvePenetration()).  
 */
class MovementSystemBaseG : public GameSystemBase
{
public:
    CEFunction(Reflect)
    virtual const PhysicsGeometryBase* GetPhysicsGeometry(ecs::EntityID entity) const;

    /*
     * Process impact when movement is blocked
     * 
     * @param Hit: Describes the collision.
     * @param PosRatio: Amount of move to apply (between 0 and 1) for the simulation that produced this hit.  
     * @param MoveDelta: Attempted move that resulted in the hit.
     */
    virtual void HandleImpact(ecs::EntityID inEntity, const HitResult& hit, float posRatio = 0.f, const Float3& moveDelta = Float3::Zero());

    /*
     * Compute a vector to slide along a surface, given an attempted move, positionRatio, and normal.
     * 
     * @param InDelta: Attempted move.
     * @param PosRatio: Amount of move to apply (between 0 and 1).
     * @param Normal: Normal opposed to movement. Not necessarily equal to Hit.Normal.
     * @param Hit: HitResult of the move that resulted in the slide.
     */
    virtual Float3 ComputeSlideVector(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, const HitResult& hit) const;

    /*
     * Slide smoothly along a surface, and slide away from multiple impacts using TwoSurfaceAdjust() if necessary. Calls HandleImpact() for each surface hit, if requested.
     * Use SafeMovement() for movement, and ComputeSlideVector() to determine the slide direction.
     * 
     * @param Delta: Attempted movement vector.
     * @param PosRatio: Percent of Delta to apply (between 0 and 1). Usually equal to the remaining time after a collision: (1.0 - Hit.PositionRatio).
     * @param Normal: Normal opposing movement, along which we will slide.
     * @param Hit: [In] HitResult of the attempted move that resulted in the impact triggering the slide. [Out] HitResult of last attempted move.
     * @param HandleImpact: Whether to call HandleImpact() on each hit.
     * @return The percentage of requested distance (Delta * Percent) actually applied (between 0 and 1). 0 if no movement occurred, non-zero if movement occurred.
     */
    virtual float SlideAlongSurface(ecs::EntityID inEntity, const Float3& inDelta, float posRatio, const Float3& normal, HitResult& outHitResult, bool handleImpact = false);

    /*
     * Compute a movement direction when contacting two surfaces.
     * 
     * @param Delta: [In] Amount of move attempted before impact. [Out] Computed adjustment based on impacts.
     * @param Hit: Impact from last attempted move
     * @param OldHitNormal:	Normal of impact before last attempted move
     * @return Result in Delta that is the direction to move when contacting two surfaces.
     */
    virtual void TwoSurfaceAdjust(ecs::EntityID inEntity, Float3& outDelta, const HitResult& hit, const Float3& oldHitNormal) const;


    ////// Move entity safely
    /*
     * Call MoveImpl(), handling initial penetrations by calling ResolvePenetration(). If this adjustment succeeds, the original movement will be attempted again.
     * 
     * @return result of the final MoveImpl() call.
     */
    virtual bool SafeMovement(ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit = nullptr, bool drawDebug = false);


    ////// Process penetration with other entity
    /*
     * Calculate a movement adjustment to try to move out of a penetration from a failed move.
     * 
     * @param Hit: the result of the failed move
     * @return The adjustment to use after a failed move, or a zero vector if no attempt should be made.
     */
    virtual Float3 GetPenetrationPullOut(ecs::EntityID inEntity, const HitResult& hit);

    /*
     * Try to move out of penetration in an entity after a failed move.
     * 
     * @param Hit: The result of the failed move
     * @param PullOut: The requested adjustment, usually from GetPenetrationPullOut()
     * @return True if the adjustment was successful and the original move should be retried, or false if no repeated attempt should be made.
     */
    virtual bool ResolvePenetration(ecs::EntityID inEntity, const HitResult& hit, const Float3& pullOut, const Quaternion& newRotation);


    ////// Movement implementation
    /*
     * Try to move the entity by a movement vector (Delta) and set rotation to NewRotation.
     * Assume that the entity's current location is valid and that the entity does fit in its current Location.
     *
     * @param InDelta: The desired location change in world space.
     * @param NewRotation: The new desired rotation in world space.
     * @param UseSweep: Whether we sweep to the destination location, triggering overlaps along the way and stopping short of the target if blocked by something.
     * @param Hit: Optional output describing the blocking hit that stopped the move, if any.
     * @return True if some movement occurred, false if no movement occurred.
     */
    virtual bool MoveImpl(ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit = nullptr, bool drawDebug = false);

protected:
    MovementSystemBaseG() = default;
    virtual ~MovementSystemBaseG() = default;

public:
    static bool SafeMovement_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit = nullptr, bool drawDebug = false);

private:
    static const PhysicsGeometryBase* GetPhysicsGeometry_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity);

    static bool MoveEntity_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const Float3& inDelta, const Quaternion& newRotation, bool useSweep, HitResult* outHit = nullptr, bool drawDebug = false);

    static Float3 GetPenetrationPullOut_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const HitResult& hit);
    static bool ResolvePenetration_DefaultImpl(GameWorld* inWorld, ecs::EntityID inEntity, const HitResult& hit, const Float3& pullOut, const Quaternion& newRotation);
};

}
