#include "EnginePrefix.h"
#include "SimpleMovementSystemG.h"

#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/SettingsManager.h"
#include "CrossBase/String/UniqueString.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "MovementSystemBaseG.h"
#include "Resource/AssetStreaming.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Threading/RenderingThread.h"

namespace cross {
ecs::ComponentDesc* cross::SimpleMovementComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::SimpleMovementComponentG>(
        { false, true, true }, &SimpleMovementSystemG::SerializeMotionMovementComponent, &SimpleMovementSystemG::DeserializeMotionMovementComponent, &SimpleMovementSystemG::PostDeserializeMotionMovementComponent);
}

SimpleMovementSystemG* SimpleMovementSystemG::CreateInstance()
{
    return new SimpleMovementSystemG();
}

void SimpleMovementSystemG::Release()
{
    delete this;
}

void SimpleMovementSystemG::NotifyAddRenderSystemToRenderWorld() {}

SimpleMovementSystemG::SimpleMovementSystemG() {}

SimpleMovementSystemG::~SimpleMovementSystemG() {}

void SimpleMovementSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto transSystem = mGameWorld->GetGameSystem<TransformSystemG>();

    SInt32 numThreads = threading::TaskSystem::Get()->GetNumMasterThreads() + threading::TaskSystem::Get()->GetNumTaskThreads();
    const SInt32 minSizeForSingleThread = 50;

    // grab all sockets for execute
    auto queryResult = mGameWorld->Query<SimpleMovementComponentG, LocalTransformComponentG>();
    // grab single executable thread's task number
    const SInt32 entityNum = queryResult.GetEntityNum();
    const SInt32 taskCountForSingleThread = (std::max)((SInt32)std::ceil(entityNum * 1.0f / numThreads), minSizeForSingleThread);

    SInt32 leftEntityNum = entityNum;
    float elapsedTime = frameParam->GetDeltaTime();
    bool isExecutable = EngineGlobal::GetSettingMgr()->GetAppStartUpType() != cross::AppStartUpType::AppStartUpTypeCrossEditor || mGameWorld->GetWorldType() == WorldTypeTag::PIEWorld;

    for (SInt32 Index = 0; Index < numThreads; Index += 1)
    {
        CreateTaskFunction(FrameTickStage::Update, {}, [this, Index, numThreads, taskCountForSingleThread, entityNum, transSystem, isExecutable, elapsedTime]() mutable {
            QUICK_SCOPED_CPU_TIMING("SimpleMovementSystemG");
            if (isExecutable)
            {
                // grab all sockets for execute, forbid other task override query result
                auto queryResult = mGameWorld->Query<SimpleMovementComponentG, LocalTransformComponentG, WorldTransformComponentG>();
                // grab start index for current thread
                SInt32 startIndex = Index * taskCountForSingleThread;
                SInt32 endIndex = std::min(startIndex + taskCountForSingleThread, entityNum);
                for (SInt32 curIndex = startIndex; curIndex < endIndex; ++curIndex)
                {
                    auto&& [motionMoveComp, localTransComp, worldTransComp] = queryResult[curIndex];

                    auto motionMoveCompW = motionMoveComp.Write();
                    auto motionMoveCompR = motionMoveComp.Read();
                    auto localTransCompW = localTransComp.Write();
                    auto localTransCompR = localTransComp.Read();

                    if (motionMoveCompW->bRootMotionEnabled)
                    {
                        anim::RootMotionParams RootMotion;
                        ProcessRootMotionFromAnimation(motionMoveCompW);
                        // Apply root motion to velocity
                        if (HasAnimRootMotion(motionMoveCompR))
                        {
                            //// Convert local space root motion to world space, because animation root motion is always local
                            // ConvertLocalRootMotionToWorld(motionMoveCompW);
                            if (elapsedTime > 0.f)
                            {
                                NodeTransform oldLocalTransform = {transSystem->GetLocalScale(localTransCompR), transSystem->GetLocalRotation(localTransCompR), transSystem->GetLocalTranslation(localTransCompR)};
                                // NodeTransform oldWorldTransform = {mTransformSys->GetWorldMatrix(worldTransComp.Read())};

                                // Apply local root motion to local transform
                                NodeTransform newLocalTransform = motionMoveCompR->RootMotion.GetRootMotionTransform() * oldLocalTransform;

                                transSystem->SetLocalTranslation(localTransCompW, newLocalTransform.GetTranslation());
                                transSystem->SetLocalRotation(localTransCompW, newLocalTransform.GetRotation());
                            }
                            motionMoveCompW->RootMotion.Reset();
                        }
                    }
                }
            }
        });

        // no more valid task exists, out early
        leftEntityNum -= taskCountForSingleThread;
        if (leftEntityNum <= 0)
            break;
    }
}

void SimpleMovementSystemG::GetMotionMovementComponent(const MovementCompReader& component, cross::SimpleMovementComponentG& outValue)
{
    outValue.bRootMotionEnabled = component->bRootMotionEnabled;
}

void SimpleMovementSystemG::SetMotionMovementComponent(const MovementCompWriter& component, const cross::SimpleMovementComponentG& inValue)
{
    component->bRootMotionEnabled = inValue.bRootMotionEnabled;
}

SerializeNode SimpleMovementSystemG::SerializeMotionMovementComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    SerializeNode localJson;
    auto motionMovementCompPtr = static_cast<SimpleMovementComponentG*>(componentPtr);

    SerializeContext context;
    SerializeNode outNode = motionMovementCompPtr->Serialize(context);
    return outNode;
}

void SimpleMovementSystemG::DeserializeMotionMovementComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    if (json.IsNull())
        return;

    auto motionMovementCompPtr = static_cast<SimpleMovementComponentG*>(componentPtr);
    if (json.IsObject())
    {
        SerializeContext node;
        motionMovementCompPtr->Deserialize(json, node);
    }
}

void SimpleMovementSystemG::PostDeserializeMotionMovementComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

bool SimpleMovementSystemG::HasAnimRootMotion(const MovementCompReader& inHandle) const
{
    return inHandle->RootMotion.HasRootMotion();
}

void SimpleMovementSystemG::ProcessRootMotionFromAnimation(const MovementCompWriter& inHandle)
{
    auto animatorSys = mGameWorld->GetGameSystem<AnimatorSystemG>();
    auto animComp = mGameWorld->GetComponent<AnimatorComponentG>(inHandle.GetEntityID());

    if (animComp.IsValid())
    {
        auto& animator = animatorSys->GetAnimator(animComp.Read());

        if (animator.IsPlayingRootMotion())
        {
            inHandle->RootMotion = animator.ConsumeRootMotion();
        }
    }
}

void SimpleMovementSystemG::ConvertLocalRootMotionToWorld(const MovementCompWriter& inHandle)
{
    auto mTransformSys = mGameWorld->GetGameSystem<TransformSystemG>();
    const auto& reader = ecs::GrantReadAccess(inHandle);

    if (HasAnimRootMotion(reader))
    {
        auto [worldTransComp, localTransComp] = mGameWorld->GetComponent<WorldTransformComponentG, LocalTransformComponentG>(inHandle.GetEntityID());

        NodeTransform oldLocalTransform = {mTransformSys->GetLocalScale(localTransComp.Read()), mTransformSys->GetLocalRotation(localTransComp.Read()), mTransformSys->GetLocalTranslation(localTransComp.Read())};
        NodeTransform oldWorldTransform = {mTransformSys->GetWorldMatrix(worldTransComp.Read())};

        // Apply local root motion to local transform
        NodeTransform newLocalTransform = inHandle->RootMotion.GetRootMotionTransform() * oldLocalTransform;

        // Calculate new world transform when local transform changes
        NodeTransform newWorldTransform{NodeTransform::Identity()};

        auto parentEntity = mTransformSys->GetEntityParent(localTransComp.Read());
        // Have parent node
        if (parentEntity != ecs::EntityID::InvalidHandle())
        {
            auto parentWorldTransComp = mGameWorld->GetComponent<WorldTransformComponentG>(parentEntity);
            auto parentWorldTransform = NodeTransform{mTransformSys->GetWorldMatrix(parentWorldTransComp.Read())};

            newWorldTransform = newLocalTransform * parentWorldTransform;
        }
        else
        {
            newWorldTransform = newLocalTransform;
        }

        Float3 deltaTranslation = newWorldTransform.GetTranslation() - oldWorldTransform.GetTranslation();
        Quaternion deltaRotation = oldWorldTransform.GetRotation().Inverse() * newWorldTransform.GetRotation();

        NodeTransform worldRootMotion{Float3::One(), deltaRotation, deltaTranslation};
        // Set world root motion
        inHandle->RootMotion.SetRootMotion(worldRootMotion);
    }
}
}   // namespace cross
