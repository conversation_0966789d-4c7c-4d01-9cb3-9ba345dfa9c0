#include "EnginePrefix.h"
#include <filesystem>
#include <locale>
#include <codecvt>
#include "Runtime/Interface/CrossEngine.h"

#include "Client/CrossPlatform/Public/PublicClient.h"
#include "Runtime/Input/Core/Public/SlateApplication.h"
#include "CECommon/Common/SettingsManager.h"
#include "CECommon/Common/CmdSettings.h"


#include <Windowsx.h>

// Global Variables:
const char *szTitle = "CrossEngine Standalone";          // The title bar text
const char *szWindowClass = "CrossEngine Standalone"; // the main window class name
const char *szEngineDll = "CrossEngine.dll";
HMODULE gModule = nullptr;

std::unique_ptr<cross::PublicClient> gClient;

static std::string gRootDir = ".";
static std::string gLaunchScene = "";
static std::string gEngineHome{};






// Forward declarations of functions included in this code module:
BOOL InitInstance(HINSTANCE, int);

char* WideCharToUTF8(const wchar_t* unicode)
{
    int len;
    len = WideCharToMultiByte(CP_UTF8, 0, unicode, -1, NULL, 0, NULL, NULL);
    char *szUtf8 = static_cast<char*>(malloc(len + 1));
    memset(szUtf8, 0, len + 1);
    WideCharToMultiByte(CP_UTF8, 0, unicode, -1, szUtf8, len, NULL, NULL);
    return szUtf8;
}

class AutoConsole
{
public:
    AutoConsole()
        : mAllocated(false), mStdOut(nullptr), mStdErr(nullptr)
    {
        if (AllocConsole())
        {
            mAllocated = true;
            freopen_s(&mStdOut, "CONOUT$", "w", stdout);
            freopen_s(&mStdErr, "CONOUT$", "w", stderr);
        }
    }

    ~AutoConsole()
    {
        if (mAllocated)
        {
            fclose(mStdOut);
            mStdOut = nullptr;
            fclose(mStdErr);
            mStdErr = nullptr;
            FreeConsole();
            mAllocated = false;
        }
    }

    AutoConsole(AutoConsole const &) = delete;
    AutoConsole &operator=(AutoConsole const &) = delete;

private:
    bool mAllocated;
    FILE *mStdOut;
    FILE *mStdErr;
};

int APIENTRY wWinMain(_In_ HINSTANCE hInstance,
                      _In_opt_ HINSTANCE hPrevInstance,
                      _In_ LPWSTR lpCmdLine,
                      _In_ int nCmdShow)
{
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);

    //LPCSTR us_kb = "00000409";
    //HKL keyboard_layout = LoadKeyboardLayoutA(us_kb, KLF_ACTIVATE);

    cross::WindowsApplication::InstanceHandle = hInstance;
    cross::WindowsApplication::IconHandle = ::LoadIcon((HINSTANCE)NULL, IDI_APPLICATION); 

    LPWSTR *szArgList;
    int argCount;

    std::filesystem::path workDirectory{};
    std::filesystem::path engineHome{};

    bool hideConsole = false;
    bool engineResourceConfigurated = false;

    if (lpCmdLine[0])
    {
        szArgList = CommandLineToArgvW(lpCmdLine, &argCount);

        if(0)
        {
            if (szArgList != nullptr && argCount >= 1)
            {
                int argIndex = 0;
                while (argIndex < argCount)
                {
                    const wchar_t* pArg = szArgList[argIndex];
                    const int argsRemain = argCount - (argIndex + 1);
                    int curArgCount = 1;

#define REMAINING_ARGS_CHECK(n) if (argsRemain < (n)) { fprintf(stderr, "Error: Expected %u values to follow %ws!\n", n, pArg); Assert(false); }

                    if (wcscmp(szArgList[argIndex], L"-h") == 0 || wcscmp(szArgList[argIndex], L"--help") == 0)
                    {
                        printf("\nUsage: client.exe work_dir\n");
                        puts(
                            "Options:\n"
                            "  --help display help information.\n"
                            "  --hide_console hide console.\n"
                            "  --work_dir set work directory.\n"
                            "  --engine_resource set engine resource directory\n"
                            "  --launch_scene set launch scene file.\n"
                            "  --showfps enable showing fps.\n"
                            "  --message_keep enable to keep showing last five messages\n"
                        );
                    }
                    else if (wcscmp(szArgList[argIndex], L"--hide_console") == 0)
                    {
                        hideConsole = true;
                    }
                    else if (wcscmp(szArgList[argIndex], L"--showfps") == 0)
                    {
                        cross::CmdSettings::Inst().SetValue("showfps", true);
                    }
                    else if (wcscmp(szArgList[argIndex], L"--message_keep") == 0)
                    {
                        cross::CmdSettings::Inst().SetValue("message_keep", true);
                    }
                    else if (wcscmp(szArgList[argIndex], L"--gpu_dump") == 0)
                    {
                        cross::CmdSettings::Inst().SetValue("gpu_dump", true);
                    }
                    else if (wcscmp(szArgList[argIndex], L"--work_dir") == 0)
                    {
                        REMAINING_ARGS_CHECK(1)
                            std::filesystem::path path{ szArgList[argIndex + 1] };

                        if (path.is_absolute())
                        {
                            workDirectory = path;
                        }
                        else
                        {
                            workDirectory = std::filesystem::absolute(path);
                        }
                        curArgCount++;
                    }
                    else if (wcscmp(szArgList[argIndex], L"--engine_resource") == 0)
                    {
                        REMAINING_ARGS_CHECK(1)
                            std::filesystem::path homePath{ szArgList[argIndex + 1] };
                        if (homePath.is_absolute())
                        {
                            engineHome = homePath;
                        }
                        else
                        {
                            engineHome = std::filesystem::absolute(homePath);
                        }
                        engineResourceConfigurated = true;
                        curArgCount++;
                    }
                    else if (wcscmp(szArgList[argIndex], L"--launch_scene") == 0)
                    {
                        REMAINING_ARGS_CHECK(1)
                            gLaunchScene = WideCharToUTF8(szArgList[argIndex + 1]);
                        curArgCount++;
                    }

                    else
                    {
                        std::filesystem::path path{ szArgList[argIndex] };
                        workDirectory = path;
                    }

                    argIndex += curArgCount;
                }
            }
        }

        auto& argParser = cross::CmdSettings::Inst().GetArgParser();
        argParser.add_argument("--hide_console").default_value(false).implicit_value(true).help("hide console");
        argParser.add_argument("--work_dir").help("set work directory.");
        argParser.add_argument("--engine_resource").help("set engine resource directory.");
        argParser.add_argument("--launch_scene").help("set launch scene file.");

        std::vector<std::string> args;
        // push back a args acting as exe to fit argparse lib
        args.push_back("");
        std::wstring_convert<std::codecvt_utf8<wchar_t>, wchar_t> converters;
        for (int i = 0; i < argCount; i++)
        {
            std::wstring ws(szArgList[i]);

            args.push_back(converters.to_bytes(ws));
        }
        
        cross::CmdSettings::Inst().Parse(args);

        hideConsole = argParser.get<bool>("--hide_console");

        if (auto fn = argParser.present("--work_dir"))
        {
            std::filesystem::path path{ *fn };

            if (path.is_absolute())
            {
                workDirectory = path;
            }
            else
            {
                workDirectory = std::filesystem::absolute(path);
            }
        }

        if (auto fn = argParser.present("--engine_resource"))
        {
            std::filesystem::path path{ *fn };

            if (path.is_absolute())
            {
                engineHome = path;
            }
            else
            {
                engineHome = std::filesystem::absolute(path);
            }
            engineResourceConfigurated = true;
        }

        if (auto fn = argParser.present("--launch_scene"))
        {
            gLaunchScene = argParser.get<std::string>("--launch_scene");
        }

        // get relative home path
        if (!engineResourceConfigurated) {
            char instanceName[MAX_PATH];
            ::GetModuleFileName(hInstance, instanceName, sizeof(instanceName));
            std::filesystem::path homePath = instanceName;
            homePath = homePath.parent_path();

            homePath += "../../../../Resource";
            homePath = std::filesystem::absolute(homePath);
            if (std::filesystem::exists(homePath) && std::filesystem::is_directory(homePath))
            {
                engineHome = homePath;
            }
        }
        LocalFree(szArgList);
    }
    else
    {
        workDirectory = std::filesystem::current_path();
#if CROSSENGINE_DEPLOY
        hideConsole = true;
        TCHAR szPath[MAX_PATH];
        if (GetModuleFileName(NULL, szPath, MAX_PATH))
        {
            workDirectory = std::filesystem::path(szPath).parent_path();
            workDirectory.append(gCrossEngineDefaultAssetsName);
        }
#endif   // CROSSENGINE_DEPLOY
    }

    // Setup the engine binary directory
    {
        TCHAR szPath[MAX_PATH];
        if (GetModuleFileName(NULL, szPath, MAX_PATH))
        {
            cross::PathHelper::SetEngineBinaryDirectoryPath(std::filesystem::path(szPath).parent_path().string());
        }
    }

    AutoConsole *autoConsole = nullptr;
    if (!hideConsole)
        autoConsole = new AutoConsole();

    gRootDir = workDirectory.string();
    if (!engineHome.empty())
        gEngineHome = engineHome.string();

    // Perform application initialization:
    if (!InitInstance(hInstance, nCmdShow))
        return -1;

    HACCEL hAccelTable = LoadAccelerators(hInstance, "Client");

#if INPUT_CLIENT
    // XInput create
    int playerId = -1;
    XINPUT_STATE state;
    WORD lastButton = 0;
    ZeroMemory(&state, sizeof(XINPUT_STATE));

    for (DWORD i = 0; i < XUSER_MAX_COUNT && playerId == -1; i++)
    {
        ZeroMemory(&state, sizeof(XINPUT_STATE));

        if (XInputGetState(i, &state) == ERROR_SUCCESS)
            playerId = i;
    }
#endif

    // Main message loop:
    bool bQuit = false;
    MSG msg{};
    while (!bQuit)
    {
        // update client on every tick
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE))
        {
            if (!TranslateAccelerator(msg.hwnd, hAccelTable, &msg))
            {
                TranslateMessage(&msg);
                DispatchMessage(&msg);
            }

            if (msg.message == WM_QUIT)
                bQuit = true;
        }
        if (bQuit)
            break;

        //ActivateKeyboardLayout(keyboard_layout, KLF_RESET);
        gClient->Update();
    }

    gClient->OnShutDown();
    return static_cast<int>(msg.wParam);
}

//
//   FUNCTION: InitInstance(HINSTANCE, int)
//
//   PURPOSE: Saves instance handle and creates main window
//
//   COMMENTS:
//
//        In this function, we save the instance handle in a global variable and
//        create and display the main program window.
//
BOOL InitInstance(HINSTANCE hInstance, int nCmdShow)
{
    gClient.reset(new cross::PublicClient());
    gClient->SetAssetPath(gRootDir.c_str());
    gClient->SetLaunchScene(gLaunchScene.c_str());
    
    if (!gEngineHome.empty())
        gClient->SetEnginePath(gEngineHome.c_str());

    if (!gClient->Initialize())
        return FALSE;

    LONG width = static_cast<LONG>(gClient->GetScreenWidth() * gClient->GetScreenScale());
    LONG height = static_cast<LONG>(gClient->GetScreenHeight() * gClient->GetScreenScale());

    auto* settingsMgr = gClient->GetEngine()->GetSettingManager();
    settingsMgr->SetMessageKeepOnInit(cross::CmdSettings::Inst().gKeepMessage);
 
    DWORD dwExStyle = 0;
    DWORD dwStyle = 0;

    int xPos = 0;
    int yPos = 0;

    auto nMon = GetSystemMetrics(SM_CMONITORS);
    std::vector<MONITORINFOEX> monInfos;
    monInfos.reserve(nMon);
    auto MonitorEnumProc = [](__in HMONITOR hMonitor, __in HDC hdcMonitor, __in LPRECT lprcMonitor, __in LPARAM dwData) -> BOOL {
        auto mis = reinterpret_cast<std::vector<MONITORINFOEX>*>(dwData);
        MONITORINFOEX mi{sizeof(MONITORINFOEX)};
        GetMonitorInfo(hMonitor, &mi);
        mis->emplace_back(mi);
        return TRUE;
    };
    EnumDisplayMonitors(NULL, NULL, MonitorEnumProc, reinterpret_cast<LPARAM>(&monInfos));

    MONITORINFOEX monInfo{};
    if (auto monIdx = settingsMgr->GetMonitorIndex(); nMon > 1 && monIdx != -1)
    {
        monIdx = std::min(monIdx, nMon);
        monInfo = monInfos[monIdx];
    }
    else
    {
        auto ret = std::find_if(monInfos.begin(), monInfos.end(), [](auto& mi) -> bool { return mi.dwFlags & MONITORINFOF_PRIMARY; });
        AssertMsg(ret != monInfos.end(), "Can't find primary monitor");
        monInfo = *ret;
    }

    LONG screenWidth = monInfo.rcMonitor.right - monInfo.rcMonitor.left;
    LONG screenHeight = monInfo.rcMonitor.bottom - monInfo.rcMonitor.top;

    switch (settingsMgr->GetFullScreen())
    {
    case cross::FullScreenMode::Windowed:
    {
        dwExStyle = WS_EX_APPWINDOW;
        dwStyle = (WS_OVERLAPPEDWINDOW ^ (WS_MAXIMIZEBOX | WS_THICKFRAME)) | WS_CLIPSIBLINGS | WS_CLIPCHILDREN;
        break;
    }
    case cross::FullScreenMode::Exclusive:
    {
    }
    case cross::FullScreenMode::BoardlessWindowed:
    {      
        xPos = monInfo.rcMonitor.left;
        yPos = monInfo.rcMonitor.top;
        if ((width != screenWidth) || (height != screenHeight))
        {
            DEVMODE dmScreenSettings{};
            dmScreenSettings.dmSize = sizeof(dmScreenSettings);
            dmScreenSettings.dmPelsWidth = width;
            dmScreenSettings.dmPelsHeight = height;
            dmScreenSettings.dmBitsPerPel = 32;
            dmScreenSettings.dmFields = DM_BITSPERPEL | DM_PELSWIDTH | DM_PELSHEIGHT;

            if (ChangeDisplaySettingsEx(monInfo.szDevice, &dmScreenSettings, NULL, CDS_FULLSCREEN, NULL) != DISP_CHANGE_SUCCESSFUL)
            {
                if (MessageBox(NULL, "Fullscreen Mode not supported!\n Switch to window mode?", "Error", MB_YESNO | MB_ICONEXCLAMATION) == IDYES)
                {
                    dwExStyle = WS_EX_APPWINDOW;
                    dwStyle = (WS_OVERLAPPEDWINDOW ^ (WS_MAXIMIZEBOX | WS_THICKFRAME)) | WS_CLIPSIBLINGS | WS_CLIPCHILDREN;
                    break;
                }
                else
                {
                    LOG_ERROR("FullMode Set False");
                }
            }
        }
        dwStyle = settingsMgr->GetFullScreen() == cross::FullScreenMode::Exclusive ? WS_POPUP: WS_OVERLAPPED | WS_CLIPSIBLINGS | WS_CLIPCHILDREN;
        dwExStyle = WS_EX_APPWINDOW;
        break;
    }
    default:
        Assert(false);
        break;
    }

    gClient->mMonitorInfos.clear();
    for (auto& monitor : monInfos)
    {
        gClient->mMonitorInfos.push_back({monitor.rcMonitor.bottom, monitor.rcMonitor.top, monitor.rcMonitor.left, monitor.rcMonitor.right});
    }

    RECT rc = { 0, 0, width, height };
    AdjustWindowRectEx(&rc, dwStyle, FALSE, dwExStyle);

    auto clientName = settingsMgr->GetClientName();
    if (clientName != "")
    {
        szTitle = clientName.c_str();
    }
    
    HWND hWnd = CreateWindowEx(dwExStyle,
        szWindowClass,  // name of the window class
        szTitle,
        dwStyle,
        xPos, 
        yPos,
        rc.right - rc.left,
        rc.bottom - rc.top,
        nullptr,        // we have no parent window, NULL
        nullptr,        // we aren't using menus, NULL
        hInstance,      // application handle
        nullptr);       // used with multiple windows, NULL
    if (!hWnd) return FALSE;
    Assert(SUCCEEDED(GetLastError()));

    // Make the window borderless so that the client area can fill the screen.
    if (settingsMgr->GetFullScreen() == cross::FullScreenMode::BoardlessWindowed)
    {
        SetWindowPos(hWnd, HWND_TOP, monInfo.rcMonitor.left, monInfo.rcMonitor.top, rc.right - rc.left, rc.bottom - rc.top, SWP_FRAMECHANGED | SWP_NOACTIVATE);
   
        ::SetWindowLong(hWnd, GWL_STYLE, WS_OVERLAPPED | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);
        ::ShowWindow(hWnd, SW_SHOW);
    }
    else if (settingsMgr->GetFullScreen() == cross::FullScreenMode::Exclusive)
    {
        SetWindowPos(hWnd, HWND_TOPMOST, monInfo.rcMonitor.left, monInfo.rcMonitor.top, rc.right - rc.left, rc.bottom - rc.top, SWP_FRAMECHANGED | SWP_NOACTIVATE);    
        ::SetWindowLong(hWnd, GWL_STYLE, WS_POPUP);
        ::ShowWindow(hWnd, SW_MAXIMIZE);
    } 
    else
    {
        SetWindowPos(hWnd, HWND_TOP, monInfo.rcMonitor.left, monInfo.rcMonitor.top, rc.right - rc.left, rc.bottom - rc.top, SWP_FRAMECHANGED | SWP_NOACTIVATE);    

        ::SetWindowLong(hWnd, GWL_STYLE, (WS_OVERLAPPEDWINDOW ^ (WS_MAXIMIZEBOX | WS_THICKFRAME)) | WS_CLIPSIBLINGS | WS_CLIPCHILDREN);
        ::ShowWindow(hWnd, SW_SHOW);
    }

    ::UpdateWindow(hWnd);

    if (::RegisterTouchWindow(hWnd, 0) == false)
    {
        SInt32 Error = GetLastError();
        LOG_WARN("Register touch input failed for: {}!", Error);
    }

    RECT clientRect{};
    GetClientRect(hWnd, &clientRect);
    if (clientRect.right != width || clientRect.bottom != height)
    {
        LOG_WARN("Adjust Window Size By Screen Size");
        gClient->SetScreenWidth(static_cast<float>(clientRect.right));
        gClient->SetScreenHeight(static_cast<float>(clientRect.bottom));
    }

    if (!(gClient->InitWindow(hWnd) && gClient->InitWorld()))
        return FALSE;

    return TRUE;
}

//#define XINPUT_CLIENT 0
//
//#if XINPUT_CLIENT
//#    include <Xinput.h>
//#endif
//
//#define XClamp(i) (i > 0.1 ? i : (i < -0.1 ? i : 0.f))

//#if INPUT_CLIENT
// if (playerId != -1)
//{
//    XInputGetState(playerId, &state);
//
//    float LStickX = static_cast<float>(state.Gamepad.sThumbLX) / 65535.f;
//    float LStickY = static_cast<float>(state.Gamepad.sThumbLY) / 65535.f;
//    float RStickX = static_cast<float>(state.Gamepad.sThumbRX) / 65535.f;
//    float RStickY = static_cast<float>(state.Gamepad.sThumbRY) / 65535.f;
//
//    gClient->OnAxis(InputAxis::GamepadLeftStickX, XClamp(LStickX) * 2.f);
//    gClient->OnAxis(InputAxis::GamepadLeftStickY, XClamp(LStickY) * 2.f);
//    gClient->OnAxis(InputAxis::GamepadRightStickX, XClamp(RStickX) / gClient->GetScreenWidth() * 4.f);
//    gClient->OnAxis(InputAxis::GamepadRightStickY, -XClamp(RStickY) / gClient->GetScreenHeight() * 4.f);
//
//    WORD button = state.Gamepad.wButtons;
//    win_gamepad_button_map(lastButton, button, 0x0001, InputButton::Gamepad_Up);
//    win_gamepad_button_map(lastButton, button, 0x0002, InputButton::Gamepad_Down);
//    win_gamepad_button_map(lastButton, button, 0x0004, InputButton::Gamepad_Left);
//    win_gamepad_button_map(lastButton, button, 0x0008, InputButton::Gamepad_Right);
//    win_gamepad_button_map(lastButton, button, 0x0010, InputButton::Gamepad_Start);
//    win_gamepad_button_map(lastButton, button, 0x0020, InputButton::Gamepad_Back);
//    win_gamepad_button_map(lastButton, button, 0x0040, InputButton::Gamepad_Thumb_Left);
//    win_gamepad_button_map(lastButton, button, 0x0080, InputButton::Gamepad_Thumb_Right);
//    win_gamepad_button_map(lastButton, button, 0x0100, InputButton::Gamepad_Shoulder_Left);
//    win_gamepad_button_map(lastButton, button, 0x0200, InputButton::Gamepad_Shoulder_Right);
//    win_gamepad_button_map(lastButton, button, 0x1000, InputButton::Gamepad_A);
//    win_gamepad_button_map(lastButton, button, 0x2000, InputButton::Gamepad_B);
//    win_gamepad_button_map(lastButton, button, 0x4000, InputButton::Gamepad_X);
//    win_gamepad_button_map(lastButton, button, 0x8000, InputButton::Gamepad_Y);
//
//    gClient->OnButtonDown(InputButton::Gamepad_Trigger_Left, true, static_cast<float>(state.Gamepad.bLeftTrigger) / 255.0f);
//    gClient->OnButtonDown(InputButton::Gamepad_Trigger_Right, true, static_cast<float>(state.Gamepad.bRightTrigger) / 255.0f);
//    lastButton = button;
//}
//#endif


//
//  FUNCTION: MyRegisterClass()
//
//  PURPOSE: Registers the window class.
//
// ATOM MyRegisterClass(HINSTANCE hInstance)
//{
//    WNDCLASSEX wcex;
//
//    wcex.cbSize = sizeof(WNDCLASSEX);
//
//    wcex.style = CS_HREDRAW | CS_VREDRAW;
//    wcex.lpfnWndProc = WndProc;
//    wcex.cbClsExtra = 0;
//    wcex.cbWndExtra = 0;
//    wcex.hInstance = hInstance;
//    wcex.hIcon = nullptr;
//    wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
//    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
//    wcex.lpszMenuName = nullptr;
//    wcex.lpszClassName = szWindowClass;
//    wcex.hIconSm = nullptr;
//
//    return RegisterClassEx(&wcex);
//}