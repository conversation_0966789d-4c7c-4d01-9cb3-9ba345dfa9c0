#pragma once

namespace cross
{
    namespace detail
    {
        template <typename ... Args>
        class TWhenAllContext<std::tuple<TFuture<Args>...>, void> final
            : public TPromiseContext<TWhenAllContext<std::tuple<TFuture<Args>...>, void>>
        {
        public:
            using ValueType = std::tuple<typename TFuture<Args>::StorageType...>;
            using PromiseType = TPromise<ValueType>;
            using BaseType = TPromiseContext<TWhenAllContext<std::tuple<TFuture<Args>...>, void>>;
            static constexpr SizeType PrecedeCount = sizeof...(Args);

        public:
            TWhenAllContext() = default;

            template <CROSS_REQUIRE_IF((LaunchType, ExecutorType), TIsLaunchPolicy<LaunchType>, TIsExecutorOf<ExecutorType, void()>)>
            void Launch(LaunchType policy, ExecutorType& executor, TFuture<Args>& ... futures)
            {
                TUniqueLock<Mutex> lock{ mMutex };
                auto futuresTuple = std::forward_as_tuple(futures...);
                ApplyForEachFuture(policy, executor, futuresTuple, std::make_index_sequence<PrecedeCount>{});
                CheckWhenAll();
            }

            auto GetFuture()
            {
                return mPromise.GetFuture();
            }

        private:
            template <size_t Index, typename TryValueType>
            void ApplyPromiseValue(TryValueType const& tryValue)
            {
                std::get<Index>(mValue) = tryValue;
                mCompleteCount++;
            }

            template <size_t Index, typename Launch, typename Executor, typename T>
            void ApplyForEachFutureImpl(Launch policy, Executor& executor, TFuture<T>& future)
            {
                if(future.IsReady())
                {
                    ApplyPromiseValue<Index>(BaseType::GetSharedState(future)->GetStorage());
                }
                else
                {
                    using TryValueType = typename TFuture<T>::StorageType;
                    future.Then(policy, executor, [ctx = this->shared_from_this(), this](TryValueType const& tryValue)
                    {
                        TUniqueLock<Mutex> lock{ mMutex };
                        ApplyPromiseValue<Index>(tryValue);
                        CheckWhenAll();
                    });
                }
            }

            template <typename Launch, typename Executor, typename FutureTuple, size_t ... Indices>
            void ApplyForEachFuture(Launch policy, Executor& executor, FutureTuple& futuresTuple, std::index_sequence<Indices...>)
            {
                (ApplyForEachFutureImpl<Indices>(policy, executor, std::get<Indices>(futuresTuple)), ...);
            }
            
            void CheckWhenAll()
            {
                if(mCompleteCount == PrecedeCount)
                {
                    mPromise.SetValue(std::move(mValue));
                }
                else if(mCompleteCount > PrecedeCount)
                {
                    throw std::overflow_error{"Complete Count Overflow."};
                }
            }

        private:
            Mutex       mMutex;
            SizeType    mCompleteCount{ 0 };
            PromiseType mPromise;
            ValueType   mValue;
        };

        template <typename Iterator>
        class TWhenAllContext<Iterator, TEnableIfFutureIterator<Iterator>> final
            : public TPromiseContext<TWhenAllContext<Iterator, TEnableIfFutureIterator<Iterator>>>
        {
        public:
            using IteratorType = Iterator;
            using FutureType = typename std::iterator_traits<Iterator>::value_type;
            using TryValueType = typename FutureType::StorageType;
            using ValueType = std::vector<TryValueType>;
            using PromiseType = TPromise<ValueType>;
            using BaseType = TPromiseContext<TWhenAllContext<Iterator, TEnableIfFutureIterator<Iterator>>>;

        public:
            TWhenAllContext() = default;

            template <CROSS_REQUIRE_IF((LaunchType, ExecutorType), TIsLaunchPolicy<LaunchType>, TIsExecutorOf<ExecutorType, void()>)>
            void Launch(LaunchType policy, ExecutorType& executor, Iterator begin, Iterator end)
            {
                DEBUG_ASSERT(begin <= end);
                mValue.resize(std::distance(begin, end));
                SizeType loop = 0;
                for(auto itr = begin; itr != end; ++itr, ++loop)
                {
                    ApplyForEachFuture(policy, executor, loop, itr);
                }
                CheckWhenAll();
            }

            auto GetFuture()
            {
                return mPromise.GetFuture();
            }

        private:
            void ApplyPromiseValue(SizeType index, TryValueType const& tryValue)
            {
                mValue[index] = tryValue;
                mCompleteCount++;
            }

            template <typename Launch, typename Executor>
            void ApplyForEachFuture(Launch policy, Executor& executor, SizeType index, Iterator itr)
            {
                if(itr->IsReady())
                {
                    ApplyPromiseValue(index, BaseType::GetSharedState(*itr)->GetStorage());
                }
                else
                {
                    itr->Then(policy, executor, [ctx = this->shared_from_this(), this, index](TryValueType const& tryValue)
                    {
                        TUniqueLock<Mutex> lock{ mMutex };
                        ApplyPromiseValue(index, tryValue);
                        CheckWhenAll();
                    });
                }
            }

            void CheckWhenAll()
            {
                SizeType const precedeCount = mValue.size();
                if(mCompleteCount == precedeCount)
                {
                    mPromise.SetValue(std::move(mValue));
                }
                else if(mCompleteCount > precedeCount)
                {
                    throw std::overflow_error{"Complete Count Overflow."};
                }
            }

        private:
            Mutex       mMutex;
            SizeType    mCompleteCount;
            PromiseType mPromise;
            ValueType   mValue;
        };
    }

    template <CROSS_REQUIRE((Launch, Executor, ... Args),
        std::enable_if_t<std::conjunction_v<TIsLaunchPolicy<Launch>, TIsExecutorOf<Executor, void()>>>,
        TEnableIfMatchFuturePattern<TRemoveRCVT<Args>...>)>
    auto WhenAll(Launch policy, Executor& executor, Args&& ... args)
    {
        using PatternType = typename TPackFuturePattern<std::tuple<TRemoveRCVT<Args>...>>::Type;
        using ContextType = detail::TWhenAllContext<PatternType>;
        //using PromiseType = typename ContextType::PromiseType;
        auto ctx = std::make_shared<ContextType>();
        ctx->Launch(policy, executor, std::forward<Args>(args)...);
        return ctx->GetFuture();
    }

    template <CROSS_REQUIRE((Launch, ... Args),
        std::enable_if_t<TIsLaunchPolicyV<Launch>>,
        TEnableIfMatchFuturePattern<TRemoveRCVT<Args>...>)>
    auto WhenAll(Launch policy, Args&& ... args)
    {
        static LaunchThreadExecutor executor{};
        return WhenAll(policy, executor, std::forward<Args>(args)...);
    }

    template <CROSS_REQUIRE((Executor, ... Args),
        std::enable_if_t<TIsExecutorOfV<Executor, void()>>,
        TEnableIfMatchFuturePattern<TRemoveRCVT<Args>...>)>
    auto WhenAll(Executor& executor, Args&& ... args)
    {
        return WhenAll(launch::Post, executor, std::forward<Args>(args)...);
    }

    template <CROSS_REQUIRE((... Args), TEnableIfMatchFuturePattern<TRemoveRCVT<Args>...>)>
    auto WhenAll(Args&& ... args)
    {
        return WhenAll(launch::Post, std::forward<Args>(args)...);
    }
}
