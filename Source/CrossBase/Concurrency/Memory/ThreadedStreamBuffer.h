#pragma once

// TODO ... thread library forward header
namespace cross
{
    class Mutex;
    class Semaphore;
}

// A single producer, single consumer ringbuffer

// Most read and write operations are done even without atomic operations and use no expensive synchronization primitives
// Each thread owns a part of the buffer and only locks when reaching the end


// *** Common usage *** 
// * Create the ring buffer
// ThreadedStreamBuffer buffer (ModeThreaded);

// * Producer thread...
// buffer.WriteValueType<int>(5);
// buffer.WriteValueType<int>(7);
// buffer.WriteSubmitData();

// * Consumer Thread...
// print(buffer.ReadValueType<int>());
// print(buffer.ReadValueType<int>());
// buffer.ReadReleaseData();

namespace cross
{
    // TODO ... refactor ring buffer
    class ThreadedStreamBuffer final : public NonCopyable
    {
    public:
        struct CROSS_BASE_API BufferState
        {
            // These should not be size_t, as the Device may run across processes of different
            // bitness, and the data serialized in the command buffer must match.
            // TODO ... remove volatile
            void Reset() volatile;
            volatile UInt32 bufferPos;
            volatile UInt32 bufferEnd;
            volatile UInt32 bufferWraps;
            volatile UInt32 checkedPos;
            volatile UInt32 checkedWraps;
#ifndef CROSSENGINE_RELEASE
            volatile UInt32 totalBytes;
#endif
        };

        struct BufferHeader
        {
            BufferState reader;
            BufferState writer;
        };

        typedef unsigned size_t;

        enum Mode
        {
            // This is the most common usage. One producer, one consumer on different threads.
            ModeThreaded,

            // When in read mode, we pass a pointer to the external data which can then be read using ReadValueType and ReadReleaseData.
            ModeReadOnly,

            // When in growable you are only allowed to write into the ring buffer. Essentially like a std::vector. It will keep on growing as you write data.
            ModeGrowable,

            ModeCrossProcess,
        };

        CROSS_BASE_API ThreadedStreamBuffer(Mode mode, size_t size);
        CROSS_BASE_API ThreadedStreamBuffer();
        CROSS_BASE_API ~ThreadedStreamBuffer();

        enum
        {
            DefaultAlignment = 4,
            DefaultStep = 4096
        };

        // Read data from the ringbuffer
        // This function blocks until data new data has arrived in the ringbuffer.
        // It uses semaphores to wait on the producer thread in a efficient way.
        template <class T> const T&	ReadValueType();
        template <class T> T* ReadArrayType(int count);

        // ReadReleaseData should be called when the data has been read & used completely.
        // At this point the memory will become available to the producer to write into it again.
        CROSS_BASE_API void ReadReleaseData();

        // Write data into the ringbuffer
        template <class T> void WriteValueType(const T& val);
        template <class T> void	WriteArrayType(const T* vals, int count);
        template <class T> T* GetWritePointer();

        // WriteSubmitData should be called after data has been completely written and should be made available to the consumer thread to read it.
        // Before WriteSubmitData is called, any data written with WriteValueType can not be read by the consumer.
        CROSS_BASE_API void WriteSubmitData();

        // Ringbuffer Streaming support. This will automatically call WriteSubmitData & ReadReleaseData.
        // It Splits the data into smaller chunks (step). So that the size of the ringbuffer can be smaller than the data size passed into this function.
        // The consumer thread will be reading the streaming data while WriteStreamingData is still called on the producer thread.
        CROSS_BASE_API void ReadStreamingData(void* data, size_t size, size_t alignment = DefaultAlignment, size_t step = DefaultStep);
        CROSS_BASE_API void WriteStreamingData(const void* data, size_t size, size_t alignment = DefaultAlignment, size_t step = DefaultStep);

        // Utility functions
        void* GetReadDataPointer(size_t size, size_t alignment);
        void* GetWriteDataPointer(size_t size, size_t alignment);

        size_t GetDebugReadPosition() const { return mReader->bufferPos; }
        size_t GetDebugWritePosition() const { return mWriter->bufferPos; }

        double GetReadWaitTime() const { return mReadWaitTime; }
        double GetWriteWaitTime() const { return mWriteWaitTime; }
        void ResetReadWaitTime() { mReadWaitTime = 0.0; }
        void ResetWriteWaitTime() { mWriteWaitTime = 0.0; }

        // Creation methods
        CROSS_BASE_API void Create(Mode mode, size_t size);
        CROSS_BASE_API void CreateReadOnly(const void* buffer, size_t size);
        CROSS_BASE_API void CreateFromMemory(Mode mode, size_t size, void *buffer);
        CROSS_BASE_API void ResetGrowable();
        CROSS_BASE_API void Destroy();

        // Is there data available to be read
        // typicall this is not used
        bool HasData() const;
        bool HasDataToRead() const;

        size_t GetAllocatedSize() const { return mBufferSize; }
        CROSS_BASE_API size_t GetCurrentSize() const;
        CROSS_BASE_API const void*	GetBuffer() const;
    private:
        size_t Align(size_t pos, size_t alignment) const { return (pos + alignment - 1)&~(alignment - 1); }

        CROSS_BASE_API void SetDefaults();

        CROSS_BASE_API void HandleReadOverflow(size_t& dataPos, size_t& dataEnd);
        CROSS_BASE_API void HandleWriteOverflow(size_t& dataPos, size_t& dataEnd);

        CROSS_BASE_API void SendReadSignal();
        CROSS_BASE_API void SendWriteSignal();

        Mode                    mMode;
        char*                   mBuffer;
        char*                   mOriginBuffer;  // origin buffer address saved for alignment
        size_t                  mBufferSize;
        size_t                  mGrowStepSize;
        BufferState*            mReader;
        BufferState*            mWriter;
        BufferHeader            mHeader;
        Mutex*                  mMutex;
        Semaphore*              mReadSemaphore;
        Semaphore*              mWriteSemaphore;
        std::atomic<int>        mNeedsReadSignal;
        std::atomic<int>        mNeedsWriteSignal;
        double                  mReadWaitTime;
        double                  mWriteWaitTime;
    };

    inline bool ThreadedStreamBuffer::HasData() const
    {
        return (mReader->bufferPos != mWriter->checkedPos);
    }

    inline void* ThreadedStreamBuffer::GetReadDataPointer(size_t size, size_t alignment)
    {
        size = Align(size, alignment);
        size_t dataPos = Align(mReader->bufferPos, alignment);
        size_t dataEnd = dataPos + size;
        if (dataEnd > mReader->bufferEnd)
        {
            HandleReadOverflow(dataPos, dataEnd);
        }
        mReader->bufferPos = dataEnd;
#ifndef CROSSENGINE_RELEASE
        mReader->totalBytes += size;
#endif
        return &mBuffer[dataPos];
    }

    inline void* ThreadedStreamBuffer::GetWriteDataPointer(size_t size, size_t alignment)
    {
        size = Align(size, alignment);
        assert(size * 2 <= mBufferSize || mMode == ModeGrowable);
        size_t dataPos = Align(mWriter->bufferPos, alignment);
        size_t dataEnd = dataPos + size;
        if (dataEnd > mWriter->bufferEnd)
        {
            HandleWriteOverflow(dataPos, dataEnd);
        }
        mWriter->bufferPos = dataEnd;
#ifndef CROSSENGINE_RELEASE
        mWriter->totalBytes += size;
#endif
        return &mBuffer[dataPos];
    }

    template <class T>
    inline const T& ThreadedStreamBuffer::ReadValueType()
    {
        // Read simple data type from queue
        const void* pdata = GetReadDataPointer(sizeof(T), alignof(T));
        const T& src = *reinterpret_cast<const T*>(pdata);
        return src;
    }

    template <class T>
    inline T* ThreadedStreamBuffer::ReadArrayType(int count)
    {
        // Read array of data from queue-
        void* pdata = GetReadDataPointer(count * sizeof(T), alignof(T));
        T* src = reinterpret_cast<T*>(pdata);
        return src;
    }

    template <class T>
    inline void ThreadedStreamBuffer::WriteValueType(const T& val)
    {
        // Write simple data type to queue
        void* pdata = GetWriteDataPointer(sizeof(T), alignof(T));
        new (pdata) T(val);
    }

    template <class T>
    inline void ThreadedStreamBuffer::WriteArrayType(const T* vals, int count)
    {
        // Write array of data to queue
        T* pdata = (T*)GetWriteDataPointer(count * sizeof(T), alignof(T));
        for (int i = 0; i < count; i++)
            new (&pdata[i]) T(vals[i]);
    }

    template <class T> 
    inline T* ThreadedStreamBuffer::GetWritePointer()
    {
        // Write simple data type to queue
        void* pdata = GetWriteDataPointer(sizeof(T), alignof(T));
        return static_cast<T*>(pdata);
    }

}