#pragma once

#include "CrossBaseForward.h"
#include "Concurrency/Thread/ThreadForward.h"
#include "Concurrency/Thread/ThreadUtility.h"
#include "Concurrency/Details/ThreadTraits.hpp"

namespace cross
{
    class PlatformThread;

    // CAUTION!!! Thread object is not thread safe
    class CROSS_BASE_API Thread final
    {
    public:  // constructors
        // trivil constructor
        Thread();

        // constructor with default thread parameters
        explicit Thread(ThreadFunctionPtr threadFunc);

        // complete constructor
        Thread(ThreadParams const& param, ThreadFunctionPtr threadFunc);

        // higher-order constructor
        template <typename Func, typename ... Args, typename = detail::TEnableHigherOrderConstructor<TRemoveRCVT<Func>>>
        Thread(ThreadParams const& param, Func&& func, Args&& ... args)
            : Thread(param, MakeThreadFunction(std::forward<Func>(func), std::forward<Args>(args)...))
        {}

        // higher-order constructor with default thread parameters
        template <typename Func, typename ... Args, typename = detail::TEnableHigherOrderConstructor<TRemoveRCVT<Func>>>
        explicit Thread(Func&& func, Args&& ... args)
            : Thread(MakeThreadFunction(std::forward<Func>(func), std::forward<Args>(args)...))
        {}

    public: // other semantics
        // non-copy-constructible
        Thread(Thread const&) = delete;

        // non-copy-assignable
        Thread& operator= (Thread const&) = delete;

        // move-constructible
        Thread(Thread&&);

        // move-assignable
        Thread& operator= (Thread&&);

        // destrurctor
        ~Thread();

    public:
        // get the stack size
        UInt32 GetStackSize() const noexcept;

        // get the priority
        ThreadPriority GetPriority() const noexcept;

        // get the affinity
        ThreadAffinity GetAffinity() const noexcept;

        // get the thread ID
        ThreadID GetThreadID() const noexcept;

        // thread name
        char const* GetName() const noexcept;

        // get the native handle
        void* GetNativeHandle() const noexcept;

        // is thread joinable
        bool Joinable() const noexcept;

    public:
        // synchronization point for the thread and calling thread
        void Join();

        // release management of thread kernal object
        void Detach();

    public:
        static ThreadParams DefaultParams(std::string name);

    private:
        template <typename Func, typename ... Args>
        static auto MakeThreadFunction(Func&& func, Args&& ... args) -> ThreadFunctionPtr
        {
            // TODO ... allocator
            return std::make_unique<ThreadFunction>
            (
                [f = std::forward<Func>(func), argsTuple = std::make_tuple(std::forward<Args>(args)...)]() mutable
                {
                    std::apply(f, argsTuple); 
                }
            ); 
        }
        
    private:
        PlatformThread* mPImpl;
    };

    namespace thread
    {
        CROSS_BASE_API ThreadID GetCurrentThreadID();
        CROSS_BASE_API ThreadID GetMainThreadID() noexcept;
        CROSS_BASE_API void MakeThreadCurrent();
        CROSS_BASE_API bool MainThreadCurrent();
        // TODO ... using system clock and duration
        CROSS_BASE_API void SleepThisFor(MillionSecondCount millionSecondCounts);
        template <typename Duration>
        void SleepThisFor(Duration const& duration)
        {
            SleepThisFor(time::Cast<MillionSecondCount>(duration));
        }
        CROSS_BASE_API void YieldThis();
        CROSS_BASE_API UInt32 HardwareConcurrency();
    }
}
