#pragma once

#include <unistd.h>
#include <pthread.h>
#include <thread>
#include "Template.h"

namespace cross
{
    // posix platform thread
    class PlatformThread
    {
        struct ThreadState
        {
            ThreadState(ThreadParams const& params, ThreadFunctionPtr&& funcPtr)
                : Params(params)
                , Function(std::move(funcPtr))
            {}
        
            ThreadParams        Params;
            ThreadFunctionPtr   Function;
        };
    
    public:
        // default parameters
        static constexpr UInt32 DefaultStackSize() noexcept { return 0; }
        static constexpr ThreadPriority DefaultThreadPriority() noexcept { return ThreadPriority::Normal; }
        static constexpr ThreadAffinity DefaultThreadAffinity() noexcept { return ThreadAffinity::AllCore; }
        static std::string DefaultThreadName() { return ""; }
        static ThreadParams DefaultThreadParams() noexcept
        {
            return { DefaultThreadName(), DefaultStackSize(), DefaultThreadPriority(), DefaultThreadAffinity() };
        }
        
        static ThreadParams DefaultThreadParamsWithName(std::string name)
        {
            return { std::move(name), DefaultStackSize(), DefaultThreadPriority(), DefaultThreadAffinity() };
        }
        
        // default thread data
        static constexpr void* InvalidHandle() noexcept { return nullptr; }
        static constexpr ThreadID InvalidID() noexcept { return { 0 }; }
        
        // this thread interface
        static ThreadID GetCurrentThreadID()
        {
            pthread_t currentThread = pthread_self();
            return ThreadID::From(currentThread);
        }

        // TODO ... using system clock and duration
        static void SleepThisFor(MillionSecondCount const& duration)
        {
            // TODO using nanosleep
            auto microSecondCount = time::Cast<std::chrono::microseconds>(duration);
            ::usleep(static_cast<int>(microSecondCount.count()));
        }

        static void YieldThis()
        {
            sched_yield();
        }

    public:
        PlatformThread(ThreadParams const& params, ThreadFunctionPtr threadFunction)
            : mParams(params)
            , mNativeHandle(CreateThread(mParams, std::move(threadFunction)))
        {
        }

        // explicit destructors disable move-assignable semantics
        ~PlatformThread() = default;
        
        // disable copy-assignable semantics
        PlatformThread(PlatformThread const&) = delete;
        PlatformThread& operator=(PlatformThread const&) = delete;
        
    public:
        bool Join()
        {
            pthread_join(mNativeHandle, nullptr);
            mNativeHandle = 0;
            return true;
        }
        
        bool Detach()
        {
            pthread_detach(mNativeHandle);
            mNativeHandle = 0;
            return true;
        }
        
    public:
        auto GetStackSize() const noexcept
        {
            return mParams.StackSize;
        }
        
        auto GetPriority() const noexcept
        {
            return mParams.Priority;
        }
        
        auto GetAffinity() const noexcept
        {
            return mParams.Affinity;
        }
        
        auto const& GetThreadName() const noexcept
        {
            return mParams.Name;
        }
        
        void* GetNativeHandle() const noexcept
        {
            return reinterpret_cast<void*>(mNativeHandle);
        }
        
        ThreadID GetThreadID() const noexcept
        {
            return ThreadID::From(mNativeHandle);
        }

    private:
        static pthread_t CreateThread(ThreadParams& params, ThreadFunctionPtr threadFunction)
        {
            pthread_attr_t* threadAttribute = nullptr;
            // TODO ... using preprocessors to eliminate codes
            TScopeGuard<std::function<void(void)>> guard
            { 
                [&threadAttribute]
                {
                    if(!threadAttribute)
                    {
                        pthread_attr_destroy(threadAttribute);
                    }
                }
            };

            pthread_attr_t attribute;
            if(params.StackSize != DefaultStackSize())
            {
                // TODO ... error handling for system error
                pthread_attr_init(&attribute);
                auto stackSize = static_cast<size_t>(params.StackSize);
                pthread_attr_setstacksize(&attribute, stackSize);
                threadAttribute = &attribute;
            }

            auto* threadState = new ThreadState{params, std::move(threadFunction)};

            // TODO ... error handling for system error
            pthread_t threadNativehandle;
            pthread_create(&threadNativehandle, threadAttribute, &ThreadEntry, (void*)threadState);
#if CROSSENGINE_ANDROID
            if(!params.Name.empty())
            {
                constexpr size_t MaxPthreadNameSize = 16;
                if(params.Name.size() > MaxPthreadNameSize)
                {
                    params.Name.resize(MaxPthreadNameSize);
                    params.Name.back() = '\0';
                }
                pthread_setname_np(threadNativehandle, params.Name.c_str());
            }
#endif
            return threadNativehandle;
        }
                           
        static void* ThreadEntry(void* data)
        {
            auto* threadState{ static_cast<ThreadState*>(data) };
#if CROSSENGINE_IOS || CROSSENGINE_OSX
            if(!threadState->Params.Name.empty())
            {
                pthread_setname_np(threadState->Params.Name.c_str());
            }
#endif
            (*threadState->Function)();
            delete static_cast<ThreadState*>(data);
            pthread_exit(nullptr);
        }
        
    private:
        ThreadParams        mParams;
        pthread_t           mNativeHandle = 0;
    };
}
