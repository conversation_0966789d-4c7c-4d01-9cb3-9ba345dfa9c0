#pragma once

#if CROSSENGINE_WIN && defined(CROSS_BASE_DYNAMIC)
#pragma warning(disable : 4251)
  #ifdef CrossBase_EXPORTS
    #define CROSS_BASE_API __declspec(dllexport)
  #else
    #define CROSS_BASE_API __declspec(dllimport)
  #endif
#else
  #ifdef __GNUC__
    #define CROSS_BASE_API __attribute__((visibility("default")))
  #else
    #define CROSS_BASE_API
  #endif
#endif

#include "PlatformDefs.h"