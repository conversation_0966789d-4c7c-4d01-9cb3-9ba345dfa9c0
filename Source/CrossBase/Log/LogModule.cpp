#include "LogModule.h"
#include "spdlog/async.h"
#include "FileSystem/PathHelper.h"
#include "TimeDef.h"
namespace cross {

LogModule& LogModule::Instance()
{
    static LogModule instance;
    return instance;
}
constexpr size_t ASYNC_LOG_QUEUE_SIZE = spdlog::details::default_async_q_size;
LogModule::LogModule()
    : mAsyncLoggerThreadPool(std::make_shared<spdlog::details::thread_pool>(ASYNC_LOG_QUEUE_SIZE, 1))
{}
std::string LogModule::GetLogOutFilePath()
{
#if CROSSENGINE_WIN || CROSSENGINE_OSX
    auto project_path = PathHelper::GetCurrentDirectoryPath();
    auto log_path = project_path + "/logs";
    if (!PathHelper::IsDirectoryExist(log_path))
    {
        PathHelper::MakeDirectory(log_path);
    }
    std::string fileName = log_path + "/CROSSENGINE_" + cross::time::TimeStamp() + ".log";
#elif CROSSENGINE_IOS
    const std::string& documentPath = PathHelper::GetDocumentDirectoryPath();
    std::string fileName = documentPath + "CROSSENGINE_" + cross::time::TimeStamp() + ".log";
#elif CROSSENGINE_ANDROID
    const std::string& documentPath = PathHelper::GetDocumentDirectoryPath();
    std::string fileName = documentPath + "log/CROSSENGINE_" + cross::time::TimeStamp() + ".log";
#endif
    return fileName;
}

void LogModule::Init(bool write_to_file, bool enable_udp, bool isEditor)
{
    mDefaultLogger.reset(new Logger(mAsyncLoggerThreadPool));
    if (write_to_file)
    {
        mDefaultLogger->SetLogFile(GetLogOutFilePath().c_str());
    }
    mDefaultLogger->SetUdpLogger(enable_udp);
    mLogToEditor = isEditor;

}
void LogModuleUtil::LogModule_Log(int level, const char* file, const char* func, int line, const char* message)
{
    using namespace cross;
    LogModule::Instance().Log(LogModule::LogMode::eGame, Logger::LogLevel{level}, SourceLoc{file, func, line}, "{}", message);
}
void LogModule::Init(bool isEditor, Logger::USER_LOGGER userLoggerFunc, void* userData, bool write_to_file, bool enable_udp)
    {
    // init mDefaultLogger;
    Init(write_to_file, enable_udp);

    mEditorLogger.reset(new Logger(mAsyncLoggerThreadPool));
    mEditorLogger->SetUserLogger(userLoggerFunc, userData);
    mEditorLogger->SetConsoleOutput(false);
    mLogToEditor = isEditor;
}
// reset mDefaultLogger and destroy mEditorLogger
void LogModule::CreateNewLogFile()
{
    mDefaultLogger->SetLogFile(GetLogOutFilePath().c_str());
    mDefaultLogger->ResetLogger();

    if (mEditorLogger)
        mEditorLogger->ResetLogger();
}

void LogModule::Shutdown()
{
    mDefaultLogger.reset();
    mEditorLogger.reset();
}

void LogModule::Flush()
{
    if (mDefaultLogger)
        mDefaultLogger->Flush();
    if (mEditorLogger)
        mEditorLogger->Flush();
}

}   // namespace cross
