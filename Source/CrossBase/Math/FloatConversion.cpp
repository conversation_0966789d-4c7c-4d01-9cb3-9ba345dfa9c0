#include "PCH/CrossBasePCHPrivate.h"

namespace cross
{
    void HalfToFloat(UInt16 src, float& dest)
    {
        // Integer alias
        UInt32& bits = *reinterpret_cast<UInt32*>(&dest);

        // Based on <PERSON>'s public domain half_to_float_fast3
        static const UInt32 magic = { 113 << 23 };
        const float& magicFloat = *reinterpret_cast<const float*>(&magic);
        static const UInt32 shiftedExp = 0x7c00 << 13; // exponent mask after shift

        // Mask out sign bit
        bits = src & 0x7fff;
        if (bits)
        {
            // Move exponent + mantissa to correct bits
            bits <<= 13;
            UInt32 exponent = bits & shiftedExp;
            if (exponent == 0)
            {
                // Handle denormal
                bits += magic;
                dest -= magicFloat;
            }
            else if (exponent == shiftedExp) // Inf/NaN
                bits += (255 - 31) << 23;
            else
                bits += (127 - 15) << 23;
        }

        // Copy sign bit
        bits |= (src & 0x8000) << 16;
    }

    void FloatToHalfConverter::InitializePrecomputeTables()
    {
        for (int i = 0; i < 256; i++)
        {
            int e = i - 127;
            if (e < -24)
            {
                // Too small to represent becomes zero
                m_ExponentTable[i] = 0x0000;
                m_MantissaShift[i] = 24;
            }
            else if (e < -14)
            {
                // Small numbers become denormals
                m_ExponentTable[i] = 0x0400 >> (-14 - e);
                m_MantissaShift[i] = UInt8(-1 - e);
            }
            else if (e < 16)
            {
                // Handle normalized numbers
                m_ExponentTable[i] = UInt16(15 + e) << 10;
                m_MantissaShift[i] = 13;
            }
            else if (e < 128)
            {
                // Large numbers become infinity
                m_ExponentTable[i] = 0x7C00;
                m_MantissaShift[i] = 24;
            }
            else
            {
                // Handle infinity and NaN
                m_ExponentTable[i] = 0x7C00;
                m_MantissaShift[i] = 13;
            }
        }

        m_PrecomputeTablesInited = true;
    }
}
