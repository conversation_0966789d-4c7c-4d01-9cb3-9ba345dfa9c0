#include "EnginePrefix.h"
#include "NodeTransformSIMD.h"

#if ENABLE_SIMD_NODE_TRANSFORM
namespace cross {
void NodeTransform::NormalizeRotation()
{
    Rotation = MathSIMD::Vector4Normalize(Rotation);
}
NodeTransform NodeTransform::Inverse() const
{
    const SIMDVector4 invScale = MathSIMD::VectorSetW0(MathSIMD::VectorReciprocalSafe(Scale));
    const SIMDVector4 invRot = MathSIMD::QuaternionInverse(Rotation);

    const SIMDVector4 scaleTrans = MathSIMD::VectorMultiply(invScale, Translation);
    const SIMDVector4 rotTrans = MathSIMD::Vector3Rotate(scaleTrans, invRot);
    const SIMDVector4 invTranslation = MathSIMD::VectorSetW0(MathSIMD::VectorNegate(rotTrans));

    return {invScale, invRot, invTranslation};
}

Float3A NodeTransform::InverseTransformFloat3A(const Float3A& inPosition)
{
    /*
    result = (R^-1 * (InPosition - T)) / (S);
    */
    const SIMDVector4 InPosVec = SIMD::LoadFloat3A(&inPosition);

    const SIMDVector4 ScaleRcp = MathSIMD::VectorReciprocalSafe(Scale);
    const SIMDVector4 invRot = MathSIMD::QuaternionInverse(Rotation);

    SIMDVector4 TranslatedVec = MathSIMD::VectorSubtract(InPosVec, Translation);
    TranslatedVec = MathSIMD::Vector3Rotate(TranslatedVec, invRot);
    TranslatedVec = MathSIMD::VectorMultiply(TranslatedVec, ScaleRcp);

    Float3A Result;
    SIMD::StoreFloat3A(&Result, TranslatedVec);
    return Result;
}

Float3A NodeTransform::InverseTransformFloat3ANoScale(const Float3A& inPosition) const
{
    /*
        result = (R^-1 * (InPosition - T));
    */
    const SIMDVector4 InPosVec = SIMD::LoadFloat3A(&inPosition);

    const SIMDVector4 invRot = MathSIMD::QuaternionInverse(Rotation);

    SIMDVector4 TranslatedVec = MathSIMD::VectorSubtract(InPosVec, Translation);
    TranslatedVec = MathSIMD::Vector3Rotate(TranslatedVec, invRot);


    Float3A Result;
    SIMD::StoreFloat3A(&Result, TranslatedVec);
    return Result;
}

Float3A NodeTransform::InverseTransformVectorNoScale(const Float3A& inVector) const
{
    /*
        result = (R^-1 * (InV);
    */
    const SIMDVector4 InPosVec = SIMD::LoadFloat3A(&inVector);

    const SIMDVector4 invRot = MathSIMD::QuaternionInverse(Rotation);


    auto TranslatedVec = MathSIMD::Vector3Rotate(InPosVec, invRot);


    Float3A Result;
    SIMD::StoreFloat3A(&Result, TranslatedVec);
    return Result;
}


Float3A NodeTransform::InverseTransformVector(const Float3A& inVector) const
{
    /*
        result = (R^-1 * (InV) *(1./Scale);
    */
    const SIMDVector4 InPosVec = SIMD::LoadFloat3A(&inVector);
    const SIMDVector4 invRot = MathSIMD::QuaternionInverse(Rotation);


    auto TranslatedVec = MathSIMD::Vector3Rotate(InPosVec, invRot);

    const SIMDVector4 ScaleReciprocal = MathSIMD::VectorReciprocalSafe(Scale);

    TranslatedVec = MathSIMD::VectorMultiply(TranslatedVec, ScaleReciprocal);

    Float3A Result;
    SIMD::StoreFloat3A(&Result, TranslatedVec);
    return Result;
}


Float3A NodeTransform::TransformFloat3A(const Float3A& inPosition)
{
    /*

    result = R * S * InPosition + T;

    */
    const SIMDVector4 InPosVec = SIMD::LoadFloat3A(&inPosition);

    const SIMDVector4 PosMulScale = MathSIMD::VectorMultiply(InPosVec, Scale);
    const SIMDVector4 PosRot = MathSIMD::Vector3Rotate(PosMulScale, Rotation);
    const SIMDVector4 TranslatedVec = MathSIMD::VectorAdd(PosRot, Translation);

    Float3A Result;
    SIMD::StoreFloat3A(&Result, TranslatedVec);
    return Result;
}

// qua1 and qua2 are quaternions.
// The result is qua1 + (|qua1.qua2| >= 0 ? 1 : -1) * qua2 * blendWeight
SIMDVector4 NodeTransform::AccumulateQuaternionShortestPath(const SIMDVector4& qua1, const SIMDVector4& qua2, const SIMDVector4& weightVec)
{
    const SIMDVector4 BlendRot = MathSIMD::VectorMultiply(qua2, weightVec);
    const SIMDVector4 RotDot = MathSIMD::Vector4Dot(qua1, BlendRot);
    const SIMDVector4 RotMask = MathSIMD::VectorGreaterOrEqual(RotDot, MathSIMD::VectorZero());
    const SIMDVector4 NegativeBlendRot = MathSIMD::VectorSubtract(MathSIMD::VectorZero(), BlendRot);
    const SIMDVector4 SelectedRot = MathSIMD::VectorSelect(NegativeBlendRot, BlendRot, RotMask);

    return MathSIMD::VectorAdd(qua1, SelectedRot);
}

// Accumulates another transform with this one, with an optional blending weight
// Rotation is accumulated additively, in the shortest direction ( Result.Rotation = transform1.Rotation +/- transform2.Rotation * weight)
// Translation is accumulated additively ( Result.Translation = transform1.Translation + transform2.Translation * weight)
// Scale is accumulated additively ( Result.Scale = transform1.Scale + transform2.Scale * weight)
//
NodeTransform NodeTransform::AccumulateByShortestRotation(const NodeTransform& transform1, const NodeTransform& transform2, float blendWeight)
{
    const SIMDVector4 WeightVector = MathSIMD::VectorReplicate(blendWeight);

    const SIMDVector4 Scale = MathSIMD::VectorMultiplyAdd(transform2.Scale, WeightVector, transform1.Scale);
    const SIMDVector4 Rot = AccumulateQuaternionShortestPath(transform1.Rotation, transform2.Rotation, WeightVector);
    const SIMDVector4 Trans = MathSIMD::VectorMultiplyAdd(transform2.Translation, WeightVector, transform1.Translation);

    return {Scale, Rot, Trans};
}

NodeTransform NodeTransform::Blend(const NodeTransform& transform1, const NodeTransform& transform2, float blendWeight)
{
    const SIMDVector4 WeightVector = MathSIMD::VectorReplicate(blendWeight);

    const SIMDVector4 Scale = MathSIMD::VectorLerpV(transform1.Scale, transform2.Scale, WeightVector);
    const SIMDVector4 Trans = MathSIMD::VectorLerpV(transform1.Translation, transform2.Translation, WeightVector);
    SIMDVector4 Rot = MathSIMD::QuaternionSlerpV(transform1.Rotation, transform2.Rotation, WeightVector);

    Rot = MathSIMD::QuaternionNormalize(Rot);

    return {Scale, Rot, Trans};
}

NodeTransform NodeTransform::GetRelativeTransform(const NodeTransform& parentWorld, const NodeTransform& childWorld)
{
    /*
    result S = S_c / S_p;
    result R =  R_c * inv(R_p);
    result T = ((R_p) * (T_c - T_p) * invR_p) / S_p;
    */

    const SIMDVector4 ScaleRcp = MathSIMD::VectorReciprocalSafe(parentWorld.Scale);
    const SIMDVector4 RotInv = MathSIMD::QuaternionInverse(parentWorld.Rotation);
    const SIMDVector4 DeltaT = MathSIMD::VectorSubtract(childWorld.Translation, parentWorld.Translation);

    return NodeTransform(MathSIMD::VectorMultiply(ScaleRcp, childWorld.Scale), MathSIMD::QuaternionMultiply(childWorld.Rotation, RotInv), MathSIMD::VectorMultiply(ScaleRcp, MathSIMD::Vector3Rotate(DeltaT, RotInv)));
}

NodeTransform NodeTransform::GetRelativeTransformR(const NodeTransform& childLocal, const NodeTransform& childWorld)
{
    /*
    result S = S_w / S_l;
    result R =  inv(R_l) * R_w ;
    result T = T_w - T_l * result_S * result_R
    */

    const SIMDVector4 ScaleRcp = MathSIMD::VectorReciprocalSafe(childLocal.Scale);
    const SIMDVector4 RotInv = MathSIMD::QuaternionInverse(childLocal.Rotation);
    const SIMDVector4 DeltaT = MathSIMD::VectorSubtract(childWorld.Translation, childLocal.Translation);

    const SIMDVector4 ScaleDelta = MathSIMD::VectorMultiply(ScaleRcp, childWorld.Scale);
    const SIMDVector4 RotDelta = MathSIMD::QuaternionMultiply(RotInv, childWorld.Rotation);
    const SIMDVector4 TransScaleRot = MathSIMD::VectorMultiply(ScaleDelta, MathSIMD::Vector3Rotate(childLocal.Translation, RotDelta));
    const SIMDVector4 TransDelta = MathSIMD::VectorSubtract(childWorld.Translation, TransScaleRot);
    return {ScaleDelta, RotDelta, TransDelta};
}
/*
    return the concatenation of a NodeTransform a followed by the NodeTransform b
*/
NodeTransform operator*(const NodeTransform& a, const NodeTransform& b)
{
    const SIMDVector4 Scale = MathSIMD::VectorMultiply(a.Scale, b.Scale);
    const SIMDVector4 Rot = MathSIMD::QuaternionMultiply(a.Rotation, b.Rotation);
    const SIMDVector4 Trans = MathSIMD::VectorMultiplyAdd(b.Scale, MathSIMD::Vector3Rotate(a.Translation, b.Rotation), b.Translation);

    return {Scale, Rot, Trans};
}

void NodeTransform::GetTransformMatrix(SIMDMatrix& Res) const
{
    Res = MathSIMD::MatrixScalingFromVector(Scale);
    Res = MathSIMD::MatrixMultiply(Res, MathSIMD::MatrixRotationQuaternion(Rotation));
    Res = MathSIMD::MatrixMultiply(Res, MathSIMD::MatrixTranslationFromVector(Translation));
}

void NodeTransform::GetTransformMatrix(Float4x4A& outMat) const
{
    SIMDMatrix Res = MathSIMD::MatrixScalingFromVector(Scale);
    Res = MathSIMD::MatrixMultiply(Res, MathSIMD::MatrixRotationQuaternion(Rotation));
    Res = MathSIMD::MatrixMultiply(Res, MathSIMD::MatrixTranslationFromVector(Translation));
    SIMD::StoreFloat4x4A(&outMat, Res);
}

//
Float4x4A NodeTransform::GetTransformMatrix() const
{
    Float4x4A OutMat;
    GetTransformMatrix(OutMat);
    return OutMat;
}

NodeTransform operator*(const NodeTransform& a, const float b)
{
    return NodeTransform(MathSIMD::VectorScale(a.Scale, b), MathSIMD::VectorScale(a.Rotation, b), MathSIMD::VectorScale(a.Translation, b));
}

}   // namespace cross
#endif