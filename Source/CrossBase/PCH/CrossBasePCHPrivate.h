#pragma once

#ifdef _MSC_VER
// Visual Studio
#include "PCH/VisualStudioPrefix.h"
#elif defined(ANDROID) || defined(__ANDROID__)
#include "PCH/AndroidPrefix.h"
#elif defined(linux) || defined(__linux__)
#include "PCH/LinuxPrefix.h"
#elif defined(__APPLE__)
#include "PCH/OSXPrefix.h"
#else
// TODO
#endif

// standard libraries
#include <cstddef>
#include <cstdlib>
#include <cstdint>
#include <cassert>
#include <cmath>
#include <type_traits>
#include <algorithm>
#include <vector>
#include <deque>
#include <map>
#include <set>
#include <unordered_map>
#include <unordered_set>
#include <array>
#include <stack>
#include <limits>
#include <list>
#include <cstring>
#include <string>
#include <iterator>
#include <iostream>
#include <functional>
#include <cassert>
#include <atomic>
#include <bitset>
#include <numeric>
#include <string_view>
#include <chrono>
#include <optional>
#include <variant>

// platform macro definition
#include "PlatformDefs.h"

// common headers
#include "CrossBaseForward.h"
#include "CrossUtilities.h"
#include "Annotations.h"
#include "Containers/HashMap/HashMap.hpp"
#include "TimeDef.h"
#include "FileSystem/PathHelper.h"
#include "Log.h"
#include "Format.h"
#include "MathLib.h"
#include "uuid/CrossUUID.h"
#include "BitWise.h"

#include "CrossBase/CEMetaMacros.h"