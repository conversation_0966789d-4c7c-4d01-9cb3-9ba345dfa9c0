#include "GPUProfiling.h"
#include "tracy/Tracy.hpp"
#include "Threading/TaskSystem.h"
#include "Profiling.h"
#include "Log.h"
using namespace tracy;

namespace cross {

#if TRACY_ENABLE

inline uint8_t CreateTracyContext(GPUProfiling::GPUProfilingContextInfo startUp)
{
    uint8_t tracyCtx = GetGpuCtxCounter().fetch_add(1, std::memory_order_relaxed);
    uint8_t flags = 0;
    if (startUp.calibrationType == GPUProfiling::CalibrationType::ePerformanceCounter)
    {
        flags |= GpuContextCalibration;
    }
    TracyLfqPrepareC(tracy::QueueType::GpuNewContext);
    MemWrite(&item->gpuNewContext.cpuTime, startUp.gpuCalibration.cpuTimeReference);
    MemWrite(&item->gpuNewContext.gpuTime, startUp.gpuCalibration.gpuTimeReference);
    memset(&item->gpuNewContext.thread, 0, sizeof(item->gpuNewContext.thread));
    MemWrite(&item->gpuNewContext.period, startUp.timestampPeriod);
    MemWrite(&item->gpuNewContext.context, tracyCtx);
    MemWrite(&item->gpuNewContext.flags, flags);
    MemWrite(&item->gpuNewContext.type, GpuContextType::Vulkan);
#ifdef TRACY_ON_DEMAND
    GetProfiler().DeferItem(*item);
#endif
    TracyLfqCommitC;
    return tracyCtx;
}

inline void TracyRecordCPUBegin(const GPUProfiling::GPUProfilingItem& stamp, uint8_t tracyCtx)
{
    const auto srcloc = Profiler::AllocSourceLocation(__LINE__, __FILE__, strlen(__FILE__), __FUNCTION__, strlen(__FUNCTION__), stamp.mEventName.c_str(), stamp.mEventName.length());

    TracyLfqPrepareC(tracy::QueueType::GpuZoneBeginAllocSrcLoc);
    MemWrite(&item->gpuZoneBegin.cpuTime, stamp.mCPUTimestamp);
    MemWrite(&item->gpuZoneBegin.srcloc, (uint64_t)srcloc);
    MemWrite(&item->gpuZoneBegin.thread, GetThreadHandle());
    MemWrite(&item->gpuZoneBegin.queryId, (uint16_t)stamp.mQueryId);
    MemWrite(&item->gpuZoneBegin.context, tracyCtx);
    TracyLfqCommitC;
}

inline void TracyRecordCPUEnd(const GPUProfiling::GPUProfilingItem& stamp, uint8_t tracyCtx)
{
    TracyLfqPrepareC(tracy::QueueType::GpuZoneEnd);
    MemWrite(&item->gpuZoneEnd.cpuTime, stamp.mCPUTimestamp);
    MemWrite(&item->gpuZoneEnd.thread, GetThreadHandle());
    MemWrite(&item->gpuZoneEnd.queryId, (uint16_t)stamp.mQueryId);
    MemWrite(&item->gpuZoneEnd.context, tracyCtx);
    TracyLfqCommitC;
}

inline void TracyRecordGPU(const GPUProfiling::GPUProfilingItem& stamp, uint8_t tracyCtx)
{
    TracyLfqPrepareC(tracy::QueueType::GpuTime);
    MemWrite(&item->gpuTime.gpuTime, stamp.mGPUTimestamp);
    MemWrite(&item->gpuTime.queryId, (uint16_t)stamp.mQueryId);
    MemWrite(&item->gpuTime.context, tracyCtx);
    TracyLfqCommitC;
}

inline void TracyCalibration(GPUProfiling::GPUProfilingContextInfo startUp, uint8_t tracyCtx)
{
    TracyLfqPrepareC(tracy::QueueType::GpuCalibration);
    MemWrite(&item->gpuCalibration.gpuTime, startUp.gpuCalibration.gpuTimeReference);
    MemWrite(&item->gpuCalibration.cpuTime, startUp.gpuCalibration.cpuTimeReference);
    MemWrite(&item->gpuCalibration.cpuDelta, startUp.gpuCalibration.cpuDelta);
    MemWrite(&item->gpuCalibration.context, tracyCtx);
    TracyLfqCommitC;
}

GPUProfiling& GPUProfiling::GetInstance()
{
    static GPUProfiling Instance;
    return Instance;
}

void GPUProfiling::PushGPUProfilingTimestamp(GPUProfilingContextInfo startUp, std::span<GPUProfilingItem> items)
{
    SCOPED_CPU_TIMING(GPUProfiling, "PushGPUProfilingTimestamp");
    Assert(threading::TaskSystem::IsInRenderingThread());

    if (!GetProfiler().IsConnected())
    {
        return;
    }

    auto it = mTracyContextMap.find(startUp.vkQueue);
    uint8_t tracyCtx = 0xFF;
    if (it == mTracyContextMap.end())
    {
        tracyCtx = CreateTracyContext(startUp);
        mTracyContextMap.emplace(startUp.vkQueue, tracyCtx);
    }
    else
    {
        tracyCtx = it->second;
    }

    Assert(tracyCtx != 0xFF);

    for (auto& item : items)
    {
        switch (item.mType)
        {
        case GPUProfilingType::eBegin:
            TracyRecordCPUBegin(item, tracyCtx);
            break;
        case GPUProfilingType::eEnd:
            TracyRecordCPUEnd(item, tracyCtx);
            break;
        }
    }

    for (auto& item : items)
    {
        TracyRecordGPU(item, tracyCtx);
    }

    if (startUp.calibrationType == CalibrationType::ePerformanceCounter && startUp.gpuCalibration.cpuDelta > 0)
    {
        TracyCalibration(startUp, tracyCtx);
    }
}

#else

GPUProfiling& GPUProfiling::GetInstance()
{
    static GPUProfiling Instance;
    return Instance;
}

void GPUProfiling::PushGPUProfilingTimestamp(GPUProfilingContextInfo startUp, std::span<GPUProfilingItem> items) {}
#endif
}   // namespace cross
