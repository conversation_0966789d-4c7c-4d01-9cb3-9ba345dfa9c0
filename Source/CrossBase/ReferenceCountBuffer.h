
#pragma once
#include "CrossBase/ReferenceCountObject.h"
#include "CrossBase/NSharedPtr.h"

namespace cross
{

class ReferenceCountBuffer : public ReferenceCountObject
{
public:
	static RenderTargetReferenceCountBufferPtr CreateInstance() 
	{
		return RenderTargetReferenceCountBufferPtr(new ReferenceCountBuffer());
	}

	virtual ~ReferenceCountBuffer() { OnZeroReference(); }

	void Reallocate(uint32_t sizeInByte)
	{
		if (mSizeInByte < sizeInByte)
		{
			mSizeInByte = sizeInByte;
			if (mBuffer)
			{
				delete[] mBuffer;
			}
			mBuffer = new uint8_t[mSizeInByte];
		}
	}

protected:

	virtual void OnZeroReference() override
	{
		if (mBuffer)
		{
			delete[] mBuffer;
			mBuffer = nullptr;
			mSizeInByte = 0;
		}
	}

private:
	ReferenceCountBuffer() = default;

	void* mBuffer{ nullptr };
	uint32_t mSizeInByte{ 0 };
};

using RenderTargetReferenceCountBufferPtr = NSharedPtr<ReferenceCountBuffer>;

}
