#pragma once
#include "CECommon/Utilities/NDACommon.h"
#include <vector>
#include "CrossBase/PlatformDefs.h"
#include "CrossBase/CEMetaMacros.h"
#include "CrossBase/uuid/CrossUUID.h"

namespace cross
{

enum class CONTENT_TYPE
{
    CONTENT_TYPE_SIMPLE = 0,
    CONTENT_TYPE_FLATBUFFER,
    CONTENT_TYPE_JSON,
    CONTENT_TYPE_BSON
};

#pragma pack (push, 1)
    struct CEMeta(Editor) ResourceMetaHeader
    {
        // fixed header
        CEProperty(Editor)
        SInt32      mMagicNumber = ASSET_MAGIC_NUMBER;
        CEProperty(Editor)
        SInt32      mVersion = 0x4;
        UInt64      mGuidL = 0;
        UInt64      mGuidH = 0;
        CEProperty(Editor)
        SInt32      mClassID = -1;
        CEProperty(Editor)
        SInt32      mDataSize = 0;
        CEProperty(Editor)
        UInt32      mContentType = 0;
        CEProperty(Editor)
        SInt32      mJsonStringLength = 0;
        CEProperty(Editor)
        std::string mGuid;
        // mutative header
        std::string mCustomInfo;
        std::string mImportSet;
        CEProperty(Editor)
        std::vector<std::string> mDependency;
        bool        mIsStreamFile = false;
    };
#pragma pack (pop)

constexpr int BINARY_HEADER_SIZE = sizeof(ResourceMetaHeader::mMagicNumber) +
                                        sizeof(ResourceMetaHeader::mVersion) +
                                        sizeof(ResourceMetaHeader::mGuidL) +
                                        sizeof(ResourceMetaHeader::mGuidH) +
                                        sizeof(ResourceMetaHeader::mClassID) +
                                        sizeof(ResourceMetaHeader::mDataSize) +
                                        sizeof(ResourceMetaHeader::mContentType) +
                                        sizeof(ResourceMetaHeader::mJsonStringLength);

static_assert(BINARY_HEADER_SIZE == 40);

constexpr int FixedHeaderSize = 172;
}
