#pragma once

#include <cassert>
#include <vector>
#include <limits>
#include <flatbuffers/reflection.h>
#include "Template/StaticReflection.hpp"
#include "stduuid/gsl/span"

// Temporary, to be replaced
template<typename T>
class Span
{
    T* mPtr{};
    UInt64 mSize{};

public:
    Span() = default;
    Span(T* ptr, UInt64 len) noexcept
        : mPtr{ptr}
        , mSize{len}
    {}

    T& operator[](int i) noexcept
    {
        return mPtr[i];
    }
    T const& operator[](int i) const noexcept
    {
        return mPtr[i];
    }
    auto size() const noexcept
    {
        return mSize;
    }

    const auto data() const noexcept
    {
        return mPtr;
    }
    auto data() noexcept
    {
        return mPtr;
    }

    auto begin() noexcept
    {
        return mPtr;
    }
    auto end() noexcept
    {
        return mPtr + mSize;
    }

    const auto begin() const noexcept
    {
        return mPtr;
    }
    const auto end() const noexcept
    {
        return mPtr + mSize;
    }
};

namespace cross {
struct FlatBufferCodec;
/// Flat buffer traits
template<typename T, typename = void>
struct TIsFBReflected : std::false_type
{};
template<typename T>
struct TIsFBReflected<T, std::void_t<decltype(std::declval<T const&>().MiniReflectTypeTable())>> : std::true_type
{};
template<typename T>
inline static constexpr bool TIsFBReflectedV = TIsFBReflected<T>::value;
template<typename T>
inline static constexpr bool TIsFBTableV = std::is_base_of_v<flatbuffers::Table, T>;

template<typename T>
inline static constexpr bool TIsSimpleFBStructV = !TIsFBTableV<T> && TIsFBReflectedV<T>;

class Archive;

template<typename T, typename = void>
struct TIsCodec : std::false_type
{};
template<typename T>
struct TIsCodec<T, std::void_t<typename T::Writer, typename T::Reader>> : std::true_type
{};
template<typename T>
inline constexpr bool TIsCodecV = TIsCodec<T>::value;

template<typename Cont>
inline auto CheckContainerSize(Cont const& cont, UInt64 arrLength) noexcept -> std::enable_if_t<TIsContainerV<Cont>, bool>
{
    return arrLength <= cont.max_size();
}

template<typename Codec>
class TSerializer
{
public:
    using CodecType = Codec;
    static_assert(TIsCodecV<CodecType>);
    using ThisType = TSerializer<CodecType>;
    using WriterType = typename CodecType::Writer;
    using ReaderType = typename CodecType::Reader;

    template<typename T, typename = void>
    struct TIsCodecWritable : std::false_type
    {};
    template<typename T>
    struct TIsCodecWritable<T, std::void_t<decltype(std::declval<WriterType&>().Write(std::declval<Archive&>(), std::declval<T>()))>> : std::true_type
    {};

    template<typename T>
    inline static constexpr bool TIsCodecWritableV = TIsCodecWritable<T>::value;

    template<typename T, typename = void>
    struct TIsCodecReadable : std::false_type
    {};
    template<typename T>
    struct TIsCodecReadable<T, std::void_t<decltype(std::declval<ReaderType const&>().Read(std::declval<Archive const&>(), std::declval<T&>()))>> : std::true_type
    {};
    template<typename T>
    inline static constexpr bool TIsCodecReadableV = TIsCodecReadable<T>::value;

    template<typename T, typename = void>
    struct THasCustomSerialization : std::false_type
    {};
    template<typename T>
    struct THasCustomSerialization<T, std::void_t<decltype(std::declval<T const&>().Serialize(std::declval<ThisType&>()))>> : std::true_type
    {};
    template<typename T>
    inline static constexpr bool THasCustomSerializationV = THasCustomSerialization<T>::value;

    template<typename T, typename = void>
    struct THasCustomDeserialization : std::false_type
    {};
    template<typename T>
    struct THasCustomDeserialization<T, std::void_t<decltype(std::declval<T&>().Deserialize(std::declval<ThisType const&>()))>> : std::true_type
    {};
    template<typename T>
    inline static constexpr bool THasCustomDeserializationV = THasCustomDeserialization<T>::value;

    template<typename T, typename = void>
    struct THasFBCustomDeserialization : std::false_type
    {};
    template<typename T>
    struct THasFBCustomDeserialization<T, std::void_t<decltype(std::declval<T&>().Deserialize(std::declval<ThisType const&>(), std::declval<const flatbuffers::Table*>()))>> : std::true_type
    {};
    template<typename T>
    inline static constexpr bool THasFBCustomDeserializationV = THasFBCustomDeserialization<T>::value;

public:
    explicit TSerializer(Archive& archive, std::uint64_t offset = 0) noexcept
        : mArchive(archive)
    {
        if constexpr (std::is_same_v<CodecType, FlatBufferCodec>)
        {
            if (mArchive.GetModeReadable())
            {
                mCache = Span<const UInt8>(mArchive.Data(), mArchive.Size());
            }
            mInitialOffset = offset;
        }
    }
    auto InitialOffset() const noexcept
    {
        return mInitialOffset;
    }
    auto& GetCachedBuffer() const noexcept
    {
        return mCache;
    }

    template<typename T>
    void Write(T const& t)
    {
        if constexpr (std::disjunction_v<std::is_pointer<T>, TIsSmartPointer<T>>)
        {
            Assert(t);
            Write(*t);
        }
        else
        {
            WriteImpl(t);
        }
    }

    template<typename... Args>
    auto Write(Args const&... args) -> std::enable_if_t<(sizeof...(Args) > 1)>
    {
        (Write(args), ...);
    }

    template<typename T>
    T Read() const
    {
        T t{};
        if (!Read(t))
            throw std::runtime_error{"Failed to deserailze."};
        return t;
    }

    template<typename T>
    bool Read(T& t) const
    {
        if constexpr (std::disjunction_v<std::is_pointer<T>, TIsSmartPointer<T>>)
        {
            Assert(t);
            return Read(*t);
        }
        else
        {
            return ReadImpl(t);
        }
    }

    template<typename T>
    bool Read(const flatbuffers::Table* table, T& t, uint16_t offset) const
    {
        if constexpr (std::disjunction_v<std::is_pointer<T>, TIsSmartPointer<T>>)
        {
            Assert(t);
            return Read(table, *t, offset);
        }
        else
        {
            return ReadImpl(table, t, offset);
        }
    }

    template<typename T>
    bool Read(const flatbuffers::Table* table, gsl::span<T>& t, uint16_t offset) const
    {
        return ReadImpl(table, t, offset);
    }

    bool ReadArray(void* data, std::uint64_t size) const
    {
        return mReader.ReadArray(mArchive, data, size) > 0;
    }

    bool WriteArray(void* data, std::uint64_t size)
    {
        return mWriter.WriteArray(mArchive, data, size) > 0;
    }

    template<typename... Args>
    auto Read(Args&... args) const -> std::enable_if_t<(sizeof...(Args) > 1), bool>
    {
        if (((!(Read(args))) || ...))
            return false;
        return true;
    };

    auto& GetArchive() const noexcept
    {
        return mArchive;
    }

    template<typename T>
    void WriteImpl(T const& t)
    {
        if constexpr (THasCustomSerializationV<T>)
            t.Serialize(*this);
        else if constexpr (TIsCodecWritableV<T>)
            mWriter.Write(mArchive, t);
        else if constexpr (TIsReflectedV<T>)
            WriteReflected(t);
        else if constexpr (TIsSequentialContainerV<T>)
            WriteSequentialContainer(t);
        else if constexpr (TIsAssociativeContainerV<T>)
            WriteAssociativeContainer(t);
        else
            static_assert(std::disjunction_v<TIsCodecWritable<T>, TIsReflected<T>, THasCustomSerialization<T>, TIsSequentialContainer<T>, TIsAssociativeContainer<T>>, "Type cannot be serialized!");
    }

    template<typename T>
    void WriteReflected(T const& t)
    {
        static_assert(TIsReflectedV<T>);
        ForEach(t, [this](auto& field) { this->Write(field); });
    }

    template<typename T>
    void WriteSequentialContainer(T const& t)
    {
        static_assert(TIsSequentialContainerV<T>);
        UInt64 count = t.size();
        Write(count);
        for (auto const& elem : t)
        {
            Write(elem);
        }
    }

    template<typename T>
    void WriteAssociativeContainer(T const& t)
    {
        static_assert(TIsAssociativeContainerV<T>);
        UInt64 count = t.size();
        Write(count);
        for (auto& [key, value] : t)
        {
            Write(key);
            Write(value);
        }
    }

    template<typename T>
    bool ReadImpl(const flatbuffers::Table* table, T& t, uint16_t offset) const
    {
        if (mArchive.AtEnd())
        {
            return false;
        }
        if constexpr (TIsBoolV<T> || TIsArithmaticV<T> || TIsStdStringV<T> || TIsSimpleFBStructV<T> || std::is_enum_v<T>)   // || std::is_base_of_v<Eigen::PlainObjectBase<T>,T>)
        {
            return mReader.Read(table, t, offset);
        }
        else if constexpr (TIsSequentialContainerV<T>)
        {
            /// FB only support fbvector which is std::vector other sequentialcontainer is not supported
            if constexpr (THasResizeV<T>)
            {
                return mReader.Read(table, t, offset);
            }
            else
            {
                return false;
            }
        }
        else
        {
            static_assert(std::disjunction_v<TIsSequentialContainer<T>> || TIsArithmaticV<T> || TIsStdStringV<T> || TIsFBReflectedV<T>, "Type cannot be deserialized!");
            return false;
        }
    }

    template<typename T>
    bool ReadImpl(const flatbuffers::Table* table, gsl::span<T>& t, uint16_t offset) const
    {
        return mReader.Read(table, t, offset);
    }

    template<typename T>
    bool ReadImpl(T& t) const
    {
        if (mArchive.AtEnd())
        {
            return false;
        }

        if constexpr (THasCustomDeserializationV<T>)
            return t.Deserialize(*this);
        else if constexpr (TIsCodecReadableV<T>)
            return mReader.Read(mArchive, t);
        else if constexpr (TIsReflectedV<T>)
            return ReadReflected(t);
        else if constexpr (TIsSequentialContainerV<T>)
            return ReadSequentialContainer(t);
        else if constexpr (TIsAssociativeContainerV<T>)
            return ReadAssociativeContainer(t);
        else
        {
            static_assert(std::disjunction_v<TIsCodecReadable<T>, TIsReflected<T>, THasCustomDeserialization<T>, TIsSequentialContainer<T>, TIsAssociativeContainer<T>>, "Type cannot be deserialized!");
            return false;
        }
    }

    template<typename T>
    bool ReadReflected(T& t) const
    {
        static_assert(TIsReflectedV<T>);
        // we have to use exception here
        try
        {
            ForEach(t, [this](auto& field) {
                if (!this->ReadImpl(field))
                {
                    std::string message = std::string("Failed to deserailze.").append(typeid(field).name());
                    throw std::runtime_error{message};
                }
            });
            return true;
        }
        // TODO ... to throw an NE deserialization exception
        catch (std::runtime_error const&)
        {
            return false;
        }
    }

    template<typename T>
    bool ReadSequentialContainer(T& t) const
    {
        UInt64 arrLength = 0;
        if (!ReadImpl(arrLength))
            return false;

        if constexpr (THasResizeV<T>)
        {
            if (!CheckContainerSize(t, arrLength))
                return false;
            t.resize(arrLength);
        }
        for (auto loop = 0ull; loop < arrLength; ++loop)
        {
            if (!ReadImpl(t[loop]))
                return false;
        }
        return true;
    }

    template<typename T>
    bool ReadAssociativeContainer(T& t) const
    {
        using KeyType = typename T::key_type;
        using ValueType = typename T::mapped_type;

        UInt64 arrLength = 0;
        if (!ReadImpl(arrLength))
            return false;

        if (!CheckContainerSize(t, arrLength))
            return false;

        KeyType key{};
        ValueType value{};
        for (auto loop = 0ull; loop < arrLength; ++loop)
        {
            if (!ReadImpl(key))
                return false;
            if (!ReadImpl(value))
                return false;

            /*if (!t.emplace(std::move(key), std::move(value)).second)
                return false;*/
            t.emplace(std::move(key), std::move(value));
        }

        return true;
    }

private:
    std::uint64_t mInitialOffset;
    Archive& mArchive;
    Span<const UInt8> mCache;
    WriterType mWriter;
    ReaderType mReader;
};
}   // namespace cross
