#include <EnginePrefix.h>
#include "SerializeNode.h"
#include "memory/allocator/tlsf_alloc_impl.hpp"
#include "memoryhooker/Module.h"
namespace cross {
static constexpr size_t mPreAllocateSize = 1024 * 1024 * 4;
static size_t mExtendSize = 1024 * 1024 * 16;
static constexpr size_t mPreAllocateSizeBig = 1024 * 1024 * 256; //For special case like foliage
static size_t mExtendSizeBig = 1024 * 1024 * 256;

// Global allocator and mutex
static TLSFAllocator g_TlsfAllocator;
static std::mutex g_TlsfAllocatorMutex;

TLSFPoolInfo TLSFAllocator::CreateTLSFPool(uint32_t expectSize)
{
    TLSFPoolInfo poolInfo;
    poolInfo.poolSegmentBegin = (std::uint8_t*)(cross::Memory::Malloc(expectSize));
    poolInfo.poolSegmentEnd = poolInfo.poolSegmentBegin + expectSize;
    poolInfo.poolReservedSize = expectSize;
    poolInfo.tlsfPool = gbf::allocator::tlsf_create_with_pool(poolInfo.poolSegmentBegin, expectSize);
    return poolInfo;
}

TLSFAllocator::TLSFAllocator()
{
    mNowUsePoolIndex = 0;
}
TLSFAllocator::~TLSFAllocator() 
{
    std::lock_guard<std::mutex> lock(g_TlsfAllocatorMutex);
    for (auto& tlsfPool : TLSFPoolArray)
    {
        DestroyTLSFPool(tlsfPool);
    }
    TLSFPoolArray.clear();
    for (auto& tlsfPool : TLSFPoolArrayBig)
    {
        DestroyTLSFPool(tlsfPool);
    }
    TLSFPoolArrayBig.clear();
}
void* TLSFAllocator::Malloc(size_t bytes)
{
    std::lock_guard<std::mutex> lock(g_TlsfAllocatorMutex);
    OnChunkPreAllocate(bytes);

    // assert(bytes < mExtendSize / 2);

    if (bytes == 0)
        return nullptr;

    if (bytes >= mPreAllocateSize)
    {
        if (TLSFPoolArrayBig.size() == 0)
        {
            auto tlsfPool = CreateTLSFPool((uint32_t)mPreAllocateSizeBig);
            TLSFPoolArrayBig.emplace_back(tlsfPool);
        }

        auto& usedTlsfPool = TLSFPoolArrayBig[mNowUsePoolIndexBig];
        constexpr size_t alignment = 8;
        void* dataPtr = usedTlsfPool.Allocate(bytes, alignment);   ////Instrusive::tlsf_memalign(usedTlsfPool.tlsfPool, alignment, bytes);

        // try to allocate in exist pools
        if (!dataPtr)
        {
            dataPtr = TryAllocateChunkInExistPools(bytes, alignment, mNowUsePoolIndexBig, TLSFPoolArrayBig);
        }
        // try to allocate in a new create pool
        if (!dataPtr)
        {
            dataPtr = RequestNewPoolAndAllocateOneChunk(mExtendSizeBig, bytes, alignment, TLSFPoolArrayBig, mNowUsePoolIndexBig);
        }
        size_t chunkSize = gbf::allocator::tlsf_block_size(dataPtr);
        ////AtomicAdd(&mTotalAllocatedMemory, chunkSize);

        return dataPtr;
    }
    else
    {
        if (TLSFPoolArray.size() == 0)
        {
            auto tlsfPool = CreateTLSFPool((uint32_t)mPreAllocateSize);
            TLSFPoolArray.emplace_back(tlsfPool);
        }
        auto& usedTlsfPool = TLSFPoolArray[mNowUsePoolIndex];
        constexpr size_t alignment = 8;
        void* dataPtr = usedTlsfPool.Allocate(bytes, alignment);   ////Instrusive::tlsf_memalign(usedTlsfPool.tlsfPool, alignment, bytes);

        // try to allocate in exist pools
        if (!dataPtr)
        {
            dataPtr = TryAllocateChunkInExistPools(bytes, alignment, mNowUsePoolIndex, TLSFPoolArray);
        }

        // try to allocate in a new create pool
        if (!dataPtr)
        {
            dataPtr = RequestNewPoolAndAllocateOneChunk(mExtendSize, bytes, alignment, TLSFPoolArray, mNowUsePoolIndex);
        }

        size_t chunkSize = gbf::allocator::tlsf_block_size(dataPtr);
        ////AtomicAdd(&mTotalAllocatedMemory, chunkSize);

        return dataPtr;
    }
}
void TLSFAllocator::Free(void* mem) 
{
    if (!mem) return;

    std::lock_guard<std::mutex> lock(g_TlsfAllocatorMutex);
    //Only static needs free
    bool bContains = false;
    for (size_t i = 0; i < g_TlsfAllocator.TLSFPoolArray.size(); i++)
    {
        if (g_TlsfAllocator.TLSFPoolArray[i].IsContain(mem))
        {
            g_TlsfAllocator.TLSFPoolArray[i].Free(mem);
            g_TlsfAllocator.OnChunkFreed(i, g_TlsfAllocator.TLSFPoolArray, g_TlsfAllocator.mNowUsePoolIndex);
            bContains = true;
            break;
        }
    }
    if (!bContains)
    {
        for (size_t i = 0; i < g_TlsfAllocator.TLSFPoolArrayBig.size(); i++)
        {
            if (g_TlsfAllocator.TLSFPoolArrayBig[i].IsContain(mem))
            {
                g_TlsfAllocator.TLSFPoolArrayBig[i].Free(mem);
                g_TlsfAllocator.OnChunkFreed(i, g_TlsfAllocator.TLSFPoolArrayBig, g_TlsfAllocator.mNowUsePoolIndexBig);
                break;
            }
        }
    }
}
void* TLSFAllocator::Realloc(void* mem, size_t originalSize, size_t newSize)
{
    if (mem == 0)
    {
        return Malloc(newSize);
    }

    if (newSize == 0)
    {
        Free(mem);
        return nullptr;
    }

    {
        void* retResult = Malloc(newSize);
        memcpy(retResult, mem, std::min<size_t>(newSize, originalSize));
        Free(mem);
        return retResult;
    }
}
void TLSFAllocator::OnChunkPreAllocate(size_t size) 
{
    if (size > (size_t)mMaxChunkSize)
    {
        mMaxChunkSize = (int)size;
    }
}

size_t TLSFAllocator::GetChunkSize(void* mem) const
{
   return gbf::allocator::tlsf_block_size(mem);
}
void TLSFAllocator::DestroyTLSFPool(TLSFPoolInfo& poolInfo) 
{
    gbf::allocator::tlsf_destroy(poolInfo.tlsfPool);
    cross::Memory::Free(poolInfo.poolSegmentBegin);
    poolInfo.poolSegmentBegin = nullptr;
    poolInfo.poolSegmentEnd = nullptr;
    poolInfo.poolReservedSize = 0;
    poolInfo.tlsfPool = nullptr;
}
void* TLSFAllocator::TryAllocateChunkInExistPools(size_t bytes, size_t alignment, int& exceptPoolIndex, std::vector<TLSFPoolInfo>& poolarrays)
{
    for (size_t i = 0; i < poolarrays.size(); i++)
    {
        if (i == (size_t)exceptPoolIndex)
            continue;

        void* dataPtr = poolarrays[i].Allocate(bytes, alignment); 
        if (dataPtr)
        {
            exceptPoolIndex = (int)i;
            return dataPtr;
        }
    }

    return nullptr;
}
void* TLSFAllocator::RequestNewPoolAndAllocateOneChunk(size_t poolSize, size_t bytes, size_t alignment, std::vector<TLSFPoolInfo>& poolarrays, int& poolindex)
{
    auto poolInfo = CreateTLSFPool((uint32_t)poolSize);
    poolarrays.emplace_back(poolInfo);

    poolindex = (int)poolarrays.size() - 1;

    return poolarrays[poolindex].Allocate(bytes, alignment);
}
void TLSFAllocator::OnChunkFreed(size_t poolIndex, std::vector<TLSFPoolInfo>& poolarrays, int& poolindex)
{
    if (poolarrays[poolIndex].IsEmpty())
    {
        // try to free2013 it
        DestroyTLSFPool(poolarrays[poolIndex]);
        poolarrays.erase(poolarrays.begin() + poolIndex);

        // Reset now use pool index
        poolindex = 0;
    }
}
void TLSFPoolInfo::Free(void* ptr)
{
    std::uint32_t chunkSize = (uint32_t)gbf::allocator::tlsf_block_size(ptr);
    gbf::allocator::tlsf_free(this->tlsfPool, ptr);
    this->poolAllocatedSize -= chunkSize;
    this->totalAllocationCount--;
}

void* TLSFPoolInfo::Allocate(size_t bytes, size_t alignment)
{
    void* dataPtr = gbf::allocator::tlsf_memalign(this->tlsfPool, alignment, bytes);
    if (dataPtr)
    {
        std::uint32_t chunkSize = (uint32_t)gbf::allocator::tlsf_block_size(dataPtr);

        ////if (this->maxBlockSize < chunkSize)
        ////{
        ////	this->maxBlockSize = chunkSize;
        ////}

        this->poolAllocatedSize += chunkSize;
        this->totalAllocationCount++;
    }

    return dataPtr;
}

TLSFAllocator& GetTLSFAllocator()
{
    return g_TlsfAllocator;
}

TLSFDocument& GetStaticDoc()
{
    // This is a bit of a hack to keep the old API working.
    // The document itself is temporary, but it uses the global allocator.
    static thread_local TLSFDocument sDocument(&GetTLSFAllocator());
    return sDocument;
}
}   // namespace cross