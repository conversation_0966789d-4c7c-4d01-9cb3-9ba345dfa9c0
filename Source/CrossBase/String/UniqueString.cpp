#include "PCH/CrossBasePCHPrivate.h"
#include "String/UniqueString.h"

namespace cross
{
static bool sIsPoolInitialized;
static UniqueStringPool sPool;
static UniqueStringPool& GetUniqueStringPool()
{
	if (sIsPoolInitialized)
	{
		return sPool;
	}

	new (&sPool) UniqueStringPool;
	sIsPoolInitialized = true;
	return sPool;
}


UniqueString::UniqueString(const char* str)
{
    *this = GetUniqueStringPool().GetUniqueString_threadsafe(str, (UInt32)strlen(str));
}

UniqueString::UniqueString(const char* str, UInt32 length)
{
	*this = GetUniqueStringPool().GetUniqueString_threadsafe(str, length);
}

const UniqueStringPool& UniqueString::GetPool()
{
	return GetUniqueStringPool();
}

UniqueStringEntry::UniqueStringEntry(const char* str, UInt32 len)
{
	mString = new char[len+1];
	memcpy(mString, str, len * sizeof(char));
	mString[len] = '\0';
	mLength = len;
}

UniqueStringEntry::~UniqueStringEntry() 
{
    delete mString;
}

UniqueString UniqueStringPool::GetUniqueString_threadsafe(const char* str, UInt32 length)
{
	UniqueString result;
	result.mHash = HashFunction::HashString64(str, length);
	{
		std::lock_guard<std::mutex> lock(mTableMtx);
		auto mTableIt = mTable.find(result.mHash);
		if (mTableIt != mTable.end())
		{
#if 0
			Assert(strcpy(mTableIt->second->mString, str) == 0);
#endif
			result.mEntry = mTableIt->second;
		}
		else
		{
			result.mEntry.reset(new UniqueStringEntry(str, length));
			mTable.emplace(result.mHash, result.mEntry);
			mConservedMemoryUsedInByte += (sStringInstanceFixedSizeInByte + length);
		}
	}
	return result;
}

}