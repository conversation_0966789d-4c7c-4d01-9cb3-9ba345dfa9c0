#pragma once
#include "CrossBase/Log.h"

#include <bitset>
#include <vector>
namespace cross
{
    namespace alist
    {
        template <typename T>
        class TChunk
        {
        public:
            static constexpr size_t BlockCount = 1024 / sizeof(T);
            static constexpr size_t BlockSize = sizeof(T);
            static constexpr size_t BlockAlignment = alignof(T);
            static_assert((BlockCount * BlockSize) / BlockSize == BlockCount);
            using BlockType = std::aligned_storage_t<BlockSize, BlockAlignment>;
            using StorageType = std::array<BlockType, BlockCount>;
            static_assert(sizeof(StorageType) == BlockCount * BlockSize);
            using FlagType = std::bitset<BlockCount>;

        public:
            TChunk()
                : mFirstAvailableBlock(0)
                , mBlocksAvailable(BlockCount)
            {
                Init();
            }

            ~TChunk()
            {
                Release();
            }

            TChunk(TChunk const&) = delete;
            TChunk& operator=(TChunk const&) = delete;

        public:
            template <typename ... Args>
            std::pair<T*, size_t> Construct(Args&& ... args)
            {
                // block is full
                if (mBlocksAvailable == 0)
                {
                    return { nullptr, BlockCount };
                }

                // get to the first available block memory
                UInt8* data = DataPtr(mFirstAvailableBlock);
                auto result = reinterpret_cast<T*>(data);

                // cache the next block for exceptional safety
                auto const nextBlock = *data;
                DEBUG_ASSERT(nextBlock <= BlockCount);

                // construct the object
                new (result) T{ std::forward<Args>(args)... };

                // recored the index for results & pointer
                auto const index = mFirstAvailableBlock;
                DEBUG_ASSERT(index < BlockCount);
                mFlags.set(index, true);

                // update block state
                mFirstAvailableBlock = nextBlock;
                --mBlocksAvailable;

                // return the results, pointer * index
                return { result, index };
            }

            void Destruct(T* ptr)
            {
                DEBUG_ASSERT(ptr);
                DEBUG_ASSERT(mFirstAvailableBlock < BlockCount);
                if constexpr (std::negation_v<std::is_trivially_destructible<T>>)
                {
                    ptr->~T();
                }
                auto data = reinterpret_cast<UInt8*>(ptr);
                DEBUG_ASSERT(BlockInChunk(ptr));
                DEBUG_ASSERT(IsBlockPointer(ptr));
                auto const index = static_cast<size_t>((data - DataPtr(0)) / BlockSize);
                DEBUG_ASSERT(index < BlockCount);
                mFlags.set(index, false);
                *data = static_cast<UInt8>(mFirstAvailableBlock);
                mFirstAvailableBlock = index;
                ++mBlocksAvailable;
            }

            void Destruct(size_t index)
            {
                DEBUG_ASSERT(index < BlockCount);
                if (index < BlockCount && mFlags.test(index))
                {
                    auto ptr = reinterpret_cast<T*>(DataPtr(index));
                    Destruct(ptr);
                }
            }

            bool BlockInChunk(T* ptr) const noexcept
            {
                auto dataPtr = reinterpret_cast<UInt8*>(ptr);
                return dataPtr >= DataPtr(0) &&
                    dataPtr <= DataPtr(BlockCount - 1);
            }

            bool IsBlockPointer(T* ptr) const noexcept
            {
                std::ptrdiff_t distance = DataPtr(0) - reinterpret_cast<UInt8*>(ptr);
                return distance % BlockSize == 0;
            }

            bool HashAvailableBlock() const noexcept
            {
                return mBlocksAvailable > 0;
            }

            T* Get(size_t index)
            {
                auto constResult = const_cast<TChunk const*>(this)->Get(index);
                return const_cast<T*>(constResult);
            }

            T const* Get(size_t index) const
            {
                DEBUG_ASSERT(index < BlockCount);
                if (index < BlockCount && mFlags.test(index))
                {
                    auto result = reinterpret_cast<T const*>(DataPtr(index));
                    return result;
                }
                return nullptr;
            }

            bool Test(size_t index) const
            {
                return mFlags.test(index);
            }

        private:
            void Init()
            {
                for (size_t loop = 0; loop < BlockCount; ++loop)
                {
                    auto data = DataPtr(loop);
                    *data = static_cast<UInt8>(loop + 1);
                }
            }

            UInt8* DataPtr(size_t index) noexcept
            {
                return reinterpret_cast<UInt8*>(&mStorage[index]);
            }

            UInt8 const* DataPtr(size_t index) const noexcept
            {
                return reinterpret_cast<UInt8 const*>(&mStorage[index]);
            }

            void Release()
            {
                for (size_t loop = 0; loop < BlockCount; ++loop)
                {
                    if (!mFlags.test(loop))
                    {
                        continue;
                    }

                    auto ptr = reinterpret_cast<T*>(DataPtr(loop));
                    if constexpr (std::negation_v<std::is_trivially_destructible<T>>)
                    {
                        ptr->~T();
                    }
                }
                mFlags.reset();
            }

        private:
            StorageType         mStorage;
            FlagType            mFlags;
            size_t              mFirstAvailableBlock;
            size_t              mBlocksAvailable;
        };
    }

    template <typename T>
    class TArrayList final
    {
    public:
        using ChunkType = alist::TChunk<T>;
        using Container = std::vector<std::unique_ptr<ChunkType>>;
        using ContainerIterator = typename Container::iterator;
        using ContainerConstIterator = typename Container::const_iterator;

    public:
        class Iterator final
        {
        public:
            Iterator() noexcept
                : mIndex(0)
                , mChunkItr()
            {}

            Iterator(UInt32 index, ContainerIterator itr) noexcept
                : mIndex(index)
                , mChunkItr(itr)
            {
                DEBUG_ASSERT(index < ChunkType::BlockCount);
            }

        public:
            Iterator operator++()
            {
                Iterator itr = *this;
                MoveForward();
                return itr;
            }

            Iterator& operator++(int)
            {
                MoveForward();
                return *this;
            }

            Iterator operator--()
            {
                Iterator itr = *this;
                MoveBackward();
                return itr;
            }

            Iterator& operator--(int)
            {
                MoveBackward();
                return *this;
            }

            T* operator*()
            {
                return (*mChunkItr)->Get(mIndex);
            }

            T const* operator*() const
            {
                return (*mChunkItr)->Get(mIndex);
            }

            bool operator==(Iterator const& other) const noexcept
            {
                return mIndex == other.mIndex && mChunkItr == other.mChunkItr;
            }

            bool operator!=(Iterator const& other) const noexcept
            {
                return !(*this == other);
            }

        private:
            void MoveForward()
            {
                mIndex += 1;
                if (mIndex == static_cast<UInt32>(ChunkType::BlockCount))
                {
                    mIndex = 0;
                    mChunkItr++;
                }
            }

            void MoveBackward()
            {
                mIndex -= 1;
                if (mIndex == (std::numeric_limits<UInt32>::max)())
                {
                    mIndex = static_cast<UInt32>(ChunkType::BlockCount) - 1;
                    mChunkItr--;
                }
            }

            UInt32                  mIndex;
            ContainerIterator       mChunkItr;
        };

        class ConstIterator final
        {
        public:
            ConstIterator()
                : mIndex(0)
                , mChunkItr()
            {}


            ConstIterator(UInt32 index, ContainerConstIterator itr)
                : mIndex(index)
                , mChunkItr(itr)
            {
                DEBUG_ASSERT(index < ChunkType::BlockCount);
            }

        public:
            ConstIterator operator++()
            {
                ConstIterator itr = *this;
                MoveForward();
                return itr;
            }

            ConstIterator& operator++(int)
            {
                MoveForward();
                return *this;
            }

            ConstIterator operator--()
            {
                ConstIterator itr = *this;
                MoveBackward();
                return itr;
            }

            ConstIterator& operator--(int)
            {
                MoveBackward();
                return *this;
            }

            T const* operator*() const
            {
                return (*mChunkItr)->Get(mIndex);
            }

        private:
            void MoveForward()
            {
                mIndex += 1;
                if (mIndex == static_cast<UInt32>(ChunkType::BlockCount))
                {
                    mIndex = 0;
                    mChunkItr++;
                }
            }

            void MoveBackward()
            {
                mIndex -= 1;
                if (mIndex == (std::numeric_limits<UInt32>::max)())
                {
                    mIndex = static_cast<UInt32>(ChunkType::BlockCount) - 1;
                    mChunkItr--;
                }
            }

        private:
            UInt32                  mIndex;
            ContainerConstIterator  mChunkItr;
        };

    public:
        TArrayList()
        {
            mChunks.emplace_back(std::move(std::make_unique<ChunkType>()));
        }

        ~TArrayList() = default;

        TArrayList(TArrayList const&) = delete;                 // TODO ... copy
        TArrayList& operator=(TArrayList const&) = delete;      // TODO ... copy assignment

    public:
        template <typename ... Args>
        std::pair<T*, size_t> Construct(Args&& ... args)
        {
            auto itr = std::find_if(mChunks.begin(), mChunks.end(), [](auto const& chunk)
            {
                return chunk->HashAvailableBlock();
            });

            size_t chunkIndex = 0;
            if (itr == mChunks.end())
            {
                chunkIndex = mChunks.size();
                // need malloc a new chunk
                mChunks.emplace_back(std::move(std::make_unique<ChunkType>()));
            }
            else
            {
                chunkIndex = static_cast<size_t>(std::distance(mChunks.begin(), itr));
            }

            auto result = mChunks[chunkIndex]->Construct(std::forward<Args>(args)...);
            size_t const index = chunkIndex * ChunkType::BlockCount + result.second;
            return { result.first, index };
        }

        void Destruct(T* ptr)
        {
            auto itr = std::find_if(mChunks.begin(), mChunks.end(), [ptr](auto const& chunk)
            {
                return chunk->BlockInChunk(ptr);
            });
            DEBUG_ASSERT(itr != mChunks.end());
            if (itr != mChunks.end())
            {
                itr->Destruct(ptr);
            }
        }

        void Destruct(size_t index)
        {
            auto const chunkIndex = index / ChunkType::BlockCount;
            if (chunkIndex >= mChunks.size())
            {
                return;
            }

            auto const indexInChunk = index % ChunkType::BlockCount;
            mChunks[chunkIndex]->Destruct(indexInChunk);
        }

        T* Get(size_t index)
        {
            auto const chunkIndex = index / ChunkType::BlockCount;
            if (chunkIndex >= mChunks.size())
            {
                return nullptr;
            }
            auto const indexInChunk = index % ChunkType::BlockCount;
            return mChunks[chunkIndex]->Get(indexInChunk);
        }

        Iterator Begin()
        {
            return Iterator{ 0, mChunks.begin() };
        }

        Iterator End()
        {
            return Iterator{ 0, mChunks.end() };
        }

        ConstIterator ConstBegin() const
        {
            return ConstIterator{ 0, mChunks.cbegin() };
        }

        ConstIterator ConstEnd() const
        {
            return ConstIterator{ 0, mChunks.cend() };
        }

    public:
        // adaption for stl
        Iterator begin()
        {
            return Begin();
        }

        Iterator end()
        {
            return End();
        }

        ConstIterator cbegin() const
        {
            return ConstBegin();
        }

        ConstIterator cend() const
        {
            return ConstEnd();
        }

    private:
        Container mChunks;
    };
}