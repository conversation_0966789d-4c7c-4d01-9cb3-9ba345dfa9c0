<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;MinSizeRel;Release;RelWithDebInfo</Configurations>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <BaseOutputPath>..\..\..\bin\Visual_Studio_17_2022_Win64_md</BaseOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <WarningLevel>2</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|AnyCPU'">
    <DefineConstants>TRACE;REL_WITH_DEB_INFO</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <WarningLevel>2</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <WarningLevel>2</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|AnyCPU'">
    <WarningLevel>2</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="CrossEditorCore\**" />
    <Compile Remove="Source\CrossEditor\Editor\**" />
    <Compile Remove="Source\CrossEditor\Runtime\**" />
    <EmbeddedResource Remove="CrossEditorCore\**" />
    <EmbeddedResource Remove="Source\CrossEditor\Editor\**" />
    <EmbeddedResource Remove="Source\CrossEditor\Runtime\**" />
    <None Remove="CrossEditorCore\**" />
    <None Remove="Source\CrossEditor\Editor\**" />
    <None Remove="Source\CrossEditor\Runtime\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Security.Permissions" Version="8.0.0" />
    <PackageReference Include="System.Windows.Extensions" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="EditorUI">
      <HintPath>..\..\..\ThirdParty\EditorUI\EditorUI.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder">
      <HintPath>..\..\..\ThirdParty\QRCoder\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <HintPath>..\..\..\ThirdParty\System\System.Drawing.Common.dll</HintPath>
    </Reference>
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="CrossEditorCore\CrossEditorCore.csproj" />
  </ItemGroup>
</Project>
