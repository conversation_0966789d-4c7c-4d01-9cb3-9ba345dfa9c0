<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;MinSizeRel;Release;RelWithDebInfo</Configurations>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
    <BaseOutputPath>..\..\..\..\bin\Visual_Studio_17_2022_Win64_md</BaseOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|AnyCPU'">
    <DefineConstants>TRACE;REL_WITH_DEB_INFO</DefineConstants>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Security.Permissions" Version="4.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\build_x64\Source\GameplayBaseFramework\editor\managed\managed_gbf.vcxproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="EditorUI">
      <HintPath>..\..\..\..\ThirdParty\EditorUI\EditorUI.dll</HintPath>
    </Reference>
    <Reference Include="QRCoder">
      <HintPath>..\..\..\..\ThirdParty\QRCoder\QRCoder.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Common">
      <HintPath>..\..\..\..\ThirdParty\System\System.Drawing.Common.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>   
    <Compile Include="..\..\..\GeneratedCode\Editor\**\*.cs" LinkBase="GeneratedCode" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\Source\CrossEditor\Editor\**\*.cs" LinkBase="Editor" />
    <Compile Include="..\Source\CrossEditor\Runtime\**\*.cs" LinkBase="Runtime" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="..\..\..\GeneratedCode\Editor\FullFlightSimulator\**" />
  </ItemGroup>

</Project>
