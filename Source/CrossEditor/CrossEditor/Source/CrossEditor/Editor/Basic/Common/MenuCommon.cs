using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{

    [AttributeUsage(AttributeTargets.Class)]
    public class CommonMenuAttribute : System.Attribute
    {
        // static display ui names pattern string. The parser will automatically generate menu items.
        // e.g, "Year/Season/[Spring, Summer, Fall, Winter]"
        public string DisplayUINames = "";
        public string Icon = "";
        public int ShowOrder = 50; // 0 mean do not show

        // Since DisplayUINames is defined at compile time, so it is a static string.
        // But sometimes we want to change the menu item at runtime, so a dynamic pattern is needed and that is it.
        // if UseDynamicUINames is true, DisplayUINames will not be used and instead, the parser will call ToolBarMenuItem::GetDynamicUINames to generate a dynamic pattern string at runtime.
        public bool UseDynamicUINames = false;
    }


    [CommonMenuAttribute()]
    public class CommonDisplayMenuItem : MenuItem
    {
        public CommonDisplayMenuItem()
        {
            ClickedEvent += OnClick;
        }

        public virtual bool Show() { return true; }

        public virtual void OnClick(MenuItem sender) { }
    }


    [AttributeUsage(AttributeTargets.Class)]
    public class ToolBarMenuAttribute : CommonMenuAttribute { }

    [ToolBarMenuAttribute()]
    public class ToolBarMenuItem : CommonDisplayMenuItem
    {
        public ToolBarMenuItem()
        {
        }

        // SEE CommonMenuAttribute.UseDynamicUINames
        public virtual string GetDynamicUINames()
        {
            return "";
        }
    }


    [AttributeUsage(AttributeTargets.Class)]
    public class ProjectMenuAttribute : CommonMenuAttribute
    {
        public bool OnlyListViewShow = false;
    }


    public class ProjectMenuItem : CommonDisplayMenuItem
    {
    }


    [AttributeUsage(AttributeTargets.Class)]
    public class DockingMenuAttribute : CommonMenuAttribute
    {
        public DockingBlockSection DefaultDocking = DockingBlockSection.RightInspector;
    }


    public class DockingMenuItems : CommonDisplayMenuItem
    {

    }


    public class MenuItemUtils
    {
        public delegate void MenuInitialize(Menu m, int level);

        public delegate MenuItem MenuItemInitialize((List<string>, Type) entry);

        public static Dictionary<string, MenuItem> CreateMenu(Menu menu, UIManager uimanager, List<(string, Type)> displayMenus, MenuItemInitialize menuitem_initializer, MenuInitialize menu_initializer = null)
        {
            Dictionary<string, Menu> Menus = new Dictionary<string, Menu>();
            Dictionary<string, MenuItem> MenuItems = new Dictionary<string, MenuItem>();

            List<(List<string>, Type)> parsedResults = new List<(List<string>, Type)>();

            foreach ((string, Type) entry in displayMenus)
            {
                string[] hierarchy = entry.Item1.Split('/');

                string lastWord = hierarchy[hierarchy.Length - 1];

                if (lastWord.StartsWith("[") && lastWord.EndsWith("]"))
                {
                    // erase head and tail
                    lastWord = lastWord.Substring(1, lastWord.Length - 2);
                    var lastWords = lastWord.Split(',');

                    for (int i = 0; i < lastWords.Length; i++)
                    {
                        hierarchy[hierarchy.Length - 1] = lastWords[i];
                        parsedResults.Push((hierarchy.ToList(), entry.Item2));
                    }
                }
                else
                {
                    parsedResults.Push((hierarchy.ToList(), entry.Item2));
                }
            }


            foreach ((List<string>, Type) entry in parsedResults)
            {
                var hierarchy = entry.Item1;

                string hierachyName = "";

                Menu currentMenu = menu;

                for (int i = 0; i < hierarchy.Count; i++)
                {
                    hierachyName += hierarchy[i];

                    string currentName = hierarchy[i];



                    if (i != hierarchy.Count - 1)
                    {
                        if (!Menus.ContainsKey(hierachyName))
                        {
                            Menu new_menu = new Menu(uimanager);
                            new_menu.Initialize();
                            new_menu.SetText(currentName);
                            if (menu_initializer != null)
                            {
                                menu_initializer(new_menu, i);
                            }

                            Menus.Add(hierachyName, new_menu);

                            MenuItem new_menuItem = new MenuItem();
                            new_menuItem.SetText(currentName);
                            new_menuItem.SetMenu(new_menu);

                            MenuItems.Add(hierachyName, new_menuItem);

                            if (currentMenu != null)
                                currentMenu.AddMenuItem(new_menuItem);
                        }
                        currentMenu = Menus[hierachyName];
                    }
                    else
                    {
                        var MenuItem_Add = menuitem_initializer(entry);
                        if (MenuItem_Add != null)
                        {
                            MenuItem_Add.SetText(currentName);

                            MenuItems.Add(hierachyName, MenuItem_Add);

                            if (currentMenu != null)
                                currentMenu.AddMenuItem(MenuItem_Add);
                        }

                    }
                    hierachyName += "/";
                }
            }

            return MenuItems;
        }
    }
}
