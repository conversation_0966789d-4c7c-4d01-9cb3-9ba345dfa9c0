using EditorUI;
using System;

namespace CrossEditor
{
    class ComparePanel : Panel
    {
        Texture _Image2;
        float _Ratio;
        int _HandleRadius;

        bool _LeftMouseDown;
        int _SavedMouseX;
        float _SavedRatio;

        public ComparePanel()
        {
            _Image2 = null;
            _Ratio = 0.5f;
            _HandleRadius = 30;

            _LeftMouseDown = false;
            _SavedMouseX = 0;
            _SavedRatio = 0.0f;
        }

        public void SetImage2(Texture Image2)
        {
            if (Image2 != _Image2)
            {
                _Image2 = Image2;
                Invalidate();
            }
        }

        public Texture GetImage2()
        {
            return _Image2;
        }

        public override void PaintThis()
        {
            int ScreenX = GetScreenX();
            int ScreenY = GetScreenY();
            PaintBackground(ScreenX, ScreenY);
            PaintImagesAndHandle(ScreenX, ScreenY);
            PaintText(ScreenX, ScreenY);
            PaintBorder(ScreenX, ScreenY);
            PaintMask(ScreenX, ScreenY);
        }

        void PaintImagesAndHandle(int ScreenX, int ScreenY)
        {
            Color Color = Color.White;
            int X = ScreenX + _ImageOffsetX;
            int Y = ScreenY + _ImageOffsetY;
            int Width = _Width;
            int Height = _Height;

            int X1 = X;
            int Y1 = Y;
            int Width1 = (int)(_Width * _Ratio);
            int Height1 = _Height;

            UIManager UIManager = GetUIManager();
            Graphics2D Graphics2D = UIManager.GetGraphics2D();

            UIManager.PushClip(true, X1, Y1, Width1, Height1);

            if (_Image != null)
            {
                int ImageWidth = _Image.GetWidth();
                int ImageHeight = _Image.GetHeight();

                int SourceX = 0;
                int SourceY = 0;
                int SourceWidth = ImageWidth;
                int SourceHeight = ImageHeight;

                Graphics2D.DrawTexture(_Image, Color, X, Y, Width, Height, SourceX, SourceY, SourceWidth, SourceHeight);
            }

            int X2 = X + Width1;
            int Y2 = Y;
            int Width2 = _Width - Width1;
            int Height2 = _Height;

            UIManager.PopClip();

            UIManager.PushClip(true, X2, Y2, Width2, Height2);

            if (_Image2 != null)
            {
                int ImageWidth = _Image2.GetWidth();
                int ImageHeight = _Image2.GetHeight();

                int SourceX = 0;
                int SourceY = 0;
                int SourceWidth = ImageWidth;
                int SourceHeight = ImageHeight;

                Graphics2D.DrawTexture(_Image2, Color, X, Y, Width, Height, SourceX, SourceY, SourceWidth, SourceHeight);
            }

            UIManager.PopClip();

            int X3 = X + Width1;
            int Y3 = Y;
            Graphics2D.DrawLine(_BorderColor, X3, Y3, X3, Y3 + _Height);

            int Segment = 32;
            int CenterX = X3;
            int CenterY = Y3 + _Height / 2;
            int Radius = _HandleRadius;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawCircleF(_BorderColor, new Vector2f(CenterX, CenterY), Radius, 1, Segment);
        }

        public override void OnLeftMouseDown(int MouseX, int MouseY, ref bool bContinue)
        {
            CaptureMouse();
            _LeftMouseDown = true;
            _SavedMouseX = MouseX;
            _SavedRatio = _Ratio;
        }

        public override void OnLeftMouseUp(int MouseX, int MouseY, ref bool bContinue)
        {
            if (_LeftMouseDown)
            {
                ReleaseMouse();
                _LeftMouseDown = false;
                bContinue = false;
            }
        }

        public override void OnMouseMove(int MouseX, int MouseY, ref bool bContinue)
        {
            if (_LeftMouseDown)
            {
                int SavedWidth = (int)(_SavedRatio * _Width);
                int NewWidth = SavedWidth + (MouseX - _SavedMouseX);
                _Ratio = NewWidth / (float)_Width;
                _Ratio = MathF.Max(_Ratio, 0.0f);
                _Ratio = MathF.Min(_Ratio, 1.0f);
            }
        }
    }
}