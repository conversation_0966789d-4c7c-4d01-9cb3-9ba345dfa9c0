using EditorUI;
using System;

namespace CrossEditor
{
    public class OperationBarUI
    {
        public const int BAR_HEIGHT = 24;
        public static Color BAR_COLOR = Color.FromRGB(50, 50, 50);

        public bool _bAutoResize = false;

        int _BarHeight = BAR_HEIGHT;

        Panel _PanelBar;
        Panel _PanelLeftAligned;
        Panel _PanelRightAligned;

        public OperationBarUI()
        {
        }

        public void Initialize()
        {
            _PanelBar = new Panel();
            _PanelBar.SetSize(500, BAR_HEIGHT);
            _PanelBar.SetBackgroundColor(BAR_COLOR);
            _PanelBar.PositionChangedEvent += OnPanelBarPositionChanged;

            _PanelRightAligned = new Panel();
            _PanelRightAligned.SetSize(500, BAR_HEIGHT);
            _PanelRightAligned.SetBackgroundColor(BAR_COLOR);
            _PanelBar.AddChild(_PanelRightAligned);

            _PanelLeftAligned = new Panel();
            _PanelLeftAligned.SetSize(500, BAR_HEIGHT);
            _PanelLeftAligned.SetBackgroundColor(BAR_COLOR);
            _PanelBar.AddChild(_PanelLeftAligned);
        }

        public void SetBarHeight(int BarHeight)
        {
            _BarHeight = BarHeight;
        }

        public Panel GetPanelBar()
        {
            return _PanelBar;
        }

        public static Button CreateTextButton(string Text)
        {
            Button TextButton = new Button();
            TextButton.SetText(Text);
            TextButton.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            TextButton.SetPosition(4, 4, TextButton.CalculateTextWidth() + 8, 16);
            TextButton.SetTextOffsetY(2);
            TextButton.SetBorderColor(Color.FromRGB(130, 130, 130));
            TextButton.SetNormalColor(Color.FromRGB(94, 94, 94));
            TextButton.SetHoverColor(Color.FromRGB(130, 130, 130));
            TextButton.SetDownColor(Color.FromRGB(81, 81, 81));
            return TextButton;
        }

        public void AddLeft(Control Control)
        {
            _PanelLeftAligned.AddChild(Control);
        }

        public void AddRight(Control Control)
        {
            _PanelRightAligned.AddChild(Control);
        }

        public void Reset()
        {
            _PanelLeftAligned.ClearChildren();
            _PanelRightAligned.ClearChildren();
        }

        int GetVisibleChildCount(Control Control)
        {
            int VisibleChildCount = 0;
            int ChildCount = Control.GetChildCount();
            for (int i = 0; i < ChildCount; i++)
            {
                Control Child = Control.GetChild(i);
                if (Child.GetVisible())
                {
                    VisibleChildCount++;
                }
            }
            return VisibleChildCount;
        }

        public void Refresh()
        {
            int Width = _PanelBar.GetWidth();

            int SpanX = 4;

            int VisibleChildCount_Left = GetVisibleChildCount(_PanelLeftAligned);
            int VisibleChildCount_Right = GetVisibleChildCount(_PanelRightAligned);

            if (_bAutoResize)
            {
                int SpanTotal = Math.Max(VisibleChildCount_Left + VisibleChildCount_Right - 1, 0) * SpanX;
                int UiTotal = _PanelBar.GetWidth() - SpanTotal;

                int DesiredTotal = 0;
                for (int i = 0; i < VisibleChildCount_Left; i++)
                {
                    Control Control = _PanelLeftAligned.GetChild(i);
                    if (Control.GetVisible())
                    {
                        DesiredTotal += Control.GetWidth();
                    }
                }

                for (int i = 0; i < VisibleChildCount_Right; i++)
                {
                    Control Control = _PanelRightAligned.GetChild(i);
                    if (Control.GetVisible())
                    {
                        DesiredTotal += Control.GetWidth();
                    }
                }

                if (DesiredTotal > 0)
                {
                    float radio = UiTotal * 1.0f / DesiredTotal;

                    int currentStep = 0;
                    for (int i = 0; i < VisibleChildCount_Left; i++)
                    {
                        Control Control = _PanelLeftAligned.GetChild(i);
                        if (Control.GetVisible())
                        {
                            Control.SetX(currentStep);
                            Control.SetWidth((int)(Control.GetWidth() * radio));

                            currentStep += Control.GetWidth() + SpanX;
                        }
                    }

                    _PanelLeftAligned.SetX(0);
                    _PanelLeftAligned.SetWidth(currentStep);

                    for (int i = 0; i < VisibleChildCount_Right; i++)
                    {
                        Control Control = _PanelRightAligned.GetChild(i);
                        if (Control.GetVisible())
                        {
                            Control.SetX(currentStep);
                            Control.SetWidth((int)(Control.GetWidth() * radio));

                            currentStep += Control.GetWidth() + SpanX;
                        }
                    }

                    _PanelRightAligned.SetX(currentStep + SpanX);
                    _PanelRightAligned.SetWidth(currentStep);

                    _PanelBar.SetHeight(_BarHeight);

                    return;
                }
            }

            int X = SpanX;
            int Count = _PanelLeftAligned.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Control Control = _PanelLeftAligned.GetChild(i);
                if (Control.GetVisible())
                {
                    Control.SetX(X);
                    X += (Control.GetWidth() + SpanX);
                }
            }
            _PanelLeftAligned.SetX(0);
            _PanelLeftAligned.SetWidth(X);

            X = SpanX;
            Count = _PanelRightAligned.GetChildCount();
            for (int i = 0; i < Count; i++)
            {
                Control Control = _PanelRightAligned.GetChild(i);
                if (Control.GetVisible())
                {
                    Control.SetX(X);
                    X += (Control.GetWidth() + SpanX);
                }
            }
            _PanelRightAligned.SetX(Width - X);
            _PanelRightAligned.SetWidth(X);

            if (VisibleChildCount_Left > 0 &&
                VisibleChildCount_Right > 0 &&
                _PanelLeftAligned.GetWidth() + _PanelRightAligned.GetWidth() > _PanelBar.GetWidth())
            {
                _PanelBar.SetHeight(_BarHeight * 2);
                _PanelRightAligned.SetY(_BarHeight);
            }
            else
            {
                _PanelBar.SetHeight(_BarHeight);
                _PanelRightAligned.SetY(0);
            }
            _PanelLeftAligned.SetHeight(_BarHeight);
            _PanelRightAligned.SetHeight(_BarHeight);
        }

        void OnPanelBarPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            Refresh();
        }
    }
}
