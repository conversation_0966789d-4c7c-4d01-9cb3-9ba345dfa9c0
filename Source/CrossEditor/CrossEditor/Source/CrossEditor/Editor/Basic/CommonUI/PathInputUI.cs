using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    public delegate void PathInputUIInputedEventHandler(PathInputUIEx Sender, string PathInputed);

    class Drive
    {
        public string Name;
        public string Path;
    }

    public class PathInputUIEx : DialogUI
    {
        PathInputUIType _PathInputUIType;
        PathInputUIFilterItem _PathInputUIFilterItem;
        string _RootDirectory;

        Texture _TextureTreeItemFoldedFolder;
        Texture _TextureTreeItemExpandedFolder;
        Texture _TextureTreeItemFile;
        Texture _TextureTreeItemTextFile;

        Label _LabelNavigation;
        Edit _EditNavigation;
        Button _Mask;
        Tree _TreeDrives;
        Tree _Tree;
        Label _LabelFile;
        Edit _EditFile;
        Label _LabelFilterItem;

        List<Drive> _Drives;
        int _MaxMatchLength;

        public event PathInputUIInputedEventHandler InputedEvent;

        public PathInputUIEx()
        {
            _Drives = new List<Drive>();
            _MaxMatchLength = 0;
        }

        public void Initialize(UIManager UIManager, string Title, PathInputUIType PathInputUIType, PathInputUIFilterItem PathInputUIFilterItem, string RootDirectory)
        {
            _PathInputUIType = PathInputUIType;
            _PathInputUIFilterItem = PathInputUIFilterItem;
            _RootDirectory = PathHelper.ToStandardForm(RootDirectory);

            if (Title == null || Title == "")
            {
                if (_PathInputUIType == PathInputUIType.OpenFile)
                {
                    Title = "Open File";
                }
                else if (_PathInputUIType == PathInputUIType.SaveFile)
                {
                    Title = "Save File";
                }
                else if (_PathInputUIType == PathInputUIType.OpenFolder)
                {
                    Title = "Open Folder";
                }
                else if (_PathInputUIType == PathInputUIType.NewFolder)
                {
                    Title = "New Folder";
                }
            }

            base.Initialize(UIManager, Title, 1000, 500);

            _LabelNavigation = new Label();
            _LabelNavigation.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelNavigation.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelNavigation.SetTextAlign(TextAlign.CenterLeft);
            _LabelNavigation.SetText("Path:");
            _LabelNavigation.SetPosition(50, 50, 66, 18);
            _PanelDialog.AddChild(_LabelNavigation);

            _EditNavigation = new Edit();
            _EditNavigation.SetFontSize(18);
            _EditNavigation.Initialize(EditMode.Simple_SingleLine);
            _EditNavigation.LoadSource("");
            _EditNavigation.SelfFocusChangedEvent += OnEditNavigationFocusChanged;
            _EditNavigation.CharInputEvent += OnEditNavigationCharInput;
            _EditNavigation.SetSize(814, 18);
            _PanelDialog.AddChild(_EditNavigation);
            UIHelper.GetInstance().SetNextTo(_EditNavigation, _LabelNavigation, 20);
            EditContextUI.GetInstance().RegisterEdit(_EditNavigation);
            _EditNavigation.SetText(_RootDirectory);

            _Mask = new Button();
            _Mask.SetHoverColor(Color.FromRGBA(255, 255, 255, 0));
            _Mask.SetDownColor(Color.FromRGBA(255, 255, 255, 0));
            _Mask.ClickedEvent += OnMaskClicked;
            _Mask.SetSize(814, 18);
            UIHelper.GetInstance().SetNextTo(_Mask, _LabelNavigation, 20);
            _PanelDialog.AddChild(_Mask);

            _TextureTreeItemFoldedFolder = UIManager.LoadUIImage("Editor/Tree/Project/FoldedFolder.png");
            _TextureTreeItemExpandedFolder = UIManager.LoadUIImage("Editor/Tree/Project/Folder.png");
            _TextureTreeItemFile = UIManager.LoadUIImage("Editor/Tree/Project/File.png");
            _TextureTreeItemTextFile = UIManager.LoadUIImage("Editor/Tree/Project/TextFile.png");

            _LabelFile = new Label();
            _LabelFile.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelFile.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelFile.SetTextAlign(TextAlign.CenterLeft);
            if (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile)
            {
                _LabelFile.SetText("File:");
            }
            else
            {
                _LabelFile.SetText("Folder:");
            }
            _LabelFile.SetPosition(50, 400, 66, 18);
            _PanelDialog.AddChild(_LabelFile);

            _EditFile = new Edit();
            _EditFile.SetFontSize(18);
            _EditFile.Initialize(EditMode.Simple_SingleLine);
            _EditFile.LoadSource("");
            _EditFile.SetSize(534, 18);
            _PanelDialog.AddChild(_EditFile);
            UIHelper.GetInstance().SetNextTo(_EditFile, _LabelFile, 20);
            EditContextUI.GetInstance().RegisterEdit(_EditFile);

            _LabelFilterItem = new Label();
            _LabelFilterItem.SetFontSize(Control.UI_DEFAULT_FONT_SIZE);
            _LabelFilterItem.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);
            _LabelFilterItem.SetTextAlign(TextAlign.CenterLeft);
            _LabelFilterItem.SetText("All Files(*.*)");
            _LabelFilterItem.SetSize(400, 18);
            UIHelper.GetInstance().SetNextTo(_LabelFilterItem, _EditFile, 20);
            _PanelDialog.AddChild(_LabelFilterItem);

            if (_PathInputUIFilterItem != null)
            {
                _LabelFilterItem.SetText(_PathInputUIFilterItem.ToString());
            }

            if (_PathInputUIType == PathInputUIType.OpenFolder ||
                _PathInputUIType == PathInputUIType.NewFolder)
            {
                _LabelFilterItem.SetVisible(false);
                _EditFile.SetSize(814, 18);
            }

            _TreeDrives = new Tree();
            _TreeDrives.Initialize();
            _TreeDrives.SetPosition(50, 80, 200, 300);
            _PanelDialog.AddChild(_TreeDrives);

            _TreeDrives.ItemSelectedEvent += OnTreeDrivesItemSelected;

            UpdateDrives();

            _Tree = new Tree();
            _Tree.Initialize();
            _Tree.SetPosition(260, 80, 690, 300);
            _PanelDialog.AddChild(_Tree);

            _Tree.ItemSelectedEvent += OnTreeItemSelected;
            _Tree.ItemOpenedEvent += OnTreeItemOpened;
            _Tree.ItemExpandedEvent += OnTreeItemExpanded;
            _Tree.RightMouseUpEvent += OnTreeRightMouseUp;

            ShowDirectory();

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(_PanelDialog.GetHeight() - 45);
        }

        #region Edit Navigation

        private void OnMaskClicked(Button Sender)
        {
            Sender.SetVisible(false);
            _EditNavigation.SetFocus();
            _EditNavigation.DoSelectAll();
        }

        private void OnEditNavigationCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (Char == '\r')
            {
                TryUpdateNavigation();
                _EditNavigation.DoSelectAll();
            }
        }

        private void OnEditNavigationFocusChanged(Control Sender)
        {
            if (Sender.IsFocused() == false)
            {
                _Mask.SetVisible(true);
                TryUpdateNavigation();
                _EditNavigation.SetSelection(new Selection());
            }
        }

        void TryUpdateNavigation()
        {
            string InputPath = PathHelper.ToStandardForm(_EditNavigation.GetText());
            if (File.Exists(InputPath) &&
                (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile))
            {
                string FileName = Path.GetFileName(InputPath);
                string Extension = Path.GetExtension(InputPath);
                string FileDirectory = PathHelper.GetDirectoryName(InputPath);

                if (IsMatchExtension(Extension) && IsInDrives(FileDirectory))
                {
                    _EditNavigation.SetText(FileDirectory);
                    _EditFile.SetText(FileName);
                    DoSelectDriveTree(FileDirectory);
                    DoSelectDirectoryTree(InputPath);
                    return;
                }
            }
            else if (Directory.Exists(InputPath) && IsInDrives(InputPath))
            {
                _EditNavigation.SetText(InputPath);
                _EditFile.SetText("");
                DoSelectDriveTree(InputPath);
                DoSelectDirectoryTree(InputPath);
                return;
            }

            _EditNavigation.SetText(_RootDirectory);
        }

        bool IsMatchExtension(string Extension)
        {
            if (_PathInputUIFilterItem == null)
                return true;

            if (_PathInputUIFilterItem.IsExtensionMatch(Extension))
                return true;

            return false;
        }

        bool IsInDrives(string Path)
        {
            foreach (Drive Drive in _Drives)
            {
                string DrivePath = PathHelper.ToStandardForm(Drive.Path);
                if (Path.StartsWith(DrivePath))
                {
                    return true;
                }
            }
            return false;
        }

        void DoSelectDriveTree(string Path)
        {
            _TreeDrives.GetRootItem().SetExpanded(true);
            foreach (Drive Drive in _Drives)
            {
                string DrivePath = PathHelper.ToStandardForm(Drive.Path);
                if (Path.StartsWith(DrivePath))
                {
                    TreeItem Target = _TreeDrives.FindItemByTagString(Drive.Path);
                    if (Target != null)
                    {
                        _TreeDrives.SelectItem(Target);
                    }
                }
            }
        }

        void DoSelectDirectoryTree(string Path)
        {
            TreeItem RootItem = _Tree.GetRootItem();
            Queue<TreeItem> Items = new Queue<TreeItem>();
            Items.Enqueue(RootItem);
            while (Items.Count > 0)
            {
                TreeItem Item = Items.Dequeue();
                if (Item.GetTagString() == Path)
                {
                    _Tree.SelectItem(Item);
                    Items.Clear();
                    break;
                }
                if (Path.StartsWith(Item.GetTagString()))
                {
                    ShowSubDirectoriesAndFiles(Item);
                }
                foreach (TreeItem Child in Item.GetChildList())
                {
                    Items.Enqueue(Child);
                }
            }
        }

        #endregion

        public override void ShowDialog()
        {
            base.ShowDialog();
            _EditFile.SetFocus();
            _EditFile.LocateToHome();
            _EditFile.DoSelectAll();

            Device Device = GetDevice();
            Device.ActivateEvent += OnDeviceActivate;
        }

        public override void CloseDialog()
        {
            base.CloseDialog();

            Device Device = GetDevice();
            Device.ActivateEvent -= OnDeviceActivate;
        }

        public void OnDeviceActivate(Device Sender, bool bActivated)
        {
            if (bActivated)
            {
                UpdateDirectory();
            }
        }

        public void AddDrive(string DriveName, string DrivePath)
        {
            Drive Drive = new Drive();
            Drive.Name = DriveName;
            Drive.Path = PathHelper.ToStandardForm(DrivePath);
            _Drives.Add(Drive);
        }

        public void AddDefaultDrives()
        {
            string DesktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
            AddDrive("Desktop", DesktopPath);

            string MyDocumentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            AddDrive("My Documents", MyDocumentsPath);

            string[] LogicalDrives = Environment.GetLogicalDrives();
            foreach (string LogicalDrive in LogicalDrives)
            {
                string LocalDrive1 = PathHelper.ToStandardForm(LogicalDrive);
                AddDrive(LocalDrive1, LocalDrive1);
            }
        }

        void UpdateDrives()
        {
            TreeItem RootItem = _TreeDrives.GetRootItem();
            RootItem.SetFolder(true);
            RootItem.SetExpanded(true);
            RootItem.SetText("My Computer");
            RootItem.SetImageFolded(_TextureTreeItemFoldedFolder);
            RootItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
            RootItem.SetTagString("");

            _MaxMatchLength = 0;
            if (_Drives.Count == 0)
            {
                AddDefaultDrives();
            }
            foreach (Drive Drive in _Drives)
            {
                AddDriveTreeItem(RootItem, Drive.Name, Drive.Path);
            }
        }

        void AddDriveTreeItem(TreeItem ParentItem, string DriveName, string DrivePath)
        {
            TreeItem TreeItem = _TreeDrives.CreateItem();
            TreeItem.SetFolder(true);
            TreeItem.SetExpanded(true);
            TreeItem.SetText(DriveName);
            TreeItem.SetImageFolded(_TextureTreeItemFoldedFolder);
            TreeItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
            TreeItem.SetTagString(DrivePath);
            ParentItem.AddChild(TreeItem);
            if (_RootDirectory.StartsWith(DrivePath))
            {
                int MatchLength = DrivePath.Length;
                if (MatchLength > _MaxMatchLength)
                {
                    _MaxMatchLength = MatchLength;
                    _TreeDrives.SelectItemNoEvent(TreeItem);
                }
            }
        }

        void OnTreeDrivesItemSelected(Tree Sender, TreeItem TreeItem)
        {
            string DrivePath = TreeItem.GetTagString();
            if (DrivePath != "")
            {
                _RootDirectory = DrivePath;
                _EditNavigation.SetText(DrivePath);
                ShowDirectory();
            }
        }

        void UpdateDirectory()
        {
            TreeItem RootItem = _Tree.GetRootItem();
            TreeState TreeState = _Tree.SaveState();
            ShowDirectory();
            ExpandDirectoryRecursively(RootItem, TreeState);
            _Tree.LoadState(TreeState);
        }

        void ExpandDirectoryRecursively(TreeItem TreeItem, TreeState TreeState)
        {
            string Path = TreeItem.GetTagString();
            if (Path == "")
            {
                return;
            }
            string TreeStatePath = TreeItem.GetPath();
            bool bExpanded = TreeState.GetExpanded(TreeStatePath);
            if (bExpanded)
            {
                ShowSubDirectoriesAndFiles(TreeItem);
                List<TreeItem> ChildList = TreeItem.GetChildList();
                foreach (TreeItem Child in ChildList)
                {
                    ExpandDirectoryRecursively(Child, TreeState);
                }
            }
        }

        void ShowDirectory()
        {
            TreeItem RootItem = _Tree.GetRootItem();
            RootItem.ClearChildren();
            ShowSingleDirectory(RootItem, _RootDirectory);
        }

        void ShowSingleDirectory(TreeItem TreeItem, string DirectoryPath)
        {
            DirectoryPath = PathHelper.ToStandardForm(DirectoryPath);
            string DirectoryName = "";
            if (DirectoryPath.Length == 3 &&
                StringHelper.GetChar(DirectoryPath, 1) == ':' &&
                StringHelper.GetChar(DirectoryPath, 2) == '/')
            {
                DirectoryName = DirectoryPath;
            }
            else
            {
                DirectoryName = PathHelper.GetFileName(DirectoryPath);
            }
            if (DirectoryPath == "")
            {
                DirectoryName = "Empty";
            }
            TreeItem.SetFolder(true);
            TreeItem.SetExpanded(false);
            TreeItem.SetText(DirectoryName);
            TreeItem.SetImageFolded(_TextureTreeItemFoldedFolder);
            TreeItem.SetImageExpanded(_TextureTreeItemExpandedFolder);
            TreeItem.SetTagString(DirectoryPath);

            bool bHasChild = false;
            DirectoryInfo[] SubDirectoryInfos = DirectoryHelper.GetSubDirectories(DirectoryPath);
            if (SubDirectoryInfos.Length > 0)
            {
                bHasChild = true;
            }
            else
            {
                if (_PathInputUIType == PathInputUIType.OpenFile ||
                    _PathInputUIType == PathInputUIType.SaveFile)
                {
                    FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(DirectoryPath);
                    int Count = FileInfos.Length;
                    for (int i = 0; i < Count; i++)
                    {
                        FileInfo FileInfo = FileInfos[i];
                        string FilePath = FileInfo.FullName;
                        if (_PathInputUIFilterItem != null)
                        {
                            string Extension = PathHelper.GetExtension(FilePath);
                            if (_PathInputUIFilterItem.IsExtensionMatch(Extension) == false)
                            {
                                continue;
                            }
                        }
                        bHasChild = true;
                        break;
                    }
                }
            }
            if (bHasChild)
            {
                TreeItem TreeItemDummyChild = _Tree.CreateItem();
                TreeItem.AddChild(TreeItemDummyChild);
            }
            else
            {
                TreeItem.SetImageExpanded(_TextureTreeItemFoldedFolder);
            }
        }

        void ShowSubDirectoriesAndFiles(TreeItem TreeItem)
        {
            string DirectoryPath = TreeItem.GetTagString();
            TreeItem.ClearChildren();

            DirectoryPath = PathHelper.ToStandardForm(DirectoryPath);
            DirectoryInfo[] SubDirectoryInfos = DirectoryHelper.GetSubDirectories(DirectoryPath);
            int Count = SubDirectoryInfos.Length;
            for (int i = 0; i < Count; i++)
            {
                DirectoryInfo SubDirectoryInfo = SubDirectoryInfos[i];
                TreeItem TreeItemDirectory = _Tree.CreateItem();
                TreeItem.AddChild(TreeItemDirectory);
                ShowSingleDirectory(TreeItemDirectory, SubDirectoryInfo.FullName);
            }

            if (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile)
            {
                FileInfo[] FileInfos = DirectoryHelper.GetFilesInDirectory(DirectoryPath);
                Count = FileInfos.Length;
                for (int i = 0; i < Count; i++)
                {
                    FileInfo FileInfo = FileInfos[i];
                    string FilePath = FileInfo.FullName;
                    if (_PathInputUIFilterItem != null)
                    {
                        string Extension = PathHelper.GetExtension(FilePath);
                        if (_PathInputUIFilterItem.IsExtensionMatch(Extension) == false)
                        {
                            continue;
                        }
                    }
                    FilePath = PathHelper.ToStandardForm(FilePath);
                    string FileName = FileInfo.Name;
                    TreeItem TreeItemFile = _Tree.CreateItem();
                    TreeItemFile.SetFolder(false);
                    TreeItemFile.SetExpanded(false);
                    TreeItemFile.SetText(FileName);
                    TreeItemFile.SetImage(_TextureTreeItemTextFile);
                    TreeItemFile.SetTagString(FilePath);
                    TreeItem.AddChild(TreeItemFile);
                }
            }
        }

        public string GetPathInputed()
        {
            string DirectoryPath = _EditNavigation.GetText();
            string FileName = _EditFile.GetText();
            if (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile)
            {
                if (FileName == "")
                {
                    return "";
                }
                string FilePath = PathHelper.CombinePath(DirectoryPath, FileName);
                if (_PathInputUIFilterItem != null)
                {
                    string Extension = PathHelper.GetExtension(FilePath);
                    if (_PathInputUIType == PathInputUIType.SaveFile)
                    {
                        if (Extension == "")
                        {
                            if (FileName.Contains(".") == false)
                            {
                                string DefaultExtension = _PathInputUIFilterItem.GetDefaultExtension();
                                if (DefaultExtension != "")
                                {
                                    FilePath = FilePath + "." + DefaultExtension;
                                    Extension = DefaultExtension;
                                }
                            }
                        }
                    }
                    if (_PathInputUIFilterItem.IsExtensionMatch(Extension) == false)
                    {
                        return "";
                    }
                }
                if (_PathInputUIType == PathInputUIType.OpenFile)
                {
                    if (File.Exists(FilePath) == false)
                    {
                        return "";
                    }
                }
                return FilePath;
            }
            else if (_PathInputUIType == PathInputUIType.OpenFolder ||
                     _PathInputUIType == PathInputUIType.NewFolder)
            {
                if (FileName == "")
                {
                    return "";
                }
                string DirectoryPath1 = PathHelper.CombinePath(DirectoryPath, FileName);
                if (_PathInputUIType == PathInputUIType.OpenFolder)
                {
                    if (Directory.Exists(DirectoryPath1) == false)
                    {
                        return "";
                    }
                }
                else
                {
                    if (Directory.Exists(DirectoryPath1))
                    {
                        return "";
                    }
                }
                return DirectoryPath1;
            }
            else
            {
                return "";
            }
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                Control FocusControl = GetUIManager().GetFocusControl();
                if (FocusControl != null &&
                    FocusControl.IsChildOf(_PanelDialog) &&
                    FocusControl != _EditNavigation)
                {
                    OnButtonOKClicked(null);
#if EDITOR_UI_TEMP
                    Device.CharEvent.ForceStop();
#endif
                }
            }
        }

        void OnButtonOKClicked(Button Sender)
        {
            string PathInputed = GetPathInputed();
            if (PathInputed != "")
            {
                if (_PathInputUIType == PathInputUIType.SaveFile &&
                    File.Exists(PathInputed))
                {
                    string FileName = PathHelper.GetFileName(PathInputed);
                    string Message = string.Format("File \"{0}\" already exists, do you really want to override this file?", FileName);
                    CommonDialogUI CommonDialogUI = new CommonDialogUI();
                    CommonDialogUI.Initialize(GetUIManager(), "Tips", Message, CommonDialogType.YesNo);
                    CommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
                    {
                        if (Result == CommonDialogResult.Yes)
                        {
                            if (InputedEvent != null)
                            {
                                InputedEvent(this, PathInputed);
                            }
                            CloseDialog();
                        }
                        else if (Result == CommonDialogResult.No)
                        {
                        }
                    };
                    DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
                }
                else
                {
                    if (InputedEvent != null)
                    {
                        InputedEvent(this, PathInputed);
                    }
                    CloseDialog();
                }
            }
        }

        void OnTreeItemSelected(Tree Sender, TreeItem TreeItem)
        {
            if (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile)
            {
                if (TreeItem.GetFolder() == false)
                {
                    string FilePath = TreeItem.GetTagString();
                    string FileName = PathHelper.GetFileName(FilePath);
                    string Directory = PathHelper.GetDirectoryName(FilePath);
                    _EditNavigation.SetText(Directory);
                    _EditFile.SetText(FileName);
                }
                else
                {
                    string DirectoryPath = TreeItem.GetTagString();
                    _EditNavigation.SetText(DirectoryPath);
                }
            }
            else if (_PathInputUIType == PathInputUIType.OpenFolder)
            {
                if (TreeItem.GetFolder() == true)
                {
                    string DirectoryPath = TreeItem.GetTagString();
                    string DirectoryName = PathHelper.GetFileName(DirectoryPath);
                    string DirectoryDirectory = PathHelper.GetDirectoryName(DirectoryPath);
                    _EditNavigation.SetText(DirectoryDirectory);
                    _EditFile.SetText(DirectoryName);
                }
            }
            else if (_PathInputUIType == PathInputUIType.NewFolder)
            {
                if (TreeItem.GetFolder() == true)
                {
                    string DirectoryPath = TreeItem.GetTagString();
                    _EditNavigation.SetText(DirectoryPath);
                }
            }
        }

        void OnTreeItemOpened(Control Sender, TreeItem TreeItem)
        {
            if (_PathInputUIType == PathInputUIType.OpenFile ||
                _PathInputUIType == PathInputUIType.SaveFile)
            {
                if (TreeItem.GetFolder() == false)
                {
                    OnButtonOKClicked(null);
                }
            }
            else if (_PathInputUIType == PathInputUIType.OpenFolder)
            {
                if (TreeItem.GetFolder())
                {
                    if (TreeItem.GetChildCount() == 0)
                    {
                        OnButtonOKClicked(null);
                    }
                }
            }
        }

        void OnTreeItemExpanded(Tree Sender, TreeItem TreeItem)
        {
            ShowSubDirectoriesAndFiles(TreeItem);
        }

        void OnTreeRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (Sender.IsPointIn(MouseX, MouseY) == false)
            {
                return;
            }
            TreeItemHitTest HitTest = _Tree.HitTest(MouseX, MouseY);
            if (HitTest.HitResult != TreeItemHitResult.Nothing)
            {
                ShowContextMenu_Item(MouseX, MouseY);
            }
            else
            {
                ShowContextMenu_Blank(MouseX, MouseY);
            }
            bContinue = false;
        }

        void ShowContextMenu_Item(int MouseX, int MouseY)
        {
            MenuItem MenuItem_ShowInExplorer = new MenuItem();
            MenuItem_ShowInExplorer.SetText("Show In Explorer");
            MenuItem_ShowInExplorer.ClickedEvent += OnMenuItemShowInExplorerClicked;

            MenuItem MenuItem_CreateFolder = new MenuItem();
            MenuItem_CreateFolder.SetText("Create Folder");
            MenuItem_CreateFolder.ClickedEvent += OnMenuItemCreateFolderClicked;

            MenuItem MenuItem_DeleteFolder = new MenuItem();
            MenuItem_DeleteFolder.SetText("Delete Folder");
            MenuItem_DeleteFolder.ClickedEvent += OnMenuItemDeleteFolderClicked;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuContextMenu.AddMenuItem(MenuItem_ShowInExplorer);

            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null && SelectedItem.GetFolder())
            {
                MenuContextMenu.AddSeperator();
                MenuContextMenu.AddMenuItem(MenuItem_CreateFolder);
                MenuContextMenu.AddMenuItem(MenuItem_DeleteFolder);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }

        void ShowContextMenu_Blank(int MouseX, int MouseY)
        {
            MenuItem MenuItem_CreateFolder = new MenuItem();
            MenuItem_CreateFolder.SetText("Create Folder");
            MenuItem_CreateFolder.ClickedEvent += OnMenuItemCreateFolderClicked;

            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();
            MenuContextMenu.AddMenuItem(MenuItem_CreateFolder);

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, MouseX, MouseY);
        }

        void OnMenuItemShowInExplorerClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null)
            {
                string Path = SelectedItem.GetTagString();
                ProcessHelper.OpenContainingFolder(Path);
            }
        }

        void OnMenuItemCreateFolderClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem == null)
            {
                SelectedItem = _Tree.GetRootItem();
            }
            string Path = SelectedItem.GetTagString();

            TextInputUI TextInputUI = new TextInputUI();
            TextInputUI.Initialize(GetUIManager(), "Input Folder Name", "Please input a folder name:", "NewFolder");
            TextInputUI.InputedEvent += (TextInputUI Sender, string StringInputed) =>
            {
                if (StringInputed != "")
                {
                    string DirectoryName = Path + "/" + StringInputed;
                    DirectoryHelper.CreateDirectory(DirectoryName);
                    UpdateDirectory();

                    TreeItem Root = _Tree.GetRootItem();
                    TreeItem TreeItem = Root.SearchByTagString(Path);
                    if (TreeItem != null)
                    {
                        TreeItem.SetExpanded(true);
                        ShowSubDirectoriesAndFiles(TreeItem);
                    }
                }
            };
            TextInputUI.ShowDialog();
        }

        void OnMenuItemDeleteFolderClicked(MenuItem MenuItem)
        {
            TreeItem SelectedItem = _Tree.GetSelectedItem();
            if (SelectedItem != null)
            {
                string Path = SelectedItem.GetTagString();
                string DirectoryName = PathHelper.GetFileName(Path);
                string Question = string.Format("Do you want to delete folder: {0}?", DirectoryName);
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Tips", Question, CommonDialogType.OKCancel);
                CommonDialogUI.CloseEvent += (CommonDialogUI Sender, CommonDialogResult Result) =>
                {
                    if (Result == CommonDialogResult.OK)
                    {
                        TreeItem NextVisibileNonChild = _Tree.FindNextVisibleNonChild();
                        if (NextVisibileNonChild != null)
                        {
                            _Tree.SelectItem(NextVisibileNonChild);
                        }
                        DirectoryHelper.DeleteDirectory(Path);
                        UpdateDirectory();
                    }
                };
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
            }
        }
    }
}
