using EditorUI;

namespace CrossEditor
{
    public class ReadOnlyTextUI : DialogUI
    {
        Edit _Edit;

        public ReadOnlyTextUI()
        {
        }

        public void Initialize(UIManager UIManager, string Title, string content)
        {
            int width = 1500;
            int height = 1600;
            int marginX = 15;
            int marginTop = 40;
            int marginBottom = 15;

            base.Initialize(UIManager, Title, width, height);

            _Edit = new Edit();
            _Edit.SetFontSize(18);
            _Edit.Initialize(EditMode.Simple_SingleLine);
            _Edit.LoadSource("");
            _Edit.SetPosition(marginX, marginTop, width - marginX * 2, height - marginTop - marginBottom);
            _Edit.SetText(content);
            _Edit.SetReadOnly(true);
            EditContextUI.GetInstance().RegisterEdit(_Edit);

            _PanelDialog.AddChild(_Edit);
        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }
    }
}
