using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperationRecord
    {
        public List<EditOperation> mUndoList;
        public List<EditOperation> mRedoList;

        public EditOperationRecord()
        {
            mUndoList = new List<EditOperation>();
            mRedoList = new List<EditOperation>();
        }
    }

    class EditOperationManager
    {
        static EditOperationManager _Instance = new EditOperationManager();

        string _CurRecordKey;
        EditOperationRecord _CurRecord;
        Dictionary<string, EditOperationRecord> _RecordMap = new Dictionary<string, EditOperationRecord>();

        public static EditOperationManager GetInstance()
        {
            return _Instance;
        }

        EditOperationManager()
        {
            SetRecord();
        }

        public void SetRecord(string key = "Default")
        {
            if (_CurRecordKey != key)
            {
                if (!_RecordMap.ContainsKey(key))
                {
                    _RecordMap[key] = new EditOperationRecord();
                }
                _CurRecord = _RecordMap[key];
                _CurRecordKey = key;
            }
        }

        public void ClearAll()
        {
            _RecordMap.Clear();
        }

        public void Clear(string key = "Default")
        {
            if (_RecordMap.ContainsKey(key))
            {
                _RecordMap[key].mUndoList.Clear();
                _RecordMap[key].mRedoList.Clear();
            }
        }

        public void AddOperation(EditOperation Operation)
        {
            if (Operation.Valid())
            {
                _CurRecord.mUndoList.Add(Operation);
                _CurRecord.mRedoList.Clear();
            }
        }

        public bool CanUndo()
        {
            return _CurRecord.mUndoList.Count > 0;
        }

        public bool CanRedo()
        {
            return _CurRecord.mRedoList.Count > 0;
        }

        public void Undo()
        {
            if (_CurRecord.mUndoList.Count > 0)
            {
                EditOperation Undo = _CurRecord.mUndoList.Pop();
                Undo.Undo();
                _CurRecord.mRedoList.Add(Undo);
            }
        }

        public void Redo()
        {
            if (_CurRecord.mRedoList.Count > 0)
            {
                EditOperation Redo = _CurRecord.mRedoList.Pop();
                Redo.Redo();
                _CurRecord.mUndoList.Add(Redo);
            }
        }

        public EditOperation GetLatestOperation()
        {
            if (_CurRecord.mUndoList.Count > 0)
            {
                return _CurRecord.mUndoList[_CurRecord.mUndoList.Count - 1];
            }
            else
            {
                return null;
            }
        }

        bool IsSameModifyPropertyOperation(EditOperation Operation1, EditOperation Operation2)
        {
            if (Operation1 == null)
            {
                return false;
            }
            if (Operation2 == null)
            {
                return false;
            }
            if (!(Operation1 is EditOperation_ModifyProperty))
            {
                return false;
            }
            if (!(Operation2 is EditOperation_ModifyProperty))
            {
                return false;
            }
            EditOperation_ModifyProperty Operation_ModifyProperty1 = (EditOperation_ModifyProperty)Operation1;
            EditOperation_ModifyProperty Operation_ModifyProperty2 = (EditOperation_ModifyProperty)Operation2;
            if (Operation_ModifyProperty1._bCombinable == false)
            {
                return false;
            }
            if (Operation_ModifyProperty2._bCombinable == false)
            {
                return false;
            }
            if (Operation_ModifyProperty1._Object != Operation_ModifyProperty2._Object)
            {
                return false;
            }
            if (Operation_ModifyProperty1._ObjectProperty.Name != Operation_ModifyProperty2._ObjectProperty.Name)
            {
                return false;
            }
            return true;
        }

        bool IsSameModifyProperties2Operation(EditOperation Operation1, EditOperation Operation2)
        {
            if (Operation1 == null)
            {
                return false;
            }
            if (Operation2 == null)
            {
                return false;
            }
            if (!(Operation1 is EditOperation_ModifyProperties2))
            {
                return false;
            }
            if (!(Operation2 is EditOperation_ModifyProperties2))
            {
                return false;
            }
            EditOperation_ModifyProperties2 Operation_ModifyProperties2_1 = (EditOperation_ModifyProperties2)Operation1;
            EditOperation_ModifyProperties2 Operation_ModifyProperties2_2 = (EditOperation_ModifyProperties2)Operation2;
            if (Operation_ModifyProperties2_1._bCombinable == false)
            {
                return false;
            }
            if (Operation_ModifyProperties2_2._bCombinable == false)
            {
                return false;
            }
            int Count1 = Operation_ModifyProperties2_1._ModifyPropertyItemList.Count;
            int Count2 = Operation_ModifyProperties2_2._ModifyPropertyItemList.Count;
            if (Count1 != Count2)
            {
                return false;
            }
            for (int i = 0; i < Count1; i++)
            {
                ModifyPropertyItem ModifyPropertyItem1 = Operation_ModifyProperties2_1._ModifyPropertyItemList[i];
                ModifyPropertyItem ModifyPropertyItem2 = Operation_ModifyProperties2_2._ModifyPropertyItemList[i];
                if (ModifyPropertyItem1._Object != ModifyPropertyItem2._Object)
                {
                    return false;
                }
                if (ModifyPropertyItem1._ObjectProperty.Name != ModifyPropertyItem2._ObjectProperty.Name)
                {
                    return false;
                }
            }
            return true;
        }

        bool IsSameModifyPropertyOrModifyPropertiesOperation(EditOperation Operation1, EditOperation Operation2)
        {
            return IsSameModifyPropertyOperation(Operation1, Operation2) ||
                   IsSameModifyProperties2Operation(Operation1, Operation2);
        }

        EditOperation CombineTwoModifyPropertyOperation(EditOperation OperationOld, EditOperation OperationNew)
        {
            EditOperation_ModifyProperty EditOperation_ModifyPropertyOld = (EditOperation_ModifyProperty)OperationOld;
            EditOperation_ModifyProperty EditOperation_ModifyPropertyNew = (EditOperation_ModifyProperty)OperationNew;
            ObjectProperty ObjectProperty = EditOperation_ModifyPropertyNew._ObjectProperty;
            object OldValue = EditOperation_ModifyPropertyOld._OldValue;
            object NewValue = EditOperation_ModifyPropertyNew._NewValue;
            Inspector ValueContainer = EditOperation_ModifyPropertyNew._ValueConatainer;
            EditOperation_ModifyProperty Operation_ModifyProperty = new EditOperation_ModifyProperty(ObjectProperty, OldValue, NewValue, ValueContainer);
            return Operation_ModifyProperty;
        }

        EditOperation CombineTwoModifyPropertiesOperation(EditOperation OperationOld, EditOperation OperationNew)
        {
            EditOperation_ModifyProperties2 EditOperation_ModifyPropertiesOld = (EditOperation_ModifyProperties2)OperationOld;
            EditOperation_ModifyProperties2 EditOperation_ModifyPropertiesNew = (EditOperation_ModifyProperties2)OperationNew;
            EditOperation_ModifyProperties2 EditOperation_ModifyProperties = new EditOperation_ModifyProperties2();
            int Count = EditOperation_ModifyPropertiesOld._ModifyPropertyItemList.Count;
            for (int i = 0; i < Count; i++)
            {
                ModifyPropertyItem ModifyPropertyItemOld = EditOperation_ModifyPropertiesOld._ModifyPropertyItemList[i];
                ModifyPropertyItem ModifyPropertyItemNew = EditOperation_ModifyPropertiesNew._ModifyPropertyItemList[i];
                object Object = ModifyPropertyItemNew._Object;
                ObjectProperty ObjectProperty = ModifyPropertyItemNew._ObjectProperty;
                object OldValue = ModifyPropertyItemOld._OldValue;
                object NewValue = ModifyPropertyItemNew._NewValue;
                EditOperation_ModifyProperties.AddModifyProperty(Object, ObjectProperty, OldValue, NewValue);
            }
            return EditOperation_ModifyProperties;
        }

        EditOperation CombineTwoModifyPropertyOrModifyPropertiesOperation(EditOperation OperationOld, EditOperation OperationNew)
        {
            if (OperationOld is EditOperation_ModifyProperty)
            {
                return CombineTwoModifyPropertyOperation(OperationOld, OperationNew);
            }
            else if (OperationOld is EditOperation_ModifyProperties2)
            {
                return CombineTwoModifyPropertiesOperation(OperationOld, OperationNew);
            }
            return null;
        }

        public void AddOperation_AutoCombine(EditOperation Operation)
        {
            EditOperation LatestOperation = GetLatestOperation();
            if (IsSameModifyPropertyOrModifyPropertiesOperation(LatestOperation, Operation))
            {
                _CurRecord.mUndoList.Pop();
                EditOperation OperationCombined = CombineTwoModifyPropertyOrModifyPropertiesOperation(LatestOperation, Operation);
                _CurRecord.mUndoList.Add(OperationCombined);
            }
            else
            {
                _CurRecord.mUndoList.Add(Operation);
            }
            _CurRecord.mRedoList.Clear();
        }

        public void BeginRecordCompoundOperation()
        {
            DebugHelper.Assert(_CurRecordKey != "Compound");
            SetRecord("Compound");
        }

        public void EndRecordCompoundOperation()
        {
            DebugHelper.Assert(_CurRecordKey == "Compound");
            EditOperation_Compound EditOperation_Compound = new EditOperation_Compound(_CurRecord.mUndoList);
            SetRecord();
            AddOperation(EditOperation_Compound);
            Clear("Compound");
        }
    }
}
