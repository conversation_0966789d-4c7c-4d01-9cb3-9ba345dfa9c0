namespace CrossEditor
{
    class EditOperation_ChangeMaterial : EditOperation
    {
        Entity _TargetEntity;
        int _ModelIndex;
        int _MeshIndex;
        int _lodIndex;
        string _OldMaterialPath;
        string _NewMaterialPath;

        public EditOperation_ChangeMaterial(Entity TargetEntity, int ModelIndex, int MeshIndex, int lodindex, string OldMaterialPath, string NewMaterialPath)
        {
            _TargetEntity = TargetEntity;
            _ModelIndex = ModelIndex;
            _MeshIndex = MeshIndex;
            _lodIndex = lodindex;
            _OldMaterialPath = OldMaterialPath;
            _NewMaterialPath = NewMaterialPath;
        }

        public override void Undo()
        {
            ModelComponent ModelComponent = _TargetEntity.GetModelComponent();
            if (ModelComponent != null)
            {
                if (ModelComponent.Models.Count > _ModelIndex)
                {
                    Model Model = ModelComponent.Models[_ModelIndex];
                    if (Model.LODProperties.Count > _MeshIndex)
                    {
                        LODProperty SubModelProperty = Model.LODProperties[_lodIndex];
                        SubModelProperty.SubModels[_MeshIndex].MaterialPath = _OldMaterialPath;
                        ModelComponent.Models = ModelComponent.Models;
                    }
                }
            }
        }

        public override void Redo()
        {
            ModelComponent ModelComponent = _TargetEntity.GetModelComponent();
            if (ModelComponent != null)
            {
                if (ModelComponent.Models.Count > _ModelIndex)
                {
                    Model Model = ModelComponent.Models[_ModelIndex];
                    if (Model.LODProperties.Count > _MeshIndex)
                    {
                        LODProperty SubModelProperty = Model.LODProperties[_lodIndex];
                        SubModelProperty.SubModels[_MeshIndex].MaterialPath = _NewMaterialPath;
                        ModelComponent.Models = ModelComponent.Models;
                    }
                }
            }
        }
    }
}
