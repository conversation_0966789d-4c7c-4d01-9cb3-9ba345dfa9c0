using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperation_Compound : EditOperation
    {
        List<EditOperation> _EditOperationList;

        public EditOperation_Compound(List<EditOperation> EditOperationList)
        {
            _EditOperationList = new List<EditOperation>();
            foreach (EditOperation EditOperation in EditOperationList)
            {
                _EditOperationList.Add(EditOperation);
            }
        }

        public override void Undo()
        {
            int Count = _EditOperationList.Count;
            for (int i = Count - 1; i >= 0; i--)
            {
                EditOperation EditOperation = _EditOperationList[i];
                EditOperation.Undo();
            }
        }

        public override void Redo()
        {
            int Count = _EditOperationList.Count;
            for (int i = 0; i < Count; i++)
            {
                EditOperation EditOperation = _EditOperationList[i];
                EditOperation.Redo();
            }
        }
    }
}
