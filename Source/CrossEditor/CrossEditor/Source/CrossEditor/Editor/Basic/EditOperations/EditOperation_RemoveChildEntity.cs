namespace CrossEditor
{
    class EditOperation_RemoveChildEntity : EditOperation
    {
        Entity _ParentEntity;
        Entity _ChildEntity;
        int _EntityIndex;

        public EditOperation_RemoveChildEntity(Entity ParentEntity, Entity ChildEntity, int EntityIndex)
        {
            _ParentEntity = ParentEntity;
            _ChildEntity = ChildEntity;
            _EntityIndex = EntityIndex;
        }

        public override void Undo()
        {
            _ParentEntity.InsertChildEntity(_ChildEntity, _EntityIndex);
            _ChildEntity.RuntimeAdd();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_ChildEntity);
        }

        public override void Redo()
        {
            _ParentEntity.RemoveChildEntity(_ChildEntity);
            _ChildEntity.RuntimeRemove();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_ParentEntity);
        }
    }
}
