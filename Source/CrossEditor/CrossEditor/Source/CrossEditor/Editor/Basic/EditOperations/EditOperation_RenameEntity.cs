namespace CrossEditor
{
    class EditOperation_RenameEntity : EditOperation
    {
        Entity _Entity;
        string _OldName;
        string _NewName;

        public EditOperation_RenameEntity(Entity Entity, string OldName, string NewName)
        {
            _Entity = Entity;
            _OldName = OldName;
            _NewName = NewName;
        }

        public override void Undo()
        {
            _Entity.SetName(_OldName);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_Entity);
        }

        public override void Redo()
        {
            _Entity.SetName(_NewName);
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_Entity);
        }
    }
}
