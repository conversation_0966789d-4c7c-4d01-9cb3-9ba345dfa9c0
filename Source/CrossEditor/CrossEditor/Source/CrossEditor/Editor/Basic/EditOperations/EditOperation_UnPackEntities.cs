using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    class EditOperation_UnPackEntities : EditOperation
    {
        Entity _RootEntity;
        Entity _CurEntity;
        List<Entity> _SelectedEntities;
        public EditOperation_UnPackEntities(Entity RootEntity, Entity CurEntity, List<Entity> SelectedEntities)
        {
            _RootEntity = RootEntity;
            _CurEntity = CurEntity;
            _SelectedEntities = SelectedEntities;
        }

        public override void Undo()
        {
            foreach (Entity Value in _SelectedEntities)
            {
                _RootEntity.RemoveChildEntity(Value);
                _CurEntity.AddChildEntity(Value);
                Value.RuntimeJointToParent();
            }

            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(_CurEntity);
            EditorScene.GetInstance().SetDirty();

            TreeItem Item = HierarchyUI.GetTree().FindItemByTagObject(_CurEntity);
            Item.SetExpanded(true);
        }

        public override void Redo()
        {
            Entity CurEntity = _CurEntity;
            for (int i = 0; i < CurEntity.Children.Count; i++)
            {
                _RootEntity.AddChildEntity(CurEntity.Children[i]);
                CurEntity.Children[i].RuntimeJointToParent();
            }
            CurEntity.RemoveChildEntities();
            HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
            HierarchyUI.UpdateHierarchy();
            HierarchyUI.SelectEntity(null);
        }
    }
}
