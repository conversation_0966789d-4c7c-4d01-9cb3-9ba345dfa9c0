using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class InspectorHandler
    {
        public SimpleDelegate UpdateLayout;
        public SimpleDelegate InspectObject;
        public SimpleDelegate ReadValue;
    }

    public class InspectorUI : DockingUI
    {
        static InspectorUI _Instance = new InspectorUI();
        static InspectorUI _BrushInstance = new InspectorUI();

        public VContainer _Container;
        public ScrollView _ScrollView;
        public Panel _ScrollPanel;
        public OperationBarUI _OperationBarUI;
        public SearchUI _SearchUI;

        protected object _ObjectInspected;
        protected object _ObjectTag;
        protected Inspector _Inspector;

        protected InspectorHandler _InspectorHandler;

        public static InspectorUI GetInstance() => _Instance;

        public static InspectorUI GetBrushInstance() => _BrushInstance;

        public InspectorUI()
        {
            _InspectorHandler = new InspectorHandler();
            _InspectorHandler.InspectObject = DoInspectObject;
            _InspectorHandler.UpdateLayout = DoUpdateLayout;
            _InspectorHandler.ReadValue = DoReadValue;
        }

        public virtual bool Initialize(string Title)
        {
            _Container = new VContainer();
            _Container.Initialize();
            _Container.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();
            _Container.AddFixedChild(_OperationBarUI.GetPanelBar());

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollView.GetHScroll().SetEnable(false);
            _Container.AddSizableChild(_ScrollView, 1.0f);

            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_GENERAL_BACK_COLOR);
            _ScrollPanel.PaintEvent += (Sender) =>
            {
                SetChildInnerEnable(Sender);
                Sender.PaintThis();
                Sender.PaintChildren();
            };

            base.Initialize(Title, _Container);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;

            return true;
        }

        public void SetChildInnerEnable(Control Control)
        {
            for (int i = 0; i < Control.GetChildCount(); ++i)
            {
                Control Child = Control.GetChild(i);
                if (UIManager.RectInRect(Child.GetScreenX(), Child.GetScreenY(), Child.GetWidth(), Child.GetHeight(),
                    _ScrollView.GetScreenX(), _ScrollView.GetScreenY(), _ScrollView.GetWidth(), _ScrollView.GetHeight()))
                {
                    Child.SetInnerEnable(true);

                    if (Child.GetChildCount() != 0)
                    {
                        SetChildInnerEnable(Child);
                    }
                }
                else
                {
                    Child.SetInnerEnable(false);
                }
            }
        }

        void OnObjectsInspectedChanged()
        {
            _ScrollView.ResetScroll();
        }

        public object GetObjectInspected() => _ObjectInspected;

        public void SetObjectInspected(object ObjectInspected)
        {
            if (ObjectInspected is List<Entity>)
            {
                List<Entity> Entities = (List<Entity>)ObjectInspected;
                if (Entities.Count == 0)
                {
                    ObjectInspected = null;
                }

                if (_ObjectInspected is List<Entity> && (_ObjectInspected as List<Entity>).Count == Entities.Count)
                {
                    for (int i = 0; i < Entities.Count; ++i)
                    {
                        if ((_ObjectInspected as List<Entity>)[i] != Entities[i])
                        {
                            OnObjectsInspectedChanged();
                            break;
                        }
                    }
                }
                else
                {
                    OnObjectsInspectedChanged();
                }
            }
            else
            {
                if (_ObjectInspected != ObjectInspected)
                {
                    OnObjectsInspectedChanged();
                }
            }

            _ObjectInspected = ObjectInspected;
            if (_Inspector != null)
            {
                _Inspector.WriteValue();
            }

            if (_ObjectInspected == null)
            {
                _Inspector = null;
            }

            _OperationBarUI.GetPanelBar().SetVisible(_ObjectInspected != null);
            _SearchUI.ClearSearchPattern();
        }

        public Inspector GetInspector() => _Inspector;

        public void SetInspector(Inspector Inspector)
        {
            _Inspector = Inspector;
        }

        public InspectorHandler GetInspectorHandler() => _InspectorHandler;

        public void SetInspectorHandler(InspectorHandler InspectorHandler)
        {
            _InspectorHandler = InspectorHandler;
        }

        public Inspector_Entity GetEntityInspector()
        {
            return _Inspector as Inspector_Entity;
        }

        public Inspector_GameObject GetGameObjectInspector()
        {
            if (_Inspector == null)
            {
                return null;
            }
            else
            {
                foreach (var inspector in _Inspector.GetChildInspectors())
                {
                    if (inspector is Inspector_GameObject)
                    {
                        return inspector as Inspector_GameObject;
                    }
                }
            }
            return null;
        }

        void DoInspectObject()
        {
            _SearchUI.ClearSearchPattern();
            _ScrollPanel.ClearChildren();
            if (_ObjectInspected != null)
            {
                InspectorManager InspectorManager = InspectorManager.GetInstance();
                Type Type = _ObjectInspected.GetType();
                _Inspector = InspectorManager.CreateObjectInspector(Type);
                if (_Inspector != null)
                {
                    _Inspector.SetContainer(_ScrollPanel);
                    _Inspector.SetInspectorHandler(_InspectorHandler);
                    _Inspector.InspectObject(_ObjectInspected, _ObjectTag);
                    _Inspector.UpdateCheckExpand();
                }
                UpdateLayout();
            }
        }

        public void SetObjectTag(Object Tag)
        {
            _ObjectTag = Tag;
        }

        public void InspectObject()
        {
            _InspectorHandler.InspectObject();
            _InspectorHandler.UpdateLayout();
        }

        void DoUpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            int Y = 0;
            if (_Inspector != null && _ObjectInspected != null)
            {
                _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                if (Y > _ScrollView.GetHeight())
                {
                    ScrollPanelWidth = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
                    Y = 0;
                    _Inspector.UpdateLayout(ScrollPanelWidth, ref Y);
                }
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public void UpdateLayout()
        {
            _InspectorHandler.UpdateLayout();
        }

        public override void Update(long TimeElapsed)
        {
            base.Update(TimeElapsed);
            if (_Inspector != null)
            {
                _Inspector.Update();
            }
        }
        public void DoReadValue()
        {
            if (_Inspector != null)
            {
                _Inspector.ReadValue();
            }
        }

        public void ReadValue()
        {
            _InspectorHandler.ReadValue();
        }

        public void ReadValueAndUpdateLayout()
        {
            ReadValue();
            UpdateLayout();
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                _SearchUI.GetPanelBack().SetWidth(_Container.GetWidth() - 12);
                _ScrollView.SetWidth(_Container.GetWidth());
                UpdateLayout();
            }
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (_ScrollView.GetVisible_Recursively() && _ScrollView.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    DropPathOnInspector(_Inspector, MouseX, MouseY, PathesDragged);
                }
                HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
                if (HierarchyUI.IsDraggingEntity())
                {
                    Entity Entity = HierarchyUI.GetEntityDragged();
                    DropEntityOnInspector(_Inspector, MouseX, MouseY, Entity);
                }
            }
        }

        bool DropPathOnInspector(Inspector Inspector, int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (Inspector == null)
            {
                return false;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                if (DropPathOnInspector(ChildInspector, MouseX, MouseY, PathesDragged))
                {
                    return true;
                }
            }
            return Inspector.OnDropPathes(MouseX, MouseY, PathesDragged);
        }

        void DropEntityOnInspector(Inspector Inspector, int MouseX, int MouseY, Entity EntityDragged)
        {
            if (Inspector == null)
            {
                return;
            }
            List<Inspector> ChildInspectors = Inspector.GetChildInspectors();
            foreach (Inspector ChildInspector in ChildInspectors)
            {
                DropEntityOnInspector(ChildInspector, MouseX, MouseY, EntityDragged);
            }
            Inspector.OnDropEntity(MouseX, MouseY, EntityDragged);
        }

        public void ScrollToInspector(Inspector Inspector)
        {
            if (Inspector == null)
            {
                return;
            }

            int Y = Inspector.GetSelfContainer().GetScreenY();
            int ScrollY = _ScrollView.GetScreenY();
            int Offset = _ScrollPanel.GetY();

            _ScrollView.SetBaseY((ScrollY - Y) + Offset);
            _ScrollView.UpdateScrollPanelPos();
            _ScrollView.UpdateScrollBar();
        }

        public void ExpandAll()
        {
            if (_Inspector != null)
            {
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetCheckExpand(true);
                });
            }
        }

        public void CollapseAll()
        {
            if (_Inspector != null)
            {
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetCheckExpand(false);
                });
            }
        }

        #region Search Event

        private void OnSearchUICancel(SearchUI Sender)
        {
            if (_Inspector != null)
            {
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(true);
                });

                UpdateLayout();
            }
        }

        private void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            if (_Inspector != null)
            {
                List<Inspector> SearchResult = new List<Inspector>();
                _Inspector.ForEach((Inspector) =>
                {
                    if (Inspector == _Inspector)
                    {
                        return;
                    }
                    // Traverse all controls
                    Queue<Control> Controls = new Queue<Control>();
                    Controls.Enqueue(Inspector.GetSelfContainer());
                    while (Controls.Count > 0)
                    {
                        Control Control = Controls.Dequeue();
                        for (int i = 0; i < Control.GetChildCount(); ++i)
                        {
                            Controls.Enqueue(Control.GetChild(i));
                        }
                        // Try to match label text
                        if (Control is Label)
                        {
                            Label Label = Control as Label;
                            if (StringHelper.IgnoreCaseContains(Label.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                        // Try to match edit text
                        else if (Control is Edit)
                        {
                            Edit Edit = Control as Edit;
                            if (StringHelper.IgnoreCaseContains(Edit.GetText(), Pattern))
                            {
                                SearchResult.Add(Inspector);
                                break;
                            }
                        }
                    }
                });
                // Hide all inspectors
                _Inspector.ForEach((Inspector) =>
                {
                    Inspector.SetVisible(false);
                });
                // Show search result
                foreach (Inspector Result in SearchResult)
                {
                    Inspector This = Result;
                    while (This != null)
                    {
                        This.SetVisible(true);
                        This = This.GetParentInspector();
                    }

                    Result.ForEach((Inspector) =>
                    {
                        Inspector.SetVisible(true);
                    });
                }
                if (SearchResult.Count == 0)
                {
                    _Inspector.SetVisible(true);
                }
                UpdateLayout();
            }
        }

        #endregion
    }
}
