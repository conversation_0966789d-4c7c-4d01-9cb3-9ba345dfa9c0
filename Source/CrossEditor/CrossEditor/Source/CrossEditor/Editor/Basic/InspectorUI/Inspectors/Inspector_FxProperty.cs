using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_FxProperty : Inspector_Struct_With_Property
    {
        Fx.Property _Property;
        Button _Button;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Property = (Fx.Property)Object;

            Type Type = Object.GetType();
            ClearChildInspectors();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                    if (AttributeData != null)
                    {
                        if (PropertyInfo.Name == "IsColor")
                        {
                            AttributeData.NamedAttributes["bHide"] = !(_Property.Type == Fx.PropertyType.Float3 || _Property.Type == Fx.PropertyType.Float4);
                        }
                        if (PropertyInfo.Name == "Min" || PropertyInfo.Name == "Max")
                        {
                            AttributeData.NamedAttributes["bHide"] = _Property.Type != Fx.PropertyType.Float;
                        }
                        if (PropertyInfo.Name == "Value")
                        {
                            string PropertyTypeString = _Property.Value.GetType().ToString();
                            if (_Property.IsColor)
                            {
                                if (_Property.Type == Fx.PropertyType.Float3)
                                {
                                    PropertyTypeString = "Vector3fAsColor";
                                }
                                else if (_Property.Type == Fx.PropertyType.Float4)
                                {
                                    PropertyTypeString = "Vector4fAsColor";
                                }
                            }
                            if (_Property.Type == Fx.PropertyType.Texture)
                            {
                                PropertyTypeString = "StringAsResource";
                            }
                            AttributeData.NamedAttributes["PropertyType"] = PropertyTypeString;
                        }
                    }
                }

                AddPropertyInspector(PropertyInfo, Object);
            }

            _Button = new Button();
            _Button.Initialize();
            _Button.SetText("Apply");
            _Button.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _Button.SetFontSize(18);
            _Button.SetTextOffsetY(2);
            _Button.SetToolTips("Apply changes and save to file.");
            _Button.ClickedEvent += OnButtonApplyClicked;
            _Button.SetVisible(true);
            _ChildContainer.AddChild(_Button);
        }

        private void OnButtonApplyClicked(Button Sender)
        {
            FxEditorUI.GetInstance().OnButtonSaveClicked(null);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            if (_Button != null)
            {
                _Button.SetPosition(SPAN_X, Y + SPAN_Y, Width - SPAN_X * 2, DEFAULT_HEIGHT);
                Y += DEFAULT_HEIGHT + 2 * SPAN_Y;
                _ChildContainer.SetHeight(Y);
            }
        }
    }
}