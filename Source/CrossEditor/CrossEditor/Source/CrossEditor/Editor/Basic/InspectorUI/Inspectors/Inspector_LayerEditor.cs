using CEngine;

namespace CrossEditor
{
    class Inspector_LayerEditor : Inspector
    {
        protected LayerSetting _LayerSetting;
        const string PropertyNamePrefix = "Layer ";

        public override void InspectObject(object Object, object Tag = null)
        {
            _LayerSetting = Object as LayerSetting;

            ClearChildInspectors();

            for (uint layerIndex = 0; layerIndex < 32u; layerIndex++)
            {
                ObjectProperty ObjectProperty = new ObjectProperty();
                ObjectProperty.Object = Object;
                ObjectProperty.Type = typeof(string);
                ObjectProperty.Name = PropertyNamePrefix + layerIndex.ToString();
                ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
                ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
                ObjectProperty.ReadOnly = _LayerSetting.GetLayerInfo(layerIndex).IsBuiltInLayer;

                Inspector ChildInspector = InspectorManager.GetInstance().CreatePropertyInspector(typeof(string).ToString(), false);
                AddChildInspector(ChildInspector);
                ChildInspector.InspectProperty(ObjectProperty);
            }
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            uint LayerIndex = uint.Parse(PropertyName.Substring(PropertyNamePrefix.Length, PropertyName.Length - PropertyNamePrefix.Length));
            return _LayerSetting.GetLayerInfo(LayerIndex).LayerName;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            uint LayerIndex = uint.Parse(PropertyName.Substring(PropertyNamePrefix.Length, PropertyName.Length - PropertyNamePrefix.Length));
            _LayerSetting.SetLayer(LayerIndex, (PropertyValue as string).Trim());
            _LayerSetting.SerializeConfigToFile();
        }
    }
}
