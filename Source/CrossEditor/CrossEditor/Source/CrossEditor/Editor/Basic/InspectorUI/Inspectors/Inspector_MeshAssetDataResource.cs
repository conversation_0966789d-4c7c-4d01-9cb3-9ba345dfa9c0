using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_MeshAssetDataResources : Inspector_Struct_With_Property
    {
        protected MeshAssetDataResource _Mesh;
        protected Panel _PanelIcon;
        protected Button _ApplyButton;
        protected Button _ImpostorBakeButton;
        //private int MeshReduceTaskState = 0;
        static private bool _NeedReApply = false;

        public override void InspectObject(object Object, object Tag = null)
        {
            _Mesh = (MeshAssetDataResource)Object;

            if (_Mesh.ShowImpostorSetting)
            {
                base.InspectObject(_Mesh.ImpostorSetting);

                _ImpostorBakeButton = new Button();
                _ImpostorBakeButton.Initialize();
                _ImpostorBakeButton.SetText("Impostor Bake");
                _ImpostorBakeButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ImpostorBakeButton.SetFontSize(18);
                _ImpostorBakeButton.SetTextOffsetY(2);
                _ImpostorBakeButton.SetToolTips("Bake entity for impostor");
                _ImpostorBakeButton.ClickedEvent += OnImpostorBakeButtonClicked;
                _ImpostorBakeButton.SetVisible(true);
                _ImpostorBakeButton.SetSize(100, 13);
                _ChildContainer.AddChild(_ImpostorBakeButton);

            }
            else
            {
                base.InspectObject(_Mesh.MeshDataInfo);

                _PanelIcon = new Panel();
                _PanelIcon.Initialize();
                _PanelIcon.SetEnable(true);
                _PanelIcon.SetTagString1(_Mesh.Path);
                ThumbnailHelper.GetInstance().EnableThumbnail(_PanelIcon);
                _SelfContainer.AddChild(_PanelIcon);

                _ApplyButton = new Button();
                _ApplyButton.Initialize();
                _ApplyButton.SetText("Apply");
                _ApplyButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ApplyButton.SetFontSize(18);
                _ApplyButton.SetTextOffsetY(2);
                _ApplyButton.SetToolTips("Extract poses and features from animations");
                _ApplyButton.ClickedEvent += OnApplyButtonClicked;
                _ApplyButton.SetVisible(true);
                _ChildContainer.AddChild(_ApplyButton);

            }

            InspectorManager.GetInstance().PropertyValueChangedEvent += OnPropertyValueChanged;

            if (_NeedReApply)
            {
                OnApplyButtonClicked(null);
                _NeedReApply = false;
            }
        }

        private void OnPropertyValueChanged(Inspector_Property PropertyInspector, object Object, string Name, object NewValue)
        {
            if (NewValue is MaterialSlot)
            {
                MaterialSlot Slot = NewValue as MaterialSlot;
                string MaterialPath = Slot.MaterialPath;
                Entity Model = MEPreviewScene.GetInstance().GetModelEntity();
                IntPtr NativeWorld = MEPreviewScene.GetInstance().GetWorld().GetNativePointer();
                CrossEngineApi.SetModelLODMaterialPath(NativeWorld, Model.EntityID, 0, (uint)Slot.MeshPartIndex, (uint)Slot.LodIndex, MaterialPath);
            }
        }

        private void RefreshIcon()
        {
            _PanelIcon.SetImage(null);
        }

        private void CheckLodSetting()
        {
            int LodCount = _Mesh.MeshDataInfo.LodGroups.Count;
            float CachedHeight = Math.Max(0.01f, _Mesh.MeshDataInfo.LodGroups[0].ScreenHeight);
            for (int i = 1; i < LodCount; i++)
            {
                float ScreenHeight = _Mesh.MeshDataInfo.LodGroups[i].ScreenHeight;
                if (ScreenHeight < CachedHeight)
                {
                    CachedHeight = ScreenHeight;
                }
                else
                {
                    CachedHeight -= 0.001f;
                    _Mesh.MeshDataInfo.LodGroups[i].ScreenHeight = CachedHeight;
                }
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            if (_PanelIcon != null)
            {
                _PanelIcon.SetPosition((Width - 128) / 2, 11, 128, 128);
                _SelfContainer.SetPosition(0, Y, Width, 150);
                Y += 150;
            }

            base.UpdateLayout(Width, ref Y);

            int Y1 = _ChildContainer.GetHeight();
            if (_ApplyButton != null)
            {
                _ApplyButton.SetPosition(SPAN_X, Y1 + 5, Width - SPAN_X * 2, 20);
                Y1 += 20 + 5;
                _ChildContainer.SetHeight(Y1);
                Y += 20 + 5;
            }

            if (_ImpostorBakeButton != null)
            {
                _ImpostorBakeButton.SetPosition(SPAN_X, Y1 + 5, Width - SPAN_X * 2, 20);
                Y1 += 20 + 5;
                _ChildContainer.SetHeight(Y1);
                Y += 20 + 5;
            }
        }

        public override void ModifyButtonClickedEvent(Button Sender)
        {
            //if (MeshReduceTaskState != 0)
            //{
            //    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Mesh Smplifier", "Seems a reduce task already in progress, \nwait it finish before start a new task.");
            //    return;
            //}

            //StringBuilder TipString = new StringBuilder();
            //for (int i = 0; i < _Mesh.MeshDataInfo.Simplification.SimplifyLods.Count; i++)
            //{
            //    if (_Mesh.MeshDataInfo.Simplification.SimplifyLods[i].TriangleRate <= 0 ||
            //                _Mesh.MeshDataInfo.Simplification.SimplifyLods[i].TriangleRate > 100)
            //    {
            //        TipString.Append("The model smplifing rate must be between 0 and 100.");
            //        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Smplify Fail", TipString.ToString());
            //        return;
            //    }
            //}
            //string path = EditorUtilities.StandardFilenameToEditorFilename(_Mesh.Path);
            //if (path.Length == 0)
            //    return;

            //string ProjectDirectory;

            //if (path.IndexOf("Contents") != -1)
            //{
            //    ProjectDirectory = PathHelper.ToStandardForm(MainUI.GetInstance().GetProjectDirectory());
            //}
            //else
            //{
            //    ProjectDirectory = PathHelper.ToStandardForm(EditorUtilities.GetResourceDirectory());
            //}

            //var thread = new System.Threading.Thread(() =>
            //{
            //    path = PathHelper.ToStandardForm(path).Remove(0, ProjectDirectory.Length + 1);

            //    MeshSimplifier.SetReduceEngine(_Mesh.MeshDataInfo.Simplification.ReduceEngine);
            //    MeshSimplifier.SetMergeVertexThresholds(_Mesh.MeshDataInfo.Simplification.MergeVertex);

            //    int result = 0;
            //    if (_Mesh.MeshDataInfo.Simplification.OptimizeOnly || _Mesh.MeshDataInfo.Simplification.SmoothNormal)
            //    {
            //        MeshOptimizeInfo meshOptimizeInfo = new MeshOptimizeInfo();
            //        meshOptimizeInfo.mStaging = ProjectDirectory;
            //        meshOptimizeInfo.mMesh = path;
            //        meshOptimizeInfo.mSmoothNormal = _Mesh.MeshDataInfo.Simplification.SmoothNormal;

            //        result = MeshSimplifier.MeshOptimize(meshOptimizeInfo);

            //        if (meshOptimizeInfo.mSmoothNormal && _Mesh.MeshDataInfo.EnableVirtualGeometry)
            //        {
            //            _NeedReApply = true;
            //        }
            //    }
            //    else
            //    {

            //        MeshReduceInfo meshReduceInfo = new MeshReduceInfo();
            //        meshReduceInfo.mStaging = ProjectDirectory;
            //        meshReduceInfo.mMesh = path;
            //        for (int i = 0; i < _Mesh.MeshDataInfo.Simplification.SimplifyLods.Count; i++)
            //        {
            //            var LODInfo = new MeshLODInfo();
            //            LODInfo.mPercentTriangles = _Mesh.MeshDataInfo.Simplification.SimplifyLods[i].TriangleRate;
            //            LODInfo.mPercentVertices = _Mesh.MeshDataInfo.Simplification.SimplifyLods[i].TriangleRate;
            //            // LODInfo.SubMeshIndices
            //            meshReduceInfo.mLODs.Add(LODInfo);
            //        }
            //        result = MeshSimplifier.MeshReduce(meshReduceInfo);
            //    }

            //    string resultLog = MeshSimplifier.GetReduceResultLog();
            //    MeshReduceTaskState = 0;

            //    OperationQueue.GetInstance().AddOperation(() =>
            //    {
            //        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Mesh Smplifier", (result == 0 ? "Task complete success." : resultLog));
            //        if (result == 0)
            //        {
            //            Runtime.CrossEngine_ReloadResource(path);
            //            MEPreviewUI MEPreviewUI = MEPreviewUI.GetInstance();
            //            MEPreviewUI.SetMeshPath(path);
            //            ProjectUI.GetInstance().RefreshListView(false);
            //        }
            //    });
            //});

            //MeshReduceTaskState = 1;
            //CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Mesh Smplifier", "Task started in the background.");
            //thread.Start();
            //return;
        }

        void OnApplyButtonClicked(Button Sender)
        {
            CheckLodSetting();
            CEResource.UpdateMeshLodSetting(_Mesh.ResourcePtr.GetPointer(), _Mesh.MeshDataInfo);
            if (_Mesh.MeshDataInfo.WGS84CurvationCorrection.MeshCurvation || _Mesh.MeshDataInfo.WGS84CurvationCorrection.PhysicsCurvation)
            {
                switch (_Mesh.MeshDataInfo.WGS84CurvationCorrection.LengthUnit)
                {
                    case kSystemUnit.UNIT_INCH:
                    case kSystemUnit.UNIT_FOOT:
                    case kSystemUnit.UNIT_MILE:
                    case kSystemUnit.UNIT_YARD:
                        CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "WGS84 Curvature Correction", "The selected length unit hasn't been supported yet.");
                        return;
                }
                WGS84SystemG.Mesh_WGS84CurvationCorrection_v2(_Mesh.ResourcePtr.GetPointer(), _Mesh.MeshDataInfo.WGS84CurvationCorrection.RefLongitude,
                    _Mesh.MeshDataInfo.WGS84CurvationCorrection.RefLatitude, _Mesh.MeshDataInfo.WGS84CurvationCorrection.RefAltitude,
                    new Double4(_Mesh.MeshDataInfo.WGS84CurvationCorrection.RangeMinLatitude, _Mesh.MeshDataInfo.WGS84CurvationCorrection.RangeMinLongitude,
                         _Mesh.MeshDataInfo.WGS84CurvationCorrection.RangeMaxLatitude, _Mesh.MeshDataInfo.WGS84CurvationCorrection.RangeMaxLongitude),
                    _Mesh.MeshDataInfo.WGS84CurvationCorrection.LengthUnit);
            }
            _Mesh.Save();
            _Mesh.Reload();
            if (_Mesh.MeshDataInfo.WGS84CurvationCorrection.MeshCurvation)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "WGS84 Curvature Correction", "Correction Succeeded");
            }
        }

        void OnImpostorBakeButtonClicked(Button Sender)
        {
            World PreviewWorld = MEPreviewScene.GetInstance().GetWorld();
            Entity ModelEntity = MEPreviewScene.GetInstance().GetModelEntity();
            ImpostorBakeClient.GetInstance().Bake(PreviewWorld, ModelEntity, _Mesh.ImpostorSetting);
        }
    }
}
