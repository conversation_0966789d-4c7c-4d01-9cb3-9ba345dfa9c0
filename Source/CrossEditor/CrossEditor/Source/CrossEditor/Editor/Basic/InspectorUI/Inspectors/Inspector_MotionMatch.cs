using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_MotionMatch : Inspector
    {
        protected MotionMatchAsset _MotionMatchAsset;
        protected Edit _EditName;
        protected Button _CookButton;
        protected Label _StatisticsLabel;

        public override void InspectObject(object Object, object Tag = null)
        {
            _MotionMatchAsset = (MotionMatchAsset)Object;

            _EditName = new Edit();
            _EditName.SetFontSize(16);
            _EditName.Initialize(EditMode.Simple_SingleLine);
            _EditName.SetReadOnly(true);
            EditContextUI.GetInstance().RegisterEdit(_EditName);
            _EditName.SetSize(100, 13);
            string resource_path = PathHelper.GetNameOfPath(_MotionMatchAsset.Path);
            _EditName.SetText(resource_path + "(MotionMatch)");
            _SelfContainer.AddChild(_EditName);

            Type type = _MotionMatchAsset.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(type);

            foreach (PropertyInfo info in Properties)
            {
                bool add = true;
                if (_MotionMatchAsset.PoseMatching)
                {
                    if (MotionMatchAsset.MotionMatchingProperties.Contains(info.Name))
                    {
                        add = false;
                    }
                }
                else
                {
                    if (MotionMatchAsset.PoseMatchingProperties.Contains(info.Name))
                    {
                        add = false;
                    }

                }

                if (add)
                {
                    AddPropertyInspector(info, _MotionMatchAsset);
                }
            }

            _CookButton = new Button();
            _CookButton.Initialize();
            _CookButton.SetText("ProcessAnimations");
            _CookButton.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _CookButton.SetFontSize(18);
            _CookButton.SetTextOffsetY(2);
            _CookButton.SetToolTips("Extract poses and features from animations");
            _CookButton.ClickedEvent += OnProcessButtonClicked;
            _CookButton.SetVisible(true);
            _CookButton.SetSize(100, 13);
            _SelfContainer.AddChild(_CookButton);

            _StatisticsLabel = new Label();
            _StatisticsLabel.Initialize();
            _StatisticsLabel.SetMultiLine(true);
            _StatisticsLabel.SetFontSize(15);
            _StatisticsLabel.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _StatisticsLabel.SetText(_MotionMatchAsset.GetMotionMatchStatistics().ToString());
            _SelfContainer.AddChild(_StatisticsLabel);
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }


            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            bool Changed = true;
            if (PropertyInfo != null)
            {
                if (PropertyInfo.GetValue(Object).Equals(PropertyValue))
                {
                    Changed = false;
                }

                PropertyInfo.SetValue(Object, PropertyValue);
            }


            if (Changed)
            {
                _MotionMatchAsset.Save();
                _MotionMatchAsset.Reload();
            }


            if (PropertyName == "PoseMatching")
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    ReadValue();
                    GetInspectorHandler().UpdateLayout();
                });
            }
        }


        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            int EditNameWidth = Width - SPAN_X * 2;
            _EditName.SetPosition(SPAN_X, Height + SPAN_Y, EditNameWidth, 14);
            Height += 20;
            _CookButton.SetPosition(SPAN_X, Height + 2, EditNameWidth, 18);
            Height += 20;
            _StatisticsLabel.SetPosition(SPAN_X, Height + 5, EditNameWidth, 40);
            Height += 50;
            _SelfContainer.SetPosition(0, Y, Width, Height);

            base.UpdateLayout(Width, ref Y);
        }


        void OnProcessButtonClicked(Button sender)
        {
            bool asset_being_played = _MotionMatchAsset.IsPlayingInPreviewScene();

            if (asset_being_played)
            {
                // asset being playing, all the sequence is being attached to skeleton,
                // if we cook motion match, all the sequence may change, i.e attached skeleton will be clear.
                // so it's necessary(safer) to stop the playing

                MSAPreviewContext.GetInstance().AttemptRemove(_MotionMatchAsset);
            }

            _MotionMatchAsset.SetMotionMatchSettings();
            Clicross.AnimationEditorUtil.Animation_CookMotionMatchData(_MotionMatchAsset.ResourcePtr as Clicross.anim.MotionDataAsset);
            _MotionMatchAsset.Save();
            _MotionMatchAsset.Reload();

            _StatisticsLabel.SetText(_MotionMatchAsset.GetMotionMatchStatistics().ToString());

            if (asset_being_played)
            {
                // we need re-add this animation
                MSAPreviewContext.GetInstance().AttemptDrop(_MotionMatchAsset.GetRelativePath());
            }
        }



    }
}
