using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_PostProcessVolumeComponent : Inspector_Component
    {
        protected PostProcessVolumeComponent _PostProcessVolumeComponent = null;

        public Inspector_PostProcessVolumeComponent(List<Entity> Entities)
            : base(Entities)
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _PostProcessVolumeComponent = Object as PostProcessVolumeComponent;
            _PostProcessVolumeComponent.SyncDataFromEngine();

            base.InspectObject(Object, Tag);

            var ComponentData = _PostProcessVolumeComponent.ComponentData;
            if (ComponentData == null)
            {
                return;
            }

            // None-Grouped Properties
            Type Type = ComponentData.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            foreach (PropertyInfo PropertyInfo in Properties)
            {
                PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                if (PropertyInfoAttribute.Category == "")
                {
                    AddPropertyInspector(PropertyInfo, ComponentData);
                }
            }

            PropertyModifiedHandler PropertyModifiedFunc = (PropertyOwner, Property) =>
            {
                // add anim from previewer context
                DebugHelper.Assert(PropertyOwner == ComponentData);
                OnPropertyChanged();
            };

            // Grouped Properties
            //Inspector_GroupedProperty Label_Bloom = new Inspector_GroupedProperty(ComponentData, "Bloom", PropertyModifiedFunc);
            //Label_Bloom.InspectObject(ComponentData, null);
            //AddChildInspector(Label_Bloom);

            //Inspector_GroupedProperty Label_WhiteBalance = new Inspector_GroupedProperty(ComponentData, "WhiteBalance", PropertyModifiedFunc);
            //Label_WhiteBalance.InspectObject(ComponentData, null);
            //AddChildInspector(Label_WhiteBalance);

            //Inspector_GroupedProperty Label_ColorGrading = new Inspector_GroupedProperty(ComponentData, "ColorGrading", PropertyModifiedFunc);
            //Label_ColorGrading.InspectObject(ComponentData, null);
            //AddChildInspector(Label_ColorGrading);
        }


        public override object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            var ComponentData = _PostProcessVolumeComponent.ComponentData;
            if (ComponentData == null)
            {
                return null;
            }

            Type Type = ComponentData.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(ComponentData);
            }

            return null;
        }

        public override void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            var ComponentData = _PostProcessVolumeComponent.ComponentData;
            if (ComponentData == null)
            {
                return;
            }

            Type Type = ComponentData.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);

            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(ComponentData, PropertyValue);

                OnPropertyChanged();
            }
        }

        protected void OnPropertyChanged()
        {
            _PostProcessVolumeComponent.OnComponentDataChanged();
            _PostProcessVolumeComponent.SyncDataFromEngine();

            // Need refresh inspectors after setting PropertyInfo
            OperationQueue.GetInstance().AddOperation(() =>
            {
                ReadValue();
                GetInspectorHandler().UpdateLayout();
            });

            EditorScene.GetInstance().SetDirty();
        }
    }
}
