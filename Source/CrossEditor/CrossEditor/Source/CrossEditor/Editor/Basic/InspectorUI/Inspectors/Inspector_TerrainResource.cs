using EditorUI;

namespace CrossEditor
{
    class Inspector_TerrainResource : Inspector_Struct_With_Property
    {
        protected TerrainResource _Terrain;
        protected Panel _PanelIcon;
        public override void InspectObject(object Object, object Tag = null)
        {
            _Terrain = (TerrainResource)Object;

            _PanelIcon = new Panel();
            _PanelIcon.Initialize();
            _PanelIcon.SetEnable(true);
            _PanelIcon.SetTagString1(_Terrain.Path);
            ThumbnailHelper.GetInstance().EnableThumbnail(_PanelIcon);
            _SelfContainer.AddChild(_PanelIcon);

            base.InspectObject(_Terrain.TerrainInfo);
        }

        private void RefreshIcon()
        {
            _PanelIcon.SetImage(null);
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _PanelIcon.SetPosition((Width - 256) / 2, Height + SPAN_Y, 256, 256);
            Height += 256 + 2 * SPAN_Y;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            base.UpdateLayout(Width, ref Y);
        }
    }
}
