using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    class Inspector_Property_Entity : Inspector_Property
    {
        Entity _Entity;

        Edit _EditValue;
        Button _ButtonLocate;
        Button _ButtonOperations;

        public Inspector_Property_Entity()
        {
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.SetReadOnly(true);
            _EditValue.LoadSource("");
            _EditValue.CharInputedEvent += OnEditValueCharInpupted;
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            Container.AddChild(_EditValue);

            _ButtonLocate = new Button();
            _ButtonLocate.Initialize();
            _ButtonLocate.SetFontSize(12);
            _ButtonLocate.SetTextOffsetY(2);
            _ButtonLocate.SetText("->");
            _ButtonLocate.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonLocate.SetToolTips("Locate");
            _ButtonLocate.ClickedEvent += OnButtonLocalClicked;
            Container.AddChild(_ButtonLocate);

            _ButtonOperations = new Button();
            _ButtonOperations.Initialize();
            _ButtonOperations.SetFontSize(12);
            _ButtonOperations.SetText("...");
            _ButtonOperations.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonOperations.SetToolTips("Operations");
            _ButtonOperations.ClickedEvent += OnButtonOperationsClicked;
            Container.AddChild(_ButtonOperations);

            ReadValue();
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = GetValueWidth() - 4 * SPAN_X - 2 * BUTTON_WIDTH;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _EditValue.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
            _ButtonLocate.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonLocate);
            _ButtonOperations.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonOperations);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            if (PropertyValue is Entity)
            {
                _Entity = (Entity)PropertyValue;
            }
            else if (PropertyValue is EntityIDStruct)
            {
                EntityIDStruct EntityId = (EntityIDStruct)PropertyValue;
                object Obj = InspectorUI.GetInstance().GetObjectInspected();
                if (Obj != null && Obj.GetType() == typeof(List<Entity>))
                {
                    var entities = Obj as List<Entity>;
                    _Entity = (entities[0]).World.Root.SearchChildByEntityID(EntityId.GetValue());
                }
            }
            else if (PropertyValue is Clicross.ecs.EntityIDStruct)
            {
                Clicross.ecs.EntityIDStruct EntityId = (Clicross.ecs.EntityIDStruct)PropertyValue;
                object Obj = InspectorUI.GetInstance().GetObjectInspected();
                if (Obj != null && Obj.GetType() == typeof(List<Entity>))
                {
                    var entities = Obj as List<Entity>;
                    _Entity = (entities[0]).World.Root.SearchChildByEntityID(EntityId.GetValue());
                }
            }
            string PropertyValueString = "";
            if (_Entity != null)
            {
                PropertyValueString = _Entity.GetCascadeName();
            }
            else
            {
                PropertyValueString = "<null>";
            }
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }
            _EditValue.SetText(PropertyValueString);
        }

        public void SetEntityPropertyValue()
        {
            object PropertyValue = GetPropertyValue();
            if (PropertyValue is EntityIDStruct)
            {
                ((EntityIDStruct)PropertyValue).SetValue(_Entity != null ? _Entity.EntityID : Entity.InvalidHandle);
                SetPropertyValue(PropertyValue);
            }
            else if (PropertyValue is Clicross.ecs.EntityIDStruct)
            {
                ((Clicross.ecs.EntityIDStruct)PropertyValue).SetValue(_Entity != null ? _Entity.EntityID : Entity.InvalidHandle);
                SetPropertyValue(PropertyValue);
            }
            else
            {
                SetPropertyValue(_Entity);
            }
        }

        public override void WriteValue()
        {
            base.WriteValue();
            SetEntityPropertyValue();
        }

        public override void OnDropEntity(int MouseX, int MouseY, Entity EntityDragged)
        {
            if (_LabelName.IsPointIn(MouseX, MouseY) || _EditValue.IsPointIn(MouseX, MouseY))
            {
                RecordAndWriteValue(() =>
                {
                    _Entity = EntityDragged;
                    SetEntityPropertyValue();
                });
                InspectorUI.GetInstance().InspectObject();
            }
        }

        void OnEditValueCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            RecordAndWriteValue();
        }

        void OnButtonLocalClicked(Button Sender)
        {
            DoLocate();
        }

        public void OnButtonOperationsClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_Reset = new MenuItem();
            MenuItem_Reset.SetText("Reset");
            MenuItem_Reset.ClickedEvent += OnMenuItemResetClicked;

            MenuItem MenuItem_Locate = new MenuItem();
            MenuItem_Locate.SetText("Locate");
            MenuItem_Locate.ClickedEvent += OnMenuItemLocateClicked;

            MenuContextMenu.AddMenuItem(MenuItem_Reset);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_Locate);

            if (_ObjectProperty.ReadOnly)
            {
                MenuItem_Reset.SetEnable(false);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OnMenuItemResetClicked(MenuItem MenuItem)
        {
            DoReset();
        }

        void OnMenuItemLocateClicked(MenuItem MenuItem)
        {
            DoLocate();
        }

        void DoReset()
        {
            Entity SelectedEntity = HierarchyUI.GetInstance().GetSelectedEntity();
            if (SelectedEntity != null)
            {
                RecordAndWriteValue(() =>
                {
                    _Entity = null;
                    SetEntityPropertyValue();
                });
                InspectorUI.GetInstance().InspectObject();
            }
        }

        void DoLocate()
        {
            if (_Entity != null)
            {
                HierarchyUI.GetInstance().SelectEntity(_Entity);
            }
        }
    }
}
