using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class Inspector_Property_File : Inspector_Property
    {
        Edit _EditValue;
        Button _ButtonAssignWithSelectedFile;
        Button _ButtonShowInProjectView;
        Button _ButtonFilter;
        Button _ButtonOperations;

        string _FileTypeDescriptor;
        ClassIDType _ObjectClassID1;
        ClassIDType _ObjectClassID2;
        ClassIDType _ObjectClassID3;

        string _FileType;
        List<string> _FileExtensions;

        public Inspector_Property_File()
        {
            _FileTypeDescriptor = "All Files#*";
            ParseFileTypeDescriptor();
            _ObjectClassID1 = ClassIDType.CLASS_NullType;
            _ObjectClassID2 = ClassIDType.CLASS_NullType;
            _ObjectClassID3 = ClassIDType.CLASS_NullType;
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.SetReadOnly(true);
            _EditValue.LoadSource("");
            _EditValue.CharInputedEvent += OnEditValueCharInpupted;
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            Container.AddChild(_EditValue);

            _ButtonAssignWithSelectedFile = new Button();
            _ButtonAssignWithSelectedFile.Initialize();
            _ButtonAssignWithSelectedFile.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/UseContent.png"));
            _ButtonAssignWithSelectedFile.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonAssignWithSelectedFile.SetToolTips("Assign With Selected File");
            _ButtonAssignWithSelectedFile.ClickedEvent += OnButtonAssignWithSelectedFileClicked;
            Container.AddChild(_ButtonAssignWithSelectedFile);

            _ButtonShowInProjectView = new Button();
            _ButtonShowInProjectView.Initialize();
            _ButtonShowInProjectView.SetImage(UIManager.LoadUIImage("Editor/Icons/Common/BrowseInContent.png"));
            _ButtonShowInProjectView.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonShowInProjectView.SetToolTips("Show In Project View");
            _ButtonShowInProjectView.ClickedEvent += OnButtonShowInProjectViewClicked;
            Container.AddChild(_ButtonShowInProjectView);

            _ButtonFilter = new Button();
            _ButtonFilter.Initialize();
            _ButtonFilter.SetFontSize(12);
            _ButtonFilter.SetTextOffsetY(1);
            _ButtonFilter.SetText("v");
            _ButtonFilter.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonFilter.SetToolTips("Filter");
            _ButtonFilter.ClickedEvent += OnButtonFilterClicked;
            Container.AddChild(_ButtonFilter);

            _ButtonOperations = new Button();
            _ButtonOperations.Initialize();
            _ButtonOperations.SetFontSize(12);
            _ButtonOperations.SetText("...");
            _ButtonOperations.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonOperations.SetToolTips("Operations");
            _ButtonOperations.ClickedEvent += OnButtonOperationsClicked;
            Container.AddChild(_ButtonOperations);

            SetFilePropertyInfo(_PropertyInfoAttribute.FileTypeDescriptor, _PropertyInfoAttribute.ObjectClassID1, _PropertyInfoAttribute.ObjectClassID2, _PropertyInfoAttribute.ObjectClassID3);
            SetReadOnly(_ObjectProperty.ReadOnly);

            ReadValue();
        }

        public int GetEditValueWidth()
        {
            int EditWidth;
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(MULTIPLE_VALUES_STRING);
            }
            else
            {
                string PropertyValue = GetPropertyValue().ToString();
                EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(PropertyValue);
            }
            return EditWidth + 10;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditValueWidth = Math.Max(GetEditValueWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 6 * SPAN_X - 4 * BUTTON_WIDTH);
            _EditValue.SetPosition(0, SPAN_Y, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
            _ButtonAssignWithSelectedFile.SetPosition(0, 2, 16, 16);
            GetValueContainer().FloatToLeft(_ButtonAssignWithSelectedFile);
            _ButtonShowInProjectView.SetPosition(0, 2, 16, 16);
            GetValueContainer().FloatToLeft(_ButtonShowInProjectView);
            _ButtonFilter.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonFilter);
            _ButtonOperations.SetPosition(0, 2, BUTTON_WIDTH, 16);
            GetValueContainer().FloatToLeft(_ButtonOperations);

            if (_ObjectProperty.ReadOnly)
            {
                _ButtonAssignWithSelectedFile.SetEnable(false);
            }
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = PropertyValue.ToString();
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }
            else
            {
                PropertyValueString = ResourceManager.Instance().ConvertGuidToPath(PropertyValueString);
            }
            _EditValue.SetText(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            ValueString = ResourceManager.Instance().ConvertPathToGuid(ValueString);
            SetPropertyValue(ValueString);
            GetInspectorHandler().UpdateLayout();
        }

        public bool IsLegalExtension(string Path)
        {
            string Extension = PathHelper.GetExtensionOfPath(Path);
            foreach (string Extension1 in _FileExtensions)
            {
                if (Extension1 == "*")
                {
                    return true;
                }
                if (StringHelper.IgnoreCaseEqual(Extension, Extension1))
                {
                    return true;
                }
            }
            return false;
        }

        public bool ExtensionIsAsset()
        {
            if (_FileExtensions.Count == 1)
            {
                string Extension = _FileExtensions[0];
                if (StringHelper.IgnoreCaseEqual(Extension, "nda"))
                {
                    return true;
                }
            }
            return false;
        }

        public bool CheckPath(string Path)
        {
            if (IsLegalExtension(Path) == false)
            {
                string Message = string.Format("File extension mismatch: {0}.", Path);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Message);
                return false;
            }
            if (ExtensionIsAsset())
            {
                ClassIDType ObjectClassID2 = Resource.GetResourceTypeStatic(Path);
                if (EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID1) == false &&
                    EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID2) == false &&
                    EditorUtilities.IsSpecificAsset(ObjectClassID2, _ObjectClassID3) == false)
                {
                    string Message = string.Format("Resource type mismatch: {0}.", Path);
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Message);
                    return false;
                }
            }
            return true;
        }

        public override bool OnDropPathes(int MouseX, int MouseY, List<string> PathesDragged)
        {
            if (_LabelName.IsPointIn(MouseX, MouseY) || _EditValue.IsPointIn(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (PathesDragged.Count > 0)
                {
                    string PathDragged = PathesDragged[0];
                    string PathDragged1 = EditorUtilities.EditorFilenameToStandardFilename(PathDragged);
                    if (CheckPath(PathDragged1))
                    {
                        _EditValue.SetText_End(PathDragged1);
                        RecordAndWriteValue();
                    }
                }
                return true;
            }
            return false;
        }

        void SetFilePropertyInfo(string FileTypeDescriptor, ClassIDType ObjectClassID1, ClassIDType ObjectClassID2, ClassIDType ObjectClassID3)
        {
            _FileTypeDescriptor = FileTypeDescriptor;
            ParseFileTypeDescriptor();
            _ObjectClassID1 = ObjectClassID1;
            _ObjectClassID2 = ObjectClassID2;
            _ObjectClassID3 = ObjectClassID3;
        }

        void ParseFileTypeDescriptor()
        {
            string[] Strings1 = _FileTypeDescriptor.Split("#");
            DebugHelper.Assert(Strings1.Length == 2);
            _FileType = Strings1[0];
            string[] Strings2 = Strings1[1].Split("|");
            DebugHelper.Assert(Strings2.Length >= 1);
            _FileExtensions = new List<string>();
            _FileExtensions.AddRange(Strings2);
        }

        void OnEditValueCharInpupted(Control Sender, char Char, ref bool bContinue)
        {
            RecordAndWriteValue();
        }

        void OnButtonAssignWithSelectedFileClicked(Button Sender)
        {
            DoAssignWithSelectedFile();
        }

        void OnButtonShowInProjectViewClicked(Button Sender)
        {
            DoShowInProjectView();
        }

        void SetReadOnly(bool bReadOnly)
        {
            _ButtonAssignWithSelectedFile.SetEnable(!bReadOnly);
        }

        public void OnButtonOperationsClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_AssignWithSelectedFile = new MenuItem();
            MenuItem_AssignWithSelectedFile.SetText("Assign With Selected File");
            MenuItem_AssignWithSelectedFile.ClickedEvent += OnMenuItemAssignWithSelectedFileClicked;

            MenuItem MenuItem_ExploreFile = new MenuItem();
            MenuItem_ExploreFile.SetText("Explore File");
            MenuItem_ExploreFile.ClickedEvent += OnMenuItemExploreFileClicked;

            MenuItem MenuItem_ResetToDefault = new MenuItem();
            MenuItem_ResetToDefault.SetText("Reset To Default");
            MenuItem_ResetToDefault.ClickedEvent += OnMenuItemResetToDefaultClicked;

            MenuItem MenuItem_ShowInProjectView = new MenuItem();
            MenuItem_ShowInProjectView.SetText("Show In Resource View");
            MenuItem_ShowInProjectView.ClickedEvent += OnMenuItemShowInProjectViewClicked;

            MenuItem MenuItem_ShowInExplorer = new MenuItem();
            MenuItem_ShowInExplorer.SetText("Show In Explorer");
            MenuItem_ShowInExplorer.ClickedEvent += OnMenuItemShowInExplorerClicked;

            MenuItem MenuItem_MakeRelativeToMaterial = new MenuItem();
            MenuItem_MakeRelativeToMaterial.SetText("Make Relative To Material");
            MenuItem_MakeRelativeToMaterial.ClickedEvent += OnMenuItemMakeRelativeToMaterialClicked;

            MenuContextMenu.AddMenuItem(MenuItem_AssignWithSelectedFile);
            MenuContextMenu.AddMenuItem(MenuItem_ExploreFile);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ResetToDefault);
            MenuContextMenu.AddSeperator();
            MenuContextMenu.AddMenuItem(MenuItem_ShowInProjectView);
            MenuContextMenu.AddMenuItem(MenuItem_ShowInExplorer);

            Material material = _Object as Material;
            if (material != null)
            {
                MenuContextMenu.AddSeperator();
                MenuContextMenu.AddMenuItem(MenuItem_MakeRelativeToMaterial);
            }

            if (_PropertyInfoAttribute.DefaultValue == null)
            {
                MenuItem_ResetToDefault.SetEnable(false);
            }

            if (_ObjectProperty.ReadOnly)
            {
                MenuItem_AssignWithSelectedFile.SetEnable(false);
                MenuItem_ResetToDefault.SetEnable(false);
                MenuItem_ExploreFile.SetEnable(false);
            }

            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, Sender);
        }

        void OnMenuItemAssignWithSelectedFileClicked(MenuItem MenuItem)
        {
            DoAssignWithSelectedFile();
        }

        void OnMenuItemExploreFileClicked(MenuItem MenuItem)
        {
            DoExploreFile();
        }

        void OnMenuItemShowInProjectViewClicked(MenuItem MenuItem)
        {
            DoShowInProjectView();
        }

        void OnMenuItemShowInExplorerClicked(MenuItem MenuItem)
        {
            DoShowInExplorer();
        }

        void OnMenuItemResetToDefaultClicked(MenuItem MenuItem)
        {
            DoResetToDefault();
        }

        void OnMenuItemMakeRelativeToMaterialClicked(MenuItem MenuItem)
        {
            DoMakeRelativeToMaterial();
        }

        void DoAssignWithSelectedFile()
        {
            string Path = ProjectUI.GetInstance().GetSelectedListViewPath();
            if (Path != "" && FileHelper.IsFileExists(Path))
            {
                string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                if (CheckPath(Path1))
                {
                    _EditValue.SetText_End(Path1);
                    RecordAndWriteValue();
                }
            }
        }

        void DoExploreFile()
        {
            string FilePath = (string)GetPropertyValue();

            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = _FileType;
            PathInputUIFilterItem.Extensions = _FileExtensions;
            string s = PathInputUIFilterItem.ToString();

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                if (CheckPath(StandardFilename))
                {
                    _EditValue.SetText(StandardFilename);
                    RecordAndWriteValue();
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        string ConvertFilePath(string FilePath)
        {
            if (StringHelper.GetChar(FilePath, 0) == '.')
            {
                Material material = (Material)_Object;
                if (material != null)
                {
                    string MaterialPath = material.Path;
                    string MaterialPath1 = EditorUtilities.StandardFilenameToEditorFilename(MaterialPath);
                    string MaterialDirectory = PathHelper.GetDirectoryName(MaterialPath1);
                    FilePath = Path.Combine(MaterialDirectory, FilePath);
                    FilePath = Path.GetFullPath(FilePath);
                    FilePath = PathHelper.ToStandardForm(FilePath);
                    FilePath = EditorUtilities.EditorFilenameToStandardFilename(FilePath);
                }
            }
            return FilePath;
        }

        void DoShowInProjectView()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                FilePath = ConvertFilePath(FilePath);
                string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);
                ProjectUI.GetInstance().JumpToPath(FilePath1);
            }
        }

        void DoShowInExplorer()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                FilePath = ConvertFilePath(FilePath);
                string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);
                ProcessHelper.OpenContainingFolder(FilePath1);
            }
        }

        void DoResetToDefault()
        {
            string DefaultValue = (string)_PropertyInfoAttribute.DefaultValue;
            _EditValue.SetText_End(DefaultValue);
            RecordAndWriteValue();
        }

        void DoMakeRelativeToMaterial()
        {
            string FilePath = (string)GetPropertyValue();
            if (FilePath != null)
            {
                if (StringHelper.GetChar(FilePath, 0) == '.')
                {
                    return;
                }

                Material material = (Material)_Object;
                if (material != null)
                {
                    string MaterialPath = material.Path;
                    string MaterialPath1 = EditorUtilities.StandardFilenameToEditorFilename(MaterialPath);
                    string MaterialDirectory = PathHelper.GetDirectoryName(MaterialPath1);

                    string FilePath1 = EditorUtilities.StandardFilenameToEditorFilename(FilePath);

                    string RelativePath = Path.GetRelativePath(MaterialDirectory, FilePath1);
                    string RelativePath1 = PathHelper.ToStandardForm(RelativePath);

                    if (PathHelper.IsAbsolutePath(RelativePath1) == false &&
                        StringHelper.GetChar(RelativePath1, 0) != '.')
                    {
                        RelativePath1 = "./" + RelativePath1;
                    }

                    _EditValue.SetText_End(RelativePath1);
                    RecordAndWriteValue();
                }
            }
        }

        void OnButtonFilterClicked(Button Sender)
        {
            FileFilterUI.GetInstance().ShowUI(_FileExtensions, _ButtonFilter, (string Resource) =>
            {
                _EditValue.SetText(Resource);
                RecordAndWriteValue();
            });
        }
    }
}
