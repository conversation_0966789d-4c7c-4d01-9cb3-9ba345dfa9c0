using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Float : Inspector_Property
    {
        EditWithProgress _Edit;
        Edit _EditValue;

        public Inspector_Property_Float()
        {
        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _Edit = new EditWithProgress(Container);
            _Edit.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _Edit.TextChangedEvent += OnEditTextChanged;
            _EditValue = _Edit.GetEditValue();

            SetReadOnly(_ObjectProperty.ReadOnly);

            if (_ObjectProperty.DefaultValue == null && !_ValueExtraProperty._bHasMultipleValues)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public void SetReadOnly(bool Value)
        {
            if (Value)
            {
                _Edit.SetReadOnly(true);
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditValueWidth = GetValueWidth() - 2 * SPAN_X;
            EditValueWidth = Math.Min(EditValueWidth, DEFAULT_WIDTH);
            _Edit.SetPosition(0, SPAN_Y, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_Edit);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                PropertyValueString = MathHelper.NumberToString(PropertyValue);
            }
            else
            {
                PropertyValueString = "<null>";
            }
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }

            _Edit.SetText(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            object NewValue = null;
            Type Type = _ObjectProperty.Type;
            if (Type == typeof(string))
            {
                NewValue = ValueString;
            }
            else
            {
                if (Type == typeof(object))
                {
                    object PropertyValue = GetPropertyValue();
                    Type = PropertyValue.GetType();
                }
                NewValue = MathHelper.ParseNumber(ValueString, Type);
                if (NewValue == null)
                {
                    return;
                }
            }
            SetPropertyValue(NewValue);
        }

        void OnEditTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            RecordAndWriteValue();
        }

        public override object GetPropertyEditValue()
        {
            return float.Parse(_EditValue.GetText());
        }
    }
}
