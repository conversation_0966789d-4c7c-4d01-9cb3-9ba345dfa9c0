using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Int4 : Inspector_Property
    {
        EditWithProgress _EditX;
        EditWithProgress _EditY;
        EditWithProgress _EditZ;
        EditWithProgress _EditW;
        Edit _EditValueX;
        Edit _EditValueY;
        Edit _EditValueZ;
        Edit _EditValueW;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditX = new EditWithProgress(Container);
            _EditX.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditX.TextChangedEvent += OnEditValueTextChanged;
            _EditValueX = _EditX.GetEditValue();

            _EditY = new EditWithProgress(Container);
            _EditY.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditY.TextChangedEvent += OnEditValueTextChanged;
            _EditValueY = _EditY.GetEditValue();

            _EditZ = new EditWithProgress(Container);
            _EditZ.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditZ.TextChangedEvent += OnEditValueTextChanged;
            _EditValueZ = _EditZ.GetEditValue();

            _EditW = new EditWithProgress(Container);
            _EditW.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditW.TextChangedEvent += OnEditValueTextChanged;
            _EditValueW = _EditW.GetEditValue();

            if (_ObjectProperty.ReadOnly)
            {
                _EditX.SetReadOnly(true);
                _EditY.SetReadOnly(true);
                _EditZ.SetReadOnly(true);
                _EditW.SetReadOnly(true);
            }

            ReadValue();
        }

        public Panel CreateColorPanel(Control Container, Color Color)
        {
            Panel ColorPanel = new Panel();
            ColorPanel.Initialize();
            ColorPanel.SetBackgroundColor(Color);
            Container.AddChild(ColorPanel);
            return ColorPanel;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 4) / 4;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _EditX.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditX);
            _EditY.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditY);
            _EditZ.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditZ);
            _EditW.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditW);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            Vector4i Value = (Vector4i)PropertyValue;
            string XString = MathHelper.NumberToString(Value.X);
            string YString = MathHelper.NumberToString(Value.Y);
            string ZString = MathHelper.NumberToString(Value.Z);
            string WString = MathHelper.NumberToString(Value.W);
            _EditX.SetText(XString);
            _EditY.SetText(YString);
            _EditZ.SetText(ZString);
            _EditW.SetText(WString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueXString = _EditValueX.GetText();
            string ValueYString = _EditValueY.GetText();
            string ValueZString = _EditValueZ.GetText();
            string ValueWString = _EditValueW.GetText();
            Vector4i NewValue = new Vector4i();
            NewValue.X = MathHelper.ParseInt(ValueXString);
            NewValue.Y = MathHelper.ParseInt(ValueYString);
            NewValue.Z = MathHelper.ParseInt(ValueZString);
            NewValue.W = MathHelper.ParseInt(ValueWString);
            SetPropertyValue(NewValue);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueX)
            {
                SetSubProperty("X");
            }
            else if (Sender == _EditValueY)
            {
                SetSubProperty("Y");
            }
            else if (Sender == _EditValueZ)
            {
                SetSubProperty("Z");
            }
            else if (Sender == _EditValueW)
            {
                SetSubProperty("W");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
