using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_Property_MinMaxGradient : Inspector_Property
    {
        private MinMaxGradient _Value;
        private MinMaxGradientMode _RecordMode;

        public Inspector_Property_MinMaxGradient()
        {

        }

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _Value = ObjectProperty.GetPropertyValueFunction(ObjectProperty.Object, ObjectProperty.Name, null) as MinMaxGradient;

            InitializeCheckExpand();
            GetNameContainer().AddChild(_CheckExpand);

            AddChildInspectors();
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }
            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            Type Type = Object.GetType();
            PropertyInfo PropertyInfo = Type.GetProperty(PropertyName);
            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(Object, PropertyValue);
            }
            WriteValue();
            if (_RecordMode != _Value.Mode)
            {
                AddChildInspectors();
                GetInspectorHandler().UpdateLayout();
            }
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void AddChildInspectors()
        {
            ClearChildInspectors();
            Type Type = _Value.GetType();
            List<PropertyInfo> Properties = PropertyCollector.CollectPropertiesOfType(Type);
            MinMaxGradientMode Mode = _Value.Mode;
            _RecordMode = Mode;

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AttributeList AttributeList = AttributeManager.GetInstance().GetAttributeList(PropertyInfo);
                if (AttributeList != null)
                {
                    AttributeData AttributeData = AttributeList.GetPropertyInfoAttr();
                    object Value = PropertyInfo.GetValue(_Value);
                    string PorpertyTypeName = Value != null ? Value.GetType().ToString() : PropertyInfo.PropertyType.ToString();
                    bool bContainsProperty = InspectorManager.GetInstance().ContainsProperty(PorpertyTypeName);

                    if (PropertyInfo.PropertyType.IsEnum)
                    {
                        AddPropertyInspector(PropertyInfo, _Value, PorpertyTypeName);
                    }
                    else if (AttributeData != null || bContainsProperty)
                    {
                        PropertyInfoAttribute PropertyInfoAttribute = PropertyInfoAttribute.GetPropertyInfoAttribute(PropertyInfo);
                        switch (Mode)
                        {
                            case MinMaxGradientMode.COLOR:
                                {
                                    if (PropertyInfo.Name == "ColorMax")
                                    {
                                        AddPropertyInspector(PropertyInfo, _Value);
                                    }
                                    break;
                                }
                            case MinMaxGradientMode.TWOCOLORS:
                                {
                                    if (PropertyInfo.Name == "ColorMin" || PropertyInfo.Name == "ColorMax")
                                    {
                                        AddPropertyInspector(PropertyInfo, _Value);
                                    }
                                    break;
                                }
                            case MinMaxGradientMode.GRADIENT:
                                {
                                    if (PropertyInfo.Name == "GradientMax")
                                    {
                                        AddPropertyInspector(PropertyInfo, _Value);
                                    }
                                    break;
                                }
                            case MinMaxGradientMode.TWOGRADIENTS:
                                {
                                    if (PropertyInfo.Name == "GradientMin" || PropertyInfo.Name == "GradientMax")
                                    {
                                        AddPropertyInspector(PropertyInfo, _Value);
                                    }
                                    break;
                                }
                            case MinMaxGradientMode.RANDOMCOLOR:
                                {
                                    if (PropertyInfo.Name == "GradientMax")
                                    {
                                        AddPropertyInspector(PropertyInfo, _Value);
                                    }
                                    break;
                                }
                        }
                    }
                }
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);
            if (_Value.Mode == MinMaxGradientMode.GRADIENT || _Value.Mode == MinMaxGradientMode.TWOGRADIENTS)
            {
                int CurveWidth = GetValueWidth() - 3 * SPAN_X - BUTTON_WIDTH;
            }

            _CheckExpand.SetPosition(SPAN_X + GetIndent() - 20, 0, 20, 20);
            _LabelName.SetX(_CheckExpand.GetEndX());
            if (_ButtonToolTips != null)
                _ButtonToolTips.SetX(_LabelName.GetX());
        }

        public override void ReadValue()
        {
            base.ReadValue();
        }

        public override void WriteValue()
        {
            SetPropertyValue(_Value);
        }

    }
}