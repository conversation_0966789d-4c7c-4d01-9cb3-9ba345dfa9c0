using CEngine;
using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_String : Inspector_Property
    {
        protected Edit _EditValue;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditValue = new Edit();
            _EditValue.SetFontSize(PROPERTY_FONT_SIZE);
            _EditValue.Initialize(EditMode.Simple_SingleLine);
            _EditValue.LoadSource("");
            EditContextUI.GetInstance().RegisterEdit(_EditValue);
            _EditValue.CharInputEvent += OnEditCharInput;
            _EditValue.SelfFocusChangedEvent += OnEditFocusChanged;
            Container.AddChild(_EditValue);

            if (_ObjectProperty.ReadOnly)
            {
                _EditValue.SetReadOnly(true);
                _EditValue.SetBackgroundColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
            }
            ReadValue();
        }

        public int GetEditValueWidth()
        {
            int EditWidth = GetUIManager().GetDefaultFont(PROPERTY_FONT_SIZE).MeasureString_Fast(_EditValue.GetTagString1());
            return EditWidth + 10;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditValueWidth = Math.Max(GetEditValueWidth(), DEFAULT_WIDTH);
            EditValueWidth = Math.Min(EditValueWidth, GetValueWidth() - 2 * SPAN_X);
            _EditValue.SetPosition(0, SPAN_Y, EditValueWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditValue);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string PropertyValueString = "";
            if (PropertyValue != null)
            {
                if (_ObjectProperty.Type == typeof(UniqueString))
                {
                    UniqueString UniStr = (UniqueString)PropertyValue;
                    PropertyValueString = UniStr.GetCString();
                }
                else
                {
                    PropertyValueString = PropertyValue.ToString();
                }
            }
            else
            {
                PropertyValueString = "<null>";
            }
            if (_ValueExtraProperty._bHasMultipleValues)
            {
                PropertyValueString = MULTIPLE_VALUES_STRING;
            }
            _EditValue.SetText(PropertyValueString);
            _EditValue.SetTagString1(PropertyValueString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueString = _EditValue.GetText();
            object NewValue = null;
            Type Type = _ObjectProperty.Type;
            if (Type == typeof(string))
            {
                NewValue = ValueString;
            }
            else if (Type == typeof(UniqueString))
            {
                NewValue = new UniqueString(ValueString);
            }
            else
            {
                NewValue = MathHelper.ParseNumber(ValueString, Type);
                if (NewValue == null)
                {
                    return;
                }
            }
            SetPropertyValue(NewValue);
            GetInspectorHandler().UpdateLayout();
        }

        void OnEditCharInput(Control Sender, char Char, ref bool bContinue)
        {
            if (_EditValue.GetReadOnly())
                return;

            _EditValue.SetTagString1(_EditValue.GetText());
            GetInspectorHandler().UpdateLayout();

            if (Char == '\r' || Char == '\n')
            {
                GetUIManager().SetFocusControl(null);
            }
        }

        void OnEditFocusChanged(Control Sender)
        {
            if (_EditValue.GetReadOnly())
                return;

            if (Sender.IsFocused() == false)
            {
                RecordAndWriteValue();
            }
        }
    }
}
