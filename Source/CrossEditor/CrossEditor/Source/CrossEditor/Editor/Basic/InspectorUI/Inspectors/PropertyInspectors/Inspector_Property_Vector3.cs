using EditorUI;
using System;

namespace CrossEditor
{
    class Inspector_Property_Vector3 : Inspector_Property
    {
        Panel _ColorX;
        Panel _ColorY;
        Panel _ColorZ;

        EditWithProgress _EditX;
        EditWithProgress _EditY;
        EditWithProgress _EditZ;
        Edit _EditValueX;
        Edit _EditValueY;
        Edit _EditValueZ;

        public override void InspectProperty(ObjectProperty ObjectProperty)
        {
            base.InspectProperty(ObjectProperty);
            Control Container = GetValueContainer();

            _EditX = new EditWithProgress(Container);
            _EditX.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditX.TextChangedEvent += OnEditValueTextChanged;
            _EditValueX = _EditX.GetEditValue();

            _EditY = new EditWithProgress(Container);
            _EditY.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditY.TextChangedEvent += OnEditValueTextChanged;
            _EditValueY = _EditY.GetEditValue();

            _EditZ = new EditWithProgress(Container);
            _EditZ.SetRange(ObjectProperty.ValueMin, ObjectProperty.ValueMax);
            _EditZ.TextChangedEvent += OnEditValueTextChanged;
            _EditValueZ = _EditZ.GetEditValue();

            _ColorX = CreateColorPanel(Container, Color_Red);
            _ColorY = CreateColorPanel(Container, Color_Green);
            _ColorZ = CreateColorPanel(Container, Color_Blue);

            if (_ObjectProperty.ReadOnly)
            {
                _EditX.SetReadOnly(true);
                _EditY.SetReadOnly(true);
                _EditZ.SetReadOnly(true);
            }

            if (_ObjectProperty.DefaultValue == null)
            {
                _ObjectProperty.DefaultValue = GetPropertyValue();
                _bCanRevert = true;
            }

            ReadValue();
        }

        public Panel CreateColorPanel(Control Container, Color Color)
        {
            Panel ColorPanel = new Panel();
            ColorPanel.Initialize();
            ColorPanel.SetBackgroundColor(Color);
            Container.AddChild(ColorPanel);
            return ColorPanel;
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            base.UpdateLayout(Width, ref Y);

            int EditWidth = (GetValueWidth() - SPAN_X * 4) / 3;
            EditWidth = Math.Min(EditWidth, DEFAULT_WIDTH);
            _EditX.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditX);
            _ColorX.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorX, 2);
            _EditY.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditY);
            _ColorY.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorY, 2);
            _EditZ.SetPosition(0, SPAN_Y, EditWidth, PROPERTY_FONT_SIZE);
            GetValueContainer().FloatToLeft(_EditZ);
            _ColorZ.SetPosition(0, SPAN_Y + 1, 3, PROPERTY_FONT_SIZE - 2);
            GetValueContainer().PlaceAsLeft(_ColorZ, 2);
        }

        public override void ReadValue()
        {
            object PropertyValue = GetPropertyValue();
            string XString = "0";
            string YString = "0";
            string ZString = "0";
            if (_ObjectProperty.Type == typeof(Vector3d))
            {
                Vector3d Value = (Vector3d)PropertyValue;
                XString = MathHelper.NumberToString(Value.X);
                YString = MathHelper.NumberToString(Value.Y);
                ZString = MathHelper.NumberToString(Value.Z);
            }
            else
            {
                Vector3f Value = (Vector3f)PropertyValue;
                XString = MathHelper.NumberToString(Value.X);
                YString = MathHelper.NumberToString(Value.Y);
                ZString = MathHelper.NumberToString(Value.Z);
            }

            if (_ValueExtraProperty._bHasMultipleValues)
            {
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("X"))
                {
                    XString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("Y"))
                {
                    YString = MULTIPLE_VALUES_STRING;
                }
                if (_ValueExtraProperty._HaveMultipleValuesSubProperties.Contains("Z"))
                {
                    ZString = MULTIPLE_VALUES_STRING;
                }
            }

            _EditX.SetText(XString);
            _EditY.SetText(YString);
            _EditZ.SetText(ZString);
        }

        public override void WriteValue()
        {
            base.WriteValue();
            string ValueXString = _EditValueX.GetText();
            string ValueYString = _EditValueY.GetText();
            string ValueZString = _EditValueZ.GetText();
            object NewObject = null;
            if (_ObjectProperty.Type == typeof(Vector3d))
            {
                Vector3d NewValue = new Vector3d();
                NewValue.X = MathHelper.ParseDouble(ValueXString);
                NewValue.Y = MathHelper.ParseDouble(ValueYString);
                NewValue.Z = MathHelper.ParseDouble(ValueZString);
                NewObject = NewValue;
            }
            else
            {
                Vector3f NewValue = new Vector3f();
                NewValue.X = MathHelper.ParseFloat(ValueXString);
                NewValue.Y = MathHelper.ParseFloat(ValueYString);
                NewValue.Z = MathHelper.ParseFloat(ValueZString);
                NewObject = NewValue;
            }
            SetPropertyValue(NewObject);
        }

        void OnEditValueTextChanged(Control Sender)
        {
            if (_ObjectProperty.ReadOnly)
                return;

            if (Sender == _EditValueX)
            {
                SetSubProperty("X");
            }
            else if (Sender == _EditValueY)
            {
                SetSubProperty("Y");
            }
            else if (Sender == _EditValueZ)
            {
                SetSubProperty("Z");
            }

            RecordAndWriteValue();

            ClearSubProperty();
        }
    }
}
