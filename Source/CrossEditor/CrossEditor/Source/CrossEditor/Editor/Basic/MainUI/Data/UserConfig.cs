using System;
using EditorUI;
using System.IO;

namespace CrossEditor
{
    public class UserConfig
    {
        public const string USER_CONFIG_FILENAME = "UserConfig.config";

        static UserConfig _Instance = new UserConfig();

        public static UserConfig GetInstance()
        {
            return _Instance;
        }

        public void SaveUserConfig()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            if (ProjectDirectory == "")
            {
                return;
            }
            string UserConfigFilename = ProjectDirectory + "/" + USER_CONFIG_FILENAME;
            XmlScript Xml = new XmlScript();
            Record RootRecord = Xml.GetRootRecord();

            Record RecordGlobalConfig = RootRecord.AddChild();
            RecordGlobalConfig.SetTypeString("GlobalConfig");

            ProjectUI.GetInstance().SaveUserConfig(RootRecord);
            ConsoleUI.GetInstance().SaveUserConfig(RootRecord);
            HierarchyUI.GetInstance().SaveUserConfig(RootRecord);
            EditorSceneUI.GetInstance().SaveUserConfig(RootRecord);
            CaptureComparerUI.GetInstance().SaveUserConfig(RootRecord);

            Record RecordMainWindow = RootRecord.AddChild();
            Device Device = UIManager.GetMainUIManager().GetDevice();
            int X = Device.GetOriginalX();
            int Y = Device.GetOriginalY();
            int Width = Device.GetOriginalWidth();
            int Height = Device.GetOriginalHeight();
            bool bMaximized = Device.IsMaximized();
            RecordMainWindow.SetTypeString("MainWindow");
            RecordMainWindow.SetInt("X", X);
            RecordMainWindow.SetInt("Y", Y);
            RecordMainWindow.SetInt("Width", Width);
            RecordMainWindow.SetInt("Height", Height);
            RecordMainWindow.SetBool("Maximized", bMaximized);

            MainUI.GetDockingControl().SaveDockingInfo(RecordMainWindow);

            WindowManager.GetInstance().SaveUserConfig(RootRecord);

            Xml.Save(UserConfigFilename);
        }

        public void LoadUserConfig()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string UserConfigFilename = ProjectDirectory + "/" + USER_CONFIG_FILENAME;

            Device Device = UIManager.GetMainUIManager().GetDevice();
            Device.SetPosition(0, 0, 1280, 720);
            Device.Center();
            if (File.Exists(UserConfigFilename) == false)
            {
                Device.ShowNormal();
                return;
            }

            XmlScript Xml = new XmlScript();
            Xml.Open(UserConfigFilename);
            Record RootRecord = Xml.GetRootRecord();

            Record RecordGlobalConfig = RootRecord.FindByTypeString("GlobalConfig");
            if (RecordGlobalConfig != null)
            {
            }

            ProjectUI.GetInstance().LoadUserConfig(RootRecord);
            ConsoleUI.GetInstance().LoadUserConfig(RootRecord);
            HierarchyUI.GetInstance().LoadUserConfig(RootRecord);
            EditorSceneUI.GetInstance().LoadUserConfig(RootRecord);
            CaptureComparerUI.GetInstance().LoadUserConfig(RootRecord);

            Record RecordMainWindow = RootRecord.FindByTypeString("MainWindow");
            if (RecordMainWindow != null)
            {
                int X = RecordMainWindow.GetInt("X");
                int Y = RecordMainWindow.GetInt("Y");
                int Width = RecordMainWindow.GetInt("Width");
                int Height = RecordMainWindow.GetInt("Height");
                bool bMaximized = RecordMainWindow.GetBool("Maximized");
                if (Device.IsPointInScreens(X, Y) == false &&
                    Device.IsPointInScreens(X + Width / 2, Y + Height / 2) == false &&
                    Device.IsPointInScreens(X + Width, Y + Height) == false)
                {
                    X = 100;
                    Y = 100;
                    Width = 1280;
                    Height = 720;
                }
                Device.ShowNormal();
                Device.SetPosition(X, Y, Width, Height);
                if (bMaximized)
                {
                    Device.ShowMaximize();
                }

                MainUI.GetDockingControl().LoadDockingInfo(RecordMainWindow);
                /*bool bOpenLastScene = ProjectsUI.GetInstance().GetOpenLastScene();
                bool bHasRecentScene = RecentList.GetInstance().GetRecentItemCount() > 0;
				if (bOpenLastScene || bHasRecentScene == false)
                {
                    MainUI.ActivateDockingCard_Scene();
                }
                else
                {
                    MainUI.ActivateDockingCard_Welcome();
                }*/
                MainUI.ActivateDockingCard_Scene();

                WindowManager.GetInstance().LoadUserConfig(RootRecord);
            }
            else
            {
                Device.ShowNormal();
            }
        }

        public void OnResetLayout()
        {
            MainUI MainUI = MainUI.GetInstance();
            string ProjectDirectory = MainUI.GetProjectDirectory();
            string UserConfigFilename = ProjectDirectory + "/" + USER_CONFIG_FILENAME;



            CommonDialogUI CommonDialogUI = new CommonDialogUI();
            CommonDialogUI.Initialize(MainUI.GetUIManager(), "Tips", "Do you want to reset layout, restart editor is need", CommonDialogType.YesNoCancel);
            CommonDialogUI.CloseEvent += (CommonDialogUI Sender1, CommonDialogResult Result) =>
            {
                if (Result == CommonDialogResult.Yes)
                {
                    if (File.Exists(UserConfigFilename))
                    {
                        File.Delete(UserConfigFilename);
                        Environment.Exit(0);
                    }
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);

        }
    }
}
