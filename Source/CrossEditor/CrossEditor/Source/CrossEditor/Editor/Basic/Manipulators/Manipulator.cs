namespace CrossEditor
{
    class Manipulator
    {
        public virtual void AddEditOperation_ModifyProperty(object Object)
        {
        }

        public virtual void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
        }

        public virtual void OnMouseMove(int MouseX, int MouseY)
        {
        }

        public virtual void OnMouseUp(int MouseX, int MouseY)
        {
        }
    }
}
