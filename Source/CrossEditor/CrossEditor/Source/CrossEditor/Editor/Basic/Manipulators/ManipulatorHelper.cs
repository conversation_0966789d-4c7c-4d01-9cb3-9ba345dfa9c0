using System.Collections.Generic;

namespace CrossEditor
{
    public class ManipulatorHelper
    {
        // handler
        LocalTranslator _LocalTranslator;
        GlobalTranslator _GlobalTranslator;
        LocalRotator _LocalRotator;
        GlobalRotator _GlobalRotator;
        LocalScaler _LocalScaler;
        Manipulator _CurrentManipulator;

        ManipulatorType _ManipulatorType;

        public Vector3d _OldTranslation;
        public Vector3d _NewTranslation;
        public Quaterniond _OldRotation;
        public Quaterniond _NewRotation;
        public Vector3d _OldScaling;
        public Vector3d _NewScaling;

        public Matrix4x4d _SavedLocalMatrix;
        public Matrix4x4d _TargetLocalMatrix;
        public Matrix4x4d _SavedWorldMatrix;
        public Matrix4x4d _TargetWorldMatrix;
        public Matrix4x4d _ParentWorldMatrix;
        public Matrix4x4d _ParentWorldMatrixInverse;

        public Vector3d _Pivot;

        public Vector3d _OldTranslation_Pivot;
        public Vector3d _NewTranslation_Pivot;
        public Quaterniond _OldRotation_Pivot;
        public Quaterniond _NewRotation_Pivot;
        public Vector3d _OldScaling_Pivot;
        public Vector3d _NewScaling_Pivot;

        public Matrix4x4d _SavedLocalMatrix_Pivot;
        public Matrix4x4d _TargetLocalMatrix_Pivot;
        public Matrix4x4d _SavedWorldMatrix_Pivot;
        public Matrix4x4d _TargetWorldMatrix_Pivot;

        Entity _CameraEntity;
        SceneUIBase _SceneUI;

        bool _Manipulating;

        List<EntityTransformItem> _EntityTransformItemList;

        int _TranslationStep;
        float _RotationStep;
        float _ScalingStep;

        bool _SurfaceSnapping = true;
        bool _RotateToNormal = true;
        float _SurfaceOffset;

        public ManipulatorHelper(SceneUIBase SceneUI)
        {
            _SceneUI = SceneUI;
            _LocalTranslator = new LocalTranslator(this);
            _GlobalTranslator = new GlobalTranslator(this);
            _LocalRotator = new LocalRotator(this);
            _GlobalRotator = new GlobalRotator(this);
            _LocalScaler = new LocalScaler(this);
            _Manipulating = false;

            _CurrentManipulator = _LocalTranslator;

            _OldTranslation = new Vector3d();
            _NewTranslation = new Vector3d();
            _OldRotation = new Quaterniond();
            _NewRotation = new Quaterniond();
            _OldScaling = new Vector3d();
            _NewScaling = new Vector3d();

            _SavedLocalMatrix = new Matrix4x4d();
            _SavedLocalMatrix.LoadIdentity();
            _TargetLocalMatrix = new Matrix4x4d();
            _TargetLocalMatrix.LoadIdentity();

            _SavedWorldMatrix = new Matrix4x4d();
            _SavedWorldMatrix.LoadIdentity();
            _TargetWorldMatrix = new Matrix4x4d();
            _TargetWorldMatrix.LoadIdentity();

            _ParentWorldMatrix = new Matrix4x4d();
            _ParentWorldMatrix.LoadIdentity();

            _ParentWorldMatrixInverse = new Matrix4x4d();
            _ParentWorldMatrixInverse.LoadIdentity();

            _Pivot = new Vector3d();

            _OldTranslation_Pivot = new Vector3d();
            _NewTranslation_Pivot = new Vector3d();
            _OldRotation_Pivot = new Quaterniond();
            _NewRotation_Pivot = new Quaterniond();
            _OldScaling_Pivot = new Vector3d();
            _NewScaling_Pivot = new Vector3d();

            _SavedLocalMatrix_Pivot = new Matrix4x4d();
            _SavedLocalMatrix_Pivot.LoadIdentity();
            _TargetLocalMatrix_Pivot = new Matrix4x4d();
            _TargetLocalMatrix_Pivot.LoadIdentity();

            _SavedWorldMatrix_Pivot = new Matrix4x4d();
            _SavedWorldMatrix_Pivot.LoadIdentity();
            _TargetWorldMatrix_Pivot = new Matrix4x4d();
            _TargetWorldMatrix_Pivot.LoadIdentity();

            _EntityTransformItemList = new List<EntityTransformItem>();
        }

        public bool GetManipulating()
        {
            return _Manipulating;
        }

        public void SaveTransformStates(Transform Transform)
        {
            Matrix4x4d LocalMatrix = new Matrix4x4d();
            Transform.GetMatrix(ref LocalMatrix);

            Matrix4x4d WorldMatrix = new Matrix4x4d();
            Transform.GetWorldMatrix(ref WorldMatrix);

            Vector3d Pivot = Transform.Entity.GetPivot();

            SaveTransformStates_Common(ref LocalMatrix, ref WorldMatrix, ref Pivot);
        }

        public void SaveTransformStates(ref Matrix4x4d LocalMatrix, ref Matrix4x4d WorldMatrix)
        {
            Vector3d Pivot = new Vector3d();
            SaveTransformStates_Common(ref LocalMatrix, ref WorldMatrix, ref Pivot);
        }

        public void SaveTransformStates_Common(ref Matrix4x4d LocalMatrix, ref Matrix4x4d WorldMatrix, ref Vector3d Pivot)
        {
            _Pivot = Pivot;

            Matrix4x4d PivotMatrix = new Matrix4x4d();
            PivotMatrix.LoadIdentity();
            PivotMatrix.SetPosition(ref _Pivot);

            _SavedLocalMatrix_Pivot = LocalMatrix;
            _SavedLocalMatrix.Mul(ref PivotMatrix, ref _SavedLocalMatrix_Pivot);

            _SavedWorldMatrix_Pivot = WorldMatrix;
            _SavedWorldMatrix.Mul(ref PivotMatrix, ref _SavedWorldMatrix_Pivot);

            SaveTransformStates_Inner();
        }

        public void SaveTransformStates_Inner()
        {
            _TargetLocalMatrix_Pivot = _SavedLocalMatrix_Pivot;
            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(_SavedLocalMatrix_Pivot.ToClicrossDouble4x4());
            _OldScaling_Pivot = new Vector3d(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            _OldRotation_Pivot = new Quaterniond(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            _OldTranslation_Pivot = new Vector3d(trs.translation.x, trs.translation.y, trs.translation.z);
            _OldRotation_Pivot.Normalize();

            _NewScaling_Pivot = _OldScaling_Pivot;
            _NewRotation_Pivot = _OldRotation_Pivot;
            _NewTranslation_Pivot = _OldTranslation_Pivot;

            _TargetLocalMatrix = _SavedLocalMatrix;
            var trs2 = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(_SavedLocalMatrix.ToClicrossDouble4x4());
            _OldScaling = new Vector3d(trs2.scaling.x, trs2.scaling.y, trs2.scaling.z);
            _OldRotation = new Quaterniond(trs2.rotation.x, trs2.rotation.y, trs2.rotation.z, trs2.rotation.w);
            _OldTranslation = new Vector3d(trs2.translation.x, trs2.translation.y, trs2.translation.z);

            _OldRotation.Normalize();

            _NewScaling = _OldScaling;
            _NewRotation = _OldRotation;
            _NewTranslation = _OldTranslation;

            _TargetWorldMatrix = _SavedWorldMatrix;
            _TargetWorldMatrix_Pivot = _SavedWorldMatrix_Pivot;

            Matrix4x4d LocalMatrixInverse = _SavedLocalMatrix.Inverse();
            _ParentWorldMatrix.Mul(ref LocalMatrixInverse, ref _SavedWorldMatrix);
            _ParentWorldMatrixInverse = _ParentWorldMatrix.Inverse();
            trs.Dispose();
            trs2.Dispose();
        }

        public void ClearEntityItems()
        {
            _EntityTransformItemList.Clear();
        }

        public List<Entity> GetRelatedEntities()
        {
            List<Entity> Entities = new List<Entity>();
            foreach (EntityTransformItem Item in _EntityTransformItemList)
            {
                Item._Entity.TraverseAll((Entity) =>
                {
                    Entities.Add(Entity);
                });
            }

            return Entities;
        }

        public void AddEntityItem(Entity Entity)
        {
            EntityTransformItem EntityTransformItem = new EntityTransformItem();
            EntityTransformItem._Entity = Entity;
            Transform transform = Entity.GetTransformComponent();
            EntityTransformItem._Transform = transform;
            transform.GetWorldMatrix(ref EntityTransformItem._SavedWorldMatrix);
            _EntityTransformItemList.Add(EntityTransformItem);
        }

        public void UpdateEntityItems()
        {
            Matrix4x4d SavedWorldMatrixInverse = _SavedWorldMatrix.Inverse();
            Matrix4x4d WorldTransformMatrix = new Matrix4x4d();
            WorldTransformMatrix.Mul(ref SavedWorldMatrixInverse, ref _TargetWorldMatrix);
            foreach (EntityTransformItem EntityTransformItem in _EntityTransformItemList)
            {
                Matrix4x4d TargetWorldMatrix_EntityItem = new Matrix4x4d();
                TargetWorldMatrix_EntityItem.Mul(ref EntityTransformItem._SavedWorldMatrix, ref WorldTransformMatrix);
                EntityTransformItem._TargetWorldMatrix = TargetWorldMatrix_EntityItem;
                EntityTransformItem._Transform.SetWorldMatrix(ref TargetWorldMatrix_EntityItem);
            }
        }

        public Matrix4x4d GetSavedLocalMatrix()
        {
            return _SavedLocalMatrix;
        }

        public Matrix4x4d GetSavedWorldMatrix()
        {
            return _SavedWorldMatrix;
        }

        public Matrix4x4d GetTargetLocalMatrix()
        {
            return _TargetLocalMatrix;
        }

        public void UpdatTargetMatrix()
        {
            Clicross.Double3 NewScaling = new Clicross.Double3(_NewScaling.X, _NewScaling.Y, _NewScaling.Z);
            Clicross.Quaternion64 NewRotation = new Clicross.Quaternion64(_NewRotation.X, _NewRotation.Y, _NewRotation.Z, _NewRotation.W);
            Clicross.Double3 NewTranslation = new Clicross.Double3(_NewTranslation.X, _NewTranslation.Y, _NewTranslation.Z);
            _TargetLocalMatrix = new Matrix4x4d(Clicross.math.MatrixUtil.Math_MatrixComposeD(NewScaling, NewRotation, NewTranslation));
            _TargetWorldMatrix.Mul(ref _TargetLocalMatrix, ref _ParentWorldMatrix);
            UpdateOthers();
        }

        public void UpdateTargetWorldMatrix()
        {
            _TargetLocalMatrix.Mul(ref _TargetWorldMatrix, ref _ParentWorldMatrixInverse);

            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(_TargetLocalMatrix.ToClicrossDouble4x4());
            _NewScaling = new Vector3d(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            _NewRotation = new Quaterniond(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            _NewTranslation = new Vector3d(trs.translation.x, trs.translation.y, trs.translation.z);
            UpdateOthers();
        }

        void UpdateOthers()
        {
            Vector3d InversePivot = -_Pivot;
            Matrix4x4d InversePivotMatrix = new Matrix4x4d();
            InversePivotMatrix.LoadIdentity();
            InversePivotMatrix.SetPosition(ref InversePivot);

            _TargetLocalMatrix_Pivot.Mul(ref InversePivotMatrix, ref _TargetLocalMatrix);
            _TargetWorldMatrix_Pivot.Mul(ref InversePivotMatrix, ref _TargetWorldMatrix);

            var trs = Clicross.math.MatrixUtil.Math_MatrixDecomposeD(_TargetLocalMatrix.ToClicrossDouble4x4());
            _NewScaling_Pivot = new Vector3d(trs.scaling.x, trs.scaling.y, trs.scaling.z);
            _NewRotation_Pivot = new Quaterniond(trs.rotation.x, trs.rotation.y, trs.rotation.z, trs.rotation.w);
            _NewTranslation_Pivot = new Vector3d(trs.translation.x, trs.translation.y, trs.translation.z);
        }

        public Matrix4x4d GetTargetWorldMatrix()
        {
            return _TargetWorldMatrix;
        }

        public SceneUIBase GetSceneUI()
        {
            return _SceneUI;
        }

        public void SetCameraEntiy(Entity CameraEntity)
        {
            _CameraEntity = CameraEntity;
        }

        public Entity GetCameraEntity()
        {
            return _CameraEntity;
        }

        public void OnMouseDown(RayPickResult RayPickResult, int MouseX, int MouseY)
        {
            _CurrentManipulator.OnMouseDown(RayPickResult, MouseX, MouseY);
            _Manipulating = true;
        }

        public void OnMouseMove(int MouseX, int MouseY)
        {
            _CurrentManipulator.OnMouseMove(MouseX, MouseY);
        }

        public void OnMouseUp(int MouseX, int MouseY)
        {
            _CurrentManipulator.OnMouseUp(MouseX, MouseY);
            _Manipulating = false;
        }

        public void SetManipulatorType(ManipulatorType ManipulatorType)
        {
            if (_CurrentManipulator != null)
            {
                _CurrentManipulator.OnMouseUp(-1, -1);
            }
            switch (ManipulatorType)
            {
                case ManipulatorType.LocalTranslator:
                    _CurrentManipulator = _LocalTranslator;
                    break;
                case ManipulatorType.GlobalTranslator:
                    _CurrentManipulator = _GlobalTranslator;
                    break;
                case ManipulatorType.LocalRotator:
                    _CurrentManipulator = _LocalRotator;
                    break;
                case ManipulatorType.GlobalRotator:
                    _CurrentManipulator = _GlobalRotator;
                    break;
                case ManipulatorType.LocalScaler:
                    _CurrentManipulator = _LocalScaler;
                    break;
                case ManipulatorType.GlobalScaler:
                    break;
            }
            if (_CurrentManipulator != null)
            {
                _CurrentManipulator.OnMouseUp(-1, -1);
            }
            _ManipulatorType = ManipulatorType;
        }

        public ManipulatorType GetManipulatorType()
        {
            return _ManipulatorType;
        }

        public void AddEditOperation_ModifyProperty(object Object)
        {
            if (_CurrentManipulator == null)
            {
                return;
            }

            EditOperationManager.GetInstance().BeginRecordCompoundOperation();
            _CurrentManipulator.AddEditOperation_ModifyProperty(Object);
            AddEditOperation_Entities();
            EditOperationManager.GetInstance().EndRecordCompoundOperation();
        }

        public void AddEditOperation_Entities()
        {
            EditOperation_EntitiesTransform EditOperation_EntitiesTransform = new EditOperation_EntitiesTransform();
            foreach (EntityTransformItem EntityTransformItem in _EntityTransformItemList)
            {
                EditOperation_EntitiesTransform.AddEntityTransformItem(EntityTransformItem);
            }
            EditOperationManager.GetInstance().AddOperation(EditOperation_EntitiesTransform);
        }

        #region Get&Set Step

        public void SetTranslationStep(int Step)
        {
            _TranslationStep = Step;
        }

        public int GetTranslationStep()
        {
            return _TranslationStep;
        }

        public void SetRotationStep(float Step)
        {
            _RotationStep = Step;
        }

        public float GetRotationStep()
        {
            return _RotationStep;
        }

        public void SetScalingStep(float Step)
        {
            _ScalingStep = Step;
        }

        public float GetScalingStep()
        {
            return _ScalingStep;
        }

        #endregion

        #region Get&Set Snapping

        public void SwitchSurfaceSnapping()
        {
            _SurfaceSnapping = !_SurfaceSnapping;
        }

        public bool GetSurfaceSnapping()
        {
            return _SurfaceSnapping;
        }

        public void SwitchRotateToNormal()
        {
            _RotateToNormal = !_RotateToNormal;
        }

        public bool GetRotateToNormal()
        {
            return _RotateToNormal;
        }

        public void SetSurfaceOffset(float Offset)
        {
            _SurfaceOffset = Offset;
        }

        public float GetSurfaceOffset()
        {
            return _SurfaceOffset;
        }

        #endregion
    }
}
