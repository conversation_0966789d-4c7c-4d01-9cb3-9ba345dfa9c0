using CEngine;
using EditorUI;

namespace CrossEditor
{
    public delegate void CreateMotionMatchUIInputedEventHandler(CreateMotionMatchUI Sender, string sequences);

    public class CreateMotionMatchUI : DialogUI
    {

        Label _LabelTips;
        Edit _EditSkeleton;
        Button _ButtonBrowse;

        public event CreateMotionMatchUIInputedEventHandler InputedEvent;

        public CreateMotionMatchUI()
        {
        }

        public void Initialize(UIManager UIManager, string Title, string sequences)
        {
            base.Initialize(UIManager, Title, 600, 200);

            _LabelTips = new Label();
            _LabelTips.SetPosition(30, 80, 500, 16);
            _LabelTips.SetFontSize(16);
            _LabelTips.SetText("Animation:");
            _LabelTips.SetTextAlign(TextAlign.CenterLeft);
            _PanelDialog.AddChild(_LabelTips);

            _EditSkeleton = new Edit();
            _EditSkeleton.SetFontSize(14);
            _EditSkeleton.Initialize(EditMode.Simple_SingleLine);
            _EditSkeleton.LoadSource(sequences);
            _PanelDialog.AddChild(_EditSkeleton);
            EditContextUI.GetInstance().RegisterEdit(_EditSkeleton);
            _EditSkeleton.SetPosition(30, 100, 500, 14);

            _ButtonBrowse = new Button();
            _ButtonBrowse.Initialize();
            _ButtonBrowse.SetFontSize(12);
            _ButtonBrowse.SetText("...");
            _ButtonBrowse.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _ButtonBrowse.SetToolTips("Browse Animation");
            _ButtonBrowse.ClickedEvent += OnButtonBrowseClicked;
            _PanelDialog.AddChild(_ButtonBrowse);
            _ButtonBrowse.SetPosition(540, 100, 24, 16);

            Button ButtonOK = new Button();
            ButtonOK.Initialize();
            ButtonOK.SetBorderColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
            ButtonOK.SetText("OK");
            ButtonOK.SetFontSize(16);
            ButtonOK.SetTextOffsetY(3);
            ButtonOK.ClickedEvent += OnButtonOKClicked;
            _PanelDialog.AddChild(ButtonOK);

            ButtonOK.SetSize(100, 24);
            ButtonOK.MakeCenterX();
            ButtonOK.SetY(150);
        }


        public void UpdateLayout()
        {

        }

        public override void ShowDialog()
        {
            base.ShowDialog();
        }

        public override void OnDeviceChar(Device Sender, char Char)
        {
            base.OnDeviceChar(Sender, Char);

            Device Device = GetDevice();
            bool bControl = Device.IsControlDown();
            bool bShift = Device.IsShiftDown();
            bool bAlt = Device.IsAltDown();
            bool bNone = !bControl && !bShift && !bAlt;

            if (bNone && (Char == '\r' || Char == '\n'))
            {
                OnButtonOKClicked(null);
            }
        }

        void OnButtonBrowseClicked(Button Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Sequence File";
            PathInputUIFilterItem.Extensions.Add("nda");

            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.Initialize(GetUIManager(), "", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                string EditorFilename = PathInputed;
                string StandardFilename = EditorUtilities.EditorFilenameToStandardFilename(EditorFilename);
                _EditSkeleton.SetText(StandardFilename);
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        void OnButtonOKClicked(Button Sender)
        {
            string Shader = _EditSkeleton.GetText();
            Shader = EditorUtilities.EditorFilenameToStandardFilename(Shader);
            string Shader1 = EditorUtilities.StandardFilenameToEditorFilename(Shader);
            if (FileHelper.IsFileExists(Shader1) == false)
            {
                string Tips = string.Format("File: {0} not exists.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            string Extension = PathHelper.GetExtension(Shader1);
            if (StringHelper.IgnoreCaseEqual(Extension, ".nda") == false)
            {
                string Tips = string.Format("File: {0} is not a nda file.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(Shader);
            if (ObjectClassID != ClassIDType.CLASS_AnimSequenceRes)
            {
                string Tips = string.Format("File: {0} is not a animation asset.", Shader);
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", Tips);
                return;
            }
            CloseDialog();
            if (InputedEvent != null)
            {
                InputedEvent(this, Shader);
            }
        }


    }
}
