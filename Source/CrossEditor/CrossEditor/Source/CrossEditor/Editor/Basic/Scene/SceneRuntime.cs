using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using FloatPtr = System.IntPtr;
namespace CEngine
{
    public delegate void CrossEngineLogDelegate(int type, string log);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorGeneralCallBack();

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void ResultGeneralCallBack(bool ret);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorFetchMousePosCallBack(FloatPtr x, FloatPtr y);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorShowMouseCursorCallBack(bool bShow);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorLockMouseCursorCallBack(float x, float y, float width, float height);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorBakedReflectionProbePathCallBack([MarshalAs(UnmanagedType.LPStr)] string path);

    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate void EditorEntityGeneralCallBack(ulong entityId, bool recurive);
}

namespace CrossEditor
{
    class CallBackUtil
    {
        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SetBuildHierarchyCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorGeneralCallBack callback, IntPtr world);

        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SetEditorUpdateHierarchyCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorGeneralCallBack callback, IntPtr world);

        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SetBuildEntityHierarchyCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorEntityGeneralCallBack callback, IntPtr world);

        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        public static extern bool SetEditorGlobalUpdateCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorGeneralCallBack callback);


        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        public static extern bool SetEditorGlobalBeginFrameCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorGeneralCallBack callback);

        [DllImport("CrossEngine", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        public static extern bool SetEditorGlobalEndFrameCallback([MarshalAs(UnmanagedType.FunctionPtr)] EditorGeneralCallBack callback);
    }
    public delegate void EditorGlobalUpdateEventHandler(Device Sender, long TimeElapsed);
    class UIEventInfo
    {
        public virtual void Trigger()
        {
        }
    }


    class KeyInfo : UIEventInfo
    {
        public UIManager UIManager;
        public Device Sender;
        public KeyAction KeyAction;
        public Key Key;

        public override void Trigger()
        {
            UIManager.TriggerKeyEvent(Sender, KeyAction, Key);
        }
    }

    class MouseInfo : UIEventInfo
    {
        public UIManager UIManager;
        public Device Sender;
        public MouseButton MouseButton;
        public MouseAction MouseAction;
        public int MouseX;
        public int MouseY;
        public int MouseDeltaZ;
        public int MouseDeltaW;

        public override void Trigger()
        {
            UIManager.TriggerMouseEvent(Sender, MouseButton, MouseAction, MouseX, MouseY, MouseDeltaZ, MouseDeltaW);
            SceneRuntime.GetInstance().PostTriggerMouseEvent(Sender, MouseButton, MouseAction, MouseX, MouseY, MouseDeltaZ, MouseDeltaW);


        }
    }

    class CharInfo : UIEventInfo
    {
        public UIManager UIManager;
        public Device Sender;
        public char Char;

        public override void Trigger()
        {
            UIManager.TriggerCharEvent(Sender, Char);
        }
    }

    class SceneRuntime
    {
        public static string WindowsGenerator { get { return "Visual_Studio_17_2022_Win64_md"; } }
        public static bool IsUnix { get { return Environment.OSVersion.Platform == PlatformID.Unix; } }
        public static bool IsWindows { get { return Environment.OSVersion.Platform != PlatformID.Unix; } }
        static SceneRuntime _Instance = new SceneRuntime();

        UIManager _UIManager;
        CrossEngine _CrossEngine;
        IntPtr _RenderWindowHandle;
        public List<System.Threading.Tasks.Task> WorldSetTaskQueue = new List<System.Threading.Tasks.Task>();
        public Clicross.IRenderWindow _RenderWindow;
        public event EditorGlobalUpdateEventHandler EditorGlobalUpdateEvent;
        public bool EnableEngineTick = true;
        public EditorGeneralCallBack _BeginFrameFunc;
        public EditorGeneralCallBack _EditorUpdateFunc;
        public EditorGeneralCallBack _EndFrameFunc;
        long _LastTime = 0;
        Queue<UIEventInfo> _UIEventInfoQueue = new Queue<UIEventInfo>();
        public int _LastMouseX = 0;
        public int _LastMouseY = 0;

        public static SceneRuntime GetInstance()
        {
            return _Instance;
        }

        SceneRuntime()
        {
        }

        static void EditorLogHandler(int LogType, string LogMessage)
        {
            LogMessageType LogMessageType = LogMessageType.Information;
            LogLevel LogLevel = (LogLevel)LogType;
            switch (LogLevel)
            {
                case LogLevel.Information:
                    LogMessageType = LogMessageType.Information;
                    break;
                case LogLevel.Warning:
                    LogMessageType = LogMessageType.Warning;
                    break;
                case LogLevel.Error:
                    LogMessageType = LogMessageType.Error;
                    break;
                case LogLevel.Trace:
                    LogMessageType = LogMessageType.Trace;
                    break;
                case LogLevel.Debug:
                    LogMessageType = LogMessageType.Debug;
                    break;
                case LogLevel.Fatal:
                    LogMessageType = LogMessageType.Error;
                    break;
                case LogLevel.Quiet:
                    return;

            }
            ConsoleUI.GetInstance().AddLogItem(LogMessageType, LogMessage);
        }

        public UIManager GetUIManager()
        {
            return _UIManager;
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public CrossEngine GetCrossEngine()
        {
            return _CrossEngine;
        }

        public void Initialize(UIManager UIManager, string ProjectDirectory, string ResourceDirectory, string[] args)
        {
            _UIManager = UIManager;
            Device Device = _UIManager.GetDevice();

            EditorUICanvas EditorUICanvas = EditorUICanvas.GetInstance();
            Device.SetEditorUICanvas(EditorUICanvas);


            ImportAssetTask.Initialize();

            _CrossEngine = CrossEngine.GetInstance();
            _CrossEngine.SetLogHandler(new CrossEngineLogDelegate(EditorLogHandler));
            _CrossEngine.Initialize(AppStartUpType.CrossEditor, ProjectDirectory, ResourceDirectory, false, args);

            float DPR = 1.0f;
            string Title = "Cross Editor";
            _RenderWindowHandle = Device.GetNativeWindowPointer();

            int DeviceWidth = Device.GetWidth();
            int DeviceHeight = Device.GetHeight();

            _RenderWindow = _CrossEngine.CreateRenderWindow(_RenderWindowHandle, 0, 0, DeviceWidth, DeviceHeight, DPR, Title);

            EditorUICanvas.Initialize(OnEditorUI, _RenderWindow);

            EditorScene.GetInstance().Initialize(_CrossEngine);
            GameScene.GetInstance().Initialize(_CrossEngine);
            MSAPreviewScene.GetInstance().Initialize(_CrossEngine);
            MEPreviewScene.GetInstance().Initialize(_CrossEngine);
            ParticleSystemScene.GetInstance().Initialize(_CrossEngine);
            PrefabScene.GetInstance().Initialize(_CrossEngine);
            PreviewScene.GetInstance().Initialize(_CrossEngine);
            //PCGScene.GetInstance().Initialize(_CrossEngine);
            _BeginFrameFunc = OnBeginFrame;
            _EditorUpdateFunc = OnUpdate;
            _EndFrameFunc = OnEndFrame;
            CallBackUtil.SetEditorGlobalUpdateCallback(_EditorUpdateFunc);
            CallBackUtil.SetEditorGlobalBeginFrameCallback(_BeginFrameFunc);
            CallBackUtil.SetEditorGlobalEndFrameCallback(_EndFrameFunc);
        }

        public void ShutDown()
        {
            _CrossEngine._CrossEngineInterface.ShutDown();
        }

        public void OpenProject(string AssetPath)
        {
            _CrossEngine.SetAssetPath(AssetPath);
        }

        public void Resize(int Width, int Height)
        {
            _RenderWindow.Resize((uint)Width, (uint)Height, 0u);
        }

        public void Resizing(int Width, int Height)
        {
            _CrossEngine.FlushRendering();
        }

        public void KeyMouseEventUpdateFunc()
        {
            lock (_UIEventInfoQueueLock)
            {
                while (_UIEventInfoQueue.Count > 0)
                {
                    UIEventInfo UIEventInfo = _UIEventInfoQueue.Dequeue();
                    UIEventInfo.Trigger();
                }
            }
        }

        public void OnBeginFrame()
        {
            Device Device = GetDevice();
            GetDevice().Run();
            if (_CrossEngine == null)
            {
                return;
            }
            DeferredResizeTasks.Instance.Execute();
            int DeviceWidth = Device.GetWidth();
            int DeviceHeight = Device.GetHeight();
            if (Device.IsMinimized())
            {
                DeviceWidth = 0;
                DeviceHeight = 0;
            }
            _RenderWindow.Resize((uint)DeviceWidth, (uint)DeviceHeight, 0u);
            EditorUICanvas.GetInstance().SetSize(DeviceWidth, DeviceHeight);
        }

        public void OnUpdate()
        {
            Device Device = GetDevice();
            if (_LastTime == 0)
            {
                _LastTime = SystemHelper.GetTimeMs();
            }
            long CurrentTime = SystemHelper.GetTimeMs();
            long TimeElapsed = Math.Max(0, CurrentTime - _LastTime);
            _LastTime = CurrentTime;
            ///Process All UI Input
            KeyMouseEventUpdateFunc();
            EditorGlobalUpdateEvent?.Invoke(Device, TimeElapsed);
            /// Editor Update
            MainUI.GetInstance().Update();

            _CrossEngine._BuildingBlockManager.Update();
            /// Shader Watcher
            ShaderWatcher.Instance.Update();
            CanvasWatcher.GetInstance().Update();
            ScriptWatcher.GetInstance().Update();
            CrossSynchronizationContext.Instance.ExecuteTasks();
        }

        public void OnEndFrame()
        {
            ///Toggle world state
            lock (WorldSetTaskQueue)
            {
                var taskList = WorldSetTaskQueue.ToList();
                foreach (System.Threading.Tasks.Task SetTask in taskList)
                {
                    SetTask.RunSynchronously();
                }
                WorldSetTaskQueue.Clear();
            }
        }

        public void Tick(long TimeElapsed)
        {
            _CrossEngine.Tick();
            ClangenCli.GCObjectKeeper.Update();
        }

        public void FlushRenderingCommand()
        {
            System.Threading.Tasks.Task task = new System.Threading.Tasks.Task(() =>
            {
                _CrossEngine._CrossEngineInterface.FlushRendering();
            });
            WorldSetTaskQueue.Add(task);
        }
        public static async Task<AssetImportResult> ImportAssetAsync(string FileName, string TargetPath)
        {
            var ShaderConfig = EditorConfigManager.GetInstance().GetConfig<ShaderConfig>();
            var Settings = new ShaderImportSettings();
            Settings.UseOnDemandCompilation = ShaderConfig.useOnDemandCompilation;
            Settings.IgnoreCache = ShaderConfig.IgnoreCache;
            Settings.GenAllVariants = ShaderConfig.GenAllVariants;
            Settings.CheckShaderModules = ShaderConfig.CheckShaderModules;
            Settings.GenShaderMaps = ShaderConfig.GenShaderMaps;
            Settings.GenShaderMapsDebugInfo = ShaderConfig.GenShaderMapsDebugInfo;

            if (Settings.UseOnDemandCompilation)
            {
                var Mode = CrossEngine.GetInstance().GetRendererMode();
                switch (Mode)
                {
                    case CrossRendererMode.D3D12:
                        Settings.Version.Format = ShaderCodeFormatE.DXIL;
                        Settings.Version.Major = 6;
                        Settings.Version.Minor = 0;
                        break;
                    case CrossRendererMode.Metal:
                        Settings.Version.Format = ShaderCodeFormatE.MSL_OSX;
                        Settings.Version.Major = 2;
                        Settings.Version.Minor = 2;
                        break;
                    case CrossRendererMode.Vulkan:
                        Settings.Version.Format = ShaderCodeFormatE.SPIR_V;
                        Settings.Version.Major = 1;
                        Settings.Version.Minor = 4;
                        break;
                    case CrossRendererMode.OpenGLES30:
                        Settings.Version.Format = ShaderCodeFormatE.ESSL;
                        Settings.Version.Major = 3;
                        Settings.Version.Minor = 0;
                        break;
                    case CrossRendererMode.WebGL:
                        Settings.Version.Format = ShaderCodeFormatE.ESSL;
                        Settings.Version.Major = 1;
                        Settings.Version.Minor = 0;
                        break;
                    default:
                        Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, "", "", 0, $"Cant not found suitable shader format for render mode {0}");
                        break;
                }
            }

            AssetImporterManager.Instance().SetShaderImportSettings(Settings);
            AssetImporterManager.Instance().SetComputeShaderImportSettings(Settings);

            var task = new ImportAssetTask();
            long HandleTask = HandleManager.GetInstance().AllocateHandle(task);
            AssetImporterManager.Instance().ImportAssetAsync(HandleTask, FileName, TargetPath);
            return await task;
        }
        public void Update()
        {
            GetUIManager().Update();
            WindowManager.GetInstance().Update();
        }

        public void OnEditorUI()
        {
            Device Device = GetDevice();
            if (!Device.IsMinimized())
            {
                GetUIManager().Paint();
            }
            WindowManager.GetInstance().Paint();
            ThumbnailScene.GetInstance().DrawScene();
        }

        private readonly object _UIEventInfoQueueLock = new object();

        public void OnUIManagerRawMouse(Device Sender, MouseButton MouseButton, MouseAction MouseAction, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW)
        {
            UIManager UIManager = UIManagerList.GetInstance().FindUIManager(Sender);

            MouseInfo MouseInfo = new MouseInfo();
            MouseInfo.UIManager = UIManager;
            MouseInfo.Sender = Sender;
            MouseInfo.MouseButton = MouseButton;
            MouseInfo.MouseAction = MouseAction;
            MouseInfo.MouseX = MouseX;
            MouseInfo.MouseY = MouseY;
            MouseInfo.MouseDeltaZ = MouseDeltaZ;
            MouseInfo.MouseDeltaW = MouseDeltaW;

            if (MouseButton == MouseButton.Left && MouseAction == MouseAction.Up)
            {
                Control Root = UIManager.GetRoot();
                bool bNamedOrTextedOnly = true;
                Control Control = Root.GetControlAt(MouseX, MouseY, bNamedOrTextedOnly);
                //if (Control != null)
                //{
                //                TDM.ITDataMaster.Instance.CEReportEvent(new Dictionary<string, string>() {
                //                    { "control_name", Control.GetName() },
                //                    { "control_text", Control.GetText() },
                //                    { "control_position", string.Format("[{0},{1}]", MouseX, MouseY) },
                //                });
                //            }
            }

            lock (_UIEventInfoQueueLock)
            {
                _UIEventInfoQueue.Enqueue(MouseInfo);
            }
        }

        public void OnUIManagerRawKey(Device Sender, KeyAction KeyAction, Key Key)
        {
            UIManager UIManager = UIManagerList.GetInstance().FindUIManager(Sender);

            KeyInfo KeyInfo = new KeyInfo();
            KeyInfo.UIManager = UIManager;
            KeyInfo.Sender = Sender;
            KeyInfo.Key = Key;
            KeyInfo.KeyAction = KeyAction;
            lock (_UIEventInfoQueueLock)
            {
                _UIEventInfoQueue.Enqueue(KeyInfo);
            }
        }

        public void OnUIManagerRawChar(Device Sender, char Char)
        {
            UIManager UIManager = UIManagerList.GetInstance().FindUIManager(Sender);

            CharInfo CharInfo = new CharInfo();
            CharInfo.UIManager = UIManager;
            CharInfo.Sender = Sender;
            CharInfo.Char = Char;
            lock (_UIEventInfoQueueLock)
            {
                _UIEventInfoQueue.Enqueue(CharInfo);
            }
        }

        public void PostTriggerMouseEvent(
            Device Sender,
            MouseButton MouseButton,
            MouseAction MouseAction,
            int MouseX,
            int MouseY,
            int MouseDeltaZ,
            int MouseDeltaW)
        {
            _LastMouseX = MouseX;
            _LastMouseY = MouseY;
        }

    }
}
