using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    enum AnimAdditiveType
    {
        LocalSpaceBase,
        RotationOffsetRootSpace
    }

    [JsonObject(MemberSerialization.OptIn)]
    class Anim_AccumulatePoseNodeData : NodeData
    {
        [JsonProperty("InPoseLinks")]
        public List<string> PoseLinks;
        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks;
        [JsonProperty("AdditiveType")]
        public string AdditiveType = "LocalSpaceBase";
    }

    class Anim_AccumulatePoseNode : Anim_FlowNode
    {
        public Anim_AccumulatePoseNode() : base()
        {
            NodeData = new Anim_AccumulatePoseNodeData();
            AddInSlot("BasePose", SlotType.LocalPoseLink);
            AddInSlot("AdditivePose", SlotType.LocalPoseLink);
            AddInSlot("AdditiveWeight", SlotType.ParamImplLink, SlotSubType.Float);
            AddOutSlot("Result", SlotType.LocalPoseLink);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Additive type")]
        public AnimAdditiveType AdditiveType
        {
            get => (AnimAdditiveType)Enum.Parse(typeof(AnimAdditiveType), ((Anim_AccumulatePoseNodeData)NodeData).AdditiveType);
            set => ((Anim_AccumulatePoseNodeData)NodeData).AdditiveType = value.ToString();
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_AccumulatePoseNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);
            Data.PoseLinks = new List<string>();
            Data.ParamLinks = new List<string>();

            var BaseSlot = FindInSlot("BasePose");
            if (BaseSlot != null)
            {
                if (BaseSlot.GetConnections().Count > 0)
                {
                    Data.PoseLinks.Add(BaseSlot.GetConnections()[0].ID.ToString());

                    var AddtiveSlot = FindInSlot("AdditivePose");
                    if (AddtiveSlot != null)
                    {
                        if (AddtiveSlot.GetConnections().Count > 0)
                        {
                            Data.PoseLinks.Add(AddtiveSlot.GetConnections()[0].ID.ToString());
                        }
                    }
                }
            }

            var WeightSlot = FindInSlot("AdditiveWeight");
            if (WeightSlot != null)
            {
                if (WeightSlot.GetConnections().Count > 0)
                {
                    Data.ParamLinks.Add(WeightSlot.GetConnections()[0].ID.ToString());
                }
                else
                {
                    Data.ParamLinks.Add("");
                }
            }

            return Data;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            string ShownString = AdditiveType == AnimAdditiveType.LocalSpaceBase ? "lcl" : "root";
            GraphicsHelper.DrawString(UIManager, null, ShownString, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }
    }
}