using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_ParamImplNodeData : NodeData
    {
        [JsonProperty("InParams")]
        public List<string> ParamName = new List<string> { "" };
        [JsonProperty("ReturnType")]
        public string ReturnType = "Int";
    }
    public class Anim_ParamImplNode : Anim_FlowNode
    {
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Param name", bReadOnly = true)]
        public string ParamName
        {
            get => ((Anim_ParamImplNodeData)NodeData).ParamName[0];
            set => ((Anim_ParamImplNodeData)NodeData).ParamName[0] = value;
        }
        [PropertyInfo(PropertyType = "Auto", ToolTips = "Param type", bReadOnly = true)]
        public SlotSubType ParamType
        {
            get => (SlotSubType)Enum.Parse(typeof(SlotSubType), ((Anim_ParamImplNodeData)NodeData).ReturnType);
            set => SetParamType(value);
        }

        public Anim_ParamImplNode() : base()
        {
            NodeData = new Anim_ParamImplNodeData();
            AddOutSlot("Result", SlotType.ParamImplLink, ParamType);
        }

        void SetParamType(SlotSubType SubType)
        {
            var OldSlot = FindOutSlot("Result");
            if (OldSlot != null && OldSlot.SlotSubType != SubType)
            {
                ((Anim_ParamImplNodeData)NodeData).ReturnType = SubType.ToString();

                Slot NewSlot = new Slot();
                NewSlot.Name = "Result";
                NewSlot.SlotType = SlotType.ParamImplLink;
                NewSlot.SlotSubType = SubType;
                while (GetOutSlots().Count > 0)
                {
                    RemoveLastOutSlot();
                }
                AddOutSlot(NewSlot);
            }
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = ParamName;
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() * 2 + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, ParamName, Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.TopCenter);
            GraphicsHelper.DrawString(UIManager, null, "<" + ParamType.ToString() + ">", Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.BottomCenter);
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_ParamImplNodeData;
            Data.Name = base.ToData().Name;
            Data.Type = TrimAnimNodeType(this.GetType().Name);

            return Data;
        }
        public override void FromData(NodeData NodeData)
        {
            base.FromData(NodeData);

            var Data = NodeData as Anim_ParamImplNodeData;

            if (Data.ParamName.Count > 1)
            {
                Data.ParamName.RemoveRange(0, Data.ParamName.Count - 1);
            }
            SetParamType(ParamType);
        }

        public override void DoErrorCheck()
        {
            base.DoErrorCheck();

            AnimParameterData ParamToCheck = new AnimParameterData(ParamName, ParamType.ToString());
            NodeGraphModel Model = GetOwner();
            while (Model.GetOwner() != null)
            {
                Model = Model.GetOwner().GetOwner();
            }
            if (!(Model as AnimGraph).CheckAnimParam(ParamToCheck))
            {
                CommonDialogUI.ShowSimpleOKDialog(UIManager.GetActiveUIManager(), "Error", string.Format("Node #{0}: Param {1} <{2}> has been deleted", ID, ParamName, ParamType.ToString()));
                SetError();
            }
            else
            {
                ClearError();
            }
        }
    }
}