using CEngine;
using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace CrossEditor
{

    [JsonObject(MemberSerialization.OptIn)]
    public class Anim_PlaySequenceNodeData : NodeData
    {
        [JsonProperty("CompositePath")]
        public string _SequencePath = "";

        [JsonProperty("SyncParams")]
        public SyncParams _SyncParams = new SyncParams();

        [JsonProperty("Loop")]
        public bool _Loop = true;

        [JsonProperty("InParamLinks")]
        public List<string> ParamLinks = new List<string>();
    }

    class Anim_PlaySequenceNode : Anim_PlayAnimBaseNode
    {
        public Anim_PlaySequenceNode() : base()
        {
            AddOutSlot("Output", SlotType.LocalPoseLink);
            AddInSlot("PlayRate", SlotType.ParamImplLink, SlotSubType.Float);
            NodeData = new Anim_PlaySequenceNodeData();
        }

        public override NodeData ToData()
        {
            var Data = NodeData as Anim_PlaySequenceNodeData;
            Data.Name = base.ToData().Name;
            // Use CompositeNode in runtime.
            Data.Type = TrimAnimNodeType("Anim_PlayCompositeNode");
            Data.ParamLinks = new List<string>();

            var PlayRateSlot = FindInSlot("PlayRate");
            var PlayRateConnections = PlayRateSlot.GetConnections();
            if (PlayRateConnections.Count == 0)
            {
                Data.ParamLinks.Add("");
            }
            else
            {
                Data.ParamLinks.Add(PlayRateConnections[0].ID.ToString());
            }
            return Data;
        }

        public string GetStringContent()
        {
            string Content = AnimUtil.TrimAnimPath(PathHelper.GetFileName(SequencePath));
            return Content.Length == 0 ? "Anim" : Content;
        }

        public override void GetContentSize(ref int ContentWidth, ref int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            Font DefaultFont = GraphicsHelper.DefaultFont;

            string StringContent = GetStringContent();
            ContentWidth = SpanX + DefaultFont.MeasureString_Fast(StringContent) + SpanX;
            ContentHeight = SpanY + DefaultFont.GetCharHeight() + SpanY;
        }

        public override void DrawContent(UIManager UIManager, int ContentX, int ContentY, int ContentWidth, int ContentHeight)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color1 = Color.FromRGBA(100, 100, 100, 200);
            GraphicsHelper.FillRectangle(UIManager, Color1, ContentX, ContentY, ContentWidth, ContentHeight);

            Color Color2 = Color.FromRGBA(255, 255, 255, 255);
            GraphicsHelper.DrawString(UIManager, null, GetStringContent(), Color2, ContentX, ContentY + OffsetY, ContentWidth, ContentHeight, TextAlign.CenterCenter);
        }

        public override void UpdateUsedAnims()
        {
            _UsedAnims.Clear();
            _UsedAnims.Add(SequencePath);
        }

        // Move these two properties into anim_node, level up ui
        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Animation SequencePath", FileTypeDescriptor = "Animation Assets#nda", ObjectClassID1 = ClassIDType.CLASS_AnimSequenceRes)]
        public string SequencePath
        {
            get { return ResourceManager.Instance().ConvertGuidToPath(((Anim_PlaySequenceNodeData)NodeData)._SequencePath); }
            set
            {
                ((Anim_PlaySequenceNodeData)NodeData)._SequencePath = value;
                UpdateUsedAnims();
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "SyncParams")]
        public SyncParams SyncParams
        {
            get { return ((Anim_PlaySequenceNodeData)NodeData)._SyncParams; }
            set { ((Anim_PlaySequenceNodeData)NodeData)._SyncParams = value; }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Loop")]
        public bool Loop
        {
            get { return ((Anim_PlaySequenceNodeData)NodeData)._Loop; }
            set { ((Anim_PlaySequenceNodeData)NodeData)._Loop = value; }
        }
    }
}
