using CEngine;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class AnimNodeDataConverter : JsonConverter<NodeData>
    {
        static private Dictionary<string, System.Type> NodeDataTypeDict = new Dictionary<string, System.Type>();
        private System.Type GetNodeDataType(string NodeType)
        {
            if (!NodeDataTypeDict.ContainsKey(NodeType))
            {
                var AnimNode = NodeRegister.GetInstance().CreateNode(NodeType) as Anim_FlowNode;
                Type NodeDataType = AnimNode.NodeData.GetType();
                NodeDataTypeDict[NodeType] = NodeDataType;
            }
            return NodeDataTypeDict[NodeType];
        }

        public override NodeData ReadJson(JsonReader reader, Type objectType, NodeData existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            JObject jo = JObject.Load(reader);
            string Type = jo["Type"].ToString();
            string AnimNodeTypeName = "Anim_" + Type;

            Type NodeDataType = GetNodeDataType(AnimNodeTypeName);

            NodeData node = Activator.CreateInstance(NodeDataType) as NodeData;
            serializer.Populate(jo.CreateReader(), node);
            return node;
        }
        public override void WriteJson(JsonWriter writer, NodeData value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }

    public class AnimParameterDataConverter : JsonConverter<AnimParameterData>
    {
        public override AnimParameterData ReadJson(JsonReader reader, Type objectType, AnimParameterData existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            JObject jsonObject = JObject.Load(reader);

            string typeName = jsonObject["Type"].ToString();
            typeName = "CrossEditor.AnimParameter_" + typeName;
            Type ParamType = CrossEngine.GetType(typeName);

            AnimParameterData node = Activator.CreateInstance(ParamType) as AnimParameterData;
            serializer.Populate(jsonObject.CreateReader(), node);
            return node;
        }
        public override void WriteJson(JsonWriter writer, AnimParameterData value, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }

    class AnimatorResourceData : NodeGraphData
    {
        public string HashName = "";
        public List<AnimParameterData> Parameters;
        public RootMotionMode RootMotionMode;
    }

    class AnimatorResource : Resource
    {
        public List<AnimParameterData> Parameters => AnimatorResourceData.Parameters;
        public RootMotionMode RootMotionMode => AnimatorResourceData.RootMotionMode;
        public string HashString => AnimatorResourceData.HashName;

        public NodeGraphData NodeGraphData => AnimatorResourceData;

        private AnimatorResourceData AnimatorResourceData = new AnimatorResourceData();
        public AnimatorResource(Clicross.Resource resourcePtr) : base(resourcePtr, ClassIDType.CLASS_AnimatorRes)
        {
            var AnimGraphRes = (ResourcePtr as Clicross.anim.AnimatorRes);
            string Conent = AnimGraphRes.GetContentAsJson();

            JsonSerializerSettings settings = new JsonSerializerSettings();
            settings.Converters.Add(new AnimNodeDataConverter());
            settings.Converters.Add(new AnimParameterDataConverter());
            AnimatorResourceData = JsonConvert.DeserializeObject<AnimatorResourceData>(Conent, settings);
        }

        static public AnimatorResourceData ToAnimatorResourceData(string Name, NodeGraphData NodeGraphData, RootMotionMode RootMotionMode, List<AnimParameterData> Parameters)
        {
            AnimatorResourceData Data = new AnimatorResourceData();
            Data.Nodes = NodeGraphData.Nodes;
            Data.Links = NodeGraphData.Links;
            Data.RootMotionMode = RootMotionMode;
            Data.Parameters = Parameters;
            Data.Name = Name;
            string JsonStr = JsonConvert.SerializeObject(Data, Formatting.Indented);
            var hashString = new UniqueString(JsonStr);
            Data.HashName = hashString.GetHash64().GetHashHigh().ToString() + hashString.GetHash64().GetHashLow().ToString();
            return Data;
        }
    }
}