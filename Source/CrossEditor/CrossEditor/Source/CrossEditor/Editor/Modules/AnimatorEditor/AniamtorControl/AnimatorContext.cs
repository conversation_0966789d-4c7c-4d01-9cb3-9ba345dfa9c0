using Newtonsoft.Json;
using System;

/// <summary>
/// Context and class name contain "Desc" means AnimatorUI's data descriptor
/// data modify should handle by context, then make call back to notify UI.
/// Property with JsonProperty Label change its name could lead to deserialize old animator file fail, 
/// which need update file version and correspondingly create a deserialize method simultaneously
/// </summary>
namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorVersion
    {
        public static readonly int CURRENT_ANIMATOR_FILE_VERSION_ID = 0;
        public static readonly int SUPPORT_MIN_ANIMATOR_FILE_VERSION_ID = 0;

        int _VersionId = CURRENT_ANIMATOR_FILE_VERSION_ID;

        /// <summary>
        /// VersionId Name can not be changed while serializer using it to judge file verison & deserialize method 
        /// </summary>
        [JsonProperty("VersionId")]
        public int VersionId { get { return _VersionId; } set { _VersionId = value; } }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal partial class AnimatorContext
    {
        #region Serialize Into Json

        [JsonProperty("AnimatorVersion")]
        AnimatorVersion _Version = new AnimatorVersion();

        [JsonProperty("AnimatorAttributes")]
        protected AnimatorParams _Params = new AnimatorParams();

        [JsonProperty("BasicLayer")]
        protected AnimatorBasicLayer _BasicLayer = new AnimatorBasicLayer();

        #endregion

        protected IAnimatorLayer CurrentLayer
        {
            get { return _BasicLayer; }
        }

        protected float _Scale = 1.0f;

        public enum ParamOperation
        {
            Add,
            Remove,
            Rename,
            None
        }

        public delegate void OnAnimParamChanged(IAnimatorParam Param, ParamOperation Operate);
        public OnAnimParamChanged onAnimParamChanged = null;

        public enum StateOperation
        {
            AddState,
            RemoveState,
            RenameState,
            AddLink,
            RemoveLink,
            ModifyLink,
            ModifyState,
            None
        }

        public delegate void OnAnimStateChanged(IAnimatorState State, StateOperation Operate, params object[] Args);
        public OnAnimStateChanged onAnimStateChanged = null;

        public AnimatorContext() { }

        #region Parameter Concern

        public void AddParam<T>(T V, string Name) where T : IComparable<T>
        {
            _Params.AddParam(V, Name);

            IAnimatorParam added = _Params.FindPram(Name);
            if (added != null && onAnimParamChanged != null)
            {
                onAnimParamChanged(added, ParamOperation.Add);
            }
        }

        public IAnimatorParam FindPram(string Name)
        {
            return _Params.FindPram(Name);
        }

        public bool RenameParam(string OldName, string NewName)
        {
            IAnimatorParam renamed = _Params.FindPram(OldName);
            if (renamed != null)
            {
                renamed.Name = NewName;
                onAnimParamChanged(renamed, ParamOperation.Rename);
                return true;
            }

            return false;
        }

        public bool RemoveParam(string Name)
        {
            bool re = _Params.RemoveParam(Name);
            if (re && onAnimParamChanged != null)
            {
                IAnimatorParam removed = _Params.FindPram(Name);
                onAnimParamChanged(removed, ParamOperation.Remove);
            }

            return re;
        }

        public IAnimatorParam GetParam(int Index)
        {
            return _Params.GetParam(Index);
        }

        public int ParamCount()
        {
            return _Params.Count();
        }

        #endregion

        #region State Concern

        /// <summary>
        /// Todo: we need remove this method which destroy context Encapsulation
        /// </summary>
        /// <returns></returns>
        public AnimatorBasicLayer GetBasicLayer() { return _BasicLayer; }

        public IAnimatorState AddAnimatorState(string Name, int ScreenX, int ScreenY)
        {
            const int stateWidth = 120;
            const int stateHeight = 40;

            IAnimatorState state = CurrentLayer.AddAnimatorState(Name, ScreenX, ScreenY, stateWidth, stateHeight);
            if (onAnimStateChanged != null && state != null)
            {
                onAnimStateChanged(state, StateOperation.AddState);
            }
            return state;
        }

        public bool RemoveAnimatorState(string Name)
        {
            IAnimatorState removed = CurrentLayer.RemoveAnimatorState(Name);

            if (removed != null)
            {
                onAnimStateChanged(removed, StateOperation.RemoveState);
            }
            return removed != null;
        }

        public int CountAnimtorState()
        {
            return CurrentLayer.CountAnimtorState();
        }

        public bool ChangeStateProperty(string StateName, ChangeStatePropertyDel<AnimatorDescSimpleNode> Action)
        {
            IAnimatorState state = CurrentLayer.FindAnimatorState(StateName);
            if (state == null)
                return false;

            bool refresh = CurrentLayer.ChangeStateProperty(StateName, Action);
            if (onAnimStateChanged != null && refresh)
            {
                onAnimStateChanged(state, StateOperation.ModifyState);
            }
            return true;
        }

        public IAnimatorStateLink AddStatesLink(string StartStateName, string EndStateName)
        {
            IAnimatorStateLink link = CurrentLayer.AddStatesLink(StartStateName, EndStateName);

            if (onAnimStateChanged != null && link != null)
            {
                onAnimStateChanged(link.From, StateOperation.AddLink, link);
            }
            return link;
        }

        public bool RemoveStatesLink(IAnimatorStateLink Link)
        {
            if (Link == null)
                return false;

            IAnimatorState start = Link.From;
            if (start == null)
                return false;

            bool re = start.AnimRemoveLink(Link);

            if (onAnimStateChanged != null)
            {
                onAnimStateChanged(start, StateOperation.RemoveLink, Link);
            }
            return re;
        }

        public bool ChangeStateLinkProperty(IAnimatorStateLink Link, ChangeStateLinkDel<AnimatorStateSimpleLink> Action)
        {
            bool refresh = CurrentLayer.ChangeStateLinkProperty(Link, Action);
            if (onAnimStateChanged != null && refresh)
            {
                onAnimStateChanged(Link.From, StateOperation.ModifyLink);
            }
            return true;
        }

        public bool IsStateNameExist(string Name)
        {
            return CurrentLayer.IsStateNameExist(Name);
        }

        #endregion

        public void OnDeserializeFinished()
        {
            _BasicLayer.OnDeserializeFinished(this);
        }

    }
}
