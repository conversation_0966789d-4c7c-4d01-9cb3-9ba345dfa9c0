using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    internal interface IAnimatorUiControl
    {
        Vector2f UiPosition { get; set; }
        Vector2f UiSize { get; set; }
    }

    enum StateTransitMode
    {
        Zero,
        Match
    }

    internal interface IAnimatorStateLink
    {
        IAnimatorState From { get; }
        IAnimatorState To { get; }

        int StartBlend { get; }
        int EndBlend { get; }
        int DeltaFrame { get; }
        int Id { get; }

        StateTransitMode TransitMode { get; }
        List<IAnimatorParam> GetParamsForLink();

        bool AddNewParamForLink(IAnimatorParam param);

        #region Serialize with json

        void OnDeserializeFinished(AnimatorContext Context, AnimatorStates Layer);

        #endregion
    }

    internal interface IAnimatorState
    {
        string NameProperty { get; }
        string PathProperty { get; }
        int StartNumProperty { get; }
        int EndNumProperty { get; }
        bool LoopProperty { get; }

        // return a random link if exist multi links
        IAnimatorStateLink AnimGetLink(string StateTargetName);
        IEnumerable<IAnimatorStateLink> AnimGetLinks();

        IAnimatorStateLink AnimAddLink(IAnimatorState Target);
        IAnimatorStateLink AnimSuperposeLink(IAnimatorState Target);
        bool AnimRemoveLink(IAnimatorStateLink Link);
        bool AnimRemoveLink(IAnimatorState State);

        bool IsStateSubLayerValid();
        IAnimatorStateSubLayer GetStateSubLayer();

        bool IsSlotLayerValid();
        IAnimatorSlotLayer GetSlotLayer();

        bool IsConnectedNode();
        IAnimatorState GetParent();

        bool IsTargetCandidate();
        bool IsSourceCandidate();

        IAnimatorUiControl GetUiControl();

        void OnDeserializeFinished(AnimatorContext Context, AnimatorStates Layer);
    }

    internal interface IAnimatorStateSubLayer
    {
        IAnimatorState GetSubLayerEntry();
    }

    struct AnimatorSlotAnimDesc
    {
        //string SkletPath;
        //string SlotName;
        //float Weight;

        //IAnimatorState State;
    }

    internal interface IAnimatorSlotLayer
    {
        string OwnerName();
        AnimatorSlotAnimDesc GetSlotDesc(int index);
        IAnimatorState GetSlotResult();
    }

    internal interface IAnimatorEntry
    {
        IAnimatorStateLink Link { get; }

        IAnimatorUiControl GetUiControl();

        IAnimatorState GetParent();

        IAnimatorState GetState();
    }

    internal delegate bool ChangeStateLinkDel<T1>(T1 obj1);
    internal delegate bool ChangeStatePropertyDel<T1>(T1 obj1);

    internal interface IAnimatorLayer
    {
        IEnumerable<IAnimatorState> States { get; }

        IAnimatorState AddAnimatorState(string Name, int ScreenX, int ScreenY, int Width, int Height);
        IAnimatorState RemoveAnimatorState(string Name);
        IAnimatorState FindAnimatorState(string Name);
        int CountAnimtorState();
        bool ChangeStateProperty<T>(string StateName, ChangeStatePropertyDel<T> Action) where T : class;

        IAnimatorStateLink AddStatesLink(string StartStateName, string EndStateName);
        bool RemoveStatesLink(IAnimatorStateLink Link);
        bool ChangeStateLinkProperty<T>(IAnimatorStateLink Link, ChangeStateLinkDel<T> Action) where T : class;

        bool IsStateNameExist(string Name);
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorStateSimpleLink : IAnimatorStateLink, IComparable<AnimatorStateSimpleLink>
    {
        static int IdCountor = 0;

        public AnimatorStateSimpleLink(IAnimatorState from, IAnimatorState to)
        {
            From = from;
            To = to;
            StartBlend = 0;
            EndBlend = 0;
            Id = IdCountor++;
        }

        #region Serialize Into Json

        protected string _SerializeFrom = "";

        protected string _SerializeTo = "";

        [JsonProperty("From")]
        private string SerializeFrom
        {
            get { return From.NameProperty; }
            set { _SerializeFrom = value; }
        }

        [JsonProperty("To")]
        private string SerializeTo
        {
            get { return To.NameProperty; }
            set { _SerializeTo = value; }
        }

        [JsonProperty]
        public int StartBlend { get; set; }

        [JsonProperty]
        public int EndBlend { get; set; }

        [JsonProperty]
        public int DeltaFrame { get; set; }

        [JsonProperty]
        [JsonConverter(typeof(StringEnumConverter))]
        public StateTransitMode TransitMode { get; set; }

        [JsonProperty("Parameters")]
        protected List<IAnimatorParam> _Params = new List<IAnimatorParam>();

        public AnimatorStateSimpleLink() { }

        public void OnDeserializeFinished(AnimatorContext Context, AnimatorStates Layer)
        {
            From = Layer.FindAnimatorState(_SerializeFrom);
            To = Layer.FindAnimatorState(_SerializeTo);
        }

        #endregion

        public int Id { get; set; }
        public IAnimatorState From { get; protected set; }
        public IAnimatorState To { get; protected set; }

        public List<IAnimatorParam> GetParamsForLink() { return _Params; }

        public int CompareTo(AnimatorStateSimpleLink other)
        {
            return Id == other.Id ? 0 : -1;
        }

        public bool AddNewParamForLink(IAnimatorParam param)
        {
            _Params.Add(param);
            return true;
        }

        public bool AddNewParamForLink<T>(T value, string name, StateCompareOperate operate) where T : IComparable<T>
        {
            _Params.Add(new AnimatorParam<T>(value, name, operate));
            return true;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorDescSimpleNode : IAnimatorState, IAnimatorUiControl, IComparable<AnimatorDescSimpleNode>
    {
        internal AnimatorDescSimpleNode()
        {
            PathProperty = "";
        }

        public virtual IAnimatorStateLink AnimGetLink(string stateTargetName)
        {
            foreach (IAnimatorStateLink link in _AnimLinks)
            {
                if (link.To.NameProperty == stateTargetName &&
                    link.From.NameProperty == NameProperty)
                    return link;
            }

            return null;
        }

        public virtual IAnimatorStateLink AnimAddLink(IAnimatorState target)
        {
            bool validAdd = this.IsSourceCandidate() && target.IsTargetCandidate();
            if (!validAdd)
                return null;

            if (AnimGetLink(target.NameProperty) != null)
                return null;

            IAnimatorStateLink link = new AnimatorStateSimpleLink(this, target);
            _AnimLinks.Add(link);

            return link;
        }

        public virtual IAnimatorStateLink AnimSuperposeLink(IAnimatorState target)
        {
            bool validAdd = this.IsSourceCandidate() && target.IsTargetCandidate();
            if (!validAdd)
                return null;

            IAnimatorStateLink link = AnimGetLink(target.NameProperty);
            if (link == null)
                return null;

            link = new AnimatorStateSimpleLink(this, target);
            _AnimLinks.Add(link);

            return link;
        }

        public bool AnimRemoveLink(IAnimatorStateLink link)
        {
            foreach (IAnimatorStateLink cursor in _AnimLinks)
            {
                if (cursor == link)
                {
                    _AnimLinks.Remove(cursor);
                    return true;
                }
            }

            return false;
        }

        public bool AnimRemoveLink(IAnimatorState state)
        {
            bool re = false;
            for (int i = _AnimLinks.Count - 1; i > -1; --i)
            {
                if (((AnimatorDescSimpleNode)_AnimLinks[i].To).CompareTo((AnimatorDescSimpleNode)state) == 0)
                {
                    _AnimLinks.RemoveAt(i);
                    re = true;
                }
            }

            return re;
        }

        public IEnumerable<IAnimatorStateLink> AnimGetLinks()
        {
            return _AnimLinks;
        }

        public virtual bool IsStateSubLayerValid()
        {
            return false;
        }

        public virtual IAnimatorStateSubLayer GetStateSubLayer()
        {
            return null;
        }

        public virtual bool IsSlotLayerValid()
        {
            return false;
        }

        public virtual IAnimatorSlotLayer GetSlotLayer()
        {
            return null;
        }

        public virtual bool IsConnectedNode()
        {
            return false;
        }

        public virtual IAnimatorState GetParent()
        {
            return null;
        }

        public IAnimatorUiControl GetUiControl() { return this; }

        public virtual bool IsTargetCandidate() { return true; }

        public virtual bool IsSourceCandidate() { return true; }

        #region Serialize Into Json

        // Animator Data
        [JsonProperty("Name")]
        public virtual string NameProperty { get; set; }

        [JsonProperty("Path")]
        public string PathProperty { get; set; }

        [JsonProperty("StartNum")]
        public int StartNumProperty { get; set; }

        [JsonProperty("EndNum")]
        public int EndNumProperty { get; set; }

        [JsonProperty("Loop")]
        public bool LoopProperty { get; set; }

        [JsonProperty("Links")]
        protected List<IAnimatorStateLink> _AnimLinks = new List<IAnimatorStateLink>();

        // Ui Concerns
        protected Vector2f _UiPosition = new Vector2f(0, 0);
        protected Vector2f _UiSize = new Vector2f(120, 40);

        [JsonProperty]
        public Vector2f UiPosition
        {
            get { return _UiPosition; }

            set { _UiPosition = value; }
        }

        [JsonProperty]
        public Vector2f UiSize
        {
            get { return _UiSize; }
            set { _UiSize = value; }
        }

        public void OnDeserializeFinished(AnimatorContext Context, AnimatorStates Layer)
        {
            foreach (IAnimatorStateLink link in _AnimLinks)
            {
                link.OnDeserializeFinished(Context, Layer);
            }
        }

        public int CompareTo(AnimatorDescSimpleNode other)
        {
            return NameProperty == other.NameProperty ? 0 : -1;
        }

        #endregion
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorDescBasicLayerEntry : AnimatorDescSimpleNode, IAnimatorEntry
    {
        public IAnimatorStateLink Link
        {
            get
            {
                if (_AnimLinks.Count == 0)
                    return null;

                return _AnimLinks[0];
            }
        }

        public override string NameProperty
        {
            get { return "Entry"; }
            set { }
        }

        public IAnimatorState GetState()
        {
            return this;
        }

        public override IAnimatorStateLink AnimAddLink(IAnimatorState target)
        {
            bool validAdd = this.IsSourceCandidate() && target.IsTargetCandidate();
            if (!validAdd)
                return null;

            _AnimLinks.Clear();

            IAnimatorStateLink link = new AnimatorStateSimpleLink(this, target);
            _AnimLinks.Add(link);

            return link;
        }

        public override IAnimatorStateLink AnimGetLink(string stateTargetName)
        {
            return null;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorDescAnyNode : AnimatorDescSimpleNode
    {
        public override bool IsTargetCandidate() { return false; }

        public override bool IsSourceCandidate() { return false; }

        public override string NameProperty
        {
            get { return "Any"; }
            set { }
        }
    }

    internal class AnimatorDescSubLayerNode : AnimatorDescSimpleNode, IAnimatorStateSubLayer
    {
        public override bool IsStateSubLayerValid()
        {
            return true;
        }

        public override IAnimatorStateSubLayer GetStateSubLayer()
        {
            return this;
        }

        public IAnimatorState GetSubLayerEntry()
        {
            throw new NotImplementedException();
        }
    }

    internal class AnimatorDescSubLayerResultNode : AnimatorDescSimpleNode
    {
        IAnimatorState _Parent;

        public AnimatorDescSubLayerResultNode(IAnimatorState Parent) { _Parent = Parent; }

        public override bool IsConnectedNode()
        {
            return true;
        }

        public override IAnimatorState GetParent()
        {
            return _Parent;
        }
    }

    internal class AnimatorDescSlotNode : AnimatorDescSimpleNode
    {

    }

    internal class AnimatorDescSlotEntryNode : AnimatorDescSimpleNode
    {

    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorStates : IAnimatorLayer
    {
        [JsonProperty("States")]
        protected List<IAnimatorState> _States = new List<IAnimatorState>();

        public IEnumerable<IAnimatorState> States { get { return _States; } }

        public IAnimatorState AddAnimatorState(string Name, int ScreenX, int ScreenY, int Width, int Height)
        {
            IAnimatorState state = FindAnimatorState(Name);
            if (state != null)
                return state;

            AnimatorDescSimpleNode simpleState = new AnimatorDescSimpleNode();
            simpleState.NameProperty = Name;
            simpleState.UiPosition = new Vector2f(ScreenX, ScreenY);
            simpleState.UiSize = new Vector2f(Width, Height);

            _States.Add(simpleState);
            return simpleState;
        }

        public virtual IAnimatorState FindAnimatorState(string Name)
        {
            return _States.Find(item => item.NameProperty == Name);
        }

        public virtual IAnimatorState RemoveAnimatorState(string Name)
        {
            IAnimatorState state = FindAnimatorState(Name);
            if (state == null)
                return null;

            _States.Remove(state);
            foreach (IAnimatorState cursorState in _States)
                cursorState.AnimRemoveLink(state);

            return state;
        }

        public int CountAnimtorState()
        {
            return _States.Count;
        }

        public bool ChangeStateProperty<T>(string StateName, ChangeStatePropertyDel<T> Action) where T : class
        {
            IAnimatorState state = FindAnimatorState(StateName);
            if (state == null)
                return false;

            if ((state as T) == null)
                return false;

            return Action(state as T);
        }

        public IAnimatorStateLink AddStatesLink(string StartStateName, string EndStateName)
        {
            IAnimatorState start = FindAnimatorState(StartStateName);
            IAnimatorState end = FindAnimatorState(EndStateName);

            if (start == null || end == null)
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, "Animator add new link failed");

            IAnimatorStateLink link = null;
            if (start.AnimGetLink(end.NameProperty) == null)
                link = start.AnimAddLink(end);
            else
                link = start.AnimSuperposeLink(end);

            if (link == null)
                return null;

            return link;
        }

        public bool RemoveStatesLink(IAnimatorStateLink Link)
        {
            if (Link == null)
                return false;

            IAnimatorState start = Link.From;
            if (start == null)
                return false;

            return start.AnimRemoveLink(Link);
        }

        public bool ChangeStateLinkProperty<T>(IAnimatorStateLink Link, ChangeStateLinkDel<T> Action) where T : class
        {
            IAnimatorState state = FindAnimatorState(Link.From.NameProperty);
            if (state == null)
                return false;

            foreach (IAnimatorStateLink cursor in state.AnimGetLinks())
            {
                if (cursor == Link)
                {
                    if ((cursor as T) == null)
                        return false;

                    return Action(cursor as T);
                }
            }

            return false;
        }

        public bool IsStateNameExist(string Name)
        {
            foreach (IAnimatorState state in _States)
            {
                if (state.NameProperty == Name)
                    return true;
            }

            return false;
        }
    }

    [JsonObject(MemberSerialization.OptIn)]
    internal class AnimatorBasicLayer : AnimatorStates
    {
        #region Serialize With Json

        [JsonProperty("Entry")]
        private AnimatorDescBasicLayerEntry _Entry { get; set; }

        [JsonProperty("Any")]
        private AnimatorDescAnyNode _AnyState { get; set; }

        #endregion

        public IAnimatorEntry GetEntryState() { return _Entry; }

        public IAnimatorState GetAnyState() { return _AnyState; }

        public AnimatorBasicLayer()
        {
            _Entry = new AnimatorDescBasicLayerEntry();
            _Entry.UiPosition = new Vector2f(100.0f, 100.0f);

            _AnyState = new AnimatorDescAnyNode();
            _AnyState.UiPosition = new Vector2f(300.0f, 100.0f);
        }

        public override IAnimatorState FindAnimatorState(string Name)
        {
            if (Name == _Entry.NameProperty)
                return _Entry;

            if (Name == _AnyState.NameProperty)
                return _AnyState;

            return base.FindAnimatorState(Name);
        }

        public void OnDeserializeFinished(AnimatorContext Context)
        {
            foreach (IAnimatorState State in _States)
                State.OnDeserializeFinished(Context, this);

            _Entry.OnDeserializeFinished(Context, this);
            _AnyState.OnDeserializeFinished(Context, this);
        }
    }
}
