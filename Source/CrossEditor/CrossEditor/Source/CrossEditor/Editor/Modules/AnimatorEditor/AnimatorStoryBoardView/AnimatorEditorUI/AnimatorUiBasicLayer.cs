using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    internal interface IAnimatorUiLayerDesc
    {
        string Name();

        IEnumerable<AnimatorStatePanel> Panels();

        AnimatorStateLink TemporaryLink { get; set; }
        AnimatorStateLink SelectedLink { get; set; }
        AnimatorStatePanel TemporaryTargetPanel { get; set; }

        int StatesLinkedNumber(AnimatorStatePanel src, AnimatorStatePanel target);
        int StatesLinkedIndex(AnimatorStateLink link);

        bool IsMakingTranslate();
    }

    internal interface IAnimatorUiLayerCtrl
    {
        bool Initialize(AnimatorContext Context);

        void OnPanelRightMouseUp(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue);
        void OnPanelMouseMove(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue);
        void OnPanelDoubleClick(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue);
        void OnPanelSingleClick(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue);
        void OnPanelStatePropertyChanged(AnimatorContext Context, IAnimatorState state, AnimatorContext.StateOperation op, params object[] args);
        void OnLayerPaint(AnimatorContext Context, Control Sender);
        void OnUpdateLayout(AnimatorContext Context);
    }

    internal class AnimatorLayerMenuItem : MenuItem
    {
        public AnimatorContext Context { get; set; }
    }

    internal class AnimatorUiBasicLayer : IAnimatorUiLayerDesc, IAnimatorUiLayerCtrl
    {
        private AnimatorStateLink _TemporaryLink = null;

        private AnimatorStatePanel _TemporaryLinkTarget = null;

        private List<AnimatorStatePanel> _Panels = new List<AnimatorStatePanel>();

        private Panel _Container;

        private AnimatorStateEntry _Entry = null;

        private AnimatorStateAny _Any = null;

        public AnimatorStateLink TemporaryLink { get { return _TemporaryLink; } set { _TemporaryLink = value; } }

        public AnimatorStateLink SelectedLink { get; set; }

        public AnimatorStatePanel TemporaryTargetPanel { get { return _TemporaryLinkTarget; } set { _TemporaryLinkTarget = value; } }

        public string Name() { return "Basic Layer"; }

        protected delegate void OnStatePanelRemoved(AnimatorContext Context, AnimatorStatePanel Panel);
        protected OnStatePanelRemoved onStatePanelRemoved = null;

        protected delegate void OnStatePanelLinkRemoved(AnimatorContext Context, IAnimatorStateLink linkDesc);
        protected OnStatePanelLinkRemoved onStatePanelLinkRemoved = null;

        public AnimatorUiBasicLayer(Panel Container)
        {
            _Container = Container;
        }

        public UIManager GetUIManager()
        {
            if (_Container != null)
            {
                return _Container.GetUIManager();
            }
            else
            {
                return UIManager.GetMainUIManager();
            }
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public bool Initialize(AnimatorContext Context)
        {
            _Entry = new AnimatorStateEntry(Context);
            _Entry.onStatePanelMakeTranslate += OnAnimStatePanelMakeTranslate;
            _Entry.onStatePanelSelectChange += OnAnimStatePanelSelectChange;
            onStatePanelRemoved += _Entry.OnStatePanelRemovedBroadCast;
            onStatePanelLinkRemoved += _Entry.OnStatePanelLinkRemovedBroadCast;

            _Any = new AnimatorStateAny(Context);
            _Any.onStatePanelMakeTranslate += OnAnimStatePanelMakeTranslate;
            _Any.onStatePanelSelectChange += OnAnimStatePanelSelectChange;
            onStatePanelRemoved += _Any.OnStatePanelRemovedBroadCast;
            onStatePanelLinkRemoved += _Any.OnStatePanelLinkRemovedBroadCast;

            _Panels.Clear();
            foreach (IAnimatorState state in Context.GetBasicLayer().States)
            {
                AddPanel(state, Context, _Container, (int)state.GetUiControl().UiPosition.X, (int)state.GetUiControl().UiPosition.Y);
            }

            foreach (IAnimatorState state in Context.GetBasicLayer().States)
            {
                foreach (IAnimatorStateLink link in state.AnimGetLinks())
                {
                    _TemporaryLink = new AnimatorStateLink(_Panels.Find(x => { return x.CompareTo(link.From) == 0 ? true : false; }));
                    _TemporaryLinkTarget = _Panels.Find(x => { return x.CompareTo(link.To) == 0 ? true : false; });

                    AddLink(link, Context, _Container);
                }
            }

            foreach (IAnimatorStateLink link in Context.GetBasicLayer().GetEntryState().GetState().AnimGetLinks())
            {
                _TemporaryLink = new AnimatorStateLink(_Entry);
                _TemporaryLinkTarget = _Panels.Find(x => { return x.CompareTo(link.To) == 0 ? true : false; });

                AddLink(link, Context, _Container);
            }

            foreach (IAnimatorStateLink link in Context.GetBasicLayer().GetAnyState().AnimGetLinks())
            {
                _TemporaryLink = new AnimatorStateLink(_Any);
                _TemporaryLinkTarget = _Panels.Find(x => { return x.CompareTo(link.To) == 0 ? true : false; });

                AddLink(link, Context, _Container);
            }

            _TemporaryLink = null;
            _TemporaryLinkTarget = null;

            return true;
        }

        protected bool AddLink(IAnimatorStateLink Link, AnimatorContext Context, Control Container)
        {
            if (_TemporaryLinkTarget == null)
                ConsoleUI.GetInstance().AddLogItem(LogMessageType.Error, "Animator complete link failed");

            _TemporaryLink.Link(_TemporaryLinkTarget, Link);

            _TemporaryLink = null;
            _TemporaryLinkTarget = null;

            return true;
        }

        protected bool AddPanel(IAnimatorState State, AnimatorContext Context, Control Container)
        {
            Device device = GetDevice();
            int cursorX = device.GetMouseX();
            int cursorY = device.GetMouseY();

            int localCursorX = 0, localCursorY = 0;
            AnimatorUI.ConvertToLocalCoordinate(Container, cursorX, cursorY, ref localCursorX, ref localCursorY);

            return AddPanel(State, Context, Container, localCursorX, localCursorY);
        }

        protected bool AddPanel(IAnimatorState State, AnimatorContext Context, Control Container, int LocalX, int LocalY)
        {
            AnimatorStatePanel panel = new AnimatorStatePanel(State, Context);
            panel.SetPosition(LocalX, LocalY, (int)State.GetUiControl().UiSize.X, (int)State.GetUiControl().UiSize.Y);
            panel.SetBackgroundColor(Color.EDITOR_UI_CONTROL_BACK_COLOR);
            panel.onStatePanelMakeTranslate += OnAnimStatePanelMakeTranslate;
            panel.onStatePanelSelectChange += OnAnimStatePanelSelectChange;
            onStatePanelRemoved += panel.OnStatePanelRemovedBroadCast;
            onStatePanelLinkRemoved += panel.OnStatePanelLinkRemovedBroadCast;

            _Panels.Add(panel);
            return true;
        }

        protected bool RemoveLink(IAnimatorStateLink Link, AnimatorContext Context)
        {
            if (onStatePanelLinkRemoved != null)
                onStatePanelLinkRemoved(Context, Link);

            // Close current inspector for refresh later
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(null);
            InspectorUI.InspectObject();

            return true;
        }

        protected bool RemovePanel(IAnimatorState State, AnimatorContext Context)
        {
            // 
            AnimatorStatePanel removed = null;
            foreach (AnimatorStatePanel panel in _Panels)
            {
                if (panel.CompareTo(State) == 0)
                {
                    removed = panel;
                    onStatePanelRemoved -= removed.OnStatePanelRemovedBroadCast;
                    onStatePanelLinkRemoved -= removed.OnStatePanelLinkRemovedBroadCast;

                    _Panels.Remove(removed);
                    break;
                }
            }

            if (onStatePanelRemoved != null)
                onStatePanelRemoved(Context, removed);

            // Close current inspector for refresh later
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(null);
            InspectorUI.InspectObject();

            return true;
        }

        public IEnumerable<AnimatorStatePanel> Panels() { return _Panels; }

        public bool IsMakingTranslate() { return _TemporaryLink != null; }

        protected bool IsTranslateReady(AnimatorStatePanel Panel)
        {
            return IsMakingTranslate() &&
                _TemporaryLinkTarget == Panel &&
                _TemporaryLink.Source.IsLinkValid(_TemporaryLinkTarget);
        }

        public int StatesLinkedNumber(AnimatorStatePanel Source, AnimatorStatePanel Target)
        {
            int sum = 0;

            foreach (AnimatorStatePanel panel in _Panels)
            {
                foreach (AnimatorStateLink link in panel.Links)
                {
                    if (link.Source == Source &&
                        link.Target == Target)
                    {
                        sum++;
                        continue;
                    }

                    if (link.Source == Target &&
                        link.Target == Source)
                    {
                        sum++;
                        continue;
                    }
                }
            }

            return sum;
        }

        public int StatesLinkedIndex(AnimatorStateLink Link)
        {
            int validLinks = 0;

            bool continue_loop = true;
            foreach (AnimatorStatePanel panel in _Panels)
            {
                foreach (AnimatorStateLink cursor in panel.Links)
                {
                    if (Link == cursor)
                    {
                        continue_loop = false;
                        break;
                    }

                    if (Link.Source == cursor.Source &&
                        Link.Target == cursor.Target)
                        validLinks++;
                    else if (Link.Source == cursor.Target &&
                        Link.Target == cursor.Source)
                        validLinks++;
                }

                if (!continue_loop)
                    break;
            }

            return validLinks;
        }

        protected virtual void OnAnimStatePanelMakeTranslate(AnimatorContext Context, AnimatorStatePanel Panel)
        {
            if (IsMakingTranslate())
                return;

            _TemporaryLink = new AnimatorStateLink(Panel);
        }

        protected virtual void OnAnimStatePanelSelectChange(AnimatorContext Context, AnimatorStatePanel Panel, bool Selected)
        {
            foreach (AnimatorStatePanel panel in _Panels)
            {
                foreach (AnimatorStateLink link in panel.Links)
                {
                    link.UnSelect();
                }
            }

            if (IsTranslateReady(Panel) && Selected == false)
            {
                Context.AddStatesLink(_TemporaryLink.Source.Name(), _TemporaryLinkTarget.Name());
                return;
            }
        }

        #region IAnimatorUiLayerCtrl

        void OnMainAnimPanelRightClick_Create(MenuItem MenuItem)
        {
            Device device = GetDevice();
            int cursorX = device.GetMouseX();
            int cursorY = device.GetMouseY();

            AnimatorContext context = (MenuItem as AnimatorLayerMenuItem).Context;

            int localCursorX = 0, localCursorY = 0;
            AnimatorUI.ConvertToLocalCoordinate(_Container, cursorX, cursorY, ref localCursorX, ref localCursorY);

            int stateIndex = 0;
            string stateCandidate = "New State" + stateIndex;
            do
            {
                stateCandidate = "New State" + stateIndex;
                stateIndex++;
            }
            while (context.GetBasicLayer().FindAnimatorState(stateCandidate) != null);

            context.AddAnimatorState(stateCandidate, localCursorX, localCursorY);
        }

        public void OnPanelRightMouseUp(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsMakingTranslate())
            {
                _TemporaryLink = null;
                _TemporaryLinkTarget = null;
                return;
            }

            Menu menu = new Menu(GetUIManager());
            menu.Initialize();

            // Build Menu Item
            AnimatorLayerMenuItem MenuItem_Create = new AnimatorLayerMenuItem();
            MenuItem_Create.SetText("Create States");
            MenuItem_Create.ClickedEvent += OnMainAnimPanelRightClick_Create;
            MenuItem_Create.Context = Context;

            menu.AddMenuItem(MenuItem_Create);

            GetUIManager().GetContextMenu().ShowMenu(menu, MouseX, MouseY);
        }

        public void OnPanelMouseMove(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (!IsMakingTranslate())
                return;

            foreach (AnimatorStatePanel panel in _Panels)
            {
                if (panel.IsInSide(MouseX, MouseY) && !_TemporaryLink.IsStartState(panel))
                {
                    _TemporaryLinkTarget = panel;
                    return;
                }
            }

            _TemporaryLinkTarget = null;
        }

        public void OnPanelSingleClick(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsMakingTranslate())
                return;

            SelectedLink = null;

            foreach (AnimatorStatePanel panel in _Panels)
            {
                foreach (AnimatorStateLink link in panel.Links)
                {
                    if (SelectedLink != null)
                    {
                        link.UnSelect();
                        continue;
                    }

                    if (link.TrySelect(MouseX, MouseY))
                    {
                        SelectedLink = link;

                        InspectorUI InspectorUI = InspectorUI.GetInstance();
                        InspectorUI.SetObjectInspected(new AnimatorStateLink.InspectorParameter(SelectedLink, Context));
                        InspectorUI.InspectObject();
                        continue;
                    }
                }
            }
        }

        public void OnPanelDoubleClick(AnimatorContext Context, Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {

        }

        public void OnPanelStatePropertyChanged(AnimatorContext Context, IAnimatorState State, AnimatorContext.StateOperation op, params object[] args)
        {
            switch (op)
            {
                case AnimatorContext.StateOperation.AddState:
                    {
                        AddPanel(State, Context, _Container);
                        break;
                    }
                case AnimatorContext.StateOperation.RemoveState:
                    {
                        RemovePanel(State, Context);
                        break;
                    }
                case AnimatorContext.StateOperation.AddLink:
                    {
                        AddLink(args[0] as IAnimatorStateLink, Context, _Container);
                        break;
                    }
                case AnimatorContext.StateOperation.RemoveLink:
                    {
                        RemoveLink(args[0] as IAnimatorStateLink, Context);
                        break;
                    }
                case AnimatorContext.StateOperation.ModifyLink:
                    {
                        if (SelectedLink != null)
                        {
                            InspectorUI InspectorUI = InspectorUI.GetInstance();
                            InspectorUI.SetObjectInspected(new AnimatorStateLink.InspectorParameter(SelectedLink, Context));
                            InspectorUI.InspectObject();
                        }

                        break;
                    }
                case AnimatorContext.StateOperation.RenameState:
                    {

                        break;
                    }
                default:
                    {
                        break;
                    }
            }
        }

        public void OnLayerPaint(AnimatorContext Context, Control Sender)
        {
            if (_TemporaryLink != null)
                _TemporaryLink.OnAnimStatePanelPaint(this);

            foreach (AnimatorStatePanel panel in _Panels)
            {
                foreach (AnimatorStateLink link in panel.Links)
                {
                    link.OnAnimStatePanelPaint(this);
                }
            }

            // 
            foreach (AnimatorStateLink link in _Entry.Links)
            {
                link.OnAnimStatePanelPaint(this);
            }

            foreach (AnimatorStateLink link in _Any.Links)
            {
                link.OnAnimStatePanelPaint(this);
            }
        }

        public void OnUpdateLayout(AnimatorContext Context)
        {
            foreach (AnimatorStatePanel panel in Panels())
                _Container.AddChild(panel);

            _Container.AddChild(_Entry);
            _Container.AddChild(_Any);
        }

        #endregion
    }
}
