using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    internal class AnimatorStatePanel : Panel, IComparable<AnimatorStatePanel>, IComparable<IAnimatorState>
    {

        #region Inspector Parameter

        internal class InspectorParameter
        {
            internal IAnimatorState Data;
            internal AnimatorContext Context;

            internal InspectorParameter(IAnimatorState InData, AnimatorContext InContext)
            {
                Data = InData;
                Context = InContext;
            }
        }

        #endregion

        const int PROPERTY_FONT_SIZE = 14;

        protected IAnimatorState _Data;
        protected AnimatorContext _Context;

        protected bool _IsSelected = false;
        protected long _SelectRightDownTimeStamp = -1;

        protected Vector2i _StartPanelInLocal = new Vector2i(0, 0);
        protected Vector2i _StartCursorInWorld = new Vector2i(0, 0);

        protected Color _SelectColor = Color.EDITOR_UI_MENU_HILIGHT_COLOR;
        protected Color _UnSelectColor = Color.EDITOR_UI_MENU_BACK_COLOR;

        protected Label _LabelName;

        protected List<AnimatorStateLink> _Links = new List<AnimatorStateLink>();
        public IEnumerable<AnimatorStateLink> Links { get { return _Links; } }

        public delegate void OnStatePanelMakeTranslate(AnimatorContext Context, AnimatorStatePanel panel);
        public OnStatePanelMakeTranslate onStatePanelMakeTranslate = null;

        public delegate void OnStatePanelSelectChange(AnimatorContext Context, AnimatorStatePanel panel, bool Selected);
        public OnStatePanelSelectChange onStatePanelSelectChange = null;

        public AnimatorStatePanel(IAnimatorState Data, AnimatorContext Context)
        {
            this._Data = Data;
            this._Context = Context;

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText("HhHhHhH");
            _LabelName.SetFontSize(PROPERTY_FONT_SIZE);
            _LabelName.SetTextColor(Color.White);
            _LabelName.SetTextAlign(TextAlign.CenterCenter);
            _LabelName.SetPosition(0, 0, this.GetWidth(), this.GetHeight());
            AddChild(_LabelName);
        }

        public override void OnUpdate(int TimeElapsed)
        {
            base.OnUpdate(TimeElapsed);

            Device device = GetUIManager().GetDevice();
            int cursorX = device.GetMouseX();
            int cursorY = device.GetMouseY();

            if (_IsSelected && IsInPanel(cursorX, cursorY))
            {
                this.SetPos(_StartPanelInLocal.X + cursorX - _StartCursorInWorld.X, _StartPanelInLocal.Y + cursorY - _StartCursorInWorld.Y);
            }

            _LabelName.SetText(_Data.NameProperty);
            _LabelName.SetPosition(0, 0, this.GetWidth(), this.GetHeight());
        }

        public string Name()
        {
            return _Data.NameProperty;
        }

        public bool IsLinkIncluded(AnimatorStatePanel Target)
        {
            return _Data.AnimGetLink(Target._Data.NameProperty) != null;
        }

        public bool IsLinkValid(AnimatorStatePanel Target) { return Target._Data.IsTargetCandidate() && _Data.IsSourceCandidate(); }

        public override void OnLeftMouseDown(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnLeftMouseDown(MouseX, MouseY, ref bContinue);
            bContinue = false;
            Select(true);
        }

        public override void OnLeftMouseUp(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnLeftMouseUp(MouseX, MouseY, ref bContinue);
            bContinue = false;
            Select(false);
        }

        public override void OnRightMouseDown(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnRightMouseDown(MouseX, MouseY, ref bContinue);
            _SelectRightDownTimeStamp = DateTime.Now.Millisecond;
        }

        public override void OnRightMouseUp(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnRightMouseUp(MouseX, MouseY, ref bContinue);

            long SelectUpTimeStamp = DateTime.Now.Millisecond;
            if ((SelectUpTimeStamp - _SelectRightDownTimeStamp) < 150)
            {
                Menu menu = new Menu(GetUIManager());
                menu.Initialize();

                MenuItem MenuItem_Trans = new MenuItem();
                MenuItem_Trans.SetText("Make Translate");
                MenuItem_Trans.ClickedEvent += OnMenuItem_MakeTranslate;

                MenuItem MenuItem_Remove = new MenuItem();
                MenuItem_Remove.SetText("Delete");
                MenuItem_Remove.ClickedEvent += OnMenuItem_Delete;

                menu.AddMenuItem(MenuItem_Trans);
                menu.AddMenuItem(MenuItem_Remove);
                GetUIManager().GetContextMenu().ShowMenu(menu, MouseX, MouseY);
            }

            bContinue = false;
            _SelectRightDownTimeStamp = -100;
        }

        public override void OnMouseMove(int MouseX, int MouseY, ref bool bContinue)
        {
            base.OnMouseMove(MouseX, MouseY, ref bContinue);

            if (!IsInPanel(MouseX, MouseY))
                Select(false);
        }

        public bool IsInSide(int MouseX, int MouseY)
        {
            int LocalX = 0, LocalY = 0;
            AnimatorUI.ConvertToLocalCoordinate(this, MouseX, MouseY, ref LocalX, ref LocalY);

            return LocalX < this.GetWidth() && LocalX > 0 &&
                LocalY < this.GetHeight() && LocalY > 0;
        }

        public bool IsInPanel(int MouseX, int MouseY)
        {
            int localCursorX = 0, localCursorY = 0;
            return IsInPanel(MouseX, MouseY, ref localCursorX, ref localCursorY);
        }

        public bool IsInPanel(int MouseX, int MouseY, ref int LocalX, ref int LocalY)
        {
            AnimatorUI.ConvertToLocalCoordinate(this.GetParent(), MouseX, MouseY, ref LocalX, ref LocalY);

            return LocalX < this.GetParent().GetWidth() && LocalX > 0 &&
                LocalY < this.GetParent().GetHeight() && LocalY > 0;
        }

        protected virtual void Select(bool flag)
        {
            if (_IsSelected == flag)
                return;

            if (flag)
            {
                this.SetBackgroundColor(_SelectColor);

                Device device = GetDevice();
                int cursorX = device.GetMouseX();
                int cursorY = device.GetMouseY();
                _StartCursorInWorld.X = cursorX;
                _StartCursorInWorld.Y = cursorY;

                _StartPanelInLocal.X = this.GetX();
                _StartPanelInLocal.Y = this.GetY();
            }
            else
            {
                this.SetBackgroundColor(_UnSelectColor);
                _StartPanelInLocal = new Vector2i(0, 0);
                _StartCursorInWorld = new Vector2i(0, 0);
            }

            _IsSelected = flag;

            // Refresh Inspector UI
            if (_IsSelected)
            {
                InspectorUI InspectorUI = InspectorUI.GetInstance();
                InspectorUI.SetObjectInspected(new InspectorParameter(_Data, _Context));
                InspectorUI.InspectObject();
            }

            // Refresh Animator UI
            if (onStatePanelSelectChange != null)
                onStatePanelSelectChange(_Context, this, _IsSelected);

            // Record Animator State Ui info
            _Data.GetUiControl().UiPosition = new Vector2f(GetX() * 1.0f, GetY() * 1.0f);
            _Data.GetUiControl().UiSize = new Vector2f(GetWidth() * 1.0f, GetHeight() * 1.0f);
        }

        void OnMenuItem_MakeTranslate(MenuItem MenuItem)
        {
            if (onStatePanelMakeTranslate != null)
                onStatePanelMakeTranslate(_Context, this);
        }

        void OnMenuItem_Delete(MenuItem MenuItem)
        {
            _Context.RemoveAnimatorState(_Data.NameProperty);
        }

        public virtual void OnStatePanelBeSourceCreateLink(AnimatorStatePanel Target, AnimatorStateLink UiLink)
        {
            _Links.Add(UiLink);
        }

        public virtual void OnStatePanelBeTargetCreateLink(AnimatorStatePanel Source, AnimatorStateLink UiLink)
        {

        }

        public virtual void OnStatePanelLinkRemovedBroadCast(AnimatorContext Context, IAnimatorStateLink LinkDesc)
        {
            foreach (AnimatorStateLink panelLink in _Links)
            {
                if (panelLink.CompareTo(LinkDesc) == 0)
                {
                    _Links.Remove(panelLink);
                    break;
                }
            }
        }

        public virtual void OnStatePanelRemovedBroadCast(AnimatorContext Context, AnimatorStatePanel Removed)
        {
            for (int i = _Links.Count - 1; i > -1; --i)
            {
                if (_Links[i].Target == Removed)
                    _Links.RemoveAt(i);
            }
        }

        public int CompareTo(IAnimatorState other)
        {
            if (other == this._Data)
                return 0;

            return -1;
        }

        public int CompareTo(AnimatorStatePanel other)
        {
            if (other._Data == this._Data)
                return 0;

            return -1;
        }
    }
}
