namespace CrossEditor
{
    public class MoveEvnetArgs { }
    public delegate void MoveHandler(object Sender, MoveEvnetArgs Args);

    public interface IMovable
    {
        event MoveHandler MoveEvent;
        void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper GraphicsHelper);
        void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper GraphicsHelper);
        void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper GraphicsHelper);
        void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper GraphicsHelper);
    }
}
