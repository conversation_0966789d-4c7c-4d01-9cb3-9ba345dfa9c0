using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_AddNewCurve : EditOperation
    {
        CurveManager NewCurve;
        List<CurveManager> Curves;
        List<CurveManager> SelectedCurves;

        public EditOperation_AddNewCurve(CurveManager NewCurve, List<CurveManager> Curves, List<CurveManager> SelectedCurves)
        {
            this.NewCurve = NewCurve;
            this.Curves = Curves;
            this.SelectedCurves = SelectedCurves;
        }

        public override void Redo()
        {
            Curves.Add(NewCurve);
            CurveEditorUI.GetInstance().UpdateListView();
        }

        public override void Undo()
        {
            Curves.Remove(NewCurve);
            SelectedCurves.Remove(NewCurve);
            CurveEditorUI.GetInstance().UpdateListView();
        }
    }
}
