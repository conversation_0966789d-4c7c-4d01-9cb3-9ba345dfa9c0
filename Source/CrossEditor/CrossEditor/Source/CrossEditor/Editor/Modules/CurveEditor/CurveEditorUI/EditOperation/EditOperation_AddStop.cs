using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_AddStop : EditOperation
    {
        MoveStop CurMoveStop;
        int MouseX = 0;
        int MouseY = 0;
        public EditOperation_AddStop(MoveStop MoveStop, int MouseX, int MouseY)
        {
            this.CurMoveStop = MoveStop;
            this.MouseX = MouseX;
            this.MouseY = MouseY;
        }

        public override void Redo()
        {
            StopType StopType = CurMoveStop._Type;
            if (StopType == StopType.ColorStop)
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._ColorStops.Add(CurMoveStop);
            }
            else
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._AlphaStops.Add(CurMoveStop);
            }
            List<Point> Points = LinearColorCurveEditorUI.GetInstance().AddKeyInGradient(MouseX, MouseY, StopType);
            CurMoveStop.SetPoints(Points);
        }

        public override void Undo()
        {
            StopType StopType = CurMoveStop._Type;
            if (StopType == StopType.ColorStop)
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._ColorStops.Remove(CurMoveStop);
            }
            else
            {
                LinearColorCurveEditorUI.GetInstance().GradientColor._AlphaStops.Remove(CurMoveStop);
            }
            LinearColorCurveEditorUI.GetInstance().DeleteKeyInGradientAlpha(CurMoveStop._ContainPoints);
        }
    }
}
