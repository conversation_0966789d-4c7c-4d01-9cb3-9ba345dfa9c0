using EditorUI;

namespace CrossEditor
{
    public class EditOperation_ChangeCurveColor : EditOperation
    {
        CurveManager ModifiedCurve;
        Color OldColor;
        Color NewColor;

        public EditOperation_ChangeCurveColor(CurveManager ModifiedCurve, Color OldColor, Color NewColor)
        {
            this.ModifiedCurve = ModifiedCurve;
            this.OldColor = OldColor;
            this.NewColor = NewColor;
        }

        public override void Redo()
        {
            ModifiedCurve.UnselectedColor = NewColor;
            CurveEditorUI.GetInstance().UpdateListView();
        }

        public override void Undo()
        {
            ModifiedCurve.UnselectedColor = OldColor;
            CurveEditorUI.GetInstance().UpdateListView();
        }
    }
}
