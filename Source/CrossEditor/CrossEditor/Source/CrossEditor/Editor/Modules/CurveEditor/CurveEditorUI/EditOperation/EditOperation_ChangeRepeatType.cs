using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_ChangeRepeatType : EditOperation
    {
        bool bLeaveType;
        List<CurveManager> ModifiedCurveList;
        List<CurveRepeatType> OldTypeList;
        List<CurveRepeatType> NewTypeList;

        public EditOperation_ChangeRepeatType(List<CurveManager> ModifiedCurveList, List<CurveRepeatType> OldTypeList, List<CurveRepeatType> NewTypeList, bool bLeaveType)
        {
            this.ModifiedCurveList = ModifiedCurveList;
            this.OldTypeList = OldTypeList;
            this.NewTypeList = NewTypeList;
            this.bLeaveType = bLeaveType;
        }

        public override void Redo()
        {
            if (bLeaveType)
            {
                for (int i = 0; i < ModifiedCurveList.Count; ++i)
                {
                    ModifiedCurveList[i].LeaveRepeatType = NewTypeList[i];
                }
            }
            else
            {
                for (int i = 0; i < ModifiedCurveList.Count; ++i)
                {
                    ModifiedCurveList[i].EnterRepeatType = NewTypeList[i];
                }
            }

            CurveEditorUI.GetInstance().SetModified(true, ModifiedCurveList);
        }

        public override void Undo()
        {
            if (bLeaveType)
            {
                for (int i = 0; i < ModifiedCurveList.Count; ++i)
                {
                    ModifiedCurveList[i].LeaveRepeatType = OldTypeList[i];
                }
            }
            else
            {
                for (int i = 0; i < ModifiedCurveList.Count; ++i)
                {
                    ModifiedCurveList[i].EnterRepeatType = OldTypeList[i];
                }
            }

            CurveEditorUI.GetInstance().SetModified(true, ModifiedCurveList);
        }
    }
}
