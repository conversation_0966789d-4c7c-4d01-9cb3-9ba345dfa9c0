using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class EditOperation_MovePoints : EditOperation
    {
        List<Point> ModifiedPoints;
        List<Vector2m> OldLocs;
        List<Vector2m> NewLocs;

        public EditOperation_MovePoints(List<Point> ModifiedPoints, List<Vector2m> OldLocs, List<Vector2m> NewLocs)
        {
            this.ModifiedPoints = ModifiedPoints;
            this.OldLocs = OldLocs;
            this.NewLocs = NewLocs;
        }

        public override void Redo()
        {
            for (int i = 0; i < ModifiedPoints.Count(); ++i)
            {
                ModifiedPoints[i].ValueX = (float)NewLocs[i].X;
                ModifiedPoints[i].ValueY = NewLocs[i].Y;

                ModifiedPoints[i].OwnerCurve.Points.Sort();
            }

            CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPoints));
        }

        public override void Undo()
        {
            for (int i = 0; i < ModifiedPoints.Count(); ++i)
            {
                ModifiedPoints[i].ValueX = (float)OldLocs[i].X;
                ModifiedPoints[i].ValueY = OldLocs[i].Y;

                ModifiedPoints[i].OwnerCurve.Points.Sort();
            }

            CurveEditorUI.GetInstance().SetModified(true, CurveEditorUI.CollectOwnerCurves(ModifiedPoints));
        }
    }
}
