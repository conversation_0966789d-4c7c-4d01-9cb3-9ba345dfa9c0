using Clicross;
using EditorUI;

namespace CrossEditor
{
    class InputActionMappingEditorUI : DockingUI
    {
        VContainer mMainContainer = new VContainer();
        Button mSaveBtn = OperationBarUI.CreateTextButton("Save");
        InputActionMappingContentMapView MappingView = new InputActionMappingContentMapView();
        InputActionMappingContext mContext;

        public InputActionMappingEditorUI(string FilePath)
        {
            mContext = new InputActionMappingContext(FilePath);
        }

        public void Initialize(string fileName)
        {
            mMainContainer.Initialize();
            mMainContainer.SetSize(1, 1);

            // OperationBar
            {
                OperationBarUI operationBar = new OperationBarUI();
                operationBar.Initialize();

                mSaveBtn.ClickedEvent += sender =>
                {
                    mContext.Save();
                };
                operationBar.AddLeft(mSaveBtn);

                mMainContainer.AddFixedChild(operationBar.GetPanelBar());
            }

            // Mapping
            VContainer MappingContainer = new VContainer();
            {
                MappingContainer.Initialize();
                MappingContainer.SetSize(1, 1);

                MappingView.OnAddToParent(MappingContainer);
                MappingView.BindProperty(mContext.mInputMap, mContext.mContextKey, "Mapping");
            }

            mMainContainer.AddSizableChild(MappingContainer, 1);

            base.Initialize(fileName, mMainContainer);

        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            mContext.Dispose();
        }
    }
}