using EditorUI;
using System.Collections.Generic;
using System.Reflection;

namespace CrossEditor
{
    class Inspector_PreviewBone : Inspector
    {
        protected PreviewBone _MsaPreviewBone;

        protected Button _ButtonBar;
        protected Label _LabelName;

        protected const int SpanX = 5;

        public Inspector_PreviewBone()
        {
        }

        public override void InspectObject(object Object, object Tag = null)
        {
            _MsaPreviewBone = (PreviewBone)Object;

            _ButtonBar = new Button();
            _ButtonBar.Initialize();
            _ButtonBar.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetHoverColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.SetDownColor(Color.EDITOR_UI_COLOR_KEY);
            _ButtonBar.ClickedEvent += OnButtonBarClicked;
            _SelfContainer.AddChild(_ButtonBar);

            InitializeCheckExpand();
            _ButtonBar.AddChild(_CheckExpand);

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText(_MsaPreviewBone.BoneName + "(Belongs To " + _MsaPreviewBone.GetOwnerRefSkeleton().GetOwnerAsset().GetName() + ")");
            _LabelName.SetFontSize(16);
            _LabelName.SetTextAlign(TextAlign.CenterLeft);
            _ButtonBar.AddChild(_LabelName);

            RefreshChildInspectors();
        }

        public override void BindPropertyFunction(ref ObjectProperty ObjectProperty)
        {
            ObjectProperty.GetPropertyValueFunction = GetPropertyValueFunction;
            ObjectProperty.SetPropertyValueFunction = SetPropertyValueFunction;
        }

        protected virtual void RefreshChildInspectors()
        {
            ClearChildInspectors();
            List<PropertyInfo> Properties = MSAPreviewContext.GetInstance().GetPreviewBoneProperties(_MsaPreviewBone);

            foreach (PropertyInfo PropertyInfo in Properties)
            {
                AddPropertyInspector(PropertyInfo, _MsaPreviewBone);
            }
        }

        public object GetPropertyValueFunction(object Object, string PropertyName, ValueExtraProperty ValueExtraProperty)
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            PropertyInfo PropertyInfo = PreviewContext.GetPreviewBoneProperty(_MsaPreviewBone, PropertyName);

            if (PropertyInfo != null)
            {
                return PropertyInfo.GetValue(Object);
            }

            return null;
        }

        public void SetPropertyValueFunction(object Object, string PropertyName, object PropertyValue, SubProperty SubProperty)
        {
            MSAPreviewContext PreviewContext = MSAPreviewContext.GetInstance();
            if (PreviewContext.SetPreviewBoneProperty(_MsaPreviewBone, PropertyName, PropertyValue))
            {
                // need refresh inspectors after setting PropertyInfo
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    OnButtonRefreshMenuClicked(null);
                });
            }
        }

        public override void UpdateLayout(int Width, ref int Y)
        {
            int Height = 0;
            _ButtonBar.SetPosition(0, 0, Width, 24);
            Height += 24;
            _SelfContainer.SetPosition(0, Y, Width, Height);
            Y += Height;

            _CheckExpand.SetPosition(SPAN_X, 2, 20, 20);
            int LabelNameX = _CheckExpand.GetEndX() + SpanX;
            int LabelNameWidth = Width - SPAN_X - LabelNameX;
            _LabelName.SetPosition(LabelNameX, 4, LabelNameWidth, 20);

            base.UpdateLayout(Width, ref Y);
        }

        public Inspector_Property FindChildInspector(string PropertyName)
        {
            foreach (Inspector Inspector in _ChildInspectors)
            {
                Inspector_Property Inspector_Property = Inspector as Inspector_Property;
                if (Inspector_Property != null)
                {
                    string CurPropertyName = Inspector_Property.GetPropertyName();
                    if (CurPropertyName == PropertyName)
                    {
                        return Inspector_Property;
                    }
                }
            }

            return null;
        }

        public void RefreshBoneTransform()
        {
            var RotProperty = FindChildInspector("Rotation");
            RotProperty.ReadValue();

            var TransProperty = FindChildInspector("Translate");
            TransProperty.ReadValue();

            var ScaleProperty = FindChildInspector("Scale");
            ScaleProperty.ReadValue();
        }

        public void RefreshBoneRetargetingMode()
        {
            var RetargetingModeProperty = FindChildInspector(PreviewBone.RetargetingModeStr);
            RetargetingModeProperty.ReadValue();
        }

        public void RefreshBoneMirrorBoneName()
        {
            var MirrorBoneProperty = FindChildInspector(PreviewBone.MirrorBoneNameStr);
            MirrorBoneProperty.ReadValue();
        }

        protected void OnButtonBarClicked(Button Sender)
        {
            bool bChecked = _CheckExpand.GetChecked();
            SetCheckExpand(!bChecked);
        }

        protected virtual void OnButtonRefreshMenuClicked(Button Sender)
        {
            GetInspectorHandler().UpdateLayout();
        }
    }
}
