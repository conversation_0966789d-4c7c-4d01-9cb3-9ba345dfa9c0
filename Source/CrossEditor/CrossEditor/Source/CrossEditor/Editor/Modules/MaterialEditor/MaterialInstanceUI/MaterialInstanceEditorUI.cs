using CEngine;
using Clicross;
using EditorUI;
using System.IO;

namespace CrossEditor
{
    public class MaterialInstanceEditorUI : DockingUI
    {
        MaterialInstanceEditor mMaterialInstanceEditor;
        MaterialPreview mPreview;
        MaterialInstanceDetailView mDetailView;

        string mFileGuid;

        Panel mPreviewPanel = new Panel();

        public MaterialInstanceEditorUI(string FileGuid)
        {
            mFileGuid = FileGuid;

            mMaterialInstanceEditor = new MaterialInstanceEditor(FileGuid);
            mPreview = new MaterialPreview(mMaterialInstanceEditor);
            mDetailView = new MaterialInstanceDetailView(mMaterialInstanceEditor);
        }

        public void Initialize()
        {
            VContainer mainContainer = new VContainer();
            mainContainer.Initialize();
            mainContainer.SetSize(1, 1);

            // OperationBar
            {
                OperationBarUI operationBar = new OperationBarUI();
                operationBar.Initialize();

                mainContainer.AddFixedChild(operationBar.GetPanelBar());
            }

            // Left
            mPreviewPanel.Initialize();
            mPreviewPanel.SetEnable(true);
            mPreviewPanel.SetTagString1(mFileGuid);

            // Right
            var rightContainer = new VContainer();
            mDetailView.OnAddToParent(rightContainer);

            // Splitter
            var splitter = new HSplitter();
            splitter.Initialize();
            splitter.AddChild(mPreviewPanel);
            splitter.AddChild(rightContainer);
            mainContainer.AddSizableChild(splitter, 1.0f);

            var fileName = Path.GetFileName(ResourceManager.Instance().ConvertGuidToPath(mFileGuid));
            base.Initialize(fileName, mainContainer);

            mPreviewPanel.LeftMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, true);
            };
            mPreviewPanel.LeftMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.LeftButton, false);
            };
            mPreviewPanel.RightMouseDownEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.CaptureMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, true);
            };
            mPreviewPanel.RightMouseUpEvent += (Control sender, int x, int y, ref bool _) =>
            {
                sender.ReleaseMouse();
                mPreview.OnKeyEvent((EditorKey)EditorKey.RightButton, false);
            };
            mPreviewPanel.MiddleMouseDownEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, true);
            mPreviewPanel.MiddleMouseUpEvent += (Control sender, int x, int y, ref bool _) => mPreview.OnKeyEvent((EditorKey)EditorKey.MiddleButton, false);
            mPreviewPanel.MouseMoveEvent += (Control Sender, int MouseX, int MouseY, ref bool bContinue) => mPreview.OnMouseMoveEvent(MouseX - mPreviewPanel.GetScreenX(), MouseY - mPreviewPanel.GetScreenY());
            mPreviewPanel.MouseWheelEvent += (Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue) => mPreview.OnMouseWheelEvent(MouseDeltaZ);

            mPreviewPanel.PositionChangedEvent += (_, posChanged, sizeChanged) =>
            {
                if (sizeChanged)
                {
                    mPreview.OnResize(new Clicross.Float2(mPreviewPanel.GetWidth(), mPreviewPanel.GetHeight()));
                }
            };
            mPreviewPanel.PaintEvent += sender =>
            {
                var texture = mPreview.GetTexture();
                var width = Clicross.EditorUICanvasInterface.Instance().GetImageWidth(texture);
                var height = Clicross.EditorUICanvasInterface.Instance().GetImageHeight(texture);
                var color = new Color(1, 1, 1, 1);
                (GetDevice().GetEditorUICanvas() as EditorUICanvas).DrawImage(texture, mPreviewPanel.GetScreenX(), mPreviewPanel.GetScreenY(), width, height, ref color);
            };

            GetDockingCard().SetTagString1(mFileGuid);

            DragDropManager DragDropManager = DragDropManager.GetInstance();
            DragDropManager.DragEndEvent += OnDragDropManagerDragEnd;
        }

        public new void Update(long TimeElapsed)
        {
            base.Update(TimeElapsed);
            if (GetDockingCard().GetActive())
            {
                mPreview.SetWorldEnable(true);
            }
            else
            {
                mPreview.SetWorldEnable(false);
            }
            mPreview.Tick();
            mDetailView.Tick();

            mMaterialInstanceEditor.OnFrame();
        }

        public string GetGuid()
        {
            return mFileGuid;
        }

        public void OnResourceRenamed()
        {
            var fileName = Path.GetFileName(ResourceManager.Instance().ConvertGuidToPath(mFileGuid));
            GetDockingCard().SetText(fileName);
        }

        public bool IsParentChanged()
        {
            return mMaterialInstanceEditor.IsParentChanged();
        }

        public void SetParentModifiedStatus(bool value)
        {
            mMaterialInstanceEditor.SetParentModifiedStatus(value);
        }

        public bool IsDependent(string guid)
        {
            return mMaterialInstanceEditor.IsDependent(guid);
        }

        public void OnParentChange(string guid)
        {
            if (IsDependent(guid))
            {
                mMaterialInstanceEditor.OnParentChange();
                mDetailView.InspectObject();
            }
        }

        public void OnMaterialChange(string guid)
        {
            if (mMaterialInstanceEditor.OnMaterialChange(guid))
            {
                mDetailView.InspectObject();
            }
        }

        public override void OnClose(DockingCard Sender, ref bool bNotToClose)
        {
            base.OnClose(Sender, ref bNotToClose);
            mPreview.Dispose();
        }

        protected override void OnEnter()
        {
            base.OnEnter();
            //mPreview.OnActivate(true);
        }

        protected override void OnLeave()
        {
            base.OnLeave();
            //mPreview.OnActivate(false);
        }

        protected void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager() || !GetDockingCard().GetActive())
            {
                return;
            }

            mDetailView.OnDragDropManagerDragEnd(Sender, UIManager, MouseX, MouseY, ref bContinue);
        }
    }
}
