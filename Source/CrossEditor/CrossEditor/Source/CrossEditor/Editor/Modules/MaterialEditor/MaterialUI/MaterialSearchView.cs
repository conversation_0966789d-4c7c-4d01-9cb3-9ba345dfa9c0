using Clicross;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Numerics;
using System.Reflection;

namespace CrossEditor
{
    public class MaterialSearchView : VContainer
    {
        OperationBarUI mOperationBar;
        SearchUI mSearchUI = new SearchUI();
        Tree mNodeTree = new Tree();

        MaterialEditor mMaterialEditorContext;

        public MaterialSearchView(MaterialEditor mContext)
        {
            mMaterialEditorContext = mContext;

            mSearchUI.Initialize();
            mSearchUI.SearchEvent += OnSearchUISearch;


            mNodeTree.Initialize();
            mNodeTree.SetTextAlign(TextAlign.TopLeft);
            mNodeTree.GetRootItem().SetExpanded(true);
            mNodeTree.SetPosition(2, 2, 299, 50);

            TreeItem treeRoot = mNodeTree.GetRootItem();
            treeRoot.ClearChildren();

            mNodeTree.ItemSelectedEvent += (Sender, TreeItem) =>
            {
                if (TreeItem != mNodeTree.GetRootItem())
                {
                    string selectItemInfo = TreeItem.GetTagString();
                    if (TreeItem.GetText() != "" && selectItemInfo != "")
                    {
                        int expressionIndex = int.Parse(selectItemInfo);
                        if (expressionIndex < mContext.GetExpressionsCount())
                        {
                            mContext.ZoomToExpression(mContext.GetExpression(expressionIndex));
                        }
                    }
                }
            };
        }

        public override void Initialize()
        {
            base.Initialize();

            mOperationBar = new OperationBarUI();
            mOperationBar.Initialize();

            Panel PanelBack = mSearchUI.GetPanelBack();
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);

            mOperationBar.AddRight(PanelBack);
            AddFixedChild(PanelBack);
            AddSizableChild(mNodeTree, 1.0f);
        }
        
        private void OnSearchUISearch(SearchUI Sender, string Pattern)
        {
            TreeItem treeRoot = mNodeTree.GetRootItem();
            treeRoot.ClearChildren();

            int expCnt = mMaterialEditorContext.GetExpressionsCount();

            if (expCnt == 0 || Pattern == "")
            {
                TreeItem treeItem = mNodeTree.CreateItem();

                treeItem.SetText("No result is found...");
                treeItem.SetFolder(true);
                treeItem.SetExpanded(true);
                treeRoot.AddChild(treeItem);
                return;
            }

            bool is_find = false;
            for (int index = 0; index < expCnt; index++)
            {
                var exp = mMaterialEditorContext.GetExpression(index);
                string expName = exp.GetCxxTypeName();
                string prefix = "cross::MaterialExpression";
                if (expName.StartsWith(prefix))
                {
                    expName = expName.Substring(prefix.Length);
                }
                TreeItem treeItem = mNodeTree.CreateItem();
                treeItem.SetTagString(index.ToString());

                treeItem.SetFolder(true);
                treeItem.SetExpanded(true);
                treeItem.SetText(expName);

                bool is_visible = false;
                if (expName.ToLower().Contains(Pattern.ToLower()))
                    is_visible = true;

                int inputCnt = exp.GetInputPinCount();
                for (int i = 0; i < inputCnt; i++)
                {
                    var input = exp.GetInputPin(i);
                    string nodeText = input.m_Name;
                    if (input.m_BindedPropertyName != "")
                    {
                        nodeText = input.m_Name + "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t" + input.GetBindedPropertyValue();
                    }
                    if (!nodeText.ToLower().Contains(Pattern.ToLower()))
                        continue;

                    is_visible = true;
                    TreeItem leafTreeItem = mNodeTree.CreateItem();
                    treeItem.AddChild(leafTreeItem);
                    leafTreeItem.SetFolder(true);
                    leafTreeItem.SetExpanded(true);


                    leafTreeItem.SetText(nodeText.ToString());
                    leafTreeItem.SetTagString(index.ToString());
                }

                int outputCnt = exp.GetOutputPinCount();
                for (int i = 0; i < outputCnt; i++)
                {
                    var output = exp.GetOutputPin(i);
                    string nodeText = output.m_Name;
                    if (output.m_BindedPropertyName != "")
                    {
                        nodeText = output.m_Name + "\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t" + output.GetBindedPropertyValue();
                    }
                    if (!nodeText.ToLower().Contains(Pattern.ToLower()))
                        continue;

                    is_visible = true;
                    TreeItem leafTreeItem = mNodeTree.CreateItem();
                    treeItem.AddChild(leafTreeItem);
                    leafTreeItem.SetFolder(true);
                    leafTreeItem.SetExpanded(true);


                    leafTreeItem.SetText(nodeText.ToString());
                    leafTreeItem.SetTagString(index.ToString());
                }

                if (is_visible)
                {
                    treeRoot.AddChild(treeItem);
                    is_find = true;
                }
            }
            if (!is_find)
            {
                TreeItem treeItem = mNodeTree.CreateItem();

                treeItem.SetText("No result is found...");
                treeItem.SetFolder(true);
                treeItem.SetExpanded(true);
                treeRoot.AddChild(treeItem);
            }
        }
    }
}