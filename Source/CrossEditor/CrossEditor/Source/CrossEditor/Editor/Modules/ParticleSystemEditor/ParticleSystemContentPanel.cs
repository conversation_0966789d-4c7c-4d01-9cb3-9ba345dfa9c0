using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace CrossEditor
{
    public enum ChangedEvent
    {
        Add,
        Delete,
        Rename
    }

    public delegate void ResourceChangedDelegate(Resource Res, ChangedEvent Event, uint EmitterIndex = 0);

    public sealed class ParticleSystemContentPanel : Panel
    {
        private ParticleEmitterModel Model;

        private GraphCamera2D Camera
        {
            get => Model?.Camera;
        }

        public event ResourceChangedDelegate OnResourceChanged;

        // For Dragging
        private bool bRightMouseDown;

        private bool bMouseMoved;
        private float SavedWorldX;
        private float SavedWorldY;
        private int RightMouseDownX;
        private int RightMouseDownY;

        // For Moving
        private bool bLeftMouseDown;

        private int LeftMouseDownX;
        private int LeftMouseDownY;
        private int SavedNodeX;
        private int SavedNodeY;
        public static int MoveStep = 10;

        // For Select Node(s)
        //bool bMultipleSelect;
        private List<ParticleEmitterNode> LastSelectedNodes;

        private List<ParticleEmitterNode> SelectedNodes;

        // For Hover
        private bool bCanUpdateHover;

        private object HoverObject;

        // For Copy/Paste
        private SerializeContext _SerializeContext = new SerializeContext();

        private SerializeNode _TmpSerNode = null;
        private static Type[] _SerializeParameterType = { typeof(SerializeContext) };
        private static Type[] _DeserializeParameterType = { typeof(SerializeNode), typeof(SerializeContext) };

        private bool bEmitterDirty = false;

        private Dictionary<string, string> OldSimulationCodes = new Dictionary<string, string>();

        public ParticleSystemContentPanel()
        {
            Model = new ParticleEmitterModel();

            LastSelectedNodes = new List<ParticleEmitterNode>();
            SelectedNodes = new List<ParticleEmitterNode>();

            this.PaintEvent += OnPanelPaint;
            this.PositionChangedEvent += OnPanelPositionChanged;
            this.LeftMouseDownEvent += OnPanelLeftMouseDown;
            this.LeftMouseUpEvent += OnPanelLeftMouseUp;
            this.RightMouseDownEvent += OnPanelRightMouseDown;
            this.RightMouseUpEvent += OnPanelRightMouseUp;
            this.MouseMoveEvent += OnPanelMouseMove;
            this.MouseWheelEvent += OnPanelMouseWheel;

            InspectorManager.GetInstance().PropertyValueChangedEvent += OnPropertyValueChanged;
        }

        public void ReloadEmitters(ParticleSystemResource SystemResource)
        {
            OnPanelPositionChanged(null, false, false);
            ClearStatus();
            Model.ReloadEmitters(SystemResource);
        }

        public ParticleEmitterNode GetSelectedNode()
        {
            return HoverObject as ParticleEmitterNode;
        }

        public void DoDeleteEmitter(ParticleEmitterNode EmitterNode)
        {
            if (EmitterNode != null)
            {
                var emitterInfos = Model.SystemResource.SystemInfo.EmitterInfos;
                for (int i = 0; i < emitterInfos.Count; ++i)
                {
                    if (ReferenceEquals(Model.Nodes[i], EmitterNode))
                    {
                        emitterInfos.RemoveAt(i);
                        Model.SystemResource.SystemInfo.EmitterResourceSlots.RemoveAt(i);
                        break;
                    }
                }

                HoverObject = null;
                SelectedNodes.Remove(EmitterNode);
                Inspect();
                OnResourceChanged(null, ChangedEvent.Delete, Convert.ToUInt32(EmitterNode.EmitterIndex));
                ReloadEmitters(Model.SystemResource);
                bEmitterDirty = true;
            }
        }

        public void DoAddEmitter()
        {
            var Res = Resource.Get("EngineResource/ParticleSystem/DefaultParticleEmitter.emitter") as ParticleEmitterResource;
            if (Res != null)
            {
                ParticleEmitterResourceSlot slot = new ParticleEmitterResourceSlot();
                Model.SystemResource.SystemInfo.EmitterResourceSlots.Add(slot);
                Model.SystemResource.SystemInfo.EmitterInfos.Add(Res.EmitterInfo);
                ReloadEmitters(Model.SystemResource);
                CEResource.UpdateParticleSystem(Model.SystemResource.ResourcePtr.GetPointer(), Model.SystemResource.SystemInfo);

                Inspect();
                OnResourceChanged(null, ChangedEvent.Add);

                Model.SystemResource.Save();
                Model.SystemResource.Reload();
                ParticleSystemUI.GetInstance().SetResourcePath(Model.SystemResource.GetAssetPath());
            }
        }

        public void DoSaveEmitter()
        {
            if (Model.SystemResource != null)
            {
                int EmitterCount = Model.SystemResource.SystemInfo.EmitterInfos.Count;
                Debug.Assert(Model.SystemResource.SystemInfo.EmitterInfos.Count == EmitterCount);
                for (int i = 0; i < EmitterCount; ++i)
                {
                    Model.SystemResource.SystemInfo.EmitterInfos[i] = Model.Nodes[i].EmitterInfo;
                }
                CEResource.UpdateParticleSystem(Model.SystemResource.ResourcePtr.GetPointer(), Model.SystemResource.SystemInfo);
                Model.SystemResource.Save();
                Model.SystemResource.Reload();

                if (bEmitterDirty)
                {
                    ParticleSystemUI.GetInstance().SetResourcePath(Model.SystemResource.GetAssetPath());
                    bEmitterDirty = false;
                }

                ParticleSystemUI.GetInstance().SetTileState(false);
                ParticleSystemUI.GetInstance().NotifyRefreshParticleSystem();
            }
        }

        public void DoExportEmitter(ParticleEmitterNode SelectedNode)
        {
            if (SelectedNode != null)
            {
                string DirectoryPath = Path.GetDirectoryName(Model.SystemResource.Path);
                string ExportPath = Path.Combine(DirectoryPath, SelectedNode.EmitterInfo.EmitterName + ".emitter");
                ParticleEmitterResource EmitterRes = ParticleEmitterResource.CreateResource(ExportPath, false);
                if (EmitterRes != null)
                {
                    CEResource.UpdateParticleEmitter(EmitterRes.ResourcePtr.GetPointer(), SelectedNode.EmitterInfo);
                    if (File.Exists(ExportPath))
                    {
                        string ExportTime = DateTime.Now.ToString("_yyyyMMdd_HHmmss");
                        ExportPath = Path.Combine(DirectoryPath, SelectedNode.EmitterInfo.EmitterName + ExportTime + ".emitter");
                    }
                    EmitterRes.SaveTo(ExportPath);
                    EmitterRes.Reload();
                    ProjectUI.GetInstance().UpdateAll();
                }
            }
        }

        public void DoCopyEmitter(ParticleEmitterNode SelectedNode)
        {
            if (SelectedNode == null)
            {
                return;
            }

            if (_TmpSerNode != null)
            {
                _TmpSerNode.Dispose();
            }

            if (SelectedNode.InspectIndex != -1)
            {
                PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
                object Module = Properties[SelectedNode.InspectIndex].GetValue(SelectedNode.EmitterInfo);
                _TmpSerNode = DoSerializeModuleItem(Module, "Serialize", _SerializeParameterType, new object[] { _SerializeContext });
            }
            else
            {
                _TmpSerNode = SelectedNode.EmitterInfo.Serialize(_SerializeContext);
            }
        }

        public void DoPasteEmitter(ParticleEmitterNode SelectedNode)
        {
            if (_TmpSerNode != null && SelectedNode != null)
            {
                if (SelectedNode.InspectIndex != -1)
                {
                    PropertyInfo[] Properties = typeof(ParticleEmitterInfo).GetProperties();
                    object Module = Properties[SelectedNode.InspectIndex].GetValue(SelectedNode.EmitterInfo);
                    DoSerializeModuleItem(Module, "Deserialize", _DeserializeParameterType, new object[] { _TmpSerNode, _SerializeContext });
                }
                else
                {
                    SelectedNode.EmitterInfo.Deserialize(_TmpSerNode, _SerializeContext);
                }

                int EmitterCount = Model.SystemResource.SystemInfo.EmitterInfos.Count;
                Debug.Assert(Model.SystemResource.SystemInfo.EmitterInfos.Count == EmitterCount);
                for (int i = 0; i < EmitterCount; ++i)
                {
                    Model.SystemResource.SystemInfo.EmitterInfos[i] = Model.Nodes[i].EmitterInfo;
                    Model.Nodes[i].Initialize();
                }
                ParticleSystemUI.GetInstance().SetTileState(true);
                ParticleSystemUI.GetInstance().NotifyRefreshParticleSystem();
            }
        }

        private object InvokeMethod(object InfoObj, string MethodName, Type[] ParameterType, Object[] Parameters)
        {
            MethodInfo Method = InfoObj.GetType().GetMethod(MethodName, ParameterType);
            if (Method == null)
            {
                return null;
            }
            Object Result = Method.Invoke(InfoObj, Parameters);
            return Method.ReturnType == typeof(void) ? null : Result;
        }

        private SerializeNode DoSerializeModuleItem(object Module, string MethodName, Type[] ParameterType, Object[] Parameters)
        {
            Object Result = null;

            switch (Module)
            {
                case EmitterStateInfo EmitterStateInfo:
                    Result = InvokeMethod(EmitterStateInfo, MethodName, ParameterType, Parameters);
                    break;

                case ParticleSpawnInfo ParticleSpawnInfo:
                    Result = InvokeMethod(ParticleSpawnInfo, MethodName, ParameterType, Parameters);
                    break;

                case LocationShapeInfo LocationShapeInfo:
                    Result = InvokeMethod(LocationShapeInfo, MethodName, ParameterType, Parameters);
                    break;

                case ParticleInitInfo ParticleInitInfo:
                    Result = InvokeMethod(ParticleInitInfo, MethodName, ParameterType, Parameters);
                    break;

                case SizeScaleInfo SizeScaleInfo:
                    Result = InvokeMethod(SizeScaleInfo, MethodName, ParameterType, Parameters);
                    break;

                case ColorScaleInfo ColorScaleInfo:
                    Result = InvokeMethod(ColorScaleInfo, MethodName, ParameterType, Parameters);
                    break;

                case SpriteRotationRateInfo SpriteRotationRateInfo:
                    Result = InvokeMethod(SpriteRotationRateInfo, MethodName, ParameterType, Parameters);
                    break;

                case VelocityInfo VelocityInfo:
                    Result = InvokeMethod(VelocityInfo, MethodName, ParameterType, Parameters);
                    break;

                case VectorNoiseInfo VectorNoiseInfo:
                    Result = InvokeMethod(VectorNoiseInfo, MethodName, ParameterType, Parameters);
                    break;

                case GravityInfo GravityInfo:
                    Result = InvokeMethod(GravityInfo, MethodName, ParameterType, Parameters);
                    break;

                case ForceInfo ForceInfo:
                    Result = InvokeMethod(ForceInfo, MethodName, ParameterType, Parameters);
                    break;

                case VortexForceInfo VortexForceInfo:
                    Result = InvokeMethod(VortexForceInfo, MethodName, ParameterType, Parameters);
                    break;

                case PointAttractionForceInfo PointAttractionForceInfo:
                    Result = InvokeMethod(PointAttractionForceInfo, MethodName, ParameterType, Parameters);
                    break;

                case SolveForceVelocityInfo SolveForceVelocityInfo:
                    Result = InvokeMethod(SolveForceVelocityInfo, MethodName, ParameterType, Parameters);
                    break;

                case SubUVInfo SubUVInfo:
                    Result = InvokeMethod(SubUVInfo, MethodName, ParameterType, Parameters);
                    break;

                case ParticleStateInfo ParticleStateInfo:
                    Result = InvokeMethod(ParticleStateInfo, MethodName, ParameterType, Parameters);
                    break;

                case ParticleSpriteRendererInfo ParticleSpriteRendererInfo:
                    Result = InvokeMethod(ParticleSpriteRendererInfo, MethodName, ParameterType, Parameters);
                    break;

                case ParticleMeshRendererInfo ParticleMeshRendererInfo:
                    Result = InvokeMethod(ParticleMeshRendererInfo, MethodName, ParameterType, Parameters);
                    break;
            }

            return Result != null ? (Result as SerializeNode) : null;
        }

        private void OnPanelPaint(Control Sender)
        {
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            GraphicsHelper.SetCamera(Camera);

            UIManager UIManager = Sender.GetUIManager();

            DrawGrid(UIManager);
            Model.Draw(UIManager);

            Sender.PaintChildren();
            GraphicsHelper.SetCamera(null);
        }

        private void OnPanelPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (Camera != null)
            {
                Camera.ScreenX = GetScreenX();
                Camera.ScreenY = GetScreenY();
                Camera.ScreenHeight = GetHeight();
                Camera.AspectRatio = GetWidth() * 1.0f / GetHeight();
            }
        }

        private void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bLeftMouseDown = true;
            bCanUpdateHover = false;
            bMouseMoved = false;
            LeftMouseDownX = MouseX;
            LeftMouseDownY = MouseY;
            bContinue = false;
            Sender.CaptureMouse();

            int WorldX = 0, WorldY = 0;
            Camera.ScreenToWorld(MouseX, MouseY, ref WorldX, ref WorldY);

            if (HoverObject != null)
            {
                // Start move node(s)
                if (HoverObject is Node)
                {
                    Node HoverNode = HoverObject as Node;
                    SavedNodeX = HoverNode.X;
                    SavedNodeY = HoverNode.Y;
                }
            }
            else
            {
                HoverObject = Model.HitTest(WorldX, WorldY);
            }
        }

        private void OnPanelLeftMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            int WorldX = 0, WorldY = 0;
            Camera.ScreenToWorld(MouseX, MouseY, ref WorldX, ref WorldY);

            // Click on the node without move
            if (!bMouseMoved)
            {
                InspectEmitter(HoverObject, WorldX, WorldY);
            }
            // Move Node(s)
            else if (bMouseMoved)
            {
                if (HoverObject is ParticleEmitterNode)
                {
                    ParticleEmitterNode HoverNode = HoverObject as ParticleEmitterNode;
                    HoverNode.EmitterInfo.WorldX = WorldX;
                    HoverNode.EmitterInfo.WorldY = WorldY;
                    var EmitterInfo = Model.SystemResource.SystemInfo.EmitterInfos[HoverNode.EmitterIndex];
                    EmitterInfo.WorldX = WorldX;
                    EmitterInfo.WorldY = WorldY;
                }
            }

            bLeftMouseDown = false;
            bCanUpdateHover = true;
            bMouseMoved = false;
            bContinue = false;
            Sender.ReleaseMouse();
        }

        private void OnPanelRightMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            bRightMouseDown = true;
            bMouseMoved = false;
            SavedWorldX = Camera.WorldX;
            SavedWorldY = Camera.WorldY;
            RightMouseDownX = MouseX;
            RightMouseDownY = MouseY;

            bContinue = false;
            Sender.CaptureMouse();
        }

        private void OnPanelRightMouseUp(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (bRightMouseDown)
            {
                if (bMouseMoved == false && Sender.IsPointIn(MouseX, MouseY))
                {
                    InspectEmitter(HoverObject, MouseX, MouseY);
                    ShowMenu(HoverObject, MouseX, MouseY);
                }
                bContinue = false;
            }

            bRightMouseDown = false;
            bCanUpdateHover = true;
            bMouseMoved = false;
            Sender.ReleaseMouse();
        }

        private void OnPanelMouseMove(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            int WorldX = 0, WorldY = 0;
            Camera.ScreenToWorld(MouseX, MouseY, ref WorldX, ref WorldY);

            if (bCanUpdateHover)
            {
                HoverObject = Model.HitTest(WorldX, WorldY);
            }

            // Dragging
            if (bRightMouseDown)
            {
                bMouseMoved = Math.Abs(MouseX - RightMouseDownX) + Math.Abs(MouseY - RightMouseDownY) > 2;

                float ZoomRatio = Camera.GetZoomRatio();
                Camera.WorldX = SavedWorldX - (MouseX - RightMouseDownX) / ZoomRatio;
                Camera.WorldY = SavedWorldY - (MouseY - RightMouseDownY) / ZoomRatio;
            }

            if (bLeftMouseDown)
            {
                bMouseMoved = Math.Abs(MouseX - LeftMouseDownX) + Math.Abs(MouseY - LeftMouseDownY) > 2;

                float ZoomRatio = Camera.GetZoomRatio();
                int DeltaX = (int)((MouseX - LeftMouseDownX) / ZoomRatio);
                int DeltaY = (int)((MouseY - LeftMouseDownY) / ZoomRatio);
                DeltaX = DeltaX / MoveStep * MoveStep;
                DeltaY = DeltaY / MoveStep * MoveStep;

                // Move Node(s)
                if (HoverObject is ParticleEmitterNode)
                {
                    ParticleEmitterNode HoverNode = HoverObject as ParticleEmitterNode;
                    AddSelectedNode(HoverNode);

                    int NodeXBefore = HoverNode.X;
                    int NodeYBefore = HoverNode.Y;
                    int NodeXAfter = SavedNodeX + DeltaX;
                    int NodeYAfter = SavedNodeY + DeltaY;

                    SelectedNodes.ForEach(Item =>
                    {
                        Item.X += (NodeXAfter - NodeXBefore);
                        Item.Y += (NodeYAfter - NodeYBefore);
                    });
                }
            }
        }

        private void OnPanelMouseWheel(Control Sender, int MouseX, int MouseY, int MouseDeltaZ, int MouseDeltaW, ref bool bContinue)
        {
            if (IsPointIn(MouseX, MouseY))
            {
                int WorldX = 0, WorldY = 0;
                Camera.ScreenToWorld(MouseX, MouseY, ref WorldX, ref WorldY);

                if (MouseDeltaZ > 0) // bigger
                {
                    Camera.ZoomBigger(WorldX, WorldY);
                }
                else if (MouseDeltaZ < 0) // smaller
                {
                    Camera.ZoomSmaller(WorldX, WorldY);
                }
            }
        }

        public void AddSelectedNode(ParticleEmitterNode Node, bool bInspect = true)
        {
            if (Node == null || Node.bSelected == true)
            {
                return;
            }

            if (SelectedNodes.Contains(Node) == false)
            {
                Node.bSelected = true;
                SelectedNodes.Add(Node);
            }

            if (bInspect)
            {
                Inspect();
            }
        }

        public void RemoveSelectedNode(ParticleEmitterNode Node)
        {
            if (Node == null)
                return;

            if (SelectedNodes.Contains(Node) == true)
            {
                Node.bSelected = false;
                SelectedNodes.Remove(Node);
            }
            Inspect();
        }

        public void ClearSelectedNodes(bool bInspect = true)
        {
            foreach (ParticleEmitterNode Node in SelectedNodes)
            {
                Node.bSelected = false;
                Node.ClearHighlightItem();
            }
            SelectedNodes.Clear();
            if (bInspect)
            {
                Inspect();
            }
        }

        public void Inspect(bool Force = false)
        {
            // Always inspect the last one
            ParticleEmitterNode InspectObject = null;
            if (SelectedNodes.Count > 0)
            {
                InspectObject = SelectedNodes[SelectedNodes.Count - 1];
            }

            InspectorUI InspectorUI = InspectorUI.GetInstance();
            if (InspectObject != null)
            {
                if (InspectorUI.GetObjectInspected() != InspectObject || Force)
                {
                    InspectorUI.SetObjectInspected(InspectObject.Resource);
                    InspectorUI.InspectObject();
                }
            }
            else
            {
                InspectorUI.SetObjectInspected(Model.SystemResource);
                InspectorUI.InspectObject();
            }
        }

        public void UpdateInspect()
        {
            InspectorUI.GetInstance().ReadValueAndUpdateLayout();
        }

        private void DrawGrid(UIManager UIManager)
        {
            float size = 10.0f;
            int statePanelOffsetX = 0;
            int statePanelOffsetY = 0;
            int stateScrollWidth = this.GetWidth() + (int)(1000 * 2 * size);
            int stateScrollHeight = this.GetHeight() + (int)(1000 * 2 * size);
            Vector4f ThickColor = new Vector4f(0.0f, 0.0f, 0.0f, 0.5f);
            Vector4f ThinColor = new Vector4f(0.5f, 0.5f, 0.5f, 0.5f);
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();
            GraphicsHelper.DrawGrid(UIManager, size, 10, statePanelOffsetX * 1.0f, statePanelOffsetY * 1.0f,
            stateScrollWidth * 1.0f, stateScrollHeight * 1.0f, ThickColor, ThinColor, 0);
        }

        #region Menu Code

        private void ShowMenu(object Object, int MouseX, int MouseY)
        {
            Menu Menu = new Menu(GetUIManager());

            if (Object == null)
            {
                MenuItem MenuItem = new MenuItem();
                MenuItem.SetText("Save (ctrl+S)");
                MenuItem.ClickedEvent += OnMenuSaveEmitter;
                Menu.AddMenuItem(MenuItem);
                Menu.AddSeperator();

                MenuItem = new MenuItem();
                MenuItem.SetText("Add Emitter (ctrl+D)");
                MenuItem.ClickedEvent += OnMenuAddEmitter;
                Menu.AddMenuItem(MenuItem);
            }
            else if (Object is ParticleEmitterNode)
            {
                MenuItem MenuItem = new MenuItem();
                MenuItem.SetText("Delete Emitter (Backspace)");
                MenuItem.ClickedEvent += OnMenuDeleteEmitter;
                Menu.AddMenuItem(MenuItem);
                Menu.AddSeperator();

                MenuItem = new MenuItem();
                MenuItem.SetText("Export Emitter (ctrl+E)");
                MenuItem.ClickedEvent += OnMenuExportEmitter;
                Menu.AddMenuItem(MenuItem);
                Menu.AddSeperator();

                MenuItem = new MenuItem();
                MenuItem.SetText("Copy Emitter");
                MenuItem.ClickedEvent += OnMenuCopyEmitter;
                Menu.AddMenuItem(MenuItem);
                Menu.AddSeperator();

                MenuItem = new MenuItem();
                MenuItem.SetText("Paste Emitter");
                MenuItem.ClickedEvent += OnMenuPasteEmitter;
                Menu.AddMenuItem(MenuItem);
            }

            GetUIManager().GetContextMenu().ShowMenu(Menu, MouseX, MouseY);
        }

        private void OnMenuAddEmitter(MenuItem Sender)
        {
            DoAddEmitter();
        }

        private void OnMenuSaveEmitter(MenuItem Sender)
        {
            DoSaveEmitter();
        }

        private void OnMenuDeleteEmitter(MenuItem Sender)
        {
            var SelectedNode = HoverObject as ParticleEmitterNode;
            DoDeleteEmitter(SelectedNode);
        }

        private void OnMenuExportEmitter(MenuItem Sender)
        {
            var SelectedNode = HoverObject as ParticleEmitterNode;
            DoExportEmitter(SelectedNode);
        }

        private void OnMenuCopyEmitter(MenuItem Sender)
        {
            var EmitterNode = HoverObject as ParticleEmitterNode;
            if (EmitterNode != null && EmitterNode.EmitterInfo != null)
            {
                DoCopyEmitter(EmitterNode);
            }
        }

        private void OnMenuPasteEmitter(MenuItem Sender)
        {
            var TargetNode = HoverObject as ParticleEmitterNode;
            if (TargetNode != null && TargetNode.EmitterInfo != null)
            {
                DoPasteEmitter(TargetNode);
            }
        }

        #endregion Menu Code

        private void InspectEmitter(object Object, int WorldX, int WorldY)
        {
            ClearSelectedNodes(false);
            MainUI.GetInstance().ActivateDockingCard_Inspector();
            HoverObject = Object;
            ParticleEmitterNode HoverNode = Object as ParticleEmitterNode;
            if (HoverNode != null && HoverNode.Resource != null)
            {
                int EmitterIndex = HoverNode.EmitterIndex;
                var ModuleItem = HoverNode.HitModuleItem(WorldX, WorldY);
                if (ModuleItem != null)
                {
                    ModuleItem.Highlight = true;
                    HoverNode.InspectIndex = ModuleItem.Index;
                    HoverNode.Resource.ModuleIndex = ModuleItem.Index;

                    ParticleSystemScene.GetInstance().LocationShape = HoverNode.EmitterInfo.LocationShape;
                    ParticleSystemScene.GetInstance().SetShapeVisible(ModuleItem.Name == "LocationShape", EmitterIndex);
                    ParticleSystemScene.GetInstance().ParticleState = HoverNode.EmitterInfo.ParticleState;
                    ParticleSystemScene.GetInstance().SetKillVolumeVisible(ModuleItem.Name == "ParticleState", EmitterIndex);
                }
                else
                {
                    HoverNode.InspectIndex = -1;
                    HoverNode.Resource.ModuleIndex = (int)EmitterResourceMode.EMITTER_INTEGRATION;
                    ParticleSystemScene.GetInstance().SetShapeVisible(false, EmitterIndex);
                    ParticleSystemScene.GetInstance().SetKillVolumeVisible(false, EmitterIndex);
                }
                AddSelectedNode(HoverNode);
            }
            else
            {
                // Inspect the ParticleSystemResource
                Inspect();
                ParticleSystemScene.GetInstance().SetShapeVisible(false, -1);
                ParticleSystemScene.GetInstance().SetKillVolumeVisible(false, -1);
            }
        }

        private void ClearStatus()
        {
            ClearSelectedNodes();
            LastSelectedNodes.Clear();
            HoverObject = null;
        }

        private void OnPropertyValueChanged(Inspector_Property PropertyInspector, object Object, string Name, object NewValue)
        {
            if (NewValue == null || !InspectorUI.GetInstance().IsVisible())
            {
                return;
            }

            if (Name == "EmitterName" && SelectedNodes.Count > 0)
            {
                var EmitterNode = SelectedNodes[SelectedNodes.Count - 1];
                if (NewValue.ToString().Length > 0)
                {
                    EmitterNode.NodeName = NewValue.ToString();
                }
            }
            else if (Name == "ShapeType" || Name == "DistributionType")
            {
                Inspect(true);
            }
            else if (NewValue.GetType() == typeof(ParticleEmitterResourceSlot))
            {
                int Index = Convert.ToInt32(Object);
                OnEmitterResourceChanged(Object, NewValue, Index);
            }
            else if (Object.GetType() == typeof(ParticleSpriteRendererInfo) && SelectedNodes.Count > 0)
            {
                var EmitterNode = SelectedNodes[SelectedNodes.Count - 1];
                ParticleSpriteRendererInfo RendererInfo = Object as ParticleSpriteRendererInfo;
                RendererInfo.Modified = true;
                EmitterNode.EmitterInfo.SpriteRenderer = RendererInfo;
            }
            else if (Object.GetType() == typeof(ParticleMeshRendererInfo) && SelectedNodes.Count > 0)
            {
                var EmitterNode = SelectedNodes[SelectedNodes.Count - 1];
                ParticleMeshRendererInfo RendererInfo = Object as ParticleMeshRendererInfo;
                RendererInfo.Modified = true;
                EmitterNode.EmitterInfo.MeshRenderer = RendererInfo;
            }
            else if (Object.GetType() == typeof(LocationShapeInfo))
            {
                OnLocationShapeChanged(NewValue, Name);
            }
            else if (Object.GetType() == typeof(ParticleCustomProperty) || Object.GetType() == typeof(DynamicMaterialProperty))
            {
                bEmitterDirty = true;
            }
            else if (Object.GetType() == typeof(ParticleStateInfo) && SelectedNodes.Count > 0)
            {
                var EditorWorld = EditorScene.GetInstance().GetWorld();
                var StateInfo = Object as ParticleStateInfo;
                if (EditorWorld != null && StateInfo.KillTrigger != KillTrigger.None)
                {
                    var EmitterNode = SelectedNodes[SelectedNodes.Count - 1];
                    ParticleSystemDebugEvent Event = new ParticleSystemDebugEvent();
                    Event.eventType = ParticleDebugEventType.DrawKillVolume;
                    Event.emitterIndex = EmitterNode.EmitterIndex;
                    ParticleSimulationSystemG.CacheToDebugEvent(EditorWorld.GetNativePointer(), ParticleSystemUI.GetInstance().CurrentParticleSystem.GetGUID(), Event);
                }
            }
            else if (Name == "EnableOriginVelocity")
            {
                Inspect(true);
            }
            else if (Name == "Simulation")
            {
                Inspect(true);
                ParticleSystemUI.GetInstance().RestartParticleSystem();
                ParticleSystemUI.GetInstance().NotifyRefreshParticleSystem();
                ParticleSystemUI.GetInstance().SetTileState(true);
            }
            else if (SelectedNodes.Count > 0 && Object.GetType() == typeof(ParticleEmitterInfo) &&
                     (Name == "SimulationParameters" || Name == "OverrideDashboard" || Name == "OverrideInitStage" ||
                      Name == "OverrideUpdateStage"))
            {
                var code = NewValue.ToString();
                if (!OldSimulationCodes.ContainsKey(Name))
                    OldSimulationCodes[Name] = "";
                if (code != OldSimulationCodes[Name])
                {
                    var emitterNode = SelectedNodes[SelectedNodes.Count - 1];
                    var systemPath = Model.SystemResource.GetAssetPath();
                    systemPath = EditorUtilities.StandardFilenameToEditorFilename(systemPath);
                    var shaderPath = systemPath + "." + emitterNode.EmitterIndex + ".compute.nda";
                    var shader = (Object as ParticleEmitterInfo).GenSimulationShader();
                    //absolute path for nda generation
                    AssetImporterManager.Instance().GenComputeShaderOnDemand(shader, shaderPath);
                    //relative path for particle system
                    (Object as ParticleEmitterInfo).GPUComputeSim.SimulationPath = EditorUtilities.EditorDirectoryToStandardDirectory(shaderPath);

                    ParticleSystemUI.GetInstance().RestartParticleSystem();
                    ParticleSystemUI.GetInstance().NotifyRefreshParticleSystem();
                    ParticleSystemUI.GetInstance().SetTileState(true);
                    OldSimulationCodes[Name] = code;
                }
            }

            // Only restart with enabled module has been modified when system is not stopped.
            var PropInfo = Object.GetType().GetProperty("Enabled");
            if (PropInfo != null)
            {
                bool Enabled = (bool)PropInfo.GetValue(Object);
                if (Enabled)
                {
                    ParticleSystemUI.GetInstance().RestartParticleSystem();
                    ParticleSystemUI.GetInstance().NotifyRefreshParticleSystem();
                }
                ParticleSystemUI.GetInstance().SetTileState(true);
            }
        }

        private void OnEmitterResourceChanged(object Object, object NewValue, int Index)
        {
            if (Index >= 0)
            {
                var Slot = NewValue as ParticleEmitterResourceSlot;
                var EmitterResource = Resource.Get(Slot.ParticleEmitterPath) as ParticleEmitterResource;
                if (EmitterResource != null && Index < Model.SystemResource.SystemInfo.EmitterInfos.Count)
                {
                    Model.SystemResource.SystemInfo.EmitterInfos[Index] = EmitterResource.EmitterInfo;
                    Model.ReloadEmitters(Model.SystemResource);
                    CEResource.UpdateParticleSystem(Model.SystemResource.ResourcePtr.GetPointer(), Model.SystemResource.SystemInfo);
                    Model.SystemResource.Reload();
                    Model.SystemResource.Save();

                    var Entity = ParticleSystemScene.GetInstance().GetPreviewEntity();
                    var ParticleComponent = Entity.GetComponent<ParticleSystemComponent>();
                    ParticleComponent.SystemInfo = ParticleSystemUI.GetInstance().CurrentSystemInfo.SystemResourceSlot;
                }
            }
        }

        private void OnLocationShapeChanged(object NewValue, string Name)
        {
            var EmitterNode = HoverObject as ParticleEmitterNode;
            if (EmitterNode == null)
            {
                return;
            }
            var EmitterInfo = EmitterNode.EmitterInfo;

            if (Name == "ModelPath")
            {
                string UUID = NewValue.ToString();
                if (Resource.Get(UUID) is MeshAssetDataResource Mesh && SelectedNodes.Count > 0)
                {
                    ParticleSystemUI.GetInstance().Controller.SampleMesh(Mesh, (UInt32)EmitterNode.EmitterIndex, EmitterInfo.LocationShape.LodIndex);
                }
            }
            else if (Name == "LodIndex")
            {
                MeshAssetDataResource Mesh = Resource.Get(EmitterInfo.LocationShape.ModelPath) as MeshAssetDataResource;
                if (Mesh != null)
                {
                    var LodIndex = Convert.ToUInt32(NewValue);
                    ParticleSystemUI.GetInstance().Controller.SampleMesh(Mesh, (UInt32)EmitterNode.EmitterIndex, LodIndex);
                }
            }
        }
    }
}