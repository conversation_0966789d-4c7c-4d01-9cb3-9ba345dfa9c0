using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;

namespace CrossEditor
{
    // deprecated, use BuildProject instead
    class BuildUI : DialogUI
    {
        ScrollView _ScrollView;
        Panel _ScrollPanel;

        List<Button> _ButtonItemList;
        List<Check> _CheckItemList;

        Button _ButtonClearAll;
        Button _ButtonSelectAll;
        Label _LabelSelectionCount;

        Label _LabelClientName;
        Edit _EditGameName;

        Label _LabelInitWorld;
        ComboBox _ComboBoxInitWorld;
        string _InitWorld;

        Label _LabelPlatform;
        ComboBox _ComboBoxPlatform;

        Label _LabelBuildType;
        ComboBox _ComboBoxBuildType;

        Button _ButtonGenerate;
        Button _ButtonBuild;
        Button _ButtonRun;
        Color _ButtonInitColor;

        Label _LabelStagingFolder;
        Edit _EditStagingFolder;

        Label _LabelPackAll;
        Check _CheckPackAll;

        Label _LabelDirectCopyResToBin;
        Check _CheckDirectCopyResToBin;

        Label _LabelEnableFetchResources;
        Check _CheckEnableFetchResources;

        Panel _MainPanel;

        Label _LabelSrcCookDir;
        Edit _EditSrcCookDir;

        Label _LabelDesCookDir;
        Edit _EditDesCookDir;
        Button _ButtonCookDir;

        static int _IDGenerator = 0;
        int _ID;

        enum ECompileTaskState
        {
            NotReady,
            InProgress,
            Succeed,
            Failed
        }

        class BuildCache
        {
            public int TaskID = -1;
            public ECompileTaskState CompileTaskState = ECompileTaskState.NotReady;
            public Builder.BuildOptions Options;
            public Process CompileProcess;
        }

        static readonly BuildCache Cache = new BuildCache();
        static Process CompileProcess
        {
            get
            {
                return Cache.CompileProcess;
            }
            set
            {
                Cache.CompileProcess = value;
            }
        }

        public BuildUI()
        {
            _ID = _IDGenerator++;
            _ButtonItemList = new List<Button>();
            _CheckItemList = new List<Check>();
        }

        public void Initialize()
        {
            const int InX = 26;
            const int InTitleW = 300;
            const int InEditW = 450;
            const int InCloumnOffset = 20;
            const int Width = InTitleW + InEditW + InX * 2 + InCloumnOffset;
            const int Height = 550;
            const int InHeightOffset = 25;
            const int ScrollViewHeight = (int)(Height * 0.25);
            const int InY = ScrollViewHeight + 65 + InHeightOffset;

            base.Initialize(GetUIManager(), "Build", Width, Height);
            _MainPanel = new Panel();
            _MainPanel.SetWidth(_PanelDialog.GetWidth());
            _MainPanel.SetHeight(_PanelDialog.GetHeight());
            _PanelDialog.AddChild(_MainPanel);

            int Index = 0;
            {
                _ScrollView = new ScrollView();
                _ScrollView.Initialize();
                _ScrollView.SetPosition(25, 45, Width - 50, ScrollViewHeight);
                _ScrollView.SetBackgroundColor(Color.FromRGBA(30, 30, 30, 255));
                _MainPanel.AddChild(_ScrollView);

                _ScrollPanel = _ScrollView.GetScrollPanel();

                const int ButtonInY = ScrollViewHeight + 55;
                _ButtonClearAll = new Button();
                _ButtonClearAll.Initialize();
                _ButtonClearAll.SetPosition(InX, ButtonInY, 150, 20);
                _ButtonClearAll.SetFontSize(16);
                _ButtonClearAll.SetText("Clear All");
                _ButtonClearAll.SetTextOffsetY(2);
                _ButtonClearAll.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonClearAll.ClickedEvent += OnButtonClearAllClicked;
                _MainPanel.AddChild(_ButtonClearAll);

                _ButtonSelectAll = new Button();
                _ButtonSelectAll.Initialize();
                _ButtonSelectAll.SetPosition(183, ButtonInY, 150, 20);
                _ButtonSelectAll.SetFontSize(16);
                _ButtonSelectAll.SetText("Select All");
                _ButtonSelectAll.SetTextOffsetY(2);
                _ButtonSelectAll.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonSelectAll.ClickedEvent += OnButtonSelectAllClicked;
                _MainPanel.AddChild(_ButtonSelectAll);

                _LabelSelectionCount = new Label();
                _LabelSelectionCount.SetPosition(350, ButtonInY + 2, 500, 20);
                _LabelSelectionCount.SetFontSize(16);
                _LabelSelectionCount.SetTextAlign(TextAlign.CenterLeft);
                _LabelSelectionCount.SetText("XXX scenes and YYY canvases selected.");
                _MainPanel.AddChild(_LabelSelectionCount);

                AddScenesAndCanvases();
            }

            {
                int Y = InY + InHeightOffset * Index;
                _LabelClientName = new Label();
                _LabelClientName.SetPosition(InX, Y, InTitleW, 20);
                _LabelClientName.SetFontSize(16);
                _LabelClientName.SetTextAlign(TextAlign.CenterLeft);
                _LabelClientName.SetText("Client Name");
                _MainPanel.AddChild(_LabelClientName);

                _EditGameName = new Edit();
                _EditGameName.SetFontSize(16);
                _EditGameName.Initialize(EditMode.Simple_SingleLine);
                _EditGameName.LoadSource("");
                _MainPanel.AddChild(_EditGameName);
                EditContextUI.GetInstance().RegisterEdit(_EditGameName);
                _EditGameName.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 16);
                _EditGameName.SetText_End("CrossGame");
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelStagingFolder = new Label();
                _LabelStagingFolder.SetPosition(InX, Y, InTitleW, 20);
                _LabelStagingFolder.SetFontSize(16);
                _LabelStagingFolder.SetTextAlign(TextAlign.CenterLeft);
                _LabelStagingFolder.SetText("Staging");
                _MainPanel.AddChild(_LabelStagingFolder);

                _EditStagingFolder = new Edit();
                _EditStagingFolder.SetFontSize(16);
                _EditStagingFolder.Initialize(EditMode.Simple_SingleLine);
                _EditStagingFolder.LoadSource("");
                _MainPanel.AddChild(_EditStagingFolder);
                EditContextUI.GetInstance().RegisterEdit(_EditStagingFolder);
                _EditStagingFolder.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 16);
                _EditStagingFolder.SetText_End(string.Format("{0}/{1}/{2}", MainUI.GetInstance().GetProjectDirectory(), "Build", "NewCrossGame"));
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelInitWorld = new Label();
                _LabelInitWorld.SetPosition(InX, Y, InTitleW, 20);
                _LabelInitWorld.SetFontSize(16);
                _LabelInitWorld.SetTextAlign(TextAlign.CenterLeft);
                _LabelInitWorld.SetText("Initial World");
                _MainPanel.AddChild(_LabelInitWorld);

                _ComboBoxInitWorld = new ComboBox();
                _ComboBoxInitWorld.Initialize();
                _ComboBoxInitWorld.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 16);
                _MainPanel.AddChild(_ComboBoxInitWorld);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelPlatform = new Label();
                _LabelPlatform.SetPosition(InX, Y, InTitleW, 20);
                _LabelPlatform.SetFontSize(16);
                _LabelPlatform.SetTextAlign(TextAlign.CenterLeft);
                _LabelPlatform.SetText("Platform");
                _MainPanel.AddChild(_LabelPlatform);

                _ComboBoxPlatform = new ComboBox();
                _ComboBoxPlatform.Initialize();
                if (SceneRuntime.IsUnix)
                {
                    _ComboBoxPlatform.AddItem("macOS");
                    _ComboBoxPlatform.AddItem("iOS");
                    _ComboBoxPlatform.SetSelectedItemByText("macOS");
                }
                else
                {
                    _ComboBoxPlatform.AddItem("Windows");
                    _ComboBoxPlatform.SetSelectedItemByText("Windows");
                }
                _ComboBoxPlatform.AddItem("Android");
                //_ComboBoxPlatform.AddItem("WXGame");
                _ComboBoxPlatform.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 16);
                _ComboBoxPlatform.ItemSelectedEvent += _ComboBoxPlatform_ItemSelectedEvent;
                _MainPanel.AddChild(_ComboBoxPlatform);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelBuildType = new Label();
                _LabelBuildType.SetPosition(InX, Y, InTitleW, 20);
                _LabelBuildType.SetFontSize(16);
                _LabelBuildType.SetTextAlign(TextAlign.CenterLeft);
                _LabelBuildType.SetText("Build Type");
                _MainPanel.AddChild(_LabelBuildType);

                _ComboBoxBuildType = new ComboBox();
                _ComboBoxBuildType.Initialize();
                _ComboBoxBuildType.AddItem("Debug");
                _ComboBoxBuildType.AddItem("Release");
                _ComboBoxBuildType.AddItem("RelWithDebInfo");
                _ComboBoxBuildType.AddItem("MinSizeRel");
                _ComboBoxBuildType.SetSelectedItemByText("Debug");
                _ComboBoxBuildType.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 16);
                _MainPanel.AddChild(_ComboBoxBuildType);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelPackAll = new Label();
                _LabelPackAll.SetPosition(InX, Y, InTitleW, 20);
                _LabelPackAll.SetFontSize(16);
                _LabelPackAll.SetTextAlign(TextAlign.CenterLeft);
                _LabelPackAll.SetText("Pack All Resources");
                _MainPanel.AddChild(_LabelPackAll);

                _CheckPackAll = new Check();
                _CheckPackAll.Initialize();
                _CheckPackAll.SetPosition(InX + InTitleW + InCloumnOffset, Y, InTitleW, 20);
                _CheckPackAll.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                _CheckPackAll.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                _CheckPackAll.SetAutoCheck(true);
                _CheckPackAll.SetEnable(true);
                _CheckPackAll.SetChecked(true);
                _MainPanel.AddChild(_CheckPackAll);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelDirectCopyResToBin = new Label();
                _LabelDirectCopyResToBin.SetPosition(InX, Y, InTitleW, 20);
                _LabelDirectCopyResToBin.SetFontSize(16);
                _LabelDirectCopyResToBin.SetTextAlign(TextAlign.CenterLeft);
                _LabelDirectCopyResToBin.SetText("Copy Res Direct To Bin (experimental)");
                _MainPanel.AddChild(_LabelDirectCopyResToBin);

                _CheckDirectCopyResToBin = new Check();
                _CheckDirectCopyResToBin.Initialize();
                _CheckDirectCopyResToBin.SetPosition(InX + InTitleW + InCloumnOffset, Y, InTitleW, 20);
                _CheckDirectCopyResToBin.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                _CheckDirectCopyResToBin.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                _CheckDirectCopyResToBin.SetAutoCheck(true);
                _CheckDirectCopyResToBin.SetEnable(true);
                _CheckDirectCopyResToBin.SetChecked(false);
                _MainPanel.AddChild(_CheckDirectCopyResToBin);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index;
                _LabelEnableFetchResources = new Label();
                _LabelEnableFetchResources.SetPosition(InX, Y, InTitleW, 20);
                _LabelEnableFetchResources.SetFontSize(16);
                _LabelEnableFetchResources.SetTextAlign(TextAlign.CenterLeft);
                _LabelEnableFetchResources.SetText("Enable Fetch Resources (experimental)");
                _MainPanel.AddChild(_LabelEnableFetchResources);

                _CheckEnableFetchResources = new Check();
                _CheckEnableFetchResources.Initialize();
                _CheckEnableFetchResources.SetPosition(InX + InTitleW + InCloumnOffset, Y, InTitleW, 20);
                _CheckEnableFetchResources.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                _CheckEnableFetchResources.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                _CheckEnableFetchResources.SetAutoCheck(true);
                _CheckEnableFetchResources.SetEnable(true);
                _CheckEnableFetchResources.SetChecked(false);
                _MainPanel.AddChild(_CheckEnableFetchResources);
            }

            Index++;
            {
                int W = (InTitleW + InEditW) / 3;
                int Y = InY + InHeightOffset * Index;
                _ButtonGenerate = new Button();
                _ButtonGenerate.Initialize();
                _ButtonGenerate.SetPosition(InX, Y, W, 20);
                _ButtonGenerate.SetFontSize(16);
                _ButtonGenerate.SetText("Generate");
                _ButtonGenerate.SetTextOffsetY(2);
                _ButtonGenerate.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonGenerate.ClickedEvent += OnButtonGenerateClicked;
                _MainPanel.AddChild(_ButtonGenerate);

                _ButtonBuild = new Button();
                _ButtonBuild.Initialize();
                _ButtonBuild.SetPosition(InX + W + 10, Y, W, 20);
                _ButtonBuild.SetFontSize(16);
                _ButtonBuild.SetText("Build");
                _ButtonBuild.SetTextOffsetY(2);
                _ButtonBuild.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonBuild.ClickedEvent += OnButtonBuildClicked;
                _MainPanel.AddChild(_ButtonBuild);

                _ButtonRun = new Button();
                _ButtonRun.Initialize();
                _ButtonRun.SetPosition(InX + 2 * W + 20, Y, W, 20);
                _ButtonRun.SetFontSize(16);
                _ButtonRun.SetText("Run");
                _ButtonRun.SetTextOffsetY(2);
                _ButtonRun.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonRun.ClickedEvent += OnButtonRunClicked;
                _MainPanel.AddChild(_ButtonRun);
                _ButtonInitColor = _ButtonRun.GetTextColor();
                _UpdateButtonBuildAndRun();
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index + 10;

                _LabelSrcCookDir = new Label();
                _LabelSrcCookDir.SetPosition(InX, Y, InTitleW, 20);
                _LabelSrcCookDir.SetFontSize(16);
                _LabelSrcCookDir.SetTextAlign(TextAlign.CenterLeft);
                _LabelSrcCookDir.SetText("Cook Directory");
                _MainPanel.AddChild(_LabelSrcCookDir);

                _EditSrcCookDir = new Edit();
                _EditSrcCookDir.SetFontSize(16);
                _EditSrcCookDir.Initialize(EditMode.Simple_SingleLine);
                _EditSrcCookDir.LoadSource("");
                _EditSrcCookDir.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 20);
                _MainPanel.AddChild(_EditSrcCookDir);
                EditContextUI.GetInstance().RegisterEdit(_EditSrcCookDir);
            }

            Index++;
            {
                int Y = InY + InHeightOffset * Index + 10;
                _LabelDesCookDir = new Label();
                _LabelDesCookDir.SetPosition(InX, Y, InTitleW, 20);
                _LabelDesCookDir.SetFontSize(16);
                _LabelDesCookDir.SetTextAlign(TextAlign.CenterLeft);
                _LabelDesCookDir.SetText("Target Directory");
                _MainPanel.AddChild(_LabelDesCookDir);

                _EditDesCookDir = new Edit();
                _EditDesCookDir.SetFontSize(16);
                _EditDesCookDir.Initialize(EditMode.Simple_SingleLine);
                _EditDesCookDir.LoadSource("");
                _EditDesCookDir.SetPosition(InX + InTitleW + InCloumnOffset, Y, InEditW, 20);
                _MainPanel.AddChild(_EditDesCookDir);
                EditContextUI.GetInstance().RegisterEdit(_EditDesCookDir);
            }
            Index++;
            {
                int W = (InTitleW + InEditW) / 3;
                int Y = InY + InHeightOffset * Index + 15;
                _ButtonCookDir = new Button();
                _ButtonCookDir.Initialize();
                _ButtonCookDir.SetPosition(InX + W + 10, Y, W, 20);
                _ButtonCookDir.SetFontSize(16);
                _ButtonCookDir.SetText("Cook Directory");
                _ButtonCookDir.SetTextOffsetY(2);
                _ButtonCookDir.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
                _ButtonCookDir.ClickedEvent += OnButtonCookDirClicked;
                _MainPanel.AddChild(_ButtonCookDir);
            }

            // Check previous compile task state
            if (Cache.CompileTaskState != ECompileTaskState.NotReady)
            {
                if (Cache.CompileTaskState == ECompileTaskState.InProgress)
                {
                    _ButtonBuild.SetText("Build (Compiling...)");
                }
                else
                {
                    _ButtonBuild.SetText(Cache.CompileTaskState == ECompileTaskState.Succeed ? "Build (Succeed)" : "Build(Failed)");
                }
            }
        }

        private void _ComboBoxPlatform_ItemSelectedEvent(ComboBox Sender)
        {
            _UpdateButtonBuildAndRun();

            string Tips = "";
            var Platform = GetAssetPlatform();
            if (Platform == AssetPlatform.ANDROID)
            {
                Tips = "Use Android Studio To Build And Run Android Apps.";
            }
            else if (Platform == AssetPlatform.IOS || Platform == AssetPlatform.MACOS)
            {
                Tips = "Use XCode To Run macOS Or iOS Apps.";
            }

            if (Tips != "")
            {
                CommonDialogUI CommonDialogUI = new CommonDialogUI();
                CommonDialogUI.Initialize(GetUIManager(), "Tips", Tips, CommonDialogType.OK);
                DialogUIManager.GetInstance().ShowDialogUI(CommonDialogUI);
            }
        }

        private void _UpdateButtonBuildAndRun()
        {
            var Platform = GetAssetPlatform();
            if (Platform == AssetPlatform.ANDROID)
            {
                _ButtonBuild.SetTextColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
                _ButtonBuild.SetEnable(false);
                _ButtonRun.SetTextColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
                _ButtonRun.SetEnable(false);
            }
            else if (Platform == AssetPlatform.IOS || Platform == AssetPlatform.MACOS)
            {
                _ButtonBuild.SetTextColor(_ButtonInitColor);
                _ButtonBuild.SetEnable(true);
                _ButtonRun.SetTextColor(Color.EDITOR_UI_GRAY_DRAW_COLOR);
                _ButtonRun.SetEnable(false);
            }
            else
            {
                _ButtonBuild.SetTextColor(_ButtonInitColor);
                _ButtonBuild.SetEnable(true);
                _ButtonRun.SetTextColor(_ButtonInitColor);
                _ButtonRun.SetEnable(true);
            }
        }

        public override void Update()
        {
            base.Update();

            List<string> CheckedScenes = new List<string>();
            CountCheckedScenes(CheckedScenes);
            int SceneCount = CheckedScenes.Count;

            _LabelSelectionCount.SetText(string.Format("{0} scene{1} selected.", SceneCount, SceneCount > 1 ? "s" : ""));

            {
                _InitWorld = "";
                _ComboBoxInitWorld.ClearItems();
                string Current = _ComboBoxInitWorld.GetSelectedItemText();
                if (SceneCount > 0)
                {
                    int SelectIndex = 0;
                    for (int i = 0; i < SceneCount; i++)
                    {
                        var Scene = _InitWorld = CheckedScenes[i];
                        var SceneName = string.Format("{0}({1})", Path.GetFileName(Scene), Scene);
                        _ComboBoxInitWorld.AddItem(SceneName);
                        if (SceneName == Current) SelectIndex = i;
                    }
                    _ComboBoxInitWorld.SetSelectedItemIndex(SelectIndex);
                }
            }

            var GameName = _EditGameName.GetText();
            if (GameName.Contains(" "))
            {
                Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Warning, "Builder", "Config", 1, "The game name cannot contain Spaces. (auto converted to _)");
                _EditGameName.SetText(GameName.Replace(" ", "_"));
            }
        }

        AssetPlatform GetAssetPlatform()
        {
            return AssetPlatformHelper.StringToAssetPlatform(_ComboBoxPlatform.GetSelectedItemText());
        }

        string GetClientName() { return _EditGameName.GetText(); }

        string GetSrcCookDir() { return PathHelper.ToStandardForm(_EditSrcCookDir.GetText()); }

        string GetDesCookDir() { return PathHelper.ToStandardForm(_EditDesCookDir.GetText()); }

        Builder.BuildType GetBuildType()
        {
            Builder.BuildType Result;
            Enum.TryParse(_ComboBoxBuildType.GetSelectedItemText(), true, out Result);
            return Result;
        }

        string GetStagingFolder()
        {
            return PathHelper.ToStandardForm(_EditStagingFolder.GetSourceString()).Replace("//", "/");
        }

        string GetInitialWorld()
        {
            return _InitWorld;
        }

        bool IsPackAllResources()
        {
            return _CheckPackAll.GetChecked();
        }

        bool IsDirectCopyResToBin()
        {
            return _CheckDirectCopyResToBin.GetChecked();
        }
        bool IsEnableFetchResources()
        {
            return _CheckEnableFetchResources.GetChecked();
        }

        void CountCheckedScenes(List<string> Output)
        {
            foreach (Check CheckItem in _CheckItemList)
            {
                bool bChecked = CheckItem.GetChecked();
                if (bChecked) Output.Add(CheckItem.GetTagString1());
            }
        }

        void AddScenesAndCanvases()
        {
            List<string> ScenesAndCanvases = CollectScenesAndCanvases();

            int Width = _ScrollView.GetWidth() - ScrollView.SCROLL_BAR_SIZE;
            int LabelItemX = 18;

            _ScrollPanel.ClearChildren();
            _ButtonItemList.Clear();
            _CheckItemList.Clear();

            int OffsetY = 0;
            foreach (string Path in ScenesAndCanvases)
            {
                Button ButtonItem = new Button();
                ButtonItem.Initialize();
                ButtonItem.SetPosition(0, OffsetY, Width, 20);
                ButtonItem.SetDownColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
                ButtonItem.SetHoverColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
                ButtonItem.ClickedEvent += OnButtonItemXClicked;
                _ScrollPanel.AddChild(ButtonItem);
                _ButtonItemList.Add(ButtonItem);

                Check CheckItem = new Check();
                CheckItem.Initialize();
                CheckItem.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
                CheckItem.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
                CheckItem.SetAutoCheck(true);
                CheckItem.SetPosition(3, OffsetY + 3, 13, 13);
                CheckItem.SetTagString1(Path);
                _ScrollPanel.AddChild(CheckItem);
                _CheckItemList.Add(CheckItem);

                Label LabelItem = new Label();
                LabelItem.Initialize();
                LabelItem.SetPosition(LabelItemX, OffsetY, Width - LabelItemX, 20);
                LabelItem.SetText(Path);
                LabelItem.SetTextAlign(TextAlign.CenterLeft);
                LabelItem.SetTextOffsetY(0);
                LabelItem.SetFontSize(15);
                _ScrollPanel.AddChild(LabelItem);

                OffsetY += 20;
            }
            if (ScenesAndCanvases.Count > 0)
            {
                var CurrentSceneFullName = EditorScene.GetInstance().GetCurrentSceneFilename();
                if (CurrentSceneFullName == "")
                {
                    _CheckItemList[0].SetChecked(true);
                }
                else
                {
                    var SubName = EditorUtilities.EditorFilenameToStandardFilename(CurrentSceneFullName);
                    var Index = ScenesAndCanvases.IndexOf(SubName);
                    if (Index >= 0)
                    {
                        _CheckItemList[Index].SetChecked(true);
                    }
                }
            }
            _ScrollPanel.SetSize(Width, OffsetY);
            _ScrollView.UpdateScrollBar();
        }

        public List<string> CollectScenesAndCanvases()
        {
            List<string> Scenes = new List<string>();
            List<string> Canvases = new List<string>();
            CollectScenesAndCanvases(Scenes, Canvases);
            List<string> ScenesAndCanvases = new List<string>();
            foreach (string Scene in Scenes)
            {
                ScenesAndCanvases.Add(Scene);
            }
            foreach (string Canvas in Canvases)
            {
                ScenesAndCanvases.Add(Canvas);
            }
            return ScenesAndCanvases;
        }

        void CollectScenesAndCanvases(List<string> Scenes, List<string> Canvases)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ContentsDirectory = ProjectDirectory + "/Contents";
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(ContentsDirectory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string Path = DirectoryWalkItem.Path;
                    string Extension = PathHelper.GetExtensionOfPath(Path);
                    if (Extension == "world")
                    {
                        string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                        Scenes.Add(Path1);
                    }
                    else if (Extension == "canvas")
                    {
                        string Path1 = EditorUtilities.EditorFilenameToStandardFilename(Path);
                        Canvases.Add(Path1);
                    }
                }
            }
        }

        void OnButtonItemXClicked(Button Sender)
        {
            foreach (Button ButtonItemX in _ButtonItemList)
            {
                ButtonItemX.SetNormalColor(Color.EDITOR_UI_COLOR_KEY);
            }
            Sender.SetNormalColor(Color.EDITOR_UI_HILIGHT_COLOR_GRAY);
        }

        void OnButtonClearAllClicked(Button Sender)
        {
            foreach (Check CheckItem in _CheckItemList)
            {
                CheckItem.SetChecked(false);
            }
        }

        void OnButtonSelectAllClicked(Button Sender)
        {
            foreach (Check CheckItem in _CheckItemList)
            {
                CheckItem.SetChecked(true);
            }
        }

        List<string> GetCheckedScenes()
        {
            List<string> CheckedScenes = new List<string>();
            foreach (Check CheckItem in _CheckItemList)
            {
                bool bChecked = CheckItem.GetChecked();
                if (bChecked)
                {
                    string Path = CheckItem.GetTagString1();
                    CheckedScenes.Add(Path);
                }
            }
            return CheckedScenes;
        }

        void OnButtonGenerateClicked(Button Sender)
        {
            if (CompileProcess != null)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "A compilation task is in progress, please wait for it to complete.");
                return;
            }

            List<string> CheckedScenesAndCanvases = GetCheckedScenes();
            if (CheckedScenesAndCanvases.Count == 0)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Please choose one world at least.");
                return;
            }

            var EnginePath = EditorConfigManager.GetInstance().GetConfig<GlobalConfig>().EnginePath;
            if (!File.Exists(string.Format("{0}/CMakeLists.txt", EnginePath)))
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", string.Format("Incorrect engine path: \"{0}\", \nPlease set the correct path in the GlobalConfig panel.", EnginePath));
                return;
            }

            string StagingFolder = GetStagingFolder();
            SimpleDelegate OnFinished = () => ProcessHelper.OpenContainingFolder(StagingFolder);

            Cache.TaskID = _ID;
            Builder.BuildOptions Options = Cache.Options = new Builder.BuildOptions(
                GetAssetPlatform(),
                GetBuildType(),
                GetClientName(),
                StagingFolder,
                GetInitialWorld(),
                CheckedScenesAndCanvases,
                IsPackAllResources(),
                IsDirectCopyResToBin(),
                true,
                false,
                "",
                "",
                IsEnableFetchResources(),
                OnFinished
            );

            Builder Builder = Builder.GetInstance();
            Progress Progress = Builder.StartBuild(Options);
            ProgressUI ProgressUI = new ProgressUI();
            ProgressUI.Initialize(GetUIManager(), Progress, true);
            ProgressUI.CancelEvent += (ProgressUI ProgressUI1) => { Builder.StopBuild(); };
            DialogUIManager.GetInstance().ShowDialogUI(ProgressUI);
        }

        void OnButtonBuildClicked(Button Sender)
        {
            if (CompileProcess != null && !CompileProcess.HasExited)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "A compile task is already running in the background.");
                return;
            }

            if (Cache.TaskID == -1)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Genrate project first please.");
                return;
            }
            else if (Cache.TaskID != _ID)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Seems you have not generate new project, compile will use cache.");
            }

            var Options = Cache.Options;
            Progress Progress = new Progress("Compile Progress", 1);
            ProgressUI ProgressUI = new ProgressUI();
            ProgressUI.Initialize(GetUIManager(), Progress, true);
            DialogUIManager.GetInstance().ShowDialogUI(ProgressUI);
            System.Threading.Thread CompileThread = new System.Threading.Thread(() =>
            {
                _ButtonBuild.SetText("Build (Compiling...)");
                Progress.SetStep(0, "Compile project...", 1);
                Progress.SetItem(0, "Compiling...");

                // Doing Asynchronous compilation ...
                Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Information, "Builder", "CompileTask", 0, string.Format("Started asynchronous compile task on the background."));

                Cache.CompileTaskState = ECompileTaskState.InProgress;

                var BatchFileSubPath = string.Format("{0}.{1}", "Scripts/Compile", SceneRuntime.IsUnix ? "sh" : "bat");
                var BatchFilePath = string.Format("{0}/{1}", DirectoryHelper.GetExecutableDirectory(), BatchFileSubPath);
                var Process = CompileProcess = new Process();
                Process.EnableRaisingEvents = true;

                Process.Exited += (object sender, EventArgs s) =>
                {
                    if (Process.ExitCode != 0)
                    {
                        Cache.CompileTaskState = ECompileTaskState.Failed;
                        _ButtonBuild.SetText("Build (Failed)");
                        Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, "Builder", "CompileTask", 1, string.Format("Compile failed. get error return code {0}", Process.ExitCode));
                    }
                    else
                    {
                        Cache.CompileTaskState = ECompileTaskState.Succeed;
                        _ButtonBuild.SetText("Build (Succeed)");
                        Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Information, "Builder", "CompileTask", 0, string.Format("Compile succeed. binary storage in \"{0}/bin\".", Options.StagingFolder));
                    }

                    Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Information, "Builder", "CompileTask", 0, string.Format("Compile task log was stored in \"{0}/Compile.log\".", Options.StagingFolder));
                    CompileProcess = null;
                };

                Process.StartInfo.FileName = BatchFilePath;
                Process.StartInfo.CreateNoWindow = false;
                Process.StartInfo.UseShellExecute = false;
                Process.StartInfo.RedirectStandardOutput = false;
                Process.StartInfo.RedirectStandardInput = false;
                Process.StartInfo.WorkingDirectory = Path.GetDirectoryName(BatchFilePath);
                Process.StartInfo.Arguments = string.Format("\"{0}\" \"{1}\" \"{2}\"", Options.StagingFolder, Options.BuildType, Options.ClientName);
                Process.Start();
                Process.WaitForExit(2000); // 2 seconds
                Progress.SetItem(1, "Done.");
                Progress.WaitForAWhile();
                Progress.Done();
                Progress.WaitForAWhile();
                Progress.Close();
            });
            CompileThread.Start();
        }

        void OnButtonRunClicked(Button Sender)
        {
            Progress Progress = new Progress("Launch Progress", 1);
            ProgressUI ProgressUI = new ProgressUI();
            ProgressUI.Initialize(GetUIManager(), Progress, true);
            DialogUIManager.GetInstance().ShowDialogUI(ProgressUI);

            Builder.BuildOptions Options = Cache.Options;
            bool IsDirectRun = Cache.TaskID == -1;
            bool IsUseCache = Cache.TaskID != _ID && !IsDirectRun;

            if (IsDirectRun)
            {
                Options = new Builder.BuildOptions(
                    GetAssetPlatform(),
                    GetBuildType(),
                    GetClientName(),
                    GetStagingFolder(),
                    GetInitialWorld(),
                    null,
                    IsPackAllResources(),
                    IsDirectCopyResToBin(),
                    false,
                    false,
                    "",
                    "",
                    IsEnableFetchResources(),
                    null
                );
            }

            var ExecPath = SceneRuntime.IsUnix ?
                string.Format("{0}/bin/{1}/{2}", Options.StagingFolder, Options.BuildType, string.Format("{0}.app/Contents/MacOS/CrossGame", Options.ClientName)) :
                string.Format("{0}/bin/{1}/{2}", Options.StagingFolder, Options.BuildType, string.Format("{0}.exe", Options.ClientName));

            if (!File.Exists(ExecPath))
            {
                if (Cache.TaskID == -1)
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Genrate and Build project first please.");
                    return;
                }

                if (Cache.CompileTaskState == ECompileTaskState.InProgress)
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "A compilation task is in progress, please wait for it to complete.");
                    return;
                }

                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", string.Format("Not found exe file:\"{0}\" , try Generate and Build again.", ExecPath));
                return;
            }

            if (IsUseCache)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Seems you have not Generate and Build a new project, Start process will use cache.");
            }

            var LaunchThread = new System.Threading.Thread(
                () =>
                {
                    Progress.SetStep(0, "Start", 1);
                    Progress.SetItem(0, "Starting up...");
                    var Process = new Process();
                    Process.StartInfo.FileName = ExecPath;
                    Process.StartInfo.WorkingDirectory = Path.GetDirectoryName(ExecPath);
                    Process.Start();
                    Process.WaitForExit(3000); // 3 seconds
                    Progress.SetItem(1, "Done.");
                    Progress.WaitForAWhile();
                    Progress.Done();
                    Progress.WaitForAWhile();
                    Progress.Close();
                }
            );
            LaunchThread.Start();
        }

        void OnButtonCookDirClicked(Button Sender)
        {
            if (CompileProcess != null)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "A compilation task is in progress, please wait for it to complete.");
                return;
            }

            List<string> CheckedScenesAndCanvases = GetCheckedScenes();
            if (CheckedScenesAndCanvases.Count == 0)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Please choose one world at least.");
                return;
            }

            var EnginePath = EditorConfigManager.GetInstance().GetConfig<GlobalConfig>().EnginePath;
            if (!File.Exists(string.Format("{0}/CMakeLists.txt", EnginePath)))
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", string.Format("Incorrect engine path: \"{0}\", \nPlease set the correct path in the GlobalConfig panel.", EnginePath));
                return;
            }

            string StagingFolder = GetStagingFolder();
            SimpleDelegate OnFinished = () => ProcessHelper.OpenContainingFolder(StagingFolder);

            string SrcCookDir = GetSrcCookDir();
            if (SrcCookDir != null && !(SrcCookDir.StartsWith(MainUI.GetInstance().GetProjectDirectory()) || SrcCookDir.StartsWith(EnginePath)))
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", string.Format("Incorrect src path: \"{0}\", \nPlease set the correct path.", SrcCookDir));
                return;
            }

            string DesCookDir = GetDesCookDir();
            if (DesCookDir == null)
            {
                CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Tips", "Empty target path");
                return;
            }



            Cache.TaskID = _ID;
            Builder.BuildOptions Options = Cache.Options = new Builder.BuildOptions(
                GetAssetPlatform(),
                GetBuildType(),
                GetClientName(),
                StagingFolder,
                GetInitialWorld(),
                CheckedScenesAndCanvases,
                IsPackAllResources(),
                IsDirectCopyResToBin(),
                true,
                true,
                SrcCookDir,
                DesCookDir,
                IsEnableFetchResources(),
                OnFinished
            );

            Builder Builder = Builder.GetInstance();
            Progress Progress = Builder.StartBuild(Options);
            ProgressUI ProgressUI = new ProgressUI();
            ProgressUI.Initialize(GetUIManager(), Progress, true);
            ProgressUI.CancelEvent += (ProgressUI ProgressUI1) => { Builder.StopBuild(); };
            DialogUIManager.GetInstance().ShowDialogUI(ProgressUI);
        }
    }
}
