using CEngine;
using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CrossEditor
{
    public class Builder
    {
        public enum BuildType
        {
            Debug,
            Release,
            RelWithDebInfo,
            MinSizeRel
        }

        public readonly struct BuildOptions
        {
            public readonly AssetPlatform Platform;
            public readonly BuildType BuildType;
            public readonly string ClientName;
            public readonly string StagingFolder;
            public readonly string InitialWorld;
            public readonly List<string> CheckedScenes;
            public readonly bool IsPackAllResources;
            public readonly bool IsDirectCopyResToBin;
            public readonly bool Cooking;
            public readonly bool IsCookDirectory;
            public readonly string SrcCookDir;
            public readonly string DesCookDir;
            public readonly bool IsEnableFetchResources;
            public readonly SimpleDelegate OnFinished;
            public BuildOptions(
                AssetPlatform Platform,
                BuildType BuildType,
                string ClientName,
                string StagingFolder,
                string InitialWorld,
                List<string> CheckedScenes,
                bool IsPackAllResources,
                bool IsDirectCopyResToBin,
                bool Cooking,
                bool IsCookDirectory,
                string SrcCookDir,
                string DesCookDir,
                bool IsEnableFetchResources,
                SimpleDelegate OnFinished)
            {
                this.Platform = Platform;
                this.BuildType = BuildType;
                this.ClientName = ClientName;
                this.StagingFolder = StagingFolder;
                this.InitialWorld = InitialWorld;
                this.CheckedScenes = CheckedScenes;
                this.IsPackAllResources = IsPackAllResources;
                this.IsDirectCopyResToBin = IsDirectCopyResToBin;
                this.Cooking = Cooking;
                this.IsCookDirectory = IsCookDirectory;
                this.SrcCookDir = SrcCookDir;
                this.DesCookDir = DesCookDir;
                this.IsEnableFetchResources = IsEnableFetchResources;
                this.OnFinished = OnFinished;
            }
        }

        static Builder _Instance = new Builder();

        Thread _BuildThread;
        bool _ToExitThread = false;

        Texture _GameQRCodeTexture;

        long _Bytes;
        long _TotalBytes;
        long _TotalBytesExpected;

        public static Builder GetInstance()
        {
            return _Instance;
        }

        public Texture GetGameQRCodeTexture()
        {
            return _GameQRCodeTexture;
        }

        void CollectRelatedResources(List<string> CheckedScenes, List<string> StandardResources, List<string> EditorResources, List<string> RuntimeResources)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            string EngineResourceDirectory = ResourceDirectory + "/EngineResource";
            int ResourceDirectoryLength = ResourceDirectory.Length;
            Dictionary<string, Dictionary<string, string[]>> OutputScenes = AssetBuilder.BuildScenes(CheckedScenes, ProjectDirectory);
            SortedSet<string> Resources = new SortedSet<string>();
            foreach (var Pair in OutputScenes)
            {
                foreach (var SubPair1 in Pair.Value)
                {
                    foreach (var StandardPath in SubPair1.Value)
                    {
                        if (Resources.Contains(StandardPath) == false)
                        {
                            string EditorPath = EditorUtilities.StandardFilenameToEditorFilename(StandardPath);
                            StandardResources.Add(StandardPath);
                            EditorResources.Add(EditorPath);
                            Resources.Add(StandardPath);
                            RuntimeResources.Add(EditorPath);
                        }
                    }
                }
            }
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(EngineResourceDirectory, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorPath = DirectoryWalkItem.Path;
                    string StandardPath = EditorPath.Substring(ResourceDirectoryLength + 1);
                    if (Resources.Contains(StandardPath) == false)
                    {
                        string Extension = PathHelper.GetExtensionOfPath(StandardPath);
                        if (StringHelper.IgnoreCaseEqual(Extension, "nda") ||
                            StringHelper.IgnoreCaseEqual(Extension, "ttf") ||
                            StringHelper.IgnoreCaseEqual(Extension, "js"))
                        {
                            StandardResources.Add(StandardPath);
                            EditorResources.Add(EditorPath);
                            Resources.Add(StandardPath);
                        }
                    }
                }
            }
        }

        static void GatherStaticResources(List<string> Worlds, string ProjectDirectory, List<string> Output)
        {
            foreach (var World in Worlds)
            {
                string SceneFilePath = Path.Combine(ProjectDirectory, World);
                var AbsolutBlockPaths = new vector_string();
                CrossEngineApi.GetBlockPaths(SceneFilePath, AbsolutBlockPaths);
                var RelativeBlockPaths = new List<string>();
                foreach (var Path in AbsolutBlockPaths)
                {
                    var TmpPath = PathHelper.ToStandardForm(Path).Remove(0, ProjectDirectory.Length + 1);
                    RelativeBlockPaths.Add(TmpPath);
                }
                foreach (var Path in RelativeBlockPaths)
                {
                    AddResourcesDenpenciesRecursion(Path, World, Output);
                }
            }
        }

        static void AddResourcesDenpenciesRecursion(string Path, string Parent, List<string> Resources)
        {
            Path = PathHelper.ToStandardForm(Path);
            var ProjectDirectory = PathHelper.ToStandardForm(MainUI.GetInstance().GetProjectDirectory());
            var ResourcesDirectory = PathHelper.ToStandardForm(EditorUtilities.GetResourceDirectory());

            bool isFullPath(string P) => File.Exists(P);
            if (isFullPath(Path))
            {
                if (Path.IndexOf(ProjectDirectory) != -1)
                {
                    Path = PathHelper.ToStandardForm(Path).Remove(0, ProjectDirectory.Length + 1);
                }
                else if (Path.IndexOf(ResourcesDirectory) != -1)
                {
                    Path = PathHelper.ToStandardForm(Path).Remove(0, ResourcesDirectory.Length + 1);
                }
                else
                {
                    StackTrace ST = new StackTrace(true);
                    StackFrame SF = ST.GetFrame(0);
                    Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, SF.GetFileName(), SF.GetMethod().Name, SF.GetFileLineNumber(), string.Format("Unable to get relative path of the file \"{0}\" in \"{1}\"", Path, Parent));
                    return;
                }
            }
            else
            {
                bool IsProjectRes(string Res) => Res.Substring(0, 8) == "Contents";
                string FileName = string.Format("{0}/{1}", IsProjectRes(Path) ? ProjectDirectory : ResourcesDirectory, Path);
                if (!File.Exists(FileName))
                {
                    StackTrace ST = new StackTrace(true);
                    StackFrame SF = ST.GetFrame(0);
                    Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, SF.GetFileName(), SF.GetMethod().Name, SF.GetFileLineNumber(), string.Format("Found missing dependencies \"{0}\" in \"{1}\"", Path, Parent));
                    return;
                }
            }

            Resources.Add(Path);

            var _Deps = new vector_string();
            CrossEngineApi.GetResourceDependencies(Path, _Deps);
            foreach (var Dep in _Deps)
            {
                if (!Resources.Contains(Dep) && Dep != "")
                {
                    Resources.Add(Dep);
                    AddResourcesDenpenciesRecursion(Dep, Path, Resources);
                }
            }
        }

        static void AddResourcesDenpenciesRecursion(string ConfigName, List<DirectoryInfo> RequireDirsFull, List<DirectoryInfo> IgnoreDirsFull, List<FileInfo> IgnoreFilesFull, List<string> Resources)
        {
            foreach (var Dir in RequireDirsFull)
            {
                if (Dir.Exists && !IgnoreDirsFull.Exists(Element => Element.FullName == Dir.FullName))
                {
                    var Files = Dir.GetFiles();
                    foreach (var File in Files)
                    {
                        if (File.Exists && !IgnoreFilesFull.Exists(Element => Element.FullName == File.FullName))
                            AddResourcesDenpenciesRecursion(File.FullName, ConfigName, Resources);
                    }
                }

                var SubDirs = new List<DirectoryInfo>(Dir.GetDirectories());
                AddResourcesDenpenciesRecursion(ConfigName, SubDirs, IgnoreDirsFull, IgnoreFilesFull, Resources);
            }
        }

        public Progress StartBuild(BuildOptions Options)
        {
            _ToExitThread = false;
            _GameQRCodeTexture = null;
            int StepCount = (int)BuildSteps.Max1;
            Progress _Progress = new Progress("Build Progress", StepCount);
            _BuildThread = new Thread(() =>
            {
                Build(Options, _Progress);
            });
            _BuildThread.Start();
            return _Progress;
        }

        public void StopBuild()
        {
            _ToExitThread = true;
        }

        string GetPackagePath()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            string PackagePath = ProjectDirectory + "/Intermediate/Game.qapkg";
            return PackagePath;
        }

        void Build(BuildOptions Options, Progress Progress)
        {
            var ProjectDirectory = PathHelper.ToStandardForm(MainUI.GetInstance().GetProjectDirectory());
            var BuildDirectory = PathHelper.ToStandardForm(Options.StagingFolder);
            var ResourcesDirectory = PathHelper.ToStandardForm(EditorUtilities.GetResourceDirectory());
            var OverrideResourcesDirectory = ProjectDirectory;
            var AssetDirectory = BuildDirectory;

            const string ContentsName = "Contents";
            const string EngineResName = "EngineResource";

            EditorConfigManager.GetInstance().GetConfig<CookSettingManager>();

            // Delete old package
            if (_ToExitThread) return;
            {
                Progress.SetStep((int)BuildSteps.DeleteOldPackage, "Delete old package...", 1);
                Progress.SetItem(0, "Delete old package...");
                var PackagePath = GetPackagePath();
                var ZippedPackagePath = PackagePath + ".gz";
                FileHelper.DeleteFile(PackagePath);
                FileHelper.DeleteFile(ZippedPackagePath);
                Progress.SetItem(1, "Done.");
                Progress.WaitForAWhile();
            }

            if (_ToExitThread) return;
            if (Options.IsCookDirectory)
            {
                CookDirectory(Options.SrcCookDir, Options.DesCookDir, Options, Progress);

                Progress.Done();
                Progress.WaitForAWhile();
                Progress.Close();

                Options.OnFinished?.Invoke();
                return;
            }

            // Generate Project
            if (_ToExitThread) return;
            {
                Progress.SetStep((int)BuildSteps.GenerateProject, "Generate project...", 1);
                Progress.SetItem(0, "Building...");

                if (!DirectoryHelper.IsDirectoryExists(BuildDirectory)) DirectoryHelper.CreateDirectory(BuildDirectory);

                var BatchFileSubPath = string.Format("{0}.{1}", "Scripts/Generate", SceneRuntime.IsUnix ? "sh" : "bat");
                var BatchFilePath = string.Format("{0}/{1}", DirectoryHelper.GetExecutableDirectory(), BatchFileSubPath);
                var GenrateProcess = new Process();
                GenrateProcess.EnableRaisingEvents = true;
                GenrateProcess.Exited += (object sender, EventArgs s) =>
                {
                    if (GenrateProcess.ExitCode != 0)
                        Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, "Builder", "GenerateTask", 1, string.Format("Generate failed, get error return code {0}.", GenrateProcess.ExitCode));
                    else
                        Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Information, "Builder", "GenerateTask", 0, string.Format("Generate succeed, project was staging at {0}.", Options.StagingFolder));

                    Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Information, "Builder", "GenerateTask", 0, string.Format("Generate task log was stored in {0}/Generate.log.", Options.StagingFolder));
                };

                GenrateProcess.StartInfo.FileName = BatchFilePath;
                GenrateProcess.StartInfo.CreateNoWindow = false;
                GenrateProcess.StartInfo.UseShellExecute = false;
                GenrateProcess.StartInfo.RedirectStandardOutput = false;
                GenrateProcess.StartInfo.RedirectStandardInput = false;
                GenrateProcess.StartInfo.WorkingDirectory = Path.GetDirectoryName(BatchFilePath);

                var GlobalConfig = EditorConfigManager.GetInstance().GetConfig<GlobalConfig>();
                var EnginePath = GlobalConfig.EnginePath;
                var ANDROID_SDK = GlobalConfig.AndroidSDK;
                var ANDROID_NDK = GlobalConfig.AndroidNDK;
                var ClientName = Options.ClientName;
                var BuildType = Options.BuildType;
                var ArgsCommon = string.Format("\"{0}\" \"{1}\" \"{2}\" \"{3}\"", BuildDirectory, EnginePath, BuildType, ClientName);
                if (Options.Platform == AssetPlatform.MACOS || Options.Platform == AssetPlatform.IOS)
                {
                    var ARCH = "x64";
                    var TargetPlatform = Options.Platform == AssetPlatform.MACOS ? "macOS" : "iOS";
                    GenrateProcess.StartInfo.Arguments = string.Format("{0} {1} {2}", TargetPlatform, ArgsCommon, ARCH);
                    AssetDirectory = string.Format("{0}/GameAssets", BuildDirectory);

                    // For compatibility
                    if (!DirectoryHelper.IsDirectoryExists(AssetDirectory)) DirectoryHelper.CreateDirectory(AssetDirectory);

                    if (Options.IsDirectCopyResToBin)
                    {
                        if (Options.Platform == AssetPlatform.MACOS)
                        {
                            AssetDirectory = string.Format("{0}/bin/{1}/{2}.app/GameAssets", BuildDirectory, BuildType, ClientName);
                        }
                        else if (Options.Platform == AssetPlatform.IOS)
                        {
                            AssetDirectory = string.Format("{0}/bin/{1}-iphoneos/{2}.app/GameAssets", BuildDirectory, BuildType, ClientName);
                        }
                    }
                }
                else if (Options.Platform == AssetPlatform.WIN)
                {
                    var ARCH = "x64";
                    GenrateProcess.StartInfo.Arguments = string.Format("{0} {1} {2}", "Windows", ArgsCommon, ARCH);
                    AssetDirectory = string.Format("{0}/GameAssets", BuildDirectory);

                    // For compatibility
                    if (!DirectoryHelper.IsDirectoryExists(AssetDirectory)) DirectoryHelper.CreateDirectory(AssetDirectory);

                    if (Options.IsDirectCopyResToBin) AssetDirectory = string.Format("{0}/bin/{1}/GameAssets", BuildDirectory, BuildType);
                }
                else if (Options.Platform == AssetPlatform.ANDROID)
                {
                    var ARCH = "arm64-v8a";
                    GenrateProcess.StartInfo.Arguments = string.Format("{0} {1} {2} {3} {4}", "Android", ArgsCommon, ARCH, ANDROID_SDK, ANDROID_NDK);
                    AssetDirectory = string.Format("{0}/{1}", BuildDirectory, "Demo/app/src/main/assets/CrossEngine_Assets");
                }

                if (!DirectoryHelper.IsDirectoryExists(AssetDirectory)) DirectoryHelper.CreateDirectory(AssetDirectory);

                GenrateProcess.Start();
                GenrateProcess.WaitForExit(30000);
                Progress.SetItem(1, "Done.");
                Progress.WaitForAWhile();
            }

            if (_ToExitThread) return;

            if (Options.IsPackAllResources)
            {
                CookDirectory($"{ProjectDirectory}/{ContentsName}", $"{AssetDirectory}/{ContentsName}", Options, Progress);
                CookDirectory($"{ResourcesDirectory}/{EngineResName}", $"{AssetDirectory}/{EngineResName}", Options, Progress);
                CookDirectory($"{OverrideResourcesDirectory}/{EngineResName}", $"{AssetDirectory}/{EngineResName}", Options, Progress);
                Progress.WaitForAWhile();
            }
            else
            {
                Progress.SetStep((int)BuildSteps.PackAllResources, "Pack engine resources...", 1);
                Progress.SetItem(0, "Packing...");
                // Copy entire engine resource; need refactoring
                CookDirectory($"{ResourcesDirectory}/{EngineResName}", $"{AssetDirectory}/{EngineResName}", Options, Progress);
                CookDirectory($"{OverrideResourcesDirectory}/{EngineResName}", $"{AssetDirectory}/{EngineResName}", Options, Progress);
                Progress.SetItem(1, "Done.");
                Progress.WaitForAWhile();

                var RlativeStaticResources = new List<string>();
                // Analyzing dependency
                if (_ToExitThread) return;
                {
                    Progress.SetStep((int)BuildSteps.AnalyzeDependency, "Analyzing dependency...", 1);
                    Progress.SetItem(0, "Analyzing dependency...");
                    RlativeStaticResources.AddRange(Options.CheckedScenes);
                    GatherStaticResources(Options.CheckedScenes, ProjectDirectory, RlativeStaticResources);

                    // Gather Manually Required Resources
                    const string ConfigName = "CookConfig.json";
                    string CookConfigPath = string.Format("{0}/{1}", ProjectDirectory, ConfigName);
                    if (File.Exists(CookConfigPath))
                    {
                        JObject JsonObj;
                        string JsonText = File.ReadAllText(CookConfigPath, System.Text.Encoding.UTF8);
                        try
                        {
                            JsonObj = JObject.Parse(JsonText);
                        }
                        catch (JsonReaderException Error)
                        {
                            StackTrace ST = new StackTrace(true);
                            StackFrame SF = ST.GetFrame(0);
                            var Tips = $"{CookConfigPath} maybe not a valid Json;\n{Error.Message};";
                            Clicross.LogModuleUtil.LogModule_Log((int)LogLevel.Error, SF.GetFileName(), SF.GetMethod().Name, SF.GetFileLineNumber(), Tips);
                            Progress.Done();
                            Progress.WaitForAWhile();
                            Progress.Close();
                            OperationQueue.GetInstance().AddOperation(() =>
                            {
                                CommonDialogUI.ShowSimpleOKDialog(UIManager.GetMainUIManager(), "Tips", Tips);
                            });
                            return;
                        }

                        var Require = (JArray)JsonObj["Require"];
                        var RequireList = Require == null ? new List<string>() : Require.ToObject<List<string>>();

                        var Ignore = (JArray)JsonObj["Ignore"];
                        var IgnoreList = Ignore == null ? new List<string>() : Ignore.ToObject<List<string>>();
                        var IgnoreDirList = new List<DirectoryInfo>();
                        var IgnoreFileList = new List<FileInfo>();

                        var PathRoot = ProjectDirectory;
                        for (int i = 0; i < IgnoreList.Count; i++)
                        {
                            var FullPath = $"{PathRoot}/{IgnoreList[i]}";

                            if (Directory.Exists(FullPath))
                                IgnoreDirList.Add(new DirectoryInfo(FullPath));
                            else if (File.Exists(FullPath))
                                IgnoreFileList.Add(new FileInfo(FullPath));
                        }

                        var RequiredDirInfoList = new List<DirectoryInfo>();
                        for (int i = 0; i < RequireList.Count; i++)
                        {
                            var Required = RequireList[i];
                            var RequiredFullPath = $"{PathRoot}/{Required}";
                            if (Directory.Exists(RequiredFullPath))
                            {
                                var RequiredDirInfo = new DirectoryInfo(RequiredFullPath);

                                if (IgnoreDirList.Exists(Element => Element == RequiredDirInfo))
                                    continue;

                                RequiredDirInfoList.Add(RequiredDirInfo);
                            }
                            else if (File.Exists(RequiredFullPath))
                            {
                                var RequiredFileInfo = new FileInfo(RequiredFullPath);

                                if (IgnoreFileList.Exists(Element => Element == RequiredFileInfo))
                                    continue;

                                AddResourcesDenpenciesRecursion(RequiredFullPath, ConfigName, RlativeStaticResources);
                            }
                        }

                        AddResourcesDenpenciesRecursion(ConfigName, RequiredDirInfoList, IgnoreDirList, IgnoreFileList, RlativeStaticResources);
                    }

                    Progress.SetItem(1, "Done.");
                    Progress.WaitForAWhile();
                }

                // Cooking resources
                if (_ToExitThread) return;
                {
                    bool IsOverride(string Path) => File.Exists($"{OverrideResourcesDirectory}/{Path}");
                    string EngineResDirectory(string EngineRes) => IsOverride(EngineRes) ? OverrideResourcesDirectory : ResourcesDirectory;
                    string RootDirectory(string Path) => Path.Contains("EngineResource") ? EngineResDirectory(Path) : ProjectDirectory;

                    bool bOverWrite = true;
                    bool bShouldCook = Options.Cooking;
                    int Count = RlativeStaticResources.Count;
                    string Tips = bShouldCook ? "Cooking" : "Direct Copy";
                    Progress.SetStep((int)BuildSteps.CookResources, $"{Tips} resources...", Count);
                    for (int i = 0; i < Count; i++)
                    {
                        if (_ToExitThread) return;
                        string Resource = RlativeStaticResources[i];
                        string SourcePath = string.Format("{0}/{1}", RootDirectory(Resource), Resource);
                        string TargetPath = string.Format("{0}/{1}", AssetDirectory, Resource);

                        Progress.SetItem(i, string.Format("{0}: {1}...", Tips, Resource));

                        if (DirectoryHelper.IsDirectoryExists(SourcePath))
                        {
                            DirectoryHelper.CreateDirectory(TargetPath);
                            ExportDirectory(AssetDirectory, SourcePath, TargetPath, bOverWrite, Options.Platform, bShouldCook);
                        }
                        else if (FileHelper.IsFileExists(SourcePath))
                        {
                            ExportAsset(SourcePath, TargetPath, bOverWrite, Options.Platform, bShouldCook);
                        }
                    }
                    Progress.SetItem(Count, "Done.");
                    Progress.WaitForAWhile();
                }

            }

            // Copy config/script files
            if (_ToExitThread) return;
            {
                string ProjectContentsDirectory = $"{ProjectDirectory}/{ContentsName}";
                List<FileInfo> FileScripts = new List<FileInfo>();
                FileGet.GetFile(ProjectContentsDirectory, ".lua", FileScripts);
                FileGet.GetFile(ResourcesDirectory, ".lua", FileScripts);

                // Override
                {
                    string OverrideEngineResDirectory = $"{OverrideResourcesDirectory}/{EngineResName}";
                    List<FileInfo> OverrideFileScripts = new List<FileInfo>();
                    FileGet.GetFile(OverrideEngineResDirectory, ".lua", OverrideFileScripts);

                    foreach (var Res in OverrideFileScripts)
                    {
                        var ResRlativeName = Res.FullName.Substring(OverrideResourcesDirectory.Length);
                        var ResFileInfo = new FileInfo($"{ResourcesDirectory}/{ResRlativeName}");

                        var Finded = FileScripts.FindIndex((FileInfo F) => { return F.FullName == ResFileInfo.FullName; });

                        if (Finded >= 0)
                            FileScripts.RemoveAt(Finded);

                        FileScripts.Add(Res);
                    }
                }

                List<FileInfo> FileConfigs = new List<FileInfo>();
                FileConfigs.Add(new FileInfo($"{ProjectDirectory}/Project.png"));
                FileGet.GetFile(ResourcesDirectory, ".json", FileConfigs, false);
                FileGet.GetFile(ProjectDirectory, ".json", FileConfigs, false);

                var FileScriptsCount = FileScripts.Count;
                var FileConfigsCount = FileConfigs.Count;
                var Count = FileScriptsCount + FileConfigsCount;
                Progress.SetStep((int)BuildSteps.CopyMiscFiles, "Copy misc files...", Count);

                var index = 0;

                // Copy script file
                for (int i = 0; i < FileScriptsCount; i++)
                {
                    var Resource = FileScripts[i];
                    if (Resource.Exists)
                    {
                        string SourcePath = PathHelper.ToStandardForm(Resource.FullName);
                        Progress.SetItem(index++, string.Format("Copy: {0}...", SourcePath));
                        string RelativeFile(string Path) => Path.Remove(0, (Path.Contains(ProjectDirectory) ? ProjectDirectory.Length : ResourcesDirectory.Length) + 1);
                        string TargetPath = string.Format("{0}/{1}", AssetDirectory, RelativeFile(SourcePath));

                        if (!Directory.Exists(Path.GetDirectoryName(TargetPath)))
                            Directory.CreateDirectory(Path.GetDirectoryName(TargetPath));

                        Resource.CopyTo(TargetPath, true);
                    }
                }

                // Copy config files
                for (int i = 0; i < FileConfigsCount; i++)
                {
                    var Resource = FileConfigs[i];
                    if (Resource.Exists)
                    {
                        Progress.SetItem(index++, string.Format("Copy: {0}...", Resource.FullName));
                        string TargetPath = string.Format("{0}/{1}", AssetDirectory, Resource.Name);

                        if (!Directory.Exists(Path.GetDirectoryName(TargetPath)))
                            Directory.CreateDirectory(Path.GetDirectoryName(TargetPath));

                        Resource.CopyTo(TargetPath, true);
                    }
                }
                Progress.SetItem(Count, "Done.");
                Progress.WaitForAWhile();
            }

            // Configuration
            if (_ToExitThread) return;
            {
                Progress.SetStep((int)BuildSteps.Configuration, "Configuration...", 1);
                Progress.SetItem(0, "Configuration...");
                string ProjectConfigPath = string.Format("{0}/{1}", AssetDirectory, "/ProjectConfig.json");
                string ProjectConfigText = File.ReadAllText(ProjectConfigPath, System.Text.Encoding.UTF8);
                JObject ProjectConfigJson = JObject.Parse(ProjectConfigText);
                ProjectConfigJson["Launch"] = Options.InitialWorld;

                if (Options.IsEnableFetchResources)
                    ProjectConfigJson["FetchPath"] = ProjectDirectory;

                File.WriteAllText(ProjectConfigPath, Convert.ToString(ProjectConfigJson));
                Progress.SetItem(1, "Done.");
                Progress.WaitForAWhile();
            }

            Progress.Done();
            Progress.WaitForAWhile();
            Progress.Close();

            Options.OnFinished?.Invoke();
        }

        void CookDirectory(string SourceDirectory, string TargetDirectory, BuildOptions Option, Progress Progress)
        {
            DirectoryInfo folder = new DirectoryInfo(SourceDirectory);
            if (!folder.Exists) return;

            int fileCount = folder.GetFiles().Length;
            Progress.SetStep((int)BuildSteps.CookResources, string.Format("Cooking Directory: {0}", SourceDirectory), fileCount);
            // cook files
            int index = 0;
            foreach (FileInfo fileInfo in folder.GetFiles())
            {
                if (_ToExitThread) return;
                string sourcePath = fileInfo.FullName;
                string targetPath = PathHelper.CombinePath(TargetDirectory, fileInfo.Name);
                Progress.SetItem(++index, string.Format("Cooking File: {0}", fileInfo.Name));
                CookFile(sourcePath, targetPath, Option);
            }
            Progress.SetItem(fileCount, "Done.");
            // cook directory
            foreach (DirectoryInfo directoryInfo in folder.GetDirectories())
            {
                if (_ToExitThread) return;
                string sourcePath = directoryInfo.FullName;
                string targetPath = PathHelper.CombinePath(TargetDirectory, directoryInfo.Name);
                CookDirectory(sourcePath, targetPath, Option, Progress);
            }
        }

        void CookFile(string SourcePath, string TargetPath, BuildOptions Option)
        {
            // tag file may cause some error
            if (SourcePath.EndsWith(".tga"))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(TargetPath));
                File.Copy(SourcePath, TargetPath, true);
                return;
            }

            string targetDirctory = PathHelper.GetDirectoryName(TargetPath);
            if (!DirectoryHelper.IsDirectoryExists(targetDirctory))
                DirectoryHelper.CreateDirectory(targetDirctory);
            if (!Option.Cooking || !AssetCookerManager.Instance().CookAsset(PathHelper.ToStandardForm(SourcePath), PathHelper.ToStandardForm(TargetPath), Option.Platform))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(TargetPath));
                File.Copy(SourcePath, TargetPath, true);
            }
        }

        void ExportDirectory(string BuildDirectory, string SourcePath, string TargetPath, bool bOverWrite, AssetPlatform AssetPlatform, bool bShouldCook)
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            int ProjectDirectoryLength = ProjectDirectory.Length;
            DirectoryWalker DirectoryWalker = new DirectoryWalker();
            DirectoryWalker.WalkDirectory(SourcePath, true);
            int Count = DirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = DirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string EditorPath = DirectoryWalkItem.Path;
                    string StandardPath = EditorPath.Substring(ProjectDirectoryLength + 1);
                    string TargetPath1 = string.Format("{0}/{1}", BuildDirectory, StandardPath);
                    ExportAsset(EditorPath, TargetPath1, bOverWrite, AssetPlatform, bShouldCook);
                }
            }
        }

        void ExportAsset(string SourcePath, string TargetPath, bool bOverWrite, AssetPlatform AssetPlatform, bool bShouldCook)
        {
            string DirectoryName = PathHelper.GetDirectoryName(TargetPath);
            if (DirectoryHelper.IsDirectoryExists(DirectoryName) == false)
            {
                DirectoryHelper.CreateDirectory(DirectoryName);
            }
            if (bShouldCook)
            {
                bool bSuccess = AssetCookerManager.Instance().CookAsset(SourcePath, TargetPath, AssetPlatform);
                if (bSuccess)
                {
                    return;
                }
            }
            File.Copy(SourcePath, TargetPath, bOverWrite);
        }

        string CompressGame(string FileName)
        {
            FileInfo FileToCompress = new FileInfo(FileName);
            string GameZipPath = FileToCompress.FullName + ".gz";
            using (FileStream OriginalFileStream = FileToCompress.OpenRead())
            {
                if ((File.GetAttributes(FileToCompress.FullName) &
                   FileAttributes.Hidden) != FileAttributes.Hidden & FileToCompress.Extension != ".gz")
                {
                    using (FileStream CompressedFileStream = File.Create(GameZipPath))
                    {
                        using (GZipStream CompressionStream = new GZipStream(CompressedFileStream,
                           CompressionMode.Compress))
                        {
                            OriginalFileStream.CopyTo(CompressionStream);
                            CompressionStream.Close();
                        }
                        CompressedFileStream.Close();
                    }
                }
                OriginalFileStream.Close();
            }
            return GameZipPath;
        }

        void HandleUploadProgress(long Bytes, long TotalBytes, long TotalBytesExpected)
        {
            _Bytes = Bytes;
            _TotalBytes = TotalBytes;
            _TotalBytesExpected = TotalBytesExpected;
            Console.WriteLine("Bytes: {0} TotalBytes: {1} TotalBytesExpected: {2}", Bytes, TotalBytes, TotalBytesExpected);
        }

        void UploadGame(string AppID, Progress Progress, string GameZipPath)
        {
            int Type = 4;
            string Ticket = Login.GetInstance().GetTicket();
            string FileName = "game.qapkg";
            int PackageType = 3;
            string RequestUrl = string.Format("https://q.qq.com/ide/compile?appid={0}&type={1}&ticket={2}&fileName={3}&pkgType={4}",
                AppID, Type, Ticket, FileName, PackageType);

            Progress.SetStep((int)BuildSteps.Upload, "Uploading...", 1000);
            Progress.SetItem(0, "Uploading...");

            string UIN = Login.GetInstance().GetUIN();
            HttpClient HttpClient = new HttpClient();
            HttpClient.DefaultRequestHeaders.Add("Method", "Post");
            HttpClient.DefaultRequestHeaders.Add("x-uin", UIN);
            FileStream FileStream = File.OpenRead(GameZipPath);
            long FileLength = FileStream.Length;

            _Bytes = FileLength;
            _TotalBytes = FileLength;
            _TotalBytesExpected = FileLength;

            bool bResponseReceived = false;

            ProgressStreamContent ProgressStreamContent = new ProgressStreamContent(FileStream, CancellationToken.None);
            ProgressStreamContent.Progress = HandleUploadProgress;
            Task<HttpResponseMessage> Task = HttpClient.PostAsync(RequestUrl, ProgressStreamContent);
            Task.ContinueWith((Task<HttpResponseMessage> Task1) =>
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    FileStream.Close();
                    HttpResponseMessage HttpResponseMessage = Task1.Result;
                    if (HttpResponseMessage.IsSuccessStatusCode)
                    {
                        string ResponseContent = HttpResponseMessage.Content.ReadAsStringAsync().Result;
                        string Link = "";
                        dynamic Object = JsonConvert.DeserializeObject(ResponseContent);
                        try
                        {
                            Link = Object.data.link;
                        }
                        catch
                        {
                        }
                        Texture Texture = QRCodeHelper.GenerateQRCode(Link, 0xFF000000, 0xFFFFFFFF);
                        _GameQRCodeTexture = Texture;
                    }
                    bResponseReceived = true;
                });
            });

            while (true)
            {
                double Progress1 = _TotalBytes / (double)_TotalBytesExpected;
                int Progress2 = (int)(Progress1 * 1000);

                Progress.SetItem(Progress2, "Uploading...");

                if (_ToExitThread)
                {
                    return;
                }

                if (_TotalBytes >= _TotalBytesExpected)
                {
                    break;
                }
            }

            Progress.SetItem(1000, "Done.");
            Progress.WaitForAWhile();

            Progress.SetStep((int)BuildSteps.Check, "Checking...", 1);
            Progress.SetItem(0, "Checking...");

            while (true)
            {
                if (_ToExitThread)
                {
                    return;
                }

                if (bResponseReceived)
                {
                    break;
                }
            }

            Progress.SetItem(1, "Done.");
            Progress.WaitForAWhile();
        }
    }

    public static class FileGet
    {
        public static void GetFile(string Path, string Ext, List<FileInfo> Output, bool IncludeSubDirs = true)
        {
            if (!Directory.Exists(Path))
                return;

            DirectoryInfo DirInfo = new DirectoryInfo(Path);

            FileInfo[] Files = DirInfo.GetFiles();
            foreach (FileInfo F in Files)
            {
                if (Ext.ToLower().IndexOf(F.Extension.ToLower()) >= 0)
                    Output.Add(F);
            }

            if (IncludeSubDirs)
            {
                string[] Dirs = Directory.GetDirectories(Path);
                foreach (string D in Dirs)
                    GetFile(D, Ext, Output);
            }
        }

        public static void CopyDirectory(string SourcePath, string DestPath, bool CopySubDirs = true)
        {
            if (!Directory.Exists(SourcePath))
                return;

            if (!Directory.Exists(DestPath))
                Directory.CreateDirectory(DestPath);

            DirectoryInfo Dir = new DirectoryInfo(SourcePath);

            FileInfo[] Files = Dir.GetFiles();
            foreach (FileInfo File in Files)
            {
                string TempPath = Path.Combine(DestPath, File.Name);
                File.CopyTo(TempPath, true);
            }

            if (CopySubDirs)
            {
                DirectoryInfo[] Dirs = Dir.GetDirectories();
                foreach (DirectoryInfo Subdir in Dirs)
                {
                    string TempPath = Path.Combine(DestPath, Subdir.Name);
                    CopyDirectory(Subdir.FullName, TempPath, CopySubDirs);
                }
            }
        }
    }
}
