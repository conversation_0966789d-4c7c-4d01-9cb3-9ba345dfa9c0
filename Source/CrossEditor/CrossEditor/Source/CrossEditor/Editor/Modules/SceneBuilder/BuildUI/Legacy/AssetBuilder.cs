using CEngine;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Xml;

namespace CrossEditor
{
    // This class was imported from LevelEditor directly
    public class AssetBuilder
    {
        //resourceRoot: Absolute path for the src resource root. e.g.: D:/UGit/CrossEngine_Assets
        //outputFolder: Absolute path for the output folder. e.g.:D:/output/
        //srcScenes: List of absolute path for the XML scene files. e.g.: D:/UGit/CrossEngine_Assets/Contents/World/Sponza.world
        //           or relative paths from resource root: Contents/World/Sponza.world
        //assetPlatform:
        //  0.  Win
        //  1.  iOS
        //  2.  Android
        static public void ExportGame(string resourceRoot, string outputFolder, uint assetPlatform, List<string> srcScenes, List<string> srcUICanvas)
        {
            bool shouldCook = true;
            Dictionary<string, Dictionary<string, string[]>> outputScenes = BuildScenes(srcScenes, resourceRoot);

            List<string> resources = outputScenes
                .SelectMany(scene => scene.Value)
                .SelectMany(res => res.Value)
                .ToList();

            List<string> engineResource = GetEngineResource();

            List<string> resourcePath = new List<string>();
            List<string> resourceTargetPath = new List<string>();

            foreach (string resource in resources)
            {
                resourcePath.Add(Path.Combine(resourceRoot, resource));
                resourceTargetPath.Add(Path.Combine(outputFolder, resource));
            }

            foreach (string resource in engineResource)
            {
                resourcePath.Add(Path.Combine(GetResourceDirectory(), resource));
                resourceTargetPath.Add(Path.Combine(outputFolder, resource));
            }

            for (int i = 0; i < resourcePath.Count; i++)
            {
                string path = resourcePath[i];
                if (!Directory.Exists(path) && !File.Exists(path))
                {
                    Console.WriteLine("{0} not exists.", path);
                    continue;
                }

                // target path
                string targetPath = resourceTargetPath[i];

                // file or directory
                FileAttributes attr = File.GetAttributes(path);
                // export directory
                if (attr.HasFlag(FileAttributes.Directory))
                {
                    if (!Directory.Exists(targetPath))
                    {
                        Directory.CreateDirectory(targetPath);
                    }

                    foreach (string filePath in Directory.GetFiles(path, "*.*", SearchOption.AllDirectories))
                    {
                        ExportAsset(filePath, Path.Combine(targetPath, Path.GetFileName(filePath)), true, assetPlatform, shouldCook);
                    }
                }
                else // export specific file
                {
                    string dir = Path.GetDirectoryName(targetPath);
                    if (!Directory.Exists(dir))
                    {
                        Directory.CreateDirectory(dir);
                    }
                    ExportAsset(path, targetPath, true, assetPlatform, shouldCook);
                }

                // update progress
                var percentage = i * 100 / resourcePath.Count;
                Console.WriteLine(string.Format("cooking {0}, current progress {1}%({2}/{3})", Path.GetFileName(path), percentage, i, resourcePath.Count));

            }
        }

        //public static bool 

        public static Dictionary<string, Dictionary<string, string[]>> BuildScenes(List<string> scenes, string resourceRoot)
        {

            List<string> resources = new List<string>();
            Dictionary<string, Dictionary<string, string[]>> outputScenes = new Dictionary<string, Dictionary<string, string[]>>();

            resources.Add("project.png");
            resources.Add("EngineLaunch.json");
            resources.Add("ProjectConfig.json");

            resources.AddRange(scenes);
            foreach (var scene in scenes)
            {
                string _sceneFilePath = Path.Combine(resourceRoot, scene);
                var _absolutBlockPaths = new vector_string();
                CrossEngineApi.GetBlockPaths(_sceneFilePath, _absolutBlockPaths);
                var _relativeBlockPaths = new List<string>();
                foreach (var path in _absolutBlockPaths)
                {
                    var _tmpPath = path.Replace("\\", "/").Remove(0, resourceRoot.Length + 1);
                    _relativeBlockPaths.Add(_tmpPath);
                }
                resources.AddRange(_relativeBlockPaths);
                foreach (var path in _relativeBlockPaths)
                {
                    AddResourcesDenpenciesRecursion(path, resources);
                }

                string _sceneName = Path.GetFileNameWithoutExtension(_sceneFilePath);
                string[] _noDup = resources.Distinct().ToArray();
                var _updatePaths = new Dictionary<string, string[]>() { { "nda_files", _noDup } };
                outputScenes.Add(_sceneName, _updatePaths);
                resources.Clear();
            }

            return outputScenes;
        }

        static void AddResourcesDenpenciesRecursion(string path, List<string> resources)
        {
            var _deps = new vector_string();
            CrossEngineApi.GetResourceDependencies(path, _deps);
            foreach (var dep in _deps)
            {
                if (!resources.Contains(dep))
                {
                    resources.Add(dep);
                    AddResourcesDenpenciesRecursion(dep, resources);
                }
            }
        }

        static string GetResourceDirectory()
        {
            string ResourceDirectory = EditorUtilities.GetResourceDirectory();
            return ResourceDirectory;
        }

        public static List<string> GetEngineResource()
        {
            List<string> resources = new List<string>();

            string engineResPath = "EngineResource";
            string contentsFolder = Path.Combine(GetResourceDirectory(), engineResPath);

            FindFiles(contentsFolder, engineResPath, ref resources, delegate (string path) { return true; });

            return resources;
        }

        private delegate bool Predicate(string path);
        static private void FindFiles(string dirPath, string dir, ref List<string> fileList, Predicate predicate)
        {
            DirectoryInfo dirInfo = new DirectoryInfo(dirPath);
            if (dirInfo.Exists)
            {
                FileSystemInfo[] fileSysInfo = dirInfo.GetFileSystemInfos();
                foreach (var item in fileSysInfo)
                {
                    if (item is DirectoryInfo)
                    {
                        FindFiles(item.FullName, dir + "/" + item.Name, ref fileList, predicate);
                    }
                    else
                    {
                        string path = dir + "/" + item.Name;
                        if (predicate(path))
                        {
                            fileList.Add(path);
                        }

                    }
                }
            }
        }

        static void FindResource(XmlNode root, string resourceRoot, ref List<string> materialRes, ref List<string> meshRes, ref List<string> otherRes)
        {
            // TODO: Refactor, find a more generic mechanical
            if (root.Name == "component")
            {
                switch (root.Attributes["xsi:type"].Value)
                {
                    case "meshRendererComponentType":
                        string mesh = root.Attributes["mesh"].Value;
                        string material = root.Attributes["material"].Value;
                        AddResString(material, ref materialRes);

                        if (mesh.Contains(":"))
                        {
                            mesh = mesh.Substring(mesh.IndexOf(':') + 1);
                        }

                        AddResString(mesh, ref meshRes);

                        break;
                    case "particleComponentType":
                        string materialName = root.Attributes["materialName"].Value;
                        string textureName = root.Attributes["textureName"].Value;
                        string particleName = "Contents/SFX/" + root.Attributes["particleName"].Value + ".nda";
                        AddResString(materialName, ref materialRes);
                        AddResString(textureName, ref otherRes);
                        AddResString(particleName, ref otherRes);

                        break;
                    case "particleManageSystemComponentType":
                        string rendererMaterial = root.Attributes["rendererMaterial"].Value;
                        string emitterName = "Contents/SFX/" + root.Attributes["emitterName"].Value + ".nda";
                        AddResString(rendererMaterial, ref materialRes);
                        AddResString(emitterName, ref otherRes);

                        break;
                    case "skeletalMeshRendererComponentType":
                        string smesh = root.Attributes["smesh"].Value;
                        string mmaterial = root.Attributes["mmaterial"].Value;
                        AddResString(mmaterial, ref materialRes);

                        if (smesh.Contains(":"))
                        {
                            smesh = smesh.Substring(smesh.IndexOf(':') + 1);
                        }
                        AddResString(smesh, ref meshRes);

                        IntPtr pathStrPtrSMesh = NativateGetSkeletalMeshReferenceString(Path.Combine(resourceRoot, smesh));
                        SplitAddNativeStringPath(pathStrPtrSMesh, ref otherRes);
                        break;
                    case "animationComponentType":
                        string animation = root.Attributes["animation"].Value;
                        AddResString(animation, ref otherRes);

                        IntPtr pathStrPtrAnimator = NativeGetAnimationReferenceString(Path.Combine(resourceRoot, animation));
                        SplitAddNativeStringPath(pathStrPtrAnimator, ref otherRes);
                        break;
                    case "scriptComponentType":
                        var scriptPath = root.Attributes["scripts"].Value;

                        AddResString(scriptPath, ref otherRes);
                        break;
                    case "audioComponentType":
                        string audioPath = root.Attributes["audioPath"].Value;

                        AddResString(audioPath, ref otherRes);
                        break;
                    case "navMeshComponentType":
                        string navPath = root.Attributes["navMeshName"].Value;
                        AddResString(navPath, ref otherRes);
                        break;
                    default:
                        break;

                }

            }
            else
            {
                if (root.Name == "gameWorld")
                {
                    string lvlscriptPath = root.Attributes["script"].Value;
                    AddResString(lvlscriptPath, ref otherRes);
                }
                foreach (XmlNode node in root.ChildNodes)
                {
                    FindResource(node, resourceRoot, ref materialRes, ref meshRes, ref otherRes);
                }
            }

        }

        static void AddResString(string res_str, ref List<string> resources)
        {
            if (!resources.Contains(res_str) && !String.IsNullOrEmpty(res_str))
            {
                resources.Add(res_str);
            }
        }

        static void ExportAsset(string srcPath, string targetPath, bool overWrite, uint platform, bool cook)
        {
            if (cook && AssetCookerManager.Instance().CookAsset(srcPath, targetPath, (AssetPlatform)platform))
                return;
            File.Copy(srcPath, targetPath, overWrite);
        }

        static void SplitAddNativeStringPath(IntPtr pathStrPtr, ref List<string> resources)
        {
            string cp = Marshal.PtrToStringAnsi(pathStrPtr);
            String[] DependentResourceNames = cp.Split(':');

            foreach (string name in DependentResourceNames)
            {
                AddResString(name, ref resources);
            }
        }

        [DllImportAttribute("AssetPipeline", EntryPoint = "GetSkeletalMeshReferenceString", CallingConvention = CallingConvention.StdCall)]
        public static extern IntPtr NativateGetSkeletalMeshReferenceString(string assetPath);

        [DllImportAttribute("AssetPipeline", EntryPoint = "GetAnimationReferenceString", CallingConvention = CallingConvention.StdCall)]
        public static extern IntPtr NativeGetAnimationReferenceString(string assetPath);
    }
}
