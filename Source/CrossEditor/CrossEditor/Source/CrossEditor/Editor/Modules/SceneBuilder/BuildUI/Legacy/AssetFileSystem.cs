using EditorUI;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    class WXAssetEncoder
    {
        private static List<AssetFile> AssetFiles = new List<AssetFile>();
        private static List<string> AssetDirectories = new List<string>();

        private static void CreateFile(string Filename, long Start, long End, int Audio)
        {
            WXAssetEncoder.AssetFiles.Add(new AssetFile(Filename, Start, End, Audio));
            CreateDirectory(Path.GetDirectoryName(Filename));
        }

        private static void CreateDirectory(string FilePath)
        {
            if (FilePath == null)
            {
                return;
            }

            if (!WXAssetEncoder.AssetDirectories.Contains(FilePath))
            {
                WXAssetEncoder.CreateDirectory(Path.GetDirectoryName(FilePath));
                WXAssetEncoder.AssetDirectories.Add(FilePath);
            }
        }

        public static void Encode()
        {
            string ProjectDirectory = MainUI.GetInstance().GetProjectDirectory();
            int ProjectDirectoryLength = ProjectDirectory.Length;
            string BuildDirectory = Path.Combine(ProjectDirectory, "Intermediate/Build");
            string OutputPath = Path.Combine(ProjectDirectory, "Intermediate/wxgame");

            if (DirectoryHelper.IsDirectoryExists(OutputPath))
            {
                DirectoryHelper.DeleteDirectory(OutputPath);
            }
            DirectoryHelper.CreateDirectory(OutputPath);

            // Collect Build Resources
            List<string> AssetPaths = new List<string>();
            DirectoryWalker AssetDirectoryWalker = new DirectoryWalker();
            AssetDirectoryWalker.WalkDirectory(BuildDirectory, true);
            int Count = AssetDirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = AssetDirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string AssetPath = DirectoryWalkItem.Path;
                    AssetPaths.Add(AssetPath);
                }
            }

            FileStream PackedStream = File.Open(Path.Combine(OutputPath, "client.data"), FileMode.CreateNew);

            // Combine FileScript
            Count = AssetPaths.Count;
            long Offset = 0;
            for (int i = 0; i < Count; i++)
            {
                string AssetPath = AssetPaths[i];
                string TargetPath = string.Format("/CrossEngine_Assets/{0}", AssetPath.Substring(ProjectDirectoryLength + 1));
                long FileSize = FileHelper.GetFileSize(AssetPath);
                WXAssetEncoder.CreateFile(TargetPath, Offset, Offset + FileSize, 0);
                File.Open(AssetPath, FileMode.Open).CopyTo(PackedStream);
                Offset += FileSize;
            }
            PackedStream.Close();
            PackedStream.Dispose();

            string FileScript = JsonConvert.SerializeObject(WXAssetEncoder.AssetFiles);

            // Combine PathScript
            List<string> PathScript = new List<string>();
            Count = WXAssetEncoder.AssetDirectories.Count;
            for (int i = 0; i < Count; ++i)
            {
                string AssetPath = WXAssetEncoder.AssetDirectories[i];
                string ParentPath = Path.GetDirectoryName(AssetPath);
                string CurrentPath = Path.GetFileName(AssetPath);
                if (ParentPath == null || StringHelper.IgnoreCaseEqual(CurrentPath, ""))
                {
                    continue;
                }
                PathScript.Add(string.Format("Module['FS_createPath']('{0}', '{1}', true, true);", ParentPath.Replace("\\", "/"), CurrentPath.Replace("\\", "/")));
            }

            // Generate client.js
            string TemplateScript = File.ReadAllText(Path.Combine(ProjectDirectory, "Resource/WASM/template.js"));
            AssetTemplate Template = new AssetTemplate(TemplateScript);
            Template.SetPackageName("client.data");
            Template.SetPathList(string.Join("\n", PathScript.ToArray()));
            Template.SetFileList(FileScript);
            Template.SetPackageSize(Offset.ToString());
            Template.SetBinaryPath("asset/client.wasm");
            FileHelper.WriteTextFile(Path.Combine(OutputPath, "client.js"), Template.GetCode());

            List<string> CopyList = new List<string>();
            CopyList.Add("fetch.js");
            CopyList.Add("config.js");
            CopyList.Add("game.json");
            CopyList.Add("weapp-adapter.js");
            CopyList.Add("asset/game.js");
            DirectoryHelper.CreateDirectory(Path.Combine(OutputPath, "asset"));
            for (int i = 0, l = CopyList.Count; i < l; ++i)
            {
                File.Copy(Path.Combine(ProjectDirectory, "Resource/WASM", CopyList[i]), Path.Combine(OutputPath, CopyList[i]), true);
            }

            // Generate game.js
            string[] FrameworkScript = {
                "EngineResource/UIAsset/js_basic_lib.js",
                "EngineResource/JS/ScriptProxy.js",
                "EngineResource/JS/script_jsb_builtin.js",
                "EngineResource/JS/SimpleCameraController.js",
                "EngineResource/UIAsset/js_flutter_basic_types.js",
                "EngineResource/UIAsset/js_flutter_framework.js",
                "EngineResource/UIAsset/js_flutter_layout.js",
                "EngineResource/UIAsset/js_flutter_text.js",
                "EngineResource/UIAsset/js_flutter_image.js",
                "EngineResource/UIAsset/js_flutter_animation.js",
                "EngineResource/UIAsset/js_flutter_material.js",
                "EngineResource/UIAsset/uiEngine.js"
            };
            FileStream GameStream = File.Open(Path.Combine(OutputPath, "game.js"), FileMode.CreateNew);
            string EngineResourcePath = EditorUtilities.GetResourceDirectory();
            for (int i = 0, l = FrameworkScript.Length; i < l; ++i)
            {
                // Write Framework Scripts
                FileStream ScriptStream = File.Open(Path.Combine(EngineResourcePath, FrameworkScript[i]), FileMode.Open);
                ScriptStream.CopyTo(GameStream);
                ScriptStream.Close();
                ScriptStream.Dispose();
            }

            // Collect Build Resources
            DirectoryWalker ScriptDirectoryWalker = new DirectoryWalker();
            ScriptDirectoryWalker.WalkDirectory(Path.Combine(BuildDirectory, "Contents"), true);
            Count = ScriptDirectoryWalker.GetDirectoryWalkItemCount();
            for (int i = 0; i < Count; i++)
            {
                DirectoryWalkItem DirectoryWalkItem = ScriptDirectoryWalker.GetDirectoryWalkItem(i);
                if (DirectoryWalkItem.bIsDirectory == false)
                {
                    string ScriptPath = DirectoryWalkItem.Path;
                    string Extension = PathHelper.GetExtensionOfPath(ScriptPath);
                    if (StringHelper.IgnoreCaseEqual(Extension, "js"))
                    {
                        FileStream ScriptStream = File.Open(ScriptPath, FileMode.Open);
                        ScriptStream.CopyTo(GameStream);
                        ScriptStream.Close();
                        ScriptStream.Dispose();
                    }
                }
            }

            // Write Template Script
            File.Open(Path.Combine(ProjectDirectory, "Resource/WASM/game.js"), FileMode.Open).CopyTo(GameStream);
            GameStream.Close();
            GameStream.Dispose();

            WXAssetEncoder.AssetFiles.Clear();
            WXAssetEncoder.AssetDirectories.Clear();
        }

    }

    class AssetFile
    {
        public string filename;
        public long start;
        public long end;
        public int audio;

        public AssetFile(string Filename, long Start, long End, int Audio)
        {
            this.filename = Filename;
            this.start = Start;
            this.end = End;
            this.audio = Audio;
        }
    }

    class AssetTemplate
    {

        string Code = "";
        public AssetTemplate(string Template)
        {
            Code = Template;
        }

        public void SetPackageName(string PackageName)
        {
            Code = Code.Replace("{{PACKAGE_NAME}}", PackageName);
        }

        public void SetPathList(string PathList)
        {
            Code = Code.Replace("{{PATH_LIST}}", PathList);
        }

        public void SetFileList(string FileList)
        {
            Code = Code.Replace("{{FILE_LIST}}", FileList);
        }

        public void SetPackageSize(string PackageSize)
        {
            Code = Code.Replace("{{PACKAGE_SIZE}}", PackageSize);
        }

        public void SetBinaryPath(string BinaryPath)
        {
            Code = Code.Replace("{{BINARY_PATH}}", BinaryPath);
        }

        public string GetCode()
        {
            return this.Code;
        }

    }
}