using EditorUI;

namespace CrossEditor
{
    class LoginUI : DialogUI
    {
        Label _LabelTips;
        Panel _PanelQRCode;
        Label _LabelMessage;

        SimpleDelegate _OnLogined;

        public LoginUI()
        {

        }

        public void Initialize(UIManager UIManager, SimpleDelegate OnLogined)
        {
            base.Initialize(UIManager, "Login", 720, 440);
            _OnLogined = OnLogined;

            _LabelTips = new Label();
            _LabelTips.SetPosition(50, 60, 620, 20);
            _LabelTips.SetFontSize(16);
            _LabelTips.SetTextAlign(TextAlign.CenterLeft);
            _LabelTips.SetText("Scan this QRCode by mobile QQ to login (authorized QQ accounts only):");
            _PanelDialog.AddChild(_LabelTips);

            _PanelQRCode = new Panel();
            _PanelQRCode.SetPosition(210, 90, 300, 300);
            _PanelDialog.AddChild(_PanelQRCode);

            _LabelMessage = new Label();
            _LabelMessage.SetPosition(50, 400, 620, 20);
            _LabelMessage.SetFontSize(16);
            _LabelMessage.SetTextAlign(TextAlign.CenterLeft);
            _LabelMessage.SetText("Success message or failure messages.");
            _PanelDialog.AddChild(_LabelMessage);

            Login Login = Login.GetInstance();
            Login.GetLoginCode(OnLoginCodeGenerated: () =>
            {
                Texture QRCodeTexture = Login.GetQRCodeTexture();
                _PanelQRCode.SetImage(QRCodeTexture);
                Login.StartPollingCountDown();
            });
        }

        public override void Update()
        {
            Login Login = Login.GetInstance();
            Login.Update();
            string Message = Login.GetMessage();
            _LabelMessage.SetText(Message);
            string Ticket = Login.GetTicket();
            if (Ticket != "")
            {
                OperationQueue.GetInstance().AddOperation(() =>
                {
                    CloseDialog();
                    if (_OnLogined != null)
                    {
                        _OnLogined();
                    }
                });
            }
        }

        public static void ShowLoginUI(SimpleDelegate OnLogined)
        {
            if (Login.GetInstance().IsLogined() == false)
            {
                LoginUI LoginUI = new LoginUI();
                LoginUI.Initialize(UIManager.GetMainUIManager(), OnLogined);
                DialogUIManager.GetInstance().ShowDialogUI(LoginUI);
            }
            else
            {
                if (OnLogined != null)
                {
                    OnLogined();
                }
            }
        }
    }
}
