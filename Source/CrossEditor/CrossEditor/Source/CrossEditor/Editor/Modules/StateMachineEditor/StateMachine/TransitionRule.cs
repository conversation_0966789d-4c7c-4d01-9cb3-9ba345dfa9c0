using CEngine;
using EditorUI;

namespace CrossEditor
{
    public class TransitionRule : Node
    {
        public Transition Transition;

        public int CenterX;
        public int CenterY;

        public const int Interval = 10;
        public const int Radius = 8;
        public const int Size = 20;
        public const int SegmentCount = 20;

        public int _Priority;

        [PropertyInfo(PropertyType = "Auto", ToolTips = "The Priority of Current Transition Rule")]
        public int Priority
        {
            get => _Priority;
            set => _Priority = value;
        }

        [PropertyInfo(bHide = true)]
        public new string NodeName
        {
            get => Name;
        }

        [PropertyInfo(bHide = true)]
        public TransitionRuleGraph TransitionRuleGraph
        {
            get => SubGraph as TransitionRuleGraph;
        }

        public TransitionRule()
        {
            Name = "Transition Rule";
            _Priority = 1;
            bOperable = false;

            SetSubGraph(new TransitionRuleGraph());

            RenameEvent = () =>
            {
                // Do nothing
            };
        }

        public void SetTransition(Transition Transition)
        {
            this.Transition = Transition;
            SubGraph.Name = string.Format("{0} to {1}(Rule)", Transition.OutSlot.Node.Name, Transition.InSlot.Node.Name);
        }

        public override object HitTest(int WorldX, int WorldY)
        {
            if (!(Transition.OutSlot.Node is StateEntryNode) && UIManager.PointInRect(WorldX, WorldY, CenterX - Size / 2, CenterY - Size / 2, Size, Size))
            {
                return this;
            }
            return null;
        }

        public override void Draw(UIManager UIManager)
        {
            if (!GetVisibility())
            {
                return;
            }
            GraphicsHelper GraphicsHelper = GraphicsHelper.GetInstance();

            Color Color = Color.FromRGBA(0, 0, 0, 160);
            GraphicsHelper.FillRectangle(UIManager, Color, X, Y, Size, Size);

            if (bSelected)
            {
                GraphicsHelper.DrawRectangle(UIManager, Color.FromRGB(255, 204, 0), X, Y, Size, Size);
            }

            Color Color1 = Color.FromRGB(105, 105, 105);
            GraphicsHelper.DrawCircleF(UIManager, Color1, CenterX, CenterY, Radius, Radius, SegmentCount);
        }

        public string ToExpression()
        {
            return TransitionRuleGraph.ToExpression();
        }

        public override bool RectInRect(int X, int Y, int Width, int Height)
        {
            return UIManager.RectInRect(X, Y, Width, Height, CenterX - Size / 2, CenterY - Size / 2, Size, Size);
        }
    }
}
