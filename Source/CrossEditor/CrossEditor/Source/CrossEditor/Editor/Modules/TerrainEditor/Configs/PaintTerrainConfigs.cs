using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    class PaintTerrainConfigs
    {
        static PaintTerrainConfigs _Instance = new PaintTerrainConfigs();

        public static PaintTerrainConfigs GetInstance()
        {
            return _Instance;
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush radius")]
        public float Radius
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationRadius;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationRadius = value;
            }
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush strength")]
        public float Strength
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationStrength;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationStrength = value;
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable target terrain layer weight value")]
        public bool EnableTargetWeight
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationEnableTargetWeight;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationEnableTargetWeight = value;
            }
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Target terrain layer weight value")]
        public float TargetWeightValue
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationTargetWeightValue;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationTargetWeightValue = value;
            }
        }

        [PropertyInfo(PropertyType = "EnumWithTab", ToolTips = "Terrain falloff type")]
        public TerrainFalloffType TerrainFalloffType
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainFalloffType;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainFalloffType = value;
            }
        }

        [PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Terrain brush falloff")]
        public float Falloff
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainOperationFalloff;
            }
            set
            {
                TerrainEditor.GetInstance()._TerrainOperationFalloff = value;
            }
        }

        [PropertyInfo(PropertyType = "SelectionList", ChildPropertyType = "Struct", ToolTips = "Terrain layer.",
            FileTypeDescriptor = "Layer Files#nda", ObjectClassID1 = ClassIDType.CLASS_Texture)]
        public List<TerrainLayer> Layers
        {
            get
            {
                return TerrainEditor.GetInstance()._TerrainLayers;
            }
            set
            {
                TerrainEditor TerrainEditor = TerrainEditor.GetInstance();
                TerrainEditor._TerrainLayers = value;
                TerrainEditor.UpdateTerrainLayers();
                TerrainEditor.SetTerrainDirty();
            }
        }
    }
}
