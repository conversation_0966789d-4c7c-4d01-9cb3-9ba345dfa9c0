using CEngine;
using System;

namespace CrossEditor
{
    class TerrainTraverser
    {
        public delegate bool Delegate_Cull(Double3 Center, Double3 Extent);
        public delegate void Delegate_Traverse(TileIndex TileIndex);

        Delegate_Cull _CullFunction;
        Delegate_Traverse _TraverseFunction;

        Entity _TerrainEntity;

        ulong _TerrainEntityID;
        IntPtr _WorldPtr;
        uint _GridSizeX;
        uint _GridSizeY;
        uint _BlockSize;
        uint _TotalLevels;

        double _TerrainPositionX;
        double _TerrainPositionZ;
        double _TerrainScalingX;
        double _TerrainScalingZ;

        public TerrainTraverser(Entity TerrainEntity, Delegate_Cull CheckFunction, Delegate_Traverse TraverseFunction)
        {
            _TerrainEntity = TerrainEntity;
            _CullFunction = CheckFunction;
            _TraverseFunction = TraverseFunction;

            _TerrainEntityID = TerrainEntity.EntityID;
            EditorScene EditorScene = EditorScene.GetInstance();
            _WorldPtr = EditorScene.GetWorld().GetNativePointer();
            TerrainInfo TerrainInfo = TerrainSystemG.GetTerrainInfo(_WorldPtr, _TerrainEntityID);
            _GridSizeX = TerrainInfo.mGridSizeX;
            _GridSizeY = TerrainInfo.mGridSizeY;
            _BlockSize = TerrainInfo.mBlockSize;
            _TotalLevels = CalculateLevels(_BlockSize);

            Transform Transform = _TerrainEntity.GetTransformComponent();

            Matrix4x4d WorldMatrix = new Matrix4x4d();
            Transform.GetWorldMatrix(ref WorldMatrix);

            Vector3d TerrainPosition = new Vector3d();
            WorldMatrix.GetPosition(out TerrainPosition);
            _TerrainPositionX = TerrainPosition.X;
            _TerrainPositionZ = TerrainPosition.Z;

            Vector3d TerrainScaling = new Vector3d();
            WorldMatrix.GetScale(out TerrainScaling);
            _TerrainScalingX = TerrainScaling.X;
            _TerrainScalingZ = TerrainScaling.Z;
        }

        uint CalculateLevels(uint BlockSize)
        {
            uint Levels = 0;
            while (BlockSize > 0)
            {
                BlockSize >>= 1;
                Levels++;
            }
            return Levels;
        }

        public void TraverseTerrainTiles()
        {
            for (uint GridX = 0; GridX < _GridSizeX; GridX++)
            {
                for (uint GridY = 0; GridY < _GridSizeY; GridY++)
                {
                    TraverseTerrainTiles_Block(GridX, GridY);
                }
            }
        }

        void TraverseTerrainTiles_Block(uint GridX, uint GridY)
        {
            uint TopLevel = _TotalLevels - 1;
            TileIndex TileIndex = new TileIndex(GridX, GridY, TopLevel, 0, 0);
            TraverseTerrainTiles_Tile(TileIndex);
        }

        void TraverseTerrainTiles_Tile(TileIndex TileIndex)
        {
            Double3 Center;
            Double3 Extent;
            TerrainEditor.GetTileBoundingBox(_WorldPtr, _TerrainEntityID, TileIndex, out Center, out Extent);
            Center.x = _TerrainPositionX + Center.x * _TerrainScalingX;
            Center.z = _TerrainPositionZ + Center.z * _TerrainScalingZ;
            Extent.x *= _TerrainScalingX;
            Extent.z *= _TerrainScalingZ;

            bool bVisible = _CullFunction(Center, Extent);
            if (bVisible)
            {
                _TraverseFunction(TileIndex);

                int Level = (int)TileIndex.mLevel;
                uint GridX = TileIndex.mBlockX;
                uint GridY = TileIndex.mBlockY;
                int NextLevel = Level - 1;
                if (NextLevel >= 0)
                {
                    uint BlockSizeInNextLevel = (uint)(1 << ((int)_TotalLevels - NextLevel - 1));
                    for (uint TileX = 0; TileX < BlockSizeInNextLevel; TileX++)
                    {
                        for (uint TileY = 0; TileY < BlockSizeInNextLevel; TileY++)
                        {
                            TileIndex TileIndexNextLevel = new TileIndex(GridX, GridY, (uint)NextLevel, TileX, TileY);
                            TraverseTerrainTiles_Tile(TileIndexNextLevel);
                        }
                    }
                }
            }
        }
    }
}
