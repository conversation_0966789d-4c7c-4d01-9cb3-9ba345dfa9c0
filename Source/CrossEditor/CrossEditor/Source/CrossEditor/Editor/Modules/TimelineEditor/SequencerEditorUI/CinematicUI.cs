using CEngine;
using EditorUI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace CrossEditor
{
    public class CinematicUI : TimelineEditorUI
    {
        public static new readonly string DockingCardName = "Cinematic";

        static readonly CinematicUI _Instance = new CinematicUI();
        public static new CinematicUI GetInstance() => _Instance;

        protected ComponentTrackHolder _ComponentTrackHolder = null;

        #region TopButton
        protected OperationBarUI OperationBarUI0;
        private Button _SaveButton;
        private Button _CameraButton;
        private Button _AutoButton;

        ToolBar _ToolBar;

        #endregion

        #region video
        protected OperationBarUI _OperationBarUI4;
        protected Camera _PreviewingCamera = null;
        protected Camera _CaptureCamera = null;
        protected RenderTexture _CaptureCameraRenderTexture = null;
        protected List<Entity> _CubemapCapturerList = null;
        private List<Entity> _PreviewCamerasList = new List<Entity>();
        Dictionary<Entity, decimal> _CamerasStartTimeDict = new Dictionary<Entity, decimal>();
        struct CameraSettings
        {
            public float sensorWidth;
            public float sensorHeight;
        }
        Dictionary<Entity, CameraSettings> _PreviewCameraSettingsDict = new Dictionary<Entity, CameraSettings>();
        const string VIEWPORT = "viewport", OneK = "1k", TwoK = "2k", FourK = "4k", Custom = "Custom";
        readonly Dictionary<string, Tuple<uint, uint>> _ResolutionPresets = new Dictionary<string, Tuple<uint, uint>> {
            { OneK, new Tuple<uint, uint>(1920, 1080) },
            { TwoK, new Tuple<uint, uint>(2560, 1440) },
            { FourK, new Tuple<uint, uint>(3840, 2160) },
            { Custom, new Tuple<uint, uint>(1920, 1080) },
        };
        private int _CustomWidth;
        private int _CustomHeight;
        private const int RESO_LIMIT_MIN = 360;
        private const int RESO_LIMIT_MAX = 7680;
        readonly Dictionary<string, uint> _CubemapResolution = new Dictionary<string, uint>
        {
            { OneK, 512 },
            { TwoK, 768 },
            { FourK, 1024 }
        };
        private string _CaptureResolution = VIEWPORT;
        private string _CaptureStagingFolder = null;
        private long _CaptureFrameIndex = 0;
        private long _TimelineFrameIndex = 0;
        private int _CaptureInterval = 1;
        private int _VideoFps = 24;
        private Button _GenerateVideoButtonManually;
        private Button _SetPathButton;
        private string _SetSavePath = null;
        private Label _SavePathLabel;
        private Button _SavePathButton;
        private Button _PinOutputDirButton;
        private Label _PinOutputDirLabel;
        private bool _PinnedOutput;
        private string _PinnedOutputDir = null;

        private Label _LabelCaptureInterval;
        private ComboBox _BoxCaptureInterval;
        private Check _CheckCaptureFrame;
        private Label _LabelCaptureFrame;
        private bool _hasCaptureTask = false;
        private ComboBox _BoxCaptureResolution;
        private Edit _CustomWidthEdit;
        private Edit _CustomHeightEdit;

        private ComboBox _BoxCaptureMode;
        private Label _LabelTrackMode;
        private ComboBox _BoxTrackMode;
        private Label _LabelFps;
#if false
        private ComboBox _ComboBoxUseRenderToTarget;
#endif
        enum VideoFps { FPS24 = 24, FPS25 = 25, FPS30 = 30, FPS60 = 60 };
        public enum EditStatus { EnterEdit = 1, ExitEdit = 2 }
        private ComboBox _BoxFps;
        enum CaptureMode { Flimlic = 0, Cubemap, Panoramic };
        enum TrackMode { Single = 0, Composite };
        enum CubeFace { Front = 0, Right, Back, Left, Top, Bottom };
        private CaptureMode _CaptureMode = CaptureMode.Flimlic;
        private TrackMode _TrackMode = TrackMode.Single;
        private short lagFrame = -1;
        private bool hasDeleteCameraTask = false;
        private List<Entity> toDeleteCaptureCameras = new List<Entity>();
        enum ImageSaveType { PNG = 0, BMP };
        private string[] _SaveTypeSuffix = { ".png", ".bmp" };
        private ImageSaveType _ImageSaveType;
        private ComboBox _BoxImageSaveType;
        #endregion
        protected Entity _Previewing = null;
        protected Entity PreviewEntity = null;
        protected Entity LevelSequenceEntity = null;

        protected List<Entity> _PreviewEntities = new List<Entity>();
        protected List<Entity> _DeleteEntities = new List<Entity>();
        protected List<Material> _CurModifyMaterials = new List<Material>();

        protected string _CurveCtrPath = "";
        protected EditStatus _EditStatus = EditStatus.ExitEdit;

        protected bool _DrawTrajectoryGizmo = false;

        public Dictionary<string, bool> ComponentNodes = new Dictionary<string, bool>();
        public Dictionary<string, Dictionary<string, bool>> PropertyNodes = new Dictionary<string, Dictionary<string, bool>>();

        protected long _CountDownRun;
        protected bool refresh;
        protected bool refreshMaterial;
        public bool IsNeedFocus = false;
        protected int Duration = 15;
        protected bool IsSelectTrackTree = false;
        protected decimal SaveEndValueSecond = 0;
        protected int SubSeqTrackIndex = 0;

        #region view items

        // operation bar ui items
        protected Button _TrackBtn;
        protected Label _CurveCtrFileName;
        protected Button _EntityOperationsBtn;
        protected Button _FPSBtn;
        protected EditWithProgress _TimeProgress;
        protected Label _PlayRateLabel;
        protected float _PlayRate;


        protected OperationBarUI _OperationBarUI2;
        TrackBar _PlayRateSlider;
        protected Edit _PlayRateEdit;

        protected Check _CheckCameraView;
        protected Label _LabelCameraView;

        // const values
        protected const int PROPERTY_FONT_SIZE = 20;
        TimeElapsedRingBuffer TimeElapsedRingBuffer = new TimeElapsedRingBuffer(25);

        #endregion

        public CinematicUI() : base()
        {
            Name = DockingCardName;
            _CountDownRun = 20;
            refresh = false;
        }

        public override bool Initialize()
        {
            base.Initialize();
            SubSeqTrackIndex = 0;
            // default component track holder
            TrackTree.ClearTrack();
            TrackTree.OnTrackItemClicked += (track) =>
            {
                IsSelectTrackTree = true;
                SelectTrack(track);
                DolocateEntity(track);
            };

            // lerp for scale cursor
            SetIsFrameLockOn(false);

            OperationBarUI0 = new OperationBarUI();
            OperationBarUI0.Initialize();

            _ToolBar = new ToolBar();
            _ToolBar.Initialize();
            _ToolBar.SetBackgroundColor(Color.FromRGB(36, 36, 36));
            _ToolBar.SetHeight(30);

            _ToolBar.AddSeperator();
            _SaveButton = _ToolBar.AddTool("Editor/Icons/File/Save.png");
            _SaveButton.SetToolTips("Save File");
            _SaveButton.ClickedEvent += SaveButtonClickedEvent;

            _CameraButton = _ToolBar.AddTool("Editor/Icons/Sequencer/CreateCamera.png");
            _CameraButton.SetToolTips("Create Camera");
            _CameraButton.ClickedEvent += CameraButtonClickedEvent;

            _AutoButton = _ToolBar.AddTool("Editor/Icons/Sequencer/AutoKey.png");
            _AutoButton.SetToolTips("Auto Key");
            _AutoButton.SetTagObject(false);
            _AutoButton.ClickedEvent += AutoButtonClickedEvent;

            _ToolBar.AddSeperator();
            _ViewContainer.AddFixedChild(_ToolBar);

            // entity operation bar for cinematic 
            _OperationBarUI.Reset();
            _OperationBarUI._bAutoResize = true;

            _TrackBtn = new Button();
            _TrackBtn.Initialize();
            _TrackBtn.SetFontSize(14);
            _TrackBtn.SetTextOffsetY(2);
            _TrackBtn.SetText("+ Track");
            _TrackBtn.SetEnable(true);
            _TrackBtn.SetSize(200, PROPERTY_FONT_SIZE);
            _TrackBtn.SetBorderColor(Color.FromRGB(130, 130, 130));
            _TrackBtn.SetNormalColor(Color.FromRGB(94, 94, 94));
            _TrackBtn.SetHoverColor(Color.FromRGB(130, 130, 130));
            _TrackBtn.SetDownColor(Color.FromRGB(81, 81, 81));
            _TrackBtn.ClickedEvent += OnButtonAddTrackClicked;
            _OperationBarUI.AddLeft(_TrackBtn);

            _SearchUI = new SearchUI();
            _SearchUI.Initialize();
            _SearchUI.GetPanelBack().SetEnable(false);
            _SearchUI.GetPanelBack().SetPos(0, 2);
            _SearchUI.GetPanelBack().SetWidth(130);
            _SearchUI.GetPanelBack().SetSize(200, PROPERTY_FONT_SIZE);
            _OperationBarUI.AddLeft(_SearchUI.GetPanelBack());

            Panel TimePanel = new Panel();
            TimePanel.Initialize();
            _TimeProgress = new EditWithProgress(TimePanel);
            _TimeProgress.SetStep(new decimal(0.01));
            _TimeProgress.SetPosition(0, 0, 200, PROPERTY_FONT_SIZE);
            TimePanel.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _TimeProgress.SetText("0.00");
            _TimeProgress.TextChangedEvent += OnTimeProgressTextChanged;
            TimePanel.SetSize(200, PROPERTY_FONT_SIZE);
            _OperationBarUI.AddLeft(TimePanel);

            _OperationBarUI1 = new OperationBarUI();
            _OperationBarUI1.Initialize();
            _OperationBarUI1.Reset();
            _OperationBarUI1._bAutoResize = true;

            _CurveCtrFileName = new Label();
            _CurveCtrFileName.Initialize();
            _CurveCtrFileName.SetFontSize(12);
            _CurveCtrFileName.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _CurveCtrFileName.SetText("New CurveCtr");
            _CurveCtrFileName.SetSize(200, PROPERTY_FONT_SIZE);
            _CurveCtrFileName.SetTextAlign(TextAlign.CenterCenter);
            _OperationBarUI1.AddLeft(_CurveCtrFileName);


            _EntityOperationsBtn = new Button();
            _EntityOperationsBtn.Initialize();
            _EntityOperationsBtn.SetFontSize(12);
            _EntityOperationsBtn.SetText("...");
            _EntityOperationsBtn.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _EntityOperationsBtn.SetToolTips("Operations");
            _EntityOperationsBtn.SetSize(200, PROPERTY_FONT_SIZE);
            _EntityOperationsBtn.ClickedEvent += OnEntityMoreOperationsClicked;
            _OperationBarUI1.AddLeft(_EntityOperationsBtn);

            _FPSBtn = new Button();
            _FPSBtn.Initialize();
            _FPSBtn.SetFontSize(12);
            _FPSBtn.SetText("Show Second");
            _FPSBtn.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _FPSBtn.SetSize(200, PROPERTY_FONT_SIZE);
            _FPSBtn.ClickedEvent += (Button Sender) =>
            {
                ShowFPSMenuImpl(_FPSBtn.GetScreenX(), _FPSBtn.GetScreenY() + _FPSBtn.GetHeight());
            };
            _OperationBarUI1.AddLeft(_FPSBtn);

            _ViewContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            _ViewContainer.AddFixedChild(_OperationBarUI1.GetPanelBar());
            _ViewContainer.AddSizableChild(TrackTree.GetScrollView(), 1f);

            _OperationBarUI2 = new OperationBarUI();
            _OperationBarUI2.Initialize();

            #region preview
            _LabelCameraView = new Label();
            _LabelCameraView.Initialize();
            _LabelCameraView.SetFontSize(12);
            _LabelCameraView.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelCameraView.SetText("Preview Camera");
            _LabelCameraView.SetSize(100, PROPERTY_FONT_SIZE);
            _LabelCameraView.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI2.AddLeft(_LabelCameraView);

            _CheckCameraView = new Check();
            _CheckCameraView.Initialize();
            _CheckCameraView.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckCameraView.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckCameraView.SetAutoCheck(true);
            _CheckCameraView.SetEnable(true);
            _CheckCameraView.SetChecked(false);
            _CheckCameraView.SetSize(50, PROPERTY_FONT_SIZE);
            _CheckCameraView.ClickedEvent += _CheckCameraView_ClickedEvent;
            _OperationBarUI2.AddLeft(_CheckCameraView);

            _PlayRateLabel = new Label();
            _PlayRateLabel.Initialize();
            _PlayRateLabel.SetFontSize(12);
            _PlayRateLabel.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _PlayRateLabel.SetText("Play Rate:");

            _PlayRateLabel.SetSize(70, PROPERTY_FONT_SIZE);
            _PlayRateLabel.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI2.AddLeft(_PlayRateLabel);

            _PlayRateEdit = new Edit();
            _PlayRateEdit.Initialize(EditMode.Simple_SingleLine);
            _PlayRateEdit.SetPos(0, 2);
            _PlayRateEdit.SetFontSize(12);
            _PlayRateEdit.SetText("1");
            _PlayRateEdit.SetTextAlign(TextAlign.CenterLeft);
            _PlayRateEdit.SetSize(50, PROPERTY_FONT_SIZE - 4);
            _PlayRateEdit.TextChangedEvent += OnPlayRateEditTextChanged;
            _OperationBarUI2.AddLeft(_PlayRateEdit);

            _PlayRateSlider = new TrackBar();
            _PlayRateSlider.Initialize();
            _PlayRateSlider.SetPos(0, 2);
            _PlayRateSlider.SetValue(1.0f);
            _PlayRateSlider.SetSize(200, PROPERTY_FONT_SIZE - 4);
            _PlayRateSlider.ValueChangedEvent += OnPlayRateSliderChanged;
            _OperationBarUI2.AddLeft(_PlayRateSlider);

            var emptyLabel = new Label();
            emptyLabel.Initialize();
            emptyLabel.SetFontSize(12);
            emptyLabel.SetSize(30, PROPERTY_FONT_SIZE);
            emptyLabel.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI2.AddLeft(emptyLabel);

            _BoxCaptureResolution = new ComboBox();
            _BoxCaptureResolution.Initialize();
            _BoxCaptureResolution.SetFontSize(12);
            _BoxCaptureResolution.SetSize(85, PROPERTY_FONT_SIZE);
            _BoxCaptureResolution.AddItem(VIEWPORT);
            _BoxCaptureResolution.AddItem(OneK);
            _BoxCaptureResolution.AddItem(TwoK);
            _BoxCaptureResolution.AddItem(FourK);
            _BoxCaptureResolution.AddItem(Custom);
            _BoxCaptureResolution.ItemSelectedEvent += OnResolutionChanged;
            _BoxCaptureResolution.SetSelectedItemIndex(0);
            _OperationBarUI2.AddLeft(_BoxCaptureResolution);

            var CustomWidth = new Label();
            CustomWidth.Initialize();
            CustomWidth.SetFontSize(12);
            CustomWidth.SetSize(40, PROPERTY_FONT_SIZE);
            CustomWidth.SetText("Width");
            CustomWidth.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI2.AddLeft(CustomWidth);

            _CustomWidthEdit = new Edit();
            _CustomWidthEdit.Initialize(EditMode.Simple_SingleLine);
            _CustomWidthEdit.SetPos(0, 2);
            _CustomWidthEdit.SetFontSize(12);
            _CustomWidthEdit.SetEnable(false);
            _CustomWidthEdit.SetText("1");
            _CustomWidthEdit.SetTextAlign(TextAlign.CenterLeft);
            _CustomWidthEdit.SetSize(50, PROPERTY_FONT_SIZE - 4);
            _CustomWidthEdit.SetTextColor(Color.EDITOR_UI_DISABLE_TEXT_COLOR);
            _CustomWidthEdit.TextChangedEvent += OnCustomWidthTextChanged;
            _OperationBarUI2.AddLeft(_CustomWidthEdit);

            var CustomHeight = new Label();
            CustomHeight.Initialize();
            CustomHeight.SetFontSize(12);
            CustomHeight.SetSize(40, PROPERTY_FONT_SIZE);
            CustomHeight.SetText("Height");
            CustomHeight.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI2.AddLeft(CustomHeight);

            _CustomHeightEdit = new Edit();
            _CustomHeightEdit.Initialize(EditMode.Simple_SingleLine);
            _CustomHeightEdit.SetPos(0, 2);
            _CustomHeightEdit.SetFontSize(12);
            _CustomHeightEdit.SetEnable(false);
            _CustomHeightEdit.SetText("1");
            _CustomHeightEdit.SetTextAlign(TextAlign.CenterLeft);
            _CustomHeightEdit.SetSize(50, PROPERTY_FONT_SIZE - 4);
            _CustomHeightEdit.SetTextColor(Color.EDITOR_UI_DISABLE_TEXT_COLOR);
            _CustomHeightEdit.TextChangedEvent += OnCustomHeightTextChanged;
            _OperationBarUI2.AddLeft(_CustomHeightEdit);

            _ViewContainer.AddFixedChild(_OperationBarUI2.GetPanelBar());
            #endregion

            #region video
            _OperationBarUI4 = new OperationBarUI();
            _OperationBarUI4.Initialize();
            _LabelCaptureFrame = new Label();
            _LabelCaptureFrame.Initialize();
            _LabelCaptureFrame.SetFontSize(12);
            _LabelCaptureFrame.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelCaptureFrame.SetText("Capture Frame");
            _LabelCaptureFrame.SetSize(100, PROPERTY_FONT_SIZE);
            _LabelCaptureFrame.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_LabelCaptureFrame);

            _CheckCaptureFrame = new Check();
            _CheckCaptureFrame.Initialize();
            _CheckCaptureFrame.SetImageUnchecked(UIManager.LoadUIImage("Editor/UI/Check/Unchecked.png"));
            _CheckCaptureFrame.SetImageChecked(UIManager.LoadUIImage("Editor/UI/Check/Checked.png"));
            _CheckCaptureFrame.SetAutoCheck(true);
            _CheckCaptureFrame.SetEnable(true);
            _CheckCaptureFrame.SetChecked(false);
            _CheckCaptureFrame.SetSize(50, PROPERTY_FONT_SIZE);
            _CheckCaptureFrame.ClickedEvent += _CheckCaptureFrame_ClickedEvent;
            _OperationBarUI4.AddLeft(_CheckCaptureFrame);

            _GenerateVideoButtonManually = new Button();
            _GenerateVideoButtonManually.Initialize();
            _GenerateVideoButtonManually.SetFontSize(12);
            _GenerateVideoButtonManually.SetImage(UIManager.LoadUIImage("Editor/Game/Camera.png"));
            _GenerateVideoButtonManually.ClickedEvent += _GenerateVideoButtonManually_ClickEvent;
            _GenerateVideoButtonManually.SetSize(20, PROPERTY_FONT_SIZE);
            _OperationBarUI4.AddLeft(_GenerateVideoButtonManually);

            _SetPathButton = new Button();
            _SetPathButton.Initialize();
            _SetPathButton.SetFontSize(12);
            _SetPathButton.SetImage(UIManager.LoadUIImage("Editor/Tree/Project/FoldedFolder.png"));
            _SetPathButton.ClickedEvent += _SetPathButton_ClickedEvent;
            _SetPathButton.SetSize(20, PROPERTY_FONT_SIZE);
            _OperationBarUI4.AddLeft(_SetPathButton);

            _SavePathButton = new Button();
            _SavePathButton.Initialize();
            _SavePathButton.SetFontSize(12);
            _SavePathButton.SetImage(UIManager.LoadUIImage("Editor/Tree/Project/Folder.png"));
            _SavePathButton.ClickedEvent += _SavePathButton_ClickedEvent;
            _SavePathButton.SetSize(20, PROPERTY_FONT_SIZE);
            _OperationBarUI4.AddLeft(_SavePathButton);

            _SavePathLabel = new Label();
            _SavePathLabel.Initialize();
            _SavePathLabel.SetFontSize(12);
            _SavePathLabel.SetSize(10, PROPERTY_FONT_SIZE);
            _SavePathLabel.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_SavePathLabel);

            _BoxCaptureMode = new ComboBox();
            _BoxCaptureMode.Initialize();
            _BoxCaptureMode.SetFontSize(12);
            _BoxCaptureMode.SetSize(75, PROPERTY_FONT_SIZE);
            _BoxCaptureMode.AddItem("Flimlic");
            _BoxCaptureMode.AddItem("Cubemap");
            _BoxCaptureMode.AddItem("Panoramic");
            _BoxCaptureMode.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                _CaptureMode = (CaptureMode)Index;
            };
            _BoxCaptureMode.SetSelectedItemIndex(0);
            _OperationBarUI4.AddLeft(_BoxCaptureMode);

            _LabelFps = new Label();
            _LabelFps.Initialize();
            _LabelFps.SetFontSize(12);
            _LabelFps.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelFps.SetText("Fps");
            _LabelFps.SetSize(40, PROPERTY_FONT_SIZE);
            _LabelFps.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_LabelFps);
            _BoxFps = new ComboBox();
            _BoxFps.Initialize();
            _BoxFps.SetFontSize(12);
            _BoxFps.SetSize(50, PROPERTY_FONT_SIZE);
            _BoxFps.AddItem("24");
            _BoxFps.AddItem("25");
            _BoxFps.AddItem("30");
            _BoxFps.AddItem("60");
            _BoxFps.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                var Arr = Enum.GetValues(typeof(VideoFps));
                _VideoFps = (int)Arr.GetValue(Index);
            };
            _BoxFps.SetSelectedItemIndex(0);
            _OperationBarUI4.AddLeft(_BoxFps);

            _LabelTrackMode = new Label();
            _LabelTrackMode.Initialize();
            _LabelTrackMode.SetFontSize(12);
            _LabelTrackMode.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelTrackMode.SetText("Track");
            _LabelTrackMode.SetSize(50, PROPERTY_FONT_SIZE);
            _LabelTrackMode.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_LabelTrackMode);
            _BoxTrackMode = new ComboBox();
            _BoxTrackMode.Initialize();
            _BoxTrackMode.SetFontSize(12);
            _BoxTrackMode.SetSize(100, PROPERTY_FONT_SIZE);
            _BoxTrackMode.AddItem("Single");
            _BoxTrackMode.AddItem("Composite");
            _BoxTrackMode.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                _TrackMode = (TrackMode)Index;
            };
            _BoxTrackMode.SetSelectedItemIndex(0);
            _OperationBarUI4.AddLeft(_BoxTrackMode);

            _LabelCaptureInterval = new Label();
            _LabelCaptureInterval.Initialize();
            _LabelCaptureInterval.SetFontSize(12);
            _LabelCaptureInterval.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _LabelCaptureInterval.SetText("Intervals");
            _LabelCaptureInterval.SetSize(70, PROPERTY_FONT_SIZE);
            _LabelCaptureInterval.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_LabelCaptureInterval);

            _BoxCaptureInterval = new ComboBox();
            _BoxCaptureInterval.Initialize();
            _BoxCaptureInterval.SetFontSize(12);
            _BoxCaptureInterval.SetSize(50, PROPERTY_FONT_SIZE);
            _BoxCaptureInterval.AddItem("1");
            _BoxCaptureInterval.AddItem("2");
            _BoxCaptureInterval.AddItem("4");
            _BoxCaptureInterval.AddItem("8");
            _BoxCaptureInterval.AddItem("16");
            _BoxCaptureInterval.AddItem("32");
            _BoxCaptureInterval.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                _CaptureInterval = 1 << Index;
            };
            _BoxCaptureInterval.SetSelectedItemIndex(0);
            _OperationBarUI4.AddLeft(_BoxCaptureInterval);
#if true
            _BoxImageSaveType = new ComboBox();
            _BoxImageSaveType.Initialize();
            _BoxImageSaveType.SetFontSize(12);
            _BoxImageSaveType.SetSize(50, PROPERTY_FONT_SIZE);
            _BoxImageSaveType.AddItem("png");
            _BoxImageSaveType.AddItem("bmp");
            _BoxImageSaveType.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                _ImageSaveType = (ImageSaveType)Index;
            };
            _ImageSaveType = ImageSaveType.BMP;
            _BoxImageSaveType.SetSelectedItemIndex(1);
            _OperationBarUI4.AddLeft(_BoxImageSaveType);
#else
            _ImageSaveType = ImageSaveType.BMP;
#endif

#if false
            _ComboBoxUseRenderToTarget = new ComboBox();
            _ComboBoxUseRenderToTarget.Initialize();
            _ComboBoxUseRenderToTarget.SetFontSize(12);
            _ComboBoxUseRenderToTarget.SetSize(100, PROPERTY_FONT_SIZE);
            _ComboBoxUseRenderToTarget.AddItem(".net capture");
            _ComboBoxUseRenderToTarget.AddItem("camera render2rt");
            _ComboBoxUseRenderToTarget.ItemSelectedEvent += (Sender) =>
            {
                int Index = Sender.GetSelectedItemIndex();
                useDotNet = Index == 0 ? true : false;
            };
            _ComboBoxUseRenderToTarget.SetSelectedItemIndex(1);
            _OperationBarUI4.AddLeft(_ComboBoxUseRenderToTarget);
#endif

            _PinOutputDirLabel = new Label();
            _PinOutputDirLabel.Initialize();
            _PinOutputDirLabel.SetFontSize(12);
            _PinOutputDirLabel.SetBorderColor(Color.EDITOR_UI_BUTTON_BORDER_COLOR);
            _PinOutputDirLabel.SetText("Pin Output");
            _PinOutputDirLabel.SetSize(70, PROPERTY_FONT_SIZE);
            _PinOutputDirLabel.SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI4.AddLeft(_PinOutputDirLabel);
            _PinOutputDirButton = new Button();
            _PinOutputDirButton.Initialize();
            _PinOutputDirButton.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrameGrey.png"));
            _PinOutputDirButton.SetEnable(true);
            _PinOutputDirButton.SetSize(PROPERTY_FONT_SIZE, PROPERTY_FONT_SIZE);
            _PinOutputDirButton.ClickedEvent += _PinOutputDirButton_ClickedEvent;
            _PinnedOutput = false;
            _OperationBarUI4.AddLeft(_PinOutputDirButton);

            _ViewContainer.AddFixedChild(_OperationBarUI4.GetPanelBar());

            #endregion
            // docking card concerns
            var DockingCard = GetDockingCard();
            DockingCard.SetText(DockingCardName);
            DockingCard.SetEnableDragDrop(true);
            DockingCard.CloseEvent += DockingCard_CloseEvent;

            // dragging manager concerns
            DragDropManager.GetInstance().DragEndEvent += OnDragDropManagerDragEnd;

            ButtonAddTrack.SetVisible(false);

            HoldingObjectMoveEvent += (Sender, HoldingObject) =>
            {
                if (HoldingObject is MoveHead)
                {
                    var Head = HoldingObject as MoveHead;

                    TimelineControl.SetIsPlaying(false);
                    bool bKeyFrame = false;

                    //Absorb
                    if (HoldingObject == PlayHead)
                    {
                        //Track.GetNearestKeyValue
                        foreach (var Item in TrackTree.GetTracks())
                        {
                            if (Item is ComponentTrack)
                            {
                                float Value = ((ComponentTrack)Item).GetNearestKeyValue();
                                if (Value != 0.0)
                                {
                                    Head.Value = (Decimal)Value;
                                    bKeyFrame = true;
                                }
                            }
                        }
                    }

                    //ScaleUI.GetNearestValue
                    if (!bKeyFrame)
                    {
                        decimal NearestGrid = _ScaleUI.GetNearestValue(Head.Value);
                        decimal HeadDistance = Math.Abs(Head.Value - NearestGrid);
                        if (HeadDistance < (decimal)_ScaleUI.GetUnit())
                        {
                            Head.Value = NearestGrid;
                            SetTimeProgress(Head, Head.Value);
                        }
                    }

                }
                else if (HoldingObject is KeyFrameMover)
                {
                    KeyFrameMover Mover = (KeyFrameMover)HoldingObject;
                    foreach (var KeyFrame in Mover.KeyFrames)
                    {
                        var playHeadScreenX = _ScaleUI.ValueToScreenX(PlayHead.Value);
                        var keyScreenX = _ScaleUI.ValueToScreenX(KeyFrame.GetKeyValue());

                        decimal Distance = Math.Abs(PlayHead.Value - KeyFrame.GetKeyValue());
                        bool bPlayHead = false;
                        if (Distance < (decimal)_ScaleUI.GetUnit())
                        {
                            KeyFrame.SetKeyValue(PlayHead.Value);
                            bPlayHead = true;
                        }

                        if (!bPlayHead)
                        {
                            decimal NearestGrid = _ScaleUI.GetNearestValue(KeyFrame.GetKeyValue());
                            decimal GridDistance = Math.Abs(KeyFrame.GetKeyValue() - NearestGrid);
                            if (GridDistance < (decimal)_ScaleUI.GetUnit())
                            {
                                KeyFrame.SetKeyValue(NearestGrid);
                            }
                        }
                    }
                }
            };

            TimelineControl.PlayStateChangeEvent += (Sender, NewState) =>
            {
                if (NewState)
                {
                    InitPreviewState();
                    InitCaptureState();
                }
                else
                {
                    FinishPreviewState();
                    FinishCaptureState();
                }

                if (!_CheckCameraView.GetChecked() && !EditorSceneUI.GetInstance().GetCameraView())
                {
                    EditorScene.GetInstance().SetSequencePlayState(false);
                    return;
                }

                EditorScene.GetInstance().SetSequencePlayState(NewState);

                if (NewState)
                {
                    OnBeginCapture();

                    CreateCaptureDirectory();
                }
            };

            TimelineControl.EditStateChangeEvent += (Sender, index) =>
            {
                if (index == (int)EditStatus.EnterEdit)
                {
                    ResetHead(true);
                }
                else if (index == (int)EditStatus.ExitEdit)
                {

                    RestoreSceneValue(true);
                }

            };

            LoadCinematicConfigFromFile();

            return true;
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            base.OnPositionChanged(Sender, bPositionChanged, bSizeChanged);
            OperationQueue.GetInstance().AddOperation(() =>
            {
                _MainContainer.OnPositionChanged(false, true);
            });
        }

        private void DockingCard_CloseEvent(DockingCard Sender, ref bool bNotToClose)
        {
            RestoreSceneValue(true);
        }

        #region Update
        protected override void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            if (LevelSequenceEntity != null && LevelSequenceEntity.World != null)
            {
                ControllableUnitSystemG.SetCurveControllerTimelinePlay(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, TimelineControl.GetIsPlaying());
                AnimatorSystemG.EnableUpdateInEditor(LevelSequenceEntity.World.GetNativePointer(), TimelineControl.GetIsPlaying());
            }

            float lastCursorInSecond = (float)GetHeadLocation();

            if (TimelineControl.GetIsPlaying() && CanDoCapture())
            {
                OnCaptureFrameUpdate(TimeElapsed);
            }
            else if (TimelineControl.GetIsPlaying())
            {
                decimal NewValue;
                NewValue = GetHeadLocation() + TimeElapsed * (decimal)_PlayRate / 1000m;

                SwitchPreviewCamera(NewValue);

                if (NewValue >= GetEndLocation())
                {
                    if (TimelineControl.GetIsLoop())
                    {
                        if (GetLength() != 0m)
                            NewValue = GetStartLocation() + (NewValue - GetStartLocation()) % GetLength();
                        else
                            NewValue = GetStartLocation();
                    }
                    else
                    {
                        NewValue = GetEndLocation();
                        TimelineControl.StopPlay();
                    }
                }
                SetHeadLocation(NewValue);
                SetTimeProgress(PlayHead, NewValue);
                bShouldUpdateValue = true;
            }

            if (bShouldUpdateValue)
            {
                float cursorInSecond = (float)GetHeadLocation();
                if (LevelSequenceEntity == null || LevelSequenceEntity.World == null)
                {
                    return;
                }

                ControllableUnitSystemG.SetCurveControllerDeltaTime(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, cursorInSecond - lastCursorInSecond);
                ControllableUnitSystemG.SetCurveControllerCursor(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, cursorInSecond);

                foreach (var _Entity in _PreviewEntities)
                {
                    if (_Entity.World == null || !_Entity.IsAlive())
                    {
                        return;
                    }
                    _Entity.SyncDataFromEngine();
                    var cam = (Camera)_Entity.GetComponent(typeof(Camera));
                    var trans = (Transform)_Entity.GetComponent(typeof(Transform));
                    if(cam != null && trans != null && EditorScene.GetInstance().GetPreviewState() == PreviewState.PreviewMaximized)
                    {
                        EditorScene.GetInstance().SyncEditorCamera(cam, trans);
                    }

                    if (InspectorUI.GetInstance().GetObjectInspected() is List<Entity> &&
                        (InspectorUI.GetInstance().GetObjectInspected() as List<Entity>).Contains(_Entity))
                    {
                        refresh = true;
                    }
                    ResourceInspectorUI.ForEach((ObjectInspected, Instance) =>
                    {
                        if (ObjectInspected is Material)
                        {
                            refreshMaterial = true;
                        }
                    });
                }
                if (_CountDownRun >= 0)
                {
                    _CountDownRun -= TimeElapsed;
                    if (_CountDownRun < 0)
                    {
                        if (refreshMaterial)
                        {
                            ResourceInspectorUI.ForEach((ObjectInspected, Instance) =>
                            {
                                if (ObjectInspected is Material Material)
                                {
                                    if (!_CurModifyMaterials.Contains(Material))
                                        _CurModifyMaterials.Add(Material);
                                    ComponentTrackHolder ComponentTrackHolder = QueryCurResourceMaterial(Material);
                                    RefreshCurResourceMaterial(Material, ComponentTrackHolder);
                                    //Material.Refresh();
                                    Instance.ReadValue();
                                }
                            });
                            refreshMaterial = false;
                        }
                        if (refresh)
                        {
                            InspectorUI.GetInstance().ReadValue();
                            refresh = false;
                        }
                        _CountDownRun = Duration;
                    }
                }
                foreach (var Item in TrackTree.GetRootTracks())
                {
                    RecursiveTrack(Item);
                }
                bShouldUpdateValue = false;
            }

            if (PreviewEntity != null && _DrawTrajectoryGizmo)
            {
                if (!(LevelSequenceEntity == null || LevelSequenceEntity.World == null))
                {
                    ControllableUnitSystemG.DrawCurveTransformTrajectory(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, true, PreviewEntity.GetEntityIdStruct());
                }
            }

            if (hasDeleteCameraTask)
            {
                if (lagFrame-- == 0)
                {
                    EditorScene.GetInstance().DeleteEntities(toDeleteCaptureCameras, false);
                    toDeleteCaptureCameras.Clear();
                    hasDeleteCameraTask = false;

                    GenerateVideo();
                }
            }
        }
        #endregion

        #region Interface Operation

        private void OnPlayRateEditTextChanged(Edit Sender)
        {
            float newPlayRate = 1.0f;
            if (float.TryParse(Sender.GetText(), out newPlayRate))
            {
                newPlayRate = Math.Clamp(newPlayRate, 1e-5f, 10.0f);
                SetPlayRate(newPlayRate, true);

                if (_PreviewEntities.Count != 0)
                {
                    ControllableUnitSystemG.SetCurveControllerPlayRate(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, newPlayRate);
                    SetModified();
                }
            }
        }

        private void OnPlayRateSliderChanged(TrackBar Sender)
        {
            float newPlayRate = Math.Max(Sender.GetValue() * 10, 1e-5f);
            SetPlayRate(newPlayRate, false);

            if (_PreviewEntities.Count != 0)
            {
                ControllableUnitSystemG.SetCurveControllerPlayRate(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, newPlayRate);
                SetModified();
            }
        }

        private void OnEntityMoreOperationsClicked(Button Sender)
        {
            Menu MenuContextMenu = new Menu(GetUIManager());
            MenuContextMenu.Initialize();

            MenuItem MenuItem_Import = new MenuItem();
            MenuItem_Import.SetText("Import");
            MenuItem_Import.SetEnable(false);
            MenuItem_Import.ClickedEvent += OnMenuItemImport;

            MenuContextMenu.AddMenuItem(MenuItem_Import);

            if (GetIsModified() || _PreviewEntities.Count != 0)
            {
                MenuItem MenuItem_Save = new MenuItem();
                MenuItem_Save.SetText("Save");
                MenuItem_Save.ClickedEvent += OnMenuItemSaveClicked;

                MenuContextMenu.AddMenuItem(MenuItem_Save);
            }

            if (GetIsModified() || _PreviewEntities.Count != 0)
            {
                MenuItem MenuItem_SaveResOnly = new MenuItem();
                MenuItem_SaveResOnly.SetText("Save Resource Only");
                MenuItem_SaveResOnly.ClickedEvent += OnMenuItemSaveResourceOnlyClicked;

                MenuContextMenu.AddMenuItem(MenuItem_SaveResOnly);
            }

            MenuItem MenuItem_Turn = new MenuItem();
            MenuItem_Turn.SetText(_DrawTrajectoryGizmo ? "Trajectory On" : "Trajectory Off");
            MenuItem_Turn.SetEnable(true);
            MenuItem_Turn.ClickedEvent += OnMenuItemTurnTrajectory;
            MenuContextMenu.AddMenuItem(MenuItem_Turn);

            MenuItem MenuItem_ClearAll = new MenuItem();
            MenuItem_ClearAll.SetText("Clear All");
            MenuItem_ClearAll.SetEnable(false);
            MenuItem_ClearAll.ClickedEvent += OnMenuItemClearAll;

            MenuContextMenu.AddMenuItem(MenuItem_ClearAll);

            var item = _EntityOperationsBtn;
            GetUIManager().GetContextMenu().ShowMenu(MenuContextMenu, item.GetScreenX(), item.GetScreenY() + item.GetHeight());
        }

        private void OnMenuItemImport(MenuItem Sender)
        {
            PathInputUIFilterItem PathInputUIFilterItem = new PathInputUIFilterItem();
            PathInputUIFilterItem.Name = "Curve Files";
            PathInputUIFilterItem.Extensions.Add("nda");

            string SelectedFilePath = "";
            bool bContentsOnly = false;

            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.AddDefaultDrives();
            PathInputUI.Initialize(GetUIManager(), "Select Curve File As", PathInputUIType.OpenFile, PathInputUIFilterItem, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {
                    _CurveCtrPath = ResourceManager.Instance().ConvertPathToGuid(EditorUtilities.EditorFilenameToStandardFilename(SelectedFilePath));
                    OnMenuItemClearAll(null);
                    InitializationJudgment();
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        public void OnTimeProgressTextChanged(Control Sender)
        {
            ForceSetHeadLocation(decimal.Parse(Sender.GetText()));
        }

        private void OnMenuItemSaveClicked(MenuItem Sender)
        {
            DoSave();
        }

        private void OnMenuItemSaveResourceOnlyClicked(MenuItem Sender)
        {
            DoSaveResourceOnly();
        }

        private void OnMenuItemTurnTrajectory(MenuItem Sender)
        {
            _DrawTrajectoryGizmo = !_DrawTrajectoryGizmo;
        }

        public void OnMenuItemClearAll(MenuItem Sender)
        {
            TrackTree.ClearTrack();
            _PreviewEntities.Clear();
        }

        new void OnButtonAddTrackClicked(Button Sender)
        {
            Menu AddTrackMenu = new Menu(GetUIManager());
            AddTrackMenu.Initialize();

            MenuItem NewItem = new MenuItem();
            NewItem.SetText("Add SubSeq Track");
            NewItem.ClickedEvent += (ItemSender) =>
            {
                SubSeqTrack SubSeqTrack = new SubSeqTrack();
                IntPtr CurveDataPtr = ControllableUnitSystemG.GetCurveControllerData(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, LevelSequenceEntity.GetEntityIdStruct());
                var CurveControllerDataRef = new CurveControllerData(CurveDataPtr, false);
                var SubSeqTrackPtr = CurveControllerDataRef.CreateCurveDataItemAsSubSeqTrack("SubSeq", SubSeqTrackIndex);
                if (SubSeqTrackPtr != IntPtr.Zero)
                {
                    SubSeqTrack.Initialize(_ScaleUI, null, SubSeqTrackPtr, SubSeqTrackIndex);
                    TrackTree.AddTrack(SubSeqTrack, true);
                    SubSeqTrackIndex++;
                    CinematicUI.GetInstance().SetModified();
                }
            };
            AddTrackMenu.AddMenuItem(NewItem);
            GetUIManager().GetContextMenu().ShowMenu(AddTrackMenu, Sender.GetScreenX(), Sender.GetScreenY() + Sender.GetHeight());
        }

        public override void ShowMenu(int MouseX, int MouseY)
        {
            if (_ScaleUI.HitTest(MouseX, MouseY))
            {
                //ShowFPSMenuImpl(MouseX, MouseY);
            }
            else
            {
                base.ShowMenu(MouseX, MouseY);
            }
        }

        public override void OnPanelLeftMouseDoubleClick(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            HoldingObject = TryHit(MouseX, MouseY);

            if (HoldingObject is KeyFrameMover && ((KeyFrameMover)HoldingObject).KeyFrames.Count == 1)
            {
                if (((KeyFrameMover)HoldingObject).KeyFrames[0] is SubSeqKeyFrame)
                {
                    SubSeqKeyFrame SubSeqKeyFrame = (SubSeqKeyFrame)((KeyFrameMover)HoldingObject).KeyFrames[0];
                    if (SubSeqKeyFrame != null)
                    {
                        SubSeqKeyFrame.OpenSequencer();
                    }
                }
            }

            HoldingObject = null;
            Sender.ReleaseMouse();
            bContinue = false;
        }

        void ShowFPSMenuImpl(int MouseX, int MouseY)
        {
            Menu Menu = new Menu(GetUIManager());
            Menu.Initialize();
            // Set fps menu
            Menu Menu_FPSCandidates = new Menu(GetUIManager());
            Menu_FPSCandidates.Initialize();
            MenuItem MenuItem_SetFps = new MenuItem();
            MenuItem_SetFps.SetMenu(Menu_FPSCandidates);
            MenuItem_SetFps.SetText("Set FPS (Not supported yet)");
            MenuItem_SetFps.SetEnable(false);
            List<decimal> _CandidateFPS = new List<decimal> { 15, 24, 25, 30, 60, 90, 120, 144 };
            foreach (decimal fps in _CandidateFPS)
            {
                MenuItem MenuItem_fps = new MenuItem();
                MenuItem_fps.SetText("Set FPS " + fps.ToString());
                //MenuItem_fps.SetEnable(fps != _ScaleUI.GetFps());
                MenuItem_fps.SetEnable(false);
                MenuItem_fps.ClickedEvent += (Sender) =>
                {
                    SetFps(fps);
                    if (_PreviewEntities.Count != 0)
                    {
                        foreach (Entity _Entity in _PreviewEntities)
                        {
                            if (!_Entity.IsAlive()) continue;
                            ControllableUnitSystemG.SetCurveControllerFPS(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, (float)fps, _Entity.GetEntityIdStruct());
                        }
                        SetModified();
                    }
                };
                Menu_FPSCandidates.AddMenuItem(MenuItem_fps);
            }

            Menu.AddMenuItem(MenuItem_SetFps);

            // set show modes menu
            Menu Menu_ShowModes = new Menu(GetUIManager());
            Menu_ShowModes.Initialize();

            MenuItem MenuItem__ShowModes = new MenuItem();
            MenuItem__ShowModes.SetMenu(Menu_ShowModes);
            MenuItem__ShowModes.SetText("Set display mode");
            MenuItem MenuItem_ShowFrame = new MenuItem();
            MenuItem_ShowFrame.SetText("Shown as Frame.");
            MenuItem_ShowFrame.ClickedEvent += (Sender) =>
            {
                if (ScaleUI.GetShowMode() == ShowMode.ShowTime)
                {
                    EndHead.Value = SaveEndValueSecond;
                }
                ScaleUI.SetShowMode(ShowMode.ShowFPS);
                _FPSBtn.SetText("Show Frame");
            };
            Menu_ShowModes.AddMenuItem(MenuItem_ShowFrame);

            MenuItem MenuItem_ShowSecond = new MenuItem();
            MenuItem_ShowSecond.SetText("Shown as Second.");
            MenuItem_ShowSecond.ClickedEvent += (Sender) =>
            {
                if (ScaleUI.GetShowMode() == ShowMode.ShowFPS)
                {
                    EndHead.Value = SaveEndValueSecond;
                }
                ScaleUI.SetShowMode(ShowMode.ShowTime);
                _FPSBtn.SetText("Show Second");
            };
            Menu_ShowModes.AddMenuItem(MenuItem_ShowSecond);
            Menu.AddMenuItem(MenuItem__ShowModes);


            if (Menu.GetMenuItemCount() > 0)
                GetUIManager().GetContextMenu().ShowMenu(Menu, MouseX, MouseY);
        }

        public override void DoSave()
        {
            if (IsVisible())
            {
                DoSaveResourceOnly();
                EditorScene.GetInstance().SaveScene();
                SetPreviewNull();
            }
        }

        public void DoSaveResourceOnly()
        {
            if (IsVisible())
            {
                RefreshDeleteEntities();
                if (_DeleteEntities.Count != 0)
                {
                    DeleteEntitiesData();
                    _DeleteEntities.Clear();
                }
                //restore scene parameter
                RestoreSceneValue();

                if (_PreviewEntities.Count != 0 || LevelSeqContainCurve())
                {
                    string CurvePath = ResourceManager.Instance().ConvertPathToGuid(_CurveCtrPath);
                    if (LevelSequenceEntity.World == null || LevelSequenceEntity == null)
                        return;
                    ControllableUnitSystemG.SaveCurveControllerRes(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, CurvePath);
                }
                ClearModified();
                SetPreviewNull();
            }
        }
        #endregion

        #region Add/Delete
        public void AddComponentTrack(ComponentTrack track)
        {
            if (track != null && track.CanBeDeleted)
            {
                Entity TrackEntity = (Entity)track.GetPrevieEntity();
                if (TrackEntity != null)
                {
                    bool ret = false;
                    if (track is MaterialTrack)
                    {
                        MaterialTrack MaterialTrack = (MaterialTrack)track;
                        ret = ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, MaterialTrack.GetContext(),
                            MaterialTrack.CurveType, TrackEntity.GetEntityIdStruct());
                        var property = MaterialTrack._Material.Properties.Where(p =>
                            p.Name.Equals(MaterialTrack.GetContext().ParamName.GetCString())).ToList()[0];
                        FloatCurveListInfo info = MaterialTrack.GetFloatCurveListInfo(property);
                        ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, MaterialTrack.GetContext(), info, TrackEntity.GetEntityIdStruct());
                    }
                    else if (track is ComponentTrack)
                    {
                        ret = ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, track.ComponentName,
                            new UniqueString(track.GetDisplayPropertyName()), track.SystemName, track.CurveType, TrackEntity.GetEntityIdStruct());
                        FloatCurveListInfo info = track.GetFloatCurveListInfo(track.PreviewingProperty);
                        ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, track.ComponentName,
                            new UniqueString(track.GetDisplayPropertyName()), info, TrackEntity.GetEntityIdStruct());
                    }

                    foreach (var KeyFrame in track.GetKeyFrames())
                    {
                        foreach (var subtrack in track.GetChildList())
                        {
                            ((NumericTrack)subtrack).PropertyAddKeyFrame(KeyFrame.GetBeginX(), KeyFrame.GetKeyValue());
                        }
                    }
                    if (ret)
                    {
                        SetModified();
                    }
                }
            }
        }

        public void AddEventTrack(EventTrack Track)
        {
            if (Track != null)
            {
                Track.AddTrackData(Track.GetContext().TrackIndex);

                foreach (var KeyFrame in Track.GetKeyFrames())
                {
                    List<string> StringList = new List<string>();
                    var SplitList = ((SmoothPoint)KeyFrame.BindObject).EventString.Split("|");
                    foreach (var Split in SplitList)
                    {
                        if (Split != "")
                        {
                            StringList.Add(Split);
                        }
                    }
                    Track.PropertyAddKeyFrame(KeyFrame.GetKeyValue(), StringList);
                }
                SetModified();
            }
        }

        public void AddComponentTrackHolder(ComponentTrackHolder Holder)
        {
            if (Holder.GetTrackType() == TrackType.Entity)
            {
                AddPreviewEntity(Holder.GetTagObject() as Entity);
                List<Track> Lists = Holder.GetChildList();
                foreach (var Value in Lists)
                {
                    if (Value is ComponentTrackHolder)
                    {
                        AddComponentHolder((ComponentTrackHolder)Value);
                    }
                    else if (Value is MaterialTrack)
                    {
                        MaterialTrack MaterialTrack = (MaterialTrack)Value;
                        AddComponentTrack(MaterialTrack);
                    }
                }
            }
            else if (Holder.GetTrackType() == TrackType.Component)
            {
                AddComponentHolder(Holder);
            }
        }

        public void AddComponentHolder(ComponentTrackHolder Holder)
        {
            List<Track> Lists = Holder.GetChildList();
            foreach (var Value in Lists)
            {
                if (Value is ComponentTrack)
                {
                    ComponentTrack componentTrack = (ComponentTrack)Value;
                    AddComponentTrack(componentTrack);
                }
            }
        }

        public void AddPreviewEntity(Entity previewEntity)
        {
            if (!_PreviewEntities.Contains(previewEntity))
            {
                _PreviewEntities.Add(previewEntity);
            }
        }

        public Track AddEntityTrack(Entity Entity, bool bModified = true)
        {
            foreach (var Value in _PreviewEntities)
            {
                if (Value.IsAlive() && Entity == Value)
                {
                    if (_Instance.IsFocused())
                    {
                        //CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Error", string.Format("The entity is exist."));
                    }
                    return null;
                }
            }

            //new Entity add ComponentTrackHolder.
            ComponentTrackHolder NewComponentTrackHolder = new ComponentTrackHolder(PlayHead);
            NewComponentTrackHolder.Initialize(ScaleUI, null, Entity.GetName(), Entity);
            _PreviewEntities.Add(Entity);
            TrackTree.AddTrack(NewComponentTrackHolder);
            ReloadControllableUnit(Entity, NewComponentTrackHolder, _PreviewEntities.Count == 1);

            if (bModified)
            {
                SetModified();
            }
            return NewComponentTrackHolder;
        }

        public void AddSubSeqTrack(bool bModified = true)
        {
            IntPtr CurveDataPtr = ControllableUnitSystemG.GetCurveControllerData(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, LevelSequenceEntity.GetEntityIdStruct());
            var CurveControllerDataRef = new CurveControllerData(CurveDataPtr, false);
            for (int i = 0; i < CurveControllerDataRef.mDataItems.Count; i++)
            {
                var ItemData = CurveControllerDataRef.mDataItems[i];
                if (ItemData.Type == CurveControllerDataItem.ItemType.SubSequncerTrack)
                {
                    IntPtr SubSeqTrackPtr = ItemData.GetSubSeqTrackPtr(0);
                    if (SubSeqTrackPtr != IntPtr.Zero)
                    {
                        SubSeqTrack newTrack = new SubSeqTrack();
                        newTrack.Initialize(_ScaleUI, null, SubSeqTrackPtr, ItemData.TrackIndex);
                        ReloadControllableUnit(LevelSequenceEntity, newTrack, _PreviewEntities.Count == 1);
                        TrackTree.AddTrack(newTrack, true);
                    }
                }
            }
        }

        public void AddSubSeqTrack(SubSeqTrack Track)
        {
            if (Track != null)
            {
                TrackTree.AddTrack(Track, true);
                foreach (var KeyFrame in Track.GetKeyFrames())
                {
                    SubSeqKeyFrame SubSeqKeyFrame = KeyFrame as SubSeqKeyFrame;
                    LevelSubSeqSection Section = SubSeqKeyFrame.Section;
                    Track.SubSeqTrackRef.AddNewSubSeqSection(Section.SectionStart, Section.SubSeqPath, Section.SubSeqDuration, Section.SubSeqFPS, Section.SubSeqPlayRate);
                }
                SetModified();
            }
        }

        public void AddControllableUnitComponent(Entity Entity)
        {
            ControllableUnitComponent ControllableUnitComponent = Entity.CreateComponent<ControllableUnitComponent>();
            ControllableUnitComponent.Reset();
            ControllableUnitComponent.ControllerType = ControllableUnitType.CurveController;
            ControllableUnitComponent.ControllableUnit.Entity = new EntityIDStruct(Entity.EntityID);

            Type type = ControllableUnitComponent.ControllableUnit.GetType();
            PropertyInfo PropertyInfo = type.GetProperty("CurveCtrResPath");

            if (PropertyInfo != null)
            {
                PropertyInfo.SetValue(ControllableUnitComponent.ControllableUnit, _CurveCtrPath);

                var controllerJson = JsonConvert.SerializeObject(ControllableUnitComponent.ControllableUnit);
                ControllableUnitSystemG.SetControllableUnitJson(
                    Entity.World.GetNativePointer(),
                    Entity.EntityID,
                    controllerJson);
            }

        }

        public void DeleteTrack(Track Track)
        {
            Type TrackType = Track.GetType();
            if (TrackType == typeof(ComponentTrackHolder))
            {
                DeleteComponentTrackHolder(Track as ComponentTrackHolder);
            }
            else if (TrackType == typeof(ComponentTrack))
            {
                DeleteComponentTrack(Track as ComponentTrack);
            }
            else if (TrackType == typeof(MaterialTrack))
            {
                DeleteMaterialTrack(Track as MaterialTrack);
            }
            else if (TrackType == typeof(EventTrack))
            {
                DeleteEventTrack(Track as EventTrack);
            }
            else if (TrackType == typeof(SeqAnimSectionTrack))
            {
                DeleteAnimSectionTrack(Track as SeqAnimSectionTrack);
            }
            else if (TrackType == typeof(ParticleMaterialTrack))
            {
                DeleteParticleMaterialTrack(Track as ParticleMaterialTrack);
            }
            else if (TrackType == typeof(SubSeqTrack))
            {
                DeleteSubSeqTrack(Track as SubSeqTrack);
            }

            List<Track> Children = Track.GetChildList();
            foreach (Track Child in Children)
            {
                DeleteTrack(Child);
            }
        }

        public void DeleteComponentTrackHolder(ComponentTrackHolder Holder)
        {
            if (Holder.GetTrackType() == TrackType.Entity)
            {
                _PreviewEntities.Remove(Holder.GetTagObject() as Entity);
            }
            else if (Holder.GetTrackType() == TrackType.Component)
            {
                // Do nothing?
            }
        }

        public void DeleteComponentTrack(ComponentTrack track)
        {
            if (track != null && track.CanBeDeleted)
            {
                Entity TrackEntity = track.GetPrevieEntity();
                if (TrackEntity != null)
                {
                    if (LevelSequenceEntity != null && LevelSequenceEntity.World != null)
                    {
                        bool ret = ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, track.ComponentName, new UniqueString(track.GetDisplayPropertyName()),
                                        TrackEntity.GetEntityIdStruct());
                        if (ret)
                        {
                            SetModified();
                        }
                    }
                }
            }
        }

        public void DeleteMaterialTrack(MaterialTrack Track)
        {
            if (Track != null && Track.CanBeDeleted)
            {
                Entity TrackEntity = Track.GetPrevieEntity();
                if (TrackEntity != null)
                {
                    bool ret = ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.GetContext(), TrackEntity.GetEntityIdStruct());
                    if (ret)
                    {
                        SetModified();
                        // Reset target property value
                        var mat = Track._Material.ResourcePtr as Clicross.resource.Material;
                        mat.RemoveProperty(new Clicross.NameID(Track.PropertyStr));
                    }
                }
            }
        }

        public void DeleteEventTrack(EventTrack Track)
        {
            if (Track != null && Track.CanBeDeleted)
            {
                Entity TrackEntity = Track.GetPrevieEntity();
                if (TrackEntity != null)
                {
                    bool ret = ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.GetContext(), TrackEntity.GetEntityIdStruct());
                    if (ret)
                    {
                        SetModified();
                    }
                }
            }
        }

        public void DeleteAnimSectionTrack(SeqAnimSectionTrack Track)
        {
            if (Track != null && Track.CanBeDeleted)
            {
                ControllableUnitSystemG.RemoveCurveControllerAnimTrack(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.Previewing.GetEntityIdStruct(), Track.TrackIndex);
            }
        }

        public void DeleteParticleMaterialTrack(ParticleMaterialTrack Track)
        {
            if (Track != null && Track.CanBeDeleted)
            {
                Entity TrackEntity = Track.GetPrevieEntity();
                if (TrackEntity != null)
                {
                    bool ret = ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.GetContext(), TrackEntity.GetEntityIdStruct());
                    if (ret)
                    {
                        SetModified();
                        // Reset target property value
                        var mat = Track._Material.ResourcePtr as Clicross.resource.Material;
                        mat.RemoveProperty(new Clicross.NameID(Track.PropertyStr));
                    }
                }
            }
        }

        public void DeleteSubSeqTrack(SubSeqTrack Track)
        {
            if (Track != null && Track.CanBeDeleted)
            {
                IntPtr CurveDataPtr = ControllableUnitSystemG.GetCurveControllerData(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, LevelSequenceEntity.GetEntityIdStruct());
                if (CurveDataPtr != IntPtr.Zero && Track.SubSeqTrackRef != null)
                {
                    var CurveControllerDataRef = new CurveControllerData(CurveDataPtr, false);
                    CurveControllerDataRef.RemoveSubSeqTrack(Track.SubSeqTrackIndex);
                }
            }
        }

        public void DeleteEntitiesData()
        {
            foreach (var Entity in _DeleteEntities)
            {
                CurveControllerData CurveControllerData = new CurveControllerData();
                ControllableUnitSystemG.GetCurveControllerData(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, CurveControllerData, Entity.GetEntityIdStruct(), _CurveCtrPath);

                foreach (var Data in CurveControllerData.mDataItems)
                {
                    if (Data.Type == CurveControllerDataItem.ItemType.ComponentProperty)
                    {
                        ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Data.ComponentName, Data.PropertyName,
                                                                      Entity.GetEntityIdStruct());
                    }
                    else
                    {
                        MaterialCurveContext Context = new MaterialCurveContext();
                        Context.ModelIndex = Data.ModelIndex;
                        Context.ParamName = Data.MatParamName;
                        Context.SubModelIndex = Data.SubModelIndex;
                        ControllableUnitSystemG.RemoveCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Context, Entity.GetEntityIdStruct());
                    }
                }
            }
        }

        public void RefreshDeleteEntities()
        {
            List<int> Indexes = new List<int>();
            for (int i = _DeleteEntities.Count - 1; i >= 0; i--)
            {
                if (_PreviewEntities.Contains(_DeleteEntities[i]))
                {
                    Indexes.Add(i);
                }
            }

            foreach (var index in Indexes)
            {
                _DeleteEntities.RemoveAt(index);
            }
        }

        public void DeleteTrackFromTree(List<Entity> DeleteEntities, List<object> Tracks, List<object> ParentTrack)
        {
            Entity Entity = HierarchyUI.GetInstance().GetScene().GetSelection();
            CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
            {
                ControllableUnitSystemG.SetCurveControllerCursor(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.OriginKeyValue);
                foreach (var Value in Tracks)
                {
                    DeleteTrack(Value as Track);
                    TrackTree.RemoveTrack(Value as Track);
                }
                RefreshInspector(Entity);
            };
            CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
            {
                for (int i = 0; i < Tracks.Count; i++)
                {
                    if (Tracks[i] is ComponentTrackHolder)
                    {
                        AddComponentTrackHolder((ComponentTrackHolder)Tracks[i]);
                    }
                    else if (Tracks[i] is ComponentTrack)
                    {
                        ComponentTrack componentTrack = (ComponentTrack)Tracks[i];
                        AddComponentTrack(componentTrack);
                        //AddComponentTrack(ref componentTrack);
                    }
                    else if (Tracks[i] is EventTrack)
                    {
                        EventTrack eventTrack = (EventTrack)Tracks[i];
                        AddEventTrack(eventTrack);
                    }
                    //else if (Tracks[i] is SubSeqTrack)
                    //{
                    //    SubSeqTrack subseqTrack = (SubSeqTrack)Tracks[i];
                    //    AddSubSeqTrack(subseqTrack);
                    //}
                    if (ParentTrack[i] == null)
                    {
                        TrackTree.AddTrack((Track)Tracks[i]);
                        if (!(Tracks[i] is SubSeqTrack))
                        {
                            Track AddTrack = (Track)Tracks[i];
                            while (true)
                            {
                                if (AddTrack.GetChildList().Count != 0)
                                {
                                    for (int j = 0; j < AddTrack.GetChildList().Count; j++)
                                    {
                                        TrackTree.PostTrackAdd(AddTrack, AddTrack.GetChildList()[j]);
                                    }
                                }
                                else
                                {
                                    break;
                                }
                                AddTrack = AddTrack.GetChildList()[0];
                            }
                        }
                    }
                    else
                    {
                        ((Track)ParentTrack[i]).AddTrack((Track)Tracks[i]);
                    }
                }

                //undo git rid of deleted entity
                foreach (var Entity in DeleteEntities)
                {
                    _DeleteEntities.Remove(Entity);
                }
                RefreshInspector(Entity);
            };
            CinematicOperation operation = new CinematicOperation(null, UndoFunc, RedoFunc);
            EditOperationManager.GetInstance().AddOperation(operation);
            RedoFunc();
        }

        #endregion

        #region Input Handle
        public void OnDockingCardKeyUpImpl(Control Sender, Key Key, ref bool bContinue)
        {
            if (_PreviewEntities.Count == 0 && TrackTree.GetTracks().Count == 0)
            {
                return;
            }

            if (!IsFocused())
            {
                return;
            }
            Device Device = GetDevice();
            decimal currentCursor = PlayHead.Value;
            // record current selected component's value
            if (Key == Key.Enter)
            {
                List<ComponentTrack> candidates = new List<ComponentTrack>();
                candidates = SelectTrackList();

                List<Track> candidateTrack = new List<Track>();
                candidateTrack = SelectedTrackList();

                CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
                {
                    PlayHead.Value = currentCursor;
                    RemoveComponentValueFromSelected(candidates);
                };

                CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
                {
                    PlayHead.Value = currentCursor;
                    ExtractComponentValueToSelected(candidates);
                    ExtractEventValueTrackToSelected(candidateTrack);
                };

                // execute concrete add operation immediately
                RedoFunc();

                // add above execute command into operation queue for REDO/UNDO later
                CinematicOperation operation = new CinematicOperation(candidates, UndoFunc, RedoFunc);
                EditOperationManager.GetInstance().AddOperation(operation);

                SetModified();
                bContinue = false;
            }
            // remove current selected component's value
            else if (Key == Key.Delete)
            {
                if (IsSelectTrackTree)
                {
                    //delete track
                    List<object> Tracks = null;
                    List<object> ParentTrack = null;
                    List<Entity> DeleteEntities = SelectTrackList(out Tracks, out ParentTrack);

                    if (Tracks.Count == 0) return;

                    DeleteSelectedCineCamera(DeleteEntities);
                    DeleteTrackFromTree(DeleteEntities, Tracks, ParentTrack);
                    IsSelectTrackTree = false;
                }
                else
                {
                    List<ComponentTrack> candidates = new List<ComponentTrack>();
                    candidates = SelectTrackList();
                    List<EventTrack> eventTracks = new List<EventTrack>();
                    eventTracks = SelectEventTrackList();
                    List<SubSeqTrack> subSeqTracks = new List<SubSeqTrack>();
                    subSeqTracks = SelectTTrackList<SubSeqTrack>();
                    List<KeyFrame> CurKeyFrames = SelectedKeyFrames;
                    for (int i = SelectedKeyFrames.Count - 1; i >= 0; i--)
                    {
                        if (SelectedKeyFrames[i] == null)
                        {
                            SelectedKeyFrames.RemoveAt(i);
                        }
                    }

                    CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
                    {
                        PlayHead.Value = currentCursor;
                        RemoveComponentValueFromSelected(candidates, CurKeyFrames);
                        RemoveEventValueFromSelected(eventTracks, CurKeyFrames);
                        RemoveSubSeqKeyFrameFromSelected(subSeqTracks, CurKeyFrames);
                    };
                    CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
                    {
                        PlayHead.Value = currentCursor;
                        ExtractComponentValueToSelected(candidates, CurKeyFrames);
                        ExtractEventValueToSelected(eventTracks, CurKeyFrames);
                    };
                    // execute concrete add operation immediately
                    RedoFunc();
                    // add above execute command into operation queue for REDO/UNDO later
                    CinematicOperation operation = new CinematicOperation(candidates, UndoFunc, RedoFunc);
                    EditOperationManager.GetInstance().AddOperation(operation);
                }
                SetModified();
                bContinue = false;
            }
            // select next track
            else if (Key == Key.Tab)
            {
                var selectedTracks = GetSelectedTracks();

                int selectedCount = 0;
                ComponentTrack currentTrack = null;
                foreach (var selectedTrack in selectedTracks)
                {
                    if (selectedTrack as ComponentTrack != null)
                        currentTrack = selectedTrack as ComponentTrack;
                    else
                        currentTrack = selectedTrack.GetParentTrack() as ComponentTrack;

                    selectedCount++;
                }

                if (selectedCount == 1 && currentTrack != null)
                {
                    if (_ComponentTrackHolder != null)
                    {
                        var tracksCount = _ComponentTrackHolder.GetChildList().Count;
                        if (tracksCount > 1)
                        {
                            var currentIndex = _ComponentTrackHolder.GetChildList().IndexOf(currentTrack);
                            var nextIndex = (currentIndex + 1) % tracksCount;

                            SelectTrack(_ComponentTrackHolder.GetChildList()[nextIndex]);
                        }
                    }
                }

                bContinue = false;
            }
            else if (Key == Key.Space)
            {
                TimelineControl.SetIsPlaying(!TimelineControl.GetIsPlaying());
            }
            else if (Key == Key.S && !Device.IsControlDown())
            {
                //Auto Key Entity TRS
                AutoKeyTRSByS();
            }
            else if (Key == Key.D && Device.IsControlDown())
            {
                TrackTree.UpdateTrackFold();
            }
            else if (Key == Key.C && Device.IsControlDown())
            {
                TempFramesCopied.Clear();
                bFramesCopied = true;
                foreach (var Frame in SelectedKeyFrames)
                {
                    TempFramesCopied.Add(Frame);
                }
                ProcessRotateKeyFrameMover(TempFramesCopied);
            }
            else if (Key == Key.V && Device.IsControlDown())
            {
                List<KeyFrame> Temp = TempFramesCopied;
                if (Temp[0].GetKeyValue() != PlayHead.Value)
                {
                    bFramesCopied = false;
                    PasteKeyFrames(Temp);
                    EditOperation_PasteKeyFrames operation = new EditOperation_PasteKeyFrames(SelectedKeyFrames.Clone());
                    EditOperationManager.GetInstance().AddOperation(operation);
                    TempFramesCopied.Clear();
                    SetModified();
                }
            }
        }

        protected override void OnPanelLeftMouseUpImpl(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsLeftMouseDown)
            {
                if (HoldingObject is KeyFrameMover && ((KeyFrameMover)HoldingObject).KeyFrames.Count == 1)
                {
                    if (((KeyFrameMover)HoldingObject).KeyFrames[0].OwnerTrack.GetParentTrack() is ComponentTrack)
                    {
                        if (((ComponentTrack)((KeyFrameMover)HoldingObject).KeyFrames[0].OwnerTrack.GetParentTrack()).GetDisplayPropertyName() == "Rotation")
                        {
                            IsLeftMouseDown = false;
                            HoldingObject = null;
                            Sender.ReleaseMouse();
                            bContinue = false;
                            return;
                        }
                    }
                }
                HoldingObject?.MoveEnd(new Vector2f(MouseX, MouseY), null);

                if (HoldingObject is EndHead && _PreviewEntities.Count != 0)
                {
                    var CachedPosition = CachedEndHeadPosition;
                    var ThisPosition = EndHead.Value;
                    CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
                    {
                        EndHead.Value = CachedPosition;
                        SaveEndValueSecond = EndHead.Value;
                        if (_PreviewEntities.Count != 0)
                        {
                            foreach (var Entity in _PreviewEntities)
                            {
                                if (!Entity.IsAlive()) continue;
                                ControllableUnitSystemG.SetCurveControllerDuration(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, (float)CachedPosition, Entity.GetEntityIdStruct());
                            }
                        }
                        SetModified();
                        if (PlayHead.Value > EndHead.Value)
                        {
                            PlayHead.Value = EndHead.Value;
                            bShouldUpdateValue = true;
                        }
                    };

                    CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
                    {

                        EndHead.Value = ThisPosition;
                        SaveEndValueSecond = EndHead.Value;
                        if (_PreviewEntities.Count != 0)
                        {
                            foreach (var Entity in _PreviewEntities)
                            {
                                if (!Entity.IsAlive()) continue;
                                ControllableUnitSystemG.SetCurveControllerDuration(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, (float)ThisPosition, Entity.GetEntityIdStruct());
                            }
                        }
                        SetModified();
                        if (PlayHead.Value > EndHead.Value)
                        {
                            PlayHead.Value = EndHead.Value;
                            bShouldUpdateValue = true;
                        }
                    };

                    RedoFunc();
                    // add above execute command into operation queue for REDO/UNDO later
                    CinematicOperation operation = new CinematicOperation(null, UndoFunc, RedoFunc);
                    EditOperationManager.GetInstance().AddOperation(operation);
                }
                else if (HoldingObject is KeyFrameMover)
                {
                    KeyFrameMover Mover = (KeyFrameMover)HoldingObject;
                    List<ComponentTrack> candidates = new List<ComponentTrack>();
                    List<KeyFrame> PreviousKeyFrames = new List<KeyFrame>();
                    List<KeyFrame> MovedKeyFrames = new List<KeyFrame>();

                    CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
                    {
                        if (PreviousKeyFrames.Count != 0)
                        {
                            RemoveComponentValueFromSelected(candidates, PreviousKeyFrames);
                        }
                        MovedKeyFrames.Clear();
                        foreach (var KeyFrame in Mover.KeyFrames)
                        {
                            Track OwnerTrack = KeyFrame.OwnerTrack;
                            if (OwnerTrack is NumericTrack)
                            {
                                candidates.Add((ComponentTrack)OwnerTrack.GetParentTrack());
                                EditWithProgress Edit = ((NumericTrack)OwnerTrack).GetEditWithPrcogress();
                                double Value = (double)((Point)(KeyFrame.BindObject)).ValueY;
                                Edit.SetText(Value.ToString());
                                ((NumericTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), Value);
                            }
                            else if (OwnerTrack is EventTrack)
                            {
                                string Value = "";
                                ((EventTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), Value);
                            }
                            MovedKeyFrames.Add(KeyFrame);
                        }
                    };

                    CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
                    {
                        if (MovedKeyFrames.Count != 0)
                        {
                            RemoveComponentValueFromSelected(candidates, MovedKeyFrames);
                        }
                        foreach (var KeyFrame in Mover.KeyFrames)
                        {
                            Track OwnerTrack = KeyFrame.OwnerTrack;
                            if (OwnerTrack is NumericTrack)
                            {
                                EditWithProgress Edit = ((NumericTrack)OwnerTrack).GetEditWithPrcogress();
                                double Value = (double)((Point)(KeyFrame.BindObject)).ValueY;
                                Edit.SetText(Value.ToString());
                                ((NumericTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetBeginX(), Value);

                                KeyFrame PreviousKeyFrame = ((NumericTrack)OwnerTrack).FindKeyFrame(KeyFrame.GetBeginX());
                                if (PreviousKeyFrame != null)
                                {
                                    PreviousKeyFrames.Add(PreviousKeyFrame);
                                }
                            }
                        }
                    };
                    RedoFunc();
                    CinematicOperation operation = new CinematicOperation(candidates, UndoFunc, RedoFunc);
                    EditOperationManager.GetInstance().AddOperation(operation);
                    SetModified();
                }
                else if (HoldingObject != null && HoldingObject == SelectRectangle)
                {
                    SelectInRectangle(SelectRectangle.GetBoundRect());
                    HoldingObject.MoveEnd(new Vector2f(MouseX, MouseY), null);
                }
                IsLeftMouseDown = false;
                HoldingObject = null;
                SelectRectangle = null;
                Sender.ReleaseMouse();
                bContinue = false;
            }
        }

        protected override void OnDockingCardKeyUp(Control Sender, Key Key, ref bool bContinue)
        {
            OnDockingCardKeyUpImpl(Sender, Key, ref bContinue);
        }

        void OnDragDropManagerDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            if (GetDockingCard().GetDockingBlock() == null)
                return;

            if (_MainSplitter.GetVisible_Recursively() && _MainSplitter.IsPointIn_Recursively(MouseX, MouseY))
            {
                HierarchyUI HierarchyUI = HierarchyUI.GetInstance();
                if (HierarchyUI.IsDraggingEntity())
                {
                    Entity Entity = HierarchyUI.GetEntityDragged();
                    if (!Entity.HasComponent(typeof(ControllableUnitComponent)))
                    {
                        AutoKeyTRS(Entity);
                    }
                }
            }
        }

        public override void OnPanelLeftMouseDown(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            if (IsRightMouseDown) return;

            IsLeftMouseDown = true;
            IsSelectTrackTree = false;
            LastMouseLocationX = MouseX;
            LastMouseLocationY = MouseY;
            CumulatedMovement = 0;

            Sender.CaptureMouse();

            HoldingObject = TryHit(MouseX, MouseY);

            if (HoldingObject is EndHead)
            {
                CachedEndHeadPosition = EndHead.Value;
            }

            if (HoldingObject is KeyFrameMover)
            {
                if (((KeyFrameMover)HoldingObject).KeyFrames.Count == 1)
                {
                    if (((KeyFrameMover)HoldingObject).KeyFrames[0].OwnerTrack.GetParentTrack() is ComponentTrack)
                    {
                        if (((ComponentTrack)((KeyFrameMover)HoldingObject).KeyFrames[0].OwnerTrack.GetParentTrack()).GetDisplayPropertyName() == "Rotation")
                            return;
                    }
                }
            }

            if (HoldingObject == null)
            {
                SelectRectangle = new SelectRectangle();
                HoldingObject = SelectRectangle;
            }

            HoldingObject?.MoveBegin(new Vector2f(MouseX, MouseY), null);
        }
        #endregion

        #region Set/Get
        private void SetFps(decimal fps)
        {
            _ScaleUI.SetFps(fps);
            //_FPSBtn.SetText("FPS:" + _ScaleUI.GetFps().ToString());
        }

        public void SetPlayRate(float pr, bool updateSlider = true)
        {
            _PlayRate = pr;
            _PlayRateEdit.SetText(pr.ToString());
            if (updateSlider)
            {
                _PlayRateSlider.SetValue(pr / 10.0f);
            }
        }

        public void SetCurveCtrPath(String value)
        {
            _CurveCtrPath = value;
        }

        public String GetCurveCtrPath()
        {
            return _CurveCtrPath;
        }

        protected CurveController GetCurveController(Entity Entity)
        {
            ControllableUnitComponent Component = (ControllableUnitComponent)Entity.GetComponent(typeof(ControllableUnitComponent));
            CurveController Controller = new CurveController();
            string outJson = ControllableUnitSystemG.GetControllableUnitJson(
                Entity.World.GetNativePointer(),
                Entity.EntityID);

            Component.ControllableUnit = JsonConvert.DeserializeObject(outJson, Controller.GetType())
                as CurveController;
            Controller = (CurveController)Component.ControllableUnit;
            return Controller;
        }

        public void SetPreviewEntity(Entity Entity)
        {
            PreviewEntity = Entity;
        }

        public bool GetIsPlaying()
        {
            return TimelineControl.GetIsPlaying();
        }

        public void SetTimeProgress(MoveHead MoveHead, decimal Value)
        {
            if (MoveHead == PlayHead)
            {
                _TimeProgress.SetText(string.Format("{0:0.00}", Value));
                SetHeadLocation(Value);
            }
        }

        public decimal GetPlayHeadValue()
        {
            return PlayHead.Value;
        }

        public void SetPreviewNull()
        {
            PreviewEntity = null;
            TrackTree.SetTrackSelected(false);
        }
        #endregion

        #region Refresh
        public List<string> RefreshCurResourceMaterial(Material Material, ComponentTrackHolder ComponentTrackHolder)
        {
            List<string> MaterialPropertyList = new List<string>();
            if (ComponentTrackHolder == null) return MaterialPropertyList;
            List<MaterialTrack> Tracks = new List<MaterialTrack>();
            foreach (var Track in ComponentTrackHolder.GetChildList())
            {
                if (Track is MaterialTrack)
                {
                    Tracks.Add((MaterialTrack)Track);
                }
            }

            foreach (var MaterialTrack in Tracks)
            {
                foreach (var property in Material.Properties)
                {
                    if (MaterialTrack.PropertyStr == property.Name)
                    {
                        if (property.Value.GetType() == typeof(Vector4f))
                        {
                            float X = ((NumericTrack)(MaterialTrack.GetChildList()[0])).GetEditWithPrcogress().GetEditBoxValue();
                            float Y = ((NumericTrack)(MaterialTrack.GetChildList()[1])).GetEditWithPrcogress().GetEditBoxValue();
                            float Z = ((NumericTrack)(MaterialTrack.GetChildList()[2])).GetEditWithPrcogress().GetEditBoxValue();
                            float W = ((NumericTrack)(MaterialTrack.GetChildList()[3])).GetEditWithPrcogress().GetEditBoxValue();
                            property.Value = new Vector4f(X, Y, Z, W);
                        }
                        else if (property.Value.GetType() == typeof(float))
                        {
                            property.Value = ((NumericTrack)(MaterialTrack.GetChildList()[0])).GetEditWithPrcogress().GetEditBoxValue();
                        }
                        MaterialPropertyList.Add(MaterialTrack.PropertyStr);
                    }
                }
            }
            return MaterialPropertyList;
        }

        public void RefreshInspector(Entity Selected)
        {
            InspectorUI InspectorUI = InspectorUI.GetInstance();
            InspectorUI.SetObjectInspected(Selected);
            InspectorUI.InspectObject();
        }

        #endregion

        #region Whether condition

        public bool CheckIsPreviewing(Entity inEntity)
        {
            return inEntity == _Previewing;
        }

        public override bool IsVisible()
        {
            if (_DockingCard == null) return false;
            DockingBlock DockingBlock = _DockingCard.GetDockingBlock();
            if (DockingBlock == null)
                return false;
            else
                return true;
        }

        public bool IsExistCompAndProp(Entity Entity, string ComponentName, string PropertyName)
        {
            // look for cinematic entity
            Entity CinematicEntity = null;
            foreach (var Value in _PreviewEntities)
            {
                if (Value.IsAlive() && Entity == Value)
                {
                    CinematicEntity = Value;
                    break;
                }
            }
            if (CinematicEntity == null)
            {
                return false;
            }
            // look for entity
            Track CurrentEntityTrack = null;

            foreach (var Track in TrackTree.GetRootTracks())
            {
                if (Track is ComponentTrackHolder)
                {
                    if (((ComponentTrackHolder)Track).GetName() == Entity.GetName())
                    {
                        CurrentEntityTrack = Track;
                        break;
                    }
                }
            }

            if (CurrentEntityTrack == null)
            {
                ComponentTrackHolder Holder = (ComponentTrackHolder)AddEntityTrack(Entity);
                if (Holder == null) return false;
                ComponentTrackHolder ComHolder = (ComponentTrackHolder)(Holder).ClickComPropTrack(ComponentName);
                if (ComHolder == null) return false;
            }
            // look for component
            Track CurrentComponentTrack = null;
            foreach (var track in CurrentEntityTrack.GetChildList())
            {
                if (track is ComponentTrackHolder)
                {
                    if (((ComponentTrackHolder)track).GetName() == ComponentName)
                    {
                        CurrentComponentTrack = track;
                        break;
                    }
                }
            }
            if (CurrentComponentTrack == null)
            {
                return false;
            }
            // look for property corresponding to  
            Track PropertyComponentTrack = null;
            foreach (var track in CurrentComponentTrack.GetChildList())
            {
                if (track is ComponentTrack)
                {
                    if (((ComponentTrack)track).GetDisplayPropertyName() == PropertyName)
                    {
                        PropertyComponentTrack = track;
                        break;
                    }
                }
            }
            if (PropertyComponentTrack == null)
            {
                return false;
            }
            return true;
        }

        public bool InitializationJudgment()
        {
            //clear tracktree
            TrackTree.RemoveTrackList();
            //Initialize
            var CurveCtr = ResourceManager.Instance().ConvertGuidToPath(_CurveCtrPath);
            _CurveCtrFileName.SetText(PathHelper.GetNameOfPath(CurveCtr));
            string CurrentSceneFilename = EditorScene.GetInstance().GetCurrentSceneFilename();
            string SceneName = PathHelper.GetNameOfPath(CurrentSceneFilename);
            //if (SceneName == "")
            //{
            //    return true;
            //}

            World World = EditorScene.GetInstance().GetWorld();
            var Ptr = ControllableUnitSystemG.GetCurveControllerRes(World.GetNativePointer(), _CurveCtrPath);
            CurveControllerRes curveControllerRes = new CurveControllerRes(Ptr, false);
            foreach (var Data in curveControllerRes.mCurveData)
            {
                //Console.WriteLine("EUID : {0} , DataCount : {1}", data.mEntityID , data.mDataItems.Count);
            }

            if (World.Root != null)
            {
                Queue<Entity> Queue = new Queue<Entity>();
                Queue.Enqueue(World.Root);
                while (Queue.Count != 0)
                {
                    Entity Head = Queue.Dequeue();
                    foreach (Entity Child in Head.Children)
                    {
                        Queue.Enqueue(Child);
                    }
                    CurveControllerData curveControllerData = new CurveControllerData();
                    foreach (var Data in curveControllerRes.mCurveData)
                    {
                        if (Data.mEntityID.Length == 0 && Data.mEntityName.Length == 0)
                        {
                            if (Head.HasComponent(typeof(Camera)))
                            {
                                Data.mEntityID = Head.EUID;
                            }
                        }

                        CrossUUID CrossUUID = new CrossUUID();
                        if (Data.mPrefabEuid != "")
                        {
                            CrossUUID.FromString(Data.mPrefabEuid);
                        }
                        
                        if (Data.mEntityID == Head.EUID || (!CrossUUID.Invalid() && (CrossUUID.Compare(Head.GetPrefabEuid()) == 0 || CrossUUID.Compare(Head.GetInheritPrefabEuid()) == 0)))
                        {
                            if (Data.mDataItems.Count != 0)
                            {
                                curveControllerData = Data;
                            }
                        }
                        if (Data.mEntityName == Head.GetName())
                        {
                            if (Data.mDataItems.Count != 0)
                            {
                                curveControllerData = Data;
                            }
                        }
                    }
                    if (curveControllerData.mDataItems.Count != 0)
                    {
                        if (Head == LevelSequenceEntity)
                        {
                            AddSubSeqTrack();
                        }
                        else
                        {
                            AddEntityTrack(Head, false);
                        }
                        //Console.WriteLine("Entity Name : {0}   {1}", Head.GetName(), index++);
                    }
                }
            }
            InspectorUI.GetInstance().InspectObject();
            return true;
        }

        private bool ShouldInspectorAddKey(Entity Entity, ref ComponentTrackHolder CurrentEntityTrack)
        {
            // look for cinematic entity
            Entity CinematicEntity = null;
            foreach (var Value in _PreviewEntities)
            {
                if (Value.IsAlive() && Entity == Value)
                {
                    CinematicEntity = Value;
                    break;
                }
            }
            if (CinematicEntity == null)
            {
                //DoSave();
            }
            // look for entity
            foreach (var track in TrackTree.GetTracks())
            {
                if (track is ComponentTrackHolder)
                {
                    if (((ComponentTrackHolder)track).GetName() == Entity.GetName())
                    {
                        CurrentEntityTrack = (ComponentTrackHolder)track;
                        break;
                    }
                }
            }
            return true;
        }
        #endregion

        #region Reload
        public void ReloadControllableUnit(Entity Value, Track Track, bool IsFirst)
        {
            float duration = (float)30.0f;
            ControllableUnitSystemG.GetCurveControllerDuration(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref duration, Value.GetEntityIdStruct());
            if (IsFirst)
            {
                EndHead.Value = (decimal)duration;
            }

            SubSeqTrack SubTrack = Track as SubSeqTrack;
            if (SubTrack != null)
            {
                foreach (var Section in SubTrack.SubSeqTrackRef.SubSeqSections)
                {
                    if (EndHead.Value < new decimal(Section.SectionEnd))
                    {
                        EndHead.Value = (decimal)Section.SectionEnd;
                    }
                }
            }

            StartHead.Value = (decimal)0;
            SaveEndValueSecond = EndHead.Value;

            float fps = 30.0f;
            ControllableUnitSystemG.GetCurveControllerFPS(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref fps, Value.GetEntityIdStruct());
            SetFps((decimal)fps);

            float pr = 1.0f;
            ControllableUnitSystemG.GetCurveControllerPlayRate(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref pr);
            SetPlayRate(pr);

            TimelineControl.StopPlay();
            ComponentTrackHolder ComponentTrackHolder = Track as ComponentTrackHolder;
            if (ComponentTrackHolder != null)
            {
                ComponentTrackHolder.ChangePreviewingEntity(Value);
            }

            ClearSelect();
            ClearModified();
        }

        public void ReloadControllableUnit()
        {
            float duration = (float)EndHead.Value;
            ControllableUnitSystemG.GetCurveControllerDuration(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref duration, _Previewing.GetEntityIdStruct());
            StartHead.Value = (decimal)0;
            SaveEndValueSecond = EndHead.Value;

            float fps = 30.0f;
            ControllableUnitSystemG.GetCurveControllerFPS(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref fps, _Previewing.GetEntityIdStruct());
            SetFps((decimal)fps);

            float pr = 1.0f;
            ControllableUnitSystemG.GetCurveControllerPlayRate(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, ref pr);
            SetPlayRate(pr);

            TimelineControl.StopPlay();
            _ComponentTrackHolder.ChangePreviewingEntity(_Previewing);

            ClearSelect();
            ClearModified();
        }
        #endregion

        #region Track Relation

        private void ExtractComponentValueToSelected(List<ComponentTrack> Tracks)
        {
            foreach (var track in Tracks)
                track.ExtractComponentValueToSelected(PlayHead.Value);
        }

        private void ExtractComponentValueToSelected(List<ComponentTrack> Tracks, List<KeyFrame> SelectedKeyFrames = null)
        {
            foreach (var KeyFrame in SelectedKeyFrames)
            {
                Track OwnerTrack = KeyFrame.OwnerTrack;
                if (OwnerTrack is NumericTrack)
                {
                    Tracks.Add((ComponentTrack)OwnerTrack.GetParentTrack());
                    EditWithProgress Edit = ((NumericTrack)OwnerTrack).GetEditWithPrcogress();
                    double Value = (double)((Point)(KeyFrame.BindObject)).ValueY;
                    Edit.SetText(Value.ToString());
                    ((NumericTrack)OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), Value);
                }
            }
        }

        private void ExtractEventValueTrackToSelected(List<Track> Tracks)
        {
            foreach (var track in Tracks)
                ((EventTrack)track).ExtractComponentValueToSelected(PlayHead.Value);
        }

        private void ExtractEventValueToSelected(List<EventTrack> Tracks, List<KeyFrame> SelectedKeyFrames = null)
        {
            foreach (var KeyFrame in SelectedKeyFrames)
            {
                if (KeyFrame.OwnerTrack is EventTrack)
                {
                    List<string> StringList = new List<string>();
                    var SplitList = ((SmoothPoint)KeyFrame.BindObject).EventString.Split("|");
                    foreach (var Split in SplitList)
                    {
                        if (Split != "")
                        {
                            StringList.Add(Split);
                        }
                    }
                    ((EventTrack)KeyFrame.OwnerTrack).PropertyAddKeyFrame(KeyFrame.GetKeyValue(), StringList);
                }
            }
        }

        private void RemoveComponentValueFromSelected(List<ComponentTrack> Tracks, List<KeyFrame> SelectedKeyFrames = null)
        {
            foreach (var track in Tracks)
                track.RemoveComponentValueFromSelected(SelectedKeyFrames);
        }

        private void RemoveEventValueFromSelected(List<EventTrack> Tracks, List<KeyFrame> SelectedKeyFrames = null)
        {
            foreach (var track in Tracks)
                track.RemoveComponentValueFromSelected(SelectedKeyFrames);
        }

        private void RemoveSubSeqKeyFrameFromSelected(List<SubSeqTrack> Tracks, List<KeyFrame> SelectedKeyFrames = null)
        {
            foreach (var KeyFrame in SelectedKeyFrames)
            {
                KeyFrame.OwnerTrack.RemoveKeyFrame(KeyFrame);
            }
        }

        public List<ComponentTrack> SelectTrackList()
        {
            List<ComponentTrack> candidates = new List<ComponentTrack>();
            foreach (var track in GetSelectedTracks())
            {
                if (track as ComponentTrack != null)
                {
                    var componentTrack = track as ComponentTrack;
                    if (!candidates.Contains(componentTrack))
                    {
                        candidates.Add(componentTrack);
                    }
                }
                else if (track.GetParentTrack() as ComponentTrack != null)
                {
                    var componentTrack = track.GetParentTrack() as ComponentTrack;
                    if (!candidates.Contains(componentTrack))
                    {
                        candidates.Add(componentTrack);
                    }
                }
            }
            return candidates;
        }

        public List<EventTrack> SelectEventTrackList()
        {
            List<EventTrack> candidates = new List<EventTrack>();
            foreach (var track in GetSelectedTracks())
            {
                if (track as EventTrack != null)
                {
                    var eventTrack = track as EventTrack;
                    candidates.Add(eventTrack);
                }
            }
            return candidates;
        }

        public List<T> SelectTTrackList<T>() where T : class
        {
            List<T> candidates = new List<T>();
            foreach (var track in GetSelectedTracks())
            {
                if (track as T != null)
                {
                    var Track = track as T;
                    candidates.Add(Track);
                }
            }
            return candidates;
        }

        public List<Track> SelectedTrackList()
        {
            List<Track> candidates = new List<Track>();
            foreach (var track in GetSelectedTracks())
            {
                if (track as EventTrack != null)
                {
                    var componentTrack = track as EventTrack;
                    candidates.Add(componentTrack);
                }
            }
            return candidates;
        }

        public List<Entity> SelectTrackList(out List<object> Track, out List<object> ParentTrack)
        {
            Track = new List<object>();
            ParentTrack = new List<object>();
            List<Entity> DeleteEntities = new List<Entity>();
            foreach (var track in GetSelectedTracks())
            {
                if (track is ComponentTrack || track is ComponentTrackHolder || track is MaterialTrack || track is EventTrack || track is SubSeqTrack)
                {
                    if (track is ComponentTrackHolder && ((ComponentTrackHolder)track).GetTrackType() == TrackType.Entity)
                    {
                        Entity DeleteEntity = ((ComponentTrackHolder)track).GetPreviewingEntity();
                        DeleteEntities.Add(DeleteEntity);
                        _DeleteEntities.Add(DeleteEntity);
                    }

                    if (!Track.Contains(track))
                    {
                        Track.Add(track);
                        if (track is SubSeqTrack)
                        {
                            ParentTrack.Add(track);
                        }
                        else
                        {
                            ParentTrack.Add(track.GetParentTrack());
                        }
                    }
                }
            }
            return DeleteEntities;
        }

        public void InspectorAddKey(Entity Entity, int ModelIndex, int SubModelIndex, string ParamName, object propertyValue)
        {
            ComponentTrackHolder CurrentEntityTrack = null;
            if (!ShouldInspectorAddKey(Entity, ref CurrentEntityTrack))
                return;

            if (CurrentEntityTrack == null)
            {
                CurrentEntityTrack = (ComponentTrackHolder)AddEntityTrack(Entity);
                if (CurrentEntityTrack == null) return;
            }
            // look for material track
            MaterialTrack CurrentTrack = null;
            foreach (var track in CurrentEntityTrack.GetChildList())
            {
                if (track is MaterialTrack)
                {
                    var MatTrack = track as MaterialTrack;
                    if (MatTrack.IsSameMaterialCurveContext(ModelIndex, SubModelIndex, ParamName))
                    {
                        CurrentTrack = MatTrack;
                        break;
                    }
                }
            }

            if (CurrentTrack == null)
            {
                CurrentTrack = new MaterialTrack();
                CurrentTrack.Initialize(CurrentEntityTrack.GetScaleUI(), CurrentEntityTrack, "", Entity);
                CurrentTrack.SetModelIndexAndParamName(ModelIndex, SubModelIndex, new UniqueString(ParamName));
                CurrentTrack.AddParameterItem(ParamName);
                CurrentTrack.Previewing = Entity;
                CurrentTrack.ExtractComponentValueToEdit(0);
                CurrentEntityTrack.AddTrack(CurrentTrack);
            }
            else
            {
                foreach (var childTrack in CurrentTrack.GetChildList())
                {
                    NumericTrack NumericTrack = (NumericTrack)childTrack;
                    if (NumericTrack != null)
                    {
                        NumericTrack.PropertyAddKeyFrame(CurrentTrack.GetScaleUI().GetCurrentUI().GetHeadLocation(), propertyValue);
                    }
                }
            }

            SetModified();
        }

        public void InspectorAddKey(Entity Entity, string ComponentName, string PropertyName, object PropertyValue)
        {
            SetModified();
            ComponentTrackHolder CurrentEntityTrack = null;
            if (!ShouldInspectorAddKey(Entity, ref CurrentEntityTrack))
                return;

            if (CurrentEntityTrack == null)
            {
                ComponentTrackHolder Holder = (ComponentTrackHolder)AddEntityTrack(Entity);
                if (Holder == null) return;
                ComponentTrackHolder ComHolder = (ComponentTrackHolder)(Holder).ClickComPropTrack(ComponentName);
                if (ComHolder == null) return;
                ComHolder.ClickComPropTrack(PropertyName);
                return;
            }
            // look for component
            Track CurrentComponentTrack = null;
            foreach (var track in CurrentEntityTrack.GetChildList())
            {
                if (track is ComponentTrackHolder)
                {
                    if (((ComponentTrackHolder)track).GetName() == ComponentName)
                    {
                        CurrentComponentTrack = track;
                        break;
                    }
                }
            }
            if (CurrentComponentTrack == null)
            {
                ComponentTrackHolder ComHolder = (ComponentTrackHolder)((ComponentTrackHolder)CurrentEntityTrack).ClickComPropTrack(ComponentName);
                ComHolder.ClickComPropTrack(PropertyName);
                return;
            }
            // look for property corresponding to  
            Track PropertyComponentTrack = null;
            foreach (var track in CurrentComponentTrack.GetChildList())
            {
                if (track is ComponentTrack)
                {
                    if (((ComponentTrack)track).GetDisplayPropertyName() == PropertyName)
                    {
                        PropertyComponentTrack = track;
                        break;
                    }
                }
            }
            if (PropertyComponentTrack == null)
            {
                PropertyComponentTrack = ((ComponentTrackHolder)CurrentComponentTrack).ClickComPropTrack(PropertyName);
                //KeySceneValue(PropertyComponentTrack, PropertyName, PropertyValue);
                return;
            }

            //KeySceneValue(PropertyComponentTrack, PropertyName, PropertyValue);
            if (PropertyName == "Rotation")
            {
                PropertyValue = Quaternion64.EulerToQuaternion64((PropertyValue as Double3).ToRadian());
            }

            foreach (var subtrack in PropertyComponentTrack.GetChildList())
            {
                ((NumericTrack)subtrack).PropertyAddKeyFrame(PlayHead.Value, PropertyValue);
            }

        }

        public void AutoKeyTRSByS()
        {
            if (PreviewEntity == null || (HierarchyUI.GetInstance().GetScene().GetSelection() != null &&
                PreviewEntity != HierarchyUI.GetInstance().GetScene().GetSelection())) return;
            List<ComponentTrack> candidates = new List<ComponentTrack>();
            CinematicOperation.OnCinematicOperationRedo RedoFunc = () =>
            {
                AutoKeyTRS(PreviewEntity);
            };
            CinematicOperation.OnCinematicOperationUndo UndoFunc = () =>
            {
                if (PreviewEntity == null) return;
                candidates = TrackTree.FindTRSComponent(PreviewEntity);
                ComponentTrackHolder TransformHolder = new ComponentTrackHolder(PlayHead);
                if (candidates.Count != 0) TransformHolder = (ComponentTrackHolder)candidates[0].GetParentTrack();
                DeleteTrack(TransformHolder as Track);
                TrackTree.RemoveTrack(TransformHolder as Track);
                foreach (var ComponentTrack in candidates)
                {
                    DeleteTrack(ComponentTrack as Track);
                    TrackTree.RemoveTrack(ComponentTrack as Track);
                }
                //RemoveComponentValueFromSelected(candidates);
            };
            // execute concrete add operation immediately
            RedoFunc();
            // add above execute command into operation queue for REDO/UNDO later
            CinematicOperation operation = new CinematicOperation(candidates, UndoFunc, RedoFunc);
            EditOperationManager.GetInstance().AddOperation(operation);
        }


        public void AutoKeyTRS(Entity Entity)
        {
            if (Entity == null) return;
            Transform Transform = Entity.GetTransformComponent();
            InspectorAddKey(Entity, Transform.GetType().Name, "Translation", Transform.Translation);
            InspectorAddKey(Entity, Transform.GetType().Name, "Rotation", Transform.Rotation);
            InspectorAddKey(Entity, Transform.GetType().Name, "Scale", Transform.Scale);
            RefreshInspector(Entity);
        }

        public void AutoKeySingle(Entity Entity, ManipulatorType ManipulatorType)
        {
            if (PreviewEntity == null || Entity != PreviewEntity) return;
            Transform Transform = Entity.GetTransformComponent();
            if (ManipulatorType == ManipulatorType.LocalTranslator || ManipulatorType == ManipulatorType.GlobalTranslator)
            {
                InspectorAddKey(Entity, Transform.GetType().Name, "Translation", Transform.Translation);
            }
            else if (ManipulatorType == ManipulatorType.LocalRotator || ManipulatorType == ManipulatorType.GlobalRotator)
            {
                InspectorAddKey(Entity, Transform.GetType().Name, "Rotation", Transform.Rotation);
            }
            else if (ManipulatorType == ManipulatorType.LocalScaler || ManipulatorType == ManipulatorType.GlobalScaler)
            {
                InspectorAddKey(Entity, Transform.GetType().Name, "Scale", Transform.Scale);
            }
            RefreshInspector(Entity);
        }


        protected void RecursiveTrack(Track Item)
        {
            if (Item is ParticleMaterialTrack)
            {
                (Item as ParticleMaterialTrack).ExtractComponentValueToEdit(PlayHead.Value);
            }
            else if (Item is MaterialTrack)
            {
                (Item as MaterialTrack).ExtractComponentValueToEdit(PlayHead.Value);
            }
            else if (Item is EventTrack)
            {
                (Item as EventTrack).ExtractComponentValueToEdit(PlayHead.Value);
            }
            else if (Item is ComponentTrackHolder)
            {
                List<Track> Tracks = Item.GetChildList();
                foreach (var value in Tracks)
                {
                    RecursiveTrack(value);
                }
            }
            else if (Item is ComponentTrack)
            {
                ((ComponentTrack)Item).ExtractComponentValueToEdit(PlayHead.Value);
            }
        }
        #endregion

        #region Restore Scene

        public void KeySceneValue(Track Track, string PropertyName, object PropertyValue)
        {
            foreach (var subtrack in Track.GetChildList())
            {
                if (PropertyName == "Rotation" && subtrack.GetName() == "t") continue;
                ((NumericTrack)subtrack).PropertyAddKeyFrame(new Decimal(Track.OriginKeyValue), PropertyValue);
            }
        }

        public void RestoreSceneValue(bool status = false)
        {
            SetTimeProgress(PlayHead, (Decimal)0.0f);

            if (LevelSequenceEntity.IsAlive())
            {
                ControllableUnitSystemG.SetCurveControllerCursor(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, Track.OriginKeyValue);
                foreach (var Entity in _PreviewEntities)
                {
                    Entity.GetTransformComponent().SyncDataFromEngine();
                    Entity.GetTransformComponent().RefreshTransform();
                }
            }

            if (status)
            {
                SetEditStatus(EditStatus.ExitEdit);
                TimelineControl.SetEditButtonText(TimelineControlUI.EnterEdit);
                GetFrameUI().GetPanelStatusBar().SetBackgroundColor(Color.FromRGB(0, 0, 0));
            }
            InspectorUI.GetInstance().InspectObject();
        }

        public void SetEditStatus(EditStatus EditStatus)
        {
            _EditStatus = EditStatus;
            //Console.WriteLine("Edit Status : {0}", GetEditStatus());
        }

        public EditStatus GetEditStatus()
        {
            return _EditStatus;
        }

        #endregion

        #region video
        private void InitCaptureState()
        {
            _CaptureFrameIndex = 0;
            _TimelineFrameIndex = 0;
            _CamerasStartTimeDict.Clear();
            if (_CaptureCamera != null)
            {
                _CaptureCamera.Enable = false;
                // @cx
                //_CaptureCamera.RenderToTargetCamera = false;
                _CaptureCamera.Enable = false;
                EditorScene.GetInstance().DeleteEntities(new List<Entity> { _CaptureCamera.Entity }, false);
                _CaptureCamera = null;
            }
            if (_CubemapCapturerList != null)
            {
                EditorScene.GetInstance().DeleteEntities(_CubemapCapturerList, false);
                _CubemapCapturerList = null;
            }

            if (_CheckCaptureFrame.GetChecked())
            {
                _hasCaptureTask = true;
            }
        }

        private void FinishCaptureState()
        {
            CrossEngine.GetInstance()._CrossEngineInterface.SetFixedUpdateMode(false, 0.033f);

            if (_CubemapCapturerList != null)
            {
                foreach (var cameraEntity in _CubemapCapturerList)
                {
                    toDeleteCaptureCameras.Add(cameraEntity);
                    var cubemapCaptureCamera = cameraEntity.GetComponent(typeof(Camera)) as Camera;
                    cubemapCaptureCamera.Enable = false;
                    // @cx
                    //cubemapCaptureCamera.RenderToTargetCamera = false;
                    cubemapCaptureCamera.Enable = false;
                }
                _CubemapCapturerList = null;
            }
            if (_CaptureCamera != null)
            {
                _CaptureCamera.Enable = false;
                // @cx
                //_CaptureCamera.RenderToTargetCamera = false;
                _CaptureCamera.Enable = false;
                toDeleteCaptureCameras.Add(_CaptureCamera.Entity);
                _CaptureCamera = null;
            }

            if (_hasCaptureTask)
            {
                // leave enough time for async readback task finish
                lagFrame = 4;
                hasDeleteCameraTask = true;
                _hasCaptureTask = false;
            }
        }

        private void InitPreviewState()
        {
            _PreviewCameraSettingsDict.Clear();

            if (_CheckCameraView.GetChecked())
            {
                EditorScene.GetInstance().SetScreenShotMode(true);
                EditorSceneUI.GetInstance().SetScreenShotMode(true);
            }

            if (_CaptureResolution == VIEWPORT || _CaptureMode == CaptureMode.Cubemap)
                return;
        }
        private void FinishPreviewState()
        {
            foreach (var cameraSettings in _PreviewCameraSettingsDict)
            {
                Camera entityCamera = cameraSettings.Key.GetComponent(typeof(Camera)) as Camera;
                if (entityCamera != null)
                {
                    entityCamera.SensorWidth = cameraSettings.Value.sensorWidth;
                    entityCamera.SensorHeight = cameraSettings.Value.sensorHeight;
                }
            }

            _PreviewCameraSettingsDict.Clear();

            if (_CheckCameraView.GetChecked())
            {
                EditorScene.GetInstance().SetScreenShotMode(false);
                EditorSceneUI.GetInstance().SetScreenShotMode(false);
            }

            if (_CaptureResolution == VIEWPORT || _CaptureMode == CaptureMode.Cubemap)
                return;
        }

        private void CreateCaptureDirectory()
        {
            string date = DateTime.Now.ToString("yy-MM-dd"); // includes leading zeros
            string time = DateTime.Now.ToString("HH.mm.ss"); // includes leading zeros
            string directory = MainUI.GetInstance().GetProjectDirectory() + "/Saved/Sequence/" + date;
            if (_SetSavePath != null)
            {
                directory = _SetSavePath + date;
            }

            bool hasCapturer = _CaptureCamera != null || (_CubemapCapturerList != null && _CubemapCapturerList.Count > 0);

            if (_CheckCaptureFrame.GetChecked() && _PinnedOutputDir != null)
            {
                _CaptureStagingFolder = _PinnedOutputDir;
            }
            else if (_CheckCaptureFrame.GetChecked() && hasCapturer)
            {
                _CaptureStagingFolder = directory + "_" + time;
                Directory.CreateDirectory(_CaptureStagingFolder);

                if (_PinnedOutput)
                {
                    _PinnedOutputDir = _CaptureStagingFolder;
                }
            }
        }

        private bool CanDoCapture()
        {
            bool satisfied = false;
            if (_CaptureMode == CaptureMode.Flimlic)
                satisfied = _CheckCaptureFrame.GetChecked() && _CaptureCamera != null && _CaptureCamera.Entity != null && _CaptureStagingFolder != null;
            else if (_CaptureMode == CaptureMode.Cubemap || _CaptureMode == CaptureMode.Panoramic)
                satisfied = _CheckCaptureFrame.GetChecked() && _CubemapCapturerList != null && _CaptureStagingFolder != null;

            return satisfied;
        }

        private void OnBeginCapture()
        {
            _PreviewCamerasList.Clear();
            //clamp custom resolution if set
            _CustomWidth = Math.Clamp(_CustomWidth, RESO_LIMIT_MIN, RESO_LIMIT_MAX);
            _CustomHeight = Math.Clamp(_CustomHeight, RESO_LIMIT_MIN, RESO_LIMIT_MAX);
            _CustomWidthEdit.SetText(_CustomWidth.ToString());
            _CustomHeightEdit.SetText(_CustomHeight.ToString());

            _ResolutionPresets[Custom] = new Tuple<uint, uint>((uint)_CustomWidth, (uint)_CustomHeight);

            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();
            Camera PreviewCandidate = null;
            // if has camera then set as main when begin playing
            foreach (var Entity in _PreviewEntities)
            {
                Camera EntityCamera = Entity.GetComponent(typeof(Camera)) as Camera;
                if (EntityCamera != null && Entity.World != null && EntityCamera.RuntimeHasComponent())
                {
                    // @cx
                    //EntityCamera.RenderToTargetCamera = false;
                    EntityCamera.Enable = false;
                    _PreviewCamerasList.Add(Entity);

                    var settings = new CameraSettings
                    {
                        sensorWidth = EntityCamera.SensorWidth,
                        sensorHeight = EntityCamera.SensorHeight
                    };
                    _PreviewCameraSettingsDict.Add(Entity, settings);
                }
            }

            if (_PreviewCamerasList.Count == 0)
            {
                _hasCaptureTask = false;
                return;
            }

            foreach (var Entity in _PreviewCamerasList)
            {
                Camera EntityCamera = Entity.GetComponent(typeof(Camera)) as Camera;
                PreviewCandidate = EntityCamera;
                if (EntityCamera == _PreviewingCamera)
                {
                    break;
                }
            }

            SortPreviewCameras();

            //use the most front camera
            if (_TrackMode == TrackMode.Composite && _PreviewCamerasList.Count > 0)
            {
                var frontCamera = _PreviewCamerasList[_PreviewCamerasList.Count - 1];
                _PreviewCamerasList.RemoveAt(_PreviewCamerasList.Count - 1);

                _PreviewingCamera = frontCamera.GetComponent(typeof(Camera)) as Camera;
            }
            else if (PreviewCandidate != null)
            {
                _PreviewingCamera = PreviewCandidate;
            }

            if (_PreviewingCamera != null)
            {
                _PreviewingCamera.SetMainCamera();
                _PreviewingCamera.Enable = true;
            }

            if (_CheckCaptureFrame.GetChecked() == false)
                return;

            float fixedDeltaTime = (1.0f / (float)_VideoFps) / _CaptureInterval;
            CrossEngine.GetInstance()._CrossEngineInterface.SetFixedUpdateMode(true, fixedDeltaTime);

            if (_CaptureMode == CaptureMode.Cubemap || _CaptureMode == CaptureMode.Panoramic)
            {
                SetupCubemapCaptureCameras();
            }
            else
            {
                SetupViewportCaptureCamera();
            }

            if (_PinnedOutput)
            {
                decimal time = GetHeadLocation() - GetStartLocation();
                _CaptureFrameIndex = (long)Math.Floor(time * GetTargetVideoFrameRate());
            }
        }
        private void OnFinishCapture()
        {
            RestoreCaptureCameras();
        }

        private void GenerateVideo()
        {
            //Clicross.RenderTargetUtil.Camera_WaitForCompletion(CrossEngine.GetInstance()._CrossEngineInterface, 0);

            int OutputFrameRate = (int)GetTargetVideoFrameRate();
            FFmpegWrapper.GeneratorVideoFromImages(_CaptureStagingFolder, _SaveTypeSuffix[(UInt32)_ImageSaveType], OutputFrameRate);
        }

        private void SortPreviewCameras()
        {
            List<KeyValuePair<Entity, decimal>> CamerasStartTimeList = new List<KeyValuePair<Entity, decimal>>();
            foreach (var cameraEntity in _PreviewCamerasList)
            {
                foreach (var track in TrackTree.GetTracks())
                {
                    if (track is ComponentTrack && (track as ComponentTrack).Previewing == cameraEntity)
                    {
                        var keyFrames = track.GetKeyFrames();

                        // keyFrames start from [1], reserve [0] as other use 
                        if (keyFrames.Count > 1)
                        {
                            _CamerasStartTimeDict[cameraEntity] = track.GetKeyFrameBegin();
                            break;
                        }
                    }
                }
            }

            foreach (var pair in _CamerasStartTimeDict)
                CamerasStartTimeList.Add(pair);

            CamerasStartTimeList.Sort((x, y) =>
            { return x.Value.CompareTo(y.Value); });

            _PreviewCamerasList.Clear();
            foreach (var pair in CamerasStartTimeList)
                _PreviewCamerasList.Add(pair.Key);
            _PreviewCamerasList.Reverse();
        }

        private void SwitchPreviewCamera(decimal currentHead)
        {
            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();

            if (_TrackMode == TrackMode.Composite && _PreviewCamerasList.Count > 0)
            {
                var frontCamera = _PreviewCamerasList[_PreviewCamerasList.Count - 1];
                var startTime = _CamerasStartTimeDict[frontCamera];
                if (currentHead < startTime)
                    return;
                _PreviewingCamera.Enable = false;
                _PreviewCamerasList.RemoveAt(_PreviewCamerasList.Count - 1);

                _PreviewingCamera = frontCamera.GetComponent(typeof(Camera)) as Camera;
                _PreviewingCamera.SetMainCamera();
                _PreviewingCamera.Enable = true;
                _PreviewingCamera.SensorWidth = EditorCamera.SensorWidth;
            }
        }

        private void SwitchCaptureCamera(decimal currentHead)
        {
            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();

            if (_CaptureCamera != null)
            {
                _CaptureCamera.FocalLength = _PreviewingCamera.FocalLength;
                _CaptureCamera.SensorHeight = _PreviewingCamera.SensorHeight;
                _CaptureCamera.SensorWidth = _PreviewingCamera.SensorWidth;
            }

            if (_TrackMode == TrackMode.Composite && _PreviewCamerasList.Count > 0)
            {
                var frontCamera = _PreviewCamerasList[_PreviewCamerasList.Count - 1];
                var startTime = _CamerasStartTimeDict[frontCamera];
                if (currentHead < startTime)
                    return;
                _PreviewCamerasList.RemoveAt(_PreviewCamerasList.Count - 1);
                _PreviewingCamera.Enable = false;

                if (_CaptureMode == CaptureMode.Flimlic && _CaptureCamera != null)
                {
                    {
                        var ParentNode = _PreviewingCamera.Entity;
                        ParentNode.RemoveChildEntity(_CaptureCamera.Entity);
                    }

                    _PreviewingCamera = frontCamera.GetComponent(typeof(Camera)) as Camera;
                    _PreviewingCamera.SetMainCamera();
                    _PreviewingCamera.Enable = true;

                    {
                        var ParentNode = _PreviewingCamera.Entity;
                        Matrix4x4d Matrix = new Matrix4x4d();
                        Matrix.LoadIdentity();

                        ParentNode.AddChildEntity(_CaptureCamera.Entity);
                        _CaptureCamera.Entity.RuntimeJointToParent();

                        Transform TransformComponent = _CaptureCamera.Entity.GetTransformComponent();
                        TransformComponent.SetMatrix(ref Matrix);

                        _CaptureCamera.FocalLength = _PreviewingCamera.FocalLength;
                        _CaptureCamera.PerspectiveNear = _PreviewingCamera.PerspectiveNear;
                        _CaptureCamera.PerspectiveFar = _PreviewingCamera.PerspectiveFar;
                    }
                }
                else if ((_CaptureMode == CaptureMode.Cubemap || _CaptureMode == CaptureMode.Panoramic) && _CubemapCapturerList.Count > 0)
                {
                    var ParentNode = _PreviewingCamera.Entity;
                    foreach (var entity in _CubemapCapturerList)
                    {
                        ParentNode.RemoveChildEntity(entity);
                    }
                    _PreviewingCamera = frontCamera.GetComponent(typeof(Camera)) as Camera;
                    _PreviewingCamera.SetMainCamera();
                    _PreviewingCamera.Enable = true;

                    Matrix4x4d Matrix = new Matrix4x4d();
                    Matrix.LoadIdentity();

                    ParentNode = _PreviewingCamera.Entity;
                    int faceId = 0;
                    foreach (var entity in _CubemapCapturerList)
                    {
                        ParentNode.AddChildEntity(entity);
                        Transform transform = entity.GetTransformComponent();
                        entity.RuntimeJointToParent();
                        transform.SetMatrix(ref Matrix);
                        SetCaptureCameraLookAt(true, faceId++, transform);
                    }
                }
            }
        }

        private void CaptureFrame()
        {
            Device Device = GetDevice();
            World World = EditorScene.GetInstance().GetWorld();
            string file = _CaptureStagingFolder + "/img" + String.Format("{0:00000}", _CaptureFrameIndex++) + _SaveTypeSuffix[(UInt32)_ImageSaveType];

            if (_CaptureMode == CaptureMode.Flimlic && _CaptureCamera != null)
            {
#if false
                if (useDotNet)
                {
                    var width = EditorSceneUI.GetInstance().GetPanelWidth();
                    var height = EditorSceneUI.GetInstance().GetPanelHeight();
                    var x = Device.GetX() + EditorSceneUI.GetInstance().GetPanel().GetScreenX();
                    var y = Device.GetY() + EditorSceneUI.GetInstance().GetPanel().GetScreenY();

                    using (System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(width, height))
                    {
                        using (System.Drawing.Graphics g = System.Drawing.Graphics.FromImage(bitmap))
                        {
                            g.CopyFromScreen(new System.Drawing.Point(x, y), System.Drawing.Point.Empty, new System.Drawing.Size(width, height));
                        }
                        if(_ImageSaveType == ImageSaveType.PNG)
                            bitmap.Save(file, System.Drawing.Imaging.ImageFormat.Png);
                        else if(_ImageSaveType == ImageSaveType.BMP)
                            bitmap.Save(file, System.Drawing.Imaging.ImageFormat.Bmp);
                    }
                }
                else
#endif
                {
                    int width = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelWidth() : (int)_ResolutionPresets[_CaptureResolution].Item1;
                    int height = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelHeight() : (int)_ResolutionPresets[_CaptureResolution].Item2;
                    Clicross.RenderTargetUtil.Camera_SaveRenderTarget_Simple_Async(World._WorldInterface, _CaptureCamera.Entity.EntityID, file, (uint)width, (uint)height, true, (UInt32)_ImageSaveType);
                }
            }
            else if (_CaptureMode == CaptureMode.Cubemap && _CubemapCapturerList != null)
            {
                int width = (int)GetCubemapResolution();
                Clicross.EntityList entityList = new Clicross.EntityList();
                List<ulong> CameraIDs = new List<ulong>(6);
                foreach (var camera in _CubemapCapturerList)
                {
                    CameraIDs.Add(camera.EntityID);
                    entityList.mEntities.Add(camera.EntityID);
                }
                Clicross.RenderTargetUtil.Camera_SaveRenderTargetToCubemap_Async(World._WorldInterface, entityList, CameraIDs.Count, file, (uint)width, true, (UInt32)_ImageSaveType);
            }
            else if (_CaptureMode == CaptureMode.Panoramic && _CubemapCapturerList != null)
            {
                int width = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelWidth() : (int)_ResolutionPresets[_CaptureResolution].Item1;
                int height = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelHeight() : (int)_ResolutionPresets[_CaptureResolution].Item2;
                List<ulong> CameraIDs = new List<ulong>(6);
                Clicross.EntityList entityList = new Clicross.EntityList();
                foreach (var camera in _CubemapCapturerList)
                {
                    CameraIDs.Add(camera.EntityID);
                    entityList.mEntities.Add(camera.EntityID);
                }
                Clicross.RenderTargetUtil.Camera_SaveRenderTargetToPanorama_Async(World._WorldInterface, entityList, CameraIDs.Count, file, (uint)width, (uint)height, true, (UInt32)_ImageSaveType);
            }
        }

        private void _CheckCameraView_ClickedEvent(Check Sender)
        {
            if (TimelineControl.GetIsPlaying())
            {
                Sender.SetChecked(!Sender.GetChecked());
            }

            if (Sender.GetChecked() == false)
            {
                _CheckCaptureFrame.SetChecked(false);
            }
        }
        private void _CheckCaptureFrame_ClickedEvent(Check Sender)
        {
            if (Sender.GetChecked())
            {
                _CheckCameraView.SetChecked(true);
            }
            else
            {
                RestoreCaptureCameras();
            }
        }

        private void _SetPathButton_ClickedEvent(Button Sender)
        {
            string SelectedFilePath = "";
            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.AddDefaultDrives();
            PathInputUI.Initialize(GetUIManager(), "Select Capture Save Path", PathInputUIType.OpenFolder, null, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {
                    bool isSysPath = SelectedFilePath.Contains("$RECYCLE.BIN") || SelectedFilePath.Contains("System Volume Information");

                    if (Directory.Exists(SelectedFilePath) && !isSysPath)
                        _SetSavePath = SelectedFilePath + "/";
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        private void _GenerateVideoButtonManually_ClickEvent(Button Sender)
        {
            string SelectedFilePath = "";
            bool bContentsOnly = true;
            PathInputUIEx PathInputUI = new PathInputUIEx();
            string DefaultDrivePath = EditorUtilities.AddEditorDrives(PathInputUI, bContentsOnly);
            PathInputUI.AddDefaultDrives();
            PathInputUI.Initialize(GetUIManager(), "Select Captures Directory and Generate Video", PathInputUIType.OpenFolder, null, DefaultDrivePath);
            PathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {
                SelectedFilePath = PathInputed;
            };
            PathInputUI.DialogCloseEvent += (DialogUI DialogSender) =>
            {
                if (SelectedFilePath != "")
                {
                    bool isSysPath = SelectedFilePath.Contains("$RECYCLE.BIN") || SelectedFilePath.Contains("System Volume Information");

                    if (Directory.Exists(SelectedFilePath) && !isSysPath)
                    {
                        int OutputFrameRate = (int)GetTargetVideoFrameRate();
                        for (int i = 0; i < _SaveTypeSuffix.Length; i++)
                        {
                            FFmpegWrapper.GeneratorVideoFromImages(SelectedFilePath, _SaveTypeSuffix[i], OutputFrameRate);
                        }
                    }
                }
            };
            DialogUIManager.GetInstance().ShowDialogUI(PathInputUI);
        }

        private void _SavePathButton_ClickedEvent(Button Sender)
        {
            if (_CaptureStagingFolder == null)
            {
                string path = MainUI.GetInstance().GetProjectDirectory() + "/Saved/Sequence/";
                ProcessHelper.OpenContainingFolder(path);
            }
            else
            {
                ProcessHelper.OpenContainingFolder(_CaptureStagingFolder);
            }
        }

        private void _PinOutputDirButton_ClickedEvent(Button Sender)
        {
            if (_PinnedOutput == false)
            {
                Sender.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrame.png"));
                _PinnedOutput = true;
                _PinnedOutputDir = _CaptureStagingFolder;
            }
            else
            {
                Sender.SetImage(UIManager.LoadUIImage("EngineResource/Editor/Icons/Edit/KeyFrameGrey.png"));
                _PinnedOutput = false;
                _PinnedOutputDir = null;
            }
        }

        private void OnResolutionChanged(ComboBox Sender)
        {
            _CaptureResolution = _BoxCaptureResolution.GetSelectedItemText();

            if (_CaptureResolution == Custom)
            {
                _CustomWidthEdit.SetEnable(true);
                _CustomWidthEdit.SetTextColor(Color.White);

                _CustomHeightEdit.SetEnable(true);
                _CustomHeightEdit.SetTextColor(Color.White);
            }
            else
            {
                _CustomWidthEdit.SetEnable(false);
                _CustomWidthEdit.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);

                _CustomHeightEdit.SetEnable(false);
                _CustomHeightEdit.SetTextColor(Color.EDITOR_UI_GRAY_TEXT_COLOR);

                int width = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelWidth() : (int)_ResolutionPresets[_CaptureResolution].Item1;
                int height = _CaptureResolution == VIEWPORT ? EditorSceneUI.GetInstance().GetPanelHeight() : (int)_ResolutionPresets[_CaptureResolution].Item2;
                string Width = width.ToString();
                string Height = height.ToString();
                _CustomWidthEdit.SetText(Width);
                _CustomHeightEdit.SetText(Height);
            }

        }

        private void OnCustomWidthTextChanged(Edit Sender)
        {
            if (int.TryParse(Sender.GetText(), out int width))
            {
                _CustomWidth = width;
            }
            else
            {
                EditorLogger.Log(LogMessageType.Error, "unrecognized input width format");
            }
        }

        private void OnCustomHeightTextChanged(Edit Sender)
        {
            if (int.TryParse(Sender.GetText(), out int height))
            {
                _CustomHeight = height;
            }
            else
            {
                EditorLogger.Log(LogMessageType.Error, "unrecognized input height format");
            }
        }

        private decimal GetTargetVideoFrameRate()
        {
            return _VideoFps;
        }

        private uint GetCubemapResolution()
        {
            if (_CaptureResolution == VIEWPORT)
            {
                return 512;
            }
            else
            {
                return _CubemapResolution[_CaptureResolution];
            }
        }

        private void SetupCubemapCaptureCameras()
        {
            if (_PreviewingCamera == null)
                return;
            var ParentNode = _PreviewingCamera.Entity;

            Matrix4x4d Matrix = new Matrix4x4d();
            Matrix.LoadIdentity();

            _CubemapCapturerList = new List<Entity>(6);
            int faceId = 0;
            foreach (var face in Enum.GetNames(typeof(CubeFace)))
            {
                Entity NewEntity = ParentNode.World.CreateEntity();
                ParentNode.AddChildEntity(NewEntity);
                Transform TransformComponent = NewEntity.CreateComponent<Transform>();
                NewEntity.RuntimeJointToParent();
                TransformComponent.SetMatrix(ref Matrix);
                SetCaptureCameraLookAt(true, faceId++, TransformComponent);

                NewEntity.CreateComponent<Camera>();
                var NewCamera = NewEntity.GetComponent(typeof(Camera)) as Camera;
                NewCamera.Reset();
                NewCamera.Enable = true;
                // @cx
                //NewCamera.RenderToTargetCamera = true;
                NewCamera.Enable = true;
                NewCamera.TargetWidth = GetCubemapResolution();
                NewCamera.TargetHeight = GetCubemapResolution();
                NewCamera.FocalLength = _PreviewingCamera.FocalLength;
                //to derive _PerspectiveHorizontalFovDegree = 90, we should make sensorWidth / (2 * FocalLength) = 1
                NewCamera.SensorWidth = 2 * NewCamera.FocalLength;
                NewCamera.SensorHeight = 2 * NewCamera.FocalLength;

                NewCamera.PerspectiveNear = _PreviewingCamera.PerspectiveNear;
                NewCamera.PerspectiveFar = _PreviewingCamera.PerspectiveFar;

                var info = new RenderTextureInfo
                {
                    Name = "CaptureCameraRenderTexture" + face,
                    Dimension = TextureDimension.Tex2D,
                    Format = RenderTextureFormat.R8G8B8A8_UNorm,
                    Width = GetCubemapResolution(),
                    Height = GetCubemapResolution(),
                };
                NewCamera.SetRenderTexture(new RenderTexture(info));
                NewEntity.SetName(EditorScene.GetInstance().CalculateNewName(ParentNode, face));
                _CubemapCapturerList.Add(NewEntity);
            }
        }

        private void SetupViewportCaptureCamera()
        {
            if (_PreviewingCamera == null)
                return;
            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();

            var ParentNode = _PreviewingCamera.Entity;
            Matrix4x4d Matrix = new Matrix4x4d();
            Matrix.LoadIdentity();

            Entity NewEntity = ParentNode.World.CreateEntity();
            ParentNode.AddChildEntity(NewEntity);
            Transform TransformComponent = NewEntity.CreateComponent<Transform>();
            NewEntity.RuntimeJointToParent();
            TransformComponent.SetMatrix(ref Matrix);

            NewEntity.CreateComponent<Camera>();
            NewEntity.SetName(EditorScene.GetInstance().CalculateNewName(ParentNode, "capture0"));
            _CaptureCamera = NewEntity.GetComponent(typeof(Camera)) as Camera;
            _CaptureCamera.SetFrom(_PreviewingCamera);
            _CaptureCamera.Enable = true;

            if (_CaptureResolution == VIEWPORT)
            {
                _CaptureCamera.SensorWidth = EditorCamera.SensorWidth;
                _CaptureCamera.SensorHeight = EditorCamera.SensorHeight;
                _CaptureCamera.TargetWidth = (uint)EditorSceneUI.GetInstance().GetPanelWidth();
                _CaptureCamera.TargetHeight = (uint)EditorSceneUI.GetInstance().GetPanelHeight();
            }
            else
            {
                _CaptureCamera.TargetWidth = _ResolutionPresets[_CaptureResolution].Item1;
                _CaptureCamera.TargetHeight = _ResolutionPresets[_CaptureResolution].Item2;
            }
#if false
            if (useDotNet)
            {
				// @cx
				//_CaptureCamera.RenderToTargetCamera = false;
				_CaptureCamera.Enable = false;
			}
			else
#endif
            {
                // @cx
                //_CaptureCamera.RenderToTargetCamera = true;
                _CaptureCamera.Enable = true;
                var info = new RenderTextureInfo
                {
                    Name = "CaptureCameraRenderTexture",
                    Dimension = TextureDimension.Tex2D,
                    Format = RenderTextureFormat.R8G8B8A8_UNorm,
                    Width = _CaptureCamera.TargetWidth,
                    Height = _CaptureCamera.TargetHeight,
                };
                _CaptureCamera.SetRenderTexture(_CaptureCameraRenderTexture = new RenderTexture(info));
            }
        }

        private void RestoreCaptureCameras()
        {
            if (_CubemapCapturerList != null)
            {
                foreach (var cameraEntity in _CubemapCapturerList)
                {
                    toDeleteCaptureCameras.Add(cameraEntity);
                    var cubemapCaptureCamera = cameraEntity.GetComponent(typeof(Camera)) as Camera;
                    cubemapCaptureCamera.Enable = false;
                    // @cx
                    //cubemapCaptureCamera.RenderToTargetCamera = false;
                    cubemapCaptureCamera.Enable = false;
                }
                _CubemapCapturerList = null;
            }
            if (_CaptureCamera != null)
            {
                _CaptureCamera.Enable = false;
                // @cx
                //_CaptureCamera.RenderToTargetCamera = false;
                _CaptureCamera.Enable = false;
                toDeleteCaptureCameras.Add(_CaptureCamera.Entity);
                _CaptureCamera = null;
            }

            _CamerasStartTimeDict.Clear();

            CrossEngine.GetInstance()._CrossEngineInterface.SetFixedUpdateMode(false, 0.033f);
            Camera EditorCamera = EditorScene.GetInstance().GetCameraEntityCamera();
            EditorCamera.SetMainCamera();
        }

        private void SetCaptureCameraLookAt(bool CameraReside, long FaceId, Transform Transform)
        {
            if (!CameraReside)
                return;

            CubeFace face = (CubeFace)FaceId;
            switch (face)
            {
                case CubeFace.Front:
                    break;
                case CubeFace.Right:
                    Transform.Rotation = new Double3(Transform.Rotation.x, Transform.Rotation.y + 90, Transform.Rotation.z);
                    break;
                case CubeFace.Back:
                    Transform.Rotation = new Double3(Transform.Rotation.x, Transform.Rotation.y + 180, Transform.Rotation.z);
                    break;
                case CubeFace.Left:
                    Transform.Rotation = new Double3(Transform.Rotation.x, Transform.Rotation.y + 270, Transform.Rotation.z);
                    break;
                case CubeFace.Top:
                    Transform.Rotation = new Double3(Transform.Rotation.x - 90, Transform.Rotation.y, Transform.Rotation.z);
                    break;
                case CubeFace.Bottom:
                    Transform.Rotation = new Double3(Transform.Rotation.x + 90, Transform.Rotation.y, Transform.Rotation.z);
                    break;
            }
        }

        private void OnCaptureFrameUpdate(long TimeElapsed)
        {
            long FrameIndexRemainder = (_TimelineFrameIndex % _CaptureInterval);
            bool DoCapture = (FrameIndexRemainder == (_CaptureInterval - 1));
            //skip first capture
            DoCapture = DoCapture && _TimelineFrameIndex > _CaptureInterval;
            //interleave the Capture and Advance so the camera motion lag the capturing
            decimal ShouldAdvcaned = (_TimelineFrameIndex % _CaptureInterval == 0) ? 1m : 0m; ;

            // step = timelineLength * videoFrameRate;
            // stepLength = TimelineLength / step = 1.0 / videoFrameRate;
            decimal VideoFrameRate = GetTargetVideoFrameRate();
            decimal StepLength = (decimal)1.0f / VideoFrameRate;
            decimal NewValue = GetHeadLocation() + StepLength * ShouldAdvcaned;
            if (NewValue >= GetEndLocation())
            {
                if (TimelineControl.GetIsLoop())
                {
                    if (GetLength() != 0m)
                        NewValue = GetStartLocation() + (NewValue - GetStartLocation()) % GetLength();
                    else
                        NewValue = GetStartLocation();
                }
                else
                {
                    NewValue = GetEndLocation();
                    TimelineControl.StopPlay();
                    OnFinishCapture();
                    DoCapture = false;
                }
            }

            if (DoCapture)
            {
                CaptureFrame();
            }

            if (ShouldAdvcaned > 0)
            {
                SwitchCaptureCamera(NewValue);

                SetHeadLocation(NewValue);
                bShouldUpdateValue = true;
            }
            else
            {
                bShouldUpdateValue = false;
            }

            _TimelineFrameIndex++;
        }

        public int GetVideoFPS()
        {
            return _VideoFps;
        }
        public void SetVideoFPS(int InFPS)
        {
            _VideoFps = InFPS;
        }

        public long GetCaptureFrameIndex()
        {
            return _CaptureFrameIndex;
        }
        public void SetCaptureFrameIndex(long Index)
        {
            _CaptureFrameIndex = Index;
        }
        public string GetCaptureFolder()
        {
            return _CaptureStagingFolder;
        }
        public void SetCaptureFolder(string InFolder)
        {
            _CaptureStagingFolder = InFolder;
        }

        public static void BeginRecordVideoPIE(out int FPS, out long CaptureFrameIndex, out string CaptureStagingFolder)
        {
            FPS = 30;
            CaptureFrameIndex = 0;
            CrossEngine.GetInstance()._CrossEngineInterface.SetFixedUpdateMode(true, 1.0f / FPS);

            string date = DateTime.Now.ToString("yy-MM-dd"); // includes leading zeros
            string time = DateTime.Now.ToString("HH.mm.ss"); // includes leading zeros
            string directory = MainUI.GetInstance().GetProjectDirectory() + "/Saved/Sequence/" + date;

            //Device Device = GetDevice();
            World World = EditorScene.GetInstance().GetWorld();
            CaptureStagingFolder = directory + "_" + time;
            Directory.CreateDirectory(CaptureStagingFolder);
        }

        public static void DoRecordVideoPIE(Device Device, string CaptureStagingFolder, long CaptureFrameIndex)
        {
            string file = CaptureStagingFolder + "/img" + String.Format("{0:00000}", CaptureFrameIndex) + ".bmp";

            var width = EditorSceneUI.GetInstance().GetPanelWidth();
            var height = EditorSceneUI.GetInstance().GetPanelHeight();
            var x = Device.GetX() + EditorSceneUI.GetInstance().GetPanel().GetScreenX();
            var y = Device.GetY() + EditorSceneUI.GetInstance().GetPanel().GetScreenY();

            using (System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap(width, height))
            {
                using (System.Drawing.Graphics g = System.Drawing.Graphics.FromImage(bitmap))
                {
                    g.CopyFromScreen(new System.Drawing.Point(x, y), System.Drawing.Point.Empty, new System.Drawing.Size(width, height));
                }
                bitmap.Save(file, System.Drawing.Imaging.ImageFormat.Bmp);
            }
        }

        public static void EndRecordVideoPIE(float FPS, string CaptureStagingFolder)
        {
            CrossEngine.GetInstance()._CrossEngineInterface.SetFixedUpdateMode(false, 1.0f / FPS);

            int OutputFrameRate = (int)FPS;
            FFmpegWrapper.GeneratorVideoFromImages(CaptureStagingFolder, ".bmp", OutputFrameRate);
        }

        #endregion

        #region Other

        public void ClearAsset(bool resetEntity = true)
        {
            TimelineControl.StopPlay();
            _Previewing = null;
            if (_ComponentTrackHolder != null)
            {
                _ComponentTrackHolder.ClearTrack();
            }
            ClearSelect();
            ClearModified();
        }
        public void ForceSetHeadLocation(decimal Value)
        {
            SetHeadLocation(Value);
            bShouldUpdateValue = true;
        }

        public void ResetHead(bool status = false)
        {
            SetTimeProgress(PlayHead, (Decimal)0.0f);
            ControllableUnitSystemG.SetCurveControllerCursor(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, (float)PlayHead.Value);
            if (status)
            {
                SetEditStatus(EditStatus.EnterEdit);
                GetFrameUI().GetPanelStatusBar().SetBackgroundColor(Color.FromRGB(202, 81, 0));
            }
            InspectorUI.GetInstance().InspectObject();
        }

        private void DolocateEntity(Track track)
        {
            if (track is ComponentTrackHolder)
            {
                ComponentTrackHolder currentTrackHolder = (ComponentTrackHolder)track;
                PreviewEntity = currentTrackHolder.GetPreviewingEntity() != null ? currentTrackHolder.GetPreviewingEntity()
                    : ((ComponentTrackHolder)currentTrackHolder.GetParentTrack()).GetPreviewingEntity();
            }
            else if (track is ComponentTrack)
            {
                ComponentTrack componentTrack = (ComponentTrack)track;
                PreviewEntity = componentTrack.Previewing;
            }
            else
            {
                return;
            }

            if (PreviewEntity != null)
            {
                HierarchyUI.GetInstance().SelectEntity(PreviewEntity);
                _Instance.GetDockingCard().SetFocus();

                _PreviewingCamera = PreviewEntity.GetComponent(typeof(Camera)) as Camera;
            }

            if (EditorSceneUI.GetInstance().GetCameraView())
            {
                _PreviewingCamera = HierarchyUI.GetInstance().GetCurPiolotEntity().GetComponent(typeof(Camera)) as Camera;
            }
        }

        void SelectInRectangle(RectangleF Bound)
        {
            ClearSelect();
            foreach (Track Track in TrackTree.GetTracks())
            {
                List<KeyFrame> FrameList = Track.ContainKeyFrame(Bound);
                if (FrameList.Count != 0)
                {
                    SelectedKeyFrames.AddRange(FrameList);
                    SelectInRectangleTrack(Track);
                }
            }
        }

        public void LoadCinematicConfigFromFile()
        {
            string CinematicConfigPath = EditorUtilities.GetResourceDirectory() + "\\EngineResource\\Config\\CinematicConfig.json";
            string JsonStr = File.ReadAllText(CinematicConfigPath, System.Text.Encoding.ASCII);
            JObject Obj = (JObject)JsonConvert.DeserializeObject(JsonStr);

            var Node = Obj["CinematicConfig"];

            var ComponentNode = Node["ComponentPairs"];
            var ComponentNodeObj = JsonConvert.DeserializeObject<Dictionary<string, bool>>(ComponentNode.ToString());
            foreach (var value in ComponentNodeObj)
            {
                string a = value.ToString();
                ComponentNodes.Add(value.Key, value.Value);
            }
            var PropertyNode = Node["PropertyPairs"];
            var PropertyNodeObj = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, bool>>>(PropertyNode.ToString());

            foreach (var value in PropertyNodeObj)
            {
                string component = value.Key;
                Dictionary<string, bool> propertyNode = new Dictionary<string, bool>();
                foreach (var property in value.Value)
                {
                    propertyNode.Add(property.Key, property.Value);
                }
                PropertyNodes.Add(component, propertyNode);
            }
        }

        public ComponentTrackHolder QueryCurResourceMaterial(Material Material)
        {
            foreach (var Track in TrackTree.GetRootTracks())
            {
                foreach (var CmpTrack in Track.GetChildList())
                {
                    if (CmpTrack is ParticleMaterialTrack ParticleMaterialTrack)
                    {
                        ParticleMaterialTrack._Material.Refresh();
                        if (ParticleMaterialTrack._Material.Path == Material.Path)
                        {
                            return (ComponentTrackHolder)Track;
                        }
                    }
                    if (CmpTrack is MaterialTrack MaterialTrack)
                    {
                        MaterialTrack._Material.Refresh();
                        if (MaterialTrack._Material.Path + "_Instance" == Material.Path)
                        {
                            return (ComponentTrackHolder)Track;
                        }
                    }
                }
            }
            return null;
        }

        public string GetCurveCtrResPath(Entity Head)
        {
            CurveController Controller = GetCurveController(Head);
            return Controller.CurveCtrResPath;
        }

        public Entity QueryCurWorldEntity(CurveControllerData Data)
        {
            World World = EditorScene.GetInstance().GetWorld();
            if (World.Root != null)
            {
                Queue<Entity> Queue = new Queue<Entity>();
                Queue.Enqueue(World.Root);
                while (Queue.Count != 0)
                {
                    Entity Head = Queue.Dequeue();
                    foreach (Entity Child in Head.Children)
                    {
                        Queue.Enqueue(Child);
                    }
                    if (Data.mEntityID.Length == 0)
                    {
                        if (Head.HasComponent(typeof(Camera)))
                        {
                            Data.mEntityID = Head.EUID;
                        }
                    }

                    CrossUUID CrossUUID = new CrossUUID();
                    if (Data.mPrefabEuid != "")
                    {
                        CrossUUID.FromString(Data.mPrefabEuid);
                    }
                    if (Data.mEntityID == Head.EUID || (!CrossUUID.Invalid() && CrossUUID.Compare(Head.GetPrefabEuid()) == 0))
                    {
                        if (Data.mDataItems.Count != 0)
                        {
                            return Head;
                        }
                    }
                }
            }
            return new Entity(World);
        }

        public Entity JudgeIsExistScene(string Path)
        {
            World World = EditorScene.GetInstance().GetWorld();
            if (World.Root != null)
            {
                Queue<Entity> Queue = new Queue<Entity>();
                Queue.Enqueue(World.Root);
                while (Queue.Count != 0)
                {
                    Entity Head = Queue.Dequeue();
                    if (Head.HasComponent(typeof(ControllableUnitComponent)))
                    {
                        if (Head.GetComponent<ControllableUnitComponent>().ControllerType == ControllableUnitType.CurveController &&
                            GetCurveCtrResPath(Head) == ResourceManager.Instance().ConvertPathToGuid(Path))
                        {
                            return Head;
                        }
                    }
                    foreach (Entity Child in Head.Children)
                    {
                        Queue.Enqueue(Child);
                    }
                }
            }
            return null;
        }

        public void DeleteSelectedCineCamera(List<Entity> Entities)
        {
            if (Entities.Count != 0)
            {
                EditorSceneUI.GetInstance().OnButtonExitPilotClicked(null);
                foreach (var Entity in Entities)
                {
                    if (Entity.GetName().Contains("CineCamera"))
                    {
                        HierarchyUI.GetInstance().DoDelete();
                    }
                }
            }
        }

        #endregion

        #region  Get rid of ControllableUnitSystem
        public Entity GetLevelSequenceEntity()
        {
            return LevelSequenceEntity;
        }

        public void SetLevelSequenceEntity(Entity Entity)
        {
            LevelSequenceEntity = Entity;
        }
        #endregion

        #region LevelSeqRelate
        public bool LevelSeqContainCurve()
        {
            if (LevelSequenceEntity.World == null || LevelSequenceEntity == null)
                return false;
            IntPtr CurveDataPtr = ControllableUnitSystemG.GetCurveControllerData(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, LevelSequenceEntity.GetEntityIdStruct());
            var CurveControllerDataRef = new CurveControllerData(CurveDataPtr, false);
            if (CurveControllerDataRef.mDataItems.Capacity != 0)
            {
                return true;
            }
            return false;
        }
        #endregion

        #region MaterialRelate
        public bool QueryMaterialIsExist(string Path)
        {
            Entity Entity = null;
            World World = EditorSceneUI.GetInstance().GetScene().GetWorld();
            var Ptr = ControllableUnitSystemG.GetCurveControllerRes(World.GetNativePointer(), GetCurveCtrPath());
            CurveControllerRes curveControllerRes = new CurveControllerRes(Ptr, false);

            foreach (var data in curveControllerRes.mCurveData)
            {
                Entity = World.Root.SearchChildByEUID(data.mEntityID) != null ? World.Root.SearchChildByEUID(data.mEntityID) : World.Root.SearchChildByPrefabEuid(data.mEntityID);
                if (Entity != null && Entity.HasComponent(typeof(ModelComponent)))
                {
                    ModelComponent Model = Entity.GetComponent(typeof(ModelComponent)) as ModelComponent;
                    foreach (var M in Model.GetModels())
                    {
                        foreach (var Sub in M.LODProperties)
                        {
                            foreach (var model in Sub.SubModels)
                            {
                                if (ResourceManager.Instance().ConvertPathToGuid(Path).CompareTo(model.MaterialPath) == 0)
                                {
                                    return true;
                                }
                            }
                        }
                    }
                }
            }
            return false;
        }
        #endregion

        #region TopButtons
        private void SaveButtonClickedEvent(Button Sender)
        {
            DoSaveResourceOnly();
        }

        private void CameraButtonClickedEvent(Button Sender)
        {
            Entity Entity = HierarchyUI.GetInstance().CreateCamera("CineCamera", true);
            Transform Transform = Entity.GetTransformComponent();
            Transform CameraTransform = HierarchyUI.GetInstance().GetScene().GetCameraEntityTransform();

            Transform.SetWorldTranslation(CameraTransform.GetWorldTranslation());
            Transform.SetWorldRotation(CameraTransform.GetWorldRotation());
            AutoKeyTRS(Entity);
        }

        private void AutoButtonClickedEvent(Button Sender)
        {
            Sender.SetTagObject(!(bool)Sender.GetTagObject());
            if ((bool)Sender.GetTagObject())
            {
                Sender.SetNormalColor(Color.FromRGB(34, 111, 255));
                Sender.SetHoverColor(Color.FromRGB(34, 111, 255));
            }
            else
            {
                Sender.SetNormalColor(Color.FromRGB(36, 36, 36));
                Sender.SetHoverColor(Color.FromRGB(36, 36, 36));
            }
        }

        public bool IsEditAndAutoStatus()
        {
            return CinematicUI.GetInstance().GetEditStatus() == CinematicUI.EditStatus.EnterEdit && (bool)_AutoButton.GetTagObject();
        }

        public void AddPreviewingCamera(Entity Entity)
        {
            _PreviewingCamera = Entity.GetComponent(typeof(Camera)) as Camera;
        }

        #endregion
    }
}