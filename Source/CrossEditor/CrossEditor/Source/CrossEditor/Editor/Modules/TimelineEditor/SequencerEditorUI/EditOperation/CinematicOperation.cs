using System.Collections.Generic;

namespace CrossEditor
{
    public class CinematicOperation : EditOperation
    {
        protected List<ComponentTrack> _Tracks = null;

        public delegate void OnCinematicOperationUndo();
        protected OnCinematicOperationUndo onOperationUndo;

        public delegate void OnCinematicOperationRedo();
        protected OnCinematicOperationRedo onOperationRedo;

        public CinematicOperation(List<ComponentTrack> Tracks, OnCinematicOperationUndo undoFunc, OnCinematicOperationRedo redoFunc)
        {
            _Tracks = Tracks;

            onOperationUndo = undoFunc;
            onOperationRedo = redoFunc;
        }

        public override void Undo()
        {
            onOperationUndo();
            CinematicUI.GetInstance().SetModified();
        }

        public override void Redo()
        {
            onOperationRedo();
            CinematicUI.GetInstance().SetModified();
        }
    }
}
