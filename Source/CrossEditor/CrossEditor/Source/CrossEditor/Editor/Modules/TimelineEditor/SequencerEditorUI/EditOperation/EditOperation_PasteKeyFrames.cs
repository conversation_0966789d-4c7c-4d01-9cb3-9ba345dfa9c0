using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class EditOperation_PasteKeyFrames : EditOperation
    {
        protected List<KeyFrame> _KeyFrames = null;

        public EditOperation_PasteKeyFrames(List<KeyFrame> KeyFrames)
        {
            _KeyFrames = KeyFrames;
        }

        public override void Undo()
        {
            TimelineEditorUI.GetInstance().UndoPasteKeyFrames(_KeyFrames.Clone());
        }

        public override void Redo()
        {
            TimelineEditorUI.GetInstance().RedoPasteKeyFrames(_KeyFrames.Clone());
        }
    }
}
