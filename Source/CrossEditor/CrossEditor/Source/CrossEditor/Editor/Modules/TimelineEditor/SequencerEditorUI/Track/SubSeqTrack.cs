using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class SubSeqTrack : Track
    {
        public LevelSubSeqTrack SubSeqTrackRef { get; private set; } = null;
        public int SubSeqTrackIndex = 0;
        PathInputUIEx SubSeqPathInputUI;

        public void Initialize(ScaleUI ScaleUI, Track Parent, IntPtr SubSeqTrackPtr, object TagObject = null)
        {
            SubSeqTrackRef = new LevelSubSeqTrack(SubSeqTrackPtr, false);
            base.Initialize(ScaleUI, Parent, SubSeqTrackRef.TrackName, TagObject);

            _ButtonAddTrack.SetText("+ Sub Seq");
            ItemHeight = 40;
            SubSeqTrackIndex = (int)TagObject;

            UpdateKeyFrames();

            InitPathInputUI();
        }

        public override object GetValue(float Key)
        {
            return 1.0f;
        }

        void InitPathInputUI()
        {
            var PathInputUIFilterItem = new PathInputUIFilterItem()
            {
                Name = "Sequencer Files",
                Extensions = new List<string> { "nda" }
            };

            SubSeqPathInputUI = new PathInputUIEx();
            var ContentsDirectory = EditorUtilities.AddEditorDrives(SubSeqPathInputUI, true);
            SubSeqPathInputUI.Initialize(GetUIManager(), "Open Sequencer Asset", PathInputUIType.OpenFile, PathInputUIFilterItem, ContentsDirectory);
            SubSeqPathInputUI.InputedEvent += (PathInputUIEx Sender1, string PathInputed) =>
            {

                ClassIDType ObjectClassID = ResourceTypeCache.GetInstance().GetResourceType_Cache(PathInputed);

                var resource = Resource.Get(PathInputed, false);
                if (ObjectClassID == ClassIDType.CLASS_CurveControllerRes)
                {
                    decimal Key = _ScaleUI.GetCurrentUI().GetHeadLocation();
                    float StartTime = (float)Key;
                    float EndTime = StartTime;
                    SubSeqTrackRef.AddNewSubSeqSection(StartTime, PathInputed, 30, 30, 1.0f);
                    UpdateKeyFrames();
                    CinematicUI.GetInstance().SetPlayRate(1.0f);
                    CinematicUI.GetInstance().SetModified();
                }
                else
                {
                    CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), "Warning", "The selected file is NOT a AnimSequenceAsset");
                }
            };
        }

        public override void Draw(UIManager UIManager)
        {
            Color BackColor = GetIsSelected() ? SelectedColor : UnSelectedColor;

            RectangleF Bound = GetBound();
            int X = (int)Bound.X;
            int Y = (int)Bound.Y;
            int Width = (int)Bound.Width;
            int Height = (int)Bound.Height;

            if (Y == 0) return;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.DrawLineF(new Vector2f(X, Y), new Vector2f(X + Width, Y), 1f, ref Color.EDITOR_UI_GRAY_DRAW_COLOR);
            EditorUICanvas.DrawLineF(new Vector2f(X, Y + Height), new Vector2f(X + Width, Y + Height), 1f, ref Color.EDITOR_UI_GRAY_DRAW_COLOR);

            DrawKeyFrames();
        }

        public override void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            bVisible = IsVisible;
            int singleLayerHeight = ItemHeight;

            GetTrackItem().SetVisible(IsVisible);
            if (IsVisible)
            {
                GetTrackItem().SetPosition(0, Y, Width, singleLayerHeight);
            }

            base.UpdateLayout(IsVisible, Width, Indent, ref Y);
        }

        public override void Update()
        {
            base.Update();
        }

        void UpdateKeyFrames()
        {
            KeyFrames.Clear();
            foreach (var Section in SubSeqTrackRef.SubSeqSections)
            {
                AddKeyFrame(Section);
            }
        }

        void AddKeyFrame(LevelSubSeqSection section)
        {
            var NewKeyFrame = new SubSeqKeyFrame(section, this, (decimal)section.SectionStart);
            //NewKeyFrame.MoveEvent += (Sender, Args) => { Curve.Points.Sort(); };
            KeyFrames.Add(NewKeyFrame);
        }

        protected override void OnButtonAddTrackClicked(Button Sender)
        {
            DialogUIManager.GetInstance().ShowDialogUI(SubSeqPathInputUI);
        }

        public void ModifyValue(SubSeqKeyFrame Key, LevelSubSeqSection Value)
        {
            SubSeqTrackRef.UpdateSubSeqSection(Value, Key.Section.RowIndex);
            Key.Section = Value;
        }

        public override KeyFrame RemoveKeyFrame(KeyFrame KeyFrame)
        {
            var index = KeyFrames.IndexOf(KeyFrame);
            if (index >= 0)
            {
                SubSeqTrackRef.SubSeqSections.RemoveAt(index);
                UpdateKeyFrames();
            }
            return null;
        }
    }
}
