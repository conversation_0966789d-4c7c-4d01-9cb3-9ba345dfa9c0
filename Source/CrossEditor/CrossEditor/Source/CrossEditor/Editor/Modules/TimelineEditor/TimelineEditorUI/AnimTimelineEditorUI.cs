using CEngine;
using EditorUI;
using System.Collections.Generic;
using System.IO;

namespace CrossEditor
{
    public class AnimTimelineEditorUI : TimelineEditorUI
    {
        public static new readonly string DockingCardName = "Animation";

        static readonly AnimTimelineEditorUI _Instance = new AnimTimelineEditorUI();
        public static new AnimTimelineEditorUI GetInstance() => _Instance;

        public SyncMarkerTrackHolder SyncMarkerTrackHolder;
        public NotifyTrackHolder NotifyTrackHolder;
        public CompositeTrackHolder CompositeTrackHolder;
        public CurveTrackHolder CurveTrackHolder;

        public AnimTimelineEditorUI() : base()
        {
            Name = DockingCardName;
        }

        public override bool Initialize()
        {
            base.Initialize();
            var DockingCard = GetDockingCard();
            DockingCard.SetText(DockingCardName);
            DockingCard.SetEnableDragDrop(true);

            DragDropManager.GetInstance().DragEndEvent += OnDragEnd;

            ButtonAddTrack.SetVisible(false);

            HoldingObjectMoveEvent += (Sender, HoldingObject) =>
            {
                if (HoldingObject is MoveHead)
                {
                    var Head = HoldingObject as MoveHead;
                    if (Head.Value < StartHead.Value) Head.Value = StartHead.Value;
                    else if (Head.Value > EndHead.Value) Head.Value = EndHead.Value;
                }
            };

            TimelineControl.PlayStateChangeEvent += (Sender, NewState) =>
            {
                if (Resource != null)
                    MSAPreviewScene.GetInstance().GetPreviewEntity().GetComponent(typeof(Animator)).Enable = NewState;
            };

            MSAPreviewContext.GetInstance().MsaAssetChangeCallBack += (Asset, Operation, Args) =>
            {
                switch (Operation)
                {
                    case MSAPreviewContext.AssetOperation.EditAnimationAssetInTimeline:
                        if (Asset == Resource)
                        {
                            foreach (var Track in TrackTree.GetRootTracks())
                            {
                                Track.Refresh();
                            }

                            if (Asset.GetDirty())
                                SetModified();
                        }
                        break;
                    case MSAPreviewContext.AssetOperation.EditAnimationAsset:
                        if (Asset == Resource && Asset.GetDirty())
                            SetModified();
                        break;
                    case MSAPreviewContext.AssetOperation.SelectAnimationAsset:
                        if (Asset != Resource || Asset is MotionMatchAsset)
                            AcivateAnimMode(Asset as PreviewAnimAsset);
                        break;
                    case MSAPreviewContext.AssetOperation.RemoveAnimationAsset:
                        if (Asset == Resource)
                            ClearAsset();
                        break;
                    case MSAPreviewContext.AssetOperation.SaveAnimationAsset:
                        if (Asset == Resource)
                            ClearModified();
                        break;
                }
            };

            return true;
        }

        public void ClearAsset()
        {
            TimelineControl.StopPlay();

            ClearSelect();
            Resource = null;
            TrackTree.ClearTrack();

            ClearModified();
        }

        public override void DoSave()
        {
            MSAPreviewContext.GetInstance().AttemptSaveAsset(Resource as MsaPreviewSingleAsset);
        }

        protected override void OnEditorGlobalUpdate(Device Sender, long TimeElapsed)
        {
            if (TimelineControl.GetIsPlaying())
            {
                decimal NewValue = GetHeadLocation() + TimeElapsed * ScaleUI.GetFps() / 1000m;
                if (NewValue >= GetEndLocation())
                {
                    if (TimelineControl.GetIsLoop())
                    {
                        if (GetLength() != 0m)
                            NewValue = GetStartLocation() + (NewValue - GetStartLocation()) % GetLength();
                        else
                            NewValue = GetStartLocation();
                    }
                    else
                    {
                        NewValue = GetEndLocation();
                        TimelineControl.StopPlay();
                    }
                }
                SetHeadLocation(NewValue);
                bShouldUpdateValue = true;
            }

            if (bShouldUpdateValue)
            {
                var Asset = MSAPreviewContext.GetInstance().ActivatedAnimAsset;

                if (Asset != null)
                {
                    if (Asset.IsLoaded())
                    {
                        Asset.MoveCursor((float)ScaleUI.FrameToSecond(GetHeadLocation()));
                    }
                }

                bShouldUpdateValue = false;
            }
        }

        void OnDragEnd(DragDropManager Sender, UIManager UIManager, int MouseX, int MouseY, ref bool bContinue)
        {
            if (UIManager != GetUIManager())
            {
                return;
            }
            var DockingCard = GetDockingCard();
            if (Resource is PreviewAnimCmpAsset && IsDockingCardActive() && DockingCard.GetVisible_Recursively() && DockingCard.IsPointIn_Recursively(MouseX, MouseY))
            {
                ProjectUI ProjectUI = ProjectUI.GetInstance();
                if (ProjectUI.IsPathesDragging())
                {
                    List<string> PathesDragged = ProjectUI.GetPathesDragged();
                    foreach (var SinglePath in PathesDragged)
                    {
                        AssetType AssetType = AssetImporterManager.Instance().GetAssetType(SinglePath);
                        if (AssetType != AssetType.Default)
                        {
                            string Title = "Information:";
                            string Content = string.Format("\"{0}\" File Can't Be Added into Composite", Path.GetExtension(SinglePath));
                            CommonDialogUI.ShowSimpleOKDialog(GetUIManager(), Title, Content);
                            return;
                        }

                        string NDAPath_Relative = EditorUtilities.EditorFilenameToStandardFilename(SinglePath);
                        ClassIDType ObjectClassID = Resource.GetResourceTypeStatic(NDAPath_Relative);

                        if (ObjectClassID == ClassIDType.CLASS_AnimCompositeRes ||
                            ObjectClassID == ClassIDType.CLASS_AnimSequenceRes)
                        {

                            PreviewAnimAsset AnimAsset = (PreviewAnimAsset)Resource.Create(ObjectClassID);

                            // load anim asset failed
                            if (!AnimAsset.Load(SinglePath))
                                return;

                            // check compatible between animation and cur skeleton
                            if (MSAPreviewContext.GetInstance().SkeletonAdded())
                            {
                                CompatibleFailInfo CompatibleFailedInfo = new CompatibleFailInfo();
                                if (MSAPreviewContext.GetInstance().RunSkelt.IsCompatible(AnimAsset, ref CompatibleFailedInfo) != MsaCompatibleType.Success)
                                    return;
                            }

                            var Segment = new SlotTrackResSegment
                            {
                                ReltvPath = SinglePath,
                                StartPos = 0f,
                                EndPos = 0f,
                                LoopingCount = 1,
                                PlayRate = 1f,
                                Name = "NoName"
                            };

                            if (AnimAsset is PreviewAnimSeqAsset)
                                Segment.EndPos = (AnimAsset as PreviewAnimSeqAsset).Duration;
                            else if (AnimAsset is PreviewAnimCmpAsset)
                            {
                                float EndPos = 0f;
                                foreach (var Seg in (AnimAsset as PreviewAnimCmpAsset).GetTrack().Segments)
                                    EndPos += Seg.EndPos;
                            }
                            else
                                DebugHelper.Assert(false);

                            var CompositeTrack = CompositeTrackHolder.GetChildList()[0] as CompositeTrack;
                            CompositeTrack.AddNewKey(ScaleUI.ScreenXToValue(MouseX), Segment);
                        }
                    }
                }
            }
        }

        protected override IMovable TryHit(int MouseX, int MouseY)
        {
            if (PlayHead.HitTest(MouseX, MouseY))
            {
                return PlayHead;
            }
            else if (ScaleUI.HitTest(MouseX, MouseY))
            {
                PlayHead.MoveTo(MouseX, MouseY, null);

                if (PlayHead.Value > EndHead.Value) PlayHead.Value = EndHead.Value;
                else if (PlayHead.Value < StartHead.Value) PlayHead.Value = StartHead.Value;

                TimelineControl.StopPlay();
                bShouldUpdateValue = true;

                return null;
            }
            else
            {
                TrySelect(MouseX, MouseY, out var KeyFrame, out var _);
                if (KeyFrame != null)
                {
                    InspectorUI.GetInstance().SetObjectInspected(KeyFrame);
                    InspectorUI.GetInstance().InspectObject();
                }
                return HoldingObject;
            }
        }

        public void AcivateAnimMode(PreviewAnimAsset Asset)
        {
            switch (Asset.ClassID)
            {
                case ClassIDType.CLASS_AnimSequenceRes:
                    ActivateAnimSeq(Asset as PreviewAnimSeqAsset);
                    break;
                case ClassIDType.CLASS_AnimCompositeRes:
                    ActivateAnimCmp(Asset as PreviewAnimCmpAsset);
                    break;
                case ClassIDType.CLASS_AnimatrixRes:
                    ActivateAnimatrix(Asset as PreviewAnimatrixAsset);
                    break;
                case ClassIDType.CLASS_MotionDataAsset:
                    var mmAsset = Asset as MotionMatchAsset;
                    if (mmAsset.GetAnimSeqSelected() != null)
                        ActivateAnimSeq(mmAsset.GetAnimSeqSelected());
                    break;
                default:
                    return;
            }

            MainUI.GetInstance().ActivateDockingCard_AnimTimelineEditor();

            Resource = Asset;
            ResetTimelineControl();
            SetIsFrameLockOn(false);
            if (Asset.GetDirty())
                SetModified();
        }

        public void ActivateAnimSeq(PreviewAnimSeqAsset SeqAsset)
        {
            CompositeTrackHolder = null;
            TrackTree.ClearTrack();

            SyncMarkerTrackHolder = new SyncMarkerTrackHolder();
            SyncMarkerTrackHolder.Initialize(ScaleUI, null, "Sync Markers", SeqAsset);

            NotifyTrackHolder = new NotifyTrackHolder();
            NotifyTrackHolder.Initialize(ScaleUI, null, "Notifies", SeqAsset);

            CurveTrackHolder = new CurveTrackHolder();
            CurveTrackHolder.Initialize(ScaleUI, null, "Curves", SeqAsset);

            TrackTree.AddTrack(SyncMarkerTrackHolder);
            TrackTree.AddTrack(NotifyTrackHolder);
            TrackTree.AddTrack(CurveTrackHolder);

            ScaleUI.SetFps((decimal)(SeqAsset.FrameCount / SeqAsset.Duration));

            SyncMarkerTrackHolder.Refresh();
            NotifyTrackHolder.Refresh();
            CurveTrackHolder.Refresh();

            SetStartLocation(0m);
            SetEndLocation(SeqAsset.FrameCount);
            FitOnRange();
        }

        public void ActivateAnimCmp(PreviewAnimCmpAsset CmpAsset)
        {
            SyncMarkerTrackHolder = null;
            TrackTree.ClearTrack();

            // Composite Track at first
            CompositeTrackHolder = new CompositeTrackHolder();
            CompositeTrackHolder.Initialize(ScaleUI, null, "Composite", CmpAsset);

            NotifyTrackHolder = new NotifyTrackHolder();
            NotifyTrackHolder.Initialize(ScaleUI, null, "Notifies", CmpAsset);

            CurveTrackHolder = new CurveTrackHolder();
            CurveTrackHolder.Initialize(ScaleUI, null, "Curves", CmpAsset);

            TrackTree.AddTrack(CompositeTrackHolder);
            TrackTree.AddTrack(NotifyTrackHolder);
            TrackTree.AddTrack(CurveTrackHolder);

            var Composite = CompositeTrackHolder.CreateNewTrack(CmpAsset.GetName());
            CompositeTrackHolder.AddTrack(Composite);

            ScaleUI.SetFps(30);

            NotifyTrackHolder.Refresh();
            CompositeTrackHolder.Refresh();
            CurveTrackHolder.Refresh();

            SetStartLocation(0m);
            FitOnRange();
        }

        public void ActivateAnimatrix(PreviewAnimatrixAsset AnimatrixAsset)
        {
            SyncMarkerTrackHolder = null;
            TrackTree.ClearTrack();

            CompositeTrackHolder = new CompositeTrackHolder();
            CompositeTrackHolder.Initialize(ScaleUI, null, "Composite", AnimatrixAsset);

            NotifyTrackHolder = new NotifyTrackHolder();
            NotifyTrackHolder.Initialize(ScaleUI, null, "Notifies", AnimatrixAsset);

            CurveTrackHolder = new CurveTrackHolder();
            CurveTrackHolder.Initialize(ScaleUI, null, "Curves", AnimatrixAsset);

            TrackTree.AddTrack(CompositeTrackHolder);
            TrackTree.AddTrack(NotifyTrackHolder);
            TrackTree.AddTrack(CurveTrackHolder);

            ScaleUI.SetFps(30);

            NotifyTrackHolder.Refresh();
            CompositeTrackHolder.Refresh();
            CurveTrackHolder.Refresh();

            SetStartLocation(0m);
            //SetEndLocation(End);
            FitOnRange();
        }
    }
}
