using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class KeyFrameMover : IMovable
    {
        public ScaleUI _ScaleUI;
        public List<KeyFrame> KeyFrames;
        public bool bFrameLockOn;

        protected List<decimal> BeginXs = new List<decimal>();
        protected float CumulatedX;
        public EditOperation LastOperation;

        public event MoveHandler MoveEvent;

        public KeyFrameMover(ScaleUI ScaleUI, List<KeyFrame> KeyFrames, bool bFrameLockOn = true)
        {
            _ScaleUI = ScaleUI;
            this.KeyFrames = KeyFrames;
            this.bFrameLockOn = bFrameLockOn;
        }

        #region IMovable Interface Implement

        public virtual void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper CurveGraphicsHelper)
        {
            CumulatedX += (float)DeltaX;

            if (BeginXs.Count != KeyFrames.Count)
            {
                BeginXs.Clear();
                foreach (var Frame in KeyFrames)
                {
                    BeginXs.Add(Frame.GetKeyValue());
                }
            }

            for (int i = 0; i < KeyFrames.Count; ++i)
            {
                decimal BeginLoc = _ScaleUI.ValueToScreenX(BeginXs[i]);
                BeginLoc += (decimal)CumulatedX;
                BeginLoc = _ScaleUI.ScreenXToValue(BeginLoc);

                decimal NearestValue;
                if (KeyFrames[i] is AnimSegmentKeyFrame)
                {
                    (KeyFrames[i] as AnimSegmentKeyFrame).SetDispValueX(BeginLoc);
                    KeyFrames[i].OnMove(new MoveEvnetArgs());
                    continue;
                }
                else if (KeyFrames[i] is SectionHeadKeyFrame)
                {
                    KeyFrames[i].Move(DeltaX, DeltaY, CurveGraphicsHelper);
                    continue;
                }
                else
                {
                    if (bFrameLockOn)
                        NearestValue = _ScaleUI.GetNearestValue(BeginLoc);
                    else
                        NearestValue = BeginLoc;
                }
                KeyFrames[i].SetKeyValue(NearestValue);
                KeyFrames[i].OnMove(new MoveEvnetArgs());
            }

            MoveEvent?.Invoke(this, new MoveEvnetArgs());
        }

        public virtual void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper CurveGraphicsHelper) { }

        public virtual void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            CumulatedX = 0f;
            BeginXs.Clear();
            foreach (var KeyFrame in KeyFrames)
            {
                BeginXs.Add(KeyFrame.GetKeyValue());
            }

            foreach (var KeyFrame in KeyFrames) KeyFrame.MoveBegin(StartPoint, CurveGraphicsHelper);

            LastOperation = null;
        }

        public virtual void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper CurveGraphicsHelper)
        {
            foreach (var KeyFrame in KeyFrames)
                KeyFrame.MoveEnd(EndPoint, CurveGraphicsHelper);
        }

        #endregion IMovable Interface Implement
    }

    public abstract class KeyFrame : IMovable, IComparable<KeyFrame>
    {
        public static bool operator <(KeyFrame A, KeyFrame B)
        {
            return A.KeyValue < B.KeyValue;
        }

        public static bool operator >(KeyFrame A, KeyFrame B)
        {
            return A.KeyValue > B.KeyValue;
        }

        public object BindObject;
        public Track OwnerTrack;
        protected decimal KeyValue;

        public float Size;
        public int FontSize;

        public bool bEnable;
        public bool bSelected;
        public static Color UnSelectedColor = Color.EDITOR_UI_HILIGHT_COLOR_GREEN;
        public static Color SelectedColor = Color.EDITOR_UI_HILIGHT_COLOR_BLUE;

        protected ScaleUI _ScaleUI;

        public event MoveHandler MoveEvent;
        public event MoveHandler MoveBeginEvent;
        public event MoveHandler MoveEndEvent;

        protected decimal BeginX;
        protected float CumulatedX;

        public KeyFrame(object BindObject, Track Owner, decimal KeyValue)
        {
            bEnable = true;
            bSelected = false;

            this.BindObject = BindObject;
            this.OwnerTrack = Owner;
            _ScaleUI = Owner.GetScaleUI();
            this.KeyValue = KeyValue;

            Size = 2f;
            FontSize = 16;
        }

        public UIManager GetUIManager()
        {
            return CinematicUI.GetInstance().GetUIManager();
        }

        public Device GetDevice()
        {
            return GetUIManager().GetDevice();
        }

        public virtual decimal GetBeginX() { return BeginX; }
        public virtual RectangleF GetBound() { return new RectangleF(); }
        public virtual decimal GetKeyValue() { return KeyValue; }
        public virtual void SetKeyValue(decimal NewKeyValue) { KeyValue = NewKeyValue; }
        public virtual void Draw() { if (!bEnable) return; }

        public virtual bool HitTest(int MouseX, int MouseY) { return bEnable && GetBound().Contains(MouseX, MouseY); }
        public virtual List<MenuItem> GetMenuItems() { return new List<MenuItem>(); }
        public virtual void ShowMenu(int MouseX, int MouseY)
        {
            Menu Menu = new Menu(GetUIManager());
            Menu.Initialize();

            var MenuItems = GetMenuItems();
            TimelineEditorCommon.AddMenuItemsSeperately(Menu, MenuItems);

            OwnerTrack.GetMenuItems();

            if (Menu.GetMenuItemCount() > 0)
                GetUIManager().GetContextMenu().ShowMenu(Menu, MouseX, MouseY);
        }
        public virtual void Move(decimal DeltaX, decimal DeltaY, CurveGraphicsHelper GraphicsHelper) { }
        public virtual void MoveBegin(Vector2m StartPoint, CurveGraphicsHelper GraphicsHelper)
        {
            MoveBeginEvent?.Invoke(this, new MoveEvnetArgs());
        }
        public virtual void MoveEnd(Vector2m EndPoint, CurveGraphicsHelper GraphicsHelper)
        {
            MoveEndEvent?.Invoke(this, new MoveEvnetArgs());
        }
        public virtual void MoveTo(decimal DispX, decimal DispY, CurveGraphicsHelper GraphicsHelper) { }

        public virtual void OnMove(MoveEvnetArgs Args)
        {
            MoveEvent?.Invoke(this, Args);
        }

        public virtual int CompareTo(KeyFrame other)
        {
            return GetKeyValue().CompareTo(other.GetKeyValue());
        }

        public virtual void Refresh() { }
    }
}
