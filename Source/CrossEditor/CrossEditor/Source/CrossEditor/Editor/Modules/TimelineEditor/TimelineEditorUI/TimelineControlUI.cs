using EditorUI;

namespace CrossEditor
{
    public delegate void PlayStateChangeEventHandler(TimelineControlUI Sender, bool NewState);
    public delegate void ControlEditButtonClickHandler(TimelineControlUI Sender, int index);

    public class TimelineControlUI
    {
        public static readonly int PAINT_HEIGHT = 30;
        public static readonly int BUTTON_SIZE = 20;

        public static readonly string EnterEdit = "Enter Editing";
        public static readonly string ExitEdit = "Exit Editing";
        public static readonly string RestoreScene = "Restore Scene";


        Panel _Panel;
        OperationBarUI _OperationBarUI;
        Check _CheckPlay;
        Check _CheckLoop;
        Button _EditButton;
        Button _RestoreButton;

        StartHead StartHead;
        EndHead EndHead;
        MoveHead MoveHead;

        public event PlayStateChangeEventHandler PlayStateChangeEvent;
        public event ControlEditButtonClickHandler EditStateChangeEvent;

        public void Initialize(StartHead StartHead, EndHead EndHead, MoveHead MoveHead)
        {
            this.StartHead = StartHead;
            this.EndHead = EndHead;
            this.MoveHead = MoveHead;

            _Panel = new Panel();
            _Panel.Initialize();
            _Panel.SetHeight(PAINT_HEIGHT);
            _Panel.PositionChangedEvent += OnPanelPositionChanged;

            _OperationBarUI = new OperationBarUI();
            _OperationBarUI.Initialize();

            _CheckPlay = new Check();
            _CheckPlay.Initialize();
            _CheckPlay.SetImageChecked(UIManager.LoadUIImage("Editor/Icons/Profile/StopProfiling.png"));
            _CheckPlay.SetImageUnchecked(UIManager.LoadUIImage("Editor/Icons/Profile/StartProfiling.png"));
            _CheckPlay.SetAutoCheck(true);
            _CheckPlay.SetChecked(false);
            _CheckPlay.SetSize(BUTTON_SIZE, BUTTON_SIZE);
            _CheckPlay.ClickedEvent += OnCheckPlayClicked;
            _OperationBarUI.AddLeft(_CheckPlay);

            _CheckLoop = new Check();
            _CheckLoop.Initialize();
            _CheckLoop.SetImageChecked(UIManager.LoadUIImage("Editor/Icons/Edit/RotateMode.png"));
            _CheckLoop.SetImageUnchecked(UIManager.LoadUIImage("Editor/Icons/View/NavigateForward.png"));
            _CheckLoop.SetAutoCheck(true);
            _CheckLoop.SetChecked(false);
            _CheckLoop.SetSize(BUTTON_SIZE, BUTTON_SIZE);
            _OperationBarUI.AddLeft(_CheckLoop);

            _RestoreButton = new Button();
            _RestoreButton.Initialize();
            _RestoreButton.SetText(RestoreScene);
            _RestoreButton.SetFontSize(12);
            _RestoreButton.SetEnable(true);
            _RestoreButton.SetSize(150, 20);
            _RestoreButton.ClickedEvent += OnRestoreButtonClicked;
            _OperationBarUI.AddRight(_RestoreButton);

            _EditButton = new Button();
            _EditButton.Initialize();
            _EditButton.SetText(EnterEdit);
            _EditButton.SetFontSize(12);
            _EditButton.SetEnable(true);
            _EditButton.SetSize(150, 20);
            _EditButton.ClickedEvent += OnEditButtonClicked;
            _OperationBarUI.AddRight(_EditButton);
        }



        public bool GetIsLoop() { return _CheckLoop.GetChecked(); }
        public void SetIsLoop(bool bLoop) { _CheckLoop.SetChecked(bLoop); }

        public Panel GetPanel() { return _OperationBarUI.GetPanelBar(); }

        public bool GetIsPlaying() { return _CheckPlay.GetChecked(); }

        public void SetIsPlaying(bool bPlaying)
        {
            if (bPlaying != _CheckPlay.GetChecked())
            {
                _CheckPlay.SetChecked(bPlaying);
                OnPlayStateChange(bPlaying);
            }
        }

        public void SwitchPlay() { SetIsPlaying(!GetIsPlaying()); OnCheckPlayClicked(_CheckPlay); }
        public void StopPlay() { SetIsPlaying(false); }

        public void SetEditButtonText(string Value)
        {
            _EditButton.SetText(Value);
        }

        void OnPanelPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            _CheckPlay.MakeCenter();
        }

        void OnCheckPlayClicked(Check Sender)
        {
            if (GetIsPlaying() && MoveHead.Value == EndHead.Value)
            {
                MoveHead.Value = StartHead.Value;
            }
            OnPlayStateChange(GetIsPlaying());
        }

        private void OnRestoreButtonClicked(Button Sender)
        {
            CinematicUI.GetInstance().RestoreSceneValue(true);
        }

        void OnEditButtonClicked(Button Sender)
        {
            int index = 0;
            if (_EditButton.GetText() == ExitEdit)
            {
                index = 2;
                _EditButton.SetText(EnterEdit);
            }
            else
            {
                index = 1;
                _EditButton.SetText(ExitEdit);
            }
            EditStateChangeEvent?.Invoke(this, index);
        }

        public void OnPlayStateChange(bool NewState)
        {
            PlayStateChangeEvent?.Invoke(this, NewState);
        }
    }
}
