using EditorUI;
using System.Collections.Generic;

namespace CrossEditor
{
    public class SyncMarkerTrackHolder : HolderTrack<SyncMarkerTrack>
    {
        public override bool RemoveChildTrack(Track Track)
        {
            if (base.RemoveChildTrack(Track))
            {
                foreach (var KeyFrame in Track.GetKeyFrames())
                    MSAPreviewContext.GetInstance().RemoveSubData(Track.GetTagObject() as PreviewAnimSeqAsset,
                        MSAPreviewContext.AnimSubDataType.SyncMarker, KeyFrame.BindObject as AnimSyncMarker);

                return true;
            }

            return false;
        }

        public override void Refresh()
        {
            if (!(TagObject is PreviewAnimSeqAsset))
                return;

            var SyncMarkerDataList = MSAPreviewContext.GetInstance().GetSubDataList(TagObject as PreviewAnimSeqAsset,
                MSAPreviewContext.AnimSubDataType.SyncMarker);

            bool bHasChild = Children.Count > 0;
            bool bHasData = SyncMarkerDataList.Count > 0;

            if (!bHasData)
            {
                foreach (var Child in Children)
                {
                    Child.GetKeyFrames().Clear();
                }
                return;
            }

            if (bHasChild)
            {
                var Comparer = Comparer<object>.Create((a, b) => (a as AnimSyncMarker).Name.CompareTo((b as AnimSyncMarker).Name));
                SyncMarkerDataList.Sort(Comparer);
                var TmpChildren = Children.Clone();
                foreach (var Child in TmpChildren)
                {
                    var TmpKeyFrames = Child.GetKeyFrames().Clone();
                    foreach (var KeyFrame in TmpKeyFrames)
                    {
                        var SyncMarkerKey = KeyFrame as SyncMarkerKeyFrame;
                        var KeyData = SyncMarkerKey.BindObject as AnimSyncMarker;
                        int Index = SyncMarkerDataList.BinarySearch(KeyData, Comparer);
                        if (Index < 0)
                        {
                            Child.GetKeyFrames().Remove(KeyFrame);
                        }
                        else
                        {
                            SyncMarkerKey.BindObject = SyncMarkerDataList[Index];
                            SyncMarkerDataList.RemoveAt(Index);
                        }
                    }
                }

                var SyncMarkerTrack = TmpChildren[0];
                foreach (AnimSyncMarker Data in SyncMarkerDataList)
                {
                    KeyFrame NewKeyFrame = new SyncMarkerKeyFrame(Data, SyncMarkerTrack, _ScaleUI.SecondToFrame(Data.TriggerTimePos));
                    SyncMarkerTrack.AddKey(NewKeyFrame);
                }
                SyncMarkerTrack.Refresh();
            }
            else
            {
                var SyncMarkerTrack = CreateNewTrack() as SyncMarkerTrack;
                AddTrack(SyncMarkerTrack);
                foreach (AnimSyncMarker SyncMarker in SyncMarkerDataList)
                {
                    KeyFrame NewKeyFrame = new SyncMarkerKeyFrame(SyncMarker, SyncMarkerTrack, _ScaleUI.SecondToFrame(SyncMarker.TriggerTimePos));
                    SyncMarkerTrack.AddKey(NewKeyFrame);
                }
            }
        }
    }
}
