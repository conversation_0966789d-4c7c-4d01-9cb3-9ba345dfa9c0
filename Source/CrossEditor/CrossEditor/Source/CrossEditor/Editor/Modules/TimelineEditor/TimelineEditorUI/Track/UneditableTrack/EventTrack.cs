using CEngine;
using EditorUI;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CrossEditor
{
    public class EventTrack : FinalTrack
    {
        protected EventCurveContext _Context = new EventCurveContext();
        protected Entity Previewing = null;

        public EventTrack() : base()
        {
            LevelSequenceEntity = CinematicUI.GetInstance().GetLevelSequenceEntity();
        }

        public override void Initialize(ScaleUI ScaleUI, Track Parent, string Name, object TagObject = null)
        {
            base.Initialize(ScaleUI, Parent, Name, TagObject);

            if (TagObject is Entity)
                Previewing = TagObject as Entity;

            _Context.EventTrackName = new UniqueString(Previewing.GetName());

            _LabelName = new Label();
            _LabelName.Initialize();
            _LabelName.SetText(Name);
            _LabelName.SetFontSize(FontSize);
            _ButtonBar.AddChild(_LabelName);

            GetTrackItem().AddChild(_ButtonAddKeyFrame);

            SelectedColor.A = 0.7f;
            UnSelectedColor.A = 0.7f;

            Curve.PostAddPointEvent += (Sender, NewPoint) =>
            {
                AddKey(NewPoint);
            };

            Curve.PostModifiedCurveEvent += () =>
             {
                 bool ret = ControllableUnitSystemG.SetCurveControllerTrack(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, _Context,
                     Curve.RuntimeCurve.Name, Curve.RuntimeCurve, Previewing.GetEntityIdStruct());
                 if (_ScaleUI.GetCurrentUI() is CinematicUI && ret)
                 {
                     CinematicUI.GetInstance().SetModified();
                 }
             };

            Curve.PostRemovePointEvent += (Sender, Point) =>
            {
                RemoveKeyFrame(Point);
            };
        }

        public virtual void AddKey(Point Pt)
        {
            KeyFrame NewKeyFrame = new PointKeyFrame(Pt, this);
            NewKeyFrame.MoveEvent += (Sender, Args) => { Curve.Points.Sort(); };
            KeyFrames.Add(NewKeyFrame);
            KeyFrames.Sort();

            PostKeyFrameAdd(NewKeyFrame);
        }

        public virtual KeyFrame RemoveKeyFrame(Point Pt)
        {
            KeyFrame MatchedKeyFrame = KeyFrames.Find(KeyFrame => KeyFrame.BindObject == Pt);
            return KeyFrames.Remove(MatchedKeyFrame) ? MatchedKeyFrame : null;
        }

        public override void UpdateLayout(bool IsVisible, int Width, int Indent, ref int Y)
        {
            bVisible = IsVisible;
            int singleLayerHeight = ItemHeight;

            GetTrackItem().SetVisible(IsVisible);
            if (IsVisible)
            {
                GetTrackItem().SetPosition(0, Y, Width, singleLayerHeight);
            }

            _EditWithProgress.SetVisible(false);
            _ButtonAddKeyFrame.SetVisible(true);
            base.UpdateLayout(IsVisible, Width, Indent, ref Y);
        }

        public override void Draw(UIManager UIManager)
        {
            Color BackColor = GetIsSelected() ? SelectedColor : UnSelectedColor;

            RectangleF Bound = GetBound();
            int X = (int)Bound.X;
            int Y = (int)Bound.Y;
            int Width = (int)Bound.Width;
            int Height = (int)Bound.Height;

            if (Y == 0) return;

            EditorUICanvas EditorUICanvas = (EditorUICanvas)UIManager.GetEditorUICanvas();
            EditorUICanvas.FillRectangle(X, Y, Width, Height, ref BackColor);

            int DrawSpan = 2;
            int DrawX = _Panel.GetScreenX();
            int DrawY = GetTrackItem().GetScreenY() + DrawSpan;
            int DrawWidth = _ScaleUI.GetWidth();
            int DrawHeight = ItemHeight - DrawSpan * 2;
            Curve.Draw(UIManager, _ScaleUI.GetStart(), _ScaleUI.GetEnd(), DrawX, DrawY, DrawWidth, DrawHeight, DrawSpan);

            DrawKeyFrames();
        }

        public override void Update()
        {
            base.Update();
        }

        public override object GetValue(float Key)
        {
            return 1.0f;
        }

        public void ExtractComponentValueToSelected(decimal axisPos)
        {
            object propertyValue = 1;
            //var selectedKeys = GetSelectedKeys();
            AddNewKey(axisPos, 1);
        }

        public override void AddNewKey(decimal Key, object Value)
        {
            float extracted = 1;
            if (Curve.CheckIsValidX((float)Key))
            {
                SmoothPoint NewPoint = (SmoothPoint)Curve.AddNewPoint((float)Key, extracted, 4f, PointType.Smooth);
                if (Value.GetType() == typeof(List<string>))
                {
                    NewPoint.EventString = ProcessEventString(Value);
                }
                Curve.PostModifiedCurve();
                //AddTrackData();
            }
        }

        public void ExtractComponentValueToEdit(decimal axisPos)
        {

        }

        public void AddTrackData(int Index)
        {
            _Context.EventTrackName = new UniqueString(Previewing.GetName());
            _Context.TrackIndex = Index;

            UniqueString CurveType = new UniqueString("Float1");
            ControllableUnitSystemG.CreateCurveController(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, _Context, CurveType, Previewing.GetEntityIdStruct());

            FloatCurveListInfo info = new FloatCurveListInfo();

            info.Items.Add(new FloatCurveListInfoItem());
            info.Items[0].CurveName = new UniqueString("Event");

            ControllableUnitSystemG.SetCurveControllerInfo(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, _Context, info, Previewing.GetEntityIdStruct());
        }

        public List<EventTrack> RefreshTrackList(int TrackIndex)
        {
            ClearTrack();
            List<EventTrack> tracks = new List<EventTrack>();

            _Context.EventTrackName = new UniqueString(Previewing.GetName());
            _Context.TrackIndex = TrackIndex;

            FloatCurveList list = new FloatCurveList();
            ControllableUnitSystemG.GetCurveControllerList(LevelSequenceEntity.World.GetNativePointer(), LevelSequenceEntity.EntityID, _Context, list, Previewing.GetEntityIdStruct());

            foreach (var item in list.mTracks)
            {
                var track = new EventTrack();
                track.Initialize(_ScaleUI, this, "Event", Previewing);
                track.GetCurve().RuntimeCurve = item;

                tracks.Add(track);
            }
            return tracks;
        }

        public void PropertyAddKeyFrame(decimal Key, object CompressedValue)
        {
            KeyFrame KeyFrame = KeyFrames.Find(KeyFrame =>
                    ((double)Math.Abs(KeyFrame.GetKeyValue() - Key)) < 1e-5);
            if (KeyFrame != null)
            {
                ModifyValue(KeyFrame, CompressedValue);
            }
            else
            {
                AddNewKey(Key, CompressedValue);
            }
        }
        public KeyFrame FindByKeyValue(decimal Key)
        {
            KeyFrame KeyFrame = KeyFrames.Find(KeyFrame =>
                    ((double)Math.Abs(KeyFrame.GetKeyValue() - Key)) < 1e-5);
            return KeyFrame;
        }

        public void ModifyValue(KeyFrame key, object Value)
        {
            //var point = key.BindObject as Point;
            var point = key.BindObject as SmoothPoint;
            if (point == null)
                return;
            if (Value.GetType() == typeof(List<string>))
            {
                point.EventString = ProcessEventString(Value);
            }
            Curve.UpdateCurves();
            Curve.PostModifiedCurve();
        }

        public string ProcessEventString(object Value)
        {
            string EventString;
            List<string> StringList = (List<string>)Value;
            if (StringList.Count != 0)
            {
                EventString = "";
                foreach (var value in StringList)
                {
                    EventString += value + "|";
                }
            }
            else
            {
                EventString = "";
            }
            return EventString;
        }

        public Entity GetPrevieEntity()
        {
            return Previewing;
        }

        public EventCurveContext GetContext()
        {
            return _Context;
        }

        public void SetContext(EventCurveContext Context)
        {
            _Context = Context;
        }

        #region Delete KeyFrames

        public ICollection<KeyFrame> GetSelectedKeys()
        {
            if (GetIsSelected() == false)
                return new List<KeyFrame>();
            return KeyFrames.Where(k =>
            {
                var holder = ParentTrack as ComponentTrackHolder;

                var distance = holder.GetScreenDistance(k);
                if (distance < 5)
                    return true;
                return false;
            }).ToList();
        }

        public ICollection<KeyFrame> GetSelectedKeys(List<KeyFrame> SelectedKeyFrames)
        {
            if (SelectedKeyFrames.Count == 0)
                return new List<KeyFrame>();
            return KeyFrames.Where(k =>
            {
                foreach (var KeyFrame in SelectedKeyFrames)
                {
                    if (KeyFrame.GetKeyValue() == k.GetKeyValue())
                    {
                        return true;
                    }
                }
                return false;
            }).ToList();
        }

        public void RemoveComponentValueFromSelected(List<KeyFrame> SelectedKeyFrames)
        {
            ICollection<KeyFrame> selectedKeys = new List<KeyFrame>();
            if (SelectedKeyFrames == null)
            {
                selectedKeys = GetSelectedKeys();
            }
            else
            {
                selectedKeys = GetSelectedKeys(SelectedKeyFrames);
            }
            if (selectedKeys.Count != 0)
            {
                foreach (var keyframe in selectedKeys)
                {
                    var point = keyframe.BindObject as Point;
                    var ownerCurve = point.OwnerCurve;
                    if (ownerCurve == null) { break; }
                    point.OwnerCurve.DeletePoint(point);
                    ownerCurve.PostModifiedCurve();
                }
            }
        }
        #endregion
    }
}