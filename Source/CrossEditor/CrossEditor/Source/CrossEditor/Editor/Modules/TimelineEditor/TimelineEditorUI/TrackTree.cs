using EditorUI;
using System;
using System.Collections.Generic;

namespace CrossEditor
{
    public class TrackTree
    {
        ScrollView _ScrollView;
        Panel _ScrollPanel;
        Panel _TrackPanel;

        List<Track> RootTrackList;
        HashSet<Track> TrackSet;

        public event System.Action<Track> OnTrackItemClicked = null;

        public void Initialize(Panel TrackPanel)
        {
            _TrackPanel = TrackPanel;

            _ScrollView = new ScrollView();
            _ScrollView.Initialize();
            _ScrollView.SetBackgroundColor(Color.EDITOR_UI_TREE_BACK_COLOR);
            _ScrollView.LeftMouseDownEvent += ScrollViewLeftMouseDownEvent;
            _ScrollView.GetHScroll().SetEnable(false);
            _ScrollPanel = _ScrollView.GetScrollPanel();
            _ScrollPanel.SetBackgroundColor(Color.EDITOR_UI_TREE_BACK_COLOR);

            RootTrackList = new List<Track>();
            TrackSet = new HashSet<Track>();
        }

        private void ScrollViewLeftMouseDownEvent(Control Sender, int MouseX, int MouseY, ref bool bContinue)
        {
            foreach (var OtherTrack in TrackSet) OtherTrack.SetIsSelected(false);
            CinematicUI.GetInstance().SetPreviewEntity(null);
        }

        public ScrollView GetScrollView() { return _ScrollView; }
        public Panel GetScrollPanel() { return _ScrollPanel; }


        public List<Track> GetRootTracks() => RootTrackList;
        public HashSet<Track> GetTracks() { return TrackSet; }

        public void AddTrack(Track NewTrack, bool Istop = false)
        {
            NewTrack.ParentTree = this;
            NewTrack.PostTrackAddEvent += PostTrackAdd;
            NewTrack.TrackItemClickedEvent += OnTrackClicked;
            if (Istop)
            {
                RootTrackList.Insert(0, NewTrack);
            }
            else
            {
                RootTrackList.Add(NewTrack);
            }
            TrackSet.Add(NewTrack);
            _ScrollPanel.AddChild(NewTrack.GetTrackItem());
        }

        public void ClearTrack()
        {
            var Tmp = new List<Track>(RootTrackList);
            foreach (var Track in Tmp)
                RemoveTrack(Track);
        }

        public bool RemoveTrack(Track Track)
        {
            if (TrackSet.Contains(Track))
            {
                UnbindTrack(Track);

                Track Parent = Track.GetParentTrack();
                if (Parent == null)
                {
                    return RootTrackList.Remove(Track);
                }
                else
                    return Parent.RemoveChildTrack(Track);
            }
            else
                return false;
        }

        public bool UnbindTrack(Track Track)
        {
            if (TrackSet.Contains(Track))
            {
                Track.TrackItemClickedEvent -= OnTrackClicked;
                Track.PostTraverse((NowTrack) => _ScrollPanel.RemoveChild(NowTrack.GetTrackItem()));
                _ScrollPanel.RemoveChild(Track.GetTrackItem());
                TrackSet.Remove(Track);

                return true;
            }
            else
                return false;
        }

        public void SelectTrack(Track Track)
        {
            HashSet<Track> Temp = new HashSet<Track>(TrackSet);
            Temp.Remove(Track);
            Track.SetIsSelected(true);
            foreach (var OtherTrack in Temp) OtherTrack.SetIsSelected(false);
        }

        public void Draw(UIManager UIManager)
        {
            Queue<Track> TrackQueue = new Queue<Track>(RootTrackList);
            while (TrackQueue.Count > 0)
            {
                Track Track = TrackQueue.Peek();
                if (Track.GetTrackItem().GetY() >= Math.Abs(_ScrollView.GetBaseY()) &&
                    Track.GetTrackItem().GetY() <= Math.Abs(_ScrollView.GetBaseY()) + _ScrollView.GetVisibleHeight())
                {
                    Track.Draw(UIManager);
                }

                if (Track.GetExpanded())
                {
                    foreach (Track Child in Track.GetChildList())
                        TrackQueue.Enqueue(Child);
                }

                TrackQueue.Dequeue();
            }
        }

        public void Update()
        {
            foreach (var Track in RootTrackList)
            {
                Track.Update();
            }
        }

        public void UpdateValue(float Key)
        {
            foreach (var Track in RootTrackList)
                Track.UpdateValue(Key);
        }

        public void UpdateLayout()
        {
            int ScrollPanelWidth = _ScrollView.GetWidth();
            if (_ScrollView.GetVScroll().GetVisible())
                ScrollPanelWidth -= ScrollView.SCROLL_BAR_SIZE;

            int Y = 0;
            foreach (var Track in RootTrackList)
            {
                Track.UpdateLayout(true, ScrollPanelWidth, 0, ref Y);
            }
            int Height = Y;
            _ScrollPanel.SetSize(ScrollPanelWidth, Height);
            _ScrollView.UpdateScrollBar();
        }

        public void PostTrackAdd(Track Sender, Track NewTrack)
        {
            NewTrack.ParentTree = this;
            NewTrack.PostTrackAddEvent += PostTrackAdd;
            NewTrack.TrackItemClickedEvent += OnTrackClicked;
            TrackSet.Add(NewTrack);
            _ScrollPanel.AddChild(NewTrack.GetTrackItem());

            foreach (var Child in NewTrack.GetChildList())
                PostTrackAdd(NewTrack, Child);
            //CinematicUI.GetInstance().SetModified();
        }

        void OnTrackClicked(Track Sender, Key MouseType, int MouseX, int MouseY)
        {
            SelectTrack(Sender);
            if (MouseType == Key.RightButton)
            {
                ShowEditMenu(Sender, MouseX, MouseY);
            }

            // call back to owner if exists one
            OnTrackItemClicked?.Invoke(Sender);
        }

        public void ShowEditMenu(Track Track, int MouseX, int MouseY)
        {
            Menu EditMenu = new Menu(_ScrollView.GetUIManager());
            EditMenu.Initialize();

            if (Track.CanBeDeleted)
            {
                MenuItem MenuItem_Delete = new MenuItem();
                MenuItem_Delete.SetText("Delete");
                MenuItem_Delete.SetImage(UIManager.LoadUIImage("Editor/Icons/Edit/Delete.png"));
                //MenuItem_Delete.SetEnable(!Track.GetIsFixed());
                MenuItem_Delete.ClickedEvent += (Sender) =>
                {
                    if (Track.GetTagObject() is PreviewAnimSeqAsset)
                    {
                        MSAPreviewContext.GetInstance().RemoveSubData(Track.GetTagObject() as PreviewAnimSeqAsset, MSAPreviewContext.AnimSubDataType.CurveTrack, Track.GetCurve().RuntimeCurve);
                    }

                    //delete track
                    List<object> Tracks = null;
                    List<object> ParentTrack = null;
                    List<Entity> DeleteEntities = CinematicUI.GetInstance().SelectTrackList(out Tracks, out ParentTrack);

                    CinematicUI.GetInstance().DeleteSelectedCineCamera(DeleteEntities);
                    CinematicUI.GetInstance().DeleteTrackFromTree(DeleteEntities, Tracks, ParentTrack);
                };
                EditMenu.AddMenuItem(MenuItem_Delete);
            }
            if (Track is CompositeTrack && Track.GetParentTrack().GetTagObject() is PreviewAnimatrixAsset)
            {
                MenuItem MenuItem_Preview = new MenuItem();
                MenuItem_Preview.SetText("Preview");
                MenuItem_Preview.ClickedEvent += (Sender) =>
                {
                    var PreviewAnimatrix = Track.GetParentTrack().GetTagObject() as PreviewAnimatrixAsset;
                    PreviewAnimatrix.PreviewSlot = Track.GetName();
                };
                EditMenu.AddMenuItem(MenuItem_Preview);

            }

            if (EditMenu.GetMenuItemCount() > 0)
            {
                UIManager UIManager = Track.GetUIManager();
                UIManager.GetContextMenu().ShowMenu(EditMenu, MouseX, MouseY);
            }
        }

        public List<ComponentTrack> FindTRSComponent(Entity Entity)
        {
            List<ComponentTrack> Lists = new List<ComponentTrack>();
            Track Target = null;
            foreach (var Track in RootTrackList)
            {
                if (Track.GetName() == Entity.GetName())
                {
                    foreach (var ComponentTrack in Track.GetChildList())
                    {
                        if (ComponentTrack is ComponentTrackHolder)
                        {
                            if (((ComponentTrackHolder)ComponentTrack).GetName() == "Transform")
                            {
                                Target = ComponentTrack;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            foreach (var Track in Target.GetChildList())
            {
                Lists.Add((ComponentTrack)Track);
            }
            return Lists;
        }


        public void UpdateTrackFold()
        {
            foreach (var Track in RootTrackList)
            {
                Track.SetExpanded();
            }
        }

        public void RemoveTrackList()
        {
            RootTrackList.Clear();
            TrackSet.Clear();
        }

        public void SetTrackSelected(bool Selected)
        {
            foreach (var Track in TrackSet) Track.SetIsSelected(Selected);
        }
    }
}
