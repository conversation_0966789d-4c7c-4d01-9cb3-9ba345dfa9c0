using Clicross;
using Editor<PERSON>;

namespace CrossEditor
{
    public class WorkflowDetailView : WorkflowEditorDetailView
    {
        OperationBarUI _OperationBarUI = new OperationBarUI();
        SearchUI _SearchUI = new SearchUI();

        public WorkflowDetailView(WorkflowEditorContext context)
            : base(context)
        {
            _OperationBarUI.Initialize();
            _OperationBarUI.GetPanelBar().SetText("Details");
            _OperationBarUI.GetPanelBar().SetTextAlign(TextAlign.CenterLeft);
            _OperationBarUI.GetPanelBar().SetFontSize(12);
            _OperationBarUI.GetPanelBar().SetTextOffsetX(6);

            _SearchUI.Initialize();
            _SearchUI.SearchEvent += OnSearchUISearch;
            _SearchUI.CancelEvent += OnSearchUICancel;
            Panel PanelBack = _SearchUI.GetPanelBack();
            PanelBack.SetPosition(0, 2, 130, 20);
            PanelBack.SetBackgroundColor(OperationBarUI.BAR_COLOR);
            _OperationBarUI.AddRight(PanelBack);
        }

        public override void OnPositionChanged(Control Sender, bool bPositionChanged, bool bSizeChanged)
        {
            if (bSizeChanged)
            {
                _SearchUI.GetPanelBack().SetWidth(_ScrollView.GetWidth() - 12);
                UpdateLayout();
            }
        }

        public void OnAddToParent(VContainer mContainer)
        {
            mContainer.AddFixedChild(_OperationBarUI.GetPanelBar());
            mContainer.AddSizableChild(Control, 1);
        }
        public void AfterInspectObjectChange(string newTitle)
        {
            _OperationBarUI.GetPanelBar().SetText("Details::" + newTitle);
        }

    }
}
