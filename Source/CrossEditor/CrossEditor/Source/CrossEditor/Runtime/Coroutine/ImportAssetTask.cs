using CEngine;
using System;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace CrossEditor
{
    public class ImportAssetTask : INotifyCompletion
    {
        public bool IsCompleted { get; private set; }

        private Action mContinuation;
        AssetImportResult mResult;

        public AssetImportResult GetResult()
        {
            Debug.Assert(IsCompleted);
            return mResult;
        }

        void INotifyCompletion.OnCompleted(Action continuation)
        {
            mContinuation = continuation;
        }

        public ImportAssetTask GetAwaiter() => this;

        public void Complete(AssetImportResult result)
        {
            Debug.Assert(!IsCompleted);
            IsCompleted = true;
            mResult = result;
            if (mContinuation != null)
            {
                CrossSynchronizationContext.Instance.Post(_ => mContinuation(), null);
            }
        }

        [UnmanagedFunctionPointer(CallingConvention.StdCall)]
        private delegate void ImportAssetCompleteCallback(long awaiter, bool success, int count, [MarshalAsAttribute(UnmanagedType.LPArray, SizeConst = 4)] int[] errors);

        [DllImport("AssetPipeline", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.StdCall)]
        private static extern void SetImportAssetCompleteCallback(ImportAssetCompleteCallback callback);

        private static ImportAssetCompleteCallback OnImportAssetComplete = (taskHandle, success, count, errors) =>
        {
            HandleManager HandleManager = HandleManager.GetInstance();
            object task = HandleManager.GetObjectByHandle(taskHandle);
            HandleManager.RemoveHandle(taskHandle);
            var importAssetTask = task as ImportAssetTask;
            if (importAssetTask != null)
            {
                AssetImportResult importResult = new AssetImportResult();
                importResult.bSuccess = success;
                for (int i = 0; i < count; i++)
                {
                    importResult.ErrorCodes.Add((AssetImportState)errors[i]);
                }
                importAssetTask.Complete(importResult);
            }
            else
            {
                Console.WriteLine("Invalid Task");
            }
        };

        public static void Initialize() => SetImportAssetCompleteCallback(OnImportAssetComplete);

    }
}
