namespace CrossEditor
{
    class Frustum
    {
        public Planed[] Planes;

        public Frustum()
        {
            Planes = new Planed[6];
        }

        public void UpdateFrustum(Vector3d[] Corners)
        {
            Planes[0] = new Planed(Corners[0], Corners[2], Corners[1]);
            Planes[1] = new Planed(Corners[1], Corners[3], Corners[5]);
            Planes[2] = new Planed(Corners[5], Corners[7], Corners[4]);
            Planes[3] = new Planed(Corners[4], Corners[6], Corners[0]);
            Planes[4] = new Planed(Corners[4], Corners[1], Corners[5]);
            Planes[5] = new Planed(Corners[2], Corners[7], Corners[3]);
        }
    }
}
