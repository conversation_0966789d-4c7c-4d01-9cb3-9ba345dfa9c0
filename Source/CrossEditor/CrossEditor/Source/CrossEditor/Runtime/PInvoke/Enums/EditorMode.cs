namespace CrossEditor
{
    public enum EditorMode
    {
        None = -1,
        Object_Mode = 0,
        Paint_Mode = 1,
        Terrain_Mode = 2,
        PCG_Mode = 3,
    }

    enum PaintMode
    {
        Editor = 0,
        Erase = 1,
    }

    enum AlignMode
    {
        World = 0,
        Terrain = 1,
    }

    enum PaintObjectMode
    {
        Cluster = 0,
        Single = 1,
    }

    enum PaintInstanceMode
    {
        Entity = 0,
        Instance = 1,
        Ectope = 2,
    }
}
