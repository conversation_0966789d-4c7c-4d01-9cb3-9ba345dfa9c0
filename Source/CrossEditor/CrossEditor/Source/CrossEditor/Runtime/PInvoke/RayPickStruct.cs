using CEngine;
namespace CrossEditor
{
    public class RayPickResult
    {
        public GeometryTag HitTag;
        public Entity HitEntity;
        public Vector3d HitPoint;
        public Vector3d HitFaceNormal;
        public int ModelIndex;
        public int MeshIndex;

        public RayPickResult()
        {
            HitTag = GeometryTag.Default;
            HitEntity = null;
            HitPoint = new Vector3d();
            ModelIndex = -1;
            MeshIndex = -1;
        }
    }

    public class RayPickResultDouble
    {
        public GeometryTag HitTag;
        public Entity HitEntity;
        public Vector3d HitPoint;
        public Vector3d HitFaceNormal;
        public int ModelIndex;
        public int MeshIndex;

        public RayPickResultDouble()
        {
            HitTag = GeometryTag.Default;
            HitEntity = null;
            HitPoint = new Vector3d();
            ModelIndex = -1;
            MeshIndex = -1;
        }
    }
}
