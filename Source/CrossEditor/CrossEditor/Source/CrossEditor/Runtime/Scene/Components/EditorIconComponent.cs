using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(ShowOrder = 0)]
    class EditorIconComponent : Component
    {
        static string[] _NativeNames = { "cross::EditorIconComponentG" };
        EditorIconComponentG _EditorIconComponentG = new EditorIconComponentG();

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public EditorIconComponent() { }

        static int _ComponentOrder = 1;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "Editor Icon Component")]
        public EditorIconComponentG EditorIcon
        {
            get
            {
                if (Entity.World != null)
                {
                    EditorIconSystemG.GetEditorIconComponent(Entity.World.GetNativePointer(), Entity.EntityID, _EditorIconComponentG);
                }
                return _EditorIconComponentG;
            }
            set
            {
                _EditorIconComponentG = value;
                EditorIconSystemG.SetEditorIconComponent(Entity.World.GetNativePointer(), Entity.EntityID, _EditorIconComponentG);
            }
        }
    }
}
