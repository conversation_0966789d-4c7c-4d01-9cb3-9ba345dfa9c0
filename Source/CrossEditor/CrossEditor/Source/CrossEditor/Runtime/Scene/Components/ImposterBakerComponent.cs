using CEngine;
using EditorUI;
using System.Reflection;

namespace CrossEditor
{
    [ComponentAttribute(NeedRenderProperty = true)]
    class ImposterBakerComponent : Component
    {
        public ImposterBakerComponent()
        {
        }

        Entity _CameraEntity;
        Camera _Camera;
        RenderTexture _RenderTexture;
        string _Prefab;
        string _OutputFolder = "Contents";
        EntityIDStruct _CaptureEntity;
        bool _DebugCamera = false;
        static string[] _NativeNames = { "cross::ImposterBakerComponentG" };

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        static int _ComponentOrder = 3;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }

        static int _GroupOrder = 1;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        public override void Reset()
        {
            base.Reset();
        }

        public void FindCaptureCamera()
        {
            if (_CameraEntity == null)
            {
                _CameraEntity = Entity.FindChildByName("CaptureCamera");
                if (_CameraEntity == null)
                {
                    _CameraEntity = Entity.World.CreateEntity();
                    Entity.AddChildEntity(_CameraEntity);
                    Transform TransformComponent = _CameraEntity.CreateComponent<Transform>();
                    _CameraEntity.RuntimeJointToParent();
                    _CameraEntity.CreateComponent<Camera>();
                    _CameraEntity.SetName("CaptureCamera");
                }
                _Camera = _CameraEntity.GetComponent(typeof(Camera)) as Camera;
                _Camera.Reset();

                ImposterBakerSystemG.SetRenderCamera(Entity.World.GetNativePointer(), _CameraEntity.GetEntityIdStruct());
            }

            if (_RenderTexture == null)
            {
                // renderTarget
                var info = new RenderTextureInfo
                {
                    Name = "CaptureCameraRenderTexture",
                    Dimension = TextureDimension.Tex2D,
                    Format = RenderTextureFormat.R8G8B8A8_UNorm,
                    Width = _Camera.TargetWidth,
                    Height = _Camera.TargetHeight,
                };
                _RenderTexture = new RenderTexture(info);
                _Camera.SetRenderTexture(_RenderTexture);
            }
        }

        public override void SyncDataFromEngine()
        {
            _OutputFolder = ImposterBakerSystemG.GetOutputFolder(Entity.World.GetNativePointer(), Entity.EntityID);
            _Prefab = ImposterBakerSystemG.GetPrefabResource(Entity.World.GetNativePointer(), Entity.EntityID);
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Place foliage prefab here.",
            FileTypeDescriptor = "Prefab Assets#prefab", ObjectClassID1 = ClassIDType.CLASS_PrefabResource)]
        public string PrefabResource
        {
            get => _Prefab;
            set
            {
                _Prefab = value;
                ImposterBakerSystemG.SetPrefabResource(Entity.World.GetNativePointer(), Entity.EntityID, _Prefab);
            }
        }


        [PropertyInfo(PropertyType = "StringAsFolder", ToolTips = "AndroidSDK")]
        public string OutputFolder
        {
            get => _OutputFolder;
            set
            {
                _OutputFolder = value;
                ImposterBakerSystemG.SetOutputFolder(Entity.World.GetNativePointer(), Entity.EntityID, _OutputFolder);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "", bReadOnly = false)]
        public EntityIDStruct CaptureEntity
        {
            get => _CaptureEntity;
            set
            {
                Entity entity = Entity.World.Root.SearchChildByEntityID(value.GetValue());
                if (entity != null && entity.GetModelComponent() != null)
                {
                    _CaptureEntity = value;
                    ImposterBakerSystemG.SetCaptureEntity(Entity.World.GetNativePointer(), Entity.EntityID, _CaptureEntity);
                }
                else
                {
                    CommonDialogUI.ShowSimpleOKDialog(UIManager.GetActiveUIManager(), "Tips", "Place a model entity here.");
                }
            }
        }

        public bool DebugCamera
        {
            get => _DebugCamera;
            set
            {
                _DebugCamera = value;
                ImposterBakerSystemG.DrawDebugCamera(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [MethodInfo(PropertyType = "Button")]
        public void Capture()
        {
            FindCaptureCamera();
            ImposterBakerSystemG.CaptureImposterGrid(Entity.World.GetNativePointer(), Entity.GetEntityIdStruct());
        }
    }
}
