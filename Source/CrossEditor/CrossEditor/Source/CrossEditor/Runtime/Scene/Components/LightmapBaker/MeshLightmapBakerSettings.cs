using CEngine;

namespace CrossEditor
{
    class LightMap : Component
    {
        static string[] _NativeNames = { "cross::LightMapComponentG" };
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 9;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        LightMapComponentG mLightMapComp = new LightMapComponentG();

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable mesh lightmap baker Settings.")]
        public override bool Enable
        {
            get
            {
                _Enable = LightMapSystemG.GetLightMapEnable(Entity.World.GetNativePointer(), Entity.EntityID);
                return _Enable;
            }
            set
            {
                _Enable = value;
                LightMapSystemG.SetLightMapEnable(Entity.World.GetNativePointer(), Entity.EntityID, _Enable);
            }
        }

        [PropertyInfo(PropertyType = "Struct", bAutoExpandStruct = true, ToolTips = "LightProbeVolume")]
        public LightMapComponentG LightMapComp
        {
            get
            {
                if (Entity.World != null)
                {
                    LightMapSystemG.GetLightMapComponent(Entity.World.GetNativePointer(), Entity.EntityID, mLightMapComp);
                }
                return mLightMapComp;
            }
            set
            {
            }
        }

        public override void RuntimeRemoveComponent()
        {
            Enable = false;
            base.RuntimeRemoveComponent();
        }
    }

    class LightMapBaker : Component
    {
        static string[] _NativeNames = { "cross::LightMapEditorComponentG" };
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 7;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }

        bool _UseDirectionality;
        bool _CastShadow;
        bool _ApplyLightmap;
        bool _ApplyShadowmap;
        bool _AbleToUse;
        bool _ForceBakeLightMap;
        float _DiffuseBoost;
        Float4 _InvPenumbraSize;

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public LightMapBaker()
        {
            _AbleToUse = false;
            _CastShadow = false;
            _ApplyLightmap = false;
            _ApplyShadowmap = false;
            _InvPenumbraSize = new Float4();
            _ForceBakeLightMap = false;
            _IsEditorOnlyComponent = true;
        }

        public override void Reset()
        {
            base.Reset();
            _AbleToUse = false;
            CastShadow = true;
            ApplyLightmap = false;
            ApplyShadowmap = false;
            LightmapResolution = 64;
            _ForceBakeLightMap = false;
            _InvPenumbraSize = new Float4(20.0f, 20.0f, 20.0f, 20.0f);
        }

        public override void Initialize(string initialization)
        {
            ApplyLightmap = true;
            ApplyShadowmap = true;
            SetLightMapRefresh(true);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Enable mesh lightmap baker Settings.")]
        public override bool Enable
        {
            get
            {
                _Enable = LightMapBakeSystemG.GetLightMapEnable(Entity.World.GetNativePointer(), Entity.EntityID);
                return _Enable;
            }
            set
            {
                _Enable = value;
                LightMapBakeSystemG.SetLightMapEnable(Entity.World.GetNativePointer(), Entity.EntityID, _Enable);
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    Entity.GetComponent(typeof(LightMap)).Enable = value;
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "have 2uv?", bReadOnly = true)]
        public bool AbleToUse
        {
            get
            {
                _AbleToUse = LightMapBakeSystemG.GetLightMapAbleToUse(Entity.World.GetNativePointer(), Entity.EntityID);
                return _AbleToUse;
            }
            set
            {
            }
        }

        public void SetLightMapRefresh(bool refresh)
        {
            LightMapBakeSystemG.SetLightMapRefresh(Entity.World.GetNativePointer(), Entity.EntityID, refresh);
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to cast shadow.")]
        public bool CastShadow
        {
            get
            {
                _CastShadow = LightMapBakeSystemG.GetLightMapCastShadow(Entity.World.GetNativePointer(), Entity.EntityID);
                return _CastShadow;
            }
            set
            {
                _CastShadow = value;
                LightMapBakeSystemG.SetLightMapCastShadow(Entity.World.GetNativePointer(), Entity.EntityID, _CastShadow);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Lightmap resolution.")]
        public int LightmapResolution
        {
            get
            {
                Float2 resolution = LightMapBakeSystemG.GetLightMapResolution(Entity.World.GetNativePointer(), Entity.EntityID);
                return (int)resolution.x;
            }
            set
            {
                Float2 resolution = new Float2(value, value);
                LightMapBakeSystemG.SetLightMapResolution(Entity.World.GetNativePointer(), Entity.EntityID, resolution);
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "")]
        public float DiffuseBoost
        {
            get
            {
                _DiffuseBoost = LightMapBakeSystemG.GetLightMapDiffuseBoost(Entity.World.GetNativePointer(), Entity.EntityID);
                return _DiffuseBoost;
            }
            set
            {
                _DiffuseBoost = value;
                LightMapBakeSystemG.SetLightMapDiffuseBoost(Entity.World.GetNativePointer(), Entity.EntityID, _DiffuseBoost);
            }
        }

        void TryCreateLightMap(bool enable)
        {
            if (enable)
            {
                if (!Entity.HasComponent(typeof(LightMap)))
                {
                    Entity.CreateComponent<LightMap>();
                    InspectorUI.GetInstance().InspectObject();
                    EditorScene.GetInstance().SetDirty();
                }
            }
            else
            {
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    Component comp = Entity.GetComponent(typeof(LightMap));
                    Entity.RemoveComponent(comp);
                    InspectorUI.GetInstance().InspectObject();
                    EditorScene.GetInstance().SetDirty();
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to apply lightmap.")]
        public bool ApplyLightmap
        {
            get
            {
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    _ApplyLightmap = LightMapSystemG.GetLightMapApplyLightMap(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                return _ApplyLightmap;
            }
            set
            {
                _ApplyLightmap = value && AbleToUse;
                TryCreateLightMap(_ApplyLightmap || _ApplyShadowmap);
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    LightMapSystemG.SetLightMapApplyLightMap(Entity.World.GetNativePointer(), Entity.EntityID, _ApplyLightmap);
                    _ApplyLightmap = LightMapSystemG.GetLightMapApplyLightMap(Entity.World.GetNativePointer(), Entity.EntityID);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to apply shadowmap.")]
        public bool ApplyShadowmap
        {
            get
            {
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    _ApplyShadowmap = LightMapSystemG.GetLightMapApplyShadowMap(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                return _ApplyShadowmap;
            }
            set
            {
                _ApplyShadowmap = value && AbleToUse;
                TryCreateLightMap(_ApplyShadowmap || _ApplyLightmap);
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    LightMapSystemG.SetLightMapApplyShadowMap(Entity.World.GetNativePointer(), Entity.EntityID, _ApplyShadowmap);
                    _ApplyShadowmap = LightMapSystemG.GetLightMapApplyShadowMap(Entity.World.GetNativePointer(), Entity.EntityID);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Softness of shadow.")]
        public float InvPenumbraSize
        {
            get
            {
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    _InvPenumbraSize = LightMapSystemG.GetLightMapInvPenumbraSize(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                return _InvPenumbraSize.x;
            }
            set
            {
                _InvPenumbraSize.x = value;
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    LightMapSystemG.SetLightMapInvPenumbraSize(Entity.World.GetNativePointer(), Entity.EntityID, _InvPenumbraSize);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Whether to UseLMDirectionality.")]
        public bool UseLMDirectionality
        {
            get
            {
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    _UseDirectionality = LightMapSystemG.GetLightMapUseDirectionality(Entity.World.GetNativePointer(), Entity.EntityID);
                }
                return _UseDirectionality;
            }
            set
            {
                _UseDirectionality = value;
                if (Entity.HasComponent(typeof(LightMap)))
                {
                    LightMapSystemG.SetLightMapUseDirectionality(Entity.World.GetNativePointer(), Entity.EntityID, _UseDirectionality);
                }
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Ignore BakeLightMapEnable in config settings")]
        public bool ForceBakeLightMap
        {
            get
            {
                _ForceBakeLightMap = LightMapBakeSystemG.GetLightMapForceBakeLightMap(Entity.World.GetNativePointer(), Entity.EntityID);
                return _ForceBakeLightMap;
            }
            set
            {
                _ForceBakeLightMap = value;
                LightMapBakeSystemG.SetLightMapForceBakeLightMap(Entity.World.GetNativePointer(), Entity.EntityID, _ForceBakeLightMap);
            }
        }

        public string GetDiffuseTex()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapLMDiffuseTex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetTransferTex0()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapLMTransferTex0(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetTransferTex1()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapLMTransferTex1(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetLocalLMTransferTex()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapLocalLMTransferTex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetShadowTex()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapLMShadowMapTex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetSkyOcclusionTex()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapSkyOcclusionTex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }

        public string GetAOMaterialMaskTex()
        {
            if (Entity.HasComponent(typeof(LightMap)))
            {
                return LightMapSystemG.GetLightMapAOMaterialMaskTex(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            return "";
        }
    }
}