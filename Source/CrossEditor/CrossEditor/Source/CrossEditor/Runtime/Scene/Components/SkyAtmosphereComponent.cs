using CEngine;

namespace CrossEditor
{
    class SkyAtmosphereComponent : Component
    {

        static string[] _NativeNames = { "cross::SkyAtmosphereComponentG" };
        SkyAtmosphereConfig _Config;
        SkyAtmosphereOuterParam _OuterParam;
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public SkyAtmosphereComponent()
        {
            _Config = new SkyAtmosphereConfig();
            _OuterParam = new SkyAtmosphereOuterParam();
        }
        static int _ComponentOrder = 0;
        public override int ComponentOrder()
        {
            return _ComponentOrder;
        }
        static int _GroupOrder = 50;
        public override int GroupOrder()
        {
            return _GroupOrder;
        }
        public override void Reset()
        {
            base.Reset();
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "SkyAtmosphere configuration")]
        public SkyAtmosphereConfig Config
        {
            get
            {
                return SkyAtmosphereSystemG.GetSkyAtmosphereConfig(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _Config = value;
                SkyAtmosphereSystemG.SetSkyAtmosphereConfig(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "SkyAtmosphere outer param")]
        public SkyAtmosphereOuterParam OuterParam
        {
            get
            {
                return SkyAtmosphereSystemG.GetSkyAtmosphereOuterParam(Entity.World.GetNativePointer(), Entity.EntityID);
            }
            set
            {
                _OuterParam = value;
                SkyAtmosphereSystemG.SetSkyAtmosphereOuterParam(Entity.World.GetNativePointer(), Entity.EntityID, value);
            }
        }

        public override void OnComponentAddToEntity()
        {
            base.OnComponentAddToEntity();
            Config = _Config;
            OuterParam = _OuterParam;
        }
    }
}