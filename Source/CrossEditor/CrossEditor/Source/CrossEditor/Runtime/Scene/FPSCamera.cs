using CEngine;
using Newtonsoft.Json;
using System;

namespace CrossEditor
{
    [JsonObject(MemberSerialization.OptIn)]
    public class FPSCamera
    {
        [JsonProperty("position")]
        Double3 _Position;

        [JsonProperty("directionX")]
        public double _DirX; // radians
        [JsonProperty("directionY")]
        public double _DirY;
        [JsonProperty("directionZ")]
        public double _DirZ;

        bool _InMovement;

        [JsonProperty("target")]
        Double3 _MovementTarget;

        [JsonProperty("speed")]
        double _MovementSpeed;

        double _LookAtDistance;

        bool _FlightMode;
        bool _bDirty;

        public FPSCamera()
        {
            _Position = new Double3();
            _InMovement = false;
            _LookAtDistance = 1000.0;

            _MovementSpeed = 0.0;

            _FlightMode = false;
            _bDirty = false;
        }

        public void Copy(FPSCamera Camera)
        {
            _Position = Camera._Position;
            _DirX = Camera._DirX;
            _DirY = Camera._DirY;

            _MovementTarget = Camera._MovementTarget;
            _MovementSpeed = Camera._MovementSpeed;

            _FlightMode = Camera._FlightMode;

            _bDirty = true;
        }

        public bool GetFlightMode()
        {
            return _FlightMode;
        }

        public void SetFlightMode(bool FlightMode)
        {
            if (_FlightMode != FlightMode) _bDirty = true;
            _FlightMode = FlightMode;
        }

        public bool IsDirty() => _bDirty;

        public void SetDirty(bool Dirty) => _bDirty = Dirty;

        public Double3 GetPosition()
        {
            return _Position;
        }

        public void SetPosition(Double3 Position)
        {
            _Position = Position;

            _bDirty = true;
        }

        public Double3 GetUpVector()
        {
            if (_FlightMode)
            {
                return new Double3(_Position.x, _Position.y, _Position.z).Normalized();
            }
            else
            {
                return new Double3(0.0, 1.0, 0.0);
            }
        }

        public Double4x4 GetGeoMatrix()
        {
            Double3 UpVector = GetUpVector();
            Double3 NorthVector1 = new Double3(0.0, 1.0, 0.0);
            Double3 EastVector = UpVector.Cross(NorthVector1).Normalized();
            Double3 NorthVector = EastVector.Cross(UpVector).Normalized();
            Double4x4 geoMatrix = new Double4x4();

            geoMatrix.m00 = EastVector.x;
            geoMatrix.m01 = EastVector.y;
            geoMatrix.m02 = EastVector.z;
            geoMatrix.m03 = 0.0;

            geoMatrix.m10 = UpVector.x;
            geoMatrix.m11 = UpVector.y;
            geoMatrix.m12 = UpVector.z;
            geoMatrix.m13 = 0.0;

            geoMatrix.m20 = NorthVector.x;
            geoMatrix.m21 = NorthVector.y;
            geoMatrix.m22 = NorthVector.z;
            geoMatrix.m23 = 0.0;

            geoMatrix.m30 = 0.0;
            geoMatrix.m31 = 0.0;
            geoMatrix.m32 = 0.0;
            geoMatrix.m33 = 1.0;

            return geoMatrix;
        }

        Double3 DirXYToDirection(double DirX, double DirY)
        {
            double Y = -Math.Sin(DirX);
            double XZ = Math.Cos(DirX);
            double Z = Math.Cos(DirY) * XZ;
            double X = Math.Sin(DirY) * XZ;
            Double3 Direction = new Double3(X, Y, Z);
            return Direction;
        }

        void DirectionToDirXY(Double3 Direction, Double3 Up, ref double DirX, ref double DirY, ref double DirZ)
        {
            DirX = Math.Asin(-Direction.y);
            DirY = Math.Atan2(Direction.x, Direction.z);
        }

        public Double3 GetRotationInDegree()
        {
            double DirX = _DirX;
            double DirY = _DirY;
            double DirZ = _DirZ;

            if (_FlightMode)
            {
                Double3 LocalRotationEuler = new Double3(DirX, DirY, DirZ);
                Quaternion64 LocalRotationQuaternion = Quaternion64.EulerToQuaternion64(LocalRotationEuler);

                Double4x4 GeoMatrix = GetGeoMatrix();
                Quaternion64 WorldRotationQuaternion = Quaternion64.CreateFromRotationMatrix(GeoMatrix);

                Quaternion64 RotationQuaternion = LocalRotationQuaternion.Mult(WorldRotationQuaternion);
                Double3 RotationEuler = Quaternion64.Quaternion64ToEuler(RotationQuaternion);
                DirX = RotationEuler.x;
                DirY = RotationEuler.y;
                DirZ = RotationEuler.z;
            }

            Double3 Rotation = new Double3(DirX, DirY, DirZ);
            return Rotation.ToDegree();
        }

        public void SetRotationInDegree(Double3 Rotation)
        {
            Double3 RotationEuler = Rotation.ToRadian();
            _DirX = RotationEuler.x;
            _DirY = RotationEuler.y;
            _DirZ = RotationEuler.z;

            if (_FlightMode)
            {
                Quaternion64 RotationQuaternion = Quaternion64.EulerToQuaternion64(RotationEuler);

                Double4x4 GeoMatrix = GetGeoMatrix();
                Quaternion64 WorldRotationQuaternion = Quaternion64.CreateFromRotationMatrix(GeoMatrix);
                Quaternion64 InverseWorldRotationQuaternion = WorldRotationQuaternion.Inverse();

                Quaternion64 LocalRotationQuaternion = RotationQuaternion.Mult(InverseWorldRotationQuaternion);
                Double3 LocalRotationEuler = Quaternion64.Quaternion64ToEuler(LocalRotationQuaternion);
                _DirX = LocalRotationEuler.x;
                _DirY = LocalRotationEuler.y;
                _DirZ = LocalRotationEuler.z;
            }

            _bDirty = true;
        }

        public Double3 GetTarget()
        {
            Double3 Direction = DirXYToDirection(_DirX, _DirY);
            if (_FlightMode)
            {
                Double4x4 GeoMatrix = GetGeoMatrix();
                Direction = Double4x4.TransformVectorF3(GeoMatrix, Direction);
                Direction.Normalize();
            }
            return _Position.Add(Direction);
        }

        public void SetTarget(Double3 Target)
        {
            Double3 Direction = Target.Subtract(_Position);
            Direction.Normalize();
            Double3 Up = GetUpVector();
            DirectionToDirXY(Direction, Up, ref _DirX, ref _DirY, ref _DirZ);

            _bDirty = true;
        }

        void TurnTo(double NewDirX, CameraMode CameraMode)
        {
            if (CameraMode == CameraMode.WGS84)
            {
                double HalfPi = Math.PI / 2.0 - 0.01;
                if (NewDirX <= -HalfPi)
                {
                    return;
                }
                if (NewDirX >= HalfPi)
                {
                    return;
                }
            }
            _DirX = NewDirX;
            _bDirty = true;
        }

        public void TurnUp(double f, CameraMode CameraMode)
        {
            double NewDirX = _DirX - f;
            TurnTo(NewDirX, CameraMode);
        }

        public void TurnDown(double f, CameraMode CameraMode)
        {
            double NewDirX = _DirX + f;
            TurnTo(NewDirX, CameraMode);
        }

        public void TurnLeft(double f)
        {
            _DirY -= f;

            _bDirty = true;
        }

        public void TurnRight(double f)
        {
            _DirY += f;

            _bDirty = true;
        }

        public void GoFront(double f)
        {
            Double3 Target = GetTarget();
            Double3 Dir = Target.Subtract(_Position);
            Dir.Normalize();
            GoTo(_Position.Add(Dir.Mult(f)));
        }

        public void GoCloser(double f)
        {
            double d = _LookAtDistance > f ? f : _LookAtDistance;
            GoFront(d);
            _LookAtDistance -= d;
        }

        public void GoBack(double f)
        {
            GoFront(-f);
        }

        public void GoLeft(double f)
        {
            GoRight(-f);
        }

        public void GoRight(double f)
        {
            Double3 Target = GetTarget();
            Double3 Dir = Target.Subtract(_Position);
            Double3 UpVector = GetUpVector();
            if (_DirX > Math.PI / 2.0 || _DirX < -Math.PI / 2.0)
            {
                UpVector = new Double3(UpVector.x, -UpVector.y, UpVector.z);
            }
            Double3 Dir1 = UpVector.Cross(Dir);
            Dir1.Normalize();
            GoTo(_Position.Add(Dir1.Mult(f)));
        }

        public void GoDown(double f)
        {
            GoUp(-f);
        }

        public void GoUp(double f)
        {
            Double3 UpVector = GetUpVector();
            GoTo(_Position.Add(UpVector.Mult(f)));
        }

        public void GoDown2(double f)
        {
            GoUp2(-f);
        }

        public void GoUp2(double f)
        {
            Double3 Target = GetTarget();
            Double3 Dir = Target.Subtract(_Position);
            Double3 UpVector = GetUpVector();
            Double3 Dir1 = UpVector.Cross(Dir);
            Dir1.Normalize();
            Double3 UpVector2 = Dir.Cross(Dir1);
            GoTo(_Position.Add(UpVector2.Mult(f)));
        }

        public void GoTo(Double3 NewPos)
        {
            _Position = NewPos;

            _bDirty = true;
        }

        public void RotateHorizontalOn(Double3 Target)
        {

        }

        public void SetMovementTarget(Double3 MovementTarget)
        {
            _InMovement = true;
            _MovementTarget = MovementTarget;
            Double3 Distance = _MovementTarget.Subtract(_Position);
            double Distance1 = Distance.Length();
            const double TimeToMoveOver = 0.20;
            _MovementSpeed = Distance1 / TimeToMoveOver;
        }

        public void UpdateMovement(double TimeElpased)
        {
            if (_DirX > Math.PI)
            {
                _DirX -= Math.PI * 2.0f;
            }
            else if (_DirX < -Math.PI)
            {
                _DirX += Math.PI * 2.0f;
            }

            if (_InMovement)
            {
                Double3 Distance = _MovementTarget.Subtract(_Position);
                double Distance1 = Distance.Length();
                double DistanceToMove = _MovementSpeed * TimeElpased;
                if (Distance1 <= DistanceToMove)
                {
                    GoTo(_MovementTarget);
                    _InMovement = false;
                }
                else
                {
                    GoTo(_Position.Add(Distance.Mult(DistanceToMove / Distance1)));
                }
            }
        }

        public double GetLookAtDistance()
        {
            return _LookAtDistance;
        }

        public void SetLookAtDistance(double Distance)
        {
            _LookAtDistance = Distance;
        }
    }
}
