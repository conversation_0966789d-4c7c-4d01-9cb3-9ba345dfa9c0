using CEngine;
using System.Collections.Generic;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "HLOD/HLODComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class HLODGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::HLODComponent" };

        string _HLODSettingAsset = string.Empty;
        string _OverrideBaseMaterial = string.Empty;
        Clicross.HLODSimplification _HLODSetting = new();
        int _ForcedHLODModel = -1;

        public HLODGameComponent()
        {
        }

        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        public override void SyncDataFromEngine()
        {
            var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::HLODComponent") as Clicegf.HLODComponent;
            if (comp == null)
            {
                return;
            }

            _HLODSettingAsset = comp.GetHLODSettingAsset();

            var setting = comp.GetHLODSetting();
            _OverrideBaseMaterial = setting.OverrideBaseMaterial;
            _HLODSetting = setting.Setting;

            _ForcedHLODModel = comp.GetForcedHLODModel();
        }

        // Properties
        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "HLOD Setting", FileTypeDescriptor = "HLOD Setting Assets#nda", ObjectClassID1 = ClassIDType.CLASS_HLODResource)]
        public string HLODSettingAsset
        {
            get => _HLODSettingAsset;
            set {
                _HLODSettingAsset = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::HLODComponent") as Clicegf.HLODComponent;
                comp.SetHLODSettingAsset(_HLODSettingAsset);
            }
        }

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "Material to use when rendering.", FileTypeDescriptor = "Material Assets#nda", ObjectClassID1 = ClassIDType.CLASS_Material, ObjectClassID2 = ClassIDType.CLASS_Fx)]
        public string OverrideBaseMaterial
        {
            get => _OverrideBaseMaterial;
            set
            {
                _OverrideBaseMaterial = value;
                SyncToHLODSetting();
            }
        }

        [PropertyInfo(PropertyType = "Struct", ToolTips = "HLODSetup")]
        public Clicross.HLODSimplification HLODSetting
        {
            get => _HLODSetting;
            set
            {
                _HLODSetting = value;
                SyncToHLODSetting();
            }
        }

        [PropertyInfo(PropertyType = "Auto", ToolTips = "Forced HLOD Model")]
        public int ForcedHLODModel
        {
            get => _ForcedHLODModel;
            set
            {
                _ForcedHLODModel = value;
                var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::HLODComponent") as Clicegf.HLODComponent;
                comp.SetForcedHLODModel(_ForcedHLODModel);
            }
        }

        void SyncToHLODSetting()
        {
            var setup = new Clicross.HLODSetup();
            setup.Setting = _HLODSetting;
            setup.OverrideBaseMaterial = _OverrideBaseMaterial;

            var comp = GetRuntimeOwnerGameObject().GetComponentByMetaClassName("cegf::HLODComponent") as Clicegf.HLODComponent;
            comp.SetHLODSetting(setup);
        }
    }
}