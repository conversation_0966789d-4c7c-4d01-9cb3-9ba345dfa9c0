namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/PostProcessVolumeComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class PostProcessVolumeGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::PostProcessVolumeComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<PostProcessVolumeComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<PostProcessVolumeComponent>();
            }

            mECSEditorComponents["PostProcessVolumeComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["PostProcessVolumeComponent"] = mGameObject.mEntity.GetComponent<PostProcessVolumeComponent>();
        }
    }
}
