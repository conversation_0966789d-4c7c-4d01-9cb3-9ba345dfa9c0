namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Rendering/SkyAtmosphereComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class SkyAtmosphereGameComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::SkyAtmosphereComponent" };
        public override string[] NativeNames()
        {
            return _NativeNames;
        }
        protected override void CreateECSEditorComponent()
        {
            var comp = mGameObject.mEntity.GetComponent<SkyAtmosphereComponent>();
            if (comp == null)
            {
                comp = mGameObject.mEntity.CreateComponent<SkyAtmosphereComponent>();
            }

            mECSEditorComponents["SkyAtmosphereComponent"] = comp;
        }

        public override void SyncDataFromEngine()
        {
            mECSEditorComponents["SkyAtmosphereComponent"] = mGameObject.mEntity.GetComponent<SkyAtmosphereComponent>();
        }
    }
}
