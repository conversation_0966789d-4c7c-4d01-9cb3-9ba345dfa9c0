using CEngine;

namespace CrossEditor
{
    [ComponentAttribute(DisplayUINames = "Misc/WorkFlowComponent", InspectorType = typeof(Inspector_GameComponent))]
    public class WorkFlowComponent : GameObjectComponent
    {
        static string[] _NativeNames = { "cegf::WorkFlowComponent" };
        private Clicegf.WorkFlowComponent mRuntimeComponent;
        public override string[] NativeNames()
        {
            return _NativeNames;
        }

        private string mRes = "123";

        [PropertyInfo(PropertyType = "StringAsResource", ToolTips = "test input value")]
        public string WorkFlow
        {
            get
            {
                return mRes;
            }
            set
            {
                mRes = value;
                mRuntimeComponent.SetWorkflowRes(value);
            }
        }

        public override void SyncDataFromEngine()
        {
            base.SyncDataFromEngine();
            var go = GetRuntimeOwnerGameObject();
            var comp = (Clicegf.WorkFlowComponent)(go.GetComponentByMetaClassName(_NativeNames[0]));
            if (comp != null)
            {
                mRuntimeComponent = comp;
                WorkFlow = comp.GetWorkflowRes();
            }
        }
    }
}