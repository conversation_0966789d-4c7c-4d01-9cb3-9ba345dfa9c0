using CEngine;
using System;

namespace CrossEditor
{
    public class InstanceDataResource : Resource
    {
        public static void CreateResource(string Path)
        {
            InstanceDataResource Res = new InstanceDataResource(Clicross.resource.InstanceDataResource.InstanceDataResource_CreateInstanceDataResource());
            Res.SaveTo(Path);
        }

        public InstanceDataResource(Clicross.Resource resourcePtr) : base(resourcePtr, ClassIDType.CLASS_InstanceDataResource)
        {
        }

        public void SetInstanceCount(uint instanceCount)
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            instanddata.SetInstanceCount(instanceCount);
        }

        public UInt32 GetInstanceCount()
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            return instanddata.GetInstanceCount();
        }

        public void ClearAllInstanceDatas()
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            instanddata.ClearAllInstanceDatas();
        }

        public void ClearInstanceMemberData(string name)
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            instanddata.ClearInstanceMemberData(name);
        }

        public void SetInstanceMemberData(string name, UInt32 type, IntPtr data, UInt32 size, UInt32 stride)
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            instanddata.SetInstanceMemberData(name, type, data, size, stride);
        }

        public UInt32 GetInstanceMemberDataNameCount()
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            return instanddata.GetInstanceMemberDataNameCount();
        }

        public string GetInstanceMemberDataNameAt(UInt32 index)
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            return instanddata.GetInstanceMemberDataNameAt(index);
        }

        public void GetInstanceMemberData(string name, ref UInt32 outType, ref IntPtr outData, ref UInt32 outSize, ref UInt32 outStride)
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            Clicross.resource.InstanceMemberDataInfo info = instanddata.EditorGetInstanceMemberData(name);
            outData = info.outData;
            outSize = info.outSize;
            outStride = info.outStride;
            outType = info.outType;
        }

        public void MarkDirty()
        {
            var instanddata = ResourcePtr as Clicross.resource.InstanceDataResource;
            instanddata.MarkDirty();
        }
    }
}
