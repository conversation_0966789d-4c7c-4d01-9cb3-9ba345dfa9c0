#include "EmitterStateModule.h"
#include "ModulePipeline.h"

namespace cross::fx
{

void EmitterStateModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("EmitterStateModule");
    context.State = ParticleEmitterState::Active;

    mAccumulateDelayTime += context.DeltaTime;
    // StartDelay DO NOT use curve, only random is useful.
    if (mAccumulateDelayTime < mEmitterState->StartDelay.Evaluate(0.0f, context.Rand))
    {
        context.State = ParticleEmitterState::Delay;
        return;
    }

    context.Age += context.DeltaTime;
    context.NormalizedAge = context.Age / mEmitterState->LoopDuration;

    if (context.Age > mEmitterState->LoopDuration)
    {
        switch (mEmitterState->LoopBehavior)
        {
        case cross::LoopBehavior::Once:
            context.State = ParticleEmitterState::Inactive;
            mAccumulateDelayTime = 0.0f;
            break;
        case cross::LoopBehavior::Infinite:
            context.State = ParticleEmitterState::Active;
            context.Age = 0.0f;
            context.NormalizedAge = 0.0f;
            context.SpawnCount = 0.0f;
            mAccumulateDelayTime = 0.0f;
            break;
        case cross::LoopBehavior::Multiple:
        {
            mTotalLoopCount += 1;
            if (mTotalLoopCount >= mEmitterState->LoopCount)
            {
                context.State = ParticleEmitterState::Inactive;
                mAccumulateDelayTime = 0.0f;
            }
            else
            {
                context.State = ParticleEmitterState::Active;
                context.Age = 0.0f;
                context.NormalizedAge = 0.0f;
            }
            break;
        }
        default:
            break;
        }
    }
}

void EmitterStateModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mEmitterState = std::make_shared<EmitterStateInfo>(emitterInfo.EmitterState);
    mEnabled = mEmitterState->IsEnabled();
    mTotalLoopCount = 0u;
    mAccumulateDelayTime = 0.0f;
}

}   // namespace cross::fx