#include "GravityModule.h"
#include "ModulePipeline.h"
#include "../ParticleSystemUtils.h"

namespace cross::fx {

void GravityModule::Update(ModulePipelineContext& context)
{
    QUICK_SCOPED_CPU_TIMING("GravityModule");
    const UInt32 particleCount = static_cast<UInt32>(context.ParticleStates.Size());
    Float3* velocitys = context.ParticleParams->GetParameterAddr<Float3>(ParticleVariable(RendererProp::VELOCITY, ParticleDataType::Float3));
    for (auto particleIndex = 0u; particleIndex < particleCount; ++particleIndex)
    {
        if (!context.ParticleStates.IsAlive(particleIndex))
        {
            continue;
        }
        const float particleAge = context.ParticleStates.GetNormalizedAge(particleIndex);
        Float3 gravity;
        gravity.x = mGravity->GravityVector.X.<PERSON>ate(particleAge, mRand) * context.DeltaTime;
        gravity.y = mGravity->GravityVector.Y.Evaluate(particleAge, mRand) * context.DeltaTime;
        gravity.z = mGravity->GravityVector.Z.Evaluate(particleAge, mRand) * context.DeltaTime;
        if (context.LocalSpace)
        {
            gravity = Float4x4::TransformVectorF3(context.InvRelativeWorldMatrix, gravity);
        }
        velocitys[particleIndex] += gravity;
    }
}

void GravityModule::Flush(const ParticleEmitterInfo& emitterInfo)
{
    mGravity = std::make_shared<GravityInfo>(emitterInfo.GravityForce);
    mEnabled = mGravity->Enabled;
}

size_t GravityModule::Transfer(const ParticleEmitterInfo& emitterInfo, ParticleCurvePool& curvePool, size_t offset, std::vector<UInt8>& output)
{
    offset += TransferProperty(mGravity->GravityVector, curvePool, output, offset);
    return offset;
}

void GravityModule::ResetSeed(UInt32 randomSeed)
{
    mRand.SetSeed(randomSeed);
}

}   // namespace cross::fx