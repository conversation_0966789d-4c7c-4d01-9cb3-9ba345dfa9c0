#pragma once
#include "../CrossFXForward.h"

namespace cross {
struct DataInterfaceCurveInfo;
struct DataInterfaceInfo;
}

namespace cross::fx
{
struct NParticleSystemStaticBuffers;

class NParticleDataInterfaceG
{
public:
    NParticleDataInterfaceG(const DataInterfaceInfo* info) {}
    virtual ~NParticleDataInterfaceG() = default;
    virtual void CacheStaticBuffers(NParticleSystemStaticBuffers& staticBuffers) {}
};

class CrossFX_API NParticleDataInterfaceCurveG : public NParticleDataInterfaceG
{
public:
    NParticleDataInterfaceCurveG(const DataInterfaceCurveInfo* info);

    virtual ~NParticleDataInterfaceCurveG() override = default;

    virtual void CacheStaticBuffers(NParticleSystemStaticBuffers& staticBuffers) override;

protected:
    std::vector<float> mLUT;
    float mMinTime = 0.f;
    float mMaxTime = 0.f;
    float mInvTimeRange = 0.f;
    UInt32 mCurveLUTNumMinusOne = 0;
    UInt32 mLUTOffset = 0;
    std::string mName;
};

}