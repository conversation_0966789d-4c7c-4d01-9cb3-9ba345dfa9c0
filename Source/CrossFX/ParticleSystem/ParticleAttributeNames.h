#pragma once
#include <string>
#include "CrossBase/String/NameID.h"

namespace cross::fx
{

class EmitterProp
{
public:
    static inline const NameID RANDOM_SEED   = "RANDOM_SEED";
    static inline const NameID LOCAL_SPACE   = "LOCAL_SPACE";
    static inline const NameID LOOP_BEHAVIOR = "LOOP_BEHAVIOR";
    static inline const NameID LOOP_DURATION = "LOOP_DURATION";
    static inline const NameID LOOP_COUNT    = "LOOP_COUNT";

    static inline const NameID COUNT_LIMIT = "COUNT_LIMIT";
    static inline const NameID SPAWN_RATE  = "SPAWN_RATE";
    static inline const NameID SPAWN_BURST = "SPAWN_BURST";
    static inline const NameID BURST_TIME  = "BURST_TIME";

    static inline const NameID PARTICLE_DEAD            = "PARTICLE_DEAD";
};

class AffectorProp
{
public:
    static inline const NameID SPRITE_ROTATION_RATE = "SPRITE_ROTATION_RATE";
    static inline const NameID SPEED_SCALE          = "SPEED_SCALE";
    static inline const NameID SPEED_LIMIT          = "SPEED_LIMIT";
};

class RendererProp
{
public:
    static inline const NameID MATERIAL        = "MATERIAL";
    static inline const NameID POSITION        = "particle_Position";
    static inline const NameID VELOCITY        = "particle_AnimatedVelocity";
    static inline const NameID COLOR           = "particle_Color";
    static inline const NameID UVSCALE         = "particle_UVScale";
    static inline const NameID SIZE            = "particle_SizeScale";
    static inline const NameID ROTATION        = "particle_Rotation";

    static inline const NameID RENDERER_DEFAULT_PIVOT    = "particle_DefaultPivot";
    static inline const NameID RENDERER_ALIGN_MODE       = "particle_RenderAlignMode";
    static inline const NameID RENDERER_FACING_MODE      = "particle_RenderFacingMode";
    static inline const NameID RENDERER_ALIGN_DIRECTION  = "particle_RenderAlignDirection";
    static inline const NameID RENDERER_FACING_DIRECTION = "particle_RenderFacingDirection";
    static inline const NameID RENDERER_FACING_BLEND     = "particle_RenderFacingBlend";
    static inline const NameID RENDERER_CULL_MASK        = "particle_CullMask";
    static inline const NameID CAMERA_DISTANCE_CULLRANGE = "particle_CameraDistanceCullRange";
    static inline const NameID ENABLE_LARGE_COORDINATE   = "ENABLE_LARGE_COORDINATE";
};

class GpuParticleProp
{
public:
    static inline const NameID SYSTEM_RELATIVE_MATRIX = "_SystemRelativeMatrix";
    static inline const NameID SYSTEM_INVERSE_RELATIVE_MATRIX = "_SystemInverseRelativeMatrix";
    static inline const NameID SYSTEM_RELATIVE_MATRIX_NO_SCALE = "_SystemRelativeMatrixNoScale";
    static inline const NameID SYSTEM_TILE_POSITION = "_SystemTilePosition";
    static inline const NameID EMITTER_INDEX = "_EmitterIndex";
    static inline const NameID EMITTER_INSTANCE_CAPACITY = "_EmitterInstanceCapacity";
    static inline const NameID EMITTER_SPAWN_COUNT = "_EmitterSpawnCount";
    static inline const NameID EMITTER_TOTAL_SPAWNED_COUNT = "_EmitterTotalSpawnedCount";
    static inline const NameID EMITTER_ALIVE_NUM = "_EmitterAliveNum";
    static inline const NameID EMITTER_TICK_COUNTER = "_EmitterTickCounter";
    static inline const NameID EMITTER_RANDOM_SEED = "_EmitterRandomSeed";
    static inline const NameID EMITTER_AGE = "_EmitterAge";
    static inline const NameID EMITTER_NORMALIZED_AGE = "_EmitterNormalizedAge";
    static inline const NameID EMITTER_DELTA_TIME = "_EmitterDeltaTime";
    static inline const NameID EMITTER_KILL_TRIGGER = "_EmitterKillTrigger";
    static inline const NameID EMITTER_KILL_VOLUME_CENTER = "_EmitterKillVolumeCenter";
    static inline const NameID EMITTER_KILL_VOLUME_EXTENTS = "_EmitterKillVolumeExtents";
    static inline const NameID EMITTER_IS_LOCAL_SPACE = "_EmitterIsLocalSpace";
    static inline const NameID PARTICLE_INIT_VIEW = "_ParticleInitView";
    static inline const NameID PARTICLE_LOCATION_VIEW = "_ParticleLocationView";
    static inline const NameID PARTICLE_SOLVE_VIEW = "_ParticleSolveView";
    static inline const NameID LOCAL_SPACE_GPU_SCENE_BUFFER = "_LocalSpaceGPUSceneBuffer";
    static inline const NameID GLOBAL_SPACE_GPU_SCENE_BUFFER = "_GlobalSpaceGPUSceneBuffer";
    static inline const NameID GPU_SCENE_BUFFER_OFFSET = "_GPUSceneBufferOffset";
    static inline const NameID SIMULATION_RESULT = "_SimulationResult";
    static inline const NameID INPUT_ORIGIN_STATE_BUFFER = "_InputOriginStateBuffer";
    static inline const NameID OUTPUT_ORIGIN_STATE_BUFFER = "_OutputOriginStateBuffer";
    static inline const NameID INPUT_SIMULATION_STATE_BUFFER = "_InputSimulationStateBuffer";
    static inline const NameID OUTPUT_SIMULATION_STATE_BUFFER = "_OutputSimulationStateBuffer";
    static inline const NameID PARTICLE_SPAWN_DASHBOARD = "_ParticleSpawnDashboard";
    static inline const NameID PARTICLE_UPDATE_DASHBOARD = "_ParticleUpdateDashboard";
    static inline const NameID EMITTER_DASHBOARD_MASK = "_EmitterDashboardMask";
    static inline const NameID EMITTER_SIMULATION_STATE = "_EmitterSimulationState";
    static inline const NameID CURVE_LUTS = "_CurveLuts";
    static inline const NameID EMITTER_LOCATION_MESH_INDEX_BUFFER = "_EmitterLocationMeshIndexBuffer";
    static inline const NameID EMITTER_LOCATION_MESH_POSITION_BUFFER = "_EmitterLocationMeshPositionBuffer";
    static inline const NameID EMITTER_LOCATION_MESH_NORMAL_BUFFER = "_EmitterLocationMeshNormalBuffer";
    static inline const NameID EMITTER_LOCATION_MESH_TRIANGLE_COUNT = "_EmitterLocationMeshTriangleCount";
    static inline const NameID EMITTER_LOCATION_MESH_VERTEX_COUNT = "_EmitterLocationMeshVertexCount";
    static inline const NameID EMITTER_LOCATION_MESH_IS_16_BIT_INDEX = "_EmitterLocationMeshIs16BitIndex";
    static inline const NameID EMITTER_VECTOR_NOISE_MODULE_SCROLL_OFFSET = "_EmitterVectorNoiseModuleScrollOffset";
    static inline const NameID PARTICLE_INSTANCE_COUNTER_UAV = "_ParticleInstanceCounterUAV";
    static inline const NameID PARTICLE_INDIRECT_ARGS_UAV = "_ParticleIndirectArgsUAV";
    static inline const NameID PARTICLE_INDIRECT_ARGS_SRV = "_ParticleIndirectArgsSRV";

    //simulation camera
    static inline const NameID SYSTEM_SIMULATION_CAMERA_VIEW = "_SystemSimulationCameraView";
    static inline const NameID SYSTEM_SIMULATION_CAMERA_INV_VIEW = "_SystemSimulationCameraInvView";
    static inline const NameID SYSTEM_SIMULATION_CAMERA_POSITION = "_SystemSimulationCameraPosition";

    static inline const NameID SYSTEM_SIMULATION_CAMERA_PRE_VIEW = "_SystemSimulationCameraPreView";
    static inline const NameID SYSTEM_SIMULATION_CAMERA_PRE_INV_VIEW = "_SystemSimulationCameraPreInvView";
    static inline const NameID SYSTEM_SIMULATION_CAMERA_PRE_POSITION = "_SystemSimulationCameraPrePosition";

    static inline const NameID SYSTEM_SIMULATION_CAMERA_TILE_POSITION = "_SystemSimulationCameraTilePosition";
    static inline const NameID SYSTEM_SIMULATION_CAMERA_PRE_TILE_POSITION = "_SystemSimulationCameraPreTilePosition";
};

#define DECLARE_PROP(x) static inline const NameID x = #x
#define SHADER_PARAMETER(type, name)        DECLARE_PROP(name);
#define SHADER_PARAMETER_SRV(type, name)    DECLARE_PROP(name);
#define SHADER_PARAMETER_UAV(type, name)    DECLARE_PROP(name);
#define SHADER_PARAMETER_ARRAY(type, name, arrayDecl)  DECLARE_PROP(name);

namespace NParticleProperty::GlobalParameters {

    SHADER_PARAMETER(float, Engine_WorldDeltaTime)
    SHADER_PARAMETER(float, Engine_DeltaTime)
    SHADER_PARAMETER(float, Engine_InverseDeltaTime)
    SHADER_PARAMETER(float, Engine_Time)
    SHADER_PARAMETER(float, Engine_RealTime)
    SHADER_PARAMETER(int32, Engine_QualityLevel)
    //SHADER_PARAMETER(int32, Engine_Pad0)
    //SHADER_PARAMETER(int32, Engine_Pad1)

    SHADER_PARAMETER(float, PREV_Engine_WorldDeltaTime)
    SHADER_PARAMETER(float, PREV_Engine_DeltaTime)
    SHADER_PARAMETER(float, PREV_Engine_InverseDeltaTime)
    SHADER_PARAMETER(float, PREV_Engine_Time)
    SHADER_PARAMETER(float, PREV_Engine_RealTime)
    SHADER_PARAMETER(int32, PREV_Engine_QualityLevel)

}

namespace NParticleProperty::SystemParameters {
    SHADER_PARAMETER(float, Engine_Owner_TimeSinceRendered)
    SHADER_PARAMETER(float, Engine_Owner_LODDistance)
    SHADER_PARAMETER(float, Engine_Owner_LODDistanceFraction)
    SHADER_PARAMETER(float, Engine_System_Age)
    SHADER_PARAMETER(uint32, Engine_Owner_ExecutionState)
    SHADER_PARAMETER(int32, Engine_System_TickCount)
    SHADER_PARAMETER(int32, Engine_System_NumEmitters)
    SHADER_PARAMETER(int32, Engine_System_NumEmittersAlive)
    SHADER_PARAMETER(int32, Engine_System_SignificanceIndex)
    SHADER_PARAMETER(int32, Engine_System_RandomSeed)
    SHADER_PARAMETER(int32, Engine_System_CurrentTimeStep)
    SHADER_PARAMETER(int32, Engine_System_NumTimeSteps)
    SHADER_PARAMETER(float, Engine_System_TimeStepFraction)
    SHADER_PARAMETER(int32, Engine_System_NumParticles)
    //SHADER_PARAMETER(int32, System_Pad0)
    //SHADER_PARAMETER(int32, System_Pad1)

    SHADER_PARAMETER(float, PREV_Engine_Owner_TimeSinceRendered)
    SHADER_PARAMETER(float, PREV_Engine_Owner_LODDistance)
    SHADER_PARAMETER(float, PREV_Engine_Owner_LODDistanceFraction)
    SHADER_PARAMETER(float, PREV_Engine_System_Age)
    SHADER_PARAMETER(uint32, PREV_Engine_Owner_ExecutionState)
    SHADER_PARAMETER(int32, PREV_Engine_System_TickCount)
    SHADER_PARAMETER(int32, PREV_Engine_System_NumEmitters)
    SHADER_PARAMETER(int32, PREV_Engine_System_NumEmittersAlive)
    SHADER_PARAMETER(int32, PREV_Engine_System_SignificanceIndex)
    SHADER_PARAMETER(int32, PREV_Engine_System_RandomSeed)
    SHADER_PARAMETER(int32, PREV_CurrentTimeStep)
    SHADER_PARAMETER(int32, PREV_NumTimeSteps)
    SHADER_PARAMETER(float, PREV_TimeStepFraction)
    SHADER_PARAMETER(int32, PREV_Engine_System_NumParticles)
    SHADER_PARAMETER(int32, PREV_System_Pad0)
    SHADER_PARAMETER(int32, PREV_System_Pad1)
}

namespace NParticleProperty::OwnerParameters {
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemLocalToWorld)
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemWorldToLocal)
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemLocalToWorldTransposed)
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemWorldToLocalTransposed)
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemLocalToWorldNoScale)
    SHADER_PARAMETER(FMatrix44f, Engine_Owner_SystemWorldToLocalNoScale)
    SHADER_PARAMETER(FQuat4f, Engine_Owner_Rotation)
    SHADER_PARAMETER(FVector3f, Engine_Owner_Position)
    SHADER_PARAMETER(float, Engine_Owner_Pad0)
    SHADER_PARAMETER(FVector3f, Engine_Owner_Velocity)
    SHADER_PARAMETER(float, Engine_Owner_Pad1)
    SHADER_PARAMETER(FVector3f, Engine_Owner_SystemXAxis)
    SHADER_PARAMETER(float, Engine_Owner_Pad2)
    SHADER_PARAMETER(FVector3f, Engine_Owner_SystemYAxis)
    SHADER_PARAMETER(float, Engine_Owner_Pad3)
    SHADER_PARAMETER(FVector3f, Engine_Owner_SystemZAxis)
    SHADER_PARAMETER(float, Engine_Owner_Pad4)
    SHADER_PARAMETER(FVector3f, Engine_Owner_Scale)
    SHADER_PARAMETER(float, Engine_Owner_Pad5)
    SHADER_PARAMETER(FVector4f, Engine_Owner_LWCTile)

    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemLocalToWorld)
    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemWorldToLocal)
    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemLocalToWorldTransposed)
    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemWorldToLocalTransposed)
    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemLocalToWorldNoScale)
    SHADER_PARAMETER(FMatrix44f, PREV_Engine_Owner_SystemWorldToLocalNoScale)
    SHADER_PARAMETER(FQuat4f, PREV_Engine_Owner_Rotation)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_Position)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad0)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_Velocity)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad1)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_SystemXAxis)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad2)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_SystemYAxis)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad3)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_SystemZAxis)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad4)
    SHADER_PARAMETER(FVector3f, PREV_Engine_Owner_Scale)
    SHADER_PARAMETER(float, PREV_Engine_Owner_Pad5)
    SHADER_PARAMETER(FVector4f, PREV_Engine_Owner_LWCTile)
}

namespace NParticleProperty::EmitterParameters {

    SHADER_PARAMETER(int32, Engine_Emitter_NumParticles)
    SHADER_PARAMETER(int32, Engine_Emitter_TotalSpawnedParticles)
    SHADER_PARAMETER(float, Engine_Emitter_SpawnCountScale)
    SHADER_PARAMETER(float, Emitter_Age)
    SHADER_PARAMETER(int32, Emitter_RandomSeed)
    SHADER_PARAMETER(int32, Engine_Emitter_InstanceSeed)
    //SHADER_PARAMETER(int32, Emitter_Pad0)
    //SHADER_PARAMETER(int32, Emitter_Pad1)

    SHADER_PARAMETER(int32, PREV_Engine_Emitter_NumParticles)
    SHADER_PARAMETER(int32, PREV_Engine_Emitter_TotalSpawnedParticles)
    SHADER_PARAMETER(float, PREV_Engine_Emitter_SpawnCountScale)
    SHADER_PARAMETER(float, PREV_Emitter_Age)
    SHADER_PARAMETER(int32, PREV_Emitter_RandomSeed)
    SHADER_PARAMETER(int32, PREV_Engine_Emitter_InstanceSeed)
    //SHADER_PARAMETER(int32, PREV_Emitter_Pad0)
    //SHADER_PARAMETER(int32, PREV_Emitter_Pad1)
}

namespace NParticleProperty::ExternalParameters {

    SHADER_PARAMETER(float, Emitter_CurrentLoopDuration)

    SHADER_PARAMETER(float, PREV_Emitter_CurrentLoopDuration)
}   // namespace NParticleProperty::ExternalParameters

namespace NParticleProperty {
    SHADER_PARAMETER(uint32, ComponentBufferSizeRead)
    SHADER_PARAMETER(uint32, ComponentBufferSizeWrite)
    SHADER_PARAMETER(uint32, SimStart)

    SHADER_PARAMETER_SRV(Buffer<float>, InputFloat)
    SHADER_PARAMETER_SRV(Buffer<half>, InputHalf)
    SHADER_PARAMETER_SRV(Buffer<int>, InputInt)
    SHADER_PARAMETER_SRV(Buffer<float>, StaticInputFloat)

    SHADER_PARAMETER_UAV(RWBuffer<float>, RWOutputFloat)
    SHADER_PARAMETER_UAV(RWBuffer<half>, RWOutputHalf)
    SHADER_PARAMETER_UAV(RWBuffer<int>, RWOutputInt)

    SHADER_PARAMETER_UAV(RWBuffer<uint>, RWInstanceCounts)
    SHADER_PARAMETER(uint32, ReadInstanceCountOffset)
    SHADER_PARAMETER(uint32, WriteInstanceCountOffset)

    SHADER_PARAMETER_SRV(Buffer<int>, FreeIDList)
    SHADER_PARAMETER_UAV(RWBuffer<int>, RWIDToIndexTable)

    SHADER_PARAMETER(FUintVector4, SimulationStageIterationInfo)

    SHADER_PARAMETER(FIntVector3, ParticleIterationStateInfo)

    SHADER_PARAMETER(uint32, EmitterTickCounter)

    SHADER_PARAMETER_ARRAY(FIntVector4, EmitterSpawnInfoOffsets, [(NPARTICLE_MAX_GPU_SPAWN_INFOS + 3) / 4])
    SHADER_PARAMETER_ARRAY(FVector4f, EmitterSpawnInfoParams, [NPARTICLE_MAX_GPU_SPAWN_INFOS])

    SHADER_PARAMETER(uint32, NumSpawnedInstances)

    SHADER_PARAMETER(FUintVector3, DispatchThreadIdBounds)
    SHADER_PARAMETER(FUintVector3, DispatchThreadIdToLinear)

    //SHADER_PARAMETER_RDG_BUFFER_SRV(Buffer<uint4>, IndirectDispatchArgs)
    SHADER_PARAMETER(uint32, IndirectDispatchArgsOffset)
    //RDG_BUFFER_ACCESS(IndirectDispatchArgsBuffer, ERHIAccess::IndirectArgs)

    //SHADER_PARAMETER_STRUCT_INCLUDE(FGlobalParameters, GlobalParameters)
    //SHADER_PARAMETER_STRUCT_INCLUDE(FSystemParameters, SystemParameters)
    //SHADER_PARAMETER_STRUCT_INCLUDE(FOwnerParameters, OwnerParameters)
    //SHADER_PARAMETER_STRUCT_INCLUDE(FEmitterParameters, EmitterParameters)
}


}   // namespace cross::fx