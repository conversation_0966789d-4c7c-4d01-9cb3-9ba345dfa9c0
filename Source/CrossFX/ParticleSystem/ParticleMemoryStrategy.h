#pragma once
#include "ParticleSystemCommon.h"

namespace cross::fx {

using ParticleStore = std::vector<UInt8, ce_stl_allocator<UInt8>>;
using ParticleCopyTempStore = std::vector<float, ce_stl_allocator<float>>;

class MemoryExpansionStrategy
{
public:
    MemoryExpansionStrategy(size_t scaler = 2) : mScaler(scaler) {};
    virtual ~MemoryExpansionStrategy() = default;

protected:
    size_t mScaler;
};

class ParticleMemExpandCPU : MemoryExpansionStrategy
{
public:
    ParticleMemExpandCPU()
    {
        mScaler = 4;
    }

    template<typename StoreType>
    void Reallocate(StoreType& mem, size_t byteNum, size_t eleSize)
    {
        if (mem.capacity() < byteNum / eleSize)
        {
            mem.reserve(byteNum * mScaler);
        }
        mem.resize(byteNum / eleSize);
    }
};

class ParticleMemoryContext
{
public:
    ParticleMemoryContext()
    {
        mExpandStrategy = std::make_unique<ParticleMemExpandCPU>();
    }

    template<typename StoreType, typename EleType>
    void ExpandMemory(StoreType& output, size_t byteNum)
    {
        mExpandStrategy->Reallocate<StoreType>(output, byteNum, sizeof(EleType));
    }

private:
    std::unique_ptr<ParticleMemExpandCPU> mExpandStrategy;
};

}   // namespace cross::fx