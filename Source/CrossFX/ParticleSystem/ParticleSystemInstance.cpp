#include "ParticleSystemInstance.h"
#include "Utilities/TransformConvert.h"

namespace cross::fx {

void ParticleSystemInstance::Complete()
{
    if (mSystemGPUComputeProxy)
    {
        auto proxy = mSystemGPUComputeProxy.release();
        proxy->RemoveFromRenderThread(mParticleSystemGPUDriven, true);
    }
}

void ParticleSystemInstance::Reset()
{
    if (mSystemGPUComputeProxy)
    {
        auto proxy = mSystemGPUComputeProxy.release();
        proxy->RemoveFromRenderThread(mParticleSystemGPUDriven, true);
    }

    mSystemGPUComputeProxy = std::make_unique<NParticleSystemGPUComputeProxy>(this);
    mSystemGPUComputeProxy->AddToRenderThread(mParticleSystemGPUDriven);

    mSwapIndex = 1;
    mIsParametersValid = false;
}

void ParticleSystemInstance::Init()
{
    InitEmitters();
}

void ParticleSystemInstance::ResetResource(ParticleSystemResPtr newResource)
{
    mResource = newResource;
    mAutoPlay = mResource->GetAutoPlay();
    InitEmitters();
}

void ParticleSystemInstance::ResetUniqueID()
{
    mID.CleanUp();
    for (auto emitterIndex = 0u; emitterIndex < mEmitters.size(); ++emitterIndex)
    {
        if (EmitterInstancePtr emitterInstance = mEmitters[emitterIndex])
        {
            mID.Rehash(emitterInstance->GetUniqueID());
        }
    }
}

void ParticleSystemInstance::ResetEmitter(UInt32 emitterIndex, ParticleEmitterResPtr newResource)
{
    Assert(mEmitters.size() < emitterIndex);
    mEmitters[emitterIndex] = std::make_shared<ParticleEmitterInstance>(emitterIndex);
    mEmitters[emitterIndex]->Init(newResource);
}

void ParticleSystemInstance::FillNParticleEmitterParameters(UInt32 emitterIndex)
{
    auto emitter = GetEmitter(emitterIndex);
    const auto& moduleContext = emitter->GetModuleContext();
    auto& parameters = mNParticleEmitterParameters[emitterIndex * 2 + GetParameterIndex()];

    parameters.mEmitterTotalSpawnedParticles = moduleContext.TotalSpawnedCount;
    parameters.mEmitterRandomSeed = moduleContext.RandomSeed;
    parameters.mEmitterAge = moduleContext.Age;

    // external
    parameters.mEmitterCurrentLoopDuration = emitter->GetModulePipeline().GetModuleInfo<EmitterStateInfo>(ModuleScope::EmitterUpdate, ModuleIndex::EmitterState)->LoopDuration;
}

void ParticleSystemInstance::GenerateAndSubmitGPUTick()
{
    if (mHasNGPUEmitters)
    {
        NParticleSystemGPUTick tick;
        Assert(mSystemGPUComputeProxy);
        tick.Init(this);

        DispatchRenderingCommandWithToken([proxy = mSystemGPUComputeProxy.get(), tick]() {
            proxy->QueueTick(tick);
        });
    }
}

void ParticleSystemInstance::InitEmitters()
{
    if (const ParticleSystemResource* system = GetResource())
    {
        const auto& infos = system->GetEmitterInfos();
        const UInt32 emitterNums = static_cast<UInt32>(infos.size());

        mEmitters.clear();
        mEmitters.reserve(emitterNums);
        ClearReadbackResults();
        AssertMsg(emitterNums <= PARTICLE_EMITTER_NUM_LIMIT, "Emitter num exceeds the limit");
        mSimulationState = mAutoPlay ? SimulationState::RUNNING : mSimulationState;
        for (auto emitterIndex = 0u; emitterIndex < emitterNums; ++emitterIndex)
        {
            auto emitterInstance = std::make_shared<ParticleEmitterInstance>(emitterIndex);
            emitterInstance->SetParticleSystemGPUDriven(mParticleSystemGPUDriven);
            emitterInstance->Init(infos[emitterIndex]);
            if (mAutoPlay)
            {
                emitterInstance->SetSimulationState(SimulationState::RUNNING);
                emitterInstance->GetModulePipeline().Flush(infos[emitterIndex]);
            }
            mEmitters.emplace_back(emitterInstance);
            if (emitterInstance->GetSimulationType() == SimulationType::GPU)
            {
                RegisterEmitterReadbackResult(emitterIndex);
            }
            else if (emitterInstance->GetSimulationType() == SimulationType::NGPU)
            {
                mHasNGPUEmitters = true;
                mHasInterpolationParameters = emitterInstance->GetEmitterGPUComputeContext()->mHasInterpolationParameters || mHasInterpolationParameters;
            }
        }

        if (mHasNGPUEmitters)
        {
            /*if (mSystemGPUComputeProxy)
            {
                auto proxy = mSystemGPUComputeProxy.release();
                proxy->RemoveFromRenderThread(mParticleSystemGPUDriven, true);
            }

            mSystemGPUComputeProxy = std::make_unique<NParticleSystemGPUComputeProxy>(this);
            mSystemGPUComputeProxy->AddToRenderThread(mParticleSystemGPUDriven);*/

            mNParticleEmitterParameters.resize(emitterNums * 2);
            mSwapIndex = 1;
            mIsParametersValid = false;

            Reset();
        }
    }
}

bool ParticleSystemInstance::IsRunning() const 
{
    for (auto index = 0; index < mEmitters.size(); ++index)
    {
        if (const EmitterInstancePtr emitter = mEmitters[index])
        {
            SimulationState state = emitter->GetModulePipeline().GetState();
            if (state == SimulationState::RUNNING || state == SimulationState::PAUSED)
            {
                return true;
            }
        }
    }
    return false;
}

ModulePtr ParticleSystemInstance::GetTargetModule(UInt32 emitterIndex, fx::ModuleScope scope, ModuleIndex moduleIndex)
{
    EmitterInstancePtr emitterInstance = GetEmitter(emitterIndex);
    auto& modulePipeline = emitterInstance->GetModulePipeline();
    return modulePipeline.GetModule(scope, moduleIndex);
}

void ParticleSystemInstance::TickNParticleParameters(float deltaTime)
{
    if (mHasNGPUEmitters)
    {
        SwapParameterBuffers();

        auto& globalParameters = mNParticleGlobalParameters[GetParameterIndex()];
        globalParameters.mEngineDeltaTime = deltaTime;
        globalParameters.mEngineInvDeltaTime = 1.0f / deltaTime;
        globalParameters.mWorldDeltaTime = deltaTime;
        globalParameters.mEngineTime = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetTime();
        globalParameters.mEngineRealTime = globalParameters.mEngineTime;

        auto& ownerParameters = mNParticleOwnerParameters[GetParameterIndex()];

        auto matrixUE = TransformConvert::CE2UE_Transform(mOwnerTransform.mMatrix);
        Double3 scale, translation;
        Quaternion64 rotation;
        matrixUE.Decompose(scale, rotation, translation);

        Float3 tilePos;
        GetTileAndOffsetForAbsolutePosition(translation, tilePos, ownerParameters.mEnginePosition);
        ownerParameters.mEngineLocalToWorld = AbsoluteMatrixToRelativeMatrix(tilePos, matrixUE);
        ownerParameters.mEngineWorldToLocal = ownerParameters.mEngineLocalToWorld.Inverted();
        ownerParameters.mEngineLocalToWorldNoScale = ParticleSystemUtils::GetTransformVelocityMatrix(ownerParameters.mEngineLocalToWorld);
        ownerParameters.mEngineWorldToLocalNoScale = ownerParameters.mEngineLocalToWorldNoScale.Inverted();
        ownerParameters.mEngineLocalToWorldTransposed = ownerParameters.mEngineLocalToWorld.Transpose();
        ownerParameters.mEngineWorldToLocalTransposed = ownerParameters.mEngineWorldToLocal.Transpose();
        ownerParameters.mEnginePosition = Float3(translation); // precision loss
        ownerParameters.mEngineXAxis = Float3(rotation.GetRightVector());
        ownerParameters.mEngineYAxis = Float3(rotation.GetUpVector());
        ownerParameters.mEngineZAxis = Float3(rotation.GetForwardVector());
        ownerParameters.mEngineRotation = Quaternion(rotation);
        ownerParameters.mEngineScale = Float3(scale);

        ownerParameters.mEngineLWCTile = {tilePos.x, tilePos.y, tilePos.z, LENGTH_PER_TILE_F};

        const UInt32 emitterNum = GetEmitterNum();
        for (auto emitterIndex = 0u; emitterIndex < emitterNum; ++emitterIndex)
        {
            EmitterInstancePtr emitter = GetEmitter(emitterIndex);
            if (emitter->GetSimulationType() != SimulationType::NGPU)
                continue;

            emitter->GetModuleContext().AliveCount = emitter->GetEmitterGPUComputeContext()->mCurrentNumInstancesR;

            FillNParticleEmitterParameters(emitterIndex);
        }
    }
}

}