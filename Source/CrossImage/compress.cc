#include "image.h"
#include <thread>

namespace imageio {

static bool initialized = false;

uint32_t compute_pitch(nvtt::Format format, int width)
{
    int block_width = 4;
    int block_height = 4;
    int bytes_per_block = (format == nvtt::Format::Format_DXT1 || format == nvtt::Format::Format_BC4) ? 8 : 16;
    switch (format)
    {
    case nvtt::Format::Format_BC6U:
        block_width = block_height = 4;
        bytes_per_block = 16;
    default:
        break;
    }

    return std::max(static_cast<uint32_t>(((width - 1) / block_width + 1) * bytes_per_block), static_cast<uint32_t>(bytes_per_block));
}

// int bytes
uint32_t compute_compressed_size(nvtt::Format format, int width, int height) {
    int block_width = 4;
    int block_height = 4;
    int bytes_per_block = (format == nvtt::Format::Format_DXT1 || format == nvtt::Format::Format_BC4)? 8:16;
    switch (format) {
        case nvtt::Format::Format_BC6U:
            block_width = block_height = 4;
            bytes_per_block = 16;
        default:
            break;
    }
    uint32_t compressed_height = (height - 1) / block_height + 1;
    return compute_pitch(format, width) * compressed_height;
}
#if _WINDOWS

bool compress2dxbcnvtt(const char* pFilename, imageio::GPUImage& image, bool cubemap, bool generate_mips, bool prefilter_mips, nvtt::Format dxformat)
{
    using namespace nvtt;
    cmft::Image base_image;
    if (!cmft::imageLoadStb(base_image, pFilename,cmft::TextureFormat::RGBA8))
    {
        return false;
    }

    return compress2dxbcnvtt(base_image, image, cubemap, generate_mips, prefilter_mips, dxformat);
}


void flip_yz_cubemap(cmft::Image& src)
{
    //Pos_x rotate 270
    //Neg_x rotate 90
    // POS_y  = NEG_Z rotate 180
    // NEG_Y = POS_Z;
    // POS_Z = POS_Y;
    // NEg_Z = NEG_Y ROTATE 180'//  not sure whether need rotation for now;

    //cmft::imageTransformArg(faceList[cmft::CMFT_FACE_POS_X], cmft::ImageTransformArgs::IMAGE_OP_ROT_270);
    cmft::imageTransform(src, cmft::IMAGE_FACE_POSITIVEX | cmft::ImageTransformArgs::IMAGE_OP_ROT_270,
        cmft::IMAGE_FACE_NEGATIVEX | cmft::ImageTransformArgs::IMAGE_OP_ROT_90,
        cmft::IMAGE_FACE_POSITIVEY | 0,
        cmft::IMAGE_FACE_NEGATIVEY | cmft::ImageTransformArgs::IMAGE_OP_ROT_180,
        cmft::IMAGE_FACE_POSITIVEZ | 0,
        cmft::IMAGE_FACE_NEGATIVEZ | cmft::ImageTransformArgs::IMAGE_OP_ROT_180);

    cmft::Image faceList[6];
    cmft::imageFaceListFromCubemap(faceList, src);

    cmft::Image newFaceList[6] = {
        faceList[cmft::CMFT_FACE_POS_X],
        faceList[cmft::CMFT_FACE_NEG_X],
        faceList[cmft::CMFT_FACE_NEG_Z],
        faceList[cmft::CMFT_FACE_POS_Z],
        faceList[cmft::CMFT_FACE_POS_Y],
        faceList[cmft::CMFT_FACE_NEG_Y]
    };
    cmft::imageCubemapFromFaceList(src, newFaceList);

}

bool compress2dxbcnvtt(cmft::Image& src, GPUImage& dst, bool cubemap, bool generate_mips, bool prefilter_mips, nvtt::Format dxformat, bool YZFlip, int glossBias)
{
    using namespace nvtt;
    bool useGPUAccel = nvtt::isCudaSupported();
    bool bHdrFormat = dxformat == nvtt::Format::Format_BC6U;
    if (bHdrFormat) cmft::imageToRgba32f(src);
    if (cubemap) cmft::imageToCubemap(src);
    if (cubemap && YZFlip) flip_yz_cubemap(src);
    if (!bHdrFormat) cmft::imageConvert(src, cmft::TextureFormat::RGBA8);
    if (generate_mips) cmft::imageGenerateMipMapChain(src);
    if (prefilter_mips)
    {
        cmft::imageRadianceFilter(src,
                                  0,
                                  cmft::LightingModel::Enum::Phong,
                                  true,
                                  UINT8_MAX,
                                  static_cast<uint8_t>(std::max(src.m_numMips + glossBias, 0)),
                                  static_cast<uint8_t>(glossBias),
                                  cmft::EdgeFixup::Warp,
                                  static_cast<uint8_t>(std::thread::hardware_concurrency()));
        //cmft::imageSave(src, (std::string("C:/ceproj/AssetExamples/Saved/mip_face_xxx")).c_str(), cmft::ImageFileType::DDS, cmft::OutputType::Cubemap, src.m_format);
    }

    dst.m_mip_count = src.m_numMips;
    dst.m_width = src.m_width;
    dst.m_height = src.m_height;
    dst.m_cubemap = cubemap;
    uint32_t offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
    cmft::imageGetMipOffsets(offsets, src);
    auto& mipmaps = dst.m_mipmaps;
    uint32_t offset = 0;

    int max_face = cubemap ? 6 : 1;
    for (int face = 0; face < max_face; ++face)
    {
        for (int mip = 0; mip < dst.m_mip_count; mip++)
        {
            // Get src data ptr for current mip and face.
            const uint8_t* mip_data = (const uint8_t*)src.m_data + offsets[face][mip];

            const uint32_t mip_width = std::max(UINT32_C(1), src.m_width >> mip);
            const uint32_t mip_height = std::max(UINT32_C(1), src.m_height >> mip);

            RefImage nv_image;
            nv_image.width = mip_width;
            nv_image.height = mip_height;
            nv_image.num_channels = 4;
            nv_image.depth = 1;
            nv_image.data = mip_data;
            void* output_buffer = nullptr;
            uint32_t output_size = 0;
            if (!bHdrFormat)
            {
                nvtt::CPUInputBuffer cpu_buffer(&nv_image, nvtt::ValueType::UINT8);
                output_size = compute_compressed_size(dxformat, mip_width, mip_height);
                output_buffer = malloc(output_size);
                if (dxformat == nvtt::Format::Format_BC1)
                {
                    nvtt::nvtt_encode_bc1(cpu_buffer, false, output_buffer, useGPUAccel, false);
                }
                else if (dxformat == nvtt::Format::Format_BC3)
                {
                    nvtt::nvtt_encode_bc3(cpu_buffer, false, output_buffer, useGPUAccel, false);
                }
                else if (dxformat == nvtt::Format::Format_BC4)
                {
                    nvtt::nvtt_encode_bc4(cpu_buffer, false, output_buffer, useGPUAccel, false);
                }
                else if (dxformat == nvtt::Format::Format_BC5)
                {
                    nvtt::nvtt_encode_bc5(cpu_buffer, false, output_buffer, useGPUAccel, false);
                }
                else if (dxformat == nvtt::Format::Format_BC7)
                {
                    nvtt::nvtt_encode_bc7(cpu_buffer, false, true, output_buffer, useGPUAccel, false);
                }
                else
                {
                    return false;
                }
            }
            else
            {
                nvtt::CPUInputBuffer cpu_buffer(&nv_image, FLOAT32);
                output_size = compute_compressed_size(Format::Format_BC6U, mip_width, mip_height);
                output_buffer = malloc(output_size);
                nvtt::nvtt_encode_bc6h(cpu_buffer, false, false, output_buffer, useGPUAccel, false);
            }
            GPUImageData& image_data = mipmaps.emplace_back();
            image_data.m_width = mip_width;
            image_data.m_height = mip_height;
            image_data.m_format = dxformat;
            image_data.m_data = output_buffer;
            image_data.m_data_size = output_size;
            image_data.m_pitch = compute_pitch(image_data.m_format, mip_width);

            dst.m_total_size += output_size;
            dst.m_mip_offsets[face][mip] = offset;
            offset += output_size;
        }
    }
    return true;
}

bool compress_hdr(const char* pFilename, imageio::GPUImage& image, double shCoeffs[SH_COEFF_NUM][3])
{
    cmft::Image base_image;
    if (!cmft::imageLoad(base_image, pFilename))
    {
        return false;
    }
    
    return compress_hdr(base_image, image, shCoeffs);
}

bool compress_hdr(cmft::Image& src, GPUImage& dst, double shCoeffs[SH_COEFF_NUM][3])
{
    using namespace nvtt;
    bool useGPUAccel = nvtt::isCudaSupported();

    cmft::imageToRgba32f(src);
    cmft::imageToCubemap(src);
    cmft::imageGenerateMipMapChain(src);

    uint32_t width = src.m_width;
    uint32_t height = src.m_height;

    int mip_count = src.m_numMips;
    uint32_t offsets[CUBE_FACE_NUM][MAX_MIP_NUM];
    cmft::imageGetMipOffsets(offsets, src);

    dst.m_mip_count = mip_count;
    dst.m_width = width;
    dst.m_height = height;
    dst.m_cubemap = true;
    auto& mipmaps = dst.m_mipmaps;

    if (!cmft::imageShCoeffs(shCoeffs, src))
    {
        return false;
    }
    uint32_t offset = 0;
    for (int face = 0; face < 6; ++face)   // f
    {
        for (int mip = 0; mip < mip_count; mip++)
        {
            // Get src data ptr for current mip and face.
            const uint8_t* mip_data = (const uint8_t*)src.m_data + offsets[face][mip];

            const uint32_t mip_width = std::max(UINT32_C(1), width >> mip);
            const uint32_t mip_height = std::max(UINT32_C(1), height >> mip);

            RefImage nv_image;
            nv_image.width = mip_width;
            nv_image.height = mip_height;
            nv_image.num_channels = 4;
            nv_image.depth = 1;
            nv_image.data = mip_data;

            nvtt::CPUInputBuffer cpu_buffer(&nv_image, FLOAT32);

            uint32_t output_size = compute_compressed_size(Format::Format_BC6U, mip_width, mip_height);
            void* output_buffer = malloc(output_size);

            nvtt::nvtt_encode_bc6h(cpu_buffer, false, false, output_buffer, useGPUAccel, false);

            GPUImageData& image_data = mipmaps.emplace_back();
            image_data.m_width = mip_width;
            image_data.m_height = mip_height;
            image_data.m_format = Format::Format_BC6U;
            image_data.m_data = output_buffer;
            image_data.m_data_size = output_size;
            image_data.m_pitch = compute_pitch(image_data.m_format, mip_width);

            dst.m_total_size += output_size;
            dst.m_mip_offsets[face][mip] = offset;
            offset += output_size;
        }
    }

    return true;
}

#endif

}