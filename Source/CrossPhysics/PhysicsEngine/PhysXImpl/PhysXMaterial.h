#pragma once
#include "PhysicsEngine/CrossPhysics.h"
#include "PxPhysicsAPI.h"

#include "PhysicsEngine/PhysicsMaterial.h"

namespace cross
{
    class PhysXMaterial : public PhysicsMaterial
    {
		PhysXMaterial(physx::PxMaterial* material) :mPxMaterial(material) {}
		~PhysXMaterial() override;
    public:
		static PhysXMaterial* Create(float staticFriction, float dynamicFriction, float restitution);
		static void Destroy(PhysXMaterial* mat);

		physx::PxMaterial* GetUnderlay() const { return mPxMaterial; }
    protected:
		physx::PxMaterial* mPxMaterial = nullptr;
    };
}
