include "BasicStruct.fbs";
namespace CrossSchema;

table float3attribute
{
    name:string;
    data:[float3];
}
table float2attribute
{
    name:string;
    data:[float2];
}
table float4attribute
{
    name:string;
    data:[float4];
}

table uint4attribute
{
    name:string;
    data:[uint4];
}

table uintattribute
{
    name:string;
    data:[uint];
}

table IndiceData
{
    indicescount:uint;
    indexbuffer:[ubyte];
}
table CollisionNode
{
    index:int;
    leftindex:int;
    rightindex:int;
    minpos:[float];
    maxpos:[float];
    trianglelist:[int];
}
//add new fields to the end of the table
table MeshVertexDescription
{
    pos:float3attribute;
    normal:float3attribute;
    uv0:float2attribute;
    uv1:float2attribute;
    uv2:float2attribute;
    uv3:float2attribute;
    color:uintattribute;
    tangent:float4attribute;
    binormal:float4attribute;
    boneweights:float4attribute;
    boneids:uint4attribute;
}
table ImportNode
{
    localtransform:[float];
    meshindex:int;
    name:string;
    children:[ImportNode];
    isrootinimportmesh:bool;
    boneindexinimportmesh:int;
}
table ImportMeshData
{
    name:string;
    indices:IndiceData;
    vertexdata:MeshVertexDescription;
    collisiontree:[CollisionNode];
}
table ImportVertexBone
{
    weights:[float];
    boneindices:[int];
}

table ImportMeshSkin
{
    vbones:[ImportVertexBone];
}

table UsedBonesId
{
    bones:[int];
}
table invmatrix
{
    val:[float];
}
table ImportMeshes
{
    rootnode:ImportNode;
    name:string;
    pathtoskeleton:string;
    meshesdata:[ImportMeshData];
    meshskininfo:[ImportMeshSkin];
    meshusedbones:[UsedBonesId];
    skeleton:[ImportBoneNode];
    bindposeinvmatrixs:[invmatrix];
}

root_type ImportMeshes;