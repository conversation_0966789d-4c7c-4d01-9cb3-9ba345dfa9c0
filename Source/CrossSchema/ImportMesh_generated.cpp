#include "ImportMesh_generated.h"
namespace CrossSchema {
 const flatbuffers::TypeTable * float3attribute::MiniReflectTypeTable(){
    return float3attributeTypeTable();
  }
const flatbuffers::String * float3attribute::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * float3attribute::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<const CrossSchema::float3 *> * float3attribute::data() const{
    return GetPointer<const flatbuffers::Vector<const CrossSchema::float3 *> *>(VT_DATA);
  }
  flatbuffers::Vector<const CrossSchema::float3 *> * float3attribute::mutable_data() {
    return GetPointer<flatbuffers::Vector<const CrossSchema::float3 *> *>(VT_DATA);
  }
 bool float3attribute::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  void float3attributeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(float3attribute::VT_NAME, name);
  }
  void float3attributeBuilder::add_data(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> data) {
    fbb_.AddOffset(float3attribute::VT_DATA, data);
  }
flatbuffers::Offset<float3attribute> Createfloat3attribute(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float3 *>> data){
    float3attributeBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<float3attribute> Createfloat3attributeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<CrossSchema::float3> *data) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto data__ = data ? _fbb.CreateVectorOfStructs<CrossSchema::float3>(*data) : 0;
  return CrossSchema::Createfloat3attribute(
      _fbb,
      name__,
      data__);
}

 const flatbuffers::TypeTable * float2attribute::MiniReflectTypeTable(){
    return float2attributeTypeTable();
  }
const flatbuffers::String * float2attribute::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * float2attribute::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<const CrossSchema::float2 *> * float2attribute::data() const{
    return GetPointer<const flatbuffers::Vector<const CrossSchema::float2 *> *>(VT_DATA);
  }
  flatbuffers::Vector<const CrossSchema::float2 *> * float2attribute::mutable_data() {
    return GetPointer<flatbuffers::Vector<const CrossSchema::float2 *> *>(VT_DATA);
  }
 bool float2attribute::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  void float2attributeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(float2attribute::VT_NAME, name);
  }
  void float2attributeBuilder::add_data(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float2 *>> data) {
    fbb_.AddOffset(float2attribute::VT_DATA, data);
  }
flatbuffers::Offset<float2attribute> Createfloat2attribute(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float2 *>> data){
    float2attributeBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<float2attribute> Createfloat2attributeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<CrossSchema::float2> *data) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto data__ = data ? _fbb.CreateVectorOfStructs<CrossSchema::float2>(*data) : 0;
  return CrossSchema::Createfloat2attribute(
      _fbb,
      name__,
      data__);
}

 const flatbuffers::TypeTable * float4attribute::MiniReflectTypeTable(){
    return float4attributeTypeTable();
  }
const flatbuffers::String * float4attribute::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * float4attribute::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<const CrossSchema::float4 *> * float4attribute::data() const{
    return GetPointer<const flatbuffers::Vector<const CrossSchema::float4 *> *>(VT_DATA);
  }
  flatbuffers::Vector<const CrossSchema::float4 *> * float4attribute::mutable_data() {
    return GetPointer<flatbuffers::Vector<const CrossSchema::float4 *> *>(VT_DATA);
  }
 bool float4attribute::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  void float4attributeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(float4attribute::VT_NAME, name);
  }
  void float4attributeBuilder::add_data(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float4 *>> data) {
    fbb_.AddOffset(float4attribute::VT_DATA, data);
  }
flatbuffers::Offset<float4attribute> Createfloat4attribute(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::float4 *>> data){
    float4attributeBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<float4attribute> Createfloat4attributeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<CrossSchema::float4> *data) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto data__ = data ? _fbb.CreateVectorOfStructs<CrossSchema::float4>(*data) : 0;
  return CrossSchema::Createfloat4attribute(
      _fbb,
      name__,
      data__);
}

 const flatbuffers::TypeTable * uint4attribute::MiniReflectTypeTable(){
    return uint4attributeTypeTable();
  }
const flatbuffers::String * uint4attribute::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * uint4attribute::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<const CrossSchema::uint4 *> * uint4attribute::data() const{
    return GetPointer<const flatbuffers::Vector<const CrossSchema::uint4 *> *>(VT_DATA);
  }
  flatbuffers::Vector<const CrossSchema::uint4 *> * uint4attribute::mutable_data() {
    return GetPointer<flatbuffers::Vector<const CrossSchema::uint4 *> *>(VT_DATA);
  }
 bool uint4attribute::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  void uint4attributeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(uint4attribute::VT_NAME, name);
  }
  void uint4attributeBuilder::add_data(flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::uint4 *>> data) {
    fbb_.AddOffset(uint4attribute::VT_DATA, data);
  }
flatbuffers::Offset<uint4attribute> Createuint4attribute(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<const CrossSchema::uint4 *>> data){
    uint4attributeBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<uint4attribute> Createuint4attributeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<CrossSchema::uint4> *data) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto data__ = data ? _fbb.CreateVectorOfStructs<CrossSchema::uint4>(*data) : 0;
  return CrossSchema::Createuint4attribute(
      _fbb,
      name__,
      data__);
}

 const flatbuffers::TypeTable * uintattribute::MiniReflectTypeTable(){
    return uintattributeTypeTable();
  }
const flatbuffers::String * uintattribute::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * uintattribute::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<uint32_t> * uintattribute::data() const{
    return GetPointer<const flatbuffers::Vector<uint32_t> *>(VT_DATA);
  }
  flatbuffers::Vector<uint32_t> * uintattribute::mutable_data() {
    return GetPointer<flatbuffers::Vector<uint32_t> *>(VT_DATA);
  }
 bool uintattribute::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
  void uintattributeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(uintattribute::VT_NAME, name);
  }
  void uintattributeBuilder::add_data(flatbuffers::Offset<flatbuffers::Vector<uint32_t>> data) {
    fbb_.AddOffset(uintattribute::VT_DATA, data);
  }
flatbuffers::Offset<uintattribute> Createuintattribute(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<uint32_t>> data){
    uintattributeBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<uintattribute> CreateuintattributeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const std::vector<uint32_t> *data) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto data__ = data ? _fbb.CreateVector<uint32_t>(*data) : 0;
  return CrossSchema::Createuintattribute(
      _fbb,
      name__,
      data__);
}

 const flatbuffers::TypeTable * IndiceData::MiniReflectTypeTable(){
    return IndiceDataTypeTable();
  }
uint32_t  IndiceData::indicescount() const{
    return GetField<uint32_t>(VT_INDICESCOUNT, 0);
  }
  bool IndiceData::mutate_indicescount (uint32_t _indicescount) {
    return SetField<uint32_t>(VT_INDICESCOUNT, _indicescount, 0);
  }
const flatbuffers::Vector<uint8_t> * IndiceData::indexbuffer() const{
    return GetPointer<const flatbuffers::Vector<uint8_t> *>(VT_INDEXBUFFER);
  }
  flatbuffers::Vector<uint8_t> * IndiceData::mutable_indexbuffer() {
    return GetPointer<flatbuffers::Vector<uint8_t> *>(VT_INDEXBUFFER);
  }
 bool IndiceData::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_INDICESCOUNT) &&
           VerifyOffset(verifier, VT_INDEXBUFFER) &&
           verifier.VerifyVector(indexbuffer()) &&
           verifier.EndTable();
  }
  void IndiceDataBuilder::add_indicescount(uint32_t indicescount) {
    fbb_.AddElement<uint32_t>(IndiceData::VT_INDICESCOUNT, indicescount, 0);
  }
  void IndiceDataBuilder::add_indexbuffer(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> indexbuffer) {
    fbb_.AddOffset(IndiceData::VT_INDEXBUFFER, indexbuffer);
  }
flatbuffers::Offset<IndiceData> CreateIndiceData(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t indicescount,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> indexbuffer){
    IndiceDataBuilder builder_(_fbb);
  builder_.add_indexbuffer(indexbuffer);
  builder_.add_indicescount(indicescount);
  return builder_.Finish();
}

flatbuffers::Offset<IndiceData> CreateIndiceDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t indicescount,
    const std::vector<uint8_t> *indexbuffer) {
  auto indexbuffer__ = indexbuffer ? _fbb.CreateVector<uint8_t>(*indexbuffer) : 0;
  return CrossSchema::CreateIndiceData(
      _fbb,
      indicescount,
      indexbuffer__);
}

 const flatbuffers::TypeTable * CollisionNode::MiniReflectTypeTable(){
    return CollisionNodeTypeTable();
  }
int32_t  CollisionNode::index() const{
    return GetField<int32_t>(VT_INDEX, 0);
  }
  bool CollisionNode::mutate_index (int32_t _index) {
    return SetField<int32_t>(VT_INDEX, _index, 0);
  }
int32_t  CollisionNode::leftindex() const{
    return GetField<int32_t>(VT_LEFTINDEX, 0);
  }
  bool CollisionNode::mutate_leftindex (int32_t _leftindex) {
    return SetField<int32_t>(VT_LEFTINDEX, _leftindex, 0);
  }
int32_t  CollisionNode::rightindex() const{
    return GetField<int32_t>(VT_RIGHTINDEX, 0);
  }
  bool CollisionNode::mutate_rightindex (int32_t _rightindex) {
    return SetField<int32_t>(VT_RIGHTINDEX, _rightindex, 0);
  }
const flatbuffers::Vector<float> * CollisionNode::minpos() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_MINPOS);
  }
  flatbuffers::Vector<float> * CollisionNode::mutable_minpos() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_MINPOS);
  }
const flatbuffers::Vector<float> * CollisionNode::maxpos() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_MAXPOS);
  }
  flatbuffers::Vector<float> * CollisionNode::mutable_maxpos() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_MAXPOS);
  }
const flatbuffers::Vector<int32_t> * CollisionNode::trianglelist() const{
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_TRIANGLELIST);
  }
  flatbuffers::Vector<int32_t> * CollisionNode::mutable_trianglelist() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_TRIANGLELIST);
  }
 bool CollisionNode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_INDEX) &&
           VerifyField<int32_t>(verifier, VT_LEFTINDEX) &&
           VerifyField<int32_t>(verifier, VT_RIGHTINDEX) &&
           VerifyOffset(verifier, VT_MINPOS) &&
           verifier.VerifyVector(minpos()) &&
           VerifyOffset(verifier, VT_MAXPOS) &&
           verifier.VerifyVector(maxpos()) &&
           VerifyOffset(verifier, VT_TRIANGLELIST) &&
           verifier.VerifyVector(trianglelist()) &&
           verifier.EndTable();
  }
  void CollisionNodeBuilder::add_index(int32_t index) {
    fbb_.AddElement<int32_t>(CollisionNode::VT_INDEX, index, 0);
  }
  void CollisionNodeBuilder::add_leftindex(int32_t leftindex) {
    fbb_.AddElement<int32_t>(CollisionNode::VT_LEFTINDEX, leftindex, 0);
  }
  void CollisionNodeBuilder::add_rightindex(int32_t rightindex) {
    fbb_.AddElement<int32_t>(CollisionNode::VT_RIGHTINDEX, rightindex, 0);
  }
  void CollisionNodeBuilder::add_minpos(flatbuffers::Offset<flatbuffers::Vector<float>> minpos) {
    fbb_.AddOffset(CollisionNode::VT_MINPOS, minpos);
  }
  void CollisionNodeBuilder::add_maxpos(flatbuffers::Offset<flatbuffers::Vector<float>> maxpos) {
    fbb_.AddOffset(CollisionNode::VT_MAXPOS, maxpos);
  }
  void CollisionNodeBuilder::add_trianglelist(flatbuffers::Offset<flatbuffers::Vector<int32_t>> trianglelist) {
    fbb_.AddOffset(CollisionNode::VT_TRIANGLELIST, trianglelist);
  }
flatbuffers::Offset<CollisionNode> CreateCollisionNode(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t index,
    int32_t leftindex,
    int32_t rightindex,
    flatbuffers::Offset<flatbuffers::Vector<float>> minpos,
    flatbuffers::Offset<flatbuffers::Vector<float>> maxpos,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> trianglelist){
    CollisionNodeBuilder builder_(_fbb);
  builder_.add_trianglelist(trianglelist);
  builder_.add_maxpos(maxpos);
  builder_.add_minpos(minpos);
  builder_.add_rightindex(rightindex);
  builder_.add_leftindex(leftindex);
  builder_.add_index(index);
  return builder_.Finish();
}

flatbuffers::Offset<CollisionNode> CreateCollisionNodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int32_t index,
    int32_t leftindex,
    int32_t rightindex,
    const std::vector<float> *minpos,
    const std::vector<float> *maxpos,
    const std::vector<int32_t> *trianglelist) {
  auto minpos__ = minpos ? _fbb.CreateVector<float>(*minpos) : 0;
  auto maxpos__ = maxpos ? _fbb.CreateVector<float>(*maxpos) : 0;
  auto trianglelist__ = trianglelist ? _fbb.CreateVector<int32_t>(*trianglelist) : 0;
  return CrossSchema::CreateCollisionNode(
      _fbb,
      index,
      leftindex,
      rightindex,
      minpos__,
      maxpos__,
      trianglelist__);
}

 const flatbuffers::TypeTable * MeshVertexDescription::MiniReflectTypeTable(){
    return MeshVertexDescriptionTypeTable();
  }
const CrossSchema::float3attribute * MeshVertexDescription::pos() const{
    return GetPointer<const CrossSchema::float3attribute *>(VT_POS);
  }
  CrossSchema::float3attribute * MeshVertexDescription::mutable_pos() {
    return GetPointer<CrossSchema::float3attribute *>(VT_POS);
  }
const CrossSchema::float3attribute * MeshVertexDescription::normal() const{
    return GetPointer<const CrossSchema::float3attribute *>(VT_NORMAL);
  }
  CrossSchema::float3attribute * MeshVertexDescription::mutable_normal() {
    return GetPointer<CrossSchema::float3attribute *>(VT_NORMAL);
  }
const CrossSchema::float2attribute * MeshVertexDescription::uv0() const{
    return GetPointer<const CrossSchema::float2attribute *>(VT_UV0);
  }
  CrossSchema::float2attribute * MeshVertexDescription::mutable_uv0() {
    return GetPointer<CrossSchema::float2attribute *>(VT_UV0);
  }
const CrossSchema::float2attribute * MeshVertexDescription::uv1() const{
    return GetPointer<const CrossSchema::float2attribute *>(VT_UV1);
  }
  CrossSchema::float2attribute * MeshVertexDescription::mutable_uv1() {
    return GetPointer<CrossSchema::float2attribute *>(VT_UV1);
  }
const CrossSchema::float2attribute * MeshVertexDescription::uv2() const{
    return GetPointer<const CrossSchema::float2attribute *>(VT_UV2);
  }
  CrossSchema::float2attribute * MeshVertexDescription::mutable_uv2() {
    return GetPointer<CrossSchema::float2attribute *>(VT_UV2);
  }
const CrossSchema::float2attribute * MeshVertexDescription::uv3() const{
    return GetPointer<const CrossSchema::float2attribute *>(VT_UV3);
  }
  CrossSchema::float2attribute * MeshVertexDescription::mutable_uv3() {
    return GetPointer<CrossSchema::float2attribute *>(VT_UV3);
  }
const CrossSchema::uintattribute * MeshVertexDescription::color() const{
    return GetPointer<const CrossSchema::uintattribute *>(VT_COLOR);
  }
  CrossSchema::uintattribute * MeshVertexDescription::mutable_color() {
    return GetPointer<CrossSchema::uintattribute *>(VT_COLOR);
  }
const CrossSchema::float4attribute * MeshVertexDescription::tangent() const{
    return GetPointer<const CrossSchema::float4attribute *>(VT_TANGENT);
  }
  CrossSchema::float4attribute * MeshVertexDescription::mutable_tangent() {
    return GetPointer<CrossSchema::float4attribute *>(VT_TANGENT);
  }
const CrossSchema::float4attribute * MeshVertexDescription::binormal() const{
    return GetPointer<const CrossSchema::float4attribute *>(VT_BINORMAL);
  }
  CrossSchema::float4attribute * MeshVertexDescription::mutable_binormal() {
    return GetPointer<CrossSchema::float4attribute *>(VT_BINORMAL);
  }
const CrossSchema::float4attribute * MeshVertexDescription::boneweights() const{
    return GetPointer<const CrossSchema::float4attribute *>(VT_BONEWEIGHTS);
  }
  CrossSchema::float4attribute * MeshVertexDescription::mutable_boneweights() {
    return GetPointer<CrossSchema::float4attribute *>(VT_BONEWEIGHTS);
  }
const CrossSchema::uint4attribute * MeshVertexDescription::boneids() const{
    return GetPointer<const CrossSchema::uint4attribute *>(VT_BONEIDS);
  }
  CrossSchema::uint4attribute * MeshVertexDescription::mutable_boneids() {
    return GetPointer<CrossSchema::uint4attribute *>(VT_BONEIDS);
  }
 bool MeshVertexDescription::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_NORMAL) &&
           verifier.VerifyTable(normal()) &&
           VerifyOffset(verifier, VT_UV0) &&
           verifier.VerifyTable(uv0()) &&
           VerifyOffset(verifier, VT_UV1) &&
           verifier.VerifyTable(uv1()) &&
           VerifyOffset(verifier, VT_UV2) &&
           verifier.VerifyTable(uv2()) &&
           VerifyOffset(verifier, VT_UV3) &&
           verifier.VerifyTable(uv3()) &&
           VerifyOffset(verifier, VT_COLOR) &&
           verifier.VerifyTable(color()) &&
           VerifyOffset(verifier, VT_TANGENT) &&
           verifier.VerifyTable(tangent()) &&
           VerifyOffset(verifier, VT_BINORMAL) &&
           verifier.VerifyTable(binormal()) &&
           VerifyOffset(verifier, VT_BONEWEIGHTS) &&
           verifier.VerifyTable(boneweights()) &&
           VerifyOffset(verifier, VT_BONEIDS) &&
           verifier.VerifyTable(boneids()) &&
           verifier.EndTable();
  }
  void MeshVertexDescriptionBuilder::add_pos(flatbuffers::Offset<CrossSchema::float3attribute> pos) {
    fbb_.AddOffset(MeshVertexDescription::VT_POS, pos);
  }
  void MeshVertexDescriptionBuilder::add_normal(flatbuffers::Offset<CrossSchema::float3attribute> normal) {
    fbb_.AddOffset(MeshVertexDescription::VT_NORMAL, normal);
  }
  void MeshVertexDescriptionBuilder::add_uv0(flatbuffers::Offset<CrossSchema::float2attribute> uv0) {
    fbb_.AddOffset(MeshVertexDescription::VT_UV0, uv0);
  }
  void MeshVertexDescriptionBuilder::add_uv1(flatbuffers::Offset<CrossSchema::float2attribute> uv1) {
    fbb_.AddOffset(MeshVertexDescription::VT_UV1, uv1);
  }
  void MeshVertexDescriptionBuilder::add_uv2(flatbuffers::Offset<CrossSchema::float2attribute> uv2) {
    fbb_.AddOffset(MeshVertexDescription::VT_UV2, uv2);
  }
  void MeshVertexDescriptionBuilder::add_uv3(flatbuffers::Offset<CrossSchema::float2attribute> uv3) {
    fbb_.AddOffset(MeshVertexDescription::VT_UV3, uv3);
  }
  void MeshVertexDescriptionBuilder::add_color(flatbuffers::Offset<CrossSchema::uintattribute> color) {
    fbb_.AddOffset(MeshVertexDescription::VT_COLOR, color);
  }
  void MeshVertexDescriptionBuilder::add_tangent(flatbuffers::Offset<CrossSchema::float4attribute> tangent) {
    fbb_.AddOffset(MeshVertexDescription::VT_TANGENT, tangent);
  }
  void MeshVertexDescriptionBuilder::add_binormal(flatbuffers::Offset<CrossSchema::float4attribute> binormal) {
    fbb_.AddOffset(MeshVertexDescription::VT_BINORMAL, binormal);
  }
  void MeshVertexDescriptionBuilder::add_boneweights(flatbuffers::Offset<CrossSchema::float4attribute> boneweights) {
    fbb_.AddOffset(MeshVertexDescription::VT_BONEWEIGHTS, boneweights);
  }
  void MeshVertexDescriptionBuilder::add_boneids(flatbuffers::Offset<CrossSchema::uint4attribute> boneids) {
    fbb_.AddOffset(MeshVertexDescription::VT_BONEIDS, boneids);
  }
flatbuffers::Offset<MeshVertexDescription> CreateMeshVertexDescription(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<CrossSchema::float3attribute> pos,
    flatbuffers::Offset<CrossSchema::float3attribute> normal,
    flatbuffers::Offset<CrossSchema::float2attribute> uv0,
    flatbuffers::Offset<CrossSchema::float2attribute> uv1,
    flatbuffers::Offset<CrossSchema::float2attribute> uv2,
    flatbuffers::Offset<CrossSchema::float2attribute> uv3,
    flatbuffers::Offset<CrossSchema::uintattribute> color,
    flatbuffers::Offset<CrossSchema::float4attribute> tangent,
    flatbuffers::Offset<CrossSchema::float4attribute> binormal,
    flatbuffers::Offset<CrossSchema::float4attribute> boneweights,
    flatbuffers::Offset<CrossSchema::uint4attribute> boneids){
    MeshVertexDescriptionBuilder builder_(_fbb);
  builder_.add_boneids(boneids);
  builder_.add_boneweights(boneweights);
  builder_.add_binormal(binormal);
  builder_.add_tangent(tangent);
  builder_.add_color(color);
  builder_.add_uv3(uv3);
  builder_.add_uv2(uv2);
  builder_.add_uv1(uv1);
  builder_.add_uv0(uv0);
  builder_.add_normal(normal);
  builder_.add_pos(pos);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * ImportNode::MiniReflectTypeTable(){
    return ImportNodeTypeTable();
  }
const flatbuffers::Vector<float> * ImportNode::localtransform() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_LOCALTRANSFORM);
  }
  flatbuffers::Vector<float> * ImportNode::mutable_localtransform() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_LOCALTRANSFORM);
  }
int32_t  ImportNode::meshindex() const{
    return GetField<int32_t>(VT_MESHINDEX, 0);
  }
  bool ImportNode::mutate_meshindex (int32_t _meshindex) {
    return SetField<int32_t>(VT_MESHINDEX, _meshindex, 0);
  }
const flatbuffers::String * ImportNode::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportNode::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>> * ImportNode::children() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>> *>(VT_CHILDREN);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>> * ImportNode::mutable_children() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>> *>(VT_CHILDREN);
  }
bool  ImportNode::isrootinimportmesh() const{
    return GetField<uint8_t>(VT_ISROOTINIMPORTMESH, 0) != 0;
  }
  bool ImportNode::mutate_isrootinimportmesh (bool _isrootinimportmesh) {
    return SetField<uint8_t>(VT_ISROOTINIMPORTMESH, static_cast<uint8_t>(_isrootinimportmesh), 0);
  }
int32_t  ImportNode::boneindexinimportmesh() const{
    return GetField<int32_t>(VT_BONEINDEXINIMPORTMESH, 0);
  }
  bool ImportNode::mutate_boneindexinimportmesh (int32_t _boneindexinimportmesh) {
    return SetField<int32_t>(VT_BONEINDEXINIMPORTMESH, _boneindexinimportmesh, 0);
  }
 bool ImportNode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_LOCALTRANSFORM) &&
           verifier.VerifyVector(localtransform()) &&
           VerifyField<int32_t>(verifier, VT_MESHINDEX) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_CHILDREN) &&
           verifier.VerifyVector(children()) &&
           verifier.VerifyVectorOfTables(children()) &&
           VerifyField<uint8_t>(verifier, VT_ISROOTINIMPORTMESH) &&
           VerifyField<int32_t>(verifier, VT_BONEINDEXINIMPORTMESH) &&
           verifier.EndTable();
  }
  void ImportNodeBuilder::add_localtransform(flatbuffers::Offset<flatbuffers::Vector<float>> localtransform) {
    fbb_.AddOffset(ImportNode::VT_LOCALTRANSFORM, localtransform);
  }
  void ImportNodeBuilder::add_meshindex(int32_t meshindex) {
    fbb_.AddElement<int32_t>(ImportNode::VT_MESHINDEX, meshindex, 0);
  }
  void ImportNodeBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportNode::VT_NAME, name);
  }
  void ImportNodeBuilder::add_children(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>>> children) {
    fbb_.AddOffset(ImportNode::VT_CHILDREN, children);
  }
  void ImportNodeBuilder::add_isrootinimportmesh(bool isrootinimportmesh) {
    fbb_.AddElement<uint8_t>(ImportNode::VT_ISROOTINIMPORTMESH, static_cast<uint8_t>(isrootinimportmesh), 0);
  }
  void ImportNodeBuilder::add_boneindexinimportmesh(int32_t boneindexinimportmesh) {
    fbb_.AddElement<int32_t>(ImportNode::VT_BONEINDEXINIMPORTMESH, boneindexinimportmesh, 0);
  }
flatbuffers::Offset<ImportNode> CreateImportNode(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<float>> localtransform,
    int32_t meshindex,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportNode>>> children,
    bool isrootinimportmesh,
    int32_t boneindexinimportmesh){
    ImportNodeBuilder builder_(_fbb);
  builder_.add_boneindexinimportmesh(boneindexinimportmesh);
  builder_.add_children(children);
  builder_.add_name(name);
  builder_.add_meshindex(meshindex);
  builder_.add_localtransform(localtransform);
  builder_.add_isrootinimportmesh(isrootinimportmesh);
  return builder_.Finish();
}

flatbuffers::Offset<ImportNode> CreateImportNodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *localtransform,
    int32_t meshindex,
    const char *name,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportNode>> *children,
    bool isrootinimportmesh,
    int32_t boneindexinimportmesh) {
  auto localtransform__ = localtransform ? _fbb.CreateVector<float>(*localtransform) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto children__ = children ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportNode>>(*children) : 0;
  return CrossSchema::CreateImportNode(
      _fbb,
      localtransform__,
      meshindex,
      name__,
      children__,
      isrootinimportmesh,
      boneindexinimportmesh);
}

 const flatbuffers::TypeTable * ImportMeshData::MiniReflectTypeTable(){
    return ImportMeshDataTypeTable();
  }
const flatbuffers::String * ImportMeshData::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportMeshData::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const CrossSchema::IndiceData * ImportMeshData::indices() const{
    return GetPointer<const CrossSchema::IndiceData *>(VT_INDICES);
  }
  CrossSchema::IndiceData * ImportMeshData::mutable_indices() {
    return GetPointer<CrossSchema::IndiceData *>(VT_INDICES);
  }
const CrossSchema::MeshVertexDescription * ImportMeshData::vertexdata() const{
    return GetPointer<const CrossSchema::MeshVertexDescription *>(VT_VERTEXDATA);
  }
  CrossSchema::MeshVertexDescription * ImportMeshData::mutable_vertexdata() {
    return GetPointer<CrossSchema::MeshVertexDescription *>(VT_VERTEXDATA);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> * ImportMeshData::collisiontree() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *>(VT_COLLISIONTREE);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> * ImportMeshData::mutable_collisiontree() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *>(VT_COLLISIONTREE);
  }
 bool ImportMeshData::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_INDICES) &&
           verifier.VerifyTable(indices()) &&
           VerifyOffset(verifier, VT_VERTEXDATA) &&
           verifier.VerifyTable(vertexdata()) &&
           VerifyOffset(verifier, VT_COLLISIONTREE) &&
           verifier.VerifyVector(collisiontree()) &&
           verifier.VerifyVectorOfTables(collisiontree()) &&
           verifier.EndTable();
  }
  void ImportMeshDataBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportMeshData::VT_NAME, name);
  }
  void ImportMeshDataBuilder::add_indices(flatbuffers::Offset<CrossSchema::IndiceData> indices) {
    fbb_.AddOffset(ImportMeshData::VT_INDICES, indices);
  }
  void ImportMeshDataBuilder::add_vertexdata(flatbuffers::Offset<CrossSchema::MeshVertexDescription> vertexdata) {
    fbb_.AddOffset(ImportMeshData::VT_VERTEXDATA, vertexdata);
  }
  void ImportMeshDataBuilder::add_collisiontree(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>>> collisiontree) {
    fbb_.AddOffset(ImportMeshData::VT_COLLISIONTREE, collisiontree);
  }
flatbuffers::Offset<ImportMeshData> CreateImportMeshData(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<CrossSchema::IndiceData> indices,
    flatbuffers::Offset<CrossSchema::MeshVertexDescription> vertexdata,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CollisionNode>>> collisiontree){
    ImportMeshDataBuilder builder_(_fbb);
  builder_.add_collisiontree(collisiontree);
  builder_.add_vertexdata(vertexdata);
  builder_.add_indices(indices);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ImportMeshData> CreateImportMeshDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    flatbuffers::Offset<CrossSchema::IndiceData> indices,
    flatbuffers::Offset<CrossSchema::MeshVertexDescription> vertexdata,
    const std::vector<flatbuffers::Offset<CrossSchema::CollisionNode>> *collisiontree) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto collisiontree__ = collisiontree ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::CollisionNode>>(*collisiontree) : 0;
  return CrossSchema::CreateImportMeshData(
      _fbb,
      name__,
      indices,
      vertexdata,
      collisiontree__);
}

 const flatbuffers::TypeTable * ImportVertexBone::MiniReflectTypeTable(){
    return ImportVertexBoneTypeTable();
  }
const flatbuffers::Vector<float> * ImportVertexBone::weights() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_WEIGHTS);
  }
  flatbuffers::Vector<float> * ImportVertexBone::mutable_weights() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_WEIGHTS);
  }
const flatbuffers::Vector<int32_t> * ImportVertexBone::boneindices() const{
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_BONEINDICES);
  }
  flatbuffers::Vector<int32_t> * ImportVertexBone::mutable_boneindices() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_BONEINDICES);
  }
 bool ImportVertexBone::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_WEIGHTS) &&
           verifier.VerifyVector(weights()) &&
           VerifyOffset(verifier, VT_BONEINDICES) &&
           verifier.VerifyVector(boneindices()) &&
           verifier.EndTable();
  }
  void ImportVertexBoneBuilder::add_weights(flatbuffers::Offset<flatbuffers::Vector<float>> weights) {
    fbb_.AddOffset(ImportVertexBone::VT_WEIGHTS, weights);
  }
  void ImportVertexBoneBuilder::add_boneindices(flatbuffers::Offset<flatbuffers::Vector<int32_t>> boneindices) {
    fbb_.AddOffset(ImportVertexBone::VT_BONEINDICES, boneindices);
  }
flatbuffers::Offset<ImportVertexBone> CreateImportVertexBone(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<float>> weights,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> boneindices){
    ImportVertexBoneBuilder builder_(_fbb);
  builder_.add_boneindices(boneindices);
  builder_.add_weights(weights);
  return builder_.Finish();
}

flatbuffers::Offset<ImportVertexBone> CreateImportVertexBoneDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *weights,
    const std::vector<int32_t> *boneindices) {
  auto weights__ = weights ? _fbb.CreateVector<float>(*weights) : 0;
  auto boneindices__ = boneindices ? _fbb.CreateVector<int32_t>(*boneindices) : 0;
  return CrossSchema::CreateImportVertexBone(
      _fbb,
      weights__,
      boneindices__);
}

 const flatbuffers::TypeTable * ImportMeshSkin::MiniReflectTypeTable(){
    return ImportMeshSkinTypeTable();
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> * ImportMeshSkin::vbones() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> *>(VT_VBONES);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> * ImportMeshSkin::mutable_vbones() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> *>(VT_VBONES);
  }
 bool ImportMeshSkin::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VBONES) &&
           verifier.VerifyVector(vbones()) &&
           verifier.VerifyVectorOfTables(vbones()) &&
           verifier.EndTable();
  }
  void ImportMeshSkinBuilder::add_vbones(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>>> vbones) {
    fbb_.AddOffset(ImportMeshSkin::VT_VBONES, vbones);
  }
flatbuffers::Offset<ImportMeshSkin> CreateImportMeshSkin(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>>> vbones){
    ImportMeshSkinBuilder builder_(_fbb);
  builder_.add_vbones(vbones);
  return builder_.Finish();
}

flatbuffers::Offset<ImportMeshSkin> CreateImportMeshSkinDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> *vbones) {
  auto vbones__ = vbones ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportVertexBone>>(*vbones) : 0;
  return CrossSchema::CreateImportMeshSkin(
      _fbb,
      vbones__);
}

 const flatbuffers::TypeTable * UsedBonesId::MiniReflectTypeTable(){
    return UsedBonesIdTypeTable();
  }
const flatbuffers::Vector<int32_t> * UsedBonesId::bones() const{
    return GetPointer<const flatbuffers::Vector<int32_t> *>(VT_BONES);
  }
  flatbuffers::Vector<int32_t> * UsedBonesId::mutable_bones() {
    return GetPointer<flatbuffers::Vector<int32_t> *>(VT_BONES);
  }
 bool UsedBonesId::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_BONES) &&
           verifier.VerifyVector(bones()) &&
           verifier.EndTable();
  }
  void UsedBonesIdBuilder::add_bones(flatbuffers::Offset<flatbuffers::Vector<int32_t>> bones) {
    fbb_.AddOffset(UsedBonesId::VT_BONES, bones);
  }
flatbuffers::Offset<UsedBonesId> CreateUsedBonesId(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<int32_t>> bones){
    UsedBonesIdBuilder builder_(_fbb);
  builder_.add_bones(bones);
  return builder_.Finish();
}

flatbuffers::Offset<UsedBonesId> CreateUsedBonesIdDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *bones) {
  auto bones__ = bones ? _fbb.CreateVector<int32_t>(*bones) : 0;
  return CrossSchema::CreateUsedBonesId(
      _fbb,
      bones__);
}

 const flatbuffers::TypeTable * invmatrix::MiniReflectTypeTable(){
    return invmatrixTypeTable();
  }
const flatbuffers::Vector<float> * invmatrix::val() const{
    return GetPointer<const flatbuffers::Vector<float> *>(VT_VAL);
  }
  flatbuffers::Vector<float> * invmatrix::mutable_val() {
    return GetPointer<flatbuffers::Vector<float> *>(VT_VAL);
  }
 bool invmatrix::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VAL) &&
           verifier.VerifyVector(val()) &&
           verifier.EndTable();
  }
  void invmatrixBuilder::add_val(flatbuffers::Offset<flatbuffers::Vector<float>> val) {
    fbb_.AddOffset(invmatrix::VT_VAL, val);
  }
flatbuffers::Offset<invmatrix> Createinvmatrix(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<float>> val){
    invmatrixBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

flatbuffers::Offset<invmatrix> CreateinvmatrixDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *val) {
  auto val__ = val ? _fbb.CreateVector<float>(*val) : 0;
  return CrossSchema::Createinvmatrix(
      _fbb,
      val__);
}

 const flatbuffers::TypeTable * ImportMeshes::MiniReflectTypeTable(){
    return ImportMeshesTypeTable();
  }
const CrossSchema::ImportNode * ImportMeshes::rootnode() const{
    return GetPointer<const CrossSchema::ImportNode *>(VT_ROOTNODE);
  }
  CrossSchema::ImportNode * ImportMeshes::mutable_rootnode() {
    return GetPointer<CrossSchema::ImportNode *>(VT_ROOTNODE);
  }
const flatbuffers::String * ImportMeshes::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ImportMeshes::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::String * ImportMeshes::pathtoskeleton() const{
    return GetPointer<const flatbuffers::String *>(VT_PATHTOSKELETON);
  }
  flatbuffers::String * ImportMeshes::mutable_pathtoskeleton() {
    return GetPointer<flatbuffers::String *>(VT_PATHTOSKELETON);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>> * ImportMeshes::meshesdata() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>> *>(VT_MESHESDATA);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>> * ImportMeshes::mutable_meshesdata() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>> *>(VT_MESHESDATA);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> * ImportMeshes::meshskininfo() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> *>(VT_MESHSKININFO);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> * ImportMeshes::mutable_meshskininfo() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> *>(VT_MESHSKININFO);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>> * ImportMeshes::meshusedbones() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>> *>(VT_MESHUSEDBONES);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>> * ImportMeshes::mutable_meshusedbones() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>> *>(VT_MESHUSEDBONES);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> * ImportMeshes::skeleton() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *>(VT_SKELETON);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> * ImportMeshes::mutable_skeleton() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *>(VT_SKELETON);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> * ImportMeshes::bindposeinvmatrixs() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> *>(VT_BINDPOSEINVMATRIXS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> * ImportMeshes::mutable_bindposeinvmatrixs() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>> *>(VT_BINDPOSEINVMATRIXS);
  }
 bool ImportMeshes::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ROOTNODE) &&
           verifier.VerifyTable(rootnode()) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_PATHTOSKELETON) &&
           verifier.VerifyString(pathtoskeleton()) &&
           VerifyOffset(verifier, VT_MESHESDATA) &&
           verifier.VerifyVector(meshesdata()) &&
           verifier.VerifyVectorOfTables(meshesdata()) &&
           VerifyOffset(verifier, VT_MESHSKININFO) &&
           verifier.VerifyVector(meshskininfo()) &&
           verifier.VerifyVectorOfTables(meshskininfo()) &&
           VerifyOffset(verifier, VT_MESHUSEDBONES) &&
           verifier.VerifyVector(meshusedbones()) &&
           verifier.VerifyVectorOfTables(meshusedbones()) &&
           VerifyOffset(verifier, VT_SKELETON) &&
           verifier.VerifyVector(skeleton()) &&
           verifier.VerifyVectorOfTables(skeleton()) &&
           VerifyOffset(verifier, VT_BINDPOSEINVMATRIXS) &&
           verifier.VerifyVector(bindposeinvmatrixs()) &&
           verifier.VerifyVectorOfTables(bindposeinvmatrixs()) &&
           verifier.EndTable();
  }
  void ImportMeshesBuilder::add_rootnode(flatbuffers::Offset<CrossSchema::ImportNode> rootnode) {
    fbb_.AddOffset(ImportMeshes::VT_ROOTNODE, rootnode);
  }
  void ImportMeshesBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ImportMeshes::VT_NAME, name);
  }
  void ImportMeshesBuilder::add_pathtoskeleton(flatbuffers::Offset<flatbuffers::String> pathtoskeleton) {
    fbb_.AddOffset(ImportMeshes::VT_PATHTOSKELETON, pathtoskeleton);
  }
  void ImportMeshesBuilder::add_meshesdata(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>>> meshesdata) {
    fbb_.AddOffset(ImportMeshes::VT_MESHESDATA, meshesdata);
  }
  void ImportMeshesBuilder::add_meshskininfo(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>>> meshskininfo) {
    fbb_.AddOffset(ImportMeshes::VT_MESHSKININFO, meshskininfo);
  }
  void ImportMeshesBuilder::add_meshusedbones(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>>> meshusedbones) {
    fbb_.AddOffset(ImportMeshes::VT_MESHUSEDBONES, meshusedbones);
  }
  void ImportMeshesBuilder::add_skeleton(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>> skeleton) {
    fbb_.AddOffset(ImportMeshes::VT_SKELETON, skeleton);
  }
  void ImportMeshesBuilder::add_bindposeinvmatrixs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>>> bindposeinvmatrixs) {
    fbb_.AddOffset(ImportMeshes::VT_BINDPOSEINVMATRIXS, bindposeinvmatrixs);
  }
flatbuffers::Offset<ImportMeshes> CreateImportMeshes(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<CrossSchema::ImportNode> rootnode,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::String> pathtoskeleton,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshData>>> meshesdata,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>>> meshskininfo,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::UsedBonesId>>> meshusedbones,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>> skeleton,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::invmatrix>>> bindposeinvmatrixs){
    ImportMeshesBuilder builder_(_fbb);
  builder_.add_bindposeinvmatrixs(bindposeinvmatrixs);
  builder_.add_skeleton(skeleton);
  builder_.add_meshusedbones(meshusedbones);
  builder_.add_meshskininfo(meshskininfo);
  builder_.add_meshesdata(meshesdata);
  builder_.add_pathtoskeleton(pathtoskeleton);
  builder_.add_name(name);
  builder_.add_rootnode(rootnode);
  return builder_.Finish();
}

flatbuffers::Offset<ImportMeshes> CreateImportMeshesDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<CrossSchema::ImportNode> rootnode,
    const char *name,
    const char *pathtoskeleton,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportMeshData>> *meshesdata,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> *meshskininfo,
    const std::vector<flatbuffers::Offset<CrossSchema::UsedBonesId>> *meshusedbones,
    const std::vector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> *skeleton,
    const std::vector<flatbuffers::Offset<CrossSchema::invmatrix>> *bindposeinvmatrixs) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto pathtoskeleton__ = pathtoskeleton ? _fbb.CreateString(pathtoskeleton) : 0;
  auto meshesdata__ = meshesdata ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportMeshData>>(*meshesdata) : 0;
  auto meshskininfo__ = meshskininfo ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>>(*meshskininfo) : 0;
  auto meshusedbones__ = meshusedbones ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::UsedBonesId>>(*meshusedbones) : 0;
  auto skeleton__ = skeleton ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportBoneNode>>(*skeleton) : 0;
  auto bindposeinvmatrixs__ = bindposeinvmatrixs ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::invmatrix>>(*bindposeinvmatrixs) : 0;
  return CrossSchema::CreateImportMeshes(
      _fbb,
      rootnode,
      name__,
      pathtoskeleton__,
      meshesdata__,
      meshskininfo__,
      meshusedbones__,
      skeleton__,
      bindposeinvmatrixs__);
}

float3attributeT *float3attribute::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<float3attributeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void float3attribute::UnPackTo(float3attributeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = *_e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<float3attribute> float3attribute::Pack(flatbuffers::FlatBufferBuilder &_fbb, const float3attributeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createfloat3attribute(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<float3attribute> Createfloat3attribute(flatbuffers::FlatBufferBuilder &_fbb, const float3attributeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const float3attributeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _data = _o->data.size() ? _fbb.CreateVectorOfStructs(_o->data) : 0;
  return CrossSchema::Createfloat3attribute(
      _fbb,
      _name,
      _data);
}

float2attributeT *float2attribute::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<float2attributeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void float2attribute::UnPackTo(float2attributeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = *_e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<float2attribute> float2attribute::Pack(flatbuffers::FlatBufferBuilder &_fbb, const float2attributeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createfloat2attribute(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<float2attribute> Createfloat2attribute(flatbuffers::FlatBufferBuilder &_fbb, const float2attributeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const float2attributeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _data = _o->data.size() ? _fbb.CreateVectorOfStructs(_o->data) : 0;
  return CrossSchema::Createfloat2attribute(
      _fbb,
      _name,
      _data);
}

float4attributeT *float4attribute::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<float4attributeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void float4attribute::UnPackTo(float4attributeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = *_e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<float4attribute> float4attribute::Pack(flatbuffers::FlatBufferBuilder &_fbb, const float4attributeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createfloat4attribute(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<float4attribute> Createfloat4attribute(flatbuffers::FlatBufferBuilder &_fbb, const float4attributeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const float4attributeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _data = _o->data.size() ? _fbb.CreateVectorOfStructs(_o->data) : 0;
  return CrossSchema::Createfloat4attribute(
      _fbb,
      _name,
      _data);
}

uint4attributeT *uint4attribute::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<uint4attributeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void uint4attribute::UnPackTo(uint4attributeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = *_e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<uint4attribute> uint4attribute::Pack(flatbuffers::FlatBufferBuilder &_fbb, const uint4attributeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createuint4attribute(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<uint4attribute> Createuint4attribute(flatbuffers::FlatBufferBuilder &_fbb, const uint4attributeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const uint4attributeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _data = _o->data.size() ? _fbb.CreateVectorOfStructs(_o->data) : 0;
  return CrossSchema::Createuint4attribute(
      _fbb,
      _name,
      _data);
}

uintattributeT *uintattribute::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<uintattributeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void uintattribute::UnPackTo(uintattributeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = data(); if (_e) { _o->data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->data[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<uintattribute> uintattribute::Pack(flatbuffers::FlatBufferBuilder &_fbb, const uintattributeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createuintattribute(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<uintattribute> Createuintattribute(flatbuffers::FlatBufferBuilder &_fbb, const uintattributeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const uintattributeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _data = _o->data.size() ? _fbb.CreateVector(_o->data) : 0;
  return CrossSchema::Createuintattribute(
      _fbb,
      _name,
      _data);
}

IndiceDataT *IndiceData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<IndiceDataT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void IndiceData::UnPackTo(IndiceDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = indicescount(); _o->indicescount = _e; }
  { auto _e = indexbuffer(); if (_e) { _o->indexbuffer.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->indexbuffer[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<IndiceData> IndiceData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const IndiceDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateIndiceData(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<IndiceData> CreateIndiceData(flatbuffers::FlatBufferBuilder &_fbb, const IndiceDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const IndiceDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _indicescount = _o->indicescount;
  auto _indexbuffer = _o->indexbuffer.size() ? _fbb.CreateVector(_o->indexbuffer) : 0;
  return CrossSchema::CreateIndiceData(
      _fbb,
      _indicescount,
      _indexbuffer);
}

CollisionNodeT *CollisionNode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<CollisionNodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void CollisionNode::UnPackTo(CollisionNodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = index(); _o->index = _e; }
  { auto _e = leftindex(); _o->leftindex = _e; }
  { auto _e = rightindex(); _o->rightindex = _e; }
  { auto _e = minpos(); if (_e) { _o->minpos.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->minpos[_i] = _e->Get(_i); } } }
  { auto _e = maxpos(); if (_e) { _o->maxpos.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->maxpos[_i] = _e->Get(_i); } } }
  { auto _e = trianglelist(); if (_e) { _o->trianglelist.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->trianglelist[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<CollisionNode> CollisionNode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const CollisionNodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateCollisionNode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<CollisionNode> CreateCollisionNode(flatbuffers::FlatBufferBuilder &_fbb, const CollisionNodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const CollisionNodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _index = _o->index;
  auto _leftindex = _o->leftindex;
  auto _rightindex = _o->rightindex;
  auto _minpos = _o->minpos.size() ? _fbb.CreateVector(_o->minpos) : 0;
  auto _maxpos = _o->maxpos.size() ? _fbb.CreateVector(_o->maxpos) : 0;
  auto _trianglelist = _o->trianglelist.size() ? _fbb.CreateVector(_o->trianglelist) : 0;
  return CrossSchema::CreateCollisionNode(
      _fbb,
      _index,
      _leftindex,
      _rightindex,
      _minpos,
      _maxpos,
      _trianglelist);
}

MeshVertexDescriptionT *MeshVertexDescription::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<MeshVertexDescriptionT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void MeshVertexDescription::UnPackTo(MeshVertexDescriptionT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = pos(); if (_e) _o->pos = std::unique_ptr<CrossSchema::float3attributeT>(_e->UnPack(_resolver)); }
  { auto _e = normal(); if (_e) _o->normal = std::unique_ptr<CrossSchema::float3attributeT>(_e->UnPack(_resolver)); }
  { auto _e = uv0(); if (_e) _o->uv0 = std::unique_ptr<CrossSchema::float2attributeT>(_e->UnPack(_resolver)); }
  { auto _e = uv1(); if (_e) _o->uv1 = std::unique_ptr<CrossSchema::float2attributeT>(_e->UnPack(_resolver)); }
  { auto _e = uv2(); if (_e) _o->uv2 = std::unique_ptr<CrossSchema::float2attributeT>(_e->UnPack(_resolver)); }
  { auto _e = uv3(); if (_e) _o->uv3 = std::unique_ptr<CrossSchema::float2attributeT>(_e->UnPack(_resolver)); }
  { auto _e = color(); if (_e) _o->color = std::unique_ptr<CrossSchema::uintattributeT>(_e->UnPack(_resolver)); }
  { auto _e = tangent(); if (_e) _o->tangent = std::unique_ptr<CrossSchema::float4attributeT>(_e->UnPack(_resolver)); }
  { auto _e = binormal(); if (_e) _o->binormal = std::unique_ptr<CrossSchema::float4attributeT>(_e->UnPack(_resolver)); }
  { auto _e = boneweights(); if (_e) _o->boneweights = std::unique_ptr<CrossSchema::float4attributeT>(_e->UnPack(_resolver)); }
  { auto _e = boneids(); if (_e) _o->boneids = std::unique_ptr<CrossSchema::uint4attributeT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<MeshVertexDescription> MeshVertexDescription::Pack(flatbuffers::FlatBufferBuilder &_fbb, const MeshVertexDescriptionT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateMeshVertexDescription(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<MeshVertexDescription> CreateMeshVertexDescription(flatbuffers::FlatBufferBuilder &_fbb, const MeshVertexDescriptionT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const MeshVertexDescriptionT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _pos = _o->pos ? Createfloat3attribute(_fbb, _o->pos.get(), _rehasher) : 0;
  auto _normal = _o->normal ? Createfloat3attribute(_fbb, _o->normal.get(), _rehasher) : 0;
  auto _uv0 = _o->uv0 ? Createfloat2attribute(_fbb, _o->uv0.get(), _rehasher) : 0;
  auto _uv1 = _o->uv1 ? Createfloat2attribute(_fbb, _o->uv1.get(), _rehasher) : 0;
  auto _uv2 = _o->uv2 ? Createfloat2attribute(_fbb, _o->uv2.get(), _rehasher) : 0;
  auto _uv3 = _o->uv3 ? Createfloat2attribute(_fbb, _o->uv3.get(), _rehasher) : 0;
  auto _color = _o->color ? Createuintattribute(_fbb, _o->color.get(), _rehasher) : 0;
  auto _tangent = _o->tangent ? Createfloat4attribute(_fbb, _o->tangent.get(), _rehasher) : 0;
  auto _binormal = _o->binormal ? Createfloat4attribute(_fbb, _o->binormal.get(), _rehasher) : 0;
  auto _boneweights = _o->boneweights ? Createfloat4attribute(_fbb, _o->boneweights.get(), _rehasher) : 0;
  auto _boneids = _o->boneids ? Createuint4attribute(_fbb, _o->boneids.get(), _rehasher) : 0;
  return CrossSchema::CreateMeshVertexDescription(
      _fbb,
      _pos,
      _normal,
      _uv0,
      _uv1,
      _uv2,
      _uv3,
      _color,
      _tangent,
      _binormal,
      _boneweights,
      _boneids);
}

ImportNodeT *ImportNode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportNodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportNode::UnPackTo(ImportNodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = localtransform(); if (_e) { _o->localtransform.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->localtransform[_i] = _e->Get(_i); } } }
  { auto _e = meshindex(); _o->meshindex = _e; }
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = children(); if (_e) { _o->children.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->children[_i] = std::unique_ptr<CrossSchema::ImportNodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = isrootinimportmesh(); _o->isrootinimportmesh = _e; }
  { auto _e = boneindexinimportmesh(); _o->boneindexinimportmesh = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportNode> ImportNode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportNodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportNode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportNode> CreateImportNode(flatbuffers::FlatBufferBuilder &_fbb, const ImportNodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportNodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _localtransform = _o->localtransform.size() ? _fbb.CreateVector(_o->localtransform) : 0;
  auto _meshindex = _o->meshindex;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _children = _o->children.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportNode>> (_o->children.size(), [](size_t i, _VectorArgs *__va) { return CreateImportNode(*__va->__fbb, __va->__o->children[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _isrootinimportmesh = _o->isrootinimportmesh;
  auto _boneindexinimportmesh = _o->boneindexinimportmesh;
  return CrossSchema::CreateImportNode(
      _fbb,
      _localtransform,
      _meshindex,
      _name,
      _children,
      _isrootinimportmesh,
      _boneindexinimportmesh);
}

ImportMeshDataT *ImportMeshData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportMeshDataT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportMeshData::UnPackTo(ImportMeshDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = indices(); if (_e) _o->indices = std::unique_ptr<CrossSchema::IndiceDataT>(_e->UnPack(_resolver)); }
  { auto _e = vertexdata(); if (_e) _o->vertexdata = std::unique_ptr<CrossSchema::MeshVertexDescriptionT>(_e->UnPack(_resolver)); }
  { auto _e = collisiontree(); if (_e) { _o->collisiontree.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->collisiontree[_i] = std::unique_ptr<CrossSchema::CollisionNodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshData> ImportMeshData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportMeshData(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshData> CreateImportMeshData(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportMeshDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _indices = _o->indices ? CreateIndiceData(_fbb, _o->indices.get(), _rehasher) : 0;
  auto _vertexdata = _o->vertexdata ? CreateMeshVertexDescription(_fbb, _o->vertexdata.get(), _rehasher) : 0;
  auto _collisiontree = _o->collisiontree.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::CollisionNode>> (_o->collisiontree.size(), [](size_t i, _VectorArgs *__va) { return CreateCollisionNode(*__va->__fbb, __va->__o->collisiontree[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateImportMeshData(
      _fbb,
      _name,
      _indices,
      _vertexdata,
      _collisiontree);
}

ImportVertexBoneT *ImportVertexBone::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportVertexBoneT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportVertexBone::UnPackTo(ImportVertexBoneT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = weights(); if (_e) { _o->weights.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->weights[_i] = _e->Get(_i); } } }
  { auto _e = boneindices(); if (_e) { _o->boneindices.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->boneindices[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportVertexBone> ImportVertexBone::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportVertexBoneT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportVertexBone(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportVertexBone> CreateImportVertexBone(flatbuffers::FlatBufferBuilder &_fbb, const ImportVertexBoneT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportVertexBoneT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _weights = _o->weights.size() ? _fbb.CreateVector(_o->weights) : 0;
  auto _boneindices = _o->boneindices.size() ? _fbb.CreateVector(_o->boneindices) : 0;
  return CrossSchema::CreateImportVertexBone(
      _fbb,
      _weights,
      _boneindices);
}

ImportMeshSkinT *ImportMeshSkin::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportMeshSkinT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportMeshSkin::UnPackTo(ImportMeshSkinT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = vbones(); if (_e) { _o->vbones.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->vbones[_i] = std::unique_ptr<CrossSchema::ImportVertexBoneT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshSkin> ImportMeshSkin::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshSkinT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportMeshSkin(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshSkin> CreateImportMeshSkin(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshSkinT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportMeshSkinT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _vbones = _o->vbones.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportVertexBone>> (_o->vbones.size(), [](size_t i, _VectorArgs *__va) { return CreateImportVertexBone(*__va->__fbb, __va->__o->vbones[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateImportMeshSkin(
      _fbb,
      _vbones);
}

UsedBonesIdT *UsedBonesId::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<UsedBonesIdT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void UsedBonesId::UnPackTo(UsedBonesIdT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = bones(); if (_e) { _o->bones.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->bones[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<UsedBonesId> UsedBonesId::Pack(flatbuffers::FlatBufferBuilder &_fbb, const UsedBonesIdT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateUsedBonesId(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<UsedBonesId> CreateUsedBonesId(flatbuffers::FlatBufferBuilder &_fbb, const UsedBonesIdT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const UsedBonesIdT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _bones = _o->bones.size() ? _fbb.CreateVector(_o->bones) : 0;
  return CrossSchema::CreateUsedBonesId(
      _fbb,
      _bones);
}

invmatrixT *invmatrix::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<invmatrixT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void invmatrix::UnPackTo(invmatrixT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = val(); if (_e) { _o->val.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->val[_i] = _e->Get(_i); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<invmatrix> invmatrix::Pack(flatbuffers::FlatBufferBuilder &_fbb, const invmatrixT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return Createinvmatrix(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<invmatrix> Createinvmatrix(flatbuffers::FlatBufferBuilder &_fbb, const invmatrixT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const invmatrixT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _val = _o->val.size() ? _fbb.CreateVector(_o->val) : 0;
  return CrossSchema::Createinvmatrix(
      _fbb,
      _val);
}

ImportMeshesT *ImportMeshes::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ImportMeshesT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ImportMeshes::UnPackTo(ImportMeshesT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = rootnode(); if (_e) _o->rootnode = std::unique_ptr<CrossSchema::ImportNodeT>(_e->UnPack(_resolver)); }
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = pathtoskeleton(); if (_e) _o->pathtoskeleton = _e->str(); }
  { auto _e = meshesdata(); if (_e) { _o->meshesdata.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->meshesdata[_i] = std::unique_ptr<CrossSchema::ImportMeshDataT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = meshskininfo(); if (_e) { _o->meshskininfo.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->meshskininfo[_i] = std::unique_ptr<CrossSchema::ImportMeshSkinT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = meshusedbones(); if (_e) { _o->meshusedbones.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->meshusedbones[_i] = std::unique_ptr<CrossSchema::UsedBonesIdT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = skeleton(); if (_e) { _o->skeleton.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->skeleton[_i] = std::unique_ptr<CrossSchema::ImportBoneNodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = bindposeinvmatrixs(); if (_e) { _o->bindposeinvmatrixs.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->bindposeinvmatrixs[_i] = std::unique_ptr<CrossSchema::invmatrixT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshes> ImportMeshes::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshesT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateImportMeshes(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ImportMeshes> CreateImportMeshes(flatbuffers::FlatBufferBuilder &_fbb, const ImportMeshesT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ImportMeshesT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _rootnode = _o->rootnode ? CreateImportNode(_fbb, _o->rootnode.get(), _rehasher) : 0;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _pathtoskeleton = _o->pathtoskeleton.empty() ? 0 : _fbb.CreateString(_o->pathtoskeleton);
  auto _meshesdata = _o->meshesdata.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportMeshData>> (_o->meshesdata.size(), [](size_t i, _VectorArgs *__va) { return CreateImportMeshData(*__va->__fbb, __va->__o->meshesdata[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _meshskininfo = _o->meshskininfo.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportMeshSkin>> (_o->meshskininfo.size(), [](size_t i, _VectorArgs *__va) { return CreateImportMeshSkin(*__va->__fbb, __va->__o->meshskininfo[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _meshusedbones = _o->meshusedbones.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::UsedBonesId>> (_o->meshusedbones.size(), [](size_t i, _VectorArgs *__va) { return CreateUsedBonesId(*__va->__fbb, __va->__o->meshusedbones[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _skeleton = _o->skeleton.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ImportBoneNode>> (_o->skeleton.size(), [](size_t i, _VectorArgs *__va) { return CreateImportBoneNode(*__va->__fbb, __va->__o->skeleton[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _bindposeinvmatrixs = _o->bindposeinvmatrixs.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::invmatrix>> (_o->bindposeinvmatrixs.size(), [](size_t i, _VectorArgs *__va) { return Createinvmatrix(*__va->__fbb, __va->__o->bindposeinvmatrixs[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateImportMeshes(
      _fbb,
      _rootnode,
      _name,
      _pathtoskeleton,
      _meshesdata,
      _meshskininfo,
      _meshusedbones,
      _skeleton,
      _bindposeinvmatrixs);
}

const CrossSchema::ImportMeshes *GetImportMeshes(const void *buf){
 return flatbuffers::GetRoot<CrossSchema::ImportMeshes>(buf);
}

const CrossSchema::ImportMeshes *GetSizePrefixedImportMeshes(const void *buf) {
return flatbuffers::GetSizePrefixedRoot<CrossSchema::ImportMeshes>(buf);
}

ImportMeshes *GetMutableImportMeshes(void *buf) {
return flatbuffers::GetMutableRoot<ImportMeshes>(buf);
}

bool VerifyImportMeshesBuffer(flatbuffers::Verifier &verifier) {
return verifier.VerifyBuffer<CrossSchema::ImportMeshes>(nullptr);
}

bool VerifySizePrefixedImportMeshesBuffer(flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<CrossSchema::ImportMeshes>(nullptr);
}

void FinishImportMeshesBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportMeshes> root) {
  fbb.Finish(root);
}

void FinishSizePrefixedImportMeshesBuffer(flatbuffers::FlatBufferBuilder &fbb,flatbuffers::Offset<CrossSchema::ImportMeshes> root) {
fbb.FinishSizePrefixed(root);
}

std::unique_ptr<CrossSchema::ImportMeshesT> UnPackImportMeshes(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ImportMeshesT>(GetImportMeshes(buf)->UnPack(res));
}

std::unique_ptr<CrossSchema::ImportMeshesT> UnPackSizePrefixedImportMeshes(const void *buf,const flatbuffers::resolver_function_t *res) {
return std::unique_ptr<CrossSchema::ImportMeshesT>(GetSizePrefixedImportMeshes(buf)->UnPack(res));
}

}  // namespace CrossSchema
