include "BasicStruct.fbs";
namespace CrossSchema;

//PropertyType:
table FloatProp
{
    name:string(key);
    value:float;
}

table Float2Prop
{
    name:string(key);
    value:float2;
}

table Float3Prop
{
    name:string(key);
    value:float3;
}

table Float4Prop
{
    name:string(key);
    value:float4;
}

table BoolProp
{
    name:string(key);
    value:bool;
}

table IntProp
{
    name:string(key);
    value:int;
}

table StringProp
{
    name:string(key);
    value:string;
}


table MaterialAsset
{
	parent:string;
    fx_file:string;
    float_prop_array:[FloatProp];
    float2_prop_array:[Float2Prop];
    float3_prop_array:[Float3Prop];
    float4_prop_array:[Float4Prop];
    matrix4x4_prop_array:[matrix4x4];
    int_prop_array:[IntProp];  
    texture_prop_array:[StringProp];
    bool_prop_array:[BoolProp];
}

root_type MaterialAsset;