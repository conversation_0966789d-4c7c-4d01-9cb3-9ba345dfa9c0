#include "ShaderAsset_generated.h"
namespace CrossSchema {
 const flatbuffers::TypeTable * ShaderVariable::MiniReflectTypeTable(){
    return ShaderVariableTypeTable();
  }
const flatbuffers::String * ShaderVariable::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ShaderVariable::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool ShaderVariable::KeyCompareLessThan(const ShaderVariable *o) const{
    return *name() < *o->name();
  }
int ShaderVariable::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
CrossSchema::ShaderVariableType  ShaderVariable::type() const{
    return static_cast<CrossSchema::ShaderVariableType>(GetField<uint32_t>(VT_TYPE, 0));
  }
  bool ShaderVariable::mutate_type (CrossSchema::ShaderVariableType _type) {
    return SetField<uint32_t>(VT_TYPE, static_cast<uint32_t>(_type), 0);
  }
uint32_t  ShaderVariable::offset() const{
    return GetField<uint32_t>(VT_OFFSET, 0);
  }
  bool ShaderVariable::mutate_offset (uint32_t _offset) {
    return SetField<uint32_t>(VT_OFFSET, _offset, 0);
  }
uint32_t  ShaderVariable::row_count() const{
    return GetField<uint32_t>(VT_ROW_COUNT, 0);
  }
  bool ShaderVariable::mutate_row_count (uint32_t _row_count) {
    return SetField<uint32_t>(VT_ROW_COUNT, _row_count, 0);
  }
uint32_t  ShaderVariable::col_count() const{
    return GetField<uint32_t>(VT_COL_COUNT, 0);
  }
  bool ShaderVariable::mutate_col_count (uint32_t _col_count) {
    return SetField<uint32_t>(VT_COL_COUNT, _col_count, 0);
  }
uint32_t  ShaderVariable::size() const{
    return GetField<uint32_t>(VT_SIZE, 0);
  }
  bool ShaderVariable::mutate_size (uint32_t _size) {
    return SetField<uint32_t>(VT_SIZE, _size, 0);
  }
uint32_t  ShaderVariable::array_size() const{
    return GetField<uint32_t>(VT_ARRAY_SIZE, 0);
  }
  bool ShaderVariable::mutate_array_size (uint32_t _array_size) {
    return SetField<uint32_t>(VT_ARRAY_SIZE, _array_size, 0);
  }
uint32_t  ShaderVariable::index() const{
    return GetField<uint32_t>(VT_INDEX, 0);
  }
  bool ShaderVariable::mutate_index (uint32_t _index) {
    return SetField<uint32_t>(VT_INDEX, _index, 0);
  }
uint32_t  ShaderVariable::stage_mask() const{
    return GetField<uint32_t>(VT_STAGE_MASK, 0);
  }
  bool ShaderVariable::mutate_stage_mask (uint32_t _stage_mask) {
    return SetField<uint32_t>(VT_STAGE_MASK, _stage_mask, 0);
  }
 bool ShaderVariable::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_OFFSET) &&
           VerifyField<uint32_t>(verifier, VT_ROW_COUNT) &&
           VerifyField<uint32_t>(verifier, VT_COL_COUNT) &&
           VerifyField<uint32_t>(verifier, VT_SIZE) &&
           VerifyField<uint32_t>(verifier, VT_ARRAY_SIZE) &&
           VerifyField<uint32_t>(verifier, VT_INDEX) &&
           VerifyField<uint32_t>(verifier, VT_STAGE_MASK) &&
           verifier.EndTable();
  }
  void ShaderVariableBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ShaderVariable::VT_NAME, name);
  }
  void ShaderVariableBuilder::add_type(CrossSchema::ShaderVariableType type) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_TYPE, static_cast<uint32_t>(type), 0);
  }
  void ShaderVariableBuilder::add_offset(uint32_t offset) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_OFFSET, offset, 0);
  }
  void ShaderVariableBuilder::add_row_count(uint32_t row_count) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_ROW_COUNT, row_count, 0);
  }
  void ShaderVariableBuilder::add_col_count(uint32_t col_count) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_COL_COUNT, col_count, 0);
  }
  void ShaderVariableBuilder::add_size(uint32_t size) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_SIZE, size, 0);
  }
  void ShaderVariableBuilder::add_array_size(uint32_t array_size) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_ARRAY_SIZE, array_size, 0);
  }
  void ShaderVariableBuilder::add_index(uint32_t index) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_INDEX, index, 0);
  }
  void ShaderVariableBuilder::add_stage_mask(uint32_t stage_mask) {
    fbb_.AddElement<uint32_t>(ShaderVariable::VT_STAGE_MASK, stage_mask, 0);
  }
flatbuffers::Offset<ShaderVariable> CreateShaderVariable(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    CrossSchema::ShaderVariableType type,
    uint32_t offset,
    uint32_t row_count,
    uint32_t col_count,
    uint32_t size,
    uint32_t array_size,
    uint32_t index,
    uint32_t stage_mask){
    ShaderVariableBuilder builder_(_fbb);
  builder_.add_stage_mask(stage_mask);
  builder_.add_index(index);
  builder_.add_array_size(array_size);
  builder_.add_size(size);
  builder_.add_col_count(col_count);
  builder_.add_row_count(row_count);
  builder_.add_offset(offset);
  builder_.add_type(type);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderVariable> CreateShaderVariableDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    CrossSchema::ShaderVariableType type,
    uint32_t offset,
    uint32_t row_count,
    uint32_t col_count,
    uint32_t size,
    uint32_t array_size,
    uint32_t index,
    uint32_t stage_mask) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateShaderVariable(
      _fbb,
      name__,
      type,
      offset,
      row_count,
      col_count,
      size,
      array_size,
      index,
      stage_mask);
}

 const flatbuffers::TypeTable * ShaderVariableEx::MiniReflectTypeTable(){
    return ShaderVariableExTypeTable();
  }
const flatbuffers::String * ShaderVariableEx::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ShaderVariableEx::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool ShaderVariableEx::KeyCompareLessThan(const ShaderVariableEx *o) const{
    return *name() < *o->name();
  }
int ShaderVariableEx::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
uint32_t  ShaderVariableEx::offset() const{
    return GetField<uint32_t>(VT_OFFSET, 0);
  }
  bool ShaderVariableEx::mutate_offset (uint32_t _offset) {
    return SetField<uint32_t>(VT_OFFSET, _offset, 0);
  }
uint32_t  ShaderVariableEx::size() const{
    return GetField<uint32_t>(VT_SIZE, 0);
  }
  bool ShaderVariableEx::mutate_size (uint32_t _size) {
    return SetField<uint32_t>(VT_SIZE, _size, 0);
  }
CrossSchema::ShaderVariableType  ShaderVariableEx::type() const{
    return static_cast<CrossSchema::ShaderVariableType>(GetField<uint32_t>(VT_TYPE, 0));
  }
  bool ShaderVariableEx::mutate_type (CrossSchema::ShaderVariableType _type) {
    return SetField<uint32_t>(VT_TYPE, static_cast<uint32_t>(_type), 0);
  }
uint32_t  ShaderVariableEx::row_count() const{
    return GetField<uint32_t>(VT_ROW_COUNT, 1);
  }
  bool ShaderVariableEx::mutate_row_count (uint32_t _row_count) {
    return SetField<uint32_t>(VT_ROW_COUNT, _row_count, 1);
  }
uint32_t  ShaderVariableEx::col_count() const{
    return GetField<uint32_t>(VT_COL_COUNT, 1);
  }
  bool ShaderVariableEx::mutate_col_count (uint32_t _col_count) {
    return SetField<uint32_t>(VT_COL_COUNT, _col_count, 1);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> * ShaderVariableEx::members() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *>(VT_MEMBERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> * ShaderVariableEx::mutable_members() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *>(VT_MEMBERS);
  }
uint32_t  ShaderVariableEx::array_size() const{
    return GetField<uint32_t>(VT_ARRAY_SIZE, 1);
  }
  bool ShaderVariableEx::mutate_array_size (uint32_t _array_size) {
    return SetField<uint32_t>(VT_ARRAY_SIZE, _array_size, 1);
  }
uint32_t  ShaderVariableEx::array_stride() const{
    return GetField<uint32_t>(VT_ARRAY_STRIDE, 0);
  }
  bool ShaderVariableEx::mutate_array_stride (uint32_t _array_stride) {
    return SetField<uint32_t>(VT_ARRAY_STRIDE, _array_stride, 0);
  }
uint32_t  ShaderVariableEx::index() const{
    return GetField<uint32_t>(VT_INDEX, 0);
  }
  bool ShaderVariableEx::mutate_index (uint32_t _index) {
    return SetField<uint32_t>(VT_INDEX, _index, 0);
  }
uint32_t  ShaderVariableEx::stage_mask() const{
    return GetField<uint32_t>(VT_STAGE_MASK, 0);
  }
  bool ShaderVariableEx::mutate_stage_mask (uint32_t _stage_mask) {
    return SetField<uint32_t>(VT_STAGE_MASK, _stage_mask, 0);
  }
 bool ShaderVariableEx::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_OFFSET) &&
           VerifyField<uint32_t>(verifier, VT_SIZE) &&
           VerifyField<uint32_t>(verifier, VT_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_ROW_COUNT) &&
           VerifyField<uint32_t>(verifier, VT_COL_COUNT) &&
           VerifyOffset(verifier, VT_MEMBERS) &&
           verifier.VerifyVector(members()) &&
           verifier.VerifyVectorOfTables(members()) &&
           VerifyField<uint32_t>(verifier, VT_ARRAY_SIZE) &&
           VerifyField<uint32_t>(verifier, VT_ARRAY_STRIDE) &&
           VerifyField<uint32_t>(verifier, VT_INDEX) &&
           VerifyField<uint32_t>(verifier, VT_STAGE_MASK) &&
           verifier.EndTable();
  }
  void ShaderVariableExBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ShaderVariableEx::VT_NAME, name);
  }
  void ShaderVariableExBuilder::add_offset(uint32_t offset) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_OFFSET, offset, 0);
  }
  void ShaderVariableExBuilder::add_size(uint32_t size) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_SIZE, size, 0);
  }
  void ShaderVariableExBuilder::add_type(CrossSchema::ShaderVariableType type) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_TYPE, static_cast<uint32_t>(type), 0);
  }
  void ShaderVariableExBuilder::add_row_count(uint32_t row_count) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_ROW_COUNT, row_count, 1);
  }
  void ShaderVariableExBuilder::add_col_count(uint32_t col_count) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_COL_COUNT, col_count, 1);
  }
  void ShaderVariableExBuilder::add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members) {
    fbb_.AddOffset(ShaderVariableEx::VT_MEMBERS, members);
  }
  void ShaderVariableExBuilder::add_array_size(uint32_t array_size) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_ARRAY_SIZE, array_size, 1);
  }
  void ShaderVariableExBuilder::add_array_stride(uint32_t array_stride) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_ARRAY_STRIDE, array_stride, 0);
  }
  void ShaderVariableExBuilder::add_index(uint32_t index) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_INDEX, index, 0);
  }
  void ShaderVariableExBuilder::add_stage_mask(uint32_t stage_mask) {
    fbb_.AddElement<uint32_t>(ShaderVariableEx::VT_STAGE_MASK, stage_mask, 0);
  }
flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableEx(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    uint32_t offset,
    uint32_t size,
    CrossSchema::ShaderVariableType type,
    uint32_t row_count,
    uint32_t col_count,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members,
    uint32_t array_size,
    uint32_t array_stride,
    uint32_t index,
    uint32_t stage_mask){
    ShaderVariableExBuilder builder_(_fbb);
  builder_.add_stage_mask(stage_mask);
  builder_.add_index(index);
  builder_.add_array_stride(array_stride);
  builder_.add_array_size(array_size);
  builder_.add_members(members);
  builder_.add_col_count(col_count);
  builder_.add_row_count(row_count);
  builder_.add_type(type);
  builder_.add_size(size);
  builder_.add_offset(offset);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableExDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    uint32_t offset,
    uint32_t size,
    CrossSchema::ShaderVariableType type,
    uint32_t row_count,
    uint32_t col_count,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members,
    uint32_t array_size,
    uint32_t array_stride,
    uint32_t index,
    uint32_t stage_mask) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto members__ = members ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderVariableEx>(members) : 0;
  return CrossSchema::CreateShaderVariableEx(
      _fbb,
      name__,
      offset,
      size,
      type,
      row_count,
      col_count,
      members__,
      array_size,
      array_stride,
      index,
      stage_mask);
}

 const flatbuffers::TypeTable * ShaderStructType::MiniReflectTypeTable(){
    return ShaderStructTypeTypeTable();
  }
uint32_t  ShaderStructType::size() const{
    return GetField<uint32_t>(VT_SIZE, 0);
  }
  bool ShaderStructType::mutate_size (uint32_t _size) {
    return SetField<uint32_t>(VT_SIZE, _size, 0);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> * ShaderStructType::members() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *>(VT_MEMBERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> * ShaderStructType::mutable_members() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *>(VT_MEMBERS);
  }
 bool ShaderStructType::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_SIZE) &&
           VerifyOffset(verifier, VT_MEMBERS) &&
           verifier.VerifyVector(members()) &&
           verifier.VerifyVectorOfTables(members()) &&
           verifier.EndTable();
  }
  void ShaderStructTypeBuilder::add_size(uint32_t size) {
    fbb_.AddElement<uint32_t>(ShaderStructType::VT_SIZE, size, 0);
  }
  void ShaderStructTypeBuilder::add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members) {
    fbb_.AddOffset(ShaderStructType::VT_MEMBERS, members);
  }
flatbuffers::Offset<ShaderStructType> CreateShaderStructType(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t size,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members){
    ShaderStructTypeBuilder builder_(_fbb);
  builder_.add_members(members);
  builder_.add_size(size);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderStructType> CreateShaderStructTypeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t size,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members) {
  auto members__ = members ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderVariableEx>(members) : 0;
  return CrossSchema::CreateShaderStructType(
      _fbb,
      size,
      members__);
}

 const flatbuffers::TypeTable * ShaderResource::MiniReflectTypeTable(){
    return ShaderResourceTypeTable();
  }
const flatbuffers::String * ShaderResource::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ShaderResource::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool ShaderResource::KeyCompareLessThan(const ShaderResource *o) const{
    return *name() < *o->name();
  }
int ShaderResource::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
CrossSchema::ShaderResourceType  ShaderResource::type() const{
    return static_cast<CrossSchema::ShaderResourceType>(GetField<uint32_t>(VT_TYPE, 0));
  }
  bool ShaderResource::mutate_type (CrossSchema::ShaderResourceType _type) {
    return SetField<uint32_t>(VT_TYPE, static_cast<uint32_t>(_type), 0);
  }
uint32_t  ShaderResource::array_size() const{
    return GetField<uint32_t>(VT_ARRAY_SIZE, 0);
  }
  bool ShaderResource::mutate_array_size (uint32_t _array_size) {
    return SetField<uint32_t>(VT_ARRAY_SIZE, _array_size, 0);
  }
uint32_t  ShaderResource::space() const{
    return GetField<uint32_t>(VT_SPACE, 0);
  }
  bool ShaderResource::mutate_space (uint32_t _space) {
    return SetField<uint32_t>(VT_SPACE, _space, 0);
  }
uint32_t  ShaderResource::index() const{
    return GetField<uint32_t>(VT_INDEX, 0);
  }
  bool ShaderResource::mutate_index (uint32_t _index) {
    return SetField<uint32_t>(VT_INDEX, _index, 0);
  }
uint32_t  ShaderResource::subpass_index() const{
    return GetField<uint32_t>(VT_SUBPASS_INDEX, 0);
  }
  bool ShaderResource::mutate_subpass_index (uint32_t _subpass_index) {
    return SetField<uint32_t>(VT_SUBPASS_INDEX, _subpass_index, 0);
  }
uint32_t  ShaderResource::stage_mask() const{
    return GetField<uint32_t>(VT_STAGE_MASK, 0);
  }
  bool ShaderResource::mutate_stage_mask (uint32_t _stage_mask) {
    return SetField<uint32_t>(VT_STAGE_MASK, _stage_mask, 0);
  }
CrossSchema::ShaderVariableType  ShaderResource::return_type() const{
    return static_cast<CrossSchema::ShaderVariableType>(GetField<uint32_t>(VT_RETURN_TYPE, 11));
  }
  bool ShaderResource::mutate_return_type (CrossSchema::ShaderVariableType _return_type) {
    return SetField<uint32_t>(VT_RETURN_TYPE, static_cast<uint32_t>(_return_type), 11);
  }
bool  ShaderResource::depth_texture() const{
    return GetField<uint8_t>(VT_DEPTH_TEXTURE, 0) != 0;
  }
  bool ShaderResource::mutate_depth_texture (bool _depth_texture) {
    return SetField<uint8_t>(VT_DEPTH_TEXTURE, static_cast<uint8_t>(_depth_texture), 0);
  }
const CrossSchema::ShaderStructType * ShaderResource::struct_type() const{
    return GetPointer<const CrossSchema::ShaderStructType *>(VT_STRUCT_TYPE);
  }
  CrossSchema::ShaderStructType * ShaderResource::mutable_struct_type() {
    return GetPointer<CrossSchema::ShaderStructType *>(VT_STRUCT_TYPE);
  }
 bool ShaderResource::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_ARRAY_SIZE) &&
           VerifyField<uint32_t>(verifier, VT_SPACE) &&
           VerifyField<uint32_t>(verifier, VT_INDEX) &&
           VerifyField<uint32_t>(verifier, VT_SUBPASS_INDEX) &&
           VerifyField<uint32_t>(verifier, VT_STAGE_MASK) &&
           VerifyField<uint32_t>(verifier, VT_RETURN_TYPE) &&
           VerifyField<uint8_t>(verifier, VT_DEPTH_TEXTURE) &&
           VerifyOffset(verifier, VT_STRUCT_TYPE) &&
           verifier.VerifyTable(struct_type()) &&
           verifier.EndTable();
  }
  void ShaderResourceBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ShaderResource::VT_NAME, name);
  }
  void ShaderResourceBuilder::add_type(CrossSchema::ShaderResourceType type) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_TYPE, static_cast<uint32_t>(type), 0);
  }
  void ShaderResourceBuilder::add_array_size(uint32_t array_size) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_ARRAY_SIZE, array_size, 0);
  }
  void ShaderResourceBuilder::add_space(uint32_t space) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_SPACE, space, 0);
  }
  void ShaderResourceBuilder::add_index(uint32_t index) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_INDEX, index, 0);
  }
  void ShaderResourceBuilder::add_subpass_index(uint32_t subpass_index) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_SUBPASS_INDEX, subpass_index, 0);
  }
  void ShaderResourceBuilder::add_stage_mask(uint32_t stage_mask) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_STAGE_MASK, stage_mask, 0);
  }
  void ShaderResourceBuilder::add_return_type(CrossSchema::ShaderVariableType return_type) {
    fbb_.AddElement<uint32_t>(ShaderResource::VT_RETURN_TYPE, static_cast<uint32_t>(return_type), 11);
  }
  void ShaderResourceBuilder::add_depth_texture(bool depth_texture) {
    fbb_.AddElement<uint8_t>(ShaderResource::VT_DEPTH_TEXTURE, static_cast<uint8_t>(depth_texture), 0);
  }
  void ShaderResourceBuilder::add_struct_type(flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type) {
    fbb_.AddOffset(ShaderResource::VT_STRUCT_TYPE, struct_type);
  }
flatbuffers::Offset<ShaderResource> CreateShaderResource(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    CrossSchema::ShaderResourceType type,
    uint32_t array_size,
    uint32_t space,
    uint32_t index,
    uint32_t subpass_index,
    uint32_t stage_mask,
    CrossSchema::ShaderVariableType return_type,
    bool depth_texture,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type){
    ShaderResourceBuilder builder_(_fbb);
  builder_.add_struct_type(struct_type);
  builder_.add_return_type(return_type);
  builder_.add_stage_mask(stage_mask);
  builder_.add_subpass_index(subpass_index);
  builder_.add_index(index);
  builder_.add_space(space);
  builder_.add_array_size(array_size);
  builder_.add_type(type);
  builder_.add_name(name);
  builder_.add_depth_texture(depth_texture);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderResource> CreateShaderResourceDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    CrossSchema::ShaderResourceType type,
    uint32_t array_size,
    uint32_t space,
    uint32_t index,
    uint32_t subpass_index,
    uint32_t stage_mask,
    CrossSchema::ShaderVariableType return_type,
    bool depth_texture,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return CrossSchema::CreateShaderResource(
      _fbb,
      name__,
      type,
      array_size,
      space,
      index,
      subpass_index,
      stage_mask,
      return_type,
      depth_texture,
      struct_type);
}

 const flatbuffers::TypeTable * ShaderConstantBuffer::MiniReflectTypeTable(){
    return ShaderConstantBufferTypeTable();
  }
const flatbuffers::String * ShaderConstantBuffer::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * ShaderConstantBuffer::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
bool ShaderConstantBuffer::KeyCompareLessThan(const ShaderConstantBuffer *o) const{
    return *name() < *o->name();
  }
int ShaderConstantBuffer::KeyCompareWithValue(const char *val) const {
    return strcmp(name()->c_str(), val);
  }
CrossSchema::ShaderResourceType  ShaderConstantBuffer::type() const{
    return static_cast<CrossSchema::ShaderResourceType>(GetField<uint32_t>(VT_TYPE, 0));
  }
  bool ShaderConstantBuffer::mutate_type (CrossSchema::ShaderResourceType _type) {
    return SetField<uint32_t>(VT_TYPE, static_cast<uint32_t>(_type), 0);
  }
uint32_t  ShaderConstantBuffer::size() const{
    return GetField<uint32_t>(VT_SIZE, 0);
  }
  bool ShaderConstantBuffer::mutate_size (uint32_t _size) {
    return SetField<uint32_t>(VT_SIZE, _size, 0);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderConstantBuffer::members() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_MEMBERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderConstantBuffer::mutable_members() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_MEMBERS);
  }
uint32_t  ShaderConstantBuffer::space() const{
    return GetField<uint32_t>(VT_SPACE, 0);
  }
  bool ShaderConstantBuffer::mutate_space (uint32_t _space) {
    return SetField<uint32_t>(VT_SPACE, _space, 0);
  }
uint32_t  ShaderConstantBuffer::index() const{
    return GetField<uint32_t>(VT_INDEX, 0);
  }
  bool ShaderConstantBuffer::mutate_index (uint32_t _index) {
    return SetField<uint32_t>(VT_INDEX, _index, 0);
  }
uint32_t  ShaderConstantBuffer::stage_mask() const{
    return GetField<uint32_t>(VT_STAGE_MASK, 0);
  }
  bool ShaderConstantBuffer::mutate_stage_mask (uint32_t _stage_mask) {
    return SetField<uint32_t>(VT_STAGE_MASK, _stage_mask, 0);
  }
uint32_t  ShaderConstantBuffer::array_size() const{
    return GetField<uint32_t>(VT_ARRAY_SIZE, 1);
  }
  bool ShaderConstantBuffer::mutate_array_size (uint32_t _array_size) {
    return SetField<uint32_t>(VT_ARRAY_SIZE, _array_size, 1);
  }
const CrossSchema::ShaderStructType * ShaderConstantBuffer::struct_type() const{
    return GetPointer<const CrossSchema::ShaderStructType *>(VT_STRUCT_TYPE);
  }
  CrossSchema::ShaderStructType * ShaderConstantBuffer::mutable_struct_type() {
    return GetPointer<CrossSchema::ShaderStructType *>(VT_STRUCT_TYPE);
  }
 bool ShaderConstantBuffer::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint32_t>(verifier, VT_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_SIZE) &&
           VerifyOffset(verifier, VT_MEMBERS) &&
           verifier.VerifyVector(members()) &&
           verifier.VerifyVectorOfTables(members()) &&
           VerifyField<uint32_t>(verifier, VT_SPACE) &&
           VerifyField<uint32_t>(verifier, VT_INDEX) &&
           VerifyField<uint32_t>(verifier, VT_STAGE_MASK) &&
           VerifyField<uint32_t>(verifier, VT_ARRAY_SIZE) &&
           VerifyOffset(verifier, VT_STRUCT_TYPE) &&
           verifier.VerifyTable(struct_type()) &&
           verifier.EndTable();
  }
  void ShaderConstantBufferBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(ShaderConstantBuffer::VT_NAME, name);
  }
  void ShaderConstantBufferBuilder::add_type(CrossSchema::ShaderResourceType type) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_TYPE, static_cast<uint32_t>(type), 0);
  }
  void ShaderConstantBufferBuilder::add_size(uint32_t size) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_SIZE, size, 0);
  }
  void ShaderConstantBufferBuilder::add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> members) {
    fbb_.AddOffset(ShaderConstantBuffer::VT_MEMBERS, members);
  }
  void ShaderConstantBufferBuilder::add_space(uint32_t space) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_SPACE, space, 0);
  }
  void ShaderConstantBufferBuilder::add_index(uint32_t index) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_INDEX, index, 0);
  }
  void ShaderConstantBufferBuilder::add_stage_mask(uint32_t stage_mask) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_STAGE_MASK, stage_mask, 0);
  }
  void ShaderConstantBufferBuilder::add_array_size(uint32_t array_size) {
    fbb_.AddElement<uint32_t>(ShaderConstantBuffer::VT_ARRAY_SIZE, array_size, 1);
  }
  void ShaderConstantBufferBuilder::add_struct_type(flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type) {
    fbb_.AddOffset(ShaderConstantBuffer::VT_STRUCT_TYPE, struct_type);
  }
flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBuffer(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    CrossSchema::ShaderResourceType type,
    uint32_t size,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> members,
    uint32_t space,
    uint32_t index,
    uint32_t stage_mask,
    uint32_t array_size,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type){
    ShaderConstantBufferBuilder builder_(_fbb);
  builder_.add_struct_type(struct_type);
  builder_.add_array_size(array_size);
  builder_.add_stage_mask(stage_mask);
  builder_.add_index(index);
  builder_.add_space(space);
  builder_.add_members(members);
  builder_.add_size(size);
  builder_.add_type(type);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBufferDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    CrossSchema::ShaderResourceType type,
    uint32_t size,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *members,
    uint32_t space,
    uint32_t index,
    uint32_t stage_mask,
    uint32_t array_size,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto members__ = members ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderVariable>(members) : 0;
  return CrossSchema::CreateShaderConstantBuffer(
      _fbb,
      name__,
      type,
      size,
      members__,
      space,
      index,
      stage_mask,
      array_size,
      struct_type);
}

 const flatbuffers::TypeTable * CombinedSampler::MiniReflectTypeTable(){
    return CombinedSamplerTypeTable();
  }
const flatbuffers::String * CombinedSampler::name() const{
    return GetPointer<const flatbuffers::String *>(VT_NAME);
  }
  flatbuffers::String * CombinedSampler::mutable_name() {
    return GetPointer<flatbuffers::String *>(VT_NAME);
  }
const flatbuffers::String * CombinedSampler::texture_name() const{
    return GetPointer<const flatbuffers::String *>(VT_TEXTURE_NAME);
  }
  flatbuffers::String * CombinedSampler::mutable_texture_name() {
    return GetPointer<flatbuffers::String *>(VT_TEXTURE_NAME);
  }
const flatbuffers::String * CombinedSampler::sampler_name() const{
    return GetPointer<const flatbuffers::String *>(VT_SAMPLER_NAME);
  }
  flatbuffers::String * CombinedSampler::mutable_sampler_name() {
    return GetPointer<flatbuffers::String *>(VT_SAMPLER_NAME);
  }
uint32_t  CombinedSampler::stage_mask() const{
    return GetField<uint32_t>(VT_STAGE_MASK, 0);
  }
  bool CombinedSampler::mutate_stage_mask (uint32_t _stage_mask) {
    return SetField<uint32_t>(VT_STAGE_MASK, _stage_mask, 0);
  }
 bool CombinedSampler::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_TEXTURE_NAME) &&
           verifier.VerifyString(texture_name()) &&
           VerifyOffset(verifier, VT_SAMPLER_NAME) &&
           verifier.VerifyString(sampler_name()) &&
           VerifyField<uint32_t>(verifier, VT_STAGE_MASK) &&
           verifier.EndTable();
  }
  void CombinedSamplerBuilder::add_name(flatbuffers::Offset<flatbuffers::String> name) {
    fbb_.AddOffset(CombinedSampler::VT_NAME, name);
  }
  void CombinedSamplerBuilder::add_texture_name(flatbuffers::Offset<flatbuffers::String> texture_name) {
    fbb_.AddOffset(CombinedSampler::VT_TEXTURE_NAME, texture_name);
  }
  void CombinedSamplerBuilder::add_sampler_name(flatbuffers::Offset<flatbuffers::String> sampler_name) {
    fbb_.AddOffset(CombinedSampler::VT_SAMPLER_NAME, sampler_name);
  }
  void CombinedSamplerBuilder::add_stage_mask(uint32_t stage_mask) {
    fbb_.AddElement<uint32_t>(CombinedSampler::VT_STAGE_MASK, stage_mask, 0);
  }
flatbuffers::Offset<CombinedSampler> CreateCombinedSampler(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name,
    flatbuffers::Offset<flatbuffers::String> texture_name,
    flatbuffers::Offset<flatbuffers::String> sampler_name,
    uint32_t stage_mask){
    CombinedSamplerBuilder builder_(_fbb);
  builder_.add_stage_mask(stage_mask);
  builder_.add_sampler_name(sampler_name);
  builder_.add_texture_name(texture_name);
  builder_.add_name(name);
  return builder_.Finish();
}

flatbuffers::Offset<CombinedSampler> CreateCombinedSamplerDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name,
    const char *texture_name,
    const char *sampler_name,
    uint32_t stage_mask) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto texture_name__ = texture_name ? _fbb.CreateString(texture_name) : 0;
  auto sampler_name__ = sampler_name ? _fbb.CreateString(sampler_name) : 0;
  return CrossSchema::CreateCombinedSampler(
      _fbb,
      name__,
      texture_name__,
      sampler_name__,
      stage_mask);
}

 const flatbuffers::TypeTable * ShaderLayout::MiniReflectTypeTable(){
    return ShaderLayoutTypeTable();
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> * ShaderLayout::constant_buffers() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *>(VT_CONSTANT_BUFFERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> * ShaderLayout::mutable_constant_buffers() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *>(VT_CONSTANT_BUFFERS);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> * ShaderLayout::resources() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *>(VT_RESOURCES);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> * ShaderLayout::mutable_resources() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *>(VT_RESOURCES);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> * ShaderLayout::combined_samplers() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *>(VT_COMBINED_SAMPLERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> * ShaderLayout::mutable_combined_samplers() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *>(VT_COMBINED_SAMPLERS);
  }
const CrossSchema::ShaderConstantBuffer * ShaderLayout::specialization_constants() const{
    return GetPointer<const CrossSchema::ShaderConstantBuffer *>(VT_SPECIALIZATION_CONSTANTS);
  }
  CrossSchema::ShaderConstantBuffer * ShaderLayout::mutable_specialization_constants() {
    return GetPointer<CrossSchema::ShaderConstantBuffer *>(VT_SPECIALIZATION_CONSTANTS);
  }
 bool ShaderLayout::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CONSTANT_BUFFERS) &&
           verifier.VerifyVector(constant_buffers()) &&
           verifier.VerifyVectorOfTables(constant_buffers()) &&
           VerifyOffset(verifier, VT_RESOURCES) &&
           verifier.VerifyVector(resources()) &&
           verifier.VerifyVectorOfTables(resources()) &&
           VerifyOffset(verifier, VT_COMBINED_SAMPLERS) &&
           verifier.VerifyVector(combined_samplers()) &&
           verifier.VerifyVectorOfTables(combined_samplers()) &&
           VerifyOffset(verifier, VT_SPECIALIZATION_CONSTANTS) &&
           verifier.VerifyTable(specialization_constants()) &&
           verifier.EndTable();
  }
  void ShaderLayoutBuilder::add_constant_buffers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>>> constant_buffers) {
    fbb_.AddOffset(ShaderLayout::VT_CONSTANT_BUFFERS, constant_buffers);
  }
  void ShaderLayoutBuilder::add_resources(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>>> resources) {
    fbb_.AddOffset(ShaderLayout::VT_RESOURCES, resources);
  }
  void ShaderLayoutBuilder::add_combined_samplers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>>> combined_samplers) {
    fbb_.AddOffset(ShaderLayout::VT_COMBINED_SAMPLERS, combined_samplers);
  }
  void ShaderLayoutBuilder::add_specialization_constants(flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants) {
    fbb_.AddOffset(ShaderLayout::VT_SPECIALIZATION_CONSTANTS, specialization_constants);
  }
flatbuffers::Offset<ShaderLayout> CreateShaderLayout(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>>> constant_buffers,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>>> resources,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>>> combined_samplers,
    flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants){
    ShaderLayoutBuilder builder_(_fbb);
  builder_.add_specialization_constants(specialization_constants);
  builder_.add_combined_samplers(combined_samplers);
  builder_.add_resources(resources);
  builder_.add_constant_buffers(constant_buffers);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderLayout> CreateShaderLayoutDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *constant_buffers,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *resources,
    const std::vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *combined_samplers,
    flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants) {
  auto constant_buffers__ = constant_buffers ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderConstantBuffer>(constant_buffers) : 0;
  auto resources__ = resources ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderResource>(resources) : 0;
  auto combined_samplers__ = combined_samplers ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::CombinedSampler>>(*combined_samplers) : 0;
  return CrossSchema::CreateShaderLayout(
      _fbb,
      constant_buffers__,
      resources__,
      combined_samplers__,
      specialization_constants);
}

 const flatbuffers::TypeTable * ShaderCode::MiniReflectTypeTable(){
    return ShaderCodeTypeTable();
  }
CrossSchema::ShaderStageBit  ShaderCode::stage() const{
    return static_cast<CrossSchema::ShaderStageBit>(GetField<uint32_t>(VT_STAGE, 0));
  }
  bool ShaderCode::mutate_stage (CrossSchema::ShaderStageBit _stage) {
    return SetField<uint32_t>(VT_STAGE, static_cast<uint32_t>(_stage), 0);
  }
const flatbuffers::String * ShaderCode::entry_point() const{
    return GetPointer<const flatbuffers::String *>(VT_ENTRY_POINT);
  }
  flatbuffers::String * ShaderCode::mutable_entry_point() {
    return GetPointer<flatbuffers::String *>(VT_ENTRY_POINT);
  }
const flatbuffers::Vector<uint8_t> * ShaderCode::code_data() const{
    return GetPointer<const flatbuffers::Vector<uint8_t> *>(VT_CODE_DATA);
  }
  flatbuffers::Vector<uint8_t> * ShaderCode::mutable_code_data() {
    return GetPointer<flatbuffers::Vector<uint8_t> *>(VT_CODE_DATA);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderCode::stage_inputs() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_STAGE_INPUTS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderCode::mutable_stage_inputs() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_STAGE_INPUTS);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderCode::stage_outputs() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_STAGE_OUTPUTS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> * ShaderCode::mutable_stage_outputs() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *>(VT_STAGE_OUTPUTS);
  }
bool  ShaderCode::debug_symbol() const{
    return GetField<uint8_t>(VT_DEBUG_SYMBOL, 0) != 0;
  }
  bool ShaderCode::mutate_debug_symbol (bool _debug_symbol) {
    return SetField<uint8_t>(VT_DEBUG_SYMBOL, static_cast<uint8_t>(_debug_symbol), 0);
  }
 bool ShaderCode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_STAGE) &&
           VerifyOffset(verifier, VT_ENTRY_POINT) &&
           verifier.VerifyString(entry_point()) &&
           VerifyOffset(verifier, VT_CODE_DATA) &&
           verifier.VerifyVector(code_data()) &&
           VerifyOffset(verifier, VT_STAGE_INPUTS) &&
           verifier.VerifyVector(stage_inputs()) &&
           verifier.VerifyVectorOfTables(stage_inputs()) &&
           VerifyOffset(verifier, VT_STAGE_OUTPUTS) &&
           verifier.VerifyVector(stage_outputs()) &&
           verifier.VerifyVectorOfTables(stage_outputs()) &&
           VerifyField<uint8_t>(verifier, VT_DEBUG_SYMBOL) &&
           verifier.EndTable();
  }
  void ShaderCodeBuilder::add_stage(CrossSchema::ShaderStageBit stage) {
    fbb_.AddElement<uint32_t>(ShaderCode::VT_STAGE, static_cast<uint32_t>(stage), 0);
  }
  void ShaderCodeBuilder::add_entry_point(flatbuffers::Offset<flatbuffers::String> entry_point) {
    fbb_.AddOffset(ShaderCode::VT_ENTRY_POINT, entry_point);
  }
  void ShaderCodeBuilder::add_code_data(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> code_data) {
    fbb_.AddOffset(ShaderCode::VT_CODE_DATA, code_data);
  }
  void ShaderCodeBuilder::add_stage_inputs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_inputs) {
    fbb_.AddOffset(ShaderCode::VT_STAGE_INPUTS, stage_inputs);
  }
  void ShaderCodeBuilder::add_stage_outputs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_outputs) {
    fbb_.AddOffset(ShaderCode::VT_STAGE_OUTPUTS, stage_outputs);
  }
  void ShaderCodeBuilder::add_debug_symbol(bool debug_symbol) {
    fbb_.AddElement<uint8_t>(ShaderCode::VT_DEBUG_SYMBOL, static_cast<uint8_t>(debug_symbol), 0);
  }
flatbuffers::Offset<ShaderCode> CreateShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    CrossSchema::ShaderStageBit stage,
    flatbuffers::Offset<flatbuffers::String> entry_point,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> code_data,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_inputs,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_outputs,
    bool debug_symbol){
    ShaderCodeBuilder builder_(_fbb);
  builder_.add_stage_outputs(stage_outputs);
  builder_.add_stage_inputs(stage_inputs);
  builder_.add_code_data(code_data);
  builder_.add_entry_point(entry_point);
  builder_.add_stage(stage);
  builder_.add_debug_symbol(debug_symbol);
  return builder_.Finish();
}

flatbuffers::Offset<ShaderCode> CreateShaderCodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    CrossSchema::ShaderStageBit stage,
    const char *entry_point,
    const std::vector<uint8_t> *code_data,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_inputs,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_outputs,
    bool debug_symbol) {
  auto entry_point__ = entry_point ? _fbb.CreateString(entry_point) : 0;
  auto code_data__ = code_data ? _fbb.CreateVector<uint8_t>(*code_data) : 0;
  auto stage_inputs__ = stage_inputs ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderVariable>(stage_inputs) : 0;
  auto stage_outputs__ = stage_outputs ? _fbb.CreateVectorOfSortedTables<CrossSchema::ShaderVariable>(stage_outputs) : 0;
  return CrossSchema::CreateShaderCode(
      _fbb,
      stage,
      entry_point__,
      code_data__,
      stage_inputs__,
      stage_outputs__,
      debug_symbol);
}

 const flatbuffers::TypeTable * GraphicsShaderCode::MiniReflectTypeTable(){
    return GraphicsShaderCodeTypeTable();
  }
const CrossSchema::GUID * GraphicsShaderCode::guid() const{
    return GetStruct<const CrossSchema::GUID *>(VT_GUID);
  }
  CrossSchema::GUID * GraphicsShaderCode::mutable_guid() {
    return GetStruct<CrossSchema::GUID *>(VT_GUID);
  }
uint64_t  GraphicsShaderCode::active_keywords() const{
    return GetField<uint64_t>(VT_ACTIVE_KEYWORDS, 0);
  }
  bool GraphicsShaderCode::mutate_active_keywords (uint64_t _active_keywords) {
    return SetField<uint64_t>(VT_ACTIVE_KEYWORDS, _active_keywords, 0);
  }
const CrossSchema::ShaderLayout * GraphicsShaderCode::layout() const{
    return GetPointer<const CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
  CrossSchema::ShaderLayout * GraphicsShaderCode::mutable_layout() {
    return GetPointer<CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::vertex_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_VERTEX_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_vertex_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_VERTEX_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::hull_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_HULL_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_hull_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_HULL_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::domain_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_DOMAIN_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_domain_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_DOMAIN_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::geometry_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_GEOMETRY_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_geometry_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_GEOMETRY_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::pixel_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_PIXEL_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_pixel_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_PIXEL_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::task_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_TASK_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_task_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_TASK_SHADER);
  }
const CrossSchema::ShaderCode * GraphicsShaderCode::mesh_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_MESH_SHADER);
  }
  CrossSchema::ShaderCode * GraphicsShaderCode::mutable_mesh_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_MESH_SHADER);
  }
uint64_t  GraphicsShaderCode::mtime() const{
    return GetField<uint64_t>(VT_MTIME, 0);
  }
  bool GraphicsShaderCode::mutate_mtime (uint64_t _mtime) {
    return SetField<uint64_t>(VT_MTIME, _mtime, 0);
  }
 bool GraphicsShaderCode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::GUID>(verifier, VT_GUID) &&
           VerifyField<uint64_t>(verifier, VT_ACTIVE_KEYWORDS) &&
           VerifyOffset(verifier, VT_LAYOUT) &&
           verifier.VerifyTable(layout()) &&
           VerifyOffset(verifier, VT_VERTEX_SHADER) &&
           verifier.VerifyTable(vertex_shader()) &&
           VerifyOffset(verifier, VT_HULL_SHADER) &&
           verifier.VerifyTable(hull_shader()) &&
           VerifyOffset(verifier, VT_DOMAIN_SHADER) &&
           verifier.VerifyTable(domain_shader()) &&
           VerifyOffset(verifier, VT_GEOMETRY_SHADER) &&
           verifier.VerifyTable(geometry_shader()) &&
           VerifyOffset(verifier, VT_PIXEL_SHADER) &&
           verifier.VerifyTable(pixel_shader()) &&
           VerifyOffset(verifier, VT_TASK_SHADER) &&
           verifier.VerifyTable(task_shader()) &&
           VerifyOffset(verifier, VT_MESH_SHADER) &&
           verifier.VerifyTable(mesh_shader()) &&
           VerifyField<uint64_t>(verifier, VT_MTIME) &&
           verifier.EndTable();
  }
  void GraphicsShaderCodeBuilder::add_guid(const CrossSchema::GUID *guid) {
    fbb_.AddStruct(GraphicsShaderCode::VT_GUID, guid);
  }
  void GraphicsShaderCodeBuilder::add_active_keywords(uint64_t active_keywords) {
    fbb_.AddElement<uint64_t>(GraphicsShaderCode::VT_ACTIVE_KEYWORDS, active_keywords, 0);
  }
  void GraphicsShaderCodeBuilder::add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout) {
    fbb_.AddOffset(GraphicsShaderCode::VT_LAYOUT, layout);
  }
  void GraphicsShaderCodeBuilder::add_vertex_shader(flatbuffers::Offset<CrossSchema::ShaderCode> vertex_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_VERTEX_SHADER, vertex_shader);
  }
  void GraphicsShaderCodeBuilder::add_hull_shader(flatbuffers::Offset<CrossSchema::ShaderCode> hull_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_HULL_SHADER, hull_shader);
  }
  void GraphicsShaderCodeBuilder::add_domain_shader(flatbuffers::Offset<CrossSchema::ShaderCode> domain_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_DOMAIN_SHADER, domain_shader);
  }
  void GraphicsShaderCodeBuilder::add_geometry_shader(flatbuffers::Offset<CrossSchema::ShaderCode> geometry_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_GEOMETRY_SHADER, geometry_shader);
  }
  void GraphicsShaderCodeBuilder::add_pixel_shader(flatbuffers::Offset<CrossSchema::ShaderCode> pixel_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_PIXEL_SHADER, pixel_shader);
  }
  void GraphicsShaderCodeBuilder::add_task_shader(flatbuffers::Offset<CrossSchema::ShaderCode> task_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_TASK_SHADER, task_shader);
  }
  void GraphicsShaderCodeBuilder::add_mesh_shader(flatbuffers::Offset<CrossSchema::ShaderCode> mesh_shader) {
    fbb_.AddOffset(GraphicsShaderCode::VT_MESH_SHADER, mesh_shader);
  }
  void GraphicsShaderCodeBuilder::add_mtime(uint64_t mtime) {
    fbb_.AddElement<uint64_t>(GraphicsShaderCode::VT_MTIME, mtime, 0);
  }
flatbuffers::Offset<GraphicsShaderCode> CreateGraphicsShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid,
    uint64_t active_keywords,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout,
    flatbuffers::Offset<CrossSchema::ShaderCode> vertex_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> hull_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> domain_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> geometry_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> pixel_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> task_shader,
    flatbuffers::Offset<CrossSchema::ShaderCode> mesh_shader,
    uint64_t mtime){
    GraphicsShaderCodeBuilder builder_(_fbb);
  builder_.add_mtime(mtime);
  builder_.add_active_keywords(active_keywords);
  builder_.add_mesh_shader(mesh_shader);
  builder_.add_task_shader(task_shader);
  builder_.add_pixel_shader(pixel_shader);
  builder_.add_geometry_shader(geometry_shader);
  builder_.add_domain_shader(domain_shader);
  builder_.add_hull_shader(hull_shader);
  builder_.add_vertex_shader(vertex_shader);
  builder_.add_layout(layout);
  builder_.add_guid(guid);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * PlatformGraphicsShader::MiniReflectTypeTable(){
    return PlatformGraphicsShaderTypeTable();
  }
const CrossSchema::ShaderVersion * PlatformGraphicsShader::version() const{
    return GetStruct<const CrossSchema::ShaderVersion *>(VT_VERSION);
  }
  CrossSchema::ShaderVersion * PlatformGraphicsShader::mutable_version() {
    return GetStruct<CrossSchema::ShaderVersion *>(VT_VERSION);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * PlatformGraphicsShader::keywords() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_KEYWORDS);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * PlatformGraphicsShader::mutable_keywords() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_KEYWORDS);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> * PlatformGraphicsShader::variants() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *>(VT_VARIANTS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> * PlatformGraphicsShader::mutable_variants() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *>(VT_VARIANTS);
  }
 bool PlatformGraphicsShader::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::ShaderVersion>(verifier, VT_VERSION) &&
           VerifyOffset(verifier, VT_KEYWORDS) &&
           verifier.VerifyVector(keywords()) &&
           verifier.VerifyVectorOfStrings(keywords()) &&
           VerifyOffset(verifier, VT_VARIANTS) &&
           verifier.VerifyVector(variants()) &&
           verifier.VerifyVectorOfTables(variants()) &&
           verifier.EndTable();
  }
  void PlatformGraphicsShaderBuilder::add_version(const CrossSchema::ShaderVersion *version) {
    fbb_.AddStruct(PlatformGraphicsShader::VT_VERSION, version);
  }
  void PlatformGraphicsShaderBuilder::add_keywords(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> keywords) {
    fbb_.AddOffset(PlatformGraphicsShader::VT_KEYWORDS, keywords);
  }
  void PlatformGraphicsShaderBuilder::add_variants(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>>> variants) {
    fbb_.AddOffset(PlatformGraphicsShader::VT_VARIANTS, variants);
  }
flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> keywords,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>>> variants){
    PlatformGraphicsShaderBuilder builder_(_fbb);
  builder_.add_variants(variants);
  builder_.add_keywords(keywords);
  builder_.add_version(version);
  return builder_.Finish();
}

flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShaderDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *keywords,
    const std::vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *variants) {
  auto keywords__ = keywords ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*keywords) : 0;
  auto variants__ = variants ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>>(*variants) : 0;
  return CrossSchema::CreatePlatformGraphicsShader(
      _fbb,
      version,
      keywords__,
      variants__);
}

 const flatbuffers::TypeTable * GraphicsShaderAsset::MiniReflectTypeTable(){
    return GraphicsShaderAssetTypeTable();
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> * GraphicsShaderAsset::platform_shaders() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *>(VT_PLATFORM_SHADERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> * GraphicsShaderAsset::mutable_platform_shaders() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *>(VT_PLATFORM_SHADERS);
  }
uint64_t  GraphicsShaderAsset::mtime() const{
    return GetField<uint64_t>(VT_MTIME, 0);
  }
  bool GraphicsShaderAsset::mutate_mtime (uint64_t _mtime) {
    return SetField<uint64_t>(VT_MTIME, _mtime, 0);
  }
 bool GraphicsShaderAsset::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLATFORM_SHADERS) &&
           verifier.VerifyVector(platform_shaders()) &&
           verifier.VerifyVectorOfTables(platform_shaders()) &&
           VerifyField<uint64_t>(verifier, VT_MTIME) &&
           verifier.EndTable();
  }
  void GraphicsShaderAssetBuilder::add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>>> platform_shaders) {
    fbb_.AddOffset(GraphicsShaderAsset::VT_PLATFORM_SHADERS, platform_shaders);
  }
  void GraphicsShaderAssetBuilder::add_mtime(uint64_t mtime) {
    fbb_.AddElement<uint64_t>(GraphicsShaderAsset::VT_MTIME, mtime, 0);
  }
flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>>> platform_shaders,
    uint64_t mtime){
    GraphicsShaderAssetBuilder builder_(_fbb);
  builder_.add_mtime(mtime);
  builder_.add_platform_shaders(platform_shaders);
  return builder_.Finish();
}

flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *platform_shaders,
    uint64_t mtime) {
  auto platform_shaders__ = platform_shaders ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>>(*platform_shaders) : 0;
  return CrossSchema::CreateGraphicsShaderAsset(
      _fbb,
      platform_shaders__,
      mtime);
}

 const flatbuffers::TypeTable * ComputeShaderCode::MiniReflectTypeTable(){
    return ComputeShaderCodeTypeTable();
  }
const CrossSchema::GUID * ComputeShaderCode::guid() const{
    return GetStruct<const CrossSchema::GUID *>(VT_GUID);
  }
  CrossSchema::GUID * ComputeShaderCode::mutable_guid() {
    return GetStruct<CrossSchema::GUID *>(VT_GUID);
  }
const CrossSchema::ShaderLayout * ComputeShaderCode::layout() const{
    return GetPointer<const CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
  CrossSchema::ShaderLayout * ComputeShaderCode::mutable_layout() {
    return GetPointer<CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
const CrossSchema::uint3 * ComputeShaderCode::group_size() const{
    return GetStruct<const CrossSchema::uint3 *>(VT_GROUP_SIZE);
  }
  CrossSchema::uint3 * ComputeShaderCode::mutable_group_size() {
    return GetStruct<CrossSchema::uint3 *>(VT_GROUP_SIZE);
  }
const CrossSchema::ShaderCode * ComputeShaderCode::compute_shader() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_COMPUTE_SHADER);
  }
  CrossSchema::ShaderCode * ComputeShaderCode::mutable_compute_shader() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_COMPUTE_SHADER);
  }
uint64_t  ComputeShaderCode::mtime() const{
    return GetField<uint64_t>(VT_MTIME, 0);
  }
  bool ComputeShaderCode::mutate_mtime (uint64_t _mtime) {
    return SetField<uint64_t>(VT_MTIME, _mtime, 0);
  }
 bool ComputeShaderCode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::GUID>(verifier, VT_GUID) &&
           VerifyOffset(verifier, VT_LAYOUT) &&
           verifier.VerifyTable(layout()) &&
           VerifyField<CrossSchema::uint3>(verifier, VT_GROUP_SIZE) &&
           VerifyOffset(verifier, VT_COMPUTE_SHADER) &&
           verifier.VerifyTable(compute_shader()) &&
           VerifyField<uint64_t>(verifier, VT_MTIME) &&
           verifier.EndTable();
  }
  void ComputeShaderCodeBuilder::add_guid(const CrossSchema::GUID *guid) {
    fbb_.AddStruct(ComputeShaderCode::VT_GUID, guid);
  }
  void ComputeShaderCodeBuilder::add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout) {
    fbb_.AddOffset(ComputeShaderCode::VT_LAYOUT, layout);
  }
  void ComputeShaderCodeBuilder::add_group_size(const CrossSchema::uint3 *group_size) {
    fbb_.AddStruct(ComputeShaderCode::VT_GROUP_SIZE, group_size);
  }
  void ComputeShaderCodeBuilder::add_compute_shader(flatbuffers::Offset<CrossSchema::ShaderCode> compute_shader) {
    fbb_.AddOffset(ComputeShaderCode::VT_COMPUTE_SHADER, compute_shader);
  }
  void ComputeShaderCodeBuilder::add_mtime(uint64_t mtime) {
    fbb_.AddElement<uint64_t>(ComputeShaderCode::VT_MTIME, mtime, 0);
  }
flatbuffers::Offset<ComputeShaderCode> CreateComputeShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout,
    const CrossSchema::uint3 *group_size,
    flatbuffers::Offset<CrossSchema::ShaderCode> compute_shader,
    uint64_t mtime){
    ComputeShaderCodeBuilder builder_(_fbb);
  builder_.add_mtime(mtime);
  builder_.add_compute_shader(compute_shader);
  builder_.add_group_size(group_size);
  builder_.add_layout(layout);
  builder_.add_guid(guid);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * PlatformComputeShader::MiniReflectTypeTable(){
    return PlatformComputeShaderTypeTable();
  }
const CrossSchema::ShaderVersion * PlatformComputeShader::version() const{
    return GetStruct<const CrossSchema::ShaderVersion *>(VT_VERSION);
  }
  CrossSchema::ShaderVersion * PlatformComputeShader::mutable_version() {
    return GetStruct<CrossSchema::ShaderVersion *>(VT_VERSION);
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> * PlatformComputeShader::code_list() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *>(VT_CODE_LIST);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> * PlatformComputeShader::mutable_code_list() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *>(VT_CODE_LIST);
  }
 bool PlatformComputeShader::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::ShaderVersion>(verifier, VT_VERSION) &&
           VerifyOffset(verifier, VT_CODE_LIST) &&
           verifier.VerifyVector(code_list()) &&
           verifier.VerifyVectorOfTables(code_list()) &&
           verifier.EndTable();
  }
  void PlatformComputeShaderBuilder::add_version(const CrossSchema::ShaderVersion *version) {
    fbb_.AddStruct(PlatformComputeShader::VT_VERSION, version);
  }
  void PlatformComputeShaderBuilder::add_code_list(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>>> code_list) {
    fbb_.AddOffset(PlatformComputeShader::VT_CODE_LIST, code_list);
  }
flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>>> code_list){
    PlatformComputeShaderBuilder builder_(_fbb);
  builder_.add_code_list(code_list);
  builder_.add_version(version);
  return builder_.Finish();
}

flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShaderDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version,
    const std::vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *code_list) {
  auto code_list__ = code_list ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>>(*code_list) : 0;
  return CrossSchema::CreatePlatformComputeShader(
      _fbb,
      version,
      code_list__);
}

 const flatbuffers::TypeTable * ComputeShaderAsset::MiniReflectTypeTable(){
    return ComputeShaderAssetTypeTable();
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> * ComputeShaderAsset::platform_shaders() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *>(VT_PLATFORM_SHADERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> * ComputeShaderAsset::mutable_platform_shaders() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *>(VT_PLATFORM_SHADERS);
  }
 bool ComputeShaderAsset::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLATFORM_SHADERS) &&
           verifier.VerifyVector(platform_shaders()) &&
           verifier.VerifyVectorOfTables(platform_shaders()) &&
           verifier.EndTable();
  }
  void ComputeShaderAssetBuilder::add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>>> platform_shaders) {
    fbb_.AddOffset(ComputeShaderAsset::VT_PLATFORM_SHADERS, platform_shaders);
  }
flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>>> platform_shaders){
    ComputeShaderAssetBuilder builder_(_fbb);
  builder_.add_platform_shaders(platform_shaders);
  return builder_.Finish();
}

flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *platform_shaders) {
  auto platform_shaders__ = platform_shaders ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>>(*platform_shaders) : 0;
  return CrossSchema::CreateComputeShaderAsset(
      _fbb,
      platform_shaders__);
}

 const flatbuffers::TypeTable * DXILReflectionCode::MiniReflectTypeTable(){
    return DXILReflectionCodeTypeTable();
  }
const CrossSchema::uint3 * DXILReflectionCode::group_size() const{
    return GetStruct<const CrossSchema::uint3 *>(VT_GROUP_SIZE);
  }
  CrossSchema::uint3 * DXILReflectionCode::mutable_group_size() {
    return GetStruct<CrossSchema::uint3 *>(VT_GROUP_SIZE);
  }
const CrossSchema::ShaderLayout * DXILReflectionCode::layout() const{
    return GetPointer<const CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
  CrossSchema::ShaderLayout * DXILReflectionCode::mutable_layout() {
    return GetPointer<CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
 bool DXILReflectionCode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::uint3>(verifier, VT_GROUP_SIZE) &&
           VerifyOffset(verifier, VT_LAYOUT) &&
           verifier.VerifyTable(layout()) &&
           verifier.EndTable();
  }
  void DXILReflectionCodeBuilder::add_group_size(const CrossSchema::uint3 *group_size) {
    fbb_.AddStruct(DXILReflectionCode::VT_GROUP_SIZE, group_size);
  }
  void DXILReflectionCodeBuilder::add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout) {
    fbb_.AddOffset(DXILReflectionCode::VT_LAYOUT, layout);
  }
flatbuffers::Offset<DXILReflectionCode> CreateDXILReflectionCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::uint3 *group_size,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout){
    DXILReflectionCodeBuilder builder_(_fbb);
  builder_.add_layout(layout);
  builder_.add_group_size(group_size);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * RayTracingShaderCode::MiniReflectTypeTable(){
    return RayTracingShaderCodeTypeTable();
  }
const CrossSchema::GUID * RayTracingShaderCode::guid() const{
    return GetStruct<const CrossSchema::GUID *>(VT_GUID);
  }
  CrossSchema::GUID * RayTracingShaderCode::mutable_guid() {
    return GetStruct<CrossSchema::GUID *>(VT_GUID);
  }
const CrossSchema::ShaderLayout * RayTracingShaderCode::layout() const{
    return GetPointer<const CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
  CrossSchema::ShaderLayout * RayTracingShaderCode::mutable_layout() {
    return GetPointer<CrossSchema::ShaderLayout *>(VT_LAYOUT);
  }
const CrossSchema::ShaderCode * RayTracingShaderCode::code() const{
    return GetPointer<const CrossSchema::ShaderCode *>(VT_CODE);
  }
  CrossSchema::ShaderCode * RayTracingShaderCode::mutable_code() {
    return GetPointer<CrossSchema::ShaderCode *>(VT_CODE);
  }
const flatbuffers::String * RayTracingShaderCode::raygen_shader_entry() const{
    return GetPointer<const flatbuffers::String *>(VT_RAYGEN_SHADER_ENTRY);
  }
  flatbuffers::String * RayTracingShaderCode::mutable_raygen_shader_entry() {
    return GetPointer<flatbuffers::String *>(VT_RAYGEN_SHADER_ENTRY);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::closesthit_shader_entries() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_CLOSESTHIT_SHADER_ENTRIES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::mutable_closesthit_shader_entries() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_CLOSESTHIT_SHADER_ENTRIES);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::anyhit_shader_entries() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_ANYHIT_SHADER_ENTRIES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::mutable_anyhit_shader_entries() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_ANYHIT_SHADER_ENTRIES);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::miss_shader_entries() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_MISS_SHADER_ENTRIES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::mutable_miss_shader_entries() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_MISS_SHADER_ENTRIES);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::callable_shader_entries() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_CALLABLE_SHADER_ENTRIES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::mutable_callable_shader_entries() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_CALLABLE_SHADER_ENTRIES);
  }
const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::intersection_shader_entries() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_INTERSECTION_SHADER_ENTRIES);
  }
  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> * RayTracingShaderCode::mutable_intersection_shader_entries() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *>(VT_INTERSECTION_SHADER_ENTRIES);
  }
uint64_t  RayTracingShaderCode::mtime() const{
    return GetField<uint64_t>(VT_MTIME, 0);
  }
  bool RayTracingShaderCode::mutate_mtime (uint64_t _mtime) {
    return SetField<uint64_t>(VT_MTIME, _mtime, 0);
  }
 bool RayTracingShaderCode::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::GUID>(verifier, VT_GUID) &&
           VerifyOffset(verifier, VT_LAYOUT) &&
           verifier.VerifyTable(layout()) &&
           VerifyOffset(verifier, VT_CODE) &&
           verifier.VerifyTable(code()) &&
           VerifyOffset(verifier, VT_RAYGEN_SHADER_ENTRY) &&
           verifier.VerifyString(raygen_shader_entry()) &&
           VerifyOffset(verifier, VT_CLOSESTHIT_SHADER_ENTRIES) &&
           verifier.VerifyVector(closesthit_shader_entries()) &&
           verifier.VerifyVectorOfStrings(closesthit_shader_entries()) &&
           VerifyOffset(verifier, VT_ANYHIT_SHADER_ENTRIES) &&
           verifier.VerifyVector(anyhit_shader_entries()) &&
           verifier.VerifyVectorOfStrings(anyhit_shader_entries()) &&
           VerifyOffset(verifier, VT_MISS_SHADER_ENTRIES) &&
           verifier.VerifyVector(miss_shader_entries()) &&
           verifier.VerifyVectorOfStrings(miss_shader_entries()) &&
           VerifyOffset(verifier, VT_CALLABLE_SHADER_ENTRIES) &&
           verifier.VerifyVector(callable_shader_entries()) &&
           verifier.VerifyVectorOfStrings(callable_shader_entries()) &&
           VerifyOffset(verifier, VT_INTERSECTION_SHADER_ENTRIES) &&
           verifier.VerifyVector(intersection_shader_entries()) &&
           verifier.VerifyVectorOfStrings(intersection_shader_entries()) &&
           VerifyField<uint64_t>(verifier, VT_MTIME) &&
           verifier.EndTable();
  }
  void RayTracingShaderCodeBuilder::add_guid(const CrossSchema::GUID *guid) {
    fbb_.AddStruct(RayTracingShaderCode::VT_GUID, guid);
  }
  void RayTracingShaderCodeBuilder::add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout) {
    fbb_.AddOffset(RayTracingShaderCode::VT_LAYOUT, layout);
  }
  void RayTracingShaderCodeBuilder::add_code(flatbuffers::Offset<CrossSchema::ShaderCode> code) {
    fbb_.AddOffset(RayTracingShaderCode::VT_CODE, code);
  }
  void RayTracingShaderCodeBuilder::add_raygen_shader_entry(flatbuffers::Offset<flatbuffers::String> raygen_shader_entry) {
    fbb_.AddOffset(RayTracingShaderCode::VT_RAYGEN_SHADER_ENTRY, raygen_shader_entry);
  }
  void RayTracingShaderCodeBuilder::add_closesthit_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> closesthit_shader_entries) {
    fbb_.AddOffset(RayTracingShaderCode::VT_CLOSESTHIT_SHADER_ENTRIES, closesthit_shader_entries);
  }
  void RayTracingShaderCodeBuilder::add_anyhit_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> anyhit_shader_entries) {
    fbb_.AddOffset(RayTracingShaderCode::VT_ANYHIT_SHADER_ENTRIES, anyhit_shader_entries);
  }
  void RayTracingShaderCodeBuilder::add_miss_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> miss_shader_entries) {
    fbb_.AddOffset(RayTracingShaderCode::VT_MISS_SHADER_ENTRIES, miss_shader_entries);
  }
  void RayTracingShaderCodeBuilder::add_callable_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> callable_shader_entries) {
    fbb_.AddOffset(RayTracingShaderCode::VT_CALLABLE_SHADER_ENTRIES, callable_shader_entries);
  }
  void RayTracingShaderCodeBuilder::add_intersection_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> intersection_shader_entries) {
    fbb_.AddOffset(RayTracingShaderCode::VT_INTERSECTION_SHADER_ENTRIES, intersection_shader_entries);
  }
  void RayTracingShaderCodeBuilder::add_mtime(uint64_t mtime) {
    fbb_.AddElement<uint64_t>(RayTracingShaderCode::VT_MTIME, mtime, 0);
  }
flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout,
    flatbuffers::Offset<CrossSchema::ShaderCode> code,
    flatbuffers::Offset<flatbuffers::String> raygen_shader_entry,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> closesthit_shader_entries,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> anyhit_shader_entries,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> miss_shader_entries,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> callable_shader_entries,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> intersection_shader_entries,
    uint64_t mtime){
    RayTracingShaderCodeBuilder builder_(_fbb);
  builder_.add_mtime(mtime);
  builder_.add_intersection_shader_entries(intersection_shader_entries);
  builder_.add_callable_shader_entries(callable_shader_entries);
  builder_.add_miss_shader_entries(miss_shader_entries);
  builder_.add_anyhit_shader_entries(anyhit_shader_entries);
  builder_.add_closesthit_shader_entries(closesthit_shader_entries);
  builder_.add_raygen_shader_entry(raygen_shader_entry);
  builder_.add_code(code);
  builder_.add_layout(layout);
  builder_.add_guid(guid);
  return builder_.Finish();
}

flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout,
    flatbuffers::Offset<CrossSchema::ShaderCode> code,
    const char *raygen_shader_entry,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *closesthit_shader_entries,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *anyhit_shader_entries,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *miss_shader_entries,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *callable_shader_entries,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *intersection_shader_entries,
    uint64_t mtime) {
  auto raygen_shader_entry__ = raygen_shader_entry ? _fbb.CreateString(raygen_shader_entry) : 0;
  auto closesthit_shader_entries__ = closesthit_shader_entries ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*closesthit_shader_entries) : 0;
  auto anyhit_shader_entries__ = anyhit_shader_entries ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*anyhit_shader_entries) : 0;
  auto miss_shader_entries__ = miss_shader_entries ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*miss_shader_entries) : 0;
  auto callable_shader_entries__ = callable_shader_entries ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*callable_shader_entries) : 0;
  auto intersection_shader_entries__ = intersection_shader_entries ? _fbb.CreateVector<flatbuffers::Offset<flatbuffers::String>>(*intersection_shader_entries) : 0;
  return CrossSchema::CreateRayTracingShaderCode(
      _fbb,
      guid,
      layout,
      code,
      raygen_shader_entry__,
      closesthit_shader_entries__,
      anyhit_shader_entries__,
      miss_shader_entries__,
      callable_shader_entries__,
      intersection_shader_entries__,
      mtime);
}

 const flatbuffers::TypeTable * PlatformRayTracingShader::MiniReflectTypeTable(){
    return PlatformRayTracingShaderTypeTable();
  }
const CrossSchema::ShaderVersion * PlatformRayTracingShader::version() const{
    return GetStruct<const CrossSchema::ShaderVersion *>(VT_VERSION);
  }
  CrossSchema::ShaderVersion * PlatformRayTracingShader::mutable_version() {
    return GetStruct<CrossSchema::ShaderVersion *>(VT_VERSION);
  }
const CrossSchema::RayTracingShaderCode * PlatformRayTracingShader::shader_code() const{
    return GetPointer<const CrossSchema::RayTracingShaderCode *>(VT_SHADER_CODE);
  }
  CrossSchema::RayTracingShaderCode * PlatformRayTracingShader::mutable_shader_code() {
    return GetPointer<CrossSchema::RayTracingShaderCode *>(VT_SHADER_CODE);
  }
 bool PlatformRayTracingShader::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyField<CrossSchema::ShaderVersion>(verifier, VT_VERSION) &&
           VerifyOffset(verifier, VT_SHADER_CODE) &&
           verifier.VerifyTable(shader_code()) &&
           verifier.EndTable();
  }
  void PlatformRayTracingShaderBuilder::add_version(const CrossSchema::ShaderVersion *version) {
    fbb_.AddStruct(PlatformRayTracingShader::VT_VERSION, version);
  }
  void PlatformRayTracingShaderBuilder::add_shader_code(flatbuffers::Offset<CrossSchema::RayTracingShaderCode> shader_code) {
    fbb_.AddOffset(PlatformRayTracingShader::VT_SHADER_CODE, shader_code);
  }
flatbuffers::Offset<PlatformRayTracingShader> CreatePlatformRayTracingShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version,
    flatbuffers::Offset<CrossSchema::RayTracingShaderCode> shader_code){
    PlatformRayTracingShaderBuilder builder_(_fbb);
  builder_.add_shader_code(shader_code);
  builder_.add_version(version);
  return builder_.Finish();
}

 const flatbuffers::TypeTable * RayTracingShaderAsset::MiniReflectTypeTable(){
    return RayTracingShaderAssetTypeTable();
  }
const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> * RayTracingShaderAsset::platform_shaders() const{
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *>(VT_PLATFORM_SHADERS);
  }
  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> * RayTracingShaderAsset::mutable_platform_shaders() {
    return GetPointer<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *>(VT_PLATFORM_SHADERS);
  }
 bool RayTracingShaderAsset::Verify(flatbuffers::Verifier &verifier) const{
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PLATFORM_SHADERS) &&
           verifier.VerifyVector(platform_shaders()) &&
           verifier.VerifyVectorOfTables(platform_shaders()) &&
           verifier.EndTable();
  }
  void RayTracingShaderAssetBuilder::add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>>> platform_shaders) {
    fbb_.AddOffset(RayTracingShaderAsset::VT_PLATFORM_SHADERS, platform_shaders);
  }
flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>>> platform_shaders){
    RayTracingShaderAssetBuilder builder_(_fbb);
  builder_.add_platform_shaders(platform_shaders);
  return builder_.Finish();
}

flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *platform_shaders) {
  auto platform_shaders__ = platform_shaders ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>>(*platform_shaders) : 0;
  return CrossSchema::CreateRayTracingShaderAsset(
      _fbb,
      platform_shaders__);
}

ShaderVariableT *ShaderVariable::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderVariableT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderVariable::UnPackTo(ShaderVariableT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = type(); _o->type = _e; }
  { auto _e = offset(); _o->offset = _e; }
  { auto _e = row_count(); _o->row_count = _e; }
  { auto _e = col_count(); _o->col_count = _e; }
  { auto _e = size(); _o->size = _e; }
  { auto _e = array_size(); _o->array_size = _e; }
  { auto _e = index(); _o->index = _e; }
  { auto _e = stage_mask(); _o->stage_mask = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> ShaderVariable::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderVariable(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> CreateShaderVariable(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderVariableT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _type = _o->type;
  auto _offset = _o->offset;
  auto _row_count = _o->row_count;
  auto _col_count = _o->col_count;
  auto _size = _o->size;
  auto _array_size = _o->array_size;
  auto _index = _o->index;
  auto _stage_mask = _o->stage_mask;
  return CrossSchema::CreateShaderVariable(
      _fbb,
      _name,
      _type,
      _offset,
      _row_count,
      _col_count,
      _size,
      _array_size,
      _index,
      _stage_mask);
}

ShaderVariableExT *ShaderVariableEx::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderVariableExT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderVariableEx::UnPackTo(ShaderVariableExT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = offset(); _o->offset = _e; }
  { auto _e = size(); _o->size = _e; }
  { auto _e = type(); _o->type = _e; }
  { auto _e = row_count(); _o->row_count = _e; }
  { auto _e = col_count(); _o->col_count = _e; }
  { auto _e = members(); if (_e) { _o->members.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->members[_i] = std::unique_ptr<CrossSchema::ShaderVariableExT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = array_size(); _o->array_size = _e; }
  { auto _e = array_stride(); _o->array_stride = _e; }
  { auto _e = index(); _o->index = _e; }
  { auto _e = stage_mask(); _o->stage_mask = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> ShaderVariableEx::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableExT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderVariableEx(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableEx(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableExT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderVariableExT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _offset = _o->offset;
  auto _size = _o->size;
  auto _type = _o->type;
  auto _row_count = _o->row_count;
  auto _col_count = _o->col_count;
  auto _members = _o->members.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> (_o->members.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderVariableEx(*__va->__fbb, __va->__o->members[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _array_size = _o->array_size;
  auto _array_stride = _o->array_stride;
  auto _index = _o->index;
  auto _stage_mask = _o->stage_mask;
  return CrossSchema::CreateShaderVariableEx(
      _fbb,
      _name,
      _offset,
      _size,
      _type,
      _row_count,
      _col_count,
      _members,
      _array_size,
      _array_stride,
      _index,
      _stage_mask);
}

ShaderStructTypeT *ShaderStructType::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderStructTypeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderStructType::UnPackTo(ShaderStructTypeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = size(); _o->size = _e; }
  { auto _e = members(); if (_e) { _o->members.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->members[_i] = std::unique_ptr<CrossSchema::ShaderVariableExT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> ShaderStructType::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderStructTypeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderStructType(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> CreateShaderStructType(flatbuffers::FlatBufferBuilder &_fbb, const ShaderStructTypeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderStructTypeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _size = _o->size;
  auto _members = _o->members.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> (_o->members.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderVariableEx(*__va->__fbb, __va->__o->members[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateShaderStructType(
      _fbb,
      _size,
      _members);
}

ShaderResourceT *ShaderResource::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderResourceT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderResource::UnPackTo(ShaderResourceT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = type(); _o->type = _e; }
  { auto _e = array_size(); _o->array_size = _e; }
  { auto _e = space(); _o->space = _e; }
  { auto _e = index(); _o->index = _e; }
  { auto _e = subpass_index(); _o->subpass_index = _e; }
  { auto _e = stage_mask(); _o->stage_mask = _e; }
  { auto _e = return_type(); _o->return_type = _e; }
  { auto _e = depth_texture(); _o->depth_texture = _e; }
  { auto _e = struct_type(); if (_e) _o->struct_type = std::unique_ptr<CrossSchema::ShaderStructTypeT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> ShaderResource::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderResourceT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderResource(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> CreateShaderResource(flatbuffers::FlatBufferBuilder &_fbb, const ShaderResourceT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderResourceT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _type = _o->type;
  auto _array_size = _o->array_size;
  auto _space = _o->space;
  auto _index = _o->index;
  auto _subpass_index = _o->subpass_index;
  auto _stage_mask = _o->stage_mask;
  auto _return_type = _o->return_type;
  auto _depth_texture = _o->depth_texture;
  auto _struct_type = _o->struct_type ? CreateShaderStructType(_fbb, _o->struct_type.get(), _rehasher) : 0;
  return CrossSchema::CreateShaderResource(
      _fbb,
      _name,
      _type,
      _array_size,
      _space,
      _index,
      _subpass_index,
      _stage_mask,
      _return_type,
      _depth_texture,
      _struct_type);
}

ShaderConstantBufferT *ShaderConstantBuffer::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderConstantBufferT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderConstantBuffer::UnPackTo(ShaderConstantBufferT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = type(); _o->type = _e; }
  { auto _e = size(); _o->size = _e; }
  { auto _e = members(); if (_e) { _o->members.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->members[_i] = std::unique_ptr<CrossSchema::ShaderVariableT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = space(); _o->space = _e; }
  { auto _e = index(); _o->index = _e; }
  { auto _e = stage_mask(); _o->stage_mask = _e; }
  { auto _e = array_size(); _o->array_size = _e; }
  { auto _e = struct_type(); if (_e) _o->struct_type = std::unique_ptr<CrossSchema::ShaderStructTypeT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> ShaderConstantBuffer::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderConstantBufferT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderConstantBuffer(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBuffer(flatbuffers::FlatBufferBuilder &_fbb, const ShaderConstantBufferT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderConstantBufferT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _fbb.CreateString(_o->name);
  auto _type = _o->type;
  auto _size = _o->size;
  auto _members = _o->members.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderVariable>> (_o->members.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderVariable(*__va->__fbb, __va->__o->members[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _space = _o->space;
  auto _index = _o->index;
  auto _stage_mask = _o->stage_mask;
  auto _array_size = _o->array_size;
  auto _struct_type = _o->struct_type ? CreateShaderStructType(_fbb, _o->struct_type.get(), _rehasher) : 0;
  return CrossSchema::CreateShaderConstantBuffer(
      _fbb,
      _name,
      _type,
      _size,
      _members,
      _space,
      _index,
      _stage_mask,
      _array_size,
      _struct_type);
}

CombinedSamplerT *CombinedSampler::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<CombinedSamplerT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void CombinedSampler::UnPackTo(CombinedSamplerT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = name(); if (_e) _o->name = _e->str(); }
  { auto _e = texture_name(); if (_e) _o->texture_name = _e->str(); }
  { auto _e = sampler_name(); if (_e) _o->sampler_name = _e->str(); }
  { auto _e = stage_mask(); _o->stage_mask = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> CombinedSampler::Pack(flatbuffers::FlatBufferBuilder &_fbb, const CombinedSamplerT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateCombinedSampler(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> CreateCombinedSampler(flatbuffers::FlatBufferBuilder &_fbb, const CombinedSamplerT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const CombinedSamplerT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _name = _o->name.empty() ? 0 : _fbb.CreateString(_o->name);
  auto _texture_name = _o->texture_name.empty() ? 0 : _fbb.CreateString(_o->texture_name);
  auto _sampler_name = _o->sampler_name.empty() ? 0 : _fbb.CreateString(_o->sampler_name);
  auto _stage_mask = _o->stage_mask;
  return CrossSchema::CreateCombinedSampler(
      _fbb,
      _name,
      _texture_name,
      _sampler_name,
      _stage_mask);
}

ShaderLayoutT *ShaderLayout::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderLayoutT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderLayout::UnPackTo(ShaderLayoutT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = constant_buffers(); if (_e) { _o->constant_buffers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->constant_buffers[_i] = std::unique_ptr<CrossSchema::ShaderConstantBufferT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = resources(); if (_e) { _o->resources.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->resources[_i] = std::unique_ptr<CrossSchema::ShaderResourceT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = combined_samplers(); if (_e) { _o->combined_samplers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->combined_samplers[_i] = std::unique_ptr<CrossSchema::CombinedSamplerT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = specialization_constants(); if (_e) _o->specialization_constants = std::unique_ptr<CrossSchema::ShaderConstantBufferT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> ShaderLayout::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderLayoutT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderLayout(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> CreateShaderLayout(flatbuffers::FlatBufferBuilder &_fbb, const ShaderLayoutT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderLayoutT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _constant_buffers = _o->constant_buffers.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> (_o->constant_buffers.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderConstantBuffer(*__va->__fbb, __va->__o->constant_buffers[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _resources = _o->resources.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderResource>> (_o->resources.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderResource(*__va->__fbb, __va->__o->resources[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _combined_samplers = _o->combined_samplers.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::CombinedSampler>> (_o->combined_samplers.size(), [](size_t i, _VectorArgs *__va) { return CreateCombinedSampler(*__va->__fbb, __va->__o->combined_samplers[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _specialization_constants = _o->specialization_constants ? CreateShaderConstantBuffer(_fbb, _o->specialization_constants.get(), _rehasher) : 0;
  return CrossSchema::CreateShaderLayout(
      _fbb,
      _constant_buffers,
      _resources,
      _combined_samplers,
      _specialization_constants);
}

ShaderCodeT *ShaderCode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ShaderCodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ShaderCode::UnPackTo(ShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = stage(); _o->stage = _e; }
  { auto _e = entry_point(); if (_e) _o->entry_point = _e->str(); }
  { auto _e = code_data(); if (_e) { _o->code_data.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->code_data[_i] = _e->Get(_i); } } }
  { auto _e = stage_inputs(); if (_e) { _o->stage_inputs.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->stage_inputs[_i] = std::unique_ptr<CrossSchema::ShaderVariableT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = stage_outputs(); if (_e) { _o->stage_outputs.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->stage_outputs[_i] = std::unique_ptr<CrossSchema::ShaderVariableT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = debug_symbol(); _o->debug_symbol = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> ShaderCode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateShaderCode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> CreateShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const ShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ShaderCodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _stage = _o->stage;
  auto _entry_point = _o->entry_point.empty() ? 0 : _fbb.CreateString(_o->entry_point);
  auto _code_data = _o->code_data.size() ? _fbb.CreateVector(_o->code_data) : 0;
  auto _stage_inputs = _o->stage_inputs.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderVariable>> (_o->stage_inputs.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderVariable(*__va->__fbb, __va->__o->stage_inputs[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _stage_outputs = _o->stage_outputs.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ShaderVariable>> (_o->stage_outputs.size(), [](size_t i, _VectorArgs *__va) { return CreateShaderVariable(*__va->__fbb, __va->__o->stage_outputs[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _debug_symbol = _o->debug_symbol;
  return CrossSchema::CreateShaderCode(
      _fbb,
      _stage,
      _entry_point,
      _code_data,
      _stage_inputs,
      _stage_outputs,
      _debug_symbol);
}

GraphicsShaderCodeT *GraphicsShaderCode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<GraphicsShaderCodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void GraphicsShaderCode::UnPackTo(GraphicsShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = guid(); if (_e) _o->guid = std::unique_ptr<CrossSchema::GUID>(new CrossSchema::GUID(*_e)); }
  { auto _e = active_keywords(); _o->active_keywords = _e; }
  { auto _e = layout(); if (_e) _o->layout = std::unique_ptr<CrossSchema::ShaderLayoutT>(_e->UnPack(_resolver)); }
  { auto _e = vertex_shader(); if (_e) _o->vertex_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = hull_shader(); if (_e) _o->hull_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = domain_shader(); if (_e) _o->domain_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = geometry_shader(); if (_e) _o->geometry_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = pixel_shader(); if (_e) _o->pixel_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = task_shader(); if (_e) _o->task_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = mesh_shader(); if (_e) _o->mesh_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = mtime(); _o->mtime = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderCode> GraphicsShaderCode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateGraphicsShaderCode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderCode> CreateGraphicsShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const GraphicsShaderCodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _guid = _o->guid ? _o->guid.get() : 0;
  auto _active_keywords = _o->active_keywords;
  auto _layout = _o->layout ? CreateShaderLayout(_fbb, _o->layout.get(), _rehasher) : 0;
  auto _vertex_shader = _o->vertex_shader ? CreateShaderCode(_fbb, _o->vertex_shader.get(), _rehasher) : 0;
  auto _hull_shader = _o->hull_shader ? CreateShaderCode(_fbb, _o->hull_shader.get(), _rehasher) : 0;
  auto _domain_shader = _o->domain_shader ? CreateShaderCode(_fbb, _o->domain_shader.get(), _rehasher) : 0;
  auto _geometry_shader = _o->geometry_shader ? CreateShaderCode(_fbb, _o->geometry_shader.get(), _rehasher) : 0;
  auto _pixel_shader = _o->pixel_shader ? CreateShaderCode(_fbb, _o->pixel_shader.get(), _rehasher) : 0;
  auto _task_shader = _o->task_shader ? CreateShaderCode(_fbb, _o->task_shader.get(), _rehasher) : 0;
  auto _mesh_shader = _o->mesh_shader ? CreateShaderCode(_fbb, _o->mesh_shader.get(), _rehasher) : 0;
  auto _mtime = _o->mtime;
  return CrossSchema::CreateGraphicsShaderCode(
      _fbb,
      _guid,
      _active_keywords,
      _layout,
      _vertex_shader,
      _hull_shader,
      _domain_shader,
      _geometry_shader,
      _pixel_shader,
      _task_shader,
      _mesh_shader,
      _mtime);
}

PlatformGraphicsShaderT *PlatformGraphicsShader::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<PlatformGraphicsShaderT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void PlatformGraphicsShader::UnPackTo(PlatformGraphicsShaderT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = version(); if (_e) _o->version = std::unique_ptr<CrossSchema::ShaderVersion>(new CrossSchema::ShaderVersion(*_e)); }
  { auto _e = keywords(); if (_e) { _o->keywords.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->keywords[_i] = _e->Get(_i)->str(); } } }
  { auto _e = variants(); if (_e) { _o->variants.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->variants[_i] = std::unique_ptr<CrossSchema::GraphicsShaderCodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> PlatformGraphicsShader::Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformGraphicsShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreatePlatformGraphicsShader(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformGraphicsShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const PlatformGraphicsShaderT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _version = _o->version ? _o->version.get() : 0;
  auto _keywords = _o->keywords.size() ? _fbb.CreateVectorOfStrings(_o->keywords) : 0;
  auto _variants = _o->variants.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> (_o->variants.size(), [](size_t i, _VectorArgs *__va) { return CreateGraphicsShaderCode(*__va->__fbb, __va->__o->variants[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreatePlatformGraphicsShader(
      _fbb,
      _version,
      _keywords,
      _variants);
}

GraphicsShaderAssetT *GraphicsShaderAsset::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<GraphicsShaderAssetT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void GraphicsShaderAsset::UnPackTo(GraphicsShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = platform_shaders(); if (_e) { _o->platform_shaders.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->platform_shaders[_i] = std::unique_ptr<CrossSchema::PlatformGraphicsShaderT>(_e->Get(_i)->UnPack(_resolver)); } } }
  { auto _e = mtime(); _o->mtime = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> GraphicsShaderAsset::Pack(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateGraphicsShaderAsset(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const GraphicsShaderAssetT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _platform_shaders = _o->platform_shaders.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> (_o->platform_shaders.size(), [](size_t i, _VectorArgs *__va) { return CreatePlatformGraphicsShader(*__va->__fbb, __va->__o->platform_shaders[i].get(), __va->__rehasher); }, &_va ) : 0;
  auto _mtime = _o->mtime;
  return CrossSchema::CreateGraphicsShaderAsset(
      _fbb,
      _platform_shaders,
      _mtime);
}

ComputeShaderCodeT *ComputeShaderCode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ComputeShaderCodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ComputeShaderCode::UnPackTo(ComputeShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = guid(); if (_e) _o->guid = std::unique_ptr<CrossSchema::GUID>(new CrossSchema::GUID(*_e)); }
  { auto _e = layout(); if (_e) _o->layout = std::unique_ptr<CrossSchema::ShaderLayoutT>(_e->UnPack(_resolver)); }
  { auto _e = group_size(); if (_e) _o->group_size = std::unique_ptr<CrossSchema::uint3>(new CrossSchema::uint3(*_e)); }
  { auto _e = compute_shader(); if (_e) _o->compute_shader = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = mtime(); _o->mtime = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderCode> ComputeShaderCode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateComputeShaderCode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderCode> CreateComputeShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ComputeShaderCodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _guid = _o->guid ? _o->guid.get() : 0;
  auto _layout = _o->layout ? CreateShaderLayout(_fbb, _o->layout.get(), _rehasher) : 0;
  auto _group_size = _o->group_size ? _o->group_size.get() : 0;
  auto _compute_shader = _o->compute_shader ? CreateShaderCode(_fbb, _o->compute_shader.get(), _rehasher) : 0;
  auto _mtime = _o->mtime;
  return CrossSchema::CreateComputeShaderCode(
      _fbb,
      _guid,
      _layout,
      _group_size,
      _compute_shader,
      _mtime);
}

PlatformComputeShaderT *PlatformComputeShader::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<PlatformComputeShaderT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void PlatformComputeShader::UnPackTo(PlatformComputeShaderT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = version(); if (_e) _o->version = std::unique_ptr<CrossSchema::ShaderVersion>(new CrossSchema::ShaderVersion(*_e)); }
  { auto _e = code_list(); if (_e) { _o->code_list.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->code_list[_i] = std::unique_ptr<CrossSchema::ComputeShaderCodeT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> PlatformComputeShader::Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformComputeShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreatePlatformComputeShader(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformComputeShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const PlatformComputeShaderT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _version = _o->version ? _o->version.get() : 0;
  auto _code_list = _o->code_list.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> (_o->code_list.size(), [](size_t i, _VectorArgs *__va) { return CreateComputeShaderCode(*__va->__fbb, __va->__o->code_list[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreatePlatformComputeShader(
      _fbb,
      _version,
      _code_list);
}

ComputeShaderAssetT *ComputeShaderAsset::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<ComputeShaderAssetT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void ComputeShaderAsset::UnPackTo(ComputeShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = platform_shaders(); if (_e) { _o->platform_shaders.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->platform_shaders[_i] = std::unique_ptr<CrossSchema::PlatformComputeShaderT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> ComputeShaderAsset::Pack(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateComputeShaderAsset(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const ComputeShaderAssetT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _platform_shaders = _o->platform_shaders.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> (_o->platform_shaders.size(), [](size_t i, _VectorArgs *__va) { return CreatePlatformComputeShader(*__va->__fbb, __va->__o->platform_shaders[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateComputeShaderAsset(
      _fbb,
      _platform_shaders);
}

DXILReflectionCodeT *DXILReflectionCode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<DXILReflectionCodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void DXILReflectionCode::UnPackTo(DXILReflectionCodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = group_size(); if (_e) _o->group_size = std::unique_ptr<CrossSchema::uint3>(new CrossSchema::uint3(*_e)); }
  { auto _e = layout(); if (_e) _o->layout = std::unique_ptr<CrossSchema::ShaderLayoutT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<DXILReflectionCode> DXILReflectionCode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const DXILReflectionCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateDXILReflectionCode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<DXILReflectionCode> CreateDXILReflectionCode(flatbuffers::FlatBufferBuilder &_fbb, const DXILReflectionCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const DXILReflectionCodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _group_size = _o->group_size ? _o->group_size.get() : 0;
  auto _layout = _o->layout ? CreateShaderLayout(_fbb, _o->layout.get(), _rehasher) : 0;
  return CrossSchema::CreateDXILReflectionCode(
      _fbb,
      _group_size,
      _layout);
}

RayTracingShaderCodeT *RayTracingShaderCode::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<RayTracingShaderCodeT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void RayTracingShaderCode::UnPackTo(RayTracingShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = guid(); if (_e) _o->guid = std::unique_ptr<CrossSchema::GUID>(new CrossSchema::GUID(*_e)); }
  { auto _e = layout(); if (_e) _o->layout = std::unique_ptr<CrossSchema::ShaderLayoutT>(_e->UnPack(_resolver)); }
  { auto _e = code(); if (_e) _o->code = std::unique_ptr<CrossSchema::ShaderCodeT>(_e->UnPack(_resolver)); }
  { auto _e = raygen_shader_entry(); if (_e) _o->raygen_shader_entry = _e->str(); }
  { auto _e = closesthit_shader_entries(); if (_e) { _o->closesthit_shader_entries.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->closesthit_shader_entries[_i] = _e->Get(_i)->str(); } } }
  { auto _e = anyhit_shader_entries(); if (_e) { _o->anyhit_shader_entries.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->anyhit_shader_entries[_i] = _e->Get(_i)->str(); } } }
  { auto _e = miss_shader_entries(); if (_e) { _o->miss_shader_entries.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->miss_shader_entries[_i] = _e->Get(_i)->str(); } } }
  { auto _e = callable_shader_entries(); if (_e) { _o->callable_shader_entries.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->callable_shader_entries[_i] = _e->Get(_i)->str(); } } }
  { auto _e = intersection_shader_entries(); if (_e) { _o->intersection_shader_entries.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->intersection_shader_entries[_i] = _e->Get(_i)->str(); } } }
  { auto _e = mtime(); _o->mtime = _e; }
}

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> RayTracingShaderCode::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRayTracingShaderCode(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RayTracingShaderCodeT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _guid = _o->guid ? _o->guid.get() : 0;
  auto _layout = _o->layout ? CreateShaderLayout(_fbb, _o->layout.get(), _rehasher) : 0;
  auto _code = _o->code ? CreateShaderCode(_fbb, _o->code.get(), _rehasher) : 0;
  auto _raygen_shader_entry = _o->raygen_shader_entry.empty() ? 0 : _fbb.CreateString(_o->raygen_shader_entry);
  auto _closesthit_shader_entries = _o->closesthit_shader_entries.size() ? _fbb.CreateVectorOfStrings(_o->closesthit_shader_entries) : 0;
  auto _anyhit_shader_entries = _o->anyhit_shader_entries.size() ? _fbb.CreateVectorOfStrings(_o->anyhit_shader_entries) : 0;
  auto _miss_shader_entries = _o->miss_shader_entries.size() ? _fbb.CreateVectorOfStrings(_o->miss_shader_entries) : 0;
  auto _callable_shader_entries = _o->callable_shader_entries.size() ? _fbb.CreateVectorOfStrings(_o->callable_shader_entries) : 0;
  auto _intersection_shader_entries = _o->intersection_shader_entries.size() ? _fbb.CreateVectorOfStrings(_o->intersection_shader_entries) : 0;
  auto _mtime = _o->mtime;
  return CrossSchema::CreateRayTracingShaderCode(
      _fbb,
      _guid,
      _layout,
      _code,
      _raygen_shader_entry,
      _closesthit_shader_entries,
      _anyhit_shader_entries,
      _miss_shader_entries,
      _callable_shader_entries,
      _intersection_shader_entries,
      _mtime);
}

PlatformRayTracingShaderT *PlatformRayTracingShader::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<PlatformRayTracingShaderT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void PlatformRayTracingShader::UnPackTo(PlatformRayTracingShaderT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = version(); if (_e) _o->version = std::unique_ptr<CrossSchema::ShaderVersion>(new CrossSchema::ShaderVersion(*_e)); }
  { auto _e = shader_code(); if (_e) _o->shader_code = std::unique_ptr<CrossSchema::RayTracingShaderCodeT>(_e->UnPack(_resolver)); }
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformRayTracingShader> PlatformRayTracingShader::Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformRayTracingShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreatePlatformRayTracingShader(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<PlatformRayTracingShader> CreatePlatformRayTracingShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformRayTracingShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const PlatformRayTracingShaderT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _version = _o->version ? _o->version.get() : 0;
  auto _shader_code = _o->shader_code ? CreateRayTracingShaderCode(_fbb, _o->shader_code.get(), _rehasher) : 0;
  return CrossSchema::CreatePlatformRayTracingShader(
      _fbb,
      _version,
      _shader_code);
}

RayTracingShaderAssetT *RayTracingShaderAsset::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::make_unique<RayTracingShaderAssetT>();
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

void RayTracingShaderAsset::UnPackTo(RayTracingShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = platform_shaders(); if (_e) { _o->platform_shaders.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->platform_shaders[_i] = std::unique_ptr<CrossSchema::PlatformRayTracingShaderT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> RayTracingShaderAsset::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRayTracingShaderAsset(_fbb, _o, _rehasher);
}

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RayTracingShaderAssetT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _platform_shaders = _o->platform_shaders.size() ? _fbb.CreateVector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> (_o->platform_shaders.size(), [](size_t i, _VectorArgs *__va) { return CreatePlatformRayTracingShader(*__va->__fbb, __va->__o->platform_shaders[i].get(), __va->__rehasher); }, &_va ) : 0;
  return CrossSchema::CreateRayTracingShaderAsset(
      _fbb,
      _platform_shaders);
}

}  // namespace CrossSchema
