// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_SHADERASSET_CROSSSCHEMA_H_
#define FLATBUFFERS_GENERATED_SHADERASSET_CROSSSCHEMA_H_

#include "flatbuffers/flatbuffers.h"

#include "CrossSchemaForward.h"
#include "BasicStruct_generated.h"
#include "ShaderDefines_generated.h"

namespace CrossSchema {

struct ShaderVariable;
struct ShaderVariableBuilder;
struct ShaderVariableT;

struct ShaderVariableEx;
struct ShaderVariableExBuilder;
struct ShaderVariableExT;

struct ShaderStructType;
struct ShaderStructTypeBuilder;
struct ShaderStructTypeT;

struct ShaderResource;
struct ShaderResourceBuilder;
struct ShaderResourceT;

struct ShaderConstantBuffer;
struct ShaderConstantBufferBuilder;
struct ShaderConstantBufferT;

struct CombinedSampler;
struct CombinedSamplerBuilder;
struct CombinedSamplerT;

struct ShaderLayout;
struct ShaderLayoutBuilder;
struct ShaderLayoutT;

struct ShaderCode;
struct ShaderCodeBuilder;
struct ShaderCodeT;

struct GraphicsShaderCode;
struct GraphicsShaderCodeBuilder;
struct GraphicsShaderCodeT;

struct PlatformGraphicsShader;
struct PlatformGraphicsShaderBuilder;
struct PlatformGraphicsShaderT;

struct GraphicsShaderAsset;
struct GraphicsShaderAssetBuilder;
struct GraphicsShaderAssetT;

struct ComputeShaderCode;
struct ComputeShaderCodeBuilder;
struct ComputeShaderCodeT;

struct PlatformComputeShader;
struct PlatformComputeShaderBuilder;
struct PlatformComputeShaderT;

struct ComputeShaderAsset;
struct ComputeShaderAssetBuilder;
struct ComputeShaderAssetT;

struct DXILReflectionCode;
struct DXILReflectionCodeBuilder;
struct DXILReflectionCodeT;

struct RayTracingShaderCode;
struct RayTracingShaderCodeBuilder;
struct RayTracingShaderCodeT;

struct PlatformRayTracingShader;
struct PlatformRayTracingShaderBuilder;
struct PlatformRayTracingShaderT;

struct RayTracingShaderAsset;
struct RayTracingShaderAssetBuilder;
struct RayTracingShaderAssetT;

inline const flatbuffers::TypeTable *ShaderVariableTypeTable();

inline const flatbuffers::TypeTable *ShaderVariableExTypeTable();

inline const flatbuffers::TypeTable *ShaderStructTypeTypeTable();

inline const flatbuffers::TypeTable *ShaderResourceTypeTable();

inline const flatbuffers::TypeTable *ShaderConstantBufferTypeTable();

inline const flatbuffers::TypeTable *CombinedSamplerTypeTable();

inline const flatbuffers::TypeTable *ShaderLayoutTypeTable();

inline const flatbuffers::TypeTable *ShaderCodeTypeTable();

inline const flatbuffers::TypeTable *GraphicsShaderCodeTypeTable();

inline const flatbuffers::TypeTable *PlatformGraphicsShaderTypeTable();

inline const flatbuffers::TypeTable *GraphicsShaderAssetTypeTable();

inline const flatbuffers::TypeTable *ComputeShaderCodeTypeTable();

inline const flatbuffers::TypeTable *PlatformComputeShaderTypeTable();

inline const flatbuffers::TypeTable *ComputeShaderAssetTypeTable();

inline const flatbuffers::TypeTable *DXILReflectionCodeTypeTable();

inline const flatbuffers::TypeTable *RayTracingShaderCodeTypeTable();

inline const flatbuffers::TypeTable *PlatformRayTracingShaderTypeTable();

inline const flatbuffers::TypeTable *RayTracingShaderAssetTypeTable();

struct ShaderVariableT : public flatbuffers::NativeTable {
  typedef ShaderVariable TableType;
  std::string name{};
  CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown;
  uint32_t offset = 0;
  uint32_t row_count = 0;
  uint32_t col_count = 0;
  uint32_t size = 0;
  uint32_t array_size = 0;
  uint32_t index = 0;
  uint32_t stage_mask = 0;
};

struct ShaderVariable FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderVariableT NativeTableType;
  typedef ShaderVariableBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TYPE = 6,
    VT_OFFSET = 8,
    VT_ROW_COUNT = 10,
    VT_COL_COUNT = 12,
    VT_SIZE = 14,
    VT_ARRAY_SIZE = 16,
    VT_INDEX = 18,
    VT_STAGE_MASK = 20
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API bool KeyCompareLessThan(const ShaderVariable *o) const;
CROSS_SCHEMA_API int KeyCompareWithValue(const char *val) const;
CROSS_SCHEMA_API  CrossSchema::ShaderVariableType type() const;
  CROSS_SCHEMA_API  bool mutate_type(CrossSchema::ShaderVariableType _type);
CROSS_SCHEMA_API  uint32_t offset() const;
  CROSS_SCHEMA_API  bool mutate_offset(uint32_t _offset);
CROSS_SCHEMA_API  uint32_t row_count() const;
  CROSS_SCHEMA_API  bool mutate_row_count(uint32_t _row_count);
CROSS_SCHEMA_API  uint32_t col_count() const;
  CROSS_SCHEMA_API  bool mutate_col_count(uint32_t _col_count);
CROSS_SCHEMA_API  uint32_t size() const;
  CROSS_SCHEMA_API  bool mutate_size(uint32_t _size);
CROSS_SCHEMA_API  uint32_t array_size() const;
  CROSS_SCHEMA_API  bool mutate_array_size(uint32_t _array_size);
CROSS_SCHEMA_API  uint32_t index() const;
  CROSS_SCHEMA_API  bool mutate_index(uint32_t _index);
CROSS_SCHEMA_API  uint32_t stage_mask() const;
  CROSS_SCHEMA_API  bool mutate_stage_mask(uint32_t _stage_mask);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderVariableT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderVariableT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderVariableBuilder {
  typedef ShaderVariable Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_type(CrossSchema::ShaderVariableType type);
  void add_offset(uint32_t offset);
  void add_row_count(uint32_t row_count);
  void add_col_count(uint32_t col_count);
  void add_size(uint32_t size);
  void add_array_size(uint32_t array_size);
  void add_index(uint32_t index);
  void add_stage_mask(uint32_t stage_mask);
  explicit ShaderVariableBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderVariable> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderVariable>(end);
    fbb_.Required(o, ShaderVariable::VT_NAME);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> CreateShaderVariable(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown,
    uint32_t offset = 0,
    uint32_t row_count = 0,
    uint32_t col_count = 0,
    uint32_t size = 0,
    uint32_t array_size = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0);
struct ShaderVariable::Traits {
  using type = ShaderVariable;
  static auto constexpr Create = CreateShaderVariable;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> CreateShaderVariableDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown,
    uint32_t offset = 0,
    uint32_t row_count = 0,
    uint32_t col_count = 0,
    uint32_t size = 0,
    uint32_t array_size = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariable> CreateShaderVariable(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderVariableExT : public flatbuffers::NativeTable {
  typedef ShaderVariableEx TableType;
  std::string name{};
  uint32_t offset = 0;
  uint32_t size = 0;
  CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown;
  uint32_t row_count = 1;
  uint32_t col_count = 1;
  std::vector<std::unique_ptr<CrossSchema::ShaderVariableExT>> members{};
  uint32_t array_size = 1;
  uint32_t array_stride = 0;
  uint32_t index = 0;
  uint32_t stage_mask = 0;
};

struct ShaderVariableEx FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderVariableExT NativeTableType;
  typedef ShaderVariableExBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_OFFSET = 6,
    VT_SIZE = 8,
    VT_TYPE = 10,
    VT_ROW_COUNT = 12,
    VT_COL_COUNT = 14,
    VT_MEMBERS = 16,
    VT_ARRAY_SIZE = 18,
    VT_ARRAY_STRIDE = 20,
    VT_INDEX = 22,
    VT_STAGE_MASK = 24
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API bool KeyCompareLessThan(const ShaderVariableEx *o) const;
CROSS_SCHEMA_API int KeyCompareWithValue(const char *val) const;
CROSS_SCHEMA_API  uint32_t offset() const;
  CROSS_SCHEMA_API  bool mutate_offset(uint32_t _offset);
CROSS_SCHEMA_API  uint32_t size() const;
  CROSS_SCHEMA_API  bool mutate_size(uint32_t _size);
CROSS_SCHEMA_API  CrossSchema::ShaderVariableType type() const;
  CROSS_SCHEMA_API  bool mutate_type(CrossSchema::ShaderVariableType _type);
CROSS_SCHEMA_API  uint32_t row_count() const;
  CROSS_SCHEMA_API  bool mutate_row_count(uint32_t _row_count);
CROSS_SCHEMA_API  uint32_t col_count() const;
  CROSS_SCHEMA_API  bool mutate_col_count(uint32_t _col_count);
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *mutable_members();
CROSS_SCHEMA_API  uint32_t array_size() const;
  CROSS_SCHEMA_API  bool mutate_array_size(uint32_t _array_size);
CROSS_SCHEMA_API  uint32_t array_stride() const;
  CROSS_SCHEMA_API  bool mutate_array_stride(uint32_t _array_stride);
CROSS_SCHEMA_API  uint32_t index() const;
  CROSS_SCHEMA_API  bool mutate_index(uint32_t _index);
CROSS_SCHEMA_API  uint32_t stage_mask() const;
  CROSS_SCHEMA_API  bool mutate_stage_mask(uint32_t _stage_mask);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderVariableExT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderVariableExT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableExT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderVariableExBuilder {
  typedef ShaderVariableEx Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_offset(uint32_t offset);
  void add_size(uint32_t size);
  void add_type(CrossSchema::ShaderVariableType type);
  void add_row_count(uint32_t row_count);
  void add_col_count(uint32_t col_count);
  void add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members);
  void add_array_size(uint32_t array_size);
  void add_array_stride(uint32_t array_stride);
  void add_index(uint32_t index);
  void add_stage_mask(uint32_t stage_mask);
  explicit ShaderVariableExBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderVariableEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderVariableEx>(end);
    fbb_.Required(o, ShaderVariableEx::VT_NAME);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableEx(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    uint32_t offset = 0,
    uint32_t size = 0,
    CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown,
    uint32_t row_count = 1,
    uint32_t col_count = 1,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members = 0,
    uint32_t array_size = 1,
    uint32_t array_stride = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0);
struct ShaderVariableEx::Traits {
  using type = ShaderVariableEx;
  static auto constexpr Create = CreateShaderVariableEx;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableExDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    uint32_t offset = 0,
    uint32_t size = 0,
    CrossSchema::ShaderVariableType type = CrossSchema::ShaderVariableType::Unknown,
    uint32_t row_count = 1,
    uint32_t col_count = 1,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members = nullptr,
    uint32_t array_size = 1,
    uint32_t array_stride = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderVariableEx> CreateShaderVariableEx(flatbuffers::FlatBufferBuilder &_fbb, const ShaderVariableExT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderStructTypeT : public flatbuffers::NativeTable {
  typedef ShaderStructType TableType;
  uint32_t size = 0;
  std::vector<std::unique_ptr<CrossSchema::ShaderVariableExT>> members{};
};

struct ShaderStructType FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderStructTypeT NativeTableType;
  typedef ShaderStructTypeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SIZE = 4,
    VT_MEMBERS = 6
  };
CROSS_SCHEMA_API  uint32_t size() const;
  CROSS_SCHEMA_API  bool mutate_size(uint32_t _size);
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *mutable_members();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderStructTypeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderStructTypeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderStructTypeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderStructTypeBuilder {
  typedef ShaderStructType Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_size(uint32_t size);
  void add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members);
  explicit ShaderStructTypeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderStructType> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderStructType>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> CreateShaderStructType(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t size = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>>> members = 0);
struct ShaderStructType::Traits {
  using type = ShaderStructType;
  static auto constexpr Create = CreateShaderStructType;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> CreateShaderStructTypeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t size = 0,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariableEx>> *members = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderStructType> CreateShaderStructType(flatbuffers::FlatBufferBuilder &_fbb, const ShaderStructTypeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderResourceT : public flatbuffers::NativeTable {
  typedef ShaderResource TableType;
  std::string name{};
  CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown;
  uint32_t array_size = 0;
  uint32_t space = 0;
  uint32_t index = 0;
  uint32_t subpass_index = 0;
  uint32_t stage_mask = 0;
  CrossSchema::ShaderVariableType return_type = CrossSchema::ShaderVariableType::Float;
  bool depth_texture = false;
  std::unique_ptr<CrossSchema::ShaderStructTypeT> struct_type{};
};

struct ShaderResource FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderResourceT NativeTableType;
  typedef ShaderResourceBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TYPE = 6,
    VT_ARRAY_SIZE = 8,
    VT_SPACE = 10,
    VT_INDEX = 12,
    VT_SUBPASS_INDEX = 14,
    VT_STAGE_MASK = 16,
    VT_RETURN_TYPE = 18,
    VT_DEPTH_TEXTURE = 20,
    VT_STRUCT_TYPE = 22
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API bool KeyCompareLessThan(const ShaderResource *o) const;
CROSS_SCHEMA_API int KeyCompareWithValue(const char *val) const;
CROSS_SCHEMA_API  CrossSchema::ShaderResourceType type() const;
  CROSS_SCHEMA_API  bool mutate_type(CrossSchema::ShaderResourceType _type);
CROSS_SCHEMA_API  uint32_t array_size() const;
  CROSS_SCHEMA_API  bool mutate_array_size(uint32_t _array_size);
CROSS_SCHEMA_API  uint32_t space() const;
  CROSS_SCHEMA_API  bool mutate_space(uint32_t _space);
CROSS_SCHEMA_API  uint32_t index() const;
  CROSS_SCHEMA_API  bool mutate_index(uint32_t _index);
CROSS_SCHEMA_API  uint32_t subpass_index() const;
  CROSS_SCHEMA_API  bool mutate_subpass_index(uint32_t _subpass_index);
CROSS_SCHEMA_API  uint32_t stage_mask() const;
  CROSS_SCHEMA_API  bool mutate_stage_mask(uint32_t _stage_mask);
CROSS_SCHEMA_API  CrossSchema::ShaderVariableType return_type() const;
  CROSS_SCHEMA_API  bool mutate_return_type(CrossSchema::ShaderVariableType _return_type);
CROSS_SCHEMA_API  bool depth_texture() const;
  CROSS_SCHEMA_API  bool mutate_depth_texture(bool _depth_texture);
CROSS_SCHEMA_API  const CrossSchema::ShaderStructType *struct_type() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderStructType *mutable_struct_type();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderResourceT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderResourceT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderResourceT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderResourceBuilder {
  typedef ShaderResource Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_type(CrossSchema::ShaderResourceType type);
  void add_array_size(uint32_t array_size);
  void add_space(uint32_t space);
  void add_index(uint32_t index);
  void add_subpass_index(uint32_t subpass_index);
  void add_stage_mask(uint32_t stage_mask);
  void add_return_type(CrossSchema::ShaderVariableType return_type);
  void add_depth_texture(bool depth_texture);
  void add_struct_type(flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type);
  explicit ShaderResourceBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderResource> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderResource>(end);
    fbb_.Required(o, ShaderResource::VT_NAME);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> CreateShaderResource(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown,
    uint32_t array_size = 0,
    uint32_t space = 0,
    uint32_t index = 0,
    uint32_t subpass_index = 0,
    uint32_t stage_mask = 0,
    CrossSchema::ShaderVariableType return_type = CrossSchema::ShaderVariableType::Float,
    bool depth_texture = false,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type = 0);
struct ShaderResource::Traits {
  using type = ShaderResource;
  static auto constexpr Create = CreateShaderResource;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> CreateShaderResourceDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown,
    uint32_t array_size = 0,
    uint32_t space = 0,
    uint32_t index = 0,
    uint32_t subpass_index = 0,
    uint32_t stage_mask = 0,
    CrossSchema::ShaderVariableType return_type = CrossSchema::ShaderVariableType::Float,
    bool depth_texture = false,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderResource> CreateShaderResource(flatbuffers::FlatBufferBuilder &_fbb, const ShaderResourceT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderConstantBufferT : public flatbuffers::NativeTable {
  typedef ShaderConstantBuffer TableType;
  std::string name{};
  CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown;
  uint32_t size = 0;
  std::vector<std::unique_ptr<CrossSchema::ShaderVariableT>> members{};
  uint32_t space = 0;
  uint32_t index = 0;
  uint32_t stage_mask = 0;
  uint32_t array_size = 1;
  std::unique_ptr<CrossSchema::ShaderStructTypeT> struct_type{};
};

struct ShaderConstantBuffer FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderConstantBufferT NativeTableType;
  typedef ShaderConstantBufferBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TYPE = 6,
    VT_SIZE = 8,
    VT_MEMBERS = 10,
    VT_SPACE = 12,
    VT_INDEX = 14,
    VT_STAGE_MASK = 16,
    VT_ARRAY_SIZE = 18,
    VT_STRUCT_TYPE = 20
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API bool KeyCompareLessThan(const ShaderConstantBuffer *o) const;
CROSS_SCHEMA_API int KeyCompareWithValue(const char *val) const;
CROSS_SCHEMA_API  CrossSchema::ShaderResourceType type() const;
  CROSS_SCHEMA_API  bool mutate_type(CrossSchema::ShaderResourceType _type);
CROSS_SCHEMA_API  uint32_t size() const;
  CROSS_SCHEMA_API  bool mutate_size(uint32_t _size);
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *members() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *mutable_members();
CROSS_SCHEMA_API  uint32_t space() const;
  CROSS_SCHEMA_API  bool mutate_space(uint32_t _space);
CROSS_SCHEMA_API  uint32_t index() const;
  CROSS_SCHEMA_API  bool mutate_index(uint32_t _index);
CROSS_SCHEMA_API  uint32_t stage_mask() const;
  CROSS_SCHEMA_API  bool mutate_stage_mask(uint32_t _stage_mask);
CROSS_SCHEMA_API  uint32_t array_size() const;
  CROSS_SCHEMA_API  bool mutate_array_size(uint32_t _array_size);
CROSS_SCHEMA_API  const CrossSchema::ShaderStructType *struct_type() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderStructType *mutable_struct_type();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderConstantBufferT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderConstantBufferT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderConstantBufferT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderConstantBufferBuilder {
  typedef ShaderConstantBuffer Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_type(CrossSchema::ShaderResourceType type);
  void add_size(uint32_t size);
  void add_members(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> members);
  void add_space(uint32_t space);
  void add_index(uint32_t index);
  void add_stage_mask(uint32_t stage_mask);
  void add_array_size(uint32_t array_size);
  void add_struct_type(flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type);
  explicit ShaderConstantBufferBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderConstantBuffer> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderConstantBuffer>(end);
    fbb_.Required(o, ShaderConstantBuffer::VT_NAME);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBuffer(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown,
    uint32_t size = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> members = 0,
    uint32_t space = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0,
    uint32_t array_size = 1,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type = 0);
struct ShaderConstantBuffer::Traits {
  using type = ShaderConstantBuffer;
  static auto constexpr Create = CreateShaderConstantBuffer;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBufferDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    CrossSchema::ShaderResourceType type = CrossSchema::ShaderResourceType::Unknown,
    uint32_t size = 0,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *members = nullptr,
    uint32_t space = 0,
    uint32_t index = 0,
    uint32_t stage_mask = 0,
    uint32_t array_size = 1,
    flatbuffers::Offset<CrossSchema::ShaderStructType> struct_type = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderConstantBuffer> CreateShaderConstantBuffer(flatbuffers::FlatBufferBuilder &_fbb, const ShaderConstantBufferT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct CombinedSamplerT : public flatbuffers::NativeTable {
  typedef CombinedSampler TableType;
  std::string name{};
  std::string texture_name{};
  std::string sampler_name{};
  uint32_t stage_mask = 0;
};

struct CombinedSampler FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef CombinedSamplerT NativeTableType;
  typedef CombinedSamplerBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_TEXTURE_NAME = 6,
    VT_SAMPLER_NAME = 8,
    VT_STAGE_MASK = 10
  };
CROSS_SCHEMA_API  const flatbuffers::String *name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_name();
CROSS_SCHEMA_API  const flatbuffers::String *texture_name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_texture_name();
CROSS_SCHEMA_API  const flatbuffers::String *sampler_name() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_sampler_name();
CROSS_SCHEMA_API  uint32_t stage_mask() const;
  CROSS_SCHEMA_API  bool mutate_stage_mask(uint32_t _stage_mask);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API CombinedSamplerT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(CombinedSamplerT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> Pack(flatbuffers::FlatBufferBuilder &_fbb, const CombinedSamplerT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API CombinedSamplerBuilder {
  typedef CombinedSampler Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_name(flatbuffers::Offset<flatbuffers::String> name);
  void add_texture_name(flatbuffers::Offset<flatbuffers::String> texture_name);
  void add_sampler_name(flatbuffers::Offset<flatbuffers::String> sampler_name);
  void add_stage_mask(uint32_t stage_mask);
  explicit CombinedSamplerBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<CombinedSampler> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<CombinedSampler>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> CreateCombinedSampler(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::String> name = 0,
    flatbuffers::Offset<flatbuffers::String> texture_name = 0,
    flatbuffers::Offset<flatbuffers::String> sampler_name = 0,
    uint32_t stage_mask = 0);
struct CombinedSampler::Traits {
  using type = CombinedSampler;
  static auto constexpr Create = CreateCombinedSampler;
};

CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> CreateCombinedSamplerDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *texture_name = nullptr,
    const char *sampler_name = nullptr,
    uint32_t stage_mask = 0);
CROSS_SCHEMA_API flatbuffers::Offset<CombinedSampler> CreateCombinedSampler(flatbuffers::FlatBufferBuilder &_fbb, const CombinedSamplerT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderLayoutT : public flatbuffers::NativeTable {
  typedef ShaderLayout TableType;
  std::vector<std::unique_ptr<CrossSchema::ShaderConstantBufferT>> constant_buffers{};
  std::vector<std::unique_ptr<CrossSchema::ShaderResourceT>> resources{};
  std::vector<std::unique_ptr<CrossSchema::CombinedSamplerT>> combined_samplers{};
  std::unique_ptr<CrossSchema::ShaderConstantBufferT> specialization_constants{};
};

struct ShaderLayout FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderLayoutT NativeTableType;
  typedef ShaderLayoutBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CONSTANT_BUFFERS = 4,
    VT_RESOURCES = 6,
    VT_COMBINED_SAMPLERS = 8,
    VT_SPECIALIZATION_CONSTANTS = 10
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *constant_buffers() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *mutable_constant_buffers();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *resources() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *mutable_resources();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *combined_samplers() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *mutable_combined_samplers();
CROSS_SCHEMA_API  const CrossSchema::ShaderConstantBuffer *specialization_constants() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderConstantBuffer *mutable_specialization_constants();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderLayoutT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderLayoutT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderLayoutT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderLayoutBuilder {
  typedef ShaderLayout Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_constant_buffers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>>> constant_buffers);
  void add_resources(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>>> resources);
  void add_combined_samplers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>>> combined_samplers);
  void add_specialization_constants(flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants);
  explicit ShaderLayoutBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderLayout> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderLayout>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> CreateShaderLayout(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>>> constant_buffers = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderResource>>> resources = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::CombinedSampler>>> combined_samplers = 0,
    flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants = 0);
struct ShaderLayout::Traits {
  using type = ShaderLayout;
  static auto constexpr Create = CreateShaderLayout;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> CreateShaderLayoutDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderConstantBuffer>> *constant_buffers = nullptr,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderResource>> *resources = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::CombinedSampler>> *combined_samplers = nullptr,
    flatbuffers::Offset<CrossSchema::ShaderConstantBuffer> specialization_constants = 0);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderLayout> CreateShaderLayout(flatbuffers::FlatBufferBuilder &_fbb, const ShaderLayoutT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ShaderCodeT : public flatbuffers::NativeTable {
  typedef ShaderCode TableType;
  CrossSchema::ShaderStageBit stage = CrossSchema::ShaderStageBit::Unknown;
  std::string entry_point{};
  std::vector<uint8_t> code_data{};
  std::vector<std::unique_ptr<CrossSchema::ShaderVariableT>> stage_inputs{};
  std::vector<std::unique_ptr<CrossSchema::ShaderVariableT>> stage_outputs{};
  bool debug_symbol = false;
};

struct ShaderCode FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ShaderCodeT NativeTableType;
  typedef ShaderCodeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STAGE = 4,
    VT_ENTRY_POINT = 6,
    VT_CODE_DATA = 8,
    VT_STAGE_INPUTS = 10,
    VT_STAGE_OUTPUTS = 12,
    VT_DEBUG_SYMBOL = 14
  };
CROSS_SCHEMA_API  CrossSchema::ShaderStageBit stage() const;
  CROSS_SCHEMA_API  bool mutate_stage(CrossSchema::ShaderStageBit _stage);
CROSS_SCHEMA_API  const flatbuffers::String *entry_point() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_entry_point();
CROSS_SCHEMA_API  const flatbuffers::Vector<uint8_t> *code_data() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<uint8_t> *mutable_code_data();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_inputs() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *mutable_stage_inputs();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_outputs() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *mutable_stage_outputs();
CROSS_SCHEMA_API  bool debug_symbol() const;
  CROSS_SCHEMA_API  bool mutate_debug_symbol(bool _debug_symbol);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ShaderCodeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ShaderCodeBuilder {
  typedef ShaderCode Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_stage(CrossSchema::ShaderStageBit stage);
  void add_entry_point(flatbuffers::Offset<flatbuffers::String> entry_point);
  void add_code_data(flatbuffers::Offset<flatbuffers::Vector<uint8_t>> code_data);
  void add_stage_inputs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_inputs);
  void add_stage_outputs(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_outputs);
  void add_debug_symbol(bool debug_symbol);
  explicit ShaderCodeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ShaderCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ShaderCode>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> CreateShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    CrossSchema::ShaderStageBit stage = CrossSchema::ShaderStageBit::Unknown,
    flatbuffers::Offset<flatbuffers::String> entry_point = 0,
    flatbuffers::Offset<flatbuffers::Vector<uint8_t>> code_data = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_inputs = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ShaderVariable>>> stage_outputs = 0,
    bool debug_symbol = false);
struct ShaderCode::Traits {
  using type = ShaderCode;
  static auto constexpr Create = CreateShaderCode;
};

CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> CreateShaderCodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    CrossSchema::ShaderStageBit stage = CrossSchema::ShaderStageBit::Unknown,
    const char *entry_point = nullptr,
    const std::vector<uint8_t> *code_data = nullptr,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_inputs = nullptr,
    std::vector<flatbuffers::Offset<CrossSchema::ShaderVariable>> *stage_outputs = nullptr,
    bool debug_symbol = false);
CROSS_SCHEMA_API flatbuffers::Offset<ShaderCode> CreateShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const ShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct GraphicsShaderCodeT : public flatbuffers::NativeTable {
  typedef GraphicsShaderCode TableType;
  std::unique_ptr<CrossSchema::GUID> guid{};
  uint64_t active_keywords = 0;
  std::unique_ptr<CrossSchema::ShaderLayoutT> layout{};
  std::unique_ptr<CrossSchema::ShaderCodeT> vertex_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> hull_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> domain_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> geometry_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> pixel_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> task_shader{};
  std::unique_ptr<CrossSchema::ShaderCodeT> mesh_shader{};
  uint64_t mtime = 0;
};

struct GraphicsShaderCode FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef GraphicsShaderCodeT NativeTableType;
  typedef GraphicsShaderCodeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GUID = 4,
    VT_ACTIVE_KEYWORDS = 6,
    VT_LAYOUT = 8,
    VT_VERTEX_SHADER = 10,
    VT_HULL_SHADER = 12,
    VT_DOMAIN_SHADER = 14,
    VT_GEOMETRY_SHADER = 16,
    VT_PIXEL_SHADER = 18,
    VT_TASK_SHADER = 20,
    VT_MESH_SHADER = 22,
    VT_MTIME = 24
  };
CROSS_SCHEMA_API  const CrossSchema::GUID *guid() const;
  CROSS_SCHEMA_API  CrossSchema::GUID *mutable_guid();
CROSS_SCHEMA_API  uint64_t active_keywords() const;
  CROSS_SCHEMA_API  bool mutate_active_keywords(uint64_t _active_keywords);
CROSS_SCHEMA_API  const CrossSchema::ShaderLayout *layout() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderLayout *mutable_layout();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *vertex_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_vertex_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *hull_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_hull_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *domain_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_domain_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *geometry_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_geometry_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *pixel_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_pixel_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *task_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_task_shader();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *mesh_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_mesh_shader();
CROSS_SCHEMA_API  uint64_t mtime() const;
  CROSS_SCHEMA_API  bool mutate_mtime(uint64_t _mtime);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API GraphicsShaderCodeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(GraphicsShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderCode> Pack(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API GraphicsShaderCodeBuilder {
  typedef GraphicsShaderCode Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_guid(const CrossSchema::GUID *guid);
  void add_active_keywords(uint64_t active_keywords);
  void add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout);
  void add_vertex_shader(flatbuffers::Offset<CrossSchema::ShaderCode> vertex_shader);
  void add_hull_shader(flatbuffers::Offset<CrossSchema::ShaderCode> hull_shader);
  void add_domain_shader(flatbuffers::Offset<CrossSchema::ShaderCode> domain_shader);
  void add_geometry_shader(flatbuffers::Offset<CrossSchema::ShaderCode> geometry_shader);
  void add_pixel_shader(flatbuffers::Offset<CrossSchema::ShaderCode> pixel_shader);
  void add_task_shader(flatbuffers::Offset<CrossSchema::ShaderCode> task_shader);
  void add_mesh_shader(flatbuffers::Offset<CrossSchema::ShaderCode> mesh_shader);
  void add_mtime(uint64_t mtime);
  explicit GraphicsShaderCodeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<GraphicsShaderCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GraphicsShaderCode>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderCode> CreateGraphicsShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    uint64_t active_keywords = 0,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> vertex_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> hull_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> domain_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> geometry_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> pixel_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> task_shader = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> mesh_shader = 0,
    uint64_t mtime = 0);
struct GraphicsShaderCode::Traits {
  using type = GraphicsShaderCode;
  static auto constexpr Create = CreateGraphicsShaderCode;
};

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderCode> CreateGraphicsShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PlatformGraphicsShaderT : public flatbuffers::NativeTable {
  typedef PlatformGraphicsShader TableType;
  std::unique_ptr<CrossSchema::ShaderVersion> version{};
  std::vector<std::string> keywords{};
  std::vector<std::unique_ptr<CrossSchema::GraphicsShaderCodeT>> variants{};
};

struct PlatformGraphicsShader FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PlatformGraphicsShaderT NativeTableType;
  typedef PlatformGraphicsShaderBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION = 4,
    VT_KEYWORDS = 6,
    VT_VARIANTS = 8
  };
CROSS_SCHEMA_API  const CrossSchema::ShaderVersion *version() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderVersion *mutable_version();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *keywords() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_keywords();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *variants() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *mutable_variants();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PlatformGraphicsShaderT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PlatformGraphicsShaderT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformGraphicsShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PlatformGraphicsShaderBuilder {
  typedef PlatformGraphicsShader Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_version(const CrossSchema::ShaderVersion *version);
  void add_keywords(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> keywords);
  void add_variants(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>>> variants);
  explicit PlatformGraphicsShaderBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PlatformGraphicsShader> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlatformGraphicsShader>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> keywords = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>>> variants = 0);
struct PlatformGraphicsShader::Traits {
  using type = PlatformGraphicsShader;
  static auto constexpr Create = CreatePlatformGraphicsShader;
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShaderDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version = 0,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *keywords = nullptr,
    const std::vector<flatbuffers::Offset<CrossSchema::GraphicsShaderCode>> *variants = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<PlatformGraphicsShader> CreatePlatformGraphicsShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformGraphicsShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct GraphicsShaderAssetT : public flatbuffers::NativeTable {
  typedef GraphicsShaderAsset TableType;
  std::vector<std::unique_ptr<CrossSchema::PlatformGraphicsShaderT>> platform_shaders{};
  uint64_t mtime = 0;
};

struct GraphicsShaderAsset FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef GraphicsShaderAssetT NativeTableType;
  typedef GraphicsShaderAssetBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PLATFORM_SHADERS = 4,
    VT_MTIME = 6
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *platform_shaders() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *mutable_platform_shaders();
CROSS_SCHEMA_API  uint64_t mtime() const;
  CROSS_SCHEMA_API  bool mutate_mtime(uint64_t _mtime);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API GraphicsShaderAssetT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(GraphicsShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> Pack(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API GraphicsShaderAssetBuilder {
  typedef GraphicsShaderAsset Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>>> platform_shaders);
  void add_mtime(uint64_t mtime);
  explicit GraphicsShaderAssetBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<GraphicsShaderAsset> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<GraphicsShaderAsset>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>>> platform_shaders = 0,
    uint64_t mtime = 0);
struct GraphicsShaderAsset::Traits {
  using type = GraphicsShaderAsset;
  static auto constexpr Create = CreateGraphicsShaderAsset;
};

CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformGraphicsShader>> *platform_shaders = nullptr,
    uint64_t mtime = 0);
CROSS_SCHEMA_API flatbuffers::Offset<GraphicsShaderAsset> CreateGraphicsShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const GraphicsShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ComputeShaderCodeT : public flatbuffers::NativeTable {
  typedef ComputeShaderCode TableType;
  std::unique_ptr<CrossSchema::GUID> guid{};
  std::unique_ptr<CrossSchema::ShaderLayoutT> layout{};
  std::unique_ptr<CrossSchema::uint3> group_size{};
  std::unique_ptr<CrossSchema::ShaderCodeT> compute_shader{};
  uint64_t mtime = 0;
};

struct ComputeShaderCode FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ComputeShaderCodeT NativeTableType;
  typedef ComputeShaderCodeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GUID = 4,
    VT_LAYOUT = 6,
    VT_GROUP_SIZE = 8,
    VT_COMPUTE_SHADER = 10,
    VT_MTIME = 12
  };
CROSS_SCHEMA_API  const CrossSchema::GUID *guid() const;
  CROSS_SCHEMA_API  CrossSchema::GUID *mutable_guid();
CROSS_SCHEMA_API  const CrossSchema::ShaderLayout *layout() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderLayout *mutable_layout();
CROSS_SCHEMA_API  const CrossSchema::uint3 *group_size() const;
  CROSS_SCHEMA_API  CrossSchema::uint3 *mutable_group_size();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *compute_shader() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_compute_shader();
CROSS_SCHEMA_API  uint64_t mtime() const;
  CROSS_SCHEMA_API  bool mutate_mtime(uint64_t _mtime);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ComputeShaderCodeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ComputeShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderCode> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ComputeShaderCodeBuilder {
  typedef ComputeShaderCode Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_guid(const CrossSchema::GUID *guid);
  void add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout);
  void add_group_size(const CrossSchema::uint3 *group_size);
  void add_compute_shader(flatbuffers::Offset<CrossSchema::ShaderCode> compute_shader);
  void add_mtime(uint64_t mtime);
  explicit ComputeShaderCodeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ComputeShaderCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ComputeShaderCode>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderCode> CreateComputeShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout = 0,
    const CrossSchema::uint3 *group_size = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> compute_shader = 0,
    uint64_t mtime = 0);
struct ComputeShaderCode::Traits {
  using type = ComputeShaderCode;
  static auto constexpr Create = CreateComputeShaderCode;
};

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderCode> CreateComputeShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PlatformComputeShaderT : public flatbuffers::NativeTable {
  typedef PlatformComputeShader TableType;
  std::unique_ptr<CrossSchema::ShaderVersion> version{};
  std::vector<std::unique_ptr<CrossSchema::ComputeShaderCodeT>> code_list{};
};

struct PlatformComputeShader FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PlatformComputeShaderT NativeTableType;
  typedef PlatformComputeShaderBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION = 4,
    VT_CODE_LIST = 6
  };
CROSS_SCHEMA_API  const CrossSchema::ShaderVersion *version() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderVersion *mutable_version();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *code_list() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *mutable_code_list();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PlatformComputeShaderT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PlatformComputeShaderT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformComputeShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PlatformComputeShaderBuilder {
  typedef PlatformComputeShader Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_version(const CrossSchema::ShaderVersion *version);
  void add_code_list(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>>> code_list);
  explicit PlatformComputeShaderBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PlatformComputeShader> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlatformComputeShader>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>>> code_list = 0);
struct PlatformComputeShader::Traits {
  using type = PlatformComputeShader;
  static auto constexpr Create = CreatePlatformComputeShader;
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShaderDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version = 0,
    const std::vector<flatbuffers::Offset<CrossSchema::ComputeShaderCode>> *code_list = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<PlatformComputeShader> CreatePlatformComputeShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformComputeShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct ComputeShaderAssetT : public flatbuffers::NativeTable {
  typedef ComputeShaderAsset TableType;
  std::vector<std::unique_ptr<CrossSchema::PlatformComputeShaderT>> platform_shaders{};
};

struct ComputeShaderAsset FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef ComputeShaderAssetT NativeTableType;
  typedef ComputeShaderAssetBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PLATFORM_SHADERS = 4
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *platform_shaders() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *mutable_platform_shaders();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API ComputeShaderAssetT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(ComputeShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> Pack(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API ComputeShaderAssetBuilder {
  typedef ComputeShaderAsset Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>>> platform_shaders);
  explicit ComputeShaderAssetBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<ComputeShaderAsset> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<ComputeShaderAsset>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>>> platform_shaders = 0);
struct ComputeShaderAsset::Traits {
  using type = ComputeShaderAsset;
  static auto constexpr Create = CreateComputeShaderAsset;
};

CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformComputeShader>> *platform_shaders = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<ComputeShaderAsset> CreateComputeShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const ComputeShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct DXILReflectionCodeT : public flatbuffers::NativeTable {
  typedef DXILReflectionCode TableType;
  std::unique_ptr<CrossSchema::uint3> group_size{};
  std::unique_ptr<CrossSchema::ShaderLayoutT> layout{};
};

struct DXILReflectionCode FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef DXILReflectionCodeT NativeTableType;
  typedef DXILReflectionCodeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GROUP_SIZE = 4,
    VT_LAYOUT = 6
  };
CROSS_SCHEMA_API  const CrossSchema::uint3 *group_size() const;
  CROSS_SCHEMA_API  CrossSchema::uint3 *mutable_group_size();
CROSS_SCHEMA_API  const CrossSchema::ShaderLayout *layout() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderLayout *mutable_layout();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API DXILReflectionCodeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(DXILReflectionCodeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<DXILReflectionCode> Pack(flatbuffers::FlatBufferBuilder &_fbb, const DXILReflectionCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API DXILReflectionCodeBuilder {
  typedef DXILReflectionCode Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_group_size(const CrossSchema::uint3 *group_size);
  void add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout);
  explicit DXILReflectionCodeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<DXILReflectionCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<DXILReflectionCode>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<DXILReflectionCode> CreateDXILReflectionCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::uint3 *group_size = 0,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout = 0);
struct DXILReflectionCode::Traits {
  using type = DXILReflectionCode;
  static auto constexpr Create = CreateDXILReflectionCode;
};

CROSS_SCHEMA_API flatbuffers::Offset<DXILReflectionCode> CreateDXILReflectionCode(flatbuffers::FlatBufferBuilder &_fbb, const DXILReflectionCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RayTracingShaderCodeT : public flatbuffers::NativeTable {
  typedef RayTracingShaderCode TableType;
  std::unique_ptr<CrossSchema::GUID> guid{};
  std::unique_ptr<CrossSchema::ShaderLayoutT> layout{};
  std::unique_ptr<CrossSchema::ShaderCodeT> code{};
  std::string raygen_shader_entry{};
  std::vector<std::string> closesthit_shader_entries{};
  std::vector<std::string> anyhit_shader_entries{};
  std::vector<std::string> miss_shader_entries{};
  std::vector<std::string> callable_shader_entries{};
  std::vector<std::string> intersection_shader_entries{};
  uint64_t mtime = 0;
};

struct RayTracingShaderCode FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef RayTracingShaderCodeT NativeTableType;
  typedef RayTracingShaderCodeBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GUID = 4,
    VT_LAYOUT = 6,
    VT_CODE = 8,
    VT_RAYGEN_SHADER_ENTRY = 10,
    VT_CLOSESTHIT_SHADER_ENTRIES = 12,
    VT_ANYHIT_SHADER_ENTRIES = 14,
    VT_MISS_SHADER_ENTRIES = 16,
    VT_CALLABLE_SHADER_ENTRIES = 18,
    VT_INTERSECTION_SHADER_ENTRIES = 20,
    VT_MTIME = 22
  };
CROSS_SCHEMA_API  const CrossSchema::GUID *guid() const;
  CROSS_SCHEMA_API  CrossSchema::GUID *mutable_guid();
CROSS_SCHEMA_API  const CrossSchema::ShaderLayout *layout() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderLayout *mutable_layout();
CROSS_SCHEMA_API  const CrossSchema::ShaderCode *code() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderCode *mutable_code();
CROSS_SCHEMA_API  const flatbuffers::String *raygen_shader_entry() const;
  CROSS_SCHEMA_API  flatbuffers::String *mutable_raygen_shader_entry();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *closesthit_shader_entries() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_closesthit_shader_entries();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *anyhit_shader_entries() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_anyhit_shader_entries();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *miss_shader_entries() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_miss_shader_entries();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *callable_shader_entries() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_callable_shader_entries();
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *intersection_shader_entries() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>> *mutable_intersection_shader_entries();
CROSS_SCHEMA_API  uint64_t mtime() const;
  CROSS_SCHEMA_API  bool mutate_mtime(uint64_t _mtime);
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API RayTracingShaderCodeT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(RayTracingShaderCodeT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderCodeT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API RayTracingShaderCodeBuilder {
  typedef RayTracingShaderCode Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_guid(const CrossSchema::GUID *guid);
  void add_layout(flatbuffers::Offset<CrossSchema::ShaderLayout> layout);
  void add_code(flatbuffers::Offset<CrossSchema::ShaderCode> code);
  void add_raygen_shader_entry(flatbuffers::Offset<flatbuffers::String> raygen_shader_entry);
  void add_closesthit_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> closesthit_shader_entries);
  void add_anyhit_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> anyhit_shader_entries);
  void add_miss_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> miss_shader_entries);
  void add_callable_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> callable_shader_entries);
  void add_intersection_shader_entries(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> intersection_shader_entries);
  void add_mtime(uint64_t mtime);
  explicit RayTracingShaderCodeBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RayTracingShaderCode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RayTracingShaderCode>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCode(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> code = 0,
    flatbuffers::Offset<flatbuffers::String> raygen_shader_entry = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> closesthit_shader_entries = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> anyhit_shader_entries = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> miss_shader_entries = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> callable_shader_entries = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<flatbuffers::String>>> intersection_shader_entries = 0,
    uint64_t mtime = 0);
struct RayTracingShaderCode::Traits {
  using type = RayTracingShaderCode;
  static auto constexpr Create = CreateRayTracingShaderCode;
};

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCodeDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::GUID *guid = 0,
    flatbuffers::Offset<CrossSchema::ShaderLayout> layout = 0,
    flatbuffers::Offset<CrossSchema::ShaderCode> code = 0,
    const char *raygen_shader_entry = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *closesthit_shader_entries = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *anyhit_shader_entries = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *miss_shader_entries = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *callable_shader_entries = nullptr,
    const std::vector<flatbuffers::Offset<flatbuffers::String>> *intersection_shader_entries = nullptr,
    uint64_t mtime = 0);
CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderCode> CreateRayTracingShaderCode(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderCodeT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct PlatformRayTracingShaderT : public flatbuffers::NativeTable {
  typedef PlatformRayTracingShader TableType;
  std::unique_ptr<CrossSchema::ShaderVersion> version{};
  std::unique_ptr<CrossSchema::RayTracingShaderCodeT> shader_code{};
};

struct PlatformRayTracingShader FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef PlatformRayTracingShaderT NativeTableType;
  typedef PlatformRayTracingShaderBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION = 4,
    VT_SHADER_CODE = 6
  };
CROSS_SCHEMA_API  const CrossSchema::ShaderVersion *version() const;
  CROSS_SCHEMA_API  CrossSchema::ShaderVersion *mutable_version();
CROSS_SCHEMA_API  const CrossSchema::RayTracingShaderCode *shader_code() const;
  CROSS_SCHEMA_API  CrossSchema::RayTracingShaderCode *mutable_shader_code();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API PlatformRayTracingShaderT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(PlatformRayTracingShaderT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<PlatformRayTracingShader> Pack(flatbuffers::FlatBufferBuilder &_fbb, const PlatformRayTracingShaderT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API PlatformRayTracingShaderBuilder {
  typedef PlatformRayTracingShader Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_version(const CrossSchema::ShaderVersion *version);
  void add_shader_code(flatbuffers::Offset<CrossSchema::RayTracingShaderCode> shader_code);
  explicit PlatformRayTracingShaderBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<PlatformRayTracingShader> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<PlatformRayTracingShader>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformRayTracingShader> CreatePlatformRayTracingShader(
    flatbuffers::FlatBufferBuilder &_fbb,
    const CrossSchema::ShaderVersion *version = 0,
    flatbuffers::Offset<CrossSchema::RayTracingShaderCode> shader_code = 0);
struct PlatformRayTracingShader::Traits {
  using type = PlatformRayTracingShader;
  static auto constexpr Create = CreatePlatformRayTracingShader;
};

CROSS_SCHEMA_API flatbuffers::Offset<PlatformRayTracingShader> CreatePlatformRayTracingShader(flatbuffers::FlatBufferBuilder &_fbb, const PlatformRayTracingShaderT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RayTracingShaderAssetT : public flatbuffers::NativeTable {
  typedef RayTracingShaderAsset TableType;
  std::vector<std::unique_ptr<CrossSchema::PlatformRayTracingShaderT>> platform_shaders{};
};

struct RayTracingShaderAsset FLATBUFFERS_FINAL_CLASS : public flatbuffers::Table {
  typedef RayTracingShaderAssetT NativeTableType;
  typedef RayTracingShaderAssetBuilder Builder;
  struct Traits;
  static const flatbuffers::TypeTable *MiniReflectTypeTable();
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PLATFORM_SHADERS = 4
  };
CROSS_SCHEMA_API  const flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *platform_shaders() const;
  CROSS_SCHEMA_API  flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *mutable_platform_shaders();
  CROSS_SCHEMA_API bool Verify(flatbuffers::Verifier &verifier) const;
  CROSS_SCHEMA_API RayTracingShaderAssetT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  CROSS_SCHEMA_API void UnPackTo(RayTracingShaderAssetT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderAssetT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct CROSS_SCHEMA_API RayTracingShaderAssetBuilder {
  typedef RayTracingShaderAsset Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_platform_shaders(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>>> platform_shaders);
  explicit RayTracingShaderAssetBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RayTracingShaderAsset> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RayTracingShaderAsset>(end);
    return o;
  }
};

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAsset(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>>> platform_shaders = 0);
struct RayTracingShaderAsset::Traits {
  using type = RayTracingShaderAsset;
  static auto constexpr Create = CreateRayTracingShaderAsset;
};

CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAssetDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<flatbuffers::Offset<CrossSchema::PlatformRayTracingShader>> *platform_shaders = nullptr);
CROSS_SCHEMA_API flatbuffers::Offset<RayTracingShaderAsset> CreateRayTracingShaderAsset(flatbuffers::FlatBufferBuilder &_fbb, const RayTracingShaderAssetT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline const flatbuffers::TypeTable *ShaderVariableTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVariableTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "type",
    "offset",
    "row_count",
    "col_count",
    "size",
    "array_size",
    "index",
    "stage_mask"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 9, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderVariableExTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVariableTypeTypeTable,
    CrossSchema::ShaderVariableExTypeTable
  };
  static const char * const names[] = {
    "name",
    "offset",
    "size",
    "type",
    "row_count",
    "col_count",
    "members",
    "array_size",
    "array_stride",
    "index",
    "stage_mask"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 11, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderStructTypeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVariableExTypeTable
  };
  static const char * const names[] = {
    "size",
    "members"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderResourceTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, 1 },
    { flatbuffers::ET_BOOL, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderResourceTypeTypeTable,
    CrossSchema::ShaderVariableTypeTypeTable,
    CrossSchema::ShaderStructTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "type",
    "array_size",
    "space",
    "index",
    "subpass_index",
    "stage_mask",
    "return_type",
    "depth_texture",
    "struct_type"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 10, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderConstantBufferTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderResourceTypeTypeTable,
    CrossSchema::ShaderVariableTypeTable,
    CrossSchema::ShaderStructTypeTypeTable
  };
  static const char * const names[] = {
    "name",
    "type",
    "size",
    "members",
    "space",
    "index",
    "stage_mask",
    "array_size",
    "struct_type"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 9, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *CombinedSamplerTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UINT, 0, -1 }
  };
  static const char * const names[] = {
    "name",
    "texture_name",
    "sampler_name",
    "stage_mask"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, nullptr, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderLayoutTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_SEQUENCE, 1, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderConstantBufferTypeTable,
    CrossSchema::ShaderResourceTypeTable,
    CrossSchema::CombinedSamplerTypeTable
  };
  static const char * const names[] = {
    "constant_buffers",
    "resources",
    "combined_samplers",
    "specialization_constants"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 4, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ShaderCodeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_UINT, 0, 0 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_UCHAR, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 },
    { flatbuffers::ET_BOOL, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderStageBitTypeTable,
    CrossSchema::ShaderVariableTypeTable
  };
  static const char * const names[] = {
    "stage",
    "entry_point",
    "code_data",
    "stage_inputs",
    "stage_outputs",
    "debug_symbol"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 6, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *GraphicsShaderCodeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_ULONG, 0, -1 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_ULONG, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::GUIDTypeTable,
    CrossSchema::ShaderLayoutTypeTable,
    CrossSchema::ShaderCodeTypeTable
  };
  static const char * const names[] = {
    "guid",
    "active_keywords",
    "layout",
    "vertex_shader",
    "hull_shader",
    "domain_shader",
    "geometry_shader",
    "pixel_shader",
    "task_shader",
    "mesh_shader",
    "mtime"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 11, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PlatformGraphicsShaderTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVersionTypeTable,
    CrossSchema::GraphicsShaderCodeTypeTable
  };
  static const char * const names[] = {
    "version",
    "keywords",
    "variants"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 3, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *GraphicsShaderAssetTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 },
    { flatbuffers::ET_ULONG, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::PlatformGraphicsShaderTypeTable
  };
  static const char * const names[] = {
    "platform_shaders",
    "mtime"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ComputeShaderCodeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_SEQUENCE, 0, 3 },
    { flatbuffers::ET_ULONG, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::GUIDTypeTable,
    CrossSchema::ShaderLayoutTypeTable,
    CrossSchema::uint3TypeTable,
    CrossSchema::ShaderCodeTypeTable
  };
  static const char * const names[] = {
    "guid",
    "layout",
    "group_size",
    "compute_shader",
    "mtime"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 5, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PlatformComputeShaderTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 1, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVersionTypeTable,
    CrossSchema::ComputeShaderCodeTypeTable
  };
  static const char * const names[] = {
    "version",
    "code_list"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *ComputeShaderAssetTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::PlatformComputeShaderTypeTable
  };
  static const char * const names[] = {
    "platform_shaders"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *DXILReflectionCodeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::uint3TypeTable,
    CrossSchema::ShaderLayoutTypeTable
  };
  static const char * const names[] = {
    "group_size",
    "layout"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *RayTracingShaderCodeTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 },
    { flatbuffers::ET_SEQUENCE, 0, 2 },
    { flatbuffers::ET_STRING, 0, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_STRING, 1, -1 },
    { flatbuffers::ET_ULONG, 0, -1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::GUIDTypeTable,
    CrossSchema::ShaderLayoutTypeTable,
    CrossSchema::ShaderCodeTypeTable
  };
  static const char * const names[] = {
    "guid",
    "layout",
    "code",
    "raygen_shader_entry",
    "closesthit_shader_entries",
    "anyhit_shader_entries",
    "miss_shader_entries",
    "callable_shader_entries",
    "intersection_shader_entries",
    "mtime"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 10, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *PlatformRayTracingShaderTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 0, 0 },
    { flatbuffers::ET_SEQUENCE, 0, 1 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::ShaderVersionTypeTable,
    CrossSchema::RayTracingShaderCodeTypeTable
  };
  static const char * const names[] = {
    "version",
    "shader_code"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 2, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

inline const flatbuffers::TypeTable *RayTracingShaderAssetTypeTable() {
  static const flatbuffers::TypeCode type_codes[] = {
    { flatbuffers::ET_SEQUENCE, 1, 0 }
  };
  static const flatbuffers::TypeFunction type_refs[] = {
    CrossSchema::PlatformRayTracingShaderTypeTable
  };
  static const char * const names[] = {
    "platform_shaders"
  };
  static const flatbuffers::TypeTable tt = {
    flatbuffers::ST_TABLE, 1, type_codes, type_refs, nullptr, names
  };
  return &tt;
}

}  // namespace CrossSchema

#endif  // FLATBUFFERS_GENERATED_SHADERASSET_CROSSSCHEMA_H_
