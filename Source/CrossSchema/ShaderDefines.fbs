namespace CrossSchema;

enum ShaderResourceType : uint
{
	Unknown,

	Con<PERSON>Buffer,
	<PERSON>ureBuffer,
	
	TexelBuffer,
	RWTexelBuffer,
	StructuredBuffer,
	RWStructuredBuffer,
	ByteAddressBuffer,
	RWByteAddressBuffer,

	Texture1D,
	Texture1DArray,
	Texture2D,
	Texture2DArray,
	Texture2DMS,
	Texture2DMSArray,
	Texture3D,
	TextureCube,
	TextureCubeArray,
	RWTexture1D,
	RWTexture1DArray,
	RWTexture2D,
	RWTexture2DArray,
	RWTexture3D,

	Sampler,

	SubpassInput,
	SubpassInputMS,
	AccelStruct,
}

enum ShaderVariableType : uint
{
	Unknown,

	Bool,

	UInt8,
	UInt16,
	UInt32,
	UInt64,

	Int8,
	Int16,
	Int32,
	Int64,

	Half,
	Float,
	Double,

	Struct,
}

enum ShaderStageBit : uint
{
	Unknown,

    Vertex = 1,
    Hull = 2,
    Domain = 4,
    Geometry = 8,
    Pixel = 16,
	
    Compute = 32,

	Task = 64,
	Mesh = 128,
    RayGen = 256,
    ClosestHit = 512,
    AnyHit = 1024,
    Miss = 2048,
    Callable = 4096,
    InterSection = 8192,
    AllRayTracing = 16128,
    
    All = 0xffffffff,
}

enum ShaderCodeFormat : uint
{
	Unknown,

	GLSL,
	ESSL,
	MSL_IOS,
	MSL_OSX,

	DXBC,
	DXIL,
	SPIR_V,
	MTLLIB_IOS,
	MTLLIB_OSX,
}

struct ShaderVersion
{
	format:ShaderCodeFormat;
	major_version:uint;
	minor_version:uint;
}