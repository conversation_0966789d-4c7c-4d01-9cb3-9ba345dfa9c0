#pragma once

#include "Resource/AsyncResource.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureUDIM.h"
#include "Resource/Material.h"
#pragma warning(push)
#pragma warning(disable : 4091)

#include "CrossUI.h"

namespace cross::ui {

class CROSS_UI_API SDFFont
{
    struct SDFKerning
    {
        int first, second, amount;
    };

    struct KerningHash
    {
        int first, second;

        std::size_t hash() const noexcept
        {
            std::size_t h1 = std::hash<int>{}(first);
            std::size_t h2 = std::hash<int>{}(second);
            return h1 ^ (h2 << 1);
        }
    };

public:
    struct SDFChar
    {
        std::string char_code; //utf8 encoded
        int id, index;
        int x, y, width, height;
        int x_advance;
        int x_offset, y_offset;
    };

    explicit SDFFont(const char *source);

    ~SDFFont() {}

    int ComputeKerning(int preId, int nextId);

    SDFChar GetChar(int charCode);

public:
    // sdf canvas size
    UInt32 mTextureWidth, mTextureHeight;
    TexturePtr mTexture;

    int mSize;
    int mAscent, mDescent;
    int mBase;
    int mLineHeight;
    std::map<int, SDFChar> mCharMap;
    std::map<std::size_t, int> mKerningMap;
};

class CROSS_UI_API Font
{
public:
    Font(std::shared_ptr<SDFFont> sdf_font)
        : mSDFFont(sdf_font)
    {}

    ~Font() {}

    void SetSize(const int size);

    float ComputeWidth(const char *content, float* offset = nullptr);

public:
    std::shared_ptr<SDFFont> mSDFFont;
    int mSize = 12;
    float mRatio = 1.0;
    float mLeading = 1.0;
};

CROSS_UI_API Font* SetDefaultFont(Font* font);
CROSS_UI_API Font* GetDefaultFont();
}

#pragma warning(pop)