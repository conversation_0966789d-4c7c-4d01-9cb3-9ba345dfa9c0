#pragma once

#include "types.h"
#include "primitive_buffer.h"

#include <set>

namespace oui {

enum class CROSS_UI_API Keycode : int
{
    None = 0,
    Break = 3,
    Backspace = 8,
    Tab = 9,
    Clear = 12,
    Enter = 13,
    Shift = 16,
    Ctrl = 17,
    Alt = 18,
    Pause = 19,
    CapsLock = 20,
    Escape = 27,
    Space = 32,
    PageUp = 33,
    PageDown = 34,
    End = 35,
    Home = 36,
    Left = 37,
    Up = 38,
    Right = 39,
    Down = 40,
    SelectKey = 41,
    Print = 42,
    Execute = 43,
    PrintScreen = 44,
    Insert = 45,
    Delete = 46,
    help = 47,
    Key0 = 48,
    Key1 = 49,
    Key2 = 50,
    Key3 = 51,
    Key4 = 52,
    Key5 = 53,
    Key6 = 54,
    Key7 = 55,
    Key8 = 56,
    Key9 = 57,
    <PERSON>on = 58,
    <PERSON> = 60,
    At = 64,
    a = 65,
    b = 66,
    c = 67,
    d = 68,
    e = 69,
    f = 70,
    g = 71,
    h = 72,
    i = 73,
    j = 74,
    k = 75,
    l = 76,
    m = 77,
    n = 78,
    o = 79,
    p = 80,
    q = 81,
    r = 82,
    s = 83,
    t = 84,
    u = 85,
    v = 86,
    w = 87,
    x = 88,
    y = 89,
    z = 90,
    LeftCommand = 91,
    RightCommand = 93,
    Sleep = 95,
    Num0 = 96,
    Num1 = 97,
    Num2 = 98,
    Num3 = 99,
    Num4 = 100,
    Num5 = 101,
    Num6 = 102,
    Num7 = 103,
    Num8 = 104,
    Num9 = 105,
    Multiply = 106,
    Add = 107,
    Periodic = 108,
    Subtract = 109,
    Point = 110,
    Divide = 111,
    F1 = 112,
    F2 = 113,
    F3 = 114,
    F4 = 115,
    F5 = 116,
    F6 = 117,
    F7 = 118,
    F8 = 119,
    F9 = 120,
    F10 = 121,
    F11 = 122,
    F12 = 123,
    Semicolon = 186,
    Equal = 187,
    Comma = 188,
    Minus = 189,
    Period = 190,
    Slash = 191,
    BackQuote = 192,
    BracketL = 219,
    BackSlash = 220,
    BracketR = 221,
    Quote = 222,
};

const int SWAP_BUFFER_COUNT = 3;
class CROSS_UI_API UIState {
public:
    float average_delta_time = 1.f;

    ui_rect rect;
    int constraint_w = 0;
    int constraint_h = 0;

    int last_active = -1;
    int lost_active = -1;

    int double_click_id = -1;
    int double_click_frame_index = 0;
    int double_click_defer_frame = 18;

    float2 mouse_location{0.f, 0.f};
    float2 mouse_offset{0.f, 0.f};
    float2 mouse_start{0.f, 0.f};
    float mouse_wheel = 1.f;
    float mouse_wheel_raw = 0.f;

    bool left_mouse_press = false;
    bool left_mouse_release = false;
    bool right_mouse_press = false;
    bool right_mouse_release = false;
    bool middle_mouse_press = false;
    bool middle_mouse_release = false;

    bool left_mouse_pressed = false;
    bool middle_mouse_pressed = false;
    bool right_mouse_pressed = false;

    std::set<Keycode>* key_press;
    std::set<Keycode>* key_pressed;

    int next_hover = -1;
    int next_hover_layer_index = -1;
    int hover = -1;
    int hover_layer = -1;

    int active = -1;

    int _defer_update_frame_count = 8;
    int _defer_update_frame_index = 8;
    bool needs_update = true;

    int last_swap_index = 0;
    int swap_index = 0;
    primitive_buffer buffers[SWAP_BUFFER_COUNT];
    
    float smooth_factor = 0.05f;

public:
    UIState();
    ~UIState();

    void SetActive(u32 id);
    void ClearActive();
    void ClearMouseState();
    bool Update();
    void RequestUpdate();
    bool IsHovering(ui_rect rect);
};

}