#include "ui_app.h"

#include "ui_label.h"
#include "ui_button.h"
#include "ui_input.h"
#include "ui_select.h"

namespace oui {
    struct SampleState {
        UIButton* button;
        UIInput* input;
        UISelect* select;
        UINumberInput* number_input;
        UIRangeInput* range_input;
        UILabel* label;
        UILabel* status;
    };

void ui_app_sample(UIState& state, UIWindow& window, ui_rect rect) {
    if (window.app_state == nullptr) {
        SampleState* sample_state = new SampleState();

        sample_state->input = new UIInput("");
        sample_state->input->radius = { 3.0f, 3.f, 3.f, 3.f };
        sample_state->input->label.constraint.alignment = UIAlignment(UIAlignment::CENTER_VERTICAL | UIAlignment::LEFT);
        sample_state->input->label.padding.left = 4.f;
        sample_state->number_input = new UINumberInput();
        sample_state->range_input = new UIRangeInput();
            
        sample_state->button = new UIButton("Click");

        sample_state->select = new UISelect();
        sample_state->label = new UILabel("Sample Window");
        sample_state->status = new UILabel("");

        window.app_state = sample_state;
    }

    ui_theme* theme = ui_theme_current();
    SampleState* sample_state = reinterpret_cast<SampleState*>(window.app_state);

    const float item_height = 28.f;

    ui_rect draw_rect = rect;
    draw_rect.h = item_height;
    draw_rect.x += 20;
    draw_rect.w = 100;

    ui_rect item_rect = draw_rect;
    item_rect = ui_rect_shrink(item_rect, 3, 3);

    sample_state->status->SetContent("hover: " + std::to_string(state.hover) + "  active: " + std::to_string(state.active));
    ui_label(state, *sample_state->status, item_rect);
    item_rect.y += item_height;

    ui_label(state, *sample_state->label, item_rect);
    item_rect.y += item_height;

    if (ui_button(state, *sample_state->button, theme->btn, item_rect)) {
        sample_state->label->SetContent("button clicked");
    }
    item_rect.y += item_height;

    ui_rect number_input_rect = item_rect;
    number_input_rect.w = 200;
    if (ui_number_input(state, *sample_state->number_input, theme->btn, number_input_rect))
    {
        sample_state->label->SetContent("number input value " + std::to_string(sample_state->number_input->value));
    }
}

}