#include "ui_input.h"
#include "ui_string.h"

namespace oui {

UIInput::UIInput(std::string text):label(text), unmodified_text(text) {}

void UIInput::SetValue(std::string text) {
    label.SetContent(text);
    unmodified_text = text;
    cursor_index = static_cast<int>(text.size());
}

std::string UIInput::GetValue() {
    return label.text;
}

void UIInput::HandleEdit(UIState& state)
{
    bool control_pressed = has_key(state.key_pressed, Keycode::LeftCommand) || has_key(state.key_pressed, Keycode::RightCommand) || has_key(state.key_pressed, Keycode::Ctrl);
    if (has_key(state.key_press, Keycode::Backspace))
    {
        if (control_pressed) {
            if (cursor_index == label.text.size()) {
                label.SetContent("");
            }
            else {
                label.SetContent(label.text.substr(cursor_index));
            }
            cursor_index = 0;
        }
        else {
            if (label.text.size() > 0) {
                if (cursor_index == label.text.size()) {
                    label.SetContent(label.text.substr(0, cursor_index - 1));
                } else {
                    std::string text = label.text;
                    label.SetContent(text.substr(0, cursor_index - 1) + text.substr(cursor_index));
                }
                cursor_index--;
            }
        }
    }

    if (control_pressed) {
        // shortcuts
        if (has_key(state.key_press, Keycode::a) || has_key(state.key_press, Keycode::Left))
        {
            cursor_index = 0;
        }
        if (has_key(state.key_press, Keycode::e) || has_key(state.key_press, Keycode::Right))
        {
            cursor_index = static_cast<int>(label.text.size() - 1);
        }
    }
    else {
        // insert new characters
        if (has_key(state.key_press, Keycode::Left))
        {
            cursor_index = ui_max(0, cursor_index);
        }
        if (has_key(state.key_press, Keycode::Right))
        {
            cursor_index = ui_min(cursor_index + 1, static_cast<int>(label.text.size()));
        }

        std::string insert;
        bool shift_pressed = has_key(state.key_pressed, Keycode::Shift);

        for (auto itr = state.key_press->begin(); itr != state.key_press->end(); ++itr)
        {
            insert += string_from_code(*itr, shift_pressed);
        }     
       
        if (insert.size() > 0) {
            if (cursor_index == label.text.size()) {
                label.SetContent((label.text + insert).data());
            }
            else {
                std::string text = label.text;
                label.SetContent(text.substr(0, cursor_index) + insert + text.substr(cursor_index));
            }
            cursor_index += static_cast<int>(insert.size());
        }
    }
}

float UIInput::GetCursorOffset()
{
    if (cursor_index < 0 || cursor_index > label.text.size()) return 0.f;
    return label.char_offsets[cursor_index - 1];
}

void UIInput::RenderCursor(primitive_layer* layer, ui_rect rect, u32 clip)
{
    ui_rect draw_rect = rect;
    float offset = GetCursorOffset() * scale;
    if (label.constraint.alignment & UIAlignment::LEFT) {
        draw_rect.x += offset + label.padding.left * scale;
    }
    else if (label.constraint.alignment & UIAlignment::RIGHT) {
        draw_rect.x += draw_rect.w - (label.padding.right + label.text_size.x) * scale - offset;
    }
    else {
        draw_rect.x += (draw_rect.w - label.text_size.x * scale) * 0.5f + offset;
    }

    // cursor
    draw_rect.w = 1.2f * scale;
    draw_rect.h -= 4.f * scale;
    draw_rect.y += 2.f * scale;
   
    ui_theme* theme = ui_theme_current();
    fill_rect(layer, theme->white, draw_rect, clip);
}

bool ui_input(UIState &state, UIInput &input, ui_style style, ui_rect rect, int layer_index, u32 clip)
{
    primitive_layer* layer = &state.buffers[state.swap_index].layers[layer_index];
    int id = input.id;
    bool result = false;

    ui_corner_radius radius = input.radius;

    bool hovering = state.IsHovering(rect);
    state.rect = rect;
    if (hovering && layer_index > state.next_hover_layer_index) {
        state.next_hover_layer_index = layer_index;
        state.next_hover = id;
    }

    // set cursor type

    ui_style draw_style = style;
    if (state.active == id) {
        draw_style.color = style.active;

        input.HandleEdit(state);

        if (has_key(state.key_press, Keycode::Enter))
        {
            result = true;
            state.ClearActive();
            state.key_press->erase(Keycode::Enter);
        }

        if (has_key(state.key_press, Keycode::Escape))
        {
            state.ClearActive();
            input.SetValue(input.unmodified_text);
        }
    }

    if (state.hover == id && (has_key(state.key_press, Keycode::Enter) || state.left_mouse_release))
    {
        state.SetActive(id);
        input.unmodified_text = input.label.text;
    }

    if (state.active == id || input.render_outline) {
        draw_style.color = style.outline;
        stroke_round_rect_per_corner(layer, draw_style, rect, radius, clip);
    }

    if (state.active == id && !hovering && state.left_mouse_release) {
        state.ClearActive();
    }

    draw_style.color = (hovering && state.active == -1 ? style.hover : style.color);
    fill_round_rect_per_corner(layer, draw_style, rect, radius, clip);

    ui_label(state, input.label, rect, layer_index, clip);

    if (state.active == input.id) {
        input.RenderCursor(layer, rect, clip);
    }

    return result;
}


}