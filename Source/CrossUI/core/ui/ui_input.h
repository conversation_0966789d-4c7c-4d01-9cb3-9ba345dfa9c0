#pragma once

#include "ui_control.h"
#include "ui_label.h"
#include <string>

namespace oui {
   
enum UINumberInputType: int {
    Float,
    Integer
};

class CROSS_UI_API UIInput: public UIElement {
public:
    UINumberInputType type = UINumberInputType::Float;
    UILabel label;
    std::string unmodified_text;
    int cursor_index = 0;
    bool render_outline = false;

    UIInput(std::string text);

    void HandleEdit(UIState& state);
    void RenderCursor(primitive_layer* layer, ui_rect rect, u32 clip = 0);
    void SetValue(std::string text);
    std::string GetValue();
private:
    float GetCursorOffset();
};

class CROSS_UI_API UINumberInput : public UIInput {
public:
    float step = 1.0f;
    int precision = 2;
    float value = 0.f;
    bool dragging = false;

    UINumberInput(float default_value = 0.f):UIInput(""), value(default_value) {
        SetNumberValue(default_value);
    }

    bool SetNumberValue(float n);
    bool ParseValue();
};

class CROSS_UI_API UIRangeInput : public UIInput {
public:
    float min = -INFINITY;
    float max = INFINITY;

    float step = 1.0f;
    float value = 0.f;

    UIRangeInput(float default_value = 0.f):UIInput(""), value(default_value) {
        SetValue(std::to_string(value));
    }

    bool ParseValue();
};

CROSS_UI_API extern bool ui_input(UIState& state, UIInput& input, ui_style style, ui_rect rect, int layer_index = 0, u32 clip = 0);

CROSS_UI_API extern bool ui_number_input(UIState& tate, UINumberInput& input, ui_style style, ui_rect rect, int layer_index = 0, u32 clip = 0);

CROSS_UI_API extern bool ui_range_input(UIState& state, UIRangeInput& input, ui_style style, ui_rect rect, int layer_index = 0, u32 clip = 0);


}