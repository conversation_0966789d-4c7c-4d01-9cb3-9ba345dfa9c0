#pragma once 

#include "ui_control.h"
#include "ui_button.h"
#include "ui_label.h"

namespace oui {
class UIWindow;
using ui_app = std::function<void(UIState&, UIWindow&, ui_rect)>;

class CROSS_UI_API UIWindow : public UIElement {
public:
    UIWindow(const char* title = "");
    ~UIWindow();

    void SetApp(ui_app app);
    ui_app GetApp();

public:
    UILabel title;
    UIButton close;
    ui_rect rect;
    bool headless = false;
    bool visible = true;
    ui_app app{ nullptr };
    void* app_state{ nullptr };
};

CROSS_UI_API void ui_window(UIState& state, UIWindow& window, ui_style style, int layer_index = 0, u32 clip = 0);

}