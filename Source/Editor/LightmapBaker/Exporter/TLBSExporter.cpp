#include "Exporter/TLBSExporter.h"
#include "GPUBakingCore/ImportExport.h"
#include "DawnSettings.h"

#include <UVAtlas/inc/UVAtlas.h>
#include <DirectXMesh/DirectXMesh.h>

//#include "Runtime/RenderSystem/GPUContext/GPUResource/ShaderProgram/VBO.h"
#include "Resource/Material.h"
#include "Resource/Texture/Texture.h"

#include <stdio.h>
#include <io.h>

#include <corecrt_io.h>
#include "imageio.h"
#include "CrossBase/Math/FloatConversion.h"
#include "CrossBase/Math/QTangents.h"

//#define LOCTEXT_NAMESPACE "TLBS"

#ifndef BIG_WORLD_TRIANGLE_THREDHOLD
#    define BIG_WORLD_TRIANGLE_THREDHOLD 5000000
#endif

using namespace NSwarm;

NS_GPUBAKING_BEGIN

#ifndef USE_LUMINANCE_AS_POWER
#    define USE_LUMINANCE_AS_POWER 1
#endif

#define FLOAT3_LUMINANCE(A) (A.x * 0.3f + A.y * 0.59f + A.z * 0.11f)

FGuidInfo DebugLevelGUID{0, 0, 0, 0};

/** Angle that the directional light's emissive surface extends relative to a receiver, affects penumbra sizes. */
constexpr float cLightSourceAngle = 1.0;

/**
 * Radius of light source shape.
 * Note that light sources shapes which intersect shadow casting geometry can cause shadowing artifacts.
 */
constexpr float cSourceRadius = 0.5;
/**
 * Length of light source shape.
 * Note that light sources shapes which intersect shadow casting geometry can cause shadowing artifacts.
 */
constexpr float cSourceLength = 0.5;

//static cross::Float4x4A ueTransform(
//1, 0, 0, 0,
//0, 0, 1, 0,
//0,-1, 0, 0,
//0, 0, 0, 1
//);

enum ECubeFace
{
    CubeFace_PosX = 0,
    CubeFace_NegX = 1,
    CubeFace_PosY = 2,
    CubeFace_NegY = 3,
    CubeFace_PosZ = 4,
    CubeFace_NegZ = 5,
    CubeFace_MAX
};

static int UECubeFaceTranslate[6] = {CubeFace_PosX, CubeFace_NegX, CubeFace_PosZ, CubeFace_NegZ, CubeFace_NegY, CubeFace_PosY};

HRESULT __cdecl UVAtlasCallback(float fPercentDone)
{
    return 0;
}

/*-----------------------------------------------------------------------------
    FTLBSExporter
-----------------------------------------------------------------------------*/
FTLBSExporter::FTLBSExporter(cross::IGameWorld* InWorld, FTLBSExportContext& InExportContext)
    : ExportStage(NotRunning)
    , CurrentAmortizationIndex(0)
    , World(InWorld)
    , ExportContext(InExportContext)
{
    // We must have a valid world
    // check( World );
}

FTLBSExporter::~FTLBSExporter()
{
    // clean up any opened channels that are opened during export
}

bool FTLBSExporter::GatherSceneInfo()
{
    return true;
}

template<class T, bool bCached = false> void ExportFile(std::string rootPath, std::string filename, NSwarm::FTLBSSwarmInterface* Swarm, NSwarm::TChannelFlags flags, T& Object)
{
    SerializeObjectFile<T>(rootPath + filename, Object, false);
    std::wstring ChannelName = string2wstring(filename);
    SerializeObject<T, bCached>(Swarm, ChannelName, Object, true);
}

template<class T, bool bCached = false> void ExportSceneInfoFile(std::string rootPath, std::string filename, NSwarm::FTLBSSwarmInterface* Swarm, NSwarm::TChannelFlags flags, T& Object)
{
    SerializeObjectFile<T>(rootPath + filename, Object, false);
}

template<class T, bool bCached = false> void ExportSceneInfoFileOut(std::string rootPath, std::string filename, NSwarm::FTLBSSwarmInterface* Swarm, NSwarm::TChannelFlags flags, T& Object)
{
    std::wstring ChannelName = string2wstring(filename);
    SerializeObject<T, bCached>(Swarm, ChannelName, Object, true);
}

template<class T> void CopyTSerializedArray(TSerializedArray<T>& dst, const TSerializedArray<T>& src)
{
    for (auto i = 0; i < src.Num(); i++)
    {
        dst[i] = src[i];
    }
}

bool FTLBSExporter::InitMeshInfoFromUE4File(const std::string& meshFileName, FMeshInfo& staticMeshData)
{
    const cross::MeshAssetData* meshAssetData = nullptr;
    for (const auto& meshAssetInfo : ExportContext.StaticMeshes)
    {
        const cross::IndexStreamAssetData& indexBuffer = meshAssetInfo.MeshAssetData->GetIndexStream();
        if (staticMeshData.NumTriangles == indexBuffer.mCount / 3 && staticMeshData.NumElements == meshAssetInfo.MeshAssetData->GetMeshPartCount(0))
        {
            meshAssetData = meshAssetInfo.MeshAssetData;
            break;
        }
    }

    if (meshAssetData)
    {
        InitMeshInfo(meshAssetData, staticMeshData, 1, 0);
        FMeshInfo ue4MeshData;
        SerializeObjectFile<FMeshInfo>(meshFileName, ue4MeshData, false);
        for (auto i = 0; i < staticMeshData.ElementInfos.Num(); ++i)
        {
            staticMeshData.ElementInfos[i].MaterialIndex = ue4MeshData.ElementInfos[i].MaterialIndex;
        }
        // panlele: vertex order is not same, trick: reused uv0!!!
        // CopyTSerializedArray<float2>(staticMeshData.TexcoordBuffers[1].TexcoordBuffer, staticMeshData.TexcoordBuffers[0].TexcoordBuffer);
    }
    else
    {
        SerializeObjectFile<FMeshInfo>(meshFileName, staticMeshData, false);
    }
    return true;
}

bool FTLBSExporter::InitMaterialInfoFromUE4File(const std::string& materialFileName, FMaterialInfo& materialData)
{
    FMaterialInfo ue4MaterialData;
    SerializeObjectFile<FMaterialInfo>(materialFileName, ue4MaterialData, false);
    const float4& color = ue4MaterialData.DiffuseTexture.Colors[0];

    auto meshInstCount = ExportContext.StaticMeshInstances.size();
    for (auto meshIdx = 0; meshIdx < meshInstCount; ++meshIdx)
    {
        for (auto& matBakePtr : ExportContext.StaticMeshInstances[meshIdx].materiaPtr)
        {
            // FMaterialInfo& matInfo = SceneInfo.Materials.AddDefaultRef();
            auto& matPtr = matBakePtr->mMaterial;
            auto baseColor = std::get_if<std::vector<float>>(matPtr->GetProperty("BaseColor"));
            const auto& colorMapDef = std::get_if<bool>(matPtr->GetProperty("COLOR_MAP"));
            if (!(*colorMapDef) && (*baseColor)[0] == color.x && (*baseColor)[1] == color.y && (*baseColor)[2] == color.z)
            {
                InitMaterialInfo(matBakePtr, materialData);
                return true;
            }
        }
    }
    // panlele: trick, can not find mat, and material can not be repeat
    InitMaterialInfo(ExportContext.StaticMeshInstances[2].materiaPtr[0], materialData);
    return true;
}

void GetUE4CacheFiles(std::vector<std::string>& meshgz, std::vector<std::string>& matgz, std::string& one_scenegz, std::vector<std::string>& jobgz, std::string& cachePath)
{
    cachePath = UE4CachePath;
    std::string inPath = cachePath + "*";

    std::vector<std::string> scenegz;

    std::vector<std::string> allFiles;
    struct _finddata_t fileinfo;
    auto handle = _findfirst(inPath.c_str(), &fileinfo);
    do
    {
        allFiles.push_back(fileinfo.name);
    } while (!_findnext(handle, &fileinfo));

    for (auto filename : allFiles)
    {
        if (filename.rfind(".meshgz") != std::string::npos)
        {
            meshgz.push_back(filename);
        }
        else if (filename.rfind(".matgz") != std::string::npos)
        {
            matgz.push_back(filename);
        }
        else if (filename.rfind(".scenegz") != std::string::npos)
        {
            scenegz.push_back(filename);
        }
        else if (filename.rfind(".jobgz") != std::string::npos)
        {
            jobgz.push_back(filename);
        }
    }
    assert(scenegz.size() == 1);
    one_scenegz = scenegz[0];
}

void FTLBSExporter::ExportSceneInfo(FTLBSExportContext& Context, FGuid& DebugMappingGuid)
{
    DebugUE4 = LightingBakeConfig::GetInstance().GetDebugUE4();
    DebugUE4LightOnly = LightingBakeConfig::GetInstance().GetDebugUE4LightOnly();
    DebugUE4JobParamsOnly = LightingBakeConfig::GetInstance().GetDebugUE4JobParamsOnly();

    // Initialize the debug mapping Guid to something not in the scene.
    DebugMappingGuid = FGuid(0x96dc6516, 0xa616421d, 0x82f0ef5b, 0x299152b5);
    if (Context.bSwarmConnectionIsValid)
    {
        std::vector<std::string> meshgz, matgz, jobgz;
        std::string scenegz;
        std::string cachePath;
        if (DebugUE4 || DebugUE4LightOnly || DebugUE4JobParamsOnly)
        {
            GetUE4CacheFiles(meshgz, matgz, scenegz, jobgz, cachePath);
        }

        auto* Swarm = Context.Swarm;
        NSwarm::TChannelFlags SceneChannelFlags = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_WRITE | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
        NSwarm::TChannelFlags ChannelFlags = (NSwarm::TChannelFlags)(NSwarm::SWARM_CHANNEL_WRITE | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);

        if (DebugUE4)
        {
            char tempCh[1024];
            {
                FSceneInfo sceneInfo;
                ExportSceneInfoFile<FSceneInfo, false>(cachePath, scenegz, Swarm, SceneChannelFlags, sceneInfo);
                for (auto i = 0; i < sceneInfo.Meshes.Num(); i++)
                {
                    FGuidInfo meshGuid = sceneInfo.Meshes[i].Guid;
                    std::sprintf(tempCh, "%08X%08X%08X%08X", meshGuid.A, meshGuid.B, meshGuid.C, meshGuid.D);
                    for (auto meshFileName : meshgz)
                    {
                        if (meshFileName.find(tempCh) != std::string::npos)
                        {
                            if (ReplaceCEMesh)
                            {
                                InitMeshInfoFromUE4File(cachePath + meshFileName, sceneInfo.Meshes[i]);
                            }
                            else
                            {
                                ExportFile(cachePath, meshFileName, Swarm, ChannelFlags, sceneInfo.Meshes[i]);
                            }
                            assert(sceneInfo.Meshes[i].Guid == meshGuid);
                        }
                    }
                }

                for (auto i = 0; i < sceneInfo.Materials.Num(); i++)
                {
                    auto matHash = sceneInfo.Materials[i].Hash;
                    std::wstring hashStr = BytesToHex(matHash.Hash, sizeof(matHash));
                    wchar2char(hashStr.c_str(), tempCh, 1024);
                    for (auto matFileName : matgz)
                    {
                        if (matFileName.find(tempCh) != std::string::npos)
                        {
                            if (ReplaceCEMaterial)
                            {
                                InitMaterialInfoFromUE4File(cachePath + matFileName, sceneInfo.Materials[i]);
                            }
                            else
                            {
                                ExportFile(cachePath, matFileName, Swarm, ChannelFlags, sceneInfo.Materials[i]);
                            }
                        }
                    }
                }

                if (ReplaceCELight)
                {
                    // replace light
                    sceneInfo.NumLights = 0;
                    auto oldLight = sceneInfo.Lights[0];
                    auto oldLightGuids = sceneInfo.LightGuids;
                    sceneInfo.SkyLight = FSkyLightInfo();
                    sceneInfo.Lights.Clear(true);
                    sceneInfo.LightGuids.Clear(true);
                    sceneInfo.LightFlags.Clear(true);
                    sceneInfo.LightPowers.Clear(true);
                    ExportLights(sceneInfo);
                    sceneInfo.LightGuids = oldLightGuids;
                    //sceneInfo.LightPowers[0] = 559;
                }

                // replace mesh instance TODO not finished
               /* for (auto i = 0; i < sceneInfo.MesheInstances.Num(); i++)
                {
                    const auto& meshInst = sceneInfo.MesheInstances[i];
                    auto trianglesNum = sceneInfo.Meshes[meshInst.MeshIndex].NumTriangles;
                    auto verticesNum = sceneInfo.Meshes[meshInst.MeshIndex].NumVertices;
                }
                sceneInfo.MesheInstances.Clear(true);
                sceneInfo.MeshGuids.Clear(true);
                sceneInfo.ImportanceVolumes.Clear(true);
                sceneInfo.NumMeshes = 0;
                ExportMeshInstance(sceneInfo);*/
                

                ExportSceneInfoFileOut<FSceneInfo, false>(cachePath, scenegz, Swarm, SceneChannelFlags, sceneInfo);

                if (ReplaceCELight && ReplaceCEJob)
                {
                    for (auto i = 0; i < sceneInfo.LightGuids.Num(); i++)
                    {
                        FGuidInfo& LightGuid = SceneInfo.LightGuids.AddDefaultRef();
                        LightGuid = sceneInfo.LightGuids[i];
                    }
                }
                if (ReplaceCEMesh && ReplaceCEJob)
                {
                    SceneInfo.MeshGuids.Clear();
                    for (auto i = 0; i < sceneInfo.MeshGuids.Num(); i++)
                    {
                        auto& meshGuid = SceneInfo.MeshGuids.AddDefaultRef();
                        meshGuid = sceneInfo.MeshGuids[i];
                    }
                }
            }

            if (ReplaceCEJob)
            {
                ExportJob();
                GPUBaking::SerializeJobs(Context.Swarm, Context.SceneGuid, JobInputs, true);
/*              FBakingJobInputs jobInputs;
                // SerializeObjectFile<FBakingJobInputs>(cachePath + jobgz[0], jobInputs, false);
                SerializeObjectFile<FBakingJobInputs>(cachePath + jobgz[0], jobInputs, false);
                std::wstring ChannelName = string2wstring(jobgz[0]);
                jobInputs.BakingParameters = JobInputs.BakingParameters;
                SerializeObject<FBakingJobInputs, false>(Swarm, ChannelName, jobInputs, true);*/
            }
            else
            {
                FBakingJobInputs jobInputs;
                // SerializeObjectFile<FBakingJobInputs>(cachePath + jobgz[0], jobInputs, false);
                SerializeObjectFile<FBakingJobInputs>(cachePath + jobgz[0], jobInputs, false);
                std::wstring ChannelName = string2wstring(jobgz[0]);
                SerializeObject<FBakingJobInputs, false>(Swarm, ChannelName, jobInputs, true);
            }
        }
        else
        {
            // auto DawnSettings = Context.GetDawnBuildSetting();
            ExportMesh();

            ExportMaterials();

            ExportMeshInstance(SceneInfo);

            ExportLights(SceneInfo);
            if (DebugUE4LightOnly)
            {
                FSceneInfo ue4SceneInfo;
                ExportSceneInfoFile<FSceneInfo, false>(cachePath, scenegz, Swarm, SceneChannelFlags, ue4SceneInfo);
                for (auto i = 0; i < SceneInfo.Lights.Num(); i++)
                {
                    float3 position = SceneInfo.Lights[i].Position;
                    memcpy(&SceneInfo.Lights[i], &ue4SceneInfo.Lights[i], sizeof(FLightInfo));
                    SceneInfo.Lights[i].Position = position;
                }
                for (auto i = 0; i < SceneInfo.LightFlags.Num(); i++)
                {
                    SceneInfo.LightFlags[i] = ue4SceneInfo.LightFlags[i];
                }
                for (auto i = 0; i < SceneInfo.LightPowers.Num(); i++)
                {
                    SceneInfo.LightPowers[i] = ue4SceneInfo.LightPowers[i];
                }
                SceneInfo.ImportanceBounds = ue4SceneInfo.ImportanceBounds;
                SceneInfo.ImportanceVolumes = ue4SceneInfo.ImportanceVolumes;

                SceneInfo.SkyLight = ue4SceneInfo.SkyLight;
            }

            ExportJob();
            if (DebugUE4JobParamsOnly)
            {
                FBakingJobInputs ue4JobInputs;
                SerializeObjectFile<FBakingJobInputs>(cachePath + jobgz[0], ue4JobInputs, false);
                for (auto j = 0; j < JobInputs.Lightmap2DJobs.Num(); j++)
                {
                    FLightmap2DInput& celmJob = JobInputs.Lightmap2DJobs[j];
                    for (auto i = 0; i < ue4JobInputs.Lightmap2DJobs.Num(); i++)
                    {
                        FLightmap2DInput& ue4lmJob = ue4JobInputs.Lightmap2DJobs[i];
                        if (ue4lmJob.Size == celmJob.Size)
                        {
                            ue4lmJob.JobID = celmJob.JobID;
                            ue4lmJob.MeshID = celmJob.MeshID;
                            ue4lmJob.LightmapUVIndex = celmJob.LightmapUVIndex;
                        }
                    }
                }
                for (auto j = 0; j < JobInputs.SDFShadowJobs.Num(); j++)
                {
                    FSDFShadowInput& cesdfJob = JobInputs.SDFShadowJobs[j];
                    for (auto i = 0; i < ue4JobInputs.SDFShadowJobs.Num(); i++)
                    {
                        FSDFShadowInput& ue4sdfJob = ue4JobInputs.SDFShadowJobs[i];
                        if (ue4sdfJob.Size == cesdfJob.Size)
                        {
                            ue4sdfJob.JobID = cesdfJob.JobID;
                            ue4sdfJob.MeshID = cesdfJob.MeshID;
                            ue4sdfJob.LightmapUVIndex = cesdfJob.LightmapUVIndex;
                            ue4sdfJob.Lights = cesdfJob.Lights;
                        }
                    }
                }
                ue4JobInputs.SDFShadowGroupJobs.Clear();
                JobInputs = ue4JobInputs;
            }

            JobInputs.DebugInput.CameraPosition = Float3CE2UE(ExportContext.DebugCameraPosition);
            JobInputs.DebugInput.LookatPosition = Float3CE2UE(ExportContext.DebugLookatPosition);
            // JobInputs.DebugInput.SizeX = 1024;
            // JobInputs.DebugInput.SizeY = 1024;

            GPUBaking::SerializeScene(Context.Swarm, Context.SceneGuid, SceneInfo, true);
            GPUBaking::SerializeJobs(Context.Swarm, Context.SceneGuid, JobInputs, true);
        }
    }
}

bool FTLBSExporter::ExportLightProbeBricks()
{
    auto& SparseVolumetricSamplesParameter = JobInputs.SparseVolumetricSamplesParameter;
//     SparseVolumetricSamplesParameter.NumSurfaceSampleLayers = 20;
//     SparseVolumetricSamplesParameter.SurfaceLightSampleSpacing = 300;
//     SparseVolumetricSamplesParameter.FirstSurfaceSampleLayerHeight = 5;
//     SparseVolumetricSamplesParameter.SurfaceSampleLayerHeightSpacing = 25;
    SparseVolumetricSamplesParameter = FSparseVolumetricSamplesParameters();

    JobInputs.LightProbeJobs.Resize(static_cast<uint32>(ExportContext.LightProbeJobs.size()));
    for (auto i = 0; i < ExportContext.LightProbeJobs.size(); i++)
    {
        auto& jobInfo = ExportContext.LightProbeJobs[i];
        const cross::Int3& BrickSize = jobInfo.BrickSize;
        auto sampleNum = (BrickSize.x + 1) * (BrickSize.y + 1) * (BrickSize.z + 1);
        const auto& center = jobInfo.Position;
        const auto& extent = jobInfo.Extent;
        cross::Float4A boxMin = center - extent;
        cross::Float4A boxMax = center + extent;
        float3 stepSize = {extent.x * 2 / BrickSize.x, extent.y * 2 / BrickSize.y, extent.z * 2 / BrickSize.z};
        const float sampleRadius = std::max<float>(std::max<float>(stepSize.x, stepSize.y), stepSize.z);

        FLightProbeInput& jobInput = JobInputs.LightProbeJobs[i];
        unsigned int jobIndex = i;
        jobInput.JobID = {0, 2, 0, jobIndex}; 
        jobInput.LevelID = DebugLevelGUID; // otherwise lightprobe will not place along mesh surface in ImportanceVolumes
        jobInput.SamplePositions.Resize(static_cast<uint32>(sampleNum));
        auto& SamplePositions = jobInput.SamplePositions;

        jobInfo.Guid = jobInput.JobID;

        for (auto z = 0; z <= BrickSize.z; ++z)
        {
            for (auto y = 0; y <= BrickSize.y; ++y)
            {
                for (auto x = 0; x <= BrickSize.x; ++x)
                {
                    auto sampleIndex = (BrickSize.x + 1) * (BrickSize.y + 1) * z + (BrickSize.x + 1) * y + x;
                    auto samplePostion = float3(boxMin.x, boxMin.y, boxMin.z) + stepSize * float3(static_cast<float>(x), static_cast<float>(y), static_cast<float>(z));
                    auto uePos = Float3CE2UE(samplePostion);
                    SamplePositions[static_cast<uint32>(sampleIndex)] = float4(uePos.x, uePos.y, uePos.z, sampleRadius);
                }
            }
        }
    }

    return true;
}

bool GPUBAKING_NS_NAME::FTLBSExporter::ExportVolumetricLightmapSettings(GPUBaking::FAdaptiveVolumetricLightmapParameters& OutSettings)
{
    Assert(ExportContext.LightProbeJobs.size() == 1);
    Assert(ExportContext.LightProbeJobs[0].UseVolumetricLightmap);
    const auto& VLMProbeJob = ExportContext.LightProbeJobs[0];

    cross::Float3 ImportanceCenter, ImportanceExtent;
    if (VLMProbeJob.VolumeSizeType == BakerLightProbeJobInfo::BakeVolumeSizeType::MANUAL)
    {
        cross::Float4A Center = VLMProbeJob.Position;
        cross::Float4A Extent = VLMProbeJob.Extent;
        ImportanceCenter = Float3CE2UE(cross::Float3A{Center.x, Center.y, Center.z});
        ImportanceExtent = Float3Abs(Float3CE2UE(cross::Float3A{Extent.x, Extent.y, Extent.z}));
    }
    else if (VLMProbeJob.VolumeSizeType == BakerLightProbeJobInfo::BakeVolumeSizeType::AUTO_INCLUDE_LIGHT_PROBE_ENTITY)
    {
        VLMSceneAABB.GetCenter(&ImportanceCenter);
        VLMSceneAABB.GetExtent(&ImportanceExtent);
        ImportanceCenter = Float3CE2UE(ImportanceCenter);
        ImportanceExtent = Float3Abs(Float3CE2UE(ImportanceExtent));
    }
    else if (VLMProbeJob.VolumeSizeType == BakerLightProbeJobInfo::BakeVolumeSizeType::AUTO_INCLUDE_BAKE_ENTITY)
    {
        SceneAABB.GetCenter(&ImportanceCenter);
        SceneAABB.GetExtent(&ImportanceExtent);
        ImportanceCenter = Float3CE2UE(ImportanceCenter);
        ImportanceExtent = Float3Abs(Float3CE2UE(ImportanceExtent));
    }
    LOG_DEBUG("ExportVolumetricLightmapSettings SceneAABB = center({}, {}, {}); extent({}, {}, {})", ImportanceCenter.x, ImportanceCenter.y, ImportanceCenter.z, ImportanceExtent.x, ImportanceExtent.y, ImportanceExtent.z);

    //ImportanceExtent = cross::Float3(1708.22046f, 1705.13513f, 902.120972f);//TODO:panlele
    //ImportanceCenter = cross::Float3(-1535.12109f, -1567.19238f, -599.617188f) + ImportanceExtent;

    float TargetDetailCellSize = LightingBakeConfig::GetInstance().GetTargetDetailCellSize();
    Assert(ExportContext.LightProbeJobs[0].BrickSize.x == ExportContext.LightProbeJobs[0].BrickSize.y && ExportContext.LightProbeJobs[0].BrickSize.x == ExportContext.LightProbeJobs[0].BrickSize.z);
    const int BrickSize = ExportContext.LightProbeJobs[0].BrickSize.x;
    OutSettings.BrickSize = BrickSize;
    Assert(OutSettings.BrickSize == 1 << 2);
    OutSettings.MaxRefinementLevels = 3;
    Assert(OutSettings.MaxRefinementLevels >= 1 && OutSettings.MaxRefinementLevels <= 6);
    OutSettings.VolumeMin = ImportanceCenter - ImportanceExtent;

    cross::Int3 FullGridSize(static_cast<int>(std::truncf(2 * ImportanceExtent.x / TargetDetailCellSize)) + 1,
                             static_cast<int>(std::truncf(2 * ImportanceExtent.y / TargetDetailCellSize)) + 1,
                             static_cast<int>(std::truncf(2 * ImportanceExtent.z / TargetDetailCellSize)) + 1);
    const int32 BrickSizeLog2 = FloorLog2(OutSettings.BrickSize);
    const int DetailCellsPerTopLevelBrick = (1 << OutSettings.MaxRefinementLevels * BrickSizeLog2);
    OutSettings.TopLevelGridSize = int3(DivideAndRoundUp(FullGridSize.x, DetailCellsPerTopLevelBrick), DivideAndRoundUp(FullGridSize.y, DetailCellsPerTopLevelBrick), DivideAndRoundUp(FullGridSize.z, DetailCellsPerTopLevelBrick));
    OutSettings.VolumeSize = OutSettings.TopLevelGridSize * DetailCellsPerTopLevelBrick * TargetDetailCellSize;

    OutSettings.SurfaceLightmapMinTexelsPerVoxelAxis = 1.0f;
    OutSettings.VoxelizationCellExpansionForSurfaceGeometry = 0.1f;
    OutSettings.VoxelizationCellExpansionForVolumeGeometry = 0.25f;
    OutSettings.WindowingTargetLaplacian = 50.f;
    OutSettings.MinBrickError = 0.01f;
    OutSettings.VoxelizationCellExpansionForLights = 0.1f;
    OutSettings.LightBrightnessSubdivideThreshold = 0.3f;
    OutSettings.bCullBricksBelowLandscape = true;
#if !DAWN_PRT_BRANCH
    OutSettings.bPrecomputedRadianceTransfer = false;
    OutSettings.bPrecomputedLocalRadianceTransfer = false;
#endif

    return true;
}

bool GPUBAKING_NS_NAME::FTLBSExporter::ExportVolumetricLightmapSettingsAndJob(GPUBaking::FBakingJobInputs& OutJobInputs)
{
    ExportVolumetricLightmapSettings(OutJobInputs.AdaptiveVolumetricLightmapParameter);
    const auto& OutSettings = OutJobInputs.AdaptiveVolumetricLightmapParameter;
    const int NumTopLevelBricks = OutSettings.TopLevelGridSize.x * OutSettings.TopLevelGridSize.y * OutSettings.TopLevelGridSize.z;
    int TargetNumVolumetricLightmapTasks = 64;
    const int NumTaskPerTopLevelBrick = std::clamp(TargetNumVolumetricLightmapTasks / NumTopLevelBricks, 1, OutSettings.BrickSize * OutSettings.BrickSize * OutSettings.BrickSize);
    // Generate task guids for top level volumetric lightmap cells
    OutJobInputs.VolumetricLightmapTaskGuids.Resize(NumTopLevelBricks * NumTaskPerTopLevelBrick);
    for (int VolumetricLightmapTaskIndex = 0; VolumetricLightmapTaskIndex < NumTopLevelBricks * NumTaskPerTopLevelBrick; VolumetricLightmapTaskIndex++)
    {
        FGuidInfo taskID;
        ::CoCreateGuid(reinterpret_cast<GUID*>(&taskID));
        OutJobInputs.VolumetricLightmapTaskGuids[VolumetricLightmapTaskIndex] = taskID;
    }
    return true;
}

bool FTLBSExporter::InitMeshInfo(const cross::MeshAssetData* meshAssetData, FMeshInfo& staticMeshData, int lightmapUVIndex, int lodIndex)
{
    // const cross::IndexStreamAssetData& indexBuffer = meshAssetData->GetIndexStream();
    // int sectionCount = meshAssetData->GetMeshPartCount(lodIndex);

    UInt32 meshPartStartIndex, meshPartCount;
    meshAssetData->GetMeshLodInfo(lodIndex, meshPartStartIndex, meshPartCount);
    auto sectionCount = meshPartCount;

    cross::MeshAssetData::GetRawVertexAndIndexDataOutput position0Data = meshAssetData->GetRawVertexAndIndexData(lodIndex, cross::VertexChannel::Position0);
    //Assert(position0Data.vertexStartFromMeshPartAssetInfo == 0);

    auto indexCount = position0Data.indexCount;
    auto vertexCount = position0Data.vertexCount;

    staticMeshData.LODIndex = lodIndex;
    // assert(meshAssetData->GetPrimitiveCount() == indexBuffer.mCount / 3);
    staticMeshData.NumTriangles = indexCount / 3;   // meshAssetData->GetPrimitiveCount();
    staticMeshData.NumVertices = vertexCount;
    staticMeshData.NumElements = sectionCount;
    // panlele: need be dynamic
    staticMeshData.LightmapUVIndex = 1;
    staticMeshData.LightmapUVDensity = 500.0f;
    staticMeshData.LightmapTexelDensity = 0.005f;

    // assert(indexBuffer.mIs16BitIndex);
    staticMeshData.Index16BitBuffer.Resize(0);
    staticMeshData.Index32BitBuffer.Resize(indexCount);
    if (position0Data.is16BitIndex)
    {
        for (uint32 i = 0; i < indexCount; ++i)
        {
            staticMeshData.Index32BitBuffer[i] = *reinterpret_cast<const UInt16*>(&position0Data.indexData[i * 2]);
        }
    }
    else
    {
        for (uint32 i = 0; i < indexCount; ++i)
        {
            staticMeshData.Index32BitBuffer[i] = *reinterpret_cast<const UInt32*>(&position0Data.indexData[i * 4]);
        }
    }

    // panlele: otherwise preview no display, why ???
    for (uint32 i = 0; i < indexCount / 3; ++i)
    {
        std::swap(staticMeshData.Index32BitBuffer[i * 3], staticMeshData.Index32BitBuffer[i * 3 + 1]);
    }

    staticMeshData.ElementInfos.Resize(sectionCount);
    for (UInt32 section = 0; section < sectionCount; section++)
    {
        FMeshElementInfo& SMElementData = staticMeshData.ElementInfos[section];
        const cross::MeshPartAssetInfo& meshPartInfo = meshAssetData->GetMeshPartInfo(meshPartStartIndex + section);
        assert(meshPartInfo.mPrimitiveCount * 3 == meshPartInfo.mIndexCount);

        assert(lodIndex == 0);   // need to adjust IndexStart if lodIndex != 0
        SMElementData.StartIndex = meshPartInfo.mIndexStart / 3;
        SMElementData.PrimitiveCount = meshPartInfo.mPrimitiveCount;
        // SMElementData.MaterialIndex = ExportContext.AddMaterial(material);;
        SMElementData.Flags = 0;

        // assert(indexBuffer.mIs16BitIndex);
        // vertex start must be adjust to zero, so adjust index buffer
        auto indexOffset = meshPartInfo.mVertexStart;
        for (uint32 idx = 0; idx < SMElementData.PrimitiveCount; idx++)
        {
            auto triIdx = SMElementData.StartIndex + idx;
            staticMeshData.Index32BitBuffer[triIdx * 3] += indexOffset;
            staticMeshData.Index32BitBuffer[triIdx * 3 + 1] += indexOffset;
            staticMeshData.Index32BitBuffer[triIdx * 3 + 2] += indexOffset;
        }
    }

    unsigned int VertexCount = staticMeshData.NumVertices;
    if (VertexCount > 0)
    {
        assert(meshAssetData->HasVertexChannel(cross::VertexChannel::Position0));
        assert(meshAssetData->HasVertexChannel(cross::VertexChannel::Normal0));
        assert(meshAssetData->HasVertexChannel(cross::VertexChannel::Tangent0));
        assert(meshAssetData->HasVertexChannel(cross::VertexChannel::TexCoord0));
        // assert(meshAssetData->HasVertexChannel(cross::VertexChannel::TexCoord1));
        staticMeshData.VertexBuffer.Resize(VertexCount);
        staticMeshData.NormalBuffer.Resize(VertexCount);
        staticMeshData.TangentBuffer.Resize(VertexCount);
        staticMeshData.BiTangentBuffer.Resize(VertexCount);
        staticMeshData.TexcoordBuffers.Resize(4);
        staticMeshData.TexcoordBuffers[0].TexcoordBuffer.Resize(VertexCount);
        staticMeshData.TexcoordBuffers[1].TexcoordBuffer.Resize(VertexCount);
        staticMeshData.TexcoordBuffers[2].TexcoordBuffer.Resize(VertexCount);
        staticMeshData.TexcoordBuffers[3].TexcoordBuffer.Resize(VertexCount);

        const auto* positionData = meshAssetData->GetVertexChannelData(cross::VertexChannel::Position0);
        const auto* normalData = meshAssetData->GetVertexChannelData(cross::VertexChannel::Normal0);
        const auto* tangentData = meshAssetData->GetVertexChannelData(cross::VertexChannel::Tangent0);
        const auto* qtangentData = meshAssetData->GetVertexChannelData(cross::VertexChannel::QUATTAN0);
        const auto* uvData = meshAssetData->GetVertexChannelData(cross::VertexChannel::TexCoord0);
        const auto* uv2Data = meshAssetData->GetVertexChannelData(cross::VertexChannel::TexCoord0 + lightmapUVIndex);
        //assert(positionData->mDataFormat == cross::VertexFormat::Float3);
        //assert(normalData->mDataFormat == cross::VertexFormat::Float3);
        //assert(tangentData->mDataFormat == cross::VertexFormat::Float3 || tangentData->mDataFormat == cross::VertexFormat::Float4);
        //assert(uvData->mDataFormat == cross::VertexFormat::Float2);
        //assert(!uv2Data || uv2Data->mDataFormat == cross::VertexFormat::Float2);

        const float* posIter = (const float*)&positionData->mData[0];
        LOG_INFO("Baked MeshInfo, name={}, pos={}, {}, {}, VertexCount={}", meshAssetData->GetName(), *posIter, *(posIter + 1), *(posIter + 2), VertexCount);

        for (unsigned int vertCount = 0; vertCount < VertexCount; vertCount++)
        {
            {
                Assert(positionData->mDataFormat == cross::VertexFormat::Float3 || positionData->mDataFormat == cross::VertexFormat::Half3);
                cross::Float3 pos;
                ConvertToFloat(&(positionData->mData[vertCount * positionData->mStride]), positionData->mDataFormat, pos);
                auto uePos = Float4CE2UE({pos.x, pos.y, pos.z, 1.0f});
                staticMeshData.VertexBuffer[vertCount] = uePos;
            }

            cross::Float3 normal{0, 1, 0};
            cross::Float4 tangent{1, 0, 0, 1};
            if(normalData && tangentData)
            {
                Assert(normalData->mDataFormat == cross::VertexFormat::Float3 || normalData->mDataFormat == cross::VertexFormat::Half3
                    || normalData->mDataFormat == cross::VertexFormat::Short3_Norm || normalData->mDataFormat == cross::VertexFormat::Byte3_Norm);
                ConvertToFloat(&(normalData->mData[vertCount * normalData->mStride]), normalData->mDataFormat, normal);

                Assert(tangentData->mDataFormat == cross::VertexFormat::Float4 || tangentData->mDataFormat == cross::VertexFormat::Half4 
                    || tangentData->mDataFormat == cross::VertexFormat::Short4_Norm || tangentData->mDataFormat == cross::VertexFormat::Byte4_Norm
                    || tangentData->mDataFormat == cross::VertexFormat::Float3 || tangentData->mDataFormat == cross::VertexFormat::Byte3_Norm);
                ConvertToFloat(&(tangentData->mData[vertCount * tangentData->mStride]), tangentData->mDataFormat, tangent);
            }
            else if (qtangentData)
            {
                //Assert(tangentData->mDataFormat == cross::VertexFormat::Float4 || tangentData->mDataFormat == cross::VertexFormat::Half4 || tangentData->mDataFormat == cross::VertexFormat::Short4_Norm ||
                //       tangentData->mDataFormat == cross::VertexFormat::Byte4_Norm || tangentData->mDataFormat == cross::VertexFormat::Float3 || tangentData->mDataFormat == cross::VertexFormat::Byte3_Norm);
                cross::Quaternion qtangent{0, 0, 0, 1};
                ConvertToFloat(&(qtangentData->mData[vertCount * qtangentData->mStride]), qtangentData->mDataFormat, qtangent);
                
//                 cross::Float3 tNormal; cross::Float4 tTangent{0, 0, 0, 1};
                quat_to_tangent_frame(qtangent, normal, tangent);

//                 if (fabsf(tNormal.x - normal.x) >= 0.001f || fabsf(tNormal.y - normal.y) >= 0.001f || fabsf(tNormal.z - normal.z) >= 0.001f 
//                     || fabsf(tTangent.x - tangent.x) >= 0.001f || fabsf(tTangent.y - tangent.y) >= 0.001f ||
//                     fabsf(tTangent.z - tangent.z) >= 0.001f || fabsf(tTangent.w - tangent.w) >= 0.001f)
//                 {
//                     Assert(false);
//                 }
            }
            auto ueNormal = Float4CE2UE({normal.x, normal.y, normal.z, 0.0f});
            staticMeshData.NormalBuffer[vertCount] = ueNormal;

            auto ueTangent = Float4CE2UE({tangent.x, tangent.y, tangent.z, tangent.w});
            staticMeshData.TangentBuffer[vertCount] = ueTangent;

            cross::Float3 normalVec = cross::Float3(staticMeshData.NormalBuffer[vertCount].x, staticMeshData.NormalBuffer[vertCount].y, staticMeshData.NormalBuffer[vertCount].z);
            cross::Float3 tangentVec = cross::Float3(staticMeshData.TangentBuffer[vertCount].x, staticMeshData.TangentBuffer[vertCount].y, staticMeshData.TangentBuffer[vertCount].z);
            cross::Float3 bitangentVec = normalVec.Cross(tangentVec);
            if (staticMeshData.TangentBuffer[vertCount].w == 1.0f || staticMeshData.TangentBuffer[vertCount].w == -1.0f)
            {
                bitangentVec *= staticMeshData.TangentBuffer[vertCount].w;
            }

            staticMeshData.BiTangentBuffer[vertCount].x = bitangentVec.x;
            staticMeshData.BiTangentBuffer[vertCount].y = bitangentVec.y;
            staticMeshData.BiTangentBuffer[vertCount].z = bitangentVec.z;
            staticMeshData.BiTangentBuffer[vertCount].w = 0.0f;

            if(uvData)
            {
                Assert(uvData->mDataFormat == cross::VertexFormat::Float2 || uvData->mDataFormat == cross::VertexFormat::Half2 || uvData->mDataFormat == cross::VertexFormat::Short2_Norm);
                cross::Float2 uvTemp;
                ConvertToFloat(&(uvData->mData[vertCount * uvData->mStride]), uvData->mDataFormat, uvTemp);
                staticMeshData.TexcoordBuffers[0].TexcoordBuffer[vertCount].x = uvTemp.x;
                staticMeshData.TexcoordBuffers[0].TexcoordBuffer[vertCount].y = uvTemp.y;
            }
            else
            {
                staticMeshData.TexcoordBuffers[0].TexcoordBuffer[vertCount].x = 0;
                staticMeshData.TexcoordBuffers[0].TexcoordBuffer[vertCount].y = 0;
            }

            if (uv2Data)
            {
                auto lightmapuvIdx = vertCount * uv2Data->mStride;
                if (lightmapuvIdx < uv2Data->mData.size())
                {
                    Assert(uv2Data->mDataFormat == cross::VertexFormat::Float2 || uv2Data->mDataFormat == cross::VertexFormat::Half2 || uv2Data->mDataFormat == cross::VertexFormat::Short2_Norm);
                    cross::Float2 uvTemp;
                    ConvertToFloat(&(uv2Data->mData[lightmapuvIdx]), uv2Data->mDataFormat, uvTemp);
                    staticMeshData.TexcoordBuffers[1].TexcoordBuffer[vertCount].x = uvTemp.x;
                    staticMeshData.TexcoordBuffers[1].TexcoordBuffer[vertCount].y = uvTemp.y;
                }
                else
                {
                    assert(false);
                    staticMeshData.TexcoordBuffers[1].TexcoordBuffer[vertCount].x = 0;
                    staticMeshData.TexcoordBuffers[1].TexcoordBuffer[vertCount].y = 0;
                }
            }
        }

        for (unsigned int VertexIndex = 0; VertexIndex < VertexCount; VertexIndex++)
        {
            int UVIndex = 2;
            for (; UVIndex < 4; UVIndex++)
            {
                staticMeshData.TexcoordBuffers[UVIndex].TexcoordBuffer[VertexIndex] = {0.0f, 0.0f};
            }
        }
    }
    return true;
}

int GetLightMapUVIndex(const std::vector<MaterialBakePtr>& vMats)
{
    Assert(vMats.size());
    int lightMapIndex = vMats[0]->GetLightMapUVIndex();
    for (const auto& mat : vMats)
    {
        Assert(lightMapIndex == mat->GetLightMapUVIndex());
    }
    return lightMapIndex;
}

void FTLBSExporter::ExportMesh()
{
    // UInt32 channel = 0;

    // auto uePos = Float4CE2UE({ 1, 1, 0, 1 });

    int lodIndex = GPUBaking::LightingBakeConfig::GetInstance().GetMeshLODIndex();

    for (const auto& meshAssetInfo : ExportContext.StaticMeshes)
    {
        const auto* meshAssetData = meshAssetInfo.MeshAssetData;
        const auto& vMaterials = meshAssetInfo.vMaterials;

        int sectionCount = meshAssetData->GetMeshPartCount(lodIndex);

        // assert(meshAssetData->GetLodCount() == 1);
        assert(sectionCount == vMaterials.size());
        assert(meshAssetData->HasIndexStream());

        // const cross::IndexStreamAssetData& indexBuffer = meshAssetData->GetIndexStream();

        FMeshInfo& staticMeshData = SceneInfo.Meshes.AddDefaultRef();
        ::CoCreateGuid(reinterpret_cast<GUID*>(&staticMeshData.Guid));
        InitMeshInfo(meshAssetData, staticMeshData, GetLightMapUVIndex(vMaterials), lodIndex);

        for (auto section = 0; section < sectionCount; section++)
        {
            FMeshElementInfo& SMElementData = staticMeshData.ElementInfos[section];
            auto& material = vMaterials[section];
            SMElementData.MaterialIndex = ExportContext.AddMaterial(material);
        }

        GPUBaking::SerializeMesh(ExportContext.Swarm, ToGuid(staticMeshData.Guid), staticMeshData, true);

        staticMeshData.ElementInfos.Resize(0);
        staticMeshData.ElementInfos.GetElements().shrink_to_fit();
        staticMeshData.Index16BitBuffer.Resize(0);
        staticMeshData.Index16BitBuffer.GetElements().shrink_to_fit();
        staticMeshData.Index32BitBuffer.Resize(0);
        staticMeshData.Index32BitBuffer.GetElements().shrink_to_fit();
        staticMeshData.VertexBuffer.Resize(0);
        staticMeshData.VertexBuffer.GetElements().shrink_to_fit();
        staticMeshData.NormalBuffer.Resize(0);
        staticMeshData.NormalBuffer.GetElements().shrink_to_fit();
        staticMeshData.TangentBuffer.Resize(0);
        staticMeshData.TangentBuffer.GetElements().shrink_to_fit();
        staticMeshData.BiTangentBuffer.Resize(0);
        staticMeshData.BiTangentBuffer.GetElements().shrink_to_fit();
        staticMeshData.TexcoordBuffers.Resize(0);
        staticMeshData.TexcoordBuffers.GetElements().shrink_to_fit();
    }
}

inline void ExportTexture(FTexture2DInfo& outTextureInfo, const float4& color, const float3& size)
{
    outTextureInfo.SizeX = static_cast<uint32>(size.x);
    outTextureInfo.SizeY = static_cast<uint32>(size.y);
    outTextureInfo.WrapMode = 0;
    outTextureInfo.Format = 2;
    outTextureInfo.Colors.Resize(outTextureInfo.SizeX * outTextureInfo.SizeY);

    for (uint32 Y = 0; Y < outTextureInfo.SizeY; ++Y)
    {
        for (uint32 X = 0; X < outTextureInfo.SizeX; ++X)
        {
            auto pixelIdx = Y * outTextureInfo.SizeX + X;
            outTextureInfo.Colors[pixelIdx].x = color.x;
            outTextureInfo.Colors[pixelIdx].y = color.y;
            outTextureInfo.Colors[pixelIdx].z = color.z;
            outTextureInfo.Colors[pixelIdx].w = color.w;
        }
    }
}

inline void ExportTexture(cross::resource::TextureResourceDataProvider* textureData, FTexture2DInfo& outTextureInfo, bool alphaMask, unsigned int miplevel)
{
    miplevel = std::min<UInt32>(miplevel, textureData->mResourceInfo.MipCount - 1);
    UInt32 SizeX, SizeY, Depth;
    textureData->GetImageSize(miplevel, SizeX, SizeY, Depth);

    auto FlipY = [height = SizeY](int y) -> int { return height - 1 - y; };

    outTextureInfo.SizeX = SizeX;
    outTextureInfo.SizeY = SizeY;
    outTextureInfo.WrapMode = 0;
    outTextureInfo.Format = 2;
    outTextureInfo.Colors.Resize(outTextureInfo.SizeX * outTextureInfo.SizeY);

    if (textureData->mResourceInfo.Format == TextureFormat::BASIS_UNIVERSAL)
    {
        for (uint32 Y = 0; Y < SizeY; Y++)
        {
            for (uint32 X = 0; X < SizeX; X++)
            {
                auto destY = Y;
                auto destX = X;
                auto destPixelIdx = destY * outTextureInfo.SizeX + destX;
                UInt8 r = 255;
                UInt8 g = 255;
                UInt8 b = 255;
                UInt8 a = 255;

                if (!alphaMask)
                {
                    outTextureInfo.Colors[destPixelIdx].x = static_cast<float>(r) / 255.0f;
                    outTextureInfo.Colors[destPixelIdx].y = static_cast<float>(g) / 255.0f;
                    outTextureInfo.Colors[destPixelIdx].z = static_cast<float>(b) / 255.0f;
                    outTextureInfo.Colors[destPixelIdx].w = textureData->mResourceInfo.Format == TextureFormat::RGBA32 ? static_cast<float>(a) / 255.0f : 1.0f;
                }
                else
                {
                    outTextureInfo.Colors[destPixelIdx].x = outTextureInfo.Colors[destPixelIdx].y = outTextureInfo.Colors[destPixelIdx].z = outTextureInfo.Colors[destPixelIdx].w = static_cast<float>(a) / 255.0f;
                }
            }
        }
        return;
    }

    assert(textureData->mResourceInfo.Dimension == TextureDimension::Tex2D);
    assert(textureData->mResourceInfo.Format == TextureFormat::RGBA32 || textureData->mResourceInfo.Format == TextureFormat::RGBX32);

    for (uint32 Y = 0; Y < SizeY; Y++)
    {
        for (uint32 X = 0; X < SizeX; X++)
        {
            const UInt8* data = textureData->GetImageData(miplevel);
            auto srcPixelIdx = FlipY(Y) * outTextureInfo.SizeX + X;
            auto destY = Y;
            auto destX = X;
            auto destPixelIdx = destY * outTextureInfo.SizeX + destX;
            UInt8 r = data[srcPixelIdx * 4];
            UInt8 g = data[srcPixelIdx * 4 + 1];
            UInt8 b = data[srcPixelIdx * 4 + 2];
            UInt8 a = data[srcPixelIdx * 4 + 3];

            if (!alphaMask)
            {
                outTextureInfo.Colors[destPixelIdx].x = static_cast<float>(r) / 255.0f;
                outTextureInfo.Colors[destPixelIdx].y = static_cast<float>(g) / 255.0f;
                outTextureInfo.Colors[destPixelIdx].z = static_cast<float>(b) / 255.0f;
                outTextureInfo.Colors[destPixelIdx].w = textureData->mResourceInfo.Format == TextureFormat::RGBA32 ? static_cast<float>(a) / 255.0f : 1.0f;
            }
            else
            {
                outTextureInfo.Colors[destPixelIdx].x = outTextureInfo.Colors[destPixelIdx].y = outTextureInfo.Colors[destPixelIdx].z = outTextureInfo.Colors[destPixelIdx].w = static_cast<float>(a) / 255.0f;
            }
        }
    }
}

inline void ExportTexture(const std::string& matName, std::shared_ptr<MaterialBakeProxy> textureData, FTexture2DInfo& outTextureInfo, EBlendMode blendMode, float boost, float maxBoost, float3 baseColor, bool flipY)
{
    if (!textureData->HasMaterial(matName))
    {
        return;
    }
    auto SizeX = textureData->GetWidth(matName);
    auto SizeY = textureData->GetHeight(matName);
    outTextureInfo.SizeX = SizeX;
    outTextureInfo.SizeY = SizeY;
    outTextureInfo.WrapMode = 0;
    outTextureInfo.Format = 2;
    outTextureInfo.Colors.Resize(outTextureInfo.SizeX * outTextureInfo.SizeY);

    auto FlipY = [height = SizeY](int y) -> int { return height - 1 - y; };

    bool bBoostColor = (boost < 0.99f || boost > 1.01f);

    for (uint32 Y = 0; Y < SizeY; Y++)
    {
        for (uint32 X = 0; X < SizeX; X++)
        {
            const UInt8* data = textureData->GetData(matName);
            auto srcPixelIdx = (flipY ? FlipY(Y) : Y) * outTextureInfo.SizeX + X;
            //auto srcPixelIdx = FlipY(Y) * outTextureInfo.SizeX + X;
            auto destY = Y;
            auto destX = X;
            auto destPixelIdx = destY * outTextureInfo.SizeX + destX;
            float r = static_cast<float>(data[srcPixelIdx * 4]) / 255.0f;
            float g = static_cast<float>(data[srcPixelIdx * 4 + 1]) / 255.f;
            float b = static_cast<float>(data[srcPixelIdx * 4 + 2]) / 255.f;
            float a = static_cast<float>(data[srcPixelIdx * 4 + 3]) / 255.f;

            auto& destColor = outTextureInfo.Colors[destPixelIdx];
            
            if (blendMode == EBlendMode::BLEND_MODE_MASKED)
            {
                destColor.x = destColor.y = destColor.z = destColor.w = a;
            }
            else if (blendMode == EBlendMode::BLEND_MODE_TRANSLUCENT)
            {
                float inv_src_alpha = 1 - a;
                destColor.x = inv_src_alpha + r * a;
                destColor.y = inv_src_alpha + g * a;
                destColor.z = inv_src_alpha + b * a;
                destColor.w = a;
            }
            else
            {
                destColor.x = r;
                destColor.y = g;
                destColor.z = b;
                destColor.w = a;
            }

            if (bBoostColor)
            {
                FLinearColor ColorXYZ = LinearRGBToXYZ(FLinearColor(destColor.x, destColor.y, destColor.z));
                FLinearColor ColorxyzY = XYZToxyzY(ColorXYZ);
                // Apply Boost to the brightness, which is Y in xyzY
                // Using xyzY allows us to modify the brightness of the color without changing the hue
                // Clamp color to be physically valid for the modified Phong lighting model
                ColorxyzY.A = std::min<float>(ColorxyzY.A * boost, maxBoost);
                ColorXYZ = xyzYToXYZ(ColorxyzY);
                FLinearColor ColorRGB = XYZToLinearRGB(ColorXYZ);
                destColor.x = ColorRGB.R;
                destColor.y = ColorRGB.G;
                destColor.z = ColorRGB.B;
            }

            destColor.x *= baseColor.x;
            destColor.y *= baseColor.y;
            destColor.z *= baseColor.z;
        }
    }
}

static bool GenerateMaterialPropertyData(FTexture2DInfo& outInfo, const cross::resource::TextureResourceDataProvider* textureData, bool alphaMask, unsigned int miplevel)
{
#if CROSSENGINE_EDITOR
    ExportTexture(const_cast<cross::resource::TextureResourceDataProvider*>(textureData), outInfo, alphaMask, miplevel);
    return true;
#else
    return false;
#endif
}

bool FTLBSExporter::InitMaterialInfo(MaterialBakePtr matBakePtr, FMaterialInfo& matInfo)
{
    auto matPtr = matBakePtr->mMaterial;
    unsigned int miplevel = GPUBaking::LightingBakeConfig::GetInstance().GetTextureMipLevel();

    matInfo.Hash.InitRandom();
    matInfo.ShadingModelID = 0;
    matInfo.BlendMode = (UInt32)GPUBaking::EBlendMode::BLEND_MODE_OPAQUE;
    matInfo.bTwoSided = false;
    matInfo.Flags = 0;
    matInfo.OpacityMaskClipValue = 0.0f;

    auto alphaClip = std::get_if<std::vector<float>>(matPtr->GetProperty("_AlphaClip"));
    auto ALPHA_CLIPPING = std::get_if<bool>(matPtr->GetProperty("ALPHA_CLIPPING"));
    cross::NameID forwardPassNameID("forward");
    auto blendEnable = matPtr->EditorGetBlendEnable(forwardPassNameID);
    if (blendEnable)
    {
        matInfo.BlendMode = (UInt32)GPUBaking::EBlendMode::BLEND_MODE_TRANSLUCENT;
    }
    else if (alphaClip && ALPHA_CLIPPING && *ALPHA_CLIPPING)
    {
        matInfo.OpacityMaskClipValue = (*alphaClip)[0];
        matInfo.BlendMode = (UInt32)GPUBaking::EBlendMode::BLEND_MODE_MASKED;
    }
    matInfo.bTwoSided = matBakePtr->IsTwoSided() || blendEnable;
    if (matInfo.bTwoSided)
    {
        matInfo.Flags |= GPUBaking::MATERIAL_FLAGS_TWOSIDE;
    }

    //const auto& colorMapDef = std::get_if<bool>(matPtr->GetProperty("COLOR_MAP") != nullptr ? matPtr->GetProperty("COLOR_MAP") : matPtr->GetProperty("BASE_MAP"));
    const auto& normalMapDef = std::get_if<bool>(matPtr->GetProperty("NORMAL_MAP"));
    // const auto& emissiveEnableDef = std::get_if<bool>(matPtr->GetProperty("ENABLE_EMISSIVE"));
    auto baseColor = std::get_if<std::vector<float>>(matPtr->GetProperty("BaseColor") != nullptr ? matPtr->GetProperty("BaseColor") : matPtr->GetProperty("_BaseColor"));
    // const cross::ResourceFuturePtr<Texture>* diffuseTex = std::get_if<cross::TexturePtr>(matPtr->GetProperty("color_texture") != nullptr ? matPtr->GetProperty("color_texture") : matPtr->GetProperty("_BaseMap"));
    const cross::ResourceFuturePtr<cross::resource::Texture>* normalTex = std::get_if<cross::TexturePtr>(matPtr->GetProperty("normal_texture") != nullptr ? matPtr->GetProperty("normal_texture") : matPtr->GetProperty("_NormalMap"));

    //     if (diffuseTex == nullptr)
    //     {
    //         diffuseTex = std::get_if<cross::TexturePtr>(matPtr->GetProperty("_BaseColorMap0"));
    //         if (diffuseTex)
    //         {
    //             const TextureResourceDataProvider* diffuseTexData = (*diffuseTex)->GetTextureData();
    //             GenerateMaterialPropertyData(matInfo.DiffuseTexture, diffuseTexData, false, miplevel);
    //             return true;
    //         }
    //     }

    float3 linearBaseColor(1, 1, 1);
    if (baseColor)
    {
        linearBaseColor.x = std::powf((*baseColor)[0], 2.2f);
        linearBaseColor.y = std::powf((*baseColor)[1], 2.2f);
        linearBaseColor.z = std::powf((*baseColor)[2], 2.2f);
    }

    auto matBakeProxy = matBakePtr->mMaterialBakeProxy;
    LOG_INFO("zzzzzzzzzzzzzzzz InitMaterialInfo {}", matPtr->GetName());
    if (matBakeProxy && matBakeProxy->IsDataCompleted())
    {
        auto blendMode = static_cast<EBlendMode>(matInfo.BlendMode);
        if (blendMode == EBlendMode::BLEND_MODE_MASKED || blendMode == EBlendMode::BLEND_MODE_OPAQUE)
        {
            ExportTexture("Diffuse", matBakeProxy, matInfo.DiffuseTexture, EBlendMode::BLEND_MODE_OPAQUE, 1.0f, 1.0f, float3(1, 1, 1), matBakePtr->GetFlipY());
        }
        if (blendMode != EBlendMode::BLEND_MODE_OPAQUE)
        {
            ExportTexture("Diffuse", matBakeProxy, matInfo.TransmissionTexture, static_cast<EBlendMode>(matInfo.BlendMode), 1.0f, 1.0f, float3(1, 1, 1), matBakePtr->GetFlipY());
        }
        ExportTexture("Emissive", matBakeProxy, matInfo.EmissiveTexture, EBlendMode::BLEND_MODE_OPAQUE, matBakeProxy->GetEmissiveBoost(), 100.f, float3(1, 1, 1), matBakePtr->GetFlipY());
        // release material proxy data
        // matBakePtr->mMaterialBakeProxy = nullptr;
//        if (blendMode == EBlendMode::BLEND_MODE_TRANSLUCENT)
//        {
//             matInfo.ShadingModelID = 0xffffffff;
//             matInfo.OpacityMaskClipValue = 0.3333f;
//             matInfo.EmissiveTexture.SizeX = matInfo.EmissiveTexture.SizeY = 1;
//             matInfo.EmissiveTexture.WrapMode = 0x10b94b;
//             matInfo.EmissiveTexture.Format = 2;
//             matInfo.EmissiveTexture.Colors.Resize(1);
//             matInfo.EmissiveTexture.Colors[0].x = matInfo.EmissiveTexture.Colors[0].y = matInfo.EmissiveTexture.Colors[0].z = matInfo.EmissiveTexture.Colors[0].w = 0;
//        }
    }

    // normal map not used by dawn
    if (normalMapDef && *normalMapDef && GPUBaking::LightingBakeConfig::GetInstance().GetUseNormalMap())
    {
        assert(normalTex);
        const cross::resource::TextureResourceDataProvider* normalTexData = (*normalTex)->GetTextureData();
        GenerateMaterialPropertyData(matInfo.NormalTexture, normalTexData, false, miplevel);
    }
    return true;
}

bool FTLBSExporter::ExportMaterials()
{
    auto materialCount = ExportContext.Materials.size();

    for (auto matIdx = 0; matIdx < materialCount; ++matIdx)
    {
        auto& matPtr = ExportContext.Materials[matIdx];
        FMaterialInfo& matInfo = SceneInfo.Materials.AddDefaultRef();

        InitMaterialInfo(matPtr, matInfo);

        GPUBaking::SerializeMaterial(ExportContext.Swarm, matInfo.Hash, matInfo, true);

        FSHAHash MaterialHash;
        GPUBaking::ToHash(matInfo.Hash, MaterialHash);
        matInfo = GPUBaking::FMaterialInfo();
        GPUBaking::ToHash(MaterialHash, matInfo.Hash);
        matInfo.ShadingModelID = -1;
    }

    return true;
}

bool FTLBSExporter::ExportMeshInstance(GPUBaking::FSceneInfo& sceneInfo)
{
    int meshIndex = 0;
    SceneAABB = cross::BoundingBox();
    VLMSceneAABB = cross::BoundingBox();
    sceneInfo.NumMeshes = 0;
    for (auto idx = 0; idx < ExportContext.StaticMeshInstances.size(); idx++)
    {
        cross::Float3 scale, translation;
        cross::Quaternion rot;
        ExportContext.StaticMeshInstances[idx].WorldMatrix.Decompose(scale, rot, translation);
        LOG_INFO("ExportMeshInstance {} = {}, {}, {}", ExportContext.StaticMeshInstances[idx].InstanceName, translation.x, translation.y, translation.z);
        auto& meshInst = ExportContext.StaticMeshInstances[idx];
        GPUBaking::FMeshInstanceInfo& MeshInstanceInfo = sceneInfo.MesheInstances.AddDefaultRef();

        const MeshAssetInfo& staticMesh = ExportContext.StaticMeshes[meshInst.StaticMeshIndex];

        MeshInstanceInfo.LevelGuid = DebugLevelGUID;
        //::CoCreateGuid((GUID*)&MeshInstanceInfo.Guid);
        MeshInstanceInfo.Guid.A = MeshInstanceInfo.Guid.B = MeshInstanceInfo.Guid.C = 0;
        MeshInstanceInfo.Guid.D = meshIndex + 1;
        sceneInfo.MeshGuids.Add(MeshInstanceInfo.Guid);   // mesh instance idx -> mesh guid

        MeshInstanceInfo.DiffuseBoost = meshInst.DiffuseBoost;
        MeshInstanceInfo.EmissiveBoost = 1.0f;
        MeshInstanceInfo.MeshIndex = meshInst.StaticMeshIndex;
        MeshInstanceInfo.TexcoordIndex = 0;
        MeshInstanceInfo.Flags = 0;
        if (meshInst.CastShadow)
        {
            MeshInstanceInfo.Flags |= MESH_INSTANCE_FLAGS_SHADOWCAST;
        }
        MeshInstanceInfo.Flags |= MESH_INSTANCE_FLAGS_SHARE_LOD0;
        //TODO(timllpan): have no effect, using material instance flag
        //MeshInstanceInfo.Flags |= MESH_INSTANCE_FLAGS_USE_TWOSIDE_LIGHTING;

        MeshInstanceInfo.Transform = MatrixCE2UE(meshInst.WorldMatrix);
        auto worldInverse = MatrixCE2UE(meshInst.WorldMatrix).Inverted();
        MeshInstanceInfo.InverseTransform = worldInverse;
        MeshInstanceInfo.InverseTransposeTransform = worldInverse.Transpose();

        cross::BoundingBox bbox = staticMesh.MeshAssetData->GetBoundingBox();
        cross::Float3 center, extent;
        bbox.GetCenter(&center);
        bbox.GetExtent(&extent);
        cross::Float4 centerPos = cross::Float4x4::Transform(meshInst.WorldMatrix, cross::Float4{center.x, center.y, center.z, 1.0f});
        center = cross::Float3(centerPos.x, centerPos.y, centerPos.z);
        extent = cross::Float4x4::TransformVectorF3(meshInst.WorldMatrix, extent);
        cross::Float3 ue4Center = Float3CE2UE(center);
        cross::Float3 ue4Extent = Float3Abs(Float3CE2UE(extent));
        MeshInstanceInfo.BoundingBox = FSceneBoxBounds(ue4Center - ue4Extent, ue4Center + ue4Extent);
        cross::BoundingBox::CreateMerged(SceneAABB, SceneAABB, cross::BoundingBox(center, extent));
        if (meshInst.EnableLightProbe)
        {
            cross::BoundingBox::CreateMerged(VLMSceneAABB, VLMSceneAABB, cross::BoundingBox(center, extent));
        }

        // TODO(lele):...RelevantLights MaterialOverrides
        for (auto lightIdx = 0; lightIdx < ExportContext.TotalLightNum; ++lightIdx)
        {
            MeshInstanceInfo.RelevantLights.Add(lightIdx);
        }

        for (auto mIdx = 0; mIdx < meshInst.materiaPtr.size(); ++mIdx)
        {
            auto matIdx = ExportContext.FindMaterial(meshInst.materiaPtr[mIdx]);
            assert(matIdx >= 0);
            MeshInstanceInfo.MaterialOverrides.Add(matIdx);
            float emissiveBoost = meshInst.materiaPtr[mIdx]->GetEmissiveBoost();
            if (emissiveBoost > 1.0)
            {
                MeshInstanceInfo.EmissiveBoost = emissiveBoost;
            }
        }
        if (MeshInstanceInfo.EmissiveBoost > 1.0)
        {
            MeshInstanceInfo.Flags |= MESH_INSTANCE_FLAGS_USE_EMISSION_LIGHTING;
            // MeshInstanceInfo.EmissiveBoost = 0.0f;//not used
        }

        meshIndex += 1;
        sceneInfo.NumMeshes++;
    }

    cross::Float3 sceneAABBCenter, sceneAABBExtent;
    SceneAABB.GetCenter(&sceneAABBCenter);
    SceneAABB.GetExtent(&sceneAABBExtent);
    cross::Float3 ue4SceneCenter = Float3CE2UE(sceneAABBCenter);
    cross::Float3 ue4SceneExtent = Float3Abs(Float3CE2UE(sceneAABBExtent));
    auto& importBounds = sceneInfo.ImportanceVolumes.AddDefaultRef();
    importBounds.Min = ue4SceneCenter - ue4SceneExtent - cross::Float3(500, 500, 500);
    importBounds.Max = ue4SceneCenter + ue4SceneExtent + cross::Float3(500, 500, 500);
    sceneInfo.Bounds.Origin = ue4SceneCenter;
    sceneInfo.Bounds.BoxExtent = ue4SceneExtent + cross::Float3(500, 500, 500);
    sceneInfo.Bounds.SphereRadius = sqrtf(sceneInfo.Bounds.BoxExtent.x * sceneInfo.Bounds.BoxExtent.x + sceneInfo.Bounds.BoxExtent.y * sceneInfo.Bounds.BoxExtent.y + sceneInfo.Bounds.BoxExtent.z * sceneInfo.Bounds.BoxExtent.z);
    sceneInfo.ImportanceBounds = sceneInfo.Bounds;
    return true;
}

float GetLightPowerFactor(const LightInfo* In)
{
    return std::fmax(0.f, In->ImportantFactor) * In->IndirectLightingIntensity;
}

float GetLightPower(const BakerDirectionLightInfo* In, float DirectPhotonDensity, float SceneRadius)
{
    int32 NumDirectPhotons = 0;
    const float ImportanceDiskAreaMillions = PI * SceneRadius * SceneRadius / 1000000.0f;
    NumDirectPhotons = static_cast<uint32>(std::floorf(std::fmax(ImportanceDiskAreaMillions * DirectPhotonDensity, 0.f)));
    return NumDirectPhotons * GetLightPowerFactor(In);
}

float GetPointLightPower(float DirectPhotonDensity, float SceneRadius, float Intensity, float AttenRadius)
{
    const float InfluenceSphereSurfaceAreaMillions = 4.0f * PI * (AttenRadius * AttenRadius) / 1000000.0f;
    const int NumDirectPhotons = static_cast<int>(InfluenceSphereSurfaceAreaMillions * DirectPhotonDensity);
    return NumDirectPhotons * Intensity;
}

float GetSpotLightPower(float DirectPhotonDensity, float AttenuationRadius, float Intensity, float InnerConeAngle, float OuterConeAngle)
{
    float ClampedInnerConeAngle = std::clamp<float>(InnerConeAngle, 0.0f, 89.0f) * PI / 180.0f;
    float ClampedOuterConeAngle = std::clamp<float>(OuterConeAngle * PI / 180.0f, ClampedInnerConeAngle + 0.001f, 89.0f * PI / 180.0f + 0.001f);
    float CosOuterConeAngle = std::cosf(ClampedOuterConeAngle);

    const float InfluenceSphereSurfaceAreaMillions = 4.0f * PI * (AttenuationRadius * AttenuationRadius) / 1000000.0f;
    const float ConeSolidAngle = 2.0f * PI * (1.0f - CosOuterConeAngle);
    // Find the fraction of the sphere's surface area that is inside the cone
    const float ConeSurfaceAreaSphereFraction = ConeSolidAngle / (4.0f * PI);
    // Gather enough photons to meet DirectPhotonDensity on the spherical cap at the influence radius of the spot light.
    const int32 NumDirectPhotons = static_cast<int>(InfluenceSphereSurfaceAreaMillions * ConeSurfaceAreaSphereFraction * DirectPhotonDensity);
    return NumDirectPhotons * Intensity;
}

float GetRectLightPower(float DirectPhotonDensity, float SceneRadius, float Intensity, float AttenRadius)
{
    const float InfluenceSphereSurfaceAreaMillions = 4.0f * PI * (AttenRadius * AttenRadius) / 1000000.0f;
    // Find the fraction of the sphere's surface area that is inside the cone
    const float SurfaceAreaSphereFraction = 0.25f;
    // Gather enough photons to meet DirectPhotonDensity on the spherical cap at the influence radius of the spot light.
    const int32 NumDirectPhotons = static_cast<uint32>(std::floorf(InfluenceSphereSurfaceAreaMillions * SurfaceAreaSphereFraction * DirectPhotonDensity));
    return NumDirectPhotons * Intensity;
}

FLinearColor MakeFromColorTemperature(float Temp)
{
    Temp = std::clamp(Temp, 1000.0f, 15000.0f);

    // Approximate Planckian locus in CIE 1960 UCS
    float u = (0.860117757f + 1.54118254e-4f * Temp + 1.28641212e-7f * Temp * Temp) / (1.0f + 8.42420235e-4f * Temp + 7.08145163e-7f * Temp * Temp);
    float v = (0.317398726f + 4.22806245e-5f * Temp + 4.20481691e-8f * Temp * Temp) / (1.0f - 2.89741816e-5f * Temp + 1.61456053e-7f * Temp * Temp);

    float x = 3.0f * u / (2.0f * u - 8.0f * v + 4.0f);
    float y = 2.0f * v / (2.0f * u - 8.0f * v + 4.0f);
    float z = 1.0f - x - y;

    float Y = 1.0f;
    float X = Y / y * x;
    float Z = Y / y * z;

    // XYZ to RGB with BT.709 primaries
    float R = 3.2404542f * X + -1.5371385f * Y + -0.4985314f * Z;
    float G = -0.9692660f * X + 1.8760108f * Y + 0.0415560f * Z;
    float B = 0.0556434f * X + -0.2040259f * Y + 1.0572252f * Z;

    return FLinearColor(R, G, B);
}

static void CopyLight(const LightInfo* in, FLightInfo& out, uint32& outFlags) 
{
    memset(&out, 0, sizeof(FLightInfo));

    outFlags = LIGHT_FLAG_INDIRECT_LIGHTING | LIGHT_FLAG_INDIRECT_LIGHTING_PROBE;

    if (in->IsStatic)
    {
        outFlags |= LIGHT_FLAG_HASSTATICLIGHTING | LIGHT_FLAG_DIRECT_LIGHTING | LIGHT_FLAG_DIRECT_LIGHTING_PROBE;
        if (in->CastStaticShadow)
        {
            outFlags |= LIGHT_FLAG_CAST_DIRECT_SHADOW;
        }
    }
    else if (in->CastStaticShadow)
    {
        outFlags |= LIGHT_FLAG_DISTANCE_FIELD_SHADOW;
        outFlags |= LIGHT_FLAG_CAST_DIRECT_SHADOW;
    }

    float Brightness = in->ComputeLightBrightness();

    cross::Float3 LightColor = in->Color;
    bool bUseTemperature = false;
    if (bUseTemperature)
    {
        float Temperature = 5700;
        auto tempColor = MakeFromColorTemperature(Temperature);
        LightColor = LightColor * cross::Float3{tempColor.R, tempColor.G, tempColor.B};
    }

    out.Attenuation = Brightness;
    out.Color = LightColor * Brightness;
    out.IndirectColor = LightColor * Brightness * in->IndirectLightingIntensity;
    out.Position = Float3CE2UE(in->Position);
    out.Normal = Float3CE2UE(in->Direction);
    out.Tangent = Float3CE2UE(in->Tangent);
}

static void ExportLightFunc(float inSceneBoundsRadius, const BakerDirectionLightInfo* inLightInfo, FLightInfo& outLightData, FGuidInfo& outLightGuid, uint32& outLightFlags, float& outLightPower) 
{
    ::CoCreateGuid(reinterpret_cast<GUID*>(&outLightGuid));

    CopyLight(inLightInfo, outLightData, outLightFlags);

    outLightData.Type = (UInt32)GPUBaking::FLightType::LIGHT_DIRECTIONAL;
    outLightData.Normal = outLightData.Normal * -1.0f;
    outLightData.Dimensions = {0, 0, inSceneBoundsRadius * 2 * tan(cLightSourceAngle * PI / 180.0f)};

#if USE_LUMINANCE_AS_POWER
    outLightPower = FLOAT3_LUMINANCE(outLightData.IndirectColor);
#else
    float DirectPhotonDensity = 350.0f;
    outLightPower = GetLightPower(&inLightInfo, DirectPhotonDensity, inSceneBoundsRadius);
#endif
}

static void ExportLightFunc(const BakerPointLightInfo* inLightInfo, FLightInfo& outLightData, FGuidInfo& outLightGuid, uint32& outLightFlags, float& outLightPower)
{
    ::CoCreateGuid(reinterpret_cast<GUID*>(&outLightGuid));

    uint32 lightFlags = 0;
    CopyLight(inLightInfo, outLightData, lightFlags);
    lightFlags |= LIGHT_FLAG_INVERSE_SQUARED;
    outLightFlags = lightFlags;

    outLightData.Type = (UInt32)GPUBaking::FLightType::LIGHT_POINT;
    outLightData.Attenuation = inLightInfo->Range;
    outLightData.Dimensions.z = std::fmax(1.0f, cSourceRadius);
    outLightData.Dimensions.w = 0.0f;   // SourceLength;
    // panlele: need modify dynamic
    outLightData.dPdu = {8.f, 1.f, 0.f};
    outLightData.dPdv = {0, 0, 0};
#if USE_LUMINANCE_AS_POWER
    float LightPower = FLOAT3_LUMINANCE(outLightData.IndirectColor);

    const float RadiusFraction = .3f;
    const float DistanceToEvaluate = RadiusFraction * outLightData.Attenuation;
    const float DistanceSquared = DistanceToEvaluate * DistanceToEvaluate;

    LightPower = LightPower / DistanceSquared;

    outLightPower = LightPower;
#else
    float DirectPhotonDensity = 350.0f;
    sceneInfo.LightPowers[totalLightIdx] = GetPointLightPower(DirectPhotonDensity, sceneInfo.Bounds.SphereRadius, lightInfo.Intensity, lightInfo.Range);   // to be modified
#endif 
}

static void ExportLightFunc(const BakerSpotLightInfo* inLightInfo, FLightInfo& outLightData, FGuidInfo& outLightGuid, uint32& outLightFlags, float& outLightPower)
{
    ::CoCreateGuid(reinterpret_cast<GUID*>(&outLightGuid));

    uint32 lightFlags = 0;
    CopyLight(inLightInfo, outLightData, lightFlags);
    lightFlags |= LIGHT_FLAG_INVERSE_SQUARED;
    outLightFlags = lightFlags;

    float CosOuterCone = cosf(inLightInfo->OuterConeAngle * M_PI / 180.0f);
    float InvCosConeDifference = 1.0f / (cosf(inLightInfo->InnerConeAngle * M_PI / 180.0f) - CosOuterCone);

    outLightData.Type = (UInt32)GPUBaking::FLightType::LIGHT_SPOT;
    outLightData.Attenuation = inLightInfo->Range;
    outLightData.dPdu.x = 8.0f;
    outLightData.Dimensions = {CosOuterCone, InvCosConeDifference, std::max<float>(1.0f, cSourceRadius), cSourceLength};
#if USE_LUMINANCE_AS_POWER
    float LightPower = FLOAT3_LUMINANCE(outLightData.IndirectColor);

    const float RadiusFraction = .3f;
    const float DistanceToEvaluate = RadiusFraction * outLightData.Attenuation;
    const float DistanceSquared = DistanceToEvaluate * DistanceToEvaluate;

    LightPower = LightPower / DistanceSquared;

    outLightPower = LightPower;
#else
    float DirectPhotonDensity = 350.0f;
    sceneInfo.LightPowers[lightIdx] = GetSpotLightPower(DirectPhotonDensity, lightInfo.Range, lightInfo.Intensity, lightInfo.InnerConeAngle, lightInfo.OuterConeAngle);
#endif 
}

static void ExportLightFunc(const BakerRectLightInfo* inLightInfo, FLightInfo& outLightData, FGuidInfo& outLightGuid, uint32& outLightFlags, float& outLightPower) 
{
    ::CoCreateGuid(reinterpret_cast<GUID*>(&outLightGuid));

    uint32 lightFlags = 0;
    CopyLight(inLightInfo, outLightData, lightFlags);
    lightFlags |= LIGHT_FLAG_INVERSE_SQUARED;
    outLightFlags = lightFlags;

    outLightData.Type = (UInt32)GPUBaking::FLightType::LIGHT_RECT;
    outLightData.dPdu = Float3CE2UE(inLightInfo->Direction.Cross(inLightInfo->Tangent));
    outLightData.dPdv = Float3CE2UE(inLightInfo->Tangent);
    outLightData.RectLightBarnCosAngle = cosf(inLightInfo->BarnDoorAngle * M_PI / 180.f);
    outLightData.RectLightBarnLength = inLightInfo->BarnDoorLength;
    outLightData.Color = outLightData.Color / (inLightInfo->SourceWidth * inLightInfo->SourceHeight * 0.5f);
    outLightData.IndirectColor = outLightData.IndirectColor / (inLightInfo->SourceWidth * inLightInfo->SourceHeight * 0.5f);

    outLightData.Attenuation = inLightInfo->Range;
    outLightData.Dimensions = {inLightInfo->SourceWidth, inLightInfo->SourceHeight, inLightInfo->SourceWidth * 0.5f};
#if USE_LUMINANCE_AS_POWER
    outLightPower = FLOAT3_LUMINANCE(outLightData.IndirectColor);
#else
    float DirectPhotonDensity = 350.0f;
    sceneInfo.LightPowers[lightIdx] = GetRectLightPower(DirectPhotonDensity, sceneInfo.Bounds.SphereRadius, inLightInfo.Intensity, inLightInfo.Range);
#endif
}

bool FTLBSExporter::ExportLights(GPUBaking::FSceneInfo& sceneInfo)
{
    uint32 totalLightsNum = static_cast<uint32>(ExportContext.DirectionalLights.size() + ExportContext.PointLights.size() + ExportContext.SpotLights.size() + ExportContext.RectLights.size());
    sceneInfo.Lights.Resize(totalLightsNum);
    sceneInfo.LightGuids.Resize(totalLightsNum);
    sceneInfo.LightFlags.Resize(totalLightsNum);
    sceneInfo.LightPowers.Resize(totalLightsNum);

    int lightIdx = 0;
    for (int idx = 0; idx < ExportContext.DirectionalLights.size(); idx++, lightIdx++)
    {
        const BakerDirectionLightInfo* inLightInfo = ExportContext.DirectionalLights[idx].get();
        ExportLightFunc(sceneInfo.Bounds.SphereRadius, inLightInfo, sceneInfo.Lights[lightIdx], sceneInfo.LightGuids[lightIdx], sceneInfo.LightFlags[lightIdx], sceneInfo.LightPowers[lightIdx]);
    }

    for (int idx = 0; idx < ExportContext.PointLights.size(); idx++, lightIdx++)
    {
        BakerPointLightInfo* inLightInfo = ExportContext.PointLights[idx].get();
        ExportLightFunc(inLightInfo, sceneInfo.Lights[lightIdx], sceneInfo.LightGuids[lightIdx], sceneInfo.LightFlags[lightIdx], sceneInfo.LightPowers[lightIdx]);
    }

    for (int idx = 0; idx < ExportContext.SpotLights.size(); idx++, lightIdx++)
    {
        BakerSpotLightInfo* inLightInfo = ExportContext.SpotLights[idx].get();
        ExportLightFunc(inLightInfo, sceneInfo.Lights[lightIdx], sceneInfo.LightGuids[lightIdx], sceneInfo.LightFlags[lightIdx], sceneInfo.LightPowers[lightIdx]);
    }

    for (int idx = 0; idx < ExportContext.RectLights.size(); idx++, lightIdx++)
    {
        const BakerRectLightInfo* inLightInfo = ExportContext.RectLights[idx].get();
        ExportLightFunc(inLightInfo, sceneInfo.Lights[lightIdx], sceneInfo.LightGuids[lightIdx], sceneInfo.LightFlags[lightIdx], sceneInfo.LightPowers[lightIdx]);
    }

    UInt32 prtLightSize = static_cast<UInt32>(ExportContext.LocalGroupedPRTLights.size());
    sceneInfo.LocalTransferLights.Resize(prtLightSize);
    sceneInfo.LocalTransferLightGuids.Resize(prtLightSize);
    sceneInfo.LocalTransferLightFlags.Resize(prtLightSize);
    sceneInfo.LocalTransferLightPowers.Resize(prtLightSize);
    sceneInfo.LocalTransferGroups.Resize(static_cast<UInt32>(ExportContext.GetLocalGroupSize()));
    std::map<FGuidInfo, int> LocalGroupIdxMap;
    int GroupIdx = 0;

    for (int idx = 0; idx < ExportContext.LocalGroupedPRTLights.size(); idx++)
    {
        auto& outLightData = sceneInfo.LocalTransferLights[idx];
        auto& outLightGuid = sceneInfo.LocalTransferLightGuids[idx];
        auto& outLightFlags = sceneInfo.LocalTransferLightFlags[idx];
        auto& outLightPower = sceneInfo.LocalTransferLightPowers[idx];

        auto prtLight = ExportContext.LocalGroupedPRTLights[idx];

        FGroupedLPRTInfo* outGroupBakingInfo = nullptr;
        FGuidInfo localGroupGuid = ExportContext.FindLocalGroupGuid(prtLight->LocalLightGroupID);
        if (LocalGroupIdxMap.find(localGroupGuid) == LocalGroupIdxMap.end()) 
        {
            outGroupBakingInfo = &sceneInfo.LocalTransferGroups[GroupIdx];
            outGroupBakingInfo->GroupGuid = localGroupGuid;
            LocalGroupIdxMap[localGroupGuid] = GroupIdx++;
        }
        else 
        {
            outGroupBakingInfo = &sceneInfo.LocalTransferGroups[LocalGroupIdxMap[localGroupGuid]];
        }
        outGroupBakingInfo->LocalTransferLightIndices.Add(idx);

        if (prtLight->LightType == cross::LightType::Point)
        {
            ExportLightFunc(static_cast<BakerPointLightInfo*>(prtLight.get()), outLightData, outLightGuid, outLightFlags, outLightPower);
        }
        else if (prtLight->LightType == cross::LightType::Spot)
        {
            ExportLightFunc(static_cast<BakerSpotLightInfo*>(prtLight.get()), outLightData, outLightGuid, outLightFlags, outLightPower);
        }
        else if (prtLight->LightType == cross::LightType::Rect)
        {
            ExportLightFunc(static_cast<BakerRectLightInfo*>(prtLight.get()), outLightData, outLightGuid, outLightFlags, outLightPower);
        }
    }

    sceneInfo.NumLights = sceneInfo.Lights.Num();
    Assert(sceneInfo.NumLights == static_cast<uint32>(ExportContext.TotalLightNum));

    // panlele: trick, cubemap face order need check
    std::shared_ptr<BakerSkyLightInfo> skyLight = ExportContext.GetSkyLight();
    if (skyLight)
    {
        auto skyMatProxy = skyLight->mMaterialBakeProxy;
        assert(skyMatProxy->GetWidth() == skyMatProxy->GetHeight());
        int32 CubemapSize = skyMatProxy->GetWidth();

        auto& SkyLight = SceneInfo.SkyLight;
        memset(&SkyLight, 0, sizeof(SkyLight));

        // float3 SkyColor{ 53/255.f, 70/255.f, 104/255.f };
        float3 SkyColor{255 / 255.f, 255 / 255.f, 255 / 255.f};
        SkyLight.SkyData.Color = SkyColor;
        SkyLight.SkyData.Color.w = skyLight->IndirectLightingIntensity;   // IndirectLightingIntensity

        /**
         * Represents a constant color light surrounding the upper hemisphere of the level, like a sky.
         * This light source currently does not get bounced as indirect lighting and causes reflection capture brightness to be incorrect.  Prefer using a Static Skylight instead.
         */
        FLinearColor EnvironmentColor(0, 0, 0, 0);
        SkyLight.SkyData.EnvColor = {EnvironmentColor.R, EnvironmentColor.G, EnvironmentColor.B, EnvironmentColor.A};

        int32 NumMips = CeilLogTwo(CubemapSize) + 1;

        auto ConvertToUE4Texel = [CubemapSize = CubemapSize](int32 faceIndex, int32 x, int32 y, float rotation) { 
            auto quat = cross::Quaternion::CreateFromAxisAngle(cross::Float3(0, 0, 1), rotation / 180.0f * PI);
            auto coordOrig = cross::Float3(x * 1.0f / (CubemapSize - 1), y * 1.0f / (CubemapSize - 1), 0.f);
            coordOrig = coordOrig - cross::Float3(0.5f, 0.5f, 0.f);
            auto coord = quat.Float3Rotate(coordOrig);
            coord = coord + cross::Float3(0.5f, 0.5f, 0.f);
            coord *= (CubemapSize - 1.0f);
            return CubemapSize * CubemapSize * faceIndex + static_cast<int>(std::round(coord.y)) * CubemapSize + static_cast<int>(std::round(coord.x)); 
        };

        const int SkyLightHasStaticLighting = LightingBakeConfig::GetInstance().GetSkyLightStaticMode();
        SkyLight.SkyData.MipDimensions = {NumMips, CubemapSize, SkyLightHasStaticLighting ? 0 : 1};
        SkyLight.EnvCube.Resize(CubemapSize * CubemapSize * 6);
        for (int32 faceIndex = 0; faceIndex < 6; faceIndex++)
        {
            int32 ue4FaceIndex = UECubeFaceTranslate[faceIndex];
            UInt8* srcData = skyMatProxy->GetData(faceIndex);
            for (int32 y = 0; y < CubemapSize; y++)
            {
                for (int32 x = 0; x < CubemapSize; x++)
                {
                    int destTexelIndex = 0;
                    if (ue4FaceIndex == 0)
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, -90);
                    }
                    else if (ue4FaceIndex == 1)
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, 90);
                    }
                    else if (ue4FaceIndex == 2)
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, 180);
                    }
                    else if (ue4FaceIndex == 3)
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, 0);
                    }
                    else if (ue4FaceIndex == 4)
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, 0);
                    }
                    else
                    {
                        destTexelIndex = ConvertToUE4Texel(ue4FaceIndex, x, y, 0);
                    }
                    
                    int32 texelIndex = y * CubemapSize + x;

                    Assert(skyMatProxy->mFormat == cross::GraphicsFormat::R16G16B16A16_SFloat);
                    uint16* srcPixelData = reinterpret_cast<uint16*>(srcData + skyMatProxy->mPixelSize * texelIndex);
                    SkyLight.EnvCube[destTexelIndex].x = FFloat16(*(srcPixelData)).GetFloat();       // 255.0f;
                    SkyLight.EnvCube[destTexelIndex].y = FFloat16(*(srcPixelData + 1)).GetFloat();   // 255.0f;
                    SkyLight.EnvCube[destTexelIndex].z = FFloat16(*(srcPixelData + 2)).GetFloat();   // 255.0f;
                    SkyLight.EnvCube[destTexelIndex].w = FFloat16(*(srcPixelData + 3)).GetFloat();   // 255.0f;
                }
            }
        }

        if (false)
        {//dump EnvCube to png
            for (auto faceIndex = 0; faceIndex < 6; faceIndex++)
            {
                imageio::image img(CubemapSize, CubemapSize);
                auto& debugTex = img.get_pixels();
                for (UInt32 y = 0; y < img.get_height(); y++)
                {
                    for (UInt32 x = 0; x < img.get_width(); x++)
                    {
                        auto srcIdx = y * img.get_width() + x;
                        auto& srcData = SkyLight.EnvCube[faceIndex * CubemapSize * CubemapSize + y * CubemapSize + x];
                        debugTex[srcIdx].r = static_cast<uint8>(srcData.x * 255);
                        debugTex[srcIdx].g = static_cast<uint8>(srcData.y * 255);
                        debugTex[srcIdx].b = static_cast<uint8>(srcData.z * 255);
                        debugTex[srcIdx].a = static_cast<uint8>(srcData.w * 255);
                    }
                }
                imageio::save_png((LightingBakeConfig::GetInstance().GetProjectionRootPath() + LightingBakeConfig::GetInstance().GetLightMapSavedRelPath() + std::string("sky_ue_") + std::to_string(faceIndex) + ".png").c_str(), img);
            }
        }

        /* if (false)
        {//replace ue4 cubemap
            for (auto faceIndex = 0; faceIndex < 6; faceIndex++)
            {
                std::string rootPath = "D:/engine/book_of_the_dead/skybox/sky_cube_";
                imageio::image img;
                imageio::load_image(rootPath + std::to_string(faceIndex) + ".png", img);
                for (int32 y = 0; y < CubemapSize; y++)
                {
                    for (int32 x = 0; x < CubemapSize; x++)
                    {
                        auto& destData = SkyLight.EnvCube[faceIndex * CubemapSize * CubemapSize + y * CubemapSize + x];
                        destData.x = img(x, y).r / 255.0f;
                        destData.y = img(x, y).g / 255.0f;
                        destData.z = img(x, y).b / 255.0f;
                        destData.w = img(x, y).a / 255.0f;
                    }
                }
            }
        }*/
    }
    return true;
}

bool FTLBSExporter::ExportJob()
{
    const UDawnSettings* settings = ExportContext.GetDawnBuildSetting();
    int jobIndex = 0;

    memset(&JobInputs, 0, sizeof(JobInputs));
    FBakingJobParameters& BakingParameters = JobInputs.BakingParameters;
    BakingParameters.bUseFastVoxelization = settings->Misc.bExportBigWorld;

    BakingParameters.Lightmap2DParameter.DenoiserMode = settings->Lightmap2D.DenoiserMode;
    BakingParameters.Lightmap2DParameter.SuperSampleFactor = 1;
    BakingParameters.Lightmap2DParameter.IterationNum = settings->Lightmap2D.SamplesPerPixel;                    // 128;
    BakingParameters.Lightmap2DParameter.PenumbraShadowFraction = settings->Lightmap2D.PenumbraShadowFraction;   // 1.0f;
    BakingParameters.Lightmap2DParameter.MaxDepth = settings->Lightmap2D.MaxBounces;                             // 2;
    BakingParameters.Lightmap2DParameter.MaxSkyBounces = settings->Lightmap2D.NumSkyLightingBounces;             // 1;

    // UPROPERTY(config, EditAnywhere, AdvancedDisplay, Category = SignedDistanceFieldShadow, meta = (UIMin = "1", UIMax = "30"))
    BakingParameters.SdfShadowParameter.MaxUpsamplingFactor = settings->SDFShadow.MaxUpsamplingFactor;   // 3;

    BakingParameters.AdaptiveSamplingParameter.AdaptiveIterationStartNum = static_cast<int>(settings->Lightmap2D.SamplesPerPixel * settings->AdaptiveSampling.StartPercentage);   // 25;
    BakingParameters.AdaptiveSamplingParameter.AdaptiveStartBounces = settings->AdaptiveSampling.StartBounces;                                                         // 3;
    BakingParameters.AdaptiveSamplingParameter.AdaptiveIterationStep = settings->AdaptiveSampling.Step;                                                                // 128;
    BakingParameters.AdaptiveSamplingParameter.AdaptiveMaxError = settings->AdaptiveSampling.MaxError;                                                                 // 0.05f;

    BakingParameters.LightProbeParameter.IterationNumForLightProbe = settings->VolumetricLightmap.SamplesPerPixel;   // 256;//TODO
    BakingParameters.LightProbeParameter.MaxDepthForLightProbe = settings->VolumetricLightmap.MaxBounces;            // 2;
    BakingParameters.RasterizationParameter.RasterizationBias = settings->Lightmap2D.RasterizationBias;              // 0.1f;

    auto& AmbientOcclusionParameter = BakingParameters.AmbientOcclusionParameter;
    AmbientOcclusionParameter.DirectAmbientOcclusionFactor = settings->AmbientOcclusion.DirectOcclusionFraction;       // 0.5f;
    AmbientOcclusionParameter.IndirectAmbientOcclusionFactor = settings->AmbientOcclusion.IndirectOcclusionFraction;   // 1.0f;
    AmbientOcclusionParameter.OcclusionExponent = settings->AmbientOcclusion.OcclusionExponent;                        // 2.0f;

    AmbientOcclusionParameter.bUseAmbientOcclusion = settings->AmbientOcclusion.bUseAmbientOcclusion;           // false;
    AmbientOcclusionParameter.bDebugAmbientOcclusion = settings->AmbientOcclusion.bVisualizeAmbientOcclusion;   // false;
    AmbientOcclusionParameter.MaxAmbientOcclusion = settings->AmbientOcclusion.MaxOcclusionDistance;            // 400;

    auto& RayTracingParameter = BakingParameters.RayTracingParameter;
    RayTracingParameter.MinRayOffset = 0.1f;
    RayTracingParameter.MinNormalOffset = 3.0f;
    RayTracingParameter.NormalOffsetSampleRadiusScale = 0.5f;
    RayTracingParameter.TangentOffsetSampleRadiusScale = 0.8f;

    auto& ArtifactParameter = BakingParameters.ArtifactParameter;
    ArtifactParameter.bSeamFixed = settings->Seam.bEnableFix ? 1 : 0;
    ArtifactParameter.SeamSampleIteration = settings->Seam.NumSamples;                // 1;
    ArtifactParameter.SeamLambda = settings->Seam.Lambda;                             // 0.1f;
    ArtifactParameter.SeamCosNormalThreshold = settings->Seam.CosNormalThreshold;     // 0.5f;
    ArtifactParameter.bFillUnmappedTexel = settings->Lightmap2D.bFillUnmappedTexel;   // false;
#if !DAWN_PRT_BRANCH
    BakingParameters.ArtifactParameter.DawnLightmapDenoiserDirectSmoothingFactor = settings->DawnLightmapDenoiser.DirectSmoothingFactor;
    BakingParameters.ArtifactParameter.DawnLightmapDenoiserIndirectSmoothingFactor = settings->DawnLightmapDenoiser.IndirectSmoothingFactor;
#endif

    auto& RasterizationParameter = BakingParameters.RasterizationParameter;
    RasterizationParameter.SmallestTexelRadius = 0.1f;
    RasterizationParameter.bUseConservativeTexelRasterization = true;
    RasterizationParameter.bUseMaxWeight = true;

    auto& PrecomputedTransferParameters = BakingParameters.PrecomputedTransferParameters;
    PrecomputedTransferParameters.bPrecomputedRadianceTransfer = true;
    PrecomputedTransferParameters.bPrecomputedLocalRadianceTransfer = true;
    PrecomputedTransferParameters.bPrecomputedRadianceTransferForSurface = true;

    JobInputs.ShadowSettingParameter = LightingBakeConfig::GetInstance().GetShadowSettingParameter();

    bool bPadMappings = LightingBakeConfig::GetInstance().bPadMappings;

    JobInputs.Lightmap2DJobs.Resize((unsigned int)ExportContext.LightMapJobs.size());
    for (auto i = 0; i < ExportContext.LightMapJobs.size(); i++)
    {
        auto& jobInfo = ExportContext.LightMapJobs[i];
        GPUBaking::FLightmap2DInput& Lightmap2DData = JobInputs.Lightmap2DJobs[i];
        memset(&Lightmap2DData, 0, sizeof(Lightmap2DData));
        // Lightmap2DData.JobID.A = 0;
        // Lightmap2DData.JobID.B = 0;
        // Lightmap2DData.JobID.C = 0;
        // Lightmap2DData.JobID.D = jobIndex + 1;
        Lightmap2DData.JobID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];
        Lightmap2DData.JobID.B = GPUBaking::EGUID_B_FOR_LIGHTMAP;

        //assert(ExportContext.GetMesh(jobInfo.MeshInstanceIndex).MeshAssetData->HasVertexChannel(cross::VertexChannel::TexCoord1));

        jobInfo.Guid = Lightmap2DData.JobID;

        Lightmap2DData.MeshID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];
        Lightmap2DData.Size.x = bPadMappings ? jobInfo.Width - 2 : jobInfo.Width;
        Lightmap2DData.Size.y = bPadMappings ? jobInfo.Height - 2 : jobInfo.Height;
        Lightmap2DData.LightmapUVIndex = 1;   // panlele: need modify dynamic
        Lightmap2DData.Flags = GPUBaking::LIGHTMAP_FLAGS_INDIRECT;
        Lightmap2DData.Flags |= GPUBaking::LIGHTMAP_FLAGS_BILINEAR_FILTER;   // to be modified
        Lightmap2DData.Flags |= bPadMappings ? GPUBaking::LIGHTMAP_FLAGS_PADDING : 0;
        Lightmap2DData.Flags |= GPUBaking::LIGHTMAP_FLAGS_DIRECT;   // to be modified
        // Lightmap2DData.Flags |= GPUBaking::LIGHTMAP_FLAGS_OVERRIDE_PARAMS;
        Lightmap2DData.OverrideBakingParams.LightmapBakingParams = JobInputs.BakingParameters.Lightmap2DParameter;
        Lightmap2DData.OverrideBakingParams.ArtifactBakingParams = JobInputs.BakingParameters.ArtifactParameter;
        Lightmap2DData.OverrideBakingParams.AOBakingParams = JobInputs.BakingParameters.AmbientOcclusionParameter;
        #if ENABLE_PRT_API
        Lightmap2DData.OverrideBakingParams.bPrecomputedRadianceTransferForSurface = true;
        #else
        Lightmap2DData.OverrideBakingParams.bPrecomputedRadianceTransferForSurface = false;
        #endif

        jobIndex++;
    }

    TSerializedArray<GPUBaking::FGuidInfo> localTransferLightGroups;
    localTransferLightGroups.Resize(SceneInfo.LocalTransferGroups.Num());
    for (auto i = 0; i < SceneInfo.LocalTransferGroups.Num(); i++)
    {
        localTransferLightGroups[i] = SceneInfo.LocalTransferGroups[i].GroupGuid;
    }
    if (SceneInfo.LocalTransferGroups.Num() > 0) 
    {
        JobInputs.LocalTransferJobs.Resize(static_cast<UInt32>(ExportContext.LocalLightMapPRTJobs.size()));
        for (auto i = 0; i < ExportContext.LocalLightMapPRTJobs.size(); i++)
        {
            BakerLocalLightMapPRTJobInfo& jobInfo = ExportContext.LocalLightMapPRTJobs[i];
            auto& LocalTransferJobData = JobInputs.LocalTransferJobs[i];
            memset(&LocalTransferJobData, 0, sizeof(LocalTransferJobData));
            LocalTransferJobData.JobID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];
            LocalTransferJobData.JobID.B = GPUBaking::EGUID_B_FOR_LOCALTRANSFER;

            jobInfo.Guid = LocalTransferJobData.JobID;

            LocalTransferJobData.MeshID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];

            LocalTransferJobData.Size.x = bPadMappings ? jobInfo.Width - 2 : jobInfo.Width;
            LocalTransferJobData.Size.y = bPadMappings ? jobInfo.Height - 2 : jobInfo.Height;
            LocalTransferJobData.LightmapUVIndex = 1;

            LocalTransferJobData.Flags = GPUBaking::LIGHTMAP_FLAGS_INDIRECT | GPUBaking::LIGHTMAP_FLAGS_DIRECT;
            LocalTransferJobData.Flags |= GPUBaking::LIGHTMAP_FLAGS_BILINEAR_FILTER;   // to be modified
            LocalTransferJobData.Flags |= bPadMappings ? GPUBaking::LIGHTMAP_FLAGS_PADDING : 0;

            LocalTransferJobData.LightGroups.Resize(static_cast<UInt32>(jobInfo.LocalLightGroupIDs.size()));
            for (auto groupIdx = 0; groupIdx < jobInfo.LocalLightGroupIDs.size(); groupIdx++)
            {
                LocalTransferJobData.LightGroups[groupIdx] = ExportContext.FindLocalGroupGuid(jobInfo.LocalLightGroupIDs[groupIdx]);
            }
        }
    }

    JobInputs.SDFShadowJobs.Resize((unsigned int)ExportContext.SDFShadowMapJobs.size());
    for (auto i = 0; i < ExportContext.SDFShadowMapJobs.size(); i++)
    {
        auto& jobInfo = ExportContext.SDFShadowMapJobs[i];
        GPUBaking::FSDFShadowInput& SDFShadowData = JobInputs.SDFShadowJobs[i];
        memset(&SDFShadowData, 0, sizeof(GPUBaking::FSDFShadowInput));

        SDFShadowData.JobID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];
        SDFShadowData.JobID.B = GPUBaking::EGUID_B_FOR_SHADOWMAP;

        jobInfo.Guid = SDFShadowData.JobID;

        //assert(ExportContext.GetMesh(jobInfo.MeshInstanceIndex).MeshAssetData->HasVertexChannel(cross::VertexChannel::TexCoord1));

        SDFShadowData.MeshID = SceneInfo.MeshGuids[jobInfo.MeshInstanceIndex];
        SDFShadowData.Size.x = bPadMappings ? jobInfo.Width - 2 : jobInfo.Width;
        SDFShadowData.Size.y = bPadMappings ? jobInfo.Height - 2 : jobInfo.Height;
        SDFShadowData.UpsampleFactor = 3;    // panlele: to be modified
        SDFShadowData.LightmapUVIndex = 1;   // panlele: to be modified
        SDFShadowData.Flags = GPUBaking::LIGHTMAP_FLAGS_INDIRECT;
        SDFShadowData.Flags |= GPUBaking::LIGHTMAP_FLAGS_DIRECT;
        SDFShadowData.Flags |= GPUBaking::LIGHTMAP_FLAGS_BILINEAR_FILTER;
        SDFShadowData.Flags |= bPadMappings ? GPUBaking::LIGHTMAP_FLAGS_PADDING : 0;

        for (auto lightIdx = 0; lightIdx < SceneInfo.LightFlags.Num(); lightIdx++)
        {
            if (SceneInfo.LightFlags[lightIdx] & LIGHT_FLAG_DISTANCE_FIELD_SHADOW)
            {
                SDFShadowData.Lights.Add(SceneInfo.LightGuids[lightIdx]);
            }
        }
        Assert(SDFShadowData.Lights.Num() <= 4);

        jobIndex++;
    }

    JobInputs.PrecomputedVisibilityParameter = FPrecomputedVisibilityParameters();
    JobInputs.PrecomputedVisibilityParameter.CellSize = LightingBakeConfig::GetInstance().GetTargetDetailCellSize();

    bool UseVolumetricLightmap = false;
    if (ExportContext.HasLightProbeJob(UseVolumetricLightmap))
    {
        if (!UseVolumetricLightmap)
        {
            ExportLightProbeBricks();
        }
        else
        {
            ExportVolumetricLightmapSettingsAndJob(JobInputs);
        }
    }
    return true;
}

NS_GPUBAKING_END
