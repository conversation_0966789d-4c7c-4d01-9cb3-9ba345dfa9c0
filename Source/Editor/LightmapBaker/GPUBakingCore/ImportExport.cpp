#pragma once

#include <assert.h>
#include "ImportExport.h"

#include "TLBSSwarmInterface.h"

#include "Log.h"

#include "imageio.h"

using namespace NSwarm;

NS_GPUBAKING_BEGIN


// NSwarm::TChannelFlags LM_SCENE_CHANNEL_WRITE_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_WRITE | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
// NSwarm::TChannelFlags LM_SCENE_CHANNEL_READ_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_JOB_CHANNEL_READ | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
// 
// NSwarm::TChannelFlags LM_CHANNEL_WRITE_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_CHANNEL_WRITE | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);
// NSwarm::TChannelFlags LM_CHANNEL_READ_FLAGS = (NSwarm::TChannelFlags)(NSwarm::SWARM_CHANNEL_READ | NSwarm::SWARM_CHANNEL_MISC_ENABLE_COMPRESSION);


/** Returns Char value of Nibble */
inline BAKING_TCHAR NibbleToTChar(uint8 Num)
{
	if (Num > 9)
	{
		return BAKING_TEXT('A') + BAKING_TCHAR(Num - 10);
	}
	return BAKING_TEXT('0') + BAKING_TCHAR(Num);
}

/**
 * Convert a byte to hex
 * @param In byte value to convert
 * @param Result out hex value output
 */
inline void ByteToHex(uint8 In, std::wstring& Result)
{
	Result += NibbleToTChar(In >> 4);
	Result += NibbleToTChar(In & 15);
}

/**
 * Convert an array of bytes to hex
 * @param In byte array values to convert
 * @param Count number of bytes to convert
 * @return Hex value in string.
 */
std::wstring BytesToHex(const uint8* In, int32 Count)
{
	std::wstring Result;
	Result.reserve(Count * 2 + 2);

	while (Count)
	{
		ByteToHex(*In++, Result);
		Count--;
	}
	return Result;
}

std::wstring CreateChannelName(const FSHAHashInfo& Hash, const int32 Version, const std::wstring& Extension)
{
	BAKING_TCHAR buf[1024];
	memset(buf, 0, 1024);
	swprintf(buf, 1024, BAKING_TEXT("v%d.%s.%s"), Version, BytesToHex(Hash.Hash, sizeof(Hash)).c_str(), Extension.c_str());
	return buf;
}

void ProcessExternalResource(class NSwarm::FTLBSSwarmInterface* Swarm, FSceneInfo& SceneInfo);

void FSwarmImportExportContext::Write(char* Data, size_t Len) {
	auto NumWriteBytes = Swarm->WriteChannel(Channel, Data, (int32)Len);
	if (NumWriteBytes != Len) {
		assert(false);
	}
}

void FSwarmImportExportContext::Read(char* Data, size_t Len) {
	auto NumReadBytes = Swarm->ReadChannel(Channel, Data, (int32)Len);
	if (NumReadBytes != Len) {
		assert(false);
	}
}

void FileImportExportContext::Write(char* Data, size_t Len) {
	auto NumWriteBytes = fwrite(Data, 1, Len, fp);
	if (NumWriteBytes != Len) {
		assert(false);
	}
}

void FileImportExportContext::Read(char* Data, size_t Len) {
	auto NumReadBytes = fread(Data, 1, Len, fp);
	if (NumReadBytes != Len) {
		assert(false);
	}
}

bool IsObjectCached(class NSwarm::FTLBSSwarmInterface* Swarm, const std::wstring ChannelName) {
	int32 ErrorCode = Swarm->TestChannel(ChannelName.c_str());
	return ErrorCode >= 0;
}

bool IsObjectCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& Guid, int32 FileVersion, std::wstring Extension) {
	return IsObjectCached(Swarm,CreateChannelName(Guid, FileVersion, Extension));
}

bool IsObjectCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& Guid,const int32 LODIndex, int32 FileVersion, std::wstring Extension) {
	return IsObjectCached(Swarm, CreateChannelName(Guid, LODIndex, FileVersion, Extension));
}

bool IsObjectCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FSHAHashInfo& Hash, int32 FileVersion, std::wstring Extension) {
	return IsObjectCached(Swarm, CreateChannelName(Hash, FileVersion, Extension));
}

template<class T,bool bCached = true>
bool SerializeObject(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& Guid, T& Object, int32 FileVersion, std::wstring Extension, bool bSaving) {
	return SerializeObject<T,bCached>(Swarm,CreateChannelName(Guid, FileVersion, Extension), Object, bSaving);
}

template<class T, bool bCached = true>
bool SerializeObject(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& Guid,int32 LODIndex, T& Object, int32 FileVersion, std::wstring Extension, bool bSaving) {
	return SerializeObject<T, bCached>(Swarm, CreateChannelName(Guid, LODIndex,FileVersion, Extension), Object, bSaving);
}

template<class T, bool bCached = true>
bool SerializeObject(class NSwarm::FTLBSSwarmInterface* Swarm, const FSHAHashInfo& Hash, T& Object, int32 FileVersion, std::wstring Extension, bool bSaving) {
	return SerializeObject<T,bCached>(Swarm, CreateChannelName(Hash, FileVersion, Extension), Object, bSaving);
}

bool SerializeScene(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& SceneGuid, FSceneInfo& Scene, bool bSaving) {
	if (!SerializeObject<FSceneInfo,false>(Swarm, SceneGuid, Scene, LM_SCENE_VERSION, LM_SCENE_EXTENSION, bSaving)) {
		return false;
	}
	if (!bSaving) {
		ProcessExternalResource(Swarm, Scene);
	}
	return true;
}

bool SerializeJobs(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& SceneGuid, FBakingJobInputs& Jobs, bool bSaving) {
	if (!SerializeObject<FBakingJobInputs,false>(Swarm, SceneGuid, Jobs, LM_JOB_VERSION, LM_JOB_EXTENSION, bSaving)) {
		return false;
	}
	if (!bSaving) {
		LOG_INFO("Lightmap2D(%d),SDFShadow(%d),ShadowDepthMap(%d),MappingGroup(%d),VolumetricLightmap(%d),LightProbeJobs(%d)",
			Jobs.Lightmap2DJobs.NumElements,
			Jobs.SDFShadowJobs.NumElements,
			Jobs.ShadowDepthMapJobs.NumElements,
			Jobs.MappingGroupJobs.NumElements,
			Jobs.VolumetricLightmapTaskGuids.NumElements,
			Jobs.LightProbeJobs.NumElements
		);
	}

	return true;
}

bool IsMeshCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& MeshGuid, int32 LODIndex) {
	return IsObjectCached(Swarm, MeshGuid, LODIndex, LM_MESH_VERSION, LM_MESH_EXTENSION);
}

bool SerializeMesh(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& MeshGuid,FMeshInfo& MeshInfo, bool bSaving) {
	return SerializeObject(Swarm, MeshGuid, MeshInfo.LODIndex, MeshInfo, LM_MESH_VERSION, LM_MESH_EXTENSION, bSaving);
}

bool IsMaterialCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FSHAHashInfo& MaterialGuid) {
	return IsObjectCached(Swarm, MaterialGuid, LM_MATERIAL_VERSION, LM_MATERIAL_EXTENSION);
}

bool SerializeMaterial(class NSwarm::FTLBSSwarmInterface* Swarm, const FSHAHashInfo& MaterialGuid, FMaterialInfo& MatInfo, bool bSaving) {
	return SerializeObject(Swarm, MaterialGuid, MatInfo, LM_MATERIAL_VERSION, LM_MATERIAL_EXTENSION, bSaving);
}

bool SerializeTexture(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& TextureGuid, FTexture2DInfo& TextureInfo, bool bSaving) {
	return SerializeObject(Swarm, TextureGuid, TextureInfo, LM_TEXTURE_VERSION, LM_TEXTURE_EXTENSION, bSaving);
}

bool IsLandscapeCached(class NSwarm::FTLBSSwarmInterface* Swarm, const FGuid& LandscapeGuid) {
	return IsObjectCached(Swarm, LandscapeGuid, LM_LANDSCAPE_VERSION, LM_LANDSCAPE_EXTENSION);
}

bool SerializeLandscape(class NSwarm::FTLBSSwarmInterface* Swarm, FLandscapeInfo& Landscape, bool bSaving,bool bCached) {
	FGuid Guid = ToGuid(Landscape.Guid);
	if (bCached) {
		return SerializeObject(Swarm, Guid, Landscape, LM_LANDSCAPE_VERSION, LM_LANDSCAPE_EXTENSION, bSaving);
	}
	return SerializeObject<FLandscapeInfo,false>(Swarm, Guid, Landscape, LM_LANDSCAPE_VERSION, LM_LANDSCAPE_EXTENSION, bSaving);
}

void ProcessExternalResource(class NSwarm::FTLBSSwarmInterface* Swarm, FSceneInfo& SceneInfo) {
#if IS_PROGRAM
	for (uint32 i = 0; i < SceneInfo.Meshes.NumElements; ++i) {
		FMeshInfo& MeshInfo = SceneInfo.Meshes[i];
		if (MeshInfo.VertexBuffer.Num() <= 0) {
			SerializeMesh(Swarm, ToGuid(MeshInfo.Guid), MeshInfo, false);
		}
	}

	for (uint32 i = 0; i < SceneInfo.Materials.NumElements; ++i) {
		FMaterialInfo& MaterialInfo = SceneInfo.Materials[i];
		if (MaterialInfo.ShadingModelID < 0) {
			SerializeMaterial(Swarm, MaterialInfo.Hash, MaterialInfo, false);
		}
	}

	for (uint32 i = 0; i < SceneInfo.Landscapes.NumElements; ++i) {
		FLandscapeInfo& LandscapeInfo = SceneInfo.Landscapes[i];
		if (LandscapeInfo.HeightMap.Num() <= 0) {
			SerializeLandscape(Swarm, LandscapeInfo, false);
		}

		FMeshInfo& MeshInfo = SceneInfo.Meshes.AddDefaultRef();
		FMeshInstanceInfo& MeshInstanceInfo = SceneInfo.MesheInstances.AddDefaultRef();

		FLandscape Landscape(LandscapeInfo);
		Landscape.CreateMesh(MeshInfo);
		Landscape.CreateMeshInstance(MeshInstanceInfo);

		MeshInstanceInfo.MeshIndex = SceneInfo.Meshes.Num() - 1;

		SceneInfo.MeshGuids.Add(MeshInstanceInfo.Guid);
	}
#endif
}

#if IS_PROGRAM
bool SerializeScene(std::string Path, FSceneInfo& Scene, bool bSaving) {
	if (bSaving) {
		return DummyObjectToFile(Scene, Path);
	}
	return ImportObjectFromFile(Scene, Path);
}

bool SerializeJobs(std::string Path, FBakingJobInputs& Jobs, bool bSaving) {
	if (bSaving) {
		return DummyObjectToFile(Jobs, Path);
	}
	return ImportObjectFromFile(Jobs, Path);
}
#endif


template <typename T>
FORCEINLINE constexpr T Align(T Val, uint64 Alignment)
{
	//static_assert(TIsIntegral<T>::Value || TIsPointer<T>::Value, "Align expects an integer or pointer type");
	return (T)(((uint64)Val + Alignment - 1) & ~(Alignment - 1));
}

void WriteBitmap(const char* BitmapBaseName, std::vector<FColor> &Samples, int32 Width, int32 Height, const std::string &rootPath)
{
	// Alignment.
#ifndef GCC_PACK_BAKING
#define GCC_PACK_BAKING(n)
#endif

#pragma pack (push,1)
	struct BITMAPFILEHEADER
	{
		uint16	bfType GCC_PACK_BAKING(1);
		uint32	bfSize GCC_PACK_BAKING(1);
		uint16	bfReserved1 GCC_PACK_BAKING(1);
		uint16	bfReserved2 GCC_PACK_BAKING(1);
		uint32	bfOffBits GCC_PACK_BAKING(1);
	} FH;
	struct BITMAPINFOHEADER
	{
		uint32	biSize GCC_PACK_BAKING(1);
		int32		biWidth GCC_PACK_BAKING(1);
		int32		biHeight GCC_PACK_BAKING(1);
		uint16	biPlanes GCC_PACK_BAKING(1);
		uint16	biBitCount GCC_PACK_BAKING(1);
		uint32	biCompression GCC_PACK_BAKING(1);
		uint32	biSizeImage GCC_PACK_BAKING(1);
		int32		biXPelsPerMeter GCC_PACK_BAKING(1);
		int32		biYPelsPerMeter GCC_PACK_BAKING(1);
		uint32	biClrUsed GCC_PACK_BAKING(1);
		uint32	biClrImportant GCC_PACK_BAKING(1);
	} IH;
#pragma pack (pop)

#undef GCC_PACK_BAKING

	int32 BytesPerLine = Align(Width * 3, 4);

	std::string debugBitmapPath = rootPath;
	std::string filename = debugBitmapPath + BitmapBaseName + ".bmp";
	FILE* file = fopen(filename.c_str(), "wb");

	// File header.
	FH.bfType = (uint16)('B' + 256 * 'M');
	FH.bfSize = (uint32)(sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + BytesPerLine * Height);
	FH.bfReserved1 = (uint16)0;
	FH.bfReserved2 = (uint16)0;
	FH.bfOffBits = (uint32)(sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER));
	fwrite(&FH, sizeof(FH), 1, file);

	// Info header.
	IH.biSize = (uint32) sizeof(BITMAPINFOHEADER);
	IH.biWidth = (uint32)Width;
	IH.biHeight = (uint32)Height;
	IH.biPlanes = (uint16)1;
	IH.biBitCount = (uint16)24;
	IH.biCompression = (uint32)0; //BI_RGB
	IH.biSizeImage = (uint32)BytesPerLine * Height;
	IH.biXPelsPerMeter = (uint32)0;
	IH.biYPelsPerMeter = (uint32)0;
	IH.biClrUsed = (uint32)0;
	IH.biClrImportant = (uint32)0;
	fwrite(&IH, sizeof(IH), 1, file);

	// write out the image, bottom up
	for (int32 Y = Height - 1; Y >= 0; Y--)
	{
		for (int32 X = 0; X < Width; X++)
		{
			FColor Color = Samples[Y * Width + X];
			if (Color.R > 50 || Color.G > 50 || Color.B > 50)
			{
				int a = 0;
				a += 1;
			}
			fwrite(&Color.B, 1, 1, file);
			fwrite(&Color.G, 1, 1, file);
			fwrite(&Color.R, 1, 1, file);
		}

		// pad if necessary
		static uint8 Zero[3] = { 0, 0, 0 };
		if (Width * 3 < BytesPerLine)
		{
			fwrite(&Zero, BytesPerLine - Width * 3, 1, file);
		}
	}

	fflush(file);
	fclose(file);
}

void WritePNG(const std::string &filename, const std::vector<FColor> &Samples, int Width, int Height)
{
    imageio::image img(Width, Height);
    for (auto y = 0; y < Height; ++y)
    {
        for (auto x = 0; x < Width; ++x)
        {
            auto idx = y * Width + x;
            auto& sample = Samples[idx];
            auto& color = img(x, y);
            color.r = sample.R;
            color.g = sample.G;
            color.b = sample.B;
            color.a = sample.A;
        }
    }

    imageio::save_png(filename.c_str(), img);
}

NS_GPUBAKING_END
