#pragma once

#include "LightMapEncoding.h"
//#include "VolumetricLightMapEncoding.h"

#include "Importer/LightMap.h"
#include "Importer/ShadowMap.h"

NS_GPUBAKING_BEGIN

class FJobResultHelper {
public:
    static void ConvertLocalLightMapOutput(const FLocalTranferLightMapOutput& LocalTransferLightMapOutput, FLocalQuantizedTransferLightmapDataArray* LocalQuantizedTransferData);

    static void ConvertLightmapOutput(const FLightmap2DOutput& LightMapOutput, FQuantizedLightmapData* QuantizedData, FQuantizedTransferLightmapData* QuantizedTransferData, bool bHasSkyShadowing);

    static void ConvertShadowOutput(const FSDFShadowMap& ShadowMapOutput, FQuantizedShadowSignedDistanceFieldData2D* QuantizedData);

    static void EmptyQuantizedLightmapData(FQuantizedLightmapData* QuantizedData, uint32 Width, uint32 Height);

private:
    static void ConvertLightmapOutput(const FQuantizedLightmap2DOutput& LightMapOutput, FQuantizedLightmapData* QuantizedData, bool bHasSkyShadowing);

    static void ConvertLightmapOutput(const FQuantizedTransferLightmap2DOutput& LightMapOutput, FQuantizedTransferLightmapData* QuantizedData);

    static void ConvertShadowOutput(const FQuantizedSDFShadowMap& ShadowMapOutput, FQuantizedShadowSignedDistanceFieldData2D* QuantizedData);
};

NS_GPUBAKING_END

