#include "VolumetricLightMapEncoding.h"

// support UE Version elder than 4.24
#if !defined(UE_ARRAY_COUNT)
#    define UE_ARRAY_COUNT ARRAY_COUNT
#endif

#define DEBUG_LIGHT_COLOR FLinearColor(10.0f, 10.0f, 10.0f, 10.0f)

NS_GPUBAKING_BEGIN

// For debugging
bool bStitchDetailBricksWithLowDensityNeighborsTLBS = true;
bool bCopyPaddingFromUniqueDataTLBS = true;
bool bCopyVolumeBorderFromInteriorTLBS = true;

static void SetFromVolumeLightingSample(FImportedVolumetricLightmapBrick& BrickData, int32 Index, const FVolumeLightingSample& Sample, bool bInsideGeometry, float MinDistanceToSurface, bool bBorderVoxel)
{
    constexpr uint8 MAX_uint8 = 0xff;
    BrickData.AmbientVector[Index] = FFloat3Packed(FLinearColor(Sample.HighQualityCoefficients[0][0], Sample.HighQualityCoefficients[0][1], Sample.HighQualityCoefficients[0][2], 0.0f));

    // Note: encoding behavior has to match CPU decoding in InterpolateVolumetricLightmap and GPU decoding in GetVolumetricLightmapSH3

    FLinearColor CoefficientNormalizationScale0(0.282095f / 0.488603f, 0.282095f / 0.488603f, 0.282095f / 0.488603f, 0.282095f / 1.092548f);

    FLinearColor CoefficientNormalizationScale1(0.282095f / 1.092548f, 0.282095f / (4.0f * 0.315392f), 0.282095f / 1.092548f, 0.282095f / (2.0f * 0.546274f));

    for (int32 ChannelIndex = 0; ChannelIndex < 3; ChannelIndex++)
    {
        const float InvAmbient = 1.0f / std::max<float>(Sample.HighQualityCoefficients[0][ChannelIndex], .0001f);

        const FLinearColor Vector0Normalized =
            FLinearColor(Sample.HighQualityCoefficients[1][ChannelIndex], Sample.HighQualityCoefficients[2][ChannelIndex], Sample.HighQualityCoefficients[3][ChannelIndex], Sample.HighQualityCoefficients[4][ChannelIndex]) *
            CoefficientNormalizationScale0 * FLinearColor(InvAmbient, InvAmbient, InvAmbient, InvAmbient);

        BrickData.SHCoefficients[ChannelIndex * 2 + 0][Index] = (Vector0Normalized * FLinearColor(.5f, .5f, .5f, .5f) + FLinearColor(.5f, .5f, .5f, .5f)).QuantizeRound();

        const FLinearColor Vector1Normalized =
            FLinearColor(Sample.HighQualityCoefficients[5][ChannelIndex], Sample.HighQualityCoefficients[6][ChannelIndex], Sample.HighQualityCoefficients[7][ChannelIndex], Sample.HighQualityCoefficients[8][ChannelIndex]) *
            CoefficientNormalizationScale1 * FLinearColor(InvAmbient, InvAmbient, InvAmbient, InvAmbient);

        BrickData.SHCoefficients[ChannelIndex * 2 + 1][Index] = (Vector1Normalized * FLinearColor(.5f, .5f, .5f, .5f) + FLinearColor(.5f, .5f, .5f, .5f)).QuantizeRound();
    }

    if (BrickData.SkyBentNormal.size() > 0)
    {
        BrickData.SkyBentNormal[Index] = (FLinearColor(Sample.SkyBentNormal.x, Sample.SkyBentNormal.y, Sample.SkyBentNormal.z, Sample.SkyBentNormal.w) * FLinearColor(.5f, .5f, .5f, .5f) + FLinearColor(.5f, .5f, .5f, .5f)).QuantizeRound();
    }

    BrickData.DirectionalLightShadowing[Index] = std::clamp<uint8>(static_cast<uint8>(std::round(Sample.DirectionalLightShadowing * MAX_uint8)), 0, MAX_uint8);

    //     {
    //     FSHVectorRGB3 HQ;
    //     FSHVectorRGB3 LQ;
    //     for (int32 CoefficientIndex = 0; CoefficientIndex < LM_NUM_SH_COEFFICIENTS; CoefficientIndex++)
    //     {
    //     HQ.R.V[CoefficientIndex] = Sample.HighQualityCoefficients[CoefficientIndex][0];
    //     HQ.G.V[CoefficientIndex] = Sample.HighQualityCoefficients[CoefficientIndex][1];
    //     HQ.B.V[CoefficientIndex] = Sample.HighQualityCoefficients[CoefficientIndex][2];
    //
    //     LQ.R.V[CoefficientIndex] = Sample.LowQualityCoefficients[CoefficientIndex][0];
    //     LQ.G.V[CoefficientIndex] = Sample.LowQualityCoefficients[CoefficientIndex][1];
    //     LQ.B.V[CoefficientIndex] = Sample.LowQualityCoefficients[CoefficientIndex][2];
    //     }
    //     FSHVectorRGB3 DirectLight = LQ - HQ;
    //     FVector MaxLightDir(DirectLight.GetLuminance().GetMaximumDirection());
    //
    //     // Set the max direction
    //     FLinearColor MaxLightDirAsColor(MaxLightDir);
    //     BrickData.LQLightDirection[Index] = (MaxLightDirAsColor * FLinearColor(.5f, .5f, .5f, .5f) + FLinearColor(.5f, .5f, .5f, .5f)).QuantizeRound();
    //
    //     // Set color along the max direction
    //     FSHVector3 BrigthestDiffuseTransferSH = FSHVector3::CalcDiffuseTransfer(MaxLightDir);
    //     FLinearColor BrightestLighting = Dot(DirectLight, BrigthestDiffuseTransferSH);
    //     BrightestLighting.R = FMath::Max(BrightestLighting.R, 0.0f);
    //     BrightestLighting.G = FMath::Max(BrightestLighting.G, 0.0f);
    //     BrightestLighting.B = FMath::Max(BrightestLighting.B, 0.0f);
    //
    //     BrickData.LQLightColor[Index] = FFloat3Packed(BrightestLighting);
    //     }

    FIrradianceVoxelImportProcessingData NewImportData;
    NewImportData.bInsideGeometry = bInsideGeometry;
    NewImportData.bBorderVoxel = bBorderVoxel;
    NewImportData.ClosestGeometryDistance = MinDistanceToSurface;
    BrickData.TaskVoxelImportProcessingData[Index] = NewImportData;
}

void FVolumetricLightMapEncoder::SetFromSHVector(const TSHRGB3& SHVector, FVolumeLightingSample& LightingSample)
{
    for (int32 CoefficientIndex = 0; CoefficientIndex < LM_NUM_SH_COEFFICIENTS; CoefficientIndex++)
    {
        LightingSample.HighQualityCoefficients[CoefficientIndex][0] = SHVector.R.V[CoefficientIndex];
        LightingSample.HighQualityCoefficients[CoefficientIndex][1] = SHVector.G.V[CoefficientIndex];
        LightingSample.HighQualityCoefficients[CoefficientIndex][2] = SHVector.B.V[CoefficientIndex];

        LightingSample.LowQualityCoefficients[CoefficientIndex][0] = SHVector.R.V[CoefficientIndex];
        LightingSample.LowQualityCoefficients[CoefficientIndex][1] = SHVector.G.V[CoefficientIndex];
        LightingSample.LowQualityCoefficients[CoefficientIndex][2] = SHVector.B.V[CoefficientIndex];
    }
}

void FVolumetricLightMapEncoder::ConvertVolumetricLightmapOutput(const FAdaptiveBrickData& JobOutput, FImportedVolumetricLightmapBrick& BrickData, int32 BrickSize)
{
    BrickData.IntersectingLevelGuid = ToGuid(JobOutput.IntersectingLevelGuid);
    BrickData.AverageClosestGeometryDistance = JobOutput.AverageClosestGeometryDistance;
    BrickData.TreeDepth = JobOutput.TreeDepth;
    BrickData.IndirectionTexturePosition = JobOutput.IndirectionTexturePosition;

    const int32 TotalBrickSize = BrickSize * BrickSize * BrickSize;

    Assert(TotalBrickSize == static_cast<int32>(JobOutput.Cells.NumElements));

    BrickData.AmbientVector.resize(TotalBrickSize);

    BrickData.LQLightColor.resize(TotalBrickSize);
    BrickData.LQLightDirection.resize(TotalBrickSize);

    BrickData.TaskVoxelImportProcessingData.resize(TotalBrickSize);

    bool bGenerateSkyShadowing = true;

    if (bGenerateSkyShadowing)
    {
        BrickData.SkyBentNormal.resize(TotalBrickSize);
    }

    BrickData.DirectionalLightShadowing.resize(TotalBrickSize);

    for (int32 i = 0; i < SHCoefficientsNum; i++)
    {
        BrickData.SHCoefficients[i].resize(TotalBrickSize);
    }

    if (JobOutput.Cells.Num() == TotalBrickSize)
    {
        for (int32 Z = 0; Z < BrickSize; Z++)
        {
            for (int32 Y = 0; Y < BrickSize; Y++)
            {
                for (int32 X = 0; X < BrickSize; X++)
                {
                    const int32 CellIndex = Z * BrickSize * BrickSize + Y * BrickSize + X;

                    auto& CellInfo = JobOutput.Cells[CellIndex];
                    // Use a radius to avoid shadowing from geometry contained in the cell
                    FVolumeLightingSample CurrentSample;
                    CurrentSample.PositionAndRadius = cross::Float4A(CellInfo.PostionAndRadius.x, CellInfo.PostionAndRadius.y, CellInfo.PostionAndRadius.z, CellInfo.PostionAndRadius.w);
                    {
                        SetFromSHVector(CellInfo.SampleValue, CurrentSample);
                    }

                    CurrentSample.SkyBentNormal = cross::Float4A(CellInfo.SkyOcclusion.x, CellInfo.SkyOcclusion.y, CellInfo.SkyOcclusion.z, 0);
                    CurrentSample.DirectionalLightShadowing = CellInfo.DirectionalLightShadowing;

                    SetFromVolumeLightingSample(BrickData, CellIndex, CurrentSample, CellInfo.Flags & LIGHTPROBE_FLAGS_INSIDE_GEOMETRY, CellInfo.MinDistanceToSurface, CellInfo.Flags & LIGHTPROBE_FLAGS_IN_BORDER_VOXEL);
                }
            }
        }
    }
}

int32 ComputeLinearVoxelIndexTLBS(const int3& VoxelCoordinate, const int3& VolumeDimensions)
{
    return (VoxelCoordinate.z * VolumeDimensions.y + VoxelCoordinate.y) * VolumeDimensions.x + VoxelCoordinate.x;
}

int32 TrimBricksByInterpolationError(std::vector<std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>>& BricksByDepth, const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings)
{
    int32 NumBricksRemoved = 0;

    if (VolumetricLightmapSettings.MaxRefinementLevels > 1)
    {
        std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& HighestDensityBricks = BricksByDepth[VolumetricLightmapSettings.MaxRefinementLevels - 1];
        const int32 ParentLevel = VolumetricLightmapSettings.MaxRefinementLevels - 2;
        std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& ParentLevelBricks = BricksByDepth[ParentLevel];

        const int32 BrickSize = VolumetricLightmapSettings.BrickSize;
        const float InvTotalBrickSize = 1.0f / (BrickSize * BrickSize * BrickSize);
        // const int32 BrickSizeLog2 = FMath::FloorLog2(BrickSize);
        Assert(BrickSize == 4);
        const int32 DetailCellsPerParentLevelBrick = 1 << (VolumetricLightmapSettings.MaxRefinementLevels - ParentLevel) * BrickSize;
        const int32 NumParentBottomLevelBricks = DetailCellsPerParentLevelBrick / BrickSize;

        for (int32 BrickIndex = 0; BrickIndex < HighestDensityBricks.size(); BrickIndex++)
        {
            const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *HighestDensityBricks[BrickIndex];
            const GPUBaking::FImportedVolumetricLightmapBrick* ParentBrick = nullptr;

            for (int32 ParentBrickIndex = 0; ParentBrickIndex < ParentLevelBricks.size(); ParentBrickIndex++)
            {
                const GPUBaking::FImportedVolumetricLightmapBrick& ParentLevelBrick = *ParentLevelBricks[ParentBrickIndex];

                if (Brick.IndirectionTexturePosition.x >= ParentLevelBrick.IndirectionTexturePosition.x && Brick.IndirectionTexturePosition.y >= ParentLevelBrick.IndirectionTexturePosition.y &&
                    Brick.IndirectionTexturePosition.z >= ParentLevelBrick.IndirectionTexturePosition.z && Brick.IndirectionTexturePosition.x < ParentLevelBrick.IndirectionTexturePosition.x + NumParentBottomLevelBricks &&
                    Brick.IndirectionTexturePosition.y < ParentLevelBrick.IndirectionTexturePosition.y + NumParentBottomLevelBricks &&
                    Brick.IndirectionTexturePosition.z < ParentLevelBrick.IndirectionTexturePosition.z + NumParentBottomLevelBricks)
                {
                    ParentBrick = &ParentLevelBrick;
                    break;
                }
            }

            Assert(ParentBrick);
            if (ParentBrick == nullptr)
            {
                continue;
            }

            FLinearColor ErrorSquared(0, 0, 0);
            const cross::Float3 ChildOffset = {1.0f * Brick.IndirectionTexturePosition.x - ParentBrick->IndirectionTexturePosition.x,
                                               1.0f * Brick.IndirectionTexturePosition.y - ParentBrick->IndirectionTexturePosition.y,
                                               1.0f * Brick.IndirectionTexturePosition.z - ParentBrick->IndirectionTexturePosition.z};

            for (int32 Z = 0; Z < BrickSize; Z++)
            {
                for (int32 Y = 0; Y < BrickSize; Y++)
                {
                    for (int32 X = 0; X < BrickSize; X++)
                    {
                        const int3 VoxelCoordinate(X, Y, Z);
                        const int32 LinearVoxelIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate, int3(BrickSize, BrickSize, BrickSize));
                        const FLinearColor AmbientVector = Brick.AmbientVector[LinearVoxelIndex].ToLinearColor();

                        const float3 ParentCoordinate = VoxelCoordinate / static_cast<float>(BrickSize) + ChildOffset;
                        const FLinearColor ParentAmbientVector = FilteredVolumeLookup(ParentCoordinate, int3(BrickSize, BrickSize, BrickSize), ParentBrick->AmbientVector.data());
                        ErrorSquared += (AmbientVector - ParentAmbientVector) * (AmbientVector - ParentAmbientVector);
                    }
                }
            }

            const float RMSE = std::sqrt((ErrorSquared * InvTotalBrickSize).GetMax());
            const bool bCullBrick = RMSE < VolumetricLightmapSettings.MinBrickError;

            if (bCullBrick)
            {
                HighestDensityBricks.erase(HighestDensityBricks.begin() + BrickIndex);
                BrickIndex--;
                NumBricksRemoved++;
            }
        }
    }

    return NumBricksRemoved;
}

inline int3 ComputeBrickLayoutPosition(int32 BrickLayoutAllocation, int3 BrickLayoutDimensions)
{
    const int3 BrickPosition(BrickLayoutAllocation % BrickLayoutDimensions.x, (BrickLayoutAllocation / BrickLayoutDimensions.x) % BrickLayoutDimensions.y, BrickLayoutAllocation / (BrickLayoutDimensions.x * BrickLayoutDimensions.y));

    return BrickPosition;
}

bool CopyFromBrickmapTexel(float3 IndirectionDataSourceCoordinate, int3 LocalCellDestCoordinate, int32 MinDestinationNumBottomLevelBricks, int32 BrickSize, int3 BrickLayoutPosition,
                           const FPrecomputedVolumetricLightmapData& CurrentLevelData, FVolumetricLightmapBrickData& BrickData, const bool bSwitched = false)
{
    const float3 IndirectionCoordMax(CurrentLevelData.IndirectionTextureDimensions * (1 - GPointFilteringThreshold));

    if (IndirectionDataSourceCoordinate.x < 0 || IndirectionDataSourceCoordinate.y < 0 || IndirectionDataSourceCoordinate.z < 0 || IndirectionDataSourceCoordinate.x > IndirectionCoordMax.x ||
        IndirectionDataSourceCoordinate.y > IndirectionCoordMax.y || IndirectionDataSourceCoordinate.z > IndirectionCoordMax.z)
    {
        return false;
    }

    int3 IndirectionBrickOffset;
    int32 IndirectionBrickSize;

    Assert(GetPixelByteSize(CurrentLevelData.IndirectionTexture.Format) == sizeof(uint8) * 4);
    SampleIndirectionTexture(IndirectionDataSourceCoordinate, CurrentLevelData.IndirectionTextureDimensions, CurrentLevelData.IndirectionTexture.Data.data(), IndirectionBrickOffset, IndirectionBrickSize);

    if (IndirectionBrickSize > MinDestinationNumBottomLevelBricks)
    {
        const float3 BrickTextureCoordinate = ComputeBrickTextureCoordinate(IndirectionDataSourceCoordinate, IndirectionBrickOffset, IndirectionBrickSize, BrickSize);

        const int3 DestCellPosition = BrickLayoutPosition + LocalCellDestCoordinate;
        const int32 LinearDestCellIndex = ComputeLinearVoxelIndexTLBS(DestCellPosition, CurrentLevelData.BrickDataDimensions);

        *reinterpret_cast<FFloat3Packed*>(&BrickData.AmbientVector.Data[LinearDestCellIndex * sizeof(FFloat3Packed)]) =
            FilteredVolumeLookupReconverted<FFloat3Packed>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const FFloat3Packed*>(BrickData.AmbientVector.Data.data()));

        for (int32 i = 0; i < SHCoefficientsNum; i++)
        {
            *reinterpret_cast<FColor*>(&BrickData.SHCoefficients[i].Data[LinearDestCellIndex * sizeof(FColor)]) =
                FilteredVolumeLookupReconverted<FColor>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const FColor*>(BrickData.SHCoefficients[i].Data.data()));
        }

        *reinterpret_cast<FFloat3Packed*>(&BrickData.LQLightColor.Data[LinearDestCellIndex * sizeof(FFloat3Packed)]) =
            FilteredVolumeLookupReconverted<FFloat3Packed>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const FFloat3Packed*>(BrickData.LQLightColor.Data.data()));
        *reinterpret_cast<FColor*>(&BrickData.LQLightDirection.Data[LinearDestCellIndex * sizeof(FColor)]) =
            FilteredVolumeLookupReconverted<FColor>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const FColor*>(BrickData.LQLightDirection.Data.data()));

        if (BrickData.SkyBentNormal.Data.size() > 0)
        {
            *reinterpret_cast<FColor*>(&BrickData.SkyBentNormal.Data[LinearDestCellIndex * sizeof(FColor)]) =
                FilteredVolumeLookupReconverted<FColor>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const FColor*>(BrickData.SkyBentNormal.Data.data()));
        }

        *reinterpret_cast<uint8*>(&BrickData.DirectionalLightShadowing.Data[LinearDestCellIndex * sizeof(uint8)]) =
            FilteredVolumeLookupReconverted<uint8>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, reinterpret_cast<const uint8*>(BrickData.DirectionalLightShadowing.Data.data()));

        return true;
    }

    return false;
}

void BuildIndirectionTexture(const std::vector<std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>>& BricksByDepth, const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings,
                             int32 MaxBricksInLayoutOneDim, int3 BrickLayoutDimensions, int32 IndirectionTextureDataStride, FPrecomputedVolumetricLightmapData& CurrentLevelData, bool bOnlyBuildForPersistentLevel)
{
    const int32 BrickSizeLog2 = FloorLog2(VolumetricLightmapSettings.BrickSize);

    int32 BrickAllocation = 0;

    for (int32 CurrentDepth = 0; CurrentDepth < VolumetricLightmapSettings.MaxRefinementLevels; CurrentDepth++)
    {
        const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth = BricksByDepth[CurrentDepth];

        for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
        {
            const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];

            if (CurrentDepth == VolumetricLightmapSettings.MaxRefinementLevels - 1)
            {
                const NSwarm::FGuid PersistentLevelGuid = NSwarm::FGuid(0, 0, 0, 0);

                // Skip non-intersecting, bottom detailed bricks for persistent level
                if (bOnlyBuildForPersistentLevel && Brick.IntersectingLevelGuid != PersistentLevelGuid)
                {
                    continue;
                }
            }

            const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickAllocation, BrickLayoutDimensions);//BrickLayout的位置,xyz分别代表第几个brick
            Assert(BrickLayoutPosition.x < MaxBricksInLayoutOneDim && BrickLayoutPosition.y < MaxBricksInLayoutOneDim && BrickLayoutPosition.z < MaxBricksInLayoutOneDim);
            Assert(IndirectionTextureDataStride == sizeof(uint8) * 4);

            const int32 DetailCellsPerCurrentLevelBrick = 1 << ((VolumetricLightmapSettings.MaxRefinementLevels - Brick.TreeDepth) * BrickSizeLog2);
            const int32 NumBottomLevelBricks = DetailCellsPerCurrentLevelBrick / VolumetricLightmapSettings.BrickSize;//在当前深度的一个brick数量 / 最细一层的brick数量
            Assert(NumBottomLevelBricks < MaxBricksInLayoutOneDim);

            for (int32 Z = 0; Z < NumBottomLevelBricks; Z++)
            {
                for (int32 Y = 0; Y < NumBottomLevelBricks; Y++)
                {
                    for (int32 X = 0; X < NumBottomLevelBricks; X++)
                    {
                        const int3 IndirectionDestDataCoordinate = Brick.IndirectionTexturePosition + int3(X, Y, Z);
                        const int32 IndirectionDestDataIndex =
                            ((IndirectionDestDataCoordinate.z * CurrentLevelData.IndirectionTextureDimensions.y) + IndirectionDestDataCoordinate.y) * CurrentLevelData.IndirectionTextureDimensions.x + IndirectionDestDataCoordinate.x;
                        uint8* IndirectionVoxelPtr = &CurrentLevelData.IndirectionTexture.Data[IndirectionDestDataIndex * IndirectionTextureDataStride];
                        *(IndirectionVoxelPtr + 0) = static_cast<uint8>(BrickLayoutPosition.x);
                        *(IndirectionVoxelPtr + 1) = static_cast<uint8>(BrickLayoutPosition.y);
                        *(IndirectionVoxelPtr + 2) = static_cast<uint8>(BrickLayoutPosition.z);
                        *(IndirectionVoxelPtr + 3) = static_cast<uint8>(NumBottomLevelBricks);
                    }
                }
            }
            BrickAllocation++;
        }
    }
}

void CopyBrickToAtlasVolumeTextureTLBS(int32 FormatSize, int3 AtlasSize, int3 BrickMin, int3 BrickSize, const uint8* SourceData, uint8* DestData, bool BGRAFormat)
{
    const int32 SourcePitch = BrickSize.x * FormatSize;
    const int32 Pitch = AtlasSize.x * FormatSize;
    const int32 DepthPitch = AtlasSize.x * AtlasSize.y * FormatSize;

    Assert(!BGRAFormat || FormatSize == 4);

    // Copy each row into the correct position in the global volume texture
    for (int32 ZIndex = 0; ZIndex < BrickSize.z; ZIndex++)
    {
        const int32 DestZIndex = (BrickMin.z + ZIndex) * DepthPitch + BrickMin.x * FormatSize;
        const int32 SourceZIndex = ZIndex * BrickSize.y * SourcePitch;

        for (int32 YIndex = 0; YIndex < BrickSize.y; YIndex++)
        {
            const int32 DestIndex = DestZIndex + (BrickMin.y + YIndex) * Pitch;
            const int32 SourceIndex = SourceZIndex + YIndex * SourcePitch;
            memcpy(&DestData[DestIndex], &SourceData[SourceIndex], SourcePitch);
            if (BGRAFormat)
            {
                for (int32 x = 0; x < SourcePitch; x += FormatSize)
                {
                    //swap R & B
                    std::swap(DestData[DestIndex + x], DestData[DestIndex + x + 2]);
                }
            }
        }
    }
}

void FVolumetricLightMapEncoder::WriteVolumetricLightmap(const FVolumetricLightmapDataLayer& TextureData, const int3& AtlasSize, const std::string& filename)
{
    constexpr bool DEBUG_OUTPUT_PNG = false;
    Assert(TextureData.Format == TextureFormat::R11G11B10Float || TextureData.Format == TextureFormat::RGBA32);
    std::vector<imageio::image> outImages;
    outImages.reserve(AtlasSize.z);

    auto DataStride = GetPixelByteSize(TextureData.Format);
    for (auto z = 0; z < AtlasSize.z; z++)
    {
        auto DepthStride = z * AtlasSize.y * AtlasSize.x * DataStride;

        outImages.emplace_back(imageio::image(AtlasSize.x, AtlasSize.y));
        imageio::image& outImg = outImages.back();

        for (auto y = 0; y < AtlasSize.y; y++)
        {
            for (auto x = 0; x < AtlasSize.x; x++)
            {
                const uint8* colorData = TextureData.Data.data() + DepthStride + y * AtlasSize.x * DataStride + x * DataStride;
                FLinearColor srcColor;
                auto& destColor = outImg(x, y);
                if (TextureData.Format == TextureFormat::R11G11B10Float)
                {
                    const FFloat3Packed* colorPtr = reinterpret_cast<const FFloat3Packed*>(colorData);
                    memcpy(destColor.m_comps, &(colorPtr->v), std::min<UInt32>(sizeof(colorPtr->v), GetPixelByteSize(TextureFormat::R11G11B10Float)));
                }
                else
                {
                    destColor.r = *colorData;
                    destColor.g = *(colorData + 1);
                    destColor.b = *(colorData + 2);
                    destColor.a = *(colorData + 3);
                }
            }
        }

        if (DEBUG_OUTPUT_PNG)
        {
            if (TextureData.Format == TextureFormat::R11G11B10Float)
            {
                imageio::image convertImage(outImg.get_width(), outImg.get_height());
                for (UInt32 tmpIndex = 0; tmpIndex < outImg.get_total_pixels(); tmpIndex++)
                {
                    const auto& color = outImg.get_pixels()[tmpIndex];
                    const FFloat3Packed* colorPtr = reinterpret_cast<const FFloat3Packed*>(color.m_comps);
                    FLinearColor srcConvertColor = colorPtr->ToLinearColor() * 255.f;
                    auto& destConvertColor = convertImage.get_pixels()[tmpIndex];
                    destConvertColor.r = static_cast<uint8>(srcConvertColor.R);
                    destConvertColor.g = static_cast<uint8>(srcConvertColor.G);
                    destConvertColor.b = static_cast<uint8>(srcConvertColor.B);
                    destConvertColor.a = static_cast<uint8>(srcConvertColor.A);
                }
                imageio::save_png((filename + "_" + std::to_string(z) + ".png").c_str(), convertImage);
            }
            else
            {
                imageio::save_png((filename + "_" + std::to_string(z) + ".png").c_str(), outImg);
            }
        }
    }

    auto& imgSave = outImages;
    CrossSchema::TextureAssetT textureAsset;
    std::vector<CrossSchema::TextureAssetImage> imagesAsset;
    auto imageCount = static_cast<UInt32>(imgSave.size());
    UInt32 offset = 0;

    const auto& loadedImage = imgSave[0];
    UInt32 width = loadedImage.get_width();
    UInt32 height = loadedImage.get_height();
    UInt32 sizePerDepth = loadedImage.get_total_pixels() * sizeof(imageio::color_rgba);

    auto& imageAsset = imagesAsset.emplace_back();
    imageAsset.mutate_width(width);
    imageAsset.mutate_height(height);
    imageAsset.mutate_depth(imageCount);
    imageAsset.mutate_dataoffset(offset);
    imageAsset.mutate_databytesize(sizePerDepth * imageCount);
    imageAsset.mutate_rowpitch(width * sizeof(imageio::color_rgba));
    offset = offset + sizePerDepth * imageCount;

    textureAsset.data.resize(offset);

    offset = imageAsset.dataoffset();
    for (UInt32 i = 0; i < imageCount; ++i)
    {
        memcpy(textureAsset.data.data() + offset, imgSave[i].get_ptr(), sizePerDepth);
        offset += sizePerDepth;
    }
    textureAsset.images = imagesAsset;
    textureAsset.mipcount = 1;
    textureAsset.format = static_cast<CrossSchema::TextureFormat>(TextureData.Format);
    if constexpr (static_cast<CrossSchema::TextureFormat>(CrossSchema::TextureFormat::R11G11B10Float) != CrossSchema::TextureFormat::R11G11B10Float) { Assert(false); }
    textureAsset.dimension = CrossSchema::TextureDimension::Tex3D;
    textureAsset.colorspace = CrossSchema::ColorSpace::Linear;
    textureAsset.flags = 0;

    flatbuffers::FlatBufferBuilder textureBuilder(1024);
    auto mloc = CrossSchema::CreateTextureAsset(textureBuilder, &textureAsset);

    auto fullFilename = filename + ".png";
    auto ndaFullFilename = filename;
    auto classID = ClassID(Texture3D);
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, static_cast<int32>(textureAsset.data.size()), static_cast<int32>(textureAsset.data.size()));
    auto mloc2 = CrossSchema::CreateResourceAsset(textureBuilder, &header, textureBuilder.CreateString(fullFilename), CrossSchema::ResourceType::TextureAsset, mloc.Union());
    CrossSchema::FinishResourceAssetBuffer(textureBuilder, mloc2);

    cross::filesystem::FileSystem* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    auto file = fileSystem->OpenForWrite(ndaFullFilename);
    fileSystem->Save(file->GetPathName(), reinterpret_cast<char*>(textureBuilder.GetBufferPointer()), textureBuilder.GetSize());
    file->Close();
    return;
}

void GPUBAKING_NS_NAME::FVolumetricLightMapEncoder::WriteVolumetricLightmap(FPrecomputedVolumetricLightmapData& LevelData, const std::string& pathdir, cross::VolumetricLightmapData& outData)
{
    auto rootPath = LightingBakeConfig::GetInstance().GetProjectionRootPath();
    auto contentPath = LightingBakeConfig::GetInstance().GetLightMapSavedRelPath();

    outData.mIndirectionTexFile = contentPath + "vlm_indirection.nda";
    WriteVolumetricLightmap(LevelData.IndirectionTexture, LevelData.IndirectionTextureDimensions, rootPath + outData.mIndirectionTexFile);

    outData.mAmbientVector = contentPath + "vlm_ambientvector.nda";
    WriteVolumetricLightmap(LevelData.BrickData.AmbientVector, LevelData.BrickDataDimensions, rootPath + outData.mAmbientVector);

    for (int32 i = 0; i < SHCoefficientsNum; i++)
    {
        outData.mSHCoefficients.push_back(contentPath + "vlm_shcoefficient_" + std::to_string(i) + ".nda");
        WriteVolumetricLightmap(LevelData.BrickData.SHCoefficients[i], LevelData.BrickDataDimensions, rootPath + outData.mSHCoefficients.back());
    }

    auto& SkyBentNormalData = LevelData.BrickData.SkyBentNormal;
    const auto& DirectionalLightShadowingData = LevelData.BrickData.DirectionalLightShadowing;
    auto SkyBentNormalDataStride = GetPixelByteSize(SkyBentNormalData.Format);
    auto DirectionalLightShadowDataStride = GetPixelByteSize(DirectionalLightShadowingData.Format);
    AssertMsg(SkyBentNormalDataStride == 4 && DirectionalLightShadowDataStride == 1, "SkyBentNormal or DirectionalLightShadowing format error");
    for (auto i = 0; i < DirectionalLightShadowingData.DataSize; i++)
    {
        auto skyDataIndex = (i + 1) * SkyBentNormalDataStride - 1;
        SkyBentNormalData.Data[skyDataIndex] = DirectionalLightShadowingData.Data[i];
    }

    outData.mSkyBentNormalTexFile = contentPath + "vlm_skybentnormal.nda";
    WriteVolumetricLightmap(LevelData.BrickData.SkyBentNormal, LevelData.BrickDataDimensions, rootPath + outData.mSkyBentNormalTexFile);

//     outData.mDirectionalLightShadowTexFile = contentPath + "vlm_shadow.nda";
//     WriteVolumetricLightmap(LevelData.BrickData.DirectionalLightShadowing, LevelData.BrickDataDimensions, rootPath + outData.mDirectionalLightShadowTexFile);
    return;
}

struct FFilteredBrickData
{
    FFloat3Packed AmbientVector;
    FColor SHCoefficients[SHCoefficientsNum];
    uint8 DirectionalLightShadowing;
};

// Fill in voxels which are inside geometry with their valid neighbors
void FilterWithNeighbors(const std::vector<const FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth, int32 BrickStartAllocation, int32 CurrentDepth, int3 BrickLayoutDimensions,
                         const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, FPrecomputedVolumetricLightmapData& CurrentLevelData,
                         const std::vector<FIrradianceVoxelImportProcessingData>& VoxelImportProcessingData, std::vector<FIrradianceVoxelImportProcessingData>& NewVoxelImportProcessingData)
{
    int32 BrickSize = VolumetricLightmapSettings.BrickSize;
    int32 PaddedBrickSize = BrickSize + 1;
    const int32 BrickSizeLog2 = FloorLog2(BrickSize);
    const float InvBrickSize = 1.0f / BrickSize;

    std::vector<FFilteredBrickData> FilteredBrickData;
    std::vector<bool> FilteredBrickDataValid;

    FilteredBrickData.resize(BrickSize * BrickSize * BrickSize);
    memset(&FilteredBrickData[0], 0, sizeof(FilteredBrickData[0]) * FilteredBrickData.size());
    FilteredBrickDataValid.resize(BrickSize * BrickSize * BrickSize);

    // Fill in voxels which are inside geometry with their valid neighbors
    for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
    {
        const FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];

        // Initialize temporary brick data to invalid
        FilteredBrickDataValid.assign(FilteredBrickDataValid.size(), 0);

        Assert(Brick.TreeDepth == CurrentDepth);

        const int32 DetailCellsPerCurrentLevelBrick = 1 << ((VolumetricLightmapSettings.MaxRefinementLevels - Brick.TreeDepth) * BrickSizeLog2);
        const int32 NumBottomLevelBricks = DetailCellsPerCurrentLevelBrick / BrickSize;
        const int3 IndirectionTexturePosition = int3(Brick.IndirectionTexturePosition);
        const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickStartAllocation + BrickIndex, BrickLayoutDimensions) * PaddedBrickSize;

        for (int32 Z = 0; Z < BrickSize; Z++)
        {
            for (int32 Y = 0; Y < BrickSize; Y++)
            {
                for (int32 X = 0; X < BrickSize; X++)
                {
                    int3 VoxelCoordinate(X, Y, Z);

                    const int32 LinearDestCellIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate + BrickLayoutPosition, CurrentLevelData.BrickDataDimensions);
                    FIrradianceVoxelImportProcessingData VoxelImportData = VoxelImportProcessingData[LinearDestCellIndex];

                    if (VoxelImportData.bInsideGeometry
                        // Don't modify border voxels
                        && !VoxelImportData.bBorderVoxel)
                    {
                        //@todo - filter SkyBentNormal from neighbors too
                        FLinearColor AmbientVector = FLinearColor(0, 0, 0, 0);
                        FLinearColor SHCoefficients[SHCoefficientsNum];
                        FLinearColor DirectionalLightShadowing = FLinearColor(0, 0, 0, 0);

                        for (int32 i = 0; i < SHCoefficientsNum; i++)
                        {
                            SHCoefficients[i] = FLinearColor(0, 0, 0, 0);
                        }

                        float TotalWeight = 0.0f;

                        for (int32 NeighborZ = -1; NeighborZ <= 1; NeighborZ++)
                        {
                            for (int32 NeighborY = -1; NeighborY <= 1; NeighborY++)
                            {
                                for (int32 NeighborX = -1; NeighborX <= 1; NeighborX++)
                                {
                                    const float3 NeighborIndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X + NeighborX, Y + NeighborY, Z + NeighborZ) * (InvBrickSize * NumBottomLevelBricks);
                                    const int3 NeighborVoxelCoordinate(X + NeighborX, Y + NeighborY, Z + NeighborZ);

                                    if (NeighborVoxelCoordinate != VoxelCoordinate 
                                        //NeighborVoxelCoordinate in LevelData Bounds
                                        && NeighborIndirectionDataSourceCoordinate.x >= 0.0f 
                                        && NeighborIndirectionDataSourceCoordinate.y >= 0.0f 
                                        && NeighborIndirectionDataSourceCoordinate.z >= 0.0f 
                                        && NeighborIndirectionDataSourceCoordinate.x < CurrentLevelData.IndirectionTextureDimensions.x 
                                        && NeighborIndirectionDataSourceCoordinate.y < CurrentLevelData.IndirectionTextureDimensions.y 
                                        && NeighborIndirectionDataSourceCoordinate.z < CurrentLevelData.IndirectionTextureDimensions.z)
                                    {
                                        int3 IndirectionBrickOffset;
                                        int32 IndirectionBrickSize;

                                        Assert(GetPixelByteSize(CurrentLevelData.IndirectionTexture.Format) == sizeof(uint8) * 4);
                                        SampleIndirectionTexture(
                                            NeighborIndirectionDataSourceCoordinate, CurrentLevelData.IndirectionTextureDimensions, CurrentLevelData.IndirectionTexture.Data.data(), IndirectionBrickOffset, IndirectionBrickSize);

                                        // Only filter from bricks with equal density, to avoid reading from uninitialized padding
                                        // This causes seams but they fall at density transitions so not noticeable
                                        if (IndirectionBrickSize == NumBottomLevelBricks)
                                        {
                                            const float3 BrickTextureCoordinate = ComputeBrickTextureCoordinate(NeighborIndirectionDataSourceCoordinate, IndirectionBrickOffset, IndirectionBrickSize, BrickSize);
                                            GPUBaking::FIrradianceVoxelImportProcessingData NeighborVoxelImportData =
                                                NearestVolumeLookup<GPUBaking::FIrradianceVoxelImportProcessingData>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, VoxelImportProcessingData.data());

                                            if (!NeighborVoxelImportData.bInsideGeometry && !NeighborVoxelImportData.bBorderVoxel)
                                            {
                                                const float Weight = 1.0f / std::max<float>(static_cast<float>(std::abs(NeighborX) + std::abs(NeighborY) + std::abs(NeighborZ)), .5f);
                                                FLinearColor NeighborAmbientVector =
                                                    FilteredVolumeLookup<FFloat3Packed>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, (const FFloat3Packed*)CurrentLevelData.BrickData.AmbientVector.Data.data());
                                                AmbientVector += NeighborAmbientVector * Weight;

                                                for (int32 i = 0; i < SHCoefficientsNum; i++)
                                                {
                                                    static_assert(SHCoefficientsNum == 6, "Assuming 2 SHCoefficient vectors per color channel");

                                                    // Weight by ambient before filtering, normalized SH coefficients don't filter properly
                                                    float AmbientCoefficient = NeighborAmbientVector.Component(i / 2);
                                                    SHCoefficients[i] += AmbientCoefficient * Weight *
                                                                         FilteredVolumeLookup<FColor>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, (const FColor*)CurrentLevelData.BrickData.SHCoefficients[i].Data.data());
                                                }

                                                FLinearColor NeighborDirectionalLightShadowing =
                                                    FilteredVolumeLookup<uint8>(BrickTextureCoordinate, CurrentLevelData.BrickDataDimensions, (const uint8*)CurrentLevelData.BrickData.DirectionalLightShadowing.Data.data());
                                                DirectionalLightShadowing += NeighborDirectionalLightShadowing * Weight;

                                                TotalWeight += Weight;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (TotalWeight > 0.0f)
                        {
                            const int32 LinearVoxelIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate, int3(BrickSize, BrickSize, BrickSize));

                            // Store filtered output to temporary brick data to avoid order dependent results between voxels
                            // This still produces order dependent filtering between neighboring bricks
                            FilteredBrickDataValid[LinearVoxelIndex] = true;

                            const float InvTotalWeight = 1.0f / TotalWeight;

                            const FLinearColor FilteredAmbientColor = AmbientVector * InvTotalWeight;
                            FilteredBrickData[LinearVoxelIndex].AmbientVector = ConvertFromLinearColor<FFloat3Packed>(FilteredAmbientColor);

                            for (int32 i = 0; i < SHCoefficientsNum; i++)
                            {
                                static_assert(SHCoefficientsNum == 6, "Assuming 2 SHCoefficient vectors per color channel");

                                float AmbientCoefficient = std::max(FilteredAmbientColor.Component(i / 2), KINDA_SMALL_NUMBER);

                                FilteredBrickData[LinearVoxelIndex].SHCoefficients[i] = ConvertFromLinearColor<FColor>(SHCoefficients[i] * InvTotalWeight / AmbientCoefficient);
                            }

                            FilteredBrickData[LinearVoxelIndex].DirectionalLightShadowing = ConvertFromLinearColor<uint8>(DirectionalLightShadowing * InvTotalWeight);
                        }
                    }
                }
            }
        }

        for (int32 Z = 0; Z < BrickSize; Z++)
        {
            for (int32 Y = 0; Y < BrickSize; Y++)
            {
                for (int32 X = 0; X < BrickSize; X++)
                {
                    int3 VoxelCoordinate(X, Y, Z);
                    const int32 LinearVoxelIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate, int3(BrickSize, BrickSize, BrickSize));

                    // Write filtered voxel data back to the original
                    if (FilteredBrickDataValid[LinearVoxelIndex])
                    {
                        const int32 LinearBrickLayoutCellIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate + BrickLayoutPosition, CurrentLevelData.BrickDataDimensions);

                        // Mark as valid for future passes now that we've overwritten with filtered neighbors
                        NewVoxelImportProcessingData[LinearBrickLayoutCellIndex].bInsideGeometry = false;

                        FFloat3Packed* DestAmbientVector = reinterpret_cast<FFloat3Packed*>(&CurrentLevelData.BrickData.AmbientVector.Data[LinearBrickLayoutCellIndex * sizeof(FFloat3Packed)]);
                        *DestAmbientVector = FilteredBrickData[LinearVoxelIndex].AmbientVector;

                        for (int32 i = 0; i < SHCoefficientsNum; i++)
                        {
                            FColor* DestCoefficients = reinterpret_cast<FColor*>(&CurrentLevelData.BrickData.SHCoefficients[i].Data[LinearBrickLayoutCellIndex * sizeof(FColor)]);
                            *DestCoefficients = FilteredBrickData[LinearVoxelIndex].SHCoefficients[i];
                        }

                        uint8* DestDirectionalLightShadowing = reinterpret_cast<uint8*>(&CurrentLevelData.BrickData.DirectionalLightShadowing.Data[LinearBrickLayoutCellIndex * sizeof(uint8)]);
                        *DestDirectionalLightShadowing = FilteredBrickData[LinearVoxelIndex].DirectionalLightShadowing;
                    }
                }
            }
        }
    }
}

void StitchDetailBricksWithLowDensityNeighbors(const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth, int32 BrickStartAllocation, int32 CurrentDepth, int3 BrickLayoutDimensions,
                                               const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, FPrecomputedVolumetricLightmapData& CurrentLevelData)
{
    int32 BrickSize = VolumetricLightmapSettings.BrickSize;
    int32 PaddedBrickSize = BrickSize + 1;
    const int32 BrickSizeLog2 = FloorLog2(BrickSize);
    const float InvBrickSize = 1.0f / BrickSize;

    // Stitch all higher density bricks with neighboring lower density bricks
    for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
    {
        const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];
        const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickStartAllocation + BrickIndex, BrickLayoutDimensions) * PaddedBrickSize;
        const int32 DetailCellsPerCurrentLevelBrick = 1 << ((VolumetricLightmapSettings.MaxRefinementLevels - Brick.TreeDepth) * BrickSizeLog2);
        const int32 NumBottomLevelBricks = DetailCellsPerCurrentLevelBrick / BrickSize;
        const float3 IndirectionTexturePosition = Brick.IndirectionTexturePosition * 1.0f;

        int32 X, Y, Z = 0;

        // Iterate over unique data on the edge of the brick which needs to match padding on lower resolution bricks
        for (X = 0, Z = 0; Z < BrickSize; Z++)
        {
            for (Y = 0; Y < BrickSize; Y++)
            {
                float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                for (int32 StitchDirection = 1; StitchDirection < 8; StitchDirection++)
                {
                    float3 StitchSourceCoordinate = IndirectionDataSourceCoordinate;

                    if ((StitchDirection & 1) && X == 0)
                    {
                        StitchSourceCoordinate.x -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 2) && Y == 0)
                    {
                        StitchSourceCoordinate.y -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 4) && Z == 0)
                    {
                        StitchSourceCoordinate.z -= GPointFilteringThreshold * 2;
                    }

                    if (StitchSourceCoordinate != IndirectionDataSourceCoordinate)
                    {
                        bool bStitched = CopyFromBrickmapTexel(StitchSourceCoordinate,
                                                               int3(X, Y, Z),
                                                               // Restrict copies to only read from bricks that are lower effective resolution (higher NumBottomLevelBricks)
                                                               NumBottomLevelBricks,
                                                               BrickSize,
                                                               BrickLayoutPosition,
                                                               CurrentLevelData,
                                                               CurrentLevelData.BrickData);

                        if (bStitched)
                        {
                            break;
                        }
                    }
                }
            }
        }

        for (Z = 0, Y = 0; Y < BrickSize; Y++)
        {
            for (X = 1; X < BrickSize; X++)
            {
                float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                for (int32 StitchDirection = 1; StitchDirection < 8; StitchDirection++)
                {
                    float3 StitchSourceCoordinate = IndirectionDataSourceCoordinate;

                    if ((StitchDirection & 1) && X == 0)
                    {
                        StitchSourceCoordinate.x -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 2) && Y == 0)
                    {
                        StitchSourceCoordinate.y -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 4) && Z == 0)
                    {
                        StitchSourceCoordinate.z -= GPointFilteringThreshold * 2;
                    }

                    if (StitchSourceCoordinate != IndirectionDataSourceCoordinate)
                    {
                        bool bStitched = CopyFromBrickmapTexel(StitchSourceCoordinate,
                                                               int3(X, Y, Z),
                                                               // Restrict copies to only read from bricks that are lower effective resolution (higher NumBottomLevelBricks)
                                                               NumBottomLevelBricks,
                                                               BrickSize,
                                                               BrickLayoutPosition,
                                                               CurrentLevelData,
                                                               CurrentLevelData.BrickData,
                                                               true);

                        if (bStitched)
                        {
                            break;
                        }
                    }
                }
            }
        }

        for (Y = 0, Z = 1; Z < BrickSize; Z++)
        {
            for (X = 1; X < BrickSize; X++)
            {
                float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                for (int32 StitchDirection = 1; StitchDirection < 8; StitchDirection++)
                {
                    float3 StitchSourceCoordinate = IndirectionDataSourceCoordinate;

                    if ((StitchDirection & 1) && X == 0)
                    {
                        StitchSourceCoordinate.x -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 2) && Y == 0)
                    {
                        StitchSourceCoordinate.y -= GPointFilteringThreshold * 2;
                    }

                    if ((StitchDirection & 4) && Z == 0)
                    {
                        StitchSourceCoordinate.z -= GPointFilteringThreshold * 2;
                    }

                    if (StitchSourceCoordinate != IndirectionDataSourceCoordinate)
                    {
                        bool bStitched = CopyFromBrickmapTexel(StitchSourceCoordinate,
                                                               int3(X, Y, Z),
                                                               // Restrict copies to only read from bricks that are lower effective resolution (higher NumBottomLevelBricks)
                                                               NumBottomLevelBricks,
                                                               BrickSize,
                                                               BrickLayoutPosition,
                                                               CurrentLevelData,
                                                               CurrentLevelData.BrickData);

                        if (bStitched)
                        {
                            break;
                        }
                    }
                }
            }
        }
    }
}

void CopyPaddingFromUniqueData(const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth, int32 BrickStartAllocation, int32 CurrentDepth, int3 BrickLayoutDimensions,
                               const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, const FPrecomputedVolumetricLightmapData& CurrentLevelData, FVolumetricLightmapBrickData& BrickData)
{
    int32 BrickSize = VolumetricLightmapSettings.BrickSize;
    int32 PaddedBrickSize = BrickSize + 1;
    const int32 BrickSizeLog2 = FloorLog2(BrickSize);
    const float InvBrickSize = 1.0f / BrickSize;

    for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
    {
        const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];
        const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickStartAllocation + BrickIndex, BrickLayoutDimensions) * PaddedBrickSize;
        const int32 DetailCellsPerCurrentLevelBrick = 1 << ((VolumetricLightmapSettings.MaxRefinementLevels - Brick.TreeDepth) * BrickSizeLog2);
        const int32 NumBottomLevelBricks = DetailCellsPerCurrentLevelBrick / BrickSize;
        const float3 IndirectionTexturePosition = int3(Brick.IndirectionTexturePosition.x, Brick.IndirectionTexturePosition.y, Brick.IndirectionTexturePosition.z) * 1.0f;

        int32 X, Y, Z = 0;

        // Iterate over padding voxels
        for (X = PaddedBrickSize - 1, Z = 0; Z < PaddedBrickSize; Z++)
        {
            for (Y = 0; Y < PaddedBrickSize; Y++)
            {
                const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                // Overwrite padding with unique data from this same coordinate in the indirection texture
                CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
            }
        }

        for (Z = PaddedBrickSize - 1, Y = 0; Y < PaddedBrickSize; Y++)
        {
            for (X = 0; X < PaddedBrickSize; X++)
            {
                const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
            }
        }

        for (Y = PaddedBrickSize - 1, Z = 0; Z < PaddedBrickSize; Z++)
        {
            for (X = 0; X < PaddedBrickSize; X++)
            {
                const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + int3(X, Y, Z) * (InvBrickSize * NumBottomLevelBricks);

                CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
            }
        }
    }
}

float3 GetLookupPositionAwayFromBorder(int32 PaddedBrickSize, int3 LocalCellCoordinate)
{
    float3 LookupCoordinate = int3(LocalCellCoordinate.x, LocalCellCoordinate.y, LocalCellCoordinate.z) * 1.0f;

    if (LocalCellCoordinate.x == PaddedBrickSize - 1)
    {
        LookupCoordinate.x -= 1;
    }

    if (LocalCellCoordinate.y == PaddedBrickSize - 1)
    {
        LookupCoordinate.y -= 1;
    }

    if (LocalCellCoordinate.z == PaddedBrickSize - 1)
    {
        LookupCoordinate.z -= 1;
    }

    return LookupCoordinate;
}

void CopyVolumeBorderFromInterior(const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth, int32 BrickStartAllocation, int32 CurrentDepth, int3 BrickLayoutDimensions,
                                  const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, FPrecomputedVolumetricLightmapData& CurrentLevelData, FVolumetricLightmapBrickData& BrickData)
{
    int32 BrickSize = VolumetricLightmapSettings.BrickSize;
    int32 PaddedBrickSize = BrickSize + 1;
    const int32 BrickSizeLog2 = FloorLog2(BrickSize);
    const float InvBrickSize = 1.0f / BrickSize;

    for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
    {
        const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];
        const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickStartAllocation + BrickIndex, BrickLayoutDimensions) * PaddedBrickSize;
        const int32 DetailCellsPerCurrentLevelBrick = 1 << ((VolumetricLightmapSettings.MaxRefinementLevels - Brick.TreeDepth) * BrickSizeLog2);
        const int32 NumBottomLevelBricks = DetailCellsPerCurrentLevelBrick / BrickSize;
        const float3 IndirectionTexturePosition = int3(Brick.IndirectionTexturePosition.x, Brick.IndirectionTexturePosition.y, Brick.IndirectionTexturePosition.z) * 1.0f;

        // Operate on bricks on the edge of the volume covered by the indirection texture
        if (Brick.IndirectionTexturePosition.x + NumBottomLevelBricks == CurrentLevelData.IndirectionTextureDimensions.x)
        {
            int32 X, Y, Z = 0;

            // Iterate over padding voxels
            for (X = PaddedBrickSize - 1, Z = 0; Z < PaddedBrickSize; Z++)
            {
                for (Y = 0; Y < PaddedBrickSize; Y++)
                {
                    const float3 LookupPosition = GetLookupPositionAwayFromBorder(PaddedBrickSize, int3(X, Y, Z));
                    const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + LookupPosition * (InvBrickSize * NumBottomLevelBricks);

                    // Overwrite padding on the edge of the volume with neighboring data inside the volume
                    CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
                }
            }
        }

        if (Brick.IndirectionTexturePosition.y + NumBottomLevelBricks == CurrentLevelData.IndirectionTextureDimensions.y)
        {
            int32 X, Y, Z = 0;

            // Iterate over padding voxels
            for (Y = PaddedBrickSize - 1, Z = 0; Z < PaddedBrickSize; Z++)
            {
                for (X = 0; X < PaddedBrickSize; X++)
                {
                    const float3 LookupPosition = GetLookupPositionAwayFromBorder(PaddedBrickSize, int3(X, Y, Z));
                    const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + LookupPosition * (InvBrickSize * NumBottomLevelBricks);

                    CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
                }
            }
        }

        if (Brick.IndirectionTexturePosition.z + NumBottomLevelBricks == CurrentLevelData.IndirectionTextureDimensions.z)
        {
            int32 X, Y, Z = 0;

            // Iterate over padding voxels
            for (Z = PaddedBrickSize - 1, Y = 0; Y < PaddedBrickSize; Y++)
            {
                for (X = 0; X < PaddedBrickSize; X++)
                {
                    const float3 LookupPosition = GetLookupPositionAwayFromBorder(PaddedBrickSize, int3(X, Y, Z));
                    const float3 IndirectionDataSourceCoordinate = IndirectionTexturePosition + LookupPosition * (InvBrickSize * NumBottomLevelBricks);

                    CopyFromBrickmapTexel(IndirectionDataSourceCoordinate, int3(X, Y, Z), 0, BrickSize, BrickLayoutPosition, CurrentLevelData, BrickData);
                }
            }
        }
    }
}

void FVolumetricLightMapEncoder::ImportVolumetricLightmap(const GPUBaking::FAdaptiveVolumetricLightmapParameters& VolumetricLightmapSettings, const std::vector<FImportedVolumetricLightmapTaskData>& ImportedVolumetricLightmapTaskData,
                                                          FPrecomputedVolumetricLightmapData& CurrentLevelData)
{
    const int BrickSize = VolumetricLightmapSettings.BrickSize;
    const int PaddedBrickSize = VolumetricLightmapSettings.BrickSize + 1;
    const int MaxBricksInLayoutOneDim = 1 << 8;

    std::vector<std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>> BricksByDepth;
    BricksByDepth.resize(VolumetricLightmapSettings.MaxRefinementLevels);
    for (int32 TaskDataIndex = 0; TaskDataIndex < ImportedVolumetricLightmapTaskData.size(); TaskDataIndex++)
    {
        const FImportedVolumetricLightmapTaskData& TaskData = ImportedVolumetricLightmapTaskData[TaskDataIndex];

        for (int32 BrickIndex = 0; BrickIndex < TaskData.Bricks.size(); BrickIndex++)
        {
            const GPUBaking::FImportedVolumetricLightmapBrick& Brick = TaskData.Bricks[BrickIndex];
            BricksByDepth[Brick.TreeDepth].push_back(&Brick);
        }
    }

    //FPrecomputedVolumetricLightmapData CurrentLevelData;
    {
        auto BoundingExtent = VolumetricLightmapSettings.VolumeSize / 2;
        auto BoundingCenter = VolumetricLightmapSettings.VolumeMin + BoundingExtent;
        CurrentLevelData.InitializeOnImport(cross::BoundingBox({BoundingCenter.x, BoundingCenter.y, BoundingCenter.z}, {BoundingExtent.x, BoundingExtent.y, BoundingExtent.z}), BrickSize);

        // Temporarily assign format as PF_B8G8R8A8 since that matches FColor and makes our operations easier
        // Will be converted to PF_R8G8B8A8 by ConvertBGRA8ToRGBA8ForLayer() later
        CurrentLevelData.BrickData.AmbientVector.Format = TextureFormat::R11G11B10Float;
        CurrentLevelData.BrickData.SkyBentNormal.Format = TextureFormat::RGBA32;
        CurrentLevelData.BrickData.DirectionalLightShadowing.Format = TextureFormat::R8;

        for (int32 i = 0; i < SHCoefficientsNum; i++)
        {
            CurrentLevelData.BrickData.SHCoefficients[i].Format = TextureFormat::RGBA32;
        }

        CurrentLevelData.BrickData.LQLightColor.Format = TextureFormat::R11G11B10Float;
        CurrentLevelData.BrickData.LQLightDirection.Format = TextureFormat::RGBA32;
    }

    int32 BrickTextureLinearAllocator = 0;

    for (int32 CurrentDepth = 0; CurrentDepth < VolumetricLightmapSettings.MaxRefinementLevels; CurrentDepth++)
    {
        const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth = BricksByDepth[CurrentDepth];
        BrickTextureLinearAllocator += static_cast<int32>(BricksAtCurrentDepth.size());
    }
    Assert(BrickTextureLinearAllocator != 0);
    if (BrickTextureLinearAllocator == 0)
    {
        return;
    }

    int3 BrickLayoutDimensions;
    BrickLayoutDimensions.x = std::min<int>(BrickTextureLinearAllocator, MaxBricksInLayoutOneDim);
    BrickTextureLinearAllocator = DivideAndRoundUp(BrickTextureLinearAllocator, BrickLayoutDimensions.x);
    BrickLayoutDimensions.y = std::min<int>(BrickTextureLinearAllocator, MaxBricksInLayoutOneDim);
    BrickTextureLinearAllocator = DivideAndRoundUp(BrickTextureLinearAllocator, BrickLayoutDimensions.y);
    BrickLayoutDimensions.z = std::min<int>(BrickTextureLinearAllocator, MaxBricksInLayoutOneDim);

    const int32 BrickSizeLog2 = FloorLog2(BrickSize);
    const int32 DetailCellsPerTopLevelBrick = 1 << (VolumetricLightmapSettings.MaxRefinementLevels * BrickSizeLog2);
    const int32 IndirectionCellsPerTopLevelCell = DetailCellsPerTopLevelBrick / BrickSize;

    int32 IndirectionTextureDataStride = 0;
    {
        CurrentLevelData.IndirectionTextureDimensions = VolumetricLightmapSettings.TopLevelGridSize * IndirectionCellsPerTopLevelCell;
        CurrentLevelData.IndirectionTexture.Format = TextureFormat::RGBA32;
        IndirectionTextureDataStride = GetPixelByteSize(CurrentLevelData.IndirectionTexture.Format);

        const int32 TotalIndirectionTextureSize = CurrentLevelData.IndirectionTextureDimensions.x * CurrentLevelData.IndirectionTextureDimensions.y * CurrentLevelData.IndirectionTextureDimensions.z;
        CurrentLevelData.IndirectionTexture.Resize(TotalIndirectionTextureSize * IndirectionTextureDataStride);
    }
    BuildIndirectionTexture(BricksByDepth, VolumetricLightmapSettings, MaxBricksInLayoutOneDim, BrickLayoutDimensions, IndirectionTextureDataStride, CurrentLevelData, false);

    bool bGenerateSkyShadowing = true;
    std::vector<GPUBaking::FIrradianceVoxelImportProcessingData> VoxelImportProcessingData;

    {
        CurrentLevelData.BrickDataDimensions = BrickLayoutDimensions * PaddedBrickSize;
        const int32 TotalBrickDataSize = CurrentLevelData.BrickDataDimensions.x * CurrentLevelData.BrickDataDimensions.y * CurrentLevelData.BrickDataDimensions.z;

        CurrentLevelData.BrickData.AmbientVector.Resize(TotalBrickDataSize * GetPixelByteSize(CurrentLevelData.BrickData.AmbientVector.Format));

        if (bGenerateSkyShadowing)
        {
            CurrentLevelData.BrickData.SkyBentNormal.Resize(TotalBrickDataSize * GetPixelByteSize(CurrentLevelData.BrickData.SkyBentNormal.Format));
        }

        CurrentLevelData.BrickData.DirectionalLightShadowing.Resize(TotalBrickDataSize * GetPixelByteSize(CurrentLevelData.BrickData.DirectionalLightShadowing.Format));

        for (int32 i = 0; i < SHCoefficientsNum; i++)
        {
            const int32 Stride = GetPixelByteSize(CurrentLevelData.BrickData.SHCoefficients[i].Format);
            CurrentLevelData.BrickData.SHCoefficients[i].Resize(TotalBrickDataSize * Stride);
        }
        CurrentLevelData.BrickData.LQLightColor.Resize(TotalBrickDataSize * GetPixelByteSize(CurrentLevelData.BrickData.LQLightColor.Format));

        CurrentLevelData.BrickData.LQLightDirection.Resize(TotalBrickDataSize * GetPixelByteSize(CurrentLevelData.BrickData.LQLightDirection.Format));

        VoxelImportProcessingData.resize(TotalBrickDataSize);
    }

    int32 BrickStartAllocation = 0;
    // copy brick data to atlas volumetric lightmap
    for (int32 CurrentDepth = 0; CurrentDepth < VolumetricLightmapSettings.MaxRefinementLevels; CurrentDepth++)
    {
        const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth = BricksByDepth[CurrentDepth];

        for (int32 BrickIndex = 0; BrickIndex < BricksAtCurrentDepth.size(); BrickIndex++)
        {
            const GPUBaking::FImportedVolumetricLightmapBrick& Brick = *BricksAtCurrentDepth[BrickIndex];
            const int3 BrickLayoutPosition = ComputeBrickLayoutPosition(BrickStartAllocation + BrickIndex, BrickLayoutDimensions) * PaddedBrickSize;

            CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.AmbientVector.Format),
                                              CurrentLevelData.BrickDataDimensions,
                                              BrickLayoutPosition,
                                              int3(BrickSize),
                                              reinterpret_cast<const uint8*>(Brick.AmbientVector.data()),
                                              CurrentLevelData.BrickData.AmbientVector.Data.data(),
                                              Brick.AmbientVector[0].IsBGRAFormat());

            for (int32 i = 0; i < SHCoefficientsNum; i++)
            {
                CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.SHCoefficients[i].Format),
                                                  CurrentLevelData.BrickDataDimensions,
                                                  BrickLayoutPosition,
                                                  int3(BrickSize),
                                                  reinterpret_cast<const uint8*>(Brick.SHCoefficients[i].data()),
                                                  CurrentLevelData.BrickData.SHCoefficients[i].Data.data(),
                                                  Brick.SHCoefficients[i][0].IsBGRAFormat());
            }

            CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.LQLightColor.Format),
                                              CurrentLevelData.BrickDataDimensions,
                                              BrickLayoutPosition,
                                              int3(BrickSize),
                                              reinterpret_cast<const uint8*>(Brick.LQLightColor.data()),
                                              CurrentLevelData.BrickData.LQLightColor.Data.data(),
                                              Brick.LQLightColor[0].IsBGRAFormat());

            CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.LQLightDirection.Format),
                                              CurrentLevelData.BrickDataDimensions,
                                              BrickLayoutPosition,
                                              int3(BrickSize),
                                              reinterpret_cast<const uint8*>(Brick.LQLightDirection.data()),
                                              CurrentLevelData.BrickData.LQLightDirection.Data.data(),
                                              Brick.LQLightDirection[0].IsBGRAFormat());

            if (bGenerateSkyShadowing)
            {
                CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.SkyBentNormal.Format),
                                                  CurrentLevelData.BrickDataDimensions,
                                                  BrickLayoutPosition,
                                                  int3(BrickSize),
                                                  reinterpret_cast<const uint8*>(Brick.SkyBentNormal.data()),
                                                  CurrentLevelData.BrickData.SkyBentNormal.Data.data(),
                                                  Brick.SkyBentNormal[0].IsBGRAFormat());
            }

            CopyBrickToAtlasVolumeTextureTLBS(GetPixelByteSize(CurrentLevelData.BrickData.DirectionalLightShadowing.Format),
                                              CurrentLevelData.BrickDataDimensions,
                                              BrickLayoutPosition,
                                              int3(BrickSize),
                                              reinterpret_cast<const uint8*>(Brick.DirectionalLightShadowing.data()),
                                              CurrentLevelData.BrickData.DirectionalLightShadowing.Data.data(),
                                              false);

            CopyBrickToAtlasVolumeTextureTLBS(sizeof(GPUBaking::FIrradianceVoxelImportProcessingData),
                                              CurrentLevelData.BrickDataDimensions,
                                              BrickLayoutPosition,
                                              int3(BrickSize),
                                              reinterpret_cast<const uint8*>(Brick.TaskVoxelImportProcessingData.data()),
                                              reinterpret_cast<uint8*>(VoxelImportProcessingData.data()),
                                              false);
        }

        BrickStartAllocation += static_cast<int32>(BricksAtCurrentDepth.size());
    }

    // One pass needed to cover the trilinear filtering footprint, another pass needed to cover exterior voxels which see backfaces due to the large ray start bias.
    constexpr int NumDilateOverEmbeddedVoxelsPasses = 2;
    for (int32 DilatePassIndex = 0; DilatePassIndex < NumDilateOverEmbeddedVoxelsPasses; DilatePassIndex++)
    {
        BrickStartAllocation = 0;

        // Compute the allocation start for the highest density level bricks
        for (int32 CurrentDepth = 0; CurrentDepth < VolumetricLightmapSettings.MaxRefinementLevels - 1; CurrentDepth++)
        {
            BrickStartAllocation += static_cast<int32>(BricksByDepth[CurrentDepth].size());
        }

        std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& HighestDensityBricks = BricksByDepth[VolumetricLightmapSettings.MaxRefinementLevels - 1];

        // Need to double buffer bInsideGeometry as it is both read and written
        std::vector<FIrradianceVoxelImportProcessingData> NewVoxelImportProcessingData = VoxelImportProcessingData;

        // Reads from unique data of any density bricks, writes to unique data
        // This is doing a filter in-place, which is only reliable because source and dest voxels are mutually exclusive
        FilterWithNeighbors(
            HighestDensityBricks, 
            BrickStartAllocation, 
            VolumetricLightmapSettings.MaxRefinementLevels - 1, 
            BrickLayoutDimensions,
            VolumetricLightmapSettings,
            CurrentLevelData,
            VoxelImportProcessingData,
            NewVoxelImportProcessingData);

        VoxelImportProcessingData = std::move(NewVoxelImportProcessingData);
    }

    BrickStartAllocation = 0;

    for (int32 CurrentDepth = 0; CurrentDepth < VolumetricLightmapSettings.MaxRefinementLevels; CurrentDepth++)
    {
        const std::vector<const GPUBaking::FImportedVolumetricLightmapBrick*>& BricksAtCurrentDepth = BricksByDepth[CurrentDepth];

        if (bStitchDetailBricksWithLowDensityNeighborsTLBS && CurrentDepth > 0)
        {
            // Reads from both unique and padding data of lower density bricks, writes to unique data
            StitchDetailBricksWithLowDensityNeighbors(BricksAtCurrentDepth, BrickStartAllocation, CurrentDepth, BrickLayoutDimensions, VolumetricLightmapSettings, CurrentLevelData);
        }

        if (bCopyPaddingFromUniqueDataTLBS)
        {
            // Compute padding for all the bricks
            // Reads from unique data, writes to padding data of bricks
            // Padding must be computed after all operations that might modify the unique data
            CopyPaddingFromUniqueData(BricksAtCurrentDepth, BrickStartAllocation, CurrentDepth, BrickLayoutDimensions, VolumetricLightmapSettings, CurrentLevelData, CurrentLevelData.BrickData);
        }

        if (bCopyVolumeBorderFromInteriorTLBS)
        {
            // The volume border padding had no unique data to copy from, replicate the neighboring interior value
            CopyVolumeBorderFromInterior(BricksAtCurrentDepth, BrickStartAllocation, CurrentDepth, BrickLayoutDimensions, VolumetricLightmapSettings, CurrentLevelData, CurrentLevelData.BrickData);
        }

        BrickStartAllocation += static_cast<int32>(BricksAtCurrentDepth.size());
    }
    return;
}

NS_GPUBAKING_END
