#pragma once

#include "BakingSceneDefine.h"
#include "BakingTypeDefine.h"
#include "SerializeUtils.h"

NS_GPUBAKING_BEGIN

#ifndef ENABLE_OCCLUSION_FLOAT16F
#    define ENABLE_OCCLUSION_FLOAT16F 0
#endif

#if ENABLE_PRT_API && ENABLE_OCCLUSION_FLOAT16F
typedef FFloat16 FSkyOcclusionType;
//#    define FSkyOcclusionType uint8
#else
typedef uint8 FSkyOcclusionType;
#endif

constexpr int LM_NUM_SH_COEFFICIENTS = 9;
constexpr int LM_NUM_STORED_LIGHTMAP_COEF = 4;
constexpr int LM_NUM_HQ_LIGHTMAP_COEF = 2;
constexpr int LM_LQ_LIGHTMAP_COEF_INDEX = 2;

enum EJobGuidType
{
    EGUID_B_FOR_LIGHTMAP = 0,    // use FLightmap2DInput & FLightmap2DOutput
    EGUID_B_FOR_SHADOWMAP = 1,   // use FSDFShadowInput & FSDFShadowOutput
    EGUID_B_FOR_LIGHTPROBE = 2,
    EGUID_B_FOR_TEXTUREMAPPING = 3,   // for InteractiveMode, use FTextureMappingInput & FTextureMappingOutput
    EGUID_B_FOR_OCCLUSION = 4,
    EGUID_B_FOR_LOCALTRANSFER = 5,
    EGUID_D_FOR_PACKING = 0xFFFFFFFF,
};

template<int MaxSHOrder> struct alignas(16) TSHVec
{
    float V[MaxSHOrder * MaxSHOrder];
    TSHVec& operator+=(const TSHVec& B)
    {
        for (int i = 0; i < MaxSHOrder * MaxSHOrder; ++i)
        {
            V[i] += B.V[i];
        }
        return *this;
    }
};

template<int MaxSHOrder> struct TSHRGB
{
    TSHVec<MaxSHOrder> R;
    TSHVec<MaxSHOrder> G;
    TSHVec<MaxSHOrder> B;
    TSHRGB& operator+=(const TSHRGB& Other)
    {
        R += Other.R;
        G += Other.G;
        B += Other.B;
        return *this;
    }
};

typedef TSHRGB<2> TSHRGB2;
typedef TSHRGB<3> TSHRGB3;

template<int MaxSHOrder> struct alignas(16) TRGBTransferMatrix
{
    float3 MM[MaxSHOrder * MaxSHOrder * MaxSHOrder * MaxSHOrder];
};

typedef TRGBTransferMatrix<2> FRGBTransferMatrix2;
typedef TRGBTransferMatrix<3> FRGBTransferMatrix3;

enum EBakingJobType
{
    BAKING_JOB_NONE = 0,
    BAKING_JOB_LIGHTMAP2D = 1,
    BAKING_JOB_SDFSHADOWMAP = 2,
    BAKING_JOB_VOLUMETRICLIGHTMAP = 3,
    BAKING_JOB_PRECOMPUTED_RADIANCE_TRANSFOR = 4,
    BAKING_JOB_LOCAL_PRECOMPUTED_RADIANCE_TRANSFOR = 5,
    BAKING_JOB_UNKOWN
};

enum ELightmapFlags
{
    LIGHTMAP_FLAGS_DIRECT = 1 << 0,
    LIGHTMAP_FLAGS_INDIRECT = 1 << 1,
    LIGHTMAP_FLAGS_BILINEAR_FILTER = 1 << 10,
    LIGHTMAP_FLAGS_INTERIORMASK = 1 << 11,
    LIGHTMAP_FLAGS_PADDING = 1 << 20,
    LIGHTMAP_FLAGS_OVERRIDE_PARAMS = 1 << 21,
    LIGHTMAP_FLAGS_QUANTIZED = 1 << 22,
};

enum ELightProbeFlags
{
    LIGHTPROBE_FLAGS_INSIDE_GEOMETRY = 1 << 0,
    LIGHTPROBE_FLAGS_IN_BORDER_VOXEL = 1 << 1,
};

struct FAdaptiveSamplingParameters
{
    int AdaptiveStartBounces;
    int AdaptiveIterationStartNum;
    int AdaptiveIterationStep;
    float AdaptiveMaxError;
};

struct FLightmap2DBakingParameters
{
    int DenoiserMode;
    int IterationNum;
    int SuperSampleFactor = 1;
    int MaxDepth;
    int MaxSkyBounces;
    float PenumbraShadowFraction;
};

struct FSdfShadowBakingParameters
{
    int MaxUpsamplingFactor;
};

struct FLightProbeBakingParameters
{
    int IterationNumForLightProbe;
    int MaxDepthForLightProbe;
};

struct FAmbientOcclusionBakingParameters
{
    unsigned int bDebugAmbientOcclusion;
    unsigned int bUseAmbientOcclusion;
    float MaxAmbientOcclusion;
    float DirectAmbientOcclusionFactor;
    float IndirectAmbientOcclusionFactor;
    float OcclusionExponent;
};

struct FRayTracingBakingParameters
{
    float MinRayOffset;
    float MinNormalOffset;
    float NormalOffsetSampleRadiusScale;
    float TangentOffsetSampleRadiusScale;
};

struct FArtifactBakingParameters
{
    int bSeamFixed;
    int SeamSampleIteration;
    float SeamLambda;
    float SeamCosNormalThreshold;
    bool bFillUnmappedTexel;
#if !DAWN_PRT_BRANCH
    int DawnLightmapDenoiserDirectSmoothingFactor;
    int DawnLightmapDenoiserIndirectSmoothingFactor;
#endif
};

struct FRasterizationBakingParameters
{
    float RasterizationBias;
    float SmallestTexelRadius;
    bool bUseConservativeTexelRasterization;
    bool bUseMaxWeight;
};

struct FPrecomputedRadianceTransferParameters
{
    bool bPrecomputedRadianceTransfer;
    bool bPrecomputedLocalRadianceTransfer;
    bool bPrecomputedRadianceTransferForSurface;
};

struct FLightmap2DOverrideBakingParameters
{
    FLightmap2DBakingParameters LightmapBakingParams;
    FArtifactBakingParameters ArtifactBakingParams;
    FAmbientOcclusionBakingParameters AOBakingParams;
#if DAWN_PRT_BRANCH
    bool bPrecomputedRadianceTransferForSurface;
#endif
};

struct FLightmap2DInput
{
    FGuidInfo JobID;
    FGuidInfo MeshID;
    unsigned int Flags = 0;
    int2 Size;
    uint16 LightmapUVIndex;
    FLightmap2DOverrideBakingParameters OverrideBakingParams;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FLightmap2DSample
{
    TSHRGB2 SHVector;
    float4 IncidentLighting;
    float SHCorrection;
    float3 SkyOcclusion;
    float AOMaterialMask;
    bool bIsMapped;

    FLightmap2DSample& operator+=(const FLightmap2DSample& B)
    {
        SHVector += B.SHVector;
        IncidentLighting += B.IncidentLighting;
        SHCorrection += B.SHCorrection;
        SkyOcclusion += B.SkyOcclusion;
        AOMaterialMask += B.AOMaterialMask;
        return *this;
    }
};

struct FLightmap2DOutputHeader
{
    FGuidInfo JobID;
    FGuidInfo MeshID;
    int2 Size;
    double ExecutionTime;
};

struct FLightmap2DOutput : public FLightmap2DOutputHeader
{
    TSerializedArray<FGuidInfo> Lights;
    TSerializedArray<FLightmap2DSample> LightmapData;
#if DAWN_PRT_BRANCH
    TSerializedArray<TSHRGB2> PrecomputedTransferData;
#endif
    int JobInputIndex;   // No serialization required

    const FLightmap2DSample& operator()(unsigned int X, unsigned int Y) const
    {
        return LightmapData[Size.x * Y + X];
    }
    FLightmap2DSample& operator()(unsigned int X, unsigned int Y)
    {
        return LightmapData[Size.x * Y + X];
    }

#if DAWN_PRT_BRANCH
    const TSHRGB2& GetTransferData(unsigned int X, unsigned int Y) const
    {
        return PrecomputedTransferData[Size.x * Y + X];
    }
    TSHRGB2& GetTransferData(unsigned int X, unsigned int Y)
    {
        return PrecomputedTransferData[Size.x * Y + X];
    }
#endif

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        Lights.Serialize(Context);
        LightmapData.Serialize(Context);
#if DAWN_PRT_BRANCH
        PrecomputedTransferData.Serialize(Context);
#endif
    }
};

struct FQuantizedLightmap2DSample
{
    uint8 Coverage;
    uint8 Coefficients[LM_NUM_STORED_LIGHTMAP_COEF][4];
    FSkyOcclusionType SkyOcclusion[4];
    uint8 AOMaterialMask;
};

struct FQuantizedLightmap2DOutput : public FLightmap2DOutputHeader
{
    float Scale[LM_NUM_STORED_LIGHTMAP_COEF][4];
    float Add[LM_NUM_STORED_LIGHTMAP_COEF][4];
    TSerializedArray<FGuidInfo> Lights;
    TSerializedArray<FQuantizedLightmap2DSample> LightmapData;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        Context.Serialize(&Scale);
        Context.Serialize(&Add);
        Lights.Serialize(Context);
        LightmapData.Serialize(Context);
    }
};

struct FLightmapOcclusionSample
{
    FSkyOcclusionType SkyOcclusion[4];
    uint8 AOMaterialMask;
    uint8 SkyVisibilty;
    uint8 Coverage;
};

struct FLightmapOcclusionOutput : public FLightmap2DOutputHeader
{
    TSerializedArray<FLightmapOcclusionSample> LightmapData;
    int JobInputIndex;   // No serialization required

    const FLightmapOcclusionSample& operator()(unsigned int X, unsigned int Y) const
    {
        return LightmapData[Size.x * Y + X];
    }
    FLightmapOcclusionSample& operator()(unsigned int X, unsigned int Y)
    {
        return LightmapData[Size.x * Y + X];
    }

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        LightmapData.Serialize(Context);
    }
};

struct FLightmapOcclusionGroupOutput
{
    FGuidInfo JobID;
    TCustomSerializedArray<FLightmapOcclusionOutput> LightmapOcclusionOutputs;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        LightmapOcclusionOutputs.Serialize(Context);
    }
};

struct FSDFShadowInputHeader
{
    FGuidInfo JobID;
    FGuidInfo MeshID;
    unsigned int Flags = 0;
    int2 Size;
    int UpsampleFactor = 1;
    uint16 LightmapUVIndex;
};

struct FSDFShadowInput : public FSDFShadowInputHeader
{
    TSerializedArray<FGuidInfo> Lights;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FSDFShadowInputHeader>(this);
        Lights.Serialize(Context);
    }
};

struct FLocalTransferInputHeader
{
    FGuidInfo JobID;
    FGuidInfo MeshID;
    unsigned int Flags = 0;
    int2 Size;
    uint16 LightmapUVIndex;
};

struct FLocalTransferInput : public FLocalTransferInputHeader
{
    TSerializedArray<FGuidInfo> LightGroups;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLocalTransferInputHeader>(this);
        LightGroups.Serialize(Context);
    }
};

struct FLocalTransferSample
{
    float3 LocalTransferIrradiance;
    FLocalTransferSample operator-(const FLocalTransferSample& SampleB) const
    {
        return {LocalTransferIrradiance - SampleB.LocalTransferIrradiance};
    }

    FLocalTransferSample operator*(const float& Scalar) const
    {
        return {LocalTransferIrradiance * Scalar};
    }
};

struct FLocalTransferLightMap
{
    FGuidInfo LightGroupID;
    int2 Size;
    TSerializedArray<FLocalTransferSample> LightData;

    const FLocalTransferSample& operator()(unsigned int X, unsigned int Y) const
    {
        return LightData[Size.x * Y + X];
    }
    FLocalTransferSample& operator()(unsigned int X, unsigned int Y)
    {
        return LightData[Size.x * Y + X];
    }

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&LightGroupID);
        LightData.Serialize(Context);
    }
};

struct FLocalTranferLightMapOutput : public FLightmap2DOutputHeader
{
    TCustomSerializedArray<FLocalTransferLightMap> LightMaps;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        LightMaps.Serialize(Context);
    }
};


struct FSDFShadowInfo
{
    float Distance;
    float PenumbraSize;
    bool bIsMapped;

    FSDFShadowInfo operator-(const FSDFShadowInfo& SampleB) const
    {
        return {Distance - SampleB.Distance, PenumbraSize - SampleB.PenumbraSize, bIsMapped};
    }

    FSDFShadowInfo operator*(const float& Scalar) const
    {
        return {Distance * Scalar, PenumbraSize * Scalar, bIsMapped};
    }
};

struct FSDFShadowMap
{
    FGuidInfo LightID;
    int2 Size;
    TSerializedArray<FSDFShadowInfo> ShadowData;

    const FSDFShadowInfo& operator()(unsigned int X, unsigned int Y) const
    {
        return ShadowData[Size.x * Y + X];
    }
    FSDFShadowInfo& operator()(unsigned int X, unsigned int Y)
    {
        return ShadowData[Size.x * Y + X];
    }

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&LightID);
        ShadowData.Serialize(Context);
    }
};

struct FSDFShadowOutput : public FLightmap2DOutputHeader
{
    TCustomSerializedArray<FSDFShadowMap> ShadowMaps;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        ShadowMaps.Serialize(Context);
    }
};

struct FQuantizedSDFShadowInfo
{
    uint8 Distance;
    uint8 PenumbraSize;
    uint8 Coverage;
};

struct FQuantizedSDFShadowMap
{
    FGuidInfo LightID;
    TSerializedArray<FQuantizedSDFShadowInfo> ShadowData;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&LightID);
        ShadowData.Serialize(Context);
    }
};

struct FQuantizedSDFShadowOutput : public FLightmap2DOutputHeader
{
    TCustomSerializedArray<FQuantizedSDFShadowMap> ShadowMaps;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FLightmap2DOutputHeader>(this);
        ShadowMaps.Serialize(Context);
    }
};

struct FSDFShadowGroupInput
{
    // guid to identify SDF shadow group
    FGuidInfo JobID;
    // grouped size for this group
    int GroupSize;
    // Upsample factor for this group
    int UpsampleFactor;
    // series of shadow job indices
    TSerializedArray<int> SDFShadowJobIndices;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&GroupSize);
        Context.Serialize(&UpsampleFactor);
        SDFShadowJobIndices.Serialize(Context);
    }
};

struct FSDFShadowGroupOutput
{
    FGuidInfo JobID;
    TCustomSerializedArray<FSDFShadowOutput> SDFShadowOutputs;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        SDFShadowOutputs.Serialize(Context);
    }
};

struct FShadowDepthMapInput
{
    FGuidInfo JobID;
    FGuidInfo LightID;
    float ShadowResolutionScale;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FShadowDepthMap
{
    FGuidInfo LightID;
    int2 Size;
    Matrix4x4 WorldToLight;
    TSerializedArray<float> ShadowMapData;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&LightID);
        Context.Serialize(&Size);
        Context.Serialize(&WorldToLight);
        ShadowMapData.Serialize(Context);
    }
};

struct FShadowDepthMapOutput
{
    FGuidInfo JobID;
    FShadowDepthMap ShadowMap;
    double ExecutionTime;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&ExecutionTime);
        ShadowMap.Serialize(Context);
    }
};

struct FLightTransferMatrixInfo
{
    FGuidInfo LightGuid;
    TSerializedArray<FRGBTransferMatrix3> TransferMatrices;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&LightGuid);
        TransferMatrices.Serialize(Context);
    }
};

struct FLightTransferVectorInfo
{
    float4 SamplePosition;
    TSerializedArray<TSHRGB3> TransferVectors;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&SamplePosition);
        TransferVectors.Serialize(Context);
    }
};

struct FLightLocalTransferVectorInfo
{
    TSerializedArray<TSHRGB3> LocalTransferVectors;
    TSerializedArray<FGuidInfo> LocalTransferGroupIDs;

    void Serialize(FImportExportContext& Context)
    {
        LocalTransferVectors.Serialize(Context);
        LocalTransferGroupIDs.Serialize(Context);
    }
};

template<typename SampleType> struct TLightProbeInfo
{
    float4 PostionAndRadius;
    SampleType SampleValue;
    float3 SkyOcclusion;   // panlele: ue to ce
    float MinDistanceToSurface;
    float DirectionalLightShadowing;
    unsigned int Flags;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&PostionAndRadius);
        Context.Serialize(&SkyOcclusion);
        Context.Serialize(&MinDistanceToSurface);
        Context.Serialize(&DirectionalLightShadowing);
        Context.Serialize(&Flags);
        SampleValue.Serialize(Context);
    }
};

typedef TLightProbeInfo<TSHRGB3> FLightProbeInfo;

struct FVolumetricLightmapInput
{
    FGuidInfo JobID;
    float3 WorldPostion;
    float3 CellSize;
    int3 BrickSize;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FAdaptiveBrickData
{
    FGuidInfo IntersectingLevelGuid;
    int TreeDepth;
    int3 IndirectionTexturePosition;
    float AverageClosestGeometryDistance;
    TSerializedArray<FLightProbeInfo> Cells;
    TCustomSerializedArray<FLightTransferMatrixInfo> LightTransferMatrices;
    TCustomSerializedArray<FLightTransferVectorInfo> LightTransferVectors;
#if DAWN_PRT_BRANCH
    TCustomSerializedArray<FLightLocalTransferVectorInfo> LightLocalTransferVectors;
    TSerializedArray<float> CellOctahedrons;
#endif

    void Serialize(FImportExportContext& Context)
    {
    #if DAWN_PRT_BRANCH
        Context.Serialize(&IntersectingLevelGuid);
    #endif
        Context.Serialize(&TreeDepth);
        Context.Serialize(&IndirectionTexturePosition);
        Context.Serialize(&AverageClosestGeometryDistance);
        Cells.Serialize(Context);
        LightTransferMatrices.Serialize(Context);
        LightTransferVectors.Serialize(Context);
    #if DAWN_PRT_BRANCH
        LightLocalTransferVectors.Serialize(Context);
        CellOctahedrons.Serialize(Context);
    #endif
    }
};

struct FAdaptiveVolumetricLightmapOutput
{
    FGuidInfo JobID;
    TCustomSerializedArray<FAdaptiveBrickData> BrickData;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        BrickData.Serialize(Context);
    }
};

struct FLightProbeInput
{
    FGuidInfo JobID;
    FGuidInfo LevelID;
    TSerializedArray<float4> SamplePositions;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&LevelID);
        SamplePositions.Serialize(Context);
    }
};

struct FLightProbeOutput
{
    FGuidInfo JobID;
    FGuidInfo LevelID;
    TSerializedArray<FLightProbeInfo> LightProbes;
#if DAWN_PRT_BRANCH
    TSerializedArray<FRGBTransferMatrix3> TransferMatrices;
    TCustomSerializedArray<FLightLocalTransferVectorInfo> LocalTransferVectors;
#endif

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&LevelID);
        LightProbes.Serialize(Context);
    #if DAWN_PRT_BRANCH
        TransferMatrices.Serialize(Context);
        LocalTransferVectors.Serialize(Context);
    #endif
    }
};

struct FSparseLightProbeOutput
{
    FGuidInfo JobID;
    TCustomSerializedArray<FLightProbeOutput> LightProbesForLevel;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        LightProbesForLevel.Serialize(Context);
    }
};

struct FMappingGroupInput
{
    FGuidInfo JobID;
    int GroupIndex;
    int GroupedSize;
    TSerializedArray<int3> MappingOffsets;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&GroupIndex);
        Context.Serialize(&GroupedSize);
        MappingOffsets.Serialize(Context);
    }
};

struct FLightmap2DGroupOutput
{
    FGuidInfo JobID;
    TCustomSerializedArray<FLightmap2DOutput> Lightmap2DOutputs;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Lightmap2DOutputs.Serialize(Context);
    }
};

struct FTextureMappingInput
{
    FGuidInfo JobID;
    unsigned int JobFlags = 0;
    FLightmap2DInput Lightmap2DInput;
    FSDFShadowInput ShadowMapInput;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Context.Serialize(&JobFlags);
        Lightmap2DInput.Serialize(Context);
        ShadowMapInput.Serialize(Context);
    }
};

struct FTextureMappingOutput
{
    FGuidInfo JobID;
    FQuantizedLightmap2DOutput Lightmap2DOutput;
    FQuantizedSDFShadowOutput ShadowMapOutput;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&JobID);
        Lightmap2DOutput.Serialize(Context);
        ShadowMapOutput.Serialize(Context);
    }
};

struct FBakingJobParameters
{
    unsigned int bUseFastVoxelization;
    FLightmap2DBakingParameters Lightmap2DParameter;
    FSdfShadowBakingParameters SdfShadowParameter;
    FAdaptiveSamplingParameters AdaptiveSamplingParameter;
    FAmbientOcclusionBakingParameters AmbientOcclusionParameter;
    FLightProbeBakingParameters LightProbeParameter;
    FRayTracingBakingParameters RayTracingParameter;
    FArtifactBakingParameters ArtifactParameter;
    FRasterizationBakingParameters RasterizationParameter;
    #if DAWN_PRT_BRANCH
    FPrecomputedRadianceTransferParameters PrecomputedTransferParameters;
    #endif

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FAdaptiveVolumetricLightmapParameters
{
    int3 TopLevelGridSize;
    float3 VolumeMin;
    float3 VolumeSize;
    int BrickSize;
    float SurfaceLightmapMinTexelsPerVoxelAxis;
    float VoxelizationCellExpansionForSurfaceGeometry;
    float VoxelizationCellExpansionForVolumeGeometry;
    int MaxRefinementLevels;
    float WindowingTargetLaplacian;
    float MinBrickError;
    float VoxelizationCellExpansionForLights;
    float LightBrightnessSubdivideThreshold;
    bool bCullBricksBelowLandscape;
    #if DAWN_PRT_BRANCH
    int OctahedronResolution;
    #else
    bool bPrecomputedRadianceTransfer;
    bool bPrecomputedLocalRadianceTransfer;
    #endif

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FSparseVolumetricSamplesParameters
{
    int NumSurfaceSampleLayers = 2;
    float SurfaceLightSampleSpacing = 300;
    float FirstSurfaceSampleLayerHeight = 50;
    float SurfaceSampleLayerHeightSpacing = 250;
    float DetailVolumeSampleSpacing = 300;
    float VolumeLightSampleSpacing = 3000;
    int MaxVolumeSamples = 250000;
    int MaxSurfaceLightSamples = 500000;
    bool bUseMaxSurfaceSampleNum = true;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FShadowSettingParameters
{
    int ApproximateHighResTexelsPerMaxTransitionDistance;
    float MaxTransitionDistanceWorldSpace;
    int MinDistanceFieldUpsampleFactor;
    bool bAllowSignedDistanceFieldShadows;

    int StaticShadowDepthMapMaxSamples;
    int StaticShadowDepthMapSuperSampleFactor;
    float StaticShadowDepthMapTransitionSampleDistanceX;
    float StaticShadowDepthMapTransitionSampleDistanceY;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(this);
    }
};

struct FPrecomputedVisibilityParameterHeader
{
    int NumCellDistributionBuckets{800};
    int MinMeshSamples{14};
    int MaxMeshSamples{40};
    float MeshBoundsScale{1.2f};
    float CellSize{200.f};
    float PlayAreaHeight{220.f};
    bool bPlaceCellsOnOpaqueOnly{true};
    bool bPlaceCellsOnlyAlongCameraTracks{false};
};

struct FPrecomputedVisibilityParameters : public FPrecomputedVisibilityParameterHeader
{
    TSerializedArray<float3> CameraTrackPositions;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize<FPrecomputedVisibilityParameterHeader>(this);
        CameraTrackPositions.Serialize(Context);
    }
};

struct FBakingDebugInput_1_0_0
{
    FGuidInfo MeshID;
    int SizeX;
    int SizeY;
    int LocalX;
    int LocalY;
};

struct FBakingDebugInput : public FBakingDebugInput_1_0_0
{
    float3 CameraPosition;
    float3 LookatPosition;

    void Serialize(FImportExportContext& Context)
    {
        if (Context.Version >= EGPUBakingVersion::GPUBAKING_EXPORT_VERSION_1_0_1)
        {
            Context.Serialize(this);
        }
        else
        {
            Context.Serialize<FBakingDebugInput_1_0_0>(this);
        }
    }
};

struct FPathVertex
{
    float3 Position;
    float3 Color;
    unsigned int Bounces;
    unsigned int bValid;
};

struct FBakingJobInputs
{
    FBakingJobParameters BakingParameters;
    FAdaptiveVolumetricLightmapParameters AdaptiveVolumetricLightmapParameter;
    FSparseVolumetricSamplesParameters SparseVolumetricSamplesParameter;
    FShadowSettingParameters ShadowSettingParameter;
    FPrecomputedVisibilityParameters PrecomputedVisibilityParameter;
    FBakingDebugInput DebugInput;
    TCustomSerializedArray<FLightmap2DInput> Lightmap2DJobs;
    TCustomSerializedArray<FSDFShadowInput> SDFShadowJobs;
    #if DAWN_PRT_BRANCH
    TCustomSerializedArray<FLocalTransferInput> LocalTransferJobs;
    #endif
    TCustomSerializedArray<FSDFShadowGroupInput> SDFShadowGroupJobs;
    TCustomSerializedArray<FShadowDepthMapInput> ShadowDepthMapJobs;
    TCustomSerializedArray<FVolumetricLightmapInput> VolumetricLightmapInputJobs;
    TCustomSerializedArray<FLightProbeInput> LightProbeJobs;
    TSerializedArray<FGuidInfo> VisibilityBucketGuids;
    TSerializedArray<FGuidInfo> VolumetricLightmapTaskGuids;
    TCustomSerializedArray<FMappingGroupInput> MappingGroupJobs;
    TCustomSerializedArray<FTextureMappingInput> TextureMappingJobs;

    #if DAWN_PRT_BRANCH
    TCustomSerializedArray<FLightmap2DInput> LightmapOcclusionJobs;
    TCustomSerializedArray<FMappingGroupInput> OcclusionGroupJobs;
    #endif

    void Serialize(FImportExportContext& Context)
    {
        if (Context.Version >= EGPUBakingVersion::GPUBAKING_EXPORT_VERSION_1_0_1)
        {
            Serialize_1_0_1(Context);
        }
        else
        {
            Serialize_1_0_0(Context);
        }
    }

    void Serialize_1_0_0(FImportExportContext& Context)
    {
        BakingParameters.Serialize(Context);
        AdaptiveVolumetricLightmapParameter.Serialize(Context);
        SparseVolumetricSamplesParameter.Serialize(Context);
        ShadowSettingParameter.Serialize(Context);
        PrecomputedVisibilityParameter.Serialize(Context);
        DebugInput.Serialize(Context);
        Lightmap2DJobs.Serialize(Context);
        SDFShadowJobs.Serialize(Context);
        #if DAWN_PRT_BRANCH
        LocalTransferJobs.Serialize(Context);
        #endif
        SDFShadowGroupJobs.Serialize(Context);
        ShadowDepthMapJobs.Serialize(Context);
        VolumetricLightmapInputJobs.Serialize(Context);
        LightProbeJobs.Serialize(Context);
        VisibilityBucketGuids.Serialize(Context);
        VolumetricLightmapTaskGuids.Serialize(Context);
        MappingGroupJobs.Serialize(Context);

        #if DAWN_PRT_BRANCH
        LightmapOcclusionJobs.Serialize(Context);
        OcclusionGroupJobs.Serialize(Context);
        #endif
    }
    void Serialize_1_0_1(FImportExportContext& Context)
    {
        Serialize_1_0_0(Context);
        TextureMappingJobs.Serialize(Context);
    }
};

struct FBakingJobOutputs
{
    TCustomSerializedArray<FLightmap2DOutput> Lightmap2DOutputs;
    TCustomSerializedArray<FSDFShadowOutput> SDFShadowOutputs;
    TCustomSerializedArray<FShadowDepthMapOutput> ShadowDepthMapOutputs;
    TCustomSerializedArray<FAdaptiveVolumetricLightmapOutput> VolumetricLightmapOutputs;
    TCustomSerializedArray<FLightProbeOutput> LightProbeOutputs;
    TCustomSerializedArray<FLightmap2DGroupOutput> Lightmap2DGroupOutputs;
    TCustomSerializedArray<FSDFShadowGroupOutput> SDFShadowGroupOutputs;
    #if DAWN_PRT_BRANCH
    TCustomSerializedArray<FLocalTranferLightMapOutput> LocalTransferOutputs;
    #endif

    void Serialize(FImportExportContext& Context)
    {
        Lightmap2DOutputs.Serialize(Context);
        SDFShadowOutputs.Serialize(Context);
        ShadowDepthMapOutputs.Serialize(Context);
        VolumetricLightmapOutputs.Serialize(Context);
        LightProbeOutputs.Serialize(Context);
        Lightmap2DGroupOutputs.Serialize(Context);
        SDFShadowGroupOutputs.Serialize(Context);
        #if DAWN_PRT_BRANCH
        LocalTransferOutputs.Serialize(Context);
        #endif
    }
};

struct FBakingJobs
{
    FSceneInfo Scene;
    FBakingJobInputs Input;
    FBakingJobOutputs Output;
};

NS_GPUBAKING_END
