#pragma once 

#include "BakingCoreDefine.h" 
#include "SerializeUtils.h" 
#include "SwarmInterface.h"

NS_GPUBAKING_BEGIN

constexpr int MAX_SKY_LIGHT_SH_BAND = 3;

constexpr int MAX_LIGHT_PROFILE_SIZE = 256;

constexpr int MAX_RELAVANT_LIGHT_NUM = 64;

constexpr int MAX_MESH_LOD_INDEX = 7;

constexpr int LIGHT_FLAG_NONE = 0;
constexpr int LIGHT_FLAG_DISTANCE_FIELD_SHADOW = 1;
constexpr int LIGHT_FLAG_DIRECT_LIGHTING = 1 << 1;
constexpr int LIGHT_FLAG_INDIRECT_LIGHTING = 1 << 2;
constexpr int LIGHT_FLAG_DIRECT_LIGHTING_PROBE = 1 << 3;
constexpr int LIGHT_FLAG_INDIRECT_LIGHTING_PROBE = 1 << 4;
constexpr int LIGHT_FLAG_INVERSE_SQUARED = 1 << 5;
constexpr int LIGHT_FLAG_HASSTATICLIGHTING = 1 << 6;
constexpr int LIGHT_FLAG_CAST_DIRECT_SHADOW = 1 << 7;

struct FGuidInfo {
	unsigned int A = 0;
	unsigned int B = 0;
	unsigned int C = 0;
	unsigned int D = 0;

	friend bool operator==(const FGuidInfo& X, const FGuidInfo& Y)
	{
		return ((X.A ^ Y.A) | (X.B ^ Y.B) | (X.C ^ Y.C) | (X.D ^ Y.D)) == 0;
	}
	friend bool operator!=(const FGuidInfo& X, const FGuidInfo& Y)
	{
		return ((X.A ^ Y.A) | (X.B ^ Y.B) | (X.C ^ Y.C) | (X.D ^ Y.D)) != 0;
	}
	friend bool operator<(const FGuidInfo& X, const FGuidInfo& Y)
	{
		return	((X.A < Y.A) ? true : ((X.A > Y.A) ? false :
			((X.B < Y.B) ? true : ((X.B > Y.B) ? false :
			((X.C < Y.C) ? true : ((X.C > Y.C) ? false :
				((X.D < Y.D) ? true : ((X.D > Y.D) ? false : false)))))))); //-V583
	}

	inline bool IsValid() const {
        return A || B || C || D;
	}

	inline std::string ToString() const {
		return std::to_string(A)
			+ "-" + std::to_string(B)
			+ "-" + std::to_string(C)
			+ "-" + std::to_string(D)
			;
	}
	struct FGuidHasher
	{
		std::hash<int> h;
		size_t operator ()(const FGuidInfo& Guid) const
		{
			return h(Guid.D);
		}
	};
};

inline FGuidInfo ToGuid(const NSwarm::FGuid& Input) {
	return { Input.A,Input.B,Input.C,Input.D };
}

inline NSwarm::FGuid ToGuid(const FGuidInfo& Input) {
	return NSwarm::FGuid(Input.A, Input.B, Input.C, Input.D);
}

class FSHAHash
{
public:
	alignas(uint32) uint8 Hash[20];

	FSHAHash()
	{
		memset(Hash, 0, sizeof(Hash));
	}
};

struct FSHAHashInfo
{
	alignas(unsigned int) unsigned char Hash[20];

	FSHAHashInfo()
	{
		memset(Hash, 0, sizeof(Hash));
	}
	struct FGuidHasher
	{
		std::hash<long long> h;
		size_t operator ()(const FSHAHashInfo& Guid) const
		{
			static_assert(sizeof(long long) < sizeof(Guid.Hash), "FSHAHashInfo Hash");
			long long Data = 0;
			memcpy(&Data,&Guid.Hash[0],sizeof(long long));
			return h(Data);
		}
	};

	void InitRandom()
	{
		for (int i = 0; i < 20; i++)
		{
			Hash[i] = rand() % 255;
		}
	}
};

inline void ToHash(const FSHAHash& Input, FSHAHashInfo& Output) {
	static_assert(sizeof(Input.Hash) == sizeof(Output.Hash), "ToHash");
	memcpy(&Output.Hash[0], &Input.Hash[0], sizeof(Input.Hash));
}

inline void ToHash(const FSHAHashInfo& Input, FSHAHash& Output) {
	static_assert(sizeof(Input.Hash) == sizeof(Output.Hash), "ToHash");
	memcpy(&Output.Hash[0], &Input.Hash[0], sizeof(Input.Hash));
}

enum class EBlendMode {
   BLEND_MODE_OPAQUE				=0,
   BLEND_MODE_ALPHA_COMPOSITE		=1,
   BLEND_MODE_TRANSLUCENT			=2,
   BLEND_MODE_ADDITIVE				=3,
   BLEND_MODE_MODULATE				=4,
   BLEND_MODE_MASKED				=1,
};

enum class FLightType {
	LIGHT_SKY = 0,
	LIGHT_POINT = 1,
	LIGHT_DIRECTIONAL = 2,
	LIGHT_RECT = 3,
	LIGHT_SPOT = 4,
	LIGHT_MAX_NUM,
};

struct FLightProfileInfo {
	unsigned char Profile[MAX_LIGHT_PROFILE_SIZE * MAX_LIGHT_PROFILE_SIZE];
};

struct alignas(16) FLightInfo {

	// Light-specific
	float4			Dimensions;

	// Geometry
	float3			Position;
	float3			Normal;
	float3			Tangent;
	float3			dPdu;
	float3			dPdv;

	// Color
	float3			Color;
	float3			IndirectColor;

	// Light-specific
	float			Attenuation;
	float			RectLightBarnCosAngle;
	float			RectLightBarnLength;
	unsigned int	Type;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FLightInfo>(this);
	}
};

struct FLightFullInfo
{
	FLightInfo		LightData;
	FGuidInfo		LightGuid;
	unsigned int	LightFlags;
	float			LightPower;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FLightFullInfo>(this);
	}
};

struct FSkyLightHeaderInfo {
	float4	Color;
	int4	MipDimensions;
	float4	EnvColor;
};

struct FSkyLightInfo {
	FSkyLightHeaderInfo			SkyData;
	TSerializedArray<float4>	EnvCube;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FSkyLightHeaderInfo>(&SkyData);
		EnvCube.Serialize(Context);
	}
};


struct FTexture2DInfoHeader {
	unsigned int SizeX;
	unsigned int SizeY;
	unsigned int WrapMode;
	unsigned int Format;
};

struct FTexture2DInfo : FTexture2DInfoHeader {

	TSerializedArray<float4> Colors;

	float4 SampleColor(const float2& Texcoord) const
	{
		int X = int(Texcoord.x * SizeX + 0.5f) % SizeX;
		int Y = int(Texcoord.y * SizeY + 0.5f) % SizeY;
		int Index = SizeX * Y + X;
		return Colors[Index];
	}

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FTexture2DInfoHeader>(this);
		Colors.Serialize(Context);
	}
};

enum EMaterialInfoFlags {
	MATERIAL_FLAGS_TWOSIDE = 1 << 0,
	MATERIAL_FLAGS_SUBSURFACE = 1 << 1,
	MATERIAL_FLAGS_SHADOW_AS_MASK = 1 << 2,
    MATERIAL_FLAGS_SAMPLE_WITH_CUBEMAP = 1 << 3,
};

struct FMaterialInfoHeader {
	FSHAHashInfo	Hash;
	int				ShadingModelID = 0;
	unsigned int	BlendMode;
	unsigned int	bTwoSided;
	unsigned int	Flags = 0;
	float			OpacityMaskClipValue;
};

struct FMaterialInfo : public FMaterialInfoHeader {
	FTexture2DInfo DiffuseTexture;
	FTexture2DInfo TransmissionTexture;
	FTexture2DInfo EmissiveTexture;
	FTexture2DInfo NormalTexture;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FMaterialInfoHeader>(this);
		DiffuseTexture.Serialize(Context);
		TransmissionTexture.Serialize(Context);
		EmissiveTexture.Serialize(Context);
		NormalTexture.Serialize(Context);
	}
};

struct FSceneBounds {
	float3	Origin;
	float3	BoxExtent;
	float	SphereRadius;
};

struct FSceneBoxBounds
{
	float3 Min;
	float3 Max;

	FSceneBoxBounds() {

	}

	FSceneBoxBounds(const float3& Min, const float3& Max):Min(Min),Max(Max){

	}

	FSceneBoxBounds(const float3* Points,int Count) {
		Min = Max = Points[0];
		for (int i = 1; i < Count; ++i) {
			const float3& Other = Points[i];
			Min.x = fmin(Min.x, Other.x);
			Min.y = fmin(Min.y, Other.y);
			Min.z = fmin(Min.z, Other.z);
			Max.x = fmax(Max.x, Other.x);
			Max.y = fmax(Max.y, Other.y);
			Max.z = fmax(Max.z, Other.z);
		}
	}

	float3 GetSize() const {
		return Max - Min;
	}

	float3 GetExtent() const {
		return 0.5f * (Max - Min);
	}

	FSceneBoxBounds ExpandBy(float W) const {
		return FSceneBoxBounds(Min - float3(W, W, W), Max + float3(W, W, W));
	}
	FSceneBoxBounds ExpandBy(const float3& V) const
	{
		return FSceneBoxBounds(Min - V, Max + V);
	}
	bool Intersect(const FSceneBoxBounds& Other) const
	{
		if ((Min.x > Other.Max.x) || (Other.Min.x > Max.x))
		{
			return false;
		}

		if ((Min.y > Other.Max.y) || (Other.Min.y > Max.y))
		{
			return false;
		}

		if ((Min.z > Other.Max.z) || (Other.Min.z > Max.z))
		{
			return false;
		}

		return true;
	}
};

enum EMeshInstanceFlags {
	MESH_INSTANCE_FLAGS_SHADOWCAST = 1 << 0,
	MESH_INSTANCE_FLAGS_TWOSIDE = 1 << 1,
	MESH_INSTANCE_FLAGS_TRANSIENT = 1 << 2,
	MESH_INSTANCE_FLAGS_USE_VERTEX_NORMAL = 1 << 3,
	MESH_INSTANCE_FLAGS_REVERSE_WINDING = 1 << 4,
	MESH_INSTANCE_FLAGS_SHARE_LOD0 = 1 << 5,
    MESH_INSTANCE_FLAGS_BACKFACE_CULLING = 1 << 6,
	MESH_INSTANCE_FLAGS_LANDSCAPE = 1 << 10,
	MESH_INSTANCE_FLAGS_SPLINE = 1 << 11,
	MESH_INSTANCE_FLAGS_USE_TWOSIDE_LIGHTING = 1 << 12,
	MESH_INSTANCE_FLAGS_USE_EMISSION_LIGHTING = 1 << 13,
	MESH_INSTANCE_FLAGS_IS_OUTDOOR = 1 << 20,
};

struct FMeshElementInfo {
	unsigned int StartIndex;
	unsigned int PrimitiveCount;
	unsigned int MaterialIndex;
	unsigned int Flags;
};

struct FMeshInfoHeader {
	FGuidInfo		Guid;
	unsigned int	LODIndex;
	unsigned int	NumTriangles;
	unsigned int	NumVertices;
	unsigned int	NumElements;
	int				LightmapUVIndex;
	float			LightmapUVDensity;
	float			LightmapTexelDensity;
};

struct FMeshTexcoordBuffer {
	TSerializedArray<float2> TexcoordBuffer;

	void Serialize(FImportExportContext& Context)
	{
		TexcoordBuffer.Serialize(Context);
	}
};

struct FMeshInfo : public FMeshInfoHeader {	
	TSerializedArray<FMeshElementInfo>			ElementInfos;
	TSerializedArray<unsigned short>			Index16BitBuffer;
	TSerializedArray<unsigned int>				Index32BitBuffer;
	TSerializedArray<float4>					VertexBuffer;
	TSerializedArray<float4>					NormalBuffer;
	TSerializedArray<float4>					TangentBuffer;
	TSerializedArray<float4>					BiTangentBuffer;
	TCustomSerializedArray<FMeshTexcoordBuffer> TexcoordBuffers;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FMeshInfoHeader>(this);
		Index16BitBuffer.Serialize(Context);
		ElementInfos.Serialize(Context);
		VertexBuffer.Serialize(Context);
		NormalBuffer.Serialize(Context);
		TangentBuffer.Serialize(Context);
		BiTangentBuffer.Serialize(Context);
		TexcoordBuffers.Serialize(Context);
		if (Context.Version >= EGPUBakingVersion::GPUBAKING_EXPORT_VERSION_1_0_1) {
			if (Index16BitBuffer.Num() == 0 && VertexBuffer.Num () > 0) {
				Index32BitBuffer.Serialize(Context);
			}
		}
	}
};

struct FMeshIntanceInfoHeader {
	FGuidInfo		LevelGuid;
	FGuidInfo		Guid;
	Matrix4x4		Transform;
	Matrix4x4		InverseTransform;
	Matrix4x4		InverseTransposeTransform;
	FSceneBoxBounds BoundingBox;
	unsigned int	MeshIndex;	
	unsigned int	TexcoordIndex;
	unsigned int	Flags;
	float			DiffuseBoost;
	float			EmissiveBoost;

#ifdef ENABLE_PRT_NEW_BRANCH
    unsigned int MeshInstanceVisibilityMask{0x00FF};            // Geometry visibility mask(LOD considered)
    unsigned int MeshInstanceVisibilityMaskInHLODScene{0};   // Geometry visibility mask in HLOD Scene
    int32 LODLevelInHLODScene{-1};
#endif
};

struct FMeshInstanceInfo : public FMeshIntanceInfoHeader {
	TSerializedArray<unsigned int> MaterialOverrides;
	TSerializedArray<unsigned int> RelevantLights;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FMeshIntanceInfoHeader>(this);
		MaterialOverrides.Serialize(Context);
		RelevantLights.Serialize(Context);
	}
};

struct FLandscapeInfoHeader {
	FGuidInfo		LevelGuid;
	FGuidInfo		Guid;
	Matrix4x4		Transform;
	FSceneBoxBounds BoundingBox;
	unsigned int	Flags;
	float			DiffuseBoost;
	float			EmissiveBoost;

	int				NumTriangles;
	int				ComponentSizeQuads;
	float			LightMapRatio;
	int				ExpandQuadsX;
	int				ExpandQuadsY;

#ifdef ENABLE_PRT_NEW_BRANCH
    unsigned int MeshInstanceVisibilityMask{0x00FF};               // Geometry visibility mask(LOD considered)
    unsigned int MeshInstanceVisibilityMaskInHLODScene{0};   // Geometry visibility mask in HLOD Scene
    int32 LODLevelInHLODScene{-1};
#endif
};

struct FLandscapeInfo : public FLandscapeInfoHeader {
	TSerializedArray<unsigned int>	MaterialOverrides;
	TSerializedArray<unsigned int>	RelevantLights;
	TSerializedArray<ubyte4>		HeightMap;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FLandscapeInfoHeader>(this);
		MaterialOverrides.Serialize(Context);
		RelevantLights.Serialize(Context);
		HeightMap.Serialize(Context);
	}
};

struct FSceneDensityVolume
{
	TSerializedArray<float4>	Planes;
	int2						AllowedMipLevelRange;

	void Serialize(FImportExportContext& Context)
	{
		Planes.Serialize(Context);
		Context.Serialize(&AllowedMipLevelRange);
	}
};

struct FSceneBrickVolume
{
	int3			BrickSize;
	FSceneBoxBounds	Bounds;
	FLightFullInfo	LightTemplate;

	void GenerateSamplePoints(std::vector<float3>& OutSamplePoints) const
	{
		int32 SampleNum = (BrickSize.x + 1) * (BrickSize.y + 1) * (BrickSize.z + 1);
		OutSamplePoints.resize(SampleNum);

		float3 StepSize(Bounds.GetSize().x / BrickSize.x, Bounds.GetSize().y / BrickSize.y, Bounds.GetSize().z / BrickSize.z);

		for (int32 Z = 0; Z <= BrickSize.z; ++Z)
		{
			for (int32 Y = 0; Y <= BrickSize.y; ++Y)
			{
				for (int32 X = 0; X <= BrickSize.x; ++X)
				{
					int32 SampleIndex = (BrickSize.x + 1) * (BrickSize.y + 1) * Z + (BrickSize.x + 1) * Y + X;
					float3 SamplePosition = Bounds.Min + StepSize * float3(float(X), float(Y), float(Z));
					OutSamplePoints[SampleIndex] = SamplePosition;
				}
			}
		}
	}
};

struct FSceneDetailVolume
{
	FSceneBoxBounds Bounds;
	FGuidInfo		LevelGuid;
};

struct FSceneInfoHeader
{
	FSceneBounds Bounds;
	FSceneBounds ImportanceBounds;
	unsigned int NumLights;
	unsigned int NumMeshes;
};

struct FGroupedLPRTInfo
{
    FGuidInfo GroupGuid;
    // Index Into LocalTransferLights Array
    TSerializedArray<unsigned int> LocalTransferLightIndices;

    void Serialize(FImportExportContext& Context)
    {
        Context.Serialize(&GroupGuid);
        LocalTransferLightIndices.Serialize(Context);
    }
};

struct FSceneInfo : FSceneInfoHeader {	
	TSerializedArray<FSceneBoxBounds>			ImportanceVolumes;
	TCustomSerializedArray<FSceneDensityVolume> DensityVolumes;
	TSerializedArray<FSceneBrickVolume>			BrickVolumes;
    #if DAWN_PRT_BRANCH
    TSerializedArray<float4> InteriorVolumes;
    TSerializedArray<float4> InteriorProbes;
    #endif
	TSerializedArray<FSceneDetailVolume>		DetailVolumes;
	FSkyLightInfo								SkyLight;
	TCustomSerializedArray< FLightInfo >		Lights;	
	TSerializedArray<FGuidInfo>					LightGuids;
	TSerializedArray<unsigned int>				LightFlags;
	TSerializedArray<float>						LightPowers;
	TCustomSerializedArray< FLightInfo >		LocalLights;
	TSerializedArray<FGuidInfo>					LocalLightGuids;
	TSerializedArray<unsigned int>				LocalLightFlags;

    #if DAWN_PRT_BRANCH
    TCustomSerializedArray<FLightInfo> LocalTransferLights;
    TSerializedArray<FGuidInfo> LocalTransferLightGuids;
    TSerializedArray<unsigned int> LocalTransferLightFlags;
    TSerializedArray<float> LocalTransferLightPowers;
    TCustomSerializedArray<FGroupedLPRTInfo> LocalTransferGroups;
    #endif
	TSerializedArray<FGuidInfo>					MeshGuids;
	TCustomSerializedArray< FLightProfileInfo > LightProfiles;	
	TCustomSerializedArray< FMeshInstanceInfo > MesheInstances;
	TCustomSerializedArray< FMeshInfo >			Meshes;
	TCustomSerializedArray< FLandscapeInfo >	Landscapes;
	TCustomSerializedArray<FMaterialInfo>		Materials;

	void Serialize(FImportExportContext& Context)
	{
		Context.Serialize<FSceneInfoHeader>(this);

		ImportanceVolumes.Serialize(Context);
		DensityVolumes.Serialize(Context);
		BrickVolumes.Serialize(Context);
        #if DAWN_PRT_BRANCH
        InteriorVolumes.Serialize(Context);
        InteriorProbes.Serialize(Context);
        #endif
		DetailVolumes.Serialize(Context);

		SkyLight.Serialize(Context);
		Lights.Serialize(Context);
		LightGuids.Serialize(Context);
		LightFlags.Serialize(Context);
		LightPowers.Serialize(Context);

		LocalLights.Serialize(Context);
		LocalLightGuids.Serialize(Context);
		LocalLightFlags.Serialize(Context);

        #if DAWN_PRT_BRANCH
        LocalTransferLights.Serialize(Context);
        LocalTransferLightGuids.Serialize(Context);
        LocalTransferLightFlags.Serialize(Context);
        LocalTransferLightPowers.Serialize(Context);
        LocalTransferGroups.Serialize(Context);
        #endif

		Materials.Serialize(Context);

		Meshes.Serialize(Context);
		MesheInstances.Serialize(Context);
		MeshGuids.Serialize(Context);

		Landscapes.Serialize(Context);
	}

};


NS_GPUBAKING_END