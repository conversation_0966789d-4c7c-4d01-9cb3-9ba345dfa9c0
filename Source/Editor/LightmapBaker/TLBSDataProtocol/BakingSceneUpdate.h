#pragma once 

#include "BakingSceneDefine.h" 

NS_GPUBAKING_BEGIN

enum ESceneCommandType {
	ESCENE_UPDATE_TYPE_LIGHT_INFO = 1,
	ESCENE_UPDATE_TYPE_SKY_LIGHT_INFO = 2,
	ESC<PERSON>E_UPDATE_TYPE_MATERIAL = 3,
	ESCENE_UPDATE_TYPE_MESH_INFO = 4,
	ESCENE_UPDATE_TYPE_MESH_INSTANCE = 5,
	ESCENE_UPDATE_TYPE_LANDSCAPE = 6,

	ESCENE_ADD_TYPE_LIGHT_INFO = 128,
	ESCENE_ADD_TYPE_SKY_LIGHT_INFO = 129,
	ESCENE_ADD_TYPE_MATERIAL = 130,
	ESCENE_ADD_TYPE_MESH_INFO = 131,
	ESCENE_ADD_TYPE_MESH_INSTANCE = 132,
	ESCENE_ADD_TYPE_LANDSCAPE = 133,

	ESCENE_REMOVE_TYPE_LIGHT_INFO = 256,
	ESCENE_REMOVE_TYPE_SKY_LIGHT_INFO = 257,
	ESCENE_REMOVE_TYPE_MATERIAL = 258,
	ESCENE_REMOVE_TYPE_MESH_INFO = 259,
	ESCENE_REMOVE_TYPE_MESH_INSTANCE = 260,
	ESCENE_REMOVE_TYPE_LANDSCAPE = 261,

	EJOB_ADD_TYPE_TEXTUREMAPPING = 512,
	EJOB_REMOVE_TYPE_TEXTUREMAPPING = 513,
    EJOB_UPDATE_TOKEN = 514,
};

struct FLightUpdateInfo : public FLightFullInfo
{
	TSerializedArray<FGuidInfo> RelevantMeshes;

	void Serialize(FImportExportContext& Context)
	{
		FLightFullInfo::Serialize(Context);
		RelevantMeshes.Serialize(Context);
	}
};

struct FSkyLightUpdateInfo : public FSkyLightInfo
{
	FGuidInfo	   LightGuid;

	void Serialize(FImportExportContext& Context)
	{
		FSkyLightInfo::Serialize(Context);
		Context.Serialize(&LightGuid);	
	}
};

struct FMaterialUpdateInfo : public FMaterialInfo
{
	FMaterialUpdateInfo()
	{

	}
	FMaterialUpdateInfo(const FMaterialInfo& Other) :FMaterialInfo(Other)
	{

	}
	void Serialize(FImportExportContext& Context)
	{
		FMaterialInfo::Serialize(Context);
	}
};

struct FMeshUpdateInfo : public FMeshInfo
{
	FMeshUpdateInfo()
	{

	}
	FMeshUpdateInfo(const FMeshInfo& Other):FMeshInfo(Other)
	{

	}
	void Serialize(FImportExportContext& Context)
	{
		FMeshInfo::Serialize(Context);
	}
};

struct FMeshInstanceUpdateInfo : public FMeshInstanceInfo
{
	FGuidInfo	   StaticMeshGuid;
	TSerializedArray<FGuidInfo> RelevantLightGuids;

	FMeshInstanceUpdateInfo()
	{

	}

	FMeshInstanceUpdateInfo(const FMeshInstanceInfo& Other) :FMeshInstanceInfo(Other)
	{

	}
	void Serialize(FImportExportContext& Context)
	{
		FMeshInstanceInfo::Serialize(Context);
		Context.Serialize(&StaticMeshGuid);
		RelevantLightGuids.Serialize(Context);
	}
};

struct FLandscapeUpdateInfo : public FLandscapeInfo
{
	TSerializedArray<FGuidInfo> RelevantLights;

	FLandscapeUpdateInfo()
	{

	}

	FLandscapeUpdateInfo(const FLandscapeInfo& Other) :FLandscapeInfo(Other)
	{

	}
	void Serialize(FImportExportContext& Context)
	{
		FLandscapeInfo::Serialize(Context);
		RelevantLights.Serialize(Context);
	}
};

NS_GPUBAKING_END