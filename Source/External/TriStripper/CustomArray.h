#ifndef CUSTOMARRAY_H
#define CUSTOMARRAY_H
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Source code for "Creating Efficient Triangle Strips"
// (C) 2000, <PERSON> (<EMAIL>)
//
// Version is 2.0.
//
// This is a versatile and customized import/export array class I use for a long time.
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// History	:		01/15/99: first version for Irion MAX Plug-In
//					02/05/99: GetOffset() added
//					03/xx/99: Collapse() added, self-references added
//					03/xx/99: BOOL definition added in header if not defined yet, addies-stack mecanism added
//					04/xx/99: "push" renamed to "store", for a more coherent name since people were expecting a "pop" method to be used.............
//					04/xx/99: BOOL handled as a long. Use bool (in lower case) for a real boolean value.
//					05/xx/99: heap size is now 4Kb, and allocated ram is doubled for each new block. The more you eat, the more ram you're given.
//					04/11/99: address stack is now resizable.
//					06/01/00: local memory manager and local error codes removed, CustomArray class added to IrionBasics
//
// More notes:		- always keep in mind that an CustomArray eats CUSTOMARRAY_BLOCKSIZE bytes when initialized, even if you don't use it later.
//					  That's why you may use this class for very specific reasons, or even change this #define to match your own needs.
//
//					- I know I could've used templates.
//
//					- THIS IS NOT THREAD-SAFE.
//
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Include Guard
#include "StriperIncludes.h"


#define CUSTOMARRAY_BLOCKSIZE	(4*1024)		// 4 Kb => heap size


	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	//
	//																					CustomArray Class Definition
	//
	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

class CustomArray
{
	///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
	// Structures and enums
	struct CustomBlock{
		CustomBlock()		{ Addy = nullptr; }
		~CustomBlock()		{ RELEASEARRAY((ubyte*&)Addy); }
		void*				Addy;						// Stored data
		unsigned long		Size;						// Length of stored data
		unsigned long		Max;						// Heap size
	};

	struct CustomCell{
		CustomCell()		{ NextCustomCell = nullptr; }

		struct CustomCell*	NextCustomCell;
		CustomBlock			Item;
	};

private:
	CustomCell*				mCurrentCell;				// Current block cell
	CustomCell*				mInitCell;					// First block cell

	void*					mCollapsed;					// Possible collapsed buffer

	// Management methods
	CustomArray&			CheckArray(unsigned long bytesneeded);
	CustomArray&			NewBlock(CustomCell* previouscell, unsigned long size=0);
public:
	// Constructor / destructor
	CustomArray(unsigned long startsize=CUSTOMARRAY_BLOCKSIZE);
	~CustomArray();

	// Store methods
	CustomArray&			StoreU32(unsigned int d);

	unsigned long			GetOffset();
	void*					Collapse(void* userbuffer = nullptr);
};

#endif
