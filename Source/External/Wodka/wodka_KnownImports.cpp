// Written by <PERSON><PERSON>
// I hereby place this code in the public domain

#include "wodka_KnownImports.h"
#include "wodka_WinHelper.h"
#include <string.h>
#include <locale.h>
#include <stdlib.h>
#include <stdio.h>
#include <ctype.h>
#include <cmath>
#include <assert.h>
#include <time.h>
#include <map>
#if WODKA_WINDOWS
	#include <windows.h>
	#include <float.h>
	#define strdup _strdup
#elif WODKA_MAC
	#include <libkern/OSAtomic.h>
	#include <pthread.h>
	#include <sys/mman.h>
	#include <unistd.h>
#elif WODKA_LINUX
	#include <pthread.h>
	#include <sys/mman.h>
	#include <unistd.h>
#else
	#error Unknown platform!
#endif

#define TRACE_IMPORT_CALLS 0

#if TRACE_IMPORT_CALLS
static int _trace_counter = 0;
#define TRACE(n,a) ++_trace_counter; printf("%04i %s(", _trace_counter, #n); printf a; printf(")\n")
#define TRACE2(a) printf("       "); printf a; printf("\n")
#else
#define TRACE(n,a)
#define TRACE2(a)
#endif

#pragma warning(push)
#pragma warning(disable : 4201)		// nonstandard extension used: nameless struct/union

struct SYSTEM_INFO_impl {
	union {
		DWORD  dwOemId;
		struct {
			WORD wProcessorArchitecture;
			WORD wReserved;
		};
	};
	DWORD     dwPageSize;
	void*    lpMinimumApplicationAddress;
	void*    lpMaximumApplicationAddress;
	DWORD_PTR dwActiveProcessorMask;
	DWORD     dwNumberOfProcessors;
	DWORD     dwProcessorType;
	DWORD     dwAllocationGranularity;
	WORD      wProcessorLevel;
	WORD      wProcessorRevision;
};

#pragma warning(pop)

struct LARGE_INTEGER_impl {
	DWORD LowPart;
	LONG HighPart;
};

struct FILETIME_impl {
	DWORD dwLowDateTime;
	DWORD dwHighDateTime;
};


static void WINAPI_IMPL GetSystemTimeAsFileTime_impl(FILETIME_impl* systemTimeAsFileTime)
{
	clock_t t = clock();
	systemTimeAsFileTime->dwHighDateTime = 0;
	systemTimeAsFileTime->dwLowDateTime = t;
}

static DWORD WINAPI_IMPL GetCurrentProcessId_impl()
{
	#if WODKA_WINDOWS
	return GetCurrentProcessId();
	#else
	return getpid();
	#endif
}

static DWORD WINAPI_IMPL GetCurrentThreadId_impl()
{
	#if WODKA_WINDOWS
	return GetCurrentThreadId();
	#else
	return (DWORD)(size_t)pthread_self();
	#endif
}

static DWORD WINAPI_IMPL GetTickCount_impl()
{
	return clock();
}

static DWORD WINAPI_IMPL GetVersion_impl()
{
	return 0x1db10106; // 6.1.7601, i.e. Win 7 SP1
}

typedef void (CDECL_IMPL *_PVFV)(void);
typedef int  (CDECL_IMPL *_PIFV)(void);

static void CDECL_IMPL _initterm_impl (_PVFV* pfbegin, _PVFV* pfend)
{
	while (pfbegin < pfend)
	{
		if (*pfbegin != nullptr)
			(**pfbegin)();
		++pfbegin;
	}
}

static int CDECL_IMPL _initterm_e_impl (_PIFV* pfbegin, _PIFV* pfend)
{
	int ret = 0;

	while (pfbegin < pfend)
	{
		if (*pfbegin != nullptr)
			ret = (**pfbegin)();
		++pfbegin;
	}

	return ret;
}

static void* CDECL_IMPL _CRT_RTC_INIT_impl(void *unk1, void *unk2, int unk3, int unk4, int unk5)
{
    return nullptr;
}

static void* CDECL_IMPL _CRT_RTC_INITW_impl(void *unk1, void *unk2, int unk3, int unk4, int unk5)
{
    return nullptr;
}

static int CDECL_IMPL _CrtSetCheckCount_impl(int fCheckCount)
{
    return 0;
}

#if WODKA_WINDOWS
typedef CRITICAL_SECTION CRITICAL_SECTION_impl;
#else
typedef pthread_mutex_t CRITICAL_SECTION_impl;
#endif


static void WINAPI_IMPL InitializeCriticalSection_impl(CRITICAL_SECTION_impl* cs)
{
	TRACE(InitializeCriticalSection,("%p",cs));
	#if WODKA_WINDOWS

	InitializeCriticalSection(cs);

	#else

	pthread_mutexattr_t attr;
	pthread_mutexattr_init (&attr);
	pthread_mutexattr_settype (&attr, PTHREAD_MUTEX_RECURSIVE);
	pthread_mutex_init (cs, &attr);
	pthread_mutexattr_destroy (&attr);
	#endif
}

static void WINAPI_IMPL DeleteCriticalSection_impl(CRITICAL_SECTION_impl* cs)
{
	TRACE(DeleteCriticalSection,("%p",cs));
	#if WODKA_WINDOWS
	DeleteCriticalSection(cs);
	#else
	pthread_mutex_destroy (cs);
	#endif
}

static void WINAPI_IMPL EnterCriticalSection_impl(CRITICAL_SECTION_impl* cs)
{
	TRACE(EnterCriticalSection,("%p",cs));
	#if WODKA_WINDOWS
	EnterCriticalSection(cs);
	#else
	pthread_mutex_lock (cs);
	#endif
}

static void WINAPI_IMPL LeaveCriticalSection_impl(CRITICAL_SECTION_impl* cs)
{
	TRACE(LeaveCriticalSection,("%p",cs));
	#if WODKA_WINDOWS
	LeaveCriticalSection(cs);
	#else
	pthread_mutex_unlock (cs);
	#endif
}

static void WINAPI_IMPL InitializeCriticalSectionAndSpinCount_impl(CRITICAL_SECTION_impl* cs, DWORD spinCount)
{
	TRACE(InitializeCriticalSectionAndSpinCount,("%p,%u",cs,(unsigned int)spinCount));
	InitializeCriticalSection_impl(cs);
}

static void CDECL_IMPL _lock_impl (int locknum)
{
	TRACE(_lock,("%d",locknum));
	//@HACK: called at CRT init with locknum = 8 (_EXIT_LOCK1)
}

static void CDECL_IMPL _unlock_impl (int locknum)
{
	TRACE(_unlock,("%d",locknum));
	//@HACK: called at CRT init with locknum = 8 (_EXIT_LOCK1)
}

typedef int (CDECL_IMPL* _onexit_t)(void);

static _onexit_t CDECL_IMPL __dllonexit_impl(_onexit_t func, _PVFV** pbegin, _PVFV** pend)
{
	//@HACK: just don't setup on exit calls table
	return func;
}


static BOOL WINAPI_IMPL DisableThreadLibraryCalls_impl (HMODULE_impl mod)
{
	TRACE(DisableThreadLibraryCalls,("%p",mod));
	return 1;
}



static void WINAPI_IMPL GetSystemInfo_impl (SYSTEM_INFO_impl* out)
{
	TRACE(GetSystemInfo,("%p",out));
	if (!out)
		return;
	out->wProcessorArchitecture = 0; // x86
	out->wReserved = 0;
	out->dwPageSize = 4096; //@TODO
	out->lpMinimumApplicationAddress = 0;
	out->lpMaximumApplicationAddress = 0;
	out->dwActiveProcessorMask = 1;
	out->dwNumberOfProcessors = 1;
	out->dwProcessorType = 586; // pentium
	out->dwAllocationGranularity = 4096;
	out->wProcessorLevel = 0;
	out->wProcessorRevision = 0;
}

static void WINAPI_IMPL Sleep_impl (DWORD ms)
{
	TRACE(Sleep,("%u", (unsigned)ms));
	//@TODO?
}

static void* WINAPI_IMPL EncodePointer_impl(void* ptr)
{
	TRACE(EncodePointer,("%p", ptr));
	return ptr;
}

static void* WINAPI_IMPL DecodePointer_impl(void* ptr)
{
	TRACE(DecodePointer,("%p", ptr));
	return ptr;
}

static BOOL WINAPI_IMPL IsProcessorFeaturePresent_impl(DWORD ProcessorFeature)
{
	TRACE(IsProcessorFeaturePresent,("%u", (unsigned int)ProcessorFeature));
	switch(ProcessorFeature)
	{
		case 3:  //PF_MMX_INSTRUCTIONS_AVAILABLE
		case 6:  //PF_XMMI_INSTRUCTIONS_AVAILABLE
		case 7:  //PF_3DNOW_INSTRUCTIONS_AVAILABLE
		case 10: //PF_XMMI64_INSTRUCTIONS_AVAILABLE
			return 1;
	}
	return 0;
}


static int CDECL_IMPL memcmp_impl (const void* buf1, const void* buf2, size_t count)
{
	TRACE(memcmp,("%p,%p,%u",buf1,buf2,(unsigned)count));
	return memcmp(buf1, buf2, count);
}

static void* CDECL_IMPL memcpy_impl (void* dst, const void* src, size_t count)
{
	TRACE(memcpy,("%p,%p,%u",dst,src,(unsigned)count));
	return memcpy(dst, src, count);
}

static void* CDECL_IMPL memmove_impl (void* dst, const void* src, size_t count)
{
	TRACE(memmove,("%p,%p,%u",dst,src,(unsigned)count));
	return memmove(dst, src, count);
}

static int CDECL_IMPL memmove_s_impl (void* dst, size_t numberOfElements, const void* src, size_t count)
{
	TRACE(memmove_s,("%p,%u,%p,%u",dst,(unsigned)numberOfElements,src,(unsigned)count));
	memmove(dst, src, count);
	return 0;
}

static void* CDECL_IMPL memset_impl (void* dst, int c, size_t count)
{
	TRACE(memset,("%p,%i,%u",dst,c,(unsigned)count));
	return memset (dst, c, count);
}

static char* CDECL_IMPL setlocale_impl (int category, const char* locale)
{
	TRACE(setlocale,("%i,\"%s\"",category,locale));
	return setlocale (category, locale);
}

static size_t _mbstrlen_impl(const char* s)
{
	TRACE(_mbstrlen,("\"%s\"",s));
	size_t len = strlen(s); //@TODO: proper impl?
	return len;
}

static char* CDECL_IMPL _strdup_impl (const char* s)
{
	TRACE(_strdup,("\"%s\"",s));
	char* r = strdup (s);
	return r;
}

static char* CDECL_IMPL strchr_impl(char* str, int ch)
{
	TRACE(strchr,("\"%s\",'%c'",str,ch));
	return strchr(str,ch);
}

static char* CDECL_IMPL strrchr_impl(char* str, int ch)
{
	TRACE(strrchr,("\"%s\",'%c'",str,ch));
	return strrchr(str,ch);
}

static char* CDECL_IMPL strstr_impl(char* str, const char* subStr)
{
	TRACE(strstr,("\"%s\",\"%s\"",str,subStr));
	return strstr(str,subStr);
}

static int CDECL_IMPL strncmp_impl(const char* str1, const char* str2, size_t maxCount)
{
	TRACE(strncmp,("\"%s\",\"%s\",%u",str1,str2,(unsigned)maxCount));
	return strncmp(str1,str2,maxCount);
}

static int CDECL_IMPL _strnicmp_impl(const char* str1, const char* str2, size_t maxCount)
{
	TRACE(_strnicmp,("\"%s\",\"%s\",%u",str1,str2,(unsigned)maxCount));
	return strncasecmp(str1,str2,maxCount);
}

static int CDECL_IMPL _stricmp_impl(const char* a, const char* b)
{
	TRACE(_stricmp,("\"%s\",\"%s\"",a,b));
	return strcasecmp(a,b);
}

static int CDECL_IMPL isdigit_impl(int c)
{
	TRACE(isdigit,("'%c'",c));
	return isdigit(c);
}

static int CDECL_IMPL isxdigit_impl(int c)
{
	TRACE(isxdigit,("'%c'",c));
	return isxdigit(c);
}

static int CDECL_IMPL isalpha_impl(int c)
{
	TRACE(isalpha,("'%c'",c));
	return isalpha(c);
}

static int CDECL_IMPL isspace_impl(int c)
{
	TRACE(isspace,("'%c'",c));
	return isspace(c);
}

static int CDECL_IMPL isalnum_impl(int c)
{
	TRACE(isalnum,("'%c'",c));
	return isalnum(c);
}

static int CDECL_IMPL tolower_impl(int c)
{
	TRACE(tolower,("'%c'",c));
	return tolower(c);
}

static int CDECL_IMPL toupper_impl(int c)
{
	TRACE(toupper,("'%c'",c));
	return toupper(c);
}

static double CDECL_IMPL atof_impl (const char* s)
{
	TRACE(atof,("\"%s\"",s));
	return atof(s);
}

static int CDECL_IMPL atoi_impl (const char* s)
{
	TRACE(atoi,("\"%s\"",s));
	return atoi(s);
}

static double CDECL_IMPL modf_impl(double x, double* y)
{
	TRACE(modf,("%f,%p",x,y));
	return modf(x, y);
}

static double CDECL_IMPL ceil_impl(double x)
{
	TRACE(ceil,("%f",x));
	return ceil(x);
}

static double CDECL_IMPL floor_impl(double x)
{
	TRACE(floor,("%f",x));
	return floor(x);
}

static int CDECL_IMPL _finite_impl(double x)
{
	TRACE(_finite,("%f",x));
	int r;
#if WODKA_WINDOWS
	r = _finite(x);
#else
	r = std::isfinite(x);
#endif
	return r;
}

static int CDECL_IMPL _isnan_impl(double x)
{
	TRACE(_isnan,("%f",x));
#if WODKA_WINDOWS
	return _isnan(x);
#else
	return std::isnan(x);
#endif
}

static int CDECL_IMPL _fpclass_impl(double x)
{
	TRACE(_fpclass,("%f",x));
#if WODKA_WINDOWS
	return _fpclass(x);
#else
	int c = std::fpclassify(x);
	switch (c) {
	case FP_NAN: return 0x0002;
	case FP_INFINITE: return std::signbit(x) ? 0x0004 : 0x0200;
	case FP_SUBNORMAL: return std::signbit(x) ? 0x0010 : 0x0080;
	case FP_ZERO: return std::signbit(x) ? 0x0020 : 0x0040;
	default: return std::signbit(x) ? 0x0008 : 0x0100;
	}
#endif
}

static double CDECL_IMPL fabs_impl (double x)
{
	TRACE(fabs,("%f",x));
	return fabs(x);
}
static double CDECL_IMPL sqrt_impl (double x)
{
	TRACE(sqrt,("%f",x));
	return sqrt(x);
}
static double CDECL_IMPL tan_impl (double x)
{
	TRACE(tan,("%f",x));
	return tan(x);
}
static double CDECL_IMPL sin_impl (double x)
{
	TRACE(sin,("%f",x));
	return sin(x);
}
static double CDECL_IMPL cos_impl (double x)
{
	TRACE(cos,("%f",x));
	return cos(x);
}
static double CDECL_IMPL asin_impl (double x)
{
	TRACE(asin,("%f",x));
	return asin(x);
}
static double CDECL_IMPL acos_impl (double x)
{
	TRACE(acos,("%f",x));
	return acos(x);
}
static double CDECL_IMPL atan_impl (double x)
{
	TRACE(atan,("%f",x));
	return atan(x);
}
static double CDECL_IMPL atan2_impl (double x, double y)
{
	TRACE(atan2,("%f,%f",x,y));
	return atan2(x,y);
}
static double CDECL_IMPL sinh_impl (double x)
{
	TRACE(sinh,("%f",x));
	return sinh(x);
}
static double CDECL_IMPL cosh_impl (double x)
{
	TRACE(cosh,("%f",x));
	return cosh(x);
}
static double CDECL_IMPL tanh_impl (double x)
{
	TRACE(tanh,("%f",x));
	return tanh(x);
}
static double CDECL_IMPL exp_impl (double x)
{
	TRACE(exp,("%f",x));
	return exp(x);
}
static double CDECL_IMPL log_impl (double x)
{
	TRACE(log,("%f",x));
	return log(x);
}
static double CDECL_IMPL frexp_impl (double x, int* n)
{
	TRACE(frexp,("%f",x));
	return frexp(x,n);
}
static double CDECL_IMPL pow_impl (double x, double y)
{
	TRACE(pow,("%f,%f",x,y));
	return pow(x,y);
}
static double CDECL_IMPL fmod_impl (double x, double y)
{
	TRACE(fmod,("%f,%f",x,y));
	return fmod(x,y);
}

#if !WODKA_64

#if WODKA_WINDOWS
#define POP_FPU_1(x) \
	double x; \
	__asm fstp [x] __asm fwait
#define POP_FPU_2(x,y) \
	double x, y; \
	__asm fstp [y] __asm fwait \
	__asm fstp [x] __asm fwait
#else
#define POP_FPU_1(x) \
	double x; \
	__asm__ volatile ("fstpl %0;fwait;" : "=m"(x))
#define POP_FPU_2(x,y) \
	double x, y; \
	__asm__ volatile ("fstpl %0;fwait;" : "=m"(y)); \
	__asm__ volatile ("fstpl %0;fwait;" : "=m"(x))
#endif

static LONGLONG CDECL_IMPL _ftol2_sse_impl ()
{
	POP_FPU_1(x);
	TRACE(_ftol2_sse,("%f",x));
	LONGLONG r = (LONGLONG)x;
	return r;
}

static LONGLONG CDECL_IMPL _ftol2_impl ()
{
	POP_FPU_1(x);
	TRACE(_ftol2,("%f",x));
	LONGLONG r = (LONGLONG)x;
	return r;
}

static double CDECL_IMPL _CIsqrt_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIsqrt,("%f",x));
	return sqrt(x);
}
static double CDECL_IMPL _CItan_impl ()
{
	POP_FPU_1(x);
	TRACE(_CItan,("%f",x));
	return tan(x);
}
static double CDECL_IMPL _CIsin_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIsin,("%f",x));
	return sin(x);
}
static double CDECL_IMPL _CIcos_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIcos,("%f",x));
	return cos(x);
}
static double CDECL_IMPL _CIasin_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIasin,("%f",x));
	return asin(x);
}
static double CDECL_IMPL _CIacos_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIacos,("%f",x));
	return acos(x);
}
static double CDECL_IMPL _CIatan_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIatan,("%f",x));
	return atan(x);
}
static double CDECL_IMPL _CIatan2_impl ()
{
	POP_FPU_2(x,y);
	TRACE(_CIatan2,("%f,%f",x,y));
	return atan2(x,y);
}
static double CDECL_IMPL _CIsinh_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIsinh,("%f",x));
	return sinh(x);
}
static double CDECL_IMPL _CIcosh_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIcosh,("%f",x));
	return cosh(x);
}
static double CDECL_IMPL _CItanh_impl ()
{
	POP_FPU_1(x);
	TRACE(_CItanh,("%f",x));
	return tanh(x);
}
static double CDECL_IMPL _CIexp_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIexp,("%f",x));
	return exp(x);
}
static double CDECL_IMPL _CIlog_impl ()
{
	POP_FPU_1(x);
	TRACE(_CIlog,("%f",x));
	return log(x);
}
static double CDECL_IMPL _CIpow_impl ()
{
	POP_FPU_2(x,y);
	TRACE(_CIpow,("%f,%f",x,y));
	return pow(x,y);
}
static double CDECL_IMPL _CIfmod_impl ()
{
	POP_FPU_2(x,y);
	TRACE(_CIfmod,("%f,%f",x,y));
	return fmod(x,y);
}

#endif // #if !WODKA_64


static void* CDECL_IMPL operator_new_impl (unsigned int size)
{
	TRACE(opnew,("%u",(unsigned)size));
	void* r = malloc(size);
	TRACE2(("%p",r));
	return r;
}

static void* CDECL_IMPL operator_new_array_impl (unsigned int size)
{
	TRACE(opnew_array,("%u",(unsigned)size));
	void* r = malloc(size);
	TRACE2(("%p",r));
	return r;
}

static void* CDECL_IMPL operator_new64_impl (size_t size)
{
	TRACE(opnew,("%u",(unsigned)size));
	void* r = malloc(size);
	TRACE2(("%p",r));
	return r;
}

static void CDECL_IMPL operator_delete_impl (void* ptr)
{
	TRACE(opdelete,("%p",ptr));
	if (!ptr)
		return;
	free (ptr);
}

static void CDECL_IMPL operator_delete_array_impl (void* ptr)
{
	TRACE(opdelete_array,("%p",ptr));
	if (!ptr)
		return;
	free (ptr);
}

static int CDECL_IMPL _vsnprintf_impl (char *string, size_t count, const char *format, va_list ap)
{
	TRACE(_vsnprintf,("%p,%u,\"%s\",...",string,(unsigned)count,format));
	return vsnprintf(string, count, format, ap);
}

static LONG WINAPI_IMPL InterlockedExchange_impl (LONG volatile* target, LONG value)
{
	TRACE(InterlockedExchange,("%p,%i",target,(int)value));
	LONG r;
#if WODKA_WINDOWS
	r = InterlockedExchange (target, value);
#elif WODKA_MAC
	//@TODO: correct?
	LONG oldValue = *target;
	while (!OSAtomicCompareAndSwap32 (oldValue, (int)value, (int volatile*)target))
		oldValue = *target;
	r = oldValue;
#elif WODKA_LINUX
	r = *target;
	__sync_bool_compare_and_swap(target, *target, value);
#endif
	TRACE2(("%u",(unsigned)r));
	return r;
}

static LONG WINAPI_IMPL InterlockedCompareExchange_impl (LONG volatile* dest, LONG exchange, LONG comperand)
{
	TRACE(InterlockedCompareExchange,("%p,%i,%i",dest,(int)exchange,(int)comperand));
	LONG r;
#if WODKA_WINDOWS
	r = InterlockedCompareExchange (dest, exchange, comperand);
#elif WODKA_MAC
	LONG ret = *dest;
	bool rr = OSAtomicCompareAndSwap32 (comperand, (int)exchange, (int volatile*)dest);
	r = rr ? comperand : ret;
#elif WODKA_LINUX
	LONG ret = *dest;
	bool rr = __sync_bool_compare_and_swap(dest, comperand, exchange);
	r = rr ? comperand : ret;
#endif
	TRACE2(("%u",(unsigned)r));
	return r;
}

static DWORD WINAPI_IMPL TlsAlloc_impl()
{
	TRACE(TlsAlloc,(""));
	DWORD r;
#if WODKA_WINDOWS
	r = TlsAlloc();
#else
	pthread_key_t key;
	pthread_key_create (&key, nullptr);
	r = key;
#endif
	TRACE2(("%u",(unsigned)r));
	return r;
}

static void* WINAPI_IMPL TlsGetValue_impl(DWORD idx)
{
	TRACE(TlsGetValue,("%u",(unsigned)idx));
	void* r;
#if WODKA_WINDOWS
	r = TlsGetValue(idx);
#else
	pthread_key_t key = idx;
	r = pthread_getspecific(key);
#endif
	TRACE2(("%p",r));
	return r;
}

static BOOL WINAPI_IMPL TlsSetValue_impl(DWORD idx, void* val)
{
	TRACE(TlsSetValue,("%u,%p",(unsigned)idx,val));
#if WODKA_WINDOWS
	return TlsSetValue(idx, val);
#else
	pthread_key_t key = idx;
	pthread_setspecific(key, val);
	return 1;
#endif
}

static BOOL WINAPI_IMPL TlsFree_impl(DWORD idx)
{
	TRACE(TlsFree,("%u",(unsigned)idx));
#if WODKA_WINDOWS
	return TlsFree(idx);
#else
	pthread_key_t key = idx;
	pthread_key_delete(key);
	return 1;
#endif
}

static void CDECL_IMPL free_impl(void* p)
{
	TRACE(free,("%p",p));
	free (p);
}

static void* CDECL_IMPL malloc_impl(size_t s)
{
	TRACE(malloc,("%u",(unsigned)s));
	void* r = malloc (s);
	return r;
}

static void* CDECL_IMPL _malloc_crt_impl(size_t s)
{
	TRACE(_malloc_crt,("%u",(unsigned)s));
	void* r = malloc (s);
	return r;
}

static void* CDECL_IMPL _malloc_dbg_impl(size_t size, int /*blockType*/, const char* /*filename*/, int /*linenumber*/)
{
	TRACE(_malloc_dbg,("%u",(unsigned)size));
	void* r = malloc (size);
	return r;
}

static void* CDECL_IMPL realloc_impl(void* p, size_t s)
{
	TRACE(realloc,("%p %u",p,(unsigned)s));
	void* r = realloc (p,s);
	return r;
}

static int s_VAInitialized = 0;
static CRITICAL_SECTION_impl s_VAMutex;
static std::map<void*,size_t> s_VARecords;


static void* WINAPI_IMPL VirtualAlloc_impl(void* addr, SIZE_T size, DWORD type, DWORD prot)
{
	TRACE(VirtualAlloc,("%p,%u,0x%x,0x%x",addr,(unsigned)size,(unsigned)type,(unsigned)prot));
#if WODKA_WINDOWS
	return VirtualAlloc (addr, size, type, prot);
#else
	#define MEM_COMMIT  0x00001000
	#define MEM_RESERVE 0x00002000
	if ((type & MEM_RESERVE) || !addr) {
		//@TODO: proper flags?
		void* r = mmap (addr, size, PROT_READ|PROT_WRITE|PROT_EXEC, MAP_ANON|MAP_PRIVATE, 0, 0);

		// record the size passed for this address, will need it in VirtualFree implementation
		EnterCriticalSection_impl(&s_VAMutex);
		s_VARecords.insert(std::make_pair(r, size));
		LeaveCriticalSection_impl(&s_VAMutex);

		return r;
	}
	//@TODO: memory commit?
	return addr;
#endif
}

static BOOL WINAPI_IMPL VirtualFree_impl(void* addr, SIZE_T size, DWORD type)
{
	TRACE(VirtualFree,("%p,%u,0x%x",addr,(unsigned)size,(unsigned)type));
#if WODKA_WINDOWS
	return VirtualFree (addr, size, type);
#else
	#define MEM_COMMIT  0x00001000
	#define MEM_RESERVE 0x00002000
	#define MEM_RELEASE 0x00008000

	EnterCriticalSection_impl(&s_VAMutex);
	// For zero or MEM_RELEASE, we need to figure out the size
	// used in VirtualAlloc.
	if (size==0 || type==MEM_RELEASE)
		size = s_VARecords[addr];

	//@TODO
	munmap(addr, size);

	s_VARecords.erase(addr);

	LeaveCriticalSection_impl(&s_VAMutex);
	return 1;
#endif
}


static DWORD WINAPI_IMPL GetFullPathNameA_impl(const char* lpFileName, DWORD nBufferLength, char* lpBuffer, char* *lpFilePart)
{
	TRACE(GetFullPathNameA,("\"%s\",%u,%p,%p",lpFileName,(unsigned)nBufferLength,lpBuffer,lpFilePart));
	//@TODO: proper impl?
	//return GetFullPathNameA (lpFileName, nBufferLength, lpBuffer, lpFilePart);
	DWORD len = (DWORD)strlen(lpFileName);
	if (len >= nBufferLength)
		return len+1;
	memcpy (lpBuffer, lpFileName, len);
	lpBuffer[len] = 0;
	return len;
}

static int WINAPI_IMPL MultiByteToWideChar_impl(UINT codePage, DWORD flags, const char* multiByteStr, int cbMultiByte, unsigned short* wideCharStr, int cchWideChar)
{
	TRACE(MultiByteToWideChar,("%u,0x%x,\"%s\",%i,%p,%i",(unsigned)codePage, (unsigned)flags, multiByteStr, cbMultiByte, wideCharStr, cchWideChar));
	int r = 0;
	//#if WODKA_WINDOWS
	//r = MultiByteToWideChar (codePage, flags, multiByteStr, cbMultiByte, wideCharStr, cchWideChar);
	//#else

	if (!multiByteStr || (cchWideChar && !wideCharStr))
		return 0; //@TODO: set error
	if (cbMultiByte < 0)
		cbMultiByte = (int)strlen(multiByteStr)+1;

	if (codePage == 65001) // CP_UTF8
	{
		//@TODO: more checking/error codes?
		bool ok = cross::ConvertUTF8toUTF16 (multiByteStr, cbMultiByte, wideCharStr, cchWideChar);
		if (ok)
			return cchWideChar;
		return 0;
	}
	else
	{
		//@TODO: other code pages
		assert(false);
	}

	//#endif
	TRACE2(("%i",r));
	return r;
}


static inline size_t strlen16(const unsigned short* str)
{
	const unsigned short* s = str;
	while (*s) ++s;
	return s - str;
}


static int WINAPI_IMPL WideCharToMultiByte_impl(UINT codePage, DWORD flags, const unsigned short* wideCharStr, int cchWideChar, char* multiByteStr, int cbMultiByte, const char* defChar, BOOL* usedDefaultChar)
{
	TRACE(WideCharToMultiByte,("%u,0x%x,%p,%i,\"%s\",%i,\"%s\",%p",(unsigned)codePage, (unsigned)flags, wideCharStr, cchWideChar, multiByteStr, cbMultiByte, defChar, usedDefaultChar));
	int r = 0;
	//#if WODKA_WINDOWS
	//r = WideCharToMultiByte (codePage, flags, wideCharStr, cchWideChar, multiByteStr, cbMultiByte, defChar, usedDefaultChar);
	//#else

	if (!wideCharStr || (cbMultiByte && !multiByteStr))
		return 0; //@TODO: set error

	if (cchWideChar < 0)
		cchWideChar = (int)strlen16(wideCharStr) + 1;

	if (usedDefaultChar)
		*usedDefaultChar = 0;
	if (codePage == 0) // CP_ACP
	{
		//@TODO: buffer size checking
		for (int i = 0; i < cchWideChar; ++i)
		{
			unsigned short c = wideCharStr[i];
			multiByteStr[i] = (char)c; //@TODO: wrong
		}
		return cchWideChar;
	}
	else if (codePage == 65001) // CP_UTF8
	{
		//@TODO: more checking/error codes?
		//@TODO: defChar
		bool ok = cross::ConvertUTF16toUTF8 (wideCharStr, cchWideChar, multiByteStr, cbMultiByte);
		if (ok)
			return cbMultiByte;
		return 0;
	}
	else
	{
		//@TODO: other code pages
		assert(false);
	}

	//#endif
	TRACE2(("%i",r));
	return r;
}

static char* CDECL_IMPL getenv_impl(const char* v)
{
	TRACE(getenv,("\"%s\"",v));
	char* r = getenv(v);
	TRACE2(("%s",r));
	return r;
}

static DWORD WINAPI_IMPL GetEnvironmentVariableA_impl(const char* name, char* buffer, DWORD size)
{
	TRACE(GetEnvironmentVariableA,("\"%s\",%p,%u",name,buffer,(unsigned)size));
	char* r = getenv(name);
	if (!r)
		return 0; //@TODO: last error
	DWORD len = (DWORD)strlen(r);
	if (len > size-1)
		len = size-1;
	memcpy(buffer, r, len);
	buffer[len] = 0;
	return len;
}

static BOOL WINAPI_IMPL QueryPerformanceCounter_impl(LARGE_INTEGER_impl* count)
{
	TRACE(QueryPerformanceCounter,("%p",count));
	clock_t t = clock();
	count->HighPart = 0;
	count->LowPart = t;
	return 1;
}

struct SECURITY_ATTRIBUTES_impl;

static HANDLE WINAPI_IMPL CreateFileA_impl (const char* fileName, DWORD desiredAccess, DWORD shareMode, SECURITY_ATTRIBUTES_impl* sec, DWORD creationDisposition, DWORD flagsAndAttributes, HANDLE templateFile)
{
	TRACE(CreateFileA,("\"%s\",%d,%d",fileName,(unsigned int)desiredAccess,(unsigned int)shareMode));
	//@TODO: proper impl?
	//return CreateFileA(fileName,desiredAccess,shareMode,(SECURITY_ATTRIBUTES*)sec,creationDisposition,flagsAndAttributes,templateFile);
	return (HANDLE)-1;
}

static DWORD WINAPI_IMPL GetFileSize_impl(HANDLE file, DWORD* fileSizeHigh)
{
	//@TODO: proper impl?
	//return GetFileSize(file,fileSizeHigh);
	return 0;
}

static BOOL WINAPI_IMPL CloseHandle_impl(HANDLE h)
{
	//@TODO: proper impl?
	//return CloseHandle(h);
	return 1;
}

static LONG WINAPI_IMPL RegOpenKeyExA_impl (HKEY_impl key, const char* subKey, DWORD options, DWORD samDesired, HKEY_impl* res)
{
	//@TODO: proper impl?
	//return RegOpenKeyExA((HKEY)key, subKey, options, samDesired, (HKEY*)res);
	return ERROR_INVALID_PARAMETER_IMPL;
}

static LONG WINAPI_IMPL RegEnumKeyExA_impl (HKEY_impl key, DWORD index, char* name, DWORD* cchName, DWORD* reserved, char* clazz, DWORD* cchClass, FILETIME_impl* lastWriteTime)
{
	//@TODO: proper impl?
	//return RegEnumKeyExA((HKEY)key, index, name, cchName, reserved, clazz, cchClass, lastWriteTime);
	return ERROR_INVALID_PARAMETER_IMPL;
}

static LONG WINAPI_IMPL RegQueryValueExA_impl (HKEY_impl key, const char* valueName, DWORD* reserved, DWORD* type, BYTE* data, DWORD* cbData)
{
	//@TODO: proper impl?
	//return RegQueryValueExA((HKEY)key, valueName, reserved, type, data, cbData);
	return ERROR_INVALID_PARAMETER_IMPL;
}

static LONG WINAPI_IMPL RegCloseKey_impl (HKEY_impl key)
{
	//@TODO: proper impl?
	//return RegCloseKey((HKEY)key);
	return ERROR_INVALID_PARAMETER_IMPL;
}



static HMODULE_impl WINAPI_IMPL GetModuleHandleA_impl(const char* moduleName)
{
	//@TODO: proper impl?

	// so far it looks like it only tries to load d3d9.dll and find a Direct3DShaderValidatorCreate9
	// function, but succeeds when that is not there.
	return nullptr;
}

static HMODULE_impl WINAPI_IMPL LoadLibraryA_impl(const char* moduleName)
{
	//@TODO: proper impl?

	// so far it looks like it only tries to load d3d9.dll and find a Direct3DShaderValidatorCreate9
	// function, but succeeds when that is not there.
	return nullptr;
}

static void* WINAPI_IMPL GetProcAddress_impl(HMODULE_impl mod, const char* name)
{
	//@TODO: proper impl?

	// so far it looks like it only tries to load d3d9.dll and find a Direct3DShaderValidatorCreate9
	// function, but succeeds when that is not there.
	return nullptr;
}

static DWORD WINAPI_IMPL GetModuleFileNameA_impl(HMODULE_impl mod, char* filename, DWORD size)
{
	//@TODO: proper impl?
	strncpy(filename, "foo", size);
	return 3;
	//return GetModuleFileNameA((HMODULE)mod, filename, size);
}

static HANDLE WINAPI_IMPL GetProcessHeap_impl()
{
	TRACE(GetProcessHeap,(""));
	//@TODO: proper heap?
	return (HANDLE)1;
}


static HANDLE WINAPI_IMPL HeapCreate_impl(DWORD flOptions, SIZE_T dwInitialSize, SIZE_T dwMaximumSize)
{
	TRACE(HeapCreate,("0x%x,%u,%u",(unsigned)flOptions,(unsigned)dwInitialSize,(unsigned)dwMaximumSize));
	//@TODO: proper heap?
	return (HANDLE)1;
}

static BOOL WINAPI_IMPL HeapDestroy_impl(HANDLE hHeap)
{
	TRACE(HeapDestroy,("%p",hHeap));
	//@TODO: proper heap?
	return 1;
}

static void* WINAPI_IMPL HeapAlloc_impl(HANDLE hHeap, DWORD dwFlags, SIZE_T dwBytes)
{
	TRACE(HeapAlloc,("%p,0x%x,%u",hHeap,(unsigned int)dwFlags,(unsigned int)dwBytes));
	#define HEAP_ZERO_MEMORY 0x00000008
	void* r = malloc(dwBytes);
	if (dwFlags & HEAP_ZERO_MEMORY)
		memset (r, 0, dwBytes);
	return r;
}

static BOOL WINAPI_IMPL HeapFree_impl(HANDLE hHeap, DWORD dwFlags, void* lpMem)
{
	TRACE(HeapFree,("%p,0x%x,%p",hHeap,(unsigned int)dwFlags,lpMem));
	free (lpMem);
	return 1;
}

static void WINAPI_IMPL OutputDebugStringA_impl(const char* str)
{
	TRACE(OutputDebugStringA,("%p",str));
	::printf("%s\n", str);
}


static unsigned int CDECL_IMPL _controlfp_impl(unsigned int val, unsigned int mask)
{
	TRACE(_controlfp,("0x%x,0x%x",val,mask));
	//return _controlfp(val,mask);
	return 0x8001; //@TODO?
}

static unsigned int CDECL_IMPL _clearfp_impl()
{
	TRACE(_clearfp,(""));
	//return _clearfp();
	return 0; //@TODO
}


// SSE2 intrinsics impl

#if WODKA_WINDOWS

	// vs cant use builtin asm when targeting x64
	// so we just reroute to the very same funcs but now exported for real
	// we do it even for x86 just for the sake of consistency

	#define IMPL_SSE2_FUNC_1ARG(name, call)							\
	static void CDECL_IMPL name##_impl()							\
	{																\
		TRACE(name,(""));											\
																	\
		typedef void (*_ImplFunc)();								\
		static _ImplFunc _Impl = 0;									\
		if(!_Impl)													\
		{															\
			HMODULE dll = ::LoadLibraryA("msvcrt.dll");				\
			_Impl = (_ImplFunc)::GetProcAddress(dll, #name);		\
		}															\
		_Impl();													\
	}																\

#else

	#define INTRINSIC_IMPL_PROLOGUE() __asm__ __volatile__( "movq %%xmm0,%0" : "=m" (d) )
	#define INTRINSIC_IMPL_EPILOGUE() __asm__ __volatile__( "movq %0,%%xmm0" : : "m" (d) )

	#define IMPL_SSE2_FUNC_1ARG(name, call)	\
	static void CDECL_IMPL name##_impl()	\
	{										\
		TRACE(name,(""));					\
											\
		double d;							\
		INTRINSIC_IMPL_PROLOGUE();			\
		d = call(d);						\
		INTRINSIC_IMPL_EPILOGUE();			\
	}										\

#endif


IMPL_SSE2_FUNC_1ARG(__libm_sse2_acos, acos);
IMPL_SSE2_FUNC_1ARG(__libm_sse2_asin, asin);
IMPL_SSE2_FUNC_1ARG(__libm_sse2_cos, cos);
IMPL_SSE2_FUNC_1ARG(__libm_sse2_sin, sin);
IMPL_SSE2_FUNC_1ARG(__libm_sse2_log, log);

#undef INTRINSIC_IMPL_PROLOGUE
#undef INTRINSIC_IMPL_EPILOGUE


#define IMP(f) { #f, (void*)f##_impl, 0 }
#define IMPWIN(f,sz) { #f, (void*)f##_impl, sz }

PEKnownImport kWodkaKnownImports[] = {
	IMPWIN(GetSystemTimeAsFileTime,sizeof(FILETIME_impl*)),
	IMPWIN(GetCurrentProcessId,0),
	IMPWIN(GetCurrentThreadId,0),
	IMPWIN(GetTickCount,0),
	IMPWIN(GetVersion,0),
	IMPWIN(InitializeCriticalSection,sizeof(CRITICAL_SECTION_impl*)),
	IMPWIN(InitializeCriticalSectionAndSpinCount,sizeof(CRITICAL_SECTION_impl*)+sizeof(DWORD)),
	IMPWIN(GetProcessHeap,0),
	IMPWIN(EncodePointer, sizeof(void*)),
	IMPWIN(DecodePointer, sizeof(void*)),
	IMPWIN(IsProcessorFeaturePresent, sizeof(DWORD)),
	IMP(_initterm),
	IMP(_initterm_e),
	IMP(_lock),
	IMP(_unlock),
	IMP(__dllonexit),
	IMP(_CRT_RTC_INIT),
	IMP(_CRT_RTC_INITW),
	IMP(_CrtSetCheckCount),
	IMPWIN(DisableThreadLibraryCalls,sizeof(HMODULE_impl)),
	IMPWIN(GetSystemInfo,sizeof(SYSTEM_INFO_impl*)),
	IMPWIN(Sleep,sizeof(DWORD)),
	IMP(memset),
	IMP(setlocale),
	IMP(_mbstrlen),
	IMP(_strdup),
	{ "??2@YAPAXI@Z", (void*)operator_new_impl, 0 },
	{ "??_U@YAPAXI@Z", (void*)operator_new_array_impl, 0 },
	{ "??3@YAXPAX@Z", (void*)operator_delete_impl, 0 },
	{ "??_V@YAXPAX@Z", (void*)operator_delete_array_impl, 0 },
	#if WODKA_64
	{ "??2@YAPEAX_K@Z", (void*)operator_new64_impl, 0 },
	{ "??3@YAXPEAX@Z", (void*)operator_delete_impl, 0 },
	{ "??_V@YAXPEAX@Z", (void*)operator_delete_array_impl, 0 },
	#endif
	IMP(_vsnprintf),
	IMPWIN(InterlockedCompareExchange,sizeof(void*)+sizeof(LONG)+sizeof(LONG)),
	IMPWIN(InterlockedExchange,sizeof(void*)+sizeof(LONG)),
	IMPWIN(TlsAlloc,0),
	IMPWIN(TlsGetValue,sizeof(LONG)),
	IMPWIN(TlsSetValue,sizeof(LONG)+sizeof(void*)),
	IMPWIN(TlsFree,sizeof(LONG)),
	IMP(malloc),
	IMP(_malloc_dbg),
	IMP(_malloc_crt),
	IMP(realloc),
	IMP(free),
	IMPWIN(VirtualAlloc,sizeof(void*) + sizeof(SIZE_T) + 2*sizeof(DWORD)),
	IMPWIN(VirtualFree,sizeof(void*) + sizeof(SIZE_T) + sizeof(DWORD)),
	IMPWIN(GetFullPathNameA,3*sizeof(void*) + sizeof(DWORD)),
	IMPWIN(MultiByteToWideChar,3*sizeof(UINT) + sizeof(DWORD) + 2*sizeof(void*)),
	IMPWIN(WideCharToMultiByte,3*sizeof(UINT) + sizeof(DWORD) + 4*sizeof(void*)),
	IMP(memcpy),
	IMP(memmove),
	IMP(memmove_s),
	IMP(memcmp),
	IMP(getenv),
	IMPWIN(GetEnvironmentVariableA,2*sizeof(void*) + sizeof(DWORD)),
	IMPWIN(QueryPerformanceCounter,sizeof(void*)),
	IMPWIN(GetModuleHandleA,sizeof(void*)),
	IMPWIN(LoadLibraryA,sizeof(void*)),
	IMPWIN(GetProcAddress,2*sizeof(void*)),
	IMPWIN(GetModuleFileNameA,2*sizeof(void*) + sizeof(DWORD)),
	IMPWIN(CreateFileA,2*sizeof(void*) + 4*sizeof(DWORD) + sizeof(HANDLE)),
	IMPWIN(GetFileSize,sizeof(HANDLE)+sizeof(void*)),
	IMPWIN(CloseHandle,sizeof(HANDLE)),
	IMPWIN(RegOpenKeyExA,sizeof(HKEY_impl)+sizeof(const char*)+sizeof(DWORD)+sizeof(DWORD)+sizeof(HKEY_impl*)),
	IMPWIN(RegEnumKeyExA,sizeof(HKEY_impl)+sizeof(DWORD)+sizeof(char*)+sizeof(DWORD*)+sizeof(DWORD*)+sizeof(char*)+sizeof(DWORD*)+sizeof(FILETIME_impl*)),
	IMPWIN(RegQueryValueExA,sizeof(HKEY_impl)+sizeof(const char*)+sizeof(DWORD*)+sizeof(DWORD*)+sizeof(BYTE*)+sizeof(DWORD*)),
	IMPWIN(RegCloseKey,sizeof(HKEY_impl)),
	IMPWIN(HeapCreate,sizeof(DWORD) + 2*sizeof(SIZE_T)),
	IMPWIN(HeapDestroy,sizeof(HANDLE)),
	IMPWIN(HeapAlloc,sizeof(HANDLE) + sizeof(DWORD) + sizeof(SIZE_T)),
	IMPWIN(HeapFree,sizeof(HANDLE) + sizeof(DWORD) + sizeof(void*)),
	IMPWIN(OutputDebugStringA,sizeof(const char*)),
	IMP(_controlfp),
	IMP(_clearfp),
	IMP(strchr),
	IMP(strrchr),
	IMP(strstr),
	IMP(strncmp),
	IMP(_strnicmp),
	IMP(_stricmp),
	IMP(isdigit),
	IMP(isxdigit),
	IMP(isalpha),
	IMP(isspace),
	IMP(isalnum),
	IMP(tolower),
	IMP(toupper),
	IMP(atof),
	IMP(atoi),
	IMP(modf),
	IMP(ceil),
	IMP(floor),
	IMP(_finite),
	IMP(_isnan),
	IMP(_fpclass),
	IMP(fabs),
	IMP(sqrt),
	IMP(tan),
	IMP(sin),
	IMP(cos),
	IMP(acos),
	IMP(asin),
	IMP(atan),
	IMP(atan2),
	IMP(cosh),
	IMP(sinh),
	IMP(tanh),
	IMP(exp),
	IMP(log),
	IMP(frexp),
	IMP(pow),
	IMP(fmod),
#if !WODKA_64
	IMP(_ftol2_sse),
	IMP(_ftol2),
	IMP(_CIsqrt),
	IMP(_CItan),
	IMP(_CIsin),
	IMP(_CIcos),
	IMP(_CIacos),
	IMP(_CIasin),
	IMP(_CIatan),
	IMP(_CIatan2),
	IMP(_CIcosh),
	IMP(_CIsinh),
	IMP(_CItanh),
	IMP(_CIexp),
	IMP(_CIlog),
	IMP(_CIpow),
	IMP(_CIfmod),
#endif // #if !WODKA_64
	IMP(__libm_sse2_acos),
	IMP(__libm_sse2_asin),
	IMP(__libm_sse2_cos),
	IMP(__libm_sse2_sin),
	IMP(__libm_sse2_log),
};
int kWodkaKnownImportsCount = sizeof(kWodkaKnownImports)/sizeof(kWodkaKnownImports[0]);


void InitializeWodkaImports()
{
	if (s_VAInitialized <= 0)
		InitializeCriticalSection_impl(&s_VAMutex);
	++s_VAInitialized;
}
void CleanupWodkaImports()
{
	--s_VAInitialized;
	if (s_VAInitialized <= 0)
		DeleteCriticalSection_impl(&s_VAMutex);
}
