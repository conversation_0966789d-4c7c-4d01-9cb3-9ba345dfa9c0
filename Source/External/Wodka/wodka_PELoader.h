// Written by <PERSON><PERSON>
// I hereby place this code in the public domain

#pragma once

#include <string.h>

struct PEKnownImport {
	const char* name;
	void* func;
	int unwind;
};

struct PEModule;

PEModule* PELoadLibrary (const char* path, const PEKnownImport* imports, unsigned importCount);
PEModule* PELoadLibrary (const void* data, size_t size, const PEKnownImport* imports, unsigned importCount);
void PEFreeLibrary (PEModule* module);
void* PEGetProcAddress (PEModule* module, const char* name);
void* PECreateCallThunk (PEModule* module, void* func, int unwind, void** destAddr = nullptr);

void PESetupFS();
