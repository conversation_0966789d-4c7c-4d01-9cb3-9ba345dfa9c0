// Written by <PERSON><PERSON>
// I hereby place this code in the public domain

#pragma once

#if defined(_WIN64)
#	define WODKA_WINDOWS 1
#	define WODKA_64 1
#elif defined(_WIN32)
#	define WODKA_WINDOWS 1
#elif defined(__APPLE__) && !defined(__arm__)
#	define WODKA_MAC 1
#	if defined(_AMD64_) || defined(__LP64__)
#		define WODKA_64 1
#	endif
#elif defined(LINUX)
#	define WODKA_LINUX 1
#	if defined(_AMD64_) || defined(__LP64__)
#		define WODKA_64 1
#	endif
#else
#	error "Unknown platform"
#endif

#define WODKA_USE_ABI_THUNKS (WODKA_MAC || WODKA_LINUX)


#if WODKA_WINDOWS
#	define WINAPI_IMPL __stdcall
#	define CDECL_IMPL __cdecl
#	define strcasecmp _stricmp
#	define strncasecmp _strnicmp
#elif WODKA_64
#	define WINAPI_IMPL
#	define CDECL_IMPL
#else
// Apple ABI requires stack to be 16 byte aligned. Using
// force_align_arg_pointer attribute on our imported function
// implementations would do this nicely. If llvm-gcc-4.2 would actually
// support the attribute properly.
#	define WINAPI_IMPL __attribute__((__stdcall__)) __attribute__((__force_align_arg_pointer__))
#	define CDECL_IMPL __attribute__((__cdecl__)) __attribute__((__force_align_arg_pointer__))
#endif


typedef unsigned char BYTE;
typedef unsigned short WORD;
typedef unsigned int UINT;
#if WODKA_64 && (WODKA_LINUX || WODKA_MAC)
typedef int HRESULT;
typedef int LONG;
typedef unsigned int ULONG;
#else
typedef long HRESULT;
typedef long LONG;
typedef unsigned long ULONG;
#endif
typedef ULONG DWORD;
#if WODKA_64 && WODKA_WINDOWS
typedef unsigned __int64 ULONG_PTR;
#else
typedef unsigned long ULONG_PTR;
#endif
#if WODKA_WINDOWS
typedef __int64 LONGLONG;
typedef unsigned __int64 UINT64;
#else
typedef long long int LONGLONG;
typedef unsigned long long int UINT64;
#endif
typedef ULONG_PTR DWORD_PTR;
typedef ULONG_PTR SIZE_T;
typedef int BOOL;
typedef void* HANDLE;
typedef void* HMODULE_impl;
typedef void* HKEY_impl;

#define S_OK_IMPL 0x00000000
#define E_FAIL_IMPL 0x80004005
#define E_OUTOFMEMORY_IMPL 0x8007000E
#define ERROR_INVALID_PARAMETER_IMPL 87

#define SUCCEEDED_IMPL(Status) ((HRESULT)(Status) >= 0)
#define FAILED_IMPL(Status) ((HRESULT)(Status)<0)

// This is required to get the 64 bit version
// of CgBatch to pass its unit tests at the moment.
#if UNITY_64
#  define WODKA_USE_D3DCOMPILER_46 1
#  define WODKA_USE_D3DCOMPILER_47 0
#else
#  define WODKA_USE_D3DCOMPILER_46 0
#  define WODKA_USE_D3DCOMPILER_47 1
#endif
