#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/cancellation/cancellation_signal.hpp"
#include "async_task/asio_lite/tools/thread_context_type_traits.hpp"
#include "async_task/asio_lite/tools/associator.hpp"


namespace asio {

template <typename T, typename CancellationSlot>
struct associated_cancellation_slot;

namespace detail {

template <typename T, typename = void>
struct has_cancellation_slot_type : std::false_type
{
};

template <typename T>
struct has_cancellation_slot_type<T,
  typename void_type<typename T::cancellation_slot_type>::type>
    : std::true_type
{
};

template <typename T, typename S, typename = void, typename = void>
struct associated_cancellation_slot_impl
{
  typedef void asio_associated_cancellation_slot_is_unspecialised;

  typedef S type;

  static type get(const T&, const S& s = S()) noexcept
  {
    return s;
  }
};

template <typename T, typename S>
struct associated_cancellation_slot_impl<T, S,
  typename void_type<typename T::cancellation_slot_type>::type>
{
  typedef typename T::cancellation_slot_type type;

  static type get(const T& t, const S& = S()) noexcept
  {
    return t.get_cancellation_slot();
  }
};

template <typename T, typename S>
struct associated_cancellation_slot_impl<T, S,
  typename std::enable_if<
    !has_cancellation_slot_type<T>::value
  >::type,
  typename void_type<
    typename associator<associated_cancellation_slot, T, S>::type
  >::type> : associator<associated_cancellation_slot, T, S>
{
};

} // namespace detail

/// Traits type used to obtain the cancellation_slot associated with an object.
/**
 * A program may specialise this traits type if the @c T template parameter in
 * the specialisation is a user-defined type. The template parameter @c
 * CancellationSlot shall be a type meeting the CancellationSlot requirements.
 *
 * Specialisations shall meet the following requirements, where @c t is a const
 * reference to an object of type @c T, and @c s is an object of type @c
 * CancellationSlot.
 *
 * @li Provide a nested typedef @c type that identifies a type meeting the
 * CancellationSlot requirements.
 *
 * @li Provide a noexcept static member function named @c get, callable as @c
 * get(t) and with return type @c type.
 *
 * @li Provide a noexcept static member function named @c get, callable as @c
 * get(t,s) and with return type @c type.
 */
template <typename T, typename CancellationSlot = cancellation_slot>
struct associated_cancellation_slot
#if !defined(GENERATING_DOCUMENTATION)
  : detail::associated_cancellation_slot_impl<T, CancellationSlot>
#endif // !defined(GENERATING_DOCUMENTATION)
{
#if defined(GENERATING_DOCUMENTATION)
  /// If @c T has a nested type @c cancellation_slot_type,
  /// <tt>T::cancellation_slot_type</tt>. Otherwise
  /// @c CancellationSlot.
  typedef see_below type;

  /// If @c T has a nested type @c cancellation_slot_type, returns
  /// <tt>t.get_cancellation_slot()</tt>. Otherwise returns @c s.
  static type get(const T& t,
      const CancellationSlot& s = CancellationSlot()) ASIO_NOEXCEPT;
#endif // defined(GENERATING_DOCUMENTATION)
};

/// Helper function to obtain an object's associated cancellation_slot.
/**
 * @returns <tt>associated_cancellation_slot<T>::get(t)</tt>
 */
template <typename T>
ASIO_NODISCARD inline typename associated_cancellation_slot<T>::type
get_associated_cancellation_slot(const T& t) noexcept
{
  return associated_cancellation_slot<T>::get(t);
}

/// Helper function to obtain an object's associated cancellation_slot.
/**
 * @returns <tt>associated_cancellation_slot<T,
 * CancellationSlot>::get(t, st)</tt>
 */
template <typename T, typename CancellationSlot>
ASIO_NODISCARD inline
typename associated_cancellation_slot<T, CancellationSlot>::type
get_associated_cancellation_slot(const T& t,
    const CancellationSlot& st) noexcept
{
  return associated_cancellation_slot<T, CancellationSlot>::get(t, st);
}

#if defined(ASIO_HAS_ALIAS_TEMPLATES)

template <typename T, typename CancellationSlot = cancellation_slot>
using associated_cancellation_slot_t =
  typename associated_cancellation_slot<T, CancellationSlot>::type;

#endif // defined(ASIO_HAS_ALIAS_TEMPLATES)

namespace detail {

template <typename T, typename S, typename = void>
struct associated_cancellation_slot_forwarding_base
{
};

template <typename T, typename S>
struct associated_cancellation_slot_forwarding_base<T, S,
    typename std::enable_if<
      std::is_same<
        typename associated_cancellation_slot<T,
          S>::asio_associated_cancellation_slot_is_unspecialised,
        void
      >::value
    >::type>
{
  typedef void asio_associated_cancellation_slot_is_unspecialised;
};

} // namespace detail
} // namespace asio

