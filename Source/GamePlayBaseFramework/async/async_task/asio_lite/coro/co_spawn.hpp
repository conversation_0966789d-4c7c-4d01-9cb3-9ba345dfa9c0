#pragma once

#include <exception>

#include "async_task/async_task_config.hpp"

#include "async_task/asio_lite/coro/awaitable.hpp"
#include "async_task/asio_lite/executor.hpp"
#include "async_task/asio_lite/execution_context.hpp"
#include "async_task/asio_lite/concepts/is_executor.hpp"
#include "async_task/asio_lite/tools/thread_context_type_traits.hpp"
#include "async_task/asio_lite/operation/handler/async_result.hpp"

// clang-format off

namespace asio {
namespace detail {

template <typename T>
struct awaitable_signature;

template <typename T, typename Executor>
struct awaitable_signature<awaitable<T, Executor>>
{
  using type = void(std::exception_ptr, T);
};

template <typename Executor>
struct awaitable_signature<awaitable<void, Executor>>
{
  using type = void(std::exception_ptr);
};

} // namespace detail

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ex The executor that will be used to schedule the new thread of
 * execution.
 *
 * @param a The asio::awaitable object that is the result of calling the
 * coroutine's entry point function.
 *
 * @param token The @ref completion_token that will handle the notification that
 * the thread of execution has completed. The function signature of the
 * completion handler must be:
 * @code void handler(std::exception_ptr, T); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr, T) @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<std::size_t> echo(tcp::socket socket)
 * {
 *   std::size_t bytes_transferred = 0;
 *
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *
 *       bytes_transferred += n;
 *     }
 *   }
 *   catch (const std::exception&)
 *   {
 *   }
 *
 *   co_return bytes_transferred;
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_executor,
 *   echo(std::move(my_tcp_socket)),
 *   [](std::exception_ptr e, std::size_t n)
 *   {
 *     std::cout << "transferred " << n << "\n";
 *   });
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
  typename Executor, 
  typename T, 
  typename AwaitableExecutor,
  typename CompletionToken
> requires (
    is_executor<Executor>::value
    && std::is_convertible<Executor, AwaitableExecutor>::value
    && !std::is_void_v<T>
    && completion_token_for<CompletionToken, void(std::exception_ptr, T)>
  )
inline auto co_spawn(
    const Executor& ex, 
    awaitable<T, AwaitableExecutor> a,
    CompletionToken&& token
      /*ASIO_DEFAULT_COMPLETION_TOKEN(Executor)*/);

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ex The executor that will be used to schedule the new thread of
 * execution.
 *
 * @param a The asio::awaitable object that is the result of calling the
 * coroutine's entry point function.
 *
 * @param token The @ref completion_token that will handle the notification that
 * the thread of execution has completed. The function signature of the
 * completion handler must be:
 * @code void handler(std::exception_ptr); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr) @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<void> echo(tcp::socket socket)
 * {
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *     }
 *   }
 *   catch (const std::exception& e)
 *   {
 *     std::cerr << "Exception: " << e.what() << "\n";
 *   }
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_executor,
 *   echo(std::move(my_tcp_socket)),
 *   asio::detached);
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
  typename Executor, 
  typename AwaitableExecutor,
  typename CompletionToken
> requires(
    is_executor<Executor>::value
    && std::is_convertible<Executor, AwaitableExecutor>::value
    && completion_token_for<CompletionToken, void(std::exception_ptr)>
  )
inline auto co_spawn(
    const Executor& ex, 
    awaitable<void, AwaitableExecutor> a,
    CompletionToken&& token);

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ctx An execution context that will provide the executor to be used to
 * schedule the new thread of execution.
 *
 * @param a The asio::awaitable object that is the result of calling the
 * coroutine's entry point function.
 *
 * @param token The @ref completion_token that will handle the notification that
 * the thread of execution has completed. The function signature of the
 * completion handler must be:
 * @code void handler(std::exception_ptr); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr, T) @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<std::size_t> echo(tcp::socket socket)
 * {
 *   std::size_t bytes_transferred = 0;
 *
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *
 *       bytes_transferred += n;
 *     }
 *   }
 *   catch (const std::exception&)
 *   {
 *   }
 *
 *   co_return bytes_transferred;
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_io_context,
 *   echo(std::move(my_tcp_socket)),
 *   [](std::exception_ptr e, std::size_t n)
 *   {
 *     std::cout << "transferred " << n << "\n";
 *   });
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
  typename ExecutionContext, 
  typename T, 
  typename AwaitableExecutor,
  typename CompletionToken
> requires(
    std::is_convertible<ExecutionContext&, execution_context&>::value
    && std::is_convertible<typename ExecutionContext::executor_type, AwaitableExecutor>::value
    && !std::is_void_v<T>
    && completion_handler_for<void(std::exception_ptr, T)>
  ) 
inline auto co_spawn(
    ExecutionContext& ctx, 
    awaitable<T, AwaitableExecutor> a, 
    CompletionToken&& token
);

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ctx An execution context that will provide the executor to be used to
 * schedule the new thread of execution.
 *
 * @param a The asio::awaitable object that is the result of calling the
 * coroutine's entry point function.
 *
 * @param token The @ref completion_token that will handle the notification that
 * the thread of execution has completed. The function signature of the
 * completion handler must be:
 * @code void handler(std::exception_ptr); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr) @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<void> echo(tcp::socket socket)
 * {
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *     }
 *   }
 *   catch (const std::exception& e)
 *   {
 *     std::cerr << "Exception: " << e.what() << "\n";
 *   }
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_io_context,
 *   echo(std::move(my_tcp_socket)),
 *   asio::detached);
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
  typename ExecutionContext, 
  typename AwaitableExecutor,
  typename CompletionToken
> requires(
    std::is_convertible<
        ExecutionContext&, 
        execution_context&
      >::value 
    && std::is_convertible<
        typename ExecutionContext::executor_type, 
        AwaitableExecutor
      >::value
    && completion_token_for<CompletionToken, void(std::exception_ptr)>
  )
inline auto co_spawn(
    ExecutionContext& ctx, 
    awaitable<void, AwaitableExecutor> a,
    CompletionToken&& token
);

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ex The executor that will be used to schedule the new thread of
 * execution.
 *
 * @param f A nullary function object with a return type of the form
 * @c asio::awaitable<R,E> that will be used as the coroutine's entry
 * point.
 *
 * @param token The @ref completion_token that will handle the notification
 * that the thread of execution has completed. If @c R is @c void, the function
 * signature of the completion handler must be:
 *
 * @code void handler(std::exception_ptr); @endcode
 * Otherwise, the function signature of the completion handler must be:
 * @code void handler(std::exception_ptr, R); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr, R) @endcode
 * where @c R is the first template argument to the @c awaitable returned by the
 * supplied function object @c F:
 * @code asio::awaitable<R, AwaitableExecutor> F() @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<std::size_t> echo(tcp::socket socket)
 * {
 *   std::size_t bytes_transferred = 0;
 *
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *
 *       bytes_transferred += n;
 *     }
 *   }
 *   catch (const std::exception&)
 *   {
 *   }
 *
 *   co_return bytes_transferred;
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_executor,
 *   [socket = std::move(my_tcp_socket)]() mutable
 *     -> asio::awaitable<void>
 *   {
 *     try
 *     {
 *       char data[1024];
 *       for (;;)
 *       {
 *         std::size_t n = co_await socket.async_read_some(
 *             asio::buffer(data), asio::use_awaitable);
 *
 *         co_await asio::async_write(socket,
 *             asio::buffer(data, n), asio::use_awaitable);
 *       }
 *     }
 *     catch (const std::exception& e)
 *     {
 *       std::cerr << "Exception: " << e.what() << "\n";
 *     }
 *   }, asio::detached);
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
  typename Executor, 
  typename F,
  typename CompletionToken
> requires(
    is_executor<Executor>::value
    && completion_token_for<
          CompletionToken, 
          typename detail::awaitable_signature<
              typename std::invoke_result<F>::type
          >::type 
    >
  )
inline auto co_spawn(
    const Executor& ex, 
    F&& f,
    CompletionToken&& token
);

/// Spawn a new coroutined-based thread of execution.
/**
 * @param ctx An execution context that will provide the executor to be used to
 * schedule the new thread of execution.
 *
 * @param f A nullary function object with a return type of the form
 * @c asio::awaitable<R,E> that will be used as the coroutine's entry
 * point.
 *
 * @param token The @ref completion_token that will handle the notification
 * that the thread of execution has completed. If @c R is @c void, the function
 * signature of the completion handler must be:
 *
 * @code void handler(std::exception_ptr); @endcode
 * Otherwise, the function signature of the completion handler must be:
 * @code void handler(std::exception_ptr, R); @endcode
 *
 * @par Completion Signature
 * @code void(std::exception_ptr, R) @endcode
 * where @c R is the first template argument to the @c awaitable returned by the
 * supplied function object @c F:
 * @code asio::awaitable<R, AwaitableExecutor> F() @endcode
 *
 * @par Example
 * @code
 * asio::awaitable<std::size_t> echo(tcp::socket socket)
 * {
 *   std::size_t bytes_transferred = 0;
 *
 *   try
 *   {
 *     char data[1024];
 *     for (;;)
 *     {
 *       std::size_t n = co_await socket.async_read_some(
 *           asio::buffer(data), asio::use_awaitable);
 *
 *       co_await asio::async_write(socket,
 *           asio::buffer(data, n), asio::use_awaitable);
 *
 *       bytes_transferred += n;
 *     }
 *   }
 *   catch (const std::exception&)
 *   {
 *   }
 *
 *   co_return bytes_transferred;
 * }
 *
 * // ...
 *
 * asio::co_spawn(my_io_context,
 *   [socket = std::move(my_tcp_socket)]() mutable
 *     -> asio::awaitable<void>
 *   {
 *     try
 *     {
 *       char data[1024];
 *       for (;;)
 *       {
 *         std::size_t n = co_await socket.async_read_some(
 *             asio::buffer(data), asio::use_awaitable);
 *
 *         co_await asio::async_write(socket,
 *             asio::buffer(data, n), asio::use_awaitable);
 *       }
 *     }
 *     catch (const std::exception& e)
 *     {
 *       std::cerr << "Exception: " << e.what() << "\n";
 *     }
 *   }, asio::detached);
 * @endcode
 *
 * @par Per-Operation Cancellation
 * The new thread of execution is created with a cancellation state that
 * supports @c cancellation_type::terminal values only. To change the
 * cancellation state, call asio::this_coro::reset_cancellation_state.
 */
template <
    typename ExecutionContext, 
    typename F,
    typename CompletionToken 
> requires(
    std::is_convertible<ExecutionContext&, execution_context&>::value
    && completion_token_for<
      CompletionToken,
      typename detail::awaitable_signature<
        typename std::invoke_result<F>::type
      >::type
    >
  )
inline auto co_spawn(
    ExecutionContext& ctx, 
    F&& f,
    CompletionToken&& token
);

} // namespace asio

// clang-format on

#include "async_task/asio_lite/coro/co_spawn.inl"


