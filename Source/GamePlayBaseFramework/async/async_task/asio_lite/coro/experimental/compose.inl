#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/associated_executor.hpp"
#include "async_task/asio_lite/cancellation/base_from_cancellation_state.hpp"
#include "async_task/asio_lite/operation/handler/handler_alloc_helpers.hpp"
#include "async_task/asio_lite/operation/handler/handler_cont_helpers.hpp"
#include "async_task/asio_lite/operation/handler/handler_invoke_helpers.hpp"
#include "async_task/asio_lite/tools/thread_context_type_traits.hpp"
////#include "asio/execution/executor.hpp"
////#include "asio/execution/outstanding_work.hpp"
#include "async_task/asio_lite/executor_work_guard.hpp"
#include "async_task/asio_lite/concepts/is_executor.hpp"
#include "async_task/asio_lite/system_executor.hpp"

// clang-format off

namespace asio {

namespace detail
{
  template <typename Executor, typename = void>
  class composed_work_guard
  {
  public:
    ////typedef typename decay<
    ////    typename prefer_result<Executor,
    ////      execution::outstanding_work_t::tracked_t
    ////    >::type
    ////  >::type executor_type;
    using executor_type = typename std::decay<Executor>::type;

    composed_work_guard(const Executor& ex)
      ////: executor_(asio::prefer(ex, execution::outstanding_work.tracked))
      : executor_(ex)
    {
    }

    void reset()
    {
    }

    executor_type get_executor() const noexcept
    {
      return executor_;
    }

  private:
    executor_type executor_;
  };

  template <>
  struct composed_work_guard<system_executor>
  {
  public:
    using executor_type = system_executor;

    composed_work_guard(const system_executor&)
    {
    }

    void reset()
    {
    }

    executor_type get_executor() const noexcept
    {
      return system_executor();
    }
  };

  template <typename>
  struct composed_io_executors;

  template <>
  struct composed_io_executors<void()>
  {
    composed_io_executors() noexcept
      : head_(system_executor())
    {
    }

    typedef system_executor head_type;
    system_executor head_;
  };

  inline composed_io_executors<void()> make_composed_io_executors()
  {
    return composed_io_executors<void()>();
  }

  template <typename Head>
  struct composed_io_executors<void(Head)>
  {
    explicit composed_io_executors(const Head& ex) noexcept
      : head_(ex)
    {
    }

    typedef Head head_type;
    Head head_;
  };

  template <typename Head>
  inline composed_io_executors<void(Head)>
  make_composed_io_executors(const Head& head)
  {
    return composed_io_executors<void(Head)>(head);
  }

  template <typename Head, typename... Tail>
  struct composed_io_executors<void(Head, Tail...)>
  {
    explicit composed_io_executors(const Head& head,
        const Tail&... tail) noexcept
      : head_(head),
        tail_(tail...)
    {
    }

    void reset()
    {
      head_.reset();
      tail_.reset();
    }

    typedef Head head_type;
    Head head_;
    composed_io_executors<void(Tail...)> tail_;
  };

  template <typename Head, typename... Tail>
  inline composed_io_executors<void(Head, Tail...)>
  make_composed_io_executors(const Head& head, const Tail&... tail)
  {
    return composed_io_executors<void(Head, Tail...)>(head, tail...);
  }



  template <typename>
  struct composed_work;

  template <>
  struct composed_work<void()>
  {
    typedef composed_io_executors<void()> executors_type;

    composed_work(const executors_type&) noexcept
      : head_(system_executor())
    {
    }

    void reset()
    {
      head_.reset();
    }

    using head_type = system_executor ;
    composed_work_guard<system_executor> head_;
  };

  template <typename Head>
  struct composed_work<void(Head)>
  {
    typedef composed_io_executors<void(Head)> executors_type;

    explicit composed_work(const executors_type& ex) noexcept
      : head_(ex.head_)
    {
    }

    void reset()
    {
      head_.reset();
    }

    typedef Head head_type;
    composed_work_guard<Head> head_;
  };

  template <typename Head, typename... Tail>
  struct composed_work<void(Head, Tail...)>
  {
    typedef composed_io_executors<void(Head, Tail...)> executors_type;

    explicit composed_work(const executors_type& ex) noexcept
      : head_(ex.head_),
        tail_(ex.tail_)
    {
    }

    void reset()
    {
      head_.reset();
      tail_.reset();
    }

    typedef Head head_type;
    composed_work_guard<Head> head_;
    composed_work<void(Tail...)> tail_;
  };


  template <
    typename Impl, 
    typename Work, 
    typename Handler, 
    typename Signature
  >
  class composed_op;

  template <
    typename Impl, 
    typename Work, 
    typename Handler,
    typename R, 
    typename... Args
  >
  class composed_op<Impl, Work, Handler, R(Args...)>
    : public base_from_cancellation_state<Handler>
  {
  public:
    template <typename I, typename W, typename H>
    composed_op(I&& impl, W&& work, H&& handler)
      : base_from_cancellation_state<Handler>( handler, enable_terminal_cancellation()),
        impl_(static_cast<I&&>(impl)),
        work_(static_cast<W&&>(work)),
        handler_(static_cast<H&&>(handler)),
        invocations_(0)
    {
    }

    composed_op(composed_op&& other)
      : base_from_cancellation_state<Handler>(
          static_cast<base_from_cancellation_state<Handler>&&>(other)),
        impl_(static_cast<Impl&&>(other.impl_)),
        work_(static_cast<Work&&>(other.work_)),
        handler_(static_cast<Handler&&>(other.handler_)),
        invocations_(other.invocations_)
    {
    }

    typedef typename associated_executor<Handler,
        typename composed_work_guard<
          typename Work::head_type
        >::executor_type
      >::type executor_type;

    executor_type get_executor() const noexcept
    {
      return (get_associated_executor)(handler_, work_.head_.get_executor());
    }

    typedef typename associated_allocator<Handler,
      std::allocator<void> >::type allocator_type;

    allocator_type get_allocator() const noexcept
    {
      return (get_associated_allocator)(handler_, std::allocator<void>());
    }

    template<typename... T>
    void operator()(T&&... t)
    {
      if (invocations_ < ~0u)
        ++invocations_;
      this->get_cancellation_state().slot().clear();
      impl_(*this, static_cast<T&&>(t)...);
    }

    void complete(Args... args)
    {
      this->work_.reset();
      static_cast<Handler&&>(this->handler_)(
          static_cast<Args&&>(args)...);
    }



    void reset_cancellation_state()
    {
      base_from_cancellation_state<Handler>::reset_cancellation_state(handler_);
    }

    template <typename Filter>
    void reset_cancellation_state(Filter&& filter)
    {
      base_from_cancellation_state<Handler>::reset_cancellation_state(handler_,
          static_cast<Filter&&>(filter));
    }

    template <typename InFilter, typename OutFilter>
    void reset_cancellation_state(InFilter&& in_filter,
        OutFilter&& out_filter)
    {
      base_from_cancellation_state<Handler>::reset_cancellation_state(handler_,
          static_cast<InFilter&&>(in_filter),
          static_cast<OutFilter&&>(out_filter));
    }

  //private:
    Impl impl_;
    Work work_;
    Handler handler_;
    unsigned invocations_;
  };

  template <typename Impl, typename Work, typename Handler, typename Signature>
  inline bool asio_handler_is_continuation(
      composed_op<Impl, Work, Handler, Signature>* this_handler)
  {
    return this_handler->invocations_ > 1 ? true
      : asio_handler_cont_helpers::is_continuation(
          this_handler->handler_);
  }

  template <typename Signature, typename Executors>
  class initiate_composed_op
  {
  public:
    typedef typename composed_io_executors<Executors>::head_type executor_type;

    template <typename T>
    explicit initiate_composed_op(int, T&& executors)
      : executors_(static_cast<T&&>(executors))
    {
    }

    executor_type get_executor() const noexcept
    {
      return executors_.head_;
    }

    template <typename Handler, typename Impl>
    void operator()(Handler&& handler, Impl&& impl) const
    {
      composed_op<typename std::decay<Impl>::type, composed_work<Executors>,
        typename std::decay<Handler>::type, Signature>(
          static_cast<Impl&&>(impl),
          composed_work<Executors>(executors_),
          static_cast<Handler&&>(handler))();
    }

  private:
    composed_io_executors<Executors> executors_;
  };

  template <typename Signature, typename Executors>
  inline auto make_initiate_composed_op(
      composed_io_executors<Executors>&& executors)
  {
    return initiate_composed_op<Signature, Executors>(0,
        static_cast<composed_io_executors<Executors>&&>(executors));
  }

  template <typename IoObject>
    requires(!is_executor<IoObject>::value)
  inline auto get_composed_io_executor(IoObject& io_object)
  {
    return io_object.get_executor();
  }

  template <typename Executor>
    requires is_executor<Executor>::value
  inline const Executor& get_composed_io_executor(const Executor& ex)
  {
    return ex;
  }
} // namespace detail


template <
  template <typename, typename> class Associator,
  typename Impl, typename Work, typename Handler,
    typename Signature, typename DefaultCandidate>
struct associator<Associator,
    detail::composed_op<Impl, Work, Handler, Signature>,
    DefaultCandidate>
  : Associator<Handler, DefaultCandidate>
{
  static typename Associator<Handler, DefaultCandidate>::type get(
      const detail::composed_op<Impl, Work, Handler, Signature>& h,
      const DefaultCandidate& c = DefaultCandidate()) noexcept
  {
    return Associator<Handler, DefaultCandidate>::get(h.handler_, c);
  }
};


template<
  typename CompletionToken, 
  typename Signature,
  typename Implementation, 
  typename... IoObjectsOrExecutors
>
auto async_compose(
  Implementation&& implementation,
  CompletionToken& token,
  IoObjectsOrExecutors&&... io_objects_or_executors)
{
  return async_initiate<CompletionToken, Signature>(
      detail::make_initiate_composed_op<Signature>(
        detail::make_composed_io_executors(
          detail::get_composed_io_executor(
            static_cast<IoObjectsOrExecutors&&>(io_objects_or_executors))...)),
      token, 
      static_cast<Implementation&&>(implementation) );
}


} // namespace asio

// clang-format on

