#pragma once

#include "async_task/async_task_config.hpp"

#include "async_task/asio_lite/tools/associator.hpp"
#include "async_task/asio_lite/operation/handler/async_result.hpp"
#include "async_task/asio_lite/operation/handler/handler_alloc_helpers.hpp"
#include "async_task/asio_lite/operation/handler/handler_cont_helpers.hpp"
#include "async_task/asio_lite/operation/handler/handler_invoke_helpers.hpp"
#include "async_task/asio_lite/tools/thread_context_type_traits.hpp"


namespace asio {
namespace experimental {
namespace detail {

// Class to adapt a prepend_t as a completion handler.
template <typename Handler, typename... Values>
class prepend_handler
{
public:
  typedef void result_type;

  template <typename H>
  prepend_handler(H&& handler, std::tuple<Values...> values)
    : handler_(static_cast<H&&>(handler)),
      values_(static_cast<std::tuple<Values...>&&>(values))
  {
  }

  template <typename... Args>
  void operator()(Args&&... args)
  {
    this->invoke(
        std::make_index_sequence<sizeof...(Values)>{},
        static_cast<Args&&>(args)...);
  }

  template <std::size_t... I, typename... Args>
  void invoke(std::index_sequence<I...>, Args&&... args)
  {
    static_cast<Handler&&>(handler_)(
        static_cast<Values&&>(std::get<I>(values_))...,
        static_cast<Args&&>(args)...);
  }

//private:
  Handler handler_;
  std::tuple<Values...> values_;
};

template <typename Handler>
inline bool asio_handler_is_continuation(
    prepend_handler<Handler>* this_handler)
{
  return asio_handler_cont_helpers::is_continuation(
        this_handler->handler_);
}

template <typename Signature, typename... Values>
struct prepend_signature;

template <typename R, typename... Args, typename... Values>
struct prepend_signature<R(Args...), Values...>
{
  typedef R type(Values..., typename std::decay<Args>::type...);
};

} // namespace detail
} // namespace experimental

template <typename CompletionToken, typename... Values, typename Signature>
struct async_result<
    experimental::prepend_t<CompletionToken, Values...>, Signature>
  : async_result<CompletionToken,
      typename experimental::detail::prepend_signature<
        Signature, Values...>::type>
{
  typedef typename experimental::detail::prepend_signature<
      Signature, Values...>::type signature;

  template <typename Initiation>
  struct init_wrapper
  {
    init_wrapper(Initiation init)
      : initiation_(static_cast<Initiation&&>(init))
    {
    }

    template <typename Handler, typename... Args>
    void operator()(
        Handler&& handler,
        std::tuple<Values...> values,
        Args&&... args)
    {
      static_cast<Initiation&&>(initiation_)(
          experimental::detail::prepend_handler<
            typename std::decay<Handler>::type, Values...>(
              static_cast<Handler&&>(handler),
              static_cast<std::tuple<Values...>&&>(values)),
          static_cast<Args&&>(args)...);
    }

    Initiation initiation_;
  };

  template <typename Initiation, typename RawCompletionToken, typename... Args>
  static auto initiate(
      Initiation&& initiation,
      RawCompletionToken&& token,
      Args&&... args)
  {
    return async_initiate<CompletionToken, signature>(
        init_wrapper<typename std::decay<Initiation>::type>(
          static_cast<Initiation&&>(initiation)),
        token.token_,
        static_cast<std::tuple<Values...>&&>(token.values_),
        static_cast<Args&&>(args)...);
  }
};

template <template <typename, typename> class Associator,
    typename Handler, typename... Values, typename DefaultCandidate>
struct associator<Associator,
    experimental::detail::prepend_handler<Handler, Values...>, DefaultCandidate>
  : Associator<Handler, DefaultCandidate>
{
  static auto get(
      const experimental::detail::prepend_handler<Handler, Values...>& h,
      const DefaultCandidate& c = DefaultCandidate()) noexcept
  {
    return Associator<Handler, DefaultCandidate>::get(h.handler_, c);
  }
};


} // namespace asio

