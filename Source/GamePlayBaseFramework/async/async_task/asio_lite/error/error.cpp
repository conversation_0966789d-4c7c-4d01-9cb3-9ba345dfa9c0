#include "async_task/asio_lite/error/error.hpp"

#include <string>

namespace asio {
namespace error {

namespace detail {

class misc_category : public asio::error_category
{
public:
  const char* name() const noexcept override
  {
    return "asio.misc";
  }

  std::string message(int value) const override
  {
    if (value == error::already_open)
      return "Already open";
    if (value == error::eof)
      return "End of file";
    if (value == error::not_found)
      return "Element not found";
    if (value == error::fd_set_failure)
      return "The descriptor does not fit into the select call's fd_set";
    return "asio.misc error";
  }
};

} // namespace detail

const asio::error_category& get_misc_category()
{
  static detail::misc_category instance;
  return instance;
}

} // namespace error
} // namespace asio

