#pragma once

#include <exception>
#include "async_task/async_task_config.hpp"


namespace asio {


/// Exception thrown when there are multiple pending exceptions to rethrow.
class multiple_exceptions
  : public std::exception
{
public:
  /// Constructor.
  ASYNC_TASKS_API multiple_exceptions(
      std::exception_ptr first) noexcept;

  /// Obtain message associated with exception.
  ASYNC_TASKS_API virtual const char* what() const noexcept;

  /// Obtain a pointer to the first exception.
  ASYNC_TASKS_API std::exception_ptr first_exception() const;

private:
  std::exception_ptr first_;
};


} // namespace asio

