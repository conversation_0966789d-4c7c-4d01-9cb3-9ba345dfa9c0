#include "async_task/asio_lite/error/throw_error.hpp"
#include "async_task/asio_lite/error/throw_exception.hpp"
#include "async_task/asio_lite/error/system_error.hpp"

namespace asio {
namespace detail {

void do_throw_error(const asio::error_code& err)
{
  asio::system_error e(err);
  asio::detail::throw_exception(e);
}

void do_throw_error(const asio::error_code& err, const char* location)
{
  // boostify: non-boost code starts here
  // Microsoft's implementation of std::system_error is non-conformant in that
  // it ignores the error code's message when a "what" string is supplied. We'll
  // work around this by explicitly formatting the "what" string.
  std::string what_msg = location;
  what_msg += ": ";
  what_msg += err.message();
  asio::system_error e(err, what_msg);
  asio::detail::throw_exception(e);
}

} // namespace detail
} // namespace asio
