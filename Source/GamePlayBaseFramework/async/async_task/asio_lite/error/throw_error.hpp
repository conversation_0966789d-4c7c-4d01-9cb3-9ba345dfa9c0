#pragma once

#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/error/error_code.hpp"

namespace asio {
namespace detail {

ASYNC_TASKS_API void do_throw_error(const asio::error_code& err);

ASYNC_TASKS_API void do_throw_error(const asio::error_code& err,
    const char* location);

inline void throw_error(const asio::error_code& err)
{
  if (err)
    do_throw_error(err);
}

inline void throw_error(const asio::error_code& err,
    const char* location)
{
  if (err)
    do_throw_error(err, location);
}

} // namespace detail
} // namespace asio

