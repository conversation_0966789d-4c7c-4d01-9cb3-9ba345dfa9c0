#include "async_task/asio_lite/sync_primitives/null_event.hpp"

# include <thread>

#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
# define WIN32_LEAN_AND_MEAN
# include <windows.h>
#else
# include <unistd.h>
# if defined(__hpux)
#  include <sys/time.h>
# endif
# if !defined(__hpux) || defined(__SELECT)
#  include <sys/select.h>
# endif
#endif


namespace asio {
namespace detail {

void null_event::do_wait()
{
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  std::this_thread::sleep_until((std::chrono::steady_clock::time_point::max)());
#else
  ::pause();
#endif
}

void null_event::do_wait_for_usec(long usec)
{
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32
  std::this_thread::sleep_for(std::chrono::microseconds(usec));
#elif defined(__hpux) && defined(__SELECT)
  timespec ts;
  ts.tv_sec = usec / 1000000;
  ts.tv_nsec = (usec % 1000000) * 1000;
  ::pselect(0, 0, 0, 0, &ts, 0);
#else
  timeval tv;
  tv.tv_sec = usec / 1000000;
  tv.tv_usec = usec % 1000000;
  ::select(0, 0, 0, 0, &tv);
#endif
}

} // namespace detail
} // namespace asio

