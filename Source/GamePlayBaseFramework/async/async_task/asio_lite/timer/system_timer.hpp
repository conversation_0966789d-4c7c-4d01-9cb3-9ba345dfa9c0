#pragma once

#include <chrono>
#include "async_task/async_task_config.hpp"
#include "async_task/asio_lite/timer/basic_waitable_timer.hpp"

namespace asio {

/// Typedef for a timer based on the system clock.
/**
 * This typedef uses the C++11 @c &lt;chrono&gt; standard library facility, if
 * available. Otherwise, it may use the Boost.Chrono library. To explicitly
 * utilise Boost.Chrono, use the basic_waitable_timer template directly:
 * @code
 * typedef basic_waitable_timer<boost::chrono::system_clock> timer;
 * @endcode
 */
using system_timer = basic_waitable_timer<std::chrono::system_clock>;

} // namespace asio


