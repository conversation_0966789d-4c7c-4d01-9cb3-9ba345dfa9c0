#include "async_task/asio_lite/timer/timer_queue_set.hpp"

namespace asio {
namespace detail {

timer_queue_set::timer_queue_set()
  : first_(0)
{
}

void timer_queue_set::insert(timer_queue_base* q)
{
  q->next_ = first_;
  first_ = q;
}

void timer_queue_set::erase(timer_queue_base* q)
{
  if (first_)
  {
    if (q == first_)
    {
      first_ = q->next_;
      q->next_ = 0;
      return;
    }

    for (timer_queue_base* p = first_; p->next_; p = p->next_)
    {
      if (p->next_ == q)
      {
        p->next_ = q->next_;
        q->next_ = 0;
        return;
      }
    }
  }
}

bool timer_queue_set::all_empty() const
{
  for (timer_queue_base* p = first_; p; p = p->next_)
    if (!p->empty())
      return false;
  return true;
}

long timer_queue_set::wait_duration_msec(long max_duration) const
{
  long min_duration = max_duration;
  for (timer_queue_base* p = first_; p; p = p->next_)
    min_duration = p->wait_duration_msec(min_duration);
  return min_duration;
}

long timer_queue_set::wait_duration_usec(long max_duration) const
{
  long min_duration = max_duration;
  for (timer_queue_base* p = first_; p; p = p->next_)
    min_duration = p->wait_duration_usec(min_duration);
  return min_duration;
}

void timer_queue_set::get_ready_timers(op_queue<operation>& ops)
{
  for (timer_queue_base* p = first_; p; p = p->next_)
    p->get_ready_timers(ops);
}

void timer_queue_set::get_all_timers(op_queue<operation>& ops)
{
  for (timer_queue_base* p = first_; p; p = p->next_)
    p->get_all_timers(ops);
}

} // namespace detail
} // namespace asio
