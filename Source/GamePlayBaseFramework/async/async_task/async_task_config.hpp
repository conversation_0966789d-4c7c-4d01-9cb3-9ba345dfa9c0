#pragma once

#include "platform.hpp"
#include "async_task/async_task_export.hpp"

#define ASIO_MOVE_ARG(T) T&&
#define ASIO_MOVE_CAST(T) static_cast<T&&>
#define ASIO_NONDEDUCED_MOVE_ARG(type) type&
#define ASIO_MOVE_OR_LVALUE(type) static_cast<type&&>
#define ASIO_MOVE_OR_LVALUE_TYPE(type) type

#define ASIO_HAS_CHRONO 1

#define ASIO_NO_DEPRECATED 1

#define ASIO_HAS_MOVE 1

#define ASIO_HAS_CXX11_ALLOCATORS 1

#define ASIO_HAS_STD_ALIGNED_ALLOC 1

#define ASIO_HAS_STD_ERROR_CODE 1

#define ASIO_HAS_STD_EXCEPTION_PTR 1

#define ASIO_HAS_NULLPTR 1

#define ASIO_HAS_NOEXCEPT 1

#define ASIO_HAS_NOEXCEPT_FUNCTION_TYPE 1

#define ASIO_HAS_ALIAS_TEMPLATES 1

#define ASIO_HAS_DECLTYPE 1

#if GBF_ENABLE_CPP20
# define ASIO_HAS_CONCEPTS 1
#endif

#define ASIO_HAS_VARIADIC_TEMPLATES 1

#define ASIO_HAS_DEFAULT_FUNCTION_TEMPLATE_ARGUMENTS 1

#define ASIO_HAS_THREADS 1

#define ASIO_HAS_RETURN_TYPE_DEDUCTION 1

#define ASIO_HAS_REF_QUALIFIED_FUNCTIONS 1

#define ASIO_NODISCARD [[nodiscard]]

#define ASIO_HAS_ENUM_CLASS 1

#define ASIO_HAS_CONSTEXPR 1

#define ASIO_NO_TS_EXECUTORS 1
////#define ASIO_HAS_IOCP 1


#define ASIO_UNUSED_TYPEDEF


#define ASIO_HAS_ALIGNOF 1
#if defined(ASIO_HAS_ALIGNOF)
  #define ASIO_ALIGNOF(T) alignof(T)
  #if defined(__STDCPP_DEFAULT_NEW_ALIGNMENT__)
    #define ASIO_DEFAULT_ALIGN __STDCPP_DEFAULT_NEW_ALIGNMENT__
  #elif defined(__GNUC__)
    #if ((__GNUC__ == 4) && (__GNUC_MINOR__ >= 9)) || (__GNUC__ > 4)
      #define ASIO_DEFAULT_ALIGN alignof(std::max_align_t)
    #else  // ((__GNUC__ == 4) && (__GNUC_MINOR__ >= 9)) || (__GNUC__ > 4)
      #define ASIO_DEFAULT_ALIGN alignof(max_align_t)
    #endif  // ((__GNUC__ == 4) && (__GNUC_MINOR__ >= 9)) || (__GNUC__ > 4)
  #else     // defined(__GNUC__)
    #define ASIO_DEFAULT_ALIGN alignof(std::max_align_t)
  #endif  // defined(__GNUC__)
#else     // defined(ASIO_HAS_ALIGNOF)
  #define ASIO_ALIGNOF(T) 1
  #define ASIO_DEFAULT_ALIGN 1
#endif  // defined(ASIO_HAS_ALIGNOF)

#if GBF_ENABLE_CPP20
# define ASIO_HAS_STD_COROUTINE 1
#endif


