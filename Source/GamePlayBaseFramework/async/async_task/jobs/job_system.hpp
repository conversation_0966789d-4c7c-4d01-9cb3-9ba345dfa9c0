#pragma once

#include <vector>
#include "async_task/jobs/jobs_define.hpp"
#include "async_task/tasks/tasks_define.hpp"

namespace gbf::jobs {

class timer_quest_helper;

class ASYNC_TASKS_API job_system {
 public:
  job_system();

  virtual ~job_system();

 public:
  void init_in_main_thread();

  bool add_new_slot(JobType job_type, int work_thread_num);

  bool check_slot_array_no_empty_item() const;

  void destroy();

  void update();

  void post(thread_job_function&& jobFunc, JobType slot_id = JobType::kLogicJob);

  void dispatch(thread_job_function&& jobFunc, JobType job_type = JobType::kLogicJob);

  // coroutines api here
  void dispatch_async_task(coro::async_task_ptr&& atask, JobType job_type = JobType::kLogicJob);

  job_strand_ptr request_strand(JobType slot_id = JobType::kLogicJob);

  job_waiter_ptr request_waiter();

  job_notify_ptr request_notify();

  job_ticket_ptr request_ticket();

  job_fence_ptr request_fence();

  int hardware_cores();

  static bool this_thread_is_logic_slot();

  static JobType this_thread_job_type();

  static const char* this_thread_job_type_name();

  static int this_thread_work_index();

  static bool this_thread_slot_type_is(JobType slot_id = JobType::kLogicJob);

  static void register_external_thread(const char* threadName);

  void reset_main_thread();

  uint64_t add_always_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms);

  uint64_t add_always_run_job_no_skip(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms);

  uint64_t add_times_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long period_time_ms, unsigned int run_count);

  uint64_t add_delay_run_job(JobType slot_id, thread_job_function&& period_job, unsigned long delay_time_ms);

  void kill_timer_job(uint64_t tid);

  std::string get_thread_name(JobType slot_id, int thread_index);

  coro::coro_service_manager& coro_service_manager() { return *coro_service_manager_; }

 protected:
  using job_system_slot_array = std::vector<job_system_slot_ptr>;
  job_system_slot_array job_system_slot_array_;
  timer_quest_helper* timer_quest_helper_;

  coro::coro_service_manager* coro_service_manager_ = nullptr;
};

}  // namespace gbf::jobs
