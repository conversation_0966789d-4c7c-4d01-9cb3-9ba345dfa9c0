#pragma once

////#include <atomic>
#include "async_task/async_task_export.hpp"
#include "core/thread/normal_mutex.h"

namespace gbf::jobs {

class ASYNC_TASKS_API job_ticket {
 public:
  class ticket_keeper {
   public:
    ticket_keeper() = delete;

    ticket_keeper(job_ticket& ticket) : ticket_(ticket) { is_keeped_ = ticket_.try_to_keep(); }

    ~ticket_keeper() { ticket_.release_keep(); }

    bool IsKeeped() const noexcept { return is_keeped_; }

   private:
    job_ticket& ticket_;
    bool is_keeped_;
  };

 public:
  job_ticket();
  ~job_ticket();

  bool is_expired();

  void discard();

 protected:
  bool try_to_keep();
  void release_keep();

 protected:
  volatile int expired_flag_;
  threads::NormalRecursiveMutex mutex_;
  bool current_keep_ = false;
};

}  // namespace gbf::jobs
