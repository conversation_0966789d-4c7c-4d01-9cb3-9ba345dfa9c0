#include "async_task/tasks/async_task17.hpp"
#include "async_task/jobs/job_system_slot.hpp"
#include "core/core_global.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf::coro {

async_task17::async_task17(uint64_t taskId, coro::coroutine_handle17 coHandle, coro_service_manager* manager)
    : iasync_task(taskId, *manager), co_handle_(coHandle) {
  implement_type_ = CoroImplementType::kCxx17;
}

async_task17::~async_task17() {
  co_handle_.destroy();
  co_handle_ = nullptr;
}

CoroRunningState async_task17::resume_impl(JobType job_type) {
  // Just clear system call before resume
  if (current_awaitable_) {
    // Do await resume here~~
    current_awaitable_->invoke_resume(this, &manager_);
    clear_awaitable();
  }

  co_handle_.resume();
  // ClearResumeObject();	//Clear resume object here

  return co_handle_.get_state();
}

AwaitMode async_task17::do_awaitable_suspend_impl() {
  LOG_PROCESS_ERROR(has_awaitable());

  { current_awaitable_->invoke_suspend(this, &manager_); }
Exit0:
  return await_mode_;
}

void async_task17::do_awaitable_resume_impl() {}

}  // namespace gbf::coro
