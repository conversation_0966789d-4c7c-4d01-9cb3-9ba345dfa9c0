#pragma once

#include <queue>
#include <unordered_set>

#include "async_task/asio_lite/execution_context.hpp"
#include "async_task/jobs/jobs_define.hpp"
#include "async_task/tasks/tasks_define.hpp"

#include "reflection/meta/meta_class.hpp"
#include "reflection/objects/userobject.hpp"

#include "async_task/tasks/iasync_task.hpp"
////#include "async_task/tasks/resume_object.hpp"

#include "async_task/tasks/async_task17.hpp"

#if defined(GBF_ENABLE_CPP20)
#include "async_task/tasks/async_task20.hpp"
#include "async_task/tasks/coroutines/cotask20.hpp"
////#include "async_task/tasks/await_handle20.h"
#endif

namespace gbf::coro {

class core_service_id : public asio::execution_context::id {};
//-----------------------------------------------------------------------------------------------
class ASYNC_TASKS_API coro_service : public asio::execution_context::service {
 public:
  coro_service() = delete;
  coro_service(asio::execution_context& _context, jobs::job_system_slot& _job_slot, coro_service_manager& _parent_manager, bool _support_next_frame)
      : asio::execution_context::service(_context),
        job_slot_(_job_slot),
        parent_manager_(_parent_manager),
        support_next_frame_(_support_next_frame) {}

  jobs::job_system_slot& job_slot() const noexcept { return job_slot_; }

  coro_service_manager& parent_manager() const noexcept { return parent_manager_; }

  void dispatch(const async_task_ptr& async_task);

  void awake_next_frame_queue();

  bool support_next_frame() const noexcept { return support_next_frame_; }

  static core_service_id id;

 private:
  void add_to_next_frame_queue(const async_task_ptr& async_task) { next_frame_queue_.emplace(async_task); }
  void after_suspend_handle(const async_task_ptr& async_task, AwaitMode amode, uint64_t atimeout_ms);

  void register_timeout_for_task(const async_task_ptr& async_task, uint64_t atimeout_ms);

  void shutdown() override{};

 private:
  jobs::job_system_slot& job_slot_;
  coro_service_manager& parent_manager_;

  bool support_next_frame_ = false;
  std::queue<async_task_ptr> next_frame_queue_;
};

}  // namespace gbf::coro
