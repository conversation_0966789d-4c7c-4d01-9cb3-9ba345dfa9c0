#pragma once

#include "async_task/async_task_export.hpp"
#include "async_task/tasks/tasks_define.hpp"

namespace gbf::coro {
  struct ASYNC_TASKS_API coro_util {
    static void awake_one_task_in_manager_with_nothing(coro_service_manager* manager, uint64_t tid);

    static void awake_one_task_in_manager_with_user_object(coro_service_manager* manager, uint64_t tid, const reflection::UserObject& obj);
  };
}
