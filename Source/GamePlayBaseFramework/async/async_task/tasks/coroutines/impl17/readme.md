#### 一些说明 

1. switch case 机制导致的一些限制 
    - 协程函数 co_begin和co_return之间不能直接以int a = 1;这样的方式定义变量，只能int a;a = 1
    - 不能嵌套switch case

2. CoHander
    - 内部存储的数据为根据函数类型萃取的参数去掉引用修饰的类型，如CoroutineTask f(int, int&, std::pair<int, int>&, std::string)，在内部的存储为tuple<int, int, std::pair<int, int>, std::string>，实现co_start调用时传入的参数要能转为对应的类型就可以。

    - 与c++20的coroutines实现相比，这里多了一层参数类型的转换，如上面的f函数实际调用时可以是这样：f(1, 1, std::pair<int, int>(0, 0), "")，这里的const char*隐式转为string，同时，如果参数类型为引用，在c++20里，它的变化是对外部生效的，而对这里的实现来说，只是用来初始化内部tuple中的数据而已，函数参数的变化体现在tuple中。

    - 参数说明
    ```cpp
    struct LocalStruct
	{
		int local_i = 0;
		const char* local_p = nullptr;
	};

    // p: 传值，所以协程内部无论怎么改动，下次恢复时还是会以tuple中的值重新传递
    // c: 因为是引用，修改直接体现在tuple中，每次恢复时都能获得上次的值
    // local: 简单数据，单段使用的可以传值 
    // locals: 局部变量过多时，可以打包到struct中传递
    // 为了达到c++20的协程效果，这里理论上可以都以引用的方式传参，在替换为20版协程时，大多情况都需要替换成值传递
	CoroutineTask co_func(std::pair<int, int> p, int& c, int local, LocalStruct& locals)
	{
		co_begin();
		c = p.first + p.second + c;
		locals.local_i = 1024;
		locals.local_p = "balabala";
		INF_DEF("step1 %d", p.first);
		co_yield();
		INF_DEF("step2 %d", p.second);
		co_yield();
		INF_DEF("step3 %d", c);
		co_yield();
		INF_DEF("step4 %s", locals.local_p);
		co_end();
	}
    ```

3. CoHandler对象的回收
    - 当一个协程结束时，会自动析构对象，这里实现没有支持final_suspend，但是如果协程在未结束的状态下就不再需要，需要手动调用handle.destroy()。
    <!-- - 与c++20协程不一样的是，对象释放后，handle中的ptr会置空。 -->

4. 协程函数的返回值类型可自定义，要求如下：
	- 接受nullptr_t类型的隐式构造，如：constexpr CoroutineHandlestd::nullptr_t) noexcept {}
	- 接受CoroutineHandle作为参数的构造函数，如：CoroutineTaskBase(CoroutineHandle handler):m_handler(handler){}

	- 可选 static constexpr bool initial_suspend() { return false; } 函数定义，决定创建好协程后是否挂起，或者直接resume一次，默认false，自动resume。
	- 可选 static constexpr bool final_suspend() { return false; } 函数定义，决定结束后是否挂起，如果挂起，需要手动destroy，未定义此函数则默认false，不挂起。
	- 示例如下：
		```cpp
		struct CoroutineTaskBase
		{
			CoroutineHandle m_handler = nullptr;

			CoroutineTaskBase(std::nullptr_t) {}
			CoroutineTaskBase(CoroutineHandle handler)
				: m_handler(handler)
			{

			}

			// static constexpr bool initial_suspend() { return false; }
			// static constexpr bool final_suspend() { return false; }
		};
		```

#### TODO
    
- [ ] 支持bind()，lambda类型萃取

#### 示例见： app/co_task_test    