#include "async_task/tasks/default_awaitable17.hpp"
#include "async_task/tasks/async_task17.hpp"
#include "core/core_global.hpp"
#include "async_task/tasks/coro_service_manager.hpp"

namespace gbf::coro::tasks17 {
//-------------------------------------------------------------------------------------
void next_frame::invoke_suspend(async_task17* task, coro_service_manager* manager) { task->await_setting(AwaitMode::kAwaitNextframe); }
//-------------------------------------------------------------------------------------

void sleep::invoke_suspend(async_task17* task, coro_service_manager* manager) {
  task->await_setting(AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_);
}
//-------------------------------------------------------------------------------------
void wait_task_finish::invoke_suspend(async_task17* task, coro_service_manager* manager) {
  if (manager->request_wait_for_task_quit(task, task->work_job_type(), wait_task_id_, AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_)) {
    // wait suc
    task->await_setting(AwaitMode::kAwaitForNotifyWithTimeout, timeout_ms_);
  } else {
    task->await_setting(AwaitMode::kAwaitNever);  // run next frame, wait quest not exist
  }
}
//-----------------------------------------------------------------------------------------------
void transfer::invoke_suspend(async_task17* task, coro_service_manager* manager) {
  co_query_manager()->request_task_transfer(task, task->work_job_type(), target_job_type_);
  co_query_self()->await_setting(AwaitMode::kAwaitForNotifyNoTimeout);
}

}  // namespace gbf
