cmake_minimum_required(VERSION 3.16)

include_directories(
    ${PROJECT_SOURCE_DIR}/base
    # ${PROJECT_SOURCE_DIR}/meta
    # ${PROJECT_SOURCE_DIR}/logic
    ${PROJECT_SOURCE_DIR}/third_party
)

add_definitions(-DGBF_CORE_EXPORTS)


set(all_project_src "")

file(GLOB core_src "*.*")
list(APPEND core_src "../platform.hpp" "../meta_attribute_macros.hpp")
source_group(\\ FILES ${core_src})
list(APPEND all_project_src ${core_src})


file(GLOB modules_src "modules/*.*")
source_group(\\modules FILES ${modules_src})
list(APPEND all_project_src ${modules_src})

# add original here for some original code handle
file(GLOB original_src "original/*.*")
source_group(\\original FILES ${original_src})
list(APPEND all_project_src ${original_src})

file(GLOB imodules_src "imodules/*.*")
source_group(\\imodules FILES ${imodules_src})
list(APPEND all_project_src ${imodules_src})

file(GLOB utils_src "utils/*.*")
source_group(\\utils FILES ${utils_src})
list(APPEND all_project_src ${utils_src})

file(GLOB plugin_src "plugin/*.*")
source_group(\\plugin FILES ${plugin_src})
list(APPEND all_project_src ${plugin_src})

file(GLOB error_src "error/*.*")
source_group(\\error FILES ${error_src})
list(APPEND all_project_src ${error_src})

file(GLOB thread_impl_src "thread/impl/*.*")
source_group(\\thread\\impl FILES ${thread_impl_src})
list(APPEND all_project_src ${thread_impl_src})

file(GLOB thread_src "thread/*.*")
source_group(\\thread FILES ${thread_src})
list(APPEND all_project_src ${thread_src})


set(gbf_core_lib_name "gbf_core")

add_library(${gbf_core_lib_name} SHARED 
    ${all_project_src}
)


if(WIN32)
    target_link_libraries(${gbf_core_lib_name} PUBLIC memoryhooker)
elseif(ANDROID)
    target_link_libraries(${gbf_core_lib_name} PUBLIC 
		dl 
        m
        c++_shared
        memoryhooker
	)
elseif(UNIX)
    target_link_libraries(${gbf_core_lib_name} PUBLIC
        stdc++fs
        dl
        rt
        m
        pthread
        memoryhooker
    )
#else()
    #target_link_libraries(${gbf_core_lib_name} INTERFACE rapidjson)
endif()

#set(LIBRARY_OUTPUT_PATH "${CMAKE_BINARY_DIR}")
#set(LIBRARY_OUTPUT_PATH ${CMAKE_BINARY_DIR})

set_target_properties(${gbf_core_lib_name} PROPERTIES UNITY_BUILD ON)
SET_PROPERTY(TARGET ${gbf_core_lib_name} PROPERTY FOLDER "framework c++/base")

define_filename_macro(${gbf_core_lib_name})
