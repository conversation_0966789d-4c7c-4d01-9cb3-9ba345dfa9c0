
namespace gbf {

template <typename T>
T Error::prepare(T error, const std::string_view& file, int line, const std::string_view& function)
{
    error.m_location = StringUtil::Format("%s \" (\"%d ) - %s", file.data(), line, function.data());
    return error;
}

////template <typename T>
////std::string Error::str(T value)
////{
////    return detail::convert<ponder::String>(value);
////}

} // namespace ponder
