#pragma once

GBF_CORE_API extern gbf::IProfilerModule* GProfiler;

namespace gbf {
class GBF_CORE_API ProfilerIdTimerScope {
 public:
  ProfilerIdTimerScope(const ProfilerMethodInfo& methodInfo) {
    if (GProfiler->IsRemoteConnected()) {
      mMethodInfo = methodInfo;
      mStartTime = GProfiler->GetClockCount();
      GProfiler->StartScope();
    } else {
      mMethodInfo.extra_source_info = nullptr;
    }
  }

  ~ProfilerIdTimerScope() {
    if (mMethodInfo.extra_source_info != nullptr) {
      auto endTime = GProfiler->GetClockCount();
      GProfiler->AddTimeSpan(mMethodInfo, mStartTime, endTime);
      GProfiler->StopScope();
    }
  }

 protected:
  ProfilerMethodInfo mMethodInfo;
  int64_t mStartTime;
};
#pragma warning(push)
#pragma warning(disable : 4251)
class GBF_CORE_API ProfilerStringNamedTimerScope {
 public:
  ProfilerStringNamedTimerScope(std::string_view pName, const char* pSourceInfo) : mName(pName) {
    if (GProfiler->IsRemoteConnected()) {
      mStartTime = GProfiler->GetClockCount();
      GProfiler->StartScope();
      mSourceInfo = pSourceInfo;
    }
  }

  ~ProfilerStringNamedTimerScope() {
    if (mSourceInfo) {
      auto endTime = GProfiler->GetClockCount();
      GProfiler->AddTimeSpan(mName.c_str(), mSourceInfo, mStartTime, endTime);
      GProfiler->StopScope();
    }
  }

 protected:
  std::string mName;
  const char* mSourceInfo = nullptr;
  int64 mStartTime = 0;
};
}  // namespace gbf
#pragma warning(pop)
#if GBF_CORE_ENABLE_PROFILER

  #define GBF_PROFILER_METHOD_TYPE gbf::ProfilerMethodInfo
  #define GBF_PROFILER_CREATE_METHOD(METHOD_NAME, GROUP) \
    GProfiler->CreateMethodInfo(METHOD_NAME, GROUP, GBF_SOURCE_STRING)
  #define GBF_PROFILER_CREATE_METHOD_WITH_SOURCE(METHOD_NAME, GROUP, SOURCE) \
    GProfiler->CreateMethodInfo(METHOD_NAME, GROUP, SOURCE)

  #define GBF_PROFILER_METHOD_INFO(VAR_NAME, METHOD_NAME, GROUP) \
    static gbf::ProfilerMethodInfo VAR_NAME = GProfiler->CreateMethodInfo(METHOD_NAME, GROUP, GBF_SOURCE_STRING)

  #define GBF_PROFILER_CUSTOM_LINE_INFO(VAR_NAME, NAME, GRAPH_KIND, UNIT_NAME, COLOR) \
    static gbf::ProfilerCustomLineInfo VAR_NAME =                                     \
        GProfiler->CreateCustomLineInfo(NAME, GRAPH_KIND, UNIT_NAME, COLOR)

  #define GBF_PROFILER_AUTO_SCOPE(METHOD_INFO) \
    gbf::ProfilerIdTimerScope GBF_UNIQUE(timer_scope)(METHOD_INFO)

  #define GBF_PROFILER_AUTO_SCOPE_DYNAMIC(METHOD_NAME) \
    gbf::ProfilerStringNamedTimerScope GBF_UNIQUE(named_timer_scope)(METHOD_NAME, GBF_SOURCE_STRING)

  #define GBF_PROFILER_AUTO_SCOPE_DYNAMIC_WITH_SOURCE(METHOD_NAME, SOURCE) \
    gbf::ProfilerStringNamedTimerScope GBF_UNIQUE(named_timer_scope)(METHOD_NAME, SOURCE)

  #define GBF_PROFILER_BEGIN_SAMPLE(METHOD_INFO) GProfiler->BeginSample(METHOD_INFO)

  #define GBF_PROFILER_BEGIN_SAMPLE_DYAMIC(METHOD_NAME) \
    GProfiler->BeginSampleDynamic(METHOD_NAME, GBF_SOURCE_STRING)

  #define GBF_PROFILER_BEGIN_SAMPLE_LUA(SOURCE_NAME, LINE, NAMEWHAT, METHODNAME) \
    GProfiler->BeginSampleLua(SOURCE_NAME, LINE, NAMEWHAT, METHODNAME)

  #define GBF_PROFILER_END_SAMPLE() GProfiler->EndSample()

  #define GBF_PROFILER_END_SAMPLE_WITH_FIX(LEVEL) GProfiler->EndSampleWithFix(LEVEL)

  #define GBF_PROFILER_CUSTOM_LINE_VALUE(CUSTOM_LINE_INFO, VALUE) \
    GProfiler->SetCustomLineValue(CUSTOM_LINE_INFO, VALUE)

  #define GBF_PROFILER_SET_THREAD_NAME(THREAD_NAME) GProfiler->SetThreadName(THREAD_NAME)

  #define GBF_PROFILER_CLEANUP_THREAD() GProfiler->CleanupThread()

  #define GBF_PROFILER_SET_NETWORK_PORT(PORT) GProfiler->SetNetworkPort(PORT)

  #define GBF_PROFILER_FRAME_START() GProfiler->OnFrameStart()

  #define GBF_PROFILER_RECODING_START() GProfiler->StartRecodeToFile()

  #define GBF_PROFILER_RECODING_END() GProfiler->StopRecodeToFile()

#else
  #define GBF_PROFILER_METHOD_TYPE int
  #define GBF_PROFILER_CREATE_METHOD(METHOD_NAME, GROUP) 0

  #define GBF_PROFILER_METHOD_INFO(VAR_NAME, METHOD_NAME, GROUP)

  #define GBF_PROFILER_CUSTOM_LINE_INFO(VAR_NAME, NAME, GRAPH_KIND, UNIT_NAME, COLOR)

  #define GBF_PROFILER_AUTO_SCOPE(METHOD_INFO)

  #define GBF_PROFILER_AUTO_SCOPE_DYNAMIC(METHOD_NAME)

  #define GBF_PROFILER_AUTO_SCOPE_DYNAMIC_WITH_SOURCE(METHOD_NAME, SOURCE)

  #define GBF_PROFILER_BEGIN_SAMPLE(METHOD_INFO) 0

  #define GBF_PROFILER_BEGIN_SAMPLE_DYAMIC(METHOD_NAME) 0

  #define GBF_PROFILER_BEGIN_SAMPLE_LUA(SOURCE_NAME, LINE, NAMEWHAT, METHODNAME) 0

  #define GBF_PROFILER_END_SAMPLE()

  #define GBF_PROFILER_END_SAMPLE_WITH_FIX(LEVEL)

  #define GBF_PROFILER_CUSTOM_LINE_VALUE(CUSTOM_LINE_INFO, VALUE)

  #define GBF_PROFILER_SET_THREAD_NAME(THREAD_NAME)

  #define GBF_PROFILER_CLEANUP_THREAD()

  #define GBF_PROFILER_SET_NETWORK_PORT(PORT)

  #define GBF_PROFILER_FRAME_START()

  #define GBF_PROFILER_RECODING_START()

  #define GBF_PROFILER_RECODING_END()
#endif

////#define GBF_PROFILER_METHOD_TYPE GBF_PROFILER_METHOD_TYPE
////
////#define GBF_PROFILER_CREATE_METHOD GBF_PROFILER_CREATE_METHOD
////
////#define GBF_PROFILER_METHOD_INFO GBF_PROFILER_METHOD_INFO
////
////#define GBF_PROFILER_CUSTOM_LINE_INFO GBF_PROFILER_CUSTOM_LINE_INFO
////
////#define GBF_PROFILER_AUTO_SCOPE GBF_PROFILER_AUTO_SCOPE
////
////#define GBF_PROFILER_AUTO_SCOPE_DYNAMIC GBF_PROFILER_AUTO_SCOPE_DYNAMIC
////
////#define GBF_PROFILER_AUTO_SCOPE_DYNAMIC_WITH_SOURCE GBF_PROFILER_AUTO_SCOPE_DYNAMIC_WITH_SOURCE
////
////#define GBF_PROFILER_BEGIN_SAMPLE GBF_PROFILER_BEGIN_SAMPLE
////
////#define GBF_PROFILER_BEGIN_SAMPLE_DYAMIC GBF_PROFILER_BEGIN_SAMPLE_DYAMIC
////
////#define GBF_PROFILER_BEGIN_SAMPLE_LUA GBF_PROFILER_BEGIN_SAMPLE_LUA
////
////#define GBF_PROFILER_END_SAMPLE GBF_PROFILER_END_SAMPLE
////
////#define GBF_PROFILER_END_SAMPLE_WITH_FIX GBF_PROFILER_END_SAMPLE_WITH_FIX
////
////#define GBF_PROFILER_CUSTOM_LINE_VALUE GBF_PROFILER_CUSTOM_LINE_VALUE
////
////#define GBF_PROFILER_SET_THREAD_NAME GBF_PROFILER_SET_THREAD_NAME
////
////#define GBF_PROFILER_CLEANUP_THREAD GBF_PROFILER_CLEANUP_THREAD
////
////#define GBF_PROFILER_SET_NETWORK_PORT GBF_PROFILER_SET_NETWORK_PORT
////
////#define GBF_PROFILER_FRAME_START GBF_PROFILER_FRAME_START
////
////#define GBF_PROFILER_RECODING_START GBF_PROFILER_RECODING_START
////
////#define GBF_PROFILER_RECODING_END GBF_PROFILER_RECODING_END
