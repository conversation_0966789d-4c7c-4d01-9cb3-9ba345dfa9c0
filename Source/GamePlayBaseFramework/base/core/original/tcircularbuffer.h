/************************************************************
 *  File Module:    tcircularbuffer.h
 *  File Description: lock-free queue for data producer and consumer
 *  File History:
 *  <author>  <time>            <version >   <desc>
 *  Hardway    2012/03/02         1.0           create this moudle
 *  hustqiu    2019/12/19         1.1           modify for xgame
 *  fangshen   2020/1/8           1.2           modify for new namespace
 ***********************************************************/

#pragma once

#include "core/config.hpp"

namespace rstudio_original {

typedef struct {
  unsigned int dwDataLen;
  const char* pszData;
} DATAVEC;

class GBF_CORE_API TCircularBuffer {
 public:
  TCircularBuffer();
  ~TCircularBuffer();

  /**
   *@brief:create circular buffer
   *@param        dwsize:          buffer size
   *@retval         = 0      for success
   *@retval         <0       fail to allocate memory
   */
  int Create(unsigned int a_Buffersize);

  /**
   *@brief:free circular buffer
   */
  void Destroy();

  /**
   *@brief:single write
   *@param        dwlen:data len
   *@param        pbuf:data address
   *@retval         = 0      for success
   *@retval         <0       write fail,insufficient free size
   */
  int Write(unsigned int a_dwdatalen, const char* a_pszdata);

  /**
   *@brief:single write
   *@param        dwlen:buffer len[IN],read data len[OUT]
   *@param        pbuf:data address
   *@retval         > 0      success read data
   *@retval         == 0     no data available
   *@retval         ==-1     stored data size too short
   *@retval         ==-2     incoming data buffer insuffficient
   *@retval         ==-3     stored data size invalid
   */
  int Read(unsigned int* a_dwBufflen, char* a_pszbuf);

  /**
   *@brief:clear data,reset buffer
   */
  void ClearBuffer();
  /**
   *@brief:get data size
   */
  unsigned int GetDataSize();

  /**
   *@brief:get free room
   */
  unsigned int GetFreeRoom();

  /**
   *@brief:get capacity
   */
  unsigned int GetCapacity();

 private:
  unsigned int m_dwsize;          /*capacity*/
  char* m_dataptr;                /*buffer addr*/
  volatile unsigned int m_dwhead; /*read pointer*/
  volatile unsigned int m_dwtail; /*write pointer*/
};

/*@brief:clear buffer
 */
inline void TCircularBuffer::ClearBuffer() {
  m_dwtail = m_dwhead;
  return;
}

inline unsigned int TCircularBuffer::GetCapacity() { return m_dwsize; }
/**
 *@brief:return left data size
 */
inline unsigned int TCircularBuffer::GetDataSize() {
  if (m_dwtail >= m_dwhead) {
    return m_dwtail - m_dwhead;
  }

  return m_dwsize - (m_dwhead - m_dwtail);
}

/**
 *@brief:return free room
 */
inline unsigned int TCircularBuffer::GetFreeRoom() {
  /*reserve one byte differ empty from full*/

  if (m_dwtail >= m_dwhead) {
    return m_dwsize + m_dwhead - m_dwtail - 1;
  }

  return m_dwhead - m_dwtail - 1;
}

}  // namespace gbf_original
