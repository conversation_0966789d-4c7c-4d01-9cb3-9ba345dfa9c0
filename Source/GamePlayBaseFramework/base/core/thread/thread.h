#pragma once

#include <string>
#include <vector>
#include <string>

#include "core/thread/thread_config.hpp"
#include "core/thread/thread_define.hpp"

namespace gbf {
namespace threads {
class ThreadImplSys;

class GBF_CORE_API Thread {
 public:
  Thread();
  Thread(ThreadJobFunction&& func, unsigned int stack_size = 0);
  Thread(const Thread& other);

  // It's a move semantic
  Thread& operator=(const Thread& other);

  ~Thread();

  bool Run(ThreadJobFunction&& func, unsigned int stack_size = 0);

  bool Join();
  bool Terminate();

  void Detach();

  size_t GetThreadId() const;

  bool Joinable() const;

  void SetThreadPriority(int priority_value);

  void QueryRunningCores(std::vector<int>& out_cores);

  void BindThreadToCpuCores(const std::vector<int>& cores);

  static size_t CurrentThreadId();

  static int HardwareConcurrency();

  static bool IsSupportBindCores();

  static void QueryAllCpuCores(std::vector<int>& out_cores);

  static bool IsSupportQueryRunningCores();

  static int GetPriorityMinValue();

  static int GetPriorityMaxValue();

 private:
  ThreadImplSys* m_impl;
};

class GBF_CORE_API ThisThread {
 public:
  static void YieldThis();

  static void Sleep(unsigned long milliseconds);

  static int GetThreadId();

  static void SetThreadName(const std::string& thread_name);
};
}  // namespace threads
}  // namespace gbf