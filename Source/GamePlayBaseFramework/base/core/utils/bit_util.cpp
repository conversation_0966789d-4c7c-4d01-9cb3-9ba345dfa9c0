#include "core/utils/bit_util.hpp"
#include <limits>

#if defined(_MSC_VER) && (_MSC_VER >= 1400) && (defined(_M_IX86) || defined(_M_X64))
#include <intrin.h>

namespace gbf {

size_t bitutil::bits_count(std::uint32_t value) { return __popcnt(value); }

size_t bitutil::first_valid_bit_from_high(std::uint32_t value) {
  if (value == 0) return std::numeric_limits<size_t>::max();

  unsigned long result = 0;
  _BitScanForward(&result, value);
  return result;
}

size_t bitutil::first_valid_bit_from_low(std::uint32_t value) {
  if (value == 0) return std::numeric_limits<size_t>::max();

  unsigned long result = 0;
  _BitScanReverse(&result, value);
  return result;
}

#if GBF_CORE_ARCH_TYPE == GBF_CORE_ARCHITECTURE_64
size_t bitutil::bits_count(std::uint64_t value) { return __popcnt64(value); }

size_t bitutil::first_valid_bit_from_low(std::uint64_t value) {
  if (value == 0) return std::numeric_limits<size_t>::max();

  unsigned long result = 0;
  _BitScanReverse64(&result, value);
  return result;
}

size_t bitutil::first_valid_bit_from_high(std::uint64_t value) {
  if (value == 0) return std::numeric_limits<size_t>::max();

  unsigned long result = 0;
  _BitScanForward64(&result, value);
  return result;
}
#endif

}  // namespace gbf

////#elif defined __clang__
#else

namespace gbf {

size_t bitutil::bits_count(std::uint32_t value) { return __builtin_popcount(value); }

size_t bitutil::first_valid_bit_from_low(std::uint32_t value) { return 0 == value ? std::numeric_limits<size_t>::max() : __builtin_ctz(value); }

size_t bitutil::first_valid_bit_from_high(std::uint32_t value) { return 0 == value ? std::numeric_limits<size_t>::max() : 31 - __builtin_clz(value); }

#if GBF_CORE_ARCH_TYPE == GBF_CORE_ARCHITECTURE_64
size_t bitutil::bits_count(std::uint64_t value) { return __builtin_popcountll(value); }

size_t bitutil::first_valid_bit_from_low(std::uint64_t value) { return 0 == value ? std::numeric_limits<size_t>::max() : __builtin_ctzll(value); }

size_t bitutil::first_valid_bit_from_high(std::uint64_t value) { return 0 == value ? std::numeric_limits<size_t>::max() : 63 - __builtin_clzll(value); }
#endif

}  // namespace gbf

#endif
