#include "byte_buffer_error.h"

namespace gbf {
ByteBufferError::ByteBufferError(bool read, size_t pos, size_t opsize, size_t size, const std::string_view errorMessage)
    : Error(StringUtil::Format("Attempted to %s in ByteBuffer (pos:%d, size:%d, opsize:%d)! msg:%s",
                               (read ? "read" : "write"), pos, size, opsize, errorMessage.data())) {}

ByteBufferWriteOverflow::ByteBufferWriteOverflow(size_t wpos, size_t wsize, size_t size,
                                                 const std::string_view errorMessage)
    : Error(StringUtil::Format("ByteBuffer write overflowed! writePos:%d, bufferMaxSize:%d, writeSize:%s! msg:%s", wpos,
                               size, wsize, errorMessage.data())) {}
}  // namespace gbf