#pragma once

#include <memory.h>
#include <cassert>

#include <list>
#include <map>
#include <string>
#include <vector>

#include <algorithm>
#include <memory>

#include "core/config.hpp"
#include "core/utils/byte_buffer_storage.h"
#include "core/utils/byte_buffer_view.h"
#include "core/utils/byte_conv.h"

namespace gbf {
class ByteBufferWriter {
 public:
  template <bool is_little_endian>
  class TBytePackStream {
   public:
    TBytePackStream() = delete;
    TBytePackStream(ByteBufferStorage& _storage, size_t& _wpos) : m_ref_storage(_storage), m_ref_write_size(_wpos) {}

    template <typename T>
    void Put(size_t pos, T value) {
      DoEndianConvert(value);
      m_ref_storage.PutStraight(pos, m_ref_write_size, (unsigned char*)&value, sizeof(value));
    }

    template <typename T>
    TBytePackStream<is_little_endian>& operator<<(T value) {
      Append(value);
      return *this;
    }

    size_t Size() const { return m_ref_write_size; }

   protected:
    template <typename T>
    static inline void DoEndianConvert(T& val) {
      const bool is_system_little_endian = BYTECONV_ENDIAN == BYTECONV_LITTLE_ENDIAN;

      // if endian not match, do convert
      if constexpr (is_system_little_endian != is_little_endian) {
        byte_converter::Apply<T>(&val);
      }
    }

    template <typename T>
    void Append(T value) {
      static_assert(std::is_integral_v<T> || std::is_floating_point_v<T>);

      DoEndianConvert(value);
      m_ref_write_size = m_ref_storage.AppendStraight(m_ref_write_size, (unsigned char*)&value, sizeof(value));
    }

    ////template<bool is_little_endian>
    void Append(const std::string_view value) {
      Append((uint32_t)value.length());
      m_ref_write_size = m_ref_storage.AppendStraight(m_ref_write_size, (const uint8_t*)value.data(), value.length());
    }

    ////template<bool is_little_endian>
    void Append(const std::string& value) { Append(std::string_view(value)); }

    ////template<bool is_little_endian>
    void Append(const char* str) { Append(std::string_view(str, strlen(str))); }

   protected:
    ByteBufferStorage& m_ref_storage;
    size_t& m_ref_write_size;
  };

 public:
  // constructor
  ByteBufferWriter() : m_write_size(0) {}

  // constructor
  ByteBufferWriter(size_t res) : m_write_size(0) { m_storage.Reserve(res); }

  // copy constructor
  ByteBufferWriter(const ByteBufferWriter& buf) : m_write_size(buf.m_write_size), m_storage(buf.m_storage) {}

  ByteBufferWriter(ByteBufferWriter&& buf) noexcept { *this = std::move(buf); }

  ~ByteBufferWriter() { FreeBuffer(); }

  ByteBufferWriter& operator=(const ByteBufferWriter&) = default;

  ByteBufferWriter& operator=(ByteBufferWriter&& other) noexcept {
    std::swap(m_write_size, other.m_write_size);
    m_storage = std::move(other.m_storage);
    return *this;
  }

  void FreeBuffer() {
    m_storage.Clear();
    m_write_size = 0;
  }

  size_t WritePosition() const { return m_write_size; }

  size_t WritePosition(size_t wpos_) {
    m_write_size = wpos_;
    return m_write_size;
  }

  char* WritePtr() { return m_storage.ModifyContents(m_write_size); }

  size_t Space() const {
    if (m_write_size >= m_storage.Capacity()) {
      return 0;
    } else {
      return m_storage.Capacity() - m_write_size;
    }
  }

  void ResetAsBuffer() {
    m_storage.ResetAsBuffer(m_write_size);
    m_write_size = 0;
  }

  const char* Contents() const { return m_storage.Contents(); }

  size_t Size() const { return m_write_size; }

  bool Empty() const { return m_write_size == 0; }

  void Append(const char* src, size_t cnt) { return Append((const unsigned char*)src, cnt); }

  void Append(const uint8_t* src, size_t cnt) { m_write_size = m_storage.AppendStraight(m_write_size, src, cnt); }

  ////void append_buffer(const byte_buffer_writer& buffer)
  ////{
  ////	size_t bufsize = buffer.size();
  ////	if (bufsize > 0)
  ////	{
  ////		append(buffer.contents(), buffer.wpos());
  ////	}
  ////}

  bool PutStraight(size_t pos, const unsigned char* src, size_t cnt) {
    return m_storage.PutStraight(pos, m_write_size, src, cnt);
  }

  void SetAsHexString(const std::string_view hexStr) { m_write_size = m_storage.SetAsHexString(hexStr); }

  ByteBufferView CreateView() { return ByteBufferView(Contents(), Size()); }

  TBytePackStream<true> GetLittleEndianStream() { return TBytePackStream<true>(m_storage, m_write_size); }

  TBytePackStream<false> GetBigEndianStream() { return TBytePackStream<false>(m_storage, m_write_size); }

  void Reserve(size_t ressize) { m_storage.Reserve(ressize); }

  // for compatible with old code
  template <class T>
  ByteBufferWriter& operator<<(T value) {
    GetBigEndianStream() << value;
    return *this;
  }

 protected:
  ByteBufferStorage m_storage;
  size_t m_write_size;
};

template <typename T>
inline ByteBufferWriter& operator<<(ByteBufferWriter& b, std::vector<T> const& v) {
  b << (unsigned int)v.size();
  // memcpy()
  // b.append((const UINT8*)&v[0],sizeof(T)*v.size());
  for (typename std::vector<T>::const_iterator i = v.begin(); i != v.end(); ++i) {
    b << *i;
  }
  return b;
}

template <typename T>
inline ByteBufferWriter& operator<<(ByteBufferWriter& b, std::list<T> const& v) {
  b << (unsigned int)v.size();
  for (typename std::list<T>::iterator i = v.begin(); i != v.end(); ++i) {
    b << *i;
  }
  return b;
}

template <typename K, typename V>
inline ByteBufferWriter& operator<<(ByteBufferWriter& b, std::map<K, V>& m) {
  b << (unsigned int)m.size();
  for (typename std::map<K, V>::iterator i = m.begin(); i != m.end(); ++i) {
    b << i->first << i->second;
  }
  return b;
}

}  // namespace gbf
