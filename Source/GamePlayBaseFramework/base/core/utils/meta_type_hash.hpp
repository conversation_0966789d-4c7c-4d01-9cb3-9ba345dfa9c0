#pragma once

#include <cstdint>
#include <string_view>

namespace gbf {

constexpr inline uint32_t Hash32Fnv1a(const char* key, size_t size) {
  constexpr uint32_t c_offset_basis = 2166136261U;
  constexpr uint32_t c_prime = 16777619U;

  uint32_t value = c_offset_basis;
  for (size_t i = 0; i < size; ++i) {
    value ^= static_cast<uint32_t>(static_cast<uint8_t>(key[i]));
    value *= c_prime;
  }

  return value;
}

constexpr inline uint32_t Hash32Fnv1a(const char* key) {
  constexpr uint32_t c_offset_basis = 2166136261U;
  constexpr uint32_t c_prime = 16777619U;

  uint32_t value = c_offset_basis;

  int i = 0;
  while (key[i]) {
    value ^= static_cast<uint32_t>(static_cast<uint8_t>(key[i++]));
    value *= c_prime;
  }

  return value;
}



inline constexpr uint64_t Hash64Fnv1a(const char* key, const uint64_t len) {
  uint64_t hash = 0xcbf29ce484222325;
  uint64_t prime = 0x100000001b3;

  for (int i = 0; i < len; ++i) {
    uint8_t value = key[i];
    hash = hash ^ value;
    hash *= prime;
  }

  return hash;
}

inline constexpr uint64_t HashFnv1a(const char* key) {
  uint64_t hash = 0xcbf29ce484222325;
  uint64_t prime = 0x100000001b3;

  int i = 0;
  while (key[i]) {
    uint8_t value = key[i++];
    hash = hash ^ value;
    hash *= prime;
  }

  return hash;
}

struct MetatypeHash {
  uint64_t name_hash{0};
  uint64_t matcher_hash{0};

  bool operator==(const MetatypeHash& other) const { return name_hash == other.name_hash; }
  template <typename T>
  static constexpr const char* NameDetail() {
#if defined(_clang__) || defined(__GNUC__)
    return __PRETTY_FUNCTION__;
#else
    return __FUNCSIG__;
#endif
  }

#if !defined(_MSC_VER)
  template <typename T>
  static constexpr auto NamePretty() {
    // name_detail() is like: static constexpr const char* gbf::MetatypeHash::name_detail() [with T =
    // gbf::math::Vector3]
    constexpr std::string_view pretty_name = NameDetail<T>();
  #if defined(__GNUC__)
    std::string_view prefix = "static constexpr const char* gbf::MetatypeHash::NameDetail() [with T = ";
  #elif defined(_clang__)
    // ToDo: need check here for clang
    std::string_view prefix = "static const char* gbf::MetatypeHash::NameDetail() [with T = ";
  #else
    #error("Unknown platform here!")
  #endif
    constexpr size_t start = pretty_name.find_first_of('[') + 5;
    constexpr size_t end = pretty_name.find_last_of(']');
    return pretty_name.substr(start, end-start);
  }
#else
  template <typename T>
  static constexpr auto NamePretty() {
    // name_detail() is like "const char *__cdecl gbf::MetatypeHash::NameDetail<class rstudio::math::Vector3>(void)"
    //                       "const char *__cdecl gbf::MetatypeHash::NameDetail<enum Enum>(void)"
    //                       "const char *__cdecl gbf::MetatypeHash::NameDetail<unsigned int>(void)"
    std::string_view pretty_name = NameDetail<T>();
    std::string_view prefix = "const char *__cdecl gbf::MetatypeHash::NameDetail<";
    std::string_view suffix = ">(void)";

    pretty_name.remove_prefix(prefix.size());
    pretty_name.remove_suffix(suffix.size());

    bool is_user_type = pretty_name.rfind("class ", 0) == 0|| pretty_name.rfind("struct ", 0) == 0 || pretty_name.rfind("enum ", 0) == 0;

    if (is_user_type)
        return pretty_name.substr(pretty_name.find_first_of(' ') + 1);
    else
        return pretty_name;
  }
#endif

  template <typename T>
  static constexpr uint64_t Hash() {
    // Do not need here, params and return value have type here~~
    ////static_assert(!std::is_reference_v<T>, "dont send references to hash");
    ////static_assert(!std::is_const_v<T>, "dont send const to hash");
    auto detail_name = NamePretty<T>();
    return Hash64Fnv1a(detail_name.data(), detail_name.length());
  }
};
}  // namespace gbf
