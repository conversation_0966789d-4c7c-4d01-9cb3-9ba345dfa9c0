#pragma once

#include <functional>
#include <string>
#include "platform.hpp"
#include "core/utils/byte_buffer.h"
#include "core/utils/string_util.h"
#include "core/export.hpp"

namespace gbf
{

using FileTraverseFunc = std::function<void(const char*)>;

class GBF_CORE_API PathTool {
 public:
  PathTool();

  ~PathTool();

  static std::string NormalizePath(const std::string& path);

  //////////////////////////////////////////////////////////////////////////
  // @ don't add '/' at the beginning.
  static std::string NormalizeFilePath(const std::string& path);

  static std::string NormalieWorkPath(const std::string& path);

  static std::string GetParentPath(const std::string& path);

  static std::string GetParentFolderName(const std::string& path);

  static std::string CombinePath(const std::string& path1, const std::string& path2);

  static bool IsFileExsit(const std::string& path);

  static std::string GetDirectory(const std::string& file_name);

  static std::string GetFileBaseName(const std::string& file_name);

  static std::string GetFileExtName(const std::string& filePath);

  static bool GetFileSize(const std::string& file_name, uint32_t& out_size);

  static time_t GetFileLastModTime(const std::string& file_name);

  static bool RenameFile(const std::string& old_name, const std::string& new_name);

  static bool DeleteFileCustom(const std::string& file_name);

  static ByteBufferPtr ReadFileData(const char* pszFileName);

  static ByteBufferPtr ReadFileDataWithNullTerminator(const char* pszFileName);

  static bool SaveDataToFile(const std::string& file_name, const uint8_t* data_ptr, uint32_t data_len);

  static bool SaveDataToFileWithOffset(const std::string& file_name, const uint8_t* data_ptr, uint32_t data_len, uint32_t offset);

  static unsigned int TraverseFile(const char* work_path, const char* file_path, const char* ext, const FileTraverseFunc& handle_func);

  static void CreateDirIfNotExist(const char* dir);

  // Note: this function do not use any platform special `CopyFile` function
  // Like the iOS APFS, this function have not any benefit! Try to use platform special function!
  static bool CopyFileData(const std::string& src, const std::string& dst, bool failIfExists = true);

 protected:
  static StringVector BreakPaths(const std::string& path_info, bool need_handle_special_heads = false);

  static std::string GeneratePathFromStrVec(const StringVector& vecs);

 protected:
  static StringVector m_special_head_list;
};

}  // namespace gbf
