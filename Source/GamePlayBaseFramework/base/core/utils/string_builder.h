#pragma once

#include <memory.h>
#include <cassert>

#include <list>
#include <map>
#include <string>
#include <vector>

#include <algorithm>
#include <cstdarg>
#include <memory>

#include "core/config.hpp"
////#include "byte_buffer_error.h"

#include "core/utils/byte_buffer_storage.h"
#include "core/utils/string_converter.h"
#include "core/utils/string_util.h"

namespace gbf {

class StringBuilder {
 public:
  // constructor
  StringBuilder() {}

  StringBuilder(std::string_view sv) { AppendStraight(sv.data(), sv.length()); }

  // constructor
  StringBuilder(size_t res) { m_storage.Reserve(res); }

  // copy constructor
  StringBuilder(const StringBuilder& buf) : m_write_size(buf.m_write_size), m_storage(buf.m_storage) {}

  ~StringBuilder() { FreeBuffer(); }

  size_t Space() const {
    if (m_write_size >= m_storage.Capacity()) {
      return 0;
    } else {
      return m_storage.Capacity() - m_write_size;
    }
  }

  void ResetAsBuffer() {
    m_storage.ResetAsBuffer(m_write_size);
    m_write_size = 0;
  }

  const char* Contents() const { return (const char*)m_storage.Contents(); }

  size_t Size() const { return m_write_size; }

  bool Empty() const { return m_write_size == 0; }

  template <class T>
  StringBuilder& Append(T val) {
    auto str = StringConverter::ToString(val);
    AppendStraight(str.c_str(), str.length());
    return *this;
  }

  // template<>
  StringBuilder& Append(const std::string& str) {
    AppendStraight(str.c_str(), str.length());
    return *this;
  }

  // template<>
  StringBuilder& Append(std::string_view sv) {
    AppendStraight(sv.data(), sv.length());
    return *this;
  }

  // StringBuilder& Append(const std::string_view& sv)
  // {
  // 	AppendStraight(sv.data(), sv.length());
  // 	return *this;
  // }

  // template<>
  StringBuilder& Append(const char* sv) {
    Append(std::string_view(sv));
    return *this;
  }

  // template<>
  StringBuilder& Append(const StringBuilder& buffer) {
    if (buffer.Size()) {
      AppendStraight(buffer.Contents(), buffer.Size());
    }
    return *this;
  }

  template <class T>
  StringBuilder& operator<<(const T& val) {
    return Append(val);
  }

  StringBuilder& AppendFormat(const char* format, ...) {
    constexpr int kMaxStringLength = 1 * 1024;  // 1k
    std::string retstr;
    va_list ap;
    va_start(ap, format);

    Reserve(Size() + kMaxStringLength);
    // char* buf = (char*)CAP_MALLOC(CC_MAX_STRING_LENGTH, MemoryGroupLabel::Temp); // Temp Memory has bug!

    int ret = vsnprintf(ModifyContents(Size()), kMaxStringLength, format, ap);

    if (ret > 0) {
      m_write_size += ret;
    }

    va_end(ap);

    return *this;
  }

  StringBuilder& AppendLine() {
    return Append("\r\n");
    return *this;
  }

  StringBuilder& AppendLine(std::string_view lineContent) {
    Append(lineContent);
    return AppendLine();
  }

  std::string_view StrView() { return std::string_view(Contents(), Size()); }

  std::string Str() { return std::string(Contents(), Size()); }

  void ResetWritePosition(size_t oldPos) {
    if (oldPos < m_write_size) {
      memset(m_storage.ModifyContents(oldPos), 0, m_write_size - oldPos);
      m_write_size = oldPos;
    }
  }

 protected:
  void AppendStraight(const char* src, size_t cnt) {
    m_write_size = m_storage.AppendStraight(m_write_size, (uint8_t*)src, cnt);
  }

  void Reserve(size_t needsize) { m_storage.Reserve(needsize); }

  void FreeBuffer() {
    m_write_size = 0;
    m_storage.Clear();
  }

  char* ModifyContents(size_t pos) { return (char*)m_storage.ModifyContents(pos); }

 protected:
  ByteBufferStorage m_storage;
  size_t m_write_size = 0;
};
}  // namespace gbf
