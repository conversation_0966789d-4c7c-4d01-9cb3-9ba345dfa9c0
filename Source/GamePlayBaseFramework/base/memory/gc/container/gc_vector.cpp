#include "memory/gc/container/gc_vector.hpp"
#include "memory/gc/gc_manager.hpp"
#include "memory/gc/gc_make.hpp"
////#include "vmcore/gc/gc.h"

namespace gbf {
namespace gc {

void gc_vector::assign(size_type count, value_type v) { impl_.assign(count, v); }

void gc_vector::assign(std::initializer_list<value_type> ilist) { impl_.assign(ilist); }

gc_vector::allocator_type gc_vector::get_allocator() const { return impl_.get_allocator(); }

gc_vector::reference gc_vector::operator[](size_t index) { return impl_[index]; }

gc_vector::const_reference gc_vector::operator[](size_t index) const { return impl_[index]; }

gc_vector::reference gc_vector::at(size_type pos) { return impl_.at(pos); }

gc_vector::const_reference gc_vector::at(size_type pos) const { return impl_.at(pos); }

gc_vector::reference gc_vector::front() { return impl_.front(); }

gc_vector::const_reference gc_vector::front() const { return impl_.front(); }

gc_vector::reference gc_vector::back() { return impl_.back(); }

gc_vector::const_reference gc_vector::back() const { return impl_.back(); }

gc_vector::iterator gc_vector::begin() noexcept { return impl_.begin(); }

gc_vector::iterator gc_vector::end() noexcept { return impl_.end(); }

gc_vector::const_iterator gc_vector::begin() const noexcept { return impl_.begin(); }

gc_vector::const_iterator gc_vector::end() const noexcept { return impl_.end(); }

gc_vector::const_iterator gc_vector::cbegin() const noexcept { return impl_.cbegin(); }

gc_vector::const_iterator gc_vector::cend() const noexcept { return impl_.cend(); }

gc_vector::reverse_iterator gc_vector::rbegin() noexcept { return impl_.rbegin(); }

gc_vector::reverse_iterator gc_vector::rend() noexcept { return impl_.rend(); }

gc_vector::const_reverse_iterator gc_vector::rbegin() const noexcept { return impl_.rbegin(); }

gc_vector::const_reverse_iterator gc_vector::rend() const noexcept { return impl_.rend(); }

gc_vector::const_reverse_iterator gc_vector::crbegin() const noexcept { return impl_.crbegin(); }

gc_vector::const_reverse_iterator gc_vector::crend() const noexcept { return impl_.crend(); }

/// capacity
bool gc_vector::empty() const noexcept { return impl_.empty(); }

gc_vector::size_type gc_vector::size() const noexcept { return impl_.size(); }

gc_vector::size_type gc_vector::max_size() const noexcept { return impl_.max_size(); }

void gc_vector::reserve(size_type new_size) { impl_.reserve(new_size); }

gc_vector::size_type gc_vector::capacity() const noexcept { return impl_.capacity(); }

void gc_vector::shrink_to_fit() { impl_.shrink_to_fit(); }

/// modifier
void gc_vector::clear() { impl_.clear(); }

gc_vector::iterator gc_vector::insert(const_iterator pos, value_type value) { return impl_.insert(pos, value); }

gc_vector::iterator gc_vector::insert(const_iterator pos, size_type count, value_type v) { return impl_.insert(pos, count, v); }

gc_vector::iterator gc_vector::insert(const_iterator pos, std::initializer_list<value_type> ilist) { return impl_.insert(pos, ilist); }

// emplace is equal to insert, so no necessary

gc_vector::iterator gc_vector::erase(iterator pos) { return impl_.erase(pos); }

gc_vector::iterator gc_vector::erase(const_iterator pos) { return impl_.erase(pos); }

gc_vector::iterator gc_vector::erase(iterator first, iterator last) { return impl_.erase(first, last); }

gc_vector::iterator gc_vector::erase(const_iterator first, const_iterator last) { return impl_.erase(first, last); }

void gc_vector::push_back(value_type v) { impl_.push_back(v); }

void gc_vector::pop_back() { impl_.pop_back(); }

void gc_vector::resize(size_type count, value_type value) { impl_.resize(count, value); }

void gc_vector::resize(size_type count) { impl_.resize(count); }

void gc_vector::swap(gc_vector& other) noexcept { impl_.swap(other.impl_); }

void gc_vector::do_scope_mark(gc_manager* manager) { 
  
  for (auto& iter : impl_) {
    if (GBF_LIKELY(iter)) {
      manager->mark_one_obj(iter.get_info());
    }
  }
}

bool gc_vector::remove_item(value_type val) noexcept {
  auto iter = std::find(begin(), end(), val);
  if (iter != end()) {
    erase(iter);
    return true;
  }
  return false;
}

void gc_vector::remove_at(size_type index) {
  auto itr = begin() + index;
  erase(itr);
}

local_ptr<gc_vector> gc_vector::clone() const {
  auto result = make_local<gc_vector>(no_run_scope);
  result->assign(this->begin(), this->end());
  return result;
}

}  // namespace gc
}  // namespace gbf
