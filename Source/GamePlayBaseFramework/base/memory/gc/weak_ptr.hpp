#pragma once

#include "memory/gc/gc_fwd.hpp"
#include "memory/gc/gc_util.hpp"
#include "memory/gc/smart_ptr.hpp"

namespace gbf {
namespace gc {

template <typename T>
class weak_ptr {
 public:
  weak_ptr(){
    allocate_slot();
  }

  template<typename SlotPolicy>
  weak_ptr(const smart_ptr<T, SlotPolicy>& x) noexcept : weak_ptr() {
    *this = x;
  }

  weak_ptr(const weak_ptr<T>& x) noexcept: weak_ptr() {
    *this = x;
  }

  ~weak_ptr() noexcept {
    free_slot();
  }

  template<typename SlotPolicy>
  weak_ptr& operator=(const smart_ptr<T, SlotPolicy>& x) noexcept {
    //ToDo: Do not need mark assign flag here?
    gc_info* info = x.get_info();
    *m_weak_slot = info;
    m_ptr = x.get();
    return *this;
  }

  weak_ptr& operator=(const weak_ptr<T>& x) noexcept {
    *m_weak_slot = *(x.m_weak_slot);
    m_ptr = x.m_ptr;
    return *this;
  }

  bool is_valid() const noexcept {
    gc_info* info = gc_util::_try_get_object_from_weak(m_weak_slot);
    return info != nullptr;
  }

  local_ptr<T> lock() const noexcept {
    gc_info* info = gc_util::_try_get_object_from_weak(m_weak_slot);
    T* obj = info? m_ptr: nullptr;
    return local_ptr<T>(info, obj);
  }

  operator bool() const noexcept { return is_valid(); }
 protected:
  void allocate_slot(){
    m_weak_slot = gc_util::_allocate_weak_slot();
  }

  void free_slot(){
    if(!m_weak_slot) return;

    gc_util::_free_weak_slot(m_weak_slot);
    m_weak_slot = nullptr;
  }
 protected:
  gc_info** m_weak_slot = nullptr;
  T*        m_ptr = nullptr;
};

}  // namespace gc
}  // namespace gbf

