ClangenRootPath: "E:/IEG-RED-GameEngine/framework-cpp/tools/dotnet/clang_tool"

SystemIncludes:
  - "E:/IEG-RED-GameEngine/Tools/clang-tool/clang_include"
  - "E:/IEG-RED-GameEngine/Tools/clang-tool/clang_win_include"  
ExportPassArray:
  - Type: "CrossEngine"
    ClangProjectSettings:
      RootSourcePath: "E:/IEG-RED-GameEngine/CrossEngine_debug/"
      AdditionalIncludeDirectories:
        - Path: "BuildingBlocks/FullFlightSimulator/../NetTbuspp"
        - Path: "BuildingBlocks/FullFlightSimulator/../NetTbuspp/tbus2/api/thirdparty/include"
        - Path: "BuildingBlocks/FullFlightSimulator/../NetTbuspp/ThirdParty/atomic_queue/include"
        - Path: "BuildingBlocks/NetTbuspp/tbus2/api/inc"
        - Path: "BuildingBlocks/NetTbuspp/ThirdParty/atomic_queue/include"
        - Path: "ManagedThirdParty_new/../Source/External/ispc_texcomp/include"
        - Path: "ManagedThirdParty_new/basisu-1.15/include"
        - Path: "ManagedThirdParty_new/cmft/include"
        - Path: "ManagedThirdParty_new/cppcodec/include"
        - Path: "ManagedThirdParty_new/DirectXMesh/include"
        - Path: "ManagedThirdParty_new/eigen-3.3.7/include"
        - Path: "ManagedThirdParty_new/flatbuffers-1.12.0.0/include"
        - Path: "ManagedThirdParty_new/freetype-2.7.1/include"
        - Path: "ManagedThirdParty_new/hash-library"
        - Path: "ManagedThirdParty_new/hnswlib/include"
        - Path: "ManagedThirdParty_new/lz4-1.9.3/include"
        - Path: "ManagedThirdParty_new/MaskedOcclusionCulling"
        - Path: "ManagedThirdParty_new/mimalloc/Includes"
        - Path: "ManagedThirdParty_new/nvtt/include"
        - Path: "ManagedThirdParty_new/OpenAL/include"
        - Path: "ManagedThirdParty_new/OpenEXR/include/Imath"
        - Path: "ManagedThirdParty_new/OpenEXR/include/OpenEXR"
        - Path: "ManagedThirdParty_new/OpenGLES3ANGLE/include"
        - Path: "ManagedThirdParty_new/OpenXR/include"
        - Path: "ManagedThirdParty_new/physfs-3.0.2/src"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/characterkinematic"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/collision"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/common"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/cooking"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/cudamanager"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/extensions"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/filebuf"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/foundation"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/geometry"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/geomutils"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/gpu"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/pvd"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/solver"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/task"
        - Path: "ManagedThirdParty_new/PhysX-4.1/include/vehicle"
        - Path: "ManagedThirdParty_new/PhysX-4.1/pxshared/include"
        - Path: "ManagedThirdParty_new/pixui/include"
        - Path: "ManagedThirdParty_new/pystring"
        - Path: "ManagedThirdParty_new/Renderdoc/include"
        - Path: "ManagedThirdParty_new/UVAtlas/include"
        - Path: "Source"
        - Path: "Source/Animation"
        - Path: "Source/AssetPipeline/"
        - Path: "Source/CrossSchema/"
        - Path: "Source/AssetPipeline/DXILReflection/"
        - Path: "Source/AssetPipeline/Import/ModelImporter/FBXImporter/MeshletGenerator"
        - Path: "Source/CECommon"
        - Path: "Source/CrossBase"
        - Path: "Source/CrossFX"
        - Path: "Source/CrossImage"
        - Path: "Source/CrossImage/."
        - Path: "Source/CrossPhysics"
        - Path: "Source/CrossSchema"
        - Path: "Source/CrossUI"
        - Path: "Source/CrossUI/."
        - Path: "Source/CrossUI/./core"
        - Path: "Source/CrossUI/./core/ui"
        - Path: "Source/ECS"
        - Path: "Source/Editor/LightmapBaker"
        - Path: "Source/External"
        - Path: "Source/External/Asio/include"
        - Path: "Source/External/DirectX/builds/dx11include"
        - Path: "Source/External/DirectX/builds/dx12include"
        - Path: "Source/External/DirectXMath/Inc"
        - Path: "Source/External/FBXSDK/2020.2/include"
        - Path: "Source/External/gli"
        - Path: "Source/External/gli/external"
        - Path: "Source/External/Lua/win64/include"
        - Path: "Source/External/NavMesh/DebugUtils"
        - Path: "Source/External/NavMesh/Detour"
        - Path: "Source/External/NavMesh/DetourCrowd"
        - Path: "Source/External/NavMesh/DetourTileCache"
        - Path: "Source/External/NavMesh/Recast"
        - Path: "Source/External/NavMesh/Tools"
        - Path: "Source/External/NsightAftermath/include"
        - Path: "Source/External/rapidjson/include"
        - Path: "Source/External/RapidXML/rapidxml-1.13"
        - Path: "Source/External/V8/include"
        - Path: "Source/External/Vulkan/include/vulkan"
        - Path: "Source/FileSystem"
        - Path: "Source/NativeGraphicsInterface"
        - Path: "Source/Projects/PrecompiledHeaders"
        - Path: "Source/Reflection/include"
        - Path: "Source/RenderEngine"
        - Path: "Source/RenderEngine/./IMGUINodeEditor"
        - Path: "Source/Resource"
        - Path: "Source/Resource/BaseClasses"
        - Path: "Source/Runtime/UI"
        - Path: "Source/ScriptEngine"
        - Path: "Source/ScriptEngine/src/include"
        - Path: "Source/Scripts"
        - Path: "ThirdParty/DirectXShaderCompiler/include"
        - Path: "ThirdParty/SPIRV-Cross"
        - Path: "./"
        - Path: "BuildingBlocks/BudgetStreaming"
        - Path: "BuildingBlocks/Clocker"
        - Path: "BuildingBlocks/DGWNetwork"
        - Path: "BuildingBlocks/EditorRuntime"
        - Path: "BuildingBlocks/FullFlightSimulator"
        - Path: "BuildingBlocks/GamePlay"
        - Path: "BuildingBlocks/NetTbuspp"
        - Path: "BuildingBlocks/TemplateBuildingBlock"
      ASTGenerateIncludes:
        # - "extern_pch.h"
        # - "MyTest.h"
        # - "Tools/CodeGen/SourceList/standard_libraries.list"
        #- "EnginePrefix.h"
        # - "source.list"
        #- "build_x64/GeneratedCode/parser.source_list"
        #- "Tools/CodeGen/SourceList/CrossBase.source_list"
        #- "BuildingBlocks/BudgetStreaming/BudgetStreamingSystemG.h"
        - "build_x64/GeneratedCode/test.list"
      # CompileInfoPath:
      #   - "E:/IEG-RED-GameEngine/CrossEngine/build_x64/compile_info/modules/CrossBase.yml"

      CompileExtraArgs:
        - "-x"
        - "c++"
        - "-std=c++17"
        - "-stdlib=libc++"
        - "-Wno-everything"
        - "-DCLANG_GENERATOR=1"
        - "-D__CE_HEAD_TOOL_PARSER__"
        - "-D_MSC_VER=1929"
        - "-DANTLR4CPP_STATIC"
        - "-DCECOMMON_DYNAMIC"
        - "-DCE_USE_DOUBLE_TRANSFORM"
        - "-DCHECK_GL_ERROR"
        - "-DCROSSEDITOR_FBXNEWIMPORTER"
        - "-DCROSSENGINE_DEBUG"
        - "-DCROSSENGINE_EDITOR"
        - "-DCROSSENGINE_WIN"
        - "-DCROSS_BASE_DYNAMIC"
        - "-DENABLE_CPU_PROFILING=1"
        - "-DENABLE_GUARDED_COMPONENT_ACCESS=0"
        - "-DENABLE_GUARDED_COMPONENT_ACCESS_CHECKS=0"
        - "-DENABLE_GUARDED_COMPONENT_ACCESS_RANDOMNESS=0"
        - "-DENABLE_HOOK_NEW_AND_DELETE"
        - "-DENABLE_VR=0"
        - "-DEXPORT_WEBGL"
        - "-DFILESYSTEM_DYNAMIC"
        - "-DJSON_NOEXCEPTION"
        - "-DLIGHTMAP_BAKE_SYSTEM_ENABLE=1"
        - "-DMICROPROFILE_ENABLED=1"
        - "-DMICROPROFILE_GPU_TIMERS=0"
        - "-DNGI_DYNAMIC"
        - "-DNOMINMAX"
        - "-DPHYSICS_ENGINE_DYNAMIC"
        - "-DPONDER_USES_RUNTIME_IMPL"
        - "-DSCRIPT_ENGINE_BACKEND_LUA"
        - "-DSCRIPT_ENGINE_BACKEND_TRAIT_PREFIX=../backend/Lua/trait/Trait"
        - "-DSCRIPT_ENGINE_BIND_FUNCTION_CRASH_HANLDING_SCRIPT_STACK_TRACE"
        - "-DSCRIPT_ENGINE_LANG_LUA"
        - "-DSCRIPT_ENGINE_USE_HOLDING_MEMBER_AND_REF=1"
        - "-DUSE_MIMALLOC=0"
        - "-DUSE_PREFAB_EDITOR"
        - "-DWIN32"
        - "-D_CRT_SECURE_NO_WARNINGS"
        - "-D_ENABLE_EXTENDED_ALIGNED_STORAGE"
        - "-D_MATH_DEFINES_DEFINED"
        - "-D_SILENCE_ALL_CXX17_DEPRECATION_WARNING"
        - "-D_SILENCE_CXX17_ADAPTOR_TYPEDEFS_DEPRECATION_WARNING"
        - "-D_SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING"
        - "-D_SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING"
        - "-D_WINDOWS"
        #- "-D_LIBCPP_CXX03_LANG=1"
        #- "-D__clang__=1"
        #- "-D__cplusplus=202004L"
        #- "-D_LIBCPP_STD_VER=20L"
        # - "-DGBF_ENABLE_CPP20=1"
        #- "-DWIN32"
    Path: "ClangenOutput"
    ServiceProxyPath: "app/lua/exposing/auto/rpc_proxy"
    DetailConfigFile: "E:/IEG-RED-GameEngine/framework-cpp/app/lua/ExportDetailConfig.yaml"
    OutFileName: "CrossPhysics"
  # message
  

