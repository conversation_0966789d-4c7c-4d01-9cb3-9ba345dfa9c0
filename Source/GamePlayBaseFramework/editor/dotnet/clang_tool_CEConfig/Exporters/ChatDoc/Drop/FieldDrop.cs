using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Clangen.Parser;
using DotLiquid;
using ClangSharp.Interop;
using Clangen.Parser.ClangAST;
using System.IO.MemoryMappedFiles;

namespace CppAst.ChatDoc
{
    public class FieldDrop : DotLiquid.Drop
    {
        private static Template msFieldBodyLT;
        private CppField mNativeField;
        private Exporter mExporter;

        public int Index { get; private set; }
        public string AiDoc { get; set; }

        public string DetailName => mNativeField.ToString();

        static FieldDrop()
        {
            msFieldBodyLT = Clangen.TemplateHelper.ParseTemplate("Exporters/ChatDoc/Templates/chatdoc_field_body.liquid");
        }

        public FieldDrop(CppField field, int index, Exporter exporter) 
        {
            mNativeField = field;
            Index = index;
            mExporter = exporter;
        }

        public string RenderBody
        {
            get
            {
                string result = msFieldBodyLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    field = this,
                }));
                return result;
            }
        }

    }
}
