using DotLiquid;

namespace CppAst.CliPlus
{
    public class EnumDrop : General.EnumDrop
    {
        private static Template embedded_template_;
        private static Template global_template_;

        static EnumDrop()
        {
            embedded_template_ = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportCliPlus/Templates/cli_enum_embeded.liquid");
            global_template_ = Clangen.TemplateHelper.ParseTemplate("Exporters/ExportCliPlus/Templates/cli_enum.liquid");
        }

        public EnumDrop(string className, CppEnum e, Exporter exporter) :
            base(className, e, exporter)
        {
        }

        public string Render
        {
            get
            {
                var tempate = HasParent ? embedded_template_ : global_template_;
                ////var tempate = global_template_;
                string result = tempate.Render(Hash.FromAnonymousObject(new
                {
                    @enum = this,
                }));
                return result;
            }
        }
    }
}
