#pragma make_public({{this_class.full_name}})
// {{ this_class.unscoped_name }} export start
{%- unless this_class.is_embedded -%}
{%- for ns in this_class.export_namespace_list -%}namespace {{ns}}{
{%- endfor -%}
{%- endunless -%}
{%- assign extra = this_class.extra_config -%}
{%- if extra.attributes -%}
	{%- if extra.attributes.category -%}
[ClassCategory("{{ extra.attributes.category }}")]
	{%- endif -%}
	{%- if extra.attributes.category -%}
[ClassDisplayName("{{ extra.attributes.display_name }}")]
	{%- endif -%}
[ClassVisible({{ extra.attributes.browsable }})]
{%- endif -%}
{%- unless this_class.is_embedded -%} {{ "public "}} {%- endunless -%} ref class {{ this_class.unscoped_name }}
{%- if this_class.has_base_class -%}
    : {{ this_class.base_class_leaf_name }}
{%- endif -%}
{
public:
	//embeded enums export here
{%- for enum in this_class.enums -%}
{{ enum.render }}
{%- endfor -%}
public:
	//embeded classes export here
{%- for embeded_class in this_class.embeded_classes -%}
	{%- unless embeded_class.need_ignore -%}
	{{ embeded_class.render_bridge_header }}
	{%- endunless -%}
{%- endfor -%}
public:
	//stl container export here
{%- for stl_container in this_class.stl_containers -%}
{{ stl_container.render_header }}
{%- endfor -%}

	//Internal Declarations
{{ this_class.render_help_definitions_header }}

	//Fields export here
{%- for field in this_class.fields -%}
{%- if field.is_public -%}
{{ field.render_bridge_header }}
{%- endif -%}
{%- endfor -%}

{{this_class.render_bridge_ctor_dtor_header}}

{%- for method in this_class.methods -%}
{{ method.main_signature_drop.render_bridge_header }}
{%- endfor -%}

};

{%- unless this_class.is_embedded -%}
{%- for ns in this_class.export_namespace_list -%}
}   //end namespace {{ns}}
{%- endfor -%}
{%- endunless -%}

