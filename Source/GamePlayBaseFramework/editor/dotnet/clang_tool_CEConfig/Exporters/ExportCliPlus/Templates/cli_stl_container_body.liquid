// {{Type.full_cpp_name}} export start
{%- unless Type.is_embeded -%}
{%- for ns in Type.export_namespace_list -%}namespace {{ns}}{
{%- endfor -%}
{%- endunless -%}
{%- if Type.is_vector -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	CPP_DECLARE_STLVECTOR({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_list -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	CPP_DECLARE_STLLIST({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_set -%}
	{%- assign value_type = Type.first_param_drop -%}
	#define STLDECL_MANAGEDTYPE {{ value_type.managed_cli_type_name }}
	#define STLDECL_NATIVETYPE {{ value_type.native_cpp_type_name }}
	CPP_DECLARE_STLSET({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE
{%- elsif Type.is_map -%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	CPP_DECLARE_STLMAP({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- elsif Type.is_multimap -%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	CPP_DECLARE_STLMULTIMAP({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- elsif Type.is_unordered_map-%}
	{%- assign key_type = Type.first_param_drop -%}
	{%- assign mapped_type = Type.second_param_drop -%}
	#define STLDECL_MANAGEDKEY {{ key_type.managed_cli_type_name }}
	#define STLDECL_MANAGEDVALUE {{ mapped_type.managed_cli_type_name }}
	#define STLDECL_NATIVEKEY {{ key_type.native_cpp_type_name }}
	#define STLDECL_NATIVEVALUE {{ mapped_type.native_cpp_type_name }}
	CPP_DECLARE_STLHASHMAP({{ Type.owner_class_name }}, {{ Type.unscoped_name }}, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE
{%- else -%}
	// {{ Type.unscoped_name }} unsupported.
{%- endif -%}
{%- unless Type.is_embeded -%}
{%- for ns in Type.export_namespace_list -%}
}   //end namespace {{ns}}
{%- endfor -%}
{%- endunless -%}
