using System;

namespace CppAst.CliPlus
{
    public class BuildInTypeCarrayItem : General.TypeCarryItem
    {
        protected string mBridgeCppTypeName;
        protected string mBridgeCliTypeName;
        const string kCliIntptrName = "System::IntPtr";

        public BuildInTypeCarrayItem(string nativeType, string cliType)
        {
            mNativeTypeName = nativeType;
            mBridgeCppTypeName = nativeType;
            mBridgeCliTypeName = nativeType;
        }

        public override string ToTypeName(TypeConverterType convType, General.TypeContentOffer typeContent)
        {
            if (typeContent.IsBuildInPointer)
            {
                switch (convType)
                {
                    case TypeConverterType.BridgeCliInput:
                        return kCliIntptrName;
                    case TypeConverterType.BridgeCliReturn:
                        return kCliIntptrName;
                }
            }
            else if (typeContent.IsBuildInReference || typeContent.IsAliasToOrRealBuildIn)
            {
                switch (convType)
                {
                    case TypeConverterType.BridgeCliInput:
                        if (typeContent.IsReference)
                        {
                            if (typeContent.LinkListHasConst)
                            {
                                return string.Format("{0}", mBridgeCliTypeName);
                            }
                            else
                            {
                                return string.Format("[System::Runtime::InteropServices::Out] {0}", mBridgeCliTypeName);
                            }
                        }
                        else
                        {
                            return mBridgeCliTypeName;
                        }
                    case TypeConverterType.BridgeCliReturn:
                        return mBridgeCliTypeName;
                }
            }

            throw new NotImplementedException();
        }

        public override string DoCallCovert(CallConverterType callConvType, string callExpr, General.TypeContentOffer typeContent)
        {
            if (typeContent.IsBuildInPointer)
            {
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    return $"reinterpret_cast<{mNativeTypeName}*>({callExpr}.ToInt64())";
                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    return $"{kCliIntptrName}({callExpr})";
                }
            }
            else
            {
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    if (typeContent.IsPointer)
                    {
                        return string.Format("&{0}", callExpr);
                    }
                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    if (typeContent.IsPointer)
                    {
                        return string.Format("*{0}", callExpr);
                    }
                }
            }

            return callExpr;
        }

        public override bool IsValidForExport(General.TypeContentOffer typeContent)
        {
            return true;
        }
    }
}
