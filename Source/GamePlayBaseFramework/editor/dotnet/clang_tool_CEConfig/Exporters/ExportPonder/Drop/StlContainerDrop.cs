using DotLiquid;

namespace CppAst.Ponder
{
    public class StlContainerDrop : General.StlContainerDrop
    {
        private static DotLiquid.Template msStlContainerHeaderLT;
        private static DotLiquid.Template msStlContainerBodyLT;
        private static DotLiquid.Template msSharedPtrLT;
        private static DotLiquid.Template msPreDeclarationLT;


        static StlContainerDrop()
        {
            ////msStlContainerHeaderLT = TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/cli_stl_container_header.liquid");
            ////msStlContainerBodyLT = TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/cli_stl_container_body.liquid");
            ////msSharedPtrLT = TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/cli_stl_container_shared_ptr.liquid");
            ////msPreDeclarationLT = TemplateHelper.ParseTemplate("Exporters/ExportPonder/Templates/cli_class_pre_declaration.liquid");
        }

        public StlContainerDrop(General.StlContainerType containerType, string leafName, string cookNamespace, string fullName, CppClass templateInstanceNode, Exporter exporter) :
            base(containerType, leafName, cookNamespace, fullName, templateInstanceNode, exporter)
        {
        }

        public string RenderAsSharedPtr
        {
            get
            {
                return msSharedPtrLT.Render(Hash.FromAnonymousObject(new
                {
                    Type = this
                }));
            }
        }

        public string RenderHeader
        {
            get
            {
                if (NeedIgnore)
                {
                    return "";
                }

                return msStlContainerHeaderLT.Render(Hash.FromAnonymousObject(new
                {
                    Type = this
                }));
                ////return StringTools.AddIndentString(render, 1);
            }
        }

        public string RenderBody
        {
            get
            {
                if (NeedIgnore)
                {
                    return "";
                }

                return msStlContainerBodyLT.Render(Hash.FromAnonymousObject(new
                {
                    Type = this
                }));
            }
        }

        public string RenderPreDeclaration
        {
            get
            {
                if (NeedIgnore)
                {
                    return "";
                }

                string result = msPreDeclarationLT.Render(DotLiquid.Hash.FromAnonymousObject(new
                {
                    this_class = this,
                }));
                return result;
            }
        }

        public string CliClassName
        {
            get
            {
                if (IsEmbeded)
                {
                    return string.Format("{0}::{1}", ParentClass.CliClassName, UnscopedName);
                }
                else
                {
                    return string.Format("{0}::{1}", Clangen.NamespaceTool.CookNamespaceToCppNamespace(ExportNamespace), UnscopedName);
                }
            }
        }
    }

}
