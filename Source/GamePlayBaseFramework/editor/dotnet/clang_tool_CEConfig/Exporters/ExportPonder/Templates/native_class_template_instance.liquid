// {{ this_class.unscoped_name }} export start
static int ponder_register_class_template_instance_{{ this_class.identify_name }}()
{
    ponder_register_template_class_{{ this_class.template_identify_name }}<{{ this_class.template_param_list_string }}>("{{ this_class.name }}");

    __register_type<{{ this_class.name }}>("{{ this_class.name }}")
{% comment %} class export start {% endcomment %}
    {%- if this_class.has_base_class -%}
        .base<{{this_class.base_class_name}}>()
    {%- endif -%}
        //member fields export here
    {%- for field in this_class.fields -%}
        {{ field.render }}
    {%- endfor -%}
        //member properties export here
    {%- for prop in this_class.properties -%}
        {{ prop.render }}
    {%- endfor -%}
    {% comment %}
        //embeded enum values export here
    {%- for enum in this_class.enums -%}
{{ enum.render }}
    {%- endfor -%}
    {% endcomment %}
        //constructor export here
    {%- unless this_class.is_abstract -%}
        {%- if this_class.has_instance_constructor -%}
{{ this_class.constructor.render_constructor }}
        {%- endif -%}
    {%- endunless -%}
        //methods export here
    {%- for method in this_class.methods -%}
        {{ method.render }}
    {%- endfor -%}
    ;{% comment %} class export end {% endcomment %}
    return 0;
}