// global functions export start


{%- for method in this_group.methods -%}
    {%- if method.is_overload -%}
{{ method.render_overload_definition }}
    {%- endif -%}
{%- endfor -%}

static int lua_register_global_functions_{{ this_group.identify_name }}(lua_State* L)
{
    luabridge::getGlobalNamespace(L)
        {% for ns in this_group.export_namespace_list %}.beginNamespace("{{ns}}"){% endfor %}
            //methods export here
            {%- for method in this_group.methods -%}
            {{ method.render | strip_newlines}}
            {%- endfor -%}
        {% for ns in this_group.export_namespace_list %}.endNamespace(){% endfor %};

    return 0;
}
