        virtual {{ method.return_type_name }} {{ method.name }}( {{ method.native_param_list_string }} ) {{ method.const_flag }} override 
        {
            luabridge::LuaStackRecover tmpRecover(L);
            int error_func = luabridge::debugger::push_error_func(L);
            luabridge::PushLuaOverrideFunction<{{ method.class_delegate_use_name }}>(L, mSubClassName, "{{ method.name }}");
            if (lua_isfunction(L, -1))
            {
                mSelfRef.push(); //luabridge::Stack<{{ method.const_flag }} {{ method.class_delegate_use_name }}*>::push(L, this);
                {%- for param in method.param_drop_list -%}
                luabridge::Stack<{{param.native_type_name}}>::push(L, {{param.name}});
                {%- endfor -%}

                int errcode = lua_pcall(L, 1 + {{ method.total_param_count }}, {{ method.total_return_count }}, error_func);
                {%- if method.is_return_not_void -%}
                if(errcode == 0)
                {
                    if(luabridge::Stack<{{ method.return_type_name }}>::is_type(L, -1))
                    {
                        return luabridge::Stack<{{ method.return_type_name }}>::get(L, -1);
                    }
                    else
                    {
                        luaL_error(L, "[error] call lua override [{{ method.main_signature_drop.class_name }}::{{ method.name }}()] return a not right value!"); 
                        {{ method.default_return_code }}
					}
                }
                {%- else -%}
                if(errcode == 0){ return; }
                {%- endif -%}
                luaL_error(L, "[error]  call lua override [{{ method.main_signature_drop.class_name }}::{{ method.name }}()] failed!");
                {{ method.default_return_code }}
            }
            else
            {
                {%- if method.is_pure_virtual -%}
                luaL_error(L, "[error] Can not call a base class pure virutal method [{{ method.main_signature_drop.class_name }}::{{ method.name }}()]");
                {{ method.default_return_code }}
                {%- else -%}
                    {%- if method.is_return_not_void -%}
                return {{ method.main_signature_drop.class_name }}::{{ method.name }}( {{ method.param_list_no_type_string }} );    
                    {%- else -%}
                {{ method.main_signature_drop.class_name }}::{{ method.name }}( {{ method.param_list_no_type_string }} );
                    {%- endif -%}
                {%- endif -%}
			}
        }