{%- assign main_signature = method.main_signature_drop -%}
{%- case use_case -%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Constructor' -%}
    {%- for sig in method.signatures -%}
        .constructor<{{sig.constructor_type_list}}>()
    {%- endfor -%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Overload' -%}
        .overload("{{ main_signature.target_name }}"
    {%- for sig in method.signatures -%}
            ,{{ sig.render_call_lambda }}
    {%- endfor -%}
        ){% if main_signature.tag_number != 0 %}.function_tag_number({{main_signature.tag_number}}){% endif %}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'Normal' -%}
        .function("{{ main_signature.target_name }}", &{{ main_signature.class_name }}::{{ main_signature.name }}){% if main_signature.tag_number != 0 %}.function_tag_number({{main_signature.tag_number}}){% endif %}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'NormalStatic' -%}
        .static_function("{{ main_signature.target_name }}", &{{ main_signature.class_name }}::{{ main_signature.name }}){% if main_signature.tag_number != 0 %}.function_tag_number({{main_signature.tag_number}}){% endif %}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'RpcClangProxy' -%}
    {%- if main_signature.rpc_with_context_flag -%}
    gbf::logic::RpcRequestAHandle {{ main_signature.target_name }}({{ main_signature.rpc_declare_params }}, gbf::logic::ServiceContextPtr context = gbf::logic::ServiceContextPtr{})
    {
        //rpc return value type is:     {{main_signature.return_type_name}}
        return gbf::logic::RpcRequestAHandle{ mServiceProxy, "{{ main_signature.target_name }}",  gbf::reflection::Args{ {{main_signature.rpc_call_params}} }, context};
    }
    {%- else -%}
    gbf::logic::RpcRequestAHandle {{ main_signature.target_name }}({{ main_signature.rpc_declare_params }})
    {
        //rpc return value type is:     {{main_signature.return_type_name}}
        return gbf::logic::RpcRequestAHandle{ mServiceProxy, "{{ main_signature.target_name }}",  gbf::reflection::Args{ {{main_signature.rpc_call_params}} }, timeoutMs};
    }
    {%- endif -%}
{%comment%}----------------------------------------------------------------------------------{%endcomment%}
  {%- when 'EntityRpcClangProxy' -%}
    {%- if main_signature.rpc_with_context_flag -%}
    gbf::logic::EntityRpcRequestAHandle {{ main_signature.target_name }}({{ main_signature.rpc_declare_params }}, gbf::logic::ServiceContextPtr context = gbf::logic::ServiceContextPtr{})
    {
        //rpc return value type is:     {{main_signature.return_type_name}}
        return gbf::logic::EntityRpcRequestAHandle{ mServiceServerPtr, mGroupName, mEntityId, mServiceName, "{{ main_signature.target_name }}",  gbf::reflection::Args{ {{main_signature.rpc_call_params}} }, context};
    }
    {%- else -%}
    gbf::logic::EntityRpcRequestAHandle {{ main_signature.target_name }}({{ main_signature.rpc_declare_params }})
    {
        //rpc return value type is:     {{main_signature.return_type_name}}
        return gbf::logic::EntityRpcRequestAHandle{ mServiceServerPtr, mGroupName, mEntityId, mServiceName, "{{ main_signature.target_name }}",  gbf::reflection::Args{ {{main_signature.rpc_call_params}} }, timeoutMs};
    }
    {%- endif -%}
{%- endcase -%}