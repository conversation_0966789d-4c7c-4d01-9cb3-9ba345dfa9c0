using System;
using System.Diagnostics;

namespace CppAst.Ponder
{
    public class UDTTypeCarrayItem : General.TypeCarryItem
    {
        public UDTTypeCarrayItem()
        {
            mNativeTypeName = "udt";
        }

        bool QueryDropInfoFromType(General.TypeContentOffer typeContent, out string cliClassName)
        {
            string cookClassName = typeContent.LinkListTargetType.FullCookName;

            if (cookClassName.StartsWith("std"))
            {
                Debugger.Break();
            }

            cookClassName = Clangen.NamespaceTool.CppNamespaceToCookNamespace(cookClassName);

            //Check record
            {
                General.ContentContainerDrop classDrop = Exporter.Instance.TryGetCollectNormalClassDrop(cookClassName);
                if (classDrop != null)
                {
                    cliClassName = classDrop.CliClassName;
                    return true;
                }
            }

            //Check template instance
            {
                cookClassName = Exporter.Instance.UsedAliasNameMap.GetFinalyAliasName(cookClassName);
                General.ContentContainerDrop classDrop = Exporter.Instance.TryGetCollectTemplateInstanceClassDrop(cookClassName);
                if (classDrop != null)
                {
                    cliClassName = classDrop.CliClassName;
                    return true;
                }
            }

            //Check Stl Container
            {
                General.StlContainerDrop stlDrop = Exporter.Instance.TryGetStlContainerDrop(cookClassName);
                if (stlDrop != null)
                {
                    cliClassName = (stlDrop as StlContainerDrop).CliClassName;
                    return true;
                }
            }

            cliClassName = "";
            return false;
        }

        public override string ToTypeName(TypeConverterType convType, General.TypeContentOffer typeContent)
        {
            string cliClassName;
            bool querySuc = QueryDropInfoFromType(typeContent, out cliClassName);
            switch (convType)
            {
                case TypeConverterType.BridgeCliInput:
                case TypeConverterType.BridgeCliReturn:
                    return querySuc ? string.Format("{0}^", cliClassName) : "UnknowKeeper^";
            }
            throw new NotImplementedException();
        }
        public override string DoCallCovert(CallConverterType callConvType, string callExpr, General.TypeContentOffer typeContent)
        {
            string cliClassName;
            bool querySuc = QueryDropInfoFromType(typeContent, out cliClassName);
            if (querySuc)
            {
                string mangedClassName = cliClassName;
                string cppClassName = typeContent.FullCppName;
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    if (typeContent.IsValueUDT)
                    {
                        return string.Format("*(({1}*)({0}))", callExpr, cppClassName);
                    }
                    else
                    {
                        return string.Format("({1})({0})", callExpr, cppClassName);
                    }

                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    return string.Format("({1}^)({2}{0})", callExpr, mangedClassName, typeContent.IsValueUDT ? "&" : "");
                }
            }
            else
            {
                //UnknowKeeper
                string cppClassName = General.NativeHelper.RemoveStartConstPrefixFromName(typeContent.FullCppName);
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    if (typeContent.IsValueUDT)
                    {
                        return string.Format("*(UnknowKeeper::get_native_with_type_for_pointer<{1}*>({0}))", callExpr, cppClassName);
                    }
                    else
                    {
                        if (typeContent.IsReference)
                        {
                            return string.Format("(UnknowKeeper::get_native_with_type_for_reference<{1}>({0}))", callExpr, cppClassName);
                        }
                        else
                        {
                            return string.Format("(UnknowKeeper::get_native_with_type_for_pointer<{1}>({0}))", callExpr, cppClassName);
                        }

                    }

                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    return string.Format("UnknownKeeperCast({1}{0})", callExpr, typeContent.IsValueUDT ? "&" : "");
                }
            }

            return callExpr;
        }

        public override bool IsValidForExport(General.TypeContentOffer typeContent)
        {
            string cliClassName;
            return QueryDropInfoFromType(typeContent, out cliClassName);
        }
    }
}
