using System.Collections.Generic;

namespace CppAst.Pyra
{
    public class MethodSignatureDrop : General.MethodSignatureDrop
    {
        static MethodSignatureDrop()
        {
        }

        public MethodSignatureDrop(General.ClassDrop parentDrop,
            General.MethodSignatureContentOffer method, string targetName, Exporter exporter) :
            base(parentDrop, method, targetName, exporter)
        {
        }

        protected string LambdaDeclareParams
        {
            get
            {
                List<string> paramStrList = new List<string>();

                if (!IsStatic)
                {
                    string constFlag = "";
                    if (IsConst)
                    {
                        constFlag = "const ";
                    }
                    paramStrList.Add($"{constFlag}{ClassName}* self");
                }

                foreach (var param in ParamList)
                {
                    paramStrList.Add($"{param.TypeDrop.NativeCppTypeName.Trim()} {param.Name}");
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }


        protected string LambdaCallParams
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.Name);
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }


        public string RpcDeclareParams
        {
            get
            {
                List<string> paramStrList = new List<string>();
                for (int i = 0; i < ParamList.Count; i++)
                {
                    var param = ParamList[i];
                    if (IsAutoFillRpcParam(param))
                    {
                        continue;
                    }

                    paramStrList.Add($"{param.TypeDrop.NativeCppTypeName.Trim()} {param.Name}");
                }
                if (!Clangen.GBFCodeGenCommand.UsedConfig.RpcWithContextFlag)
                {
                    paramStrList.Add("int timeoutMs = 10000");
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string RpcCallParams
        {
            get
            {
                List<string> paramStrList = new List<string>();
                for (int i = 0; i < ParamList.Count; i++)
                {
                    var param = ParamList[i];
                    if (IsAutoFillRpcParam(param))
                    {
                        continue;
                    }

                    paramStrList.Add($"gbf::reflection::make_value({param.Name})");
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string ConstructorTypeList
        {
            get
            {
                List<string> typeStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    typeStrList.Add(param.TypeDrop.NativeCppTypeName.Trim());
                }

                return Clangen.NamespaceTool.GetCombinedString(typeStrList, ", ");
            }
        }

        public string RenderCallLambda
        {
            get
            {
                string objCallStart;
                if (IsStatic)
                {
                    objCallStart = $"{ClassName}::";
                }
                else
                {
                    objCallStart = "self->";
                }

                string returnPrefix = " ";
                if (IsReturnNotVoid)
                {
                    returnPrefix = "return ";
                }

                return $"[]({LambdaDeclareParams}){{{returnPrefix}{objCallStart}{Name}({LambdaCallParams}); }}";
            }
        }

        private bool IsAutoFillRpcParam(General.MethodParamDrop param)
        {
            return param.TypeDrop.NativeTypeOffer.LinkListTargetType.FullCppName == "gbf::logic::Scheduler"
                    || param.TypeDrop.NativeTypeOffer.LinkListTargetType.FullCppName == "gbf::logic::ServiceContext"
                    || param.TypeDrop.NativeTypeOffer.LinkListTargetType.FullCppName == "gbf::logic::RouterEntity";
        }
    }
}
