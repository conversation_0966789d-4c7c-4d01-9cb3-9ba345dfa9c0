        static {{ method.return_type_name }} {{ method.name }}({{ method.main_signature_drop.class_name }}* obj{%- unless method.is_param_list_empty -%}, {%- endunless -%}{{ method.native_param_list_string }}, lua_State* L )
        {
            {%- if method.is_return_not_void -%}
                {%- if method.is_pure_virtual -%}
            luaL_error(L, "[error] Can not call a base class pure virutal method [{{ method.main_signature_drop.class_name }}::{{ method.name }}()]");
            {{ method.default_return_code }}
                {%- else -%}
            return obj->{{ method.main_signature_drop.class_name }}::{{ method.name }}( {{ method.param_list_no_type_string }} );
                {%- endif -%}
            {%- else -%}
                {%- unless method.is_pure_virtual -%}
            obj->{{ method.main_signature_drop.class_name }}::{{ method.name }}( {{ method.param_list_no_type_string }} );
                {%- endunless -%}
            {%- endif -%}
        }