using System;
using System.Collections.Generic;
using System.Linq;

namespace CppAst.General
{
    public class MethodSignatureDrop : DotLiquid.Drop
    {
        protected MethodSignatureContentOffer mMethodContentOffer;

        protected ClassDrop mParentDrop;

        protected Exporter mExporter;

        public TypeOfferDrop ReturnDrop { get; private set; }
        public List<MethodParamDrop> ParamList { get; } = new List<MethodParamDrop>();


        public MethodSignatureDrop(ClassDrop parentDrop,
            MethodSignatureContentOffer method, string targetName, Exporter parentExporter)
        {
            mMethodContentOffer = method;
            mParentDrop = parentDrop;
            TargetName = targetName;
            mExporter = parentExporter;

            ReturnDrop = mExporter.CreateTypeOfferDrop(method.ReturnType);

            int indexCount = 0;
            foreach (var param in mMethodContentOffer.ParamList)
            {
                ParamList.Add(mExporter.CreateMethodParamDrop(param, indexCount));
                indexCount++;
            }

            TagNumber = (uint)mMethodContentOffer.MetaMap.QueryArgumentAsInteger(MetaAttributeArgNames.Tag, 0);
        }

        public void AddParam(MethodParamDrop param)
        {
            ParamList.Add(param);
        }

        public bool Last { get; set; } = false;
        public bool IsMemberFunction { get { return !mMethodContentOffer.IsStatic; } }

        public int SignatureIndex
        {
            get; set;
        }

        public bool IsStatic
        {
            get
            {
                return mMethodContentOffer.IsStatic;
            }
        }

        public bool IsConst
        {
            get
            {
                return mMethodContentOffer.IsConst;
            }
        }

        public bool IsConstructor
        {
            get
            {
                return mMethodContentOffer.IsConstructor;
            }
        }

        public string Name
        {
            get
            {
                return mMethodContentOffer.MethodName;
            }
        }

        public string TargetName
        {
            get;
            private set;
        }

        public string ClassName
        {
            get
            {
                return mParentDrop.GetUsedClassNameForChild();
            }
        }

        public string AliasClassName
        {
            get
            {
                return mParentDrop.AliasName;
            }
        }

        public string LeafClassName
        {
            get
            {
                return mParentDrop.NoNamespaceName;
            }
        }

        public ClassDrop ParentDrop
        {
            get
            {
                return mParentDrop;
            }
        }

        public bool IsGlobalFunction
        {
            get
            {
                return mMethodContentOffer.IsGlobalFunction;
            }
        }

        public bool IsPureVirtual
        {
            get
            {
                return mMethodContentOffer.IsPureVirtual;
            }
        }

        public bool IsReturnNotVoid
        {
            get
            {
                return !mMethodContentOffer.ReturnType.IsVoid;
            }
        }

        public int TotalParamCount
        {
            get
            {
                return mMethodContentOffer.ParamList.Count;
            }
        }

        public bool IsParamListEmpty
        {
            get
            {
                return mMethodContentOffer.ParamList.Count == 0;
            }
        }

        public int TotalReturnCount
        {
            get
            {
                return IsReturnNotVoid ? 1 : 0;
            }
        }

        public string ReturnTypeName
        {
            get
            {
                return mMethodContentOffer.ReturnType.FullCppName;
            }
        }

        public bool VoidParam
        {
            get { return ParamList.Count() == 0; }
        }

        public int ParamCount
        {
            get
            {
                return ParamList.Count;
            }
        }

        public string FunctionType
        {
            get
            {
                if (IsMemberFunction)
                {
                    string result = string.Format("{0}({1}::*)({2})", mMethodContentOffer.FullReturnTypeName, mParentDrop.Name, RenderDecl());
                    if (mMethodContentOffer.IsConst)
                        result += "const";
                    return result;
                }

                return string.Format("{0}({1})", mMethodContentOffer.FullReturnTypeName, RenderDecl());
            }
        }

        public string RenderDecl()
        {
            string result = string.Join(", ", ParamList.Select(param => param.TypeDrop.BridgeCppParamPair));
            return result;
        }


        public string BridgeInstanceParamName
        {
            get
            {
                return "__self_instance";
            }
        }

        public string BridgeCppReturnTypeName
        {
            get
            {
                return ReturnDrop.BridgeCppReturnTypeName;
            }
        }


        public string BridgeCppParamListString
        {
            get
            {


                List<string> paramStrList = new List<string>();

                ////if (!IsStatic && !IsConstructor)
                ////{
                ////    paramStrList.Add(string.Format("{0}* {1}", AliasClassName, BridgeInstanceParamName));
                ////}

                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.TypeDrop.BridgeCppParamPair);
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string NativeParamListString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(string.Format("{0} {1}", param.TypeDrop.NativeCppTypeName, param.Name));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string NativeParamListStringProxyConstructor
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(string.Format("{0} {1}", param.TypeDrop.NativeCppTypeName, param.Name));
                }
                paramStrList.Add(string.Format("{0}^ managedObj", ParentDrop.CliClassName));
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string ParamListNoTypeString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.Name);
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string BridgeToNativeParamListNoTypeString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.TypeDrop.DoCallConvert(TypeCarryItem.CallConverterType.Bridge2Native, param.Name));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string BridgeToNativeParamListNoTypeStringOverrideProxy
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.TypeDrop.DoCallConvert(TypeCarryItem.CallConverterType.Bridge2Native, param.Name));
                }
                paramStrList.Add("this");
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }

        public string NativeToBridgeParamListNoTypeString
        {
            get
            {
                List<string> paramStrList = new List<string>();
                foreach (var param in ParamList)
                {
                    paramStrList.Add(param.TypeDrop.DoCallConvert(TypeCarryItem.CallConverterType.Native2Bridge, param.Name));
                }
                return Clangen.NamespaceTool.GetCombinedString(paramStrList, ", ");
            }
        }


        public List<MethodParamDrop> ParamDropList
        {
            get
            {
                List<MethodParamDrop> tmpParamList = new List<MethodParamDrop>();
                foreach (var param in mMethodContentOffer.ParamList)
                {
                    tmpParamList.Add(mExporter.CreateMethodParamDrop(param, tmpParamList.Count));
                }

                return tmpParamList;
            }
        }

        public uint TagNumber
        {
            get;
            private set;
        }

        public bool RpcWithContextFlag
        {
            get
            {
                return Clangen.GBFCodeGenCommand.UsedConfig.RpcWithContextFlag;
            }
        }

        public static List<MethodSignatureDrop> ParamListToSignatures(ClassDrop parentDrop, CppFunction method, Exporter exporter)
        {
            var paramList = method.Parameters;
            List<MethodSignatureDrop> result = new List<MethodSignatureDrop>();

            int firstDefaultParamIndex = paramList.Count - method.DefaultParamCount;
            int lastDefaultParamIndex = firstDefaultParamIndex + method.DefaultParamCount - 1;
            for (int loop = firstDefaultParamIndex; loop <= lastDefaultParamIndex; ++loop)
            {
                var signature = exporter.CreateMethodSignatureDrop(parentDrop, exporter.GenerateCppFunctionContentOffer(method, null), method.Name);
                for (int index = 0; index < loop; ++index)
                {
                    var param = exporter.CreateMethodParamDrop(new MethodParamContentOffer(exporter.ParentCompilation, paramList[index].Type, paramList[index].Name, null), index);
                    signature.AddParam(param);
                }
                result.Add(signature);
            }

            if (result.Count > 0)
            {
                result.Last().Last = true;
            }
            return result;
        }
    }
}
