using DotLiquid;

namespace CppAst.General
{
    public class PropertyDrop : Drop
    {
        protected string mParentClassName;

        public string Name { get; private set; }
        public string Get { get; private set; }
        public string Set { get; private set; }

        public PropertyDrop(string parentClassName, string name, string get, string set)
        {
            mParentClassName = parentClassName;
            Name = name;
            Get = get;
            Set = set;
        }

        public string ClassName
        {
            get { return mParentClassName; }
        }
    }
}
