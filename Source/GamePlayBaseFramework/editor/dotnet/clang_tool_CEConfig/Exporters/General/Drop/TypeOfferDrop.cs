using DotLiquid;

namespace CppAst.General
{
    public class TypeOfferDrop : Drop
    {
        private Exporter mExporter;
        public TypeContentOffer NativeTypeOffer;
        public string ExtraName
        {
            get; private set;
        }

        public TypeOfferDrop(TypeContentOffer typeOffer, Exporter parentExporter, string extraName = "")
        {
            mExporter = parentExporter;
            NativeTypeOffer = typeOffer;
            ExtraName = extraName;
        }

        public string BridgeCppReturnTypeName
        {
            get
            {
                return mExporter.QueryTypeBridgeTool().ToTypeName(TypeCarryItem.TypeConverterType.BridgeCliReturn, NativeTypeOffer);
            }
        }

        public string BridgeCppInputTypeName
        {
            get
            {
                return mExporter.QueryTypeBridgeTool().ToTypeName(TypeCarryItem.TypeConverterType.BridgeCliInput, NativeTypeOffer);
            }
        }

        public string ManagedCliTypeName => BridgeCppInputTypeName;

        public string ManagedCliTypeNameNoSymbol
        {
            get
            {
                return ManagedCliTypeName.Substring(0, ManagedCliTypeName.Length - 1).Trim();
            }
        }

        public string NativeCppTypeName
        {
            get
            {
                return NativeTypeOffer.FullCppName;
            }
        }

        public string BridgeCppParamPair
        {
            get
            {
                return string.Format("{0} {1}", BridgeCppInputTypeName, ExtraName);
            }
        }

        public bool NeedIgnore
        {
            get
            {
                return ManagedCliTypeName.Contains("UnknowKeeper");
            }
        }

        public string DoCallConvert(TypeCarryItem.CallConverterType callConvType, string callExpr)
        {
            return mExporter.QueryTypeBridgeTool().DoCallCovert(callConvType, callExpr, NativeTypeOffer);
        }

    }
}
