// {{ this_class.unscoped_name }} export start
{%- unless this_class.is_embedded -%}
{%- for ns in this_class.export_namespace_list -%}
namespace {{ns}}
{
{%- endfor -%}
{%- endunless -%}

//embeded classes
{%- for embeded_class in this_class.embeded_classes -%}
	{%- unless embeded_class.need_ignore -%}
{{ embeded_class.render_bridge_body }}
	{%- endunless -%}
{%- endfor -%}

//stl container export here
{%- for stl_container in this_class.stl_containers -%}
{{ stl_container.render_body }}
{%- endfor -%}

//fields export here
{%- for field in this_class.fields -%}
{%- if field.is_public -%}
{{ field.render_bridge_body }}
{%- endif -%}
{%- endfor -%}

{{ this_class.render_bridge_ctor_dtor_body }}

{{ this_class.render_help_definitions_body }}

{%- for method in this_class.methods -%}
{{ method.render_bridge_body }}
{%- endfor -%}

{%- unless this_class.is_embedded -%}
{%- for ns in this_class.export_namespace_list -%}
}   //end namespace {{ns}}
{%- endfor -%}
{%- endunless -%}
