{%- if method.is_constructor -%}
	{{ method.leaf_class_name }}({{ method.bridge_cpp_param_list_string }} );
{%- else -%}
	{%- if method.is_override -%}
	virtual {{ method.bridge_cpp_return_type_name }} {{ method.target_name }}({{ method.bridge_cpp_param_list_string }} ) = 0;
	{%- else -%}
	{% if method.is_static %}static {% endif %}{{ method.bridge_cpp_return_type_name }} {{ method.target_name }}({{ method.bridge_cpp_param_list_string }} );
	{%- endif -%}
{%- endif -%}