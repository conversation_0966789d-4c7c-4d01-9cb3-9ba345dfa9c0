/* ----------------------------------------------------------------------------
 * This file was automatically generated by CodeGen {% include 'version' %}
 * --------------------------------------------------------------------------*/
#include "Runtime/Reflection/ReflectionRegister.h"

#if defined(CROSSENGINE_WIN)
#    define REFLECT_REGISTER_IMPORT __declspec(dllimport)
#else
#    define REFLECT_REGISTER_IMPORT
#endif

{%- for register_func in register_functions -%}
void  {{register_func}}();
{%- endfor -%}
{%- comment -%} void SystemRegisterG(); {%- endcomment -%}

namespace cross {
void ReflectionRegister() 
{
    {%- for register_func in register_functions -%}
    {{register_func}}();
    {%- endfor -%}
    {%- comment -%} SystemRegisterG(); {%- endcomment -%}
}
}
