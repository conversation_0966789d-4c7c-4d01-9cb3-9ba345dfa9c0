{%- assign template_arguments_class = class.template_arguments_classes[0] -%}
{%- if template_arguments_class.need_binding_ts -%}
    struct {{ class.type_script_extension_name }}
    {
        {%- assign type_script_function = class.template_arguments_classes[0].type_script_function -%}
        {%- for func in type_script_function.select_functions -%}
            {%- if func.is_static == false -%}
        static {{func.return_type.full_name}} {{func.name}}(const {{class.full_name}}& ptr{% if func.parameters.size > 0 %}, {% endif %}{{func.str_parameters}})
        {
            {% if func.return_type.is_void == false %}return {% endif %}ptr->{{func.name}}({{func.str_parameters_no_type}});
        }
            {%- endif -%}
        {%- endfor -%}
        static uint64_t GetClassID(const {{class.full_name}}& ptr)
        {
            return ptr->GetClassID();
        }
    
        static uint64_t StaticClassID()
        {
            return {{class.full_name}}::StaticClassID();
        }
    };
{%- endif -%}
   