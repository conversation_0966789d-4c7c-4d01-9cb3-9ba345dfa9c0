using CppAst;
using System.Collections.Generic;
using System.Linq;

namespace Clangen.Parser
{
    public class CEMetaManager
    {
        //private Dictionary<string, object> metaDict;
        public static readonly string Prefix = "CEMeta:";

        private Dictionary<string, List<object>> MetaDict;
        public CEMetaManager()
        {
            MetaDict = new Dictionary<string, List<object>>();
        }

        public CEMetaManager(MetaAttributeMap metaAttrMap)
        {

            MetaDict = new Dictionary<string, List<object>>();
            foreach (var meta in metaAttrMap.MetaList)
            {
                foreach (var item in meta.ArgumentMap)
                {
                    if (!MetaDict.ContainsKey(item.Key))
                    {
                        MetaDict[item.Key] = new List<object>();

                    }
                    MetaDict[item.Key].Add(item.Value);

                }
            }
        }

        public void Append(CEMetaManager ceMeta)
        {
            foreach (var item in ceMeta.MetaDict)
            {
                if (!MetaDict.ContainsKey(item.Key))
                {
                    MetaDict[item.Key] = new();
                }
                MetaDict[item.Key].AddRange(item.Value);
            }
        }

        public bool HasKey(string key) => MetaDict.ContainsKey(key);

        public List<object> GetValue(string key, List<object> defaultValue = null)
        {
            if (MetaDict.ContainsKey(key))
            {
                return MetaDict[key];
            }
            else
            {
                return defaultValue ?? new List<object>();
            }
        }

        public object GetFirstValue(string key, object defaultValue = null)
        {
            if (MetaDict.ContainsKey(key) && MetaDict[key].Count > 0)
            {
                return MetaDict[key][0];
            }
            return defaultValue ?? null;
        }

        public string GetFirstValueAsString(string key)
        {
            if (MetaDict.ContainsKey(key))
            {
                return CEMetaObjectToString(MetaDict[key][0]);
            }
            else
            {
                throw new KeyNotFoundException($"The given key '{key}' was not present in the CEMeta.");
            }
        }

        public string GetFirstValueAsStringWithDefault(string key, string defaultValue)
        {
            if (MetaDict.ContainsKey(key))
            {
                if (MetaDict[key][0] == null)
                {
                    return defaultValue;
                }
                return CEMetaObjectToString(MetaDict[key][0]);
            }
            else
            {
                return defaultValue;
            }
        }

        public bool IsCeFunction => HasKey("Function");
        public bool IsCeProperty => HasKey("Property");
        public bool IsSystem => HasKey("IsSystem");
        public bool IsComponent => HasKey("IsComponent");
        public bool IsGameplay => HasKey("IsGameplay");
        public bool IsWorflowType => HasKey("WorkflowType");

        public string SystemType
        {
            get
            {
                string systemType = null;
                if (HasKey("SystemType"))
                {
                    systemType = GetValue("SystemType").FirstOrDefault() as string;
                }
                return systemType ?? "";
            }
        }
        override public string ToString()
        {
            List<string> list = new List<string>();
            foreach (var item in MetaDict)
            {
                if (item.Value.Count == 0)
                {
                    list.Add(item.Key);
                }
                else
                {
                    foreach (var v in item.Value)
                    {
                        list.Add(item.Key + CEMetaObjectToString(v, false));
                    }
                }
            }
            return string.Join(", ", list);
        }

        public static string CEMetaObjectToString(object value, bool isOutermost = true)
        {
            if (value is null)
            {
                return "";
            }
            else
            if (value != null && value is Dictionary<string, object> valueDict)
            {
                var strList = new List<string>();
                var paramList = valueDict.Select(item => item.Key + CEMetaObjectToString(item.Value, false));

                var ret = string.Join(", ", paramList);
                return isOutermost ? ret : "(" + ret + ")";
            }
            else
            {
                return (isOutermost ? "" : " = ") + value.ToString();
            }
        }
    };

}