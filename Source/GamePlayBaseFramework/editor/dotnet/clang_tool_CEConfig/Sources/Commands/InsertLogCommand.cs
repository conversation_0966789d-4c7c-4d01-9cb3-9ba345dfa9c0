using CppAst;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using YamlDotNet.Serialization;

namespace Clangen
{
    public class InsertLogConfig
    {
        public int MinLineCount { get; set; } = 1;
        public bool SkipInline { get; set; } = true;
        public bool SkipConstructor { get; set; } = false;
        public bool SkipDestructor { get; set; } = false;
        public bool RecurseCallExpr { get; set; } = true;
        public List<string> ModuleFilters { get; set; } = null;
        public List<string> FileFilters { get; set; } = null;
        public List<string> FunctionFilters { get; set; } = null;
        public List<string> Statements { get; set; } = new List<string>();
        public List<string> Includes { get; set; } = new List<string>();

        static IDeserializer deserializer = new DeserializerBuilder()
                                                .IgnoreUnmatchedProperties()
                                                //.WithNamingConvention(PascalCaseNamingConvention.Instance)
                                                .Build();
        public static InsertLogConfig FromYaml(string filePath)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"InsertLogConfig {filePath} does NOT exist");
            }
            string fileContent = File.ReadAllText(filePath);
            var content = deserializer.Deserialize<InsertLogConfig>(fileContent);

            if (content.FileFilters != null)
            {
                for (int i = 0; i < content.FileFilters.Count; i++)
                {
                    content.FileFilters[i] = content.FileFilters[i].Replace('\\', '/');
                }
            }
            return content;
        }

        public List<ModuleInfo> Filte(List<ModuleInfo> modules)
        {
            List<ModuleInfo> ModuleInfoList = modules;
            if (ModuleFilters != null)
                ModuleInfoList = ModuleInfoList.Where(v => ModuleFilters.Contains(v.name)).ToList();
            if (FileFilters != null)
            {
                foreach (var module in ModuleInfoList)
                {
                    List<string> sources = new List<string>();
                    module.sources = module.sources.Where(
                        file => FileFilters.Any(pattern => file.Contains(pattern))
                    ).ToList();
                }
            }
            return ModuleInfoList;
        }

        public List<CppFunction> FilteFiles(List<CppFunction> functions)
        {
            List<CppFunction> funcList = functions;
            if (FileFilters != null)
            {
                funcList = funcList.Where(
                    func => func.Body != null && FileFilters.Any(pattern => func.Body.Span.Start.File.Contains(pattern))
                ).ToList();
            }
            return funcList;
        }

        public List<CppFunction> FilteFunctionNames(List<CppFunction> functions)
        {
            List<CppFunction> funcList = functions;
            if (FunctionFilters != null)
            {
                funcList = new List<CppFunction>();
                foreach (var func in functions)
                {
                    string fullname = func.FullParentName + "::" + func.Name;
                    if (FunctionFilters.Any(pattern => fullname.EndsWith(pattern)))
                    {
                        funcList.Add(func);
                    }
                }
            }
            return funcList;
        }
    }

    public class InsertLogCommand : IClangToolCommand
    {
        static readonly string MODULE_NAME_TAG = "%MODULE_NAME%";
        static readonly string CLASS_NAME_TAG = "%CLASS_NAME%";
        static readonly string FUNCTION_NAME_TAG = "%FUNCTION_NAME%";

        public static readonly string INSERT_BEGING_COMMENT = "//************** INSERT BEGIN **************";
        public static readonly string INSERT_END_COMMENT = "//************** INSERT END   **************";

        public string CommandName => "InsertLog";
        public List<ModuleInfo> ModuleInfoList = new List<ModuleInfo>();

        CompileOption CompileOption;
        InsertLogConfig InsertLogConfig;

        bool CleanModel = false;
        public InsertLogCommand()
        {
        }

        public bool Init(string[] args)
        {
            bool showHelp = false;
            string configPath = "";
            string CompileOptionFile = "";

            NDesk.Options.OptionSet argset = new NDesk.Options.OptionSet() {
                { "c|compile-cfg=",  "{PATH} to the compile info file", v => CompileOptionFile = v},
                { "clean", "clean inserted code", v => CleanModel = true},
                { "cfg=", "{PATH} to config", v => configPath = v},
                { "h|help", "show this help message and exit", v => showHelp = v != null },
            };
            List<string> extra = argset.Parse(args);

            if (string.IsNullOrEmpty(CompileOptionFile) || string.IsNullOrEmpty(configPath))
                showHelp = true;

            if (showHelp)
            {
                Console.WriteLine($"Usage:");
                Console.WriteLine($"  clang-tools {CommandName} -c <compile_config_file> --cfg=<config_path> [--clean]");
                Console.WriteLine($"Options:");
                argset.WriteOptionDescriptions(Console.Out);
                return false;
            }

            CompileOption = CompileOption.FromYaml(CompileOptionFile);
            InsertLogConfig = InsertLogConfig.FromYaml(configPath);
            string module_dir = CompileOption.module_dir;

            if (!Directory.Exists(module_dir))
                module_dir = Path.Combine(Directory.GetParent(CompileOptionFile).FullName, module_dir);


            foreach (var module_file in Directory.GetFiles(module_dir, "*.yml"))
            {
                // note ,the crossengine option is only use for dummy
                var model_info = ModuleInfo.FromYaml(module_file, new CrossEngineOption());
                ModuleInfoList.Add(model_info);
            }

            if (!CleanModel)
                ModuleInfoList = InsertLogConfig.Filte(ModuleInfoList);

            if (ModuleInfoList.Count > 0)
            {
                string module_names = string.Join(",", ModuleInfoList.Select(v => v.name));
                Log.Info($"ModuleInfoList : [{module_names}]");
            }
            else
            {
                Log.Error("ModuleInfoList Is Empty");
                return false;
            }

            return true;
        }

        public List<string> SystemIncludeDirectories => CompileOption.implicit_include_directories;

        public List<string> ClangArguments
        {
            get
            {
                var args = new List<string>()
                {
                     "-std=c++20",  "-w", "-ferror-limit=50", "-D__CE_HEAD_TOOL_PARSER__",
                };

                if (!string.IsNullOrEmpty(CompileOption.compiler_target))
                {
                    args.Add($"--target={CompileOption.compiler_target}");
                }
                if (!string.IsNullOrEmpty(CompileOption.sysroot))
                {
                    args.Add($"-isysroot{CompileOption.sysroot}");
                }
                return args;
            }
        }
        CppCompilation Parse(ModuleInfo module)
        {
            var cppFiles = module.sources.Where(x => x.EndsWith(".cpp")).ToList();
            var Defines = new List<string>(module.definitions);
            Defines.Add(module.name + "_EXPORTS");
            var parserOptions = new CppAst.CppParserOptions()
            {
                Defines = Defines,
                IncludeFolders = module.include_dirs,
                SystemIncludeFolders = SystemIncludeDirectories,
                AdditionalArguments = ClangArguments,
                ParseAsCpp = true,
                ParseMacros = false,
                ParseComments = false,
                ParseSystemIncludes = false,
                ParseFunctionBodies = true,
                KeepGoing = true
                //TargetSystem = "windows",
                //TargetAbi = ""
            };
            var compilation = CppParser.ParseFiles(cppFiles, parserOptions);

            //if (compilation.Diagnostics.HasErrors)
            //{
            //    Log.Info("There are some compilation errors, but the process will keep going");
            //    foreach (var msg in compilation.Diagnostics.Messages.Where(msg => msg.Type == CppLogMessageType.Error))
            //    {
            //        Log.Info("Compilation error: " + msg.ToString());
            //    }
            //}
            return compilation;
        }

        List<CppFunction> TraverseFuncBodyCallExpr(List<CppFunction> functions)
        {
            HashSet<string> visitedFunc = functions.Select(f => f.USR).ToHashSet();
            int i = 0;
            while (i < functions.Count)
            {
                if (functions[i].Body == null)
                    continue;
                foreach (var func in functions[i].Body.CallExpressions)
                {
                    if (visitedFunc.Contains(func.USR) == false)
                    {
                        visitedFunc.Add(func.USR);
                        if (func.Body != null)
                            functions.Add(func);
                    }
                }
                i++;
            }
            return functions;
        }

        List<CppFunction> GetCppFunctions(ICppDeclarationContainer container)
        {
            List<CppFunction> functions = new List<CppFunction>();
            if (container is ICppGlobalDeclarationContainer global_container)
            {
                foreach (var ns in global_container.Namespaces)
                    functions.AddRange(GetCppFunctions(ns));
            }
            {
                foreach (var cls in container.Classes)
                {
                    foreach (var ctor in cls.Constructors)
                        if (ctor.Body != null)
                            functions.Add(ctor);
                    functions.AddRange(GetCppFunctions(cls));
                }

                foreach (var func in container.Functions)
                {
                    if (func.Body != null)
                        functions.Add(func);
                }
            }
            return functions;
        }

        void InserteLog(ModuleInfo module, List<CppFunction> functions)
        {
            Dictionary<string, List<CppFunction>> FunctionDict = new Dictionary<string, List<CppFunction>>();
            var extraIncludes = InsertLogConfig.Includes.Select(v => $"#include \"{v}\"");
            Console.WriteLine("module {0}", module.name);

            foreach (var func in functions)
            {
                string filepath = func.Body.Span.Start.File;
                if (!string.IsNullOrEmpty(filepath))
                {
                    if (!FunctionDict.ContainsKey(filepath))
                    {
                        FunctionDict[filepath] = new();
                    }
                    FunctionDict[filepath].Add(func);
                }
            }

            foreach (var (filepath, func_list) in FunctionDict)
            {
                if (!filepath.StartsWith(module.source_dir))
                    continue;
                //var lines = File.ReadAllLines(filepath).ToList();
                var encoding = FileUtils.GetFileEncoding(filepath);
                var cppText = File.ReadAllText(filepath, encoding);
                var lines = cppText.Split(Environment.NewLine).ToList();

                func_list.Sort((l, r) =>
                {
                    if (l.Body.Span.Start.Line < r.Body.Span.Start.Line) return -1;
                    if (l.Body.Span.Start.Line > r.Body.Span.Start.Line) return 1;
                    if (l.Body.Span.Start.Column < r.Body.Span.Start.Column) return -1;
                    if (l.Body.Span.Start.Column > r.Body.Span.Start.Column) return 1;
                    return 0;
                });
                bool needUpdating = false;
                //string relativePath = Path.GetRelativePath(inputPrefix, filepath);
                for (int i = func_list.Count - 1; i >= 0; i--)
                {
                    var func = func_list[i];
                    int lineCount = func.Body.Span.End.Line - func.Body.Span.Start.Line;

                    int lineNumber = func.Body.Span.Start.Line;
                    bool is_inserted = lines[lineNumber].Contains(INSERT_BEGING_COMMENT);

                    bool skipFunc = is_inserted;
                    skipFunc |= lineCount < InsertLogConfig.MinLineCount;
                    skipFunc |= InsertLogConfig.SkipConstructor && func.Flags.HasFlag(CppFunctionFlags.Constructor);
                    skipFunc |= InsertLogConfig.SkipDestructor && func.Flags.HasFlag(CppFunctionFlags.Destructor);
                    skipFunc |= InsertLogConfig.SkipInline && func.Flags.HasFlag(CppFunctionFlags.Inline);

                    if (!skipFunc)
                    {
                        int indent = func.Span.Start.Column - 1;
                        var indentStr = StringUtil.ReplicateString(" ", indent + 4);

                        string moduleName = module.name;
                        string className = (func.Parent is CppClass parentCls) ? parentCls.Name : "";
                        string funcName = func.Name;

                        lines.Insert(lineNumber++, INSERT_BEGING_COMMENT);
                        foreach (var stat in InsertLogConfig.Statements)
                        {
                            string statement = stat;
                            statement = statement.Replace(MODULE_NAME_TAG, moduleName);
                            statement = statement.Replace(CLASS_NAME_TAG, className);
                            statement = statement.Replace(FUNCTION_NAME_TAG, funcName);
                            lines.Insert(lineNumber++, indentStr + statement);
                        }
                        lines.Insert(lineNumber++, INSERT_END_COMMENT);
                        needUpdating = true;
                    }
                }

                //string outputFile = Path.Combine(outputPrefix, relativePath);
                if (needUpdating)
                {
                    bool is_inserted = lines[0].Contains(INSERT_BEGING_COMMENT);
                    if (!is_inserted && extraIncludes.Count() > 0)
                    {
                        lines.Insert(0, INSERT_BEGING_COMMENT);
                        lines.Insert(1, INSERT_END_COMMENT);
                        lines.InsertRange(1, extraIncludes);
                    }
                    //File.WriteAllLines(filepath, lines);
                    File.WriteAllText(filepath, string.Join(Environment.NewLine, lines), encoding);
                }
            }
        }

        void RemoveLog(ModuleInfo module)
        {
            var cppFiles = module.sources.Where(v => v.EndsWith(".h") || v.EndsWith(".hpp") || v.EndsWith(".cpp") || v.EndsWith(".c"));
            foreach (string filepath in cppFiles)
            {
                //var lines = File.ReadAllLines(filepath);
                var encoding = FileUtils.GetFileEncoding(filepath);
                var cppText = File.ReadAllText(filepath, encoding);
                var lines = cppText.Split(Environment.NewLine);
                bool is_inserted = lines.Any(v => v == INSERT_BEGING_COMMENT);
                if (is_inserted)
                {
                    var newLines = new List<string>();
                    bool findTag = false;
                    foreach (string line in lines)
                    {
                        if (line.Contains(INSERT_BEGING_COMMENT))
                            findTag = true;

                        if (findTag == false)
                            newLines.Add(line);

                        if (line.Contains(INSERT_END_COMMENT))
                            findTag = false;
                    }

                    //File.WriteAllLines(filepath, newLines);
                    File.WriteAllText(filepath, string.Join(Environment.NewLine, newLines), encoding);
                }
            }
        }

        public void Run()
        {
            if (CleanModel)
            {
                foreach (var module in ModuleInfoList)
                {
                    Log.Info($"clean {module.name}");
                    RemoveLog(module);
                }
            }
            else
            {
                foreach (var module in ModuleInfoList)
                {
                    Log.Info($"insert {module.name}");
                    var compilation = Parse(module);
                    var functions = GetCppFunctions(compilation);
                    functions = InsertLogConfig.FilteFiles(functions);
                    functions = InsertLogConfig.FilteFunctionNames(functions);
                    if (InsertLogConfig.RecurseCallExpr)
                        functions = TraverseFuncBodyCallExpr(functions);
                    functions = InsertLogConfig.FilteFiles(functions);
                    InserteLog(module, functions);
                }
            }
        }
    }
}