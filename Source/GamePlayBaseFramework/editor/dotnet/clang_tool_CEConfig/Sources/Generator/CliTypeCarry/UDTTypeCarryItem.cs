using Clangen.Model;
using CppAst.General;
using System;

namespace Clangen
{
    public class UDTTypeCarrayItem : CppAst.General.TypeCarryItem
    {
        public UDTTypeCarrayItem()
        {
            mNativeTypeName = "udt";
        }

        bool QueryDropInfoFromType(CppAst.General.TypeContentOffer typeContent, out string cliClassName)
        {
            string cookClassName = typeContent.LinkListTargetType.FullCookName;
            cookClassName = Clangen.NamespaceTool.CppNamespaceToCookNamespace("Cli" + cookClassName);

            //Check record
            {
                ClassModel classmodel;
                CliGenerator.mCollectNormalClassNodeMap.TryGetValue(cookClassName, out classmodel);
                if (classmodel != null)
                {
                    cliClassName = classmodel.CliClassName;
                    return true;
                }
            }
            //Check engine cli class names
            {
                string cliEngineClassName;
                CliGenerator.mCollectEngineCliClassNameMap.TryGetValue(cookClassName, out cliEngineClassName);
                if (cliEngineClassName != null)
                {
                    cliClassName = cliEngineClassName;
                    return true;
                }
            }

            //Check template instance
            {
                cookClassName = CliGenerator.UsedAliasNameMap.GetFinalyAliasName(cookClassName);
                ClassModel classmodel;
                CliGenerator.mCollectNormalClassNodeMap.TryGetValue(cookClassName, out classmodel);
                if (classmodel != null)
                {
                    cliClassName = classmodel.CliClassName;
                    return true;
                }
            }

            ////Check Stl Container
            //{
            //    General.StlContainerDrop stlDrop = Exporter.Instance.TryGetStlContainerDrop(cookClassName);
            //    if (stlDrop != null)
            //    {
            //        cliClassName = (stlDrop as StlContainerDrop).CliClassName;
            //        return true;
            //    }
            //}
            cliClassName = "";
            return false;
        }

        public override string ToTypeName(TypeConverterType convType, CppAst.General.TypeContentOffer typeContent)
        {
            string cliClassName;
            bool querySuc = QueryDropInfoFromType(typeContent, out cliClassName);
            switch (convType)
            {
                case TypeConverterType.BridgeCliInput:
                case TypeConverterType.BridgeCliReturn:
                    return querySuc ? string.Format("{0}^", cliClassName) : "UnknowKeeper^";
            }
            throw new NotImplementedException();
        }
        public override string DoCallCovert(CallConverterType callConvType, string callExpr, CppAst.General.TypeContentOffer typeContent)
        {
            string cliClassName;
            bool querySuc = QueryDropInfoFromType(typeContent, out cliClassName);
            if (querySuc)
            {
                string mangedClassName = cliClassName;
                string cppClassName = typeContent.FullCppName;
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    if (typeContent.LinkListTargetType.IsComponentHandle)
                    {
                        var comp = typeContent.LinkListTargetType as ComponentHandleContentOffer;
                        if (comp.IsComponentWriter)
                        {
                            return string.Format("static_cast<{0}*>(this->_native)->GetGameWorld()->GetComponent<{1}>({2}).Write()", comp.SystemName, comp.ComponentName, callExpr);
                        }
                        else
                        {
                            return string.Format("static_cast<{0}*>(this->_native)->GetGameWorld()->GetComponent<{1}>({2}).Read()", comp.SystemName, comp.ComponentName, callExpr);
                        }
                    }
                    else if (typeContent.IsValueUDT)
                    {
                        return string.Format("*(({1}*)({0}))", callExpr, cppClassName);
                    }

                    else
                    {
                        return string.Format("({1})({0})", callExpr, cppClassName);
                    }

                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    if (typeContent.IsValueUDT && !typeContent.IsPointer)
                    {
                        return string.Format("gcnew {1}(new {3}(({2}{0})) , true)", callExpr, mangedClassName, "", typeContent.FullCppName);
                    }
                    else
                    {
                        return string.Format("({1}^)({2}{0})", callExpr, mangedClassName, typeContent.IsValueUDT ? "&" : "");
                    }
                }
            }
            else
            {
                //UnknowKeeper
                string cppClassName = CppAst.General.NativeHelper.RemoveStartConstPrefixFromName(typeContent.FullCppName);
                if (callConvType == CallConverterType.Bridge2Native)
                {
                    if (typeContent.IsValueUDT)
                    {
                        return string.Format("*(UnknowKeeper::get_native_with_type_for_pointer<{1}*>({0}))", callExpr, cppClassName);
                    }
                    else
                    {
                        if (typeContent.IsReference)
                        {
                            return string.Format("(UnknowKeeper::get_native_with_type_for_reference<{1}>({0}))", callExpr, cppClassName);
                        }
                        else
                        {
                            return string.Format("(UnknowKeeper::get_native_with_type_for_pointer<{1}>({0}))", callExpr, cppClassName);
                        }

                    }

                }
                else if (callConvType == CallConverterType.Native2Bridge)
                {
                    return string.Format("UnknownKeeperCast({0})", callExpr);
                }
            }

            return callExpr;
        }

        public override bool IsValidForExport(CppAst.General.TypeContentOffer typeContent)
        {
            string cliClassName;
            return QueryDropInfoFromType(typeContent, out cliClassName);
        }
    }
}
