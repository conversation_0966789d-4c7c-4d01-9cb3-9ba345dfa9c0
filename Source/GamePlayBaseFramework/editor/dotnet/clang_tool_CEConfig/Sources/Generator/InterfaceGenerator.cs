using Clangen.Model;
using DotLiquid;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Clangen
{
    class InterfaceGenerator
    {
        private Template headerTemplate;
        private Template bodyTemplate;
        private CrossEngineOption CEConfig;
        private HashSet<string> generatedFiles = new HashSet<string>();

        public InterfaceGenerator(CrossEngineOption config)
        {
            headerTemplate = Template.Parse("{% include 'Interface/interface.header' %}");
            bodyTemplate = Template.Parse("{% include 'Interface/interface.body' %}");
            CEConfig = config;
        }

        public string GenerateRenderHeaderContent(IList<ClassModel> systems, ModuleInfo module)
        {
            string header = systems[0].SourceFile;
            header = Path.GetRelativePath(module.source_dir, header);

            return headerTemplate.Render(Hash.FromAnonymousObject(new
            {
                includes = new string[] { header },
                systems = systems
            }));
        }

        public string GenerateRenderBodyContent(IList<ClassModel> systems, string headerPath)
        {
            return bodyTemplate.Render(Hash.FromAnonymousObject(new
            {
                includes = new string[] { systems[0].SourceFile, headerPath },
                systems = systems
            }));
        }

        public static string GetInterfaceHeaderPath(string filePath, ModuleInfo module, CrossEngineOption CEConfig, bool? isCoreModule = null)
        {
            string outputDir;
            bool isCore = module.is_core;
            var baseName = Path.GetFileNameWithoutExtension(filePath);
            var dirName = Path.GetDirectoryName(filePath);
            if (isCore)
            {
                var relativeDir = Path.GetRelativePath(CEConfig.CrossEngineSourceRoot, dirName);
                //outputDir = CEConfig.GeneratedCodeDir + "/Source/" + relativeDir;
                outputDir = CEConfig.GeneratedCodeDir + "/" + relativeDir;
            }
            else
            {
                var relativeDir = Path.GetRelativePath(module.source_dir, dirName);
                //outputDir = CEConfig.GeneratedCodeDir + $"/BuildingBlocks/{module.name}/GeneratedCode/" + relativeDir;
                outputDir = $"{module.source_dir}/GeneratedCode/" + relativeDir;
            }
            return Path.GetFullPath($"{outputDir}/{baseName}.interface.h").Replace('\\', '/');
        }

        public HashSet<string> Generate(AstContent astContent)
        {
            generatedFiles.Clear();
            var groupAst = astContent.GroupByModule(CEConfig.ModuleNeedToBeGenerated);
            foreach (var module in CEConfig.ModuleNeedToBeGenerated)
            {
                if (groupAst.TryGetValue(module.name, out var moduleAst))
                {
                    foreach (var (filePath, ast) in moduleAst.GroupByFile())
                    {
                        var classes = ast.Classes.Where(c => c.IsSystem).ToList();

                        if (classes.Count > 0)
                        {
                            var headerPath = GetInterfaceHeaderPath(filePath, module, CEConfig, module.is_core);
                            var headerContent = GenerateRenderHeaderContent(classes, module);
                            FileUtils.WriteTextAfterCompare(headerPath, headerContent);
                            generatedFiles.Add(headerPath);

                            var bodyPath = Path.ChangeExtension(headerPath, ".cpp");
                            var bodyContent = GenerateRenderBodyContent(classes, headerPath);
                            FileUtils.WriteTextAfterCompare(bodyPath, bodyContent);
                            generatedFiles.Add(bodyPath);

                        }
                    }
                }
            }
            return generatedFiles;
        }
    }
}