using ClangSharp.Interop;
using System.Collections.Generic;

namespace CppAst.General
{
    public enum StlContainerType
    {
        Unknown = 0,
        Vector = 1,
        List = 2,
        Set = 3,
        Map = 4,
        MultiMap = 5,
        UnorderedMap = 6,
        Pair = 7,
        SharedPtr = 8,
        Deque,
        MultiSet,
    }

    public static class NativeHelper
    {
        private static CppType RemoveQualified(CppType type)
        {
            if (type.TypeKind == CppTypeKind.Qualified)
            {
                var newtype = (type as CppQualifiedType).ElementType;
                return RemoveQualified(newtype);
            }
            else
            {
                return type;
            }
        }

        private static bool IsConstQualified(CppType type)
        {
            if (type.TypeKind == CppTypeKind.Qualified)
            {
                var qualtype = type as CppQualifiedType;
                return qualtype.Qualifier == CppTypeQualifier.Const;
            }

            return false;
        }

        private static bool IsStdString(CppClass cls)
        {
            if (cls.TemplateKind == CppTemplateKind.TemplateSpecializedClass)
            {
                if (cls.FullName == "std::__1::basic_string" || cls.FullName == "std::string")
                {
                    return true;
                }
            }

            return false;
        }

        public static TypeContentOffer NativeType2ContentOffer(CppAst.CppCompilation builder, CppType orgType)
        {
            return DeduceTemplateType(builder, orgType, null);
        }

        public static CppAst.General.TypeContentOffer DeduceTemplateType(CppAst.CppCompilation builder, CppType orgType, Dictionary<string, CppTemplateArgument> templateArgTypeMap)
        {
            bool is_const_qualified = IsConstQualified(orgType);
            CppType not_qualified_type = RemoveQualified(orgType);

            TypeContentOffer retOffer = null;
            switch (not_qualified_type.TypeKind)
            {
                case CppTypeKind.Primitive:
                    {
                        var primtype = not_qualified_type as CppPrimitiveType;
                        retOffer = new BuildInTypeContentOffer(primtype.ToString(), is_const_qualified, primtype.Kind == CppPrimitiveKind.Void);
                    }
                    break;
                case CppTypeKind.Typedef:
                    {
                        var typedef = not_qualified_type as CppTypedef;
                        string fullCppName = typedef.FullName;

                        //Just deduce canonical type here~~
                        var aliasType = DeduceTemplateType(builder, typedef.GetCanonicalType(), templateArgTypeMap);
                        retOffer = new TypedefTypeContentOffer(aliasType, fullCppName, is_const_qualified);
                    }
                    break;
                case CppTypeKind.StructOrClass:
                    {
                        var cls = not_qualified_type as CppClass;
                        string className = cls.FullName;

                        if (IsStdString(cls))
                        {
                            retOffer = new StringTypeContentOffer("std::string", StringTypeKind.StdString, is_const_qualified, 0);
                        }
                        else
                        {
                            retOffer = new RecordTypeContentOffer(Clangen.NamespaceTool.GetOnlyLeafName(className), className, is_const_qualified);
                        }
                    }
                    break;
                case CppTypeKind.Enum:
                    {
                        var e = not_qualified_type as CppEnum;
                        retOffer = new EnumTypeContentOffer(e.FullName);

                    }
                    break;
                case CppTypeKind.TemplateArgumentType:
                    {
                        //ToDo: add Template argument fix here
                    }
                    break;
                case CppTypeKind.TemplateParameterType:
                    {
                        //ToDo: add Template fix here
                        // var tmpl = not_qualified_type as CppTemplateParameterType;
                        // string tempArgName = TypeHandleTool.RemoveStartConstPrefixFromName(orgType.Spelling.CString);
                        // if (templateArgTypeMap != null)
                        // {
                        // 	if (templateArgTypeMap.ContainsKey(tempArgName))
                        // 	{
                        // 		retOffer = DeduceTemplateType(parentAST, templateArgTypeMap[tempArgName].InstanceType, templateArgTypeMap);
                        // 		break;
                        // 	}
                        // 	else
                        // 	{
                        // 		retOffer = new UnknownTypeContentOffer("Template Arg: " + orgType.Spelling.CString);
                        // 		break;
                        // 	}
                        // }
                        // else
                        // {
                        // 	retOffer = new UnknownTypeContentOffer("Template Arg: " + tempArgName);
                        // 	break;
                        // }
                    }
                    break;
                case CppTypeKind.Array:
                    {
                        var arr_type = not_qualified_type as CppArrayType;
                        var elem_type = arr_type.ElementType;
                        is_const_qualified = IsConstQualified(elem_type);
                        elem_type = RemoveQualified(elem_type);
                        if (elem_type.TypeKind == CppTypeKind.Primitive && (elem_type as CppPrimitiveType).Kind == CppPrimitiveKind.Char)
                        {
                            //char [];
                            retOffer = new StringTypeContentOffer("char[]", StringTypeKind.CharArray, is_const_qualified, (int)arr_type.Size);
                        }
                        else
                        {
                            retOffer = DeduceTemplateType(builder, arr_type.ElementType, templateArgTypeMap);
                            retOffer.IsBuildInArray = true;
                        }
                    }
                    break;
                case CppTypeKind.Function:
                    {

                    }
                    break;
                case CppTypeKind.Qualified:
                    {

                    }
                    break;
                case CppTypeKind.Pointer:
                    {
                        var pt_type = not_qualified_type as CppPointerType;
                        var elem_type = pt_type.ElementType;
                        is_const_qualified = IsConstQualified(elem_type);
                        elem_type = RemoveQualified(elem_type);

                        //raw string
                        if (elem_type.TypeKind == CppTypeKind.Primitive && (elem_type as CppPrimitiveType).Kind == CppPrimitiveKind.Char)
                        {
                            retOffer = new StringTypeContentOffer(pt_type.ToString(), StringTypeKind.RawString, is_const_qualified, 0);
                        }
                        else
                        {
                            retOffer = new PointerOrReferenceTypeContentOffer(DeduceTemplateType(builder, pt_type.ElementType, templateArgTypeMap),
                                "*",
                                TypeContentKind.Pointer,
                                is_const_qualified);
                        }
                    }
                    break;
                case CppTypeKind.Reference:
                    {
                        var ref_type = not_qualified_type as CppReferenceType;
                        retOffer = new PointerOrReferenceTypeContentOffer(DeduceTemplateType(builder, ref_type.ElementType, templateArgTypeMap),
                            "&",
                            TypeContentKind.LReference,
                            is_const_qualified);
                    }
                    break;
                case CppTypeKind.Unexposed:
                    {
                        retOffer = new UnknownTypeContentOffer(orgType.ToString());
                    }
                    break;

            }
            return retOffer;
        }

        public static StlContainerType GetStlContainerTypeFromCookName(string cookName)
        {
            ////int findPos = cookName.IndexOf('<');
            ////if (findPos == -1)
            ////{
            ////    return StlContainerType.Unknown;
            ////}

            string onlyName = cookName.Trim();
            switch (onlyName)
            {
                case "std.vector":
                case "std.__1.vector":
                    return StlContainerType.Vector;
                case "std.list":
                case "std.__1.list":
                    return StlContainerType.List;
                case "std.set":
                case "std.__1.set":
                    return StlContainerType.Set;
                case "std.map":
                case "std.__1.map":
                    return StlContainerType.Map;
                case "std.__1.multimap":
                case "std.multimap":
                    return StlContainerType.MultiMap;
                case "std.unordered_map":
                case "std.__1.unordered_map":
                    return StlContainerType.UnorderedMap;
                case "std.pair":
                case "std.__1.pair":
                    return StlContainerType.Pair;
                case "std.shared_ptr":
                case "std.__1.shared_ptr":
                    return StlContainerType.SharedPtr;
            }

            return StlContainerType.Unknown;
        }

        public static string RemoveStartConstPrefixFromName(string name)
        {
            string const_prefix = "const ";
            if (name.StartsWith(const_prefix))
            {
                return name.Substring(const_prefix.Length);
            }
            return name;
        }

        public static string DiagnosticSeverityToString(CXDiagnosticSeverity severity)
        {
            switch (severity)
            {
                case CXDiagnosticSeverity.CXDiagnostic_Error:
                    return "Error";
                case CXDiagnosticSeverity.CXDiagnostic_Fatal:
                    return "Fatal";
                case CXDiagnosticSeverity.CXDiagnostic_Ignored:
                    return "Ignore";
                case CXDiagnosticSeverity.CXDiagnostic_Note:
                    return "Note";
                case CXDiagnosticSeverity.CXDiagnostic_Warning:
                    return "Warning";
                default:
                    return "Unknown";
            }
        }

        public static bool IsFatal(CXDiagnosticSeverity severity)
        {
            return CXDiagnosticSeverity.CXDiagnostic_Fatal == severity ||
                CXDiagnosticSeverity.CXDiagnostic_Error == severity;
        }
    }
}