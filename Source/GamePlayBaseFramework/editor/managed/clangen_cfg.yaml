ClangenRootPath: "C:/CrossEngine/Source/GamePlayBaseFramework/editor/dotnet/clang_tool_CEConfig"

SystemIncludes:
  # required on linux, should be turn off on windows
   #- "/usr/local/include"
   #- "/usr/include"
  # - "/usr/include/c++/4.8.5"
   #- "./linux_include_8_3_0/x86_64-pc-linux-gnu"
   #- "./linux_include_8_3_0"
   #- "./linux_include" 
   #- "./user_include" 
  # required on windows, should be turn off on linux
  #- "D:/workspace/cross_engine_server/framework-cpp/tools/dotnet/clang_tool_CEConfig/msvc_include/msvc/include"
  #- "D:/workspace/cross_engine_server/framework-cpp/tools/dotnet/clang_tool_CEConfig/msvc_include/msvc/atlmfc/include"
  #- "D:/workspace/cross_engine_server/framework-cpp/tools/dotnet/clang_tool_CEConfig/msvc_include/win10sdk/ucrt"
  #- "D:/workspace/cross_engine_server/framework-cpp/tools/dotnet/clang_tool_CEConfig/msvc_include/win10sdk/shared"
  # clang libc++ here
  - "C:/CrossEngine/Source/GamePlayBaseFramework/editor/dotnet/clang_tool_CEConfig/clang_include"
  - "C:/CrossEngine/Source/GamePlayBaseFramework/editor/dotnet/clang_tool_CEConfig/clang_win_include"
  #- "D:/workspace/cross_engine_server/framework-cpp/tools/dotnet/clang_tool_CEConfig/msvc_include/msvc/include"
ExportPassArray:
  - Type: "CliPlus"
    ClangProjectSettings:
      RootSourcePath: "../../"
      AdditionalIncludeDirectories:
        - Path: "base"
          Relative: true
        - Path: "logic"
          Relative: true
        - Path: "meta"
          Relative: true
        - Path: "third_party"
          Relative: true
        - Path: "editor/managed"
          Relative: true
        - Path: "editor/native"
          Relative: true
        - Path: "third_party/IMGUINodeEditor"
          Relative: true
        - Path: "editor/native/node_editor_bridge"
          Relative: true
        - Path: "third_party/imgui-ws/third-party/imgui"
          Relative: true
        - Path: "../Runtime/interface"
          Relative: true  
        - Path: ".."
          Relative: true  
      ASTGenerateIncludes:
        - "gbf_export.h"
      CompileExtraArgs:
        - "-x"
        - "c++"
        # must be not less than c++14 on windows
        # can be set c++11 safely on linux
        - "-std=c++20"
        - "-stdlib=libc++"
        - "-DCLANG_GENERATOR=1"
        #- "-D_LIBCPP_CXX03_LANG=1"
        #- "-D__clang__=1"
        #- "-D__cplusplus=202004L"
        #- "-D_LIBCPP_STD_VER=20L"
        - "-DGBF_ENABLE_CPP20=1"
        #- "-DWIN32"
    Path: "editor/managed"
    ServiceProxyPath: ""
    DetailConfigFile: "C:/CrossEngine/Source/GamePlayBaseFramework/editor/managed/clangen_detail_export_config.yaml"
    OutFileName: "gbf_managed_auto"
    Relative: true
  # message
  

