//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// DepthStencilStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool DepthStencilStateDesc::EnableDepth::get()
 {
	return (static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableDepth;
 }
 void DepthStencilStateDesc::EnableDepth::set(bool value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableDepth = value;
 }

bool DepthStencilStateDesc::EnableDepthWrite::get()
 {
	return (static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableDepthWrite;
 }
 void DepthStencilStateDesc::EnableDepthWrite::set(bool value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableDepthWrite = value;
 }

Clicross::ComparisonOp DepthStencilStateDesc::DepthCompareOp::get()
 {
	return (Clicross::ComparisonOp)((int)(static_cast<cross::DepthStencilStateDesc*>(this->_native))->DepthCompareOp);
 }
 void DepthStencilStateDesc::DepthCompareOp::set(Clicross::ComparisonOp value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->DepthCompareOp = static_cast<cross::ComparisonOp>(value);
 }

bool DepthStencilStateDesc::EnableStencil::get()
 {
	return (static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableStencil;
 }
 void DepthStencilStateDesc::EnableStencil::set(bool value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->EnableStencil = value;
 }

unsigned char DepthStencilStateDesc::StencilReadMask::get()
 {
	return (static_cast<cross::DepthStencilStateDesc*>(this->_native))->StencilReadMask;
 }
 void DepthStencilStateDesc::StencilReadMask::set(unsigned char value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->StencilReadMask = value;
 }

unsigned char DepthStencilStateDesc::StencilWriteMask::get()
 {
	return (static_cast<cross::DepthStencilStateDesc*>(this->_native))->StencilWriteMask;
 }
 void DepthStencilStateDesc::StencilWriteMask::set(unsigned char value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->StencilWriteMask = value;
 }

Clicross::StencilOperation^ DepthStencilStateDesc::FrontFace::get()
 {
	return gcnew Clicross::StencilOperation(new cross::StencilOperation(((static_cast<cross::DepthStencilStateDesc*>(this->_native))->FrontFace)) , true);
 }
 void DepthStencilStateDesc::FrontFace::set(Clicross::StencilOperation^ value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->FrontFace = value;
 }

Clicross::StencilOperation^ DepthStencilStateDesc::BackFace::get()
 {
	return gcnew Clicross::StencilOperation(new cross::StencilOperation(((static_cast<cross::DepthStencilStateDesc*>(this->_native))->BackFace)) , true);
 }
 void DepthStencilStateDesc::BackFace::set(Clicross::StencilOperation^ value )
 {
	(static_cast<cross::DepthStencilStateDesc*>(this->_native))->BackFace = value;
 }


//constructor export here
DepthStencilStateDesc::DepthStencilStateDesc(): DepthStencilStateDesc(new cross::DepthStencilStateDesc(), true) {}


DepthStencilStateDesc::DepthStencilStateDesc(const cross::DepthStencilStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::DepthStencilStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DepthStencilStateDesc::operator DepthStencilStateDesc^ (const cross::DepthStencilStateDesc* t)
{
    if(t)
    {
        return gcnew DepthStencilStateDesc(const_cast<cross::DepthStencilStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// StencilOperation export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::StencilOp StencilOperation::StencilFailOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::StencilOperation*>(this->_native))->StencilFailOp);
 }
 void StencilOperation::StencilFailOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::StencilOperation*>(this->_native))->StencilFailOp = static_cast<cross::StencilOp>(value);
 }

Clicross::StencilOp StencilOperation::StencilDepthFailOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::StencilOperation*>(this->_native))->StencilDepthFailOp);
 }
 void StencilOperation::StencilDepthFailOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::StencilOperation*>(this->_native))->StencilDepthFailOp = static_cast<cross::StencilOp>(value);
 }

Clicross::StencilOp StencilOperation::StencilPassOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::StencilOperation*>(this->_native))->StencilPassOp);
 }
 void StencilOperation::StencilPassOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::StencilOperation*>(this->_native))->StencilPassOp = static_cast<cross::StencilOp>(value);
 }

Clicross::ComparisonOp StencilOperation::StencilCompareOp::get()
 {
	return (Clicross::ComparisonOp)((int)(static_cast<cross::StencilOperation*>(this->_native))->StencilCompareOp);
 }
 void StencilOperation::StencilCompareOp::set(Clicross::ComparisonOp value )
 {
	(static_cast<cross::StencilOperation*>(this->_native))->StencilCompareOp = static_cast<cross::ComparisonOp>(value);
 }


//constructor export here
StencilOperation::StencilOperation(): StencilOperation(new cross::StencilOperation(), true) {}


StencilOperation::StencilOperation(const cross::StencilOperation * obj, bool created_by_clr): 
    _native(const_cast<cross::StencilOperation *>(obj))
	, _created_by_clr(created_by_clr)
{
}

StencilOperation::operator StencilOperation^ (const cross::StencilOperation* t)
{
    if(t)
    {
        return gcnew StencilOperation(const_cast<cross::StencilOperation*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RasterizationStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::FillMode RasterizationStateDesc::RasterizerFillMode::get()
 {
	return (Clicross::FillMode)((int)(static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterizerFillMode);
 }
 void RasterizationStateDesc::RasterizerFillMode::set(Clicross::FillMode value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterizerFillMode = static_cast<cross::FillMode>(value);
 }

Clicross::CullMode RasterizationStateDesc::FaceCullMode::get()
 {
	return (Clicross::CullMode)((int)(static_cast<cross::RasterizationStateDesc*>(this->_native))->FaceCullMode);
 }
 void RasterizationStateDesc::FaceCullMode::set(Clicross::CullMode value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->FaceCullMode = static_cast<cross::CullMode>(value);
 }

Clicross::FaceOrder RasterizationStateDesc::FrontFaceOrder::get()
 {
	return (Clicross::FaceOrder)((int)(static_cast<cross::RasterizationStateDesc*>(this->_native))->FrontFaceOrder);
 }
 void RasterizationStateDesc::FrontFaceOrder::set(Clicross::FaceOrder value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->FrontFaceOrder = static_cast<cross::FaceOrder>(value);
 }

bool RasterizationStateDesc::EnableDepthClip::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableDepthClip;
 }
 void RasterizationStateDesc::EnableDepthClip::set(bool value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableDepthClip = value;
 }

bool RasterizationStateDesc::EnableAntialiasedLine::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableAntialiasedLine;
 }
 void RasterizationStateDesc::EnableAntialiasedLine::set(bool value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableAntialiasedLine = value;
 }

bool RasterizationStateDesc::EnableDepthBias::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableDepthBias;
 }
 void RasterizationStateDesc::EnableDepthBias::set(bool value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->EnableDepthBias = value;
 }

short RasterizationStateDesc::DepthBias::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->DepthBias;
 }
 void RasterizationStateDesc::DepthBias::set(short value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->DepthBias = value;
 }

float RasterizationStateDesc::SlopeScaledDepthBias::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->SlopeScaledDepthBias;
 }
 void RasterizationStateDesc::SlopeScaledDepthBias::set(float value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->SlopeScaledDepthBias = value;
 }

float RasterizationStateDesc::DepthBiasClamp::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->DepthBiasClamp;
 }
 void RasterizationStateDesc::DepthBiasClamp::set(float value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->DepthBiasClamp = value;
 }

unsigned int RasterizationStateDesc::ForcedSampleCount::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->ForcedSampleCount;
 }
 void RasterizationStateDesc::ForcedSampleCount::set(unsigned int value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->ForcedSampleCount = value;
 }

float RasterizationStateDesc::LineWidth::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->LineWidth;
 }
 void RasterizationStateDesc::LineWidth::set(float value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->LineWidth = value;
 }

Clicross::RasterizationMode RasterizationStateDesc::RasterMode::get()
 {
	return (Clicross::RasterizationMode)((int)(static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterMode);
 }
 void RasterizationStateDesc::RasterMode::set(Clicross::RasterizationMode value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterMode = static_cast<cross::RasterizationMode>(value);
 }

unsigned int RasterizationStateDesc::RasterOverestimationSize::get()
 {
	return (static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterOverestimationSize;
 }
 void RasterizationStateDesc::RasterOverestimationSize::set(unsigned int value )
 {
	(static_cast<cross::RasterizationStateDesc*>(this->_native))->RasterOverestimationSize = value;
 }


//constructor export here
RasterizationStateDesc::RasterizationStateDesc(): RasterizationStateDesc(new cross::RasterizationStateDesc(), true) {}


RasterizationStateDesc::RasterizationStateDesc(const cross::RasterizationStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::RasterizationStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RasterizationStateDesc::operator RasterizationStateDesc^ (const cross::RasterizationStateDesc* t)
{
    if(t)
    {
        return gcnew RasterizationStateDesc(const_cast<cross::RasterizationStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// DynamicStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned char DynamicStateDesc::StencilReference::get()
 {
	return (static_cast<cross::DynamicStateDesc*>(this->_native))->StencilReference;
 }
 void DynamicStateDesc::StencilReference::set(unsigned char value )
 {
	(static_cast<cross::DynamicStateDesc*>(this->_native))->StencilReference = value;
 }


//constructor export here
DynamicStateDesc::DynamicStateDesc(): DynamicStateDesc(new cross::DynamicStateDesc(), true) {}


DynamicStateDesc::DynamicStateDesc(const cross::DynamicStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::DynamicStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DynamicStateDesc::operator DynamicStateDesc^ (const cross::DynamicStateDesc* t)
{
    if(t)
    {
        return gcnew DynamicStateDesc(const_cast<cross::DynamicStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// AtmosphereLightConfig export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool AtmosphereLightConfig::AtmosphereSunLight::get()
 {
	return (static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunLight;
 }
 void AtmosphereLightConfig::AtmosphereSunLight::set(bool value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunLight = value;
 }

unsigned int AtmosphereLightConfig::AtmosphereSunLightIndex::get()
 {
	return (static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunLightIndex;
 }
 void AtmosphereLightConfig::AtmosphereSunLightIndex::set(unsigned int value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunLightIndex = value;
 }

Clicross::Float3^ AtmosphereLightConfig::AtmosphereSunDiscColorScale::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunDiscColorScale)) , true);
 }
 void AtmosphereLightConfig::AtmosphereSunDiscColorScale::set(Clicross::Float3^ value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunDiscColorScale = value;
 }

float AtmosphereLightConfig::AtmosphereSunDiscIntensityScale::get()
 {
	return (static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunDiscIntensityScale;
 }
 void AtmosphereLightConfig::AtmosphereSunDiscIntensityScale::set(float value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereSunDiscIntensityScale = value;
 }

bool AtmosphereLightConfig::AtmosphereDirectionReversed::get()
 {
	return (static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereDirectionReversed;
 }
 void AtmosphereLightConfig::AtmosphereDirectionReversed::set(bool value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->AtmosphereDirectionReversed = value;
 }

unsigned int AtmosphereLightConfig::ReversedLightRadius::get()
 {
	return (static_cast<cross::AtmosphereLightConfig*>(this->_native))->ReversedLightRadius;
 }
 void AtmosphereLightConfig::ReversedLightRadius::set(unsigned int value )
 {
	(static_cast<cross::AtmosphereLightConfig*>(this->_native))->ReversedLightRadius = value;
 }


//constructor export here
AtmosphereLightConfig::AtmosphereLightConfig(): AtmosphereLightConfig(new cross::AtmosphereLightConfig(), true) {}


AtmosphereLightConfig::AtmosphereLightConfig(const cross::AtmosphereLightConfig * obj, bool created_by_clr): 
    _native(const_cast<cross::AtmosphereLightConfig *>(obj))
	, _created_by_clr(created_by_clr)
{
}

AtmosphereLightConfig::operator AtmosphereLightConfig^ (const cross::AtmosphereLightConfig* t)
{
    if(t)
    {
        return gcnew AtmosphereLightConfig(const_cast<cross::AtmosphereLightConfig*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIRasterizationStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::FillMode NGIRasterizationStateDesc::FillMode::get()
 {
	return (Clicross::FillMode)((int)(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->FillMode);
 }
 void NGIRasterizationStateDesc::FillMode::set(Clicross::FillMode value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->FillMode = static_cast<cross::FillMode>(value);
 }

Clicross::CullMode NGIRasterizationStateDesc::CullMode::get()
 {
	return (Clicross::CullMode)((int)(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->CullMode);
 }
 void NGIRasterizationStateDesc::CullMode::set(Clicross::CullMode value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->CullMode = static_cast<cross::CullMode>(value);
 }

Clicross::FaceOrder NGIRasterizationStateDesc::FaceOrder::get()
 {
	return (Clicross::FaceOrder)((int)(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->FaceOrder);
 }
 void NGIRasterizationStateDesc::FaceOrder::set(Clicross::FaceOrder value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->FaceOrder = static_cast<cross::FaceOrder>(value);
 }

bool NGIRasterizationStateDesc::EnableDepthClip::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableDepthClip;
 }
 void NGIRasterizationStateDesc::EnableDepthClip::set(bool value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableDepthClip = value;
 }

bool NGIRasterizationStateDesc::EnableAntialiasedLine::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableAntialiasedLine;
 }
 void NGIRasterizationStateDesc::EnableAntialiasedLine::set(bool value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableAntialiasedLine = value;
 }

bool NGIRasterizationStateDesc::EnableDepthBias::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableDepthBias;
 }
 void NGIRasterizationStateDesc::EnableDepthBias::set(bool value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->EnableDepthBias = value;
 }

short NGIRasterizationStateDesc::DepthBias::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->DepthBias;
 }
 void NGIRasterizationStateDesc::DepthBias::set(short value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->DepthBias = value;
 }

float NGIRasterizationStateDesc::SlopeScaledDepthBias::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->SlopeScaledDepthBias;
 }
 void NGIRasterizationStateDesc::SlopeScaledDepthBias::set(float value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->SlopeScaledDepthBias = value;
 }

float NGIRasterizationStateDesc::DepthBiasClamp::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->DepthBiasClamp;
 }
 void NGIRasterizationStateDesc::DepthBiasClamp::set(float value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->DepthBiasClamp = value;
 }

unsigned int NGIRasterizationStateDesc::ForcedSampleCount::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->ForcedSampleCount;
 }
 void NGIRasterizationStateDesc::ForcedSampleCount::set(unsigned int value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->ForcedSampleCount = value;
 }

float NGIRasterizationStateDesc::LineWidth::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->LineWidth;
 }
 void NGIRasterizationStateDesc::LineWidth::set(float value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->LineWidth = value;
 }

Clicross::RasterizationMode NGIRasterizationStateDesc::RasterMode::get()
 {
	return (Clicross::RasterizationMode)((int)(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->RasterMode);
 }
 void NGIRasterizationStateDesc::RasterMode::set(Clicross::RasterizationMode value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->RasterMode = static_cast<cross::RasterizationMode>(value);
 }

unsigned int NGIRasterizationStateDesc::RasterOverestimationSize::get()
 {
	return (static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->RasterOverestimationSize;
 }
 void NGIRasterizationStateDesc::RasterOverestimationSize::set(unsigned int value )
 {
	(static_cast<cross::NGIRasterizationStateDesc*>(this->_native))->RasterOverestimationSize = value;
 }


//constructor export here
NGIRasterizationStateDesc::NGIRasterizationStateDesc(): NGIRasterizationStateDesc(new cross::NGIRasterizationStateDesc(), true) {}


NGIRasterizationStateDesc::NGIRasterizationStateDesc(const cross::NGIRasterizationStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::NGIRasterizationStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGIRasterizationStateDesc::operator NGIRasterizationStateDesc^ (const cross::NGIRasterizationStateDesc* t)
{
    if(t)
    {
        return gcnew NGIRasterizationStateDesc(const_cast<cross::NGIRasterizationStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIBlendStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool NGIBlendStateDesc::EnableAlphaToCoverage::get()
 {
	return (static_cast<cross::NGIBlendStateDesc*>(this->_native))->EnableAlphaToCoverage;
 }
 void NGIBlendStateDesc::EnableAlphaToCoverage::set(bool value )
 {
	(static_cast<cross::NGIBlendStateDesc*>(this->_native))->EnableAlphaToCoverage = value;
 }

bool NGIBlendStateDesc::EnableIndependentBlend::get()
 {
	return (static_cast<cross::NGIBlendStateDesc*>(this->_native))->EnableIndependentBlend;
 }
 void NGIBlendStateDesc::EnableIndependentBlend::set(bool value )
 {
	(static_cast<cross::NGIBlendStateDesc*>(this->_native))->EnableIndependentBlend = value;
 }


//constructor export here
NGIBlendStateDesc::NGIBlendStateDesc(): NGIBlendStateDesc(new cross::NGIBlendStateDesc(), true) {}


NGIBlendStateDesc::NGIBlendStateDesc(const cross::NGIBlendStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::NGIBlendStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGIBlendStateDesc::operator NGIBlendStateDesc^ (const cross::NGIBlendStateDesc* t)
{
    if(t)
    {
        return gcnew NGIBlendStateDesc(const_cast<cross::NGIBlendStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGITargetBlendStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool NGITargetBlendStateDesc::EnableBlend::get()
 {
	return (static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->EnableBlend;
 }
 void NGITargetBlendStateDesc::EnableBlend::set(bool value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->EnableBlend = value;
 }

bool NGITargetBlendStateDesc::EnableLogicOp::get()
 {
	return (static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->EnableLogicOp;
 }
 void NGITargetBlendStateDesc::EnableLogicOp::set(bool value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->EnableLogicOp = value;
 }

Clicross::BlendFactor NGITargetBlendStateDesc::SrcBlend::get()
 {
	return (Clicross::BlendFactor)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->SrcBlend);
 }
 void NGITargetBlendStateDesc::SrcBlend::set(Clicross::BlendFactor value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->SrcBlend = static_cast<cross::BlendFactor>(value);
 }

Clicross::BlendFactor NGITargetBlendStateDesc::DestBlend::get()
 {
	return (Clicross::BlendFactor)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->DestBlend);
 }
 void NGITargetBlendStateDesc::DestBlend::set(Clicross::BlendFactor value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->DestBlend = static_cast<cross::BlendFactor>(value);
 }

Clicross::BlendOp NGITargetBlendStateDesc::BlendOp::get()
 {
	return (Clicross::BlendOp)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->BlendOp);
 }
 void NGITargetBlendStateDesc::BlendOp::set(Clicross::BlendOp value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->BlendOp = static_cast<cross::BlendOp>(value);
 }

Clicross::BlendFactor NGITargetBlendStateDesc::SrcBlendAlpha::get()
 {
	return (Clicross::BlendFactor)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->SrcBlendAlpha);
 }
 void NGITargetBlendStateDesc::SrcBlendAlpha::set(Clicross::BlendFactor value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->SrcBlendAlpha = static_cast<cross::BlendFactor>(value);
 }

Clicross::BlendFactor NGITargetBlendStateDesc::DestBlendAlpha::get()
 {
	return (Clicross::BlendFactor)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->DestBlendAlpha);
 }
 void NGITargetBlendStateDesc::DestBlendAlpha::set(Clicross::BlendFactor value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->DestBlendAlpha = static_cast<cross::BlendFactor>(value);
 }

Clicross::BlendOp NGITargetBlendStateDesc::BlendOpAlpha::get()
 {
	return (Clicross::BlendOp)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->BlendOpAlpha);
 }
 void NGITargetBlendStateDesc::BlendOpAlpha::set(Clicross::BlendOp value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->BlendOpAlpha = static_cast<cross::BlendOp>(value);
 }

Clicross::LogicOp NGITargetBlendStateDesc::LogicOp::get()
 {
	return (Clicross::LogicOp)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->LogicOp);
 }
 void NGITargetBlendStateDesc::LogicOp::set(Clicross::LogicOp value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->LogicOp = static_cast<cross::LogicOp>(value);
 }

Clicross::ColorMask NGITargetBlendStateDesc::WriteMask::get()
 {
	return (Clicross::ColorMask)((int)(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->WriteMask);
 }
 void NGITargetBlendStateDesc::WriteMask::set(Clicross::ColorMask value )
 {
	(static_cast<cross::NGITargetBlendStateDesc*>(this->_native))->WriteMask = static_cast<cross::ColorMask>(value);
 }


//constructor export here
NGITargetBlendStateDesc::NGITargetBlendStateDesc(): NGITargetBlendStateDesc(new cross::NGITargetBlendStateDesc(), true) {}


NGITargetBlendStateDesc::NGITargetBlendStateDesc(const cross::NGITargetBlendStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::NGITargetBlendStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGITargetBlendStateDesc::operator NGITargetBlendStateDesc^ (const cross::NGITargetBlendStateDesc* t)
{
    if(t)
    {
        return gcnew NGITargetBlendStateDesc(const_cast<cross::NGITargetBlendStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIDepthStencilStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool NGIDepthStencilStateDesc::EnableDepth::get()
 {
	return (static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableDepth;
 }
 void NGIDepthStencilStateDesc::EnableDepth::set(bool value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableDepth = value;
 }

bool NGIDepthStencilStateDesc::EnableDepthWrite::get()
 {
	return (static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableDepthWrite;
 }
 void NGIDepthStencilStateDesc::EnableDepthWrite::set(bool value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableDepthWrite = value;
 }

Clicross::ComparisonOp NGIDepthStencilStateDesc::DepthCompareOp::get()
 {
	return (Clicross::ComparisonOp)((int)(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->DepthCompareOp);
 }
 void NGIDepthStencilStateDesc::DepthCompareOp::set(Clicross::ComparisonOp value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->DepthCompareOp = static_cast<cross::ComparisonOp>(value);
 }

bool NGIDepthStencilStateDesc::EnableStencil::get()
 {
	return (static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableStencil;
 }
 void NGIDepthStencilStateDesc::EnableStencil::set(bool value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->EnableStencil = value;
 }

unsigned char NGIDepthStencilStateDesc::StencilReadMask::get()
 {
	return (static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->StencilReadMask;
 }
 void NGIDepthStencilStateDesc::StencilReadMask::set(unsigned char value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->StencilReadMask = value;
 }

unsigned char NGIDepthStencilStateDesc::StencilWriteMask::get()
 {
	return (static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->StencilWriteMask;
 }
 void NGIDepthStencilStateDesc::StencilWriteMask::set(unsigned char value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->StencilWriteMask = value;
 }

Clicross::NGIStencilOperation^ NGIDepthStencilStateDesc::FrontFace::get()
 {
	return gcnew Clicross::NGIStencilOperation(new cross::NGIStencilOperation(((static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->FrontFace)) , true);
 }
 void NGIDepthStencilStateDesc::FrontFace::set(Clicross::NGIStencilOperation^ value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->FrontFace = value;
 }

Clicross::NGIStencilOperation^ NGIDepthStencilStateDesc::BackFace::get()
 {
	return gcnew Clicross::NGIStencilOperation(new cross::NGIStencilOperation(((static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->BackFace)) , true);
 }
 void NGIDepthStencilStateDesc::BackFace::set(Clicross::NGIStencilOperation^ value )
 {
	(static_cast<cross::NGIDepthStencilStateDesc*>(this->_native))->BackFace = value;
 }


//constructor export here
NGIDepthStencilStateDesc::NGIDepthStencilStateDesc(): NGIDepthStencilStateDesc(new cross::NGIDepthStencilStateDesc(), true) {}


NGIDepthStencilStateDesc::NGIDepthStencilStateDesc(const cross::NGIDepthStencilStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::NGIDepthStencilStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGIDepthStencilStateDesc::operator NGIDepthStencilStateDesc^ (const cross::NGIDepthStencilStateDesc* t)
{
    if(t)
    {
        return gcnew NGIDepthStencilStateDesc(const_cast<cross::NGIDepthStencilStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIStencilOperation export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::StencilOp NGIStencilOperation::StencilFailOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilFailOp);
 }
 void NGIStencilOperation::StencilFailOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilFailOp = static_cast<cross::StencilOp>(value);
 }

Clicross::StencilOp NGIStencilOperation::StencilDepthFailOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilDepthFailOp);
 }
 void NGIStencilOperation::StencilDepthFailOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilDepthFailOp = static_cast<cross::StencilOp>(value);
 }

Clicross::StencilOp NGIStencilOperation::StencilPassOp::get()
 {
	return (Clicross::StencilOp)((int)(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilPassOp);
 }
 void NGIStencilOperation::StencilPassOp::set(Clicross::StencilOp value )
 {
	(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilPassOp = static_cast<cross::StencilOp>(value);
 }

Clicross::ComparisonOp NGIStencilOperation::StencilCompareOp::get()
 {
	return (Clicross::ComparisonOp)((int)(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilCompareOp);
 }
 void NGIStencilOperation::StencilCompareOp::set(Clicross::ComparisonOp value )
 {
	(static_cast<cross::NGIStencilOperation*>(this->_native))->StencilCompareOp = static_cast<cross::ComparisonOp>(value);
 }


//constructor export here
NGIStencilOperation::NGIStencilOperation(): NGIStencilOperation(new cross::NGIStencilOperation(), true) {}


NGIStencilOperation::NGIStencilOperation(const cross::NGIStencilOperation * obj, bool created_by_clr): 
    _native(const_cast<cross::NGIStencilOperation *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGIStencilOperation::operator NGIStencilOperation^ (const cross::NGIStencilOperation* t)
{
    if(t)
    {
        return gcnew NGIStencilOperation(const_cast<cross::NGIStencilOperation*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIBlendStateDescForEditor export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::NGIBlendStateDescForEditor::TargetBlendStateVector export start
	#define STLDECL_MANAGEDTYPE Clicross::NGITargetBlendStateDesc^
	#define STLDECL_NATIVETYPE cross::NGITargetBlendStateDesc
	CPP_DECLARE_STLVECTOR(NGIBlendStateDescForEditor::, TargetBlendStateVectorCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
NGIBlendStateDescForEditor::TargetBlendStateVectorCliType^ NGIBlendStateDescForEditor::TargetBlendStateVector::get()
 {
	return (static_cast<cross::NGIBlendStateDescForEditor*>(this->_native))->TargetBlendStateVector;
 }
 void NGIBlendStateDescForEditor::TargetBlendStateVector::set(NGIBlendStateDescForEditor::TargetBlendStateVectorCliType^ value )
 {
	(static_cast<cross::NGIBlendStateDescForEditor*>(this->_native))->TargetBlendStateVector = *value->_native;
 }


//constructor export here
NGIBlendStateDescForEditor::NGIBlendStateDescForEditor( )
    :NGIBlendStateDescForEditor(new cross::NGIBlendStateDescForEditor(), true)
{
}

NGIBlendStateDescForEditor::NGIBlendStateDescForEditor(Clicross::NGIBlendStateDesc^ base )
    :NGIBlendStateDescForEditor(new cross::NGIBlendStateDescForEditor((const cross::NGIBlendStateDesc& )(base)), true)
{
}



NGIBlendStateDescForEditor::NGIBlendStateDescForEditor(const cross::NGIBlendStateDescForEditor * obj, bool created_by_clr): Clicross::NGIBlendStateDesc(obj, created_by_clr)
{
}

NGIBlendStateDescForEditor::operator NGIBlendStateDescForEditor^ (const cross::NGIBlendStateDescForEditor* t)
{
    if(t)
    {
        return gcnew NGIBlendStateDescForEditor(const_cast<cross::NGIBlendStateDescForEditor*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// NGIDynamicStateDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned char NGIDynamicStateDesc::StencilReference::get()
 {
	return (static_cast<cross::NGIDynamicStateDesc*>(this->_native))->StencilReference;
 }
 void NGIDynamicStateDesc::StencilReference::set(unsigned char value )
 {
	(static_cast<cross::NGIDynamicStateDesc*>(this->_native))->StencilReference = value;
 }


//constructor export here
NGIDynamicStateDesc::NGIDynamicStateDesc(): NGIDynamicStateDesc(new cross::NGIDynamicStateDesc(), true) {}


NGIDynamicStateDesc::NGIDynamicStateDesc(const cross::NGIDynamicStateDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::NGIDynamicStateDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NGIDynamicStateDesc::operator NGIDynamicStateDesc^ (const cross::NGIDynamicStateDesc* t)
{
    if(t)
    {
        return gcnew NGIDynamicStateDesc(const_cast<cross::NGIDynamicStateDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// EntityIDStruct export start
namespace Clicross
{
namespace ecs
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EntityIDStruct::EntityIDStruct( )
    :EntityIDStruct(new cross::ecs::EntityIDStruct(), true)
{
}

EntityIDStruct::EntityIDStruct(unsigned long long v )
    :EntityIDStruct(new cross::ecs::EntityIDStruct(v), true)
{
}

EntityIDStruct::EntityIDStruct(unsigned short worldID, unsigned short version, unsigned int handle )
    :EntityIDStruct(new cross::ecs::EntityIDStruct(worldID, version, handle), true)
{
}



EntityIDStruct::EntityIDStruct(const cross::ecs::EntityIDStruct * obj, bool created_by_clr): 
    _native(const_cast<cross::ecs::EntityIDStruct *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EntityIDStruct::operator EntityIDStruct^ (const cross::ecs::EntityIDStruct* t)
{
    if(t)
    {
        return gcnew EntityIDStruct(const_cast<cross::ecs::EntityIDStruct*>(t));
    }
    else
        return nullptr;
}

unsigned long long EntityIDStruct::GetValue( )
{
    return (static_cast<cross::ecs::EntityIDStruct*>(this->_native))->GetValue( );
}

void EntityIDStruct::SetValue(unsigned long long value )
{
    (static_cast<cross::ecs::EntityIDStruct*>(this->_native))->SetValue( value);
}


}   //end namespace Clicross
}   //end namespace ecs

// Resource export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


Resource::Resource(const cross::Resource * obj, bool created_by_clr): 
    _native(const_cast<cross::Resource *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Resource::operator Resource^ (const cross::Resource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Resource(const_cast<cross::Resource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Resource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// InputKeyItem export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyItem::KeyName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InputKeyItem*>(this->_native))->KeyName)).c_str());
 }
 void InputKeyItem::KeyName::set(System::String^ value )
 {
	((static_cast<cross::InputKeyItem*>(this->_native))->KeyName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyItem::InputKeyItem( )
    :InputKeyItem(new cross::InputKeyItem(), true)
{
}

InputKeyItem::InputKeyItem(System::String^ name )
    :InputKeyItem(new cross::InputKeyItem(ClangenCli::ToNativeString(name).c_str()), true)
{
}



InputKeyItem::InputKeyItem(const cross::InputKeyItem * obj, bool created_by_clr): 
    _native(const_cast<cross::InputKeyItem *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InputKeyItem::operator InputKeyItem^ (const cross::InputKeyItem* t)
{
    if(t)
    {
        return gcnew InputKeyItem(const_cast<cross::InputKeyItem*>(t));
    }
    else
        return nullptr;
}

Clicross::InputKeyModifierItem^ InputKeyItem::GetModifier(int idx )
{
    return (Clicross::InputKeyModifierItem^)((static_cast<cross::InputKeyItem*>(this->_native))->GetModifier( idx));
}

int InputKeyItem::GetModifierCount( )
{
    return (static_cast<cross::InputKeyItem*>(this->_native))->GetModifierCount( );
}

void InputKeyItem::CreateModifier(System::String^ modifierName )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->CreateModifier( ClangenCli::ToNativeString(modifierName).c_str());
}

void InputKeyItem::DeleteModifier(int idx )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->DeleteModifier( idx);
}

void InputKeyItem::ChangeModifierType(System::String^ modifierName, int idx )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->ChangeModifierType( ClangenCli::ToNativeString(modifierName).c_str(), idx);
}

Clicross::InputKeyTriggerItem^ InputKeyItem::GetTrigger(int idx )
{
    return (Clicross::InputKeyTriggerItem^)((static_cast<cross::InputKeyItem*>(this->_native))->GetTrigger( idx));
}

int InputKeyItem::GetTriggerCount( )
{
    return (static_cast<cross::InputKeyItem*>(this->_native))->GetTriggerCount( );
}

void InputKeyItem::CreateTrigger(System::String^ triggerName )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->CreateTrigger( ClangenCli::ToNativeString(triggerName).c_str());
}

void InputKeyItem::DeleteTrigger(int idx )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->DeleteTrigger( idx);
}

void InputKeyItem::ChangeTriggerType(System::String^ triggerName, int idx )
{
    (static_cast<cross::InputKeyItem*>(this->_native))->ChangeTriggerType( ClangenCli::ToNativeString(triggerName).c_str(), idx);
}


}   //end namespace Clicross

// InputKeyModifierItem export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyModifierItem::ModifierName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InputKeyModifierItem*>(this->_native))->ModifierName)).c_str());
 }
 void InputKeyModifierItem::ModifierName::set(System::String^ value )
 {
	((static_cast<cross::InputKeyModifierItem*>(this->_native))->ModifierName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyModifierItem::InputKeyModifierItem( )
    :InputKeyModifierItem(new cross::InputKeyModifierItem(), true)
{
}



InputKeyModifierItem::InputKeyModifierItem(const cross::InputKeyModifierItem * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

InputKeyModifierItem::operator InputKeyModifierItem^ (const cross::InputKeyModifierItem* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyModifierItem(const_cast<cross::InputKeyModifierItem*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyModifierItem^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// InputKeyTriggerItem export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyTriggerItem::TriggerName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InputKeyTriggerItem*>(this->_native))->TriggerName)).c_str());
 }
 void InputKeyTriggerItem::TriggerName::set(System::String^ value )
 {
	((static_cast<cross::InputKeyTriggerItem*>(this->_native))->TriggerName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyTriggerItem::InputKeyTriggerItem( )
    :InputKeyTriggerItem(new cross::InputKeyTriggerItem(), true)
{
}



InputKeyTriggerItem::InputKeyTriggerItem(const cross::InputKeyTriggerItem * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

InputKeyTriggerItem::operator InputKeyTriggerItem^ (const cross::InputKeyTriggerItem* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyTriggerItem(const_cast<cross::InputKeyTriggerItem*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyTriggerItem^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// InputMapItem export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::InputMapItem::InputKeys export start
	#define STLDECL_MANAGEDTYPE Clicross::InputKeyItem^
	#define STLDECL_NATIVETYPE cross::InputKeyItem
	CPP_DECLARE_STLVECTOR(InputMapItem::, InputKeysCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ InputMapItem::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InputMapItem*>(this->_native))->Name)).c_str());
 }
 void InputMapItem::Name::set(System::String^ value )
 {
	((static_cast<cross::InputMapItem*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

System::String^ InputMapItem::ValueType::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InputMapItem*>(this->_native))->ValueType)).c_str());
 }
 void InputMapItem::ValueType::set(System::String^ value )
 {
	((static_cast<cross::InputMapItem*>(this->_native))->ValueType) = (ClangenCli::ToNativeString(value));
 }

InputMapItem::InputKeysCliType^ InputMapItem::InputKeys::get()
 {
	return (static_cast<cross::InputMapItem*>(this->_native))->InputKeys;
 }
 void InputMapItem::InputKeys::set(InputMapItem::InputKeysCliType^ value )
 {
	(static_cast<cross::InputMapItem*>(this->_native))->InputKeys = *value->_native;
 }


//constructor export here
InputMapItem::InputMapItem( )
    :InputMapItem(new cross::InputMapItem(), true)
{
}



InputMapItem::InputMapItem(const cross::InputMapItem * obj, bool created_by_clr): 
    _native(const_cast<cross::InputMapItem *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InputMapItem::operator InputMapItem^ (const cross::InputMapItem* t)
{
    if(t)
    {
        return gcnew InputMapItem(const_cast<cross::InputMapItem*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RenderSyncResource export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


RenderSyncResource::RenderSyncResource(const cross::RenderSyncResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

RenderSyncResource::operator RenderSyncResource^ (const cross::RenderSyncResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew RenderSyncResource(const_cast<cross::RenderSyncResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (RenderSyncResource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// SkeletonResource export start
namespace Clicross
{
namespace skeleton
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


SkeletonResource::SkeletonResource(const cross::skeleton::SkeletonResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

SkeletonResource::operator SkeletonResource^ (const cross::skeleton::SkeletonResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SkeletonResource(const_cast<cross::skeleton::SkeletonResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SkeletonResource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace skeleton

// AnimResourceBase export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AnimResourceBase::AnimResourceBase( )
    :AnimResourceBase(new cross::anim::AnimResourceBase(), true)
{
}



AnimResourceBase::AnimResourceBase(const cross::anim::AnimResourceBase * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

AnimResourceBase::operator AnimResourceBase^ (const cross::anim::AnimResourceBase* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimResourceBase(const_cast<cross::anim::AnimResourceBase*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimResourceBase^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// AnimCompositeRes export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AnimCompositeRes::AnimCompositeRes( )
    :AnimCompositeRes(new cross::anim::AnimCompositeRes(), true)
{
}



AnimCompositeRes::AnimCompositeRes(const cross::anim::AnimCompositeRes * obj, bool created_by_clr): Clicross::anim::AnimResourceBase(obj, created_by_clr)
{
}

AnimCompositeRes::operator AnimCompositeRes^ (const cross::anim::AnimCompositeRes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimCompositeRes(const_cast<cross::anim::AnimCompositeRes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimCompositeRes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// AnimSequenceRes export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AnimSequenceRes::AnimSequenceRes( )
    :AnimSequenceRes(new cross::anim::AnimSequenceRes(), true)
{
}



AnimSequenceRes::AnimSequenceRes(const cross::anim::AnimSequenceRes * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

AnimSequenceRes::operator AnimSequenceRes^ (const cross::anim::AnimSequenceRes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimSequenceRes(const_cast<cross::anim::AnimSequenceRes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimSequenceRes^)managedObj;
    }
    else
        return nullptr;
}

System::String^ AnimSequenceRes::EditorGetRefPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::anim::AnimSequenceRes*>(this->_native))->EditorGetRefPath( ))).c_str());
}

float AnimSequenceRes::EditorGetDuration( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->EditorGetDuration( );
}

int AnimSequenceRes::EditorGetFrameCount( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->EditorGetFrameCount( );
}

int AnimSequenceRes::EditorGetCprType( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->EditorGetCprType( );
}

bool AnimSequenceRes::GetHasRootMotion( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetHasRootMotion( );
}

void AnimSequenceRes::SetHasRootMotion(bool enableRootMotion )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetHasRootMotion( enableRootMotion);
}

int AnimSequenceRes::GetRootLockType( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetRootLockType( );
}

void AnimSequenceRes::SetRootLockType(int rootLockType )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetRootLockType( rootLockType);
}

int AnimSequenceRes::GetAdditiveSpace( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetAdditiveSpace( );
}

void AnimSequenceRes::SetAdditiveSpace(int additiveSpace )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetAdditiveSpace( additiveSpace);
}

int AnimSequenceRes::GetAdditiveBasePoseType( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetAdditiveBasePoseType( );
}

void AnimSequenceRes::SetAdditiveBasePoseType(int additiveBasePoseType )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetAdditiveBasePoseType( additiveBasePoseType);
}

Clicross::UniqueString^ AnimSequenceRes::GetBasePoseSeq( )
{
    return gcnew Clicross::UniqueString(new cross::UniqueString(((static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetBasePoseSeq( ))) , true);
}

void AnimSequenceRes::SetBasePoseSeq(System::String^ basePoseSeqPath )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetBasePoseSeq( ClangenCli::ToNativeString(basePoseSeqPath).c_str());
}

int AnimSequenceRes::GetBaseSeqFrameIndex( )
{
    return (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->GetBaseSeqFrameIndex( );
}

void AnimSequenceRes::SetBaseSeqFrameIndex(int frameIndex )
{
    (static_cast<cross::anim::AnimSequenceRes*>(this->_native))->SetBaseSeqFrameIndex( frameIndex);
}


}   //end namespace Clicross
}   //end namespace anim

// AnimatrixRes export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


AnimatrixRes::AnimatrixRes(const cross::anim::AnimatrixRes * obj, bool created_by_clr): Clicross::anim::AnimResourceBase(obj, created_by_clr)
{
}

AnimatrixRes::operator AnimatrixRes^ (const cross::anim::AnimatrixRes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimatrixRes(const_cast<cross::anim::AnimatrixRes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimatrixRes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// AnimatorRes export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


AnimatorRes::AnimatorRes(const cross::anim::AnimatorRes * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

AnimatorRes::operator AnimatorRes^ (const cross::anim::AnimatorRes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimatorRes(const_cast<cross::anim::AnimatorRes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimatorRes^)managedObj;
    }
    else
        return nullptr;
}

System::String^ AnimatorRes::GetContentAsJson( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::anim::AnimatorRes*>(this->_native))->GetContentAsJson( ))).c_str());
}


}   //end namespace Clicross
}   //end namespace anim

// ParticleSystemResource export start
namespace Clicross
{
namespace fx
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ParticleSystemResource::ParticleSystemResource(const cross::fx::ParticleSystemResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

ParticleSystemResource::operator ParticleSystemResource^ (const cross::fx::ParticleSystemResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ParticleSystemResource(const_cast<cross::fx::ParticleSystemResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ParticleSystemResource^)managedObj;
    }
    else
        return nullptr;
}

Clicross::fx::ParticleSystemResource^ ParticleSystemResource::FX_CreateParticleSystem( )
{
    return (Clicross::fx::ParticleSystemResource^)(cross::fx::ParticleSystemResource::FX_CreateParticleSystem( ));
}


}   //end namespace Clicross
}   //end namespace fx

// MinMaxCurve export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::fx::ModuleScope MinMaxCurve::ModuleScope::get()
 {
	return (Clicross::fx::ModuleScope)((int)(static_cast<cross::MinMaxCurve*>(this->_native))->ModuleScope);
 }
 void MinMaxCurve::ModuleScope::set(Clicross::fx::ModuleScope value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->ModuleScope = static_cast<cross::fx::ModuleScope>(value);
 }

Clicross::fx::ModuleIndex MinMaxCurve::ModuleIndex::get()
 {
	return (Clicross::fx::ModuleIndex)((int)(static_cast<cross::MinMaxCurve*>(this->_native))->ModuleIndex);
 }
 void MinMaxCurve::ModuleIndex::set(Clicross::fx::ModuleIndex value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->ModuleIndex = static_cast<cross::fx::ModuleIndex>(value);
 }

System::String^ MinMaxCurve::VariableName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MinMaxCurve*>(this->_native))->VariableName)).c_str());
 }
 void MinMaxCurve::VariableName::set(System::String^ value )
 {
	((static_cast<cross::MinMaxCurve*>(this->_native))->VariableName) = (ClangenCli::ToNativeString(value));
 }

Clicross::MinMaxCurveMode MinMaxCurve::Mode::get()
 {
	return (Clicross::MinMaxCurveMode)((int)(static_cast<cross::MinMaxCurve*>(this->_native))->Mode);
 }
 void MinMaxCurve::Mode::set(Clicross::MinMaxCurveMode value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->Mode = static_cast<cross::MinMaxCurveMode>(value);
 }

float MinMaxCurve::Scaler::get()
 {
	return (static_cast<cross::MinMaxCurve*>(this->_native))->Scaler;
 }
 void MinMaxCurve::Scaler::set(float value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->Scaler = value;
 }

float MinMaxCurve::FloatMin::get()
 {
	return (static_cast<cross::MinMaxCurve*>(this->_native))->FloatMin;
 }
 void MinMaxCurve::FloatMin::set(float value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->FloatMin = value;
 }

float MinMaxCurve::FloatMax::get()
 {
	return (static_cast<cross::MinMaxCurve*>(this->_native))->FloatMax;
 }
 void MinMaxCurve::FloatMax::set(float value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->FloatMax = value;
 }

Clicross::CurveEvaluateType MinMaxCurve::EvaluateMode::get()
 {
	return (Clicross::CurveEvaluateType)((int)(static_cast<cross::MinMaxCurve*>(this->_native))->EvaluateMode);
 }
 void MinMaxCurve::EvaluateMode::set(Clicross::CurveEvaluateType value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->EvaluateMode = static_cast<cross::CurveEvaluateType>(value);
 }

Clicross::FloatCurveTrack^ MinMaxCurve::CurveMin::get()
 {
	return gcnew Clicross::FloatCurveTrack(new cross::FloatCurveTrack(((static_cast<cross::MinMaxCurve*>(this->_native))->CurveMin)) , true);
 }
 void MinMaxCurve::CurveMin::set(Clicross::FloatCurveTrack^ value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->CurveMin = value;
 }

Clicross::FloatCurveTrack^ MinMaxCurve::CurveMax::get()
 {
	return gcnew Clicross::FloatCurveTrack(new cross::FloatCurveTrack(((static_cast<cross::MinMaxCurve*>(this->_native))->CurveMax)) , true);
 }
 void MinMaxCurve::CurveMax::set(Clicross::FloatCurveTrack^ value )
 {
	(static_cast<cross::MinMaxCurve*>(this->_native))->CurveMax = value;
 }


//constructor export here
MinMaxCurve::MinMaxCurve( )
    :MinMaxCurve(new cross::MinMaxCurve(), true)
{
}

MinMaxCurve::MinMaxCurve(Clicross::MinMaxCurve^ other )
    :MinMaxCurve(new cross::MinMaxCurve((const cross::MinMaxCurve& )(other)), true)
{
}

MinMaxCurve::MinMaxCurve(float constant )
    :MinMaxCurve(new cross::MinMaxCurve(constant), true)
{
}

MinMaxCurve::MinMaxCurve(float min, float max )
    :MinMaxCurve(new cross::MinMaxCurve(min, max), true)
{
}

MinMaxCurve::MinMaxCurve(Clicross::MinMaxCurveMode mode )
    :MinMaxCurve(new cross::MinMaxCurve(static_cast<cross::MinMaxCurveMode>(mode)), true)
{
}



MinMaxCurve::MinMaxCurve(const cross::MinMaxCurve * obj, bool created_by_clr): 
    _native(const_cast<cross::MinMaxCurve *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MinMaxCurve::operator MinMaxCurve^ (const cross::MinMaxCurve* t)
{
    if(t)
    {
        return gcnew MinMaxCurve(const_cast<cross::MinMaxCurve*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// Texture export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Texture::Texture( )
    :Texture(new cross::resource::Texture(), true)
{
}

Texture::Texture(UnknowKeeper^ createInfo, System::String^ pDebugName )
    :Texture(new cross::resource::Texture((UnknowKeeper::get_native_with_type_for_reference<TextureInfo& >(createInfo)), ClangenCli::ToNativeString(pDebugName).c_str()), true)
{
}



Texture::Texture(const cross::resource::Texture * obj, bool created_by_clr): Clicross::RenderSyncResource(obj, created_by_clr)
{
}

Texture::operator Texture^ (const cross::resource::Texture* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Texture(const_cast<cross::resource::Texture*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Texture^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// Shader export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


Shader::Shader(const cross::resource::Shader * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

Shader::operator Shader^ (const cross::resource::Shader* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Shader(const_cast<cross::resource::Shader*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Shader^)managedObj;
    }
    else
        return nullptr;
}

System::String^ Shader::EditorGetProperty( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Shader*>(this->_native))->EditorGetProperty( ))).c_str());
}


}   //end namespace Clicross
}   //end namespace resource

// RayTracingShader export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
RayTracingShader::RayTracingShader( )
    :RayTracingShader(new cross::resource::RayTracingShader(), true)
{
}



RayTracingShader::RayTracingShader(const cross::resource::RayTracingShader * obj, bool created_by_clr): Clicross::RenderSyncResource(obj, created_by_clr)
{
}

RayTracingShader::operator RayTracingShader^ (const cross::resource::RayTracingShader* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew RayTracingShader(const_cast<cross::resource::RayTracingShader*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (RayTracingShader^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// MaterialInterface export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


MaterialInterface::MaterialInterface(const cross::resource::MaterialInterface * obj, bool created_by_clr): Clicross::RenderSyncResource(obj, created_by_clr)
{
}

MaterialInterface::operator MaterialInterface^ (const cross::resource::MaterialInterface* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialInterface(const_cast<cross::resource::MaterialInterface*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialInterface^)managedObj;
    }
    else
        return nullptr;
}

int MaterialInterface::GetVersion( )
{
    return (static_cast<cross::resource::MaterialInterface*>(this->_native))->GetVersion( );
}


}   //end namespace Clicross
}   //end namespace resource

// Material export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Material::Material( )
    :Material(new cross::resource::Material(), true)
{
}



Material::Material(const cross::resource::Material * obj, bool created_by_clr): Clicross::resource::MaterialInterface(obj, created_by_clr)
{
}

Material::operator Material^ (const cross::resource::Material* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Material(const_cast<cross::resource::Material*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Material^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Resource^ Material::MaterialCreateMaterial(System::String^ shaderPath )
{
    return (Clicross::Resource^)(cross::resource::Material::MaterialCreateMaterial( ClangenCli::ToNativeString(shaderPath).c_str()));
}

Clicross::resource::Material^ Material::Material_CreateInstance(Clicross::resource::Material^ material )
{
    return (Clicross::resource::Material^)(cross::resource::Material::Material_CreateInstance( ( cross::resource::Material* )(material)));
}

int Material::GetPropertyType(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetPropertyType( (const cross::NameID& )(name));
}

int Material::GetPropertyOverrided(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetPropertyOverrided( (const cross::NameID& )(name));
}

float Material::GetPropertyFloat(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetPropertyFloat( (const cross::NameID& )(name));
}

Clicross::Float2^ Material::GetPropertyFloat2(Clicross::NameID^ name )
{
    return gcnew Clicross::Float2(new cross::Float2(((static_cast<cross::resource::Material*>(this->_native))->GetPropertyFloat2( (const cross::NameID& )(name)))) , true);
}

Clicross::Float3^ Material::GetPropertyFloat3(Clicross::NameID^ name )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::resource::Material*>(this->_native))->GetPropertyFloat3( (const cross::NameID& )(name)))) , true);
}

Clicross::Float4^ Material::GetPropertyFloat4(Clicross::NameID^ name )
{
    return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::resource::Material*>(this->_native))->GetPropertyFloat4( (const cross::NameID& )(name)))) , true);
}

System::String^ Material::GetPropertyString(Clicross::NameID^ name )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->GetPropertyString( (const cross::NameID& )(name)))).c_str());
}

bool Material::GetPropertyBool(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetPropertyBool( (const cross::NameID& )(name));
}

int Material::GetRenderState(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetRenderState( (const cross::NameID& )(name));
}

unsigned int Material::GetRenderGroup(Clicross::NameID^ passID )
{
    return (static_cast<cross::resource::Material*>(this->_native))->GetRenderGroup( (const cross::NameID& )(passID));
}

bool Material::SetParent(System::String^ path )
{
    return (static_cast<cross::resource::Material*>(this->_native))->SetParent( ClangenCli::ToNativeString(path).c_str());
}

void Material::SetBool(Clicross::NameID^ name, bool value )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetBool( (const cross::NameID& )(name), value);
}

void Material::SetInt(Clicross::NameID^ name, int value )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetInt( (const cross::NameID& )(name), value);
}

void Material::SetFloat(Clicross::NameID^ name, float value )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetFloat( (const cross::NameID& )(name), value);
}

void Material::SetFloatArray(Clicross::NameID^ name, int length, System::IntPtr pValue )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetFloatArray( (const cross::NameID& )(name), length, reinterpret_cast<float*>(pValue.ToInt64()));
}

void Material::SetTexture(Clicross::NameID^ name, System::String^ texturepath )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetTexture( (const cross::NameID& )(name), ClangenCli::ToNativeString(texturepath).c_str());
}

void Material::SetRenderGroup(Clicross::NameID^ passID, unsigned int val )
{
    (static_cast<cross::resource::Material*>(this->_native))->SetRenderGroup( (const cross::NameID& )(passID), val);
}

void Material::RemoveProperty(Clicross::NameID^ name )
{
    (static_cast<cross::resource::Material*>(this->_native))->RemoveProperty( (const cross::NameID& )(name));
}

void Material::ResetRenderGroup(Clicross::NameID^ name )
{
    (static_cast<cross::resource::Material*>(this->_native))->ResetRenderGroup( (const cross::NameID& )(name));
}

System::String^ Material::EditorGetPropertyString( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->EditorGetPropertyString( ))).c_str());
}

System::String^ Material::EditorGetPassString( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->EditorGetPassString( ))).c_str());
}

void Material::EditorSetRenderState(Clicross::NameID^ passID, Clicross::MaterialRenderState renderState )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorSetRenderState( (const cross::NameID& )(passID), static_cast<cross::MaterialRenderState>(renderState));
}

bool Material::EditorIsPropertyOverrided(Clicross::NameID^ propID )
{
    return (static_cast<cross::resource::Material*>(this->_native))->EditorIsPropertyOverrided( (const cross::NameID& )(propID));
}

bool Material::EditorIsRenderGroupOverrided(Clicross::NameID^ passID )
{
    return (static_cast<cross::resource::Material*>(this->_native))->EditorIsRenderGroupOverrided( (const cross::NameID& )(passID));
}

bool Material::EditorIsRenderStateOverrided(Clicross::NameID^ passID )
{
    return (static_cast<cross::resource::Material*>(this->_native))->EditorIsRenderStateOverrided( (const cross::NameID& )(passID));
}

void Material::EditorNotitfySubMaterial( )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorNotitfySubMaterial( );
}

void Material::EditorSetMPCResource(System::String^ mpcPath )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorSetMPCResource( ClangenCli::ToNativeString(mpcPath).c_str());
}

void Material::EditorSetFloat2(Clicross::NameID^ name, Clicross::Float2^ value )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorSetFloat2( (const cross::NameID& )(name), *((cross::Float2*)(value)));
}

void Material::EditorSetFloat3(Clicross::NameID^ name, Clicross::Float3^ value )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorSetFloat3( (const cross::NameID& )(name), *((cross::Float3*)(value)));
}

void Material::EditorSetFloat4(Clicross::NameID^ name, Clicross::Float4^ value )
{
    (static_cast<cross::resource::Material*>(this->_native))->EditorSetFloat4( (const cross::NameID& )(name), *((cross::Float4*)(value)));
}

System::String^ Material::EditorGetParentPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->EditorGetParentPath( ))).c_str());
}

bool Material::EditorGetIsOverrideMat( )
{
    return (static_cast<cross::resource::Material*>(this->_native))->EditorGetIsOverrideMat( );
}

System::String^ Material::EditorGetFxPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->EditorGetFxPath( ))).c_str());
}

System::String^ Material::EditorGetParameterCollectionPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Material*>(this->_native))->EditorGetParameterCollectionPath( ))).c_str());
}


}   //end namespace Clicross
}   //end namespace resource


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


