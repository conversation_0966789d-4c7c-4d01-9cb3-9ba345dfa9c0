//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// VarInspectorHelper export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
VarInspectorHelper::VarInspectorHelper( )
    :VarInspectorHelper(new gbf::logic::VarInspectorHelper(), true)
{
}



VarInspectorHelper::VarInspectorHelper(const gbf::logic::VarInspectorHelper * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

VarInspectorHelper::operator VarInspectorHelper^ (const gbf::logic::VarInspectorHelper* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper(const_cast<gbf::logic::VarInspectorHelper*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__MaterialPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__MaterialPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__MaterialPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__MaterialPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__MaterialPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__MaterialPtr::VarInspectorHelper_cross__MaterialPtr( )
    :VarInspectorHelper_cross__MaterialPtr(new gbf::logic::VarInspectorHelper_cross__MaterialPtr(), true)
{
}



VarInspectorHelper_cross__MaterialPtr::VarInspectorHelper_cross__MaterialPtr(const gbf::logic::VarInspectorHelper_cross__MaterialPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__MaterialPtr::operator VarInspectorHelper_cross__MaterialPtr^ (const gbf::logic::VarInspectorHelper_cross__MaterialPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__MaterialPtr(const_cast<gbf::logic::VarInspectorHelper_cross__MaterialPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__MaterialPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__MaterialPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__MaterialPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__MaterialPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__MaterialPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__anim__AnimatorResPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__anim__AnimatorResPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__anim__AnimatorResPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__anim__AnimatorResPtr::VarInspectorHelper_cross__anim__AnimatorResPtr( )
    :VarInspectorHelper_cross__anim__AnimatorResPtr(new gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr(), true)
{
}



VarInspectorHelper_cross__anim__AnimatorResPtr::VarInspectorHelper_cross__anim__AnimatorResPtr(const gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__anim__AnimatorResPtr::operator VarInspectorHelper_cross__anim__AnimatorResPtr^ (const gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__anim__AnimatorResPtr(const_cast<gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__anim__AnimatorResPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__anim__AnimatorResPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__anim__AnimatorResPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__anim__AnimatorResPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__TexturePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__TexturePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__TexturePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__TexturePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__TexturePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__TexturePtr::VarInspectorHelper_cross__TexturePtr( )
    :VarInspectorHelper_cross__TexturePtr(new gbf::logic::VarInspectorHelper_cross__TexturePtr(), true)
{
}



VarInspectorHelper_cross__TexturePtr::VarInspectorHelper_cross__TexturePtr(const gbf::logic::VarInspectorHelper_cross__TexturePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__TexturePtr::operator VarInspectorHelper_cross__TexturePtr^ (const gbf::logic::VarInspectorHelper_cross__TexturePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__TexturePtr(const_cast<gbf::logic::VarInspectorHelper_cross__TexturePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__TexturePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__TexturePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TexturePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__TexturePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TexturePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__ShaderPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__ShaderPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__ShaderPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__ShaderPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__ShaderPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__ShaderPtr::VarInspectorHelper_cross__ShaderPtr( )
    :VarInspectorHelper_cross__ShaderPtr(new gbf::logic::VarInspectorHelper_cross__ShaderPtr(), true)
{
}



VarInspectorHelper_cross__ShaderPtr::VarInspectorHelper_cross__ShaderPtr(const gbf::logic::VarInspectorHelper_cross__ShaderPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__ShaderPtr::operator VarInspectorHelper_cross__ShaderPtr^ (const gbf::logic::VarInspectorHelper_cross__ShaderPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__ShaderPtr(const_cast<gbf::logic::VarInspectorHelper_cross__ShaderPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__ShaderPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__ShaderPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ShaderPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__ShaderPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ShaderPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__ComputeShaderPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__ComputeShaderPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__ComputeShaderPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__ComputeShaderPtr::VarInspectorHelper_cross__ComputeShaderPtr( )
    :VarInspectorHelper_cross__ComputeShaderPtr(new gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr(), true)
{
}



VarInspectorHelper_cross__ComputeShaderPtr::VarInspectorHelper_cross__ComputeShaderPtr(const gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__ComputeShaderPtr::operator VarInspectorHelper_cross__ComputeShaderPtr^ (const gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__ComputeShaderPtr(const_cast<gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__ComputeShaderPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__ComputeShaderPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__ComputeShaderPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ComputeShaderPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Texture2DPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__Texture2DPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__Texture2DPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__Texture2DPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__Texture2DPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__Texture2DPtr::VarInspectorHelper_cross__Texture2DPtr( )
    :VarInspectorHelper_cross__Texture2DPtr(new gbf::logic::VarInspectorHelper_cross__Texture2DPtr(), true)
{
}



VarInspectorHelper_cross__Texture2DPtr::VarInspectorHelper_cross__Texture2DPtr(const gbf::logic::VarInspectorHelper_cross__Texture2DPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Texture2DPtr::operator VarInspectorHelper_cross__Texture2DPtr^ (const gbf::logic::VarInspectorHelper_cross__Texture2DPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Texture2DPtr(const_cast<gbf::logic::VarInspectorHelper_cross__Texture2DPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Texture2DPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Texture2DPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Texture2DPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Texture2DPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Texture2DPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__TextureVirtualPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__TextureVirtualPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__TextureVirtualPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__TextureVirtualPtr::VarInspectorHelper_cross__TextureVirtualPtr( )
    :VarInspectorHelper_cross__TextureVirtualPtr(new gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr(), true)
{
}



VarInspectorHelper_cross__TextureVirtualPtr::VarInspectorHelper_cross__TextureVirtualPtr(const gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__TextureVirtualPtr::operator VarInspectorHelper_cross__TextureVirtualPtr^ (const gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__TextureVirtualPtr(const_cast<gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__TextureVirtualPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__TextureVirtualPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__TextureVirtualPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TextureVirtualPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__TextureCubePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__TextureCubePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__TextureCubePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__TextureCubePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__TextureCubePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__TextureCubePtr::VarInspectorHelper_cross__TextureCubePtr( )
    :VarInspectorHelper_cross__TextureCubePtr(new gbf::logic::VarInspectorHelper_cross__TextureCubePtr(), true)
{
}



VarInspectorHelper_cross__TextureCubePtr::VarInspectorHelper_cross__TextureCubePtr(const gbf::logic::VarInspectorHelper_cross__TextureCubePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__TextureCubePtr::operator VarInspectorHelper_cross__TextureCubePtr^ (const gbf::logic::VarInspectorHelper_cross__TextureCubePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__TextureCubePtr(const_cast<gbf::logic::VarInspectorHelper_cross__TextureCubePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__TextureCubePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__TextureCubePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TextureCubePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__TextureCubePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__TextureCubePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__PrefabResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__PrefabResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__PrefabResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__PrefabResourcePtr::VarInspectorHelper_cross__PrefabResourcePtr( )
    :VarInspectorHelper_cross__PrefabResourcePtr(new gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr(), true)
{
}



VarInspectorHelper_cross__PrefabResourcePtr::VarInspectorHelper_cross__PrefabResourcePtr(const gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__PrefabResourcePtr::operator VarInspectorHelper_cross__PrefabResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__PrefabResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__PrefabResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__PrefabResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__PrefabResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__PrefabResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__MeshAssetDataResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__MeshAssetDataResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__MeshAssetDataResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__MeshAssetDataResourcePtr::VarInspectorHelper_cross__MeshAssetDataResourcePtr( )
    :VarInspectorHelper_cross__MeshAssetDataResourcePtr(new gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr(), true)
{
}



VarInspectorHelper_cross__MeshAssetDataResourcePtr::VarInspectorHelper_cross__MeshAssetDataResourcePtr(const gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__MeshAssetDataResourcePtr::operator VarInspectorHelper_cross__MeshAssetDataResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__MeshAssetDataResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__MeshAssetDataResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__MeshAssetDataResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__MeshAssetDataResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__MeshAssetDataResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CurveControllerResPtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__CurveControllerResPtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__CurveControllerResPtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__CurveControllerResPtr::VarInspectorHelper_cross__CurveControllerResPtr( )
    :VarInspectorHelper_cross__CurveControllerResPtr(new gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr(), true)
{
}



VarInspectorHelper_cross__CurveControllerResPtr::VarInspectorHelper_cross__CurveControllerResPtr(const gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CurveControllerResPtr::operator VarInspectorHelper_cross__CurveControllerResPtr^ (const gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CurveControllerResPtr(const_cast<gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CurveControllerResPtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CurveControllerResPtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CurveControllerResPtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CurveControllerResPtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__FontResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__FontResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__FontResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__FontResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__FontResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__FontResourcePtr::VarInspectorHelper_cross__FontResourcePtr( )
    :VarInspectorHelper_cross__FontResourcePtr(new gbf::logic::VarInspectorHelper_cross__FontResourcePtr(), true)
{
}



VarInspectorHelper_cross__FontResourcePtr::VarInspectorHelper_cross__FontResourcePtr(const gbf::logic::VarInspectorHelper_cross__FontResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__FontResourcePtr::operator VarInspectorHelper_cross__FontResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__FontResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__FontResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__FontResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__FontResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__FontResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__FontResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__FontResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__FontResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__WorkflowGraphResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__WorkflowGraphResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__WorkflowGraphResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__WorkflowGraphResourcePtr::VarInspectorHelper_cross__WorkflowGraphResourcePtr( )
    :VarInspectorHelper_cross__WorkflowGraphResourcePtr(new gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr(), true)
{
}



VarInspectorHelper_cross__WorkflowGraphResourcePtr::VarInspectorHelper_cross__WorkflowGraphResourcePtr(const gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__WorkflowGraphResourcePtr::operator VarInspectorHelper_cross__WorkflowGraphResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__WorkflowGraphResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__WorkflowGraphResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__WorkflowGraphResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__WorkflowGraphResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__WorkflowGraphResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__InputActionMappingResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__InputActionMappingResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__InputActionMappingResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__InputActionMappingResourcePtr::VarInspectorHelper_cross__InputActionMappingResourcePtr( )
    :VarInspectorHelper_cross__InputActionMappingResourcePtr(new gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr(), true)
{
}



VarInspectorHelper_cross__InputActionMappingResourcePtr::VarInspectorHelper_cross__InputActionMappingResourcePtr(const gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__InputActionMappingResourcePtr::operator VarInspectorHelper_cross__InputActionMappingResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__InputActionMappingResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__InputActionMappingResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__InputActionMappingResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__InputActionMappingResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__InputActionMappingResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__DataAssetResourcePtr export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_cross__DataAssetResourcePtr::mValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr*>(this->_native))->mValue)).c_str());
 }
 void VarInspectorHelper_cross__DataAssetResourcePtr::mValue::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr*>(this->_native))->mValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_cross__DataAssetResourcePtr::VarInspectorHelper_cross__DataAssetResourcePtr( )
    :VarInspectorHelper_cross__DataAssetResourcePtr(new gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr(), true)
{
}



VarInspectorHelper_cross__DataAssetResourcePtr::VarInspectorHelper_cross__DataAssetResourcePtr(const gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__DataAssetResourcePtr::operator VarInspectorHelper_cross__DataAssetResourcePtr^ (const gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__DataAssetResourcePtr(const_cast<gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__DataAssetResourcePtr^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__DataAssetResourcePtr::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__DataAssetResourcePtr::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__DataAssetResourcePtr*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// ObjectBase export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ObjectBase::ObjectBase( )
    :ObjectBase(new cegf::ObjectBase(), true)
{
}



ObjectBase::ObjectBase(const cegf::ObjectBase * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

ObjectBase::operator ObjectBase^ (const cegf::ObjectBase* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ObjectBase(const_cast<cegf::ObjectBase*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ObjectBase^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// GameObjectComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
GameObjectComponent::GameObjectComponent( )
    :GameObjectComponent(new cegf::GameObjectComponent(), true)
{
}

GameObjectComponent::GameObjectComponent(Clicegf::GameObject^ owner )
    :GameObjectComponent(new cegf::GameObjectComponent(( cegf::GameObject* )(owner)), true)
{
}



GameObjectComponent::GameObjectComponent(const cegf::GameObjectComponent * obj, bool created_by_clr): Clicegf::ObjectBase(obj, created_by_clr)
{
}

GameObjectComponent::operator GameObjectComponent^ (const cegf::GameObjectComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew GameObjectComponent(const_cast<cegf::GameObjectComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (GameObjectComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// GameObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
bool GameObject::mHasInitialized::get()
 {
	return (static_cast<cegf::GameObject*>(this->_native))->mHasInitialized;
 }
 void GameObject::mHasInitialized::set(bool value )
 {
	(static_cast<cegf::GameObject*>(this->_native))->mHasInitialized = value;
 }

bool GameObject::mHasStarted::get()
 {
	return (static_cast<cegf::GameObject*>(this->_native))->mHasStarted;
 }
 void GameObject::mHasStarted::set(bool value )
 {
	(static_cast<cegf::GameObject*>(this->_native))->mHasStarted = value;
 }

bool GameObject::mReadOnlyTest::get()
 {
	return (static_cast<cegf::GameObject*>(this->_native))->mReadOnlyTest;
 }
 void GameObject::mReadOnlyTest::set(bool value )
 {
	(static_cast<cegf::GameObject*>(this->_native))->mReadOnlyTest = value;
 }


//constructor export here
GameObject::GameObject( )
    :GameObject(new cegf::GameObject(), true)
{
}



GameObject::GameObject(const cegf::GameObject * obj, bool created_by_clr): Clicegf::ObjectBase(obj, created_by_clr)
{
}

GameObject::operator GameObject^ (const cegf::GameObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew GameObject(const_cast<cegf::GameObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (GameObject^)managedObj;
    }
    else
        return nullptr;
}

void GameObject::SetScriptPath(System::String^ path )
{
    (static_cast<cegf::GameObject*>(this->_native))->SetScriptPath( ClangenCli::ToNativeString(path).c_str());
}

System::String^ GameObject::GetScriptPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::GameObject*>(this->_native))->GetScriptPath( ))).c_str());
}

System::String^ GameObject::GetScriptEditorFieldsJson( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::GameObject*>(this->_native))->GetScriptEditorFieldsJson( ))).c_str());
}

void GameObject::RebindScriptWithEditorFieldsJson(System::String^ _param_1 )
{
    (static_cast<cegf::GameObject*>(this->_native))->RebindScriptWithEditorFieldsJson( ClangenCli::ToNativeString(_param_1).c_str());
}

Clicross::ecs::EntityIDStruct^ GameObject::GetObjectEntityID( )
{
    return (Clicross::ecs::EntityIDStruct^)((static_cast<cegf::GameObject*>(this->_native))->GetObjectEntityID( ));
}

Clicegf::GameObjectComponent^ GameObject::GetComponentByName(System::String^ compName )
{
    return (Clicegf::GameObjectComponent^)((static_cast<cegf::GameObject*>(this->_native))->GetComponentByName( ClangenCli::ToNativeString(compName).c_str()));
}

Clicegf::GameObjectComponent^ GameObject::GetComponentByMetaClassName(System::String^ compName )
{
    return (Clicegf::GameObjectComponent^)((static_cast<cegf::GameObject*>(this->_native))->GetComponentByMetaClassName( ClangenCli::ToNativeString(compName).c_str()));
}

bool GameObject::CreateComponentByClassName(System::String^ compName )
{
    return (static_cast<cegf::GameObject*>(this->_native))->CreateComponentByClassName( ClangenCli::ToNativeString(compName).c_str());
}

bool GameObject::RemoveComponentByClassName(System::String^ compName )
{
    return (static_cast<cegf::GameObject*>(this->_native))->RemoveComponentByClassName( ClangenCli::ToNativeString(compName).c_str());
}

void GameObject::SyncGOSerializer( )
{
    (static_cast<cegf::GameObject*>(this->_native))->SyncGOSerializer( );
}

void GameObject::PostEditChangeProperty(Clicegf::PropertyChangedEvent^ PropertyChangedEvent )
{
    (static_cast<cegf::GameObject*>(this->_native))->PostEditChangeProperty( ( cegf::PropertyChangedEvent& )(PropertyChangedEvent));
}


}   //end namespace Clicegf

// PropertyChangedEvent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
System::String^ PropertyChangedEvent::PropertyName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::PropertyChangedEvent*>(this->_native))->PropertyName)).c_str());
 }
 void PropertyChangedEvent::PropertyName::set(System::String^ value )
 {
	((static_cast<cegf::PropertyChangedEvent*>(this->_native))->PropertyName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
PropertyChangedEvent::PropertyChangedEvent(): PropertyChangedEvent(new cegf::PropertyChangedEvent(), true) {}


PropertyChangedEvent::PropertyChangedEvent(const cegf::PropertyChangedEvent * obj, bool created_by_clr): 
    _native(const_cast<cegf::PropertyChangedEvent *>(obj))
	, _created_by_clr(created_by_clr)
{
}

PropertyChangedEvent::operator PropertyChangedEvent^ (const cegf::PropertyChangedEvent* t)
{
    if(t)
    {
        return gcnew PropertyChangedEvent(const_cast<cegf::PropertyChangedEvent*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// Vector_std_string_wrapper export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::Vector_std_string_wrapper::holder export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(Vector_std_string_wrapper::, holderCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
Vector_std_string_wrapper::holderCliType^ Vector_std_string_wrapper::holder::get()
 {
	return (static_cast<cegf::Vector_std_string_wrapper*>(this->_native))->holder;
 }
 void Vector_std_string_wrapper::holder::set(Vector_std_string_wrapper::holderCliType^ value )
 {
	(static_cast<cegf::Vector_std_string_wrapper*>(this->_native))->holder = *value->_native;
 }


//constructor export here
Vector_std_string_wrapper::Vector_std_string_wrapper( )
    :Vector_std_string_wrapper(new cegf::Vector_std_string_wrapper(), true)
{
}



Vector_std_string_wrapper::Vector_std_string_wrapper(const cegf::Vector_std_string_wrapper * obj, bool created_by_clr): 
    _native(const_cast<cegf::Vector_std_string_wrapper *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Vector_std_string_wrapper::operator Vector_std_string_wrapper^ (const cegf::Vector_std_string_wrapper* t)
{
    if(t)
    {
        return gcnew Vector_std_string_wrapper(const_cast<cegf::Vector_std_string_wrapper*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// Controller export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Controller::Controller( )
    :Controller(new cegf::Controller(), true)
{
}



Controller::Controller(const cegf::Controller * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

Controller::operator Controller^ (const cegf::Controller* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Controller(const_cast<cegf::Controller*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Controller^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// PlayerController export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
PlayerController::PlayerController( )
    :PlayerController(new cegf::PlayerController(), true)
{
}



PlayerController::PlayerController(const cegf::PlayerController * obj, bool created_by_clr): Clicegf::Controller(obj, created_by_clr)
{
}

PlayerController::operator PlayerController^ (const cegf::PlayerController* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew PlayerController(const_cast<cegf::PlayerController*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (PlayerController^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// Pawn export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Pawn::Pawn( )
    :Pawn(new cegf::Pawn(), true)
{
}



Pawn::Pawn(const cegf::Pawn * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

Pawn::operator Pawn^ (const cegf::Pawn* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Pawn(const_cast<cegf::Pawn*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Pawn^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CameraComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CameraComponent::CameraComponent( )
    :CameraComponent(new cegf::CameraComponent(), true)
{
}



CameraComponent::CameraComponent(const cegf::CameraComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

CameraComponent::operator CameraComponent^ (const cegf::CameraComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CameraComponent(const_cast<cegf::CameraComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CameraComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// AudioComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AudioComponent::AudioComponent( )
    :AudioComponent(new cegf::AudioComponent(), true)
{
}



AudioComponent::AudioComponent(const cegf::AudioComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

AudioComponent::operator AudioComponent^ (const cegf::AudioComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AudioComponent(const_cast<cegf::AudioComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AudioComponent^)managedObj;
    }
    else
        return nullptr;
}

bool AudioComponent::GetIsSpatialListener( )
{
    return (static_cast<cegf::AudioComponent*>(this->_native))->GetIsSpatialListener( );
}

void AudioComponent::SetIsSpatialListener(bool value )
{
    (static_cast<cegf::AudioComponent*>(this->_native))->SetIsSpatialListener( value);
}

System::String^ AudioComponent::GetBank( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::AudioComponent*>(this->_native))->GetBank( ))).c_str());
}

void AudioComponent::SetBank(System::String^ value )
{
    (static_cast<cegf::AudioComponent*>(this->_native))->SetBank( ClangenCli::ToNativeString(value).c_str());
}


}   //end namespace Clicegf

// PrimitiveComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
PrimitiveComponent::PrimitiveComponent( )
    :PrimitiveComponent(new cegf::PrimitiveComponent(), true)
{
}



PrimitiveComponent::PrimitiveComponent(const cegf::PrimitiveComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

PrimitiveComponent::operator PrimitiveComponent^ (const cegf::PrimitiveComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew PrimitiveComponent(const_cast<cegf::PrimitiveComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (PrimitiveComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// AudioObstacle export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AudioObstacle::AudioObstacle( )
    :AudioObstacle(new cegf::AudioObstacle(), true)
{
}



AudioObstacle::AudioObstacle(const cegf::AudioObstacle * obj, bool created_by_clr): Clicegf::PrimitiveComponent(obj, created_by_clr)
{
}

AudioObstacle::operator AudioObstacle^ (const cegf::AudioObstacle* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AudioObstacle(const_cast<cegf::AudioObstacle*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AudioObstacle^)managedObj;
    }
    else
        return nullptr;
}

bool AudioObstacle::GetIsRoom( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetIsRoom( );
}

void AudioObstacle::SetIsRoom(bool value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetIsRoom( value);
}

System::String^ AudioObstacle::GetTexture( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::AudioObstacle*>(this->_native))->GetTexture( ))).c_str());
}

void AudioObstacle::SetTexture(System::String^ value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetTexture( ClangenCli::ToNativeString(value).c_str());
}

bool AudioObstacle::GetUseForReflectionAndDiffraction( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetUseForReflectionAndDiffraction( );
}

void AudioObstacle::SetUseForReflectionAndDiffraction(bool value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetUseForReflectionAndDiffraction( value);
}

bool AudioObstacle::GetBypassPortalSubtraction( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetBypassPortalSubtraction( );
}

void AudioObstacle::SetBypassPortalSubtraction(bool value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetBypassPortalSubtraction( value);
}

bool AudioObstacle::GetIsSolid( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetIsSolid( );
}

void AudioObstacle::SetIsSolid(bool value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetIsSolid( value);
}

System::String^ AudioObstacle::GetReverbAuxBus( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::AudioObstacle*>(this->_native))->GetReverbAuxBus( ))).c_str());
}

void AudioObstacle::SetReverbAuxBus(System::String^ value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetReverbAuxBus( ClangenCli::ToNativeString(value).c_str());
}

float AudioObstacle::GetReverbLevel( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetReverbLevel( );
}

void AudioObstacle::SetReverbLevel(float value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetReverbLevel( value);
}

float AudioObstacle::GetTransmissionLoss( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetTransmissionLoss( );
}

void AudioObstacle::SetTransmissionLoss(float value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetTransmissionLoss( value);
}

float AudioObstacle::GetAuxSendLevelToSelf( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetAuxSendLevelToSelf( );
}

void AudioObstacle::SetAuxSendLevelToSelf(float value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetAuxSendLevelToSelf( value);
}

float AudioObstacle::GetPriority( )
{
    return (static_cast<cegf::AudioObstacle*>(this->_native))->GetPriority( );
}

void AudioObstacle::SetPriority(float value )
{
    (static_cast<cegf::AudioObstacle*>(this->_native))->SetPriority( value);
}


}   //end namespace Clicegf

// AudioPortal export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AudioPortal::AudioPortal( )
    :AudioPortal(new cegf::AudioPortal(), true)
{
}



AudioPortal::AudioPortal(const cegf::AudioPortal * obj, bool created_by_clr): Clicegf::PrimitiveComponent(obj, created_by_clr)
{
}

AudioPortal::operator AudioPortal^ (const cegf::AudioPortal* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AudioPortal(const_cast<cegf::AudioPortal*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AudioPortal^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Float3^ AudioPortal::GetExtent( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::AudioPortal*>(this->_native))->GetExtent( ))) , true);
}

void AudioPortal::SetExtent(Clicross::Float3^ value )
{
    (static_cast<cegf::AudioPortal*>(this->_native))->SetExtent( *((cross::Float3*)(value)));
}


}   //end namespace Clicegf

// CameraObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CameraObject::CameraObject( )
    :CameraObject(new cegf::CameraObject(), true)
{
}



CameraObject::CameraObject(const cegf::CameraObject * obj, bool created_by_clr): Clicegf::Pawn(obj, created_by_clr)
{
}

CameraObject::operator CameraObject^ (const cegf::CameraObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CameraObject(const_cast<cegf::CameraObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CameraObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CmCameraComponent::CmCameraComponent( )
    :CmCameraComponent(new cegf::CmCameraComponent(), true)
{
}



CmCameraComponent::CmCameraComponent(const cegf::CmCameraComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

CmCameraComponent::operator CmCameraComponent^ (const cegf::CmCameraComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CmCameraComponent(const_cast<cegf::CmCameraComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CmCameraComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicegf::CmCameraType CmCameraComponent::GetCmCameraType( )
{
    return (Clicegf::CmCameraType)((int)(static_cast<cegf::CmCameraComponent*>(this->_native))->GetCmCameraType( ));
}


}   //end namespace Clicegf

// CmBrain export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
unsigned int CmBrain::priority::get()
 {
	return (static_cast<cegf::CmBrain*>(this->_native))->priority;
 }
 void CmBrain::priority::set(unsigned int value )
 {
	(static_cast<cegf::CmBrain*>(this->_native))->priority = value;
 }

Clicegf::CmBrainCameraApplyType CmBrain::type::get()
 {
	return (Clicegf::CmBrainCameraApplyType)((int)(static_cast<cegf::CmBrain*>(this->_native))->type);
 }
 void CmBrain::type::set(Clicegf::CmBrainCameraApplyType value )
 {
	(static_cast<cegf::CmBrain*>(this->_native))->type = static_cast<cegf::CmBrainCameraApplyType>(value);
 }

Clicegf::CmBrainBlendType CmBrain::blend_type::get()
 {
	return (Clicegf::CmBrainBlendType)((int)(static_cast<cegf::CmBrain*>(this->_native))->blend_type);
 }
 void CmBrain::blend_type::set(Clicegf::CmBrainBlendType value )
 {
	(static_cast<cegf::CmBrain*>(this->_native))->blend_type = static_cast<cegf::CmBrainBlendType>(value);
 }

float CmBrain::blend_time::get()
 {
	return (static_cast<cegf::CmBrain*>(this->_native))->blend_time;
 }
 void CmBrain::blend_time::set(float value )
 {
	(static_cast<cegf::CmBrain*>(this->_native))->blend_time = value;
 }


//constructor export here
CmBrain::CmBrain(): CmBrain(new cegf::CmBrain(), true) {}


CmBrain::CmBrain(const cegf::CmBrain * obj, bool created_by_clr): 
    _native(const_cast<cegf::CmBrain *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CmBrain::operator CmBrain^ (const cegf::CmBrain* t)
{
    if(t)
    {
        return gcnew CmBrain(const_cast<cegf::CmBrain*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


