//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// CinemachineComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CinemachineComponent::CinemachineComponent( )
    :CinemachineComponent(new cegf::CinemachineComponent(), true)
{
}



CinemachineComponent::CinemachineComponent(const cegf::CinemachineComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

CinemachineComponent::operator CinemachineComponent^ (const cegf::CinemachineComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CinemachineComponent(const_cast<cegf::CinemachineComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CinemachineComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicegf::CmBrainCameraApplyType CinemachineComponent::GetCameraApplyType( )
{
    return (Clicegf::CmBrainCameraApplyType)((int)(static_cast<cegf::CinemachineComponent*>(this->_native))->GetCameraApplyType( ));
}


}   //end namespace Clicegf

// CmBlendUnit export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmBlendUnit::virtual_cam::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmBlendUnit*>(this->_native))->virtual_cam)) , true);
 }
 void CmBlendUnit::virtual_cam::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmBlendUnit*>(this->_native))->virtual_cam = value;
 }

Clicegf::CmBrainBlendType CmBlendUnit::blend_type::get()
 {
	return (Clicegf::CmBrainBlendType)((int)(static_cast<cegf::CmBlendUnit*>(this->_native))->blend_type);
 }
 void CmBlendUnit::blend_type::set(Clicegf::CmBrainBlendType value )
 {
	(static_cast<cegf::CmBlendUnit*>(this->_native))->blend_type = static_cast<cegf::CmBrainBlendType>(value);
 }

float CmBlendUnit::blend_time::get()
 {
	return (static_cast<cegf::CmBlendUnit*>(this->_native))->blend_time;
 }
 void CmBlendUnit::blend_time::set(float value )
 {
	(static_cast<cegf::CmBlendUnit*>(this->_native))->blend_time = value;
 }

float CmBlendUnit::hold_time::get()
 {
	return (static_cast<cegf::CmBlendUnit*>(this->_native))->hold_time;
 }
 void CmBlendUnit::hold_time::set(float value )
 {
	(static_cast<cegf::CmBlendUnit*>(this->_native))->hold_time = value;
 }


//constructor export here
CmBlendUnit::CmBlendUnit(): CmBlendUnit(new cegf::CmBlendUnit(), true) {}


CmBlendUnit::CmBlendUnit(const cegf::CmBlendUnit * obj, bool created_by_clr): 
    _native(const_cast<cegf::CmBlendUnit *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CmBlendUnit::operator CmBlendUnit^ (const cegf::CmBlendUnit* t)
{
    if(t)
    {
        return gcnew CmBlendUnit(const_cast<cegf::CmBlendUnit*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmBrainEditorBase export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmBrainEditorBase::target_camera::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmBrainEditorBase*>(this->_native))->target_camera)) , true);
 }
 void CmBrainEditorBase::target_camera::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmBrainEditorBase*>(this->_native))->target_camera = value;
 }

unsigned int CmBrainEditorBase::priority::get()
 {
	return (static_cast<cegf::CmBrainEditorBase*>(this->_native))->priority;
 }
 void CmBrainEditorBase::priority::set(unsigned int value )
 {
	(static_cast<cegf::CmBrainEditorBase*>(this->_native))->priority = value;
 }

Clicegf::CmBrainBlendType CmBrainEditorBase::blend_type::get()
 {
	return (Clicegf::CmBrainBlendType)((int)(static_cast<cegf::CmBrainEditorBase*>(this->_native))->blend_type);
 }
 void CmBrainEditorBase::blend_type::set(Clicegf::CmBrainBlendType value )
 {
	(static_cast<cegf::CmBrainEditorBase*>(this->_native))->blend_type = static_cast<cegf::CmBrainBlendType>(value);
 }

float CmBrainEditorBase::blend_time::get()
 {
	return (static_cast<cegf::CmBrainEditorBase*>(this->_native))->blend_time;
 }
 void CmBrainEditorBase::blend_time::set(float value )
 {
	(static_cast<cegf::CmBrainEditorBase*>(this->_native))->blend_time = value;
 }


//constructor export here
CmBrainEditorBase::CmBrainEditorBase(): CmBrainEditorBase(new cegf::CmBrainEditorBase(), true) {}


CmBrainEditorBase::CmBrainEditorBase(const cegf::CmBrainEditorBase * obj, bool created_by_clr): 
    _native(const_cast<cegf::CmBrainEditorBase *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CmBrainEditorBase::operator CmBrainEditorBase^ (const cegf::CmBrainEditorBase* t)
{
    if(t)
    {
        return gcnew CmBrainEditorBase(const_cast<cegf::CmBrainEditorBase*>(t));
    }
    else
        return nullptr;
}

void CmBrainEditorBase::SetToComponent(Clicegf::CinemachineComponent^ in_comp )
{
    (static_cast<cegf::CmBrainEditorBase*>(this->_native))->SetToComponent( ( cegf::CinemachineComponent* )(in_comp));
}

void CmBrainEditorBase::GetFromComponent(Clicegf::CinemachineComponent^ in_comp )
{
    (static_cast<cegf::CmBrainEditorBase*>(this->_native))->GetFromComponent( (const cegf::CinemachineComponent* const)(in_comp));
}


}   //end namespace Clicegf

// CmBrainCamSingle export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmBrainCamSingle::virtual_cam::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmBrainCamSingle*>(this->_native))->virtual_cam)) , true);
 }
 void CmBrainCamSingle::virtual_cam::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmBrainCamSingle*>(this->_native))->virtual_cam = value;
 }


//constructor export here
CmBrainCamSingle::CmBrainCamSingle(): CmBrainCamSingle(new cegf::CmBrainCamSingle(), true) {}


CmBrainCamSingle::CmBrainCamSingle(const cegf::CmBrainCamSingle * obj, bool created_by_clr): Clicegf::CmBrainEditorBase(obj, created_by_clr)
{
}

CmBrainCamSingle::operator CmBrainCamSingle^ (const cegf::CmBrainCamSingle* t)
{
    if(t)
    {
        return gcnew CmBrainCamSingle(const_cast<cegf::CmBrainCamSingle*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmBrainCamBlend export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::CmBrainCamBlend::cameras export start
	#define STLDECL_MANAGEDTYPE Clicegf::CmBlendUnit^
	#define STLDECL_NATIVETYPE cegf::CmBlendUnit
	CPP_DECLARE_STLVECTOR(CmBrainCamBlend::, camerasCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
CmBrainCamBlend::camerasCliType^ CmBrainCamBlend::cameras::get()
 {
	return (static_cast<cegf::CmBrainCamBlend*>(this->_native))->cameras;
 }
 void CmBrainCamBlend::cameras::set(CmBrainCamBlend::camerasCliType^ value )
 {
	(static_cast<cegf::CmBrainCamBlend*>(this->_native))->cameras = *value->_native;
 }

bool CmBrainCamBlend::blend_loop::get()
 {
	return (static_cast<cegf::CmBrainCamBlend*>(this->_native))->blend_loop;
 }
 void CmBrainCamBlend::blend_loop::set(bool value )
 {
	(static_cast<cegf::CmBrainCamBlend*>(this->_native))->blend_loop = value;
 }

bool CmBrainCamBlend::blend_paused::get()
 {
	return (static_cast<cegf::CmBrainCamBlend*>(this->_native))->blend_paused;
 }
 void CmBrainCamBlend::blend_paused::set(bool value )
 {
	(static_cast<cegf::CmBrainCamBlend*>(this->_native))->blend_paused = value;
 }


//constructor export here
CmBrainCamBlend::CmBrainCamBlend(): CmBrainCamBlend(new cegf::CmBrainCamBlend(), true) {}


CmBrainCamBlend::CmBrainCamBlend(const cegf::CmBrainCamBlend * obj, bool created_by_clr): Clicegf::CmBrainEditorBase(obj, created_by_clr)
{
}

CmBrainCamBlend::operator CmBrainCamBlend^ (const cegf::CmBrainCamBlend* t)
{
    if(t)
    {
        return gcnew CmBrainCamBlend(const_cast<cegf::CmBrainCamBlend*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmBrainCamMix export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmBrainCamMix::camera1::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmBrainCamMix*>(this->_native))->camera1)) , true);
 }
 void CmBrainCamMix::camera1::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmBrainCamMix*>(this->_native))->camera1 = value;
 }

Clicross::ecs::EntityIDStruct^ CmBrainCamMix::camera2::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmBrainCamMix*>(this->_native))->camera2)) , true);
 }
 void CmBrainCamMix::camera2::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmBrainCamMix*>(this->_native))->camera2 = value;
 }

float CmBrainCamMix::mix_weight::get()
 {
	return (static_cast<cegf::CmBrainCamMix*>(this->_native))->mix_weight;
 }
 void CmBrainCamMix::mix_weight::set(float value )
 {
	(static_cast<cegf::CmBrainCamMix*>(this->_native))->mix_weight = value;
 }


//constructor export here
CmBrainCamMix::CmBrainCamMix(): CmBrainCamMix(new cegf::CmBrainCamMix(), true) {}


CmBrainCamMix::CmBrainCamMix(const cegf::CmBrainCamMix * obj, bool created_by_clr): Clicegf::CmBrainEditorBase(obj, created_by_clr)
{
}

CmBrainCamMix::operator CmBrainCamMix^ (const cegf::CmBrainCamMix* t)
{
    if(t)
    {
        return gcnew CmBrainCamMix(const_cast<cegf::CmBrainCamMix*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraEditorBase export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
float CmCameraEditorBase::focal_length::get()
 {
	return (static_cast<cegf::CmCameraEditorBase*>(this->_native))->focal_length;
 }
 void CmCameraEditorBase::focal_length::set(float value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->focal_length = value;
 }

Clicegf::CmCameraPositionMode CmCameraEditorBase::position_mode::get()
 {
	return (Clicegf::CmCameraPositionMode)((int)(static_cast<cegf::CmCameraEditorBase*>(this->_native))->position_mode);
 }
 void CmCameraEditorBase::position_mode::set(Clicegf::CmCameraPositionMode value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->position_mode = static_cast<cegf::CmCameraPositionMode>(value);
 }

Clicross::Double3^ CmCameraEditorBase::position_offset::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->position_offset)) , true);
 }
 void CmCameraEditorBase::position_offset::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->position_offset = value;
 }

Clicross::ecs::EntityIDStruct^ CmCameraEditorBase::aim_entity::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_entity)) , true);
 }
 void CmCameraEditorBase::aim_entity::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_entity = value;
 }

Clicross::Double3^ CmCameraEditorBase::aim_offset::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_offset)) , true);
 }
 void CmCameraEditorBase::aim_offset::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_offset = value;
 }

Clicross::Double2^ CmCameraEditorBase::aim_damping::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_damping)) , true);
 }
 void CmCameraEditorBase::aim_damping::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->aim_damping = value;
 }

Clicross::Double2^ CmCameraEditorBase::center_position::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->center_position)) , true);
 }
 void CmCameraEditorBase::center_position::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->center_position = value;
 }

Clicross::Double2^ CmCameraEditorBase::dead_zone::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->dead_zone)) , true);
 }
 void CmCameraEditorBase::dead_zone::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->dead_zone = value;
 }

Clicross::Double2^ CmCameraEditorBase::soft_zone::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->soft_zone)) , true);
 }
 void CmCameraEditorBase::soft_zone::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->soft_zone = value;
 }

Clicross::Double2^ CmCameraEditorBase::zone_bias::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraEditorBase*>(this->_native))->zone_bias)) , true);
 }
 void CmCameraEditorBase::zone_bias::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->zone_bias = value;
 }

bool CmCameraEditorBase::confiner_keep_aim::get()
 {
	return (static_cast<cegf::CmCameraEditorBase*>(this->_native))->confiner_keep_aim;
 }
 void CmCameraEditorBase::confiner_keep_aim::set(bool value )
 {
	(static_cast<cegf::CmCameraEditorBase*>(this->_native))->confiner_keep_aim = value;
 }


//constructor export here
CmCameraEditorBase::CmCameraEditorBase(): CmCameraEditorBase(new cegf::CmCameraEditorBase(), true) {}


CmCameraEditorBase::CmCameraEditorBase(const cegf::CmCameraEditorBase * obj, bool created_by_clr): 
    _native(const_cast<cegf::CmCameraEditorBase *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CmCameraEditorBase::operator CmCameraEditorBase^ (const cegf::CmCameraEditorBase* t)
{
    if(t)
    {
        return gcnew CmCameraEditorBase(const_cast<cegf::CmCameraEditorBase*>(t));
    }
    else
        return nullptr;
}

void CmCameraEditorBase::SetToComponent(Clicegf::CmCameraComponent^ in_comp )
{
    (static_cast<cegf::CmCameraEditorBase*>(this->_native))->SetToComponent( ( cegf::CmCameraComponent* )(in_comp));
}

void CmCameraEditorBase::GetFromComponent(Clicegf::CmCameraComponent^ in_comp )
{
    (static_cast<cegf::CmCameraEditorBase*>(this->_native))->GetFromComponent( (const cegf::CmCameraComponent* const)(in_comp));
}


}   //end namespace Clicegf

// CmCameraTRACK export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmCameraTRACK::pos_entity::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmCameraTRACK*>(this->_native))->pos_entity)) , true);
 }
 void CmCameraTRACK::pos_entity::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmCameraTRACK*>(this->_native))->pos_entity = value;
 }

Clicross::Double3^ CmCameraTRACK::position_damping::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraTRACK*>(this->_native))->position_damping)) , true);
 }
 void CmCameraTRACK::position_damping::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraTRACK*>(this->_native))->position_damping = value;
 }

Clicross::Double3^ CmCameraTRACK::rotation_damping::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraTRACK*>(this->_native))->rotation_damping)) , true);
 }
 void CmCameraTRACK::rotation_damping::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraTRACK*>(this->_native))->rotation_damping = value;
 }


//constructor export here
CmCameraTRACK::CmCameraTRACK(): CmCameraTRACK(new cegf::CmCameraTRACK(), true) {}


CmCameraTRACK::CmCameraTRACK(const cegf::CmCameraTRACK * obj, bool created_by_clr): Clicegf::CmCameraEditorBase(obj, created_by_clr)
{
}

CmCameraTRACK::operator CmCameraTRACK^ (const cegf::CmCameraTRACK* t)
{
    if(t)
    {
        return gcnew CmCameraTRACK(const_cast<cegf::CmCameraTRACK*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraDOLLY export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::CmCameraDOLLY::dolly_track export start
	#define STLDECL_MANAGEDTYPE Clicross::Double4^
	#define STLDECL_NATIVETYPE cross::Double4
	CPP_DECLARE_STLVECTOR(CmCameraDOLLY::, dolly_trackCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
bool CmCameraDOLLY::always_update::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->always_update;
 }
 void CmCameraDOLLY::always_update::set(bool value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->always_update = value;
 }

Clicross::Double3^ CmCameraDOLLY::position_damping::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraDOLLY*>(this->_native))->position_damping)) , true);
 }
 void CmCameraDOLLY::position_damping::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->position_damping = value;
 }

Clicross::Double3^ CmCameraDOLLY::rotation_damping::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraDOLLY*>(this->_native))->rotation_damping)) , true);
 }
 void CmCameraDOLLY::rotation_damping::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->rotation_damping = value;
 }

Clicegf::CmCameraDollyMode CmCameraDOLLY::dolly_mode::get()
 {
	return (Clicegf::CmCameraDollyMode)((int)(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_mode);
 }
 void CmCameraDOLLY::dolly_mode::set(Clicegf::CmCameraDollyMode value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_mode = static_cast<cegf::CmCameraDollyMode>(value);
 }

Clicegf::CmCameraDollyUnit CmCameraDOLLY::dolly_unit::get()
 {
	return (Clicegf::CmCameraDollyUnit)((int)(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_unit);
 }
 void CmCameraDOLLY::dolly_unit::set(Clicegf::CmCameraDollyUnit value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_unit = static_cast<cegf::CmCameraDollyUnit>(value);
 }

float CmCameraDOLLY::dolly_distance::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_distance;
 }
 void CmCameraDOLLY::dolly_distance::set(float value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_distance = value;
 }

float CmCameraDOLLY::progress::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->progress;
 }
 void CmCameraDOLLY::progress::set(float value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->progress = value;
 }

CmCameraDOLLY::dolly_trackCliType^ CmCameraDOLLY::dolly_track::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_track;
 }
 void CmCameraDOLLY::dolly_track::set(CmCameraDOLLY::dolly_trackCliType^ value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_track = *value->_native;
 }

unsigned int CmCameraDOLLY::dolly_resolution::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_resolution;
 }
 void CmCameraDOLLY::dolly_resolution::set(unsigned int value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_resolution = value;
 }

bool CmCameraDOLLY::dolly_loop::get()
 {
	return (static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_loop;
 }
 void CmCameraDOLLY::dolly_loop::set(bool value )
 {
	(static_cast<cegf::CmCameraDOLLY*>(this->_native))->dolly_loop = value;
 }


//constructor export here
CmCameraDOLLY::CmCameraDOLLY(): CmCameraDOLLY(new cegf::CmCameraDOLLY(), true) {}


CmCameraDOLLY::CmCameraDOLLY(const cegf::CmCameraDOLLY * obj, bool created_by_clr): Clicegf::CmCameraEditorBase(obj, created_by_clr)
{
}

CmCameraDOLLY::operator CmCameraDOLLY^ (const cegf::CmCameraDOLLY* t)
{
    if(t)
    {
        return gcnew CmCameraDOLLY(const_cast<cegf::CmCameraDOLLY*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraFREELOOK export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::ecs::EntityIDStruct^ CmCameraFREELOOK::pos_entity::get()
 {
	return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cegf::CmCameraFREELOOK*>(this->_native))->pos_entity)) , true);
 }
 void CmCameraFREELOOK::pos_entity::set(Clicross::ecs::EntityIDStruct^ value )
 {
	(static_cast<cegf::CmCameraFREELOOK*>(this->_native))->pos_entity = value;
 }

Clicross::Double3^ CmCameraFREELOOK::rig_height::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_height)) , true);
 }
 void CmCameraFREELOOK::rig_height::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_height = value;
 }

Clicross::Double3^ CmCameraFREELOOK::rig_radius::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_radius)) , true);
 }
 void CmCameraFREELOOK::rig_radius::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_radius = value;
 }

Clicross::Double2^ CmCameraFREELOOK::rig_control::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_control)) , true);
 }
 void CmCameraFREELOOK::rig_control::set(Clicross::Double2^ value )
 {
	(static_cast<cegf::CmCameraFREELOOK*>(this->_native))->rig_control = value;
 }


//constructor export here
CmCameraFREELOOK::CmCameraFREELOOK(): CmCameraFREELOOK(new cegf::CmCameraFREELOOK(), true) {}


CmCameraFREELOOK::CmCameraFREELOOK(const cegf::CmCameraFREELOOK * obj, bool created_by_clr): Clicegf::CmCameraEditorBase(obj, created_by_clr)
{
}

CmCameraFREELOOK::operator CmCameraFREELOOK^ (const cegf::CmCameraFREELOOK* t)
{
    if(t)
    {
        return gcnew CmCameraFREELOOK(const_cast<cegf::CmCameraFREELOOK*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraANIMATED export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CmCameraANIMATED::CmCameraANIMATED(): CmCameraANIMATED(new cegf::CmCameraANIMATED(), true) {}


CmCameraANIMATED::CmCameraANIMATED(const cegf::CmCameraANIMATED * obj, bool created_by_clr): Clicegf::CmCameraEditorBase(obj, created_by_clr)
{
}

CmCameraANIMATED::operator CmCameraANIMATED^ (const cegf::CmCameraANIMATED* t)
{
    if(t)
    {
        return gcnew CmCameraANIMATED(const_cast<cegf::CmCameraANIMATED*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CinemachineObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CinemachineObject::CinemachineObject( )
    :CinemachineObject(new cegf::CinemachineObject(), true)
{
}



CinemachineObject::CinemachineObject(const cegf::CinemachineObject * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

CinemachineObject::operator CinemachineObject^ (const cegf::CinemachineObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CinemachineObject(const_cast<cegf::CinemachineObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CinemachineObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// CmCameraObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CmCameraObject::CmCameraObject( )
    :CmCameraObject(new cegf::CmCameraObject(), true)
{
}



CmCameraObject::CmCameraObject(const cegf::CmCameraObject * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

CmCameraObject::operator CmCameraObject^ (const cegf::CmCameraObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CmCameraObject(const_cast<cegf::CmCameraObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CmCameraObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// BasicComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
BasicComponent::BasicComponent( )
    :BasicComponent(new cegf::BasicComponent(), true)
{
}



BasicComponent::BasicComponent(const cegf::BasicComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

BasicComponent::operator BasicComponent^ (const cegf::BasicComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew BasicComponent(const_cast<cegf::BasicComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (BasicComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// PhysicsComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
PhysicsComponent::PhysicsComponent( )
    :PhysicsComponent(new cegf::PhysicsComponent(), true)
{
}



PhysicsComponent::PhysicsComponent(const cegf::PhysicsComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

PhysicsComponent::operator PhysicsComponent^ (const cegf::PhysicsComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew PhysicsComponent(const_cast<cegf::PhysicsComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (PhysicsComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// BoxComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
BoxComponent::BoxComponent( )
    :BoxComponent(new cegf::BoxComponent(), true)
{
}



BoxComponent::BoxComponent(const cegf::BoxComponent * obj, bool created_by_clr): Clicegf::PhysicsComponent(obj, created_by_clr)
{
}

BoxComponent::operator BoxComponent^ (const cegf::BoxComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew BoxComponent(const_cast<cegf::BoxComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (BoxComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Float3^ BoxComponent::GetBoxExtents( )
{
    return (Clicross::Float3^)((static_cast<cegf::BoxComponent*>(this->_native))->GetBoxExtents( ));
}

void BoxComponent::SetBoxExtents(Clicross::Float3^ inExtents )
{
    (static_cast<cegf::BoxComponent*>(this->_native))->SetBoxExtents( (const cross::Float3& )(inExtents));
}


}   //end namespace Clicegf

// CapsuleComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CapsuleComponent::CapsuleComponent( )
    :CapsuleComponent(new cegf::CapsuleComponent(), true)
{
}



CapsuleComponent::CapsuleComponent(const cegf::CapsuleComponent * obj, bool created_by_clr): Clicegf::PhysicsComponent(obj, created_by_clr)
{
}

CapsuleComponent::operator CapsuleComponent^ (const cegf::CapsuleComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CapsuleComponent(const_cast<cegf::CapsuleComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CapsuleComponent^)managedObj;
    }
    else
        return nullptr;
}

void CapsuleComponent::SetCapsuleSize(float radius, float halfHeight )
{
    (static_cast<cegf::CapsuleComponent*>(this->_native))->SetCapsuleSize( radius, halfHeight);
}

float CapsuleComponent::GetCapsuleRadius( )
{
    return (static_cast<cegf::CapsuleComponent*>(this->_native))->GetCapsuleRadius( );
}

float CapsuleComponent::GetCapsuleHalfHeight( )
{
    return (static_cast<cegf::CapsuleComponent*>(this->_native))->GetCapsuleHalfHeight( );
}


}   //end namespace Clicegf

// ModelComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ModelComponent::ModelComponent( )
    :ModelComponent(new cegf::ModelComponent(), true)
{
}



ModelComponent::ModelComponent(const cegf::ModelComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

ModelComponent::operator ModelComponent^ (const cegf::ModelComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ModelComponent(const_cast<cegf::ModelComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ModelComponent^)managedObj;
    }
    else
        return nullptr;
}

bool ModelComponent::SetModelAssetPath(System::String^ assetpath )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->SetModelAssetPath( ClangenCli::ToNativeString(assetpath).c_str());
}

System::String^ ModelComponent::GetModelAssetPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::ModelComponent*>(this->_native))->GetModelAssetPath( ))).c_str());
}

unsigned int ModelComponent::GetModelAssetLODCount( )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->GetModelAssetLODCount( );
}

bool ModelComponent::SetModelMaterialPath(System::String^ assetpath, int subModelIndex, int lodIndex )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->SetModelMaterialPath( ClangenCli::ToNativeString(assetpath).c_str(), subModelIndex, lodIndex);
}

System::String^ ModelComponent::GetModelMaterialPath(unsigned int subModelIndex, unsigned int lodIndex )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::ModelComponent*>(this->_native))->GetModelMaterialPath( subModelIndex, lodIndex))).c_str());
}

void ModelComponent::SetModelEnityDistanceCulling(Clicross::EntityDistanceCulling^ entityCulling )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetModelEnityDistanceCulling( (const cross::EntityDistanceCulling& )(entityCulling));
}

Clicross::EntityDistanceCulling^ ModelComponent::GetModelEnityDistanceCulling( )
{
    return gcnew Clicross::EntityDistanceCulling(new cross::EntityDistanceCulling(((static_cast<cegf::ModelComponent*>(this->_native))->GetModelEnityDistanceCulling( ))) , true);
}

bool ModelComponent::IsModelVisible( )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->IsModelVisible( );
}

void ModelComponent::SetModelVisible(bool isVisible )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetModelVisible( isVisible);
}

bool ModelComponent::IsSubModelVisible(unsigned int lodIndex, unsigned int subModelIndex )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->IsSubModelVisible( lodIndex, subModelIndex);
}

void ModelComponent::SetSubModelVisible(bool isVisible, unsigned int lodIndex, unsigned int subModelIndex )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetSubModelVisible( isVisible, lodIndex, subModelIndex);
}

void ModelComponent::SetModelReceiveDecals(bool value )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetModelReceiveDecals( value);
}

bool ModelComponent::GetModelReceiveDecals( )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->GetModelReceiveDecals( );
}

void ModelComponent::SetModelDirty( )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetModelDirty( );
}

unsigned int ModelComponent::GetSubModelCount(unsigned int lodIndex )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->GetSubModelCount( lodIndex);
}

void ModelComponent::SetIntersection(bool enable )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetIntersection( enable);
}

bool ModelComponent::GetIntersection( )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->GetIntersection( );
}

bool ModelComponent::IsModelAssetStreamable( )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->IsModelAssetStreamable( );
}

void ModelComponent::SetModelAssetStreamable(bool enabled )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetModelAssetStreamable( enabled);
}

void ModelComponent::AddRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->AddRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

void ModelComponent::RemoveRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->RemoveRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

void ModelComponent::SetRuntimeRenderEffect(unsigned int flags )
{
    (static_cast<cegf::ModelComponent*>(this->_native))->SetRuntimeRenderEffect( flags);
}

bool ModelComponent::HasRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    return (static_cast<cegf::ModelComponent*>(this->_native))->HasRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

Clicross::RenderEffectTag ModelComponent::GetRenderEffectTag( )
{
    return (Clicross::RenderEffectTag)((int)(static_cast<cegf::ModelComponent*>(this->_native))->GetRenderEffectTag( ));
}


}   //end namespace Clicegf

// CloudComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CloudComponent::CloudComponent( )
    :CloudComponent(new cegf::CloudComponent(), true)
{
}



CloudComponent::CloudComponent(const cegf::CloudComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

CloudComponent::operator CloudComponent^ (const cegf::CloudComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew CloudComponent(const_cast<cegf::CloudComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (CloudComponent^)managedObj;
    }
    else
        return nullptr;
}

void CloudComponent::SetPropertyCloudSettings(Clicross::CloudSetting^ setting )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudSettings( (const cross::CloudSetting& )(setting));
}

Clicross::CloudSetting^ CloudComponent::GetPropertyCloudSettings( )
{
    return gcnew Clicross::CloudSetting(new cross::CloudSetting(((static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudSettings( ))) , true);
}

void CloudComponent::SetPropertyCloudSpeed(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudSpeed( val);
}

float CloudComponent::GetPropertyCloudSpeed( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudSpeed( );
}

void CloudComponent::SetPropertyCloudNoiseFlow(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudNoiseFlow( val);
}

float CloudComponent::GetPropertyCloudNoiseFlow( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudNoiseFlow( );
}

void CloudComponent::SetPropertyCloudShadowScale(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudShadowScale( val);
}

float CloudComponent::GetPropertyCloudShadowScale( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudShadowScale( );
}

void CloudComponent::SetPropertyCloudCutoffDistance(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudCutoffDistance( val);
}

float CloudComponent::GetPropertyCloudCutoffDistance( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudCutoffDistance( );
}

void CloudComponent::SetPropertyCloudMtl(UnknowKeeper^ mtl )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudMtl( *(UnknowKeeper::get_native_with_type_for_pointer<cross::ResourceFuturePtr<cross::resource::Material>*>(mtl)));
}

void CloudComponent::SetPropertyCloudSize(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudSize( val);
}

void CloudComponent::SetPropertyEnableThunderstorm(bool val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyEnableThunderstorm( val);
}

void CloudComponent::SetPropertyCloudBottomFade(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudBottomFade( val);
}

float CloudComponent::GetPropertyCloudBottomFade( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudBottomFade( );
}

void CloudComponent::SetPropertyCoverageCloudness(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCoverageCloudness( val);
}

float CloudComponent::GetPropertyCoverageCloudness( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCoverageCloudness( );
}

void CloudComponent::SetPropertyCoverageHeightRemap(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCoverageHeightRemap( val);
}

float CloudComponent::GetPropertyCoverageHeightRemap( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCoverageHeightRemap( );
}

void CloudComponent::SetPropertyBaseSize(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyBaseSize( val);
}

float CloudComponent::GetPropertyBaseSize( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyBaseSize( );
}

void CloudComponent::SetPropertyNoiseThreshold(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyNoiseThreshold( val);
}

float CloudComponent::GetPropertyNoiseThreshold( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyNoiseThreshold( );
}

void CloudComponent::SetPropertyNoiseThresholdExtent(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyNoiseThresholdExtent( val);
}

float CloudComponent::GetPropertyNoiseThresholdExtent( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyNoiseThresholdExtent( );
}

void CloudComponent::SetPropertyBaseCurlDistortionScale(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyBaseCurlDistortionScale( val);
}

float CloudComponent::GetPropertyBaseCurlDistortionScale( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyBaseCurlDistortionScale( );
}

void CloudComponent::SetPropertyBaseCloudDistortion(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyBaseCloudDistortion( val);
}

float CloudComponent::GetPropertyBaseCloudDistortion( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyBaseCloudDistortion( );
}

void CloudComponent::SetPropertyExtraNoiseSize(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyExtraNoiseSize( val);
}

float CloudComponent::GetPropertyExtraNoiseSize( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyExtraNoiseSize( );
}

void CloudComponent::SetPropertyExtraNoiseIntensity(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyExtraNoiseIntensity( val);
}

float CloudComponent::GetPropertyExtraNoiseIntensity( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyExtraNoiseIntensity( );
}

void CloudComponent::SetPropertyExtraNoiseThreshold(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyExtraNoiseThreshold( val);
}

float CloudComponent::GetPropertyExtraNoiseThreshold( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyExtraNoiseThreshold( );
}

void CloudComponent::SetPropertyExtraNoiseThresholdExtent(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyExtraNoiseThresholdExtent( val);
}

float CloudComponent::GetPropertyExtraNoiseThresholdExtent( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyExtraNoiseThresholdExtent( );
}

void CloudComponent::SetPropertyDetailSize(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyDetailSize( val);
}

float CloudComponent::GetPropertyDetailSize( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyDetailSize( );
}

void CloudComponent::SetPropertyCurlDistortionScale(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCurlDistortionScale( val);
}

float CloudComponent::GetPropertyCurlDistortionScale( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCurlDistortionScale( );
}

void CloudComponent::SetPropertyCloudDistortion(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudDistortion( val);
}

float CloudComponent::GetPropertyCloudDistortion( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudDistortion( );
}

void CloudComponent::SetPropertyDetailWispyBillowyGradient(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyDetailWispyBillowyGradient( val);
}

float CloudComponent::GetPropertyDetailWispyBillowyGradient( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyDetailWispyBillowyGradient( );
}

void CloudComponent::SetPropertyDetailAffect(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyDetailAffect( val);
}

float CloudComponent::GetPropertyDetailAffect( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyDetailAffect( );
}

void CloudComponent::SetPropertySunAttenuation(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertySunAttenuation( val);
}

float CloudComponent::GetPropertySunAttenuation( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertySunAttenuation( );
}

void CloudComponent::SetPropertySunIntensity(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertySunIntensity( val);
}

float CloudComponent::GetPropertySunIntensity( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertySunIntensity( );
}

void CloudComponent::SetPropertySunSaturation(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertySunSaturation( val);
}

float CloudComponent::GetPropertySunSaturation( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertySunSaturation( );
}

void CloudComponent::SetPropertyMultiscatteringIntensity(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyMultiscatteringIntensity( val);
}

float CloudComponent::GetPropertyMultiscatteringIntensity( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyMultiscatteringIntensity( );
}

void CloudComponent::SetPropertyAttenuationCoefficient(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyAttenuationCoefficient( val);
}

float CloudComponent::GetPropertyAttenuationCoefficient( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyAttenuationCoefficient( );
}

void CloudComponent::SetPropertyAmbientIntensityTop(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyAmbientIntensityTop( val);
}

float CloudComponent::GetPropertyAmbientIntensityTop( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyAmbientIntensityTop( );
}

void CloudComponent::SetPropertyAmbientIntensityBottom(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyAmbientIntensityBottom( val);
}

float CloudComponent::GetPropertyAmbientIntensityBottom( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyAmbientIntensityBottom( );
}

void CloudComponent::SetPropertyAmbientSaturation(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyAmbientSaturation( val);
}

float CloudComponent::GetPropertyAmbientSaturation( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyAmbientSaturation( );
}

void CloudComponent::SetPropertyPhaseMainOctaveWeight(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyPhaseMainOctaveWeight( val);
}

float CloudComponent::GetPropertyPhaseMainOctaveWeight( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyPhaseMainOctaveWeight( );
}

void CloudComponent::SetPropertyPhaseSecondaryOctaveWeight(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyPhaseSecondaryOctaveWeight( val);
}

float CloudComponent::GetPropertyPhaseSecondaryOctaveWeight( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyPhaseSecondaryOctaveWeight( );
}

void CloudComponent::SetPropertyCloudAnisotropyMin(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudAnisotropyMin( val);
}

float CloudComponent::GetPropertyCloudAnisotropyMin( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudAnisotropyMin( );
}

void CloudComponent::SetPropertyCloudAnisotropyMax(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudAnisotropyMax( val);
}

float CloudComponent::GetPropertyCloudAnisotropyMax( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyCloudAnisotropyMax( );
}

void CloudComponent::SetPropertyDotVLMin(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyDotVLMin( val);
}

float CloudComponent::GetPropertyDotVLMin( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyDotVLMin( );
}

void CloudComponent::SetPropertyDotVLMax(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyDotVLMax( val);
}

float CloudComponent::GetPropertyDotVLMax( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyDotVLMax( );
}

void CloudComponent::SetPropertyShadowBias(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowBias( val);
}

float CloudComponent::GetPropertyShadowBias( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowBias( );
}

void CloudComponent::SetPropertyShadowIntensity(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowIntensity( val);
}

float CloudComponent::GetPropertyShadowIntensity( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowIntensity( );
}

void CloudComponent::SetPropertyShadowSubtraction(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowSubtraction( val);
}

float CloudComponent::GetPropertyShadowSubtraction( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowSubtraction( );
}

void CloudComponent::SetPropertyShadowContrast(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowContrast( val);
}

float CloudComponent::GetPropertyShadowContrast( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowContrast( );
}

void CloudComponent::SetPropertyShadowMultiplier(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowMultiplier( val);
}

float CloudComponent::GetPropertyShadowMultiplier( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowMultiplier( );
}

void CloudComponent::SetPropertyShadowApScale(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyShadowApScale( val);
}

float CloudComponent::GetPropertyShadowApScale( )
{
    return (static_cast<cegf::CloudComponent*>(this->_native))->GetPropertyShadowApScale( );
}

void CloudComponent::SetPropertyMipNearRatio(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyMipNearRatio( val);
}

void CloudComponent::SetPropertyMipFarRatio(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyMipFarRatio( val);
}

void CloudComponent::SetPropertyMipUVFarScale(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyMipUVFarScale( val);
}

void CloudComponent::SetPropertyMipContrast(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyMipContrast( val);
}

void CloudComponent::SetPropertyCloudCoverage(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudCoverage( val);
}

void CloudComponent::SetPropertyCloudDensity(float val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyCloudDensity( val);
}

void CloudComponent::SetPropertyRefreshCloudHistory(bool val )
{
    (static_cast<cegf::CloudComponent*>(this->_native))->SetPropertyRefreshCloudHistory( val);
}


}   //end namespace Clicegf

// ControllableUnitComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ControllableUnitComponent::ControllableUnitComponent( )
    :ControllableUnitComponent(new cegf::ControllableUnitComponent(), true)
{
}



ControllableUnitComponent::ControllableUnitComponent(const cegf::ControllableUnitComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

ControllableUnitComponent::operator ControllableUnitComponent^ (const cegf::ControllableUnitComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ControllableUnitComponent(const_cast<cegf::ControllableUnitComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ControllableUnitComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicross::ControllableUnitType ControllableUnitComponent::GetControllableUnitType( )
{
    return (Clicross::ControllableUnitType)((int)(static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetControllableUnitType( ));
}

void ControllableUnitComponent::SetControllableUnitType(Clicross::ControllableUnitType type )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetControllableUnitType( static_cast<cross::ControllableUnitType>(type));
}

System::String^ ControllableUnitComponent::GetControllableUnitJson( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetControllableUnitJson( ))).c_str());
}

void ControllableUnitComponent::SetControllableUnitJson(System::String^ inJson )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetControllableUnitJson( ClangenCli::ToNativeString(inJson).c_str());
}

bool ControllableUnitComponent::CreateCurveController(Clicross::UniqueString^ ValueName, Clicross::UniqueString^ CurveType )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->CreateCurveController( (const cross::UniqueString& )(ValueName), (const cross::UniqueString& )(CurveType));
}

bool ControllableUnitComponent::RemoveCurveController(Clicross::UniqueString^ inValueName )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->RemoveCurveController( (const cross::UniqueString& )(inValueName));
}

bool ControllableUnitComponent::GetCurveControllerTrack(Clicross::UniqueString^ inValueName, Clicross::UniqueString^ inTrackName, Clicross::FloatCurveTrack^ outTrack )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetCurveControllerTrack( (const cross::UniqueString& )(inValueName), (const cross::UniqueString& )(inTrackName), ( cross::FloatCurveTrack& )(outTrack));
}

bool ControllableUnitComponent::GetCurveControllerList(Clicross::UniqueString^ inValueName, Clicross::FloatCurveList^ outList )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetCurveControllerList( (const cross::UniqueString& )(inValueName), ( cross::FloatCurveList& )(outList));
}

bool ControllableUnitComponent::SetCurveControllerTrack(Clicross::UniqueString^ inValueName, Clicross::UniqueString^ inTrackName, Clicross::FloatCurveTrack^ inTrack )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerTrack( (const cross::UniqueString& )(inValueName), (const cross::UniqueString& )(inTrackName), (const cross::FloatCurveTrack& )(inTrack));
}

bool ControllableUnitComponent::SetCurveControllerInfo(Clicross::UniqueString^ inValueName, Clicross::FloatCurveListInfo^ inCurveInfo )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerInfo( (const cross::UniqueString& )(inValueName), (const cross::FloatCurveListInfo& )(inCurveInfo));
}

bool ControllableUnitComponent::SetCurveControllerStartTime(float startTime, System::String^ path, bool bFormFile )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerStartTime( startTime, ClangenCli::ToNativeString(path).c_str(), bFormFile);
}

bool ControllableUnitComponent::SetCurveControllerCursor(float inCursor )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerCursor( inCursor);
}

bool ControllableUnitComponent::SetCurveControllerDeltaTime(float deltaTime )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerDeltaTime( deltaTime);
}

bool ControllableUnitComponent::SetCurveControllerPlayOnEditor(bool playOnEditor )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerPlayOnEditor( playOnEditor);
}

bool ControllableUnitComponent::SetCurveControllerPlayRate(float pr )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControllerPlayRate( pr);
}

bool ControllableUnitComponent::GetCurveControllerPlayRate([System::Runtime::InteropServices::Out] float pr )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetCurveControllerPlayRate( pr);
}

void ControllableUnitComponent::CreateCurveControl( )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->CreateCurveControl( );
}

void ControllableUnitComponent::ResetCurveControl( )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ResetCurveControl( );
}

void ControllableUnitComponent::StartCurveControl( )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->StartCurveControl( );
}

void ControllableUnitComponent::PauseCurveControl( )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->PauseCurveControl( );
}

bool ControllableUnitComponent::IsPlayingCurveControl( )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->IsPlayingCurveControl( );
}

void ControllableUnitComponent::SetCurveControlResPath(System::String^ path )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControlResPath( ClangenCli::ToNativeString(path).c_str());
}

void ControllableUnitComponent::SetCurveControlPlaySpeed(float speed )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->SetCurveControlPlaySpeed( speed);
}

void ControllableUnitComponent::CreateCurveControlData(float duration, float fps, float playRate )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->CreateCurveControlData( duration, fps, playRate);
}

void ControllableUnitComponent::AddCurveControlDataItem(System::String^ valueName, System::String^ curveType )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->AddCurveControlDataItem( ClangenCli::ToNativeString(valueName).c_str(), ClangenCli::ToNativeString(curveType).c_str());
}

void ControllableUnitComponent::RemoveCurveControlDataItem(System::String^ valueName )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->RemoveCurveControlDataItem( ClangenCli::ToNativeString(valueName).c_str());
}

void ControllableUnitComponent::AddCurveControlKey(System::String^ valueName, System::String^ trackName, Clicross::FloatCurveKey^ key )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->AddCurveControlKey( ClangenCli::ToNativeString(valueName).c_str(), ClangenCli::ToNativeString(trackName).c_str(), ( cross::FloatCurveKey& )(key));
}

void ControllableUnitComponent::RemoveCurveControlKey(System::String^ valueName, System::String^ trackName, Clicross::FloatCurveKey^ key )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->RemoveCurveControlKey( ClangenCli::ToNativeString(valueName).c_str(), ClangenCli::ToNativeString(trackName).c_str(), ( cross::FloatCurveKey& )(key));
}

void ControllableUnitComponent::ControllableCameraSetOperation(int op, float inValue )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraSetOperation( op, inValue);
}

void ControllableUnitComponent::ControllableCameraSetFocusonOffset(Clicross::Float3^ inValue )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraSetFocusonOffset( *((cross::Float3*)(inValue)));
}

bool ControllableUnitComponent::ControllableCameraIsFirstPersonPersMode( )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraIsFirstPersonPersMode( );
}

int ControllableUnitComponent::ControllableCameraGetMode( )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraGetMode( );
}

float ControllableUnitComponent::ControllableCameraGetFreeSpeedGearValue(int gear )
{
    return (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraGetFreeSpeedGearValue( gear);
}

void ControllableUnitComponent::ControllableCameraSetFreeSpeedGearValue(int gear, float value )
{
    (static_cast<cegf::ControllableUnitComponent*>(this->_native))->ControllableCameraSetFreeSpeedGearValue( gear, value);
}

UnknowKeeper^ ControllableUnitComponent::GetControllableCamera( )
{
    return UnknownKeeperCast((static_cast<cegf::ControllableUnitComponent*>(this->_native))->GetControllableCamera( ));
}


}   //end namespace Clicegf

// DecalComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
DecalComponent::DecalComponent( )
    :DecalComponent(new cegf::DecalComponent(), true)
{
}



DecalComponent::DecalComponent(const cegf::DecalComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

DecalComponent::operator DecalComponent^ (const cegf::DecalComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew DecalComponent(const_cast<cegf::DecalComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (DecalComponent^)managedObj;
    }
    else
        return nullptr;
}

void DecalComponent::SetDecalEnable(bool enable )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetDecalEnable( enable);
}

void DecalComponent::SetDecalVisible(bool Visible )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetDecalVisible( Visible);
}

bool DecalComponent::GetDecalEnable( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetDecalEnable( );
}

System::String^ DecalComponent::GetDecalMaterial( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::DecalComponent*>(this->_native))->GetDecalMaterial( ))).c_str());
}

void DecalComponent::SetDecalMaterial(System::String^ material )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetDecalMaterial( ClangenCli::ToNativeString(material).c_str());
}

unsigned int DecalComponent::GetSortOrder( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetSortOrder( );
}

void DecalComponent::SetSortOrder(unsigned int sortOrder )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetSortOrder( sortOrder);
}

float DecalComponent::GetFadeScreenSize( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetFadeScreenSize( );
}

void DecalComponent::SetFadeScreenSize(float fadeScreenSize )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetFadeScreenSize( fadeScreenSize);
}

float DecalComponent::GetFadeStartDelay( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetFadeStartDelay( );
}

void DecalComponent::SetFadeStartDelay(float fadeStartDelay )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetFadeStartDelay( fadeStartDelay);
}

float DecalComponent::GetFadeDuration( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetFadeDuration( );
}

void DecalComponent::SetFadeDuration(float fadeDuration )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetFadeDuration( fadeDuration);
}

float DecalComponent::GetFadeInDuration( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetFadeInDuration( );
}

void DecalComponent::SetFadeInDuration(float fadeInDuration )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetFadeInDuration( fadeInDuration);
}

float DecalComponent::GetFadeInStartDelay( )
{
    return (static_cast<cegf::DecalComponent*>(this->_native))->GetFadeInStartDelay( );
}

void DecalComponent::SetFadeInStartDelay(float fadeInStartDelay )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetFadeInStartDelay( fadeInStartDelay);
}

Clicross::Float3^ DecalComponent::GetDecalSize( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::DecalComponent*>(this->_native))->GetDecalSize( ))) , true);
}

void DecalComponent::SetDecalSize(Clicross::Float3^ decalSize )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetDecalSize( (const cross::Float3& )(decalSize));
}

UnknowKeeper^ DecalComponent::GetDecalConfig( )
{
    return UnknownKeeperCast((static_cast<cegf::DecalComponent*>(this->_native))->GetDecalConfig( ));
}

void DecalComponent::SetDecalConfig(UnknowKeeper^ config )
{
    (static_cast<cegf::DecalComponent*>(this->_native))->SetDecalConfig( (UnknowKeeper::get_native_with_type_for_reference<cross::DecalConfig& >(config)));
}


}   //end namespace Clicegf

// FoliageComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
FoliageComponent::FoliageComponent( )
    :FoliageComponent(new cegf::FoliageComponent(), true)
{
}



FoliageComponent::FoliageComponent(const cegf::FoliageComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

FoliageComponent::operator FoliageComponent^ (const cegf::FoliageComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew FoliageComponent(const_cast<cegf::FoliageComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (FoliageComponent^)managedObj;
    }
    else
        return nullptr;
}

void FoliageComponent::SetIntersection(bool enable )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetIntersection( enable);
}

bool FoliageComponent::GetIntersection( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetIntersection( );
}

void FoliageComponent::SetEnable(bool enable )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetEnable( enable);
}

bool FoliageComponent::GetEnable( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetEnable( );
}

void FoliageComponent::SetGlobalScale(float globalScale )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetGlobalScale( globalScale);
}

float FoliageComponent::GetGlobalScale( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetGlobalScale( );
}

void FoliageComponent::SetGlobalRangeScale(float globalScale )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetGlobalRangeScale( globalScale);
}

float FoliageComponent::GetGlobalRangeScale( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetGlobalRangeScale( );
}

void FoliageComponent::SetMaxRandomCulling(float maxRandomculling )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetMaxRandomCulling( maxRandomculling);
}

float FoliageComponent::GetMaxRandomCulling( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetMaxRandomCulling( );
}

void FoliageComponent::SetDensity(float density )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetDensity( density);
}

float FoliageComponent::GetDensity( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetDensity( );
}

void FoliageComponent::SetPCGReservedCapacity(unsigned int count )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetPCGReservedCapacity( count);
}

unsigned int FoliageComponent::GetPCGReservedCapacity( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetPCGReservedCapacity( );
}

bool FoliageComponent::GetSubmeshVisible(unsigned int submeshIndex )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetSubmeshVisible( submeshIndex);
}

void FoliageComponent::SetSubmeshVisible(unsigned int submeshIndex, bool visible )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetSubmeshVisible( submeshIndex, visible);
}

void FoliageComponent::SetLightCastShadow(bool castShadow )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetLightCastShadow( castShadow);
}

bool FoliageComponent::GetLightCastShadow( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetLightCastShadow( );
}

void FoliageComponent::SetEditorPrefabResource(System::String^ prefabPath )
{
    (static_cast<cegf::FoliageComponent*>(this->_native))->SetEditorPrefabResource( ClangenCli::ToNativeString(prefabPath).c_str());
}

unsigned long long FoliageComponent::GetInstanceCount( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetInstanceCount( );
}

unsigned long long FoliageComponent::GetInstanceLightCount( )
{
    return (static_cast<cegf::FoliageComponent*>(this->_native))->GetInstanceLightCount( );
}

int FoliageComponent::GetFoliageGenerationType( )
{
    return ((int)(static_cast<cegf::FoliageComponent*>(this->_native))->GetFoliageGenerationType( ));
}


}   //end namespace Clicegf

// InstancedStaticModelComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InstancedStaticModelComponent::InstancedStaticModelComponent( )
    :InstancedStaticModelComponent(new cegf::InstancedStaticModelComponent(), true)
{
}



InstancedStaticModelComponent::InstancedStaticModelComponent(const cegf::InstancedStaticModelComponent * obj, bool created_by_clr): Clicegf::ModelComponent(obj, created_by_clr)
{
}

InstancedStaticModelComponent::operator InstancedStaticModelComponent^ (const cegf::InstancedStaticModelComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InstancedStaticModelComponent(const_cast<cegf::InstancedStaticModelComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InstancedStaticModelComponent^)managedObj;
    }
    else
        return nullptr;
}

bool InstancedStaticModelComponent::SetInstanceDataResourcePath(System::String^ resourcePath )
{
    return (static_cast<cegf::InstancedStaticModelComponent*>(this->_native))->SetInstanceDataResourcePath( ClangenCli::ToNativeString(resourcePath).c_str());
}

System::String^ InstancedStaticModelComponent::GetInstanceDataResourcePath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::InstancedStaticModelComponent*>(this->_native))->GetInstanceDataResourcePath( ))).c_str());
}


}   //end namespace Clicegf

// HierachicalInstancedStaticModelComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
HierachicalInstancedStaticModelComponent::HierachicalInstancedStaticModelComponent( )
    :HierachicalInstancedStaticModelComponent(new cegf::HierachicalInstancedStaticModelComponent(), true)
{
}



HierachicalInstancedStaticModelComponent::HierachicalInstancedStaticModelComponent(const cegf::HierachicalInstancedStaticModelComponent * obj, bool created_by_clr): Clicegf::InstancedStaticModelComponent(obj, created_by_clr)
{
}

HierachicalInstancedStaticModelComponent::operator HierachicalInstancedStaticModelComponent^ (const cegf::HierachicalInstancedStaticModelComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HierachicalInstancedStaticModelComponent(const_cast<cegf::HierachicalInstancedStaticModelComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HierachicalInstancedStaticModelComponent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// LightComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
LightComponent::LightComponent( )
    :LightComponent(new cegf::LightComponent(), true)
{
}



LightComponent::LightComponent(const cegf::LightComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

LightComponent::operator LightComponent^ (const cegf::LightComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew LightComponent(const_cast<cegf::LightComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (LightComponent^)managedObj;
    }
    else
        return nullptr;
}

void LightComponent::SetLightType(Clicross::LightType val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightType( static_cast<cross::LightType>(val));
}

Clicross::LightType LightComponent::GetLightType( )
{
    return (Clicross::LightType)((int)(static_cast<cegf::LightComponent*>(this->_native))->GetLightType( ));
}

void LightComponent::SetLightColor(Clicross::Float3^ val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightColor( *((cross::Float3*)(val)));
}

Clicross::Float3^ LightComponent::GetLightColor( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::LightComponent*>(this->_native))->GetLightColor( ))) , true);
}

void LightComponent::SetLightIntensity(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightIntensity( val);
}

float LightComponent::GetLightIntensity( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightIntensity( );
}

void LightComponent::SetLightPrtIntensity(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightPrtIntensity( val);
}

float LightComponent::GetLightPrtIntensity( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightPrtIntensity( );
}

void LightComponent::SetLightSpecularIntensity(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSpecularIntensity( val);
}

float LightComponent::GetLightSpecularIntensity( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSpecularIntensity( );
}

void LightComponent::SetLightVolumetricFactor(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightVolumetricFactor( val);
}

float LightComponent::GetLightVolumetricFactor( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightVolumetricFactor( );
}

void LightComponent::SetLightSourceAngleOrRadius(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSourceAngleOrRadius( val);
}

float LightComponent::GetLightSourceAngleOrRadius( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSourceAngleOrRadius( );
}

void LightComponent::SetLightSoftSourceAngleOrRadius(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSoftSourceAngleOrRadius( val);
}

float LightComponent::GetLightSoftSourceAngleOrRadius( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSoftSourceAngleOrRadius( );
}

void LightComponent::SetLightSourceLength(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSourceLength( val);
}

float LightComponent::GetLightSourceLength( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSourceLength( );
}

void LightComponent::SetLightRange(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightRange( val);
}

float LightComponent::GetLightRange( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightRange( );
}

void LightComponent::SetLightSourceWidth(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSourceWidth( val);
}

float LightComponent::GetLightSourceWidth( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSourceWidth( );
}

void LightComponent::SetLightSourceHeight(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSourceHeight( val);
}

float LightComponent::GetLightSourceHeight( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSourceHeight( );
}

void LightComponent::SetLightBarnDoorAngle(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightBarnDoorAngle( val);
}

float LightComponent::GetLightBarnDoorAngle( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightBarnDoorAngle( );
}

void LightComponent::SetLightBarnDoorLength(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightBarnDoorLength( val);
}

float LightComponent::GetLightBarnDoorLength( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightBarnDoorLength( );
}

void LightComponent::SetLightCastShadow(bool val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightCastShadow( val);
}

bool LightComponent::GetLightCastShadow( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightCastShadow( );
}

void LightComponent::SetLightCastScreenSpaceShadow(bool val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightCastScreenSpaceShadow( val);
}

bool LightComponent::GetLightCastScreenSpaceShadow( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightCastScreenSpaceShadow( );
}

void LightComponent::SetLightShadowStrength(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightShadowStrength( val);
}

float LightComponent::GetLightShadowStrength( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightShadowStrength( );
}

void LightComponent::SetLightMode(Clicross::LightMode val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightMode( static_cast<cross::LightMode>(val));
}

Clicross::LightMode LightComponent::GetLightMode( )
{
    return (Clicross::LightMode)((int)(static_cast<cegf::LightComponent*>(this->_native))->GetLightMode( ));
}

void LightComponent::SetLightPriority(Clicross::LightPriority val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightPriority( static_cast<cross::LightPriority>(val));
}

Clicross::LightPriority LightComponent::GetLightPriority( )
{
    return (Clicross::LightPriority)((int)(static_cast<cegf::LightComponent*>(this->_native))->GetLightPriority( ));
}

void LightComponent::SetLightShadowType(Clicross::LightShadowType val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightShadowType( static_cast<cross::LightShadowType>(val));
}

Clicross::LightShadowType LightComponent::GetLightShadowType( )
{
    return (Clicross::LightShadowType)((int)(static_cast<cegf::LightComponent*>(this->_native))->GetLightShadowType( ));
}

void LightComponent::SetLightShadowAmount(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightShadowAmount( val);
}

float LightComponent::GetLightShadowAmount( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightShadowAmount( );
}

void LightComponent::SetLightShadowBias(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightShadowBias( val);
}

float LightComponent::GetLightShadowBias( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightShadowBias( );
}

void LightComponent::SetLightShadowSlopeBias(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightShadowSlopeBias( val);
}

float LightComponent::GetLightShadowSlopeBias( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightShadowSlopeBias( );
}

void LightComponent::SetLightVarianceBiasVSM(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightVarianceBiasVSM( val);
}

float LightComponent::GetLightVarianceBiasVSM( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightVarianceBiasVSM( );
}

void LightComponent::SetLightLightLeakBiasVSM(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightLightLeakBiasVSM( val);
}

float LightComponent::GetLightLightLeakBiasVSM( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightLightLeakBiasVSM( );
}

void LightComponent::SetLightFilterSizePCF(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightFilterSizePCF( val);
}

float LightComponent::GetLightFilterSizePCF( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightFilterSizePCF( );
}

void LightComponent::SetLightSoftnessPCSS(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSoftnessPCSS( val);
}

float LightComponent::GetLightSoftnessPCSS( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSoftnessPCSS( );
}

void LightComponent::SetLightSampleCountPCSS(float val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSampleCountPCSS( val);
}

float LightComponent::GetLightSampleCountPCSS( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSampleCountPCSS( );
}

void LightComponent::SetLightAtmosphereLightConfig(Clicross::AtmosphereLightConfig^ val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightAtmosphereLightConfig( *((cross::AtmosphereLightConfig*)(val)));
}

Clicross::AtmosphereLightConfig^ LightComponent::GetLightAtmosphereLightConfig( )
{
    return gcnew Clicross::AtmosphereLightConfig(new cross::AtmosphereLightConfig(((static_cast<cegf::LightComponent*>(this->_native))->GetLightAtmosphereLightConfig( ))) , true);
}

void LightComponent::SetLightEnable(bool val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightEnable( val);
}

bool LightComponent::GetLightEnable( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightEnable( );
}

void LightComponent::SetLightInnerConeAngle(float angle )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightInnerConeAngle( angle);
}

float LightComponent::GetLightInnerConeAngleInDegree( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightInnerConeAngleInDegree( );
}

void LightComponent::SetLightOuterConeAngle(float angle )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightOuterConeAngle( angle);
}

float LightComponent::GetLightOuterConeAngleInDegree( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightOuterConeAngleInDegree( );
}

void LightComponent::SetLightConeFadeIntensity(float intensity )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightConeFadeIntensity( intensity);
}

float LightComponent::GetLightConeFadeIntensity( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightConeFadeIntensity( );
}

void LightComponent::SetLightConeOverFlowLength(float intensity )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightConeOverFlowLength( intensity);
}

float LightComponent::GetLightConeOverFlowLength( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightConeOverFlowLength( );
}

void LightComponent::SetLightSpotDistanceExp(float distance_exp )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightSpotDistanceExp( distance_exp);
}

float LightComponent::GetLightSpotDistanceExp( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightSpotDistanceExp( );
}

void LightComponent::SetLightRenderingLayerMask(unsigned int val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightRenderingLayerMask( val);
}

unsigned int LightComponent::GetLightRenderingLayerMask( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightRenderingLayerMask( );
}

void LightComponent::SetLightEnableTransmittance(bool val )
{
    (static_cast<cegf::LightComponent*>(this->_native))->SetLightEnableTransmittance( val);
}

bool LightComponent::GetLightEnableTransmittance( )
{
    return (static_cast<cegf::LightComponent*>(this->_native))->GetLightEnableTransmittance( );
}


}   //end namespace Clicegf

// ParticleComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ParticleComponent::ParticleComponent( )
    :ParticleComponent(new cegf::ParticleComponent(), true)
{
}



ParticleComponent::ParticleComponent(const cegf::ParticleComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

ParticleComponent::operator ParticleComponent^ (const cegf::ParticleComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ParticleComponent(const_cast<cegf::ParticleComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ParticleComponent^)managedObj;
    }
    else
        return nullptr;
}

void ParticleComponent::ActivatePS( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->ActivatePS( );
}

void ParticleComponent::DeactivatePS( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->DeactivatePS( );
}

void ParticleComponent::OnResourceChanged(Clicross::fx::ParticleResourceEvent event, unsigned int index, System::String^ path )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->OnResourceChanged( static_cast<cross::fx::ParticleResourceEvent>(event), index, ClangenCli::ToNativeString(path).c_str());
}

void ParticleComponent::InitializeParticleSystem( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->InitializeParticleSystem( );
}

void ParticleComponent::StartParticleSystem( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->StartParticleSystem( );
}

void ParticleComponent::PauseParticleSystem( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->PauseParticleSystem( );
}

void ParticleComponent::StopParticleSystem( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->StopParticleSystem( );
}

void ParticleComponent::RestartParticleSystem( )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->RestartParticleSystem( );
}

bool ParticleComponent::IsSystemCompleted(bool waitParticleClear )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->IsSystemCompleted( waitParticleClear);
}

bool ParticleComponent::IsSystemRunning( )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->IsSystemRunning( );
}

void ParticleComponent::SetRandomSeed(unsigned int seed, bool unifiedSeed )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->SetRandomSeed( seed, unifiedSeed);
}

void ParticleComponent::SetParticleInitVar(int emitterIndex, System::String^ varName, Clicross::MinMaxCurve^ varValue )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->SetParticleInitVar( emitterIndex, ClangenCli::ToNativeString(varName).c_str(), (const cross::MinMaxCurve& )(varValue));
}

float ParticleComponent::GetParticleParamFloat(int emitterIndex, unsigned int particleIndex, System::String^ varName )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->GetParticleParamFloat( emitterIndex, particleIndex, ClangenCli::ToNativeString(varName).c_str());
}

unsigned int ParticleComponent::GetTotalParticleCount( )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->GetTotalParticleCount( );
}

unsigned int ParticleComponent::GetActiveParticleCount(unsigned int emitterIndex )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->GetActiveParticleCount( emitterIndex);
}

System::String^ ParticleComponent::GetSystemResourcePath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::ParticleComponent*>(this->_native))->GetSystemResourcePath( ))).c_str());
}

unsigned long long ParticleComponent::GetParticleSystemID( )
{
    return (static_cast<cegf::ParticleComponent*>(this->_native))->GetParticleSystemID( );
}

void ParticleComponent::DisableEmitter(unsigned int emitterIndex )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->DisableEmitter( emitterIndex);
}

void ParticleComponent::SetSimulationCamera(Clicegf::GameObject^ camera )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->SetSimulationCamera( ( cegf::GameObject* )(camera));
}

void ParticleComponent::SetSystemResourcePath(System::String^ path )
{
    (static_cast<cegf::ParticleComponent*>(this->_native))->SetSystemResourcePath( ClangenCli::ToNativeString(path).c_str());
}


}   //end namespace Clicegf

// PostProcessVolumeComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
PostProcessVolumeComponent::PostProcessVolumeComponent( )
    :PostProcessVolumeComponent(new cegf::PostProcessVolumeComponent(), true)
{
}



PostProcessVolumeComponent::PostProcessVolumeComponent(const cegf::PostProcessVolumeComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

PostProcessVolumeComponent::operator PostProcessVolumeComponent^ (const cegf::PostProcessVolumeComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew PostProcessVolumeComponent(const_cast<cegf::PostProcessVolumeComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (PostProcessVolumeComponent^)managedObj;
    }
    else
        return nullptr;
}

void PostProcessVolumeComponent::SetEnableHistogramExposure(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableHistogramExposure( val);
}

bool PostProcessVolumeComponent::IsEnabledHistogramExposure( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledHistogramExposure( );
}

void PostProcessVolumeComponent::SetEnableManualExposure(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableManualExposure( val);
}

bool PostProcessVolumeComponent::IsEnalbedManualExposure( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnalbedManualExposure( );
}

void PostProcessVolumeComponent::SetEnableLocalExposure(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableLocalExposure( val);
}

bool PostProcessVolumeComponent::IsEnabledLocalExposure( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledLocalExposure( );
}

void PostProcessVolumeComponent::SetEnableBloom(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableBloom( val);
}

bool PostProcessVolumeComponent::IsEnabledBloom( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledBloom( );
}

void PostProcessVolumeComponent::SetEnableChromaticAberration(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableChromaticAberration( val);
}

bool PostProcessVolumeComponent::IsEnabledChromaticAberration( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledChromaticAberration( );
}

void PostProcessVolumeComponent::SetEnableVignette(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableVignette( val);
}

bool PostProcessVolumeComponent::IsEnabledVignette( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledVignette( );
}

void PostProcessVolumeComponent::SetEnableTonemapSetting(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableTonemapSetting( val);
}

bool PostProcessVolumeComponent::IsEnabledTonemapSetting( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledTonemapSetting( );
}

void PostProcessVolumeComponent::SetEnableLensFlare(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableLensFlare( val);
}

bool PostProcessVolumeComponent::IsEnabledLensFlare( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledLensFlare( );
}

void PostProcessVolumeComponent::SetEnableDOF(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableDOF( val);
}

bool PostProcessVolumeComponent::IsEnabledDOF( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledDOF( );
}

void PostProcessVolumeComponent::SetEnableMotionBlur(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableMotionBlur( val);
}

bool PostProcessVolumeComponent::IsEnabledMotionBlur( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledMotionBlur( );
}

void PostProcessVolumeComponent::SetEnableScreenBlur(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableScreenBlur( val);
}

bool PostProcessVolumeComponent::IsEnabledScreenBlur( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledScreenBlur( );
}

void PostProcessVolumeComponent::SetEnableRain(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableRain( val);
}

bool PostProcessVolumeComponent::IsEnabledRain( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledRain( );
}

void PostProcessVolumeComponent::SetEnableWind(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableWind( val);
}

bool PostProcessVolumeComponent::IsEnabledWind( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledWind( );
}

void PostProcessVolumeComponent::SetEnableSnow(bool val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnableSnow( val);
}

bool PostProcessVolumeComponent::IsEnabledSnow( )
{
    return (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->IsEnabledSnow( );
}

void PostProcessVolumeComponent::SetEnable(bool bEnable )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetEnable( bEnable);
}

void PostProcessVolumeComponent::SetPropertyPostProcessVolumeSettings(UnknowKeeper^ val )
{
    (static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->SetPropertyPostProcessVolumeSettings( (UnknowKeeper::get_native_with_type_for_reference<cross::PostProcessVolumeSetting& >(val)));
}

UnknowKeeper^ PostProcessVolumeComponent::GetPropertyPostProcessVolumeSettings( )
{
    return UnknownKeeperCast((static_cast<cegf::PostProcessVolumeComponent*>(this->_native))->GetPropertyPostProcessVolumeSettings( ));
}


}   //end namespace Clicegf

// ReflectionProbeComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ReflectionProbeComponent::ReflectionProbeComponent( )
    :ReflectionProbeComponent(new cegf::ReflectionProbeComponent(), true)
{
}



ReflectionProbeComponent::ReflectionProbeComponent(const cegf::ReflectionProbeComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

ReflectionProbeComponent::operator ReflectionProbeComponent^ (const cegf::ReflectionProbeComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ReflectionProbeComponent(const_cast<cegf::ReflectionProbeComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ReflectionProbeComponent^)managedObj;
    }
    else
        return nullptr;
}

void ReflectionProbeComponent::SetReflectionProbeEnable(bool val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeEnable( val);
}

bool ReflectionProbeComponent::GetReflectionProbeEnable( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeEnable( );
}

void ReflectionProbeComponent::SetReflectionProbeNearPlane(float val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeNearPlane( val);
}

float ReflectionProbeComponent::GetReflectionProbeNearPlane( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeNearPlane( );
}

void ReflectionProbeComponent::SetReflectionProbeRefleProbeShapeType(Clicross::ReflectionProbeShapeType val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeRefleProbeShapeType( static_cast<cross::ReflectionProbeShapeType>(val));
}

Clicross::ReflectionProbeShapeType ReflectionProbeComponent::GetReflectionProbeRefleProbeShapeType( )
{
    return (Clicross::ReflectionProbeShapeType)((int)(static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeRefleProbeShapeType( ));
}

void ReflectionProbeComponent::SetReflectionProbeSphereRadius(float val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeSphereRadius( val);
}

float ReflectionProbeComponent::GetReflectionProbeSphereRadius( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeSphereRadius( );
}

void ReflectionProbeComponent::SetReflectionProbeBlendDistance(float val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeBlendDistance( val);
}

float ReflectionProbeComponent::GetReflectionProbeBlendDistance( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeBlendDistance( );
}

void ReflectionProbeComponent::SetReflectionProbeIntensity(float val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeIntensity( val);
}

float ReflectionProbeComponent::GetReflectionProbeIntensity( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeIntensity( );
}

void ReflectionProbeComponent::SetReflectionProbeBoxSize(Clicross::Float3^ val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeBoxSize( *((cross::Float3*)(val)));
}

Clicross::Float3^ ReflectionProbeComponent::GetReflectionProbeBoxSize( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeBoxSize( ))) , true);
}

void ReflectionProbeComponent::SetReflectionProbeRefleProbeType(Clicross::ReflectionProbeType val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeRefleProbeType( static_cast<cross::ReflectionProbeType>(val));
}

Clicross::ReflectionProbeType ReflectionProbeComponent::GetReflectionProbeRefleProbeType( )
{
    return (Clicross::ReflectionProbeType)((int)(static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeRefleProbeType( ));
}

void ReflectionProbeComponent::SetReflectionProbeRefreshMode(Clicross::ReflectionProbeRefreshMode val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeRefreshMode( static_cast<cross::ReflectionProbeRefreshMode>(val));
}

Clicross::ReflectionProbeRefreshMode ReflectionProbeComponent::GetReflectionProbeRefreshMode( )
{
    return (Clicross::ReflectionProbeRefreshMode)((int)(static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeRefreshMode( ));
}

void ReflectionProbeComponent::SetReflectionProbeTimeSlicing(Clicross::ReflectionProbeTimeSlicing val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeTimeSlicing( static_cast<cross::ReflectionProbeTimeSlicing>(val));
}

Clicross::ReflectionProbeTimeSlicing ReflectionProbeComponent::GetReflectionProbeTimeSlicing( )
{
    return (Clicross::ReflectionProbeTimeSlicing)((int)(static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeTimeSlicing( ));
}

void ReflectionProbeComponent::SetReflectionProbeBoxProjection(bool val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeBoxProjection( val);
}

bool ReflectionProbeComponent::GetReflectionProbeBoxProjection( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeBoxProjection( );
}

unsigned int ReflectionProbeComponent::GetReflectionProbeResolution( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeResolution( );
}

void ReflectionProbeComponent::SetReflectionProbeCaptureCacheOffset(unsigned int val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeCaptureCacheOffset( val);
}

unsigned int ReflectionProbeComponent::GetReflectionProbeCaptureCacheOffset( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeCaptureCacheOffset( );
}

void ReflectionProbeComponent::SetReflectionProbeCaptureSavePath(System::String^ val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeCaptureSavePath( ClangenCli::ToNativeString(val).c_str());
}

void ReflectionProbeComponent::SetReflectionProbeIndex(unsigned short val )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeIndex( val);
}

unsigned short ReflectionProbeComponent::GetReflectionProbeIndex( )
{
    return (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeIndex( );
}

System::String^ ReflectionProbeComponent::GetReflectionProbeReflectionTexturePath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::ReflectionProbeComponent*>(this->_native))->GetReflectionProbeReflectionTexturePath( ))).c_str());
}

void ReflectionProbeComponent::SetReflectionProbeShow(bool isShow )
{
    (static_cast<cegf::ReflectionProbeComponent*>(this->_native))->SetReflectionProbeShow( isShow);
}


}   //end namespace Clicegf

// RenderPropertyComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
RenderPropertyComponent::RenderPropertyComponent( )
    :RenderPropertyComponent(new cegf::RenderPropertyComponent(), true)
{
}



RenderPropertyComponent::RenderPropertyComponent(const cegf::RenderPropertyComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

RenderPropertyComponent::operator RenderPropertyComponent^ (const cegf::RenderPropertyComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew RenderPropertyComponent(const_cast<cegf::RenderPropertyComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (RenderPropertyComponent^)managedObj;
    }
    else
        return nullptr;
}

void RenderPropertyComponent::SetLayerIndex(unsigned int layerIndex )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetLayerIndex( layerIndex);
}

void RenderPropertyComponent::SetLayerName(System::String^ layerName )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetLayerName( ClangenCli::ToNativeString(layerName).c_str());
}

unsigned int RenderPropertyComponent::GetLayerIndex( )
{
    return (static_cast<cegf::RenderPropertyComponent*>(this->_native))->GetLayerIndex( );
}

void RenderPropertyComponent::SetCullingProperty(Clicross::CullingProperty cullingProperty )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetCullingProperty( static_cast<cross::CullingProperty>(cullingProperty));
}

Clicross::CullingProperty RenderPropertyComponent::GetCullingProperty( )
{
    return (Clicross::CullingProperty)((int)(static_cast<cegf::RenderPropertyComponent*>(this->_native))->GetCullingProperty( ));
}

void RenderPropertyComponent::SetHide(bool hide )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetHide( hide);
}

bool RenderPropertyComponent::IsHide( )
{
    return (static_cast<cegf::RenderPropertyComponent*>(this->_native))->IsHide( );
}

void RenderPropertyComponent::SetHideInGame(bool hideInGame )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetHideInGame( hideInGame);
}

bool RenderPropertyComponent::IsHideInGame( )
{
    return (static_cast<cegf::RenderPropertyComponent*>(this->_native))->IsHideInGame( );
}

void RenderPropertyComponent::AddRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->AddRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

void RenderPropertyComponent::RemoveRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->RemoveRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

void RenderPropertyComponent::SetNeedVoxelized(bool needVoxelized )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetNeedVoxelized( needVoxelized);
}

bool RenderPropertyComponent::IsNeedVoxelized( )
{
    return (static_cast<cegf::RenderPropertyComponent*>(this->_native))->IsNeedVoxelized( );
}

void RenderPropertyComponent::SetRuntimeRenderEffect(unsigned int flags )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetRuntimeRenderEffect( flags);
}

bool RenderPropertyComponent::HasRuntimeRenderEffect(Clicross::RenderEffectTag renderEffectTag )
{
    return (static_cast<cegf::RenderPropertyComponent*>(this->_native))->HasRuntimeRenderEffect( static_cast<cross::RenderEffectTag>(renderEffectTag));
}

UnknowKeeper^ RenderPropertyComponent::GetRenderEffect( )
{
    return UnknownKeeperCast((static_cast<cegf::RenderPropertyComponent*>(this->_native))->GetRenderEffect( ));
}

Clicross::RenderEffectTag RenderPropertyComponent::GetRenderEffectTag( )
{
    return (Clicross::RenderEffectTag)((int)(static_cast<cegf::RenderPropertyComponent*>(this->_native))->GetRenderEffectTag( ));
}

void RenderPropertyComponent::SetLodSelected(int lodIndex )
{
    (static_cast<cegf::RenderPropertyComponent*>(this->_native))->SetLodSelected( lodIndex);
}


}   //end namespace Clicegf

// SkeltSocketComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
SkeltSocketComponent::SkeltSocketComponent( )
    :SkeltSocketComponent(new cegf::SkeltSocketComponent(), true)
{
}



SkeltSocketComponent::SkeltSocketComponent(const cegf::SkeltSocketComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

SkeltSocketComponent::operator SkeltSocketComponent^ (const cegf::SkeltSocketComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SkeltSocketComponent(const_cast<cegf::SkeltSocketComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SkeltSocketComponent^)managedObj;
    }
    else
        return nullptr;
}

bool SkeltSocketComponent::SetSocket(System::String^ inAttachedBone, Clicross::Float3^ inTranslate, Clicross::Quaternion^ inRotate, Clicross::Float3^ inScale )
{
    return (static_cast<cegf::SkeltSocketComponent*>(this->_native))->SetSocket( ClangenCli::ToNativeString(inAttachedBone).c_str(), (const cross::Float3& )(inTranslate), (const cross::Quaternion& )(inRotate), (const cross::Float3& )(inScale));
}

Clicross::Float3^ SkeltSocketComponent::GetReltvTranslation( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::SkeltSocketComponent*>(this->_native))->GetReltvTranslation( ))) , true);
}

Clicross::Quaternion^ SkeltSocketComponent::GetReltvRotation( )
{
    return gcnew Clicross::Quaternion(new cross::Quaternion(((static_cast<cegf::SkeltSocketComponent*>(this->_native))->GetReltvRotation( ))) , true);
}

Clicross::Float3^ SkeltSocketComponent::GetReltvScale( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::SkeltSocketComponent*>(this->_native))->GetReltvScale( ))) , true);
}

System::String^ SkeltSocketComponent::GetBoneName( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::SkeltSocketComponent*>(this->_native))->GetBoneName( ))).c_str());
}


}   //end namespace Clicegf

// SkyAtmosphereComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
SkyAtmosphereComponent::SkyAtmosphereComponent( )
    :SkyAtmosphereComponent(new cegf::SkyAtmosphereComponent(), true)
{
}



SkyAtmosphereComponent::SkyAtmosphereComponent(const cegf::SkyAtmosphereComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

SkyAtmosphereComponent::operator SkyAtmosphereComponent^ (const cegf::SkyAtmosphereComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SkyAtmosphereComponent(const_cast<cegf::SkyAtmosphereComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SkyAtmosphereComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicross::SkyAtmosphereConfig^ SkyAtmosphereComponent::GetSkyAtmosphereConfig( )
{
    return gcnew Clicross::SkyAtmosphereConfig(new cross::SkyAtmosphereConfig(((static_cast<cegf::SkyAtmosphereComponent*>(this->_native))->GetSkyAtmosphereConfig( ))) , true);
}

void SkyAtmosphereComponent::SetSkyAtmosphereConfig(Clicross::SkyAtmosphereConfig^ inConfig )
{
    (static_cast<cegf::SkyAtmosphereComponent*>(this->_native))->SetSkyAtmosphereConfig( (const cross::SkyAtmosphereConfig& )(inConfig));
}

Clicross::SkyAtmosphereOuterParam^ SkyAtmosphereComponent::GetSkyAtmosphereOuterParam( )
{
    return gcnew Clicross::SkyAtmosphereOuterParam(new cross::SkyAtmosphereOuterParam(((static_cast<cegf::SkyAtmosphereComponent*>(this->_native))->GetSkyAtmosphereOuterParam( ))) , true);
}

void SkyAtmosphereComponent::SetSkyAtmosphereOuterParam(Clicross::SkyAtmosphereOuterParam^ inParam )
{
    (static_cast<cegf::SkyAtmosphereComponent*>(this->_native))->SetSkyAtmosphereOuterParam( (const cross::SkyAtmosphereOuterParam& )(inParam));
}


}   //end namespace Clicegf

// SkyLightComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
SkyLightComponent::SkyLightComponent( )
    :SkyLightComponent(new cegf::SkyLightComponent(), true)
{
}



SkyLightComponent::SkyLightComponent(const cegf::SkyLightComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

SkyLightComponent::operator SkyLightComponent^ (const cegf::SkyLightComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SkyLightComponent(const_cast<cegf::SkyLightComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SkyLightComponent^)managedObj;
    }
    else
        return nullptr;
}

void SkyLightComponent::SetLightMapIntensityDebug(float val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetLightMapIntensityDebug( val);
}

float SkyLightComponent::GetLightMapIntensityDebug( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetLightMapIntensityDebug( );
}

void SkyLightComponent::SetSkyLightIntensity(float val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetSkyLightIntensity( val);
}

float SkyLightComponent::GetSkyLightIntensity( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetSkyLightIntensity( );
}

void SkyLightComponent::SetEnable(bool val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetEnable( val);
}

bool SkyLightComponent::GetEnable( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetEnable( );
}

void SkyLightComponent::SetRealTimeCapture(bool val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetRealTimeCapture( val);
}

bool SkyLightComponent::GetRealTimeCapture( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetRealTimeCapture( );
}

void SkyLightComponent::SetRealTimeCaptureSliceCount(int val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetRealTimeCaptureSliceCount( val);
}

int SkyLightComponent::GetRealTimeCaptureSliceCount( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetRealTimeCaptureSliceCount( );
}

void SkyLightComponent::SetIsLowerHemisphereColor(bool val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetIsLowerHemisphereColor( val);
}

bool SkyLightComponent::GetIsLowerHemisphereColor( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetIsLowerHemisphereColor( );
}

void SkyLightComponent::SetTODLowerHemisphereColor(bool val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetTODLowerHemisphereColor( val);
}

bool SkyLightComponent::GetTODLowerHemisphereColor( )
{
    return (static_cast<cegf::SkyLightComponent*>(this->_native))->GetTODLowerHemisphereColor( );
}

void SkyLightComponent::SetLowerHemisphereColor(Clicross::Float4^ val )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetLowerHemisphereColor( *((cross::Float4*)(val)));
}

Clicross::Float4^ SkyLightComponent::GetLowerHemisphereColor( )
{
    return gcnew Clicross::Float4(new cross::Float4(((static_cast<cegf::SkyLightComponent*>(this->_native))->GetLowerHemisphereColor( ))) , true);
}

void SkyLightComponent::SetLightColor(Clicross::Float3^ inValue )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetLightColor( *((cross::Float3*)(inValue)));
}

void SkyLightComponent::SetDiffuseProbe(Clicross::Float4^ param, int index )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetDiffuseProbe( *((cross::Float4*)(param)), index);
}

void SkyLightComponent::SetSpecularProbe(System::String^ path )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->SetSpecularProbe( ClangenCli::ToNativeString(path).c_str());
}

void SkyLightComponent::ResetSystemState( )
{
    (static_cast<cegf::SkyLightComponent*>(this->_native))->ResetSystemState( );
}


}   //end namespace Clicegf


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


