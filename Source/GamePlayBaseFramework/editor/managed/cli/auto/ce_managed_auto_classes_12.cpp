//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// SphereComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
SphereComponent::SphereComponent( )
    :SphereComponent(new cegf::SphereComponent(), true)
{
}



SphereComponent::SphereComponent(const cegf::SphereComponent * obj, bool created_by_clr): Clicegf::PhysicsComponent(obj, created_by_clr)
{
}

SphereComponent::operator SphereComponent^ (const cegf::SphereComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SphereComponent(const_cast<cegf::SphereComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SphereComponent^)managedObj;
    }
    else
        return nullptr;
}

float SphereComponent::GetSphereRadius( )
{
    return (static_cast<cegf::SphereComponent*>(this->_native))->GetSphereRadius( );
}

void SphereComponent::SetSphereRadius(float inRadius )
{
    (static_cast<cegf::SphereComponent*>(this->_native))->SetSphereRadius( inRadius);
}


}   //end namespace Clicegf

// SplineComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
SplineComponent::SplineComponent( )
    :SplineComponent(new cegf::SplineComponent(), true)
{
}



SplineComponent::SplineComponent(const cegf::SplineComponent * obj, bool created_by_clr): Clicegf::PrimitiveComponent(obj, created_by_clr)
{
}

SplineComponent::operator SplineComponent^ (const cegf::SplineComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew SplineComponent(const_cast<cegf::SplineComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (SplineComponent^)managedObj;
    }
    else
        return nullptr;
}

int SplineComponent::GetPointNum( )
{
    return (static_cast<cegf::SplineComponent*>(this->_native))->GetPointNum( );
}

Clicross::Float3^ SplineComponent::GetSplinePointsPositionAt(int index )
{
    return (Clicross::Float3^)((static_cast<cegf::SplineComponent*>(this->_native))->GetSplinePointsPositionAt( index));
}

int SplineComponent::GetSplinePointsInterpModeAt(int index )
{
    return ((int)(static_cast<cegf::SplineComponent*>(this->_native))->GetSplinePointsInterpModeAt( index));
}

void SplineComponent::SetPointNum(int size )
{
    (static_cast<cegf::SplineComponent*>(this->_native))->SetPointNum( size);
}

void SplineComponent::SetSplinePointAt(int index, Clicross::Float3^ position, int mode )
{
    (static_cast<cegf::SplineComponent*>(this->_native))->SetSplinePointAt( index, (const cross::Float3& )(position), static_cast<cegf::InterpCurveMode>(mode));
}

bool SplineComponent::GetEndpointsIsStationary( )
{
    return (static_cast<cegf::SplineComponent*>(this->_native))->GetEndpointsIsStationary( );
}

void SplineComponent::SetEndpointsIsStationary(bool isStationaryEndpoints )
{
    (static_cast<cegf::SplineComponent*>(this->_native))->SetEndpointsIsStationary( isStationaryEndpoints);
}


}   //end namespace Clicegf

// TerrainComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
TerrainComponent::TerrainComponent( )
    :TerrainComponent(new cegf::TerrainComponent(), true)
{
}



TerrainComponent::TerrainComponent(const cegf::TerrainComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

TerrainComponent::operator TerrainComponent^ (const cegf::TerrainComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew TerrainComponent(const_cast<cegf::TerrainComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (TerrainComponent^)managedObj;
    }
    else
        return nullptr;
}

bool TerrainComponent::GetTerrainEnable( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetTerrainEnable( );
}

void TerrainComponent::SetTerrainEnable(bool enable )
{
    (static_cast<cegf::TerrainComponent*>(this->_native))->SetTerrainEnable( enable);
}

Clicross::TerrainSurfaceType TerrainComponent::GetSurfaceType( )
{
    return (Clicross::TerrainSurfaceType)((int)(static_cast<cegf::TerrainComponent*>(this->_native))->GetSurfaceType( ));
}

unsigned int TerrainComponent::GetGridSizeX( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetGridSizeX( );
}

unsigned int TerrainComponent::GetGridSizeY( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetGridSizeY( );
}

unsigned int TerrainComponent::GetBlockSize( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetBlockSize( );
}

unsigned int TerrainComponent::GetTileSize( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetTileSize( );
}

unsigned int TerrainComponent::GetTextureSize( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetTextureSize( );
}

float TerrainComponent::GetWGS84SemiMajor( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetWGS84SemiMajor( );
}

Clicross::Float3^ TerrainComponent::GetWorldScale( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::TerrainComponent*>(this->_native))->GetWorldScale( ))) , true);
}

Clicross::Float3^ TerrainComponent::GetWorldTranslation( )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cegf::TerrainComponent*>(this->_native))->GetWorldTranslation( ))) , true);
}

unsigned int TerrainComponent::GetGridDimX( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetGridDimX( );
}

unsigned int TerrainComponent::GetGridDimY( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetGridDimY( );
}

System::String^ TerrainComponent::GetTerrainPath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::TerrainComponent*>(this->_native))->GetTerrainPath( ))).c_str());
}

void TerrainComponent::SetTerrainPath(System::String^ terrainPath )
{
    (static_cast<cegf::TerrainComponent*>(this->_native))->SetTerrainPath( ClangenCli::ToNativeString(terrainPath).c_str());
}

System::String^ TerrainComponent::GetMaterialOverridePath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::TerrainComponent*>(this->_native))->GetMaterialOverridePath( ))).c_str());
}

void TerrainComponent::SetMaterialOverridePath(System::String^ materialPath )
{
    (static_cast<cegf::TerrainComponent*>(this->_native))->SetMaterialOverridePath( ClangenCli::ToNativeString(materialPath).c_str());
}

Clicross::TerrainInfo^ TerrainComponent::GetTerrainInfo( )
{
    return gcnew Clicross::TerrainInfo(new cross::TerrainInfo(((static_cast<cegf::TerrainComponent*>(this->_native))->GetTerrainInfo( ))) , true);
}

unsigned int TerrainComponent::GetNumBlendLayers( )
{
    return (static_cast<cegf::TerrainComponent*>(this->_native))->GetNumBlendLayers( );
}


}   //end namespace Clicegf

// TODLightComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
TODLightComponent::TODLightComponent( )
    :TODLightComponent(new cegf::TODLightComponent(), true)
{
}



TODLightComponent::TODLightComponent(const cegf::TODLightComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

TODLightComponent::operator TODLightComponent^ (const cegf::TODLightComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew TODLightComponent(const_cast<cegf::TODLightComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (TODLightComponent^)managedObj;
    }
    else
        return nullptr;
}

Clicross::TODLightConfig^ TODLightComponent::GetTODLightConfig( )
{
    return gcnew Clicross::TODLightConfig(new cross::TODLightConfig(((static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightConfig( ))) , true);
}

void TODLightComponent::SetTODLightConfig(Clicross::TODLightConfig^ inConfig )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightConfig( (const cross::TODLightConfig& )(inConfig));
}

unsigned int TODLightComponent::GetTODLightYear( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightYear( );
}

void TODLightComponent::SetTODLightYear(unsigned int inYear )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightYear( inYear);
}

void TODLightComponent::SetTODLightYearFloat(float inYear )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightYearFloat( inYear);
}

unsigned int TODLightComponent::GetTODLightMonth( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightMonth( );
}

void TODLightComponent::SetTODLightMonth(unsigned int inMonth )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightMonth( inMonth);
}

void TODLightComponent::SetTODLightMonthFloat(float inMonth )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightMonthFloat( inMonth);
}

unsigned int TODLightComponent::GetTODLightDay( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightDay( );
}

void TODLightComponent::SetTODLightDay(unsigned int inDay )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightDay( inDay);
}

void TODLightComponent::SetTODLightDayFloat(float inDay )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightDayFloat( inDay);
}

unsigned int TODLightComponent::GetTODLightHour( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightHour( );
}

void TODLightComponent::SetTODLightHour(unsigned int inHour )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightHour( inHour);
}

void TODLightComponent::SetTODLightHourFloat(float inHour )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightHourFloat( inHour);
}

unsigned int TODLightComponent::GetTODLightMinute( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightMinute( );
}

void TODLightComponent::SetTODLightMinute(unsigned int inMinute )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightMinute( inMinute);
}

void TODLightComponent::SetTODLightMinuteFloat(float inMinute )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightMinuteFloat( inMinute);
}

unsigned int TODLightComponent::GetTODLightSecond( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightSecond( );
}

void TODLightComponent::SetTODLightSecond(unsigned int inSecond )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightSecond( inSecond);
}

void TODLightComponent::SetTODLightSecondFloat(float inSecond )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightSecondFloat( inSecond);
}

void TODLightComponent::OnChangeConfig(bool refreshForce )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->OnChangeConfig( refreshForce);
}

void TODLightComponent::SyncAllTODLightComponents( )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SyncAllTODLightComponents( );
}

void TODLightComponent::SetTODLightTimeZone(int inTimeZone )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightTimeZone( inTimeZone);
}

void TODLightComponent::SetTODLightTimeZoneFloat(float inTimeZone )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTODLightTimeZoneFloat( inTimeZone);
}

int TODLightComponent::GetTODLightTimeZone( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->GetTODLightTimeZone( );
}

void TODLightComponent::SetTimeOfDay(Clicross::TOD4TimeState _param_1 )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTimeOfDay( static_cast<cross::TOD4TimeState>(_param_1));
}

void TODLightComponent::SetTimeOfDayOfLatLong(Clicross::TOD4TimeState _param_1, double latitude, double longitude )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTimeOfDayOfLatLong( static_cast<cross::TOD4TimeState>(_param_1), latitude, longitude);
}

void TODLightComponent::SetTimeOfDayForElapse(unsigned int Year, unsigned int Month, unsigned int Day, unsigned int Hour, unsigned int Minute, unsigned int Second )
{
    (static_cast<cegf::TODLightComponent*>(this->_native))->SetTimeOfDayForElapse( Year, Month, Day, Hour, Minute, Second);
}

float TODLightComponent::ComputeSolarElevationAngleAtCameraPosition( )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->ComputeSolarElevationAngleAtCameraPosition( );
}

float TODLightComponent::ComputeSolarElevationAngleAtAnyPosition(Clicross::Double3^ GroundPosition )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->ComputeSolarElevationAngleAtAnyPosition( (const cross::Double3& )(GroundPosition));
}

float TODLightComponent::ComputeSolarElevationAngleAtCameraPositionAnyTime(Clicross::TODLightConfig^ inConfig )
{
    return (static_cast<cegf::TODLightComponent*>(this->_native))->ComputeSolarElevationAngleAtCameraPositionAnyTime( (const cross::TODLightConfig& )(inConfig));
}


}   //end namespace Clicegf

// Element export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Element::Element(UnknowKeeper^ element )
    :Element(new cegf::Element((UnknowKeeper::get_native_with_type_for_pointer< Rml::Element* >(element))), true)
{
}



Element::Element(const cegf::Element * obj, bool created_by_clr): 
    _native(const_cast<cegf::Element *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Element::operator Element^ (const cegf::Element* t)
{
    if(t)
    {
        return gcnew Element(const_cast<cegf::Element*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// Document export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Document::Document(UnknowKeeper^ document )
    :Document(new cegf::Document((UnknowKeeper::get_native_with_type_for_pointer< Rml::ElementDocument* >(document))), true)
{
}



Document::Document(const cegf::Document * obj, bool created_by_clr): 
    _native(const_cast<cegf::Document *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Document::operator Document^ (const cegf::Document* t)
{
    if(t)
    {
        return gcnew Document(const_cast<cegf::Document*>(t));
    }
    else
        return nullptr;
}

Clicegf::Element^ Document::getElementById(System::String^ id )
{
    return (Clicegf::Element^)((static_cast<cegf::Document*>(this->_native))->getElementById( ClangenCli::ToNativeString(id).c_str()));
}


}   //end namespace Clicegf

// Style export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Style::Style(Clicegf::Element^ element )
    :Style(new cegf::Style(( cegf::Element* )(element)), true)
{
}



Style::Style(const cegf::Style * obj, bool created_by_clr): 
    _native(const_cast<cegf::Style *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Style::operator Style^ (const cegf::Style* t)
{
    if(t)
    {
        return gcnew Style(const_cast<cegf::Style*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// UIComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
UIComponent::UIComponent( )
    :UIComponent(new cegf::UIComponent(), true)
{
}

UIComponent::UIComponent(Clicegf::GameObject^ owner )
    :UIComponent(new cegf::UIComponent(( cegf::GameObject* )(owner)), true)
{
}



UIComponent::UIComponent(const cegf::UIComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

UIComponent::operator UIComponent^ (const cegf::UIComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew UIComponent(const_cast<cegf::UIComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (UIComponent^)managedObj;
    }
    else
        return nullptr;
}

void UIComponent::SetPagePath(System::String^ path )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetPagePath( ClangenCli::ToNativeString(path).c_str());
}

System::String^ UIComponent::GetPagePath( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::UIComponent*>(this->_native))->GetPagePath( ))).c_str());
}

void UIComponent::SetCanvasMode(Clicross::CanvasMode mode )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetCanvasMode( static_cast<cross::CanvasMode>(mode));
}

Clicross::CanvasMode UIComponent::GetCanvasMode( )
{
    return (Clicross::CanvasMode)((int)(static_cast<cegf::UIComponent*>(this->_native))->GetCanvasMode( ));
}

void UIComponent::SetVisible(bool visible )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetVisible( visible);
}

bool UIComponent::GetVisible( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetVisible( );
}

void UIComponent::SetWidthHeight(unsigned int width, unsigned int height )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetWidthHeight( width, height);
}

unsigned int UIComponent::GetWidth( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetWidth( );
}

unsigned int UIComponent::GetHeight( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetHeight( );
}

bool UIComponent::GetFitWindow( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetFitWindow( );
}

void UIComponent::SetFitWindow(bool value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetFitWindow( value);
}

bool UIComponent::GetWorldSpaceFade( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetWorldSpaceFade( );
}

void UIComponent::SetWorldSpaceFade(bool value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetWorldSpaceFade( value);
}

void UIComponent::SetLayer(int value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetLayer( value);
}

int UIComponent::GetLayer( )
{
    return (static_cast<cegf::UIComponent*>(this->_native))->GetLayer( );
}

void UIComponent::SetDefaultFontName(System::String^ value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetDefaultFontName( ClangenCli::ToNativeString(value).c_str());
}

System::String^ UIComponent::GetDefaultFontName( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::UIComponent*>(this->_native))->GetDefaultFontName( ))).c_str());
}

void UIComponent::SetUsedFonts(Clicegf::Vector_std_string_wrapper^ value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->SetUsedFonts( ( cegf::Vector_std_string_wrapper* )(value));
}

void UIComponent::GetUsedFonts(Clicegf::Vector_std_string_wrapper^ value )
{
    (static_cast<cegf::UIComponent*>(this->_native))->GetUsedFonts( ( cegf::Vector_std_string_wrapper* )(value));
}

void UIComponent::PostCanvasMessage(System::String^ message )
{
    (static_cast<cegf::UIComponent*>(this->_native))->PostCanvasMessage( ClangenCli::ToNativeString(message).c_str());
}

Clicegf::Document^ UIComponent::GetDocument( )
{
    return (Clicegf::Document^)((static_cast<cegf::UIComponent*>(this->_native))->GetDocument( ));
}


}   //end namespace Clicegf

// VolumeTriggerComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
VolumeTriggerComponent::VolumeTriggerComponent( )
    :VolumeTriggerComponent(new cegf::VolumeTriggerComponent(), true)
{
}



VolumeTriggerComponent::VolumeTriggerComponent(const cegf::VolumeTriggerComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

VolumeTriggerComponent::operator VolumeTriggerComponent^ (const cegf::VolumeTriggerComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VolumeTriggerComponent(const_cast<cegf::VolumeTriggerComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VolumeTriggerComponent^)managedObj;
    }
    else
        return nullptr;
}

bool VolumeTriggerComponent::GetGenerateOverlap( )
{
    return (static_cast<cegf::VolumeTriggerComponent*>(this->_native))->GetGenerateOverlap( );
}

void VolumeTriggerComponent::SetGenerateOverlap(bool value )
{
    (static_cast<cegf::VolumeTriggerComponent*>(this->_native))->SetGenerateOverlap( value);
}


}   //end namespace Clicegf

// WorkFlowComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
WorkFlowComponent::WorkFlowComponent( )
    :WorkFlowComponent(new cegf::WorkFlowComponent(), true)
{
}

WorkFlowComponent::WorkFlowComponent(Clicegf::GameObject^ owner )
    :WorkFlowComponent(new cegf::WorkFlowComponent(( cegf::GameObject* )(owner)), true)
{
}



WorkFlowComponent::WorkFlowComponent(const cegf::WorkFlowComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

WorkFlowComponent::operator WorkFlowComponent^ (const cegf::WorkFlowComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew WorkFlowComponent(const_cast<cegf::WorkFlowComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (WorkFlowComponent^)managedObj;
    }
    else
        return nullptr;
}

System::String^ WorkFlowComponent::GetWorkflowRes( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::WorkFlowComponent*>(this->_native))->GetWorkflowRes( ))).c_str());
}

void WorkFlowComponent::SetWorkflowRes(System::String^ resGUID )
{
    (static_cast<cegf::WorkFlowComponent*>(this->_native))->SetWorkflowRes( ClangenCli::ToNativeString(resGUID).c_str());
}


}   //end namespace Clicegf

// Curve export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::FloatCurveTrack^ Curve::CurveTrack::get()
 {
	return gcnew Clicross::FloatCurveTrack(new cross::FloatCurveTrack(((static_cast<cegf::Curve*>(this->_native))->CurveTrack)) , true);
 }
 void Curve::CurveTrack::set(Clicross::FloatCurveTrack^ value )
 {
	(static_cast<cegf::Curve*>(this->_native))->CurveTrack = value;
 }


//constructor export here
Curve::Curve( )
    :Curve(new cegf::Curve(), true)
{
}



Curve::Curve(const cegf::Curve * obj, bool created_by_clr): 
    _native(const_cast<cegf::Curve *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Curve::operator Curve^ (const cegf::Curve* t)
{
    if(t)
    {
        return gcnew Curve(const_cast<cegf::Curve*>(t));
    }
    else
        return nullptr;
}

float Curve::Eval(float inTime )
{
    return (static_cast<cegf::Curve*>(this->_native))->Eval( inTime);
}


}   //end namespace Clicegf

// GOHandle export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
unsigned long long GOHandle::EntityID::get()
 {
	return (static_cast<cegf::GOHandle*>(this->_native))->EntityID;
 }
 void GOHandle::EntityID::set(unsigned long long value )
 {
	(static_cast<cegf::GOHandle*>(this->_native))->EntityID = value;
 }

Clicegf::GameObject^ GOHandle::Parent::get()
 {
	return (Clicegf::GameObject^)((static_cast<cegf::GOHandle*>(this->_native))->Parent);
 }
 void GOHandle::Parent::set(Clicegf::GameObject^ value )
 {
	(static_cast<cegf::GOHandle*>(this->_native))->Parent = value;
 }

bool GOHandle::UsingWorkflow::get()
 {
	return (static_cast<cegf::GOHandle*>(this->_native))->UsingWorkflow;
 }
 void GOHandle::UsingWorkflow::set(bool value )
 {
	(static_cast<cegf::GOHandle*>(this->_native))->UsingWorkflow = value;
 }

System::String^ GOHandle::GOType::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::GOHandle*>(this->_native))->GOType)).c_str());
 }
 void GOHandle::GOType::set(System::String^ value )
 {
	((static_cast<cegf::GOHandle*>(this->_native))->GOType) = (ClangenCli::ToNativeString(value));
 }

System::String^ GOHandle::GOName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::GOHandle*>(this->_native))->GOName)).c_str());
 }
 void GOHandle::GOName::set(System::String^ value )
 {
	((static_cast<cegf::GOHandle*>(this->_native))->GOName) = (ClangenCli::ToNativeString(value));
 }

System::String^ GOHandle::WorkflowGUID::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::GOHandle*>(this->_native))->WorkflowGUID)).c_str());
 }
 void GOHandle::WorkflowGUID::set(System::String^ value )
 {
	((static_cast<cegf::GOHandle*>(this->_native))->WorkflowGUID) = (ClangenCli::ToNativeString(value));
 }

Clicross::Double3^ GOHandle::Location::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::GOHandle*>(this->_native))->Location)) , true);
 }
 void GOHandle::Location::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::GOHandle*>(this->_native))->Location = value;
 }


//constructor export here
GOHandle::GOHandle(Clicegf::GOHandle^ inHandle )
    :GOHandle(new cegf::GOHandle((const cegf::GOHandle& )(inHandle)), true)
{
}

GOHandle::GOHandle( )
    :GOHandle(new cegf::GOHandle(), true)
{
}



GOHandle::GOHandle(const cegf::GOHandle * obj, bool created_by_clr): 
    _native(const_cast<cegf::GOHandle *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GOHandle::operator GOHandle^ (const cegf::GOHandle* t)
{
    if(t)
    {
        return gcnew GOHandle(const_cast<cegf::GOHandle*>(t));
    }
    else
        return nullptr;
}

Clicegf::GOHandle^ GOHandle::operator=(Clicegf::GOHandle^ inHandle )
{
    return (Clicegf::GOHandle^)((static_cast<cegf::GOHandle*>(this->_native))->operator=( (const cegf::GOHandle& )(inHandle)));
}


}   //end namespace Clicegf

// Vector_GameObject_wrapper export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::Vector_GameObject_wrapper::holder export start
	#define STLDECL_MANAGEDTYPE Clicegf::GameObject^
	#define STLDECL_NATIVETYPE cegf::GameObject*
	CPP_DECLARE_STLVECTOR(Vector_GameObject_wrapper::, holderCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
Vector_GameObject_wrapper::holderCliType^ Vector_GameObject_wrapper::holder::get()
 {
	return (static_cast<cegf::Vector_GameObject_wrapper*>(this->_native))->holder;
 }
 void Vector_GameObject_wrapper::holder::set(Vector_GameObject_wrapper::holderCliType^ value )
 {
	(static_cast<cegf::Vector_GameObject_wrapper*>(this->_native))->holder = *value->_native;
 }


//constructor export here
Vector_GameObject_wrapper::Vector_GameObject_wrapper( )
    :Vector_GameObject_wrapper(new cegf::Vector_GameObject_wrapper(), true)
{
}



Vector_GameObject_wrapper::Vector_GameObject_wrapper(const cegf::Vector_GameObject_wrapper * obj, bool created_by_clr): 
    _native(const_cast<cegf::Vector_GameObject_wrapper *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Vector_GameObject_wrapper::operator Vector_GameObject_wrapper^ (const cegf::Vector_GameObject_wrapper* t)
{
    if(t)
    {
        return gcnew Vector_GameObject_wrapper(const_cast<cegf::Vector_GameObject_wrapper*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// GOContext export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::GOContext::GOTypeNames export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(GOContext::, GOTypeNamesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cegf::GOContext::GCompTypeNames export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(GOContext::, GCompTypeNamesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cegf::GOContext::StructTypeNames export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(GOContext::, StructTypeNamesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cegf::GOContext::ResourceTypeNames export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(GOContext::, ResourceTypeNamesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
GOContext::GOTypeNamesCliType^ GOContext::GOTypeNames::get()
 {
	return (static_cast<cegf::GOContext*>(this->_native))->GOTypeNames;
 }
 void GOContext::GOTypeNames::set(GOContext::GOTypeNamesCliType^ value )
 {
	(static_cast<cegf::GOContext*>(this->_native))->GOTypeNames = *value->_native;
 }

GOContext::GCompTypeNamesCliType^ GOContext::GCompTypeNames::get()
 {
	return (static_cast<cegf::GOContext*>(this->_native))->GCompTypeNames;
 }
 void GOContext::GCompTypeNames::set(GOContext::GCompTypeNamesCliType^ value )
 {
	(static_cast<cegf::GOContext*>(this->_native))->GCompTypeNames = *value->_native;
 }

GOContext::StructTypeNamesCliType^ GOContext::StructTypeNames::get()
 {
	return (static_cast<cegf::GOContext*>(this->_native))->StructTypeNames;
 }
 void GOContext::StructTypeNames::set(GOContext::StructTypeNamesCliType^ value )
 {
	(static_cast<cegf::GOContext*>(this->_native))->StructTypeNames = *value->_native;
 }

GOContext::ResourceTypeNamesCliType^ GOContext::ResourceTypeNames::get()
 {
	return (static_cast<cegf::GOContext*>(this->_native))->ResourceTypeNames;
 }
 void GOContext::ResourceTypeNames::set(GOContext::ResourceTypeNamesCliType^ value )
 {
	(static_cast<cegf::GOContext*>(this->_native))->ResourceTypeNames = *value->_native;
 }


//constructor export here
GOContext::GOContext(Clicross::IGameWorld^ in_game_world )
    :GOContext(new cegf::GOContext(( cross::IGameWorld* )(in_game_world)), true)
{
}

GOContext::GOContext(Clicegf::GOContext^ inContext )
    :GOContext(new cegf::GOContext(( cegf::GOContext& )(inContext)), true)
{
}

GOContext::GOContext( )
    :GOContext(new cegf::GOContext(), true)
{
}



GOContext::GOContext(const cegf::GOContext * obj, bool created_by_clr): 
    _native(const_cast<cegf::GOContext *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GOContext::operator GOContext^ (const cegf::GOContext* t)
{
    if(t)
    {
        return gcnew GOContext(const_cast<cegf::GOContext*>(t));
    }
    else
        return nullptr;
}

void GOContext::CreateGameObject(System::String^ className, Clicross::Double3^ pos, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObject( ClangenCli::ToNativeString(className).c_str(), *((cross::Double3*)(pos)), ( cegf::GOHandle* )(ret));
}

void GOContext::CreateGameObject(System::String^ className, Clicross::Double3^ pos, unsigned long long EntityID, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObject( ClangenCli::ToNativeString(className).c_str(), *((cross::Double3*)(pos)), EntityID, ( cegf::GOHandle* )(ret));
}

void GOContext::CreateGameObject(System::String^ className, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObject( ClangenCli::ToNativeString(className).c_str(), ( cegf::GOHandle* )(ret));
}

void GOContext::CreateGameObject(System::String^ className, Clicross::Double3^ pos )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObject( ClangenCli::ToNativeString(className).c_str(), *((cross::Double3*)(pos)));
}

void GOContext::CreateGameObject(System::String^ className )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObject( ClangenCli::ToNativeString(className).c_str());
}

void GOContext::CreateGameObjectWithFlowFile(System::String^ flowFilePath, Clicross::Double3^ pos, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithFlowFile( ClangenCli::ToNativeString(flowFilePath).c_str(), *((cross::Double3*)(pos)), ( cegf::GOHandle* )(ret));
}

void GOContext::CreateGameObjectWithFlowFile(System::String^ flowFilePath, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithFlowFile( ClangenCli::ToNativeString(flowFilePath).c_str(), ( cegf::GOHandle* )(ret));
}

void GOContext::CreateGameObjectWithFlowFile(System::String^ flowFilePath, Clicross::Double3^ pos )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithFlowFile( ClangenCli::ToNativeString(flowFilePath).c_str(), *((cross::Double3*)(pos)));
}

void GOContext::CreateGameObjectWithFlowFile(System::String^ flowFilePath )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithFlowFile( ClangenCli::ToNativeString(flowFilePath).c_str());
}

void GOContext::CreateGameObjectWithHandle(Clicegf::GOHandle^ inHandle, Clicegf::GOHandle^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithHandle( *((cegf::GOHandle*)(inHandle)), ( cegf::GOHandle* )(ret));
}

void GOContext::TransferToGameObjectWithHandle(Clicegf::GOHandle^ inHandle )
{
    (static_cast<cegf::GOContext*>(this->_native))->TransferToGameObjectWithHandle( *((cegf::GOHandle*)(inHandle)));
}

void GOContext::DuplicateGameObject(unsigned long long srcEntityID, unsigned long long targetEntityID )
{
    (static_cast<cegf::GOContext*>(this->_native))->DuplicateGameObject( srcEntityID, targetEntityID);
}

void GOContext::CreateGameObjectWithHandle(Clicegf::GOHandle^ inHandle )
{
    (static_cast<cegf::GOContext*>(this->_native))->CreateGameObjectWithHandle( *((cegf::GOHandle*)(inHandle)));
}

void GOContext::DeleteGameObjectByEntity(unsigned long long inEntityID )
{
    (static_cast<cegf::GOContext*>(this->_native))->DeleteGameObjectByEntity( inEntityID);
}

void GOContext::GetGameObjectComponentNames(unsigned long long inEntityID, Clicegf::Vector_std_string_wrapper^ ret )
{
    (static_cast<cegf::GOContext*>(this->_native))->GetGameObjectComponentNames( inEntityID, ( cegf::Vector_std_string_wrapper* )(ret));
}

Clicegf::GameObject^ GOContext::GetGameObject(unsigned long long inEntityID )
{
    return (Clicegf::GameObject^)((static_cast<cegf::GOContext*>(this->_native))->GetGameObject( inEntityID));
}

Clicegf::Vector_GameObject_wrapper^ GOContext::GetGameObjectsWithComponent(System::String^ inComponentName )
{
    return gcnew Clicegf::Vector_GameObject_wrapper(new cegf::Vector_GameObject_wrapper(((static_cast<cegf::GOContext*>(this->_native))->GetGameObjectsWithComponent( ClangenCli::ToNativeString(inComponentName).c_str()))) , true);
}

bool GOContext::GameObjectHasComponent(unsigned long long inEntityID, System::String^ inComponentName )
{
    return (static_cast<cegf::GOContext*>(this->_native))->GameObjectHasComponent( inEntityID, ClangenCli::ToNativeString(inComponentName).c_str());
}


}   //end namespace Clicegf

// ObjectRigRail export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
float ObjectRigRail::mCurrentPositionOnRail::get()
 {
	return (static_cast<cegf::ObjectRigRail*>(this->_native))->mCurrentPositionOnRail;
 }
 void ObjectRigRail::mCurrentPositionOnRail::set(float value )
 {
	(static_cast<cegf::ObjectRigRail*>(this->_native))->mCurrentPositionOnRail = value;
 }

bool ObjectRigRail::mLockOrientationToRail::get()
 {
	return (static_cast<cegf::ObjectRigRail*>(this->_native))->mLockOrientationToRail;
 }
 void ObjectRigRail::mLockOrientationToRail::set(bool value )
 {
	(static_cast<cegf::ObjectRigRail*>(this->_native))->mLockOrientationToRail = value;
 }


//constructor export here
ObjectRigRail::ObjectRigRail( )
    :ObjectRigRail(new cegf::ObjectRigRail(), true)
{
}



ObjectRigRail::ObjectRigRail(const cegf::ObjectRigRail * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

ObjectRigRail::operator ObjectRigRail^ (const cegf::ObjectRigRail* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ObjectRigRail(const_cast<cegf::ObjectRigRail*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ObjectRigRail^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// StaticModel export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
StaticModel::StaticModel( )
    :StaticModel(new cegf::StaticModel(), true)
{
}



StaticModel::StaticModel(const cegf::StaticModel * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

StaticModel::operator StaticModel^ (const cegf::StaticModel* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew StaticModel(const_cast<cegf::StaticModel*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (StaticModel^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// UICanvas export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
UICanvas::UICanvas( )
    :UICanvas(new cegf::UICanvas(), true)
{
}



UICanvas::UICanvas(const cegf::UICanvas * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

UICanvas::operator UICanvas^ (const cegf::UICanvas* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew UICanvas(const_cast<cegf::UICanvas*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (UICanvas^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// GASTestObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
GASTestObject::GASTestObject( )
    :GASTestObject(new cegf::GASTestObject(), true)
{
}



GASTestObject::GASTestObject(const cegf::GASTestObject * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

GASTestObject::operator GASTestObject^ (const cegf::GASTestObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew GASTestObject(const_cast<cegf::GASTestObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (GASTestObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// HLODProxyDesc export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
System::String^ HLODProxyDesc::Asset::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::HLODProxyDesc*>(this->_native))->Asset)).c_str());
 }
 void HLODProxyDesc::Asset::set(System::String^ value )
 {
	((static_cast<cegf::HLODProxyDesc*>(this->_native))->Asset) = (ClangenCli::ToNativeString(value));
 }


//constructor export here


HLODProxyDesc::HLODProxyDesc(const cegf::HLODProxyDesc * obj, bool created_by_clr): 
    _native(const_cast<cegf::HLODProxyDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

HLODProxyDesc::operator HLODProxyDesc^ (const cegf::HLODProxyDesc* t)
{
    if(t)
    {
        return gcnew HLODProxyDesc(const_cast<cegf::HLODProxyDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// HLODComponent export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
HLODComponent::HLODComponent( )
    :HLODComponent(new cegf::HLODComponent(), true)
{
}



HLODComponent::HLODComponent(const cegf::HLODComponent * obj, bool created_by_clr): Clicegf::GameObjectComponent(obj, created_by_clr)
{
}

HLODComponent::operator HLODComponent^ (const cegf::HLODComponent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HLODComponent(const_cast<cegf::HLODComponent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HLODComponent^)managedObj;
    }
    else
        return nullptr;
}

System::String^ HLODComponent::GetHLODSettingAsset( )
{
    return ClangenCli::ToManagedString((((static_cast<cegf::HLODComponent*>(this->_native))->GetHLODSettingAsset( ))).c_str());
}

void HLODComponent::SetHLODSettingAsset(System::String^ value )
{
    (static_cast<cegf::HLODComponent*>(this->_native))->SetHLODSettingAsset( ClangenCli::ToNativeString(value).c_str());
}

Clicross::HLODSetup^ HLODComponent::GetHLODSetting( )
{
    return gcnew Clicross::HLODSetup(new cross::HLODSetup(((static_cast<cegf::HLODComponent*>(this->_native))->GetHLODSetting( ))) , true);
}

void HLODComponent::SetHLODSetting(Clicross::HLODSetup^ value )
{
    (static_cast<cegf::HLODComponent*>(this->_native))->SetHLODSetting( (const cross::HLODSetup& )(value));
}

int HLODComponent::GetForcedHLODModel( )
{
    return (static_cast<cegf::HLODComponent*>(this->_native))->GetForcedHLODModel( );
}

void HLODComponent::SetForcedHLODModel(int value )
{
    (static_cast<cegf::HLODComponent*>(this->_native))->SetForcedHLODModel( value);
}


}   //end namespace Clicegf

// HLODProxyObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
HLODProxyObject::HLODProxyObject( )
    :HLODProxyObject(new cegf::HLODProxyObject(), true)
{
}



HLODProxyObject::HLODProxyObject(const cegf::HLODProxyObject * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

HLODProxyObject::operator HLODProxyObject^ (const cegf::HLODProxyObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HLODProxyObject(const_cast<cegf::HLODProxyObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HLODProxyObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// HLODObject export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
bool HLODObject::DebugBoundingSphere::get()
 {
	return (static_cast<cegf::HLODObject*>(this->_native))->DebugBoundingSphere;
 }
 void HLODObject::DebugBoundingSphere::set(bool value )
 {
	(static_cast<cegf::HLODObject*>(this->_native))->DebugBoundingSphere = value;
 }

int HLODObject::LODLevel::get()
 {
	return (static_cast<cegf::HLODObject*>(this->_native))->LODLevel;
 }
 void HLODObject::LODLevel::set(int value )
 {
	(static_cast<cegf::HLODObject*>(this->_native))->LODLevel = value;
 }


//constructor export here
HLODObject::HLODObject( )
    :HLODObject(new cegf::HLODObject(), true)
{
}



HLODObject::HLODObject(const cegf::HLODObject * obj, bool created_by_clr): Clicegf::GameObject(obj, created_by_clr)
{
}

HLODObject::operator HLODObject^ (const cegf::HLODObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HLODObject(const_cast<cegf::HLODObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HLODObject^)managedObj;
    }
    else
        return nullptr;
}

int HLODObject::GetLODLevel( )
{
    return (static_cast<cegf::HLODObject*>(this->_native))->GetLODLevel( );
}

void HLODObject::AddSubObject(Clicegf::GameObject^ object )
{
    (static_cast<cegf::HLODObject*>(this->_native))->AddSubObject( ( cegf::GameObject* )(object));
}

void HLODObject::RemoveSubObject(Clicegf::GameObject^ object )
{
    (static_cast<cegf::HLODObject*>(this->_native))->RemoveSubObject( ( cegf::GameObject* )(object));
}

void HLODObject::ClearSubObjects( )
{
    (static_cast<cegf::HLODObject*>(this->_native))->ClearSubObjects( );
}

bool HLODObject::HasSubObjects( )
{
    return (static_cast<cegf::HLODObject*>(this->_native))->HasSubObjects( );
}


}   //end namespace Clicegf

// InputActionMappingRegistry export start
namespace Clicegf
{

//embeded classes

//stl container export here
// cegf::InputActionMappingRegistry::Modifiers export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(InputActionMappingRegistry::, ModifiersCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cegf::InputActionMappingRegistry::Triggers export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(InputActionMappingRegistry::, TriggersCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
InputActionMappingRegistry::ModifiersCliType^ InputActionMappingRegistry::Modifiers::get()
 {
	return (static_cast<cegf::InputActionMappingRegistry*>(this->_native))->Modifiers;
 }
 void InputActionMappingRegistry::Modifiers::set(InputActionMappingRegistry::ModifiersCliType^ value )
 {
	(static_cast<cegf::InputActionMappingRegistry*>(this->_native))->Modifiers = *value->_native;
 }

InputActionMappingRegistry::TriggersCliType^ InputActionMappingRegistry::Triggers::get()
 {
	return (static_cast<cegf::InputActionMappingRegistry*>(this->_native))->Triggers;
 }
 void InputActionMappingRegistry::Triggers::set(InputActionMappingRegistry::TriggersCliType^ value )
 {
	(static_cast<cegf::InputActionMappingRegistry*>(this->_native))->Triggers = *value->_native;
 }

System::String^ InputActionMappingRegistry::DefaultInputModifier::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputActionMappingRegistry*>(this->_native))->DefaultInputModifier)).c_str());
 }
 void InputActionMappingRegistry::DefaultInputModifier::set(System::String^ value )
 {
	((static_cast<cegf::InputActionMappingRegistry*>(this->_native))->DefaultInputModifier) = (ClangenCli::ToNativeString(value));
 }

System::String^ InputActionMappingRegistry::DefaultInputTrigger::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputActionMappingRegistry*>(this->_native))->DefaultInputTrigger)).c_str());
 }
 void InputActionMappingRegistry::DefaultInputTrigger::set(System::String^ value )
 {
	((static_cast<cegf::InputActionMappingRegistry*>(this->_native))->DefaultInputTrigger) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputActionMappingRegistry::InputActionMappingRegistry( )
    :InputActionMappingRegistry(new cegf::InputActionMappingRegistry(), true)
{
}



InputActionMappingRegistry::InputActionMappingRegistry(const cegf::InputActionMappingRegistry * obj, bool created_by_clr): 
    _native(const_cast<cegf::InputActionMappingRegistry *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InputActionMappingRegistry::operator InputActionMappingRegistry^ (const cegf::InputActionMappingRegistry* t)
{
    if(t)
    {
        return gcnew InputActionMappingRegistry(const_cast<cegf::InputActionMappingRegistry*>(t));
    }
    else
        return nullptr;
}

Clicegf::InputActionMappingRegistry^ InputActionMappingRegistry::Instance( )
{
    return (Clicegf::InputActionMappingRegistry^)(cegf::InputActionMappingRegistry::Instance( ));
}


}   //end namespace Clicegf

// InputKeyDefaultModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InputKeyDefaultModifier::InputKeyDefaultModifier( )
    :InputKeyDefaultModifier(new cegf::InputKeyDefaultModifier(), true)
{
}



InputKeyDefaultModifier::InputKeyDefaultModifier(const cegf::InputKeyDefaultModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyDefaultModifier::operator InputKeyDefaultModifier^ (const cegf::InputKeyDefaultModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyDefaultModifier(const_cast<cegf::InputKeyDefaultModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyDefaultModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyNegativeModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InputKeyNegativeModifier::InputKeyNegativeModifier( )
    :InputKeyNegativeModifier(new cegf::InputKeyNegativeModifier(), true)
{
}



InputKeyNegativeModifier::InputKeyNegativeModifier(const cegf::InputKeyNegativeModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyNegativeModifier::operator InputKeyNegativeModifier^ (const cegf::InputKeyNegativeModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyNegativeModifier(const_cast<cegf::InputKeyNegativeModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyNegativeModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyScaleModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
float InputKeyScaleModifier::Scale::get()
 {
	return (static_cast<cegf::InputKeyScaleModifier*>(this->_native))->Scale;
 }
 void InputKeyScaleModifier::Scale::set(float value )
 {
	(static_cast<cegf::InputKeyScaleModifier*>(this->_native))->Scale = value;
 }


//constructor export here
InputKeyScaleModifier::InputKeyScaleModifier( )
    :InputKeyScaleModifier(new cegf::InputKeyScaleModifier(), true)
{
}



InputKeyScaleModifier::InputKeyScaleModifier(const cegf::InputKeyScaleModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyScaleModifier::operator InputKeyScaleModifier^ (const cegf::InputKeyScaleModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyScaleModifier(const_cast<cegf::InputKeyScaleModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyScaleModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyDeltaModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InputKeyDeltaModifier::InputKeyDeltaModifier( )
    :InputKeyDeltaModifier(new cegf::InputKeyDeltaModifier(), true)
{
}



InputKeyDeltaModifier::InputKeyDeltaModifier(const cegf::InputKeyDeltaModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyDeltaModifier::operator InputKeyDeltaModifier^ (const cegf::InputKeyDeltaModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyDeltaModifier(const_cast<cegf::InputKeyDeltaModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyDeltaModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyDeadZoneModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
float InputKeyDeadZoneModifier::LowerBound::get()
 {
	return (static_cast<cegf::InputKeyDeadZoneModifier*>(this->_native))->LowerBound;
 }
 void InputKeyDeadZoneModifier::LowerBound::set(float value )
 {
	(static_cast<cegf::InputKeyDeadZoneModifier*>(this->_native))->LowerBound = value;
 }

float InputKeyDeadZoneModifier::UpperBound::get()
 {
	return (static_cast<cegf::InputKeyDeadZoneModifier*>(this->_native))->UpperBound;
 }
 void InputKeyDeadZoneModifier::UpperBound::set(float value )
 {
	(static_cast<cegf::InputKeyDeadZoneModifier*>(this->_native))->UpperBound = value;
 }


//constructor export here
InputKeyDeadZoneModifier::InputKeyDeadZoneModifier( )
    :InputKeyDeadZoneModifier(new cegf::InputKeyDeadZoneModifier(), true)
{
}



InputKeyDeadZoneModifier::InputKeyDeadZoneModifier(const cegf::InputKeyDeadZoneModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyDeadZoneModifier::operator InputKeyDeadZoneModifier^ (const cegf::InputKeyDeadZoneModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyDeadZoneModifier(const_cast<cegf::InputKeyDeadZoneModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyDeadZoneModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyExponentModifier export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double3^ InputKeyExponentModifier::Exponent::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cegf::InputKeyExponentModifier*>(this->_native))->Exponent)) , true);
 }
 void InputKeyExponentModifier::Exponent::set(Clicross::Double3^ value )
 {
	(static_cast<cegf::InputKeyExponentModifier*>(this->_native))->Exponent = value;
 }


//constructor export here
InputKeyExponentModifier::InputKeyExponentModifier( )
    :InputKeyExponentModifier(new cegf::InputKeyExponentModifier(), true)
{
}



InputKeyExponentModifier::InputKeyExponentModifier(const cegf::InputKeyExponentModifier * obj, bool created_by_clr): Clicross::InputKeyModifierItem(obj, created_by_clr)
{
}

InputKeyExponentModifier::operator InputKeyExponentModifier^ (const cegf::InputKeyExponentModifier* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyExponentModifier(const_cast<cegf::InputKeyExponentModifier*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyExponentModifier^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyDefaultTrigger export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InputKeyDefaultTrigger::InputKeyDefaultTrigger( )
    :InputKeyDefaultTrigger(new cegf::InputKeyDefaultTrigger(), true)
{
}



InputKeyDefaultTrigger::InputKeyDefaultTrigger(const cegf::InputKeyDefaultTrigger * obj, bool created_by_clr): Clicross::InputKeyTriggerItem(obj, created_by_clr)
{
}

InputKeyDefaultTrigger::operator InputKeyDefaultTrigger^ (const cegf::InputKeyDefaultTrigger* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyDefaultTrigger(const_cast<cegf::InputKeyDefaultTrigger*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyDefaultTrigger^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyChordActionTrigger export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyChordActionTrigger::ChordActionName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputKeyChordActionTrigger*>(this->_native))->ChordActionName)).c_str());
 }
 void InputKeyChordActionTrigger::ChordActionName::set(System::String^ value )
 {
	((static_cast<cegf::InputKeyChordActionTrigger*>(this->_native))->ChordActionName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyChordActionTrigger::InputKeyChordActionTrigger( )
    :InputKeyChordActionTrigger(new cegf::InputKeyChordActionTrigger(), true)
{
}



InputKeyChordActionTrigger::InputKeyChordActionTrigger(const cegf::InputKeyChordActionTrigger * obj, bool created_by_clr): Clicross::InputKeyTriggerItem(obj, created_by_clr)
{
}

InputKeyChordActionTrigger::operator InputKeyChordActionTrigger^ (const cegf::InputKeyChordActionTrigger* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyChordActionTrigger(const_cast<cegf::InputKeyChordActionTrigger*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyChordActionTrigger^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// InputKeyOneModifierTrigger export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyOneModifierTrigger::ModifierKey::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputKeyOneModifierTrigger*>(this->_native))->ModifierKey)).c_str());
 }
 void InputKeyOneModifierTrigger::ModifierKey::set(System::String^ value )
 {
	((static_cast<cegf::InputKeyOneModifierTrigger*>(this->_native))->ModifierKey) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyOneModifierTrigger::InputKeyOneModifierTrigger( )
    :InputKeyOneModifierTrigger(new cegf::InputKeyOneModifierTrigger(), true)
{
}



InputKeyOneModifierTrigger::InputKeyOneModifierTrigger(const cegf::InputKeyOneModifierTrigger * obj, bool created_by_clr): Clicross::InputKeyTriggerItem(obj, created_by_clr)
{
}

InputKeyOneModifierTrigger::operator InputKeyOneModifierTrigger^ (const cegf::InputKeyOneModifierTrigger* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyOneModifierTrigger(const_cast<cegf::InputKeyOneModifierTrigger*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyOneModifierTrigger^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


