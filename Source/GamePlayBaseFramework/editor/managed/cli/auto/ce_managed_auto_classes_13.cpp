//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// InputKeyTwoModifiersTrigger export start
namespace Clicegf
{

//embeded classes

//stl container export here

//fields export here
System::String^ InputKeyTwoModifiersTrigger::ModifierKey_1::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputKeyTwoModifiersTrigger*>(this->_native))->ModifierKey_1)).c_str());
 }
 void InputKeyTwoModifiersTrigger::ModifierKey_1::set(System::String^ value )
 {
	((static_cast<cegf::InputKeyTwoModifiersTrigger*>(this->_native))->ModifierKey_1) = (ClangenCli::ToNativeString(value));
 }

System::String^ InputKeyTwoModifiersTrigger::ModifierKey_2::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cegf::InputKeyTwoModifiersTrigger*>(this->_native))->ModifierKey_2)).c_str());
 }
 void InputKeyTwoModifiersTrigger::ModifierKey_2::set(System::String^ value )
 {
	((static_cast<cegf::InputKeyTwoModifiersTrigger*>(this->_native))->ModifierKey_2) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
InputKeyTwoModifiersTrigger::InputKeyTwoModifiersTrigger( )
    :InputKeyTwoModifiersTrigger(new cegf::InputKeyTwoModifiersTrigger(), true)
{
}



InputKeyTwoModifiersTrigger::InputKeyTwoModifiersTrigger(const cegf::InputKeyTwoModifiersTrigger * obj, bool created_by_clr): Clicross::InputKeyTriggerItem(obj, created_by_clr)
{
}

InputKeyTwoModifiersTrigger::operator InputKeyTwoModifiersTrigger^ (const cegf::InputKeyTwoModifiersTrigger* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InputKeyTwoModifiersTrigger(const_cast<cegf::InputKeyTwoModifiersTrigger*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InputKeyTwoModifiersTrigger^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicegf

// VarInspectorHelper_cross__ColorRGBf export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::ColorRGBf^ VarInspectorHelper_cross__ColorRGBf::mValue::get()
 {
	return gcnew Clicross::ColorRGBf(new cross::ColorRGBf(((static_cast<gbf::logic::VarInspectorHelper_cross__ColorRGBf*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__ColorRGBf::mValue::set(Clicross::ColorRGBf^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__ColorRGBf*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__ColorRGBf::VarInspectorHelper_cross__ColorRGBf( )
    :VarInspectorHelper_cross__ColorRGBf(new gbf::logic::VarInspectorHelper_cross__ColorRGBf(), true)
{
}



VarInspectorHelper_cross__ColorRGBf::VarInspectorHelper_cross__ColorRGBf(const gbf::logic::VarInspectorHelper_cross__ColorRGBf * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__ColorRGBf::operator VarInspectorHelper_cross__ColorRGBf^ (const gbf::logic::VarInspectorHelper_cross__ColorRGBf* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__ColorRGBf(const_cast<gbf::logic::VarInspectorHelper_cross__ColorRGBf*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__ColorRGBf^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__ColorRGBf::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ColorRGBf*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__ColorRGBf::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ColorRGBf*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Float3 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float3^ VarInspectorHelper_cross__Float3::mValue::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<gbf::logic::VarInspectorHelper_cross__Float3*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Float3::mValue::set(Clicross::Float3^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Float3*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Float3::VarInspectorHelper_cross__Float3( )
    :VarInspectorHelper_cross__Float3(new gbf::logic::VarInspectorHelper_cross__Float3(), true)
{
}



VarInspectorHelper_cross__Float3::VarInspectorHelper_cross__Float3(const gbf::logic::VarInspectorHelper_cross__Float3 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Float3::operator VarInspectorHelper_cross__Float3^ (const gbf::logic::VarInspectorHelper_cross__Float3* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Float3(const_cast<gbf::logic::VarInspectorHelper_cross__Float3*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Float3^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Float3::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float3*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Float3::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float3*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Quaternion export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Quaternion^ VarInspectorHelper_cross__Quaternion::mValue::get()
 {
	return gcnew Clicross::Quaternion(new cross::Quaternion(((static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Quaternion::mValue::set(Clicross::Quaternion^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Quaternion::VarInspectorHelper_cross__Quaternion( )
    :VarInspectorHelper_cross__Quaternion(new gbf::logic::VarInspectorHelper_cross__Quaternion(), true)
{
}



VarInspectorHelper_cross__Quaternion::VarInspectorHelper_cross__Quaternion(const gbf::logic::VarInspectorHelper_cross__Quaternion * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Quaternion::operator VarInspectorHelper_cross__Quaternion^ (const gbf::logic::VarInspectorHelper_cross__Quaternion* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Quaternion(const_cast<gbf::logic::VarInspectorHelper_cross__Quaternion*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Quaternion^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Quaternion::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Quaternion::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Double3 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double3^ VarInspectorHelper_cross__Double3::mValue::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<gbf::logic::VarInspectorHelper_cross__Double3*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Double3::mValue::set(Clicross::Double3^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Double3*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Double3::VarInspectorHelper_cross__Double3( )
    :VarInspectorHelper_cross__Double3(new gbf::logic::VarInspectorHelper_cross__Double3(), true)
{
}



VarInspectorHelper_cross__Double3::VarInspectorHelper_cross__Double3(const gbf::logic::VarInspectorHelper_cross__Double3 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Double3::operator VarInspectorHelper_cross__Double3^ (const gbf::logic::VarInspectorHelper_cross__Double3* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Double3(const_cast<gbf::logic::VarInspectorHelper_cross__Double3*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Double3^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Double3::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double3*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Double3::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double3*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Quaternion64 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Quaternion64^ VarInspectorHelper_cross__Quaternion64::mValue::get()
 {
	return gcnew Clicross::Quaternion64(new cross::Quaternion64(((static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion64*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Quaternion64::mValue::set(Clicross::Quaternion64^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion64*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Quaternion64::VarInspectorHelper_cross__Quaternion64( )
    :VarInspectorHelper_cross__Quaternion64(new gbf::logic::VarInspectorHelper_cross__Quaternion64(), true)
{
}



VarInspectorHelper_cross__Quaternion64::VarInspectorHelper_cross__Quaternion64(const gbf::logic::VarInspectorHelper_cross__Quaternion64 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Quaternion64::operator VarInspectorHelper_cross__Quaternion64^ (const gbf::logic::VarInspectorHelper_cross__Quaternion64* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Quaternion64(const_cast<gbf::logic::VarInspectorHelper_cross__Quaternion64*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Quaternion64^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Quaternion64::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion64*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Quaternion64::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Quaternion64*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Float2 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float2^ VarInspectorHelper_cross__Float2::mValue::get()
 {
	return gcnew Clicross::Float2(new cross::Float2(((static_cast<gbf::logic::VarInspectorHelper_cross__Float2*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Float2::mValue::set(Clicross::Float2^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Float2*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Float2::VarInspectorHelper_cross__Float2( )
    :VarInspectorHelper_cross__Float2(new gbf::logic::VarInspectorHelper_cross__Float2(), true)
{
}



VarInspectorHelper_cross__Float2::VarInspectorHelper_cross__Float2(const gbf::logic::VarInspectorHelper_cross__Float2 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Float2::operator VarInspectorHelper_cross__Float2^ (const gbf::logic::VarInspectorHelper_cross__Float2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Float2(const_cast<gbf::logic::VarInspectorHelper_cross__Float2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Float2^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Float2::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float2*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Float2::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float2*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Float4 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float4^ VarInspectorHelper_cross__Float4::mValue::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<gbf::logic::VarInspectorHelper_cross__Float4*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Float4::mValue::set(Clicross::Float4^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Float4*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Float4::VarInspectorHelper_cross__Float4( )
    :VarInspectorHelper_cross__Float4(new gbf::logic::VarInspectorHelper_cross__Float4(), true)
{
}



VarInspectorHelper_cross__Float4::VarInspectorHelper_cross__Float4(const gbf::logic::VarInspectorHelper_cross__Float4 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Float4::operator VarInspectorHelper_cross__Float4^ (const gbf::logic::VarInspectorHelper_cross__Float4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Float4(const_cast<gbf::logic::VarInspectorHelper_cross__Float4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Float4^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Float4::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float4*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Float4::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float4*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Float4x4 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float4x4^ VarInspectorHelper_cross__Float4x4::mValue::get()
 {
	return gcnew Clicross::Float4x4(new cross::Float4x4(((static_cast<gbf::logic::VarInspectorHelper_cross__Float4x4*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Float4x4::mValue::set(Clicross::Float4x4^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Float4x4*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Float4x4::VarInspectorHelper_cross__Float4x4( )
    :VarInspectorHelper_cross__Float4x4(new gbf::logic::VarInspectorHelper_cross__Float4x4(), true)
{
}



VarInspectorHelper_cross__Float4x4::VarInspectorHelper_cross__Float4x4(const gbf::logic::VarInspectorHelper_cross__Float4x4 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Float4x4::operator VarInspectorHelper_cross__Float4x4^ (const gbf::logic::VarInspectorHelper_cross__Float4x4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Float4x4(const_cast<gbf::logic::VarInspectorHelper_cross__Float4x4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Float4x4^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Float4x4::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float4x4*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Float4x4::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Float4x4*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Double2 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double2^ VarInspectorHelper_cross__Double2::mValue::get()
 {
	return gcnew Clicross::Double2(new cross::Double2(((static_cast<gbf::logic::VarInspectorHelper_cross__Double2*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Double2::mValue::set(Clicross::Double2^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Double2*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Double2::VarInspectorHelper_cross__Double2( )
    :VarInspectorHelper_cross__Double2(new gbf::logic::VarInspectorHelper_cross__Double2(), true)
{
}



VarInspectorHelper_cross__Double2::VarInspectorHelper_cross__Double2(const gbf::logic::VarInspectorHelper_cross__Double2 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Double2::operator VarInspectorHelper_cross__Double2^ (const gbf::logic::VarInspectorHelper_cross__Double2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Double2(const_cast<gbf::logic::VarInspectorHelper_cross__Double2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Double2^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Double2::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double2*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Double2::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double2*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Double4 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double4^ VarInspectorHelper_cross__Double4::mValue::get()
 {
	return gcnew Clicross::Double4(new cross::Double4(((static_cast<gbf::logic::VarInspectorHelper_cross__Double4*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Double4::mValue::set(Clicross::Double4^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Double4*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Double4::VarInspectorHelper_cross__Double4( )
    :VarInspectorHelper_cross__Double4(new gbf::logic::VarInspectorHelper_cross__Double4(), true)
{
}



VarInspectorHelper_cross__Double4::VarInspectorHelper_cross__Double4(const gbf::logic::VarInspectorHelper_cross__Double4 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Double4::operator VarInspectorHelper_cross__Double4^ (const gbf::logic::VarInspectorHelper_cross__Double4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Double4(const_cast<gbf::logic::VarInspectorHelper_cross__Double4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Double4^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Double4::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double4*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Double4::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double4*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__Double4x4 export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double4x4^ VarInspectorHelper_cross__Double4x4::mValue::get()
 {
	return gcnew Clicross::Double4x4(new cross::Double4x4(((static_cast<gbf::logic::VarInspectorHelper_cross__Double4x4*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__Double4x4::mValue::set(Clicross::Double4x4^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__Double4x4*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__Double4x4::VarInspectorHelper_cross__Double4x4( )
    :VarInspectorHelper_cross__Double4x4(new gbf::logic::VarInspectorHelper_cross__Double4x4(), true)
{
}



VarInspectorHelper_cross__Double4x4::VarInspectorHelper_cross__Double4x4(const gbf::logic::VarInspectorHelper_cross__Double4x4 * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__Double4x4::operator VarInspectorHelper_cross__Double4x4^ (const gbf::logic::VarInspectorHelper_cross__Double4x4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__Double4x4(const_cast<gbf::logic::VarInspectorHelper_cross__Double4x4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__Double4x4^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__Double4x4::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double4x4*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__Double4x4::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__Double4x4*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__FogCommonSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::FogCommonSetting^ VarInspectorHelper_cross__FogCommonSetting::mValue::get()
 {
	return gcnew Clicross::FogCommonSetting(new cross::FogCommonSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__FogCommonSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__FogCommonSetting::mValue::set(Clicross::FogCommonSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__FogCommonSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__FogCommonSetting::VarInspectorHelper_cross__FogCommonSetting( )
    :VarInspectorHelper_cross__FogCommonSetting(new gbf::logic::VarInspectorHelper_cross__FogCommonSetting(), true)
{
}



VarInspectorHelper_cross__FogCommonSetting::VarInspectorHelper_cross__FogCommonSetting(const gbf::logic::VarInspectorHelper_cross__FogCommonSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__FogCommonSetting::operator VarInspectorHelper_cross__FogCommonSetting^ (const gbf::logic::VarInspectorHelper_cross__FogCommonSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__FogCommonSetting(const_cast<gbf::logic::VarInspectorHelper_cross__FogCommonSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__FogCommonSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__FogCommonSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__FogCommonSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__FogCommonSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__FogCommonSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__ScreenFogSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::ScreenFogSetting^ VarInspectorHelper_cross__ScreenFogSetting::mValue::get()
 {
	return gcnew Clicross::ScreenFogSetting(new cross::ScreenFogSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__ScreenFogSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__ScreenFogSetting::mValue::set(Clicross::ScreenFogSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__ScreenFogSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__ScreenFogSetting::VarInspectorHelper_cross__ScreenFogSetting( )
    :VarInspectorHelper_cross__ScreenFogSetting(new gbf::logic::VarInspectorHelper_cross__ScreenFogSetting(), true)
{
}



VarInspectorHelper_cross__ScreenFogSetting::VarInspectorHelper_cross__ScreenFogSetting(const gbf::logic::VarInspectorHelper_cross__ScreenFogSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__ScreenFogSetting::operator VarInspectorHelper_cross__ScreenFogSetting^ (const gbf::logic::VarInspectorHelper_cross__ScreenFogSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__ScreenFogSetting(const_cast<gbf::logic::VarInspectorHelper_cross__ScreenFogSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__ScreenFogSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__ScreenFogSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ScreenFogSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__ScreenFogSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__ScreenFogSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__VolumetricFogSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::VolumetricFogSetting^ VarInspectorHelper_cross__VolumetricFogSetting::mValue::get()
 {
	return gcnew Clicross::VolumetricFogSetting(new cross::VolumetricFogSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__VolumetricFogSetting::mValue::set(Clicross::VolumetricFogSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__VolumetricFogSetting::VarInspectorHelper_cross__VolumetricFogSetting( )
    :VarInspectorHelper_cross__VolumetricFogSetting(new gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting(), true)
{
}



VarInspectorHelper_cross__VolumetricFogSetting::VarInspectorHelper_cross__VolumetricFogSetting(const gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__VolumetricFogSetting::operator VarInspectorHelper_cross__VolumetricFogSetting^ (const gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__VolumetricFogSetting(const_cast<gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__VolumetricFogSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__VolumetricFogSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__VolumetricFogSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VolumetricFogSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__VFogQualityTradeSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::VFogQualityTradeSetting^ VarInspectorHelper_cross__VFogQualityTradeSetting::mValue::get()
 {
	return gcnew Clicross::VFogQualityTradeSetting(new cross::VFogQualityTradeSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__VFogQualityTradeSetting::mValue::set(Clicross::VFogQualityTradeSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__VFogQualityTradeSetting::VarInspectorHelper_cross__VFogQualityTradeSetting( )
    :VarInspectorHelper_cross__VFogQualityTradeSetting(new gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting(), true)
{
}



VarInspectorHelper_cross__VFogQualityTradeSetting::VarInspectorHelper_cross__VFogQualityTradeSetting(const gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__VFogQualityTradeSetting::operator VarInspectorHelper_cross__VFogQualityTradeSetting^ (const gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__VFogQualityTradeSetting(const_cast<gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__VFogQualityTradeSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__VFogQualityTradeSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__VFogQualityTradeSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VFogQualityTradeSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__VFogDustSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::VFogDustSetting^ VarInspectorHelper_cross__VFogDustSetting::mValue::get()
 {
	return gcnew Clicross::VFogDustSetting(new cross::VFogDustSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__VFogDustSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__VFogDustSetting::mValue::set(Clicross::VFogDustSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__VFogDustSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__VFogDustSetting::VarInspectorHelper_cross__VFogDustSetting( )
    :VarInspectorHelper_cross__VFogDustSetting(new gbf::logic::VarInspectorHelper_cross__VFogDustSetting(), true)
{
}



VarInspectorHelper_cross__VFogDustSetting::VarInspectorHelper_cross__VFogDustSetting(const gbf::logic::VarInspectorHelper_cross__VFogDustSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__VFogDustSetting::operator VarInspectorHelper_cross__VFogDustSetting^ (const gbf::logic::VarInspectorHelper_cross__VFogDustSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__VFogDustSetting(const_cast<gbf::logic::VarInspectorHelper_cross__VFogDustSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__VFogDustSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__VFogDustSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VFogDustSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__VFogDustSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__VFogDustSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CloudCommonSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudCommonSetting^ VarInspectorHelper_cross__CloudCommonSetting::mValue::get()
 {
	return gcnew Clicross::CloudCommonSetting(new cross::CloudCommonSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__CloudCommonSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__CloudCommonSetting::mValue::set(Clicross::CloudCommonSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__CloudCommonSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__CloudCommonSetting::VarInspectorHelper_cross__CloudCommonSetting( )
    :VarInspectorHelper_cross__CloudCommonSetting(new gbf::logic::VarInspectorHelper_cross__CloudCommonSetting(), true)
{
}



VarInspectorHelper_cross__CloudCommonSetting::VarInspectorHelper_cross__CloudCommonSetting(const gbf::logic::VarInspectorHelper_cross__CloudCommonSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CloudCommonSetting::operator VarInspectorHelper_cross__CloudCommonSetting^ (const gbf::logic::VarInspectorHelper_cross__CloudCommonSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CloudCommonSetting(const_cast<gbf::logic::VarInspectorHelper_cross__CloudCommonSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CloudCommonSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CloudCommonSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudCommonSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CloudCommonSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudCommonSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CloudShadingSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudShadingSetting^ VarInspectorHelper_cross__CloudShadingSetting::mValue::get()
 {
	return gcnew Clicross::CloudShadingSetting(new cross::CloudShadingSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadingSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__CloudShadingSetting::mValue::set(Clicross::CloudShadingSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadingSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__CloudShadingSetting::VarInspectorHelper_cross__CloudShadingSetting( )
    :VarInspectorHelper_cross__CloudShadingSetting(new gbf::logic::VarInspectorHelper_cross__CloudShadingSetting(), true)
{
}



VarInspectorHelper_cross__CloudShadingSetting::VarInspectorHelper_cross__CloudShadingSetting(const gbf::logic::VarInspectorHelper_cross__CloudShadingSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CloudShadingSetting::operator VarInspectorHelper_cross__CloudShadingSetting^ (const gbf::logic::VarInspectorHelper_cross__CloudShadingSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CloudShadingSetting(const_cast<gbf::logic::VarInspectorHelper_cross__CloudShadingSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CloudShadingSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CloudShadingSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadingSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CloudShadingSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadingSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CloudGeometrySetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudGeometrySetting^ VarInspectorHelper_cross__CloudGeometrySetting::mValue::get()
 {
	return gcnew Clicross::CloudGeometrySetting(new cross::CloudGeometrySetting(((static_cast<gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__CloudGeometrySetting::mValue::set(Clicross::CloudGeometrySetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__CloudGeometrySetting::VarInspectorHelper_cross__CloudGeometrySetting( )
    :VarInspectorHelper_cross__CloudGeometrySetting(new gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting(), true)
{
}



VarInspectorHelper_cross__CloudGeometrySetting::VarInspectorHelper_cross__CloudGeometrySetting(const gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CloudGeometrySetting::operator VarInspectorHelper_cross__CloudGeometrySetting^ (const gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CloudGeometrySetting(const_cast<gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CloudGeometrySetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CloudGeometrySetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CloudGeometrySetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudGeometrySetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CloudNoiseSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudNoiseSetting^ VarInspectorHelper_cross__CloudNoiseSetting::mValue::get()
 {
	return gcnew Clicross::CloudNoiseSetting(new cross::CloudNoiseSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__CloudNoiseSetting::mValue::set(Clicross::CloudNoiseSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__CloudNoiseSetting::VarInspectorHelper_cross__CloudNoiseSetting( )
    :VarInspectorHelper_cross__CloudNoiseSetting(new gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting(), true)
{
}



VarInspectorHelper_cross__CloudNoiseSetting::VarInspectorHelper_cross__CloudNoiseSetting(const gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CloudNoiseSetting::operator VarInspectorHelper_cross__CloudNoiseSetting^ (const gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CloudNoiseSetting(const_cast<gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CloudNoiseSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CloudNoiseSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CloudNoiseSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudNoiseSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__CloudShadowSetting export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudShadowSetting^ VarInspectorHelper_cross__CloudShadowSetting::mValue::get()
 {
	return gcnew Clicross::CloudShadowSetting(new cross::CloudShadowSetting(((static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadowSetting*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__CloudShadowSetting::mValue::set(Clicross::CloudShadowSetting^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadowSetting*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__CloudShadowSetting::VarInspectorHelper_cross__CloudShadowSetting( )
    :VarInspectorHelper_cross__CloudShadowSetting(new gbf::logic::VarInspectorHelper_cross__CloudShadowSetting(), true)
{
}



VarInspectorHelper_cross__CloudShadowSetting::VarInspectorHelper_cross__CloudShadowSetting(const gbf::logic::VarInspectorHelper_cross__CloudShadowSetting * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__CloudShadowSetting::operator VarInspectorHelper_cross__CloudShadowSetting^ (const gbf::logic::VarInspectorHelper_cross__CloudShadowSetting* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__CloudShadowSetting(const_cast<gbf::logic::VarInspectorHelper_cross__CloudShadowSetting*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__CloudShadowSetting^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__CloudShadowSetting::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadowSetting*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__CloudShadowSetting::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__CloudShadowSetting*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__SkyAtmosphereOuterParam export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::SkyAtmosphereOuterParam^ VarInspectorHelper_cross__SkyAtmosphereOuterParam::mValue::get()
 {
	return gcnew Clicross::SkyAtmosphereOuterParam(new cross::SkyAtmosphereOuterParam(((static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__SkyAtmosphereOuterParam::mValue::set(Clicross::SkyAtmosphereOuterParam^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__SkyAtmosphereOuterParam::VarInspectorHelper_cross__SkyAtmosphereOuterParam( )
    :VarInspectorHelper_cross__SkyAtmosphereOuterParam(new gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam(), true)
{
}



VarInspectorHelper_cross__SkyAtmosphereOuterParam::VarInspectorHelper_cross__SkyAtmosphereOuterParam(const gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__SkyAtmosphereOuterParam::operator VarInspectorHelper_cross__SkyAtmosphereOuterParam^ (const gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__SkyAtmosphereOuterParam(const_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__SkyAtmosphereOuterParam^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__SkyAtmosphereOuterParam::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__SkyAtmosphereOuterParam::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereOuterParam*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cross__SkyAtmosphereConfig export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicross::SkyAtmosphereConfig^ VarInspectorHelper_cross__SkyAtmosphereConfig::mValue::get()
 {
	return gcnew Clicross::SkyAtmosphereConfig(new cross::SkyAtmosphereConfig(((static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cross__SkyAtmosphereConfig::mValue::set(Clicross::SkyAtmosphereConfig^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cross__SkyAtmosphereConfig::VarInspectorHelper_cross__SkyAtmosphereConfig( )
    :VarInspectorHelper_cross__SkyAtmosphereConfig(new gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig(), true)
{
}



VarInspectorHelper_cross__SkyAtmosphereConfig::VarInspectorHelper_cross__SkyAtmosphereConfig(const gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cross__SkyAtmosphereConfig::operator VarInspectorHelper_cross__SkyAtmosphereConfig^ (const gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cross__SkyAtmosphereConfig(const_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cross__SkyAtmosphereConfig^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cross__SkyAtmosphereConfig::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cross__SkyAtmosphereConfig::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cross__SkyAtmosphereConfig*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// VarInspectorHelper_cegf__Curve export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
Clicegf::Curve^ VarInspectorHelper_cegf__Curve::mValue::get()
 {
	return gcnew Clicegf::Curve(new cegf::Curve(((static_cast<gbf::logic::VarInspectorHelper_cegf__Curve*>(this->_native))->mValue)) , true);
 }
 void VarInspectorHelper_cegf__Curve::mValue::set(Clicegf::Curve^ value )
 {
	(static_cast<gbf::logic::VarInspectorHelper_cegf__Curve*>(this->_native))->mValue = value;
 }


//constructor export here
VarInspectorHelper_cegf__Curve::VarInspectorHelper_cegf__Curve( )
    :VarInspectorHelper_cegf__Curve(new gbf::logic::VarInspectorHelper_cegf__Curve(), true)
{
}



VarInspectorHelper_cegf__Curve::VarInspectorHelper_cegf__Curve(const gbf::logic::VarInspectorHelper_cegf__Curve * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_cegf__Curve::operator VarInspectorHelper_cegf__Curve^ (const gbf::logic::VarInspectorHelper_cegf__Curve* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_cegf__Curve(const_cast<gbf::logic::VarInspectorHelper_cegf__Curve*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_cegf__Curve^)managedObj;
    }
    else
        return nullptr;
}

void VarInspectorHelper_cegf__Curve::SyncFromRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cegf__Curve*>(this->_native))->SyncFromRuntime( );
}

void VarInspectorHelper_cegf__Curve::UpdateToRuntime( )
{
    (static_cast<gbf::logic::VarInspectorHelper_cegf__Curve*>(this->_native))->UpdateToRuntime( );
}


}   //end namespace Cligbf
}   //end namespace logic

// TempStringVec export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::TempStringVec::stringVec export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(TempStringVec::, stringVecCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
TempStringVec::stringVecCliType^ TempStringVec::stringVec::get()
 {
	return (static_cast<cross::TempStringVec*>(this->_native))->stringVec;
 }
 void TempStringVec::stringVec::set(TempStringVec::stringVecCliType^ value )
 {
	(static_cast<cross::TempStringVec*>(this->_native))->stringVec = *value->_native;
 }


//constructor export here
TempStringVec::TempStringVec(): TempStringVec(new cross::TempStringVec(), true) {}


TempStringVec::TempStringVec(const cross::TempStringVec * obj, bool created_by_clr): 
    _native(const_cast<cross::TempStringVec *>(obj))
	, _created_by_clr(created_by_clr)
{
}

TempStringVec::operator TempStringVec^ (const cross::TempStringVec* t)
{
    if(t)
    {
        return gcnew TempStringVec(const_cast<cross::TempStringVec*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionCreateNodeInfo export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ ExpressionCreateNodeInfo::menuName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->menuName)).c_str());
 }
 void ExpressionCreateNodeInfo::menuName::set(System::String^ value )
 {
	((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->menuName) = (ClangenCli::ToNativeString(value));
 }

System::String^ ExpressionCreateNodeInfo::className::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->className)).c_str());
 }
 void ExpressionCreateNodeInfo::className::set(System::String^ value )
 {
	((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->className) = (ClangenCli::ToNativeString(value));
 }

System::String^ ExpressionCreateNodeInfo::materialFunctionGuid::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->materialFunctionGuid)).c_str());
 }
 void ExpressionCreateNodeInfo::materialFunctionGuid::set(System::String^ value )
 {
	((static_cast<cross::ExpressionCreateNodeInfo*>(this->_native))->materialFunctionGuid) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
ExpressionCreateNodeInfo::ExpressionCreateNodeInfo( )
    :ExpressionCreateNodeInfo(new cross::ExpressionCreateNodeInfo(), true)
{
}

ExpressionCreateNodeInfo::ExpressionCreateNodeInfo(Clicross::ExpressionCreateNodeInfo^ expressionCreateNodeInfo )
    :ExpressionCreateNodeInfo(new cross::ExpressionCreateNodeInfo((const cross::ExpressionCreateNodeInfo& )(expressionCreateNodeInfo)), true)
{
}



ExpressionCreateNodeInfo::ExpressionCreateNodeInfo(const cross::ExpressionCreateNodeInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::ExpressionCreateNodeInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ExpressionCreateNodeInfo::operator ExpressionCreateNodeInfo^ (const cross::ExpressionCreateNodeInfo* t)
{
    if(t)
    {
        return gcnew ExpressionCreateNodeInfo(const_cast<cross::ExpressionCreateNodeInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionCreateGroupInfo export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::ExpressionCreateGroupInfo::ExpressionInfos export start
	#define STLDECL_MANAGEDTYPE Clicross::ExpressionCreateNodeInfo^
	#define STLDECL_NATIVETYPE cross::ExpressionCreateNodeInfo
	CPP_DECLARE_STLVECTOR(ExpressionCreateGroupInfo::, ExpressionInfosCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ ExpressionCreateGroupInfo::categoryName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionCreateGroupInfo*>(this->_native))->categoryName)).c_str());
 }
 void ExpressionCreateGroupInfo::categoryName::set(System::String^ value )
 {
	((static_cast<cross::ExpressionCreateGroupInfo*>(this->_native))->categoryName) = (ClangenCli::ToNativeString(value));
 }

ExpressionCreateGroupInfo::ExpressionInfosCliType^ ExpressionCreateGroupInfo::ExpressionInfos::get()
 {
	return (static_cast<cross::ExpressionCreateGroupInfo*>(this->_native))->ExpressionInfos;
 }
 void ExpressionCreateGroupInfo::ExpressionInfos::set(ExpressionCreateGroupInfo::ExpressionInfosCliType^ value )
 {
	(static_cast<cross::ExpressionCreateGroupInfo*>(this->_native))->ExpressionInfos = *value->_native;
 }


//constructor export here
ExpressionCreateGroupInfo::ExpressionCreateGroupInfo( )
    :ExpressionCreateGroupInfo(new cross::ExpressionCreateGroupInfo(), true)
{
}

ExpressionCreateGroupInfo::ExpressionCreateGroupInfo(Clicross::ExpressionCreateGroupInfo^ expressionCreateGroupInfo )
    :ExpressionCreateGroupInfo(new cross::ExpressionCreateGroupInfo((const cross::ExpressionCreateGroupInfo& )(expressionCreateGroupInfo)), true)
{
}



ExpressionCreateGroupInfo::ExpressionCreateGroupInfo(const cross::ExpressionCreateGroupInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::ExpressionCreateGroupInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ExpressionCreateGroupInfo::operator ExpressionCreateGroupInfo^ (const cross::ExpressionCreateGroupInfo* t)
{
    if(t)
    {
        return gcnew ExpressionCreateGroupInfo(const_cast<cross::ExpressionCreateGroupInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionCreateMenuInfo export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::ExpressionCreateMenuInfo::Groups export start
	#define STLDECL_MANAGEDTYPE Clicross::ExpressionCreateGroupInfo^
	#define STLDECL_NATIVETYPE cross::ExpressionCreateGroupInfo
	CPP_DECLARE_STLVECTOR(ExpressionCreateMenuInfo::, GroupsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
ExpressionCreateMenuInfo::GroupsCliType^ ExpressionCreateMenuInfo::Groups::get()
 {
	return (static_cast<cross::ExpressionCreateMenuInfo*>(this->_native))->Groups;
 }
 void ExpressionCreateMenuInfo::Groups::set(ExpressionCreateMenuInfo::GroupsCliType^ value )
 {
	(static_cast<cross::ExpressionCreateMenuInfo*>(this->_native))->Groups = *value->_native;
 }

unsigned long long ExpressionCreateMenuInfo::PinHandle::get()
 {
	return (static_cast<cross::ExpressionCreateMenuInfo*>(this->_native))->PinHandle;
 }
 void ExpressionCreateMenuInfo::PinHandle::set(unsigned long long value )
 {
	(static_cast<cross::ExpressionCreateMenuInfo*>(this->_native))->PinHandle = value;
 }


//constructor export here
ExpressionCreateMenuInfo::ExpressionCreateMenuInfo( )
    :ExpressionCreateMenuInfo(new cross::ExpressionCreateMenuInfo(), true)
{
}

ExpressionCreateMenuInfo::ExpressionCreateMenuInfo(Clicross::ExpressionCreateMenuInfo^ expressionCreateMenuInfo )
    :ExpressionCreateMenuInfo(new cross::ExpressionCreateMenuInfo((const cross::ExpressionCreateMenuInfo& )(expressionCreateMenuInfo)), true)
{
}



ExpressionCreateMenuInfo::ExpressionCreateMenuInfo(const cross::ExpressionCreateMenuInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::ExpressionCreateMenuInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ExpressionCreateMenuInfo::operator ExpressionCreateMenuInfo^ (const cross::ExpressionCreateMenuInfo* t)
{
    if(t)
    {
        return gcnew ExpressionCreateMenuInfo(const_cast<cross::ExpressionCreateMenuInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialEditor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialEditor::MaterialEditor( )
    :MaterialEditor(new cross::MaterialEditor(), true)
{
}

MaterialEditor::MaterialEditor(System::String^ fileName, Clicross::MaterialEditorCallback^ callback )
    :MaterialEditor(new cross::MaterialEditor(ClangenCli::ToNativeString(fileName).c_str(), ( cross::MaterialEditorCallback* )(callback)), true)
{
}



MaterialEditor::MaterialEditor(const cross::MaterialEditor * obj, bool created_by_clr): Clicross::EditorImGuiContext(obj, created_by_clr)
{
}

MaterialEditor::operator MaterialEditor^ (const cross::MaterialEditor* t)
{
    if(t)
    {
        return gcnew MaterialEditor(const_cast<cross::MaterialEditor*>(t));
    }
    else
        return nullptr;
}

void MaterialEditor::Open(System::String^ filePath )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->Open( ClangenCli::ToNativeString(filePath).c_str());
}

void MaterialEditor::ZoomToSurfaceDataExpression( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->ZoomToSurfaceDataExpression( );
}

void MaterialEditor::ZoomToFunctionOutput( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->ZoomToFunctionOutput( );
}

void MaterialEditor::ZoomToExpression(Clicross::MaterialExpression^ expression )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->ZoomToExpression( ( cross::MaterialExpression* )(expression));
}

void MaterialEditor::Undo( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->Undo( );
}

void MaterialEditor::Redo( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->Redo( );
}

bool MaterialEditor::Apply(bool force )
{
    return (static_cast<cross::MaterialEditor*>(this->_native))->Apply( force);
}

System::String^ MaterialEditor::HLSLCode( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::MaterialEditor*>(this->_native))->HLSLCode( ))).c_str());
}

bool MaterialEditor::IsResourceChanged( )
{
    return (static_cast<cross::MaterialEditor*>(this->_native))->IsResourceChanged( );
}

Clicross::MaterialExpression^ MaterialEditor::GetSelectedExpression(int index )
{
    return (Clicross::MaterialExpression^)((static_cast<cross::MaterialEditor*>(this->_native))->GetSelectedExpression( index));
}

int MaterialEditor::GetSelectedExpressionsCount( )
{
    return (static_cast<cross::MaterialEditor*>(this->_native))->GetSelectedExpressionsCount( );
}

Clicross::MaterialExpression^ MaterialEditor::GetExpression(int index )
{
    return (Clicross::MaterialExpression^)((static_cast<cross::MaterialEditor*>(this->_native))->GetExpression( index));
}

int MaterialEditor::GetExpressionsCount( )
{
    return (static_cast<cross::MaterialEditor*>(this->_native))->GetExpressionsCount( );
}

Clicross::MaterialDefines^ MaterialEditor::GetMaterialDefines( )
{
    return (Clicross::MaterialDefines^)((static_cast<cross::MaterialEditor*>(this->_native))->GetMaterialDefines( ));
}

Clicross::MaterialFunctionDefines^ MaterialEditor::GetMaterialFunctionDefines( )
{
    return (Clicross::MaterialFunctionDefines^)((static_cast<cross::MaterialEditor*>(this->_native))->GetMaterialFunctionDefines( ));
}

void MaterialEditor::ResetRenderState( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->ResetRenderState( );
}

void MaterialEditor::SetPropertyChangedFlagForEditor(bool flag )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->SetPropertyChangedFlagForEditor( flag);
}

int MaterialEditor::GetMaterialParameterGroupsCount( )
{
    return (static_cast<cross::MaterialEditor*>(this->_native))->GetMaterialParameterGroupsCount( );
}

Clicross::MaterialParameterGroup^ MaterialEditor::GetMaterialParameterGroup(int index )
{
    return (Clicross::MaterialParameterGroup^)((static_cast<cross::MaterialEditor*>(this->_native))->GetMaterialParameterGroup( index));
}

void MaterialEditor::SetPassRenderGroup(System::String^ passID, unsigned int renderGroup )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->SetPassRenderGroup( ClangenCli::ToNativeString(passID).c_str(), renderGroup);
}

void MaterialEditor::CreateMaterialExpression(Clicross::ExpressionCreateNodeInfo^ expressionCreateInfo, int postion_x, int position_y )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->CreateMaterialExpression( *((cross::ExpressionCreateNodeInfo*)(expressionCreateInfo)), postion_x, position_y);
}

void MaterialEditor::OnPropertyChange(Clicross::MaterialExpression^ expression )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnPropertyChange( ( cross::MaterialExpression* )(expression));
}

void MaterialEditor::OnMaterialDefinesChange( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnMaterialDefinesChange( );
}

void MaterialEditor::OnMaterialFunctionDefinesChange( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnMaterialFunctionDefinesChange( );
}

void MaterialEditor::OnMaterialFunctionChange(System::String^ materialFunctionGuid )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnMaterialFunctionChange( ClangenCli::ToNativeString(materialFunctionGuid).c_str());
}

void MaterialEditor::OnParameterChange(Clicross::MaterialParameter^ parameter )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnParameterChange( ( cross::MaterialParameter* )(parameter));
}

void MaterialEditor::OnMaterialParameterCollectionSelectChange( )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnMaterialParameterCollectionSelectChange( );
}

void MaterialEditor::OnMaterialParameterCollectionChange(System::String^ mpcGuid )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnMaterialParameterCollectionChange( ClangenCli::ToNativeString(mpcGuid).c_str());
}

void MaterialEditor::RegisterMaterialFunction(System::String^ materialFunctions )
{
    cross::MaterialEditor::RegisterMaterialFunction( ClangenCli::ToNativeString(materialFunctions).c_str());
}

void MaterialEditor::ClearMaterialFunctions( )
{
    cross::MaterialEditor::ClearMaterialFunctions( );
}

void MaterialEditor::OnResourceDragEnd(System::String^ resourceGUID, unsigned int positionX, unsigned int positionY )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->OnResourceDragEnd( ClangenCli::ToNativeString(resourceGUID).c_str(), positionX, positionY);
}

void MaterialEditor::Action_PasteExpressions(System::String^ expressionsStr, int targetX, int targetY )
{
    (static_cast<cross::MaterialEditor*>(this->_native))->Action_PasteExpressions( ClangenCli::ToNativeString(expressionsStr).c_str(), targetX, targetY);
}


}   //end namespace Clicross

// PreviewBase export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


PreviewBase::PreviewBase(const cross::PreviewBase * obj, bool created_by_clr): 
    _native(const_cast<cross::PreviewBase *>(obj))
	, _created_by_clr(created_by_clr)
{
}

PreviewBase::operator PreviewBase^ (const cross::PreviewBase* t)
{
    if(t)
    {
        return gcnew PreviewBase(const_cast<cross::PreviewBase*>(t));
    }
    else
        return nullptr;
}

void PreviewBase::OnResize(Clicross::Float2^ size )
{
    (static_cast<cross::PreviewBase*>(this->_native))->OnResize( (const cross::Float2& )(size));
}

void PreviewBase::OnMouseMoveEvent(int mouseX, int mouseY )
{
    (static_cast<cross::PreviewBase*>(this->_native))->OnMouseMoveEvent( mouseX, mouseY);
}

void PreviewBase::OnMouseWheelEvent(int mouseDeltaZ )
{
    (static_cast<cross::PreviewBase*>(this->_native))->OnMouseWheelEvent( mouseDeltaZ);
}

void PreviewBase::OnKeyEvent(Clicross::EditorKey btn, bool isDown )
{
    (static_cast<cross::PreviewBase*>(this->_native))->OnKeyEvent( static_cast<cross::EditorKey>(btn), isDown);
}

void PreviewBase::OnActivate(bool active )
{
    (static_cast<cross::PreviewBase*>(this->_native))->OnActivate( active);
}

int PreviewBase::GetTexture( )
{
    return (static_cast<cross::PreviewBase*>(this->_native))->GetTexture( );
}


}   //end namespace Clicross

// MaterialPreview export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialPreview::MaterialPreview(Clicross::MaterialEditor^ editor )
    :MaterialPreview(new cross::MaterialPreview(( cross::MaterialEditor* )(editor)), true)
{
}

MaterialPreview::MaterialPreview(Clicross::MaterialInstanceEditor^ editor )
    :MaterialPreview(new cross::MaterialPreview(( cross::MaterialInstanceEditor* )(editor)), true)
{
}



MaterialPreview::MaterialPreview(const cross::MaterialPreview * obj, bool created_by_clr): Clicross::PreviewBase(obj, created_by_clr)
{
}

MaterialPreview::operator MaterialPreview^ (const cross::MaterialPreview* t)
{
    if(t)
    {
        return gcnew MaterialPreview(const_cast<cross::MaterialPreview*>(t));
    }
    else
        return nullptr;
}

void MaterialPreview::SetPreviewMeshType(System::String^ meshType )
{
    (static_cast<cross::MaterialPreview*>(this->_native))->SetPreviewMeshType( ClangenCli::ToNativeString(meshType).c_str());
}

void MaterialPreview::SetCustomPreviewMesh(System::String^ guid )
{
    (static_cast<cross::MaterialPreview*>(this->_native))->SetCustomPreviewMesh( ClangenCli::ToNativeString(guid).c_str());
}

void MaterialPreview::SetWorldEnable(bool enable )
{
    (static_cast<cross::MaterialPreview*>(this->_native))->SetWorldEnable( enable);
}

void MaterialPreview::Tick( )
{
    (static_cast<cross::MaterialPreview*>(this->_native))->Tick( );
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


