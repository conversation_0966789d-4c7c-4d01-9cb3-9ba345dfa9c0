//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// WorkflowVariableDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ WorkflowVariableDesc::Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowVariableDesc*>(this->_native))->Description)).c_str());
 }
 void WorkflowVariableDesc::Description::set(System::String^ value )
 {
	((static_cast<cross::WorkflowVariableDesc*>(this->_native))->Description) = (ClangenCli::ToNativeString(value));
 }

Clicross::GbfValueFieldType WorkflowVariableDesc::Type::get()
 {
	return (Clicross::GbfValueFieldType)((int)(static_cast<cross::WorkflowVariableDesc*>(this->_native))->Type);
 }
 void WorkflowVariableDesc::Type::set(Clicross::GbfValueFieldType value )
 {
	(static_cast<cross::WorkflowVariableDesc*>(this->_native))->Type = static_cast<cross::GbfValueFieldType>(value);
 }

Clicross::GbfValueFieldCategory WorkflowVariableDesc::Category::get()
 {
	return (Clicross::GbfValueFieldCategory)((int)(static_cast<cross::WorkflowVariableDesc*>(this->_native))->Category);
 }
 void WorkflowVariableDesc::Category::set(Clicross::GbfValueFieldCategory value )
 {
	(static_cast<cross::WorkflowVariableDesc*>(this->_native))->Category = static_cast<cross::GbfValueFieldCategory>(value);
 }

System::String^ WorkflowVariableDesc::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowVariableDesc*>(this->_native))->Value)).c_str());
 }
 void WorkflowVariableDesc::Value::set(System::String^ value )
 {
	((static_cast<cross::WorkflowVariableDesc*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
WorkflowVariableDesc::WorkflowVariableDesc( )
    :WorkflowVariableDesc(new cross::WorkflowVariableDesc(), true)
{
}



WorkflowVariableDesc::WorkflowVariableDesc(const cross::WorkflowVariableDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowVariableDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowVariableDesc::operator WorkflowVariableDesc^ (const cross::WorkflowVariableDesc* t)
{
    if(t)
    {
        return gcnew WorkflowVariableDesc(const_cast<cross::WorkflowVariableDesc*>(t));
    }
    else
        return nullptr;
}

void WorkflowVariableDesc::SetName(System::String^ new_name )
{
    (static_cast<cross::WorkflowVariableDesc*>(this->_native))->SetName( ClangenCli::ToNativeString(new_name).c_str());
}

System::String^ WorkflowVariableDesc::GetName( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::WorkflowVariableDesc*>(this->_native))->GetName( ))).c_str());
}

void WorkflowVariableDesc::SetUserObjectClassName(System::String^ _in_class_name )
{
    (static_cast<cross::WorkflowVariableDesc*>(this->_native))->SetUserObjectClassName( ClangenCli::ToNativeString(_in_class_name).c_str());
}

System::String^ WorkflowVariableDesc::GetUserObjectClassName( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::WorkflowVariableDesc*>(this->_native))->GetUserObjectClassName( ))).c_str());
}

Cligbf::logic::VarInspectorHelper^ WorkflowVariableDesc::GetInspectorHelper( )
{
    return (Cligbf::logic::VarInspectorHelper^)((static_cast<cross::WorkflowVariableDesc*>(this->_native))->GetInspectorHelper( ));
}


}   //end namespace Clicross

// WorkflowFunctionDesc export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::WorkflowFunctionDesc::Args export start
	#define STLDECL_MANAGEDTYPE Clicross::WorkflowArgDesc^
	#define STLDECL_NATIVETYPE cross::WorkflowArgDesc
	CPP_DECLARE_STLVECTOR(WorkflowFunctionDesc::, ArgsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::WorkflowFunctionDesc::Returns export start
	#define STLDECL_MANAGEDTYPE Clicross::WorkflowArgDesc^
	#define STLDECL_NATIVETYPE cross::WorkflowArgDesc
	CPP_DECLARE_STLVECTOR(WorkflowFunctionDesc::, ReturnsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ WorkflowFunctionDesc::Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Description)).c_str());
 }
 void WorkflowFunctionDesc::Description::set(System::String^ value )
 {
	((static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Description) = (ClangenCli::ToNativeString(value));
 }

WorkflowFunctionDesc::ArgsCliType^ WorkflowFunctionDesc::Args::get()
 {
	return (static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Args;
 }
 void WorkflowFunctionDesc::Args::set(WorkflowFunctionDesc::ArgsCliType^ value )
 {
	(static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Args = *value->_native;
 }

WorkflowFunctionDesc::ReturnsCliType^ WorkflowFunctionDesc::Returns::get()
 {
	return (static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Returns;
 }
 void WorkflowFunctionDesc::Returns::set(WorkflowFunctionDesc::ReturnsCliType^ value )
 {
	(static_cast<cross::WorkflowFunctionDesc*>(this->_native))->Returns = *value->_native;
 }


//constructor export here
WorkflowFunctionDesc::WorkflowFunctionDesc( )
    :WorkflowFunctionDesc(new cross::WorkflowFunctionDesc(), true)
{
}



WorkflowFunctionDesc::WorkflowFunctionDesc(const cross::WorkflowFunctionDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowFunctionDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowFunctionDesc::operator WorkflowFunctionDesc^ (const cross::WorkflowFunctionDesc* t)
{
    if(t)
    {
        return gcnew WorkflowFunctionDesc(const_cast<cross::WorkflowFunctionDesc*>(t));
    }
    else
        return nullptr;
}

void WorkflowFunctionDesc::SetName(System::String^ new_name )
{
    (static_cast<cross::WorkflowFunctionDesc*>(this->_native))->SetName( ClangenCli::ToNativeString(new_name).c_str());
}

System::String^ WorkflowFunctionDesc::GetName( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::WorkflowFunctionDesc*>(this->_native))->GetName( ))).c_str());
}


}   //end namespace Clicross

// WorkflowArgDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ WorkflowArgDesc::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowArgDesc*>(this->_native))->Name)).c_str());
 }
 void WorkflowArgDesc::Name::set(System::String^ value )
 {
	((static_cast<cross::WorkflowArgDesc*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

System::String^ WorkflowArgDesc::Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowArgDesc*>(this->_native))->Description)).c_str());
 }
 void WorkflowArgDesc::Description::set(System::String^ value )
 {
	((static_cast<cross::WorkflowArgDesc*>(this->_native))->Description) = (ClangenCli::ToNativeString(value));
 }

Clicross::GbfValueFieldType WorkflowArgDesc::Type::get()
 {
	return (Clicross::GbfValueFieldType)((int)(static_cast<cross::WorkflowArgDesc*>(this->_native))->Type);
 }
 void WorkflowArgDesc::Type::set(Clicross::GbfValueFieldType value )
 {
	(static_cast<cross::WorkflowArgDesc*>(this->_native))->Type = static_cast<cross::GbfValueFieldType>(value);
 }

System::String^ WorkflowArgDesc::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowArgDesc*>(this->_native))->Value)).c_str());
 }
 void WorkflowArgDesc::Value::set(System::String^ value )
 {
	((static_cast<cross::WorkflowArgDesc*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
WorkflowArgDesc::WorkflowArgDesc( )
    :WorkflowArgDesc(new cross::WorkflowArgDesc(), true)
{
}



WorkflowArgDesc::WorkflowArgDesc(const cross::WorkflowArgDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowArgDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowArgDesc::operator WorkflowArgDesc^ (const cross::WorkflowArgDesc* t)
{
    if(t)
    {
        return gcnew WorkflowArgDesc(const_cast<cross::WorkflowArgDesc*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// WorkflowEventDesc export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::WorkflowEventDesc::Args export start
	#define STLDECL_MANAGEDTYPE Clicross::WorkflowArgDesc^
	#define STLDECL_NATIVETYPE cross::WorkflowArgDesc
	CPP_DECLARE_STLVECTOR(WorkflowEventDesc::, ArgsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ WorkflowEventDesc::Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowEventDesc*>(this->_native))->Description)).c_str());
 }
 void WorkflowEventDesc::Description::set(System::String^ value )
 {
	((static_cast<cross::WorkflowEventDesc*>(this->_native))->Description) = (ClangenCli::ToNativeString(value));
 }

WorkflowEventDesc::ArgsCliType^ WorkflowEventDesc::Args::get()
 {
	return (static_cast<cross::WorkflowEventDesc*>(this->_native))->Args;
 }
 void WorkflowEventDesc::Args::set(WorkflowEventDesc::ArgsCliType^ value )
 {
	(static_cast<cross::WorkflowEventDesc*>(this->_native))->Args = *value->_native;
 }


//constructor export here
WorkflowEventDesc::WorkflowEventDesc( )
    :WorkflowEventDesc(new cross::WorkflowEventDesc(), true)
{
}



WorkflowEventDesc::WorkflowEventDesc(const cross::WorkflowEventDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowEventDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowEventDesc::operator WorkflowEventDesc^ (const cross::WorkflowEventDesc* t)
{
    if(t)
    {
        return gcnew WorkflowEventDesc(const_cast<cross::WorkflowEventDesc*>(t));
    }
    else
        return nullptr;
}

void WorkflowEventDesc::SetName(System::String^ new_name )
{
    (static_cast<cross::WorkflowEventDesc*>(this->_native))->SetName( ClangenCli::ToNativeString(new_name).c_str());
}

System::String^ WorkflowEventDesc::GetName( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::WorkflowEventDesc*>(this->_native))->GetName( ))).c_str());
}


}   //end namespace Clicross

// WorkflowGraphDesc export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ WorkflowGraphDesc::Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::WorkflowGraphDesc*>(this->_native))->Description)).c_str());
 }
 void WorkflowGraphDesc::Description::set(System::String^ value )
 {
	((static_cast<cross::WorkflowGraphDesc*>(this->_native))->Description) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
WorkflowGraphDesc::WorkflowGraphDesc( )
    :WorkflowGraphDesc(new cross::WorkflowGraphDesc(), true)
{
}



WorkflowGraphDesc::WorkflowGraphDesc(const cross::WorkflowGraphDesc * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowGraphDesc *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowGraphDesc::operator WorkflowGraphDesc^ (const cross::WorkflowGraphDesc* t)
{
    if(t)
    {
        return gcnew WorkflowGraphDesc(const_cast<cross::WorkflowGraphDesc*>(t));
    }
    else
        return nullptr;
}

void WorkflowGraphDesc::SetName(System::String^ new_name )
{
    (static_cast<cross::WorkflowGraphDesc*>(this->_native))->SetName( ClangenCli::ToNativeString(new_name).c_str());
}

System::String^ WorkflowGraphDesc::GetName( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::WorkflowGraphDesc*>(this->_native))->GetName( ))).c_str());
}


}   //end namespace Clicross

// VarInspectorHelper_BasicType export start
namespace Cligbf
{
namespace logic
{

//embeded classes

//stl container export here

//fields export here
System::String^ VarInspectorHelper_BasicType::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<gbf::logic::VarInspectorHelper_BasicType*>(this->_native))->Value)).c_str());
 }
 void VarInspectorHelper_BasicType::Value::set(System::String^ value )
 {
	((static_cast<gbf::logic::VarInspectorHelper_BasicType*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
VarInspectorHelper_BasicType::VarInspectorHelper_BasicType( )
    :VarInspectorHelper_BasicType(new gbf::logic::VarInspectorHelper_BasicType(), true)
{
}



VarInspectorHelper_BasicType::VarInspectorHelper_BasicType(const gbf::logic::VarInspectorHelper_BasicType * obj, bool created_by_clr): Cligbf::logic::VarInspectorHelper(obj, created_by_clr)
{
}

VarInspectorHelper_BasicType::operator VarInspectorHelper_BasicType^ (const gbf::logic::VarInspectorHelper_BasicType* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew VarInspectorHelper_BasicType(const_cast<gbf::logic::VarInspectorHelper_BasicType*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (VarInspectorHelper_BasicType^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Cligbf
}   //end namespace logic

// WorkflowNode export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::WorkflowNode::m_EditorFields export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::GbfValueField^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::GbfValueField
	CPP_DECLARE_STLMAP(WorkflowNode::, m_EditorFieldsCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE


//fields export here
WorkflowNode::m_EditorFieldsCliType^ WorkflowNode::m_EditorFields::get()
 {
	return (static_cast<cross::WorkflowNode*>(this->_native))->m_EditorFields;
 }
 void WorkflowNode::m_EditorFields::set(WorkflowNode::m_EditorFieldsCliType^ value )
 {
	(static_cast<cross::WorkflowNode*>(this->_native))->m_EditorFields = *value->_native;
 }


//constructor export here


WorkflowNode::WorkflowNode(const cross::WorkflowNode * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

WorkflowNode::operator WorkflowNode^ (const cross::WorkflowNode* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew WorkflowNode(const_cast<cross::WorkflowNode*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (WorkflowNode^)managedObj;
    }
    else
        return nullptr;
}

int WorkflowNode::GetLogicNodeId( )
{
    return (static_cast<cross::WorkflowNode*>(this->_native))->GetLogicNodeId( );
}


}   //end namespace Clicross

// GbfValueField export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::GbfValueFieldType GbfValueField::FieldType::get()
 {
	return (Clicross::GbfValueFieldType)((int)(static_cast<cross::GbfValueField*>(this->_native))->FieldType);
 }
 void GbfValueField::FieldType::set(Clicross::GbfValueFieldType value )
 {
	(static_cast<cross::GbfValueField*>(this->_native))->FieldType = static_cast<cross::GbfValueFieldType>(value);
 }

System::String^ GbfValueField::FieldValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::GbfValueField*>(this->_native))->FieldValue)).c_str());
 }
 void GbfValueField::FieldValue::set(System::String^ value )
 {
	((static_cast<cross::GbfValueField*>(this->_native))->FieldValue) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
GbfValueField::GbfValueField( )
    :GbfValueField(new cross::GbfValueField(), true)
{
}



GbfValueField::GbfValueField(const cross::GbfValueField * obj, bool created_by_clr): 
    _native(const_cast<cross::GbfValueField *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GbfValueField::operator GbfValueField^ (const cross::GbfValueField* t)
{
    if(t)
    {
        return gcnew GbfValueField(const_cast<cross::GbfValueField*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// WorkflowVariableType export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
WorkflowVariableType::WorkflowVariableType(): WorkflowVariableType(new cross::WorkflowVariableType(), true) {}


WorkflowVariableType::WorkflowVariableType(const cross::WorkflowVariableType * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowVariableType *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowVariableType::operator WorkflowVariableType^ (const cross::WorkflowVariableType* t)
{
    if(t)
    {
        return gcnew WorkflowVariableType(const_cast<cross::WorkflowVariableType*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// WorkflowSlot export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


WorkflowSlot::WorkflowSlot(const cross::WorkflowSlot * obj, bool created_by_clr): 
    _native(const_cast<cross::WorkflowSlot *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorkflowSlot::operator WorkflowSlot^ (const cross::WorkflowSlot* t)
{
    if(t)
    {
        return gcnew WorkflowSlot(const_cast<cross::WorkflowSlot*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// WorkflowPreview export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
WorkflowPreview::WorkflowPreview(Clicross::WorkflowEditor^ editor )
    :WorkflowPreview(new cross::WorkflowPreview(( cross::WorkflowEditor* )(editor)), true)
{
}



WorkflowPreview::WorkflowPreview(const cross::WorkflowPreview * obj, bool created_by_clr): Clicross::PreviewBase(obj, created_by_clr)
{
}

WorkflowPreview::operator WorkflowPreview^ (const cross::WorkflowPreview* t)
{
    if(t)
    {
        return gcnew WorkflowPreview(const_cast<cross::WorkflowPreview*>(t));
    }
    else
        return nullptr;
}

void WorkflowPreview::Tick( )
{
    (static_cast<cross::WorkflowPreview*>(this->_native))->Tick( );
}

void WorkflowPreview::CreateClassObjectWithWork(System::String^ classname )
{
    (static_cast<cross::WorkflowPreview*>(this->_native))->CreateClassObjectWithWork( ClangenCli::ToNativeString(classname).c_str());
}

void WorkflowPreview::Play( )
{
    (static_cast<cross::WorkflowPreview*>(this->_native))->Play( );
}


}   //end namespace Clicross

// WorkflowEditorContext export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
WorkflowEditorContext::WorkflowEditorContext(System::String^ fileName, Clicross::WorkflowEditorCallback^ callback )
    :WorkflowEditorContext(new cross::WorkflowEditorContext(ClangenCli::ToNativeString(fileName).c_str(), ( cross::WorkflowEditorCallback* )(callback)), true)
{
}



WorkflowEditorContext::WorkflowEditorContext(const cross::WorkflowEditorContext * obj, bool created_by_clr): Clicross::WorkflowEditor(obj, created_by_clr)
{
}

WorkflowEditorContext::operator WorkflowEditorContext^ (const cross::WorkflowEditorContext* t)
{
    if(t)
    {
        return gcnew WorkflowEditorContext(const_cast<cross::WorkflowEditorContext*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// Application export start
namespace Clinode_editor
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Application::Application( )
    :Application(new node_editor::Application(), true)
{
}

Application::Application(System::String^ name )
    :Application(new node_editor::Application(ClangenCli::ToNativeString(name).c_str()), true)
{
}

Application::Application(System::String^ name, int argc, cli::array<String^>^ argv )
    :Application(new node_editor::Application(ClangenCli::ToNativeString(name).c_str(), argc, ClangenCli::ConvertCliArrayToCharArray(argv, argc)), true)
{
}



Application::Application(const node_editor::Application * obj, bool created_by_clr): 
    _native(const_cast<node_editor::Application *>(obj))
	, _created_by_clr(created_by_clr)
{
}

Application::operator Application^ (const node_editor::Application* t)
{
    if(t)
    {
        return gcnew Application(const_cast<node_editor::Application*>(t));
    }
    else
        return nullptr;
}

bool Application::Create(int width, int height )
{
    return (static_cast<node_editor::Application*>(this->_native))->Create( width, height);
}

bool Application::CreateExternal(int width, int height, unsigned long long external_hwnd )
{
    return (static_cast<node_editor::Application*>(this->_native))->CreateExternal( width, height, external_hwnd);
}

void Application::RebindWindowCallback( )
{
    (static_cast<node_editor::Application*>(this->_native))->RebindWindowCallback( );
}

unsigned long long Application::OnExternalWindowMessage(unsigned long long hwnd, int msg, unsigned long long wparam, unsigned long long lparam )
{
    return (static_cast<node_editor::Application*>(this->_native))->OnExternalWindowMessage( hwnd, msg, wparam, lparam);
}

void Application::OnInputTextByIME(System::String^ text )
{
    (static_cast<node_editor::Application*>(this->_native))->OnInputTextByIME( ClangenCli::ToNativeString(text).c_str());
}

int Application::Run( )
{
    return (static_cast<node_editor::Application*>(this->_native))->Run( );
}

void Application::SetTitle(System::String^ title )
{
    (static_cast<node_editor::Application*>(this->_native))->SetTitle( ClangenCli::ToNativeString(title).c_str());
}

bool Application::Close( )
{
    return (static_cast<node_editor::Application*>(this->_native))->Close( );
}

void Application::Quit( )
{
    (static_cast<node_editor::Application*>(this->_native))->Quit( );
}

System::String^ Application::GetName( )
{
    return ClangenCli::ToManagedString((((static_cast<node_editor::Application*>(this->_native))->GetName( ))).c_str());
}

UnknowKeeper^ Application::DefaultFont( )
{
    return UnknownKeeperCast((static_cast<node_editor::Application*>(this->_native))->DefaultFont( ));
}

UnknowKeeper^ Application::HeaderFont( )
{
    return UnknownKeeperCast((static_cast<node_editor::Application*>(this->_native))->HeaderFont( ));
}

System::IntPtr Application::LoadTexture(System::String^ path )
{
    return System::IntPtr((static_cast<node_editor::Application*>(this->_native))->LoadTexture( ClangenCli::ToNativeString(path).c_str()));
}

System::IntPtr Application::LoadTextureFromMemory(UnknowKeeper^ buf )
{
    return System::IntPtr((static_cast<node_editor::Application*>(this->_native))->LoadTextureFromMemory( *(UnknowKeeper::get_native_with_type_for_pointer<std::shared_ptr<gbf::ByteBuffer>*>(buf))));
}

System::IntPtr Application::CreateTexture(System::IntPtr data, int width, int height )
{
    return System::IntPtr((static_cast<node_editor::Application*>(this->_native))->CreateTexture( reinterpret_cast<void*>(data.ToInt64()), width, height));
}

void Application::DestroyTexture(System::IntPtr texture )
{
    (static_cast<node_editor::Application*>(this->_native))->DestroyTexture( reinterpret_cast<void*>(texture.ToInt64()));
}

int Application::GetTextureWidth(System::IntPtr texture )
{
    return (static_cast<node_editor::Application*>(this->_native))->GetTextureWidth( reinterpret_cast<void*>(texture.ToInt64()));
}

int Application::GetTextureHeight(System::IntPtr texture )
{
    return (static_cast<node_editor::Application*>(this->_native))->GetTextureHeight( reinterpret_cast<void*>(texture.ToInt64()));
}

void Application::OnStart( )
{
    (static_cast<node_editor::Application*>(this->_native))->OnStart( );
}

void Application::OnStop( )
{
    (static_cast<node_editor::Application*>(this->_native))->OnStop( );
}

void Application::OnFrame(float deltaTime )
{
    (static_cast<node_editor::Application*>(this->_native))->OnFrame( deltaTime);
}

int Application::GetWindowFlags( )
{
    return (static_cast<node_editor::Application*>(this->_native))->GetWindowFlags( );
}

bool Application::CanClose( )
{
    return (static_cast<node_editor::Application*>(this->_native))->CanClose( );
}

void Application::Frame( )
{
    (static_cast<node_editor::Application*>(this->_native))->Frame( );
}


}   //end namespace Clinode_editor

// NodeBuilderBlueprint export start
namespace Clinode_editor
{
namespace utility
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
NodeBuilderBlueprint::NodeBuilderBlueprint(UnknowKeeper^ header_font, System::IntPtr texture, int textureWidth, int textureHeight, UnknowKeeper^ icon_size )
    :NodeBuilderBlueprint(new node_editor::utility::NodeBuilderBlueprint((UnknowKeeper::get_native_with_type_for_pointer< ImFont* >(header_font)), reinterpret_cast<void*>(texture.ToInt64()), textureWidth, textureHeight, (UnknowKeeper::get_native_with_type_for_reference<ImVec2& >(icon_size))), true)
{
}

NodeBuilderBlueprint::NodeBuilderBlueprint(UnknowKeeper^ header_font, UnknowKeeper^ subtitle_font, System::IntPtr texture, int textureWidth, int textureHeight, UnknowKeeper^ icon_size )
    :NodeBuilderBlueprint(new node_editor::utility::NodeBuilderBlueprint((UnknowKeeper::get_native_with_type_for_pointer< ImFont* >(header_font)), (UnknowKeeper::get_native_with_type_for_pointer< ImFont* >(subtitle_font)), reinterpret_cast<void*>(texture.ToInt64()), textureWidth, textureHeight, (UnknowKeeper::get_native_with_type_for_reference<ImVec2& >(icon_size))), true)
{
}



NodeBuilderBlueprint::NodeBuilderBlueprint(const node_editor::utility::NodeBuilderBlueprint * obj, bool created_by_clr): 
    _native(const_cast<node_editor::utility::NodeBuilderBlueprint *>(obj))
	, _created_by_clr(created_by_clr)
{
}

NodeBuilderBlueprint::operator NodeBuilderBlueprint^ (const node_editor::utility::NodeBuilderBlueprint* t)
{
    if(t)
    {
        return gcnew NodeBuilderBlueprint(const_cast<node_editor::utility::NodeBuilderBlueprint*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clinode_editor
}   //end namespace utility


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


