//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// MaterialPassState export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::NGIBlendStateDescForEditor^ MaterialPassState::BlendStateDesc::get()
 {
	return gcnew Clicross::NGIBlendStateDescForEditor(new cross::NGIBlendStateDescForEditor(((static_cast<cross::MaterialPassState*>(this->_native))->BlendStateDesc)) , true);
 }
 void MaterialPassState::BlendStateDesc::set(Clicross::NGIBlendStateDescForEditor^ value )
 {
	(static_cast<cross::MaterialPassState*>(this->_native))->BlendStateDesc = value;
 }

Clicross::NGIDepthStencilStateDesc^ MaterialPassState::DepthStencilStateDesc::get()
 {
	return gcnew Clicross::NGIDepthStencilStateDesc(new cross::NGIDepthStencilStateDesc(((static_cast<cross::MaterialPassState*>(this->_native))->DepthStencilStateDesc)) , true);
 }
 void MaterialPassState::DepthStencilStateDesc::set(Clicross::NGIDepthStencilStateDesc^ value )
 {
	(static_cast<cross::MaterialPassState*>(this->_native))->DepthStencilStateDesc = value;
 }

Clicross::NGIRasterizationStateDesc^ MaterialPassState::RasterizationStateDesc::get()
 {
	return gcnew Clicross::NGIRasterizationStateDesc(new cross::NGIRasterizationStateDesc(((static_cast<cross::MaterialPassState*>(this->_native))->RasterizationStateDesc)) , true);
 }
 void MaterialPassState::RasterizationStateDesc::set(Clicross::NGIRasterizationStateDesc^ value )
 {
	(static_cast<cross::MaterialPassState*>(this->_native))->RasterizationStateDesc = value;
 }

Clicross::NGIDynamicStateDesc^ MaterialPassState::DynamicStateDesc::get()
 {
	return gcnew Clicross::NGIDynamicStateDesc(new cross::NGIDynamicStateDesc(((static_cast<cross::MaterialPassState*>(this->_native))->DynamicStateDesc)) , true);
 }
 void MaterialPassState::DynamicStateDesc::set(Clicross::NGIDynamicStateDesc^ value )
 {
	(static_cast<cross::MaterialPassState*>(this->_native))->DynamicStateDesc = value;
 }

unsigned int MaterialPassState::RenderGroup::get()
 {
	return (static_cast<cross::MaterialPassState*>(this->_native))->RenderGroup;
 }
 void MaterialPassState::RenderGroup::set(unsigned int value )
 {
	(static_cast<cross::MaterialPassState*>(this->_native))->RenderGroup = value;
 }


//constructor export here
MaterialPassState::MaterialPassState(): MaterialPassState(new cross::MaterialPassState(), true) {}


MaterialPassState::MaterialPassState(const cross::MaterialPassState * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialPassState *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialPassState::operator MaterialPassState^ (const cross::MaterialPassState* t)
{
    if(t)
    {
        return gcnew MaterialPassState(const_cast<cross::MaterialPassState*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// SamplerState export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::TextureMipValueMode SamplerState::MipValueMode::get()
 {
	return (Clicross::TextureMipValueMode)((int)(static_cast<cross::SamplerState*>(this->_native))->MipValueMode);
 }
 void SamplerState::MipValueMode::set(Clicross::TextureMipValueMode value )
 {
	(static_cast<cross::SamplerState*>(this->_native))->MipValueMode = static_cast<cross::TextureMipValueMode>(value);
 }

Clicross::TextureFilter SamplerState::Filter::get()
 {
	return (Clicross::TextureFilter)((int)(static_cast<cross::SamplerState*>(this->_native))->Filter);
 }
 void SamplerState::Filter::set(Clicross::TextureFilter value )
 {
	(static_cast<cross::SamplerState*>(this->_native))->Filter = static_cast<cross::TextureFilter>(value);
 }

unsigned char SamplerState::AnisotropicLevel::get()
 {
	return (static_cast<cross::SamplerState*>(this->_native))->AnisotropicLevel;
 }
 void SamplerState::AnisotropicLevel::set(unsigned char value )
 {
	(static_cast<cross::SamplerState*>(this->_native))->AnisotropicLevel = value;
 }

Clicross::TextureAddressMode SamplerState::AddressMode::get()
 {
	return (Clicross::TextureAddressMode)((int)(static_cast<cross::SamplerState*>(this->_native))->AddressMode);
 }
 void SamplerState::AddressMode::set(Clicross::TextureAddressMode value )
 {
	(static_cast<cross::SamplerState*>(this->_native))->AddressMode = static_cast<cross::TextureAddressMode>(value);
 }


//constructor export here
SamplerState::SamplerState(): SamplerState(new cross::SamplerState(), true) {}


SamplerState::SamplerState(const cross::SamplerState * obj, bool created_by_clr): 
    _native(const_cast<cross::SamplerState *>(obj))
	, _created_by_clr(created_by_clr)
{
}

SamplerState::operator SamplerState^ (const cross::SamplerState* t)
{
    if(t)
    {
        return gcnew SamplerState(const_cast<cross::SamplerState*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialDefines export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::MaterialDomain MaterialDefines::Domain::get()
 {
	return (Clicross::MaterialDomain)((int)(static_cast<cross::MaterialDefines*>(this->_native))->Domain);
 }
 void MaterialDefines::Domain::set(Clicross::MaterialDomain value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->Domain = static_cast<cross::MaterialDomain>(value);
 }

Clicross::MaterialBlendMode MaterialDefines::BlendMode::get()
 {
	return (Clicross::MaterialBlendMode)((int)(static_cast<cross::MaterialDefines*>(this->_native))->BlendMode);
 }
 void MaterialDefines::BlendMode::set(Clicross::MaterialBlendMode value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->BlendMode = static_cast<cross::MaterialBlendMode>(value);
 }

Clicross::MaterialShadingModel MaterialDefines::ShadingModel::get()
 {
	return (Clicross::MaterialShadingModel)((int)(static_cast<cross::MaterialDefines*>(this->_native))->ShadingModel);
 }
 void MaterialDefines::ShadingModel::set(Clicross::MaterialShadingModel value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->ShadingModel = static_cast<cross::MaterialShadingModel>(value);
 }

bool MaterialDefines::TwoSided::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->TwoSided;
 }
 void MaterialDefines::TwoSided::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->TwoSided = value;
 }

bool MaterialDefines::UseMaterialAttributes::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->UseMaterialAttributes;
 }
 void MaterialDefines::UseMaterialAttributes::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->UseMaterialAttributes = value;
 }

int MaterialDefines::RenderGroupBias::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->RenderGroupBias;
 }
 void MaterialDefines::RenderGroupBias::set(int value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->RenderGroupBias = value;
 }

bool MaterialDefines::ForceDisableExponentialFog::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableExponentialFog;
 }
 void MaterialDefines::ForceDisableExponentialFog::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableExponentialFog = value;
 }

bool MaterialDefines::ForceDisableVolumetricFog::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableVolumetricFog;
 }
 void MaterialDefines::ForceDisableVolumetricFog::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableVolumetricFog = value;
 }

bool MaterialDefines::ForceDisableCloudFog::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableCloudFog;
 }
 void MaterialDefines::ForceDisableCloudFog::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableCloudFog = value;
 }

bool MaterialDefines::ForceDisableSSR::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableSSR;
 }
 void MaterialDefines::ForceDisableSSR::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->ForceDisableSSR = value;
 }

bool MaterialDefines::EnableSeparateTranslucency::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->EnableSeparateTranslucency;
 }
 void MaterialDefines::EnableSeparateTranslucency::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->EnableSeparateTranslucency = value;
 }

bool MaterialDefines::UsedWithSkeletalMesh::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->UsedWithSkeletalMesh;
 }
 void MaterialDefines::UsedWithSkeletalMesh::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->UsedWithSkeletalMesh = value;
 }

bool MaterialDefines::UsedWithLocalSpaceParticle::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->UsedWithLocalSpaceParticle;
 }
 void MaterialDefines::UsedWithLocalSpaceParticle::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->UsedWithLocalSpaceParticle = value;
 }

bool MaterialDefines::UsedWithGlobalSpaceParticle::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->UsedWithGlobalSpaceParticle;
 }
 void MaterialDefines::UsedWithGlobalSpaceParticle::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->UsedWithGlobalSpaceParticle = value;
 }

bool MaterialDefines::UsedWithTerrain::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->UsedWithTerrain;
 }
 void MaterialDefines::UsedWithTerrain::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->UsedWithTerrain = value;
 }

Clicross::TerrainMode MaterialDefines::TerrainMode::get()
 {
	return (Clicross::TerrainMode)((int)(static_cast<cross::MaterialDefines*>(this->_native))->TerrainMode);
 }
 void MaterialDefines::TerrainMode::set(Clicross::TerrainMode value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->TerrainMode = static_cast<cross::TerrainMode>(value);
 }

Clicross::BlendableLocation MaterialDefines::PostProcessBlendableLocation::get()
 {
	return (Clicross::BlendableLocation)((int)(static_cast<cross::MaterialDefines*>(this->_native))->PostProcessBlendableLocation);
 }
 void MaterialDefines::PostProcessBlendableLocation::set(Clicross::BlendableLocation value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->PostProcessBlendableLocation = static_cast<cross::BlendableLocation>(value);
 }

bool MaterialDefines::PostProcessEnableOutputAlpha::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->PostProcessEnableOutputAlpha;
 }
 void MaterialDefines::PostProcessEnableOutputAlpha::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->PostProcessEnableOutputAlpha = value;
 }

float MaterialDefines::PostProcessBlendablePriority::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->PostProcessBlendablePriority;
 }
 void MaterialDefines::PostProcessBlendablePriority::set(float value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->PostProcessBlendablePriority = value;
 }

bool MaterialDefines::PostProcessIsBlendable::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->PostProcessIsBlendable;
 }
 void MaterialDefines::PostProcessIsBlendable::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->PostProcessIsBlendable = value;
 }

bool MaterialDefines::EnableDebugSymbol::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->EnableDebugSymbol;
 }
 void MaterialDefines::EnableDebugSymbol::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->EnableDebugSymbol = value;
 }

bool MaterialDefines::EnableAdvancedMode::get()
 {
	return (static_cast<cross::MaterialDefines*>(this->_native))->EnableAdvancedMode;
 }
 void MaterialDefines::EnableAdvancedMode::set(bool value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->EnableAdvancedMode = value;
 }

Clicross::RenderStateInfo^ MaterialDefines::AdvancedRenderStates::get()
 {
	return gcnew Clicross::RenderStateInfo(new cross::RenderStateInfo(((static_cast<cross::MaterialDefines*>(this->_native))->AdvancedRenderStates)) , true);
 }
 void MaterialDefines::AdvancedRenderStates::set(Clicross::RenderStateInfo^ value )
 {
	(static_cast<cross::MaterialDefines*>(this->_native))->AdvancedRenderStates = value;
 }


//constructor export here
MaterialDefines::MaterialDefines(): MaterialDefines(new cross::MaterialDefines(), true) {}


MaterialDefines::MaterialDefines(const cross::MaterialDefines * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialDefines *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialDefines::operator MaterialDefines^ (const cross::MaterialDefines* t)
{
    if(t)
    {
        return gcnew MaterialDefines(const_cast<cross::MaterialDefines*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RenderStateInfo export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::RenderStateInfo::mRenderStates export start
	#define STLDECL_MANAGEDTYPE Clicross::MaterialPassState^
	#define STLDECL_NATIVETYPE cross::MaterialPassState
	CPP_DECLARE_STLVECTOR(RenderStateInfo::, mRenderStatesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::RenderStateInfo::mPassID export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(RenderStateInfo::, mPassIDCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
RenderStateInfo::mRenderStatesCliType^ RenderStateInfo::mRenderStates::get()
 {
	return (static_cast<cross::RenderStateInfo*>(this->_native))->mRenderStates;
 }
 void RenderStateInfo::mRenderStates::set(RenderStateInfo::mRenderStatesCliType^ value )
 {
	(static_cast<cross::RenderStateInfo*>(this->_native))->mRenderStates = *value->_native;
 }

RenderStateInfo::mPassIDCliType^ RenderStateInfo::mPassID::get()
 {
	return (static_cast<cross::RenderStateInfo*>(this->_native))->mPassID;
 }
 void RenderStateInfo::mPassID::set(RenderStateInfo::mPassIDCliType^ value )
 {
	(static_cast<cross::RenderStateInfo*>(this->_native))->mPassID = *value->_native;
 }


//constructor export here
RenderStateInfo::RenderStateInfo(): RenderStateInfo(new cross::RenderStateInfo(), true) {}


RenderStateInfo::RenderStateInfo(const cross::RenderStateInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::RenderStateInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RenderStateInfo::operator RenderStateInfo^ (const cross::RenderStateInfo* t)
{
    if(t)
    {
        return gcnew RenderStateInfo(const_cast<cross::RenderStateInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool MaterialParameter::Enable::get()
 {
	return (static_cast<cross::MaterialParameter*>(this->_native))->Enable;
 }
 void MaterialParameter::Enable::set(bool value )
 {
	(static_cast<cross::MaterialParameter*>(this->_native))->Enable = value;
 }

System::String^ MaterialParameter::ParameterName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameter*>(this->_native))->ParameterName)).c_str());
 }
 void MaterialParameter::ParameterName::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameter*>(this->_native))->ParameterName) = (ClangenCli::ToNativeString(value));
 }

System::String^ MaterialParameter::DisplayName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameter*>(this->_native))->DisplayName)).c_str());
 }
 void MaterialParameter::DisplayName::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameter*>(this->_native))->DisplayName) = (ClangenCli::ToNativeString(value));
 }

float MaterialParameter::SortPriority::get()
 {
	return (static_cast<cross::MaterialParameter*>(this->_native))->SortPriority;
 }
 void MaterialParameter::SortPriority::set(float value )
 {
	(static_cast<cross::MaterialParameter*>(this->_native))->SortPriority = value;
 }

System::String^ MaterialParameter::GroupName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameter*>(this->_native))->GroupName)).c_str());
 }
 void MaterialParameter::GroupName::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameter*>(this->_native))->GroupName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
MaterialParameter::MaterialParameter(): MaterialParameter(new cross::MaterialParameter(), true) {}


MaterialParameter::MaterialParameter(const cross::MaterialParameter * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

MaterialParameter::operator MaterialParameter^ (const cross::MaterialParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameter(const_cast<cross::MaterialParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterScalar export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float MaterialParameterScalar::Value::get()
 {
	return (static_cast<cross::MaterialParameterScalar*>(this->_native))->Value;
 }
 void MaterialParameterScalar::Value::set(float value )
 {
	(static_cast<cross::MaterialParameterScalar*>(this->_native))->Value = value;
 }

float MaterialParameterScalar::SliderMin::get()
 {
	return (static_cast<cross::MaterialParameterScalar*>(this->_native))->SliderMin;
 }
 void MaterialParameterScalar::SliderMin::set(float value )
 {
	(static_cast<cross::MaterialParameterScalar*>(this->_native))->SliderMin = value;
 }

float MaterialParameterScalar::SliderMax::get()
 {
	return (static_cast<cross::MaterialParameterScalar*>(this->_native))->SliderMax;
 }
 void MaterialParameterScalar::SliderMax::set(float value )
 {
	(static_cast<cross::MaterialParameterScalar*>(this->_native))->SliderMax = value;
 }


//constructor export here
MaterialParameterScalar::MaterialParameterScalar(): MaterialParameterScalar(new cross::MaterialParameterScalar(), true) {}


MaterialParameterScalar::MaterialParameterScalar(const cross::MaterialParameterScalar * obj, bool created_by_clr): Clicross::MaterialParameter(obj, created_by_clr)
{
}

MaterialParameterScalar::operator MaterialParameterScalar^ (const cross::MaterialParameterScalar* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterScalar(const_cast<cross::MaterialParameterScalar*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterScalar^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterVector export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float4^ MaterialParameterVector::Value::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialParameterVector*>(this->_native))->Value)) , true);
 }
 void MaterialParameterVector::Value::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialParameterVector*>(this->_native))->Value = value;
 }


//constructor export here
MaterialParameterVector::MaterialParameterVector(): MaterialParameterVector(new cross::MaterialParameterVector(), true) {}


MaterialParameterVector::MaterialParameterVector(const cross::MaterialParameterVector * obj, bool created_by_clr): Clicross::MaterialParameter(obj, created_by_clr)
{
}

MaterialParameterVector::operator MaterialParameterVector^ (const cross::MaterialParameterVector* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterVector(const_cast<cross::MaterialParameterVector*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterVector^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterBool export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool MaterialParameterBool::Value::get()
 {
	return (static_cast<cross::MaterialParameterBool*>(this->_native))->Value;
 }
 void MaterialParameterBool::Value::set(bool value )
 {
	(static_cast<cross::MaterialParameterBool*>(this->_native))->Value = value;
 }


//constructor export here
MaterialParameterBool::MaterialParameterBool(): MaterialParameterBool(new cross::MaterialParameterBool(), true) {}


MaterialParameterBool::MaterialParameterBool(const cross::MaterialParameterBool * obj, bool created_by_clr): Clicross::MaterialParameter(obj, created_by_clr)
{
}

MaterialParameterBool::operator MaterialParameterBool^ (const cross::MaterialParameterBool* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterBool(const_cast<cross::MaterialParameterBool*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterBool^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterTexture export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialParameterTexture::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameterTexture*>(this->_native))->Value)).c_str());
 }
 void MaterialParameterTexture::Value::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameterTexture*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
MaterialParameterTexture::MaterialParameterTexture(): MaterialParameterTexture(new cross::MaterialParameterTexture(), true) {}


MaterialParameterTexture::MaterialParameterTexture(const cross::MaterialParameterTexture * obj, bool created_by_clr): Clicross::MaterialParameter(obj, created_by_clr)
{
}

MaterialParameterTexture::operator MaterialParameterTexture^ (const cross::MaterialParameterTexture* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterTexture(const_cast<cross::MaterialParameterTexture*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterTexture^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialInstanceDefines export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialInstanceDefines::ParameterGroups export start
	#define STLDECL_MANAGEDTYPE Clicross::MaterialParameterGroup^
	#define STLDECL_NATIVETYPE cross::MaterialParameterGroup
	CPP_DECLARE_STLVECTOR(MaterialInstanceDefines::, ParameterGroupsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ MaterialInstanceDefines::ParentMaterial::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialInstanceDefines*>(this->_native))->ParentMaterial)).c_str());
 }
 void MaterialInstanceDefines::ParentMaterial::set(System::String^ value )
 {
	((static_cast<cross::MaterialInstanceDefines*>(this->_native))->ParentMaterial) = (ClangenCli::ToNativeString(value));
 }

bool MaterialInstanceDefines::BlendModeEnable::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->BlendModeEnable;
 }
 void MaterialInstanceDefines::BlendModeEnable::set(bool value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->BlendModeEnable = value;
 }

Clicross::MaterialBlendMode MaterialInstanceDefines::BlendMode::get()
 {
	return (Clicross::MaterialBlendMode)((int)(static_cast<cross::MaterialInstanceDefines*>(this->_native))->BlendMode);
 }
 void MaterialInstanceDefines::BlendMode::set(Clicross::MaterialBlendMode value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->BlendMode = static_cast<cross::MaterialBlendMode>(value);
 }

bool MaterialInstanceDefines::ShadingModelEnable::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->ShadingModelEnable;
 }
 void MaterialInstanceDefines::ShadingModelEnable::set(bool value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->ShadingModelEnable = value;
 }

Clicross::MaterialShadingModel MaterialInstanceDefines::ShadingModel::get()
 {
	return (Clicross::MaterialShadingModel)((int)(static_cast<cross::MaterialInstanceDefines*>(this->_native))->ShadingModel);
 }
 void MaterialInstanceDefines::ShadingModel::set(Clicross::MaterialShadingModel value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->ShadingModel = static_cast<cross::MaterialShadingModel>(value);
 }

bool MaterialInstanceDefines::TwoSidedEnable::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->TwoSidedEnable;
 }
 void MaterialInstanceDefines::TwoSidedEnable::set(bool value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->TwoSidedEnable = value;
 }

bool MaterialInstanceDefines::TwoSided::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->TwoSided;
 }
 void MaterialInstanceDefines::TwoSided::set(bool value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->TwoSided = value;
 }

int MaterialInstanceDefines::RenderGroupBias::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->RenderGroupBias;
 }
 void MaterialInstanceDefines::RenderGroupBias::set(int value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->RenderGroupBias = value;
 }

MaterialInstanceDefines::ParameterGroupsCliType^ MaterialInstanceDefines::ParameterGroups::get()
 {
	return (static_cast<cross::MaterialInstanceDefines*>(this->_native))->ParameterGroups;
 }
 void MaterialInstanceDefines::ParameterGroups::set(MaterialInstanceDefines::ParameterGroupsCliType^ value )
 {
	(static_cast<cross::MaterialInstanceDefines*>(this->_native))->ParameterGroups = *value->_native;
 }


//constructor export here
MaterialInstanceDefines::MaterialInstanceDefines(): MaterialInstanceDefines(new cross::MaterialInstanceDefines(), true) {}


MaterialInstanceDefines::MaterialInstanceDefines(const cross::MaterialInstanceDefines * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialInstanceDefines *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialInstanceDefines::operator MaterialInstanceDefines^ (const cross::MaterialInstanceDefines* t)
{
    if(t)
    {
        return gcnew MaterialInstanceDefines(const_cast<cross::MaterialInstanceDefines*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterGroup export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialParameterGroup::Parameters export start
	#define STLDECL_MANAGEDTYPE Clicross::MaterialParameter^
	#define STLDECL_NATIVETYPE cross::MaterialParameter*
	CPP_DECLARE_STLVECTOR(MaterialParameterGroup::, ParametersCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ MaterialParameterGroup::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameterGroup*>(this->_native))->Name)).c_str());
 }
 void MaterialParameterGroup::Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameterGroup*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

MaterialParameterGroup::ParametersCliType^ MaterialParameterGroup::Parameters::get()
 {
	return (static_cast<cross::MaterialParameterGroup*>(this->_native))->Parameters;
 }
 void MaterialParameterGroup::Parameters::set(MaterialParameterGroup::ParametersCliType^ value )
 {
	(static_cast<cross::MaterialParameterGroup*>(this->_native))->Parameters = *value->_native;
 }


//constructor export here
MaterialParameterGroup::MaterialParameterGroup(): MaterialParameterGroup(new cross::MaterialParameterGroup(), true) {}


MaterialParameterGroup::MaterialParameterGroup(const cross::MaterialParameterGroup * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialParameterGroup *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialParameterGroup::operator MaterialParameterGroup^ (const cross::MaterialParameterGroup* t)
{
    if(t)
    {
        return gcnew MaterialParameterGroup(const_cast<cross::MaterialParameterGroup*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialFunctionDefines export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool MaterialFunctionDefines::ExposeToLibrary::get()
 {
	return (static_cast<cross::MaterialFunctionDefines*>(this->_native))->ExposeToLibrary;
 }
 void MaterialFunctionDefines::ExposeToLibrary::set(bool value )
 {
	(static_cast<cross::MaterialFunctionDefines*>(this->_native))->ExposeToLibrary = value;
 }


//constructor export here
MaterialFunctionDefines::MaterialFunctionDefines(): MaterialFunctionDefines(new cross::MaterialFunctionDefines(), true) {}


MaterialFunctionDefines::MaterialFunctionDefines(const cross::MaterialFunctionDefines * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialFunctionDefines *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialFunctionDefines::operator MaterialFunctionDefines^ (const cross::MaterialFunctionDefines* t)
{
    if(t)
    {
        return gcnew MaterialFunctionDefines(const_cast<cross::MaterialFunctionDefines*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialParameterTextureVirtual export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialParameterTextureVirtual::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialParameterTextureVirtual*>(this->_native))->Value)).c_str());
 }
 void MaterialParameterTextureVirtual::Value::set(System::String^ value )
 {
	((static_cast<cross::MaterialParameterTextureVirtual*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
MaterialParameterTextureVirtual::MaterialParameterTextureVirtual(): MaterialParameterTextureVirtual(new cross::MaterialParameterTextureVirtual(), true) {}


MaterialParameterTextureVirtual::MaterialParameterTextureVirtual(const cross::MaterialParameterTextureVirtual * obj, bool created_by_clr): Clicross::MaterialParameter(obj, created_by_clr)
{
}

MaterialParameterTextureVirtual::operator MaterialParameterTextureVirtual^ (const cross::MaterialParameterTextureVirtual* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterTextureVirtual(const_cast<cross::MaterialParameterTextureVirtual*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterTextureVirtual^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// Fx export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
Fx::Fx( )
    :Fx(new cross::resource::Fx(), true)
{
}



Fx::Fx(const cross::resource::Fx * obj, bool created_by_clr): Clicross::resource::MaterialInterface(obj, created_by_clr)
{
}

Fx::operator Fx^ (const cross::resource::Fx* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew Fx(const_cast<cross::resource::Fx*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (Fx^)managedObj;
    }
    else
        return nullptr;
}

void Fx::SetBool(Clicross::NameID^ name, bool value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetBool( (const cross::NameID& )(name), value);
}

Clicross::resource::Fx^ Fx::FxCreateFx( )
{
    return (Clicross::resource::Fx^)(cross::resource::Fx::FxCreateFx( ));
}

Clicross::resource::Fx^ Fx::FxCreateFxNew( )
{
    return (Clicross::resource::Fx^)(cross::resource::Fx::FxCreateFxNew( ));
}

void Fx::SetPropertyGroup(System::String^ name, System::String^ group )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetPropertyGroup( ClangenCli::ToNativeString(name).c_str(), ClangenCli::ToNativeString(group).c_str());
}

void Fx::SetPropertyVisible(Clicross::NameID^ name, bool visible )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetPropertyVisible( (const cross::NameID& )(name), visible);
}

void Fx::SetPropertyIsColor(Clicross::NameID^ name, bool isColor )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetPropertyIsColor( (const cross::NameID& )(name), isColor);
}

void Fx::SetPropertyMin(Clicross::NameID^ name, float min )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetPropertyMin( (const cross::NameID& )(name), min);
}

void Fx::SetPropertyMax(Clicross::NameID^ name, float max )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetPropertyMax( (const cross::NameID& )(name), max);
}

int Fx::EditorGetPropertyType(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyType( (const cross::NameID& )(name));
}

bool Fx::EditorGetPropertyIsColor(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyIsColor( (const cross::NameID& )(name));
}

bool Fx::EditorGetPropertyUsage(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyUsage( (const cross::NameID& )(name));
}

bool Fx::EditorGetPropertyVisible(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyVisible( (const cross::NameID& )(name));
}

float Fx::EditorGetPropertyFloat(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyFloat( (const cross::NameID& )(name));
}

Clicross::Float2^ Fx::EditorGetPropertyMinMax(Clicross::NameID^ name )
{
    return gcnew Clicross::Float2(new cross::Float2(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyMinMax( (const cross::NameID& )(name)))) , true);
}

Clicross::Float2^ Fx::EditorGetPropertyFloat2(Clicross::NameID^ name )
{
    return gcnew Clicross::Float2(new cross::Float2(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyFloat2( (const cross::NameID& )(name)))) , true);
}

Clicross::Float3^ Fx::EditorGetPropertyFloat3(Clicross::NameID^ name )
{
    return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyFloat3( (const cross::NameID& )(name)))) , true);
}

Clicross::Float4^ Fx::EditorGetPropertyFloat4(Clicross::NameID^ name )
{
    return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyFloat4( (const cross::NameID& )(name)))) , true);
}

bool Fx::EditorGetPropertyBool(Clicross::NameID^ name )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyBool( (const cross::NameID& )(name));
}

System::String^ Fx::EditorGetPropertyString(Clicross::NameID^ name )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyString( (const cross::NameID& )(name)))).c_str());
}

System::String^ Fx::GetGroupNameList( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->GetGroupNameList( ))).c_str());
}

System::String^ Fx::GetPropertyNameList( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->GetPropertyNameList( ))).c_str());
}

void Fx::RemoveUnusedProps( )
{
    (static_cast<cross::resource::Fx*>(this->_native))->RemoveUnusedProps( );
}

void Fx::ChangePropertyPosition(System::String^ name, System::String^ prev, System::String^ next )
{
    (static_cast<cross::resource::Fx*>(this->_native))->ChangePropertyPosition( ClangenCli::ToNativeString(name).c_str(), ClangenCli::ToNativeString(prev).c_str(), ClangenCli::ToNativeString(next).c_str());
}

void Fx::ChangePassPosition(System::String^ name, System::String^ next )
{
    (static_cast<cross::resource::Fx*>(this->_native))->ChangePassPosition( ClangenCli::ToNativeString(name).c_str(), ClangenCli::ToNativeString(next).c_str());
}

void Fx::ChangeGroupPosition(System::String^ name, System::String^ next )
{
    (static_cast<cross::resource::Fx*>(this->_native))->ChangeGroupPosition( ClangenCli::ToNativeString(name).c_str(), ClangenCli::ToNativeString(next).c_str());
}

void Fx::EditorSetFloat(Clicross::NameID^ name, float value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetFloat( (const cross::NameID& )(name), value);
}

void Fx::EditorSetFloat2(Clicross::NameID^ name, Clicross::Float2^ value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetFloat2( (const cross::NameID& )(name), *((cross::Float2*)(value)));
}

void Fx::EditorSetFloat3(Clicross::NameID^ name, Clicross::Float3^ value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetFloat3( (const cross::NameID& )(name), *((cross::Float3*)(value)));
}

void Fx::EditorSetFloat4(Clicross::NameID^ name, Clicross::Float4^ value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetFloat4( (const cross::NameID& )(name), *((cross::Float4*)(value)));
}

void Fx::EditorSetTexture(Clicross::NameID^ name, System::String^ value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetTexture( (const cross::NameID& )(name), ClangenCli::ToNativeString(value).c_str());
}

void Fx::EditorSetShader(Clicross::NameID^ name, System::String^ value )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetShader( (const cross::NameID& )(name), ClangenCli::ToNativeString(value).c_str());
}

System::String^ Fx::EditorGetPassNameList( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPassNameList( ))).c_str());
}

void Fx::CreatePass(Clicross::NameID^ passID )
{
    (static_cast<cross::resource::Fx*>(this->_native))->CreatePass( (const cross::NameID& )(passID));
}

void Fx::DeletePass(Clicross::NameID^ passID )
{
    (static_cast<cross::resource::Fx*>(this->_native))->DeletePass( (const cross::NameID& )(passID));
}

void Fx::RenamePass(Clicross::NameID^ oldName, Clicross::NameID^ newName )
{
    (static_cast<cross::resource::Fx*>(this->_native))->RenamePass( (const cross::NameID& )(oldName), (const cross::NameID& )(newName));
}

void Fx::CreateGroup(Clicross::NameID^ name )
{
    (static_cast<cross::resource::Fx*>(this->_native))->CreateGroup( (const cross::NameID& )(name));
}

void Fx::DeleteGroup(Clicross::NameID^ name )
{
    (static_cast<cross::resource::Fx*>(this->_native))->DeleteGroup( (const cross::NameID& )(name));
}

void Fx::RenameGroup(Clicross::NameID^ oldName, Clicross::NameID^ newName )
{
    (static_cast<cross::resource::Fx*>(this->_native))->RenameGroup( (const cross::NameID& )(oldName), (const cross::NameID& )(newName));
}

void Fx::SetRenderState(Clicross::NameID^ passID, Clicross::FxRenderState renderState )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetRenderState( (const cross::NameID& )(passID), static_cast<cross::FxRenderState>(renderState));
}

void Fx::SetRenderGroup(Clicross::NameID^ passID, unsigned int renderGroup )
{
    (static_cast<cross::resource::Fx*>(this->_native))->SetRenderGroup( (const cross::NameID& )(passID), renderGroup);
}

int Fx::EditorGetPassRenderGroup(Clicross::NameID^ passID )
{
    return (static_cast<cross::resource::Fx*>(this->_native))->EditorGetPassRenderGroup( (const cross::NameID& )(passID));
}

System::String^ Fx::EditorGetPassShaderPath(Clicross::NameID^ passID )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPassShaderPath( (const cross::NameID& )(passID)))).c_str());
}

Clicross::FxRenderState Fx::EditorFxGetRenderState(Clicross::NameID^ passID )
{
    return (Clicross::FxRenderState)((int)(static_cast<cross::resource::Fx*>(this->_native))->EditorFxGetRenderState( (const cross::NameID& )(passID)));
}

Clicross::BlendStateDescForEditor^ Fx::EditorGetBlendState(Clicross::NameID^ passID )
{
    return gcnew Clicross::BlendStateDescForEditor(new cross::BlendStateDescForEditor(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetBlendState( (const cross::NameID& )(passID)))) , true);
}

Clicross::DepthStencilStateDesc^ Fx::EditorGetDepthStencilState(Clicross::NameID^ passID )
{
    return gcnew Clicross::DepthStencilStateDesc(new cross::DepthStencilStateDesc(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetDepthStencilState( (const cross::NameID& )(passID)))) , true);
}

Clicross::RasterizationStateDesc^ Fx::EditorGetRasterizationState(Clicross::NameID^ passID )
{
    return gcnew Clicross::RasterizationStateDesc(new cross::RasterizationStateDesc(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetRasterizationState( (const cross::NameID& )(passID)))) , true);
}

Clicross::DynamicStateDesc^ Fx::EditorGetDynamicState(Clicross::NameID^ passID )
{
    return gcnew Clicross::DynamicStateDesc(new cross::DynamicStateDesc(((static_cast<cross::resource::Fx*>(this->_native))->EditorGetDynamicState( (const cross::NameID& )(passID)))) , true);
}

System::String^ Fx::EditorGetPropertyGroupName(System::String^ name )
{
    return ClangenCli::ToManagedString((((static_cast<cross::resource::Fx*>(this->_native))->EditorGetPropertyGroupName( ClangenCli::ToNativeString(name).c_str()))).c_str());
}

void Fx::EditorSetDepthStencilState(Clicross::NameID^ passID, Clicross::DepthStencilStateDesc^ state )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetDepthStencilState( (const cross::NameID& )(passID), *((cross::DepthStencilStateDesc*)(state)));
}

void Fx::EditorSetBlendState(Clicross::NameID^ passID, Clicross::BlendStateDescForEditor^ state )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetBlendState( (const cross::NameID& )(passID), *((cross::BlendStateDescForEditor*)(state)));
}

void Fx::EditorSetRasterizerState(Clicross::NameID^ passID, Clicross::RasterizationStateDesc^ state )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetRasterizerState( (const cross::NameID& )(passID), *((cross::RasterizationStateDesc*)(state)));
}

void Fx::EditorSetDynamicState(Clicross::NameID^ passID, Clicross::DynamicStateDesc^ state )
{
    (static_cast<cross::resource::Fx*>(this->_native))->EditorSetDynamicState( (const cross::NameID& )(passID), *((cross::DynamicStateDesc*)(state)));
}


}   //end namespace Clicross
}   //end namespace resource

// MaterialParameterCollection export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


MaterialParameterCollection::MaterialParameterCollection(const cross::resource::MaterialParameterCollection * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

MaterialParameterCollection::operator MaterialParameterCollection^ (const cross::resource::MaterialParameterCollection* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialParameterCollection(const_cast<cross::resource::MaterialParameterCollection*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialParameterCollection^)managedObj;
    }
    else
        return nullptr;
}

void MaterialParameterCollection::MPC_SetScalerParameter(Clicross::resource::MaterialParameterCollection^ mpc, int index, System::String^ name, float value, bool usage, bool asbool )
{
    cross::resource::MaterialParameterCollection::MPC_SetScalerParameter( ( cross::resource::MaterialParameterCollection* )(mpc), index, ClangenCli::ToNativeString(name).c_str(), value, usage, asbool);
}

void MaterialParameterCollection::MPC_AddScalerParameter(Clicross::resource::MaterialParameterCollection^ mpc, System::String^ name, float value )
{
    cross::resource::MaterialParameterCollection::MPC_AddScalerParameter( ( cross::resource::MaterialParameterCollection* )(mpc), ClangenCli::ToNativeString(name).c_str(), value);
}

void MaterialParameterCollection::MPC_DeleteScalerParameter(Clicross::resource::MaterialParameterCollection^ mpc, int index )
{
    cross::resource::MaterialParameterCollection::MPC_DeleteScalerParameter( ( cross::resource::MaterialParameterCollection* )(mpc), index);
}

void MaterialParameterCollection::MPC_ClearScalerParameter(Clicross::resource::MaterialParameterCollection^ mpc )
{
    cross::resource::MaterialParameterCollection::MPC_ClearScalerParameter( ( cross::resource::MaterialParameterCollection* )(mpc));
}

void MaterialParameterCollection::MPC_SetVectorParameter(Clicross::resource::MaterialParameterCollection^ mpc, int index, System::String^ name, Clicross::Float4^ value )
{
    cross::resource::MaterialParameterCollection::MPC_SetVectorParameter( ( cross::resource::MaterialParameterCollection* )(mpc), index, ClangenCli::ToNativeString(name).c_str(), ( cross::Float4* )(value));
}

void MaterialParameterCollection::MPC_SetVectorParameterAutoUsage(Clicross::resource::MaterialParameterCollection^ mpc, int index, bool usage )
{
    cross::resource::MaterialParameterCollection::MPC_SetVectorParameterAutoUsage( ( cross::resource::MaterialParameterCollection* )(mpc), index, usage);
}

void MaterialParameterCollection::MPC_AddVectorParameter(Clicross::resource::MaterialParameterCollection^ mpc, System::String^ name, Clicross::Float4^ value )
{
    cross::resource::MaterialParameterCollection::MPC_AddVectorParameter( ( cross::resource::MaterialParameterCollection* )(mpc), ClangenCli::ToNativeString(name).c_str(), ( cross::Float4* )(value));
}

void MaterialParameterCollection::MPC_DeleteVectorParameter(Clicross::resource::MaterialParameterCollection^ mpc, int index )
{
    cross::resource::MaterialParameterCollection::MPC_DeleteVectorParameter( ( cross::resource::MaterialParameterCollection* )(mpc), index);
}

void MaterialParameterCollection::MPC_ClearVectorParameter(Clicross::resource::MaterialParameterCollection^ mpc )
{
    cross::resource::MaterialParameterCollection::MPC_ClearVectorParameter( ( cross::resource::MaterialParameterCollection* )(mpc));
}

Clicross::resource::MaterialParameterCollection^ MaterialParameterCollection::MPC_CreateMaterialParameterCollection( )
{
    return (Clicross::resource::MaterialParameterCollection^)(cross::resource::MaterialParameterCollection::MPC_CreateMaterialParameterCollection( ));
}


}   //end namespace Clicross
}   //end namespace resource

// MaterialFunction export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


MaterialFunction::MaterialFunction(const cross::resource::MaterialFunction * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

MaterialFunction::operator MaterialFunction^ (const cross::resource::MaterialFunction* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialFunction(const_cast<cross::resource::MaterialFunction*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialFunction^)managedObj;
    }
    else
        return nullptr;
}

Clicross::resource::MaterialFunction^ MaterialFunction::MaterialFunction_CreateMaterialFunction( )
{
    return (Clicross::resource::MaterialFunction^)(cross::resource::MaterialFunction::MaterialFunction_CreateMaterialFunction( ));
}


}   //end namespace Clicross
}   //end namespace resource

// MeshAssetDataResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


MeshAssetDataResource::MeshAssetDataResource(const cross::resource::MeshAssetDataResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

MeshAssetDataResource::operator MeshAssetDataResource^ (const cross::resource::MeshAssetDataResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MeshAssetDataResource(const_cast<cross::resource::MeshAssetDataResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MeshAssetDataResource^)managedObj;
    }
    else
        return nullptr;
}

int MeshAssetDataResource::Mesh_UpdateLodMesh(int lod, System::String^ lodPath )
{
    return (static_cast<cross::resource::MeshAssetDataResource*>(this->_native))->Mesh_UpdateLodMesh( lod, ClangenCli::ToNativeString(lodPath).c_str());
}

int MeshAssetDataResource::Mesh_AddLodMesh(System::String^ lodPath )
{
    return (static_cast<cross::resource::MeshAssetDataResource*>(this->_native))->Mesh_AddLodMesh( ClangenCli::ToNativeString(lodPath).c_str());
}

int MeshAssetDataResource::Mesh_DelLodMesh(int lod )
{
    return (static_cast<cross::resource::MeshAssetDataResource*>(this->_native))->Mesh_DelLodMesh( lod);
}

bool MeshAssetDataResource::Mesh_HasCluster( )
{
    return (static_cast<cross::resource::MeshAssetDataResource*>(this->_native))->Mesh_HasCluster( );
}


}   //end namespace Clicross
}   //end namespace resource

// TerrainInfo export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::TerrainInfo::mBaseColorTextures export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(TerrainInfo::, mBaseColorTexturesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::TerrainInfo::mNormalTextures export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(TerrainInfo::, mNormalTexturesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::TerrainInfo::mHMRATextures export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(TerrainInfo::, mHMRATexturesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
Clicross::TerrainSurfaceType TerrainInfo::mSurfaceType::get()
 {
	return (Clicross::TerrainSurfaceType)((int)(static_cast<cross::TerrainInfo*>(this->_native))->mSurfaceType);
 }
 void TerrainInfo::mSurfaceType::set(Clicross::TerrainSurfaceType value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mSurfaceType = static_cast<cross::TerrainSurfaceType>(value);
 }

unsigned int TerrainInfo::mGridSizeX::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mGridSizeX;
 }
 void TerrainInfo::mGridSizeX::set(unsigned int value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mGridSizeX = value;
 }

unsigned int TerrainInfo::mGridSizeY::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mGridSizeY;
 }
 void TerrainInfo::mGridSizeY::set(unsigned int value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mGridSizeY = value;
 }

unsigned int TerrainInfo::mBlockSize::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mBlockSize;
 }
 void TerrainInfo::mBlockSize::set(unsigned int value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mBlockSize = value;
 }

unsigned int TerrainInfo::mTileSize::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mTileSize;
 }
 void TerrainInfo::mTileSize::set(unsigned int value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mTileSize = value;
 }

float TerrainInfo::mTexelDensity::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mTexelDensity;
 }
 void TerrainInfo::mTexelDensity::set(float value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mTexelDensity = value;
 }

TerrainInfo::mBaseColorTexturesCliType^ TerrainInfo::mBaseColorTextures::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mBaseColorTextures;
 }
 void TerrainInfo::mBaseColorTextures::set(TerrainInfo::mBaseColorTexturesCliType^ value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mBaseColorTextures = *value->_native;
 }

TerrainInfo::mNormalTexturesCliType^ TerrainInfo::mNormalTextures::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mNormalTextures;
 }
 void TerrainInfo::mNormalTextures::set(TerrainInfo::mNormalTexturesCliType^ value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mNormalTextures = *value->_native;
 }

TerrainInfo::mHMRATexturesCliType^ TerrainInfo::mHMRATextures::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mHMRATextures;
 }
 void TerrainInfo::mHMRATextures::set(TerrainInfo::mHMRATexturesCliType^ value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mHMRATextures = *value->_native;
 }

System::String^ TerrainInfo::mRootDataPath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mRootDataPath)).c_str());
 }
 void TerrainInfo::mRootDataPath::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mRootDataPath) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mHeightmapPrefix::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mHeightmapPrefix)).c_str());
 }
 void TerrainInfo::mHeightmapPrefix::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mHeightmapPrefix) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mAlbedoTexturePrefix::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mAlbedoTexturePrefix)).c_str());
 }
 void TerrainInfo::mAlbedoTexturePrefix::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mAlbedoTexturePrefix) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mWeightTexturePrefix::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mWeightTexturePrefix)).c_str());
 }
 void TerrainInfo::mWeightTexturePrefix::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mWeightTexturePrefix) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mMaterialPath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mMaterialPath)).c_str());
 }
 void TerrainInfo::mMaterialPath::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mMaterialPath) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mDefaultHeightmapPath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mDefaultHeightmapPath)).c_str());
 }
 void TerrainInfo::mDefaultHeightmapPath::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mDefaultHeightmapPath) = (ClangenCli::ToNativeString(value));
 }

System::String^ TerrainInfo::mDefaultAlbedoTexturePath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainInfo*>(this->_native))->mDefaultAlbedoTexturePath)).c_str());
 }
 void TerrainInfo::mDefaultAlbedoTexturePath::set(System::String^ value )
 {
	((static_cast<cross::TerrainInfo*>(this->_native))->mDefaultAlbedoTexturePath) = (ClangenCli::ToNativeString(value));
 }

bool TerrainInfo::mHasCollision::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mHasCollision;
 }
 void TerrainInfo::mHasCollision::set(bool value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mHasCollision = value;
 }

double TerrainInfo::mWGS84SemiMajor::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mWGS84SemiMajor;
 }
 void TerrainInfo::mWGS84SemiMajor::set(double value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mWGS84SemiMajor = value;
 }

float TerrainInfo::mWGS84HeightScale::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mWGS84HeightScale;
 }
 void TerrainInfo::mWGS84HeightScale::set(float value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mWGS84HeightScale = value;
 }

bool TerrainInfo::mRegenerateTileCache::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mRegenerateTileCache;
 }
 void TerrainInfo::mRegenerateTileCache::set(bool value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mRegenerateTileCache = value;
 }

unsigned int TerrainInfo::mLoadingPriority::get()
 {
	return (static_cast<cross::TerrainInfo*>(this->_native))->mLoadingPriority;
 }
 void TerrainInfo::mLoadingPriority::set(unsigned int value )
 {
	(static_cast<cross::TerrainInfo*>(this->_native))->mLoadingPriority = value;
 }


//constructor export here
TerrainInfo::TerrainInfo(): TerrainInfo(new cross::TerrainInfo(), true) {}


TerrainInfo::TerrainInfo(const cross::TerrainInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::TerrainInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

TerrainInfo::operator TerrainInfo^ (const cross::TerrainInfo* t)
{
    if(t)
    {
        return gcnew TerrainInfo(const_cast<cross::TerrainInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RenderTextureResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
RenderTextureResource::RenderTextureResource( )
    :RenderTextureResource(new cross::resource::RenderTextureResource(), true)
{
}

RenderTextureResource::RenderTextureResource(Clicross::resource::RenderTextureInfo^ info )
    :RenderTextureResource(new cross::resource::RenderTextureResource((const cross::resource::RenderTextureInfo& )(info)), true)
{
}



RenderTextureResource::RenderTextureResource(const cross::resource::RenderTextureResource * obj, bool created_by_clr): Clicross::resource::Texture(obj, created_by_clr)
{
}

RenderTextureResource::operator RenderTextureResource^ (const cross::resource::RenderTextureResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew RenderTextureResource(const_cast<cross::resource::RenderTextureResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (RenderTextureResource^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Resource^ RenderTextureResource::CreateRenderTextureFromScratch(System::String^ name, int dimension, int format, int width, int height, int depth, int mipcount, int layercount, int samplecount )
{
    return (Clicross::Resource^)(cross::resource::RenderTextureResource::CreateRenderTextureFromScratch( ClangenCli::ToNativeString(name).c_str(), dimension, format, width, height, depth, mipcount, layercount, samplecount));
}

Clicross::resource::RenderTextureResource^ RenderTextureResource::RenderTexture_CreateRenderTexture(System::String^ path )
{
    return (Clicross::resource::RenderTextureResource^)(cross::resource::RenderTextureResource::RenderTexture_CreateRenderTexture( ClangenCli::ToNativeString(path).c_str()));
}


}   //end namespace Clicross
}   //end namespace resource

// InstanceDataResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InstanceDataResource::InstanceDataResource( )
    :InstanceDataResource(new cross::resource::InstanceDataResource(), true)
{
}



InstanceDataResource::InstanceDataResource(const cross::resource::InstanceDataResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

InstanceDataResource::operator InstanceDataResource^ (const cross::resource::InstanceDataResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InstanceDataResource(const_cast<cross::resource::InstanceDataResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InstanceDataResource^)managedObj;
    }
    else
        return nullptr;
}

Clicross::resource::InstanceDataResource^ InstanceDataResource::InstanceDataResource_CreateInstanceDataResource( )
{
    return (Clicross::resource::InstanceDataResource^)(cross::resource::InstanceDataResource::InstanceDataResource_CreateInstanceDataResource( ));
}

void InstanceDataResource::SetInstanceCount(unsigned int instanceCount )
{
    (static_cast<cross::resource::InstanceDataResource*>(this->_native))->SetInstanceCount( instanceCount);
}

unsigned int InstanceDataResource::GetInstanceCount( )
{
    return (static_cast<cross::resource::InstanceDataResource*>(this->_native))->GetInstanceCount( );
}

void InstanceDataResource::ClearAllInstanceDatas( )
{
    (static_cast<cross::resource::InstanceDataResource*>(this->_native))->ClearAllInstanceDatas( );
}

void InstanceDataResource::ClearInstanceMemberData(System::String^ name )
{
    (static_cast<cross::resource::InstanceDataResource*>(this->_native))->ClearInstanceMemberData( ClangenCli::ToNativeString(name).c_str());
}

unsigned int InstanceDataResource::GetInstanceMemberDataNameCount( )
{
    return (static_cast<cross::resource::InstanceDataResource*>(this->_native))->GetInstanceMemberDataNameCount( );
}

System::String^ InstanceDataResource::GetInstanceMemberDataNameAt(unsigned int index )
{
    return ClangenCli::ToManagedString((static_cast<cross::resource::InstanceDataResource*>(this->_native))->GetInstanceMemberDataNameAt( index));
}

void InstanceDataResource::SetInstanceMemberData(System::String^ name, unsigned int type, System::IntPtr data, unsigned int size, unsigned int stride )
{
    (static_cast<cross::resource::InstanceDataResource*>(this->_native))->SetInstanceMemberData( ClangenCli::ToNativeString(name).c_str(), type, reinterpret_cast<void*>(data.ToInt64()), size, stride);
}

Clicross::resource::InstanceMemberDataInfo^ InstanceDataResource::EditorGetInstanceMemberData(System::String^ name )
{
    return gcnew Clicross::resource::InstanceMemberDataInfo(new cross::resource::InstanceMemberDataInfo(((static_cast<cross::resource::InstanceDataResource*>(this->_native))->EditorGetInstanceMemberData( ClangenCli::ToNativeString(name).c_str()))) , true);
}

void InstanceDataResource::MarkDirty( )
{
    (static_cast<cross::resource::InstanceDataResource*>(this->_native))->MarkDirty( );
}


}   //end namespace Clicross
}   //end namespace resource

// InstanceMemberData export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here
// cross::resource::InstanceMemberData::mData export start
	#define STLDECL_MANAGEDTYPE unsigned char
	#define STLDECL_NATIVETYPE unsigned char
	CPP_DECLARE_STLVECTOR(InstanceMemberData::, mDataCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ InstanceMemberData::mName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::resource::InstanceMemberData*>(this->_native))->mName)).c_str());
 }
 void InstanceMemberData::mName::set(System::String^ value )
 {
	((static_cast<cross::resource::InstanceMemberData*>(this->_native))->mName) = (ClangenCli::ToNativeString(value));
 }

Clicross::InstanceMemberType InstanceMemberData::mType::get()
 {
	return (Clicross::InstanceMemberType)((int)(static_cast<cross::resource::InstanceMemberData*>(this->_native))->mType);
 }
 void InstanceMemberData::mType::set(Clicross::InstanceMemberType value )
 {
	(static_cast<cross::resource::InstanceMemberData*>(this->_native))->mType = static_cast<cross::InstanceMemberType>(value);
 }

InstanceMemberData::mDataCliType^ InstanceMemberData::mData::get()
 {
	return (static_cast<cross::resource::InstanceMemberData*>(this->_native))->mData;
 }
 void InstanceMemberData::mData::set(InstanceMemberData::mDataCliType^ value )
 {
	(static_cast<cross::resource::InstanceMemberData*>(this->_native))->mData = *value->_native;
 }


//constructor export here
InstanceMemberData::InstanceMemberData(): InstanceMemberData(new cross::resource::InstanceMemberData(), true) {}


InstanceMemberData::InstanceMemberData(const cross::resource::InstanceMemberData * obj, bool created_by_clr): 
    _native(const_cast<cross::resource::InstanceMemberData *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InstanceMemberData::operator InstanceMemberData^ (const cross::resource::InstanceMemberData* t)
{
    if(t)
    {
        return gcnew InstanceMemberData(const_cast<cross::resource::InstanceMemberData*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// InstanceMemberDataInfo export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here
unsigned int InstanceMemberDataInfo::outType::get()
 {
	return (static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outType;
 }
 void InstanceMemberDataInfo::outType::set(unsigned int value )
 {
	(static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outType = value;
 }

System::IntPtr InstanceMemberDataInfo::outData::get()
 {
	return System::IntPtr((static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outData);
 }
 void InstanceMemberDataInfo::outData::set(System::IntPtr value )
 {
	(static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outData=reinterpret_cast< void* >(value.ToPointer());
 }

unsigned int InstanceMemberDataInfo::outSize::get()
 {
	return (static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outSize;
 }
 void InstanceMemberDataInfo::outSize::set(unsigned int value )
 {
	(static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outSize = value;
 }

unsigned int InstanceMemberDataInfo::outStride::get()
 {
	return (static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outStride;
 }
 void InstanceMemberDataInfo::outStride::set(unsigned int value )
 {
	(static_cast<cross::resource::InstanceMemberDataInfo*>(this->_native))->outStride = value;
 }


//constructor export here
InstanceMemberDataInfo::InstanceMemberDataInfo(): InstanceMemberDataInfo(new cross::resource::InstanceMemberDataInfo(), true) {}


InstanceMemberDataInfo::InstanceMemberDataInfo(const cross::resource::InstanceMemberDataInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::resource::InstanceMemberDataInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InstanceMemberDataInfo::operator InstanceMemberDataInfo^ (const cross::resource::InstanceMemberDataInfo* t)
{
    if(t)
    {
        return gcnew InstanceMemberDataInfo(const_cast<cross::resource::InstanceMemberDataInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// DataAssetResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
DataAssetResource::DataAssetResource( )
    :DataAssetResource(new cross::resource::DataAssetResource(), true)
{
}



DataAssetResource::DataAssetResource(const cross::resource::DataAssetResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

DataAssetResource::operator DataAssetResource^ (const cross::resource::DataAssetResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew DataAssetResource(const_cast<cross::resource::DataAssetResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (DataAssetResource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// DataAssetItem export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here
// cross::resource::DataAssetItem::Child export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::resource::DataAssetItem^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::resource::DataAssetItem
	CPP_DECLARE_STLMAP(DataAssetItem::, ChildCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE


//fields export here
System::String^ DataAssetItem::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::resource::DataAssetItem*>(this->_native))->Name)).c_str());
 }
 void DataAssetItem::Name::set(System::String^ value )
 {
	((static_cast<cross::resource::DataAssetItem*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::resource::DataAssetItemType DataAssetItem::Type::get()
 {
	return (Clicross::resource::DataAssetItemType)((int)(static_cast<cross::resource::DataAssetItem*>(this->_native))->Type);
 }
 void DataAssetItem::Type::set(Clicross::resource::DataAssetItemType value )
 {
	(static_cast<cross::resource::DataAssetItem*>(this->_native))->Type = static_cast<cross::resource::DataAssetItemType>(value);
 }

unsigned long long DataAssetItem::TypeId::get()
 {
	return (static_cast<cross::resource::DataAssetItem*>(this->_native))->TypeId;
 }
 void DataAssetItem::TypeId::set(unsigned long long value )
 {
	(static_cast<cross::resource::DataAssetItem*>(this->_native))->TypeId = value;
 }

Clicross::resource::DataAssetItemTypeCategory DataAssetItem::Category::get()
 {
	return (Clicross::resource::DataAssetItemTypeCategory)((int)(static_cast<cross::resource::DataAssetItem*>(this->_native))->Category);
 }
 void DataAssetItem::Category::set(Clicross::resource::DataAssetItemTypeCategory value )
 {
	(static_cast<cross::resource::DataAssetItem*>(this->_native))->Category = static_cast<cross::resource::DataAssetItemTypeCategory>(value);
 }

System::String^ DataAssetItem::Value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::resource::DataAssetItem*>(this->_native))->Value)).c_str());
 }
 void DataAssetItem::Value::set(System::String^ value )
 {
	((static_cast<cross::resource::DataAssetItem*>(this->_native))->Value) = (ClangenCli::ToNativeString(value));
 }

System::String^ DataAssetItem::DefaultValue::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::resource::DataAssetItem*>(this->_native))->DefaultValue)).c_str());
 }
 void DataAssetItem::DefaultValue::set(System::String^ value )
 {
	((static_cast<cross::resource::DataAssetItem*>(this->_native))->DefaultValue) = (ClangenCli::ToNativeString(value));
 }

DataAssetItem::ChildCliType^ DataAssetItem::Child::get()
 {
	return (static_cast<cross::resource::DataAssetItem*>(this->_native))->Child;
 }
 void DataAssetItem::Child::set(DataAssetItem::ChildCliType^ value )
 {
	(static_cast<cross::resource::DataAssetItem*>(this->_native))->Child = *value->_native;
 }


//constructor export here
DataAssetItem::DataAssetItem( )
    :DataAssetItem(new cross::resource::DataAssetItem(), true)
{
}



DataAssetItem::DataAssetItem(const cross::resource::DataAssetItem * obj, bool created_by_clr): 
    _native(const_cast<cross::resource::DataAssetItem *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DataAssetItem::operator DataAssetItem^ (const cross::resource::DataAssetItem* t)
{
    if(t)
    {
        return gcnew DataAssetItem(const_cast<cross::resource::DataAssetItem*>(t));
    }
    else
        return nullptr;
}

Clicross::resource::DataAssetItem^ DataAssetItem::GetDataItem(System::String^ name )
{
    return gcnew Clicross::resource::DataAssetItem(new cross::resource::DataAssetItem(((static_cast<cross::resource::DataAssetItem*>(this->_native))->GetDataItem( ClangenCli::ToNativeString(name).c_str()))) , true);
}


}   //end namespace Clicross
}   //end namespace resource

// HLODResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
HLODResource::HLODResource( )
    :HLODResource(new cross::resource::HLODResource(), true)
{
}



HLODResource::HLODResource(const cross::resource::HLODResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

HLODResource::operator HLODResource^ (const cross::resource::HLODResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HLODResource(const_cast<cross::resource::HLODResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HLODResource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// HLODSetup export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::HLODSimplification^ HLODSetup::Setting::get()
 {
	return gcnew Clicross::HLODSimplification(new cross::HLODSimplification(((static_cast<cross::HLODSetup*>(this->_native))->Setting)) , true);
 }
 void HLODSetup::Setting::set(Clicross::HLODSimplification^ value )
 {
	(static_cast<cross::HLODSetup*>(this->_native))->Setting = value;
 }

System::String^ HLODSetup::OverrideBaseMaterial::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::HLODSetup*>(this->_native))->OverrideBaseMaterial)).c_str());
 }
 void HLODSetup::OverrideBaseMaterial::set(System::String^ value )
 {
	((static_cast<cross::HLODSetup*>(this->_native))->OverrideBaseMaterial) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
HLODSetup::HLODSetup(): HLODSetup(new cross::HLODSetup(), true) {}


HLODSetup::HLODSetup(const cross::HLODSetup * obj, bool created_by_clr): 
    _native(const_cast<cross::HLODSetup *>(obj))
	, _created_by_clr(created_by_clr)
{
}

HLODSetup::operator HLODSetup^ (const cross::HLODSetup* t)
{
    if(t)
    {
        return gcnew HLODSetup(const_cast<cross::HLODSetup*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// HLODSimplification export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float HLODSimplification::TransitionScreenSize::get()
 {
	return (static_cast<cross::HLODSimplification*>(this->_native))->TransitionScreenSize;
 }
 void HLODSimplification::TransitionScreenSize::set(float value )
 {
	(static_cast<cross::HLODSimplification*>(this->_native))->TransitionScreenSize = value;
 }

bool HLODSimplification::UseOverrideDrawDistance::get()
 {
	return (static_cast<cross::HLODSimplification*>(this->_native))->UseOverrideDrawDistance;
 }
 void HLODSimplification::UseOverrideDrawDistance::set(bool value )
 {
	(static_cast<cross::HLODSimplification*>(this->_native))->UseOverrideDrawDistance = value;
 }

float HLODSimplification::OverrideDrawDistance::get()
 {
	return (static_cast<cross::HLODSimplification*>(this->_native))->OverrideDrawDistance;
 }
 void HLODSimplification::OverrideDrawDistance::set(float value )
 {
	(static_cast<cross::HLODSimplification*>(this->_native))->OverrideDrawDistance = value;
 }


//constructor export here
HLODSimplification::HLODSimplification(): HLODSimplification(new cross::HLODSimplification(), true) {}


HLODSimplification::HLODSimplification(const cross::HLODSimplification * obj, bool created_by_clr): 
    _native(const_cast<cross::HLODSimplification *>(obj))
	, _created_by_clr(created_by_clr)
{
}

HLODSimplification::operator HLODSimplification^ (const cross::HLODSimplification* t)
{
    if(t)
    {
        return gcnew HLODSimplification(const_cast<cross::HLODSimplification*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// HLODSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::HLODSetup^ HLODSetting::DefaultSetup::get()
 {
	return gcnew Clicross::HLODSetup(new cross::HLODSetup(((static_cast<cross::HLODSetting*>(this->_native))->DefaultSetup)) , true);
 }
 void HLODSetting::DefaultSetup::set(Clicross::HLODSetup^ value )
 {
	(static_cast<cross::HLODSetting*>(this->_native))->DefaultSetup = value;
 }

System::String^ HLODSetting::BaseMaterial::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::HLODSetting*>(this->_native))->BaseMaterial)).c_str());
 }
 void HLODSetting::BaseMaterial::set(System::String^ value )
 {
	((static_cast<cross::HLODSetting*>(this->_native))->BaseMaterial) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
HLODSetting::HLODSetting(): HLODSetting(new cross::HLODSetting(), true) {}


HLODSetting::HLODSetting(const cross::HLODSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::HLODSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

HLODSetting::operator HLODSetting^ (const cross::HLODSetting* t)
{
    if(t)
    {
        return gcnew HLODSetting(const_cast<cross::HLODSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ImageInfo export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int ImageInfo::Width::get()
 {
	return (static_cast<cross::ImageInfo*>(this->_native))->Width;
 }
 void ImageInfo::Width::set(int value )
 {
	(static_cast<cross::ImageInfo*>(this->_native))->Width = value;
 }

int ImageInfo::Height::get()
 {
	return (static_cast<cross::ImageInfo*>(this->_native))->Height;
 }
 void ImageInfo::Height::set(int value )
 {
	(static_cast<cross::ImageInfo*>(this->_native))->Height = value;
 }

System::IntPtr ImageInfo::Data::get()
 {
	return System::IntPtr((static_cast<cross::ImageInfo*>(this->_native))->Data);
 }
 void ImageInfo::Data::set(System::IntPtr value )
 {
	(static_cast<cross::ImageInfo*>(this->_native))->Data=reinterpret_cast< unsigned int* >(value.ToPointer());
 }


//constructor export here
ImageInfo::ImageInfo(): ImageInfo(new cross::ImageInfo(), true) {}


ImageInfo::ImageInfo(const cross::ImageInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::ImageInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ImageInfo::operator ImageInfo^ (const cross::ImageInfo* t)
{
    if(t)
    {
        return gcnew ImageInfo(const_cast<cross::ImageInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ResourceAABB export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Double3^ ResourceAABB::min::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cross::ResourceAABB*>(this->_native))->min)) , true);
 }
 void ResourceAABB::min::set(Clicross::Double3^ value )
 {
	(static_cast<cross::ResourceAABB*>(this->_native))->min = value;
 }

Clicross::Double3^ ResourceAABB::max::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cross::ResourceAABB*>(this->_native))->max)) , true);
 }
 void ResourceAABB::max::set(Clicross::Double3^ value )
 {
	(static_cast<cross::ResourceAABB*>(this->_native))->max = value;
 }


//constructor export here
ResourceAABB::ResourceAABB(): ResourceAABB(new cross::ResourceAABB(), true) {}


ResourceAABB::ResourceAABB(const cross::ResourceAABB * obj, bool created_by_clr): 
    _native(const_cast<cross::ResourceAABB *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ResourceAABB::operator ResourceAABB^ (const cross::ResourceAABB* t)
{
    if(t)
    {
        return gcnew ResourceAABB(const_cast<cross::ResourceAABB*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ResourceUtil export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ResourceUtil::ResourceUtil(const cross::ResourceUtil * obj, bool created_by_clr): 
    _native(const_cast<cross::ResourceUtil *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ResourceUtil::operator ResourceUtil^ (const cross::ResourceUtil* t)
{
    if(t)
    {
        return gcnew ResourceUtil(const_cast<cross::ResourceUtil*>(t));
    }
    else
        return nullptr;
}

void ResourceUtil::ReleaseResource(Clicross::Resource^ resource )
{
    cross::ResourceUtil::ReleaseResource( ( cross::Resource* )(resource));
}

Clicross::Resource^ ResourceUtil::ResourceGet(System::String^ path )
{
    return (Clicross::Resource^)(cross::ResourceUtil::ResourceGet( ClangenCli::ToNativeString(path).c_str()));
}

int ResourceUtil::ResourceGetClassID(System::String^ path )
{
    return cross::ResourceUtil::ResourceGetClassID( ClangenCli::ToNativeString(path).c_str());
}

void ResourceUtil::ResourceSaveToFile(Clicross::Resource^ resource, System::String^ path )
{
    cross::ResourceUtil::ResourceSaveToFile( ( cross::Resource* )(resource), ClangenCli::ToNativeString(path).c_str());
}

bool ResourceUtil::ResourceCheckDependency(Clicross::Resource^ resource, System::String^ path, bool recursive )
{
    return cross::ResourceUtil::ResourceCheckDependency( ( cross::Resource* )(resource), ClangenCli::ToNativeString(path).c_str(), recursive);
}

bool ResourceUtil::CompareTexture(System::String^ path, System::String^ path1, System::String^ path2, [System::Runtime::InteropServices::Out] double MSE, [System::Runtime::InteropServices::Out] double PSNR, [System::Runtime::InteropServices::Out] double SSIM )
{
    return cross::ResourceUtil::CompareTexture( ClangenCli::ToNativeString(path).c_str(), ClangenCli::ToNativeString(path1).c_str(), ClangenCli::ToNativeString(path2).c_str(), MSE, PSNR, SSIM);
}

void ResourceUtil::Editor_SaveImage(System::String^ filename, int width, int height, System::IntPtr data )
{
    cross::ResourceUtil::Editor_SaveImage( ClangenCli::ToNativeString(filename).c_str(), width, height, reinterpret_cast<unsigned int*>(data.ToInt64()));
}

Clicross::ImageInfo^ ResourceUtil::Editor_LoadImage(System::String^ filename )
{
    return gcnew Clicross::ImageInfo(new cross::ImageInfo((cross::ResourceUtil::Editor_LoadImage( ClangenCli::ToNativeString(filename).c_str()))) , true);
}

System::IntPtr ResourceUtil::Editor_CreateImageBuffer(int width, int height )
{
    return System::IntPtr(cross::ResourceUtil::Editor_CreateImageBuffer( width, height));
}

void ResourceUtil::Editor_FreeImage(System::IntPtr image )
{
    cross::ResourceUtil::Editor_FreeImage( reinterpret_cast<unsigned int*>(image.ToInt64()));
}

System::IntPtr ResourceUtil::Editor_CreateImage(System::String^ filename, int width, int height, unsigned int color )
{
    return System::IntPtr(cross::ResourceUtil::Editor_CreateImage( ClangenCli::ToNativeString(filename).c_str(), width, height, color));
}

void ResourceUtil::CrossEngine_DeleteResource(System::String^ filename )
{
    cross::ResourceUtil::CrossEngine_DeleteResource( ClangenCli::ToNativeString(filename).c_str());
}

void ResourceUtil::CrossEngine_ReloadResource(System::String^ filename )
{
    cross::ResourceUtil::CrossEngine_ReloadResource( ClangenCli::ToNativeString(filename).c_str());
}


}   //end namespace Clicross

// DynamicEnum export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ DynamicEnum::mSelectedProperty::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::DynamicEnum*>(this->_native))->mSelectedProperty)).c_str());
 }
 void DynamicEnum::mSelectedProperty::set(System::String^ value )
 {
	((static_cast<cross::DynamicEnum*>(this->_native))->mSelectedProperty) = (ClangenCli::ToNativeString(value));
 }


//constructor export here


DynamicEnum::DynamicEnum(const cross::DynamicEnum * obj, bool created_by_clr): 
    _native(const_cast<cross::DynamicEnum *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DynamicEnum::operator DynamicEnum^ (const cross::DynamicEnum* t)
{
    if(t)
    {
        return gcnew DynamicEnum(const_cast<cross::DynamicEnum*>(t));
    }
    else
        return nullptr;
}

UnknowKeeper^ DynamicEnum::GetEnumElementsCopy( )
{
    return UnknownKeeperCast((static_cast<cross::DynamicEnum*>(this->_native))->GetEnumElementsCopy( ));
}

unsigned int DynamicEnum::GetEnumElementsNum( )
{
    return (static_cast<cross::DynamicEnum*>(this->_native))->GetEnumElementsNum( );
}

System::String^ DynamicEnum::GetEnumElementByIndex(unsigned int index )
{
    return ClangenCli::ToManagedString((((static_cast<cross::DynamicEnum*>(this->_native))->GetEnumElementByIndex( index))).c_str());
}

bool DynamicEnum::SelectElement(System::String^ enumElement )
{
    return (static_cast<cross::DynamicEnum*>(this->_native))->SelectElement( ClangenCli::ToNativeString(enumElement).c_str());
}

bool DynamicEnum::SelectElementIndex(unsigned int index )
{
    return (static_cast<cross::DynamicEnum*>(this->_native))->SelectElementIndex( index);
}

System::String^ DynamicEnum::GetSelectElementCopy( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::DynamicEnum*>(this->_native))->GetSelectElementCopy( ))).c_str());
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


