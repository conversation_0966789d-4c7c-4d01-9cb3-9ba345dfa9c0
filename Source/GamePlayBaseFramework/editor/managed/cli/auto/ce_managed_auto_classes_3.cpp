//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// RenderTextureInfo export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here
System::String^ RenderTextureInfo::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Name)).c_str());
 }
 void RenderTextureInfo::Name::set(System::String^ value )
 {
	((static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

int RenderTextureInfo::Dimension::get()
 {
	return ((int)(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Dimension);
 }
 void RenderTextureInfo::Dimension::set(int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Dimension = static_cast<TextureDimension>(value);
 }

int RenderTextureInfo::Format::get()
 {
	return ((int)(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Format);
 }
 void RenderTextureInfo::Format::set(int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Format = static_cast<RenderTextureFormat>(value);
 }

unsigned int RenderTextureInfo::Width::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Width;
 }
 void RenderTextureInfo::Width::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Width = value;
 }

unsigned int RenderTextureInfo::Height::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Height;
 }
 void RenderTextureInfo::Height::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Height = value;
 }

unsigned int RenderTextureInfo::Depth::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Depth;
 }
 void RenderTextureInfo::Depth::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->Depth = value;
 }

unsigned int RenderTextureInfo::MipCount::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->MipCount;
 }
 void RenderTextureInfo::MipCount::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->MipCount = value;
 }

unsigned int RenderTextureInfo::LayerCount::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->LayerCount;
 }
 void RenderTextureInfo::LayerCount::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->LayerCount = value;
 }

unsigned int RenderTextureInfo::SampleCount::get()
 {
	return (static_cast<cross::resource::RenderTextureInfo*>(this->_native))->SampleCount;
 }
 void RenderTextureInfo::SampleCount::set(unsigned int value )
 {
	(static_cast<cross::resource::RenderTextureInfo*>(this->_native))->SampleCount = value;
 }


//constructor export here
RenderTextureInfo::RenderTextureInfo(): RenderTextureInfo(new cross::resource::RenderTextureInfo(), true) {}


RenderTextureInfo::RenderTextureInfo(const cross::resource::RenderTextureInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::resource::RenderTextureInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RenderTextureInfo::operator RenderTextureInfo^ (const cross::resource::RenderTextureInfo* t)
{
    if(t)
    {
        return gcnew RenderTextureInfo(const_cast<cross::resource::RenderTextureInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// FloatCurveResource export start
namespace Clicross
{
namespace resource
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


FloatCurveResource::FloatCurveResource(const cross::resource::FloatCurveResource * obj, bool created_by_clr): Clicross::Resource(obj, created_by_clr)
{
}

FloatCurveResource::operator FloatCurveResource^ (const cross::resource::FloatCurveResource* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew FloatCurveResource(const_cast<cross::resource::FloatCurveResource*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (FloatCurveResource^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace resource

// EntityDistanceCulling export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float EntityDistanceCulling::minCullingDistance::get()
 {
	return (static_cast<cross::EntityDistanceCulling*>(this->_native))->minCullingDistance;
 }
 void EntityDistanceCulling::minCullingDistance::set(float value )
 {
	(static_cast<cross::EntityDistanceCulling*>(this->_native))->minCullingDistance = value;
 }

float EntityDistanceCulling::maxCullingDistance::get()
 {
	return (static_cast<cross::EntityDistanceCulling*>(this->_native))->maxCullingDistance;
 }
 void EntityDistanceCulling::maxCullingDistance::set(float value )
 {
	(static_cast<cross::EntityDistanceCulling*>(this->_native))->maxCullingDistance = value;
 }


//constructor export here
EntityDistanceCulling::EntityDistanceCulling(): EntityDistanceCulling(new cross::EntityDistanceCulling(), true) {}


EntityDistanceCulling::EntityDistanceCulling(const cross::EntityDistanceCulling * obj, bool created_by_clr): 
    _native(const_cast<cross::EntityDistanceCulling *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EntityDistanceCulling::operator EntityDistanceCulling^ (const cross::EntityDistanceCulling* t)
{
    if(t)
    {
        return gcnew EntityDistanceCulling(const_cast<cross::EntityDistanceCulling*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// FogCommonSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float FogCommonSetting::Density::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->Density;
 }
 void FogCommonSetting::Density::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->Density = value;
 }

float FogCommonSetting::HeightFallOff::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->HeightFallOff;
 }
 void FogCommonSetting::HeightFallOff::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->HeightFallOff = value;
 }

float FogCommonSetting::HeightOffset::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->HeightOffset;
 }
 void FogCommonSetting::HeightOffset::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->HeightOffset = value;
 }

float FogCommonSetting::DensitySecond::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->DensitySecond;
 }
 void FogCommonSetting::DensitySecond::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->DensitySecond = value;
 }

float FogCommonSetting::HeightFallOffSecond::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->HeightFallOffSecond;
 }
 void FogCommonSetting::HeightFallOffSecond::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->HeightFallOffSecond = value;
 }

float FogCommonSetting::HeightOffsetSecond::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->HeightOffsetSecond;
 }
 void FogCommonSetting::HeightOffsetSecond::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->HeightOffsetSecond = value;
 }

float FogCommonSetting::StartDistance::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->StartDistance;
 }
 void FogCommonSetting::StartDistance::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->StartDistance = value;
 }

float FogCommonSetting::EndDistance::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->EndDistance;
 }
 void FogCommonSetting::EndDistance::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->EndDistance = value;
 }

float FogCommonSetting::CutOffDistance::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->CutOffDistance;
 }
 void FogCommonSetting::CutOffDistance::set(float value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->CutOffDistance = value;
 }

bool FogCommonSetting::UseWGS84::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->UseWGS84;
 }
 void FogCommonSetting::UseWGS84::set(bool value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->UseWGS84 = value;
 }

bool FogCommonSetting::CloudyAtomsphere::get()
 {
	return (static_cast<cross::FogCommonSetting*>(this->_native))->CloudyAtomsphere;
 }
 void FogCommonSetting::CloudyAtomsphere::set(bool value )
 {
	(static_cast<cross::FogCommonSetting*>(this->_native))->CloudyAtomsphere = value;
 }


//constructor export here
FogCommonSetting::FogCommonSetting(): FogCommonSetting(new cross::FogCommonSetting(), true) {}


FogCommonSetting::FogCommonSetting(const cross::FogCommonSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::FogCommonSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

FogCommonSetting::operator FogCommonSetting^ (const cross::FogCommonSetting* t)
{
    if(t)
    {
        return gcnew FogCommonSetting(const_cast<cross::FogCommonSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ScreenFogSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float ScreenFogSetting::MaxFogOpacity::get()
 {
	return (static_cast<cross::ScreenFogSetting*>(this->_native))->MaxFogOpacity;
 }
 void ScreenFogSetting::MaxFogOpacity::set(float value )
 {
	(static_cast<cross::ScreenFogSetting*>(this->_native))->MaxFogOpacity = value;
 }

Clicross::Float3^ ScreenFogSetting::Inscatter::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::ScreenFogSetting*>(this->_native))->Inscatter)) , true);
 }
 void ScreenFogSetting::Inscatter::set(Clicross::Float3^ value )
 {
	(static_cast<cross::ScreenFogSetting*>(this->_native))->Inscatter = value;
 }

Clicross::Float3^ ScreenFogSetting::InscatterSecond::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::ScreenFogSetting*>(this->_native))->InscatterSecond)) , true);
 }
 void ScreenFogSetting::InscatterSecond::set(Clicross::Float3^ value )
 {
	(static_cast<cross::ScreenFogSetting*>(this->_native))->InscatterSecond = value;
 }

Clicross::ScreenFogDirectionalSetting^ ScreenFogSetting::Directional::get()
 {
	return gcnew Clicross::ScreenFogDirectionalSetting(new cross::ScreenFogDirectionalSetting(((static_cast<cross::ScreenFogSetting*>(this->_native))->Directional)) , true);
 }
 void ScreenFogSetting::Directional::set(Clicross::ScreenFogDirectionalSetting^ value )
 {
	(static_cast<cross::ScreenFogSetting*>(this->_native))->Directional = value;
 }


//constructor export here
ScreenFogSetting::ScreenFogSetting(): ScreenFogSetting(new cross::ScreenFogSetting(), true) {}


ScreenFogSetting::ScreenFogSetting(const cross::ScreenFogSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::ScreenFogSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ScreenFogSetting::operator ScreenFogSetting^ (const cross::ScreenFogSetting* t)
{
    if(t)
    {
        return gcnew ScreenFogSetting(const_cast<cross::ScreenFogSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ScreenFogDirectionalSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float3^ ScreenFogDirectionalSetting::DirectionalInscatter::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalInscatter)) , true);
 }
 void ScreenFogDirectionalSetting::DirectionalInscatter::set(Clicross::Float3^ value )
 {
	(static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalInscatter = value;
 }

Clicross::Float3^ ScreenFogDirectionalSetting::AtomsphereContribution::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->AtomsphereContribution)) , true);
 }
 void ScreenFogDirectionalSetting::AtomsphereContribution::set(Clicross::Float3^ value )
 {
	(static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->AtomsphereContribution = value;
 }

float ScreenFogDirectionalSetting::DirectionalIntensity::get()
 {
	return (static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalIntensity;
 }
 void ScreenFogDirectionalSetting::DirectionalIntensity::set(float value )
 {
	(static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalIntensity = value;
 }

float ScreenFogDirectionalSetting::InscatterExponent::get()
 {
	return (static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->InscatterExponent;
 }
 void ScreenFogDirectionalSetting::InscatterExponent::set(float value )
 {
	(static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->InscatterExponent = value;
 }

float ScreenFogDirectionalSetting::DirectionalStartDistance::get()
 {
	return (static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalStartDistance;
 }
 void ScreenFogDirectionalSetting::DirectionalStartDistance::set(float value )
 {
	(static_cast<cross::ScreenFogDirectionalSetting*>(this->_native))->DirectionalStartDistance = value;
 }


//constructor export here
ScreenFogDirectionalSetting::ScreenFogDirectionalSetting(): ScreenFogDirectionalSetting(new cross::ScreenFogDirectionalSetting(), true) {}


ScreenFogDirectionalSetting::ScreenFogDirectionalSetting(const cross::ScreenFogDirectionalSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::ScreenFogDirectionalSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ScreenFogDirectionalSetting::operator ScreenFogDirectionalSetting^ (const cross::ScreenFogDirectionalSetting* t)
{
    if(t)
    {
        return gcnew ScreenFogDirectionalSetting(const_cast<cross::ScreenFogDirectionalSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// VolumetricFogSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool VolumetricFogSetting::enable::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->enable;
 }
 void VolumetricFogSetting::enable::set(bool value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->enable = value;
 }

bool VolumetricFogSetting::EnableAdaptiveVolume::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->EnableAdaptiveVolume;
 }
 void VolumetricFogSetting::EnableAdaptiveVolume::set(bool value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->EnableAdaptiveVolume = value;
 }

float VolumetricFogSetting::AdaptiveSpeed::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveSpeed;
 }
 void VolumetricFogSetting::AdaptiveSpeed::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveSpeed = value;
 }

float VolumetricFogSetting::AdaptiveVolumeMinLength::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveVolumeMinLength;
 }
 void VolumetricFogSetting::AdaptiveVolumeMinLength::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveVolumeMinLength = value;
 }

float VolumetricFogSetting::AdaptiveVolumeMaxLength::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveVolumeMaxLength;
 }
 void VolumetricFogSetting::AdaptiveVolumeMaxLength::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->AdaptiveVolumeMaxLength = value;
 }

float VolumetricFogSetting::StartDistance::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->StartDistance;
 }
 void VolumetricFogSetting::StartDistance::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->StartDistance = value;
 }

float VolumetricFogSetting::CutOffDistance::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->CutOffDistance;
 }
 void VolumetricFogSetting::CutOffDistance::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->CutOffDistance = value;
 }

Clicross::Float3^ VolumetricFogSetting::Albedo::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::VolumetricFogSetting*>(this->_native))->Albedo)) , true);
 }
 void VolumetricFogSetting::Albedo::set(Clicross::Float3^ value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->Albedo = value;
 }

float VolumetricFogSetting::ExtinctionScale::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->ExtinctionScale;
 }
 void VolumetricFogSetting::ExtinctionScale::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->ExtinctionScale = value;
 }

float VolumetricFogSetting::AtomsphereFactor::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->AtomsphereFactor;
 }
 void VolumetricFogSetting::AtomsphereFactor::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->AtomsphereFactor = value;
 }

float VolumetricFogSetting::MiePhase1::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->MiePhase1;
 }
 void VolumetricFogSetting::MiePhase1::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->MiePhase1 = value;
 }

float VolumetricFogSetting::MiePhase2::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->MiePhase2;
 }
 void VolumetricFogSetting::MiePhase2::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->MiePhase2 = value;
 }

float VolumetricFogSetting::LightVolumetricFactor::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->LightVolumetricFactor;
 }
 void VolumetricFogSetting::LightVolumetricFactor::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->LightVolumetricFactor = value;
 }

bool VolumetricFogSetting::BoundingFadeOut::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->BoundingFadeOut;
 }
 void VolumetricFogSetting::BoundingFadeOut::set(bool value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->BoundingFadeOut = value;
 }

float VolumetricFogSetting::FadeOutDistance::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->FadeOutDistance;
 }
 void VolumetricFogSetting::FadeOutDistance::set(float value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->FadeOutDistance = value;
 }

bool VolumetricFogSetting::CloudShadow::get()
 {
	return (static_cast<cross::VolumetricFogSetting*>(this->_native))->CloudShadow;
 }
 void VolumetricFogSetting::CloudShadow::set(bool value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->CloudShadow = value;
 }

Clicross::VFogQualityTradeSetting^ VolumetricFogSetting::QualityTrade::get()
 {
	return gcnew Clicross::VFogQualityTradeSetting(new cross::VFogQualityTradeSetting(((static_cast<cross::VolumetricFogSetting*>(this->_native))->QualityTrade)) , true);
 }
 void VolumetricFogSetting::QualityTrade::set(Clicross::VFogQualityTradeSetting^ value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->QualityTrade = value;
 }

Clicross::VFogDustSetting^ VolumetricFogSetting::Dust::get()
 {
	return gcnew Clicross::VFogDustSetting(new cross::VFogDustSetting(((static_cast<cross::VolumetricFogSetting*>(this->_native))->Dust)) , true);
 }
 void VolumetricFogSetting::Dust::set(Clicross::VFogDustSetting^ value )
 {
	(static_cast<cross::VolumetricFogSetting*>(this->_native))->Dust = value;
 }


//constructor export here
VolumetricFogSetting::VolumetricFogSetting(): VolumetricFogSetting(new cross::VolumetricFogSetting(), true) {}


VolumetricFogSetting::VolumetricFogSetting(const cross::VolumetricFogSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::VolumetricFogSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

VolumetricFogSetting::operator VolumetricFogSetting^ (const cross::VolumetricFogSetting* t)
{
    if(t)
    {
        return gcnew VolumetricFogSetting(const_cast<cross::VolumetricFogSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// VFogQualityTradeSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int VFogQualityTradeSetting::MaxLight::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MaxLight;
 }
 void VFogQualityTradeSetting::MaxLight::set(int value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MaxLight = value;
 }

float VFogQualityTradeSetting::MultiSampleNum::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MultiSampleNum;
 }
 void VFogQualityTradeSetting::MultiSampleNum::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MultiSampleNum = value;
 }

float VFogQualityTradeSetting::MultiSampleJitter::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MultiSampleJitter;
 }
 void VFogQualityTradeSetting::MultiSampleJitter::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->MultiSampleJitter = value;
 }

bool VFogQualityTradeSetting::UseBlur::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->UseBlur;
 }
 void VFogQualityTradeSetting::UseBlur::set(bool value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->UseBlur = value;
 }

float VFogQualityTradeSetting::BlurSize::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->BlurSize;
 }
 void VFogQualityTradeSetting::BlurSize::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->BlurSize = value;
 }

float VFogQualityTradeSetting::BlurStrength::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->BlurStrength;
 }
 void VFogQualityTradeSetting::BlurStrength::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->BlurStrength = value;
 }

float VFogQualityTradeSetting::ResolutionRatio::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ResolutionRatio;
 }
 void VFogQualityTradeSetting::ResolutionRatio::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ResolutionRatio = value;
 }

float VFogQualityTradeSetting::ZSliceNum::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ZSliceNum;
 }
 void VFogQualityTradeSetting::ZSliceNum::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ZSliceNum = value;
 }

float VFogQualityTradeSetting::ZVoxelScale::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ZVoxelScale;
 }
 void VFogQualityTradeSetting::ZVoxelScale::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->ZVoxelScale = value;
 }

bool VFogQualityTradeSetting::Temporal::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->Temporal;
 }
 void VFogQualityTradeSetting::Temporal::set(bool value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->Temporal = value;
 }

float VFogQualityTradeSetting::HistoryWeight::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->HistoryWeight;
 }
 void VFogQualityTradeSetting::HistoryWeight::set(float value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->HistoryWeight = value;
 }

bool VFogQualityTradeSetting::LightInjection::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->LightInjection;
 }
 void VFogQualityTradeSetting::LightInjection::set(bool value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->LightInjection = value;
 }

bool VFogQualityTradeSetting::TurnOffDirectionalLight::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->TurnOffDirectionalLight;
 }
 void VFogQualityTradeSetting::TurnOffDirectionalLight::set(bool value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->TurnOffDirectionalLight = value;
 }

bool VFogQualityTradeSetting::CpuLightProjection::get()
 {
	return (static_cast<cross::VFogQualityTradeSetting*>(this->_native))->CpuLightProjection;
 }
 void VFogQualityTradeSetting::CpuLightProjection::set(bool value )
 {
	(static_cast<cross::VFogQualityTradeSetting*>(this->_native))->CpuLightProjection = value;
 }


//constructor export here
VFogQualityTradeSetting::VFogQualityTradeSetting(): VFogQualityTradeSetting(new cross::VFogQualityTradeSetting(), true) {}


VFogQualityTradeSetting::VFogQualityTradeSetting(const cross::VFogQualityTradeSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::VFogQualityTradeSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

VFogQualityTradeSetting::operator VFogQualityTradeSetting^ (const cross::VFogQualityTradeSetting* t)
{
    if(t)
    {
        return gcnew VFogQualityTradeSetting(const_cast<cross::VFogQualityTradeSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// VFogDustSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool VFogDustSetting::enable::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->enable;
 }
 void VFogDustSetting::enable::set(bool value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->enable = value;
 }

Clicross::Float3^ VFogDustSetting::DustAlbedo::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::VFogDustSetting*>(this->_native))->DustAlbedo)) , true);
 }
 void VFogDustSetting::DustAlbedo::set(Clicross::Float3^ value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->DustAlbedo = value;
 }

float VFogDustSetting::DustDensity::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->DustDensity;
 }
 void VFogDustSetting::DustDensity::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->DustDensity = value;
 }

float VFogDustSetting::DustScale::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->DustScale;
 }
 void VFogDustSetting::DustScale::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->DustScale = value;
 }

float VFogDustSetting::Height::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->Height;
 }
 void VFogDustSetting::Height::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->Height = value;
 }

float VFogDustSetting::Spiral::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->Spiral;
 }
 void VFogDustSetting::Spiral::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->Spiral = value;
 }

float VFogDustSetting::LightAbsorb::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->LightAbsorb;
 }
 void VFogDustSetting::LightAbsorb::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->LightAbsorb = value;
 }

Clicross::Float3^ VFogDustSetting::Wind::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::VFogDustSetting*>(this->_native))->Wind)) , true);
 }
 void VFogDustSetting::Wind::set(Clicross::Float3^ value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->Wind = value;
 }

bool VFogDustSetting::DustOverride::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->DustOverride;
 }
 void VFogDustSetting::DustOverride::set(bool value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->DustOverride = value;
 }

float VFogDustSetting::SFogLightFactor::get()
 {
	return (static_cast<cross::VFogDustSetting*>(this->_native))->SFogLightFactor;
 }
 void VFogDustSetting::SFogLightFactor::set(float value )
 {
	(static_cast<cross::VFogDustSetting*>(this->_native))->SFogLightFactor = value;
 }


//constructor export here
VFogDustSetting::VFogDustSetting(): VFogDustSetting(new cross::VFogDustSetting(), true) {}


VFogDustSetting::VFogDustSetting(const cross::VFogDustSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::VFogDustSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

VFogDustSetting::operator VFogDustSetting^ (const cross::VFogDustSetting* t)
{
    if(t)
    {
        return gcnew VFogDustSetting(const_cast<cross::VFogDustSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudRenderingSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool CloudRenderingSetting::RefreshHistoryTex::get()
 {
	return (static_cast<cross::CloudRenderingSetting*>(this->_native))->RefreshHistoryTex;
 }
 void CloudRenderingSetting::RefreshHistoryTex::set(bool value )
 {
	(static_cast<cross::CloudRenderingSetting*>(this->_native))->RefreshHistoryTex = value;
 }


//constructor export here
CloudRenderingSetting::CloudRenderingSetting(): CloudRenderingSetting(new cross::CloudRenderingSetting(), true) {}


CloudRenderingSetting::CloudRenderingSetting(const cross::CloudRenderingSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudRenderingSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudRenderingSetting::operator CloudRenderingSetting^ (const cross::CloudRenderingSetting* t)
{
    if(t)
    {
        return gcnew CloudRenderingSetting(const_cast<cross::CloudRenderingSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudCommonSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float CloudCommonSetting::coverage::get()
 {
	return (static_cast<cross::CloudCommonSetting*>(this->_native))->coverage;
 }
 void CloudCommonSetting::coverage::set(float value )
 {
	(static_cast<cross::CloudCommonSetting*>(this->_native))->coverage = value;
 }

float CloudCommonSetting::density::get()
 {
	return (static_cast<cross::CloudCommonSetting*>(this->_native))->density;
 }
 void CloudCommonSetting::density::set(float value )
 {
	(static_cast<cross::CloudCommonSetting*>(this->_native))->density = value;
 }


//constructor export here
CloudCommonSetting::CloudCommonSetting(): CloudCommonSetting(new cross::CloudCommonSetting(), true) {}


CloudCommonSetting::CloudCommonSetting(const cross::CloudCommonSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudCommonSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudCommonSetting::operator CloudCommonSetting^ (const cross::CloudCommonSetting* t)
{
    if(t)
    {
        return gcnew CloudCommonSetting(const_cast<cross::CloudCommonSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudShadingSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float4^ CloudShadingSetting::cloud_color::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_color)) , true);
 }
 void CloudShadingSetting::cloud_color::set(Clicross::Float4^ value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_color = value;
 }

Clicross::Float4^ CloudShadingSetting::cloud_ambient_color::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_ambient_color)) , true);
 }
 void CloudShadingSetting::cloud_ambient_color::set(Clicross::Float4^ value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_ambient_color = value;
 }

float CloudShadingSetting::sun_attenuation::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->sun_attenuation;
 }
 void CloudShadingSetting::sun_attenuation::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->sun_attenuation = value;
 }

float CloudShadingSetting::sun_intensity::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->sun_intensity;
 }
 void CloudShadingSetting::sun_intensity::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->sun_intensity = value;
 }

float CloudShadingSetting::sun_saturation::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->sun_saturation;
 }
 void CloudShadingSetting::sun_saturation::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->sun_saturation = value;
 }

float CloudShadingSetting::multiscattering_intensity::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->multiscattering_intensity;
 }
 void CloudShadingSetting::multiscattering_intensity::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->multiscattering_intensity = value;
 }

float CloudShadingSetting::attenuation_coefficient::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->attenuation_coefficient;
 }
 void CloudShadingSetting::attenuation_coefficient::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->attenuation_coefficient = value;
 }

float CloudShadingSetting::ambient_intensity_top::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_intensity_top;
 }
 void CloudShadingSetting::ambient_intensity_top::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_intensity_top = value;
 }

float CloudShadingSetting::ambient_intensity_bottom::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_intensity_bottom;
 }
 void CloudShadingSetting::ambient_intensity_bottom::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_intensity_bottom = value;
 }

float CloudShadingSetting::ambient_saturation::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_saturation;
 }
 void CloudShadingSetting::ambient_saturation::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->ambient_saturation = value;
 }

float CloudShadingSetting::phase_main_octave_weight::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->phase_main_octave_weight;
 }
 void CloudShadingSetting::phase_main_octave_weight::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->phase_main_octave_weight = value;
 }

float CloudShadingSetting::phase_secondary_octave_weight::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->phase_secondary_octave_weight;
 }
 void CloudShadingSetting::phase_secondary_octave_weight::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->phase_secondary_octave_weight = value;
 }

float CloudShadingSetting::cloud_anisotropy_min::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_anisotropy_min;
 }
 void CloudShadingSetting::cloud_anisotropy_min::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_anisotropy_min = value;
 }

float CloudShadingSetting::cloud_anisotropy_max::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_anisotropy_max;
 }
 void CloudShadingSetting::cloud_anisotropy_max::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->cloud_anisotropy_max = value;
 }

float CloudShadingSetting::dotVL_min::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->dotVL_min;
 }
 void CloudShadingSetting::dotVL_min::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->dotVL_min = value;
 }

float CloudShadingSetting::dotVL_Max::get()
 {
	return (static_cast<cross::CloudShadingSetting*>(this->_native))->dotVL_Max;
 }
 void CloudShadingSetting::dotVL_Max::set(float value )
 {
	(static_cast<cross::CloudShadingSetting*>(this->_native))->dotVL_Max = value;
 }


//constructor export here
CloudShadingSetting::CloudShadingSetting(): CloudShadingSetting(new cross::CloudShadingSetting(), true) {}


CloudShadingSetting::CloudShadingSetting(const cross::CloudShadingSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudShadingSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudShadingSetting::operator CloudShadingSetting^ (const cross::CloudShadingSetting* t)
{
    if(t)
    {
        return gcnew CloudShadingSetting(const_cast<cross::CloudShadingSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudGeometrySetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float CloudGeometrySetting::cloud_bottom_fade::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->cloud_bottom_fade;
 }
 void CloudGeometrySetting::cloud_bottom_fade::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->cloud_bottom_fade = value;
 }

float CloudGeometrySetting::coverage_cloudness::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->coverage_cloudness;
 }
 void CloudGeometrySetting::coverage_cloudness::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->coverage_cloudness = value;
 }

float CloudGeometrySetting::coverage_height_remap::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->coverage_height_remap;
 }
 void CloudGeometrySetting::coverage_height_remap::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->coverage_height_remap = value;
 }

float CloudGeometrySetting::mip_near_ratio::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_near_ratio;
 }
 void CloudGeometrySetting::mip_near_ratio::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_near_ratio = value;
 }

float CloudGeometrySetting::mip_far_ratio::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_far_ratio;
 }
 void CloudGeometrySetting::mip_far_ratio::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_far_ratio = value;
 }

float CloudGeometrySetting::mip_uv_far_scale::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_uv_far_scale;
 }
 void CloudGeometrySetting::mip_uv_far_scale::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_uv_far_scale = value;
 }

float CloudGeometrySetting::mip_contrast::get()
 {
	return (static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_contrast;
 }
 void CloudGeometrySetting::mip_contrast::set(float value )
 {
	(static_cast<cross::CloudGeometrySetting*>(this->_native))->mip_contrast = value;
 }


//constructor export here
CloudGeometrySetting::CloudGeometrySetting(): CloudGeometrySetting(new cross::CloudGeometrySetting(), true) {}


CloudGeometrySetting::CloudGeometrySetting(const cross::CloudGeometrySetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudGeometrySetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudGeometrySetting::operator CloudGeometrySetting^ (const cross::CloudGeometrySetting* t)
{
    if(t)
    {
        return gcnew CloudGeometrySetting(const_cast<cross::CloudGeometrySetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudNoiseSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float CloudNoiseSetting::base_size::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->base_size;
 }
 void CloudNoiseSetting::base_size::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->base_size = value;
 }

float CloudNoiseSetting::noise_threshold::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->noise_threshold;
 }
 void CloudNoiseSetting::noise_threshold::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->noise_threshold = value;
 }

float CloudNoiseSetting::noise_threshold_extent::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->noise_threshold_extent;
 }
 void CloudNoiseSetting::noise_threshold_extent::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->noise_threshold_extent = value;
 }

float CloudNoiseSetting::base_curl_distortion_scale::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->base_curl_distortion_scale;
 }
 void CloudNoiseSetting::base_curl_distortion_scale::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->base_curl_distortion_scale = value;
 }

float CloudNoiseSetting::base_cloud_distortion::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->base_cloud_distortion;
 }
 void CloudNoiseSetting::base_cloud_distortion::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->base_cloud_distortion = value;
 }

float CloudNoiseSetting::extra_noise_size::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_size;
 }
 void CloudNoiseSetting::extra_noise_size::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_size = value;
 }

float CloudNoiseSetting::extra_noise_intensity::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_intensity;
 }
 void CloudNoiseSetting::extra_noise_intensity::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_intensity = value;
 }

float CloudNoiseSetting::extra_noise_threshold::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_threshold;
 }
 void CloudNoiseSetting::extra_noise_threshold::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_threshold = value;
 }

float CloudNoiseSetting::extra_noise_threshold_extent::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_threshold_extent;
 }
 void CloudNoiseSetting::extra_noise_threshold_extent::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->extra_noise_threshold_extent = value;
 }

float CloudNoiseSetting::detail_size::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_size;
 }
 void CloudNoiseSetting::detail_size::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_size = value;
 }

float CloudNoiseSetting::curl_distortion_scale::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->curl_distortion_scale;
 }
 void CloudNoiseSetting::curl_distortion_scale::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->curl_distortion_scale = value;
 }

float CloudNoiseSetting::cloud_distortion::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->cloud_distortion;
 }
 void CloudNoiseSetting::cloud_distortion::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->cloud_distortion = value;
 }

float CloudNoiseSetting::detail_wispy_billowy_gradient::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_wispy_billowy_gradient;
 }
 void CloudNoiseSetting::detail_wispy_billowy_gradient::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_wispy_billowy_gradient = value;
 }

float CloudNoiseSetting::detail_affect::get()
 {
	return (static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_affect;
 }
 void CloudNoiseSetting::detail_affect::set(float value )
 {
	(static_cast<cross::CloudNoiseSetting*>(this->_native))->detail_affect = value;
 }


//constructor export here
CloudNoiseSetting::CloudNoiseSetting(): CloudNoiseSetting(new cross::CloudNoiseSetting(), true) {}


CloudNoiseSetting::CloudNoiseSetting(const cross::CloudNoiseSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudNoiseSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudNoiseSetting::operator CloudNoiseSetting^ (const cross::CloudNoiseSetting* t)
{
    if(t)
    {
        return gcnew CloudNoiseSetting(const_cast<cross::CloudNoiseSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudLayerPresetSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudType CloudLayerPresetSetting::CloudType::get()
 {
	return (Clicross::CloudType)((int)(static_cast<cross::CloudLayerPresetSetting*>(this->_native))->CloudType);
 }
 void CloudLayerPresetSetting::CloudType::set(Clicross::CloudType value )
 {
	(static_cast<cross::CloudLayerPresetSetting*>(this->_native))->CloudType = static_cast<cross::CloudType>(value);
 }

System::String^ CloudLayerPresetSetting::SpreadTex::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::CloudLayerPresetSetting*>(this->_native))->SpreadTex)).c_str());
 }
 void CloudLayerPresetSetting::SpreadTex::set(System::String^ value )
 {
	((static_cast<cross::CloudLayerPresetSetting*>(this->_native))->SpreadTex) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
CloudLayerPresetSetting::CloudLayerPresetSetting(): CloudLayerPresetSetting(new cross::CloudLayerPresetSetting(), true) {}


CloudLayerPresetSetting::CloudLayerPresetSetting(const cross::CloudLayerPresetSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudLayerPresetSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudLayerPresetSetting::operator CloudLayerPresetSetting^ (const cross::CloudLayerPresetSetting* t)
{
    if(t)
    {
        return gcnew CloudLayerPresetSetting(const_cast<cross::CloudLayerPresetSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudLayerRuntimeSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool CloudLayerRuntimeSetting::Enable::get()
 {
	return (static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->Enable;
 }
 void CloudLayerRuntimeSetting::Enable::set(bool value )
 {
	(static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->Enable = value;
 }

float CloudLayerRuntimeSetting::BottomHeight::get()
 {
	return (static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->BottomHeight;
 }
 void CloudLayerRuntimeSetting::BottomHeight::set(float value )
 {
	(static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->BottomHeight = value;
 }

float CloudLayerRuntimeSetting::Height::get()
 {
	return (static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->Height;
 }
 void CloudLayerRuntimeSetting::Height::set(float value )
 {
	(static_cast<cross::CloudLayerRuntimeSetting*>(this->_native))->Height = value;
 }


//constructor export here
CloudLayerRuntimeSetting::CloudLayerRuntimeSetting(): CloudLayerRuntimeSetting(new cross::CloudLayerRuntimeSetting(), true) {}


CloudLayerRuntimeSetting::CloudLayerRuntimeSetting(const cross::CloudLayerRuntimeSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudLayerRuntimeSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudLayerRuntimeSetting::operator CloudLayerRuntimeSetting^ (const cross::CloudLayerRuntimeSetting* t)
{
    if(t)
    {
        return gcnew CloudLayerRuntimeSetting(const_cast<cross::CloudLayerRuntimeSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudLayerSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudLayerPresetSetting^ CloudLayerSetting::CloudPresetData::get()
 {
	return gcnew Clicross::CloudLayerPresetSetting(new cross::CloudLayerPresetSetting(((static_cast<cross::CloudLayerSetting*>(this->_native))->CloudPresetData)) , true);
 }
 void CloudLayerSetting::CloudPresetData::set(Clicross::CloudLayerPresetSetting^ value )
 {
	(static_cast<cross::CloudLayerSetting*>(this->_native))->CloudPresetData = value;
 }

Clicross::CloudLayerRuntimeSetting^ CloudLayerSetting::CloudRuntimeData::get()
 {
	return gcnew Clicross::CloudLayerRuntimeSetting(new cross::CloudLayerRuntimeSetting(((static_cast<cross::CloudLayerSetting*>(this->_native))->CloudRuntimeData)) , true);
 }
 void CloudLayerSetting::CloudRuntimeData::set(Clicross::CloudLayerRuntimeSetting^ value )
 {
	(static_cast<cross::CloudLayerSetting*>(this->_native))->CloudRuntimeData = value;
 }


//constructor export here
CloudLayerSetting::CloudLayerSetting(): CloudLayerSetting(new cross::CloudLayerSetting(), true) {}


CloudLayerSetting::CloudLayerSetting(const cross::CloudLayerSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudLayerSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudLayerSetting::operator CloudLayerSetting^ (const cross::CloudLayerSetting* t)
{
    if(t)
    {
        return gcnew CloudLayerSetting(const_cast<cross::CloudLayerSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudRangeSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudProjectType CloudRangeSetting::ProjectType::get()
 {
	return (Clicross::CloudProjectType)((int)(static_cast<cross::CloudRangeSetting*>(this->_native))->ProjectType);
 }
 void CloudRangeSetting::ProjectType::set(Clicross::CloudProjectType value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->ProjectType = static_cast<cross::CloudProjectType>(value);
 }

float CloudRangeSetting::Size::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->Size;
 }
 void CloudRangeSetting::Size::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->Size = value;
 }

float CloudRangeSetting::Height::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->Height;
 }
 void CloudRangeSetting::Height::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->Height = value;
 }

float CloudRangeSetting::BottomHeight::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->BottomHeight;
 }
 void CloudRangeSetting::BottomHeight::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->BottomHeight = value;
 }

float CloudRangeSetting::BottomBleed::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->BottomBleed;
 }
 void CloudRangeSetting::BottomBleed::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->BottomBleed = value;
 }

float CloudRangeSetting::TopBleed::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->TopBleed;
 }
 void CloudRangeSetting::TopBleed::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->TopBleed = value;
 }

float CloudRangeSetting::GroundHeight::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->GroundHeight;
 }
 void CloudRangeSetting::GroundHeight::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->GroundHeight = value;
 }

float CloudRangeSetting::CutOffDistance::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->CutOffDistance;
 }
 void CloudRangeSetting::CutOffDistance::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->CutOffDistance = value;
 }

float CloudRangeSetting::SphereRadius::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->SphereRadius;
 }
 void CloudRangeSetting::SphereRadius::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->SphereRadius = value;
 }

Clicross::Float3^ CloudRangeSetting::WindDirection::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::CloudRangeSetting*>(this->_native))->WindDirection)) , true);
 }
 void CloudRangeSetting::WindDirection::set(Clicross::Float3^ value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->WindDirection = value;
 }

float CloudRangeSetting::CloudSpeed::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->CloudSpeed;
 }
 void CloudRangeSetting::CloudSpeed::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->CloudSpeed = value;
 }

float CloudRangeSetting::CloudNoiseFlow::get()
 {
	return (static_cast<cross::CloudRangeSetting*>(this->_native))->CloudNoiseFlow;
 }
 void CloudRangeSetting::CloudNoiseFlow::set(float value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->CloudNoiseFlow = value;
 }

Clicross::CloudLayerSetting^ CloudRangeSetting::LayerBottom::get()
 {
	return gcnew Clicross::CloudLayerSetting(new cross::CloudLayerSetting(((static_cast<cross::CloudRangeSetting*>(this->_native))->LayerBottom)) , true);
 }
 void CloudRangeSetting::LayerBottom::set(Clicross::CloudLayerSetting^ value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->LayerBottom = value;
 }

Clicross::CloudLayerSetting^ CloudRangeSetting::LayerMiddle::get()
 {
	return gcnew Clicross::CloudLayerSetting(new cross::CloudLayerSetting(((static_cast<cross::CloudRangeSetting*>(this->_native))->LayerMiddle)) , true);
 }
 void CloudRangeSetting::LayerMiddle::set(Clicross::CloudLayerSetting^ value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->LayerMiddle = value;
 }

Clicross::CloudLayerSetting^ CloudRangeSetting::LayerTop::get()
 {
	return gcnew Clicross::CloudLayerSetting(new cross::CloudLayerSetting(((static_cast<cross::CloudRangeSetting*>(this->_native))->LayerTop)) , true);
 }
 void CloudRangeSetting::LayerTop::set(Clicross::CloudLayerSetting^ value )
 {
	(static_cast<cross::CloudRangeSetting*>(this->_native))->LayerTop = value;
 }


//constructor export here
CloudRangeSetting::CloudRangeSetting(): CloudRangeSetting(new cross::CloudRangeSetting(), true) {}


CloudRangeSetting::CloudRangeSetting(const cross::CloudRangeSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudRangeSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudRangeSetting::operator CloudRangeSetting^ (const cross::CloudRangeSetting* t)
{
    if(t)
    {
        return gcnew CloudRangeSetting(const_cast<cross::CloudRangeSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudShadowSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool CloudShadowSetting::CloudCastShadow::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudCastShadow;
 }
 void CloudShadowSetting::CloudCastShadow::set(bool value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudCastShadow = value;
 }

float CloudShadowSetting::CloudShadowBias::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowBias;
 }
 void CloudShadowSetting::CloudShadowBias::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowBias = value;
 }

float CloudShadowSetting::CloudShadowIntensity::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowIntensity;
 }
 void CloudShadowSetting::CloudShadowIntensity::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowIntensity = value;
 }

float CloudShadowSetting::CloudShadowContrast::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowContrast;
 }
 void CloudShadowSetting::CloudShadowContrast::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowContrast = value;
 }

float CloudShadowSetting::CloudShadowMultiplier::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowMultiplier;
 }
 void CloudShadowSetting::CloudShadowMultiplier::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowMultiplier = value;
 }

float CloudShadowSetting::CloudShadowSubtraction::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowSubtraction;
 }
 void CloudShadowSetting::CloudShadowSubtraction::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowSubtraction = value;
 }

float CloudShadowSetting::CloudShadowForAPScale::get()
 {
	return (static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowForAPScale;
 }
 void CloudShadowSetting::CloudShadowForAPScale::set(float value )
 {
	(static_cast<cross::CloudShadowSetting*>(this->_native))->CloudShadowForAPScale = value;
 }


//constructor export here
CloudShadowSetting::CloudShadowSetting(): CloudShadowSetting(new cross::CloudShadowSetting(), true) {}


CloudShadowSetting::CloudShadowSetting(const cross::CloudShadowSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudShadowSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudShadowSetting::operator CloudShadowSetting^ (const cross::CloudShadowSetting* t)
{
    if(t)
    {
        return gcnew CloudShadowSetting(const_cast<cross::CloudShadowSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RainShaftPreset export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool RainShaftPreset::enable::get()
 {
	return (static_cast<cross::RainShaftPreset*>(this->_native))->enable;
 }
 void RainShaftPreset::enable::set(bool value )
 {
	(static_cast<cross::RainShaftPreset*>(this->_native))->enable = value;
 }

Clicross::Float3^ RainShaftPreset::rltvPos::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::RainShaftPreset*>(this->_native))->rltvPos)) , true);
 }
 void RainShaftPreset::rltvPos::set(Clicross::Float3^ value )
 {
	(static_cast<cross::RainShaftPreset*>(this->_native))->rltvPos = value;
 }

Clicross::Float4^ RainShaftPreset::shapeParams::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::RainShaftPreset*>(this->_native))->shapeParams)) , true);
 }
 void RainShaftPreset::shapeParams::set(Clicross::Float4^ value )
 {
	(static_cast<cross::RainShaftPreset*>(this->_native))->shapeParams = value;
 }


//constructor export here
RainShaftPreset::RainShaftPreset(): RainShaftPreset(new cross::RainShaftPreset(), true) {}


RainShaftPreset::RainShaftPreset(const cross::RainShaftPreset * obj, bool created_by_clr): 
    _native(const_cast<cross::RainShaftPreset *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RainShaftPreset::operator RainShaftPreset^ (const cross::RainShaftPreset* t)
{
    if(t)
    {
        return gcnew RainShaftPreset(const_cast<cross::RainShaftPreset*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ThunderstormTypePreset export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ ThunderstormTypePreset::SDF::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ThunderstormTypePreset*>(this->_native))->SDF)).c_str());
 }
 void ThunderstormTypePreset::SDF::set(System::String^ value )
 {
	((static_cast<cross::ThunderstormTypePreset*>(this->_native))->SDF) = (ClangenCli::ToNativeString(value));
 }

Clicross::Float3^ ThunderstormTypePreset::Extent::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::ThunderstormTypePreset*>(this->_native))->Extent)) , true);
 }
 void ThunderstormTypePreset::Extent::set(Clicross::Float3^ value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->Extent = value;
 }

float ThunderstormTypePreset::ExtentScale::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->ExtentScale;
 }
 void ThunderstormTypePreset::ExtentScale::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->ExtentScale = value;
 }

float ThunderstormTypePreset::Threshold::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->Threshold;
 }
 void ThunderstormTypePreset::Threshold::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->Threshold = value;
 }

float ThunderstormTypePreset::Density::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->Density;
 }
 void ThunderstormTypePreset::Density::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->Density = value;
 }

float ThunderstormTypePreset::HeightOffset::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->HeightOffset;
 }
 void ThunderstormTypePreset::HeightOffset::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->HeightOffset = value;
 }

float ThunderstormTypePreset::ShadowOffset::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->ShadowOffset;
 }
 void ThunderstormTypePreset::ShadowOffset::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->ShadowOffset = value;
 }

float ThunderstormTypePreset::RainOffset::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainOffset;
 }
 void ThunderstormTypePreset::RainOffset::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainOffset = value;
 }

float ThunderstormTypePreset::Absorb::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->Absorb;
 }
 void ThunderstormTypePreset::Absorb::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->Absorb = value;
 }

float ThunderstormTypePreset::DetailScale::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailScale;
 }
 void ThunderstormTypePreset::DetailScale::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailScale = value;
 }

float ThunderstormTypePreset::DetailErosion::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailErosion;
 }
 void ThunderstormTypePreset::DetailErosion::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailErosion = value;
 }

float ThunderstormTypePreset::DetailIntensity::get()
 {
	return (static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailIntensity;
 }
 void ThunderstormTypePreset::DetailIntensity::set(float value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->DetailIntensity = value;
 }

Clicross::RainShaftPreset^ ThunderstormTypePreset::RainShaftPreset1::get()
 {
	return gcnew Clicross::RainShaftPreset(new cross::RainShaftPreset(((static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset1)) , true);
 }
 void ThunderstormTypePreset::RainShaftPreset1::set(Clicross::RainShaftPreset^ value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset1 = value;
 }

Clicross::RainShaftPreset^ ThunderstormTypePreset::RainShaftPreset2::get()
 {
	return gcnew Clicross::RainShaftPreset(new cross::RainShaftPreset(((static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset2)) , true);
 }
 void ThunderstormTypePreset::RainShaftPreset2::set(Clicross::RainShaftPreset^ value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset2 = value;
 }

Clicross::RainShaftPreset^ ThunderstormTypePreset::RainShaftPreset3::get()
 {
	return gcnew Clicross::RainShaftPreset(new cross::RainShaftPreset(((static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset3)) , true);
 }
 void ThunderstormTypePreset::RainShaftPreset3::set(Clicross::RainShaftPreset^ value )
 {
	(static_cast<cross::ThunderstormTypePreset*>(this->_native))->RainShaftPreset3 = value;
 }


//constructor export here
ThunderstormTypePreset::ThunderstormTypePreset(): ThunderstormTypePreset(new cross::ThunderstormTypePreset(), true) {}


ThunderstormTypePreset::ThunderstormTypePreset(const cross::ThunderstormTypePreset * obj, bool created_by_clr): 
    _native(const_cast<cross::ThunderstormTypePreset *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ThunderstormTypePreset::operator ThunderstormTypePreset^ (const cross::ThunderstormTypePreset* t)
{
    if(t)
    {
        return gcnew ThunderstormTypePreset(const_cast<cross::ThunderstormTypePreset*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudThunderstormInfo export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ThunderstormTypePreset^ CloudThunderstormInfo::Params::get()
 {
	return gcnew Clicross::ThunderstormTypePreset(new cross::ThunderstormTypePreset(((static_cast<cross::CloudThunderstormInfo*>(this->_native))->Params)) , true);
 }
 void CloudThunderstormInfo::Params::set(Clicross::ThunderstormTypePreset^ value )
 {
	(static_cast<cross::CloudThunderstormInfo*>(this->_native))->Params = value;
 }


//constructor export here
CloudThunderstormInfo::CloudThunderstormInfo(): CloudThunderstormInfo(new cross::CloudThunderstormInfo(), true) {}


CloudThunderstormInfo::CloudThunderstormInfo(const cross::CloudThunderstormInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudThunderstormInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudThunderstormInfo::operator CloudThunderstormInfo^ (const cross::CloudThunderstormInfo* t)
{
    if(t)
    {
        return gcnew CloudThunderstormInfo(const_cast<cross::CloudThunderstormInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudThunderstormSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool CloudThunderstormSetting::enable::get()
 {
	return (static_cast<cross::CloudThunderstormSetting*>(this->_native))->enable;
 }
 void CloudThunderstormSetting::enable::set(bool value )
 {
	(static_cast<cross::CloudThunderstormSetting*>(this->_native))->enable = value;
 }


//constructor export here
CloudThunderstormSetting::CloudThunderstormSetting( )
    :CloudThunderstormSetting(new cross::CloudThunderstormSetting(), true)
{
}

CloudThunderstormSetting::CloudThunderstormSetting(Clicross::CloudThunderstormSetting^ rhs )
    :CloudThunderstormSetting(new cross::CloudThunderstormSetting((const cross::CloudThunderstormSetting& )(rhs)), true)
{
}

CloudThunderstormSetting::CloudThunderstormSetting(Clicross::CloudThunderstormInfo^ rhs, bool active )
    :CloudThunderstormSetting(new cross::CloudThunderstormSetting((const cross::CloudThunderstormInfo& )(rhs), active), true)
{
}



CloudThunderstormSetting::CloudThunderstormSetting(const cross::CloudThunderstormSetting * obj, bool created_by_clr): Clicross::CloudThunderstormInfo(obj, created_by_clr)
{
}

CloudThunderstormSetting::operator CloudThunderstormSetting^ (const cross::CloudThunderstormSetting* t)
{
    if(t)
    {
        return gcnew CloudThunderstormSetting(const_cast<cross::CloudThunderstormSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CloudSetting export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::CloudRangeSetting^ CloudSetting::CloudRange::get()
 {
	return gcnew Clicross::CloudRangeSetting(new cross::CloudRangeSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudRange)) , true);
 }
 void CloudSetting::CloudRange::set(Clicross::CloudRangeSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudRange = value;
 }

System::String^ CloudSetting::SkyCloudMtl::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::CloudSetting*>(this->_native))->SkyCloudMtl)).c_str());
 }
 void CloudSetting::SkyCloudMtl::set(System::String^ value )
 {
	((static_cast<cross::CloudSetting*>(this->_native))->SkyCloudMtl) = (ClangenCli::ToNativeString(value));
 }

Clicross::CloudCommonSetting^ CloudSetting::CloudCommon::get()
 {
	return gcnew Clicross::CloudCommonSetting(new cross::CloudCommonSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudCommon)) , true);
 }
 void CloudSetting::CloudCommon::set(Clicross::CloudCommonSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudCommon = value;
 }

Clicross::CloudGeometrySetting^ CloudSetting::CloudGeometry::get()
 {
	return gcnew Clicross::CloudGeometrySetting(new cross::CloudGeometrySetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudGeometry)) , true);
 }
 void CloudSetting::CloudGeometry::set(Clicross::CloudGeometrySetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudGeometry = value;
 }

Clicross::CloudShadingSetting^ CloudSetting::CloudShading::get()
 {
	return gcnew Clicross::CloudShadingSetting(new cross::CloudShadingSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudShading)) , true);
 }
 void CloudSetting::CloudShading::set(Clicross::CloudShadingSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudShading = value;
 }

Clicross::CloudNoiseSetting^ CloudSetting::CloudNoise::get()
 {
	return gcnew Clicross::CloudNoiseSetting(new cross::CloudNoiseSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudNoise)) , true);
 }
 void CloudSetting::CloudNoise::set(Clicross::CloudNoiseSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudNoise = value;
 }

Clicross::CloudShadowSetting^ CloudSetting::CloudShadow::get()
 {
	return gcnew Clicross::CloudShadowSetting(new cross::CloudShadowSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudShadow)) , true);
 }
 void CloudSetting::CloudShadow::set(Clicross::CloudShadowSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudShadow = value;
 }

Clicross::CloudRenderingSetting^ CloudSetting::CloudRendering::get()
 {
	return gcnew Clicross::CloudRenderingSetting(new cross::CloudRenderingSetting(((static_cast<cross::CloudSetting*>(this->_native))->CloudRendering)) , true);
 }
 void CloudSetting::CloudRendering::set(Clicross::CloudRenderingSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->CloudRendering = value;
 }

Clicross::CloudThunderstormSetting^ CloudSetting::ThunderDebug::get()
 {
	return gcnew Clicross::CloudThunderstormSetting(new cross::CloudThunderstormSetting(((static_cast<cross::CloudSetting*>(this->_native))->ThunderDebug)) , true);
 }
 void CloudSetting::ThunderDebug::set(Clicross::CloudThunderstormSetting^ value )
 {
	(static_cast<cross::CloudSetting*>(this->_native))->ThunderDebug = value;
 }


//constructor export here
CloudSetting::CloudSetting(): CloudSetting(new cross::CloudSetting(), true) {}


CloudSetting::CloudSetting(const cross::CloudSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::CloudSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CloudSetting::operator CloudSetting^ (const cross::CloudSetting* t)
{
    if(t)
    {
        return gcnew CloudSetting(const_cast<cross::CloudSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// SkyAtmosphereOuterParam export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool SkyAtmosphereOuterParam::PlanetTopAtWorldOrigin::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->PlanetTopAtWorldOrigin;
 }
 void SkyAtmosphereOuterParam::PlanetTopAtWorldOrigin::set(bool value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->PlanetTopAtWorldOrigin = value;
 }

bool SkyAtmosphereOuterParam::RenderSunDisk::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->RenderSunDisk;
 }
 void SkyAtmosphereOuterParam::RenderSunDisk::set(bool value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->RenderSunDisk = value;
 }

bool SkyAtmosphereOuterParam::RenderMoonDisk::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->RenderMoonDisk;
 }
 void SkyAtmosphereOuterParam::RenderMoonDisk::set(bool value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->RenderMoonDisk = value;
 }

bool SkyAtmosphereOuterParam::MoonPhase::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->MoonPhase;
 }
 void SkyAtmosphereOuterParam::MoonPhase::set(bool value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->MoonPhase = value;
 }

float SkyAtmosphereOuterParam::AerialPerspStartDepthKM::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->AerialPerspStartDepthKM;
 }
 void SkyAtmosphereOuterParam::AerialPerspStartDepthKM::set(float value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->AerialPerspStartDepthKM = value;
 }

float SkyAtmosphereOuterParam::Exposure::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->Exposure;
 }
 void SkyAtmosphereOuterParam::Exposure::set(float value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->Exposure = value;
 }

float SkyAtmosphereOuterParam::APScale::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->APScale;
 }
 void SkyAtmosphereOuterParam::APScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->APScale = value;
 }

bool SkyAtmosphereOuterParam::HighQualityAP::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->HighQualityAP;
 }
 void SkyAtmosphereOuterParam::HighQualityAP::set(bool value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->HighQualityAP = value;
 }

float SkyAtmosphereOuterParam::ColorTransmittance::get()
 {
	return (static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->ColorTransmittance;
 }
 void SkyAtmosphereOuterParam::ColorTransmittance::set(float value )
 {
	(static_cast<cross::SkyAtmosphereOuterParam*>(this->_native))->ColorTransmittance = value;
 }


//constructor export here
SkyAtmosphereOuterParam::SkyAtmosphereOuterParam(): SkyAtmosphereOuterParam(new cross::SkyAtmosphereOuterParam(), true) {}


SkyAtmosphereOuterParam::SkyAtmosphereOuterParam(const cross::SkyAtmosphereOuterParam * obj, bool created_by_clr): 
    _native(const_cast<cross::SkyAtmosphereOuterParam *>(obj))
	, _created_by_clr(created_by_clr)
{
}

SkyAtmosphereOuterParam::operator SkyAtmosphereOuterParam^ (const cross::SkyAtmosphereOuterParam* t)
{
    if(t)
    {
        return gcnew SkyAtmosphereOuterParam(const_cast<cross::SkyAtmosphereOuterParam*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpression export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpression::m_Id::get()
 {
	return (static_cast<cross::MaterialExpression*>(this->_native))->m_Id;
 }
 void MaterialExpression::m_Id::set(int value )
 {
	(static_cast<cross::MaterialExpression*>(this->_native))->m_Id = value;
 }

int MaterialExpression::m_EditorPositionX::get()
 {
	return (static_cast<cross::MaterialExpression*>(this->_native))->m_EditorPositionX;
 }
 void MaterialExpression::m_EditorPositionX::set(int value )
 {
	(static_cast<cross::MaterialExpression*>(this->_native))->m_EditorPositionX = value;
 }

int MaterialExpression::m_EditorPositionY::get()
 {
	return (static_cast<cross::MaterialExpression*>(this->_native))->m_EditorPositionY;
 }
 void MaterialExpression::m_EditorPositionY::set(int value )
 {
	(static_cast<cross::MaterialExpression*>(this->_native))->m_EditorPositionY = value;
 }

System::String^ MaterialExpression::m_Description::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpression*>(this->_native))->m_Description)).c_str());
 }
 void MaterialExpression::m_Description::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpression*>(this->_native))->m_Description) = (ClangenCli::ToNativeString(value));
 }


//constructor export here


MaterialExpression::MaterialExpression(const cross::MaterialExpression * obj, bool created_by_clr): Cligbf::reflection::RttiBase(obj, created_by_clr)
{
}

MaterialExpression::operator MaterialExpression^ (const cross::MaterialExpression* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpression(const_cast<cross::MaterialExpression*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpression^)managedObj;
    }
    else
        return nullptr;
}

Clicross::ExpressionInput^ MaterialExpression::GetInputPin(int index )
{
    return (Clicross::ExpressionInput^)((static_cast<cross::MaterialExpression*>(this->_native))->GetInputPin( index));
}

Clicross::ExpressionOutput^ MaterialExpression::GetOutputPin(int index )
{
    return (Clicross::ExpressionOutput^)((static_cast<cross::MaterialExpression*>(this->_native))->GetOutputPin( index));
}

int MaterialExpression::GetInputPinCount( )
{
    return (static_cast<cross::MaterialExpression*>(this->_native))->GetInputPinCount( );
}

int MaterialExpression::GetOutputPinCount( )
{
    return (static_cast<cross::MaterialExpression*>(this->_native))->GetOutputPinCount( );
}


}   //end namespace Clicross

// ExpressionPin export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ ExpressionPin::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionPin*>(this->_native))->m_Name)).c_str());
 }
 void ExpressionPin::m_Name::set(System::String^ value )
 {
	((static_cast<cross::ExpressionPin*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

System::String^ ExpressionPin::m_BindedPropertyName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::ExpressionPin*>(this->_native))->m_BindedPropertyName)).c_str());
 }
 void ExpressionPin::m_BindedPropertyName::set(System::String^ value )
 {
	((static_cast<cross::ExpressionPin*>(this->_native))->m_BindedPropertyName) = (ClangenCli::ToNativeString(value));
 }


//constructor export here


ExpressionPin::ExpressionPin(const cross::ExpressionPin * obj, bool created_by_clr): 
    _native(const_cast<cross::ExpressionPin *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ExpressionPin::operator ExpressionPin^ (const cross::ExpressionPin* t)
{
    if(t)
    {
        return gcnew ExpressionPin(const_cast<cross::ExpressionPin*>(t));
    }
    else
        return nullptr;
}

System::String^ ExpressionPin::GetBindedPropertyValue( )
{
    return ClangenCli::ToManagedString((((static_cast<cross::ExpressionPin*>(this->_native))->GetBindedPropertyValue( ))).c_str());
}


}   //end namespace Clicross

// ExpressionOutput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ExpressionOutput::ExpressionOutput(const cross::ExpressionOutput * obj, bool created_by_clr): Clicross::ExpressionPin(obj, created_by_clr)
{
}

ExpressionOutput::operator ExpressionOutput^ (const cross::ExpressionOutput* t)
{
    if(t)
    {
        return gcnew ExpressionOutput(const_cast<cross::ExpressionOutput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ExpressionInput::ExpressionInput(const cross::ExpressionInput * obj, bool created_by_clr): Clicross::ExpressionPin(obj, created_by_clr)
{
}

ExpressionInput::operator ExpressionInput^ (const cross::ExpressionInput* t)
{
    if(t)
    {
        return gcnew ExpressionInput(const_cast<cross::ExpressionInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionAttributesInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ExpressionAttributesInput::ExpressionAttributesInput(const cross::ExpressionAttributesInput * obj, bool created_by_clr): Clicross::ExpressionInput(obj, created_by_clr)
{
}

ExpressionAttributesInput::operator ExpressionAttributesInput^ (const cross::ExpressionAttributesInput* t)
{
    if(t)
    {
        return gcnew ExpressionAttributesInput(const_cast<cross::ExpressionAttributesInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// IMaterialEditor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


IMaterialEditor::IMaterialEditor(const cross::IMaterialEditor * obj, bool created_by_clr): 
    _native(const_cast<cross::IMaterialEditor *>(obj))
	, _created_by_clr(created_by_clr)
{
}

IMaterialEditor::operator IMaterialEditor^ (const cross::IMaterialEditor* t)
{
    if(t)
    {
        return gcnew IMaterialEditor(const_cast<cross::IMaterialEditor*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionVertexShader export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionVertexShader::m_CustomInterpolators export start
	#define STLDECL_MANAGEDTYPE Clicross::CustomInterpolatorInput^
	#define STLDECL_NATIVETYPE cross::CustomInterpolatorInput
	CPP_DECLARE_STLVECTOR(MaterialExpressionVertexShader::, m_CustomInterpolatorsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
MaterialExpressionVertexShader::m_CustomInterpolatorsCliType^ MaterialExpressionVertexShader::m_CustomInterpolators::get()
 {
	return (static_cast<cross::MaterialExpressionVertexShader*>(this->_native))->m_CustomInterpolators;
 }
 void MaterialExpressionVertexShader::m_CustomInterpolators::set(MaterialExpressionVertexShader::m_CustomInterpolatorsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionVertexShader*>(this->_native))->m_CustomInterpolators = *value->_native;
 }

Clicross::ExpressionAttributesInput^ MaterialExpressionVertexShader::m_WorldPositionOffset::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionVertexShader*>(this->_native))->m_WorldPositionOffset)) , true);
 }
 void MaterialExpressionVertexShader::m_WorldPositionOffset::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexShader*>(this->_native))->m_WorldPositionOffset = value;
 }


//constructor export here


MaterialExpressionVertexShader::MaterialExpressionVertexShader(const cross::MaterialExpressionVertexShader * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionVertexShader::operator MaterialExpressionVertexShader^ (const cross::MaterialExpressionVertexShader* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionVertexShader(const_cast<cross::MaterialExpressionVertexShader*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionVertexShader^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


