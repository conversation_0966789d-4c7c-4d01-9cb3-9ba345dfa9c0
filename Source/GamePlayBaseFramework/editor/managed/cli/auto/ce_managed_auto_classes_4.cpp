//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// CustomInterpolatorInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ CustomInterpolatorInput::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::CustomInterpolatorInput*>(this->_native))->Name)).c_str());
 }
 void CustomInterpolatorInput::Name::set(System::String^ value )
 {
	((static_cast<cross::CustomInterpolatorInput*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::MaterialCustomInterpolatorType CustomInterpolatorInput::Type::get()
 {
	return (Clicross::MaterialCustomInterpolatorType)((int)(static_cast<cross::CustomInterpolatorInput*>(this->_native))->Type);
 }
 void CustomInterpolatorInput::Type::set(Clicross::MaterialCustomInterpolatorType value )
 {
	(static_cast<cross::CustomInterpolatorInput*>(this->_native))->Type = static_cast<cross::MaterialCustomInterpolatorType>(value);
 }

Clicross::ExpressionInput^ CustomInterpolatorInput::Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::CustomInterpolatorInput*>(this->_native))->Input)) , true);
 }
 void CustomInterpolatorInput::Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::CustomInterpolatorInput*>(this->_native))->Input = value;
 }


//constructor export here
CustomInterpolatorInput::CustomInterpolatorInput(): CustomInterpolatorInput(new cross::CustomInterpolatorInput(), true) {}


CustomInterpolatorInput::CustomInterpolatorInput(const cross::CustomInterpolatorInput * obj, bool created_by_clr): 
    _native(const_cast<cross::CustomInterpolatorInput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CustomInterpolatorInput::operator CustomInterpolatorInput^ (const cross::CustomInterpolatorInput* t)
{
    if(t)
    {
        return gcnew CustomInterpolatorInput(const_cast<cross::CustomInterpolatorInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionParameter::m_ParameterName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_ParameterName)).c_str());
 }
 void MaterialExpressionParameter::m_ParameterName::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_ParameterName) = (ClangenCli::ToNativeString(value));
 }

bool MaterialExpressionParameter::m_IsVisibleInMaterialInstanceEditor::get()
 {
	return (static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_IsVisibleInMaterialInstanceEditor;
 }
 void MaterialExpressionParameter::m_IsVisibleInMaterialInstanceEditor::set(bool value )
 {
	(static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_IsVisibleInMaterialInstanceEditor = value;
 }

System::String^ MaterialExpressionParameter::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_Name)).c_str());
 }
 void MaterialExpressionParameter::m_Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

System::String^ MaterialExpressionParameter::m_Group::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_Group)).c_str());
 }
 void MaterialExpressionParameter::m_Group::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_Group) = (ClangenCli::ToNativeString(value));
 }

float MaterialExpressionParameter::m_SortPriority::get()
 {
	return (static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_SortPriority;
 }
 void MaterialExpressionParameter::m_SortPriority::set(float value )
 {
	(static_cast<cross::MaterialExpressionParameter*>(this->_native))->m_SortPriority = value;
 }


//constructor export here


MaterialExpressionParameter::MaterialExpressionParameter(const cross::MaterialExpressionParameter * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionParameter::operator MaterialExpressionParameter^ (const cross::MaterialExpressionParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParameter(const_cast<cross::MaterialExpressionParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSurfaceShader export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_BaseColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_BaseColor)) , true);
 }
 void MaterialExpressionSurfaceShader::m_BaseColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_BaseColor = value;
 }

Clicross::Float4^ MaterialExpressionSurfaceShader::m_BaseColorConstant::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_BaseColorConstant)) , true);
 }
 void MaterialExpressionSurfaceShader::m_BaseColorConstant::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_BaseColorConstant = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_Metallic::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Metallic)) , true);
 }
 void MaterialExpressionSurfaceShader::m_Metallic::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Metallic = value;
 }

float MaterialExpressionSurfaceShader::m_MetallicConstant::get()
 {
	return (static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_MetallicConstant;
 }
 void MaterialExpressionSurfaceShader::m_MetallicConstant::set(float value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_MetallicConstant = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_Specular::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Specular)) , true);
 }
 void MaterialExpressionSurfaceShader::m_Specular::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Specular = value;
 }

float MaterialExpressionSurfaceShader::m_SpecularConstant::get()
 {
	return (static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_SpecularConstant;
 }
 void MaterialExpressionSurfaceShader::m_SpecularConstant::set(float value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_SpecularConstant = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_Roughness::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Roughness)) , true);
 }
 void MaterialExpressionSurfaceShader::m_Roughness::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Roughness = value;
 }

float MaterialExpressionSurfaceShader::m_RoughnessConstant::get()
 {
	return (static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_RoughnessConstant;
 }
 void MaterialExpressionSurfaceShader::m_RoughnessConstant::set(float value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_RoughnessConstant = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_Opacity::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Opacity)) , true);
 }
 void MaterialExpressionSurfaceShader::m_Opacity::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Opacity = value;
 }

float MaterialExpressionSurfaceShader::m_OpacityConstant::get()
 {
	return (static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_OpacityConstant;
 }
 void MaterialExpressionSurfaceShader::m_OpacityConstant::set(float value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_OpacityConstant = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_OpacityMask::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_OpacityMask)) , true);
 }
 void MaterialExpressionSurfaceShader::m_OpacityMask::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_OpacityMask = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_Normal::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Normal)) , true);
 }
 void MaterialExpressionSurfaceShader::m_Normal::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_Normal = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_AmbientOcclusion::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_AmbientOcclusion)) , true);
 }
 void MaterialExpressionSurfaceShader::m_AmbientOcclusion::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_AmbientOcclusion = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_EmissiveColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_EmissiveColor)) , true);
 }
 void MaterialExpressionSurfaceShader::m_EmissiveColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_EmissiveColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_SubsurfaceColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_SubsurfaceColor)) , true);
 }
 void MaterialExpressionSurfaceShader::m_SubsurfaceColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_SubsurfaceColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_DebugColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_DebugColor)) , true);
 }
 void MaterialExpressionSurfaceShader::m_DebugColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_DebugColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSurfaceShader::m_TemporalReactive::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_TemporalReactive)) , true);
 }
 void MaterialExpressionSurfaceShader::m_TemporalReactive::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_TemporalReactive = value;
 }

Clicross::ExpressionAttributesInput^ MaterialExpressionSurfaceShader::m_MaterialAttributes::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_MaterialAttributes)) , true);
 }
 void MaterialExpressionSurfaceShader::m_MaterialAttributes::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionSurfaceShader*>(this->_native))->m_MaterialAttributes = value;
 }


//constructor export here
MaterialExpressionSurfaceShader::MaterialExpressionSurfaceShader( )
    :MaterialExpressionSurfaceShader(new cross::MaterialExpressionSurfaceShader(), true)
{
}



MaterialExpressionSurfaceShader::MaterialExpressionSurfaceShader(const cross::MaterialExpressionSurfaceShader * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSurfaceShader::operator MaterialExpressionSurfaceShader^ (const cross::MaterialExpressionSurfaceShader* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSurfaceShader(const_cast<cross::MaterialExpressionSurfaceShader*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSurfaceShader^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFunctionCall export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionFunctionCall::m_FunctionInputs export start
	#define STLDECL_MANAGEDTYPE Clicross::FunctionExpressionInput^
	#define STLDECL_NATIVETYPE cross::FunctionExpressionInput
	CPP_DECLARE_STLVECTOR(MaterialExpressionFunctionCall::, m_FunctionInputsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ MaterialExpressionFunctionCall::m_MaterialFunction::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionFunctionCall*>(this->_native))->m_MaterialFunction)).c_str());
 }
 void MaterialExpressionFunctionCall::m_MaterialFunction::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionFunctionCall*>(this->_native))->m_MaterialFunction) = (ClangenCli::ToNativeString(value));
 }

MaterialExpressionFunctionCall::m_FunctionInputsCliType^ MaterialExpressionFunctionCall::m_FunctionInputs::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionCall*>(this->_native))->m_FunctionInputs;
 }
 void MaterialExpressionFunctionCall::m_FunctionInputs::set(MaterialExpressionFunctionCall::m_FunctionInputsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionCall*>(this->_native))->m_FunctionInputs = *value->_native;
 }


//constructor export here


MaterialExpressionFunctionCall::MaterialExpressionFunctionCall(const cross::MaterialExpressionFunctionCall * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFunctionCall::operator MaterialExpressionFunctionCall^ (const cross::MaterialExpressionFunctionCall* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFunctionCall(const_cast<cross::MaterialExpressionFunctionCall*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFunctionCall^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// FunctionExpressionInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ FunctionExpressionInput::Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::FunctionExpressionInput*>(this->_native))->Input)) , true);
 }
 void FunctionExpressionInput::Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::FunctionExpressionInput*>(this->_native))->Input = value;
 }


//constructor export here
FunctionExpressionInput::FunctionExpressionInput(): FunctionExpressionInput(new cross::FunctionExpressionInput(), true) {}


FunctionExpressionInput::FunctionExpressionInput(const cross::FunctionExpressionInput * obj, bool created_by_clr): 
    _native(const_cast<cross::FunctionExpressionInput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

FunctionExpressionInput::operator FunctionExpressionInput^ (const cross::FunctionExpressionInput* t)
{
    if(t)
    {
        return gcnew FunctionExpressionInput(const_cast<cross::FunctionExpressionInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// FunctionExpressionOutput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
FunctionExpressionOutput::FunctionExpressionOutput(): FunctionExpressionOutput(new cross::FunctionExpressionOutput(), true) {}


FunctionExpressionOutput::FunctionExpressionOutput(const cross::FunctionExpressionOutput * obj, bool created_by_clr): 
    _native(const_cast<cross::FunctionExpressionOutput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

FunctionExpressionOutput::operator FunctionExpressionOutput^ (const cross::FunctionExpressionOutput* t)
{
    if(t)
    {
        return gcnew FunctionExpressionOutput(const_cast<cross::FunctionExpressionOutput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFunctionOutput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpressionFunctionOutput::m_FuncInoutId::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_FuncInoutId;
 }
 void MaterialExpressionFunctionOutput::m_FuncInoutId::set(int value )
 {
	(static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_FuncInoutId = value;
 }

System::String^ MaterialExpressionFunctionOutput::m_OutputName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_OutputName)).c_str());
 }
 void MaterialExpressionFunctionOutput::m_OutputName::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_OutputName) = (ClangenCli::ToNativeString(value));
 }

float MaterialExpressionFunctionOutput::m_SortPriority::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_SortPriority;
 }
 void MaterialExpressionFunctionOutput::m_SortPriority::set(float value )
 {
	(static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_SortPriority = value;
 }

Clicross::ExpressionInput^ MaterialExpressionFunctionOutput::m_Output::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_Output)) , true);
 }
 void MaterialExpressionFunctionOutput::m_Output::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_Output = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFunctionOutput::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionFunctionOutput::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionOutput*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionFunctionOutput::MaterialExpressionFunctionOutput(const cross::MaterialExpressionFunctionOutput * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFunctionOutput::operator MaterialExpressionFunctionOutput^ (const cross::MaterialExpressionFunctionOutput* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFunctionOutput(const_cast<cross::MaterialExpressionFunctionOutput*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFunctionOutput^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFunctionInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpressionFunctionInput::m_FuncInoutId::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_FuncInoutId;
 }
 void MaterialExpressionFunctionInput::m_FuncInoutId::set(int value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_FuncInoutId = value;
 }

System::String^ MaterialExpressionFunctionInput::m_InputName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_InputName)).c_str());
 }
 void MaterialExpressionFunctionInput::m_InputName::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_InputName) = (ClangenCli::ToNativeString(value));
 }

Clicross::FunctionInputType MaterialExpressionFunctionInput::m_InputType::get()
 {
	return (Clicross::FunctionInputType)((int)(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_InputType);
 }
 void MaterialExpressionFunctionInput::m_InputType::set(Clicross::FunctionInputType value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_InputType = static_cast<cross::FunctionInputType>(value);
 }

float MaterialExpressionFunctionInput::m_SortPriority::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_SortPriority;
 }
 void MaterialExpressionFunctionInput::m_SortPriority::set(float value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_SortPriority = value;
 }

bool MaterialExpressionFunctionInput::m_UsePreviewAsDefault::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_UsePreviewAsDefault;
 }
 void MaterialExpressionFunctionInput::m_UsePreviewAsDefault::set(bool value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_UsePreviewAsDefault = value;
 }

Clicross::ExpressionInput^ MaterialExpressionFunctionInput::m_Preview::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_Preview)) , true);
 }
 void MaterialExpressionFunctionInput::m_Preview::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_Preview = value;
 }

Clicross::Float4^ MaterialExpressionFunctionInput::m_PreviewValue::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_PreviewValue)) , true);
 }
 void MaterialExpressionFunctionInput::m_PreviewValue::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_PreviewValue = value;
 }

bool MaterialExpressionFunctionInput::m_IsUETexture2DInput::get()
 {
	return (static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_IsUETexture2DInput;
 }
 void MaterialExpressionFunctionInput::m_IsUETexture2DInput::set(bool value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_IsUETexture2DInput = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFunctionInput::m_Input::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionFunctionInput::m_Input::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFunctionInput*>(this->_native))->m_Input = value;
 }


//constructor export here


MaterialExpressionFunctionInput::MaterialExpressionFunctionInput(const cross::MaterialExpressionFunctionInput * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFunctionInput::operator MaterialExpressionFunctionInput^ (const cross::MaterialExpressionFunctionInput* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFunctionInput(const_cast<cross::MaterialExpressionFunctionInput*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFunctionInput^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCustom export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionCustom::m_CustomInputs export start
	#define STLDECL_MANAGEDTYPE Clicross::CustomExpressionInput^
	#define STLDECL_NATIVETYPE cross::CustomExpressionInput
	CPP_DECLARE_STLVECTOR(MaterialExpressionCustom::, m_CustomInputsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::MaterialExpressionCustom::m_AdditionalCustomOutputs export start
	#define STLDECL_MANAGEDTYPE Clicross::CustomExpressionOutput^
	#define STLDECL_NATIVETYPE cross::CustomExpressionOutput
	CPP_DECLARE_STLVECTOR(MaterialExpressionCustom::, m_AdditionalCustomOutputsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
System::String^ MaterialExpressionCustom::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Name)).c_str());
 }
 void MaterialExpressionCustom::m_Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

System::String^ MaterialExpressionCustom::m_Code::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Code)).c_str());
 }
 void MaterialExpressionCustom::m_Code::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Code) = (ClangenCli::ToNativeString(value));
 }

MaterialExpressionCustom::m_CustomInputsCliType^ MaterialExpressionCustom::m_CustomInputs::get()
 {
	return (static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_CustomInputs;
 }
 void MaterialExpressionCustom::m_CustomInputs::set(MaterialExpressionCustom::m_CustomInputsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_CustomInputs = *value->_native;
 }

Clicross::ExpressionOutput^ MaterialExpressionCustom::m_Output::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Output)) , true);
 }
 void MaterialExpressionCustom::m_Output::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_Output = value;
 }

Clicross::CustomMaterialOutputType MaterialExpressionCustom::m_OutputType::get()
 {
	return (Clicross::CustomMaterialOutputType)((int)(static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_OutputType);
 }
 void MaterialExpressionCustom::m_OutputType::set(Clicross::CustomMaterialOutputType value )
 {
	(static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_OutputType = static_cast<cross::CustomMaterialOutputType>(value);
 }

MaterialExpressionCustom::m_AdditionalCustomOutputsCliType^ MaterialExpressionCustom::m_AdditionalCustomOutputs::get()
 {
	return (static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_AdditionalCustomOutputs;
 }
 void MaterialExpressionCustom::m_AdditionalCustomOutputs::set(MaterialExpressionCustom::m_AdditionalCustomOutputsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionCustom*>(this->_native))->m_AdditionalCustomOutputs = *value->_native;
 }


//constructor export here


MaterialExpressionCustom::MaterialExpressionCustom(const cross::MaterialExpressionCustom * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCustom::operator MaterialExpressionCustom^ (const cross::MaterialExpressionCustom* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCustom(const_cast<cross::MaterialExpressionCustom*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCustom^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CustomExpressionInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ CustomExpressionInput::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::CustomExpressionInput*>(this->_native))->Name)).c_str());
 }
 void CustomExpressionInput::Name::set(System::String^ value )
 {
	((static_cast<cross::CustomExpressionInput*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionInput^ CustomExpressionInput::Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::CustomExpressionInput*>(this->_native))->Input)) , true);
 }
 void CustomExpressionInput::Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::CustomExpressionInput*>(this->_native))->Input = value;
 }


//constructor export here
CustomExpressionInput::CustomExpressionInput(): CustomExpressionInput(new cross::CustomExpressionInput(), true) {}


CustomExpressionInput::CustomExpressionInput(const cross::CustomExpressionInput * obj, bool created_by_clr): 
    _native(const_cast<cross::CustomExpressionInput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CustomExpressionInput::operator CustomExpressionInput^ (const cross::CustomExpressionInput* t)
{
    if(t)
    {
        return gcnew CustomExpressionInput(const_cast<cross::CustomExpressionInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CustomExpressionOutput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ CustomExpressionOutput::Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::CustomExpressionOutput*>(this->_native))->Name)).c_str());
 }
 void CustomExpressionOutput::Name::set(System::String^ value )
 {
	((static_cast<cross::CustomExpressionOutput*>(this->_native))->Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::CustomMaterialOutputType CustomExpressionOutput::OutputType::get()
 {
	return (Clicross::CustomMaterialOutputType)((int)(static_cast<cross::CustomExpressionOutput*>(this->_native))->OutputType);
 }
 void CustomExpressionOutput::OutputType::set(Clicross::CustomMaterialOutputType value )
 {
	(static_cast<cross::CustomExpressionOutput*>(this->_native))->OutputType = static_cast<cross::CustomMaterialOutputType>(value);
 }


//constructor export here
CustomExpressionOutput::CustomExpressionOutput(): CustomExpressionOutput(new cross::CustomExpressionOutput(), true) {}


CustomExpressionOutput::CustomExpressionOutput(const cross::CustomExpressionOutput * obj, bool created_by_clr): 
    _native(const_cast<cross::CustomExpressionOutput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CustomExpressionOutput::operator CustomExpressionOutput^ (const cross::CustomExpressionOutput* t)
{
    if(t)
    {
        return gcnew CustomExpressionOutput(const_cast<cross::CustomExpressionOutput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MpcDynamicEnum export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MpcDynamicEnum::MpcDynamicEnum( )
    :MpcDynamicEnum(new cross::MpcDynamicEnum(), true)
{
}



MpcDynamicEnum::MpcDynamicEnum(const cross::MpcDynamicEnum * obj, bool created_by_clr): Clicross::DynamicEnum(obj, created_by_clr)
{
}

MpcDynamicEnum::operator MpcDynamicEnum^ (const cross::MpcDynamicEnum* t)
{
    if(t)
    {
        return gcnew MpcDynamicEnum(const_cast<cross::MpcDynamicEnum*>(t));
    }
    else
        return nullptr;
}

void MpcDynamicEnum::OnChangeMpcFilePath(System::String^ mpcFilePath )
{
    (static_cast<cross::MpcDynamicEnum*>(this->_native))->OnChangeMpcFilePath( ClangenCli::ToNativeString(mpcFilePath).c_str());
}


}   //end namespace Clicross

// MaterialExpressionTextureObject export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpressionTextureObject::m_TextureType::get()
 {
	return ((int)(static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureType);
 }
 void MaterialExpressionTextureObject::m_TextureType::set(int value )
 {
	(static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureType = static_cast<cross::MaterialValueType>(value);
 }

System::String^ MaterialExpressionTextureObject::m_TextureObjectName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureObjectName)).c_str());
 }
 void MaterialExpressionTextureObject::m_TextureObjectName::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureObjectName) = (ClangenCli::ToNativeString(value));
 }

System::String^ MaterialExpressionTextureObject::m_TextureString::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureString)).c_str());
 }
 void MaterialExpressionTextureObject::m_TextureString::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_TextureString) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureObject::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTextureObject::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureObject*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTextureObject::MaterialExpressionTextureObject(const cross::MaterialExpressionTextureObject * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTextureObject::operator MaterialExpressionTextureObject^ (const cross::MaterialExpressionTextureObject* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTextureObject(const_cast<cross::MaterialExpressionTextureObject*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTextureObject^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCustomInterpolator export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionCustomInterpolator::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Name)).c_str());
 }
 void MaterialExpressionCustomInterpolator::m_Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionInput^ MaterialExpressionCustomInterpolator::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionCustomInterpolator::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCustomInterpolator::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCustomInterpolator::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCustomInterpolator*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCustomInterpolator::MaterialExpressionCustomInterpolator(const cross::MaterialExpressionCustomInterpolator * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCustomInterpolator::operator MaterialExpressionCustomInterpolator^ (const cross::MaterialExpressionCustomInterpolator* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCustomInterpolator(const_cast<cross::MaterialExpressionCustomInterpolator*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCustomInterpolator^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialInstanceEditor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialInstanceEditor::MaterialInstanceEditor(System::String^ resourceGuid )
    :MaterialInstanceEditor(new cross::MaterialInstanceEditor(ClangenCli::ToNativeString(resourceGuid).c_str()), true)
{
}



MaterialInstanceEditor::MaterialInstanceEditor(const cross::MaterialInstanceEditor * obj, bool created_by_clr): 
    _native(const_cast<cross::MaterialInstanceEditor *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MaterialInstanceEditor::operator MaterialInstanceEditor^ (const cross::MaterialInstanceEditor* t)
{
    if(t)
    {
        return gcnew MaterialInstanceEditor(const_cast<cross::MaterialInstanceEditor*>(t));
    }
    else
        return nullptr;
}

void MaterialInstanceEditor::OnFrame( )
{
    (static_cast<cross::MaterialInstanceEditor*>(this->_native))->OnFrame( );
}

Clicross::MaterialInstanceDefines^ MaterialInstanceEditor::GetMaterialInstanceDefines( )
{
    return (Clicross::MaterialInstanceDefines^)((static_cast<cross::MaterialInstanceEditor*>(this->_native))->GetMaterialInstanceDefines( ));
}

void MaterialInstanceEditor::OnPropertyChange( )
{
    (static_cast<cross::MaterialInstanceEditor*>(this->_native))->OnPropertyChange( );
}

bool MaterialInstanceEditor::OnMaterialChange(System::String^ materialGuid )
{
    return (static_cast<cross::MaterialInstanceEditor*>(this->_native))->OnMaterialChange( ClangenCli::ToNativeString(materialGuid).c_str());
}

bool MaterialInstanceEditor::IsDependent(System::String^ materialGuid )
{
    return (static_cast<cross::MaterialInstanceEditor*>(this->_native))->IsDependent( ClangenCli::ToNativeString(materialGuid).c_str());
}

void MaterialInstanceEditor::OnParentChange( )
{
    (static_cast<cross::MaterialInstanceEditor*>(this->_native))->OnParentChange( );
}

bool MaterialInstanceEditor::IsParentChanged( )
{
    return (static_cast<cross::MaterialInstanceEditor*>(this->_native))->IsParentChanged( );
}

void MaterialInstanceEditor::SetParentModifiedStatus(bool value )
{
    (static_cast<cross::MaterialInstanceEditor*>(this->_native))->SetParentModifiedStatus( value);
}


}   //end namespace Clicross

// AttributeExpressionInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::EMaterialProperty AttributeExpressionInput::Attribute::get()
 {
	return (Clicross::EMaterialProperty)((int)(static_cast<cross::AttributeExpressionInput*>(this->_native))->Attribute);
 }
 void AttributeExpressionInput::Attribute::set(Clicross::EMaterialProperty value )
 {
	(static_cast<cross::AttributeExpressionInput*>(this->_native))->Attribute = static_cast<cross::EMaterialProperty>(value);
 }

Clicross::ExpressionInput^ AttributeExpressionInput::Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::AttributeExpressionInput*>(this->_native))->Input)) , true);
 }
 void AttributeExpressionInput::Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::AttributeExpressionInput*>(this->_native))->Input = value;
 }


//constructor export here
AttributeExpressionInput::AttributeExpressionInput(): AttributeExpressionInput(new cross::AttributeExpressionInput(), true) {}


AttributeExpressionInput::AttributeExpressionInput(const cross::AttributeExpressionInput * obj, bool created_by_clr): 
    _native(const_cast<cross::AttributeExpressionInput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

AttributeExpressionInput::operator AttributeExpressionInput^ (const cross::AttributeExpressionInput* t)
{
    if(t)
    {
        return gcnew AttributeExpressionInput(const_cast<cross::AttributeExpressionInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// AttributeExpressionOutput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::EMaterialProperty AttributeExpressionOutput::Attribute::get()
 {
	return (Clicross::EMaterialProperty)((int)(static_cast<cross::AttributeExpressionOutput*>(this->_native))->Attribute);
 }
 void AttributeExpressionOutput::Attribute::set(Clicross::EMaterialProperty value )
 {
	(static_cast<cross::AttributeExpressionOutput*>(this->_native))->Attribute = static_cast<cross::EMaterialProperty>(value);
 }


//constructor export here
AttributeExpressionOutput::AttributeExpressionOutput(): AttributeExpressionOutput(new cross::AttributeExpressionOutput(), true) {}


AttributeExpressionOutput::AttributeExpressionOutput(const cross::AttributeExpressionOutput * obj, bool created_by_clr): 
    _native(const_cast<cross::AttributeExpressionOutput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

AttributeExpressionOutput::operator AttributeExpressionOutput^ (const cross::AttributeExpressionOutput* t)
{
    if(t)
    {
        return gcnew AttributeExpressionOutput(const_cast<cross::AttributeExpressionOutput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionAbs export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionAbs::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionAbs*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionAbs::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionAbs*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionAbs::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionAbs*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionAbs::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionAbs*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionAbs::MaterialExpressionAbs(const cross::MaterialExpressionAbs * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionAbs::operator MaterialExpressionAbs^ (const cross::MaterialExpressionAbs* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionAbs(const_cast<cross::MaterialExpressionAbs*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionAbs^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionAdd export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionAdd::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionAdd::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionAdd::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionAdd::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionAdd::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionAdd::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_Result = value;
 }

float MaterialExpressionAdd::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionAdd::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionAdd::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionAdd::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionAdd*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionAdd::MaterialExpressionAdd(const cross::MaterialExpressionAdd * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionAdd::operator MaterialExpressionAdd^ (const cross::MaterialExpressionAdd* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionAdd(const_cast<cross::MaterialExpressionAdd*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionAdd^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionAppendVector export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionAppendVector::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionAppendVector::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionAppendVector::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionAppendVector::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionAppendVector::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionAppendVector::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionAppendVector*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionAppendVector::MaterialExpressionAppendVector(const cross::MaterialExpressionAppendVector * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionAppendVector::operator MaterialExpressionAppendVector^ (const cross::MaterialExpressionAppendVector* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionAppendVector(const_cast<cross::MaterialExpressionAppendVector*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionAppendVector^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionArccosine export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionArccosine::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionArccosine*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionArccosine::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionArccosine*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionArccosine::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionArccosine*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionArccosine::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionArccosine*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionArccosine::MaterialExpressionArccosine(const cross::MaterialExpressionArccosine * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionArccosine::operator MaterialExpressionArccosine^ (const cross::MaterialExpressionArccosine* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionArccosine(const_cast<cross::MaterialExpressionArccosine*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionArccosine^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionArcsine export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionArcsine::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionArcsine*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionArcsine::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionArcsine*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionArcsine::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionArcsine*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionArcsine::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionArcsine*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionArcsine::MaterialExpressionArcsine(const cross::MaterialExpressionArcsine * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionArcsine::operator MaterialExpressionArcsine^ (const cross::MaterialExpressionArcsine* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionArcsine(const_cast<cross::MaterialExpressionArcsine*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionArcsine^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionArctangent export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionArctangent::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionArctangent*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionArctangent::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionArctangent*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionArctangent::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionArctangent*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionArctangent::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionArctangent*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionArctangent::MaterialExpressionArctangent(const cross::MaterialExpressionArctangent * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionArctangent::operator MaterialExpressionArctangent^ (const cross::MaterialExpressionArctangent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionArctangent(const_cast<cross::MaterialExpressionArctangent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionArctangent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionArctangent2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionArctangent2::m_Y::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_Y)) , true);
 }
 void MaterialExpressionArctangent2::m_Y::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_Y = value;
 }

Clicross::ExpressionInput^ MaterialExpressionArctangent2::m_X::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_X)) , true);
 }
 void MaterialExpressionArctangent2::m_X::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_X = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionArctangent2::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionArctangent2::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionArctangent2*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionArctangent2::MaterialExpressionArctangent2(const cross::MaterialExpressionArctangent2 * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionArctangent2::operator MaterialExpressionArctangent2^ (const cross::MaterialExpressionArctangent2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionArctangent2(const_cast<cross::MaterialExpressionArctangent2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionArctangent2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionBlendMaterialAttributes export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionAttributesInput^ MaterialExpressionBlendMaterialAttributes::m_A::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionBlendMaterialAttributes::m_A::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_A = value;
 }

Clicross::ExpressionAttributesInput^ MaterialExpressionBlendMaterialAttributes::m_B::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionBlendMaterialAttributes::m_B::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_B = value;
 }

Clicross::ExpressionInput^ MaterialExpressionBlendMaterialAttributes::m_Alpha::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_Alpha)) , true);
 }
 void MaterialExpressionBlendMaterialAttributes::m_Alpha::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_Alpha = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBlendMaterialAttributes::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionBlendMaterialAttributes::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBlendMaterialAttributes*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionBlendMaterialAttributes::MaterialExpressionBlendMaterialAttributes(const cross::MaterialExpressionBlendMaterialAttributes * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionBlendMaterialAttributes::operator MaterialExpressionBlendMaterialAttributes^ (const cross::MaterialExpressionBlendMaterialAttributes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionBlendMaterialAttributes(const_cast<cross::MaterialExpressionBlendMaterialAttributes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionBlendMaterialAttributes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionBreakMaterialAttributes export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionAttributesInput^ MaterialExpressionBreakMaterialAttributes::m_MaterialAttributes::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_MaterialAttributes)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_MaterialAttributes::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_MaterialAttributes = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_BaseColor::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_BaseColor)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_BaseColor::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_BaseColor = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Metallic::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Metallic)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Metallic::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Metallic = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Specular::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Specular)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Specular::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Specular = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Roughness::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Roughness)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Roughness::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Roughness = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::Anisotropy::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->Anisotropy)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::Anisotropy::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->Anisotropy = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_EmissiveColor::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_EmissiveColor)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_EmissiveColor::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_EmissiveColor = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Opacity::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Opacity)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Opacity::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Opacity = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_OpacityMask::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_OpacityMask)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_OpacityMask::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_OpacityMask = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Normal::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Normal)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Normal::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Normal = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_Tangent::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Tangent)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_Tangent::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_Tangent = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_WorldPositionOffset::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_WorldPositionOffset)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_WorldPositionOffset::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_WorldPositionOffset = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_SubsurfaceColor::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_SubsurfaceColor)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_SubsurfaceColor::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_SubsurfaceColor = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_AmbientOcclusion::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_AmbientOcclusion)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_AmbientOcclusion::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_AmbientOcclusion = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBreakMaterialAttributes::m_ShadingModel::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_ShadingModel)) , true);
 }
 void MaterialExpressionBreakMaterialAttributes::m_ShadingModel::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBreakMaterialAttributes*>(this->_native))->m_ShadingModel = value;
 }


//constructor export here
MaterialExpressionBreakMaterialAttributes::MaterialExpressionBreakMaterialAttributes( )
    :MaterialExpressionBreakMaterialAttributes(new cross::MaterialExpressionBreakMaterialAttributes(), true)
{
}



MaterialExpressionBreakMaterialAttributes::MaterialExpressionBreakMaterialAttributes(const cross::MaterialExpressionBreakMaterialAttributes * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionBreakMaterialAttributes::operator MaterialExpressionBreakMaterialAttributes^ (const cross::MaterialExpressionBreakMaterialAttributes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionBreakMaterialAttributes(const_cast<cross::MaterialExpressionBreakMaterialAttributes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionBreakMaterialAttributes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionBumpOffset export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionBumpOffset::m_Coordinate::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Coordinate)) , true);
 }
 void MaterialExpressionBumpOffset::m_Coordinate::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Coordinate = value;
 }

Clicross::ExpressionInput^ MaterialExpressionBumpOffset::m_Height::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Height)) , true);
 }
 void MaterialExpressionBumpOffset::m_Height::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Height = value;
 }

Clicross::ExpressionInput^ MaterialExpressionBumpOffset::m_HeightRatioInput::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_HeightRatioInput)) , true);
 }
 void MaterialExpressionBumpOffset::m_HeightRatioInput::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_HeightRatioInput = value;
 }

float MaterialExpressionBumpOffset::m_ReferencePlane::get()
 {
	return (static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_ReferencePlane;
 }
 void MaterialExpressionBumpOffset::m_ReferencePlane::set(float value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_ReferencePlane = value;
 }

int MaterialExpressionBumpOffset::m_ConstCoordinate::get()
 {
	return (static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_ConstCoordinate;
 }
 void MaterialExpressionBumpOffset::m_ConstCoordinate::set(int value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_ConstCoordinate = value;
 }

float MaterialExpressionBumpOffset::m_HeightRatio::get()
 {
	return (static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_HeightRatio;
 }
 void MaterialExpressionBumpOffset::m_HeightRatio::set(float value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_HeightRatio = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionBumpOffset::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionBumpOffset::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionBumpOffset*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionBumpOffset::MaterialExpressionBumpOffset(const cross::MaterialExpressionBumpOffset * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionBumpOffset::operator MaterialExpressionBumpOffset^ (const cross::MaterialExpressionBumpOffset* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionBumpOffset(const_cast<cross::MaterialExpressionBumpOffset*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionBumpOffset^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCameraPositionWS export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionCameraPositionWS::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCameraPositionWS*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCameraPositionWS::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCameraPositionWS*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCameraPositionWS::MaterialExpressionCameraPositionWS(const cross::MaterialExpressionCameraPositionWS * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCameraPositionWS::operator MaterialExpressionCameraPositionWS^ (const cross::MaterialExpressionCameraPositionWS* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCameraPositionWS(const_cast<cross::MaterialExpressionCameraPositionWS*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCameraPositionWS^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCameraTilePosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionCameraTilePosition::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCameraTilePosition*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCameraTilePosition::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCameraTilePosition*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCameraTilePosition::MaterialExpressionCameraTilePosition(const cross::MaterialExpressionCameraTilePosition * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCameraTilePosition::operator MaterialExpressionCameraTilePosition^ (const cross::MaterialExpressionCameraTilePosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCameraTilePosition(const_cast<cross::MaterialExpressionCameraTilePosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCameraTilePosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCameraVectorWS export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionCameraVectorWS::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCameraVectorWS*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCameraVectorWS::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCameraVectorWS*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCameraVectorWS::MaterialExpressionCameraVectorWS(const cross::MaterialExpressionCameraVectorWS * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCameraVectorWS::operator MaterialExpressionCameraVectorWS^ (const cross::MaterialExpressionCameraVectorWS* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCameraVectorWS(const_cast<cross::MaterialExpressionCameraVectorWS*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCameraVectorWS^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCast export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionCast::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCast*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionCast::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCast*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCast::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCast*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCast::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCast*>(this->_native))->m_Result = value;
 }

Clicross::MaterialTypeCast MaterialExpressionCast::m_DstType::get()
 {
	return (Clicross::MaterialTypeCast)((int)(static_cast<cross::MaterialExpressionCast*>(this->_native))->m_DstType);
 }
 void MaterialExpressionCast::m_DstType::set(Clicross::MaterialTypeCast value )
 {
	(static_cast<cross::MaterialExpressionCast*>(this->_native))->m_DstType = static_cast<cross::MaterialTypeCast>(value);
 }


//constructor export here


MaterialExpressionCast::MaterialExpressionCast(const cross::MaterialExpressionCast * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCast::operator MaterialExpressionCast^ (const cross::MaterialExpressionCast* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCast(const_cast<cross::MaterialExpressionCast*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCast^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCEToUEWorldPosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionCEToUEWorldPosition::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionCEToUEWorldPosition::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_Input = value;
 }

bool MaterialExpressionCEToUEWorldPosition::m_CEToUE::get()
 {
	return (static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_CEToUE;
 }
 void MaterialExpressionCEToUEWorldPosition::m_CEToUE::set(bool value )
 {
	(static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_CEToUE = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCEToUEWorldPosition::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCEToUEWorldPosition::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCEToUEWorldPosition*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCEToUEWorldPosition::MaterialExpressionCEToUEWorldPosition(const cross::MaterialExpressionCEToUEWorldPosition * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCEToUEWorldPosition::operator MaterialExpressionCEToUEWorldPosition^ (const cross::MaterialExpressionCEToUEWorldPosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCEToUEWorldPosition(const_cast<cross::MaterialExpressionCEToUEWorldPosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCEToUEWorldPosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


