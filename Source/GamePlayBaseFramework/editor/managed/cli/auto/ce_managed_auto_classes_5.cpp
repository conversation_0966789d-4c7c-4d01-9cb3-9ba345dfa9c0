//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// MaterialExpressionCeil export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionCeil::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCeil*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionCeil::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCeil*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCeil::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCeil*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCeil::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCeil*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCeil::MaterialExpressionCeil(const cross::MaterialExpressionCeil * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCeil::operator MaterialExpressionCeil^ (const cross::MaterialExpressionCeil* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCeil(const_cast<cross::MaterialExpressionCeil*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCeil^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionClamp export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionClamp::m_Value::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Value)) , true);
 }
 void MaterialExpressionClamp::m_Value::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionInput^ MaterialExpressionClamp::m_Min::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Min)) , true);
 }
 void MaterialExpressionClamp::m_Min::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Min = value;
 }

Clicross::ExpressionInput^ MaterialExpressionClamp::m_Max::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Max)) , true);
 }
 void MaterialExpressionClamp::m_Max::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Max = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionClamp::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionClamp::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_Result = value;
 }

float MaterialExpressionClamp::m_ConstMin::get()
 {
	return (static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_ConstMin;
 }
 void MaterialExpressionClamp::m_ConstMin::set(float value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_ConstMin = value;
 }

float MaterialExpressionClamp::m_ConstMax::get()
 {
	return (static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_ConstMax;
 }
 void MaterialExpressionClamp::m_ConstMax::set(float value )
 {
	(static_cast<cross::MaterialExpressionClamp*>(this->_native))->m_ConstMax = value;
 }


//constructor export here


MaterialExpressionClamp::MaterialExpressionClamp(const cross::MaterialExpressionClamp * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionClamp::operator MaterialExpressionClamp^ (const cross::MaterialExpressionClamp* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionClamp(const_cast<cross::MaterialExpressionClamp*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionClamp^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionComment export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionComment::m_CommentTitle::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionComment*>(this->_native))->m_CommentTitle)).c_str());
 }
 void MaterialExpressionComment::m_CommentTitle::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionComment*>(this->_native))->m_CommentTitle) = (ClangenCli::ToNativeString(value));
 }

Clicross::Float4^ MaterialExpressionComment::m_Color::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Color)) , true);
 }
 void MaterialExpressionComment::m_Color::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Color = value;
 }

bool MaterialExpressionComment::mShowBubbleWhenZoomed::get()
 {
	return (static_cast<cross::MaterialExpressionComment*>(this->_native))->mShowBubbleWhenZoomed;
 }
 void MaterialExpressionComment::mShowBubbleWhenZoomed::set(bool value )
 {
	(static_cast<cross::MaterialExpressionComment*>(this->_native))->mShowBubbleWhenZoomed = value;
 }

int MaterialExpressionComment::m_Width::get()
 {
	return (static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Width;
 }
 void MaterialExpressionComment::m_Width::set(int value )
 {
	(static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Width = value;
 }

int MaterialExpressionComment::m_Height::get()
 {
	return (static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Height;
 }
 void MaterialExpressionComment::m_Height::set(int value )
 {
	(static_cast<cross::MaterialExpressionComment*>(this->_native))->m_Height = value;
 }


//constructor export here


MaterialExpressionComment::MaterialExpressionComment(const cross::MaterialExpressionComment * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionComment::operator MaterialExpressionComment^ (const cross::MaterialExpressionComment* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionComment(const_cast<cross::MaterialExpressionComment*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionComment^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionComponentMask export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionComponentMask::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionComponentMask::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionComponentMask::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionComponentMask::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_Result = value;
 }

bool MaterialExpressionComponentMask::m_R::get()
 {
	return (static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_R;
 }
 void MaterialExpressionComponentMask::m_R::set(bool value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_R = value;
 }

bool MaterialExpressionComponentMask::m_G::get()
 {
	return (static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_G;
 }
 void MaterialExpressionComponentMask::m_G::set(bool value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_G = value;
 }

bool MaterialExpressionComponentMask::m_B::get()
 {
	return (static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_B;
 }
 void MaterialExpressionComponentMask::m_B::set(bool value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_B = value;
 }

bool MaterialExpressionComponentMask::m_A::get()
 {
	return (static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_A;
 }
 void MaterialExpressionComponentMask::m_A::set(bool value )
 {
	(static_cast<cross::MaterialExpressionComponentMask*>(this->_native))->m_A = value;
 }


//constructor export here


MaterialExpressionComponentMask::MaterialExpressionComponentMask(const cross::MaterialExpressionComponentMask * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionComponentMask::operator MaterialExpressionComponentMask^ (const cross::MaterialExpressionComponentMask* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionComponentMask(const_cast<cross::MaterialExpressionComponentMask*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionComponentMask^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstantBiasScale export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionConstantBiasScale::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionConstantBiasScale::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Input = value;
 }

float MaterialExpressionConstantBiasScale::m_Bias::get()
 {
	return (static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Bias;
 }
 void MaterialExpressionConstantBiasScale::m_Bias::set(float value )
 {
	(static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Bias = value;
 }

float MaterialExpressionConstantBiasScale::m_Scale::get()
 {
	return (static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Scale;
 }
 void MaterialExpressionConstantBiasScale::m_Scale::set(float value )
 {
	(static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Scale = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstantBiasScale::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstantBiasScale::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstantBiasScale*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstantBiasScale::MaterialExpressionConstantBiasScale(const cross::MaterialExpressionConstantBiasScale * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstantBiasScale::operator MaterialExpressionConstantBiasScale^ (const cross::MaterialExpressionConstantBiasScale* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstantBiasScale(const_cast<cross::MaterialExpressionConstantBiasScale*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstantBiasScale^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstantDouble export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
double MaterialExpressionConstantDouble::m_Value::get()
 {
	return (static_cast<cross::MaterialExpressionConstantDouble*>(this->_native))->m_Value;
 }
 void MaterialExpressionConstantDouble::m_Value::set(double value )
 {
	(static_cast<cross::MaterialExpressionConstantDouble*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstantDouble::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstantDouble*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstantDouble::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstantDouble*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstantDouble::MaterialExpressionConstantDouble(const cross::MaterialExpressionConstantDouble * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstantDouble::operator MaterialExpressionConstantDouble^ (const cross::MaterialExpressionConstantDouble* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstantDouble(const_cast<cross::MaterialExpressionConstantDouble*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstantDouble^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstant export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float MaterialExpressionConstant::m_Const::get()
 {
	return (static_cast<cross::MaterialExpressionConstant*>(this->_native))->m_Const;
 }
 void MaterialExpressionConstant::m_Const::set(float value )
 {
	(static_cast<cross::MaterialExpressionConstant*>(this->_native))->m_Const = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstant::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstant*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstant::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstant*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstant::MaterialExpressionConstant(const cross::MaterialExpressionConstant * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstant::operator MaterialExpressionConstant^ (const cross::MaterialExpressionConstant* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstant(const_cast<cross::MaterialExpressionConstant*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstant^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstant2Vector export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float MaterialExpressionConstant2Vector::m_X::get()
 {
	return (static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_X;
 }
 void MaterialExpressionConstant2Vector::m_X::set(float value )
 {
	(static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_X = value;
 }

float MaterialExpressionConstant2Vector::m_Y::get()
 {
	return (static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_Y;
 }
 void MaterialExpressionConstant2Vector::m_Y::set(float value )
 {
	(static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_Y = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstant2Vector::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstant2Vector::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstant2Vector*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstant2Vector::MaterialExpressionConstant2Vector(const cross::MaterialExpressionConstant2Vector * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstant2Vector::operator MaterialExpressionConstant2Vector^ (const cross::MaterialExpressionConstant2Vector* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstant2Vector(const_cast<cross::MaterialExpressionConstant2Vector*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstant2Vector^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstant3Vector export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float3^ MaterialExpressionConstant3Vector::m_Value::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::MaterialExpressionConstant3Vector*>(this->_native))->m_Value)) , true);
 }
 void MaterialExpressionConstant3Vector::m_Value::set(Clicross::Float3^ value )
 {
	(static_cast<cross::MaterialExpressionConstant3Vector*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstant3Vector::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstant3Vector*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstant3Vector::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstant3Vector*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstant3Vector::MaterialExpressionConstant3Vector(const cross::MaterialExpressionConstant3Vector * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstant3Vector::operator MaterialExpressionConstant3Vector^ (const cross::MaterialExpressionConstant3Vector* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstant3Vector(const_cast<cross::MaterialExpressionConstant3Vector*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstant3Vector^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionConstant4Vector export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float4^ MaterialExpressionConstant4Vector::m_Value::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionConstant4Vector*>(this->_native))->m_Value)) , true);
 }
 void MaterialExpressionConstant4Vector::m_Value::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionConstant4Vector*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionConstant4Vector::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionConstant4Vector*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionConstant4Vector::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionConstant4Vector*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionConstant4Vector::MaterialExpressionConstant4Vector(const cross::MaterialExpressionConstant4Vector * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionConstant4Vector::operator MaterialExpressionConstant4Vector^ (const cross::MaterialExpressionConstant4Vector* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionConstant4Vector(const_cast<cross::MaterialExpressionConstant4Vector*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionConstant4Vector^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCosine export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionCosine::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCosine*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionCosine::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCosine*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCosine::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCosine*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCosine::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCosine*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCosine::MaterialExpressionCosine(const cross::MaterialExpressionCosine * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCosine::operator MaterialExpressionCosine^ (const cross::MaterialExpressionCosine* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCosine(const_cast<cross::MaterialExpressionCosine*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCosine^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionCrossProduct export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionCrossProduct::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionCrossProduct::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionCrossProduct::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionCrossProduct::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionCrossProduct::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionCrossProduct::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionCrossProduct*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionCrossProduct::MaterialExpressionCrossProduct(const cross::MaterialExpressionCrossProduct * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionCrossProduct::operator MaterialExpressionCrossProduct^ (const cross::MaterialExpressionCrossProduct* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionCrossProduct(const_cast<cross::MaterialExpressionCrossProduct*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionCrossProduct^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDDX export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDDX::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDDX*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionDDX::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDDX*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDDX::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDDX*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDDX::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDDX*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDDX::MaterialExpressionDDX(const cross::MaterialExpressionDDX * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDDX::operator MaterialExpressionDDX^ (const cross::MaterialExpressionDDX* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDDX(const_cast<cross::MaterialExpressionDDX*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDDX^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDDY export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDDY::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDDY*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionDDY::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDDY*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDDY::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDDY*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDDY::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDDY*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDDY::MaterialExpressionDDY(const cross::MaterialExpressionDDY * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDDY::operator MaterialExpressionDDY^ (const cross::MaterialExpressionDDY* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDDY(const_cast<cross::MaterialExpressionDDY*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDDY^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDepthFade export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDepthFade::m_Opacity::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_Opacity)) , true);
 }
 void MaterialExpressionDepthFade::m_Opacity::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_Opacity = value;
 }

Clicross::ExpressionInput^ MaterialExpressionDepthFade::m_FadeDistance::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_FadeDistance)) , true);
 }
 void MaterialExpressionDepthFade::m_FadeDistance::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_FadeDistance = value;
 }

float MaterialExpressionDepthFade::m_OpacityDefault::get()
 {
	return (static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_OpacityDefault;
 }
 void MaterialExpressionDepthFade::m_OpacityDefault::set(float value )
 {
	(static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_OpacityDefault = value;
 }

float MaterialExpressionDepthFade::m_FadeDistanceDefault::get()
 {
	return (static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_FadeDistanceDefault;
 }
 void MaterialExpressionDepthFade::m_FadeDistanceDefault::set(float value )
 {
	(static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_FadeDistanceDefault = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDepthFade::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDepthFade::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDepthFade*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDepthFade::MaterialExpressionDepthFade(const cross::MaterialExpressionDepthFade * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDepthFade::operator MaterialExpressionDepthFade^ (const cross::MaterialExpressionDepthFade* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDepthFade(const_cast<cross::MaterialExpressionDepthFade*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDepthFade^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDesaturation export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDesaturation::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionDesaturation::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionInput^ MaterialExpressionDesaturation::m_Fraction::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Fraction)) , true);
 }
 void MaterialExpressionDesaturation::m_Fraction::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Fraction = value;
 }

Clicross::Float4^ MaterialExpressionDesaturation::m_LuminanceFactors::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_LuminanceFactors)) , true);
 }
 void MaterialExpressionDesaturation::m_LuminanceFactors::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_LuminanceFactors = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDesaturation::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDesaturation::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDesaturation*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDesaturation::MaterialExpressionDesaturation(const cross::MaterialExpressionDesaturation * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDesaturation::operator MaterialExpressionDesaturation^ (const cross::MaterialExpressionDesaturation* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDesaturation(const_cast<cross::MaterialExpressionDesaturation*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDesaturation^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDistance export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDistance::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionDistance::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionDistance::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionDistance::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDistance::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDistance::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDistance*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDistance::MaterialExpressionDistance(const cross::MaterialExpressionDistance * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDistance::operator MaterialExpressionDistance^ (const cross::MaterialExpressionDistance* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDistance(const_cast<cross::MaterialExpressionDistance*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDistance^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDivide export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDivide::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionDivide::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionDivide::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionDivide::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDivide::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDivide::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_Result = value;
 }

float MaterialExpressionDivide::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionDivide::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionDivide::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionDivide::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionDivide*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionDivide::MaterialExpressionDivide(const cross::MaterialExpressionDivide * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDivide::operator MaterialExpressionDivide^ (const cross::MaterialExpressionDivide* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDivide(const_cast<cross::MaterialExpressionDivide*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDivide^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionDotProduct export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionDotProduct::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionDotProduct::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionDotProduct::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionDotProduct::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionDotProduct::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionDotProduct::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionDotProduct*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionDotProduct::MaterialExpressionDotProduct(const cross::MaterialExpressionDotProduct * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionDotProduct::operator MaterialExpressionDotProduct^ (const cross::MaterialExpressionDotProduct* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionDotProduct(const_cast<cross::MaterialExpressionDotProduct*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionDotProduct^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionExponential export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionExponential::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionExponential::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionExponential::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionExponential::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_Result = value;
 }

float MaterialExpressionExponential::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionExponential::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionExponential*>(this->_native))->m_ConstA = value;
 }


//constructor export here


MaterialExpressionExponential::MaterialExpressionExponential(const cross::MaterialExpressionExponential * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionExponential::operator MaterialExpressionExponential^ (const cross::MaterialExpressionExponential* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionExponential(const_cast<cross::MaterialExpressionExponential*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionExponential^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionExponential2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionExponential2::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionExponential2::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionExponential2::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionExponential2::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_Result = value;
 }

float MaterialExpressionExponential2::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionExponential2::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionExponential2*>(this->_native))->m_ConstA = value;
 }


//constructor export here


MaterialExpressionExponential2::MaterialExpressionExponential2(const cross::MaterialExpressionExponential2 * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionExponential2::operator MaterialExpressionExponential2^ (const cross::MaterialExpressionExponential2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionExponential2(const_cast<cross::MaterialExpressionExponential2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionExponential2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionEyeAdaption export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionEyeAdaption::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionEyeAdaption*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionEyeAdaption::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionEyeAdaption*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionEyeAdaption::MaterialExpressionEyeAdaption(const cross::MaterialExpressionEyeAdaption * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionEyeAdaption::operator MaterialExpressionEyeAdaption^ (const cross::MaterialExpressionEyeAdaption* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionEyeAdaption(const_cast<cross::MaterialExpressionEyeAdaption*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionEyeAdaption^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFloor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionFloor::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFloor*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionFloor::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFloor*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFloor::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFloor*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionFloor::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFloor*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionFloor::MaterialExpressionFloor(const cross::MaterialExpressionFloor * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFloor::operator MaterialExpressionFloor^ (const cross::MaterialExpressionFloor* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFloor(const_cast<cross::MaterialExpressionFloor*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFloor^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFmod export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionFmod::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionFmod::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionFmod::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionFmod::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFmod::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionFmod::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFmod*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionFmod::MaterialExpressionFmod(const cross::MaterialExpressionFmod * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFmod::operator MaterialExpressionFmod^ (const cross::MaterialExpressionFmod* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFmod(const_cast<cross::MaterialExpressionFmod*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFmod^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFrac export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionFrac::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFrac*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionFrac::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFrac*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFrac::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFrac*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionFrac::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFrac*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionFrac::MaterialExpressionFrac(const cross::MaterialExpressionFrac * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFrac::operator MaterialExpressionFrac^ (const cross::MaterialExpressionFrac* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFrac(const_cast<cross::MaterialExpressionFrac*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFrac^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionFresnel export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionFresnel::m_ExponentIn::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_ExponentIn)) , true);
 }
 void MaterialExpressionFresnel::m_ExponentIn::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_ExponentIn = value;
 }

Clicross::ExpressionInput^ MaterialExpressionFresnel::m_BaseReflectFractionIn::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_BaseReflectFractionIn)) , true);
 }
 void MaterialExpressionFresnel::m_BaseReflectFractionIn::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_BaseReflectFractionIn = value;
 }

Clicross::ExpressionInput^ MaterialExpressionFresnel::m_Normal::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Normal)) , true);
 }
 void MaterialExpressionFresnel::m_Normal::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Normal = value;
 }

float MaterialExpressionFresnel::m_Exponent::get()
 {
	return (static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Exponent;
 }
 void MaterialExpressionFresnel::m_Exponent::set(float value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Exponent = value;
 }

float MaterialExpressionFresnel::m_BaseReflectFraction::get()
 {
	return (static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_BaseReflectFraction;
 }
 void MaterialExpressionFresnel::m_BaseReflectFraction::set(float value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_BaseReflectFraction = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionFresnel::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionFresnel::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionFresnel*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionFresnel::MaterialExpressionFresnel(const cross::MaterialExpressionFresnel * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionFresnel::operator MaterialExpressionFresnel^ (const cross::MaterialExpressionFresnel* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionFresnel(const_cast<cross::MaterialExpressionFresnel*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionFresnel^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGetMaterialAttributes export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionGetMaterialAttributes::m_AttributesOutputs export start
	#define STLDECL_MANAGEDTYPE Clicross::AttributeExpressionOutput^
	#define STLDECL_NATIVETYPE cross::AttributeExpressionOutput
	CPP_DECLARE_STLVECTOR(MaterialExpressionGetMaterialAttributes::, m_AttributesOutputsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
Clicross::ExpressionAttributesInput^ MaterialExpressionGetMaterialAttributes::m_MaterialAttributesInput::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_MaterialAttributesInput)) , true);
 }
 void MaterialExpressionGetMaterialAttributes::m_MaterialAttributesInput::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_MaterialAttributesInput = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionGetMaterialAttributes::m_MaterialAttributesOutput::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_MaterialAttributesOutput)) , true);
 }
 void MaterialExpressionGetMaterialAttributes::m_MaterialAttributesOutput::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_MaterialAttributesOutput = value;
 }

MaterialExpressionGetMaterialAttributes::m_AttributesOutputsCliType^ MaterialExpressionGetMaterialAttributes::m_AttributesOutputs::get()
 {
	return (static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_AttributesOutputs;
 }
 void MaterialExpressionGetMaterialAttributes::m_AttributesOutputs::set(MaterialExpressionGetMaterialAttributes::m_AttributesOutputsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionGetMaterialAttributes*>(this->_native))->m_AttributesOutputs = *value->_native;
 }


//constructor export here


MaterialExpressionGetMaterialAttributes::MaterialExpressionGetMaterialAttributes(const cross::MaterialExpressionGetMaterialAttributes * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionGetMaterialAttributes::operator MaterialExpressionGetMaterialAttributes^ (const cross::MaterialExpressionGetMaterialAttributes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGetMaterialAttributes(const_cast<cross::MaterialExpressionGetMaterialAttributes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGetMaterialAttributes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneData export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::GPUSceneDataLevel MaterialExpressionGPUSceneData::m_DataLevel::get()
 {
	return (Clicross::GPUSceneDataLevel)((int)(static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_DataLevel);
 }
 void MaterialExpressionGPUSceneData::m_DataLevel::set(Clicross::GPUSceneDataLevel value )
 {
	(static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_DataLevel = static_cast<cross::GPUSceneDataLevel>(value);
 }

System::String^ MaterialExpressionGPUSceneData::m_Type::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Type)).c_str());
 }
 void MaterialExpressionGPUSceneData::m_Type::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Type) = (ClangenCli::ToNativeString(value));
 }

System::String^ MaterialExpressionGPUSceneData::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Name)).c_str());
 }
 void MaterialExpressionGPUSceneData::m_Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionOutput^ MaterialExpressionGPUSceneData::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionGPUSceneData::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionGPUSceneData*>(this->_native))->m_Result = value;
 }


//constructor export here
MaterialExpressionGPUSceneData::MaterialExpressionGPUSceneData(UnknowKeeper^ type )
    :MaterialExpressionGPUSceneData(new cross::MaterialExpressionGPUSceneData(*(UnknowKeeper::get_native_with_type_for_pointer<std::basic_string_view<char, std::char_traits<char>>*>(type))), true)
{
}



MaterialExpressionGPUSceneData::MaterialExpressionGPUSceneData(const cross::MaterialExpressionGPUSceneData * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneData::operator MaterialExpressionGPUSceneData^ (const cross::MaterialExpressionGPUSceneData* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneData(const_cast<cross::MaterialExpressionGPUSceneData*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneData^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataFloat1 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataFloat1::MaterialExpressionGPUSceneDataFloat1( )
    :MaterialExpressionGPUSceneDataFloat1(new cross::MaterialExpressionGPUSceneDataFloat1(), true)
{
}



MaterialExpressionGPUSceneDataFloat1::MaterialExpressionGPUSceneDataFloat1(const cross::MaterialExpressionGPUSceneDataFloat1 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataFloat1::operator MaterialExpressionGPUSceneDataFloat1^ (const cross::MaterialExpressionGPUSceneDataFloat1* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataFloat1(const_cast<cross::MaterialExpressionGPUSceneDataFloat1*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataFloat1^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataFloat2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataFloat2::MaterialExpressionGPUSceneDataFloat2( )
    :MaterialExpressionGPUSceneDataFloat2(new cross::MaterialExpressionGPUSceneDataFloat2(), true)
{
}



MaterialExpressionGPUSceneDataFloat2::MaterialExpressionGPUSceneDataFloat2(const cross::MaterialExpressionGPUSceneDataFloat2 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataFloat2::operator MaterialExpressionGPUSceneDataFloat2^ (const cross::MaterialExpressionGPUSceneDataFloat2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataFloat2(const_cast<cross::MaterialExpressionGPUSceneDataFloat2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataFloat2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataFloat3 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataFloat3::MaterialExpressionGPUSceneDataFloat3( )
    :MaterialExpressionGPUSceneDataFloat3(new cross::MaterialExpressionGPUSceneDataFloat3(), true)
{
}



MaterialExpressionGPUSceneDataFloat3::MaterialExpressionGPUSceneDataFloat3(const cross::MaterialExpressionGPUSceneDataFloat3 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataFloat3::operator MaterialExpressionGPUSceneDataFloat3^ (const cross::MaterialExpressionGPUSceneDataFloat3* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataFloat3(const_cast<cross::MaterialExpressionGPUSceneDataFloat3*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataFloat3^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataFloat4 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataFloat4::MaterialExpressionGPUSceneDataFloat4( )
    :MaterialExpressionGPUSceneDataFloat4(new cross::MaterialExpressionGPUSceneDataFloat4(), true)
{
}



MaterialExpressionGPUSceneDataFloat4::MaterialExpressionGPUSceneDataFloat4(const cross::MaterialExpressionGPUSceneDataFloat4 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataFloat4::operator MaterialExpressionGPUSceneDataFloat4^ (const cross::MaterialExpressionGPUSceneDataFloat4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataFloat4(const_cast<cross::MaterialExpressionGPUSceneDataFloat4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataFloat4^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


