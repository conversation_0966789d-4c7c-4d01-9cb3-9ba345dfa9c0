//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// MaterialExpressionGPUSceneDataUInt1 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataUInt1::MaterialExpressionGPUSceneDataUInt1( )
    :MaterialExpressionGPUSceneDataUInt1(new cross::MaterialExpressionGPUSceneDataUInt1(), true)
{
}



MaterialExpressionGPUSceneDataUInt1::MaterialExpressionGPUSceneDataUInt1(const cross::MaterialExpressionGPUSceneDataUInt1 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataUInt1::operator MaterialExpressionGPUSceneDataUInt1^ (const cross::MaterialExpressionGPUSceneDataUInt1* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataUInt1(const_cast<cross::MaterialExpressionGPUSceneDataUInt1*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataUInt1^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataUInt2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataUInt2::MaterialExpressionGPUSceneDataUInt2( )
    :MaterialExpressionGPUSceneDataUInt2(new cross::MaterialExpressionGPUSceneDataUInt2(), true)
{
}



MaterialExpressionGPUSceneDataUInt2::MaterialExpressionGPUSceneDataUInt2(const cross::MaterialExpressionGPUSceneDataUInt2 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataUInt2::operator MaterialExpressionGPUSceneDataUInt2^ (const cross::MaterialExpressionGPUSceneDataUInt2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataUInt2(const_cast<cross::MaterialExpressionGPUSceneDataUInt2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataUInt2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataUInt3 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataUInt3::MaterialExpressionGPUSceneDataUInt3( )
    :MaterialExpressionGPUSceneDataUInt3(new cross::MaterialExpressionGPUSceneDataUInt3(), true)
{
}



MaterialExpressionGPUSceneDataUInt3::MaterialExpressionGPUSceneDataUInt3(const cross::MaterialExpressionGPUSceneDataUInt3 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataUInt3::operator MaterialExpressionGPUSceneDataUInt3^ (const cross::MaterialExpressionGPUSceneDataUInt3* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataUInt3(const_cast<cross::MaterialExpressionGPUSceneDataUInt3*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataUInt3^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataUInt4 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataUInt4::MaterialExpressionGPUSceneDataUInt4( )
    :MaterialExpressionGPUSceneDataUInt4(new cross::MaterialExpressionGPUSceneDataUInt4(), true)
{
}



MaterialExpressionGPUSceneDataUInt4::MaterialExpressionGPUSceneDataUInt4(const cross::MaterialExpressionGPUSceneDataUInt4 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataUInt4::operator MaterialExpressionGPUSceneDataUInt4^ (const cross::MaterialExpressionGPUSceneDataUInt4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataUInt4(const_cast<cross::MaterialExpressionGPUSceneDataUInt4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataUInt4^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataSInt1 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataSInt1::MaterialExpressionGPUSceneDataSInt1( )
    :MaterialExpressionGPUSceneDataSInt1(new cross::MaterialExpressionGPUSceneDataSInt1(), true)
{
}



MaterialExpressionGPUSceneDataSInt1::MaterialExpressionGPUSceneDataSInt1(const cross::MaterialExpressionGPUSceneDataSInt1 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataSInt1::operator MaterialExpressionGPUSceneDataSInt1^ (const cross::MaterialExpressionGPUSceneDataSInt1* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataSInt1(const_cast<cross::MaterialExpressionGPUSceneDataSInt1*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataSInt1^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataSInt2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataSInt2::MaterialExpressionGPUSceneDataSInt2( )
    :MaterialExpressionGPUSceneDataSInt2(new cross::MaterialExpressionGPUSceneDataSInt2(), true)
{
}



MaterialExpressionGPUSceneDataSInt2::MaterialExpressionGPUSceneDataSInt2(const cross::MaterialExpressionGPUSceneDataSInt2 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataSInt2::operator MaterialExpressionGPUSceneDataSInt2^ (const cross::MaterialExpressionGPUSceneDataSInt2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataSInt2(const_cast<cross::MaterialExpressionGPUSceneDataSInt2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataSInt2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataSInt3 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataSInt3::MaterialExpressionGPUSceneDataSInt3( )
    :MaterialExpressionGPUSceneDataSInt3(new cross::MaterialExpressionGPUSceneDataSInt3(), true)
{
}



MaterialExpressionGPUSceneDataSInt3::MaterialExpressionGPUSceneDataSInt3(const cross::MaterialExpressionGPUSceneDataSInt3 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataSInt3::operator MaterialExpressionGPUSceneDataSInt3^ (const cross::MaterialExpressionGPUSceneDataSInt3* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataSInt3(const_cast<cross::MaterialExpressionGPUSceneDataSInt3*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataSInt3^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataSInt4 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataSInt4::MaterialExpressionGPUSceneDataSInt4( )
    :MaterialExpressionGPUSceneDataSInt4(new cross::MaterialExpressionGPUSceneDataSInt4(), true)
{
}



MaterialExpressionGPUSceneDataSInt4::MaterialExpressionGPUSceneDataSInt4(const cross::MaterialExpressionGPUSceneDataSInt4 * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataSInt4::operator MaterialExpressionGPUSceneDataSInt4^ (const cross::MaterialExpressionGPUSceneDataSInt4* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataSInt4(const_cast<cross::MaterialExpressionGPUSceneDataSInt4*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataSInt4^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionGPUSceneDataBool export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionGPUSceneDataBool::MaterialExpressionGPUSceneDataBool( )
    :MaterialExpressionGPUSceneDataBool(new cross::MaterialExpressionGPUSceneDataBool(), true)
{
}



MaterialExpressionGPUSceneDataBool::MaterialExpressionGPUSceneDataBool(const cross::MaterialExpressionGPUSceneDataBool * obj, bool created_by_clr): Clicross::MaterialExpressionGPUSceneData(obj, created_by_clr)
{
}

MaterialExpressionGPUSceneDataBool::operator MaterialExpressionGPUSceneDataBool^ (const cross::MaterialExpressionGPUSceneDataBool* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionGPUSceneDataBool(const_cast<cross::MaterialExpressionGPUSceneDataBool*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionGPUSceneDataBool^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionIf export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionIf::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionIf::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionIf::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionIf::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_B = value;
 }

Clicross::ExpressionInput^ MaterialExpressionIf::m_AGreaterThanB::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_AGreaterThanB)) , true);
 }
 void MaterialExpressionIf::m_AGreaterThanB::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_AGreaterThanB = value;
 }

Clicross::ExpressionInput^ MaterialExpressionIf::m_AEqualsB::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_AEqualsB)) , true);
 }
 void MaterialExpressionIf::m_AEqualsB::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_AEqualsB = value;
 }

Clicross::ExpressionInput^ MaterialExpressionIf::m_ALessThanB::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_ALessThanB)) , true);
 }
 void MaterialExpressionIf::m_ALessThanB::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_ALessThanB = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionIf::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionIf*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionIf::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_Result = value;
 }

float MaterialExpressionIf::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionIf*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionIf::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionIf*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionIf::MaterialExpressionIf(const cross::MaterialExpressionIf * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionIf::operator MaterialExpressionIf^ (const cross::MaterialExpressionIf* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionIf(const_cast<cross::MaterialExpressionIf*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionIf^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionLerp export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionLerp::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionLerp::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionLerp::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionLerp::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_B = value;
 }

Clicross::ExpressionInput^ MaterialExpressionLerp::m_Alpha::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_Alpha)) , true);
 }
 void MaterialExpressionLerp::m_Alpha::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_Alpha = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionLerp::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionLerp::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_Result = value;
 }

float MaterialExpressionLerp::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionLerp::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionLerp::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionLerp::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstB = value;
 }

float MaterialExpressionLerp::m_ConstAlpha::get()
 {
	return (static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstAlpha;
 }
 void MaterialExpressionLerp::m_ConstAlpha::set(float value )
 {
	(static_cast<cross::MaterialExpressionLerp*>(this->_native))->m_ConstAlpha = value;
 }


//constructor export here


MaterialExpressionLerp::MaterialExpressionLerp(const cross::MaterialExpressionLerp * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionLerp::operator MaterialExpressionLerp^ (const cross::MaterialExpressionLerp* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionLerp(const_cast<cross::MaterialExpressionLerp*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionLerp^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionLogarithm10 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionLogarithm10::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLogarithm10*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionLogarithm10::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLogarithm10*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionLogarithm10::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionLogarithm10*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionLogarithm10::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionLogarithm10*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionLogarithm10::MaterialExpressionLogarithm10(const cross::MaterialExpressionLogarithm10 * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionLogarithm10::operator MaterialExpressionLogarithm10^ (const cross::MaterialExpressionLogarithm10* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionLogarithm10(const_cast<cross::MaterialExpressionLogarithm10*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionLogarithm10^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionLogarithm2 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionLogarithm2::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLogarithm2*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionLogarithm2::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLogarithm2*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionLogarithm2::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionLogarithm2*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionLogarithm2::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionLogarithm2*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionLogarithm2::MaterialExpressionLogarithm2(const cross::MaterialExpressionLogarithm2 * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionLogarithm2::operator MaterialExpressionLogarithm2^ (const cross::MaterialExpressionLogarithm2* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionLogarithm2(const_cast<cross::MaterialExpressionLogarithm2*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionLogarithm2^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionLuminance export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionLuminance::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionLuminance::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_Input = value;
 }

Clicross::Float3^ MaterialExpressionLuminance::m_LuminanceFactors::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_LuminanceFactors)) , true);
 }
 void MaterialExpressionLuminance::m_LuminanceFactors::set(Clicross::Float3^ value )
 {
	(static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_LuminanceFactors = value;
 }

Clicross::MaterialLuminanceMode MaterialExpressionLuminance::m_LuminanceMode::get()
 {
	return (Clicross::MaterialLuminanceMode)((int)(static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_LuminanceMode);
 }
 void MaterialExpressionLuminance::m_LuminanceMode::set(Clicross::MaterialLuminanceMode value )
 {
	(static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_LuminanceMode = static_cast<cross::MaterialLuminanceMode>(value);
 }

Clicross::ExpressionOutput^ MaterialExpressionLuminance::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionLuminance::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionLuminance*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionLuminance::MaterialExpressionLuminance(const cross::MaterialExpressionLuminance * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionLuminance::operator MaterialExpressionLuminance^ (const cross::MaterialExpressionLuminance* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionLuminance(const_cast<cross::MaterialExpressionLuminance*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionLuminance^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionMakeMaterialAttributes export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_BaseColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_BaseColor)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_BaseColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_BaseColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Metallic::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Metallic)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Metallic::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Metallic = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Specular::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Specular)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Specular::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Specular = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Roughness::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Roughness)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Roughness::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Roughness = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::Anisotropy::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->Anisotropy)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::Anisotropy::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->Anisotropy = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_EmissiveColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_EmissiveColor)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_EmissiveColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_EmissiveColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Opacity::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Opacity)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Opacity::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Opacity = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_OpacityMask::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_OpacityMask)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_OpacityMask::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_OpacityMask = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Normal::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Normal)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Normal::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Normal = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_Tangent::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Tangent)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Tangent::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Tangent = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_WorldPositionOffset::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_WorldPositionOffset)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_WorldPositionOffset::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_WorldPositionOffset = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_SubsurfaceColor::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_SubsurfaceColor)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_SubsurfaceColor::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_SubsurfaceColor = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_AmbientOcclusion::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_AmbientOcclusion)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_AmbientOcclusion::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_AmbientOcclusion = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMakeMaterialAttributes::m_ShadingModel::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_ShadingModel)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_ShadingModel::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_ShadingModel = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionMakeMaterialAttributes::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionMakeMaterialAttributes::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionMakeMaterialAttributes*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionMakeMaterialAttributes::MaterialExpressionMakeMaterialAttributes(const cross::MaterialExpressionMakeMaterialAttributes * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionMakeMaterialAttributes::operator MaterialExpressionMakeMaterialAttributes^ (const cross::MaterialExpressionMakeMaterialAttributes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionMakeMaterialAttributes(const_cast<cross::MaterialExpressionMakeMaterialAttributes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionMakeMaterialAttributes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionMaterialParameterCollection export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionMaterialParameterCollection::m_MpcFile::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_MpcFile)).c_str());
 }
 void MaterialExpressionMaterialParameterCollection::m_MpcFile::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_MpcFile) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionOutput^ MaterialExpressionMaterialParameterCollection::m_Output::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_Output)) , true);
 }
 void MaterialExpressionMaterialParameterCollection::m_Output::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_Output = value;
 }

Clicross::MpcDynamicEnum^ MaterialExpressionMaterialParameterCollection::m_MpcEnum::get()
 {
	return gcnew Clicross::MpcDynamicEnum(new cross::MpcDynamicEnum(((static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_MpcEnum)) , true);
 }
 void MaterialExpressionMaterialParameterCollection::m_MpcEnum::set(Clicross::MpcDynamicEnum^ value )
 {
	(static_cast<cross::MaterialExpressionMaterialParameterCollection*>(this->_native))->m_MpcEnum = value;
 }


//constructor export here


MaterialExpressionMaterialParameterCollection::MaterialExpressionMaterialParameterCollection(const cross::MaterialExpressionMaterialParameterCollection * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionMaterialParameterCollection::operator MaterialExpressionMaterialParameterCollection^ (const cross::MaterialExpressionMaterialParameterCollection* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionMaterialParameterCollection(const_cast<cross::MaterialExpressionMaterialParameterCollection*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionMaterialParameterCollection^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionMax export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionMax::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMax*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionMax::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMax*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMax::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMax*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionMax::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMax*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionMax::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionMax*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionMax::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionMax*>(this->_native))->m_Result = value;
 }

float MaterialExpressionMax::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionMax*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionMax::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionMax*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionMax::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionMax*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionMax::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionMax*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionMax::MaterialExpressionMax(const cross::MaterialExpressionMax * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionMax::operator MaterialExpressionMax^ (const cross::MaterialExpressionMax* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionMax(const_cast<cross::MaterialExpressionMax*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionMax^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionMin export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionMin::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMin*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionMin::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMin*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMin::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMin*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionMin::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMin*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionMin::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionMin*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionMin::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionMin*>(this->_native))->m_Result = value;
 }

float MaterialExpressionMin::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionMin*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionMin::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionMin*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionMin::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionMin*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionMin::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionMin*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionMin::MaterialExpressionMin(const cross::MaterialExpressionMin * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionMin::operator MaterialExpressionMin^ (const cross::MaterialExpressionMin* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionMin(const_cast<cross::MaterialExpressionMin*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionMin^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionMultiply export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionMultiply::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionMultiply::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionMultiply::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionMultiply::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionMultiply::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionMultiply::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_Result = value;
 }

float MaterialExpressionMultiply::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionMultiply::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionMultiply::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionMultiply::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionMultiply*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionMultiply::MaterialExpressionMultiply(const cross::MaterialExpressionMultiply * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionMultiply::operator MaterialExpressionMultiply^ (const cross::MaterialExpressionMultiply* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionMultiply(const_cast<cross::MaterialExpressionMultiply*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionMultiply^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionNamedRerouteDeclaration export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionNamedRerouteDeclaration::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionNamedRerouteDeclaration::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionNamedRerouteDeclaration::m_Output::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Output)) , true);
 }
 void MaterialExpressionNamedRerouteDeclaration::m_Output::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Output = value;
 }

System::String^ MaterialExpressionNamedRerouteDeclaration::m_Name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Name)).c_str());
 }
 void MaterialExpressionNamedRerouteDeclaration::m_Name::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_Name) = (ClangenCli::ToNativeString(value));
 }

Clicross::Float4^ MaterialExpressionNamedRerouteDeclaration::m_NodeColor::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_NodeColor)) , true);
 }
 void MaterialExpressionNamedRerouteDeclaration::m_NodeColor::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_NodeColor = value;
 }

System::String^ MaterialExpressionNamedRerouteDeclaration::m_VariableGuid::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_VariableGuid)).c_str());
 }
 void MaterialExpressionNamedRerouteDeclaration::m_VariableGuid::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(this->_native))->m_VariableGuid) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
MaterialExpressionNamedRerouteDeclaration::MaterialExpressionNamedRerouteDeclaration( )
    :MaterialExpressionNamedRerouteDeclaration(new cross::MaterialExpressionNamedRerouteDeclaration(), true)
{
}



MaterialExpressionNamedRerouteDeclaration::MaterialExpressionNamedRerouteDeclaration(const cross::MaterialExpressionNamedRerouteDeclaration * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionNamedRerouteDeclaration::operator MaterialExpressionNamedRerouteDeclaration^ (const cross::MaterialExpressionNamedRerouteDeclaration* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionNamedRerouteDeclaration(const_cast<cross::MaterialExpressionNamedRerouteDeclaration*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionNamedRerouteDeclaration^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionNamedRerouteUsage export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionNamedRerouteUsage::m_DeclarationGuid::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionNamedRerouteUsage*>(this->_native))->m_DeclarationGuid)).c_str());
 }
 void MaterialExpressionNamedRerouteUsage::m_DeclarationGuid::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionNamedRerouteUsage*>(this->_native))->m_DeclarationGuid) = (ClangenCli::ToNativeString(value));
 }

Clicross::ExpressionOutput^ MaterialExpressionNamedRerouteUsage::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionNamedRerouteUsage*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionNamedRerouteUsage::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionNamedRerouteUsage*>(this->_native))->m_Result = value;
 }


//constructor export here
MaterialExpressionNamedRerouteUsage::MaterialExpressionNamedRerouteUsage( )
    :MaterialExpressionNamedRerouteUsage(new cross::MaterialExpressionNamedRerouteUsage(), true)
{
}

MaterialExpressionNamedRerouteUsage::MaterialExpressionNamedRerouteUsage(Clicross::MaterialExpressionNamedRerouteDeclaration^ declaration )
    :MaterialExpressionNamedRerouteUsage(new cross::MaterialExpressionNamedRerouteUsage(( cross::MaterialExpressionNamedRerouteDeclaration* )(declaration)), true)
{
}



MaterialExpressionNamedRerouteUsage::MaterialExpressionNamedRerouteUsage(const cross::MaterialExpressionNamedRerouteUsage * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionNamedRerouteUsage::operator MaterialExpressionNamedRerouteUsage^ (const cross::MaterialExpressionNamedRerouteUsage* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionNamedRerouteUsage(const_cast<cross::MaterialExpressionNamedRerouteUsage*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionNamedRerouteUsage^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionNormalize export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionNormalize::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionNormalize*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionNormalize::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionNormalize*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionNormalize::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionNormalize*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionNormalize::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionNormalize*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionNormalize::MaterialExpressionNormalize(const cross::MaterialExpressionNormalize * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionNormalize::operator MaterialExpressionNormalize^ (const cross::MaterialExpressionNormalize* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionNormalize(const_cast<cross::MaterialExpressionNormalize*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionNormalize^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionObjectLocalBounds export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionObjectLocalBounds::m_HalfExtent::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_HalfExtent)) , true);
 }
 void MaterialExpressionObjectLocalBounds::m_HalfExtent::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_HalfExtent = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionObjectLocalBounds::m_FullExtent::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_FullExtent)) , true);
 }
 void MaterialExpressionObjectLocalBounds::m_FullExtent::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_FullExtent = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionObjectLocalBounds::m_Min::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_Min)) , true);
 }
 void MaterialExpressionObjectLocalBounds::m_Min::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_Min = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionObjectLocalBounds::m_Max::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_Max)) , true);
 }
 void MaterialExpressionObjectLocalBounds::m_Max::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionObjectLocalBounds*>(this->_native))->m_Max = value;
 }


//constructor export here


MaterialExpressionObjectLocalBounds::MaterialExpressionObjectLocalBounds(const cross::MaterialExpressionObjectLocalBounds * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionObjectLocalBounds::operator MaterialExpressionObjectLocalBounds^ (const cross::MaterialExpressionObjectLocalBounds* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionObjectLocalBounds(const_cast<cross::MaterialExpressionObjectLocalBounds*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionObjectLocalBounds^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionObjectPositionWS export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionObjectPositionWS::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionObjectPositionWS*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionObjectPositionWS::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionObjectPositionWS*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionObjectPositionWS::MaterialExpressionObjectPositionWS(const cross::MaterialExpressionObjectPositionWS * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionObjectPositionWS::operator MaterialExpressionObjectPositionWS^ (const cross::MaterialExpressionObjectPositionWS* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionObjectPositionWS(const_cast<cross::MaterialExpressionObjectPositionWS*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionObjectPositionWS^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionOneMinus export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionOneMinus::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionOneMinus*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionOneMinus::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionOneMinus*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionOneMinus::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionOneMinus*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionOneMinus::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionOneMinus*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionOneMinus::MaterialExpressionOneMinus(const cross::MaterialExpressionOneMinus * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionOneMinus::operator MaterialExpressionOneMinus^ (const cross::MaterialExpressionOneMinus* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionOneMinus(const_cast<cross::MaterialExpressionOneMinus*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionOneMinus^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionPanner export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionPanner::m_Coordinate::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Coordinate)) , true);
 }
 void MaterialExpressionPanner::m_Coordinate::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Coordinate = value;
 }

Clicross::ExpressionInput^ MaterialExpressionPanner::m_Time::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Time)) , true);
 }
 void MaterialExpressionPanner::m_Time::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Time = value;
 }

Clicross::ExpressionInput^ MaterialExpressionPanner::m_Speed::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Speed)) , true);
 }
 void MaterialExpressionPanner::m_Speed::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Speed = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionPanner::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionPanner::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_Result = value;
 }

float MaterialExpressionPanner::m_SpeedX::get()
 {
	return (static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_SpeedX;
 }
 void MaterialExpressionPanner::m_SpeedX::set(float value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_SpeedX = value;
 }

float MaterialExpressionPanner::m_SpeedY::get()
 {
	return (static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_SpeedY;
 }
 void MaterialExpressionPanner::m_SpeedY::set(float value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_SpeedY = value;
 }

bool MaterialExpressionPanner::m_bFractionalPart::get()
 {
	return (static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_bFractionalPart;
 }
 void MaterialExpressionPanner::m_bFractionalPart::set(bool value )
 {
	(static_cast<cross::MaterialExpressionPanner*>(this->_native))->m_bFractionalPart = value;
 }


//constructor export here


MaterialExpressionPanner::MaterialExpressionPanner(const cross::MaterialExpressionPanner * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionPanner::operator MaterialExpressionPanner^ (const cross::MaterialExpressionPanner* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionPanner(const_cast<cross::MaterialExpressionPanner*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionPanner^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticleData export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionParticleData::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleData*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionParticleData::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleData*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionParticleData::MaterialExpressionParticleData(const cross::MaterialExpressionParticleData * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionParticleData::operator MaterialExpressionParticleData^ (const cross::MaterialExpressionParticleData* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleData(const_cast<cross::MaterialExpressionParticleData*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleData^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticleUVScale export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionParticleUVScale::MaterialExpressionParticleUVScale( )
    :MaterialExpressionParticleUVScale(new cross::MaterialExpressionParticleUVScale(), true)
{
}



MaterialExpressionParticleUVScale::MaterialExpressionParticleUVScale(const cross::MaterialExpressionParticleUVScale * obj, bool created_by_clr): Clicross::MaterialExpressionParticleData(obj, created_by_clr)
{
}

MaterialExpressionParticleUVScale::operator MaterialExpressionParticleUVScale^ (const cross::MaterialExpressionParticleUVScale* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleUVScale(const_cast<cross::MaterialExpressionParticleUVScale*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleUVScale^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticleAnimatedVelocity export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionParticleAnimatedVelocity::MaterialExpressionParticleAnimatedVelocity( )
    :MaterialExpressionParticleAnimatedVelocity(new cross::MaterialExpressionParticleAnimatedVelocity(), true)
{
}



MaterialExpressionParticleAnimatedVelocity::MaterialExpressionParticleAnimatedVelocity(const cross::MaterialExpressionParticleAnimatedVelocity * obj, bool created_by_clr): Clicross::MaterialExpressionParticleData(obj, created_by_clr)
{
}

MaterialExpressionParticleAnimatedVelocity::operator MaterialExpressionParticleAnimatedVelocity^ (const cross::MaterialExpressionParticleAnimatedVelocity* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleAnimatedVelocity(const_cast<cross::MaterialExpressionParticleAnimatedVelocity*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleAnimatedVelocity^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticlePosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionParticlePosition::MaterialExpressionParticlePosition( )
    :MaterialExpressionParticlePosition(new cross::MaterialExpressionParticlePosition(), true)
{
}



MaterialExpressionParticlePosition::MaterialExpressionParticlePosition(const cross::MaterialExpressionParticlePosition * obj, bool created_by_clr): Clicross::MaterialExpressionParticleData(obj, created_by_clr)
{
}

MaterialExpressionParticlePosition::operator MaterialExpressionParticlePosition^ (const cross::MaterialExpressionParticlePosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticlePosition(const_cast<cross::MaterialExpressionParticlePosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticlePosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticleRotation export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionParticleRotation::MaterialExpressionParticleRotation( )
    :MaterialExpressionParticleRotation(new cross::MaterialExpressionParticleRotation(), true)
{
}



MaterialExpressionParticleRotation::MaterialExpressionParticleRotation(const cross::MaterialExpressionParticleRotation * obj, bool created_by_clr): Clicross::MaterialExpressionParticleData(obj, created_by_clr)
{
}

MaterialExpressionParticleRotation::operator MaterialExpressionParticleRotation^ (const cross::MaterialExpressionParticleRotation* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleRotation(const_cast<cross::MaterialExpressionParticleRotation*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleRotation^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionParticleSizeScale export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialExpressionParticleSizeScale::MaterialExpressionParticleSizeScale( )
    :MaterialExpressionParticleSizeScale(new cross::MaterialExpressionParticleSizeScale(), true)
{
}



MaterialExpressionParticleSizeScale::MaterialExpressionParticleSizeScale(const cross::MaterialExpressionParticleSizeScale * obj, bool created_by_clr): Clicross::MaterialExpressionParticleData(obj, created_by_clr)
{
}

MaterialExpressionParticleSizeScale::operator MaterialExpressionParticleSizeScale^ (const cross::MaterialExpressionParticleSizeScale* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleSizeScale(const_cast<cross::MaterialExpressionParticleSizeScale*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleSizeScale^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


