//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// MaterialExpressionParticleColor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_RGB::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_RGB)) , true);
 }
 void MaterialExpressionParticleColor::m_RGB::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_RGB = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_R::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_R)) , true);
 }
 void MaterialExpressionParticleColor::m_R::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_R = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_G::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_G)) , true);
 }
 void MaterialExpressionParticleColor::m_G::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_G = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_B::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionParticleColor::m_B::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_A::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionParticleColor::m_A::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_A = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionParticleColor::m_RGBA::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_RGBA)) , true);
 }
 void MaterialExpressionParticleColor::m_RGBA::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionParticleColor*>(this->_native))->m_RGBA = value;
 }


//constructor export here


MaterialExpressionParticleColor::MaterialExpressionParticleColor(const cross::MaterialExpressionParticleColor * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionParticleColor::operator MaterialExpressionParticleColor^ (const cross::MaterialExpressionParticleColor* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionParticleColor(const_cast<cross::MaterialExpressionParticleColor*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionParticleColor^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionPixelDepth export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionPixelDepth::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionPixelDepth*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionPixelDepth::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionPixelDepth*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionPixelDepth::MaterialExpressionPixelDepth(const cross::MaterialExpressionPixelDepth * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionPixelDepth::operator MaterialExpressionPixelDepth^ (const cross::MaterialExpressionPixelDepth* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionPixelDepth(const_cast<cross::MaterialExpressionPixelDepth*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionPixelDepth^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionPixelLinearDepth export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionPixelLinearDepth::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionPixelLinearDepth*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionPixelLinearDepth::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionPixelLinearDepth*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionPixelLinearDepth::MaterialExpressionPixelLinearDepth(const cross::MaterialExpressionPixelLinearDepth * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionPixelLinearDepth::operator MaterialExpressionPixelLinearDepth^ (const cross::MaterialExpressionPixelLinearDepth* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionPixelLinearDepth(const_cast<cross::MaterialExpressionPixelLinearDepth*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionPixelLinearDepth^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSceneDepth export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionSceneDepth::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSceneDepth*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSceneDepth::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneDepth*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSceneDepth::MaterialExpressionSceneDepth(const cross::MaterialExpressionSceneDepth * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSceneDepth::operator MaterialExpressionSceneDepth^ (const cross::MaterialExpressionSceneDepth* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSceneDepth(const_cast<cross::MaterialExpressionSceneDepth*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSceneDepth^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSceneLinearDepth export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionSceneLinearDepth::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSceneLinearDepth*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSceneLinearDepth::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneLinearDepth*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSceneLinearDepth::MaterialExpressionSceneLinearDepth(const cross::MaterialExpressionSceneLinearDepth * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSceneLinearDepth::operator MaterialExpressionSceneLinearDepth^ (const cross::MaterialExpressionSceneLinearDepth* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSceneLinearDepth(const_cast<cross::MaterialExpressionSceneLinearDepth*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSceneLinearDepth^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionPixelNormalWS export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionPixelNormalWS::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionPixelNormalWS*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionPixelNormalWS::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionPixelNormalWS*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionPixelNormalWS::MaterialExpressionPixelNormalWS(const cross::MaterialExpressionPixelNormalWS * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionPixelNormalWS::operator MaterialExpressionPixelNormalWS^ (const cross::MaterialExpressionPixelNormalWS* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionPixelNormalWS(const_cast<cross::MaterialExpressionPixelNormalWS*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionPixelNormalWS^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionPower export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionPower::m_Base::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Base)) , true);
 }
 void MaterialExpressionPower::m_Base::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Base = value;
 }

Clicross::ExpressionInput^ MaterialExpressionPower::m_Exponent::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Exponent)) , true);
 }
 void MaterialExpressionPower::m_Exponent::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Exponent = value;
 }

float MaterialExpressionPower::m_ConstExponent::get()
 {
	return (static_cast<cross::MaterialExpressionPower*>(this->_native))->m_ConstExponent;
 }
 void MaterialExpressionPower::m_ConstExponent::set(float value )
 {
	(static_cast<cross::MaterialExpressionPower*>(this->_native))->m_ConstExponent = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionPower::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionPower::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionPower*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionPower::MaterialExpressionPower(const cross::MaterialExpressionPower * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionPower::operator MaterialExpressionPower^ (const cross::MaterialExpressionPower* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionPower(const_cast<cross::MaterialExpressionPower*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionPower^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionRerouter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionRerouter::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRerouter*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionRerouter::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRerouter*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionRerouter::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionRerouter*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionRerouter::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionRerouter*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionRerouter::MaterialExpressionRerouter(const cross::MaterialExpressionRerouter * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionRerouter::operator MaterialExpressionRerouter^ (const cross::MaterialExpressionRerouter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionRerouter(const_cast<cross::MaterialExpressionRerouter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionRerouter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionRotateAboutAxis export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionRotateAboutAxis::m_NormalizedRotationAxis::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_NormalizedRotationAxis)) , true);
 }
 void MaterialExpressionRotateAboutAxis::m_NormalizedRotationAxis::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_NormalizedRotationAxis = value;
 }

Clicross::ExpressionInput^ MaterialExpressionRotateAboutAxis::m_RotationAngle::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_RotationAngle)) , true);
 }
 void MaterialExpressionRotateAboutAxis::m_RotationAngle::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_RotationAngle = value;
 }

Clicross::ExpressionInput^ MaterialExpressionRotateAboutAxis::m_PivotPoint::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_PivotPoint)) , true);
 }
 void MaterialExpressionRotateAboutAxis::m_PivotPoint::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_PivotPoint = value;
 }

Clicross::ExpressionInput^ MaterialExpressionRotateAboutAxis::m_Position::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Position)) , true);
 }
 void MaterialExpressionRotateAboutAxis::m_Position::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Position = value;
 }

float MaterialExpressionRotateAboutAxis::m_Period::get()
 {
	return (static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Period;
 }
 void MaterialExpressionRotateAboutAxis::m_Period::set(float value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Period = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionRotateAboutAxis::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionRotateAboutAxis::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionRotateAboutAxis*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionRotateAboutAxis::MaterialExpressionRotateAboutAxis(const cross::MaterialExpressionRotateAboutAxis * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionRotateAboutAxis::operator MaterialExpressionRotateAboutAxis^ (const cross::MaterialExpressionRotateAboutAxis* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionRotateAboutAxis(const_cast<cross::MaterialExpressionRotateAboutAxis*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionRotateAboutAxis^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionRound export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionRound::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionRound*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionRound::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionRound*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionRound::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionRound*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionRound::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionRound*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionRound::MaterialExpressionRound(const cross::MaterialExpressionRound * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionRound::operator MaterialExpressionRound^ (const cross::MaterialExpressionRound* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionRound(const_cast<cross::MaterialExpressionRound*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionRound^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSaturate export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSaturate::m_Value::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSaturate*>(this->_native))->m_Value)) , true);
 }
 void MaterialExpressionSaturate::m_Value::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSaturate*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSaturate::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSaturate*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSaturate::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSaturate*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSaturate::MaterialExpressionSaturate(const cross::MaterialExpressionSaturate * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSaturate::operator MaterialExpressionSaturate^ (const cross::MaterialExpressionSaturate* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSaturate(const_cast<cross::MaterialExpressionSaturate*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSaturate^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionScalarParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionScalarParameter::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionScalarParameter::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_Result = value;
 }

float MaterialExpressionScalarParameter::m_DefaultValue::get()
 {
	return (static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_DefaultValue;
 }
 void MaterialExpressionScalarParameter::m_DefaultValue::set(float value )
 {
	(static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_DefaultValue = value;
 }

float MaterialExpressionScalarParameter::m_SliderMin::get()
 {
	return (static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_SliderMin;
 }
 void MaterialExpressionScalarParameter::m_SliderMin::set(float value )
 {
	(static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_SliderMin = value;
 }

float MaterialExpressionScalarParameter::m_SliderMax::get()
 {
	return (static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_SliderMax;
 }
 void MaterialExpressionScalarParameter::m_SliderMax::set(float value )
 {
	(static_cast<cross::MaterialExpressionScalarParameter*>(this->_native))->m_SliderMax = value;
 }


//constructor export here


MaterialExpressionScalarParameter::MaterialExpressionScalarParameter(const cross::MaterialExpressionScalarParameter * obj, bool created_by_clr): Clicross::MaterialExpressionParameter(obj, created_by_clr)
{
}

MaterialExpressionScalarParameter::operator MaterialExpressionScalarParameter^ (const cross::MaterialExpressionScalarParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionScalarParameter(const_cast<cross::MaterialExpressionScalarParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionScalarParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSceneTexture export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSceneTexture::m_Coordinates::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Coordinates)) , true);
 }
 void MaterialExpressionSceneTexture::m_Coordinates::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Coordinates = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSceneTexture::m_Color::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Color)) , true);
 }
 void MaterialExpressionSceneTexture::m_Color::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Color = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSceneTexture::m_Size::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Size)) , true);
 }
 void MaterialExpressionSceneTexture::m_Size::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Size = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSceneTexture::m_InvSize::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_InvSize)) , true);
 }
 void MaterialExpressionSceneTexture::m_InvSize::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_InvSize = value;
 }

Clicross::SceneTextureId MaterialExpressionSceneTexture::m_SceneTextureId::get()
 {
	return (Clicross::SceneTextureId)((int)(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_SceneTextureId);
 }
 void MaterialExpressionSceneTexture::m_SceneTextureId::set(Clicross::SceneTextureId value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_SceneTextureId = static_cast<cross::SceneTextureId>(value);
 }

bool MaterialExpressionSceneTexture::m_Filtered::get()
 {
	return (static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Filtered;
 }
 void MaterialExpressionSceneTexture::m_Filtered::set(bool value )
 {
	(static_cast<cross::MaterialExpressionSceneTexture*>(this->_native))->m_Filtered = value;
 }


//constructor export here


MaterialExpressionSceneTexture::MaterialExpressionSceneTexture(const cross::MaterialExpressionSceneTexture * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSceneTexture::operator MaterialExpressionSceneTexture^ (const cross::MaterialExpressionSceneTexture* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSceneTexture(const_cast<cross::MaterialExpressionSceneTexture*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSceneTexture^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionScreenPosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionScreenPosition::m_ViewportUV::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionScreenPosition*>(this->_native))->m_ViewportUV)) , true);
 }
 void MaterialExpressionScreenPosition::m_ViewportUV::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionScreenPosition*>(this->_native))->m_ViewportUV = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionScreenPosition::m_PixelPosition::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionScreenPosition*>(this->_native))->m_PixelPosition)) , true);
 }
 void MaterialExpressionScreenPosition::m_PixelPosition::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionScreenPosition*>(this->_native))->m_PixelPosition = value;
 }


//constructor export here


MaterialExpressionScreenPosition::MaterialExpressionScreenPosition(const cross::MaterialExpressionScreenPosition * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionScreenPosition::operator MaterialExpressionScreenPosition^ (const cross::MaterialExpressionScreenPosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionScreenPosition(const_cast<cross::MaterialExpressionScreenPosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionScreenPosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionScreenUV export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionScreenUV::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionScreenUV*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionScreenUV::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionScreenUV*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionScreenUV::MaterialExpressionScreenUV(const cross::MaterialExpressionScreenUV * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionScreenUV::operator MaterialExpressionScreenUV^ (const cross::MaterialExpressionScreenUV* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionScreenUV(const_cast<cross::MaterialExpressionScreenUV*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionScreenUV^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSetMaterialAttributes export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionSetMaterialAttributes::m_AttributesInputs export start
	#define STLDECL_MANAGEDTYPE Clicross::AttributeExpressionInput^
	#define STLDECL_NATIVETYPE cross::AttributeExpressionInput
	CPP_DECLARE_STLVECTOR(MaterialExpressionSetMaterialAttributes::, m_AttributesInputsCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
Clicross::ExpressionAttributesInput^ MaterialExpressionSetMaterialAttributes::m_MaterialAttributes::get()
 {
	return gcnew Clicross::ExpressionAttributesInput(new cross::ExpressionAttributesInput(((static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_MaterialAttributes)) , true);
 }
 void MaterialExpressionSetMaterialAttributes::m_MaterialAttributes::set(Clicross::ExpressionAttributesInput^ value )
 {
	(static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_MaterialAttributes = value;
 }

MaterialExpressionSetMaterialAttributes::m_AttributesInputsCliType^ MaterialExpressionSetMaterialAttributes::m_AttributesInputs::get()
 {
	return (static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_AttributesInputs;
 }
 void MaterialExpressionSetMaterialAttributes::m_AttributesInputs::set(MaterialExpressionSetMaterialAttributes::m_AttributesInputsCliType^ value )
 {
	(static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_AttributesInputs = *value->_native;
 }

Clicross::ExpressionOutput^ MaterialExpressionSetMaterialAttributes::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSetMaterialAttributes::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSetMaterialAttributes*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSetMaterialAttributes::MaterialExpressionSetMaterialAttributes(const cross::MaterialExpressionSetMaterialAttributes * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSetMaterialAttributes::operator MaterialExpressionSetMaterialAttributes^ (const cross::MaterialExpressionSetMaterialAttributes* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSetMaterialAttributes(const_cast<cross::MaterialExpressionSetMaterialAttributes*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSetMaterialAttributes^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionShaderConstBool export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool MaterialExpressionShaderConstBool::m_Value::get()
 {
	return (static_cast<cross::MaterialExpressionShaderConstBool*>(this->_native))->m_Value;
 }
 void MaterialExpressionShaderConstBool::m_Value::set(bool value )
 {
	(static_cast<cross::MaterialExpressionShaderConstBool*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionShaderConstBool::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionShaderConstBool*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionShaderConstBool::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionShaderConstBool*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionShaderConstBool::MaterialExpressionShaderConstBool(const cross::MaterialExpressionShaderConstBool * obj, bool created_by_clr): Clicross::MaterialExpressionParameter(obj, created_by_clr)
{
}

MaterialExpressionShaderConstBool::operator MaterialExpressionShaderConstBool^ (const cross::MaterialExpressionShaderConstBool* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionShaderConstBool(const_cast<cross::MaterialExpressionShaderConstBool*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionShaderConstBool^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionShadowReplace export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionShadowReplace::m_Default::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Default)) , true);
 }
 void MaterialExpressionShadowReplace::m_Default::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Default = value;
 }

Clicross::ExpressionInput^ MaterialExpressionShadowReplace::m_Shadow::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Shadow)) , true);
 }
 void MaterialExpressionShadowReplace::m_Shadow::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Shadow = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionShadowReplace::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionShadowReplace::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionShadowReplace*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionShadowReplace::MaterialExpressionShadowReplace(const cross::MaterialExpressionShadowReplace * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionShadowReplace::operator MaterialExpressionShadowReplace^ (const cross::MaterialExpressionShadowReplace* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionShadowReplace(const_cast<cross::MaterialExpressionShadowReplace*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionShadowReplace^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSign export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSign::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSign*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionSign::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSign*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSign::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSign*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSign::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSign*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSign::MaterialExpressionSign(const cross::MaterialExpressionSign * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSign::operator MaterialExpressionSign^ (const cross::MaterialExpressionSign* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSign(const_cast<cross::MaterialExpressionSign*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSign^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSine export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSine::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSine*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionSine::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSine*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSine::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSine*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSine::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSine*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSine::MaterialExpressionSine(const cross::MaterialExpressionSine * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSine::operator MaterialExpressionSine^ (const cross::MaterialExpressionSine* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSine(const_cast<cross::MaterialExpressionSine*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSine^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSkyAtmosphereLightDirection export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned int MaterialExpressionSkyAtmosphereLightDirection::m_LightIndex::get()
 {
	return (static_cast<cross::MaterialExpressionSkyAtmosphereLightDirection*>(this->_native))->m_LightIndex;
 }
 void MaterialExpressionSkyAtmosphereLightDirection::m_LightIndex::set(unsigned int value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightDirection*>(this->_native))->m_LightIndex = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSkyAtmosphereLightDirection::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSkyAtmosphereLightDirection*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSkyAtmosphereLightDirection::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightDirection*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSkyAtmosphereLightDirection::MaterialExpressionSkyAtmosphereLightDirection(const cross::MaterialExpressionSkyAtmosphereLightDirection * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSkyAtmosphereLightDirection::operator MaterialExpressionSkyAtmosphereLightDirection^ (const cross::MaterialExpressionSkyAtmosphereLightDirection* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSkyAtmosphereLightDirection(const_cast<cross::MaterialExpressionSkyAtmosphereLightDirection*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSkyAtmosphereLightDirection^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSkyAtmosphereLightDiskLuminance export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned int MaterialExpressionSkyAtmosphereLightDiskLuminance::m_LightIndex::get()
 {
	return (static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_LightIndex;
 }
 void MaterialExpressionSkyAtmosphereLightDiskLuminance::m_LightIndex::set(unsigned int value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_LightIndex = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSkyAtmosphereLightDiskLuminance::m_DiskAngularDiameterOverride::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_DiskAngularDiameterOverride)) , true);
 }
 void MaterialExpressionSkyAtmosphereLightDiskLuminance::m_DiskAngularDiameterOverride::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_DiskAngularDiameterOverride = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSkyAtmosphereLightDiskLuminance::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSkyAtmosphereLightDiskLuminance::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSkyAtmosphereLightDiskLuminance::MaterialExpressionSkyAtmosphereLightDiskLuminance(const cross::MaterialExpressionSkyAtmosphereLightDiskLuminance * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSkyAtmosphereLightDiskLuminance::operator MaterialExpressionSkyAtmosphereLightDiskLuminance^ (const cross::MaterialExpressionSkyAtmosphereLightDiskLuminance* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSkyAtmosphereLightDiskLuminance(const_cast<cross::MaterialExpressionSkyAtmosphereLightDiskLuminance*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSkyAtmosphereLightDiskLuminance^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSkyAtmosphereLightIlluminanceOnGround export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned int MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::m_LightIndex::get()
 {
	return (static_cast<cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround*>(this->_native))->m_LightIndex;
 }
 void MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::m_LightIndex::set(unsigned int value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround*>(this->_native))->m_LightIndex = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround(const cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::operator MaterialExpressionSkyAtmosphereLightIlluminanceOnGround^ (const cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSkyAtmosphereLightIlluminanceOnGround(const_cast<cross::MaterialExpressionSkyAtmosphereLightIlluminanceOnGround*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSkyAtmosphereLightIlluminanceOnGround^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSkyAtmosphereViewLuminance export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionSkyAtmosphereViewLuminance::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSkyAtmosphereViewLuminance*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSkyAtmosphereViewLuminance::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSkyAtmosphereViewLuminance*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSkyAtmosphereViewLuminance::MaterialExpressionSkyAtmosphereViewLuminance(const cross::MaterialExpressionSkyAtmosphereViewLuminance * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSkyAtmosphereViewLuminance::operator MaterialExpressionSkyAtmosphereViewLuminance^ (const cross::MaterialExpressionSkyAtmosphereViewLuminance* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSkyAtmosphereViewLuminance(const_cast<cross::MaterialExpressionSkyAtmosphereViewLuminance*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSkyAtmosphereViewLuminance^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSobol export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSobol::m_Cell::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Cell)) , true);
 }
 void MaterialExpressionSobol::m_Cell::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Cell = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSobol::m_Index::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Index)) , true);
 }
 void MaterialExpressionSobol::m_Index::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Index = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSobol::m_Seed::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Seed)) , true);
 }
 void MaterialExpressionSobol::m_Seed::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Seed = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSobol::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSobol::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_Result = value;
 }

float MaterialExpressionSobol::m_ConstIndex::get()
 {
	return (static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_ConstIndex;
 }
 void MaterialExpressionSobol::m_ConstIndex::set(float value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_ConstIndex = value;
 }

Clicross::Float2^ MaterialExpressionSobol::m_ConstSeed::get()
 {
	return gcnew Clicross::Float2(new cross::Float2(((static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_ConstSeed)) , true);
 }
 void MaterialExpressionSobol::m_ConstSeed::set(Clicross::Float2^ value )
 {
	(static_cast<cross::MaterialExpressionSobol*>(this->_native))->m_ConstSeed = value;
 }


//constructor export here


MaterialExpressionSobol::MaterialExpressionSobol(const cross::MaterialExpressionSobol * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSobol::operator MaterialExpressionSobol^ (const cross::MaterialExpressionSobol* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSobol(const_cast<cross::MaterialExpressionSobol*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSobol^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSphereMask export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSphereMask::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionSphereMask::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSphereMask::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionSphereMask::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_B = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSphereMask::m_Radius::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Radius)) , true);
 }
 void MaterialExpressionSphereMask::m_Radius::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Radius = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSphereMask::m_Hardness::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Hardness)) , true);
 }
 void MaterialExpressionSphereMask::m_Hardness::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Hardness = value;
 }

float MaterialExpressionSphereMask::m_AttenuationRadius::get()
 {
	return (static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_AttenuationRadius;
 }
 void MaterialExpressionSphereMask::m_AttenuationRadius::set(float value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_AttenuationRadius = value;
 }

float MaterialExpressionSphereMask::m_HardnessPercent::get()
 {
	return (static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_HardnessPercent;
 }
 void MaterialExpressionSphereMask::m_HardnessPercent::set(float value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_HardnessPercent = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSphereMask::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSphereMask::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSphereMask*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSphereMask::MaterialExpressionSphereMask(const cross::MaterialExpressionSphereMask * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSphereMask::operator MaterialExpressionSphereMask^ (const cross::MaterialExpressionSphereMask* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSphereMask(const_cast<cross::MaterialExpressionSphereMask*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSphereMask^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSquareRoot export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSquareRoot::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSquareRoot*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionSquareRoot::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSquareRoot*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSquareRoot::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSquareRoot*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSquareRoot::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSquareRoot*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionSquareRoot::MaterialExpressionSquareRoot(const cross::MaterialExpressionSquareRoot * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSquareRoot::operator MaterialExpressionSquareRoot^ (const cross::MaterialExpressionSquareRoot* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSquareRoot(const_cast<cross::MaterialExpressionSquareRoot*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSquareRoot^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionStaticBool export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool MaterialExpressionStaticBool::m_Value::get()
 {
	return (static_cast<cross::MaterialExpressionStaticBool*>(this->_native))->m_Value;
 }
 void MaterialExpressionStaticBool::m_Value::set(bool value )
 {
	(static_cast<cross::MaterialExpressionStaticBool*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionStaticBool::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionStaticBool*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionStaticBool::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionStaticBool*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionStaticBool::MaterialExpressionStaticBool(const cross::MaterialExpressionStaticBool * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionStaticBool::operator MaterialExpressionStaticBool^ (const cross::MaterialExpressionStaticBool* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionStaticBool(const_cast<cross::MaterialExpressionStaticBool*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionStaticBool^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionStaticSwitch export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionStaticSwitch::m_True::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_True)) , true);
 }
 void MaterialExpressionStaticSwitch::m_True::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_True = value;
 }

Clicross::ExpressionInput^ MaterialExpressionStaticSwitch::m_False::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_False)) , true);
 }
 void MaterialExpressionStaticSwitch::m_False::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_False = value;
 }

Clicross::ExpressionInput^ MaterialExpressionStaticSwitch::m_Value::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_Value)) , true);
 }
 void MaterialExpressionStaticSwitch::m_Value::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_Value = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionStaticSwitch::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionStaticSwitch::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_Result = value;
 }

bool MaterialExpressionStaticSwitch::m_DefaultValue::get()
 {
	return (static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_DefaultValue;
 }
 void MaterialExpressionStaticSwitch::m_DefaultValue::set(bool value )
 {
	(static_cast<cross::MaterialExpressionStaticSwitch*>(this->_native))->m_DefaultValue = value;
 }


//constructor export here


MaterialExpressionStaticSwitch::MaterialExpressionStaticSwitch(const cross::MaterialExpressionStaticSwitch * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionStaticSwitch::operator MaterialExpressionStaticSwitch^ (const cross::MaterialExpressionStaticSwitch* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionStaticSwitch(const_cast<cross::MaterialExpressionStaticSwitch*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionStaticSwitch^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionStep export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionStep::m_X::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionStep*>(this->_native))->m_X)) , true);
 }
 void MaterialExpressionStep::m_X::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionStep*>(this->_native))->m_X = value;
 }

Clicross::ExpressionInput^ MaterialExpressionStep::m_Y::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionStep*>(this->_native))->m_Y)) , true);
 }
 void MaterialExpressionStep::m_Y::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionStep*>(this->_native))->m_Y = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionStep::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionStep*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionStep::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionStep*>(this->_native))->m_Result = value;
 }

float MaterialExpressionStep::m_ConstX::get()
 {
	return (static_cast<cross::MaterialExpressionStep*>(this->_native))->m_ConstX;
 }
 void MaterialExpressionStep::m_ConstX::set(float value )
 {
	(static_cast<cross::MaterialExpressionStep*>(this->_native))->m_ConstX = value;
 }

float MaterialExpressionStep::m_ConstY::get()
 {
	return (static_cast<cross::MaterialExpressionStep*>(this->_native))->m_ConstY;
 }
 void MaterialExpressionStep::m_ConstY::set(float value )
 {
	(static_cast<cross::MaterialExpressionStep*>(this->_native))->m_ConstY = value;
 }


//constructor export here


MaterialExpressionStep::MaterialExpressionStep(const cross::MaterialExpressionStep * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionStep::operator MaterialExpressionStep^ (const cross::MaterialExpressionStep* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionStep(const_cast<cross::MaterialExpressionStep*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionStep^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionSubtract export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionSubtract::m_A::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionSubtract::m_A::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_A = value;
 }

Clicross::ExpressionInput^ MaterialExpressionSubtract::m_B::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionSubtract::m_B::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionSubtract::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionSubtract::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_Result = value;
 }

float MaterialExpressionSubtract::m_ConstA::get()
 {
	return (static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_ConstA;
 }
 void MaterialExpressionSubtract::m_ConstA::set(float value )
 {
	(static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_ConstA = value;
 }

float MaterialExpressionSubtract::m_ConstB::get()
 {
	return (static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_ConstB;
 }
 void MaterialExpressionSubtract::m_ConstB::set(float value )
 {
	(static_cast<cross::MaterialExpressionSubtract*>(this->_native))->m_ConstB = value;
 }


//constructor export here


MaterialExpressionSubtract::MaterialExpressionSubtract(const cross::MaterialExpressionSubtract * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionSubtract::operator MaterialExpressionSubtract^ (const cross::MaterialExpressionSubtract* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionSubtract(const_cast<cross::MaterialExpressionSubtract*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionSubtract^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTangent export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionTangent::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTangent*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionTangent::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTangent*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTangent::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTangent*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTangent::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTangent*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTangent::MaterialExpressionTangent(const cross::MaterialExpressionTangent * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTangent::operator MaterialExpressionTangent^ (const cross::MaterialExpressionTangent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTangent(const_cast<cross::MaterialExpressionTangent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTangent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


