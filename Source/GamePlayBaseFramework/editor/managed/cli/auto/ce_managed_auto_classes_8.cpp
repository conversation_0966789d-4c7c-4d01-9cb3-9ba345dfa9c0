//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// MaterialExpressionTerrainColor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionTerrainColor::m_Output::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTerrainColor*>(this->_native))->m_Output)) , true);
 }
 void MaterialExpressionTerrainColor::m_Output::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainColor*>(this->_native))->m_Output = value;
 }


//constructor export here


MaterialExpressionTerrainColor::MaterialExpressionTerrainColor(const cross::MaterialExpressionTerrainColor * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTerrainColor::operator MaterialExpressionTerrainColor^ (const cross::MaterialExpressionTerrainColor* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTerrainColor(const_cast<cross::MaterialExpressionTerrainColor*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTerrainColor^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// TerrainLayerBlendInput export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ TerrainLayerBlendInput::m_LayerName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_LayerName)).c_str());
 }
 void TerrainLayerBlendInput::m_LayerName::set(System::String^ value )
 {
	((static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_LayerName) = (ClangenCli::ToNativeString(value));
 }

Clicross::TerrainLayerBlendType TerrainLayerBlendInput::m_BlendType::get()
 {
	return (Clicross::TerrainLayerBlendType)((int)(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_BlendType);
 }
 void TerrainLayerBlendInput::m_BlendType::set(Clicross::TerrainLayerBlendType value )
 {
	(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_BlendType = static_cast<cross::TerrainLayerBlendType>(value);
 }

Clicross::ExpressionInput^ TerrainLayerBlendInput::m_LayerInput::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_LayerInput)) , true);
 }
 void TerrainLayerBlendInput::m_LayerInput::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_LayerInput = value;
 }

Clicross::ExpressionInput^ TerrainLayerBlendInput::m_HeightInput::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_HeightInput)) , true);
 }
 void TerrainLayerBlendInput::m_HeightInput::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_HeightInput = value;
 }

Clicross::Float4^ TerrainLayerBlendInput::m_ConstLayerInput::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_ConstLayerInput)) , true);
 }
 void TerrainLayerBlendInput::m_ConstLayerInput::set(Clicross::Float4^ value )
 {
	(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_ConstLayerInput = value;
 }

float TerrainLayerBlendInput::m_ConstHeightInput::get()
 {
	return (static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_ConstHeightInput;
 }
 void TerrainLayerBlendInput::m_ConstHeightInput::set(float value )
 {
	(static_cast<cross::TerrainLayerBlendInput*>(this->_native))->m_ConstHeightInput = value;
 }


//constructor export here
TerrainLayerBlendInput::TerrainLayerBlendInput(): TerrainLayerBlendInput(new cross::TerrainLayerBlendInput(), true) {}


TerrainLayerBlendInput::TerrainLayerBlendInput(const cross::TerrainLayerBlendInput * obj, bool created_by_clr): 
    _native(const_cast<cross::TerrainLayerBlendInput *>(obj))
	, _created_by_clr(created_by_clr)
{
}

TerrainLayerBlendInput::operator TerrainLayerBlendInput^ (const cross::TerrainLayerBlendInput* t)
{
    if(t)
    {
        return gcnew TerrainLayerBlendInput(const_cast<cross::TerrainLayerBlendInput*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTerrainLayerBlend export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::MaterialExpressionTerrainLayerBlend::m_Layers export start
	#define STLDECL_MANAGEDTYPE Clicross::TerrainLayerBlendInput^
	#define STLDECL_NATIVETYPE cross::TerrainLayerBlendInput
	CPP_DECLARE_STLVECTOR(MaterialExpressionTerrainLayerBlend::, m_LayersCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
MaterialExpressionTerrainLayerBlend::m_LayersCliType^ MaterialExpressionTerrainLayerBlend::m_Layers::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerBlend*>(this->_native))->m_Layers;
 }
 void MaterialExpressionTerrainLayerBlend::m_Layers::set(MaterialExpressionTerrainLayerBlend::m_LayersCliType^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerBlend*>(this->_native))->m_Layers = *value->_native;
 }

Clicross::ExpressionOutput^ MaterialExpressionTerrainLayerBlend::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTerrainLayerBlend*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTerrainLayerBlend::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerBlend*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTerrainLayerBlend::MaterialExpressionTerrainLayerBlend(const cross::MaterialExpressionTerrainLayerBlend * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTerrainLayerBlend::operator MaterialExpressionTerrainLayerBlend^ (const cross::MaterialExpressionTerrainLayerBlend* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTerrainLayerBlend(const_cast<cross::MaterialExpressionTerrainLayerBlend*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTerrainLayerBlend^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTerrainLayerCoords export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::TerrainCoordMappingType MaterialExpressionTerrainLayerCoords::m_MappingType::get()
 {
	return (Clicross::TerrainCoordMappingType)((int)(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingType);
 }
 void MaterialExpressionTerrainLayerCoords::m_MappingType::set(Clicross::TerrainCoordMappingType value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingType = static_cast<cross::TerrainCoordMappingType>(value);
 }

Clicross::TerrainCustomizedCoordType MaterialExpressionTerrainLayerCoords::m_CustomUVType::get()
 {
	return (Clicross::TerrainCustomizedCoordType)((int)(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_CustomUVType);
 }
 void MaterialExpressionTerrainLayerCoords::m_CustomUVType::set(Clicross::TerrainCustomizedCoordType value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_CustomUVType = static_cast<cross::TerrainCustomizedCoordType>(value);
 }

float MaterialExpressionTerrainLayerCoords::m_MappingScale::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingScale;
 }
 void MaterialExpressionTerrainLayerCoords::m_MappingScale::set(float value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingScale = value;
 }

float MaterialExpressionTerrainLayerCoords::m_MappingRotation::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingRotation;
 }
 void MaterialExpressionTerrainLayerCoords::m_MappingRotation::set(float value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingRotation = value;
 }

float MaterialExpressionTerrainLayerCoords::m_MappingPanU::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingPanU;
 }
 void MaterialExpressionTerrainLayerCoords::m_MappingPanU::set(float value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingPanU = value;
 }

float MaterialExpressionTerrainLayerCoords::m_MappingPanV::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingPanV;
 }
 void MaterialExpressionTerrainLayerCoords::m_MappingPanV::set(float value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_MappingPanV = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTerrainLayerCoords::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTerrainLayerCoords::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerCoords*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTerrainLayerCoords::MaterialExpressionTerrainLayerCoords(const cross::MaterialExpressionTerrainLayerCoords * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTerrainLayerCoords::operator MaterialExpressionTerrainLayerCoords^ (const cross::MaterialExpressionTerrainLayerCoords* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTerrainLayerCoords(const_cast<cross::MaterialExpressionTerrainLayerCoords*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTerrainLayerCoords^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTerrainLayerWeight export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionTerrainLayerWeight::m_ParameterName::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_ParameterName)).c_str());
 }
 void MaterialExpressionTerrainLayerWeight::m_ParameterName::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_ParameterName) = (ClangenCli::ToNativeString(value));
 }

unsigned int MaterialExpressionTerrainLayerWeight::m_LayerIndex::get()
 {
	return (static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_LayerIndex;
 }
 void MaterialExpressionTerrainLayerWeight::m_LayerIndex::set(unsigned int value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_LayerIndex = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTerrainLayerWeight::m_Base::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Base)) , true);
 }
 void MaterialExpressionTerrainLayerWeight::m_Base::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Base = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTerrainLayerWeight::m_Layer::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Layer)) , true);
 }
 void MaterialExpressionTerrainLayerWeight::m_Layer::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Layer = value;
 }

Clicross::Float3^ MaterialExpressionTerrainLayerWeight::m_ConstBase::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_ConstBase)) , true);
 }
 void MaterialExpressionTerrainLayerWeight::m_ConstBase::set(Clicross::Float3^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_ConstBase = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTerrainLayerWeight::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTerrainLayerWeight::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTerrainLayerWeight*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTerrainLayerWeight::MaterialExpressionTerrainLayerWeight(const cross::MaterialExpressionTerrainLayerWeight * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTerrainLayerWeight::operator MaterialExpressionTerrainLayerWeight^ (const cross::MaterialExpressionTerrainLayerWeight* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTerrainLayerWeight(const_cast<cross::MaterialExpressionTerrainLayerWeight*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTerrainLayerWeight^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTextureCoordinate export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpressionTextureCoordinate::m_CoordinateIndex::get()
 {
	return (static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_CoordinateIndex;
 }
 void MaterialExpressionTextureCoordinate::m_CoordinateIndex::set(int value )
 {
	(static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_CoordinateIndex = value;
 }

float MaterialExpressionTextureCoordinate::m_UTiling::get()
 {
	return (static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_UTiling;
 }
 void MaterialExpressionTextureCoordinate::m_UTiling::set(float value )
 {
	(static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_UTiling = value;
 }

float MaterialExpressionTextureCoordinate::m_VTiling::get()
 {
	return (static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_VTiling;
 }
 void MaterialExpressionTextureCoordinate::m_VTiling::set(float value )
 {
	(static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_VTiling = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureCoordinate::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTextureCoordinate::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureCoordinate*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTextureCoordinate::MaterialExpressionTextureCoordinate(const cross::MaterialExpressionTextureCoordinate * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTextureCoordinate::operator MaterialExpressionTextureCoordinate^ (const cross::MaterialExpressionTextureCoordinate* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTextureCoordinate(const_cast<cross::MaterialExpressionTextureCoordinate*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTextureCoordinate^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTextureParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
int MaterialExpressionTextureParameter::m_TextureType::get()
 {
	return ((int)(static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_TextureType);
 }
 void MaterialExpressionTextureParameter::m_TextureType::set(int value )
 {
	(static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_TextureType = static_cast<cross::MaterialValueType>(value);
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureParameter::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTextureParameter::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_Result = value;
 }

System::String^ MaterialExpressionTextureParameter::m_TextureString::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_TextureString)).c_str());
 }
 void MaterialExpressionTextureParameter::m_TextureString::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTextureParameter*>(this->_native))->m_TextureString) = (ClangenCli::ToNativeString(value));
 }


//constructor export here


MaterialExpressionTextureParameter::MaterialExpressionTextureParameter(const cross::MaterialExpressionTextureParameter * obj, bool created_by_clr): Clicross::MaterialExpressionParameter(obj, created_by_clr)
{
}

MaterialExpressionTextureParameter::operator MaterialExpressionTextureParameter^ (const cross::MaterialExpressionTextureParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTextureParameter(const_cast<cross::MaterialExpressionTextureParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTextureParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTextureSampleParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionTextureSampleParameter::m_UV::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_UV)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_UV::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_UV = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSampleParameter::m_Level::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_Level)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_Level::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_Level = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSampleParameter::m_Bias::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_Bias)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_Bias::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_Bias = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSampleParameter::m_DDX::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_DDX)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_DDX::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_DDX = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSampleParameter::m_DDY::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_DDY)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_DDY::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_DDY = value;
 }

System::String^ MaterialExpressionTextureSampleParameter::m_TextureString::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_TextureString)).c_str());
 }
 void MaterialExpressionTextureSampleParameter::m_TextureString::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_TextureString) = (ClangenCli::ToNativeString(value));
 }

Clicross::SamplerState^ MaterialExpressionTextureSampleParameter::m_SamplerState::get()
 {
	return gcnew Clicross::SamplerState(new cross::SamplerState(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_SamplerState)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_SamplerState::set(Clicross::SamplerState^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_SamplerState = value;
 }

int MaterialExpressionTextureSampleParameter::m_TextureType::get()
 {
	return ((int)(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_TextureType);
 }
 void MaterialExpressionTextureSampleParameter::m_TextureType::set(int value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_TextureType = static_cast<cross::MaterialValueType>(value);
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_RGB::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_RGB)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_RGB::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_RGB = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_R::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_R)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_R::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_R = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_G::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_G)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_G::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_G = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_B::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_B::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_A::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_A::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_A = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSampleParameter::m_RGBA::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_RGBA)) , true);
 }
 void MaterialExpressionTextureSampleParameter::m_RGBA::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSampleParameter*>(this->_native))->m_RGBA = value;
 }


//constructor export here


MaterialExpressionTextureSampleParameter::MaterialExpressionTextureSampleParameter(const cross::MaterialExpressionTextureSampleParameter * obj, bool created_by_clr): Clicross::MaterialExpressionParameter(obj, created_by_clr)
{
}

MaterialExpressionTextureSampleParameter::operator MaterialExpressionTextureSampleParameter^ (const cross::MaterialExpressionTextureSampleParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTextureSampleParameter(const_cast<cross::MaterialExpressionTextureSampleParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTextureSampleParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ExpressionOutputColor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ExpressionOutputColor::ExpressionOutputColor(const cross::ExpressionOutputColor * obj, bool created_by_clr): Clicross::ExpressionOutput(obj, created_by_clr)
{
}

ExpressionOutputColor::operator ExpressionOutputColor^ (const cross::ExpressionOutputColor* t)
{
    if(t)
    {
        return gcnew ExpressionOutputColor(const_cast<cross::ExpressionOutputColor*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTextureSample export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ MaterialExpressionTextureSample::m_DefaultTexture::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DefaultTexture)).c_str());
 }
 void MaterialExpressionTextureSample::m_DefaultTexture::set(System::String^ value )
 {
	((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DefaultTexture) = (ClangenCli::ToNativeString(value));
 }

Clicross::SamplerState^ MaterialExpressionTextureSample::m_SamplerState::get()
 {
	return gcnew Clicross::SamplerState(new cross::SamplerState(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_SamplerState)) , true);
 }
 void MaterialExpressionTextureSample::m_SamplerState::set(Clicross::SamplerState^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_SamplerState = value;
 }

int MaterialExpressionTextureSample::m_DefaultTextureType::get()
 {
	return ((int)(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DefaultTextureType);
 }
 void MaterialExpressionTextureSample::m_DefaultTextureType::set(int value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DefaultTextureType = static_cast<cross::MaterialValueType>(value);
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_UV::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_UV)) , true);
 }
 void MaterialExpressionTextureSample::m_UV::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_UV = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_Tex::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Tex)) , true);
 }
 void MaterialExpressionTextureSample::m_Tex::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Tex = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_Level::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Level)) , true);
 }
 void MaterialExpressionTextureSample::m_Level::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Level = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_Bias::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Bias)) , true);
 }
 void MaterialExpressionTextureSample::m_Bias::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_Bias = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_DDX::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DDX)) , true);
 }
 void MaterialExpressionTextureSample::m_DDX::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DDX = value;
 }

Clicross::ExpressionInput^ MaterialExpressionTextureSample::m_DDY::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DDY)) , true);
 }
 void MaterialExpressionTextureSample::m_DDY::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_DDY = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_RGB::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_RGB)) , true);
 }
 void MaterialExpressionTextureSample::m_RGB::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_RGB = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_R::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_R)) , true);
 }
 void MaterialExpressionTextureSample::m_R::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_R = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_G::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_G)) , true);
 }
 void MaterialExpressionTextureSample::m_G::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_G = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_B::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionTextureSample::m_B::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_A::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionTextureSample::m_A::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_A = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTextureSample::m_RGBA::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_RGBA)) , true);
 }
 void MaterialExpressionTextureSample::m_RGBA::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTextureSample*>(this->_native))->m_RGBA = value;
 }


//constructor export here


MaterialExpressionTextureSample::MaterialExpressionTextureSample(const cross::MaterialExpressionTextureSample * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTextureSample::operator MaterialExpressionTextureSample^ (const cross::MaterialExpressionTextureSample* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTextureSample(const_cast<cross::MaterialExpressionTextureSample*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTextureSample^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTilePosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionTilePosition::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTilePosition*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTilePosition::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTilePosition*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTilePosition::MaterialExpressionTilePosition(const cross::MaterialExpressionTilePosition * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTilePosition::operator MaterialExpressionTilePosition^ (const cross::MaterialExpressionTilePosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTilePosition(const_cast<cross::MaterialExpressionTilePosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTilePosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTime export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionTime::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTime*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTime::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTime*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTime::MaterialExpressionTime(const cross::MaterialExpressionTime * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTime::operator MaterialExpressionTime^ (const cross::MaterialExpressionTime* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTime(const_cast<cross::MaterialExpressionTime*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTime^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTransform export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::MaterialCommonBasis MaterialExpressionTransform::m_Source::get()
 {
	return (Clicross::MaterialCommonBasis)((int)(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Source);
 }
 void MaterialExpressionTransform::m_Source::set(Clicross::MaterialCommonBasis value )
 {
	(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Source = static_cast<cross::MaterialCommonBasis>(value);
 }

Clicross::MaterialCommonBasis MaterialExpressionTransform::m_Destination::get()
 {
	return (Clicross::MaterialCommonBasis)((int)(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Destination);
 }
 void MaterialExpressionTransform::m_Destination::set(Clicross::MaterialCommonBasis value )
 {
	(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Destination = static_cast<cross::MaterialCommonBasis>(value);
 }

Clicross::TransformElementType MaterialExpressionTransform::m_ElementType::get()
 {
	return (Clicross::TransformElementType)((int)(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_ElementType);
 }
 void MaterialExpressionTransform::m_ElementType::set(Clicross::TransformElementType value )
 {
	(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_ElementType = static_cast<cross::TransformElementType>(value);
 }

Clicross::ExpressionInput^ MaterialExpressionTransform::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionTransform::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTransform::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTransform::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTransform*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTransform::MaterialExpressionTransform(const cross::MaterialExpressionTransform * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTransform::operator MaterialExpressionTransform^ (const cross::MaterialExpressionTransform* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTransform(const_cast<cross::MaterialExpressionTransform*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTransform^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionTruncate export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionInput^ MaterialExpressionTruncate::m_Input::get()
 {
	return gcnew Clicross::ExpressionInput(new cross::ExpressionInput(((static_cast<cross::MaterialExpressionTruncate*>(this->_native))->m_Input)) , true);
 }
 void MaterialExpressionTruncate::m_Input::set(Clicross::ExpressionInput^ value )
 {
	(static_cast<cross::MaterialExpressionTruncate*>(this->_native))->m_Input = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionTruncate::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionTruncate*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionTruncate::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionTruncate*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionTruncate::MaterialExpressionTruncate(const cross::MaterialExpressionTruncate * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionTruncate::operator MaterialExpressionTruncate^ (const cross::MaterialExpressionTruncate* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionTruncate(const_cast<cross::MaterialExpressionTruncate*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionTruncate^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionVectorParameter export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionVectorParameter::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionVectorParameter::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_Result = value;
 }

Clicross::Float4^ MaterialExpressionVectorParameter::m_DefaultValue::get()
 {
	return gcnew Clicross::Float4(new cross::Float4(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_DefaultValue)) , true);
 }
 void MaterialExpressionVectorParameter::m_DefaultValue::set(Clicross::Float4^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_DefaultValue = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVectorParameter::m_R::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_R)) , true);
 }
 void MaterialExpressionVectorParameter::m_R::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_R = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVectorParameter::m_G::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_G)) , true);
 }
 void MaterialExpressionVectorParameter::m_G::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_G = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVectorParameter::m_B::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionVectorParameter::m_B::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVectorParameter::m_A::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionVectorParameter::m_A::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVectorParameter*>(this->_native))->m_A = value;
 }


//constructor export here


MaterialExpressionVectorParameter::MaterialExpressionVectorParameter(const cross::MaterialExpressionVectorParameter * obj, bool created_by_clr): Clicross::MaterialExpressionParameter(obj, created_by_clr)
{
}

MaterialExpressionVectorParameter::operator MaterialExpressionVectorParameter^ (const cross::MaterialExpressionVectorParameter* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionVectorParameter(const_cast<cross::MaterialExpressionVectorParameter*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionVectorParameter^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionVertexColor export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_RGB::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_RGB)) , true);
 }
 void MaterialExpressionVertexColor::m_RGB::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_RGB = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_R::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_R)) , true);
 }
 void MaterialExpressionVertexColor::m_R::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_R = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_G::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_G)) , true);
 }
 void MaterialExpressionVertexColor::m_G::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_G = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_B::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_B)) , true);
 }
 void MaterialExpressionVertexColor::m_B::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_B = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_A::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_A)) , true);
 }
 void MaterialExpressionVertexColor::m_A::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_A = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionVertexColor::m_RGBA::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_RGBA)) , true);
 }
 void MaterialExpressionVertexColor::m_RGBA::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexColor*>(this->_native))->m_RGBA = value;
 }


//constructor export here


MaterialExpressionVertexColor::MaterialExpressionVertexColor(const cross::MaterialExpressionVertexColor * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionVertexColor::operator MaterialExpressionVertexColor^ (const cross::MaterialExpressionVertexColor* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionVertexColor(const_cast<cross::MaterialExpressionVertexColor*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionVertexColor^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionVertexID export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionVertexID::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionVertexID*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionVertexID::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionVertexID*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionVertexID::MaterialExpressionVertexID(const cross::MaterialExpressionVertexID * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionVertexID::operator MaterialExpressionVertexID^ (const cross::MaterialExpressionVertexID* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionVertexID(const_cast<cross::MaterialExpressionVertexID*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionVertexID^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionViewProperty export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExposedViewProperty MaterialExpressionViewProperty::m_Property::get()
 {
	return (Clicross::ExposedViewProperty)((int)(static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_Property);
 }
 void MaterialExpressionViewProperty::m_Property::set(Clicross::ExposedViewProperty value )
 {
	(static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_Property = static_cast<cross::ExposedViewProperty>(value);
 }

Clicross::ExpressionOutput^ MaterialExpressionViewProperty::m_PropertyOutput::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_PropertyOutput)) , true);
 }
 void MaterialExpressionViewProperty::m_PropertyOutput::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_PropertyOutput = value;
 }

Clicross::ExpressionOutput^ MaterialExpressionViewProperty::m_InvPropertyOutput::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_InvPropertyOutput)) , true);
 }
 void MaterialExpressionViewProperty::m_InvPropertyOutput::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionViewProperty*>(this->_native))->m_InvPropertyOutput = value;
 }


//constructor export here


MaterialExpressionViewProperty::MaterialExpressionViewProperty(const cross::MaterialExpressionViewProperty * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionViewProperty::operator MaterialExpressionViewProperty^ (const cross::MaterialExpressionViewProperty* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionViewProperty(const_cast<cross::MaterialExpressionViewProperty*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionViewProperty^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionWorldGeometryNormal export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionWorldGeometryNormal::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionWorldGeometryNormal*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionWorldGeometryNormal::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionWorldGeometryNormal*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionWorldGeometryNormal::MaterialExpressionWorldGeometryNormal(const cross::MaterialExpressionWorldGeometryNormal * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionWorldGeometryNormal::operator MaterialExpressionWorldGeometryNormal^ (const cross::MaterialExpressionWorldGeometryNormal* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionWorldGeometryNormal(const_cast<cross::MaterialExpressionWorldGeometryNormal*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionWorldGeometryNormal^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionWorldPosition export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionWorldPosition::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionWorldPosition*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionWorldPosition::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionWorldPosition*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionWorldPosition::MaterialExpressionWorldPosition(const cross::MaterialExpressionWorldPosition * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionWorldPosition::operator MaterialExpressionWorldPosition^ (const cross::MaterialExpressionWorldPosition* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionWorldPosition(const_cast<cross::MaterialExpressionWorldPosition*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionWorldPosition^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// MaterialExpressionWorldTangent export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::ExpressionOutput^ MaterialExpressionWorldTangent::m_Result::get()
 {
	return gcnew Clicross::ExpressionOutput(new cross::ExpressionOutput(((static_cast<cross::MaterialExpressionWorldTangent*>(this->_native))->m_Result)) , true);
 }
 void MaterialExpressionWorldTangent::m_Result::set(Clicross::ExpressionOutput^ value )
 {
	(static_cast<cross::MaterialExpressionWorldTangent*>(this->_native))->m_Result = value;
 }


//constructor export here


MaterialExpressionWorldTangent::MaterialExpressionWorldTangent(const cross::MaterialExpressionWorldTangent * obj, bool created_by_clr): Clicross::MaterialExpression(obj, created_by_clr)
{
}

MaterialExpressionWorldTangent::operator MaterialExpressionWorldTangent^ (const cross::MaterialExpressionWorldTangent* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MaterialExpressionWorldTangent(const_cast<cross::MaterialExpressionWorldTangent*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MaterialExpressionWorldTangent^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// CrossEngine export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
CrossEngine::CrossEngine( )
    :CrossEngine(new cross::CrossEngine(), true)
{
}



CrossEngine::CrossEngine(const cross::CrossEngine * obj, bool created_by_clr): 
    _native(const_cast<cross::CrossEngine *>(obj))
	, _created_by_clr(created_by_clr)
{
}

CrossEngine::operator CrossEngine^ (const cross::CrossEngine* t)
{
    if(t)
    {
        return gcnew CrossEngine(const_cast<cross::CrossEngine*>(t));
    }
    else
        return nullptr;
}

Clicross::CROSS_CODE CrossEngine::Init(Clicross::InitInfo^ info )
{
    return (Clicross::CROSS_CODE)((int)(static_cast<cross::CrossEngine*>(this->_native))->Init( (const cross::InitInfo& )(info)));
}

Clicross::CROSS_CODE CrossEngine::Init(Clicross::InitInfo^ info, int argc, cli::array<String^>^ argv )
{
    return (Clicross::CROSS_CODE)((int)(static_cast<cross::CrossEngine*>(this->_native))->Init( (const cross::InitInfo& )(info), argc, ClangenCli::ConvertCliArrayToCharArray(argv, argc)));
}

Clicross::CROSS_CODE CrossEngine::InitByNoGraphicsCard(Clicross::InitInfo^ info )
{
    return (Clicross::CROSS_CODE)((int)(static_cast<cross::CrossEngine*>(this->_native))->InitByNoGraphicsCard( (const cross::InitInfo& )(info)));
}

Clicross::EditorOnlyModules^ CrossEngine::GetEditorModeModules( )
{
    return (Clicross::EditorOnlyModules^)((static_cast<cross::CrossEngine*>(this->_native))->GetEditorModeModules( ));
}

void CrossEngine::PostInit( )
{
    (static_cast<cross::CrossEngine*>(this->_native))->PostInit( );
}

void CrossEngine::ShutDown( )
{
    (static_cast<cross::CrossEngine*>(this->_native))->ShutDown( );
}

Clicross::IGameWorld^ CrossEngine::CreateWorld(System::String^ worldName, unsigned int WorldTypeTag )
{
    return (Clicross::IGameWorld^)((static_cast<cross::CrossEngine*>(this->_native))->CreateWorld( ClangenCli::ToNativeString(worldName).c_str(), WorldTypeTag));
}

void CrossEngine::Destroy(Clicross::IGameWorld^ world )
{
    (static_cast<cross::CrossEngine*>(this->_native))->Destroy( ( cross::IGameWorld* )(world));
}

Clicross::IRenderWindow^ CrossEngine::CreateRenderWindow(Clicross::RenderWindowInitInfo^ info )
{
    return (Clicross::IRenderWindow^)((static_cast<cross::CrossEngine*>(this->_native))->CreateRenderWindow( (const cross::RenderWindowInitInfo& )(info)));
}

void CrossEngine::Destroy(Clicross::IRenderWindow^ renderWindow )
{
    (static_cast<cross::CrossEngine*>(this->_native))->Destroy( ( cross::IRenderWindow* )(renderWindow));
}

void CrossEngine::Tick( )
{
    (static_cast<cross::CrossEngine*>(this->_native))->Tick( );
}

void CrossEngine::FlushRendering( )
{
    (static_cast<cross::CrossEngine*>(this->_native))->FlushRendering( );
}

int CrossEngine::GetRendererMode( )
{
    return (static_cast<cross::CrossEngine*>(this->_native))->GetRendererMode( );
}

void CrossEngine::SetAssetPath(System::String^ assetPath )
{
    (static_cast<cross::CrossEngine*>(this->_native))->SetAssetPath( ClangenCli::ToNativeString(assetPath).c_str());
}

void CrossEngine::SetEngineBinaryPath(System::String^ assetPath )
{
    (static_cast<cross::CrossEngine*>(this->_native))->SetEngineBinaryPath( ClangenCli::ToNativeString(assetPath).c_str());
}

void CrossEngine::SetFixedUpdateMode(bool state, float fixedDeltaTime )
{
    (static_cast<cross::CrossEngine*>(this->_native))->SetFixedUpdateMode( state, fixedDeltaTime);
}


}   //end namespace Clicross

// EditorOnlyModules export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::EditorOnlyModules::mNames export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(EditorOnlyModules::, mNamesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE

// cross::EditorOnlyModules::mPathes export start
	#define STLDECL_MANAGEDTYPE System::String^
	#define STLDECL_NATIVETYPE std::string
	CPP_DECLARE_STLVECTOR(EditorOnlyModules::, mPathesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
EditorOnlyModules::mNamesCliType^ EditorOnlyModules::mNames::get()
 {
	return (static_cast<cross::EditorOnlyModules*>(this->_native))->mNames;
 }
 void EditorOnlyModules::mNames::set(EditorOnlyModules::mNamesCliType^ value )
 {
	(static_cast<cross::EditorOnlyModules*>(this->_native))->mNames = *value->_native;
 }

EditorOnlyModules::mPathesCliType^ EditorOnlyModules::mPathes::get()
 {
	return (static_cast<cross::EditorOnlyModules*>(this->_native))->mPathes;
 }
 void EditorOnlyModules::mPathes::set(EditorOnlyModules::mPathesCliType^ value )
 {
	(static_cast<cross::EditorOnlyModules*>(this->_native))->mPathes = *value->_native;
 }


//constructor export here
EditorOnlyModules::EditorOnlyModules(): EditorOnlyModules(new cross::EditorOnlyModules(), true) {}


EditorOnlyModules::EditorOnlyModules(const cross::EditorOnlyModules * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorOnlyModules *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorOnlyModules::operator EditorOnlyModules^ (const cross::EditorOnlyModules* t)
{
    if(t)
    {
        return gcnew EditorOnlyModules(const_cast<cross::EditorOnlyModules*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// InitInfo export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ InitInfo::AssetPath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InitInfo*>(this->_native))->AssetPath)).c_str());
 }
 void InitInfo::AssetPath::set(System::String^ value )
 {
	((static_cast<cross::InitInfo*>(this->_native))->AssetPath) = (ClangenCli::ToNativeString(value));
 }

System::String^ InitInfo::EngineResourcePath::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::InitInfo*>(this->_native))->EngineResourcePath)).c_str());
 }
 void InitInfo::EngineResourcePath::set(System::String^ value )
 {
	((static_cast<cross::InitInfo*>(this->_native))->EngineResourcePath) = (ClangenCli::ToNativeString(value));
 }

IntPtr InitInfo::LogDelegate::get()
 {
	return static_cast<IntPtr>(nullptr);
 }
 void InitInfo::LogDelegate::set(IntPtr value )
 {
	(static_cast<cross::InitInfo*>(this->_native))->LogDelegate=reinterpret_cast<void (*)(int, const char*)>(value.ToPointer());
 }

unsigned char InitInfo::StartupType::get()
 {
	return (static_cast<cross::InitInfo*>(this->_native))->StartupType;
 }
 void InitInfo::StartupType::set(unsigned char value )
 {
	(static_cast<cross::InitInfo*>(this->_native))->StartupType = value;
 }

bool InitInfo::IsThumbnailProcess::get()
 {
	return (static_cast<cross::InitInfo*>(this->_native))->IsThumbnailProcess;
 }
 void InitInfo::IsThumbnailProcess::set(bool value )
 {
	(static_cast<cross::InitInfo*>(this->_native))->IsThumbnailProcess = value;
 }

bool InitInfo::EnableCrashReport::get()
 {
	return (static_cast<cross::InitInfo*>(this->_native))->EnableCrashReport;
 }
 void InitInfo::EnableCrashReport::set(bool value )
 {
	(static_cast<cross::InitInfo*>(this->_native))->EnableCrashReport = value;
 }


//constructor export here
InitInfo::InitInfo(): InitInfo(new cross::InitInfo(), true) {}


InitInfo::InitInfo(const cross::InitInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::InitInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InitInfo::operator InitInfo^ (const cross::InitInfo* t)
{
    if(t)
    {
        return gcnew InitInfo(const_cast<cross::InitInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// GameWorld export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
GameWorld::GameWorld(System::String^ worldName, Clicross::CreateGameWorldParam^ createParam )
    :GameWorld(new cross::GameWorld(ClangenCli::ToNativeString(worldName).c_str(), (const cross::CreateGameWorldParam& )(createParam)), true)
{
}



GameWorld::GameWorld(const cross::GameWorld * obj, bool created_by_clr): Clicross::IGameWorld(obj, created_by_clr)
{
}

GameWorld::operator GameWorld^ (const cross::GameWorld* t)
{
    if(t)
    {
        return gcnew GameWorld(const_cast<cross::GameWorld*>(t));
    }
    else
        return nullptr;
}

unsigned int GameWorld::GetRuntimeID( )
{
    return (static_cast<cross::GameWorld*>(this->_native))->GetRuntimeID( );
}

UnknowKeeper^ GameWorld::GetName( )
{
    return UnknownKeeperCast((static_cast<cross::GameWorld*>(this->_native))->GetName( ));
}

void GameWorld::SetEnable(bool enable )
{
    (static_cast<cross::GameWorld*>(this->_native))->SetEnable( enable);
}

Clicross::WorldInternalSystem^ GameWorld::GetSystem(System::String^ systemName )
{
    return (Clicross::WorldInternalSystem^)((static_cast<cross::GameWorld*>(this->_native))->GetSystem( ClangenCli::ToNativeString(systemName).c_str()));
}

Clicross::ecs::EntityIDStruct^ GameWorld::CreateEntity(System::String^ entityType )
{
    return gcnew Clicross::ecs::EntityIDStruct(new cross::ecs::EntityIDStruct(((static_cast<cross::GameWorld*>(this->_native))->CreateEntity( ClangenCli::ToNativeString(entityType).c_str()))) , true);
}


}   //end namespace Clicross

// EntityList export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::EntityList::mEntities export start
	#define STLDECL_MANAGEDTYPE unsigned long long
	#define STLDECL_NATIVETYPE unsigned long long
	CPP_DECLARE_STLVECTOR(EntityList::, mEntitiesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
EntityList::mEntitiesCliType^ EntityList::mEntities::get()
 {
	return (static_cast<cross::EntityList*>(this->_native))->mEntities;
 }
 void EntityList::mEntities::set(EntityList::mEntitiesCliType^ value )
 {
	(static_cast<cross::EntityList*>(this->_native))->mEntities = *value->_native;
 }


//constructor export here
EntityList::EntityList(): EntityList(new cross::EntityList(), true) {}


EntityList::EntityList(const cross::EntityList * obj, bool created_by_clr): 
    _native(const_cast<cross::EntityList *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EntityList::operator EntityList^ (const cross::EntityList* t)
{
    if(t)
    {
        return gcnew EntityList(const_cast<cross::EntityList*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RenderWindowInitInfo export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned int RenderWindowInitInfo::width::get()
 {
	return (static_cast<cross::RenderWindowInitInfo*>(this->_native))->width;
 }
 void RenderWindowInitInfo::width::set(unsigned int value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->width = value;
 }

unsigned int RenderWindowInitInfo::height::get()
 {
	return (static_cast<cross::RenderWindowInitInfo*>(this->_native))->height;
 }
 void RenderWindowInitInfo::height::set(unsigned int value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->height = value;
 }

System::IntPtr RenderWindowInitInfo::handle::get()
 {
	return System::IntPtr((static_cast<cross::RenderWindowInitInfo*>(this->_native))->handle);
 }
 void RenderWindowInitInfo::handle::set(System::IntPtr value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->handle=reinterpret_cast< void* >(value.ToPointer());
 }

float RenderWindowInitInfo::dpr::get()
 {
	return (static_cast<cross::RenderWindowInitInfo*>(this->_native))->dpr;
 }
 void RenderWindowInitInfo::dpr::set(float value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->dpr = value;
 }

System::String^ RenderWindowInitInfo::name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::RenderWindowInitInfo*>(this->_native))->name)).c_str());
 }
 void RenderWindowInitInfo::name::set(System::String^ value )
 {
	((static_cast<cross::RenderWindowInitInfo*>(this->_native))->name) = (ClangenCli::ToNativeString(value));
 }

bool RenderWindowInitInfo::Offscreen::get()
 {
	return (static_cast<cross::RenderWindowInitInfo*>(this->_native))->Offscreen;
 }
 void RenderWindowInitInfo::Offscreen::set(bool value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->Offscreen = value;
 }

Clicross::NGIScreenMode RenderWindowInitInfo::ScreenMode::get()
 {
	return (Clicross::NGIScreenMode)((int)(static_cast<cross::RenderWindowInitInfo*>(this->_native))->ScreenMode);
 }
 void RenderWindowInitInfo::ScreenMode::set(Clicross::NGIScreenMode value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->ScreenMode = static_cast<cross::NGIScreenMode>(value);
 }

bool RenderWindowInitInfo::belongEditor::get()
 {
	return (static_cast<cross::RenderWindowInitInfo*>(this->_native))->belongEditor;
 }
 void RenderWindowInitInfo::belongEditor::set(bool value )
 {
	(static_cast<cross::RenderWindowInitInfo*>(this->_native))->belongEditor = value;
 }


//constructor export here
RenderWindowInitInfo::RenderWindowInitInfo(): RenderWindowInitInfo(new cross::RenderWindowInitInfo(), true) {}


RenderWindowInitInfo::RenderWindowInitInfo(const cross::RenderWindowInitInfo * obj, bool created_by_clr): 
    _native(const_cast<cross::RenderWindowInitInfo *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RenderWindowInitInfo::operator RenderWindowInitInfo^ (const cross::RenderWindowInitInfo* t)
{
    if(t)
    {
        return gcnew RenderWindowInitInfo(const_cast<cross::RenderWindowInitInfo*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// AnimationAnalyzer export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
AnimationAnalyzer::AnimationAnalyzer( )
    :AnimationAnalyzer(new cross::anim::AnimationAnalyzer(), true)
{
}

AnimationAnalyzer::AnimationAnalyzer(Clicross::UniqueString^ name, UnknowKeeper^ referenceBoneNames, bool flattenY )
    :AnimationAnalyzer(new cross::anim::AnimationAnalyzer((const cross::UniqueString& )(name), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(referenceBoneNames)), flattenY), true)
{
}

AnimationAnalyzer::AnimationAnalyzer(Clicross::UniqueString^ name, UnknowKeeper^ animationRef, UnknowKeeper^ referenceJoints, bool flattenY )
    :AnimationAnalyzer(new cross::anim::AnimationAnalyzer((const cross::UniqueString& )(name), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(animationRef)), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(referenceJoints)), flattenY), true)
{
}



AnimationAnalyzer::AnimationAnalyzer(const cross::anim::AnimationAnalyzer * obj, bool created_by_clr): Clicross::anim::AnimResourceBase(obj, created_by_clr)
{
}

AnimationAnalyzer::operator AnimationAnalyzer^ (const cross::anim::AnimationAnalyzer* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew AnimationAnalyzer(const_cast<cross::anim::AnimationAnalyzer*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (AnimationAnalyzer^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// MotionDataAsset export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MotionDataAsset::MotionDataAsset( )
    :MotionDataAsset(new cross::anim::MotionDataAsset(), true)
{
}

MotionDataAsset::MotionDataAsset(Clicross::UniqueString^ name, UnknowKeeper^ referenceBoneNames, UnknowKeeper^ inConfig, bool flattenY )
    :MotionDataAsset(new cross::anim::MotionDataAsset((const cross::UniqueString& )(name), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(referenceBoneNames)), (UnknowKeeper::get_native_with_type_for_reference<cross::anim::TrajGenConfig& >(inConfig)), flattenY), true)
{
}

MotionDataAsset::MotionDataAsset(Clicross::UniqueString^ name, UnknowKeeper^ animations, UnknowKeeper^ referenceBoneNames, UnknowKeeper^ inConfig, bool flattenY )
    :MotionDataAsset(new cross::anim::MotionDataAsset((const cross::UniqueString& )(name), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(animations)), (UnknowKeeper::get_native_with_type_for_reference<std::vector<cross::UniqueString, std::allocator<cross::UniqueString>>& >(referenceBoneNames)), (UnknowKeeper::get_native_with_type_for_reference<cross::anim::TrajGenConfig& >(inConfig)), flattenY), true)
{
}



MotionDataAsset::MotionDataAsset(const cross::anim::MotionDataAsset * obj, bool created_by_clr): Clicross::anim::AnimationAnalyzer(obj, created_by_clr)
{
}

MotionDataAsset::operator MotionDataAsset^ (const cross::anim::MotionDataAsset* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew MotionDataAsset(const_cast<cross::anim::MotionDataAsset*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (MotionDataAsset^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Resource^ MotionDataAsset::CreateMotionMatchData(System::String^ animation_sequence )
{
    return (Clicross::Resource^)(cross::anim::MotionDataAsset::CreateMotionMatchData( ClangenCli::ToNativeString(animation_sequence).c_str()));
}

void MotionDataAsset::ClearMotionMatchSequences( )
{
    (static_cast<cross::anim::MotionDataAsset*>(this->_native))->ClearMotionMatchSequences( );
}

int MotionDataAsset::AddMotionMatchSequencePath(System::String^ path )
{
    return (static_cast<cross::anim::MotionDataAsset*>(this->_native))->AddMotionMatchSequencePath( ClangenCli::ToNativeString(path).c_str());
}

int MotionDataAsset::AddMotionMatchJointName(System::String^ joint_name )
{
    return (static_cast<cross::anim::MotionDataAsset*>(this->_native))->AddMotionMatchJointName( ClangenCli::ToNativeString(joint_name).c_str());
}

void MotionDataAsset::ClearMotionMatchJoints( )
{
    (static_cast<cross::anim::MotionDataAsset*>(this->_native))->ClearMotionMatchJoints( );
}

int MotionDataAsset::GetMotionMatchJointNum( )
{
    return (static_cast<cross::anim::MotionDataAsset*>(this->_native))->GetMotionMatchJointNum( );
}

void MotionDataAsset::EditorSetMotionMatchResourceSetting(Clicross::anim::MotionMatchResourceSetting^ setting )
{
    (static_cast<cross::anim::MotionDataAsset*>(this->_native))->EditorSetMotionMatchResourceSetting( *((cross::anim::MotionMatchResourceSetting*)(setting)));
}

Clicross::anim::MotionMatchResourceSetting^ MotionDataAsset::EditorGetMotionMatchResourceSetting( )
{
    return gcnew Clicross::anim::MotionMatchResourceSetting(new cross::anim::MotionMatchResourceSetting(((static_cast<cross::anim::MotionDataAsset*>(this->_native))->EditorGetMotionMatchResourceSetting( ))) , true);
}

Clicross::anim::MotionMatchResourceStatistics^ MotionDataAsset::GetMotionMatchResourceStatistics( )
{
    return gcnew Clicross::anim::MotionMatchResourceStatistics(new cross::anim::MotionMatchResourceStatistics(((static_cast<cross::anim::MotionDataAsset*>(this->_native))->GetMotionMatchResourceStatistics( ))) , true);
}

int MotionDataAsset::GetMotionMatchSequenceNum( )
{
    return (static_cast<cross::anim::MotionDataAsset*>(this->_native))->GetMotionMatchSequenceNum( );
}

System::String^ MotionDataAsset::GetMotionMatchSequencePath(int id )
{
    return ClangenCli::ToManagedString((((static_cast<cross::anim::MotionDataAsset*>(this->_native))->GetMotionMatchSequencePath( id))).c_str());
}

bool MotionDataAsset::CookMotionMatchData( )
{
    return (static_cast<cross::anim::MotionDataAsset*>(this->_native))->CookMotionMatchData( );
}

System::String^ MotionDataAsset::GetMotionMatchJointName(int id )
{
    return ClangenCli::ToManagedString((((static_cast<cross::anim::MotionDataAsset*>(this->_native))->GetMotionMatchJointName( id))).c_str());
}


}   //end namespace Clicross
}   //end namespace anim

// MotionMatchResourceSetting export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here
// cross::anim::MotionMatchResourceSetting::TrajTimes export start
	#define STLDECL_MANAGEDTYPE float
	#define STLDECL_NATIVETYPE float
	CPP_DECLARE_STLVECTOR(MotionMatchResourceSetting::, TrajTimesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
int MotionMatchResourceSetting::PoseMatching::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseMatching;
 }
 void MotionMatchResourceSetting::PoseMatching::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseMatching = value;
 }

int MotionMatchResourceSetting::valid::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->valid;
 }
 void MotionMatchResourceSetting::valid::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->valid = value;
 }

int MotionMatchResourceSetting::flattenY::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->flattenY;
 }
 void MotionMatchResourceSetting::flattenY::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->flattenY = value;
 }

float MotionMatchResourceSetting::poseInterval::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->poseInterval;
 }
 void MotionMatchResourceSetting::poseInterval::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->poseInterval = value;
 }

float MotionMatchResourceSetting::startAnimClamp::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->startAnimClamp;
 }
 void MotionMatchResourceSetting::startAnimClamp::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->startAnimClamp = value;
 }

float MotionMatchResourceSetting::endAnimClamp::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->endAnimClamp;
 }
 void MotionMatchResourceSetting::endAnimClamp::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->endAnimClamp = value;
 }

int MotionMatchResourceSetting::TrajSize::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajSize;
 }
 void MotionMatchResourceSetting::TrajSize::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajSize = value;
 }

MotionMatchResourceSetting::TrajTimesCliType^ MotionMatchResourceSetting::TrajTimes::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajTimes;
 }
 void MotionMatchResourceSetting::TrajTimes::set(MotionMatchResourceSetting::TrajTimesCliType^ value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajTimes = *value->_native;
 }

float MotionMatchResourceSetting::QualityVsResponse::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->QualityVsResponse;
 }
 void MotionMatchResourceSetting::QualityVsResponse::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->QualityVsResponse = value;
 }

float MotionMatchResourceSetting::BodyVelocityWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BodyVelocityWeight;
 }
 void MotionMatchResourceSetting::BodyVelocityWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BodyVelocityWeight = value;
 }

float MotionMatchResourceSetting::BodyRotVelocityWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BodyRotVelocityWeight;
 }
 void MotionMatchResourceSetting::BodyRotVelocityWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BodyRotVelocityWeight = value;
 }

float MotionMatchResourceSetting::TrajTranslationWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajTranslationWeight;
 }
 void MotionMatchResourceSetting::TrajTranslationWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajTranslationWeight = value;
 }

float MotionMatchResourceSetting::TrajFacingWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajFacingWeight;
 }
 void MotionMatchResourceSetting::TrajFacingWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->TrajFacingWeight = value;
 }

float MotionMatchResourceSetting::PoseTranslationWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseTranslationWeight;
 }
 void MotionMatchResourceSetting::PoseTranslationWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseTranslationWeight = value;
 }

float MotionMatchResourceSetting::PoseVelocityWeight::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseVelocityWeight;
 }
 void MotionMatchResourceSetting::PoseVelocityWeight::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PoseVelocityWeight = value;
 }

int MotionMatchResourceSetting::PreviewAnimID::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PreviewAnimID;
 }
 void MotionMatchResourceSetting::PreviewAnimID::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PreviewAnimID = value;
 }

bool MotionMatchResourceSetting::PredictedTrajectory::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PredictedTrajectory;
 }
 void MotionMatchResourceSetting::PredictedTrajectory::set(bool value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->PredictedTrajectory = value;
 }

bool MotionMatchResourceSetting::SelectedTrajectory::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->SelectedTrajectory;
 }
 void MotionMatchResourceSetting::SelectedTrajectory::set(bool value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->SelectedTrajectory = value;
 }

bool MotionMatchResourceSetting::BonePoses::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BonePoses;
 }
 void MotionMatchResourceSetting::BonePoses::set(bool value )
 {
	(static_cast<cross::anim::MotionMatchResourceSetting*>(this->_native))->BonePoses = value;
 }


//constructor export here
MotionMatchResourceSetting::MotionMatchResourceSetting(): MotionMatchResourceSetting(new cross::anim::MotionMatchResourceSetting(), true) {}


MotionMatchResourceSetting::MotionMatchResourceSetting(const cross::anim::MotionMatchResourceSetting * obj, bool created_by_clr): 
    _native(const_cast<cross::anim::MotionMatchResourceSetting *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MotionMatchResourceSetting::operator MotionMatchResourceSetting^ (const cross::anim::MotionMatchResourceSetting* t)
{
    if(t)
    {
        return gcnew MotionMatchResourceSetting(const_cast<cross::anim::MotionMatchResourceSetting*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// MotionMatchResourceStatistics export start
namespace Clicross
{
namespace anim
{

//embeded classes

//stl container export here

//fields export here
float MotionMatchResourceStatistics::MMFeatureSizeInMemory::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMFeatureSizeInMemory;
 }
 void MotionMatchResourceStatistics::MMFeatureSizeInMemory::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMFeatureSizeInMemory = value;
 }

float MotionMatchResourceStatistics::MMMinSpeed::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMMinSpeed;
 }
 void MotionMatchResourceStatistics::MMMinSpeed::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMMinSpeed = value;
 }

float MotionMatchResourceStatistics::MMAverageSpeed::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMAverageSpeed;
 }
 void MotionMatchResourceStatistics::MMAverageSpeed::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMAverageSpeed = value;
 }

float MotionMatchResourceStatistics::MMMaxSpeed::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMMaxSpeed;
 }
 void MotionMatchResourceStatistics::MMMaxSpeed::set(float value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMMaxSpeed = value;
 }

int MotionMatchResourceStatistics::MMPoseNumber::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMPoseNumber;
 }
 void MotionMatchResourceStatistics::MMPoseNumber::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->MMPoseNumber = value;
 }

int MotionMatchResourceStatistics::FeatureNum::get()
 {
	return (static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->FeatureNum;
 }
 void MotionMatchResourceStatistics::FeatureNum::set(int value )
 {
	(static_cast<cross::anim::MotionMatchResourceStatistics*>(this->_native))->FeatureNum = value;
 }


//constructor export here
MotionMatchResourceStatistics::MotionMatchResourceStatistics(): MotionMatchResourceStatistics(new cross::anim::MotionMatchResourceStatistics(), true) {}


MotionMatchResourceStatistics::MotionMatchResourceStatistics(const cross::anim::MotionMatchResourceStatistics * obj, bool created_by_clr): 
    _native(const_cast<cross::anim::MotionMatchResourceStatistics *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MotionMatchResourceStatistics::operator MotionMatchResourceStatistics^ (const cross::anim::MotionMatchResourceStatistics* t)
{
    if(t)
    {
        return gcnew MotionMatchResourceStatistics(const_cast<cross::anim::MotionMatchResourceStatistics*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross
}   //end namespace anim

// MouseEvent export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::InputButton MouseEvent::mButton::get()
 {
	return (Clicross::InputButton)((int)(static_cast<cross::MouseEvent*>(this->_native))->mButton);
 }
 void MouseEvent::mButton::set(Clicross::InputButton value )
 {
	(static_cast<cross::MouseEvent*>(this->_native))->mButton = static_cast<cross::InputButton>(value);
 }

Clicross::InputAction MouseEvent::mAction::get()
 {
	return (Clicross::InputAction)((int)(static_cast<cross::MouseEvent*>(this->_native))->mAction);
 }
 void MouseEvent::mAction::set(Clicross::InputAction value )
 {
	(static_cast<cross::MouseEvent*>(this->_native))->mAction = static_cast<cross::InputAction>(value);
 }

Clicross::Float2^ MouseEvent::mPoint::get()
 {
	return gcnew Clicross::Float2(new cross::Float2(((static_cast<cross::MouseEvent*>(this->_native))->mPoint)) , true);
 }
 void MouseEvent::mPoint::set(Clicross::Float2^ value )
 {
	(static_cast<cross::MouseEvent*>(this->_native))->mPoint = value;
 }

float MouseEvent::mWheelDelta::get()
 {
	return (static_cast<cross::MouseEvent*>(this->_native))->mWheelDelta;
 }
 void MouseEvent::mWheelDelta::set(float value )
 {
	(static_cast<cross::MouseEvent*>(this->_native))->mWheelDelta = value;
 }


//constructor export here
MouseEvent::MouseEvent( )
    :MouseEvent(new cross::MouseEvent(), true)
{
}

MouseEvent::MouseEvent(float x, float y )
    :MouseEvent(new cross::MouseEvent(x, y), true)
{
}

MouseEvent::MouseEvent(Clicross::Float2^ point )
    :MouseEvent(new cross::MouseEvent(*((cross::Float2*)(point))), true)
{
}



MouseEvent::MouseEvent(const cross::MouseEvent * obj, bool created_by_clr): 
    _native(const_cast<cross::MouseEvent *>(obj))
	, _created_by_clr(created_by_clr)
{
}

MouseEvent::operator MouseEvent^ (const cross::MouseEvent* t)
{
    if(t)
    {
        return gcnew MouseEvent(const_cast<cross::MouseEvent*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


