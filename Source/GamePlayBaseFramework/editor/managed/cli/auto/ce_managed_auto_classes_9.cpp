//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************

//***************************************************************
//***************All class instances export here*****************
//***************************************************************
// ButtonEvent export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::InputButton ButtonEvent::mButton::get()
 {
	return (Clicross::InputButton)((int)(static_cast<cross::ButtonEvent*>(this->_native))->mButton);
 }
 void ButtonEvent::mButton::set(Clicross::InputButton value )
 {
	(static_cast<cross::ButtonEvent*>(this->_native))->mButton = static_cast<cross::InputButton>(value);
 }

Clicross::InputAction ButtonEvent::mAction::get()
 {
	return (Clicross::InputAction)((int)(static_cast<cross::ButtonEvent*>(this->_native))->mAction);
 }
 void ButtonEvent::mAction::set(Clicross::InputAction value )
 {
	(static_cast<cross::ButtonEvent*>(this->_native))->mAction = static_cast<cross::InputAction>(value);
 }


//constructor export here
ButtonEvent::ButtonEvent(): ButtonEvent(new cross::ButtonEvent(), true) {}


ButtonEvent::ButtonEvent(const cross::ButtonEvent * obj, bool created_by_clr): 
    _native(const_cast<cross::ButtonEvent *>(obj))
	, _created_by_clr(created_by_clr)
{
}

ButtonEvent::operator ButtonEvent^ (const cross::ButtonEvent* t)
{
    if(t)
    {
        return gcnew ButtonEvent(const_cast<cross::ButtonEvent*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// TODLightConfig export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned int TODLightConfig::Year::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Year;
 }
 void TODLightConfig::Year::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Year = value;
 }

unsigned int TODLightConfig::Month::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Month;
 }
 void TODLightConfig::Month::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Month = value;
 }

unsigned int TODLightConfig::Day::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Day;
 }
 void TODLightConfig::Day::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Day = value;
 }

unsigned int TODLightConfig::Hour::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Hour;
 }
 void TODLightConfig::Hour::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Hour = value;
 }

unsigned int TODLightConfig::Minute::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Minute;
 }
 void TODLightConfig::Minute::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Minute = value;
 }

unsigned int TODLightConfig::Second::get()
 {
	return (static_cast<cross::TODLightConfig*>(this->_native))->Second;
 }
 void TODLightConfig::Second::set(unsigned int value )
 {
	(static_cast<cross::TODLightConfig*>(this->_native))->Second = value;
 }


//constructor export here
TODLightConfig::TODLightConfig(): TODLightConfig(new cross::TODLightConfig(), true) {}


TODLightConfig::TODLightConfig(const cross::TODLightConfig * obj, bool created_by_clr): 
    _native(const_cast<cross::TODLightConfig *>(obj))
	, _created_by_clr(created_by_clr)
{
}

TODLightConfig::operator TODLightConfig^ (const cross::TODLightConfig* t)
{
    if(t)
    {
        return gcnew TODLightConfig(const_cast<cross::TODLightConfig*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// WorldChangedEventData export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::WorldLoadingStatus WorldChangedEventData::mWorldLoadingStatus::get()
 {
	return (Clicross::WorldLoadingStatus)((int)(static_cast<cross::WorldChangedEventData*>(this->_native))->mWorldLoadingStatus);
 }
 void WorldChangedEventData::mWorldLoadingStatus::set(Clicross::WorldLoadingStatus value )
 {
	(static_cast<cross::WorldChangedEventData*>(this->_native))->mWorldLoadingStatus = static_cast<cross::WorldLoadingStatus>(value);
 }


//constructor export here
WorldChangedEventData::WorldChangedEventData(): WorldChangedEventData(new cross::WorldChangedEventData(), true) {}


WorldChangedEventData::WorldChangedEventData(const cross::WorldChangedEventData * obj, bool created_by_clr): 
    _native(const_cast<cross::WorldChangedEventData *>(obj))
	, _created_by_clr(created_by_clr)
{
}

WorldChangedEventData::operator WorldChangedEventData^ (const cross::WorldChangedEventData* t)
{
    if(t)
    {
        return gcnew WorldChangedEventData(const_cast<cross::WorldChangedEventData*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ModelSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ModelSystemG::ModelSystemG( )
    :ModelSystemG(new cross::ModelSystemG(), true)
{
}



ModelSystemG::ModelSystemG(const cross::ModelSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

ModelSystemG::operator ModelSystemG^ (const cross::ModelSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ModelSystemG(const_cast<cross::ModelSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ModelSystemG^)managedObj;
    }
    else
        return nullptr;
}

bool ModelSystemG::SetModelAssetPath(Clicross::ecs::EntityIDStruct^ modelH, System::String^ assetpath, unsigned int modelIndex )
{
    return (static_cast<cross::ModelSystemG*>(this->_native))->SetModelAssetPath( static_cast<cross::ModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::ModelComponentG>(modelH).Write(), ClangenCli::ToNativeString(assetpath).c_str(), modelIndex);
}

unsigned int ModelSystemG::GetSubModelCount(Clicross::ecs::EntityIDStruct^ modelH, unsigned int modelIndex, unsigned int lodIndex )
{
    return (static_cast<cross::ModelSystemG*>(this->_native))->GetSubModelCount( static_cast<cross::ModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::ModelComponentG>(modelH).Read(), modelIndex, lodIndex);
}

unsigned int ModelSystemG::GetLODCount(Clicross::ecs::EntityIDStruct^ modelH, unsigned int modelIndex )
{
    return (static_cast<cross::ModelSystemG*>(this->_native))->GetLODCount( static_cast<cross::ModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::ModelComponentG>(modelH).Read(), modelIndex);
}

int ModelSystemG::Model_GetSubModelCount(Clicross::resource::MeshAssetDataResource^ meshRes, unsigned int lodIndex )
{
    return cross::ModelSystemG::Model_GetSubModelCount( ( cross::resource::MeshAssetDataResource* )(meshRes), lodIndex);
}

Clicross::Resource^ ModelSystemG::Model_GetMaterialResource(Clicross::IGameWorld^ world, unsigned long long entity, int modelIndex, int subModelIndex )
{
    return (Clicross::Resource^)(cross::ModelSystemG::Model_GetMaterialResource( ( cross::IGameWorld* )(world), entity, modelIndex, subModelIndex));
}

void ModelSystemG::Model_SetMaterialInstance(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::resource::MaterialInterface^ materialInterface, int subModelIndex, int modelIndex )
{
    cross::ModelSystemG::Model_SetMaterialInstance( ( cross::IGameWorld* )(world), entity, ( cross::resource::MaterialInterface* )(materialInterface), subModelIndex, modelIndex);
}

bool ModelSystemG::IsModelAssetStreamable(Clicross::ecs::EntityIDStruct^ modelH, unsigned int modelIndex )
{
    return (static_cast<cross::ModelSystemG*>(this->_native))->IsModelAssetStreamable( static_cast<cross::ModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::ModelComponentG>(modelH).Read(), modelIndex);
}

void ModelSystemG::SetModelAssetStreamable(Clicross::ecs::EntityIDStruct^ modelH, unsigned int modelIndex, bool enabled )
{
    (static_cast<cross::ModelSystemG*>(this->_native))->SetModelAssetStreamable( static_cast<cross::ModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::ModelComponentG>(modelH).Write(), modelIndex, enabled);
}


}   //end namespace Clicross

// EcotopeMap export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EcotopeMap::EcotopeMap( )
    :EcotopeMap(new cross::EcotopeMap(), true)
{
}



EcotopeMap::EcotopeMap(const cross::EcotopeMap * obj, bool created_by_clr): 
    _native(const_cast<cross::EcotopeMap *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EcotopeMap::operator EcotopeMap^ (const cross::EcotopeMap* t)
{
    if(t)
    {
        return gcnew EcotopeMap(const_cast<cross::EcotopeMap*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// PrimitiveRenderSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


PrimitiveRenderSystemG::PrimitiveRenderSystemG(const cross::PrimitiveRenderSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

PrimitiveRenderSystemG::operator PrimitiveRenderSystemG^ (const cross::PrimitiveRenderSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew PrimitiveRenderSystemG(const_cast<cross::PrimitiveRenderSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (PrimitiveRenderSystemG^)managedObj;
    }
    else
        return nullptr;
}

void PrimitiveRenderSystemG::EditorLogScreen(Clicross::IGameWorld^ world, System::String^ message )
{
    cross::PrimitiveRenderSystemG::EditorLogScreen( ( cross::IGameWorld* )(world), ClangenCli::ToNativeString(message).c_str());
}


}   //end namespace Clicross

// RenderPropertySystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


RenderPropertySystemG::RenderPropertySystemG(const cross::RenderPropertySystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

RenderPropertySystemG::operator RenderPropertySystemG^ (const cross::RenderPropertySystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew RenderPropertySystemG(const_cast<cross::RenderPropertySystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (RenderPropertySystemG^)managedObj;
    }
    else
        return nullptr;
}

void RenderPropertySystemG::RenderProperty_SetLodSelected(Clicross::IGameWorld^ world, unsigned long long entity, int lodIndex )
{
    cross::RenderPropertySystemG::RenderProperty_SetLodSelected( ( cross::IGameWorld* )(world), entity, lodIndex);
}


}   //end namespace Clicross

// RenderTargetUtil export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


RenderTargetUtil::RenderTargetUtil(const cross::RenderTargetUtil * obj, bool created_by_clr): 
    _native(const_cast<cross::RenderTargetUtil *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RenderTargetUtil::operator RenderTargetUtil^ (const cross::RenderTargetUtil* t)
{
    if(t)
    {
        return gcnew RenderTargetUtil(const_cast<cross::RenderTargetUtil*>(t));
    }
    else
        return nullptr;
}

void RenderTargetUtil::Camera_SaveRenderTarget_Simple_Async(Clicross::IGameWorld^ world, unsigned long long entity, System::String^ path, unsigned int w, unsigned int h, bool realSave, unsigned int imageType )
{
    cross::RenderTargetUtil::Camera_SaveRenderTarget_Simple_Async( ( cross::IGameWorld* )(world), entity, ClangenCli::ToNativeString(path).c_str(), w, h, realSave, imageType);
}

void RenderTargetUtil::Camera_SaveRenderTargetToCubemap_Async(Clicross::IGameWorld^ world, Clicross::EntityList^ entities, int entityCount, System::String^ path, unsigned int w, bool realSave, unsigned int imageType )
{
    cross::RenderTargetUtil::Camera_SaveRenderTargetToCubemap_Async( ( cross::IGameWorld* )(world), *((cross::EntityList*)(entities)), entityCount, ClangenCli::ToNativeString(path).c_str(), w, realSave, imageType);
}

void RenderTargetUtil::Camera_SaveRenderTarget(Clicross::IGameWorld^ world, unsigned long long entity, System::String^ p, unsigned int w, unsigned int h, unsigned int tileCount, unsigned int tileIndex )
{
    cross::RenderTargetUtil::Camera_SaveRenderTarget( ( cross::IGameWorld* )(world), entity, ClangenCli::ToNativeString(p).c_str(), w, h, tileCount, tileIndex);
}

void RenderTargetUtil::Camera_SaveRenderTargetToPanorama_Async(Clicross::IGameWorld^ world, Clicross::EntityList^ entities, int entityCount, System::String^ path, unsigned int w, unsigned int h, bool realSave, unsigned int imageType )
{
    cross::RenderTargetUtil::Camera_SaveRenderTargetToPanorama_Async( ( cross::IGameWorld* )(world), *((cross::EntityList*)(entities)), entityCount, ClangenCli::ToNativeString(path).c_str(), w, h, realSave, imageType);
}

void RenderTargetUtil::Camera_SetRenderTexture(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::Resource^ renderTexture )
{
    cross::RenderTargetUtil::Camera_SetRenderTexture( ( cross::IGameWorld* )(world), entity, ( cross::Resource* )(renderTexture));
}

void RenderTargetUtil::Camera_WaitForCompletion(Clicross::IGameWorld^ world, unsigned long long entity )
{
    cross::RenderTargetUtil::Camera_WaitForCompletion( ( cross::IGameWorld* )(world), entity);
}


}   //end namespace Clicross

// ParticleSimulationSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ParticleSimulationSystemG::ParticleSimulationSystemG(const cross::ParticleSimulationSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

ParticleSimulationSystemG::operator ParticleSimulationSystemG^ (const cross::ParticleSimulationSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ParticleSimulationSystemG(const_cast<cross::ParticleSimulationSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ParticleSimulationSystemG^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Resource^ ParticleSimulationSystemG::FX_CreateParticleEmitter( )
{
    return (Clicross::Resource^)(cross::ParticleSimulationSystemG::FX_CreateParticleEmitter( ));
}

Clicross::Resource^ ParticleSimulationSystemG::FX_GetParticleEmitterMaterialPtr(Clicross::IGameWorld^ world, unsigned long long entity, int emitterIndex, int materialIndex )
{
    return (Clicross::Resource^)(cross::ParticleSimulationSystemG::FX_GetParticleEmitterMaterialPtr( ( cross::IGameWorld* )(world), entity, emitterIndex, materialIndex));
}


}   //end namespace Clicross

// SkyAtmosphereConfig export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
float SkyAtmosphereConfig::MiePhase::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MiePhase;
 }
 void SkyAtmosphereConfig::MiePhase::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MiePhase = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::MieScattCoeff::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScattCoeff)) , true);
 }
 void SkyAtmosphereConfig::MieScattCoeff::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScattCoeff = value;
 }

float SkyAtmosphereConfig::MieScattScale::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScattScale;
 }
 void SkyAtmosphereConfig::MieScattScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScattScale = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::MieAbsorCoeff::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieAbsorCoeff)) , true);
 }
 void SkyAtmosphereConfig::MieAbsorCoeff::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieAbsorCoeff = value;
 }

float SkyAtmosphereConfig::MieAbsorScale::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieAbsorScale;
 }
 void SkyAtmosphereConfig::MieAbsorScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieAbsorScale = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::RayScattCoeff::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScattCoeff)) , true);
 }
 void SkyAtmosphereConfig::RayScattCoeff::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScattCoeff = value;
 }

float SkyAtmosphereConfig::RayScattScale::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScattScale;
 }
 void SkyAtmosphereConfig::RayScattScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScattScale = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::AbsorptiCoeff::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AbsorptiCoeff)) , true);
 }
 void SkyAtmosphereConfig::AbsorptiCoeff::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AbsorptiCoeff = value;
 }

float SkyAtmosphereConfig::AbsorptiScale::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AbsorptiScale;
 }
 void SkyAtmosphereConfig::AbsorptiScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AbsorptiScale = value;
 }

float SkyAtmosphereConfig::PlanetRadius::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->PlanetRadius;
 }
 void SkyAtmosphereConfig::PlanetRadius::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->PlanetRadius = value;
 }

float SkyAtmosphereConfig::AtmosHeight::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AtmosHeight;
 }
 void SkyAtmosphereConfig::AtmosHeight::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->AtmosHeight = value;
 }

float SkyAtmosphereConfig::MieScaleHeight::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScaleHeight;
 }
 void SkyAtmosphereConfig::MieScaleHeight::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MieScaleHeight = value;
 }

float SkyAtmosphereConfig::RayScaleHeight::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScaleHeight;
 }
 void SkyAtmosphereConfig::RayScaleHeight::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->RayScaleHeight = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::GroundAlbedo3::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->GroundAlbedo3)) , true);
 }
 void SkyAtmosphereConfig::GroundAlbedo3::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->GroundAlbedo3 = value;
 }

float SkyAtmosphereConfig::SFogMieScattScale::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->SFogMieScattScale;
 }
 void SkyAtmosphereConfig::SFogMieScattScale::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->SFogMieScattScale = value;
 }

Clicross::Float3^ SkyAtmosphereConfig::SkyLuminanceFactor::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::SkyAtmosphereConfig*>(this->_native))->SkyLuminanceFactor)) , true);
 }
 void SkyAtmosphereConfig::SkyLuminanceFactor::set(Clicross::Float3^ value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->SkyLuminanceFactor = value;
 }

float SkyAtmosphereConfig::MultiScatteringFactor::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MultiScatteringFactor;
 }
 void SkyAtmosphereConfig::MultiScatteringFactor::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->MultiScatteringFactor = value;
 }

float SkyAtmosphereConfig::HeightFogContribution::get()
 {
	return (static_cast<cross::SkyAtmosphereConfig*>(this->_native))->HeightFogContribution;
 }
 void SkyAtmosphereConfig::HeightFogContribution::set(float value )
 {
	(static_cast<cross::SkyAtmosphereConfig*>(this->_native))->HeightFogContribution = value;
 }


//constructor export here
SkyAtmosphereConfig::SkyAtmosphereConfig( )
    :SkyAtmosphereConfig(new cross::SkyAtmosphereConfig(), true)
{
}



SkyAtmosphereConfig::SkyAtmosphereConfig(const cross::SkyAtmosphereConfig * obj, bool created_by_clr): 
    _native(const_cast<cross::SkyAtmosphereConfig *>(obj))
	, _created_by_clr(created_by_clr)
{
}

SkyAtmosphereConfig::operator SkyAtmosphereConfig^ (const cross::SkyAtmosphereConfig* t)
{
    if(t)
    {
        return gcnew SkyAtmosphereConfig(const_cast<cross::SkyAtmosphereConfig*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// DoubleWGS84 export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
double DoubleWGS84::mLatitude::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mLatitude;
 }
 void DoubleWGS84::mLatitude::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mLatitude = value;
 }

double DoubleWGS84::mLongitude::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mLongitude;
 }
 void DoubleWGS84::mLongitude::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mLongitude = value;
 }

double DoubleWGS84::mElevation::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mElevation;
 }
 void DoubleWGS84::mElevation::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mElevation = value;
 }

double DoubleWGS84::mHeading::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mHeading;
 }
 void DoubleWGS84::mHeading::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mHeading = value;
 }

double DoubleWGS84::mPitch::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mPitch;
 }
 void DoubleWGS84::mPitch::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mPitch = value;
 }

double DoubleWGS84::mRoll::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mRoll;
 }
 void DoubleWGS84::mRoll::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mRoll = value;
 }

double DoubleWGS84::mZfight::get()
 {
	return (static_cast<cross::DoubleWGS84*>(this->_native))->mZfight;
 }
 void DoubleWGS84::mZfight::set(double value )
 {
	(static_cast<cross::DoubleWGS84*>(this->_native))->mZfight = value;
 }


//constructor export here
DoubleWGS84::DoubleWGS84( )
    :DoubleWGS84(new cross::DoubleWGS84(), true)
{
}

DoubleWGS84::DoubleWGS84(double lat, double lon, double alt, double head, double pitch, double roll )
    :DoubleWGS84(new cross::DoubleWGS84(lat, lon, alt, head, pitch, roll), true)
{
}



DoubleWGS84::DoubleWGS84(const cross::DoubleWGS84 * obj, bool created_by_clr): 
    _native(const_cast<cross::DoubleWGS84 *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DoubleWGS84::operator DoubleWGS84^ (const cross::DoubleWGS84* t)
{
    if(t)
    {
        return gcnew DoubleWGS84(const_cast<cross::DoubleWGS84*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// InstancedStaticModelSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
InstancedStaticModelSystemG::InstancedStaticModelSystemG( )
    :InstancedStaticModelSystemG(new cross::InstancedStaticModelSystemG(), true)
{
}



InstancedStaticModelSystemG::InstancedStaticModelSystemG(const cross::InstancedStaticModelSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

InstancedStaticModelSystemG::operator InstancedStaticModelSystemG^ (const cross::InstancedStaticModelSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew InstancedStaticModelSystemG(const_cast<cross::InstancedStaticModelSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (InstancedStaticModelSystemG^)managedObj;
    }
    else
        return nullptr;
}

bool InstancedStaticModelSystemG::SetModelAssetPath(Clicross::ecs::EntityIDStruct^ modelH, System::String^ assetpath )
{
    return (static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->SetModelAssetPath( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Write(), ClangenCli::ToNativeString(assetpath).c_str());
}

System::String^ InstancedStaticModelSystemG::GetModelAssetPath(Clicross::ecs::EntityIDStruct^ modelH )
{
    return ClangenCli::ToManagedString((((static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->GetModelAssetPath( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Read()))).c_str());
}

unsigned int InstancedStaticModelSystemG::GetModelAssetLODCount(Clicross::ecs::EntityIDStruct^ modelH )
{
    return (static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->GetModelAssetLODCount( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Read());
}

System::String^ InstancedStaticModelSystemG::GetModelMaterialPath(Clicross::ecs::EntityIDStruct^ modelH, unsigned int subModelIndex, unsigned int lodIndex )
{
    return ClangenCli::ToManagedString((((static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->GetModelMaterialPath( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Read(), subModelIndex, lodIndex))).c_str());
}

bool InstancedStaticModelSystemG::SetInstanceDataResourcePath(Clicross::ecs::EntityIDStruct^ modelH, System::String^ resourcePath )
{
    return (static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->SetInstanceDataResourcePath( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Write(), ClangenCli::ToNativeString(resourcePath).c_str());
}

System::String^ InstancedStaticModelSystemG::GetInstanceDataResourcePath(Clicross::ecs::EntityIDStruct^ modelH )
{
    return ClangenCli::ToManagedString((((static_cast<cross::InstancedStaticModelSystemG*>(this->_native))->GetInstanceDataResourcePath( static_cast<cross::InstancedStaticModelSystemG*>(this->_native)->GetGameWorld()->GetComponent<cross::InstancedStaticModelComponentG>(modelH).Read()))).c_str());
}


}   //end namespace Clicross

// HierachicalInstancedStaticModelSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
HierachicalInstancedStaticModelSystemG::HierachicalInstancedStaticModelSystemG( )
    :HierachicalInstancedStaticModelSystemG(new cross::HierachicalInstancedStaticModelSystemG(), true)
{
}



HierachicalInstancedStaticModelSystemG::HierachicalInstancedStaticModelSystemG(const cross::HierachicalInstancedStaticModelSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

HierachicalInstancedStaticModelSystemG::operator HierachicalInstancedStaticModelSystemG^ (const cross::HierachicalInstancedStaticModelSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew HierachicalInstancedStaticModelSystemG(const_cast<cross::HierachicalInstancedStaticModelSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (HierachicalInstancedStaticModelSystemG^)managedObj;
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// UserParam export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
bool UserParam::buttonR::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->buttonR;
 }
 void UserParam::buttonR::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->buttonR = value;
 }

bool UserParam::buttonG::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->buttonG;
 }
 void UserParam::buttonG::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->buttonG = value;
 }

bool UserParam::buttonB::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->buttonB;
 }
 void UserParam::buttonB::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->buttonB = value;
 }

bool UserParam::buttonA::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->buttonA;
 }
 void UserParam::buttonA::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->buttonA = value;
 }

bool UserParam::isRGBA::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->isRGBA;
 }
 void UserParam::isRGBA::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->isRGBA = value;
 }

bool UserParam::isSRGB::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->isSRGB;
 }
 void UserParam::isSRGB::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->isSRGB = value;
 }

bool UserParam::isMipmap::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->isMipmap;
 }
 void UserParam::isMipmap::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->isMipmap = value;
 }

bool UserParam::isUnPack::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->isUnPack;
 }
 void UserParam::isUnPack::set(bool value )
 {
	(static_cast<cross::UserParam*>(this->_native))->isUnPack = value;
 }

signed char UserParam::MipLevel::get()
 {
	return (static_cast<cross::UserParam*>(this->_native))->MipLevel;
 }
 void UserParam::MipLevel::set(signed char value )
 {
	(static_cast<cross::UserParam*>(this->_native))->MipLevel = value;
 }


//constructor export here
UserParam::UserParam(): UserParam(new cross::UserParam(), true) {}


UserParam::UserParam(const cross::UserParam * obj, bool created_by_clr): 
    _native(const_cast<cross::UserParam *>(obj))
	, _created_by_clr(created_by_clr)
{
}

UserParam::operator UserParam^ (const cross::UserParam* t)
{
    if(t)
    {
        return gcnew UserParam(const_cast<cross::UserParam*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// EditorUICanvasInterface export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EditorUICanvasInterface::EditorUICanvasInterface( )
    :EditorUICanvasInterface(new cross::EditorUICanvasInterface(), true)
{
}



EditorUICanvasInterface::EditorUICanvasInterface(const cross::EditorUICanvasInterface * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorUICanvasInterface *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorUICanvasInterface::operator EditorUICanvasInterface^ (const cross::EditorUICanvasInterface* t)
{
    if(t)
    {
        return gcnew EditorUICanvasInterface(const_cast<cross::EditorUICanvasInterface*>(t));
    }
    else
        return nullptr;
}

Clicross::EditorUICanvasInterface^ EditorUICanvasInterface::Instance( )
{
    return (Clicross::EditorUICanvasInterface^)(cross::EditorUICanvasInterface::Instance( ));
}

void EditorUICanvasInterface::Initialize(IntPtr _param_1 )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->Initialize( reinterpret_cast<void (*)()>(_param_1.ToPointer()));
}

int EditorUICanvasInterface::FindUIImage(System::String^ filename )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->FindUIImage( ClangenCli::ToNativeString(filename).c_str());
}

int EditorUICanvasInterface::LoadUIImage(System::String^ filename )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->LoadUIImage( ClangenCli::ToNativeString(filename).c_str());
}

int EditorUICanvasInterface::ReloadUIImage(System::String^ filename )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->ReloadUIImage( ClangenCli::ToNativeString(filename).c_str());
}

int EditorUICanvasInterface::LoadUIImageSRGB(System::String^ filename )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->LoadUIImageSRGB( ClangenCli::ToNativeString(filename).c_str());
}

int EditorUICanvasInterface::LoadUIImageRGBA(System::String^ filename, bool cR, bool cG, bool cB, bool cA )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->LoadUIImageRGBA( ClangenCli::ToNativeString(filename).c_str(), cR, cG, cB, cA);
}

int EditorUICanvasInterface::LoadUIImageUserDefined(System::String^ filename, Clicross::UserParam^ parameters )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->LoadUIImageUserDefined( ClangenCli::ToNativeString(filename).c_str(), *((cross::UserParam*)(parameters)));
}

int EditorUICanvasInterface::LoadUIImageEcotopeMap(Clicross::EcotopeMap^ map )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->LoadUIImageEcotopeMap( ( cross::EcotopeMap* )(map));
}

bool EditorUICanvasInterface::ReleaseUIImageUserDefined(System::String^ filename, Clicross::UserParam^ parameters )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->ReleaseUIImageUserDefined( ClangenCli::ToNativeString(filename).c_str(), *((cross::UserParam*)(parameters)));
}

int EditorUICanvasInterface::CreateUIImage(int width, int height )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->CreateUIImage( width, height);
}

int EditorUICanvasInterface::CreateUIImageByResource(Clicross::Resource^ resource )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->CreateUIImageByResource( ( cross::Resource* )(resource));
}

int EditorUICanvasInterface::CreateSceneImage(Clicross::GameWorld^ world, int width, int height )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->CreateSceneImage( ( cross::GameWorld* )(world), width, height);
}

int EditorUICanvasInterface::CreateSceneCameraImage(Clicross::GameWorld^ world, Clicross::ecs::EntityIDStruct^ cameraEntity, int width, int height )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->CreateSceneCameraImage( ( cross::GameWorld* )(world), *((cross::ecs::EntityIDStruct*)(cameraEntity)), width, height);
}

void EditorUICanvasInterface::ResizeSceneImage(Clicross::GameWorld^ world, int image, int width, int height )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->ResizeSceneImage( ( cross::GameWorld* )(world), image, width, height);
}

void EditorUICanvasInterface::ResizeSceneCameraImage(Clicross::GameWorld^ world, Clicross::ecs::EntityIDStruct^ cameraEntity, int image, int width, int height )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->ResizeSceneCameraImage( ( cross::GameWorld* )(world), *((cross::ecs::EntityIDStruct*)(cameraEntity)), image, width, height);
}

void EditorUICanvasInterface::SaveImage(Clicross::IRenderWindow^ window, int image, System::String^ filename, bool saveAlpha )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->SaveImage( ( cross::IRenderWindow* )(window), image, ClangenCli::ToNativeString(filename).c_str(), saveAlpha);
}

void EditorUICanvasInterface::ClearReadBackTask( )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->ClearReadBackTask( );
}

int EditorUICanvasInterface::GetImageWidth(int image )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->GetImageWidth( image);
}

int EditorUICanvasInterface::GetImageHeight(int image )
{
    return (static_cast<cross::EditorUICanvasInterface*>(this->_native))->GetImageHeight( image);
}

void EditorUICanvasInterface::SetFilter(int image, int textureFilter )
{
    (static_cast<cross::EditorUICanvasInterface*>(this->_native))->SetFilter( image, textureFilter);
}


}   //end namespace Clicross

// EditorUIRenderInterface export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EditorUIRenderInterface::EditorUIRenderInterface(Clicross::EditorUICanvasInterface^ globalContext, Clicross::IRenderWindow^ window )
    :EditorUIRenderInterface(new cross::EditorUIRenderInterface(( cross::EditorUICanvasInterface& )(globalContext), ( cross::IRenderWindow* )(window)), true)
{
}



EditorUIRenderInterface::EditorUIRenderInterface(const cross::EditorUIRenderInterface * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorUIRenderInterface *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorUIRenderInterface::operator EditorUIRenderInterface^ (const cross::EditorUIRenderInterface* t)
{
    if(t)
    {
        return gcnew EditorUIRenderInterface(const_cast<cross::EditorUIRenderInterface*>(t));
    }
    else
        return nullptr;
}

void EditorUIRenderInterface::SetSize(int width, int height )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->SetSize( width, height);
}

void EditorUIRenderInterface::DrawImage(int image, int x, int y, int width, int height, Clicross::Float4^ color, bool filpy )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawImage( image, x, y, width, height, *((cross::Float4*)(color)), filpy);
}

void EditorUIRenderInterface::DrawImageEx(int image, int x, int y, int width, int height, int sourceX, int sourceY, int sourceWidth, int sourceHeight, Clicross::Float4^ color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawImageEx( image, x, y, width, height, sourceX, sourceY, sourceWidth, sourceHeight, *((cross::Float4*)(color)));
}

void EditorUIRenderInterface::DrawPolygonF(int image, int n, System::IntPtr x, System::IntPtr y, Clicross::Float4^ color, bool flipy, int batch )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawPolygonF( image, n, reinterpret_cast<float*>(x.ToInt64()), reinterpret_cast<float*>(y.ToInt64()), *((cross::Float4*)(color)), flipy, batch);
}

void EditorUIRenderInterface::DrawPolygonsF(int image, System::IntPtr points, int count, float width, Clicross::Float4^ color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawPolygonsF( image, reinterpret_cast<float*>(points.ToInt64()), count, width, *((cross::Float4*)(color)));
}

void EditorUIRenderInterface::DrawGrid(float cell_size, int tile_number, float scroll_offsetx, float scroll_offsety, float scroll_sizex, float scroll_sizey, Clicross::Float4^ thickColor, Clicross::Float4^ thinColor, int gridID )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawGrid( cell_size, tile_number, scroll_offsetx, scroll_offsety, scroll_sizex, scroll_sizey, *((cross::Float4*)(thickColor)), *((cross::Float4*)(thinColor)), gridID);
}

void EditorUIRenderInterface::DrawLineF(float p1_screen_x, float p1_screen_y, float p2_screen_x, float p2_screen_y, float line_width, float blendFactor, Clicross::Float4^ color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawLineF( p1_screen_x, p1_screen_y, p2_screen_x, p2_screen_y, line_width, blendFactor, *((cross::Float4*)(color)));
}

void EditorUIRenderInterface::DrawString(System::String^ String, Clicross::Float4^ color, int X, int Y, float scaleration )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawString( ClangenCli::ToNativeString(String).c_str(), *((cross::Float4*)(color)), X, Y, scaleration);
}

void EditorUIRenderInterface::DrawLine(int p1_screen_x, int p1_screen_y, int p2_screen_x, int p2_screen_y, float line_width, float blendFactor, Clicross::Float4^ color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawLine( p1_screen_x, p1_screen_y, p2_screen_x, p2_screen_y, line_width, blendFactor, *((cross::Float4*)(color)));
}

void EditorUIRenderInterface::DrawLink(int p1_screen_x, int p1_screen_y, int p2_screen_x, int p2_screen_y, float line_width, float blendFactor, Clicross::Float4^ color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawLink( p1_screen_x, p1_screen_y, p2_screen_x, p2_screen_y, line_width, blendFactor, *((cross::Float4*)(color)));
}

void EditorUIRenderInterface::DrawCircleF(int image, float x, float y, float radius, float width, int segment, unsigned int color )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawCircleF( image, x, y, radius, width, segment, color);
}

void EditorUIRenderInterface::DrawCirclesF(int image, int n, System::IntPtr x, System::IntPtr y, System::IntPtr radiuses, System::IntPtr widths, int segment, System::IntPtr colors )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawCirclesF( image, n, reinterpret_cast<float*>(x.ToInt64()), reinterpret_cast<float*>(y.ToInt64()), reinterpret_cast<float*>(radiuses.ToInt64()), reinterpret_cast<float*>(widths.ToInt64()), segment, reinterpret_cast<unsigned int*>(colors.ToInt64()));
}

void EditorUIRenderInterface::DrawGradient(System::IntPtr size, int n, System::IntPtr stopPoseX, System::IntPtr stopPoseY, System::IntPtr stopColors )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->DrawGradient( reinterpret_cast<int*>(size.ToInt64()), n, reinterpret_cast<unsigned int*>(stopPoseX.ToInt64()), reinterpret_cast<unsigned int*>(stopPoseY.ToInt64()), reinterpret_cast<unsigned int*>(stopColors.ToInt64()));
}

void EditorUIRenderInterface::SetClipRect(int x, int y, int width, int height )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->SetClipRect( x, y, width, height);
}

void EditorUIRenderInterface::ClearClipRect( )
{
    (static_cast<cross::EditorUIRenderInterface*>(this->_native))->ClearClipRect( );
}


}   //end namespace Clicross

// AnimationEditorUtil export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


AnimationEditorUtil::AnimationEditorUtil(const cross::AnimationEditorUtil * obj, bool created_by_clr): 
    _native(const_cast<cross::AnimationEditorUtil *>(obj))
	, _created_by_clr(created_by_clr)
{
}

AnimationEditorUtil::operator AnimationEditorUtil^ (const cross::AnimationEditorUtil* t)
{
    if(t)
    {
        return gcnew AnimationEditorUtil(const_cast<cross::AnimationEditorUtil*>(t));
    }
    else
        return nullptr;
}

bool AnimationEditorUtil::RunSkelt_ReplaceRunSkelt(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::skeleton::SkeletonResource^ skeltResPtr )
{
    return cross::AnimationEditorUtil::RunSkelt_ReplaceRunSkelt( ( cross::IGameWorld* )(world), entity, ( cross::skeleton::SkeletonResource* )(skeltResPtr));
}

void AnimationEditorUtil::RunSkelt_SetBoneRetargetingMode(Clicross::IGameWorld^ world, unsigned long long entity, int boneIndex, int mode )
{
    cross::AnimationEditorUtil::RunSkelt_SetBoneRetargetingMode( ( cross::IGameWorld* )(world), entity, boneIndex, static_cast<cross::skeleton::BoneTranslateRetargetingMode>(mode));
}

void AnimationEditorUtil::RunSkelt_SetBoneMirrorBoneName(Clicross::IGameWorld^ world, unsigned long long entity, int boneIndex, Clicross::UniqueString^ mirrorBoneName )
{
    cross::AnimationEditorUtil::RunSkelt_SetBoneMirrorBoneName( ( cross::IGameWorld* )(world), entity, boneIndex, (const cross::UniqueString& )(mirrorBoneName));
}

int AnimationEditorUtil::SkeletonRes_GetBoneRetargetingMode(Clicross::skeleton::SkeletonResource^ skeltResPtr, int boneIndex )
{
    return ((int)cross::AnimationEditorUtil::SkeletonRes_GetBoneRetargetingMode( ( cross::skeleton::SkeletonResource* )(skeltResPtr), boneIndex));
}

void AnimationEditorUtil::SkeletonRes_SetBoneRetargetingMode(Clicross::skeleton::SkeletonResource^ skeltResPtr, int boneIndex, int mode )
{
    cross::AnimationEditorUtil::SkeletonRes_SetBoneRetargetingMode( ( cross::skeleton::SkeletonResource* )(skeltResPtr), boneIndex, static_cast<cross::skeleton::BoneTranslateRetargetingMode>(mode));
}

Clicross::UniqueString^ AnimationEditorUtil::SkeletonRes_GetMirrorBoneName(Clicross::skeleton::SkeletonResource^ skeltResPtr, int boneIndex )
{
    return gcnew Clicross::UniqueString(new cross::UniqueString((cross::AnimationEditorUtil::SkeletonRes_GetMirrorBoneName( ( cross::skeleton::SkeletonResource* )(skeltResPtr), boneIndex))) , true);
}

void AnimationEditorUtil::SkeletonRes_SetMirrorBoneName(Clicross::skeleton::SkeletonResource^ skeltResPtr, int boneIndex, Clicross::UniqueString^ mirrorBoneName )
{
    cross::AnimationEditorUtil::SkeletonRes_SetMirrorBoneName( ( cross::skeleton::SkeletonResource* )(skeltResPtr), boneIndex, (const cross::UniqueString& )(mirrorBoneName));
}

bool AnimationEditorUtil::AnimSeqRes_MoveCursor(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, float position )
{
    return cross::AnimationEditorUtil::AnimSeqRes_MoveCursor( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), position);
}

bool AnimationEditorUtil::AnimSeqRes_AddPreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr )
{
    return cross::AnimationEditorUtil::AnimSeqRes_AddPreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr));
}

bool AnimationEditorUtil::AnimSeqRes_RemovePreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr )
{
    return cross::AnimationEditorUtil::AnimSeqRes_RemovePreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr));
}

int AnimationEditorUtil::AnimSeqRes_GetSyncMarkersCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr )
{
    return cross::AnimationEditorUtil::AnimSeqRes_GetSyncMarkersCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr));
}

System::String^ AnimationEditorUtil::AnimSeqRes_GetSyncMarkerData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, unsigned int index )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimSeqRes_GetSyncMarkerData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), index))).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_SetSyncMarkerData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, unsigned int index, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_SetSyncMarkerData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), index, ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_SetSyncMarkerDataByName(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ inMarkerName, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_SetSyncMarkerDataByName( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(inMarkerName).c_str(), ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_AddSyncMarkerData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ addMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_AddSyncMarkerData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(addMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_RemoveSyncMarkerData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ removeMarkerName )
{
    return cross::AnimationEditorUtil::AnimSeqRes_RemoveSyncMarkerData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(removeMarkerName).c_str());
}

int AnimationEditorUtil::AnimSeqRes_GetNotifiesCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr )
{
    return cross::AnimationEditorUtil::AnimSeqRes_GetNotifiesCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr));
}

System::String^ AnimationEditorUtil::AnimSeqRes_GetNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, unsigned int index )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimSeqRes_GetNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), index))).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_SetNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, unsigned int index, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_SetNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), index, ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_SetNotifyDataByName(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ inMarkerName, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_SetNotifyDataByName( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(inMarkerName).c_str(), ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_AddNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ addMarkerJson )
{
    return cross::AnimationEditorUtil::AnimSeqRes_AddNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(addMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_RemoveNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, System::String^ removeMarkerName )
{
    return cross::AnimationEditorUtil::AnimSeqRes_RemoveNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), ClangenCli::ToNativeString(removeMarkerName).c_str());
}

bool AnimationEditorUtil::AnimSeqRes_AddCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, Clicross::FloatCurveTrack^ inCurveData )
{
    return cross::AnimationEditorUtil::AnimSeqRes_AddCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), (const cross::FloatCurveTrack& )(inCurveData));
}

bool AnimationEditorUtil::AnimSeqRes_RemoveCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, Clicross::UniqueString^ inCurveDataName )
{
    return cross::AnimationEditorUtil::AnimSeqRes_RemoveCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), (const cross::UniqueString& )(inCurveDataName));
}

bool AnimationEditorUtil::AnimSeqRes_SetCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr, Clicross::FloatCurveTrack^ inCurveData )
{
    return cross::AnimationEditorUtil::AnimSeqRes_SetCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr), (const cross::FloatCurveTrack& )(inCurveData));
}

Clicross::FloatCurveList^ AnimationEditorUtil::AnimSeqRes_GetCurveList(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimSequenceRes^ seqPtr )
{
    return gcnew Clicross::FloatCurveList(new cross::FloatCurveList((cross::AnimationEditorUtil::AnimSeqRes_GetCurveList( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimSequenceRes* )(seqPtr)))) , true);
}

Clicross::UniqueString^ AnimationEditorUtil::AnimCmpRes_GetSlotName(Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return gcnew Clicross::UniqueString(new cross::UniqueString((cross::AnimationEditorUtil::AnimCmpRes_GetSlotName( ( cross::anim::AnimCompositeRes* )(cmpPtr)))) , true);
}

bool AnimationEditorUtil::AnimCmpRes_MoveCursor(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, float position )
{
    return cross::AnimationEditorUtil::AnimCmpRes_MoveCursor( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), position);
}

bool AnimationEditorUtil::AnimCmpRes_AddPreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return cross::AnimationEditorUtil::AnimCmpRes_AddPreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr));
}

bool AnimationEditorUtil::AnimCmpRes_RemovePreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return cross::AnimationEditorUtil::AnimCmpRes_RemovePreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr));
}

bool AnimationEditorUtil::AnimCmpRes_InsertSegment(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, System::String^ inMarkerJson, int preIndexInTrack )
{
    return cross::AnimationEditorUtil::AnimCmpRes_InsertSegment( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), ClangenCli::ToNativeString(inMarkerJson).c_str(), preIndexInTrack);
}

bool AnimationEditorUtil::AnimCmpRes_RemoveSegment(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, int indexInTrack )
{
    return cross::AnimationEditorUtil::AnimCmpRes_RemoveSegment( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), indexInTrack);
}

bool AnimationEditorUtil::AnimCmpRes_SetSegmentData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, System::String^ inMarkerJson, int indexInTrack )
{
    return cross::AnimationEditorUtil::AnimCmpRes_SetSegmentData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), ClangenCli::ToNativeString(inMarkerJson).c_str(), indexInTrack);
}

int AnimationEditorUtil::AnimCmpRes_GetSegmentCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return cross::AnimationEditorUtil::AnimCmpRes_GetSegmentCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr));
}

System::String^ AnimationEditorUtil::AnimCmpRes_GetSegmentData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int indexInTrack )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimCmpRes_GetSegmentData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), indexInTrack))).c_str());
}

int AnimationEditorUtil::AnimCmpRes_GetNotifyTrackCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return cross::AnimationEditorUtil::AnimCmpRes_GetNotifyTrackCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr));
}

int AnimationEditorUtil::AnimCmpRes_GetNotifiesCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex )
{
    return cross::AnimationEditorUtil::AnimCmpRes_GetNotifiesCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex);
}

System::String^ AnimationEditorUtil::AnimCmpRes_GetNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex, unsigned int index )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimCmpRes_GetNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex, index))).c_str());
}

bool AnimationEditorUtil::AnimCmpRes_SetNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex, unsigned int index, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimCmpRes_SetNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex, index, ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimCmpRes_SetNotifyDataByName(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex, System::String^ inMarkerName, System::String^ inMarkerJson )
{
    return cross::AnimationEditorUtil::AnimCmpRes_SetNotifyDataByName( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex, ClangenCli::ToNativeString(inMarkerName).c_str(), ClangenCli::ToNativeString(inMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimCmpRes_AddNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex, System::String^ addMarkerJson )
{
    return cross::AnimationEditorUtil::AnimCmpRes_AddNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex, ClangenCli::ToNativeString(addMarkerJson).c_str());
}

bool AnimationEditorUtil::AnimCmpRes_RemoveNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, unsigned int trackIndex, System::String^ removeMarkerName )
{
    return cross::AnimationEditorUtil::AnimCmpRes_RemoveNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), trackIndex, ClangenCli::ToNativeString(removeMarkerName).c_str());
}

bool AnimationEditorUtil::AnimCmpRes_AddCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, Clicross::FloatCurveTrack^ inCurveData )
{
    return cross::AnimationEditorUtil::AnimCmpRes_AddCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), (const cross::FloatCurveTrack& )(inCurveData));
}

bool AnimationEditorUtil::AnimCmpRes_RemoveCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, Clicross::UniqueString^ inCurveDataName )
{
    return cross::AnimationEditorUtil::AnimCmpRes_RemoveCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), (const cross::UniqueString& )(inCurveDataName));
}

bool AnimationEditorUtil::AnimCmpRes_SetCurveTrack(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr, Clicross::FloatCurveTrack^ inCurveData )
{
    return cross::AnimationEditorUtil::AnimCmpRes_SetCurveTrack( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr), (const cross::FloatCurveTrack& )(inCurveData));
}

Clicross::FloatCurveList^ AnimationEditorUtil::AnimCmpRes_GetCurveList(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimCompositeRes^ cmpPtr )
{
    return gcnew Clicross::FloatCurveList(new cross::FloatCurveList((cross::AnimationEditorUtil::AnimCmpRes_GetCurveList( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimCompositeRes* )(cmpPtr)))) , true);
}

bool AnimationEditorUtil::AnimatrixRes_MoveCursor(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, System::String^ slotName, float position )
{
    return cross::AnimationEditorUtil::AnimatrixRes_MoveCursor( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), ClangenCli::ToNativeString(slotName).c_str(), position);
}

bool AnimationEditorUtil::AnimatrixRes_AddPreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr )
{
    return cross::AnimationEditorUtil::AnimatrixRes_AddPreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr));
}

bool AnimationEditorUtil::AnimatrixRes_RemovePreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr )
{
    return cross::AnimationEditorUtil::AnimatrixRes_RemovePreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr));
}

int AnimationEditorUtil::AnimatrixRes_GetSlotNamesCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr )
{
    return cross::AnimationEditorUtil::AnimatrixRes_GetSlotNamesCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr));
}

System::String^ AnimationEditorUtil::AnimatrixRes_GetSlotName(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, unsigned int index )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimatrixRes_GetSlotName( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), index))).c_str());
}

int AnimationEditorUtil::AnimatrixRes_GetNotifyTrackCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr )
{
    return cross::AnimationEditorUtil::AnimatrixRes_GetNotifyTrackCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr));
}

int AnimationEditorUtil::AnimatrixRes_GetNotifiesCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, unsigned int trackIndex )
{
    return cross::AnimationEditorUtil::AnimatrixRes_GetNotifiesCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), trackIndex);
}

System::String^ AnimationEditorUtil::AnimatrixRes_GetNotifyData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, unsigned int trackIndex, unsigned int index )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimatrixRes_GetNotifyData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), trackIndex, index))).c_str());
}

int AnimationEditorUtil::AnimatrixRes_GetSegmentCount(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, System::String^ slotName )
{
    return cross::AnimationEditorUtil::AnimatrixRes_GetSegmentCount( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), ClangenCli::ToNativeString(slotName).c_str());
}

System::String^ AnimationEditorUtil::AnimatrixRes_GetSegmentData(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::AnimatrixRes^ animtrixPtr, System::String^ slotName, unsigned int indexInTrack )
{
    return ClangenCli::ToManagedString(((cross::AnimationEditorUtil::AnimatrixRes_GetSegmentData( ( cross::IGameWorld* )(world), entity, ( cross::anim::AnimatrixRes* )(animtrixPtr), ClangenCli::ToNativeString(slotName).c_str(), indexInTrack))).c_str());
}

bool AnimationEditorUtil::AnimMMRes_MoveCursor(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::MotionDataAsset^ mmPtr, System::String^ animName, float position )
{
    return cross::AnimationEditorUtil::AnimMMRes_MoveCursor( ( cross::IGameWorld* )(world), entity, ( cross::anim::MotionDataAsset* )(mmPtr), ClangenCli::ToNativeString(animName).c_str(), position);
}

bool AnimationEditorUtil::AnimMMRes_AddPreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::MotionDataAsset^ mmPtr )
{
    return cross::AnimationEditorUtil::AnimMMRes_AddPreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::MotionDataAsset* )(mmPtr));
}

bool AnimationEditorUtil::AnimMMRes_RemovePreview(Clicross::IGameWorld^ world, unsigned long long entity, Clicross::anim::MotionDataAsset^ mmPtr )
{
    return cross::AnimationEditorUtil::AnimMMRes_RemovePreview( ( cross::IGameWorld* )(world), entity, ( cross::anim::MotionDataAsset* )(mmPtr));
}

void AnimationEditorUtil::AnimStbDebug_GetDebugItem(Clicross::IGameWorld^ world, unsigned long long entity, UnknowKeeper^ outData )
{
    cross::AnimationEditorUtil::AnimStbDebug_GetDebugItem( ( cross::IGameWorld* )(world), entity, (UnknowKeeper::get_native_with_type_for_reference< cross::anim::StbDebugData& >(outData)));
}

Clicross::Resource^ AnimationEditorUtil::AnimationCreateAnimator(System::String^ animatorContentStr, System::String^ resourceRefStr )
{
    return (Clicross::Resource^)(cross::AnimationEditorUtil::AnimationCreateAnimator( ClangenCli::ToNativeString(animatorContentStr).c_str(), ClangenCli::ToNativeString(resourceRefStr).c_str()));
}

Clicross::Resource^ AnimationEditorUtil::Animation_CreateMotionMatchData(System::String^ animation_sequence )
{
    return (Clicross::Resource^)(cross::AnimationEditorUtil::Animation_CreateMotionMatchData( ClangenCli::ToNativeString(animation_sequence).c_str()));
}

bool AnimationEditorUtil::Animation_CookMotionMatchData(Clicross::anim::MotionDataAsset^ dataset )
{
    return cross::AnimationEditorUtil::Animation_CookMotionMatchData( ( cross::anim::MotionDataAsset* )(dataset));
}

void AnimationEditorUtil::Animator_SetAnimatorEnable(Clicross::IGameWorld^ world, unsigned long long entity, bool enable )
{
    cross::AnimationEditorUtil::Animator_SetAnimatorEnable( ( cross::IGameWorld* )(world), entity, enable);
}

void AnimationEditorUtil::Skeleton_ResetSkeletonPhysics(Clicross::IGameWorld^ world, unsigned long long entity )
{
    cross::AnimationEditorUtil::Skeleton_ResetSkeletonPhysics( ( cross::IGameWorld* )(world), entity);
}

void AnimationEditorUtil::Skeleton_SetSkeletonCompEnable(Clicross::IGameWorld^ world, unsigned long long entity, bool enable )
{
    cross::AnimationEditorUtil::Skeleton_SetSkeletonCompEnable( ( cross::IGameWorld* )(world), entity, enable);
}

void AnimationEditorUtil::Skeleton_ResetRuntimeSkeleton(Clicross::IGameWorld^ world, unsigned long long entity )
{
    cross::AnimationEditorUtil::Skeleton_ResetRuntimeSkeleton( ( cross::IGameWorld* )(world), entity);
}


}   //end namespace Clicross

// EditorImGuiContext export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EditorImGuiContext::EditorImGuiContext( )
    :EditorImGuiContext(new cross::EditorImGuiContext(), true)
{
}

EditorImGuiContext::EditorImGuiContext(Clicross::EditorImGuiCallback^ callback )
    :EditorImGuiContext(new cross::EditorImGuiContext(( cross::EditorImGuiCallback* )(callback)), true)
{
}



EditorImGuiContext::EditorImGuiContext(const cross::EditorImGuiContext * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorImGuiContext *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorImGuiContext::operator EditorImGuiContext^ (const cross::EditorImGuiContext* t)
{
    if(t)
    {
        return gcnew EditorImGuiContext(const_cast<cross::EditorImGuiContext*>(t));
    }
    else
        return nullptr;
}

void EditorImGuiContext::OnMouseMoveEvent(int mouseX, int mouseY )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->OnMouseMoveEvent( mouseX, mouseY);
}

void EditorImGuiContext::OnMouseWheelEvent(int mouseDeltaZ )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->OnMouseWheelEvent( mouseDeltaZ);
}

void EditorImGuiContext::OnKeyEvent(Clicross::EditorKey btn, bool isDown )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->OnKeyEvent( static_cast<cross::EditorKey>(btn), isDown);
}

void EditorImGuiContext::OnInputCharacter(unsigned short c )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->OnInputCharacter( c);
}

void EditorImGuiContext::OnActivate(bool active )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->OnActivate( active);
}

void EditorImGuiContext::Update(Clicross::Float2^ offset, Clicross::Float2^ size )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->Update( (const cross::Float2& )(offset), (const cross::Float2& )(size));
}

void EditorImGuiContext::Paint(Clicross::Float2^ offset, Clicross::Float2^ size, Clicross::EditorUIRenderInterface^ renderInterface )
{
    (static_cast<cross::EditorImGuiContext*>(this->_native))->Paint( (const cross::Float2& )(offset), (const cross::Float2& )(size), ( cross::EditorUIRenderInterface& )(renderInterface));
}


}   //end namespace Clicross

// EditorApplication export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EditorApplication::EditorApplication( )
    :EditorApplication(new cross::EditorApplication(), true)
{
}



EditorApplication::EditorApplication(const cross::EditorApplication * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorApplication *>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorApplication::operator EditorApplication^ (const cross::EditorApplication* t)
{
    if(t)
    {
        return gcnew EditorApplication(const_cast<cross::EditorApplication*>(t));
    }
    else
        return nullptr;
}

bool EditorApplication::EditorInput_InitializeEngineInput(Clicross::CrossEngine^ crossEngine )
{
    return cross::EditorApplication::EditorInput_InitializeEngineInput( ( cross::CrossEngine* )(crossEngine));
}

bool EditorApplication::EditorInput_Tick(Clicross::CrossEngine^ crossEngine, float delta_time )
{
    return cross::EditorApplication::EditorInput_Tick( ( cross::CrossEngine* )(crossEngine), delta_time);
}

bool EditorApplication::EditorInput_SetFetchMousePosCallBack(Clicross::CrossEngine^ crossEngine, IntPtr _param_1 )
{
    return cross::EditorApplication::EditorInput_SetFetchMousePosCallBack( ( cross::CrossEngine* )(crossEngine), reinterpret_cast<void (*)(float*, float*)>(_param_1.ToPointer()));
}

bool EditorApplication::EditorInput_SetShowMouseCursorCallBack(Clicross::CrossEngine^ crossEngine, IntPtr _param_1 )
{
    return cross::EditorApplication::EditorInput_SetShowMouseCursorCallBack( ( cross::CrossEngine* )(crossEngine), reinterpret_cast<void (*)(bool)>(_param_1.ToPointer()));
}

bool EditorApplication::EditorInput_SetLockMouseCursorCallBack(Clicross::CrossEngine^ crossEngine, IntPtr _param_1 )
{
    return cross::EditorApplication::EditorInput_SetLockMouseCursorCallBack( ( cross::CrossEngine* )(crossEngine), reinterpret_cast<void (*)(float, float, float, float)>(_param_1.ToPointer()));
}

bool EditorApplication::EditorInput_SetUnLockMouseCursorCallBack(Clicross::CrossEngine^ crossEngine, IntPtr _param_1 )
{
    return cross::EditorApplication::EditorInput_SetUnLockMouseCursorCallBack( ( cross::CrossEngine* )(crossEngine), reinterpret_cast<void (*)()>(_param_1.ToPointer()));
}

bool EditorApplication::EditorInput_OnWindowActive(Clicross::CrossEngine^ crossEngine, bool bActive )
{
    return cross::EditorApplication::EditorInput_OnWindowActive( ( cross::CrossEngine* )(crossEngine), bActive);
}

bool EditorApplication::EditorInput_OnWindowResize(Clicross::CrossEngine^ crossEngine, int inLeft, int inTop, int inWidth, int inHeight )
{
    return cross::EditorApplication::EditorInput_OnWindowResize( ( cross::CrossEngine* )(crossEngine), inLeft, inTop, inWidth, inHeight);
}

bool EditorApplication::EditorInput_OnMouseDown(Clicross::CrossEngine^ crossEngine, int mouseCode, int cursorX, int cursorY )
{
    return cross::EditorApplication::EditorInput_OnMouseDown( ( cross::CrossEngine* )(crossEngine), mouseCode, cursorX, cursorY);
}

bool EditorApplication::EditorInput_OnMouseUp(Clicross::CrossEngine^ crossEngine, int mouseCode, int cursorX, int cursorY )
{
    return cross::EditorApplication::EditorInput_OnMouseUp( ( cross::CrossEngine* )(crossEngine), mouseCode, cursorX, cursorY);
}

bool EditorApplication::EditorInput_OnMouseDoubleClick(Clicross::CrossEngine^ crossEngine, int mouseCode, int cursorX, int cursorY )
{
    return cross::EditorApplication::EditorInput_OnMouseDoubleClick( ( cross::CrossEngine* )(crossEngine), mouseCode, cursorX, cursorY);
}

bool EditorApplication::EditorInput_OnRawMouseMove(Clicross::CrossEngine^ crossEngine, int deltaX, int deltaY )
{
    return cross::EditorApplication::EditorInput_OnRawMouseMove( ( cross::CrossEngine* )(crossEngine), deltaX, deltaY);
}

bool EditorApplication::EditorInput_OnMouseMove(Clicross::CrossEngine^ crossEngine )
{
    return cross::EditorApplication::EditorInput_OnMouseMove( ( cross::CrossEngine* )(crossEngine));
}

bool EditorApplication::EditorInput_OnMouseWheel(Clicross::CrossEngine^ crossEngine, int wheelDelta, int cursorX, int cursorY )
{
    return cross::EditorApplication::EditorInput_OnMouseWheel( ( cross::CrossEngine* )(crossEngine), wheelDelta, cursorX, cursorY);
}

bool EditorApplication::EditorInput_OnKeyDown(Clicross::CrossEngine^ crossEngine, int keyCode, char charCode, bool isRepeat )
{
    return cross::EditorApplication::EditorInput_OnKeyDown( ( cross::CrossEngine* )(crossEngine), keyCode, charCode, isRepeat);
}

bool EditorApplication::EditorInput_OnKeyUp(Clicross::CrossEngine^ crossEngine, int keyCode, char charCode, bool isRepeat )
{
    return cross::EditorApplication::EditorInput_OnKeyUp( ( cross::CrossEngine* )(crossEngine), keyCode, charCode, isRepeat);
}

bool EditorApplication::EditorInput_OnCharInput(Clicross::CrossEngine^ crossEngine, char charCode, bool isRepeat )
{
    return cross::EditorApplication::EditorInput_OnCharInput( ( cross::CrossEngine* )(crossEngine), charCode, isRepeat);
}


}   //end namespace Clicross

// EditorMsaPreviewSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


EditorMsaPreviewSystemG::EditorMsaPreviewSystemG(const cross::EditorMsaPreviewSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

EditorMsaPreviewSystemG::operator EditorMsaPreviewSystemG^ (const cross::EditorMsaPreviewSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew EditorMsaPreviewSystemG(const_cast<cross::EditorMsaPreviewSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (EditorMsaPreviewSystemG^)managedObj;
    }
    else
        return nullptr;
}

void EditorMsaPreviewSystemG::MSA_DrawLine(Clicross::IGameWorld^ world, Clicross::Float3^ startPos, Clicross::Float3^ endPos, bool isSelected )
{
    cross::EditorMsaPreviewSystemG::MSA_DrawLine( ( cross::IGameWorld* )(world), ( cross::Float3* )(startPos), ( cross::Float3* )(endPos), isSelected);
}


}   //end namespace Clicross

// EditorPrimitiveSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


EditorPrimitiveSystemG::EditorPrimitiveSystemG(const cross::EditorPrimitiveSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

EditorPrimitiveSystemG::operator EditorPrimitiveSystemG^ (const cross::EditorPrimitiveSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew EditorPrimitiveSystemG(const_cast<cross::EditorPrimitiveSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (EditorPrimitiveSystemG^)managedObj;
    }
    else
        return nullptr;
}

void EditorPrimitiveSystemG::World_DrawPrimitiveTriangles(Clicross::IGameWorld^ world, System::IntPtr vertexData, System::IntPtr indexData, unsigned short vertexCount, unsigned short indexCount, System::String^ materialPath )
{
    cross::EditorPrimitiveSystemG::World_DrawPrimitiveTriangles( ( cross::IGameWorld* )(world), reinterpret_cast<unsigned char*>(vertexData.ToInt64()), reinterpret_cast<unsigned short*>(indexData.ToInt64()), vertexCount, indexCount, ClangenCli::ToNativeString(materialPath).c_str());
}

void EditorPrimitiveSystemG::World_DrawMeshAABBTree(Clicross::IGameWorld^ world, bool flag )
{
    cross::EditorPrimitiveSystemG::World_DrawMeshAABBTree( ( cross::IGameWorld* )(world), flag);
}

void EditorPrimitiveSystemG::World_DrawInstanceAABBTree(Clicross::IGameWorld^ world, bool flag )
{
    cross::EditorPrimitiveSystemG::World_DrawInstanceAABBTree( ( cross::IGameWorld* )(world), flag);
}

void EditorPrimitiveSystemG::World_DrawTerrainAABBTree(Clicross::IGameWorld^ world, bool flag )
{
    cross::EditorPrimitiveSystemG::World_DrawTerrainAABBTree( ( cross::IGameWorld* )(world), flag);
}


}   //end namespace Clicross

// RayPickResult export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned long long RayPickResult::HitEntity::get()
 {
	return (static_cast<cross::RayPickResult*>(this->_native))->HitEntity;
 }
 void RayPickResult::HitEntity::set(unsigned long long value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->HitEntity = value;
 }

Clicross::Float3^ RayPickResult::HitPoint::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::RayPickResult*>(this->_native))->HitPoint)) , true);
 }
 void RayPickResult::HitPoint::set(Clicross::Float3^ value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->HitPoint = value;
 }

Clicross::Float3^ RayPickResult::HitFaceNormal::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::RayPickResult*>(this->_native))->HitFaceNormal)) , true);
 }
 void RayPickResult::HitFaceNormal::set(Clicross::Float3^ value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->HitFaceNormal = value;
 }

int RayPickResult::ModelIndex::get()
 {
	return (static_cast<cross::RayPickResult*>(this->_native))->ModelIndex;
 }
 void RayPickResult::ModelIndex::set(int value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->ModelIndex = value;
 }

int RayPickResult::MeshIndex::get()
 {
	return (static_cast<cross::RayPickResult*>(this->_native))->MeshIndex;
 }
 void RayPickResult::MeshIndex::set(int value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->MeshIndex = value;
 }

bool RayPickResult::Result::get()
 {
	return (static_cast<cross::RayPickResult*>(this->_native))->Result;
 }
 void RayPickResult::Result::set(bool value )
 {
	(static_cast<cross::RayPickResult*>(this->_native))->Result = value;
 }


//constructor export here
RayPickResult::RayPickResult(): RayPickResult(new cross::RayPickResult(), true) {}


RayPickResult::RayPickResult(const cross::RayPickResult * obj, bool created_by_clr): 
    _native(const_cast<cross::RayPickResult *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RayPickResult::operator RayPickResult^ (const cross::RayPickResult* t)
{
    if(t)
    {
        return gcnew RayPickResult(const_cast<cross::RayPickResult*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// RayPickResultDouble export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
unsigned long long RayPickResultDouble::HitEntity::get()
 {
	return (static_cast<cross::RayPickResultDouble*>(this->_native))->HitEntity;
 }
 void RayPickResultDouble::HitEntity::set(unsigned long long value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->HitEntity = value;
 }

Clicross::Double3^ RayPickResultDouble::HitPoint::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cross::RayPickResultDouble*>(this->_native))->HitPoint)) , true);
 }
 void RayPickResultDouble::HitPoint::set(Clicross::Double3^ value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->HitPoint = value;
 }

Clicross::Double3^ RayPickResultDouble::HitFaceNormal::get()
 {
	return gcnew Clicross::Double3(new cross::Double3(((static_cast<cross::RayPickResultDouble*>(this->_native))->HitFaceNormal)) , true);
 }
 void RayPickResultDouble::HitFaceNormal::set(Clicross::Double3^ value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->HitFaceNormal = value;
 }

int RayPickResultDouble::ModelIndex::get()
 {
	return (static_cast<cross::RayPickResultDouble*>(this->_native))->ModelIndex;
 }
 void RayPickResultDouble::ModelIndex::set(int value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->ModelIndex = value;
 }

int RayPickResultDouble::MeshIndex::get()
 {
	return (static_cast<cross::RayPickResultDouble*>(this->_native))->MeshIndex;
 }
 void RayPickResultDouble::MeshIndex::set(int value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->MeshIndex = value;
 }

bool RayPickResultDouble::Result::get()
 {
	return (static_cast<cross::RayPickResultDouble*>(this->_native))->Result;
 }
 void RayPickResultDouble::Result::set(bool value )
 {
	(static_cast<cross::RayPickResultDouble*>(this->_native))->Result = value;
 }


//constructor export here
RayPickResultDouble::RayPickResultDouble(): RayPickResultDouble(new cross::RayPickResultDouble(), true) {}


RayPickResultDouble::RayPickResultDouble(const cross::RayPickResultDouble * obj, bool created_by_clr): 
    _native(const_cast<cross::RayPickResultDouble *>(obj))
	, _created_by_clr(created_by_clr)
{
}

RayPickResultDouble::operator RayPickResultDouble^ (const cross::RayPickResultDouble* t)
{
    if(t)
    {
        return gcnew RayPickResultDouble(const_cast<cross::RayPickResultDouble*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// PlaneList export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::PlaneList::planes export start
	#define STLDECL_MANAGEDTYPE Clicross::Plane2^
	#define STLDECL_NATIVETYPE cross::Plane2
	CPP_DECLARE_STLVECTOR(PlaneList::, planesCliType, STLDECL_MANAGEDTYPE, STLDECL_NATIVETYPE)
	#undef STLDECL_MANAGEDTYPE
	#undef STLDECL_NATIVETYPE


//fields export here
PlaneList::planesCliType^ PlaneList::planes::get()
 {
	return (static_cast<cross::PlaneList*>(this->_native))->planes;
 }
 void PlaneList::planes::set(PlaneList::planesCliType^ value )
 {
	(static_cast<cross::PlaneList*>(this->_native))->planes = *value->_native;
 }


//constructor export here
PlaneList::PlaneList(): PlaneList(new cross::PlaneList(), true) {}


PlaneList::PlaneList(const cross::PlaneList * obj, bool created_by_clr): 
    _native(const_cast<cross::PlaneList *>(obj))
	, _created_by_clr(created_by_clr)
{
}

PlaneList::operator PlaneList^ (const cross::PlaneList* t)
{
    if(t)
    {
        return gcnew PlaneList(const_cast<cross::PlaneList*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// GameWorldInterface export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


GameWorldInterface::GameWorldInterface(const cross::GameWorldInterface * obj, bool created_by_clr): 
    _native(const_cast<cross::GameWorldInterface *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GameWorldInterface::operator GameWorldInterface^ (const cross::GameWorldInterface* t)
{
    if(t)
    {
        return gcnew GameWorldInterface(const_cast<cross::GameWorldInterface*>(t));
    }
    else
        return nullptr;
}

bool GameWorldInterface::World_SelectEntities(Clicross::IGameWorld^ world, Clicross::EntityList^ entities )
{
    return cross::GameWorldInterface::World_SelectEntities( ( cross::IGameWorld* )(world), *((cross::EntityList*)(entities)));
}

Clicross::RayPickResult^ GameWorldInterface::World_RayPick(Clicross::IGameWorld^ world, Clicross::Float3^ origin, Clicross::Float3^ direction, Clicross::Float3^ rayBaseTilePosition, unsigned int flag, Clicross::EntityList^ ignorentities )
{
    return gcnew Clicross::RayPickResult(new cross::RayPickResult((cross::GameWorldInterface::World_RayPick( ( cross::IGameWorld* )(world), *((cross::Float3*)(origin)), *((cross::Float3*)(direction)), *((cross::Float3*)(rayBaseTilePosition)), flag, (const cross::EntityList& )(ignorentities)))) , true);
}

Clicross::RayPickResultDouble^ GameWorldInterface::World_RayPick_DoublePrecision(Clicross::IGameWorld^ world, Clicross::Double3^ origin, Clicross::Double3^ direction, Clicross::Double3^ rayBaseTilePosition, unsigned int flag, Clicross::EntityList^ ignorentities )
{
    return gcnew Clicross::RayPickResultDouble(new cross::RayPickResultDouble((cross::GameWorldInterface::World_RayPick_DoublePrecision( ( cross::IGameWorld* )(world), *((cross::Double3*)(origin)), *((cross::Double3*)(direction)), *((cross::Double3*)(rayBaseTilePosition)), flag, (const cross::EntityList& )(ignorentities)))) , true);
}

bool GameWorldInterface::World_BuildingFallGroud(Clicross::IGameWorld^ world, unsigned long long building, int cameraMode )
{
    return cross::GameWorldInterface::World_BuildingFallGroud( ( cross::IGameWorld* )(world), building, cameraMode);
}

int GameWorldInterface::World_IntersectSphere(Clicross::IGameWorld^ world, unsigned long long entityID, Clicross::Float3^ center, float radius, Clicross::Float3^ baseTilePosition )
{
    return cross::GameWorldInterface::World_IntersectSphere( ( cross::IGameWorld* )(world), entityID, (const cross::Float3& )(center), radius, *((cross::Float3*)(baseTilePosition)));
}

void GameWorldInterface::World_CreateComponentByTypeMask(Clicross::IGameWorld^ world, unsigned long long entity, unsigned long long mask0, unsigned long long mask1, unsigned long long mask2, unsigned long long mask3 )
{
    cross::GameWorldInterface::World_CreateComponentByTypeMask( ( cross::IGameWorld* )(world), entity, mask0, mask1, mask2, mask3);
}

void GameWorldInterface::World_DestroyComponentByTypeMask(Clicross::IGameWorld^ world, unsigned long long entity, unsigned long long mask0, unsigned long long mask1, unsigned long long mask2, unsigned long long mask3 )
{
    cross::GameWorldInterface::World_DestroyComponentByTypeMask( ( cross::IGameWorld* )(world), entity, mask0, mask1, mask2, mask3);
}

bool GameWorldInterface::World_HasComponents(Clicross::IGameWorld^ world, unsigned long long entity, System::String^ componentname )
{
    return cross::GameWorldInterface::World_HasComponents( ( cross::IGameWorld* )(world), entity, ClangenCli::ToNativeString(componentname).c_str());
}

int GameWorldInterface::World_GetComponentsNum(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetComponentsNum( ( cross::IGameWorld* )(world), entity);
}

System::String^ GameWorldInterface::World_GetComponentsName(Clicross::IGameWorld^ world, unsigned long long entity, int index )
{
    return ClangenCli::ToManagedString(((cross::GameWorldInterface::World_GetComponentsName( ( cross::IGameWorld* )(world), entity, index))).c_str());
}

unsigned long long GameWorldInterface::World_GetEntityParent(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetEntityParent( ( cross::IGameWorld* )(world), entity);
}

unsigned long long GameWorldInterface::World_GetEntityPreSibling(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetEntityPreSibling( ( cross::IGameWorld* )(world), entity);
}

unsigned long long GameWorldInterface::World_GetEntityNextSibling(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetEntityNextSibling( ( cross::IGameWorld* )(world), entity);
}

unsigned long long GameWorldInterface::World_GetEntityFirstChild(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetEntityFirstChild( ( cross::IGameWorld* )(world), entity);
}

unsigned long long GameWorldInterface::World_GetChildEntityNumber(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetChildEntityNumber( ( cross::IGameWorld* )(world), entity);
}

System::String^ GameWorldInterface::World_GetEntityName(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return ClangenCli::ToManagedString(((cross::GameWorldInterface::World_GetEntityName( ( cross::IGameWorld* )(world), entity))).c_str());
}

void GameWorldInterface::World_SetEntityName(Clicross::IGameWorld^ world, unsigned long long entity, System::String^ name )
{
    cross::GameWorldInterface::World_SetEntityName( ( cross::IGameWorld* )(world), entity, ClangenCli::ToNativeString(name).c_str());
}

unsigned long long GameWorldInterface::World_GetRootEntity(Clicross::IGameWorld^ world )
{
    return cross::GameWorldInterface::World_GetRootEntity( ( cross::IGameWorld* )(world));
}

unsigned long long GameWorldInterface::World_GetEntityComponentsCount(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_GetEntityComponentsCount( ( cross::IGameWorld* )(world), entity);
}

System::String^ GameWorldInterface::World_GetEntityComponentsNameByIndex(Clicross::IGameWorld^ world, unsigned long long entity, unsigned long long index )
{
    return ClangenCli::ToManagedString(((cross::GameWorldInterface::World_GetEntityComponentsNameByIndex( ( cross::IGameWorld* )(world), entity, index))).c_str());
}

bool GameWorldInterface::World_JointToRoot(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_JointToRoot( ( cross::IGameWorld* )(world), entity);
}

bool GameWorldInterface::World_Joint(Clicross::IGameWorld^ world, unsigned long long childEntity, unsigned long long parentEntity )
{
    return cross::GameWorldInterface::World_Joint( ( cross::IGameWorld* )(world), childEntity, parentEntity);
}

bool GameWorldInterface::World_DisjointParent(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_DisjointParent( ( cross::IGameWorld* )(world), entity);
}

bool GameWorldInterface::World_DisjointChildren(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_DisjointChildren( ( cross::IGameWorld* )(world), entity);
}

bool GameWorldInterface::World_IsEntityAlive(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::World_IsEntityAlive( ( cross::IGameWorld* )(world), entity);
}

bool GameWorldInterface::World_LoadEntity(Clicross::IGameWorld^ world, unsigned long long mountEntity, System::String^ path )
{
    return cross::GameWorldInterface::World_LoadEntity( ( cross::IGameWorld* )(world), mountEntity, ClangenCli::ToNativeString(path).c_str());
}

bool GameWorldInterface::World_SaveEntity(Clicross::IGameWorld^ world, unsigned long long entity, System::String^ path )
{
    return cross::GameWorldInterface::World_SaveEntity( ( cross::IGameWorld* )(world), entity, ClangenCli::ToNativeString(path).c_str());
}

bool GameWorldInterface::World_TriggerRealTimeProbeCaptureByScript(Clicross::IGameWorld^ world )
{
    return cross::GameWorldInterface::World_TriggerRealTimeProbeCaptureByScript( ( cross::IGameWorld* )(world));
}

bool GameWorldInterface::World_SetTOD4Time(Clicross::IGameWorld^ world, Clicross::TOD4TimeState state )
{
    return cross::GameWorldInterface::World_SetTOD4Time( ( cross::IGameWorld* )(world), static_cast<cross::TOD4TimeState>(state));
}

bool GameWorldInterface::World_SetTOD4Time(Clicross::IGameWorld^ world, Clicross::TOD4TimeState state, double latitude, double longitude )
{
    return cross::GameWorldInterface::World_SetTOD4Time( ( cross::IGameWorld* )(world), static_cast<cross::TOD4TimeState>(state), latitude, longitude);
}

bool GameWorldInterface::World_RefreshPlanetRadiusByLocation(Clicross::IGameWorld^ world, double latitude, double altitude )
{
    return cross::GameWorldInterface::World_RefreshPlanetRadiusByLocation( ( cross::IGameWorld* )(world), latitude, altitude);
}

unsigned long long GameWorldInterface::World_CreateEntity(Clicross::IGameWorld^ world )
{
    return cross::GameWorldInterface::World_CreateEntity( ( cross::IGameWorld* )(world));
}

void GameWorldInterface::World_SetEnable(Clicross::IGameWorld^ world, bool bEnable )
{
    cross::GameWorldInterface::World_SetEnable( ( cross::IGameWorld* )(world), bEnable);
}

bool GameWorldInterface::World_GetEnable(Clicross::IGameWorld^ world )
{
    return cross::GameWorldInterface::World_GetEnable( ( cross::IGameWorld* )(world));
}

void GameWorldInterface::World_SetAlwaysEnable(Clicross::IGameWorld^ world, bool bEnable )
{
    cross::GameWorldInterface::World_SetAlwaysEnable( ( cross::IGameWorld* )(world), bEnable);
}

void GameWorldInterface::World_Destroy(Clicross::IGameWorld^ world, unsigned long long entity )
{
    cross::GameWorldInterface::World_Destroy( ( cross::IGameWorld* )(world), entity);
}

System::String^ GameWorldInterface::World_GetEntityEUID(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return ClangenCli::ToManagedString(((cross::GameWorldInterface::World_GetEntityEUID( ( cross::IGameWorld* )(world), entity))).c_str());
}

void GameWorldInterface::Editor_SaveData(Clicross::IGameWorld^ world, System::String^ data )
{
    cross::GameWorldInterface::Editor_SaveData( ( cross::IGameWorld* )(world), ClangenCli::ToNativeString(data).c_str());
}

System::String^ GameWorldInterface::Editor_LoadData(Clicross::IGameWorld^ world )
{
    return ClangenCli::ToManagedString(((cross::GameWorldInterface::Editor_LoadData( ( cross::IGameWorld* )(world)))).c_str());
}

bool GameWorldInterface::Entity_IsSerializeAble(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::Entity_IsSerializeAble( ( cross::IGameWorld* )(world), entity);
}

void GameWorldInterface::Entity_SetSerializeAble(Clicross::IGameWorld^ world, unsigned long long entity, bool serializeAble )
{
    cross::GameWorldInterface::Entity_SetSerializeAble( ( cross::IGameWorld* )(world), entity, serializeAble);
}

void GameWorldInterface::Entity_SetFolderBool(Clicross::IGameWorld^ world, unsigned long long entity, bool bFolder )
{
    cross::GameWorldInterface::Entity_SetFolderBool( ( cross::IGameWorld* )(world), entity, bFolder);
}

bool GameWorldInterface::Entity_GetFolderBool(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::Entity_GetFolderBool( ( cross::IGameWorld* )(world), entity);
}

void GameWorldInterface::Entity_SetExpandBool(Clicross::IGameWorld^ world, unsigned long long entity, bool bExpand )
{
    cross::GameWorldInterface::Entity_SetExpandBool( ( cross::IGameWorld* )(world), entity, bExpand);
}

bool GameWorldInterface::Entity_GetExpandBool(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::Entity_GetExpandBool( ( cross::IGameWorld* )(world), entity);
}

void GameWorldInterface::Entity_SetVisibilityBool(Clicross::IGameWorld^ world, unsigned long long entity, bool visibility )
{
    cross::GameWorldInterface::Entity_SetVisibilityBool( ( cross::IGameWorld* )(world), entity, visibility);
}

bool GameWorldInterface::Entity_GetVisibilityBool(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::Entity_GetVisibilityBool( ( cross::IGameWorld* )(world), entity);
}

void GameWorldInterface::Entity_SetSelectableBool(Clicross::IGameWorld^ world, unsigned long long entity, bool selectable )
{
    cross::GameWorldInterface::Entity_SetSelectableBool( ( cross::IGameWorld* )(world), entity, selectable);
}

bool GameWorldInterface::Entity_GetSelectableBool(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return cross::GameWorldInterface::Entity_GetSelectableBool( ( cross::IGameWorld* )(world), entity);
}

void GameWorldInterface::World_LoadScene(Clicross::IGameWorld^ world, System::String^ name )
{
    cross::GameWorldInterface::World_LoadScene( ( cross::IGameWorld* )(world), ClangenCli::ToNativeString(name).c_str());
}

void GameWorldInterface::World_SaveScene(Clicross::IGameWorld^ world, System::String^ name )
{
    cross::GameWorldInterface::World_SaveScene( ( cross::IGameWorld* )(world), ClangenCli::ToNativeString(name).c_str());
}

int GameWorldInterface::World_FrustumPick(Clicross::IGameWorld^ world, Clicross::PlaneList^ planes, Clicross::Float3^ frustumBaseTilePosition )
{
    return cross::GameWorldInterface::World_FrustumPick( ( cross::IGameWorld* )(world), *((cross::PlaneList*)(planes)), *((cross::Float3*)(frustumBaseTilePosition)));
}

unsigned long long GameWorldInterface::World_GetPickedEntity(int index )
{
    return cross::GameWorldInterface::World_GetPickedEntity( index);
}

Clicross::ResourceAABB^ GameWorldInterface::Resource_GetDynamicAABBData(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return gcnew Clicross::ResourceAABB(new cross::ResourceAABB((cross::GameWorldInterface::Resource_GetDynamicAABBData( ( cross::IGameWorld* )(world), entity))) , true);
}

Clicross::ResourceAABB^ GameWorldInterface::Resource_GetOrientedAABBData(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return gcnew Clicross::ResourceAABB(new cross::ResourceAABB((cross::GameWorldInterface::Resource_GetOrientedAABBData( ( cross::IGameWorld* )(world), entity))) , true);
}

Clicross::ResourceAABB^ GameWorldInterface::Resource_GetAABBFromMeshAssetData(System::String^ path )
{
    return gcnew Clicross::ResourceAABB(new cross::ResourceAABB((cross::GameWorldInterface::Resource_GetAABBFromMeshAssetData( ClangenCli::ToNativeString(path).c_str()))) , true);
}

Clicross::ResourceAABB^ GameWorldInterface::Foliage_GetAABBFromMesh(Clicross::IGameWorld^ world, unsigned long long entity )
{
    return gcnew Clicross::ResourceAABB(new cross::ResourceAABB((cross::GameWorldInterface::Foliage_GetAABBFromMesh( ( cross::IGameWorld* )(world), entity))) , true);
}

void GameWorldInterface::World_TransformJoint(Clicross::IGameWorld^ world, unsigned long long childEntity, unsigned long long parentEntity )
{
    cross::GameWorldInterface::World_TransformJoint( ( cross::IGameWorld* )(world), childEntity, parentEntity);
}

void GameWorldInterface::InGameTerminalManager_TriggerCommand(System::String^ command )
{
    cross::GameWorldInterface::InGameTerminalManager_TriggerCommand( ClangenCli::ToNativeString(command).c_str());
}

unsigned int GameWorldInterface::World_GetComponentBitMaskIndex(System::String^ componentname )
{
    return cross::GameWorldInterface::World_GetComponentBitMaskIndex( ClangenCli::ToNativeString(componentname).c_str());
}


}   //end namespace Clicross

// GizmoPickResult export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
Clicross::Float3^ GizmoPickResult::hitPoint::get()
 {
	return gcnew Clicross::Float3(new cross::Float3(((static_cast<cross::GizmoPickResult*>(this->_native))->hitPoint)) , true);
 }
 void GizmoPickResult::hitPoint::set(Clicross::Float3^ value )
 {
	(static_cast<cross::GizmoPickResult*>(this->_native))->hitPoint = value;
 }

int GizmoPickResult::geometryTag::get()
 {
	return (static_cast<cross::GizmoPickResult*>(this->_native))->geometryTag;
 }
 void GizmoPickResult::geometryTag::set(int value )
 {
	(static_cast<cross::GizmoPickResult*>(this->_native))->geometryTag = value;
 }


//constructor export here
GizmoPickResult::GizmoPickResult(): GizmoPickResult(new cross::GizmoPickResult(), true) {}


GizmoPickResult::GizmoPickResult(const cross::GizmoPickResult * obj, bool created_by_clr): 
    _native(const_cast<cross::GizmoPickResult *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GizmoPickResult::operator GizmoPickResult^ (const cross::GizmoPickResult* t)
{
    if(t)
    {
        return gcnew GizmoPickResult(const_cast<cross::GizmoPickResult*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// GizmoManager export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
GizmoManager::GizmoManager( )
    :GizmoManager(new cross::GizmoManager(), true)
{
}



GizmoManager::GizmoManager(const cross::GizmoManager * obj, bool created_by_clr): 
    _native(const_cast<cross::GizmoManager *>(obj))
	, _created_by_clr(created_by_clr)
{
}

GizmoManager::operator GizmoManager^ (const cross::GizmoManager* t)
{
    if(t)
    {
        return gcnew GizmoManager(const_cast<cross::GizmoManager*>(t));
    }
    else
        return nullptr;
}

Clicross::GizmoPickResult^ GizmoManager::Gizmo_RayPick(Clicross::IGameWorld^ world, Clicross::Float3^ origin, Clicross::Float3^ direction, Clicross::Float3^ rayBaseTilePosition )
{
    return gcnew Clicross::GizmoPickResult(new cross::GizmoPickResult((cross::GizmoManager::Gizmo_RayPick( ( cross::IGameWorld* )(world), *((cross::Float3*)(origin)), *((cross::Float3*)(direction)), *((cross::Float3*)(rayBaseTilePosition))))) , true);
}

void GizmoManager::Gizmo_SetTargetWorldMatrix(Clicross::IGameWorld^ world, Clicross::Float4x4^ targetWorldMatrix, Clicross::Float3^ targetPivot )
{
    cross::GizmoManager::Gizmo_SetTargetWorldMatrix( ( cross::IGameWorld* )(world), *((cross::Float4x4*)(targetWorldMatrix)), *((cross::Float3*)(targetPivot)));
}

void GizmoManager::Gizmo_SetDisplayLevelGrid(bool displayLevelGrid )
{
    cross::GizmoManager::Gizmo_SetDisplayLevelGrid( displayLevelGrid);
}

void GizmoManager::Gizmo_SetDisplayCompass(bool displayCompass )
{
    cross::GizmoManager::Gizmo_SetDisplayCompass( displayCompass);
}

void GizmoManager::Gizmo_SetManipulatorType(int manipulatorType )
{
    cross::GizmoManager::Gizmo_SetManipulatorType( manipulatorType);
}

void GizmoManager::World_DrawCompass(Clicross::IGameWorld^ world, unsigned long long cameraEntity )
{
    cross::GizmoManager::World_DrawCompass( ( cross::IGameWorld* )(world), cameraEntity);
}

void GizmoManager::World_DrawManipulator(Clicross::IGameWorld^ world, unsigned long long cameraEntity, int screenHeight, unsigned char highlight )
{
    cross::GizmoManager::World_DrawManipulator( ( cross::IGameWorld* )(world), cameraEntity, screenHeight, highlight);
}

void GizmoManager::World_DrawLevelGrid(Clicross::IGameWorld^ world )
{
    cross::GizmoManager::World_DrawLevelGrid( ( cross::IGameWorld* )(world));
}


}   //end namespace Clicross

// ImGuiConsoleContext export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
ImGuiConsoleContext::ImGuiConsoleContext(Clicross::EditorImGuiCallback^ callback )
    :ImGuiConsoleContext(new cross::ImGuiConsoleContext(( cross::EditorImGuiCallback* )(callback)), true)
{
}



ImGuiConsoleContext::ImGuiConsoleContext(const cross::ImGuiConsoleContext * obj, bool created_by_clr): Clicross::EditorImGuiContext(obj, created_by_clr)
{
}

ImGuiConsoleContext::operator ImGuiConsoleContext^ (const cross::ImGuiConsoleContext* t)
{
    if(t)
    {
        return gcnew ImGuiConsoleContext(const_cast<cross::ImGuiConsoleContext*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// ControllableUnitSystemG export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here


ControllableUnitSystemG::ControllableUnitSystemG(const cross::ControllableUnitSystemG * obj, bool created_by_clr): Clicross::GameSystemBase(obj, created_by_clr)
{
}

ControllableUnitSystemG::operator ControllableUnitSystemG^ (const cross::ControllableUnitSystemG* t)
{
    if(t)
    {
        System::Object^ managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryGetManagedObject(t);
        if(!managedObj)
        {
            managedObj = ClangenNative::LifeTimeObjectManagerWrapper::TryCreateManagedObjectFromMetaInfo(t);
            if(!managedObj)
            {
                managedObj = gcnew ControllableUnitSystemG(const_cast<cross::ControllableUnitSystemG*>(t));
            }
            ClangenNative::LifeTimeObjectManagerWrapper::AddRttiBaseObject(t, managedObj);
        }
        return (ControllableUnitSystemG^)managedObj;
    }
    else
        return nullptr;
}

Clicross::Resource^ ControllableUnitSystemG::EditorCreateCurveControllerRes(Clicross::IGameWorld^ gworld )
{
    return (Clicross::Resource^)(cross::ControllableUnitSystemG::EditorCreateCurveControllerRes( ( cross::IGameWorld* )(gworld)));
}


}   //end namespace Clicross

// InputActionMappingContext export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::InputActionMappingContext::mInputMap export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::InputMapItem^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::InputMapItem
	CPP_DECLARE_STLMAP(InputActionMappingContext::, mInputMapCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE

// cross::InputActionMappingContext::mContextKey export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::InputMapItem^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::InputMapItem
	CPP_DECLARE_STLMAP(InputActionMappingContext::, mContextKeyCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE


//fields export here
InputActionMappingContext::mInputMapCliType^ InputActionMappingContext::mInputMap::get()
 {
	return (static_cast<cross::InputActionMappingContext*>(this->_native))->mInputMap;
 }
 void InputActionMappingContext::mInputMap::set(InputActionMappingContext::mInputMapCliType^ value )
 {
	(static_cast<cross::InputActionMappingContext*>(this->_native))->mInputMap = *value->_native;
 }

InputActionMappingContext::mContextKeyCliType^ InputActionMappingContext::mContextKey::get()
 {
	return (static_cast<cross::InputActionMappingContext*>(this->_native))->mContextKey;
 }
 void InputActionMappingContext::mContextKey::set(InputActionMappingContext::mContextKeyCliType^ value )
 {
	(static_cast<cross::InputActionMappingContext*>(this->_native))->mContextKey = *value->_native;
 }


//constructor export here
InputActionMappingContext::InputActionMappingContext(System::String^ fileName )
    :InputActionMappingContext(new cross::InputActionMappingContext(ClangenCli::ToNativeString(fileName).c_str()), true)
{
}



InputActionMappingContext::InputActionMappingContext(const cross::InputActionMappingContext * obj, bool created_by_clr): 
    _native(const_cast<cross::InputActionMappingContext *>(obj))
	, _created_by_clr(created_by_clr)
{
}

InputActionMappingContext::operator InputActionMappingContext^ (const cross::InputActionMappingContext* t)
{
    if(t)
    {
        return gcnew InputActionMappingContext(const_cast<cross::InputActionMappingContext*>(t));
    }
    else
        return nullptr;
}

void InputActionMappingContext::CreateAndSave(System::String^ fileName )
{
    cross::InputActionMappingContext::CreateAndSave( ClangenCli::ToNativeString(fileName).c_str());
}

void InputActionMappingContext::Save( )
{
    (static_cast<cross::InputActionMappingContext*>(this->_native))->Save( );
}


}   //end namespace Clicross

// DataAssetInfoItem export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here
System::String^ DataAssetInfoItem::name::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::DataAssetInfoItem*>(this->_native))->name)).c_str());
 }
 void DataAssetInfoItem::name::set(System::String^ value )
 {
	((static_cast<cross::DataAssetInfoItem*>(this->_native))->name) = (ClangenCli::ToNativeString(value));
 }

System::String^ DataAssetInfoItem::value::get()
 {
	return ClangenCli::ToManagedString((((static_cast<cross::DataAssetInfoItem*>(this->_native))->value)).c_str());
 }
 void DataAssetInfoItem::value::set(System::String^ value )
 {
	((static_cast<cross::DataAssetInfoItem*>(this->_native))->value) = (ClangenCli::ToNativeString(value));
 }


//constructor export here
DataAssetInfoItem::DataAssetInfoItem( )
    :DataAssetInfoItem(new cross::DataAssetInfoItem(), true)
{
}

DataAssetInfoItem::DataAssetInfoItem(System::String^ name, System::String^ value )
    :DataAssetInfoItem(new cross::DataAssetInfoItem(ClangenCli::ToNativeString(name).c_str(), ClangenCli::ToNativeString(value).c_str()), true)
{
}



DataAssetInfoItem::DataAssetInfoItem(const cross::DataAssetInfoItem * obj, bool created_by_clr): 
    _native(const_cast<cross::DataAssetInfoItem *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DataAssetInfoItem::operator DataAssetInfoItem^ (const cross::DataAssetInfoItem* t)
{
    if(t)
    {
        return gcnew DataAssetInfoItem(const_cast<cross::DataAssetInfoItem*>(t));
    }
    else
        return nullptr;
}


}   //end namespace Clicross

// DataAssetEditorContext export start
namespace Clicross
{

//embeded classes

//stl container export here
// cross::DataAssetEditorContext::mInfoItems export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::DataAssetInfoItem^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::DataAssetInfoItem
	CPP_DECLARE_STLMAP(DataAssetEditorContext::, mInfoItemsCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE

// cross::DataAssetEditorContext::mDataItems export start
	#define STLDECL_MANAGEDKEY System::String^
	#define STLDECL_MANAGEDVALUE Clicross::resource::DataAssetItem^
	#define STLDECL_NATIVEKEY std::string
	#define STLDECL_NATIVEVALUE cross::resource::DataAssetItem
	CPP_DECLARE_STLMAP(DataAssetEditorContext::, mDataItemsCliType, STLDECL_MANAGEDKEY, STLDECL_MANAGEDVALUE, STLDECL_NATIVEKEY, STLDECL_NATIVEVALUE)
	#undef STLDECL_MANAGEDKEY
	#undef STLDECL_MANAGEDVALUE
	#undef STLDECL_NATIVEKEY
	#undef STLDECL_NATIVEVALUE


//fields export here
DataAssetEditorContext::mInfoItemsCliType^ DataAssetEditorContext::mInfoItems::get()
 {
	return (static_cast<cross::DataAssetEditorContext*>(this->_native))->mInfoItems;
 }
 void DataAssetEditorContext::mInfoItems::set(DataAssetEditorContext::mInfoItemsCliType^ value )
 {
	(static_cast<cross::DataAssetEditorContext*>(this->_native))->mInfoItems = *value->_native;
 }

DataAssetEditorContext::mDataItemsCliType^ DataAssetEditorContext::mDataItems::get()
 {
	return (static_cast<cross::DataAssetEditorContext*>(this->_native))->mDataItems;
 }
 void DataAssetEditorContext::mDataItems::set(DataAssetEditorContext::mDataItemsCliType^ value )
 {
	(static_cast<cross::DataAssetEditorContext*>(this->_native))->mDataItems = *value->_native;
 }


//constructor export here
DataAssetEditorContext::DataAssetEditorContext(System::String^ fileName )
    :DataAssetEditorContext(new cross::DataAssetEditorContext(ClangenCli::ToNativeString(fileName).c_str()), true)
{
}



DataAssetEditorContext::DataAssetEditorContext(const cross::DataAssetEditorContext * obj, bool created_by_clr): 
    _native(const_cast<cross::DataAssetEditorContext *>(obj))
	, _created_by_clr(created_by_clr)
{
}

DataAssetEditorContext::operator DataAssetEditorContext^ (const cross::DataAssetEditorContext* t)
{
    if(t)
    {
        return gcnew DataAssetEditorContext(const_cast<cross::DataAssetEditorContext*>(t));
    }
    else
        return nullptr;
}

void DataAssetEditorContext::CreateAndSave(System::String^ fileName, System::String^ baseClassFile, int baseClassIdType )
{
    cross::DataAssetEditorContext::CreateAndSave( ClangenCli::ToNativeString(fileName).c_str(), ClangenCli::ToNativeString(baseClassFile).c_str(), baseClassIdType);
}

void DataAssetEditorContext::Save( )
{
    (static_cast<cross::DataAssetEditorContext*>(this->_native))->Save( );
}


}   //end namespace Clicross


//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************


