//code generate from clangen.mono

#include "ce_managed_auto.hpp"

//***************************************************************
//***********All class override proxy export here****************
//***************************************************************
// cross::EditorImGuiCallback proxy class export here
class EditorImGuiCallback_Native_Proxy : public cross::EditorImGuiCallback
{
public:
    gcroot<Clicross::EditorImGuiCallback^> _managed;

    EditorImGuiCallback_Native_Proxy(Clicross::EditorImGuiCallback^ managedObj):
        cross::EditorImGuiCallback()
        , _managed(managedObj)
    {
    
	}

	virtual void OnSetInputScreenPosition( int x, int y )  override 
	{
		_managed->OnSetInputScreenPosition( x, y);
	}
	virtual void OnSetCursor( int cursor )  override 
	{
		_managed->OnSetCursor( cursor);
	}

};

// cross::GraphCallback proxy class export here
class GraphCallback_Native_Proxy : public cross::GraphCallback
{
public:
    gcroot<Clicross::GraphCallback^> _managed;

    GraphCallback_Native_Proxy(Clicross::GraphCallback^ managedObj):
        cross::GraphCallback()
        , _managed(managedObj)
    {
    
	}

	virtual void OnSetInputScreenPosition( int x, int y )  override 
	{
		_managed->OnSetInputScreenPosition( x, y);
	}
	virtual void OnSetCursor( int cursor )  override 
	{
		_managed->OnSetCursor( cursor);
	}
	virtual void OnCreateMenuAtPosition( int position_x, int position_y, ExpressionCreateMenuInfo menuCategories )  override 
	{
		_managed->OnCreateMenuAtPosition( position_x, position_y, gcnew Clicross::ExpressionCreateMenuInfo(new cross::ExpressionCreateMenuInfo((menuCategories)) , true));
	}

};

// cross::IOperationListener proxy class export here
class IOperationListener_Native_Proxy : public cross::IOperationListener
{
public:
    gcroot<Clicross::IOperationListener^> _managed;

    IOperationListener_Native_Proxy(Clicross::IOperationListener^ managedObj):
        cross::IOperationListener()
        , _managed(managedObj)
    {
    
	}

	virtual void OnOperation(  )  override 
	{
		_managed->OnOperation( );
	}
	virtual void OnSetInputScreenPosition( int x, int y )  override 
	{
		_managed->OnSetInputScreenPosition( x, y);
	}
	virtual void OnSetCursor( int cursor )  override 
	{
		_managed->OnSetCursor( cursor);
	}

};

// cross::MaterialEditorCallback proxy class export here
class MaterialEditorCallback_Native_Proxy : public cross::MaterialEditorCallback
{
public:
    gcroot<Clicross::MaterialEditorCallback^> _managed;

    MaterialEditorCallback_Native_Proxy(Clicross::MaterialEditorCallback^ managedObj):
        cross::MaterialEditorCallback()
        , _managed(managedObj)
    {
    
	}

	virtual void OnSetInputScreenPosition( int x, int y )  override 
	{
		_managed->OnSetInputScreenPosition( x, y);
	}
	virtual void OnSetCursor( int cursor )  override 
	{
		_managed->OnSetCursor( cursor);
	}
	virtual void OnCreateMenuAtPosition( int position_x, int position_y, ExpressionCreateMenuInfo menuCategories )  override 
	{
		_managed->OnCreateMenuAtPosition( position_x, position_y, gcnew Clicross::ExpressionCreateMenuInfo(new cross::ExpressionCreateMenuInfo((menuCategories)) , true));
	}
	virtual void OnOperation(  )  override 
	{
		_managed->OnOperation( );
	}
	virtual void OnOpenMaterialFunction( const char* materialFunctionGuid )  override 
	{
		_managed->OnOpenMaterialFunction( ClangenCli::ToManagedString(materialFunctionGuid));
	}
	virtual void OnActiveDetailTab(  )  override 
	{
		_managed->OnActiveDetailTab( );
	}
	virtual void OnReInspect(  )  override 
	{
		_managed->OnReInspect( );
	}
	virtual void AddWarningMessage( const char* msg )  override 
	{
		_managed->AddWarningMessage( ClangenCli::ToManagedString(msg));
	}
	virtual void AddErrorMessage( const char* msg )  override 
	{
		_managed->AddErrorMessage( ClangenCli::ToManagedString(msg));
	}
	virtual void ClearMessages(  )  override 
	{
		_managed->ClearMessages( );
	}
	virtual void PassCopyExpressionsToGlobal( const char* expressionsStr )  override 
	{
		_managed->PassCopyExpressionsToGlobal( ClangenCli::ToManagedString(expressionsStr));
	}

};

// cross::WorkflowEditorCallback proxy class export here
class WorkflowEditorCallback_Native_Proxy : public cross::WorkflowEditorCallback
{
public:
    gcroot<Clicross::WorkflowEditorCallback^> _managed;

    WorkflowEditorCallback_Native_Proxy(Clicross::WorkflowEditorCallback^ managedObj):
        cross::WorkflowEditorCallback()
        , _managed(managedObj)
    {
    
	}

	virtual void OnSetInputScreenPosition( int x, int y )  override 
	{
		_managed->OnSetInputScreenPosition( x, y);
	}
	virtual void OnSetCursor( int cursor )  override 
	{
		_managed->OnSetCursor( cursor);
	}
	virtual void OnCreateMenuAtPosition( int position_x, int position_y, ExpressionCreateMenuInfo menuCategories )  override 
	{
		_managed->OnCreateMenuAtPosition( position_x, position_y, gcnew Clicross::ExpressionCreateMenuInfo(new cross::ExpressionCreateMenuInfo((menuCategories)) , true));
	}
	virtual void OnOperation(  )  override 
	{
		_managed->OnOperation( );
	}

};


//***************************************************************
//***************All class instances export here*****************
//***************************************************************

//***************************************************************
//**********All class template instances export here*************
//***************************************************************

//***************************************************************
//**********All stl container export here************************
//***************************************************************

//***************************************************************
//**********All override class export here***********************
//***************************************************************
// EditorImGuiCallback export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
EditorImGuiCallback::EditorImGuiCallback(): EditorImGuiCallback(new EditorImGuiCallback_Native_Proxy(this), true) {}


EditorImGuiCallback::EditorImGuiCallback(const cross::EditorImGuiCallback * obj, bool created_by_clr): 
    _native(const_cast<cross::EditorImGuiCallback*>(obj))
	, _created_by_clr(created_by_clr)
{
}

EditorImGuiCallback::operator EditorImGuiCallback^ (const cross::EditorImGuiCallback* t)
{
    if(t)
    {
        EditorImGuiCallback_Native_Proxy const* const_proxy = static_cast<EditorImGuiCallback_Native_Proxy const*>(t);
        EditorImGuiCallback_Native_Proxy* proxy = const_cast<EditorImGuiCallback_Native_Proxy*>(const_proxy);
        return proxy->_managed;
    }
    else
        return nullptr;
}




}   //end namespace Clicross
;
// GraphCallback export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
GraphCallback::GraphCallback(): GraphCallback(new GraphCallback_Native_Proxy(this), true) {}


GraphCallback::GraphCallback(const cross::GraphCallback * obj, bool created_by_clr): Clicross::EditorImGuiCallback(obj, created_by_clr)
{
}

GraphCallback::operator GraphCallback^ (const cross::GraphCallback* t)
{
    if(t)
    {
        GraphCallback_Native_Proxy const* const_proxy = static_cast<GraphCallback_Native_Proxy const*>(t);
        GraphCallback_Native_Proxy* proxy = const_cast<GraphCallback_Native_Proxy*>(const_proxy);
        return proxy->_managed;
    }
    else
        return nullptr;
}



}   //end namespace Clicross
;
// IOperationListener export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
IOperationListener::IOperationListener(): IOperationListener(new IOperationListener_Native_Proxy(this), true) {}


IOperationListener::IOperationListener(const cross::IOperationListener * obj, bool created_by_clr): 
    _native(const_cast<cross::IOperationListener*>(obj))
	, _created_by_clr(created_by_clr)
{
}

IOperationListener::operator IOperationListener^ (const cross::IOperationListener* t)
{
    if(t)
    {
        IOperationListener_Native_Proxy const* const_proxy = static_cast<IOperationListener_Native_Proxy const*>(t);
        IOperationListener_Native_Proxy* proxy = const_cast<IOperationListener_Native_Proxy*>(const_proxy);
        return proxy->_managed;
    }
    else
        return nullptr;
}





}   //end namespace Clicross
;
// MaterialEditorCallback export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
MaterialEditorCallback::MaterialEditorCallback(): MaterialEditorCallback(new MaterialEditorCallback_Native_Proxy(this), true) {}


MaterialEditorCallback::MaterialEditorCallback(const cross::MaterialEditorCallback * obj, bool created_by_clr): Clicross::GraphCallback(obj, created_by_clr)
{
}

MaterialEditorCallback::operator MaterialEditorCallback^ (const cross::MaterialEditorCallback* t)
{
    if(t)
    {
        MaterialEditorCallback_Native_Proxy const* const_proxy = static_cast<MaterialEditorCallback_Native_Proxy const*>(t);
        MaterialEditorCallback_Native_Proxy* proxy = const_cast<MaterialEditorCallback_Native_Proxy*>(const_proxy);
        return proxy->_managed;
    }
    else
        return nullptr;
}










}   //end namespace Clicross
;
// WorkflowEditorCallback export start
namespace Clicross
{

//embeded classes

//stl container export here

//fields export here

//constructor export here
WorkflowEditorCallback::WorkflowEditorCallback(): WorkflowEditorCallback(new WorkflowEditorCallback_Native_Proxy(this), true) {}


WorkflowEditorCallback::WorkflowEditorCallback(const cross::WorkflowEditorCallback * obj, bool created_by_clr): Clicross::GraphCallback(obj, created_by_clr)
{
}

WorkflowEditorCallback::operator WorkflowEditorCallback^ (const cross::WorkflowEditorCallback* t)
{
    if(t)
    {
        WorkflowEditorCallback_Native_Proxy const* const_proxy = static_cast<WorkflowEditorCallback_Native_Proxy const*>(t);
        WorkflowEditorCallback_Native_Proxy* proxy = const_cast<WorkflowEditorCallback_Native_Proxy*>(const_proxy);
        return proxy->_managed;
    }
    else
        return nullptr;
}



}   //end namespace Clicross
;


