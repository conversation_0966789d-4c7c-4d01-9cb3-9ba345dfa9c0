 #pragma once

namespace ClangenCli
{
	generic <typename Type1, typename Type2>
	[System::Serializable]
	public value struct Pair
	{
	   typedef Type1 first_type;
	   typedef Type2 second_type;

		Type1 first;
		Type2 second;

		Pair( Type1 first, Type2 second ) : first(first), second(second)
		{
		}
	};
}

#define INC_DECLARE_STLPAIR_IMPL(CLASS_NAME, M1, M2, N1, N2)				\
value struct CLASS_NAME														\
{																			\
	typedef M1 first_type;													\
	typedef M2 second_type;													\
	typedef N1 first_native_type;											\
	typedef N2 second_native_type;											\
	typedef std::pair<first_native_type, second_native_type> native_type;	\
	first_type first; second_type second;									\
	CLASS_NAME(first_type f, second_type s)									\
		: first(f), second(s)												\
	{}																		\
    template <typename FT, typename ST>                                     \
    CLASS_NAME(std::pair<FT, ST> const& other)                              \
        : first(static_cast<N1>(other.first))                               \
        , second(static_cast<N2>(other.second))                             \
    {}                                                                      \
                                                                            \
	inline static operator native_type (CLASS_NAME pair)					\
	{																		\
		return {															\
			ClangenCli::ToNative<M1, N1>(pair.first),						\
			ClangenCli::ToNative<M2, N2>(pair.second)						\
		};																	\
	}																		\
                                                                            \
    template <typename FT, typename ST>                                     \
    inline static operator std::pair<FT, ST>(CLASS_NAME pair)               \
    {                                                                       \
        native_type npair = pair;                                           \
        return { npair.first, npair.second };                               \
    }                                                                       \
                                                                            \
	inline static operator CLASS_NAME (native_type const& pair)				\
	{																		\
		return CLASS_NAME(													\
			ClangenCli::ToManaged<M1, N1>(pair.first),						\
			ClangenCli::ToManaged<M2, N2>(pair.second)						\
		);																	\
	}																		\
                                                                            \
    template <typename FT, typename ST>                                     \
    inline static operator CLASS_NAME (std::pair<FT, ST> const& pair)       \
    {                                                                       \
        return CLASS_NAME(                                                  \
            pair.first, pair.second                                         \
        );                                                                  \
    }                                                                       \
};

#define INC_DECLARE_STLPAIR(CLASS_NAME, M1, N1, M2, N2, PUBLIC, ...)\
PUBLIC INC_DECLARE_STLPAIR_IMPL(CLASS_NAME, M1, N1, M2, N2)