#pragma once

#pragma managed(push, on)
#include <vcclr.h>
#include <msclr/marshal.h>
#include <msclr/marshal_cppstd.h>
#pragma managed(pop)

#include "stl_container.h"
#include "clr_help.h"
#define CLANGEN_ENABLE_GCOBJECT_SUPPORT 1
#include "gc/clr_life_time_object_manager.h"
#include "gc/clr_rttibase.h"
#include "gc/clr_native_finalizer.h"
#include "clr_class_attribute.h"

#pragma managed(push, off)
#include <string>
#pragma managed(pop)

namespace ClangenCli
{
	typedef System::Runtime::InteropServices::OutAttribute OutAttribute;
	typedef System::Runtime::InteropServices::StructLayoutAttribute StructLayoutAttribute;
	typedef System::Runtime::InteropServices::LayoutKind LayoutKind;
	typedef System::Runtime::InteropServices::FieldOffsetAttribute FieldOffsetAttribute;
	typedef System::Runtime::InteropServices::Marshal Marshal;


    char** ConvertCliArrayToCharArray(cli::array<System::String ^> ^ strings, int length);

	
    template<typename M, typename N>
    inline std::pair<typename N::first_type, typename N::second_type> ToNative(Pair<typename M::first_type, typename M::second_type> value)
    {
        return std::pair<N::first_type, N::second_type>(ToNative<M::first_type, N::first_type>(value.first), ToNative<M::second_type, N::second_type>(value.second));
    }

    template<typename M, typename N>
    inline Pair<typename M::first_type, typename M::second_type> ToManaged(const std::pair<typename N::first_type, typename N::second_type>& value)
    {
        return Pair<typename M::first_type, typename M::second_type>(ToManaged<M::first_type, N::first_type>(value.first), ToManaged<M::second_type, N::second_type>(value.second));
    }


	template <typename MElem, typename NVec>
	array<MElem>^ GetArrayFromVector(const NVec& vec)
	{
		size_t count = vec.size();
		array<MElem>^ arr = gcnew array<MElem>(count);

		for (size_t i = 0; i < count; i++)
			arr[i] = ToManaged<MElem, NVec::value_type>(vec[i]);

		return arr;
	}

	template <typename MElem, typename NList>
	array<MElem>^ GetArrayFromList(const NList& list)
	{
		size_t count = list.size();
		array<MElem>^ arr = gcnew array<MElem>(count);

		typename NList::const_iterator i;
		size_t arr_i;

		for (arr_i = 0, i = list.begin(); i != list.end(); ++i, ++arr_i)
			arr[arr_i] = ToManaged<MElem, NList::value_type>(*i);

		return arr;
	}

	template <typename MKey, typename MVal, typename NMap>
	System::Collections::Generic::SortedList<MKey, MVal>^ GetSortedListFromMap(const NMap& map)
	{
		size_t count = map.size();
		System::Collections::Generic::SortedList<MKey, MVal>^ list = gcnew System::Collections::Generic::SortedList<MKey, MVal>(count);

		typename NMap::const_iterator i;
		for (i = map.begin(); i != map.end(); ++i)
			list->Add(ToManaged<MKey, NMap::key_type>(i->first), ToManaged<MVal, NMap::mapped_type>(i->second));

		return list;
	}

	template <typename MKey, typename MVal, typename NMap>
	System::Collections::Generic::SortedList<MKey, System::Collections::Generic::List<MVal>^>^ GetSortedListFromMultiMap(const NMap& mmap)
	{
		System::Collections::Generic::SortedList<MKey, System::Collections::Generic::List<MVal>^>^ list = gcnew System::Collections::Generic::SortedList<MKey, System::Collections::Generic::List<MVal>^>();
		System::Collections::Generic::List<MVal>^ valList;

		typename NMap::const_iterator i;
		for (i = mmap.begin(); i != mmap.end(); ++i)
		{
			if (!(list->TryGetValue(i->first, valList)))
			{
				valList = gcnew System::Collections::Generic::List<MVal>();
				list->Add(ToManaged<MKey, NMap::key_type>(i->first), valList);
			}

			valList->Add(ToManaged<MVal, NMap::mapped_type>(i->second));
		}

		return list;
	}

	template <typename NList, typename MElem>
	void FillListFromGenericList(NList& nlist, System::Collections::Generic::List<MElem>^ genlist)
	{
		int count = genlist->Count;
		for (int i = 0; i < count; i++)
		{
			nlist.push_back(ToNative<MElem, NList::value_type>(genlist[i]));
		}
	}

	template <typename NList, typename MElem>
	void FillListFromGenericList(NList& nlist, array<MElem>^ arr)
	{
		for (int i = 0; i < arr->Length; i++)
		{
			nlist.push_back(ToNative<MElem, NList::value_type>(arr[i]));
		}
	}

	template <typename NMap, typename MKey, typename MVal>
	void FillMapFromSortedList(NMap& map, System::Collections::Generic::SortedList<MKey, MVal>^ list)
	{
		int count = list->Count;
		for (int i = 0; i < count; i++)
		{
			map.insert(NMap::value_type(ToNative<MKey, NMap::key_type>(list->Keys[i]), ToNative<MVal, NMap::mapped_type>(list->Values[i])));
		}
	}

	template <typename N, typename MElem>
	void FillNativeArrayFromCLRArray(N* pbuf, array<MElem>^ arr)
	{
		for (int i = 0; i < arr->Length; i++)
			pbuf[i] = ToNative<MElem, N>(arr[i]);
	}

	////void FillMapFromNameValueCollection(std::map<Captain::String, Captain::String>& map, System::Collections::Specialized::NameValueCollection^ col);

	template <typename MElem, typename NElem>
	array<MElem>^ GetArrayFromNativeArray(const NElem* ptr, int len)
	{
		array<MElem>^ arr = gcnew array<MElem>(len);
		for (int i = 0; i < len; i++)
			arr[i] = ToManaged<MElem, NElem>(ptr[i]);

		return arr;
	}

	template <typename MElem, typename NElem>
	array<MElem>^ GetValueArrayFromNativeArray(const NElem* src, int len)
	{
		//STATIC_ASSERT( sizeof(MElem) == sizeof(NElem) )

		array<MElem>^ arr = gcnew array<MElem>(len);
		pin_ptr<MElem> p_arr = &arr[0];
		memcpy(p_arr, src, len * sizeof(NElem));
		return arr;
	}

////#define DEFINE_MANAGED_NATIVE_CONVERSIONS_FOR_SHAREDPTR(T)					\
////			static operator T^ (const Captain::T& ptr) {							\
////				if (ptr.isNull()) return nullptr;								\
////				return gcnew T(const_cast<Captain::T&>(ptr));						\
////			}																	\
////			static operator Captain::T& (T^ t) {									\
////				if (CLR_NULL == t) return *((gcnew T(Captain::T()))->_sharedPtr);	\
////				return *(t->_sharedPtr);										\
////			}																	\
////			static operator Captain::T* (T^ t) {									\
////				if (CLR_NULL == t) return (gcnew T(Captain::T()))->_sharedPtr;		\
////				return t->_sharedPtr;											\
////			} 
}  // namespace ClangenCli
namespace Clicross
{
public
enum class ClassIDType : int
{
    CLASS_NullType = 0,
    CLASS_Object = 1,
    CLASS_Named = 2,
    kSmallestResourceTypeID = 3,
    CLASS_Resource = 3,
    CLASS_SubResource = 4,
    CLASS_Material = 5,
    CLASS_Texture = 6,
    CLASS_Texture2D = 7,
    CLASS_TextureUDIM = 8,
    CLASS_Texture2DVirtual = 27,
    CLASS_TextureVirtual = 28,
    CLASS_TextureCube = 15,
    CLASS_Texture3D = 18,
    CLASS_Texture2DArray = 20,
    CLASS_Shader = 10,
    CLASS_MaterialParameterCollection = 11,
    CLASS_MaterialFunction = 9,
    CLASS_Particles = 12,
    CLASS_NavMeshAsset = 17,
    CLASS_ScriptResource = 19,
    CLASS_TrailEmitter = 21,
    CLASS_ParticleSystem = 22,
    CLASS_MeshAssetDataResource = 23,
    CLASS_Fx = 24,
    CLASS_PrefabResource = 25,
    CLASS_WorldBlock = 26,
    CLASS_SkeletonResource = 30,
    CLASS_AnimSequenceRes = 33,
    CLASS_AnimCompositeRes = 34,
    CLASS_AnimatrixRes = 35,
    CLASS_AnimatorRes = 36,
    CLASS_AnimBlendSpaceRes = 37,
    CLASS_SkeletonPhysicsResource = 38,
    CLASS_MotionDataAsset = 39,
    CLASS_ComputeShader = 40,
    CLASS_RED = 41,
    CLASS_ParticleSystemResource = 50,
    CLASS_ParticleEmitterResource = 51,
    CLASS_PCGResource = 61,
    CLASS_WorkflowGraphResource = 64,
    CLASS_InstanceDataResource = 65,
    CLASS_InputActionMappingResource = 66,
    CLASS_DataAssetResource = 67,
    CLASS_AirportResource = 70,
    CLASS_CurveControllerRes = 101,
    CLASS_TerrainResource = 102,
    CLASS_FontResource = 103,
    CLASS_RenderTextureResource = 104,
    kLargestResourceTypeID = 999,
    kSmallestRuntimeClassID = 1000,
    CLASS_Window = 1000,
    CLASS_World = 1001,
    kLargestRuntimeClassID = 1002,
    kSmallestEditorClassID = 10000,
    CLASS_ResourceReference = 10001,
};
    [System::AttributeUsage(System::AttributeTargets::Property | System::AttributeTargets::Field)] 
	public ref class PropInfoAttribute : public System::Attribute
    {
    public:
        System::String^ Value;
        System::String^ DisplayName;
        System::String^ PropertyType;
        System::String^ ChildPropertyType;
        System::String^ Category;
        System::String^ ToolTips;
        bool bGenerateCode;
        bool bReadOnly;
        System::String^ FileTypeDescriptor;
        ClassIDType ObjectClassID1;
        ClassIDType ObjectClassID2;
        ClassIDType ObjectClassID3;
        System::Object^ DefaultValue;
        bool bFixedItems;
        bool bHide;
        bool bAdvanced;
        bool bModified;
        System::String^ ValueMin;
        System::String^ ValueMax;
        System::String^ ValueStep;
        bool bAutoExpandStruct;
        bool bKeyFrame;
        bool bShowInRender;
        bool bTriggerRefresh;
        PropInfoAttribute();     
    };
}