<?xml version="1.0" encoding="utf-8"?>
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">

    <Type Name="nonstd::span_lite::span&lt;*,*&gt;">
        <Intrinsic Name="size" Expression="$T2" Optional="true" />
        <Intrinsic Name="size" Expression="size_" Optional="true" />
        <DisplayString>{{ size={size()} }}</DisplayString>
        <Expand>
            <ArrayItems>
                <Size>size()</Size>
                <ValuePointer>data_</ValuePointer>
            </ArrayItems>
        </Expand>
    </Type>

    <Type Name="nonstd::sv_lite::basic_string_view&lt;*&gt;">
        <!--<DisplayString Condition="m_Type == 1">{m_Type,en} {*(crude_json::object*)&amp;m_Storage,view(simple)}</DisplayString>-->
        <!--<StringView Condition="m_Type == 3">*(crude_json::string*)&amp;m_Storage</StringView>-->

        <DisplayString>{data_,[size_]na}</DisplayString>
        <StringView>data_,[size_]</StringView>
        <Expand>
            <Item Name="[size]" ExcludeView="simple">size_</Item>
            <ArrayItems>
                <Size>size_</Size>
                <ValuePointer>data_</ValuePointer>
            </ArrayItems>
        </Expand>
    </Type>

    <Type Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;">
        <AlternativeType Name="nonstd::variants::variant&lt;*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <AlternativeType Name="nonstd::variants::variant&lt;*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*,*&gt;"/>
        <DisplayString Condition="type_index ==  0" Optional="true" IncludeView="value">{*(($T1*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  1" Optional="true" IncludeView="value">{*(($T2*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  2" Optional="true" IncludeView="value">{*(($T3*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  3" Optional="true" IncludeView="value">{*(($T4*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  4" Optional="true" IncludeView="value">{*(($T5*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  5" Optional="true" IncludeView="value">{*(($T6*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  6" Optional="true" IncludeView="value">{*(($T7*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  7" Optional="true" IncludeView="value">{*(($T8*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  8" Optional="true" IncludeView="value">{*(($T9*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index ==  9" Optional="true" IncludeView="value">{*(($T10*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 10" Optional="true" IncludeView="value">{*(($T11*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 11" Optional="true" IncludeView="value">{*(($T12*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 12" Optional="true" IncludeView="value">{*(($T13*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 13" Optional="true" IncludeView="value">{*(($T14*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 14" Optional="true" IncludeView="value">{*(($T15*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 15" Optional="true" IncludeView="value">{*(($T16*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 16" Optional="true" IncludeView="value">{*(($T17*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 17" Optional="true" IncludeView="value">{*(($T18*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 18" Optional="true" IncludeView="value">{*(($T19*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 19" Optional="true" IncludeView="value">{*(($T20*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 20" Optional="true" IncludeView="value">{*(($T21*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 21" Optional="true" IncludeView="value">{*(($T22*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 22" Optional="true" IncludeView="value">{*(($T23*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 23" Optional="true" IncludeView="value">{*(($T24*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 24" Optional="true" IncludeView="value">{*(($T25*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 25" Optional="true" IncludeView="value">{*(($T26*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 26" Optional="true" IncludeView="value">{*(($T27*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 27" Optional="true" IncludeView="value">{*(($T28*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 28" Optional="true" IncludeView="value">{*(($T29*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 29" Optional="true" IncludeView="value">{*(($T30*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 30" Optional="true" IncludeView="value">{*(($T31*)&amp;data)}</DisplayString>
        <DisplayString Condition="type_index == 31" Optional="true" IncludeView="value">{*(($T32*)&amp;data)}</DisplayString>

        <DisplayString Condition="type_index ==  0" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  1" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  2" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  3" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  4" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  5" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  6" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  7" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  8" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index ==  9" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 10" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 11" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 12" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 13" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 14" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 15" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 16" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 17" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 18" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 19" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 20" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 21" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 22" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 23" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 24" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 25" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 26" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 27" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 28" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 29" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 30" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>
        <DisplayString Condition="type_index == 31" Optional="true" IncludeView="index">{(int)type_index}</DisplayString>

        <DisplayString Condition="type_index ==  0" Optional="true">{{ index={(int)type_index}, value={*(($T1*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  1" Optional="true">{{ index={(int)type_index}, value={*(($T2*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  2" Optional="true">{{ index={(int)type_index}, value={*(($T3*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  3" Optional="true">{{ index={(int)type_index}, value={*(($T4*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  4" Optional="true">{{ index={(int)type_index}, value={*(($T5*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  5" Optional="true">{{ index={(int)type_index}, value={*(($T6*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  6" Optional="true">{{ index={(int)type_index}, value={*(($T7*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  7" Optional="true">{{ index={(int)type_index}, value={*(($T8*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  8" Optional="true">{{ index={(int)type_index}, value={*(($T9*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index ==  9" Optional="true">{{ index={(int)type_index}, value={*(($T10*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 10" Optional="true">{{ index={(int)type_index}, value={*(($T11*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 11" Optional="true">{{ index={(int)type_index}, value={*(($T12*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 12" Optional="true">{{ index={(int)type_index}, value={*(($T13*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 13" Optional="true">{{ index={(int)type_index}, value={*(($T14*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 14" Optional="true">{{ index={(int)type_index}, value={*(($T15*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 15" Optional="true">{{ index={(int)type_index}, value={*(($T16*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 16" Optional="true">{{ index={(int)type_index}, value={*(($T17*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 17" Optional="true">{{ index={(int)type_index}, value={*(($T18*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 18" Optional="true">{{ index={(int)type_index}, value={*(($T19*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 19" Optional="true">{{ index={(int)type_index}, value={*(($T20*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 20" Optional="true">{{ index={(int)type_index}, value={*(($T21*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 21" Optional="true">{{ index={(int)type_index}, value={*(($T22*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 22" Optional="true">{{ index={(int)type_index}, value={*(($T23*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 23" Optional="true">{{ index={(int)type_index}, value={*(($T24*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 24" Optional="true">{{ index={(int)type_index}, value={*(($T25*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 25" Optional="true">{{ index={(int)type_index}, value={*(($T26*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 26" Optional="true">{{ index={(int)type_index}, value={*(($T27*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 27" Optional="true">{{ index={(int)type_index}, value={*(($T28*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 28" Optional="true">{{ index={(int)type_index}, value={*(($T29*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 29" Optional="true">{{ index={(int)type_index}, value={*(($T30*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 30" Optional="true">{{ index={(int)type_index}, value={*(($T31*)&amp;data)} }}</DisplayString>
        <DisplayString Condition="type_index == 31" Optional="true">{{ index={(int)type_index}, value={*(($T32*)&amp;data)} }}</DisplayString>
        <Expand>
            <Item Name="index">(int)type_index</Item>
            <Item Name="[value]" Condition="type_index ==  0" Optional="true">*(($T1*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  1" Optional="true">*(($T2*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  2" Optional="true">*(($T3*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  3" Optional="true">*(($T4*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  4" Optional="true">*(($T5*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  5" Optional="true">*(($T6*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  6" Optional="true">*(($T7*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  7" Optional="true">*(($T8*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  8" Optional="true">*(($T9*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index ==  9" Optional="true">*(($T10*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 10" Optional="true">*(($T11*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 11" Optional="true">*(($T12*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 12" Optional="true">*(($T13*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 13" Optional="true">*(($T14*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 14" Optional="true">*(($T15*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 15" Optional="true">*(($T16*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 16" Optional="true">*(($T17*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 17" Optional="true">*(($T18*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 18" Optional="true">*(($T19*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 19" Optional="true">*(($T20*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 20" Optional="true">*(($T21*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 21" Optional="true">*(($T22*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 22" Optional="true">*(($T23*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 23" Optional="true">*(($T24*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 24" Optional="true">*(($T25*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 25" Optional="true">*(($T26*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 26" Optional="true">*(($T27*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 27" Optional="true">*(($T28*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 28" Optional="true">*(($T29*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 29" Optional="true">*(($T30*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 30" Optional="true">*(($T31*)&amp;data)</Item>
            <Item Name="[value]" Condition="type_index == 31" Optional="true">*(($T32*)&amp;data)</Item>
        </Expand>
    </Type>

    <Type Name="nonstd::variants::monostate">
        <DisplayString>monostate</DisplayString>
    </Type>

</AutoVisualizer>
