#pragma once

#include <variant>
#include "crude/runtime/crude/crude_utils.h"
#include "external/nonstd/span.hpp"
#include "crude/runtime/crude/crude_node_type_info.h"


namespace crude_blueprint {

//
// -------[ Node Factory ]-------
//
struct NodeRegistry {
  NodeRegistry();

  uint32_t RegisterNodeType(std::string_view name,  NodeTypeInfo::Factory factory);
  // void UnregisterNodeType(std::string_view name);

  Node* Create(std::string_view typeName, Blueprint& blueprint);

  const std::vector<NodeTypeInfo>& GetTypes() const;

 private:

  std::vector<NodeTypeInfo> m_BuildInNodes;

  

  std::vector<NodeTypeInfo> m_Types;

  
};

}
