#pragma once

#include "EditorUICanvas.h"
#include "crude/runtime/crude/crude_utils.h"
#include "editor/utilities/node_builder_blueprint.h"
#include "graph_callback.h"

namespace cross {
namespace ed = ax::NodeEditor;
class GraphNode;

enum class PinType
{
    Output,
    Input,
    DisplayOnly,
};

class GraphPin
{
public:
    virtual ~GraphPin() {}

    node_editor::drawing::SlotDrawing ToDrawingSlot() const;

    void LinkTo(GraphPin* pin);

public:
    std::string m_Name;
    PinType m_Type;
    GraphNode* m_Node;
    std::vector<GraphPin*> m_LinkedPins;
    node_editor::drawing::SlotDrawingType m_DrawType = node_editor::drawing::SlotDrawingType::Any;
    std::function<std::string(void)> m_GetName;
    std::function<float(void)> m_GetEnable;
    std::function<gbf::reflection::Value(void)> m_GetBindedPropertyValue;

private:
    uint32_t m_Id;

    friend class Graph;
};

using GraphNodeType = node_editor::drawing::NodeDrawingType;

class GraphNode
{
public:
    virtual ~GraphNode() {}

    node_editor::drawing::NodeDrawing ToDrawingNode() const;

public:
    std::function<std::string(void)> m_GetName;
    std::function<std::string(void)> m_GetSubTitle;
    std::function<std::string(void)> m_GetDescription;
    std::function<std::string(void)> m_GetErrorMessage;
    std::function<UITexturePtr(void)> m_GetDisplayTexture;
    std::function<ImColor(void)> m_GetTitleColor;
    std::function<Float2(void)> m_GetNodeSize;
    std::function<void(int x, int y)> m_SetCustomAttrInt2;
    std::function<bool(void)> m_ShowBubbleWhenZoomed;

    int32_t m_PositionX, m_PositionY;

    std::vector<GraphPin*> m_InputPins;
    std::vector<GraphPin*> m_OutputPins;

    GraphNodeType m_NodeDrawingType = GraphNodeType::Blueprint;
    uint32_t Id() const noexcept { return m_Id; }

private:
    uint32_t m_Id;
    bool m_PositionDirty = true;

    friend class Graph;
};

struct NODE_EDITOR_BRIDGE_API CEMeta(Cli, IsOverride) IOperationListener
{
    CEMeta(Cli, IsOverride)
    virtual void OnOperation() {}
    CEMeta(Cli, IsOverride)
    virtual void OnSetInputScreenPosition(int x, int y) {}
    CEMeta(Cli, IsOverride)
    virtual void OnSetCursor(ImGuiMouseCursor cursor) {}
};

class Graph
{
public:
    void FillImguiNodeEditorConfig(ed::Config& config);

    void Tick(node_editor::utility::NodeBuilderBlueprint* nodeBuilder);

    void CreateNodeAtPos(std::string expressionClassName, std::string materialFunctionGuid, int position_x, int position_y);
    void CreateNodeAtPos(ExpressionCreateNodeInfo createInfo, int position_x, int position_y);

    Graph() = default;
    ~Graph() = default;
    Graph(Graph&) = delete;
    Graph(const Graph&) = delete;
    Graph(Graph&&) = delete;
    Graph(GraphCallback* graphCallback);

protected:
    virtual void OnBeginAction() {}

    virtual void OnEndAction() {}

    virtual void OnCreateLink(GraphPin* outputPin, GraphPin* inputPin) {}

    virtual void OnBreakLink(GraphPin* outputPin, GraphPin* inputPin) {}

    virtual void OnNodeMoved(GraphNode* node, int32_t dstPosX, int32_t dstPosY) {}

    virtual void OnDeleteNodes(const std::vector<GraphNode*> nodes) {}

    virtual void OnShowCreateNodeDialog(bool isFirstShow, GraphPin* pin) {}

    virtual void OnShowNodesContextMenu(std::vector<GraphNode*> nodes) {}

    virtual void OnNodeDoubleClicked(GraphNode* node) {}

    virtual void OnNodesSelected(const std::vector<GraphNode*> nodes) {}

    virtual void OnUndo() {}

    virtual void OnRedo() {}

    virtual void OnCopy() {}

    virtual void OnPaste(int32_t x, int32_t y) {}

    virtual void OnCut() {}

    virtual ExpressionCreateMenuInfo GetGraphCategories(int pinType) { return {}; }

    virtual void CreateNewNode(std::string expressionClassName, std::string materialFunctionGuid, int position_x, int position_y, GraphPin* pin, ExpressionCreateNodeInfo* createInfo=nullptr) {}

protected:
    template<typename NodeType>
    NodeType* AddGraphNode();

    template<typename PinType>
    PinType* AllocateGraphPin();

    void DoDeleteNode(GraphNode* node);

    void DoClearNodePins(GraphNode* node);

    void DoCreateLink(GraphPin* outputPin, GraphPin* inputPin);
    void DoBreakLink(GraphPin* outputPin, GraphPin* inputPin);

    GraphNode* FindNode(uint32_t nodeId);
    const GraphNode* FindNode(uint32_t nodeId) const;

    GraphPin* FindPin(uint32_t pinId);
    const GraphPin* FindPin(uint32_t pinId) const;

    void SetNodePosition(GraphNode* node, int32_t x, int32_t y);

    void CloseCurrentDialog();

protected:
    std::vector<std::unique_ptr<GraphNode>> m_Nodes;
    std::vector<std::unique_ptr<GraphPin>> m_Pins;

private:
    void CommitOneNode(node_editor::utility::NodeBuilderBlueprint* nodeBuilder, GraphNode* node);
    void DrawLinks();

    void HandleCreateAction();
    void HandleDestroyAction();
    void HandleMoveAction();
    bool HandleContextMenuAction();
    void HandleShortcut();
    void HandleSelectAction();
    void HandleHoverAction();
    void HandleDoubleClickedAction();

    void ShowDialog();

    bool HasPinAnyLink(GraphPin* pin) const;

private:
    crude_blueprint::IdGenerator m_IdGenerator;
    GraphPin* m_CreateNodeFromPin = nullptr;
    ImVec2 m_CreateNodeLinkTarget;
    std::vector<GraphNode*> m_SelectedNodes;
    bool m_IsPrevFrameShowCreateNodeDialog = false;
    std::map<std::pair<GraphPin*, GraphPin*>, UInt64> m_LinkIDs;
    UInt64 m_LinkdBaseID = 1;

    // callback
    GraphCallback* m_GraphCallBack = nullptr;
    bool m_IsNeedCloseDialog = false;
    friend class ImguiNodeEditorTransactionWrapper;
};

//----- Implement -----
template<typename NodeType>
NodeType* Graph::AddGraphNode()
{
    auto* newNode = new NodeType();
    newNode->m_Id = m_IdGenerator.GenerateId();
    m_Nodes.emplace_back(newNode);
    return newNode;
}

template<typename PinType>
PinType* Graph::AllocateGraphPin()
{
    auto* newPin = new PinType();
    newPin->m_Id = m_IdGenerator.GenerateId();
    m_Pins.emplace_back(newPin);
    return newPin;
}

}   // namespace cross
