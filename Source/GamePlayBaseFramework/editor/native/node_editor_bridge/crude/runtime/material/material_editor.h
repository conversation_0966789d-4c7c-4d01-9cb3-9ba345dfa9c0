#pragma once

#include "Resource/Resource.h"
#include "MaterialBP/material_expression.h"
#include "MaterialBP/material_expression_registry.h"
#include "MaterialBP/material_expression_parameter.h"
#include "MaterialBP/material_expression_texture_object.h"
#include "MaterialBP/material_expression_vertex_shader.h"
#include "MaterialBP/material_expression_surface_shader.h"
#include "material_graph.h"
#include "material_transaction_manager.h"

#include "Resource/MaterialFunction.h"
#include "EditorUICanvas.h"
namespace cross::resource {
class Fx;
}

namespace cross {

class MaterialParameterAndShaderConstSet
{
public:
    std::string GenerateName(const std::string& prefix = "");

    void ClearNameSet() { m_NameSet.clear(); }

#pragma region Parameter
    void AddParameter(MaterialExpressionParameter* expressionParameter);

    void RemoveParameter(MaterialExpressionParameter* expressionParameter);

    MaterialParameter* UpdateParameter(MaterialExpressionParameter* expressionParameter);

    void ClearParameters();

    std::vector<MaterialParameter*> GetAllParameters();
#pragma endregion

#pragma region TextureObject
    void AddTextureObject(MaterialExpressionTextureObject* expressionTextureObject);

    void RemoveTextureObject(MaterialExpressionTextureObject* expressionTextureObject);
#pragma endregion

#pragma region ShaderConst

    void ApplyParametersAndShaderConstsToFx(FxPtr fx, MaterialEditor* materialEditor);

    auto& GetParameterGroups() { return m_ParameterGroups; }

private:
    void PushParameter(const std::string& groupName, MaterialParameter* parameter);

    MaterialParameterGroup* GetParameterGroup(MaterialParameter* parameter);

    MaterialParameter* GetParameterByDisplayNameAndType(const MaterialExpressionParameter* paramExpression);

    void RemoveParameterFromParameterGroup(MaterialParameter* parameter);

    // NameSet (Parameters and ShaderConsts share the same name scope)
    std::unordered_set<std::string> m_NameSet;

    // Parameters
    std::vector<std::shared_ptr<MaterialParameter>> m_Parameters;

    // TextureObjects (Invisible)
    std::vector<MaterialExpressionTextureObject*> m_TextureObjects;

    // ParameterGroup (Parameter + ShaderConst)
    std::vector<MaterialParameterGroup> m_ParameterGroups;

    // Used to trace change of expression's name
    std::unordered_map<MaterialExpression*, MaterialParameter*> m_ExpressionToParameterMap;
};

class FunctionInputOutputIDGenerator
{
public:
    void AddId(SInt32 id)
    {
        assert(m_IdSet.find(id) != m_IdSet.end());
        m_IdSet.insert(id);
    }

    void RemoveId(SInt32 id)
    {
        assert(m_IdSet.find(id) != m_IdSet.end());
        m_IdSet.erase(id);
    }

    SInt32 GenerateId()
    {
        SInt32 id = 0;
        while (m_IdSet.find(id) != m_IdSet.end())
        {
            id++;
        }
        m_IdSet.insert(id);
        return id;
    }

private:
    std::unordered_set<SInt32> m_IdSet;
};

class NamedRerouteGenerator
{
public:
    std::string GenerateId();
    std::string GenerateName(const std::string& prefix = "");
    void ClearNameSet() { m_NameSet.clear(); }
    std::string AddName(const std::string& prefix = "");
    void DeleteDeclaration(MaterialExpression* declaration);

    std::vector<MaterialExpression*> m_NamedRerouteExpressions;
private:
    std::unordered_set<std::string> m_NameSet;
    std::unordered_set<std::string> m_IdSet;
};

class CEMeta(Cli) NODE_EDITOR_BRIDGE_API MaterialEditor : public EditorImGuiContext, public IMaterialEditor
{
    friend class TextureExpressionHandler;

public:
    ~MaterialEditor() override;

    MaterialEditor();

    CEMeta(Cli)
    MaterialEditor(const char* fileName, MaterialEditorCallback* callback);

    void FillImguiConfigNodeEditorConfig(ax::NodeEditor::Config & config);

    CEMeta(Cli)
    void Open(const char* filePath);

    void OnUpdate(const Float2& offset, const Float2& size) override;

    void OnFrame();

    CEMeta(Cli)
    void ZoomToSurfaceDataExpression();

    CEMeta(Cli)
    void ZoomToFunctionOutput();

    CEMeta(Cli)
    void ZoomToExpression(MaterialExpression* expression);

    CEMeta(Cli)
    void Undo();

    CEMeta(Cli)
    void Redo();

    CEMeta(Cli)
    bool Apply(bool force);

    CEMeta(Cli)
    std::string HLSLCode();

    CEMeta(Cli)
    bool IsResourceChanged()
    {
        return m_IsResourceChanged;
    }

    void SetMaterialEditorCallback(MaterialEditorCallback* callback);

    auto GetFx()
    {
        return m_Fx;
    }

    // SelectedExpression
    CEMeta(Cli)
    MaterialExpression* GetSelectedExpression(int index)
    {
        return m_SelectedExpressions[index];
    }

    CEMeta(Cli)
    int GetSelectedExpressionsCount()
    {
        return static_cast<int>(m_SelectedExpressions.size());
    }

    // Expression
    CEMeta(Cli)
    MaterialExpression* GetExpression(int index) 
    { 
        return m_Expressions[index].get(); 
    }

    CEMeta(Cli)
    int GetExpressionsCount() 
    { 
        return static_cast<int>(m_Expressions.size()); 
    }

    // MaterialDefines
    CEMeta(Cli)
    MaterialDefines& GetMaterialDefines()
    {
        return mMaterialDefines;
    }

    CEMeta(Cli)
    MaterialFunctionDefines& GetMaterialFunctionDefines()
    {
        return m_MaterialFunction->GetMaterialFunctionDefines();
    }

    CEMeta(Cli)
    void ResetRenderState()
    {
        for (auto& [name, pass] : m_Fx->GetAllPass())
        {
            MaterialExpression::InitPassState(m_Fx.get(), name.GetName());
        }
        SetPropertyChangedFlagForEditor(true);
    }

    CEMeta(Cli)
    void SetPropertyChangedFlagForEditor(bool flag)
    {
        SetResourceChanged(flag);
    }

    // ParameterGroup
    CEMeta(Cli)
    int GetMaterialParameterGroupsCount()
    {
        return static_cast<int>(m_ParameterAndShaderConstSet.GetParameterGroups().size());
    }

    CEMeta(Cli)
    MaterialParameterGroup* GetMaterialParameterGroup(int index)
    {
        return &m_ParameterAndShaderConstSet.GetParameterGroups()[index];
    }

    std::vector<MaterialExpression*>& GetSelectedExpressions()
    {
        return m_SelectedExpressions;
    }

    CEMeta(Cli)
    void SetPassRenderGroup(std::string passID, UInt32 renderGroup);

    CEMeta(Cli)
    void CreateMaterialExpression(ExpressionCreateNodeInfo expressionCreateInfo, int postion_x, int position_y);

    CEMeta(Cli)
    void OnPropertyChange(MaterialExpression * expression);

    CEMeta(Cli)
    void OnMaterialDefinesChange();

    CEMeta(Cli)
    void OnMaterialFunctionDefinesChange();

    CEMeta(Cli)
    void OnMaterialFunctionChange(const std::string& materialFunctionGuid);

    // called by CrossEditor
    CEMeta(Cli)
    void OnParameterChange(MaterialParameter* parameter) override;

    // called by MaterialExpressionParameter
    void OnParameterChange(MaterialExpressionParameter* expressionParameter) override;

    // called by MaterialExpressionNamedRerouteDeclaration
    void MakeNamedRerouteNameUnique(MaterialExpression* epxression) override;

    void AddParameter(MaterialExpressionParameter* parameter) override;

    void RemoveParameter(MaterialExpressionParameter* parameter) override;

    CEMeta(Cli)
    void OnMaterialParameterCollectionSelectChange();

    CEMeta(Cli)
    void OnMaterialParameterCollectionChange(const std::string& mpcGuid);

    CEMeta(Cli)
    static void RegisterMaterialFunction(const std::string& materialFunctions);

    CEMeta(Cli)
    static void ClearMaterialFunctions();

    void SetMaterialPreview(class MaterialPreview * preview)
    {
        m_Preview = preview;
    }

    CEMeta(Cli)
    void OnResourceDragEnd(std::string resourceGUID, UInt32 positionX, UInt32 positionY);

    MaterialExpression* FindRerouteDeclarationByGuid(const std::string variableGuid) const;

    MaterialExpression* FindRerouteDeclarationByName(const std::string name) const;
public:
    // the call function with Action_ prefix will

    void BeginAction();

    void EndAction();

    void SelectExpressions(std::vector<MaterialExpression*> expressions);

    MaterialExpression* Action_CreateExpression(const std::string& expressionName, int32_t positionX = 0, int32_t positionY = 0, std::function<std::shared_ptr<MaterialExpression>()> createFunction=nullptr);

    MaterialExpression* Action_CreateExpressionFunctionCall(const std::string& materialFunction, int32_t positionX = 0, int32_t positionY = 0);

    void Action_DeleteExpression(MaterialExpression * expression);

    void Action_MoveExpression(MaterialExpression * expression, int32_t targetPositionX, int32_t targetPositionY);

    void CopyExpressionsSelected();

    CEMeta(Cli)
    void Action_PasteExpressions(const std::string& expressionsStr, int32_t targetX, int32_t targetY);

    void Action_CreateLink(ExpressionOutput * output, ExpressionInput * input);

    void Action_BreakLink(ExpressionInput * input);

    void Action_ConvertToDstExpression(const std::string& expressionName, MaterialExpression* srcExpression);

public:
    void ClearInvalidLinks() override;

    void UpdateExpressionAppearance(MaterialExpression * expression) override;
    void UpdateUITextureAndExpressionTexture(MaterialExpression * expression) override;
    bool IsEditFx() const
    {
        return m_MaterialFunction == nullptr;
    }

    void SetPreviewExpression(MaterialExpression * expression);

    MaterialExpression* GetPreviewExpression() const
    {
        return m_PreviewExpression;
    }

    // traverse expressions to find compile errors and update preview sphere
    void UpdateExpressionsState(MaterialExpression* expressionChanged = nullptr);

    bool IsMaterialExpressionAffectFinalResult(MaterialExpression * expression);

    // called by MaterialGraph
    void OpenMaterialFunction(const std::string& materialFunctionGuid);

    MaterialEditorCallback* GetCallback()
    {
        if (mCallback)
            return static_cast<MaterialEditorCallback*>(mCallback);
        else
            return nullptr;
    }

    auto* GetVertexShaderExpression()
    {
        return m_VertexShaderExpression;
    }

    auto* GetSurfaceShaderExpression()
    {
        return m_SurfaceShaderExpression;
    }

    auto* GetPreviewExpression()
    {
        return m_PreviewExpression;
    }

    std::vector<std::shared_ptr<MaterialExpression>> GetExpressions()
    {
        return m_Expressions;
    }

    std::vector<ExpressionInput*> GetLinkedInputPins(ExpressionOutput * output) override;

private:
    void OpenFx(ResourcePtr resource);

    void OpenMaterialFunction(ResourcePtr resource);

    MaterialExpression* CreateExpression(std::string expressionName, int32_t positionX, int32_t positionY, std::function<std::shared_ptr<MaterialExpression>()> callback=nullptr);

    MaterialExpression* CreateExpressionFunctionCall(std::string materialFunctionGuid, int32_t positionX, int32_t positionY);

    void AddExpression(std::shared_ptr<MaterialExpression> expression);

    void DeleteExpression(std::shared_ptr<MaterialExpression> expression);

    void MoveExpression(MaterialExpression * expression, int32_t targetPositionX, int32_t targetPositionY);

    void CreateLink(ExpressionOutput * output, ExpressionInput * input);

    void BreakLink(ExpressionInput * input);

    void ConvertToDstExpression(std::shared_ptr<MaterialExpression> srcExpression, std::shared_ptr<MaterialExpression> dstExpression);

    std::shared_ptr<MaterialExpression> FindSharedPtrExpression(MaterialExpression * expression);

    void DoUpdateExpressionsState();

    void InitializeNamedReroutes() override
    {
        m_MaterialGraph->InitializeNamedReroutes();
    }

    bool Compile(bool saveToFile);

    bool Apply_Fx();
    bool Apply_MaterialFunction();

    void TryAllocateNameOrId(MaterialExpression * expression);

    cross::SerializeNode SerializeExpressions(std::vector<MaterialExpression*> expressions);

    bool IsCurrentFxRenderGPass() const;

    void UpdateMaterialState(FxPtr fx, const MaterialDefines& materialDefines);



    // pin may be invalid when pin's layout changed
    bool IsExpressionPinValid(ExpressionPin * pin);

    MaterialExpression* GetParentExpression(ExpressionInput * input);

    void ApplyPropertiesToFx(MaterialCompiler & compiler, resource::Fx * fx);

    void RegisterExpressions();

    MaterialCompiler::InputParameters GetMaterialCompilerInputParameters();

public:
    void SetResourceChanged(bool value)
    {
        m_IsResourceChanged = value;
    }
    auto& GetVTs() 
    {
        return m_VTs;
    }
    UITexturePtr GetUITexture(MaterialExpression* expression);

    static void Sync(MaterialDefines& dst, FxPtr src);
    static void SyncForSurfaceDomain(FxPtr dst, const MaterialDefines& src);
    static void SyncForMeshDecalDomain(FxPtr dst, const MaterialDefines& src, MaterialCompiler::CompilationOutput const& compilationOutput);

protected:
    // MaterialGraph
    std::unique_ptr<MaterialGraph> m_MaterialGraph;

    // Transaction Manager
    MaterialTransactionManager m_TransactionManager;

    // IdGenerator
    crude_blueprint::IdGenerator m_IdGenerator;
    FunctionInputOutputIDGenerator m_FunctionInputOutputIdGenerator;
    NamedRerouteGenerator m_NamedRerouteIdGenerator;

    // TempShaderFilePath, used to compile generated shader
    std::string m_TempShaderFilePath;

    // Resource
    std::string m_ResourceFileGuid;
    bool m_IsResourceChanged = false;

    // Virtual Texture
    std::vector<std::string> m_VTs;        // Only 4 virtual texture can be used at the same time
    std::set<std::string> m_OverflowVTs;   // If more than 4 virtual texture are used in material editor, their names will be stored here

    // Fx
    FxPtr m_Fx;
    MaterialDefines mMaterialDefines{};

    // MaterialFunction
    MaterialFunctionPtr m_MaterialFunction;
    std::vector<MaterialExpression*> m_FunctionOutputExpressions;

    // Expressions
    std::vector<std::shared_ptr<MaterialExpression>> m_Expressions;
    std::vector<MaterialExpression*> m_SelectedExpressions;
    MaterialExpressionVertexShader* m_VertexShaderExpression = nullptr;
    MaterialExpressionSurfaceShader* m_SurfaceShaderExpression = nullptr;
    MaterialExpression* m_PreviewExpression = nullptr;
    std::string m_CopyExpressionsString;

    // Parameters + ShaderConsts for material instance
    MaterialParameterAndShaderConstSet m_ParameterAndShaderConstSet;

    // ImportShader
    std::shared_ptr<cross::threading::TaskEventArray> m_ImportTasks;
    std::function<FxPtr()> m_ImportCompleteFunction;
    bool m_NeedUpdateExpressionState;
    // Used to preview texture
    std::unordered_map<const MaterialExpression*, UITexturePtr> m_ExpressionToUITextureMap;
    // Preview
    class MaterialPreview* m_Preview;

    MaterialCompiler::CompilationOutput mCompilationOutput{};

private:
    ax::NodeEditor::EditorContext* m_Editor = nullptr;

    ImTextureID m_HeaderBackground = nullptr;
    std::unique_ptr<node_editor::utility::NodeBuilderBlueprint> m_NodeBuilder;

    friend class MaterialGraph;
    friend class MaterialTransactionCreateExpression;
    friend class MaterialTransactionDeleteExpression;
    friend class MaterialTransactionMoveExpression;
    friend class MaterialTransactionCreateLink;
    friend class MaterialTransactionBreakLink;
    friend class MaterialTransactionPasteExpressions;
    friend class MaterialTransactionConvertToDstExpression;
};

class TextureExpressionHandler
{
public:
    virtual ~TextureExpressionHandler() = default;
    virtual bool CanHandle(MaterialExpression* expression) const = 0;
    virtual void UpdateTexture(MaterialExpression* expression, MaterialEditor* editor) = 0;

protected:
    void UpdateTextureCommon(MaterialExpression* expression, const std::string& texturePath, MaterialEditor* editor, MaterialValueType& outTextureType);
    

private:
    UITexturePtr GetOrCreateUITexture(MaterialExpression* expression, MaterialEditor* editor)
    {
        if (editor->m_ExpressionToUITextureMap.find(expression) == editor->m_ExpressionToUITextureMap.end())
        {
            auto uiTexPtr = std::make_shared<UITexture>();
            editor->m_ExpressionToUITextureMap.emplace(expression, uiTexPtr);
            return uiTexPtr;
        }
        return editor->GetUITexture(expression);
    }

    void HandleVirtualTexture(TexturePtr texturePtr, MaterialInterfacePtr material, TextureType textureType, MaterialValueType& outTextureType);
    void HandleRegularTexture(TexturePtr texturePtr, MaterialInterfacePtr material, TextureType textureType, MaterialValueType& outTextureType);
};

class TextureObjectHandler : public TextureExpressionHandler
{
public:
    bool CanHandle(MaterialExpression* expression) const override;

    void UpdateTexture(MaterialExpression* expression, MaterialEditor* editor) override;
    
};

class TextureParameterHandler : public TextureExpressionHandler
{
public:
    bool CanHandle(MaterialExpression* expression) const override;

    void UpdateTexture(MaterialExpression* expression, MaterialEditor* editor) override;
};
}   // namespace cross