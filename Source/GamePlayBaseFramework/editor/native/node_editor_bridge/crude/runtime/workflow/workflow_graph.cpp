#include "workflow_graph.h"

#include <assert.h>
#include "CrossBase/String/Word.h"
#include "editor/crude_logger.h"
#include "workflow_editor.h"
#include "workflow_node.h"

#include "workflow_graph.h"

#include "blueprint/details/node/variable/blueprint_variable_generic.h"
#include "visual/blueprint/details/node/blueprint_slot.h"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/blueprint/details/blueprint_graph.h"

namespace cross {
WorkflowGraph::WorkflowGraph(WorkflowEditor& editor, WorkflowEditorCallback* callback)
    : Graph(callback)
    , m_Editor(editor)
{}

void WorkflowGraph::AllocatePinsForNode(WorkflowNode* node) 
{
    auto cur_graph_node = node->m_GraphNode;

    for (auto* input : node->GetInExecPins())
    {
        auto* newPin = AllocateGraphPin<WorkflowGraphPin>();
        newPin->m_GetName = [=]() { return std::string(input->m_BlueprintSlot->name()); };
       
        newPin->m_Node = cur_graph_node;
        newPin->m_Type = PinType::Input;
        newPin->m_Slot = input;
        newPin->m_DrawType = node_editor::drawing::SlotDrawingType::Flow;
        newPin->is_exec = true;
        input->m_GraphPin = newPin;
        cur_graph_node->m_InputPins.emplace_back(newPin);
    }

    for (auto* input : node->GetInputDataPins())
    {
        auto* newPin = AllocateGraphPin<WorkflowGraphPin>();
        newPin->m_GetName = [=]() { return std::string(input->m_BlueprintSlot->name()); };
        newPin->m_Node = cur_graph_node;
        newPin->m_Type = PinType::Input;
        newPin->m_Slot = input;
        input->m_GraphPin = newPin;
        cur_graph_node->m_InputPins.emplace_back(newPin);
    }

    for (auto* output : node->GetOutExecPins())
    {
        auto* newPin = AllocateGraphPin<WorkflowGraphPin>();
        newPin->m_GetName = [=]() { return std::string(output->m_BlueprintSlot->name()); };
        newPin->m_Node = cur_graph_node;
        newPin->m_Type = PinType::Output;
        newPin->m_Slot = output;
        newPin->m_DrawType = node_editor::drawing::SlotDrawingType::Flow;
        newPin->is_exec = true;
        output->m_GraphPin = newPin;

        cur_graph_node->m_OutputPins.emplace_back(newPin);
    }

    for (auto* output : node->GetOutputDataPins())
    {
        auto* newPin = AllocateGraphPin<WorkflowGraphPin>();
        newPin->m_GetName = [=]() { return std::string(output->m_BlueprintSlot->name()); };
        newPin->m_Node = cur_graph_node;
        newPin->m_Type = PinType::Output;
        newPin->m_Slot = output;
        output->m_GraphPin = newPin;
        cur_graph_node->m_OutputPins.emplace_back(newPin);
    }
}
WorkflowGraphNode* WorkflowGraph::AddNode(WorkflowNode* node)
    {
    node->SetGraph(this);
    WorkflowGraphNode* newNode = AddGraphNode<WorkflowGraphNode>();
    newNode->m_GetName = [=]() { return node->GetCaption(); };
    newNode->m_GetDescription = [=]() { return node->GetDescription(); };
    newNode->m_PositionX = node->m_EditorPositionX;
    newNode->m_PositionY = node->m_EditorPositionY;
    newNode->m_GetTitleColor = [=]() {
        cross::HashString name(node->GetMenuName());
        auto color = ImColor(name.GetHash32());
        color.Value.w = 1;
        return color;
    };
    node->LogicNode()->set_editor_x(node->m_EditorPositionX);
    node->LogicNode()->set_editor_y(node->m_EditorPositionY);
    newNode->m_WorkflowNode = node;
    newNode->m_GetErrorMessage = [=]() { return node->m_ErrorMessage; };
    node->m_GraphNode = newNode;
    
    AllocatePinsForNode(node);

    if (m_LogicGraph && !(m_LogicGraph->GetNodeById(node->LogicNode()->id())))
    {
        m_LogicGraph->AddAndInitNode(node->LogicNode(), node->LogicNode()->id());
    }
    return newNode;
}

void WorkflowGraph::DeleteNode(WorkflowNode* node)
{
    node->BreakUpLinks();
    if (m_LogicGraph)
    {
        m_LogicGraph->RemoveNode(node->LogicNode());
    }
    
    DoDeleteNode(node->m_GraphNode);
}

void WorkflowGraph::UpdateNodePosition(WorkflowNode* node)
{
    SetNodePosition(node->m_GraphNode, node->m_EditorPositionX, node->m_EditorPositionY);
    node->LogicNode()->set_editor_x(node->m_EditorPositionX);
    node->LogicNode()->set_editor_y(node->m_EditorPositionY);
}

void WorkflowGraph::CreateLink(WorkflowSlot* outputPin, WorkflowSlot* inputPin)
{
    DoCreateLink(outputPin->m_GraphPin, inputPin->m_GraphPin);
    outputPin->m_LinkedSlots.push_back(inputPin);
    inputPin->m_LinkedSlots.push_back(outputPin);
    m_LogicGraph->LinkSlots(outputPin->m_BlueprintSlot, inputPin->m_BlueprintSlot);
}

void WorkflowGraph::BreakLink(WorkflowSlot* outputPin, WorkflowSlot* inputPin)
{
    DoBreakLink(outputPin->m_GraphPin, inputPin->m_GraphPin);
    outputPin->m_LinkedSlots.erase(std::remove(outputPin->m_LinkedSlots.begin(), outputPin->m_LinkedSlots.end(), inputPin));
    inputPin->m_LinkedSlots.erase(std::remove(inputPin->m_LinkedSlots.begin(), inputPin->m_LinkedSlots.end(), outputPin));
    if (m_LogicGraph)
    {
        // 切换graph时不需要从logic graph里删除link
        auto cur_connect_id = gbf::logic::UBpGraphBase::GetConnectIdFromSlots(outputPin->m_BlueprintSlot, inputPin->m_BlueprintSlot);
        m_LogicGraph->UnlinkSlots(cur_connect_id);
    }
    
}

void WorkflowGraph::ReconstructLinks()
{
    for (auto& nodePtr : m_Nodes)
    {
        for (auto& pinPtr : nodePtr->m_InputPins)
        {
            pinPtr->m_LinkedPins.clear();
        }
        for (auto& pinPtr : nodePtr->m_OutputPins)
        {
            pinPtr->m_LinkedPins.clear();
        }
    }
    std::unordered_map<gbf::logic::UBlueprintSlot*, WorkflowSlot*> slot_map;
    for (auto& nodePtr : m_Nodes)
    {
        // fill workflow Slot m_LinkedSlots
        WorkflowNode* node = static_cast<WorkflowGraphNode*>(nodePtr.get())->m_WorkflowNode;
        for (auto* one_slot : node->GetOutExecPins())
        {
            slot_map[one_slot->m_BlueprintSlot] = one_slot;
        }
        for (auto* one_slot : node->GetInExecPins())
        {
            slot_map[one_slot->m_BlueprintSlot] = one_slot;
        }

        for (auto* one_slot : node->GetInputDataPins())
        {
            slot_map[one_slot->m_BlueprintSlot] = one_slot;
        }

        for (auto* one_slot : node->GetOutputDataPins())
        {
            slot_map[one_slot->m_BlueprintSlot] = one_slot;
        }
    }

    for (const auto& one_link : m_LogicGraph->GetConnections())
    {
        auto temp_in_slot = one_link.second->in_slot();
        auto temp_out_slot = one_link.second->out_slot();
        auto temp_in_workflow_slot = slot_map[temp_in_slot];
        auto temp_out_workflow_slot = slot_map[temp_out_slot];
        assert(temp_in_workflow_slot);
        assert(temp_out_workflow_slot);
        temp_in_workflow_slot->m_GraphPin->LinkTo(temp_out_workflow_slot->m_GraphPin);
        temp_in_workflow_slot->m_LinkedSlots.push_back(temp_out_workflow_slot);
        temp_out_workflow_slot->m_LinkedSlots.push_back(temp_in_workflow_slot);
    }
}


void WorkflowGraph::OnBeginAction()
{
    m_Editor.BeginAction();
}

void WorkflowGraph::OnEndAction()
{
    m_Editor.EndAction();
}

void WorkflowGraph::OnCreateLink(GraphPin* outputPin, GraphPin* inputPin)
{
    auto outputWorkflowPin = Cast(outputPin);
    auto inputWorkflowPin = Cast(inputPin);
    auto outputSlot = outputWorkflowPin->m_Slot;
    auto inputSlot = inputWorkflowPin->m_Slot;

    if (outputSlot->m_IsDataSlot != inputSlot->m_IsDataSlot)
    {
        return;
    }

    if (inputSlot->m_IsDataSlot && outputSlot->m_IsDataSlot)
    {
        if (!m_LogicGraph->CanCreatLink(inputSlot->m_BlueprintSlot, outputSlot->m_BlueprintSlot))
            return;
    }

    m_Editor.Command_CreateLink(outputSlot, inputSlot);
}

void WorkflowGraph::OnBreakLink(GraphPin* outputPin, GraphPin* inputPin)
{
    m_Editor.Command_BreakLink(Cast(outputPin)->m_Slot, Cast(inputPin)->m_Slot);
}

void WorkflowGraph::OnNodeMoved(GraphNode* node, int32_t dstPosX, int32_t dstPosY)
{
    m_Editor.Command_MoveNode(Cast(node)->m_WorkflowNode, dstPosX, dstPosY);
}

void WorkflowGraph::OnDeleteNodes(const std::vector<GraphNode*> nodes)
{
    m_Editor.BeginAction();
    for (auto* node : nodes)
    {
        m_Editor.Command_DeleteNode(Cast(node)->m_WorkflowNode);
    }
    m_Editor.EndAction();
}

const WorkflowNodeRegistry::NodeCreator* WorkflowGraph::FindSelectedNodeCreators(const std::vector<const WorkflowNodeRegistry::NodeCreator*>& nodeCreators)
{
    if (m_IsNeedRefreshSelectedNode)
    {
        int minLength = INT_MAX;
        const WorkflowNodeRegistry::NodeCreator* selectedNodeCreator = nullptr;

        // find nodeName start with seachText
        for (auto* nodeCreator : nodeCreators)
        {
            std::string_view nodeDisplayName = nodeCreator->menuName;

            if (cross::ToLower(std::string(nodeDisplayName))._Starts_with(m_SearchText))
            {
                if (nodeDisplayName.size() < minLength)
                {
                    minLength = nodeDisplayName.size();
                    selectedNodeCreator = nodeCreator;
                }
            }
        }

        // otherwise select node with minimum length
        if (!selectedNodeCreator)
        {
            minLength = INT_MAX;

            for (auto* nodeCreator : nodeCreators)
            {
                std::string_view nodeDisplayName = nodeCreator->menuName;

                if (nodeDisplayName.size() < minLength)
                {
                    minLength = nodeDisplayName.size();
                    selectedNodeCreator = nodeCreator;
                }
            }
        }

        return selectedNodeCreator;
    }
    else
    {
        if (nodeCreators.size() > 0)
        {
            m_SelectedNodeIndex = m_SelectedNodeIndex % nodeCreators.size();
            return nodeCreators[m_SelectedNodeIndex];
        }
        else
        {
            return nullptr;
        }
    }
}

static int WorkflowSearchInputTextCallback(ImGuiInputTextCallbackData* data)
{
    return reinterpret_cast<WorkflowGraph*>(data->UserData)->SearchInputTextCallback(data);
}

int WorkflowGraph::SearchInputTextCallback(ImGuiInputTextCallbackData* data)
{
    if ((data->EventFlag & ImGuiInputTextFlags_CallbackHistory) != 0)
    {
        if (data->EventKey == ImGuiKey_UpArrow)
        {
            m_SelectedNodeIndex--;
        }
        else if (data->EventKey == ImGuiKey_DownArrow)
        {
            m_SelectedNodeIndex++;
        }
    }

    if ((data->EventFlag & ImGuiInputTextFlags_CallbackEdit) != 0)
    {
        m_SearchText = cross::ToLower(std::string(data->Buf));
        m_IsNeedRefreshSelectedNode = true;
    }

    return 0;
}

void WorkflowGraph::OnShowCreateNodeDialog(bool isFirstShow, GraphPin* pin)
{
    m_Editor.BeginAction();
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImGui::GetMousePosOnOpeningCurrentPopup());

    if (isFirstShow)
    {
        m_SearchText = "";
    }

    WorkflowNode* newNode = nullptr;
    m_IsNeedRefreshSelectedNode = false;

    // filter nodes
    std::vector<const WorkflowNodeRegistry::NodeCreator*> nodeCreators;
    for (auto& [className, nodeCreator] : m_Editor.GetWorkflowNodeRegistry().GetAllNodeCreators())
    {
        if (nodeCreator.allowEditedByUser)
        {
            if (pin)
            {
                if (pin->m_Type == PinType::Input)
                {
                    if (nodeCreator.hasOutput)
                    {
                        nodeCreators.push_back(&nodeCreator);
                    }
                }
                else
                {
                    if (nodeCreator.hasInput)
                    {
                        nodeCreators.push_back(&nodeCreator);
                    }
                }
            }
            else
            {
                nodeCreators.push_back(&nodeCreator);
            }
        }
    }

    // handle search action
    bool isInSearchMode = false;
    bool isNeedCreateSelectedNode = false;
    {
        // focus to InputText
        if (isFirstShow)
        {
            ImGui::SetKeyboardFocusHere(0);
        }

        char buf[256];
        if (ImGui::InputTextWithHint("", "Search", buf, 256, ImGuiInputTextFlags_EnterReturnsTrue | ImGuiInputTextFlags_CallbackHistory | ImGuiInputTextFlags_CallbackEdit, WorkflowSearchInputTextCallback, this))
        {
            // create selected node when press [Enter]
            isNeedCreateSelectedNode = true;
        }

        // filter
        std::vector<const WorkflowNodeRegistry::NodeCreator*> searchFilteredNodeCreators;
        if (m_SearchText.size() > 0)
        {
            for (auto* creator : nodeCreators)
            {
                if (cross::ToLower(creator->menuName).find(m_SearchText) != std::string::npos)
                {
                    searchFilteredNodeCreators.push_back(creator);
                }
            }
            nodeCreators = std::move(searchFilteredNodeCreators);
            isInSearchMode = true;
        }
    }

    // find best match node
    const WorkflowNodeRegistry::NodeCreator* nodeSelected = nullptr;
    if (isInSearchMode)
    {
        nodeSelected = FindSelectedNodeCreators(nodeCreators);
    }

    // show nodes
    {
        if (isInSearchMode)
        {
            ImGui::SetNextItemOpen(true);
        }
        else if (isFirstShow)
        {
            ImGui::SetNextItemOpen(false);
        }

        if (!nodeCreators.empty() && ImGui::TreeNode("Default"))
        {
            for (auto* creator : nodeCreators)
            {
                bool isCurrentCreatorSelected = nodeSelected == creator;

                if (ImGui::Selectable(creator->menuName.c_str(), isCurrentCreatorSelected))
                {
                    newNode = m_Editor.Command_CreateNode(creator->className, nodePosition.x, nodePosition.y);
                }
            }

            ImGui::TreePop();
        }
    }

    if (!newNode && isNeedCreateSelectedNode && nodeSelected)
    {
        newNode = m_Editor.Command_CreateNode(nodeSelected->className, nodePosition.x, nodePosition.y);
    }

    // create link
    LinkNodeWithPin(newNode, pin);
    ImGui::CloseCurrentPopup();
    m_Editor.EndAction();
}

void WorkflowGraph::OnShowNodesContextMenu(std::vector<GraphNode*> nodes)
{
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImGui::GetMousePosOnOpeningCurrentPopup());

    if (ImGui::Selectable("Delete"))
    {
        m_Editor.BeginAction();
        for (auto* node : nodes)
        {
            m_Editor.Command_DeleteNode(Cast(node)->m_WorkflowNode);
        }
        m_Editor.EndAction();
    }
    else if (ImGui::Selectable("Cut"))
    {
        OnCut();
    }
    else if (ImGui::Selectable("Copy"))
    {
        OnCopy();
    }
}

void WorkflowGraph::OnNodesSelected(const std::vector<GraphNode*> nodes)
{
    std::vector<WorkflowNode*> workflow_nodes(nodes.size());

    std::transform(nodes.begin(), nodes.end(), workflow_nodes.begin(), [this](GraphNode* node) { return Cast(node)->m_WorkflowNode; });

    m_Editor.SelectNodes(workflow_nodes);
}

void WorkflowGraph::OnUndo()
{
    m_Editor.Undo();
}

void WorkflowGraph::OnRedo()
{
    m_Editor.Redo();
}

void WorkflowGraph::OnCopy()
{
    m_Editor.CopyNodesSelected();
}

void WorkflowGraph::OnPaste(int32_t x, int32_t y)
{
    m_Editor.Command_PasteNodes(x, y);
}

void WorkflowGraph::OnCut()
{
    OnCopy();

    m_Editor.BeginAction();
    for (auto* node : m_Editor.GetSelectedNodes())
    {
        m_Editor.Command_DeleteNode(node);
    }
    m_Editor.EndAction();
}

void WorkflowGraph::Reset(gbf::logic::UBpGraphBase* newGraph)
{
    m_LogicGraph = newGraph;
}

ExpressionCreateMenuInfo WorkflowGraph::GetGraphCategories(int pinType)
{
    std::map<std::string, std::vector<ExpressionCreateNodeInfo> > creatorCategoryMap;
    for (const auto& [k, createInfo] : m_Editor.GetWorkflowNodeRegistry().GetAllNodeCreators())
    {
        if ((pinType == -1 && !createInfo.hasOutput) || (pinType == 1 && !createInfo.hasInput))
        {
            continue;
        }
        auto categories = createInfo.categories;
        auto creatorClassName = createInfo.className;
        auto creatorMenuName = createInfo.menuName;
        for (auto category : categories)
        {
            ExpressionCreateNodeInfo nodeInfo;
            nodeInfo.className = creatorClassName;
            nodeInfo.menuName = creatorMenuName;
            nodeInfo.materialFunctionGuid = "null";
            creatorCategoryMap[category].emplace_back(nodeInfo);
        }
    }

    ExpressionCreateMenuInfo menuInfo;
    for (auto mapIter = creatorCategoryMap.begin(); mapIter != creatorCategoryMap.end(); mapIter++)
    {
        ExpressionCreateGroupInfo groupInfo;
        groupInfo.categoryName = mapIter->first;
        groupInfo.ExpressionInfos = mapIter->second;
        menuInfo.Groups.push_back(groupInfo);
    }
    return menuInfo;
}

void WorkflowGraph::LinkNodeWithPin(WorkflowNode* newNode, GraphPin* pin) 
{
    if (newNode)
    {
        if (pin)
        {
            auto* workflowPin = Cast(pin);
            if (workflowPin->is_exec)
            {
                if (pin->m_Type == PinType::Output)
                {
                    if (!newNode->GetInExecPins().empty())
                    {
                        auto* inputPin = newNode->GetInExecPins()[0]->m_GraphPin;
                        OnCreateLink(pin, inputPin);
                    }
                }
                else
                {
                    // pin is input
                    if (!newNode->GetOutExecPins().empty())
                    {
                        auto* outputPin = newNode->GetOutExecPins()[0]->m_GraphPin;
                        OnCreateLink(outputPin, pin);
                    }
                }
            }
            else
            {
                if (pin->m_Type == PinType::Output)
                {
                    auto* inputPin = newNode->GetInputDataPins()[0]->m_GraphPin;
                    OnCreateLink(pin, inputPin);
                }
                else
                {
                    // pin is input

                    auto* outputPin = newNode->GetOutputDataPins()[0]->m_GraphPin;
                    OnCreateLink(outputPin, pin);
                }
            }
        }
    }
}
void WorkflowGraph::CreateNewNode(std::string nodeClassName, std::string extra_info, int position_x, int position_y, GraphPin* pin, ExpressionCreateNodeInfo* createInfo)
    {
    auto nodePosition = ax::NodeEditor::ScreenToCanvas(ImVec2{(float)position_x, (float)position_y});
    WorkflowNode* newNode = m_Editor.Command_CreateNode(nodeClassName, nodePosition.x, nodePosition.y);
    
    LinkNodeWithPin(newNode, pin);
    CloseCurrentDialog();
}
void WorkflowGraph::BreakUpLinksForNode(const gbf::logic::UBlueprintNode* in_logic_node)
{
    auto cur_workflow_node = m_Editor.FindNode(in_logic_node);
    if (cur_workflow_node)
    {
        cur_workflow_node->BreakUpLinks();
    }
}

void WorkflowGraph::RefreshNodeSlots(const gbf::logic::UBlueprintNode* in_logic_node)
{
    auto cur_workflow_node = m_Editor.FindNode(in_logic_node);
    DoClearNodePins(cur_workflow_node->GraphNode());
    cur_workflow_node->Refresh();
    AllocatePinsForNode(cur_workflow_node);
}

void WorkflowGraph::OnVariableTypeChanged(const std::string& name, WorkflowVariableType new_type)
{
    TraverseNamedVariableNodes(name, [=](WorkflowGraphNode * editor_var_node) {
        BreakUpLinksForNode(editor_var_node->m_WorkflowNode->LogicNode().get());
        auto logic_var_node = dynamic_cast<gbf::logic::INamedVariableNodeInterface*>(editor_var_node->m_WorkflowNode->LogicNode().get());
        logic_var_node->on_variable_type_changed(name);
    });
}

void WorkflowGraph::OnVariableRenamed(const std::string& old_name, const std::string& new_name)
{
    TraverseNamedVariableNodes(old_name, [=](WorkflowGraphNode* editor_var_node) {
        auto logic_var_node = dynamic_cast<gbf::logic::INamedVariableNodeInterface*>(editor_var_node->m_WorkflowNode->LogicNode().get());
        logic_var_node->on_variable_name_changed(old_name, new_name);
    });
}

void WorkflowGraph::OnVariableDeleted(const std::string& name)
{
    std::vector<WorkflowNode*> _PendingDeleteList;
    _PendingDeleteList.reserve(m_Nodes.size() / 2);
    
    TraverseNamedVariableNodes(name, [=, &_pendingDeleteList = _PendingDeleteList](WorkflowGraphNode* editor_var_node) {
        _pendingDeleteList.emplace_back(editor_var_node->m_WorkflowNode);
    });

    for (auto _node : _PendingDeleteList)
    {
        DeleteNode(_node);
    }
}

void WorkflowGraph::TraverseAllVariableNodes(const std::function<void(WorkflowGraphNode*)>& lambda)
{
    for (auto& _node : m_Nodes)
    {
        if (auto wf_node = dynamic_cast<WorkflowGraphNode*>(_node.get()); wf_node && wf_node->m_WorkflowNode)
        {
            if (wf_node->m_WorkflowNode->IsVariableNode())
            {
                lambda(wf_node);
            }
        }
    }
}
void WorkflowGraph::TraverseNamedVariableNodes(const std::string& name, const std::function<void(WorkflowGraphNode*)>& lambda)
{

    for (auto& _node : m_Nodes)
    {
        if (auto wf_node = dynamic_cast<WorkflowGraphNode*>(_node.get()); wf_node && wf_node->m_WorkflowNode)
        {
            if (wf_node->m_WorkflowNode->IsVariableNode(name))
            {
                lambda(wf_node);
            }
        }
    }
}
}   // namespace cross