#pragma once

#include <inttypes.h>

#define PRI_sv ".*s"
#define FMT_sv(sv) static_cast<int>((sv).size()), (sv).data()

#define PRI_pin "s %" PRIu32 "%s%" PRI_sv "%s"
#define FMT_pin(pin) "Pin", (pin)->m_Id, (pin)->m_Name.empty() ? "" : " \"", FMT_sv((pin)->m_Name), (pin)->m_Name.empty() ? "" : "\""

#define PRI_node "s %" PRIu32 "%s%" PRI_sv "%s"
#define FMT_node(node) "Node", (node)->m_Id, (node)->GetName().empty() ? "" : " \"", FMT_sv((node)->GetName()), (node)->GetName().empty() ? "" : "\""