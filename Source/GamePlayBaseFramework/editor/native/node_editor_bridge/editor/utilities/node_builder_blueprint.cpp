#include "editor/utilities/node_builder_blueprint.h"

#include "editor/imgui_extras.h"
#include "editor/utilities/slot_edit_control.h"
#include "imgui_internal.h"
////#include "editor/utilities/other_utilities.h"
#include "memoryhooker/Module.h"
IMPORT_MODULE
namespace node_editor::utility {

//------------------------------------------------------------------------------
static IconType SlotDrawingTypeToIconType(drawing::SlotDrawingType drawing_type)
{
    switch (drawing_type)
    {
    case drawing::SlotDrawingType::Flow:
        return IconType::Flow;
    case drawing::SlotDrawingType::Void:
        return IconType::Circle;
    case drawing::SlotDrawingType::Any:
        return IconType::Diamond;
    case drawing::SlotDrawingType::Bool:
        return IconType::Circle;
    case drawing::SlotDrawingType::Int:
        return IconType::Circle;
    case drawing::SlotDrawingType::Float:
        return IconType::Circle;
    case drawing::SlotDrawingType::String:
        return IconType::Circle;
    }

    return IconType::Circle;
}
//------------------------------------------------------------------------------
static ImVec4 SlotDrawingTypeToColor(drawing::SlotDrawingType drawing_type)
{
    switch (drawing_type)
    {
    case drawing::SlotDrawingType::Flow:
        return ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    case drawing::SlotDrawingType::Void:
        return ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    case drawing::SlotDrawingType::Any:
        return ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    case drawing::SlotDrawingType::Bool:
        return ImVec4(220 / 255.0f, 48 / 255.0f, 48 / 255.0f, 1.0f);
    case drawing::SlotDrawingType::Int:
        return ImVec4(68 / 255.0f, 201 / 255.0f, 156 / 255.0f, 1.0f);
    case drawing::SlotDrawingType::Float:
        return ImVec4(147 / 255.0f, 226 / 255.0f, 74 / 255.0f, 1.0f);
    case drawing::SlotDrawingType::String:
        return ImVec4(124 / 255.0f, 21 / 255.0f, 153 / 255.0f, 1.0f);
    }

    return ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
}
//------------------------------------------------------------------------------

NodeBuilderBlueprint::NodeBuilderBlueprint(ImFont* header_font, ImTextureID texture, int textureWidth, int textureHeight, const ImVec2& icon_size)
    : header_font_(header_font)
    , subtitle_font_(header_font)
    , HeaderTextureId(texture)
    , HeaderTextureWidth(textureWidth)
    , HeaderTextureHeight(textureHeight)
    , pin_icon_size_(icon_size)
{}

NodeBuilderBlueprint::NodeBuilderBlueprint(ImFont* header_font, ImFont* subtitle_font, ImTextureID texture, int textureWidth, int textureHeight, const ImVec2& icon_size)
    : header_font_(header_font)
    , subtitle_font_(subtitle_font)
    , HeaderTextureId(texture)
    , HeaderTextureWidth(textureWidth)
    , HeaderTextureHeight(textureHeight)
    , pin_icon_size_(icon_size)
{}

void NodeBuilderBlueprint::BeginNode(const drawing::NodeDrawing& drawing_node)
{
    CurrentNodeId = drawing_node.id;
    ShowErrorMsg = drawing_node.show_error_msg;

    ax::NodeEditor::BeginNode(drawing_node.id);

    // General node layout:
    //
    // +-----------------------------------+
    // | Title                             |
    // |                                   |
    // | SubTitle                          |
    // | +-----------[ Dummy ]-----------+ |
    // | +---------------+   +-----------+ |
    // | | o Pin         |   |   Out B o | |
    // | | o Pin <Value> |   |   Out A o | |
    // | | o Pin         |   |           | |
    // | +---------------+   +-----------+ |
    // +-----------------------------------+

    float captionItemWidth = 100;

    // Show title if node has one.
    if (!drawing_node.name.empty())
    {
        // Use a callback here, so we can get right size with pins for background.
        auto headerBackgroundRenderer = ImEx::ItemBackgroundRenderer([this, nname = std::string(drawing_node.name), id = drawing_node.id, color = drawing_node.color, u32color = (ImU32)drawing_node.color](ImDrawList* drawList) {
            auto border = ax::NodeEditor::GetStyle().NodeBorderWidth;
            auto rounding = ax::NodeEditor::GetStyle().NodeRounding;

            auto itemMin = ImGui::GetItemRectMin();
            auto itemMax = ImGui::GetItemRectMax();

            auto nodeStart = ax::NodeEditor::GetNodePosition(id);
            auto nodeSize = ax::NodeEditor::GetNodeSize(id);

            itemMin = nodeStart;
            itemMin.x = itemMin.x + border - 0.5f;
            itemMin.y = itemMin.y + border - 0.5f;
            itemMax.x = nodeStart.x + nodeSize.x - border + 0.5f;
            itemMax.y = itemMax.y + ImGui::GetStyle().ItemSpacing.y + 0.5f;

            HeaderMin = itemMin;
            HeaderMax = itemMax;

            const auto uv = ImVec2((HeaderMax.x - HeaderMin.x) / (float)(4.0f * HeaderTextureWidth), (HeaderMax.y - HeaderMin.y) / (float)(4.0f * HeaderTextureHeight));

            if (HeaderTextureId == nullptr)
            {
                drawList->AddRectFilled(itemMin, itemMax, u32color, rounding, ImDrawFlags_RoundCornersTop);
            }
            else
            {
                drawList->AddImageRounded(HeaderTextureId, itemMin, itemMax, ImVec2(0, 0), uv, u32color, rounding, ImDrawFlags_RoundCornersTop);
            }
            // drawList->AddRectFilledMultiColor(itemMin, itemMax, u32color, IM_COL32(255, 0, 0, 255), rounding, ImDrawFlags_RoundCornersTop);
        });

        ImGui::PushFont(header_font_);
        ImGui::TextUnformatted(drawing_node.name.data(), drawing_node.name.data() + drawing_node.name.size());

        if (!drawing_node.subtitle.empty())
        {
            ImGui::PushFont(subtitle_font_);
            ImGui::TextUnformatted(drawing_node.subtitle.data(), drawing_node.subtitle.data() + drawing_node.subtitle.size());
            ImGui::PopFont();
        }

        captionItemWidth = std::max(captionItemWidth, ImGui::GetItemRectSize().x);

        ImGui::PopFont();

        // ImEx::Debug_DrawItemRect();
        ImGui::Spacing();
    }

    ////layout_grid_ = crude_layout::Grid();
    auto itemMin = ImGui::GetItemRectSize().x;
    auto itemMax = ImGui::GetItemRectMax();
    layout_grid_.Begin((size_t)drawing_node.id, 2, captionItemWidth);
    layout_grid_.SetColumnAlignment(0.0f);
}

void NodeBuilderBlueprint::EndNode()
{
    layout_grid_.End();

    if (ShowErrorMsg)
    {
        auto errorMsgBackgroundRenderer = ImEx::ItemBackgroundRenderer([this, id = CurrentNodeId](ImDrawList* drawList) {
            ImU32 redColor = ImColor(1.0f, 0.0f, 0.0f, 1.0f);

            auto border = ax::NodeEditor::GetStyle().NodeBorderWidth;
            auto rounding = ax::NodeEditor::GetStyle().NodeRounding;

            auto itemMin = ImGui::GetItemRectMin();
            auto itemMax = ImGui::GetItemRectMax();

            auto nodeStart = ax::NodeEditor::GetNodePosition(id);
            auto nodeSize = ax::NodeEditor::GetNodeSize(id);

            itemMin.x = nodeStart.x;
            itemMin.x = itemMin.x + border - 0.5f;
            itemMin.y = itemMin.y + border - 0.5f;
            itemMax.x = nodeStart.x + nodeSize.x - border + 0.5f;
            itemMax.y = itemMax.y + ImGui::GetStyle().ItemSpacing.y + 0.5f;

            HeaderMin = itemMin;
            HeaderMax = itemMax;

            const auto uv = ImVec2((HeaderMax.x - HeaderMin.x) / (float)(4.0f * HeaderTextureWidth), (HeaderMax.y - HeaderMin.y) / (float)(4.0f * HeaderTextureHeight));

            if (HeaderTextureId == nullptr)
            {
                drawList->AddRectFilled(itemMin, itemMax, redColor, 0.0);
            }
            else
            {
                drawList->AddImageRounded(HeaderTextureId, itemMin, itemMax, ImVec2(0, 0), uv, redColor, 0.0);
            }

            drawList->AddText(NULL, 20.0f, (itemMin + itemMax) / 2 - ImVec2(20.0f, 9.5f), ImColor(1.0f, 1.0f, 1.0f, 1.0f), "ERROR");
        });
        ImGui::PushFont(header_font_);
        ImGui::TextUnformatted(" ");
        ImGui::PopFont();
        ImGui::Spacing();
        ImGui::Spacing();
        ImGui::Spacing();
    }
    ax::NodeEditor::EndNode();
}

////void NodeBuilderBlueprint::Header(const ImVec4& color) {
////  HeaderColor = ImColor(color);
////  SetStage(Stage::Header);
////}
////
////void NodeBuilderBlueprint::EndHeader() { SetStage(Stage::Content); }

void NodeBuilderBlueprint::BeginInputGroup() {}

bool NodeBuilderBlueprint::Input(const drawing::SlotDrawing& drawing_slot, bool linked, bool hasValue, const gbf::reflection::Value& val)
{
    // Add a bit of spacing to separate pins and make value not cramped
    ImGui::Spacing();

    // Input pin layout:
    //
    //     +-[1]---+-[2]------+-[3]-------------------+
    //     |       |          |                       |
    //    [X] Icon | Pin Name | Value/Editor(disable) |
    //     |       |          |                       |
    //     +-------+----------+-----------------------+

    ax::NodeEditor::BeginPin(drawing_slot.id, ax::NodeEditor::PinKind::Input);
    // [X] - Tell editor to put pivot point in the middle of
    //       the left side of the pin. This is the point
    //       where link will be hooked to.
    //
    //       By default pivot is in pin center point which
    //       does not look good for blueprint nodes.
    ax::NodeEditor::PinPivotAlignment(ImVec2(0.0f, 0.5f));

    auto icon_type = SlotDrawingTypeToIconType(drawing_slot.type);
    auto slot_color = SlotDrawingTypeToColor(drawing_slot.type);
    if (!drawing_slot.enable)
    {
        slot_color.w = 0.2f;
    }

    // [1] - Icon
    Icon(pin_icon_size_, icon_type, linked, slot_color);

    // [2] - Show pin name if it has one
    if (!drawing_slot.name.empty())
    {
        ImGui::SameLine();
        ImVec4 text_color = drawing_slot.enable ? ImVec4(1.0f, 1.0f, 1.0f, 1.0f) : ImVec4(1.0f, 1.0f, 1.0f, 0.2f);
        ImGui::TextColored(text_color, drawing_slot.name.c_str());
    }

    // [3] - Show value/editor when pin is not linked to anything
    if (!linked && hasValue)
    {
        ImGui::SameLine();
        if (gbf::reflection::value_can_convert_to<double>(val))
        {
            ImGui::Text("%g", gbf::reflection::value_cast<double>(val));
        }
        else if (gbf::reflection::value_can_convert_to<int64_t>(val))
        {
            ImGui::Text("%lld", gbf::reflection::value_cast<int64_t>(val));
        }
        else
        {
            ImGui::Text("Value Type Not Compatible!");
        }
        // value_changed = SlotEditControl::BuildSlotEditControl(drawing_slot.id, val, out_value);
    }

    ax::NodeEditor::EndPin();
    layout_grid_.NextRow();

    return true;
}

void NodeBuilderBlueprint::EndInputGroup() {}

void NodeBuilderBlueprint::BeginOutputGroup(float alignment)
{
    layout_grid_.SetColumnAlignment(alignment);
    layout_grid_.NextColumn();
}

void NodeBuilderBlueprint::Output(const drawing::SlotDrawing& drawing_slot, bool linked)
{
    // Add a bit of spacing to separate pins and make value not cramped
    ImGui::Spacing();

    // Output pin layout:
    //
    //    +-[1]------+-[2]---+
    //    |          |       |
    //    | Pin Name | Icon [X]
    //    |          |       |
    //    +----------+-------+

    ax::NodeEditor::BeginPin(drawing_slot.id, ax::NodeEditor::PinKind::Output);

    // [X] - Tell editor to put pivot point in the middle of
    //       the right side of the pin. This is the point
    //       where link will be hooked to.
    //
    //       By default pivot is in pin center point which
    //       does not look good for blueprint nodes.
    ax::NodeEditor::PinPivotAlignment(ImVec2(1.0f, 0.5f));

    auto icon_type = SlotDrawingTypeToIconType(drawing_slot.type);
    auto slot_color = SlotDrawingTypeToColor(drawing_slot.type);

    // [1] - Show pin name if it has one
    if (!drawing_slot.name.empty())
    {
        ImGui::TextUnformatted(drawing_slot.name.data(), drawing_slot.name.data() + drawing_slot.name.size());
        ImGui::SameLine();
    }

    // [2] - Show icon
    Icon(pin_icon_size_, icon_type, linked, slot_color);

    // End the output
    ax::NodeEditor::EndPin();
    layout_grid_.NextRow();
}

void NodeBuilderBlueprint::EndOutputGroup() {}

}   // namespace node_editor::utility
