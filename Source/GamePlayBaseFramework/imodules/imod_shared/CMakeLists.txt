cmake_minimum_required(VERSION 3.16)

include_directories(
    ${PROJECT_SOURCE_DIR}/base
    ${PROJECT_SOURCE_DIR}/imodules
    ${PROJECT_SOURCE_DIR}/meta
    ${PROJECT_SOURCE_DIR}/async
    ${PROJECT_SOURCE_DIR}/third_party
)

link_directories(
    ${CMAKE_BINARY_DIR}
)

add_definitions(-DGBF_IMOD_SHARED_EXPORTS)


set(all_project_src "")

file(GLOB top_src                   "*.*")
source_group(\\                             FILES ${top_src})
list(APPEND all_project_src                 ${top_src})

file(GLOB imodules_src                      "imodules/*.*")
source_group(\\imodules                    FILES ${imodules_src})
list(APPEND all_project_src                 ${imodules_src})

# file(GLOB modules_src                       "modules/*.*")
# source_group(\\modules                      FILES ${modules_src})
# list(APPEND all_project_src                 ${modules_src})

# file(GLOB awaitable_src                     "awaitable/*.*")
# source_group(\\awaitable                    FILES ${awaitable_src})
# list(APPEND all_project_src                 ${awaitable_src})

set(imod_shared_name "imod_shared")

add_library(${imod_shared_name} SHARED 
    ${all_project_src}
)

#target_link_libraries(${network_name} "${CMAKE_BINARY_DIR}/Debug/gbf_core.lib")
target_link_libraries(${imod_shared_name} 
    gbf_core 
    reflection 
    memory
    #rpc_service
)
add_dependencies(${imod_shared_name} 
    gbf_core 
    reflection 
    memory
    #rpc_service
)

#set(LIBRARY_OUTPUT_PATH ${CMAKE_BINARY_DIR})

#file(COPY ${network_name}.dll DESTINATION ${CMAKE_BINARY_DIR})
set_target_properties(${imod_shared_name} PROPERTIES UNITY_BUILD ON)
SET_PROPERTY(TARGET ${imod_shared_name} PROPERTY FOLDER "framework c++/imodules")
