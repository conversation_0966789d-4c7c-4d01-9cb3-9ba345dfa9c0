#pragma once

#include <functional>
#include <memory>
#include <string>
#include <vector>

namespace gbf {
  
//headers 
typedef struct s_http_header {
  s_http_header(const std::string& n, const std::string& v)
      : name(n),
        value(v){

        };

  std::string name;
  std::string value;
} http_header;

/// A reply to be sent to a client.
typedef struct s_http_reply {
  /// The status of the reply.
  enum class status_type {
    ok = 200,
    created = 201,
    accepted = 202,
    no_content = 204,
    multiple_choices = 300,
    moved_permanently = 301,
    moved_temporarily = 302,
    not_modified = 304,
    bad_request = 400,
    unauthorized = 401,
    forbidden = 403,
    not_found = 404,
    internal_server_error = 500,
    not_implemented = 501,
    bad_gateway = 502,
    service_unavailable = 503
  } status;

  int code() { return static_cast<int>(status); }

  /// The headers to be included in the reply.
  std::vector<http_header> headers;

  /// The content to be sent in the reply.
  std::string content;

  /// Get a stock reply.
  // static http_reply stock_reply(status_type status);
} http_reply;



enum class http_method : int {
  UNKNOWN = 0,
  HTTP_GET,
  HTTP_POST,
  HTTP_HEAD,
  HTTP_PUT,
  HTTP_DELETE,
  HTTP_OPTIONS,
  HTTP_TRACE,
  HTTP_CONNECT,
  HTTP_PATCH
};

// http请求
typedef struct s_http_request {
  int req_id;
  std::string method;
  std::string uri;
  int http_version_major = 1;
  int http_version_minor = 1;
  std::vector<http_header> headers;
  std::string body;
} http_request;

// 解析http method
// @param convert_upper_case 如果为true，则先尝试转化为大写
http_method parse_http_method(const char* method, bool convert_upper_case = false) {
  // TODO
  return http_method::HTTP_GET;
}

// 获取http method的名字
// 如果找不到，返回指向空字符串的指针
const char* get_http_method_name(http_method method) {
  static const char* http_method_names[] = {"",       "GET",     "POST",  "HEAD",    "PUT",
                                            "DELETE", "OPTIONS", "TRACE", "CONNECT", "PATCH"};


  int idx = static_cast<int>(method);
  if (idx < 0 || idx >= (sizeof(http_method_names) / sizeof(http_method_names[0]))) {
    return http_method_names[0];
  } else {
    return http_method_names[idx];
  }
}


//
using HttpRequestHandler = std::function<bool(std::shared_ptr<http_request>)>;
using HttpReplyCallback = std::function<bool(std::shared_ptr<http_reply>)>;
}  // namespace gbf
