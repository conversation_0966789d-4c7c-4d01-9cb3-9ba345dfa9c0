#pragma once

#include "core/config.hpp"
#include "core/modules/imodule.h"
#include <functional>
namespace gbf {
static const char kModuleExceptionSystemName[] = "ExceptionSystemModule";

class IExceptionSystemModule : public IModule {
 public:
  IExceptionSystemModule() = default;
  virtual ~IExceptionSystemModule() = default;

  virtual ModuleCallReturnStatus Init() override = 0;
  virtual ModuleCallReturnStatus Start() override = 0;
  virtual ModuleCallReturnStatus Update() override = 0;
  virtual ModuleCallReturnStatus Stop() override = 0;
  virtual ModuleCallReturnStatus Release() override = 0;
  virtual void SetExceptionDumpPath(const char* path) = 0;
  virtual void SetUseFullDump(bool value) = 0;
  virtual ModuleCallReturnStatus InitWithCallBack(std::function<void()> callback) = 0;
  virtual void Free() override = 0;

};

}
