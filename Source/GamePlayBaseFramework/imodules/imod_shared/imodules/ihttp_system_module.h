#pragma once

#include "core/config.hpp"
#include "imod_shared/imodules/network_fwd.hpp"
#include "imod_shared/imodules/http_fwd.hpp"
#include "core/modules/imodule.h"


namespace gbf {
static const char kModuleHttpLibEventName[] = "LibEventHttpSystemModule";

using HttpListenCallback = std::function<void(network::NetID, bool)>;
using HttpCloseCallback = std::function<void(network::NetID, bool)>;


class IHttpSystemModule : public IModule {
 public:
  IHttpSystemModule(){};

  virtual ~IHttpSystemModule(){};

  // method from IModule
  ModuleCallReturnStatus Init() override = 0;

  ModuleCallReturnStatus Start() override = 0;

  ModuleCallReturnStatus Update() override = 0;

  ModuleCallReturnStatus Stop() override = 0;

  ModuleCallReturnStatus Release() override = 0;

  void Free() override = 0;

 public:
  virtual void SetListenCallback(const HttpListenCallback& callback) = 0;
  virtual void SetCloseCallback(const HttpCloseCallback& callback) = 0;
  virtual bool Listen(network::Port port, network::NetID* netid_out = nullptr, const char* ip_bind = nullptr) = 0;
  virtual bool Close(network::NetID net_id) = 0;
  virtual bool RegisterRequestHandler(const std::string& pattern, const HttpRequestHandler& handler) = 0;
  // 发送(已收到的http请求的)应答
  virtual void SendReply(network::HttpReqID req_id, std::shared_ptr<http_reply> reply) = 0;
  // 发送http请求
  virtual void SendRequest(std::shared_ptr<http_request> req, int timeout_in_secs,
                           const HttpReplyCallback& callback) = 0;
  // 发送Http Get请求(简化的SendRequest)
  virtual void HttpGet(const std::string& uri, const std::string& content_type, int timeout_in_seconds,
                       const HttpReplyCallback& callback) = 0;
  // 发送Http Post请求(简化的SendRequest)
  virtual void HttpPost(const std::string& uri, const std::string& content_type, const std::string& body,
                        int timeout_in_seconds, const HttpReplyCallback& callback) = 0;
};
}  // namespace gbf
