#pragma once

#include <functional>
#include <memory>
#include "core/basetypes.hpp"

namespace gbf {
class ByteBuffer;
using ByteBufferPtr = std::shared_ptr<ByteBuffer>;

namespace network {
class NetSocket;
using NetSocketPtr = std::shared_ptr<NetSocket>;

using NetID = unsigned int;
using IP = unsigned int;
using Port = unsigned short;
using MsgLen = int;

using HttpReqID = unsigned int;

class INetworkCallback {
 public:
  virtual ~INetworkCallback() {}
  //Server only
  virtual void OnServerAccept(NetID listen_id, Port listen_port, NetID remote_id, IP remote_ip, Port remote_port) = 0;
  virtual void OnServerRecv(NetID listen_id, Port listen_port, NetID remote_id, const char* head_data, size_t head_len, const char* content_data, size_t content_len) = 0;
  virtual void OnServerRemoteDisconnect(NetID listen_id, Port listen_port, NetID remote_id) = 0;
  virtual void OnServerClose(NetID listen_id, Port listen_port) = 0;

  //Client only
  virtual void OnClientRecv(NetID local_id, const char* head_data, size_t head_len, const char* content_data, size_t content_len) = 0;
  virtual void OnClientConnect(bool result, int handle, NetID local_id, const char* connect_host, Port connect_port) = 0;
  virtual void OnClientDisconnect(NetID local_id) = 0;

  //Both
  virtual size_t QueryFixedHeadSize() const = 0;
  virtual size_t QueryContentSizeByHeadInfo(void* head_info, size_t head_size) const = 0;
};

struct NetworkConfig {
  NetworkConfig()
      : job_queue_length(1024 * 16),  // 16K
        max_package_size(1024 * 1024) {}
  int job_queue_length;
  int max_package_size;
};

enum class NetSocketType: int {
  Unknown = 0,
  TcpClient = 1,
  TcpServerAccept = 2,
  TcpServerListen = 3,
};

enum class NetCmdType : uint16_t {
  ProtoBuffer = 0,  // pb message with message id

  RpcRequest = 1,  // custom rpc for rstudio
  RpcResponse = 2,

  ////EventSubscribe = 3,		//subscribe one event in service
  ////EventUnsubscribe = 4,	//unsubscribe one event in service
  EventFire = 5,  // fire event

  Notice = 6,

  ////IRpcRequest = 7,  // irpc
  ////IRpcResponse = 8,

  RouterMessage = 10,  // router to client msg

  RouterRpcRequest = 11,  // router rpc request

  kEntityPropertyMasterFullSync = 12, // send from master server, full sync message of entity property
  kEntityPropertyMasterIncreaseSync = 13, // send from master server, increase sync message of entity property
  kEntityPropertySlaveModifyReq = 14,  // send from slave server, request to modify entity property

  AutoDispachMessage = 15,  // Auto dispatch proto buffer message
};

enum class NetCmdFlag : int {
  // Use zip for compress message body, not implement now~~
  ZipCompress = 0x1 << 0,

  // for compatiable with old mode
  RpcLegacyMode = 0x1 << 1,

  // encode field tag no compress
  RpcPackNoCompressMode = 0x1 << 2,

  // encode as protobuf
  RpcPackPbMode = 0x1 << 3,

  // with meta data in it
  RpcWithMetaData = 0x1 << 4,
  //////mark message as entity message
  ////EntityMessage = 0x1 << 5,
};

const static uint32_t gMagicNum = 0x20200330;

#pragma pack(push)
#pragma pack(1)

// For client head
struct NetCmdHead {
  uint32_t    bodyLen;   // length for body content(not include head self)
  uint32_t    magicNum;  // gMagicNum
  NetCmdType  cmdType;
  uint16_t    flag;  // flag for zip & other
  uint32_t    Id0;
  uint32_t    Id1;
};
static_assert(sizeof(NetCmdHead) == 20, "NetCmdHead must be 20 bytes!");

struct RpcRequestHead {
  // user channel id, now set by tconnd~~
  NetID channelId;

  uint64_t reqId;  // For response
  uint64_t serviceId;
  uint64_t funcId;

  uint8_t totalArgs;
};

enum class RpcResponseResultType : int32_t {
  RequestSuc = 0,
  MessageError = 1,
  ServiceNotFound = 2,
  FunctionNotFound = 3,
  ArgumentMismatch = 4,  // function call argument not match
  Timeout = 5,           // request timeout
  InvokeException = 6,
  CoroutineParamError = 7,
  CoroutineRunError = 8,
  CoderNotSupport = 9,
  EntityNotFound = 10,
  EntityGroupInvalid = 11,
  ////OverloadNotSupportForRpc = 9,
};

struct RpcResponseHead {
  // user channel id, now set by tconnd~~
  NetID channelId;

  uint64_t reqId;
  uint64_t serviceId;

  // ToDo: now use it for return value typeid, future need fix
  uint64_t funcId;

  RpcResponseResultType resultType;

  uint8_t totalArgs;
};

// Use for rpc context(not use for client)
struct RpcMetaContextHead {
  uint32_t totalMetaPair : 8;
  uint32_t totalMetaBytes : 24;
};
static_assert(sizeof(RpcMetaContextHead) == sizeof(uint32_t));

struct NoticeHead {
  uint64_t serviceId;
  uint64_t funcId;
  uint8_t totalArgs;
};

struct EventHead {
  uint64_t serviceId;
  uint64_t eventId;
};

enum class RouterType : uint8_t {
  Unknown = 0,
  ToClient = 1,        // Send message to client
  ToEntity = 2,        // Send message to RouterEntity
  ToRouterCenter = 4,  // Trans message
};

//#define MAX_ENTITY_GROUP_NAME_SIZE 24

struct RouterCntHead {
  RouterType routerType;
  bool isBroadcast;
  uint16_t totalUsers;

  char entityGroupName[24];

  uint32_t routerBodyLen;
};

struct AutoDispatchMessageHead {
  int32_t msg_id;
  int32_t sub_msg_id;
  uint8_t dispatch_type;
};

#pragma pack(pop)
}  // namespace network
}  // namespace gbf
