#include "runtime_resource/runtime_resource.hpp"
#include "imod_shared/shared_global.hpp"
#include "core/imodules/ilog_module.h"
#include "core/imodules/iprofiler_module.h"
#include "core/utils/meta_type_hash.hpp"
#include "math/mathtool.h"
#include "reflection/objects/make_user_object.hpp"
#include "visual/blueprint/extend/statemachine/game_state.h"

namespace gbf {
namespace logic {
URuntimeResource::URuntimeResource(std::string_view resName, std::string_view resGroup, RuntimeResourceManager* parentManager,
                                   RuntimeResourceKeepMode keepMode /* = KeepMode::Reference */)
    : resource_name_(resName), resource_group_(resGroup), manager_(parentManager), keep_mode_(keepMode) {
  hash_id_ = Hash64Fnv1a(resName.data(), resName.length());
  if (keep_mode_ == RuntimeResourceKeepMode::CachedTime) {
    round_cached_time_ = kRoundExtraCachedTimeMs;
  }

  InitStateMachine();
}

URuntimeResource::~URuntimeResource() {}

void URuntimeResource::AddReferenceArchive(const RuntimeResourcePtr& filePtr, bool is_required /* = true */) {
  assert(filePtr.operator bool());

  if (filePtr.get() == this) {
    WRN_DEF("Can not add self as depend, name:%s", get_resource_name().c_str());
    return;
  }

  auto iter = depend_item_map_.find(filePtr->get_resource_name());
  if (iter != depend_item_map_.end()) {
    WRN_DEF("Depend resource:%s already contained in %s", filePtr->get_resource_name().c_str(), get_resource_name().c_str());
    return;
  }
   
  depend_item_map_.emplace(std::make_pair(filePtr->get_resource_name(), DependItem(filePtr->get_resource_name(), filePtr, is_required)));
}

void URuntimeResource::RemoveDepend(const RuntimeResourcePtr& filePtr) {
  if (GBF_UNLIKELY(!filePtr)) {
    return;
  }

  depend_item_map_.erase(filePtr->get_resource_name());
}

bool URuntimeResource::Reload(bool background) {
  loading_mode_ = background ? LoadingMode::ByBackground : LoadingMode::BySync;
  if (create_mode_ != CreateMode::Manual) {
    Unload();
    return Load(background);
  }
  return false;
}

bool URuntimeResource::Load(bool background) {
  // assert(GThreadModule->IsCurrentMainThread() && "Res Load Must Oper in MainThread!");

  if (create_mode_ != CreateMode::Manual) {
    return LoadImpl(background, nullptr, nullptr);
  }
  return true;
}

void URuntimeResource::InitStateMachine() {
  load_state_machine_ = reflection::make_shared_with_rtti<GameStateMachine>();

  load_state_machine_->CreateSink(kMainStateSinkId);

  auto unloaded = load_state_machine_->CreateNormalState((StateId)LoadingState::Unloaded, kMainStateSinkId);
  auto sync_data_preparing = load_state_machine_->CreateNormalState((StateId)LoadingState::SyncDataPreparing, kMainStateSinkId);
  auto async_data_preparing = load_state_machine_->CreateNormalState((StateId)LoadingState::AsyncDataPreparing, kMainStateSinkId);
  auto data_prepared = load_state_machine_->CreateNormalState((StateId)LoadingState::DataPrepared, kMainStateSinkId);
  auto sync_presenting = load_state_machine_->CreateNormalState((StateId)LoadingState::SyncPresenting, kMainStateSinkId);
  auto async_presenting = load_state_machine_->CreateNormalState((StateId)LoadingState::AsyncPresenting, kMainStateSinkId);
  auto sync_depends_loading = load_state_machine_->CreateNormalState((StateId)LoadingState::SyncDependsLoading, kMainStateSinkId);
  auto async_depends_loading = load_state_machine_->CreateNormalState((StateId)LoadingState::AsyncDependsLoading, kMainStateSinkId);
  auto loaded = load_state_machine_->CreateNormalState((StateId)LoadingState::Loaded, kMainStateSinkId);
  auto clean_state = load_state_machine_->CreateNormalState((StateId)LoadingState::CleanState, kMainStateSinkId);
  auto err_state = load_state_machine_->CreateNormalState((StateId)LoadingState::InError, kMainStateSinkId);

  unloaded->AddOutStateId((StateId)LoadingState::SyncDataPreparing);
  unloaded->AddOutStateId((StateId)LoadingState::AsyncDataPreparing);

  sync_data_preparing->AddOutStateId((StateId)LoadingState::DataPrepared);

  async_data_preparing->AddOutStateId((StateId)LoadingState::DataPrepared);

  data_prepared->AddOutStateId((StateId)LoadingState::SyncDependsLoading);
  data_prepared->AddOutStateId((StateId)LoadingState::AsyncDependsLoading);

  sync_depends_loading->AddOutStateId((StateId)LoadingState::SyncPresenting);

  async_depends_loading->AddOutStateId((StateId)LoadingState::SyncPresenting);
  async_depends_loading->AddOutStateId((StateId)LoadingState::AsyncPresenting);

  sync_presenting->AddOutStateId((StateId)LoadingState::Loaded);

  clean_state->AddOutStateId((StateId)LoadingState::InError);
  clean_state->AddOutStateId((StateId)LoadingState::Unloaded);

  // State machine only changed by resource inner call, so we can just capture this for simple
  sync_data_preparing->SetCallbackAsMultiFuctions([this]() { OnEnterStateSyncDataPreparing(); }, [this]() {}, [this]() {});
  async_data_preparing->SetCallbackAsMultiFuctions([this]() { OnEnterStateAsyncDataPreparing(); }, [this]() {},
                                                   [this]() { OnQuitStateAsyncDataPreparing(); });
  data_prepared->SetCallbackAsMultiFuctions([this]() { OnEnterStateDataPrepared(); }, [this]() {}, [this]() {});
  
  sync_depends_loading->SetCallbackAsMultiFuctions([this]() { OnEnterStateSyncDependsLoading(); }, [this]() {}, [this]() {});
  async_depends_loading->SetCallbackAsMultiFuctions([this]() { OnEnterStateAsyncDependsLoading(); }, [this]() {},
                                                    [this]() { OnQuitStateAsyncDependsLoading(); });
  
  sync_presenting->SetCallbackAsMultiFuctions([this]() { OnEnterStateSyncPresenting(); }, [this]() {}, [this]() {});
  async_presenting->SetCallbackAsMultiFuctions([this]() { OnEnterStateAsyncPresenting(); }, [this]() {}, [this]() { OnQuitStateAsyncPresenting(); });
  
  loaded->SetCallbackAsMultiFuctions([this]() { OnEnterStateLoaded(); }, [this]() {}, [this]() { OnQuitStateLoaded(); });
  clean_state->SetCallbackAsMultiFuctions([this]() { OnEnterStateClean(); }, [this]() {}, [this]() {});
  err_state->SetCallbackAsMultiFuctions([this]() { OnEnterStateError(); }, [this]() {}, [this]() {});

  load_state_machine_->ChangeState((StateId)LoadingState::Unloaded);
}

void URuntimeResource::OnEnterStateSyncDataPreparing() {
  std::string_view res_path = GetResourcePathInternal();
  loaded_data_stream_ = GPackageSystem->LoadResource(res_path, resource_group_, true, enable_output_log_);
  if (!loaded_data_stream_) {
    ForceChangeStateToError("sync load data from file failed");
    return;
  }

  loaded_data_stream_->ReadPosition(0);

  // To prepared state now
  load_state_machine_->ChangeState((StateId)LoadingState::DataPrepared);
}

void URuntimeResource::_NotifyStateResult4AsyncPreparing(const ByteBufferPtr& load_ret) {
  if (get_loading_state() != LoadingState::AsyncDataPreparing) {
    //error states, just ignore
    return;
  }

  if (load_ret) {
    TRA_DEF("Background load RuntimeResource[%s] sucess.", resource_name_.c_str());
    // Async file prepare success.
    loaded_data_stream_ = load_ret;
    load_state_machine_->ChangeState((StateId)LoadingState::DataPrepared);
  } else {
    // Async file prepare failed.
    ForceChangeStateToError("async load data from file failed");
  }
}

void URuntimeResource::OnEnterStateAsyncDataPreparing() {
  if (prepare_ticket_) {
    prepare_ticket_->discard();
    prepare_ticket_.reset();
  }

  TRA_DEF("Try to load RuntimeResource[%s] in background----->", resource_name_.c_str());

  prepare_ticket_ = GJobSystem->RequestTicket();
  RuntimeResourceWeakPtr main_weak = std::static_pointer_cast<URuntimeResource>(shared_from_this());

  std::string_view res_path = GetResourcePathInternal();
  prepare_ticket_ = GPackageSystem->LoadResourceAsync(
      res_path, resource_group_,
      [main_weak](const jobs::job_ticket_ptr& ticket, std::string_view res_name, std::string_view group_name, const ByteBufferPtr& loaded_data) {
        jobs::job_ticket::ticket_keeper keeper(*ticket);
        if (!keeper.IsKeeped()) return;

        auto main_res = main_weak.lock();
        if (!main_res) return;

        main_res->_NotifyStateResult4AsyncPreparing(loaded_data);
  });
}

void URuntimeResource::OnQuitStateAsyncDataPreparing() {
  if (prepare_ticket_) {
    prepare_ticket_->discard();
    prepare_ticket_.reset();
  }
}

void URuntimeResource::OnEnterStateDataPrepared() {
  if (!DoHeadInfoParseImpl()) {
    ForceChangeStateToError("head information parse after prepare failed");
    return;
  }

  // Call depends resource prepare here, we will load all depends in next state
  DoPrepareDependsImpl();

  if (loading_mode_ == LoadingMode::BySync) {
    load_state_machine_->ChangeState((StateId)LoadingState::SyncDependsLoading);
  } else {
    load_state_machine_->ChangeState((StateId)LoadingState::AsyncDependsLoading);
  }
}

void URuntimeResource::OnEnterStateSyncDependsLoading() {
  bool result = true;
  for (auto& [_, needItr] : depend_item_map_) {
    needItr.resource->LoadImpl(
        false,
        [this, &result, is_required = needItr.is_required](URuntimeResource* filePtr, bool bRet) {
          result &= bRet;
          if (bRet == false && enable_output_log_) {
            ERR_DEF("Load RefArchive error! Package name %s, depend file:%s, is_required[%d] can't be loaded correctly.", resource_name_.c_str(),
                    filePtr->get_resource_name().c_str(), (int)is_required);
          }
        },
        [](URuntimeResource* filePtr, int progress, int total) {

        });
  }

  if (!result) {
    ForceChangeStateToError("sync dependency load failed");
    return;
  }

  // Now just use sync mode presenting here
  load_state_machine_->ChangeState((StateId)LoadingState::SyncPresenting);
}


void URuntimeResource::_NotifyStateResult4AsyncDependsLoading() {
  // this function executed in main thread;
  if (get_loading_state() != URuntimeResource::LoadingState::AsyncDependsLoading) {
    //error states, just ignore
    return;
  }

  size_t total_depend = depend_item_map_.size();
  if (loaded_optional_depends_number_ + loaded_need_depends_number_ < (int)total_depend) {
    //Not finish yet, just return for next notify.
    return;
  }

  //Finish handle code here
  if (has_async_present_support_) {
    load_state_machine_->ChangeState((StateId)LoadingState::AsyncPresenting);
  } else {
    load_state_machine_->ChangeState((StateId)LoadingState::SyncPresenting);
  }
}

void URuntimeResource::OnEnterStateAsyncDependsLoading() {
  loaded_optional_depends_number_ = 0;
  loaded_need_depends_number_ = 0;

  if (depend_item_map_.empty()) {
    // No depends need, just call finish to present resource
    _NotifyStateResult4AsyncDependsLoading();
    return;
  }

  FireLoadProgress(false);
  RuntimeResourceWeakPtr main_resource_weak = std::static_pointer_cast<URuntimeResource>(shared_from_this());

  for (auto& [_, depend_item] : depend_item_map_) {
    auto loadTicket = GJobSystem->RequestTicket();
    RuntimeResourceWeakPtr depend_weak = std::static_pointer_cast<URuntimeResource>(depend_item.resource->shared_from_this());
    depend_item.request = loadTicket;
    depend_item.resource->LoadImpl(
        true,
        [main_resource_weak, is_required = depend_item.is_required, depend_weak, loadTicket](URuntimeResource* archive, bool ret) {
          jobs::job_ticket::ticket_keeper keeper(*loadTicket);
          if (!keeper.IsKeeped()) return;

          auto main_resource = main_resource_weak.lock();
          auto depend_resource = depend_weak.lock();
          if (!main_resource || !depend_resource) return;

          main_resource->_NotifyDependResourceLoaded(depend_resource->get_resource_name());

          if (!ret) {
            ERR_DEF("Load RefArchive error! Package name %s, optional reference file:%s can't be loaded correctly.",
                    main_resource->get_resource_name().c_str(), archive->GetResourcePathInternal().c_str());

            // Only is required resource, need goto error status here
            if (is_required) {
              main_resource->ForceChangeStateToError("required depend load failed!");
              return;
            }
          }

          // Copy all child resources to main resource
          auto& childed_cached_resources = depend_resource->get_cached_resource_array();
          std::copy(childed_cached_resources.begin(), childed_cached_resources.end(), std::back_inserter(main_resource->cached_resource_array_));

          main_resource->_NotifyStateResult4AsyncDependsLoading();
        },
        [main_resource_weak, depend_weak, loadTicket](URuntimeResource* filePtr, int progress, int total) {
          jobs::job_ticket::ticket_keeper keeper(*loadTicket);
          if (!keeper.IsKeeped()) return;

          auto main_resource = main_resource_weak.lock();
          auto depend_resource = depend_weak.lock();
          if (!main_resource || !depend_resource) return;

          main_resource->NotifyDependResourceLoadProgressChanged(depend_resource->get_resource_name(), progress, total);
        });
  }
}

void URuntimeResource::OnQuitStateAsyncDependsLoading() {
  for (auto& [_, depend] : depend_item_map_) {
    if (depend.request) {
      depend.request->discard();
      depend.request.reset();
    }
  }
}

void URuntimeResource::OnEnterStateSyncPresenting() {
  if (!DoPresentImpl()) {
    ForceChangeStateToError("deserialize failed");
    return;
  }

  load_state_machine_->ChangeState((StateId)LoadingState::Loaded);
}

void URuntimeResource::_NotifyStateResult4AsyncPresenting(bool do_present_suc) {
  if (get_loading_state() != LoadingState::AsyncPresenting) {
    // error states, just ignore.
    return;
  }
  
  if (do_present_suc) {
    load_state_machine_->ChangeState((StateId)LoadingState::Loaded);
  } else {
    ForceChangeStateToError("async presenting failed.");
  }
}

void URuntimeResource::OnEnterStateAsyncPresenting() {
  if (async_finish_ticket_ != nullptr) {
    // if unload by Sync load operation after, we need return back;
    if (async_finish_ticket_->is_expired()) return;
    async_finish_ticket_->discard();
    async_finish_ticket_.reset();
  }

  auto ticket = GJobSystem->RequestTicket();
  async_finish_ticket_ = ticket;
  RuntimeResourceWeakPtr main_weak = std::static_pointer_cast<URuntimeResource>(shared_from_this());

  GJobSystem->Post(
      [main_weak, ticket]() {
        GBF_PROFILER_AUTO_SCOPE_DYNAMIC("RefArchive::IsAllNeededArchivesLoaded::Lambda_SlowJob");
        jobs::job_ticket::ticket_keeper keeper(*ticket);
        if (!keeper.IsKeeped()) return;

        auto main_resource = main_weak.lock();
        if (!main_resource) return;

        //Not expected status, just return here.
        if (main_resource->get_loading_state() != LoadingState::AsyncPresenting)  
          return;

        // Now do my serializer operation
        bool result = main_resource->DoPresentImpl();
        // post result to the logic thread.
        GJobSystem->Post(
            [main_weak, ticket, result]() {
              GBF_PROFILER_AUTO_SCOPE_DYNAMIC("RefArchieve::IsAllNeededArchivesLoaded::Lambda_LogicJob");
              jobs::job_ticket::ticket_keeper keeper(*ticket);
              if (!keeper.IsKeeped()) return;

              auto main_resource = main_weak.lock();
              if (!main_resource) return;

              main_resource->_NotifyStateResult4AsyncPresenting(result);
            },
            JobType::kLogicJob);
      },
      JobType::kWorkJob);

}

void URuntimeResource::OnQuitStateAsyncPresenting() {
  if (async_finish_ticket_) {
    async_finish_ticket_->discard();
    async_finish_ticket_.reset();
  }
}

void URuntimeResource::OnEnterStateLoaded() {
  INF_DEF("RuntimeResource load file success. [%s, %s]", resource_group_.c_str(), resource_name_.c_str());
  FireLoadProgress(true);
  FireLoadFinish();
  UnloadUnusedImpl();
  load_count_++;
}

void URuntimeResource::OnQuitStateLoaded() { UnloadImpl(); }

void URuntimeResource::OnEnterStateClean() { UnloadUnusedImpl(); }

void URuntimeResource::OnEnterStateError() {
  FireLoadProgress(true);
  FireLoadFinish();
}

void URuntimeResource::ForceChangeStateToError(std::string_view err_info) {
  ERR_DEF("RuntimeResource load with error:%s, now in error state! [%s, %s]", err_info.data(), resource_group_.c_str(), resource_name_.c_str());
  load_state_machine_->ChangeStateForce((StateId)LoadingState::CleanState);
  load_state_machine_->ChangeState((StateId)LoadingState::InError);
}

void URuntimeResource::ForceChangeStateToUnload() {
  load_state_machine_->ChangeStateForce((StateId)LoadingState::CleanState);
  load_state_machine_->ChangeState((StateId)LoadingState::Unloaded);
}

void URuntimeResource::Unload() {
  if (get_loading_state() == LoadingState::Unloaded) return;

  load_state_machine_->ChangeStateForce((StateId)LoadingState::CleanState);
  load_state_machine_->ChangeState((StateId)LoadingState::Unloaded);

  INF_DEF("Runtime resource unloaded[%s, %s]", resource_group_.c_str(), resource_name_.c_str());
}

void URuntimeResource::LoadFromDataAsync(const ByteBufferPtr& stream, const OnLoadCallbackFunc& finishFunc,
                                                 const OnLoadProgressFunction& progressFunc, jobs::job_ticket_ptr ticket) {
  ForceChangeStateToUnload();

  loading_mode_ = LoadingMode::ByBackground;
  if (stream) {

    // Push callBack to the NotifyItemList.
    if (finishFunc) {
      NotifyItem notifyItem;
      notifyItem.finish_function = finishFunc;
      notifyItem.progress_function = progressFunc;
      notifyItem.ticket = ticket;
      AddNofityItem(std::move(notifyItem));
    }

    //Jump to DataPrepared status.
    loaded_data_stream_ = stream;
    load_state_machine_->ChangeStateForce((StateId)LoadingState::DataPrepared);
  } else {
    ForceChangeStateToError("URuntimeResource::LoadFromDataAsync() has a null stream");
  }
}

bool URuntimeResource::LoadFromDataSync(const ByteBufferPtr& stream) {
  ForceChangeStateToUnload();

  loading_mode_ = LoadingMode::BySync;
  if (stream) {
    // Jump to DataPrepared status.
    loaded_data_stream_ = stream;
    load_state_machine_->ChangeStateForce((StateId)LoadingState::DataPrepared);
  }
  else {
    ForceChangeStateToError("URuntimeResource::LoadFromDataSync() has a null stream");
  }
  
  return is_loaded();
}

bool URuntimeResource::CheckInAirLoad(bool background, const OnLoadCallbackFunc& callBack) {
  if (get_loading_state() == LoadingState::InError) {
    if (callBack) {
      callBack(this, false);
    }
    return true;
  }

  if (get_loading_state() == LoadingState::Loaded) {
    if (callBack) {
      callBack(this, true);
    }
    return true;
  }

  return false;
}

bool URuntimeResource::LoadImpl(bool background, const OnLoadCallbackFunc& finishFunc, const OnLoadProgressFunction& progressFunc,
                                jobs::job_ticket_ptr ticket) {
  //If in air, just return
  if (CheckInAirLoad(background, finishFunc)) {
    return is_loaded();
  }

  //Just goto unload state for easy handle here
  ForceChangeStateToUnload();

  loading_mode_ = background ? LoadingMode::ByBackground : LoadingMode::BySync;
  if (finishFunc) {
    NotifyItem notifyItem;
    notifyItem.finish_function = finishFunc;
    notifyItem.progress_function = progressFunc;
    notifyItem.ticket = ticket;
    AddNofityItem(std::move(notifyItem));
  }

  assert(get_loading_state() == LoadingState::Unloaded);
  load_state_machine_->ChangeState(background? (StateId)LoadingState::AsyncDataPreparing : (StateId)LoadingState::SyncDataPreparing);

  if (background) {
    return true;
  }
  else {
    //Sync mode just return load status.
    return is_loaded();
  }
}

bool URuntimeResource::is_in_air() const {
  return load_state_machine_->IsInState((StateId)LoadingState::InError) || load_state_machine_->IsInState((StateId)LoadingState::Loaded);
}

gbf::logic::URuntimeResource::LoadingState URuntimeResource::get_loading_state() const {
  return (LoadingState)(load_state_machine_->GetSinkRunState(kMainStateSinkId));
}

bool URuntimeResource::is_loaded() const { return load_state_machine_->IsInState((StateId)LoadingState::Loaded); }

size_t URuntimeResource::get_total_depend_number() {
  size_t total = 0;
  for (auto& [_, depend] : depend_item_map_) {
    total += depend.resource->get_total_depend_number();
    total++;
  }
  return total;
}

size_t URuntimeResource::get_loaded_depend_number() {
  size_t total = 0;
  for (auto& [_, depend] : depend_item_map_) {
    if (depend.resource) {
      total += depend.resource->get_loaded_depend_number();
      if (depend.resource->is_loaded()) total++;
    }
  }
  return total;
}

StringVector URuntimeResource::GetAllRequiredDependNames() {
  StringVector vec;
  for (auto& [depend_name, depend] : depend_item_map_) {
    if (depend.is_required) {
      vec.emplace_back(depend_name);
    }
  }
  return vec;
}

StringVector URuntimeResource::GetAllOptionalDependNames() {
  StringVector vec;
  for (auto& [depend_name, depend] : depend_item_map_) {
    if (!depend.is_required) {
      vec.emplace_back(depend_name);
    }
  }
  return vec;
}


void URuntimeResource::UnloadUnusedImpl() {
  loaded_data_stream_.reset();

  // Clear All Cached ResourceRTObject and reference archives. Must remove the SmartPtr object.
  cached_resource_array_.clear();
  depend_item_map_.clear();
}

void URuntimeResource::_NotifyDependResourceLoaded(const std::string& res_name) {
  auto iter = depend_item_map_.find(res_name);
  if (iter != depend_item_map_.end()) {
    iter->second.progress = iter->second.total;
    if (iter->second.is_required) {
      loaded_need_depends_number_++;
    } else {
      loaded_optional_depends_number_++;
    }
  }

  FireLoadProgress(false);
}

void URuntimeResource::NotifyDependResourceLoadProgressChanged(const std::string& res_name, int progress, int total) {
  auto iter = depend_item_map_.find(res_name);
  if (iter != depend_item_map_.end()) {
    iter->second.progress = progress;
    iter->second.total = total;
  }

  FireLoadProgress(false);
}

void URuntimeResource::AddNofityItem(NotifyItem&& notifyItem) { notify_item_list_.push_back(std::move(notifyItem)); }

void URuntimeResource::FireLoadFinish() {
  for (auto& notifyItem : notify_item_list_) {
    if (!notifyItem.ticket) {
      continue;
    }

    jobs::job_ticket::ticket_keeper keeper(*(notifyItem.ticket));
    if (!keeper.IsKeeped()) {
      continue;
    }

    if (notifyItem.finish_function) {
      notifyItem.finish_function(this, get_loading_state() == LoadingState::Loaded);
    }
  }
  notify_item_list_.clear();

  // @ For RunShader Special usage , rePrepare Material Render operation!
  load_count_++;
}

void URuntimeResource::CalProgressTotal(int& progress, int& total) {
  progress = 0;
  total = 0;

  for (auto& [_, archiveItr] : depend_item_map_) {
    progress += archiveItr.progress;
    total += archiveItr.total;
  }
}



void URuntimeResource::FireLoadProgress(bool finish) {
  int progress = 0;
  int total = 0;

  CalProgressTotal(progress, total);

  total++;
  if (finish) progress = total;

  for (auto& notifyItem : notify_item_list_)
    if (notifyItem.progress_function) notifyItem.progress_function(this, progress, total);
}

void URuntimeResource::AddListener(RuntimeResourceListener* lis) {
  threads::NormalMutex::LockGuard lock(listener_list_mutex_);
  listener_list_.insert(lis);
}

void URuntimeResource::RemoveListener(RuntimeResourceListener* lis) {
  // O(n) but not called very often
  threads::NormalMutex::LockGuard lock(listener_list_mutex_);
  listener_list_.erase(lis);
}

}  // namespace logic
}  // namespace gbf
