#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/virtual_machine/utils/periodtask.hpp"
#include "core/thread/thread_timer.h"

namespace gbf
{
	namespace logic
	{
		class BLUEPRINT_API CEMeta(Reflect) GBFPlayer
		{
		public:
		CEMeta(Reflect) 
			GBFPlayer();
			~GBFPlayer();

			machine::PeriodTaskManager* GetPeriodTaskManager()
			{
				return &mPeriodTaskManager;
			}

			void Update();

			inline std::uint64_t GetNowTimeMS()
			{
                return std::uint64_t(mTimer.GetMilliseconds());
			}

			inline std::uint64_t GetNowTimeUS()
			{
                return std::uint64_t(mTimer.GetMicroseconds());
			}

			inline double GetNowSeconds()
			{
				double tmpval = (double)mTimer.GetMicroseconds();
				return tmpval / 1000000;
			}

		private:
			machine::PeriodTaskManager	mPeriodTaskManager;
			threads::ThreadTimer		mTimer;
            std::uint64_t mNowTimeMs = 0;
		};
	}
}
