#include "visual/blueprint/details/blueprint_context.h"

#include "visual/blueprint/details/blueprint_graph.h"
#include "reflection/objects/make_user_object.hpp"

namespace gbf { namespace logic {
    static int s_usedId = 0;

    UBlueprintContext::UBlueprintContext(UBlueprintGraphPtr main_seq, machine::NamedMemoryScopePtr global_mem_scope)
        : m_main_graph(main_seq)
        , m_global_memory_scope(global_mem_scope)
    {
        m_Id = s_usedId++;
        ////InitRunEnvironment(m_global_memory_scope);
    }

    UBlueprintContext::~UBlueprintContext()
    {
        m_scheduler->Release();
        m_scheduler.reset();
    }

    void UBlueprintContext::OnGlobalEvent(std::string_view event_name, machine::VValue _param, size_t out_slot_index)
    {
        BlueprintEventInstance eventIns;
        eventIns.event_name = event_name;
        eventIns.out_slot_index = out_slot_index;
        eventIns.param_value = _param;

        m_event_ins_list.emplace_back(std::move(eventIns));
    }

    void UBlueprintContext::OnImmediateEvent(std::string_view event_name, BlueprintEventParamList param_list, size_t out_slot_index)
    {
        m_main_graph->OnEvent(m_main_coroutine.get(), event_name, param_list, out_slot_index);
        m_scheduler->Update();
    }

    void UBlueprintContext::OnGlobalEvent(std::string_view event_name, BlueprintEventParamList param_list, size_t out_slot_index)
    {
        BlueprintEventInstance eventIns;
        eventIns.event_name = event_name;
        eventIns.out_slot_index = out_slot_index;
        eventIns.param_list = param_list;

        m_event_ins_list.emplace_back(std::move(eventIns));
    }

    machine::VMRunStepStatus UBlueprintContext::Update()
    {
        for (const auto& eventIns : m_event_ins_list)
        {
            if (!eventIns.param_list.empty())
            {
                m_main_graph->OnEvent(m_main_coroutine.get(), eventIns.event_name, eventIns.param_list, eventIns.out_slot_index);
            }
            else
            {
                m_main_graph->OnEvent(m_main_coroutine.get(), eventIns.event_name, eventIns.param_value, eventIns.out_slot_index);
            }
        }

        m_event_ins_list.clear();

        return m_scheduler->Update();
    }

    void UBlueprintContext::InitRunEnvironment(machine::NamedMemoryScopePtr global_mem_scope)
    {
        m_scheduler = reflection::make_shared_with_rtti<machine::VScheduler>();
        m_main_coroutine = m_scheduler->NewRootCoroutine(reflection::__box_rtti_object(this), global_mem_scope);
    }
}}   // namespace gbf::logic