#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "visual/blueprint/details/blueprint_graph_base.h"

namespace gbf { namespace logic {

    class UBlueprintGraphGroup;
    class UBlueprintWorkspace;

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintGraph : public UBpGraphBase
    {
    public:
        CEMeta(Reflect)
        UBlueprintGraph(const std::string& _name, const std::string& _desc, UBlueprintWorkspace* parent_sheduler, UBlueprintGraphGroup* parent_group);
        ~UBlueprintGraph();

        bool ContainsEventNodeWithName(const std::string& event_name) const;
    };

}}   // namespace gbf::logic
