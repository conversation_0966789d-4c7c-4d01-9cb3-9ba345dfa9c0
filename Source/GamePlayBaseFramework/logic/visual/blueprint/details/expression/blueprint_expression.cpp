#include "visual/blueprint/details/expression/blueprint_expression.h"
#include "visual/virtual_machine/runtime/expr/evaluate_unit.hpp"
#include "visual/virtual_machine/runtime/memory_scope.hpp"
#include "reflection/objects/make_user_object.hpp"
#include "visual/virtual_machine/utils/vvalue_util.hpp"

namespace gbf { namespace logic {

    UBlueprintExpression::UBlueprintExpression(machine::VValueKind ret_type, machine::VMOperatorType expr_type, ExprFunciton&& expr, const BlueprintExprParams& param)
        : m_return_type(ret_type)
        , m_expr_type(expr_type)
        , m_expression(std::move(expr))
        , m_captured(param)
    {
        (void)m_expr_type;
    }

    UBlueprintExpressionPtr UBlueprintExpression::Create(machine::VMOperatorType expr_type, const BlueprintExprParams& params)
    {
        bool is_binary = machine::EvaluateUnit::IsBinaryOperator(expr_type);
        if (is_binary)
        {
            // Binary expression
            ExprFunciton func = [expr_type](const BlueprintExprParams& params) -> machine::VValue {
                assert(params.size() >= 2);
                auto left = CalcExprssionValue(params[0]);
                auto right = CalcExprssionValue(params[1]);
                return machine::EvaluateUnit::DoOperation(expr_type, left, right);
            };
            // ToDo: Need improve here?
            auto leftkind = CalcExpressionValueKind(params[0]);
            auto rightkind = CalcExpressionValueKind(params[1]);
            machine::VValueKind ret_type = machine::EvaluateUnit::GetEvaluateResultType(expr_type, machine::EvaluateUnit::GetEvaluateGroup(expr_type), leftkind, rightkind);
            return reflection::make_shared_with_rtti<UBlueprintExpression>(ret_type, expr_type, std::move(func), params);
        }
        else
        {
            // Unary expression
            ExprFunciton func = [expr_type](const BlueprintExprParams& params) -> machine::VValue {
                assert(params.size() >= 1);
                auto left = CalcExprssionValue(params[0]);
                return machine::EvaluateUnit::DoOperation(expr_type, left, machine::VValue::nothing);
            };
            // ToDo: Need improve here?
            auto leftkind = CalcExpressionValueKind(params[0]);
            auto rightkind = machine::VValueKind::kNone;
            machine::VValueKind ret_type = machine::EvaluateUnit::GetEvaluateResultType(expr_type, machine::EvaluateUnit::GetEvaluateGroup(expr_type), leftkind, rightkind);
            return reflection::make_shared_with_rtti<UBlueprintExpression>(ret_type, expr_type, std::move(func), params);
        }
    }

    UBlueprintExpressionPtr UBlueprintExpression::Create(std::string_view method_name, const BlueprintExprParams& params)
    {
        throw std::runtime_error("Implement function support in UBpExpression here!");
    }

    void UBlueprintExpression::SaveExpressionToJson(const UBlueprintExpression& expression, machine::IVMStreamWriter& writer)
    {
        writer.StartObject("");

        writer.AddIntProperty("expr_type", (int)expression.m_expr_type);

        writer.StartArray("param_list");

        auto itr = (expression.m_captured).begin();
        while (itr != expression.m_captured.end())
        {
            auto* expr = ToExpressionValue(*itr);
            if (expr != nullptr)
            {
                UBlueprintExpression::SaveExpressionToJson(*expr, writer);
            }
            else
            {
                machine::VValue val = machine::VValue::nothing;
                if (*itr)
                {
                    val = *((*itr).get());
                }
                machine::VValueUtil::SaveVValueToJsonMap(val, writer);
            }

            ++itr;
        }

        writer.EndArray();

        writer.EndObject();
    }

    UBlueprintExpressionPtr UBlueprintExpression::CreateExpressionFromJson(machine::IVMStreamReadNode& json_value)
    {
        machine::VMOperatorType expr_type = (machine::VMOperatorType)json_value.GetIntProperty("expr_type");
        BlueprintExprParams param_list;

        auto* paramListValue = json_value.GetNode("param_list");
        for (size_t index = 0; index < paramListValue->GetArraySize(); ++index)
        {
            auto* itemValue = paramListValue->GetArrayElement(index);
            if (itemValue->HasProperty("expr_type"))
            {
                UBlueprintExpressionPtr newItem = UBlueprintExpression::CreateExpressionFromJson(*itemValue);
                auto obj = reflection::__box(newItem);
                auto valptr = std::make_shared<machine::VValue>(reflection::make_value(obj));
                param_list.emplace_back(valptr);
            }
            else if (itemValue->HasProperty("vm_value_type"))
            {
                auto val = machine::VValueUtil::CreateVValueFromJsonMap(*itemValue);
                auto valptr = std::make_shared<machine::VValue>(val);
                param_list.emplace_back(valptr);
            }
        }

        return UBlueprintExpression::Create(expr_type, param_list);
    }

    machine::VValue UBlueprintExpression::CalcExprssionValue(const machine::VValuePtr& valptr)
    {
        if (!valptr)
            return machine::VValueKind::kNone;

        auto* expr = ToExpressionValue(valptr);
        // expression as value here
        if (expr != nullptr)
        {
            return (*expr)();   // Just call it~~
        }

        // normal value here
        return *(valptr.get());
    }

    gbf::machine::VValueKind UBlueprintExpression::CalcExpressionValueKind(const machine::VValuePtr& valptr)
    {
        if (!valptr)
            return machine::VValueKind::kNone;

        auto* expr = ToExpressionValue(valptr);
        // expression as value here
        if (expr != nullptr)
        {
            return expr->get_return_type();   // Just call it~~
        }

        // normal value here
        return valptr->kind();
    }

    UBlueprintExpression* UBlueprintExpression::ToExpressionValue(const machine::VValuePtr& valptr)
    {
        if (!valptr)
            return nullptr;

        if (valptr->kind() == machine::VValueKind::kUser && valptr->Ref<reflection::UserObject>().IsExplicitTypeOf<UBlueprintExpression>())
        {
            return &(valptr->Ref<UBlueprintExpression>());
        }
        return nullptr;
    }

}}   // namespace gbf::logic
