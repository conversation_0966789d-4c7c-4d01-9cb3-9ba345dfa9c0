#include "visual/blueprint/details/node/action/blueprint_meta_method_node.h"

#include "core/utils/string_util.h"
#include "reflection/runtime/cxx_runtime.hpp"
#include "visual/blueprint/details/node/blueprint_data_slot.h"

namespace gbf { namespace logic {
    UBlueprintMetaMethodNode::UBlueprintMetaMethodNode()
        : UBlueprintActionNode(BlueprintSlotAvailableFlag::All, "")
        , mMethodInfo(nullptr)
    {
        this->m_title = StringUtil::Format("%s", "UBpMetaMethodNode");
    }

    bool UBlueprintMetaMethodNode::init_method(std::string_view type_name, std::string_view method_name)
    {
        auto* agentMeta = reflection::query_meta_class_by_name(type_name.data());
        if (!agentMeta)
            return false;

        auto suc = agentMeta->TryGetFunction(method_name, mMethodInfo);
        if (!suc)
            return false;

        mType = type_name;
        mMethod = method_name;
        this->m_title = StringUtil::Format("%s::%s", type_name.data(), method_name.data());
        InitializeSlotsImpl();
        return true;
    }

    void UBlueprintMetaMethodNode::set_method(std::string_view method_name)
    {
        if (method_name == mMethod)
            return;

        // Change to another method.
        auto* agentMeta = reflection::query_meta_class_by_name(mType);
        if (!agentMeta)
            return;
        auto suc = agentMeta->TryGetFunction(method_name, mMethodInfo);
        if (!suc)
            return;
        mMethod = method_name;
        m_in_instruction_slots.clear();
        m_out_instruction_slots.clear();
        m_in_variable_slots.clear();
        m_out_variable_slots.clear();
        this->m_title = StringUtil::Format("%s::%s", mType.c_str(), mMethod.c_str());
        InitializeSlotsImpl();
    }

    void UBlueprintMetaMethodNode::InitializeSlotsImpl()
    {
        init_instruction_slot();
        init_variable_slot();
    }

    std::string_view UBlueprintMetaMethodNode::GetDefaultArgName(std::uint32_t arg_idx)
    {
        static std::vector<std::string> internal_arg_names;
        if (internal_arg_names.empty())
        {
            internal_arg_names.resize(100);
            for (std::uint32_t i = 0; i < internal_arg_names.size(); i++)
            {
                internal_arg_names[i] = "arg_" + std::to_string(i);
            }
        }
        return internal_arg_names[arg_idx];
    }

    void UBlueprintMetaMethodNode::init_variable_slot()
    {
        assert(mMethodInfo);
        // init variable slot

        ////if (mMethodInfo->call_type() == reflection::FuncCallType::ObjectCall) {
        ////  auto* meta = reflection::query_meta_class_by_name(mType);
        ////  auto selfVarSlot = this->AddVariableSlot(NGLinkDirection::In, 0, machine::VValueKind::kUser, meta->name());
        ////  (void)selfVarSlot;
        ////  assert(selfVarSlot);
        ////}

        auto* caller = mMethodInfo->GetOneCaller(reflection::FuncLanguageType::AsCxx);
        const auto& all_param_name_s = mMethodInfo->param_names();
        for (size_t loop = 0; loop < caller->GetParamCount(); ++loop)
        {
            auto param_kind = caller->GetParamType(loop);
            auto param_type_id = caller->GetParamBaseTypeIndex(loop);
            // default arg_name arg_xxx
            std::string_view param_arg_name = GetDefaultArgName(std::uint32_t(loop));
            if (loop < all_param_name_s.size())
            {
                param_arg_name = all_param_name_s[loop];
            }
            auto varInSlot = this->AddDataSlot(BlueprintLinkDirection::In, loop, param_kind, std::string(param_arg_name), param_type_id);
            assert(varInSlot);

            ////// default value;
            ////if (input_param->HasDefaultValue()) varInSlot->SetDefaultValue(input_param->value());
        }

        auto return_kind = caller->return_type();
        if (return_kind != machine::VValueKind::kNone)
        {
            auto* return_meta = reflection::query_meta_class_by_id(caller->GetReturnBaseTypeIndex());
            this->AddDataSlot(BlueprintLinkDirection::Out, 0, return_kind, return_meta->name(), return_meta->id());
        }
    }

    void UBlueprintMetaMethodNode::init_instruction_slot()
    {
        AddExecSlot(BlueprintLinkDirection::In, 0, "");
        AddExecSlot(BlueprintLinkDirection::Out, 0, "");
    }

    void UBlueprintMetaMethodNode::SerializeToJson(machine::IVMStreamWriter& writer)
    {
        UBlueprintActionNode::SerializeToJson(writer);

        writer.AddStringProperty("agent", mType);
        writer.AddStringProperty("method", mMethod);
    }

    void UBlueprintMetaMethodNode::DeserializeFields(machine::IVMStreamReadNode& node)
    {
        UBlueprintActionNode::DeserializeFields(node);
        // create slots first
        auto agent = node.GetStringProperty("agent");
        auto method = node.GetStringProperty("method");
        assert(!agent.empty() && !method.empty());
        if (!init_method(agent, method))
            throw std::exception();   // todo ... better error process
        
    }

    UBlueprintActionNode::ProcessingInfo UBlueprintMetaMethodNode::RtActivateLogicImpl(machine::VCoroutine* coroutine_, UBlueprintExecSlot* slot)
    {
        assert(mMethodInfo);

        UBlueprintActionNode::ProcessingInfo info;
        info.State = UBlueprintActionNode::LogicState::Ok;

        // prepare memory scopes
        auto local = coroutine_->GetCurrentMemoryScope();
        auto global = coroutine_->GetGlobalMemoryScope();

        // TODO ... check runtime cause runtime overhead
        auto input_count = this->GetDataSlotInCount();

        reflection::Args args;

        for (size_t i = 0; i < input_count; i++)
        {
            auto val = RtGetDataSlotValue(BlueprintLinkDirection::In, i, local, global);
            args += *val;
        }

        auto ret_val = reflection::cxx::CallStaticWithArgs(*mMethodInfo, std::move(args));
        if (GetDataSlotOutCount() > 0)
        {
            auto result_slot_value = RtGetDataSlotValue(BlueprintLinkDirection::Out, 0, local, global);
            assert(result_slot_value);
            *result_slot_value = ret_val;
        }

        // forward instruction
        RtActiveOuputLink(coroutine_, 0);
        return info;
    }
}}   // namespace gbf::logic
