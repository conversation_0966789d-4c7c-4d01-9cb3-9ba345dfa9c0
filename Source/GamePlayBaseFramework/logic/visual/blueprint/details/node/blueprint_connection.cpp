#include "visual/blueprint/details/node/blueprint_connection.h"

#include "visual/blueprint/details/blueprint_graph_base.h"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/blueprint/details/node/blueprint_slot.h"

namespace gbf { namespace logic {

    UBlueprintConnection::UBlueprintConnection(BlueprintSlotType slot_type, UBlueprintSlot* _out_slot, UBlueprintSlot* _in_slot)
        : m_connect_slot_type(slot_type)
        , m_connect_id(UBpGraphBase::GetConnectIdFromSlots(_out_slot, _in_slot))
        , m_out_slot(_out_slot)
        , m_in_slot(_in_slot)
    {}

    UBlueprintConnection::~UBlueprintConnection() {}

    void UBlueprintConnection::SerializeTo<PERSON>son(machine::IVMStreamWriter& writer)
    {
        writer.AddUintProperty("in_slot", m_in_slot->GetFullSlotId());

        writer.StartObject("in_slot_info");

        writer.AddUintProperty("node_id", m_in_slot->node()->id());
        writer.AddIntProperty("slot_type", (int)m_in_slot->slot_type());

        writer.EndObject();

        writer.AddUintProperty("out_slot", m_out_slot->GetFullSlotId());

        writer.StartObject("out_slot_info");

        writer.AddUintProperty("node_id", m_out_slot->node()->id());
        writer.AddIntProperty("slot_type", (int)m_out_slot->slot_type());

        writer.EndObject();
    }

    void UBlueprintConnection::DeserializeFromJson(machine::IVMStreamReadNode& node) {}

}}   // namespace gbf::logic
