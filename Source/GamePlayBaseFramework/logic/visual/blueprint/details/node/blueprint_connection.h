#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "reflection/objects/rtti_base.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"

namespace gbf { namespace logic {

    class BLUEPRINT_API CEMeta(Reflect) UBlueprintConnection : public reflection::RttiBase
    {
        friend class UBpGraphBase;

    public:
        CEMeta(Reflect)
        UBlueprintConnection(BlueprintSlotType slot_type, UBlueprintSlot * _out_slot, UBlueprintSlot * _in_slot);
        ~UBlueprintConnection();

        uint64_t connect_id() const noexcept
        {
            return m_connect_id;
        }

        BlueprintSlotType connect_slot_type() const noexcept
        {
            return m_connect_slot_type;
        }

        UBlueprintSlot* out_slot() const noexcept
        {
            return m_out_slot;
        }

        UBlueprintSlot* in_slot() const noexcept
        {
            return m_in_slot;
        }

        bool IsInstructionConnection() const noexcept
        {
            return m_connect_slot_type == BlueprintSlotType::Exec;
        }

        bool IsVariableConnection() const noexcept
        {
            return m_connect_slot_type == BlueprintSlotType::Data;
        }

    protected:
        void SerializeToJson(machine::IVMStreamWriter & writer);

        void DeserializeFromJson(machine::IVMStreamReadNode & node);

    protected:
        BlueprintSlotType m_connect_slot_type = BlueprintSlotType::Unknown;
        uint64_t m_connect_id = 0;

        // Weak reference here
        UBlueprintSlot* m_out_slot;
        UBlueprintSlot* m_in_slot;
    };
}}   // namespace gbf::logic
