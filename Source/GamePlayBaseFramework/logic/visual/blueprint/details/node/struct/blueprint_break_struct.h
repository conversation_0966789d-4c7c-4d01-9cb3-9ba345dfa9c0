#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "reflection/objects/rtti_base.hpp"

namespace gbf { namespace logic {
    class BLUEPRINT_API CEMeta(Reflect) UBlueprintBreakStruct : public UBlueprintNode
    {
    public:
        CEMeta(Reflect)
        UBlueprintBreakStruct()
            : UBlueprintNode(BlueprintNodeType::Variable, BlueprintSlotAvailableFlag::Var, "")
        {}
    };
}}   // namespace gbf::logic
