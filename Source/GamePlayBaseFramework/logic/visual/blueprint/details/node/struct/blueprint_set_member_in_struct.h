#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/details/node/blueprint_node.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/blueprint/details/node/blueprint_action_node.h"
#include "reflection/objects/rtti_base.hpp"

namespace gbf { namespace logic {
    class BLUEPRINT_API CEMeta(Reflect) UBlueprintSetMemberInStruct : public UBlueprintActionNode
    {
    public:
        CEMeta(Reflect)
        UBlueprintSetMemberInStruct()
            : UBlueprintActionNode(BlueprintSlotAvailableFlag::NodeVarIn, "")
        {}

    protected:
        gbf::logic::UBlueprintActionNode::ProcessingInfo RtActivateLogicImpl(gbf::machine::VCoroutine*, gbf::logic::UBlueprintExecSlot*) override
        {
            return {};
        }
    };
}}   // namespace gbf::logic
