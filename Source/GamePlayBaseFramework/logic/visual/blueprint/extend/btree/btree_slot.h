#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/blueprint/extend/btree/btree_fwd.h"

#include "reflection/objects/rtti_base.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"

namespace gbf {
namespace logic {

class BLUEPRINT_API UBtreeSlot : public reflection::RttiBase {
  friend class UBtreeGraph;
  friend class UBtreeNode;

 private:
  const size_t kMaxSupportConnectCount = 128;

 public:
  // Nested Types
  using ConnectionArray = std::vector<UBtreeConnection*>;

 public:
  UBtreeSlot(UBtreeNode* parent_node, BTLinkDirection link_dir, bool support_only_one_connect);
  virtual ~UBtreeSlot();

  //Properties
  UBtreeNode* get_parent_node() const noexcept { return m_parent_node; }
  BTLinkDirection get_link_direction() const noexcept { return m_link_direction; }

  bool is_only_one_connect_support() const noexcept { return m_is_only_one_connect_support; }

  size_t get_in_connection_count() const noexcept;

  size_t get_out_connection_count() const noexcept;

  uint32_t get_full_slot_id() const noexcept;

  // Functions
  void PushLinksAsInstructions(machine::VCoroutine* coroutine_, BtreeInstructionImpl* parent_instruct);
  bool CanDoConnect() const noexcept;
 protected:
  void AddOutConnection(UBtreeConnection* connection);
  void RemoveOutConnection(UBtreeConnection* connection);

  void AddInConnection(UBtreeConnection* connection);
  void RemoveInConnection(UBtreeConnection* connection);

  virtual void SerializeToJson(machine::IVMStreamWriter& writer);
  virtual void DeserializeFromJson(machine::IVMStreamReadNode& node);

 protected:
  BTLinkDirection m_link_direction;
  bool m_is_only_one_connect_support;

  UBtreeNode* m_parent_node;
  ConnectionArray m_connection_array;

  ////ConnectionArray m_out_connection_array;
  ////ConnectionArray m_in_connection_array;
};
}  // namespace logic
}  // namespace gbf
