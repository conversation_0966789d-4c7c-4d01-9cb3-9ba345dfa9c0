#include "visual/blueprint/extend/btree/btree_workspace.h"
#include "visual/blueprint/btree.hpp"

#include "visual/blueprint/extend/btree/btree_context.h"
#include "visual/blueprint/serialize_impl/jsonvmstreamreader.h"
#include "visual/blueprint/serialize_impl/jsonvmstreamwriter.h"

#include "reflection/objects/make_user_object.hpp"
#include "visual/virtual_machine/runtime/iinstruction.hpp"
#include "visual/virtual_machine/runtime/named_memory_scope.hpp"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace logic {

int UBtreeWorkspace::BehaviorTreeFileVersion = 1;
bool UBtreeWorkspace::ms_is_env_inited = false;
//-------------------------------------------------------------------------------------------
UBtreeWorkspace::UBtreeWorkspace() {
  if (!ms_is_env_inited) {
    InitBtreeEnvironment();
    ms_is_env_inited = true;
  }
}

UBtreeWorkspace::~UBtreeWorkspace() {
  if (m_listener) {
    delete m_listener;
    m_listener = nullptr;
  }
}

UBtreeContextPtr UBtreeWorkspace::CreateContext(UBtreeGraph* main_graph, const machine::VObject& agent) {
  auto global_mem_scope = reflection::make_shared_with_rtti<machine::NamedMemoryScope>();

  // global memory scope initialize
  ////for (NGNamedVariable* variable : *m_named_variable_array)
  ////{
  ////	auto* tmp_value = variable->get_default_value()->clone();
  ////	global_mem_scope->create_vmvalue(variable->get_name(), tmp_value);
  ////}

  auto new_context = reflection::make_shared_with_rtti<UBtreeContext>(this, main_graph, agent, global_mem_scope);
  return new_context;
}

UBtreeGraph* UBtreeWorkspace::CreateGraph(std::string_view _name, std::string_view _desc) {
  auto graph = reflection::make_shared_with_rtti<UBtreeGraph>(_name, _desc, this);
  m_graph_array.push_back(graph);

  // fire_on_graph_create_event(graph);

  return graph.get();
}

void UBtreeWorkspace::DestroyGraph(std::string_view _name) noexcept {
  auto iter = std::find_if(m_graph_array.begin(), m_graph_array.end(), [_name](const UBtreeGraphPtr& graph) { return graph->get_name() == _name; });

  if (iter != m_graph_array.end()) {
    FireOnGraphDestroyEvent((*iter).get());

    m_graph_array.erase(iter);
  }
}

UBtreeGraph* UBtreeWorkspace::GetGraphByIndex(size_t index) noexcept {
  assert(index < m_graph_array.size());
  return m_graph_array[index].get();
}

UBtreeGraph* UBtreeWorkspace::GetGraphByName(std::string_view _name) noexcept {
  auto iter = std::find_if(m_graph_array.begin(), m_graph_array.end(), [_name](const UBtreeGraphPtr& graph) { return graph->get_name() == _name; });

  if (iter != m_graph_array.end()) {
    return (*iter).get();
  } else {
    return nullptr;
  }
}

void UBtreeWorkspace::ClearAll() {
  // ToDo: need add clear context code here
  for (auto& graph : m_graph_array) {
    FireOnGraphDestroyEvent(graph.get());
  }
  m_graph_array.clear();
}

void UBtreeWorkspace::SaveToMemory(ByteBuffer& mem_stream) {
  JsonVMStreamWriter writer;

  writer.SetIndent('\t', 1);

  writer.StartObject("");

  writer.AddIntProperty("bt_version", BehaviorTreeFileVersion);

  writer.StartArray("behavior_tree_list");

  // write sequence to json
  for (auto& seq : m_graph_array) {
    writer.StartObject("");
    seq->SerializeToJson(writer);
    writer.EndObject();
  }
  writer.EndArray();
  writer.EndObject();

  writer.SaveToMemory(mem_stream);
}

void UBtreeWorkspace::LoadFromMemory(ByteBuffer& mem_stream) {
  ClearAll();

  JsonVMStreamReader tmp_reader;
  auto* dom = tmp_reader.LoadFromMemory(mem_stream);

  int file_version = dom->GetIntProperty("bt_version");
  assert(file_version == BehaviorTreeFileVersion && "BTWorkspace::load_from_memory() can not recogonize this version of behavior tree file!");
  (file_version);

  auto* json_sequence_list = dom->GetNode("behavior_tree_list");
  for (size_t i = 0; i < json_sequence_list->GetArraySize(); i++) {
    auto* json_seq_item = json_sequence_list->GetArrayElement(i);
    auto tmp_name = json_seq_item->GetStringProperty("name");
    auto tmp_desc = json_seq_item->GetStringProperty("description");
    auto new_seq = CreateGraph(tmp_name, tmp_desc);
    new_seq->DeserializeFromJson(*json_seq_item);
  }
}

void UBtreeWorkspace::SetListener(Listener* listener_) {
  if (m_listener) {
    delete m_listener;
    m_listener = nullptr;
  }

  m_listener = listener_;
}

void UBtreeWorkspace::FireOnGraphCreateEvent(UBtreeGraph* graph) {
  if (m_listener) {
    m_listener->OnGraphCreate(graph);
  }
}

void UBtreeWorkspace::FireOnGraphDestroyEvent(UBtreeGraph* graph) {
  if (m_listener) {
    m_listener->OnGraphDestroy(graph);
  }
}

void UBtreeWorkspace::FireOnNodeAddEvent(UBtreeNode* node) {
  if (m_listener) {
    m_listener->OnNodeAdd(node);
  }
}

void UBtreeWorkspace::FireOnNodeRemoveEvent(UBtreeNode* node) {
  if (m_listener) {
    m_listener->OnNodeRemove(node);
  }
}

void UBtreeWorkspace::FireOnConnectionCreateEvent(UBtreeConnection* conn) {
  if (m_listener) {
    m_listener->OnConnectionCreate(conn);
  }
}

void UBtreeWorkspace::FireOnConnectionDestroyEvent(UBtreeConnection* conn) {
  if (m_listener) {
    m_listener->OnConnectionDestroy(conn);
  }
}

void UBtreeWorkspace::InitBtreeEnvironment() {
  // manual register something
  /*auto* metaInfo = meta_type_helper::register_cpp_type_info<VMRunStepStatus>("VMRunStepStatus");
  metaInfo->add_cpp_const("Unknown", VMRunStepStatus::Unknown);
  metaInfo->add_cpp_const("Succeed", VMRunStepStatus::Succeed);
  metaInfo->add_cpp_const("RunNextFrame", VMRunStepStatus::RunNextFrame);
  metaInfo->add_cpp_const("Failed", VMRunStepStatus::Failed);
  metaInfo->add_cpp_const("Error", VMRunStepStatus::Error);
#define INIT_TYPE_INFO(classname) MetaHelper::create_cpp_type_info<classname>(#classname);
#define INIT_TYPE_INFO_WITHOUT_CONSTRUCTOR(classname) MetaHelper::create_cpp_type_info<classname>(#classname, false);
  INIT_TYPE_INFO(BTGraph);
  INIT_TYPE_INFO(BTAction);
  INIT_TYPE_INFO(BTActionBase);
  INIT_TYPE_INFO(BTAssignment);
  INIT_TYPE_INFO(BTCompute);
  INIT_TYPE_INFO(BTEnd);
  INIT_TYPE_INFO(BTNoop);
  INIT_TYPE_INFO(BTWait);
  INIT_TYPE_INFO(BTWaitFrames);
  INIT_TYPE_INFO(BTAttachment);
  INIT_TYPE_INFO(BTEffector);
  INIT_TYPE_INFO(BTPreCondition);
  INIT_TYPE_INFO(BTParallel);
  INIT_TYPE_INFO(BTSelector);
  INIT_TYPE_INFO(BTSequence);
  INIT_TYPE_INFO(BTCondition);
  INIT_TYPE_INFO(BTDecoratorBase);
  INIT_TYPE_INFO(BTDecoratorCount);
  INIT_TYPE_INFO(BTDecoratorCountLimit);
  INIT_TYPE_INFO(BTDecoratorLog);
  INIT_TYPE_INFO(BTDecoratorLoop);
  INIT_TYPE_INFO(BTNode);
  INIT_TYPE_INFO(BTRootNode);
  INIT_TYPE_INFO(BTConnection);
  INIT_TYPE_INFO(BTContext);
  INIT_TYPE_INFO(BTInstructionImpl);
  INIT_TYPE_INFO(BTSlot);
  INIT_TYPE_INFO(VMTerm);
  INIT_TYPE_INFO(VMTermConst);
  INIT_TYPE_INFO(VMTermField);
  INIT_TYPE_INFO(VMTermMethod);*/
}

}  // namespace logic
}  // namespace gbf
