#include "visual/blueprint/extend/btree/node/action/btree_assignment.h"
#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/expr/iterm_field.hpp"

namespace gbf {
namespace logic {

UBtreeAssignment::UBtreeAssignment() { m_title = "Assignment"; }

UBtreeAssignment::~UBtreeAssignment() {}

machine::VMRunStepStatus UBtreeAssignment::UpdateLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj) {
  machine::VMRunStepStatus result = machine::VMRunStepStatus::Succeed;
  if (m_operator_left && m_operator_right) {
    // ToDo: add cast support here

    m_operator_left.Assign(coroutine_, obj, m_operator_right.Evaluate(coroutine_, obj));
  } else {
    ////GAME_LOG_WARN("BTAssignment::update_logic() left operator or right operator is null!");
  }

  return result;
}

void UBtreeAssignment::SerializeTo<PERSON>son(machine::IVMStreamWriter& writer) {
  base::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(writer);

  writer.AddBoolProperty("use_cast", m_use_cast);

  if (m_operator_left) {
    writer.StartObject("operator_left");
    m_operator_left.SerializeToJson(writer);
    writer.EndObject();
  }

  if (m_operator_right) {
    writer.StartObject("operator_right");
    m_operator_right.SerializeToJson(writer);
    writer.EndObject();
  }
}

void UBtreeAssignment::DeserializeFromJson(machine::IVMStreamReadNode& node) {
  base::DeserializeFromJson(node);

  m_use_cast = node.GetBoolProperty("use_cast");

  auto* value = node.GetNode("operator_left");
  if (value != nullptr && value->IsObject()) {
    m_operator_left.DeserializeFromJson(*value);
  }

  value = node.GetNode("operator_right");
  if (value != nullptr && value->IsObject()) {
    m_operator_right.DeserializeFromJson(*value);
  }
}

}  // namespace logic
}  // namespace gbf
