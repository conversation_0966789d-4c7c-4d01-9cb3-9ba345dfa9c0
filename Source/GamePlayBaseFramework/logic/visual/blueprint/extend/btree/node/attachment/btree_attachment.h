#pragma once

#include "visual/blueprint/blueprint_define.hpp"

#include "reflection/objects/rtti_base.hpp"
#include "visual/blueprint/extend/btree/btree_fwd.h"
#include "visual/virtual_machine/runtime/expr/iterm.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "visual/virtual_machine/runtime/vcoroutine.hpp"
#include "visual/virtual_machine/vmcore_define.hpp"
#include "visual/blueprint/extend/btree/btree_value.h"

namespace gbf {
namespace logic {
////class BTOperator;
class BLUEPRINT_API UBtreeAttachment : public reflection::RttiBase {
 public:
  UBtreeAttachment();
  ~UBtreeAttachment();

  void set_operator_type(machine::VMOperatorType operator_type) noexcept { m_operator_type = operator_type; }

  machine::VMOperatorType get_operator_type() const noexcept { return m_operator_type; }

  void set_operator_left(const UBtreeValue& term_field) noexcept { m_operator_left = term_field; }

  const UBtreeValue& get_operator_left() const noexcept { return m_operator_left; }

  void set_operator_right_1(const UBtreeValue& term) noexcept { m_operator_right_1 = term; }

  const UBtreeValue& get_operator_right_1() const noexcept { return m_operator_right_1; }

  void set_operator_right_2(const UBtreeValue& term) noexcept { m_operator_right_2 = term; }

  const UBtreeValue& get_operator_right_2() const noexcept { return m_operator_right_2; }

  bool ExecuteLogic(machine::VCoroutine* coroutine_, const machine::VObject& obj);

 protected:
  virtual void SerializeToJson(machine::IVMStreamWriter& writer);
  virtual void DeserializeFromJson(machine::IVMStreamReadNode& node);

 protected:
  machine::VMOperatorType m_operator_type = machine::VMOperatorType::Invalid;

  UBtreeValue       m_operator_left;
  UBtreeValue m_operator_right_1;
  UBtreeValue m_operator_right_2;
};

}  // namespace logic
}  // namespace gbf
