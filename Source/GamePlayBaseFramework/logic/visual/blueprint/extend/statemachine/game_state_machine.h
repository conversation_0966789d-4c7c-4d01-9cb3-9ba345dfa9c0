#pragma once

#include "visual/blueprint/extend/statemachine/game_state_fwd.hpp"
#include "reflection/objects/rtti_base.hpp"

namespace gbf {
namespace logic {

class BLUEPRINT_API GameStateMachine: public reflection::RttiBase {
 private:
  struct GameStateSink : public reflection::RttiBase {
    SinkId id;
    std::string sink_name;
    StateId cur_state_id;
    StateId previous_state_id;

    StateIdList contain_state_id_list;
  };
  using GameStateSinkPtr = std::shared_ptr<GameStateSink>;
 public:
  GameStateMachine();
  ~GameStateMachine();

  // 调用这个更新FSM
  /// Update state machine
  void Update(float nowTime, float elapse_time);

  // 改变到一个新状态(系统会自动识别Sink)
  /// ChangeState by state_id
  bool ChangeState(StateId state_id, bool ignore_state_lock = false);
  /// ChangeState by state_name
  bool ChangeState(std::string_view state_name, bool ignore_state_lock = false);

  // 强制切换状态机至某状态
  /// ChangeStateForce by state_id
  bool ChangeStateForce(StateId state_id);
  /// ChangeStateForce by state_name
  bool ChangeStateForce(std::string_view state_name);

  // 能否切换到该状态
  /// ChangeStateTest by state_id
  bool ChangeStateTest(StateId state_id, bool ignore_state_lock = false);
  /// ChangeStateTest by state_name
  bool ChangeStateTest(std::string_view state_name, bool ignore_state_lock = false);

  /// IsInState by state_id
  bool IsInState(StateId state_id);
  /// IsInState by state_name
  bool IsInState(std::string_view state_name);

  /// FindState by state_id
  GameStatePtr FindState(StateId state_id);
  /// FindState by name
  GameStatePtr FindState(std::string_view name);

  /// GetSinkRunState by sink_id
  StateId GetSinkRunState(SinkId sink_id);
  /// GetSinkRunState by sink_name
  std::string_view GetSinkRunState(std::string_view sink_name);

  unsigned int GetCurActionFlag();

  void SinkToNullState(SinkId sink_id);
  void SinkToNullState(std::string_view sink_name);

  bool CreateSink(SinkId sink_id);
  bool CreateSink(std::string_view sink_name);

  /// CreateNormalState
  GameStatePtr CreateNormalState(StateId state_id, SinkId sink_id);
  /// CreateComposeState
  GameStatePtr CreateComposeState(StateId state_id, SinkId sink_id);
  /// CreateSubState
  GameStatePtr CreateSubState(StateId state_id, SinkId sink_id, StateId parent_id);

  /// CreateNormalState
  GameStatePtr CreateNormalState(std::string_view state_name, std::string_view sink_name);
  /// CreateComposeState
  GameStatePtr CreateComposeState(std::string_view state_name, std::string_view sink_name);
  /// CreateSubState
  GameStatePtr CreateSubState(std::string_view state_name, std::string_view sink_name, std::string_view parent_name);

  /// DestroyState
  void DestroyState(StateId state_id);
  /// DestroyState
  void DestroyState(std::string_view state_name);

  /// GetStateNameFromId
  std::string_view GetStateNameFromId(StateId state_id);
  /// GetStateIdFromName
  StateId GetStateIdFromName(std::string_view name);

  /// GetSinkNameFromId
  std::string_view GetSinkNameFromId(SinkId sink_id);
  /// GetSinkIdFromName
  SinkId GetSinkIdFromName(std::string_view name);

  // void SetListener(SCRIPT_FUNCTION_OBJECT_TYPE enter_func, SCRIPT_FUNCTION_OBJECT_TYPE exit_func);
  /// OnStateEnterNotify
  void OnStateEnterNotify(GameState* pCurState);
  /// OnStateLeaveNotify
  void OnStateLeaveNotify(GameState* pCurState);

 private:
  GameStatePtr CreateStateImpl(StateId state_id, std::string_view state_name, SinkId sink_id, StateComposeType com_type,
                             StateId parent_id = kNullStateId);
  bool CreateSinkImpl(SinkId sink_id, std::string_view set_name);

  GameStateSinkPtr FindSink(SinkId sink_id);
  GameStateSinkPtr FindSink(std::string_view name);

  std::string GetDefaultStateNameFromId(StateId id);
  std::string GetDefaultSinkNameFromId(SinkId id);

 private:
  
  using StateSinkArray = std::vector<GameStateSinkPtr> ;

  StateSinkArray m_state_sink_array;
  GameStateArray m_state_array;

  StateIdList m_free_state_id_list;
  SinkIdList m_free_sink_id_list;

  IGameStateListner* mAllStateListener;
};
/** @} */

}  // namespace logic
}  // namespace gbf

