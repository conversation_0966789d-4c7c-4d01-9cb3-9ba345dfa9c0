#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreader.h"
#include "core/utils/byte_buffer.h"

#include "visual/blueprint/serialize_impl/jsonvmstreamreadnode.h"
#include "rapidjson/document.h"

namespace gbf {
namespace logic {
class BLUEPRINT_API JsonVMStreamReader {
 public:
  JsonVMStreamReader();
  ~JsonVMStreamReader();

  JsonVMStreamReadNode* LoadFromMemory(ByteBuffer& mem_chunk);

  JsonVMStreamReadNode* CreateNodeFromJsonValue(rapidjson::Value& json_value);

 private:
  rapidjson::Document m_doc;
  std::vector<JsonVMStreamReadNode*> m_saved_read_node_array;
};
}  // namespace logic
}  // namespace gbf
