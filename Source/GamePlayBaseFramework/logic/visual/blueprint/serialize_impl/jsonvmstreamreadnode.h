#pragma once

#include "visual/blueprint/blueprint_define.hpp"
#include "rapidjson/document.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"

namespace gbf {
namespace logic {
class JsonVMStreamReader;

class BLUEPRINT_API JsonVMStreamReadNode : public machine::IVMStreamReadNode {
  friend class JsonVMStreamReader;

 public:
  ~JsonVMStreamReadNode();

  virtual machine::IVMStreamReadNode* GetNode(const std::string& prop_name) override;

  virtual machine::IVMStreamReadNode* GetArrayElement(size_t index) override;

  virtual size_t GetArraySize() override;

  virtual bool IsNull() const override;

  virtual bool IsArray() const override;

  virtual bool IsObject() const override;

  virtual bool HasProperty(const std::string& prop_name) override;

  virtual std::string GetStringProperty(const std::string& prop_name, std::string default_value = "") override;
  virtual bool GetBoolProperty(const std::string& prop_name, bool default_value = false) override;
  virtual int GetIntProperty(const std::string& prop_name, int default_value = 0) override;
  virtual unsigned GetUintProperty(const std::string& prop_name, unsigned default_value = 0) override;
  virtual int64_t GetInt64Property(const std::string& prop_name, int64_t default_value = 0) override;
  virtual uint64_t GetUint64Property(const std::string& prop_name, uint64_t default_value = 0) override;
  virtual double GetDoubleProperty(const std::string& prop_name, double default_value = 0.0) override;

 private:
  JsonVMStreamReadNode(JsonVMStreamReader& reader, rapidjson::Value& json_value);
  JsonVMStreamReadNode() = delete;
  JsonVMStreamReadNode(const JsonVMStreamReadNode&) = delete;

 private:
  JsonVMStreamReader& m_reader;
  rapidjson::Value& m_json_value;
};
}  // namespace logic
}  // namespace gbf
