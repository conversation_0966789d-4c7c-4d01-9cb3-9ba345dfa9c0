#include "visual/virtual_machine/runtime/expr/evaluate_unit.hpp"

#include <cmath>
#include "visual/virtual_machine/utils/vvalue_util.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {
namespace machine {

EvaluateUnit::EvaluateUnit() {

}

EvaluateUnit::~EvaluateUnit() {}

VValue EvaluateUnit::DoOperation(VMOperatorType op_type, VValue& larg, const VValue& rarg) {
  VMEvalGroupType group = GetEvaluateGroup(op_type);
  VValueKind expect_type = GetEvaluateResultType(op_type, group, larg.kind(), rarg.kind());
  VValue ret_value;

  if (op_type == VMOperatorType::Assign) {
    ret_value = larg;  // when assign , just retun left argument
  } else {
    ret_value = VValueUtil::CreateDefaultValueByType(expect_type);
  }

  if (expect_type != VValueKind::kNone) {
    switch (group) {
      case VMEvalGroupType::Math:
        DoMathOperation(ret_value, op_type, group, larg, rarg);
        break;
      case VMEvalGroupType::NormalComapre:
        DoNormalCompareOperation(ret_value, op_type, group, larg, rarg);
        break;
      case VMEvalGroupType::MathCompare:
        DoMathCompareOperation(ret_value, op_type, group, larg, rarg);
        break;
      case VMEvalGroupType::Logic:
        DoLogicOperation(ret_value, op_type, group, larg, rarg);
        break;
      case VMEvalGroupType::Assign:
        larg = rarg;
        break;
      default:
        break;
    }
  }

  return ret_value;
}

VValueKind EvaluateUnit::GetEvaluateResultType(VMOperatorType op_type, VMEvalGroupType group, VValueKind larg_kind, VValueKind rarg_kind) {
  VValueKind expect_type = VValueKind::kNone;
  ////VMEvaluateGroup group = get_evaluate_group(op_type);

  if (larg_kind == VValueKind::kNone || larg_kind == VValueKind::kUnknown) {
    return expect_type;
  }

  bool is_binary = IsBinaryOperator(op_type);
  if (is_binary && (larg_kind == VValueKind::kNone || larg_kind == VValueKind::kUnknown)) {
    return expect_type;
  }

  switch (group) {
    case VMEvalGroupType::Math:
      if(VValue::IsArithmaticType(larg_kind) && VValue::IsArithmaticType(rarg_kind)) {
        if (larg_kind == VValueKind::kReal || rarg_kind == VValueKind::kReal) {
          expect_type = VValueKind::kReal;
        } else {
          expect_type = VValueKind::kInteger;
        }
      }
      break;
    case VMEvalGroupType::NormalComapre:
      expect_type = VValueKind::kBoolean;
      break;
    case VMEvalGroupType::MathCompare:
      if (VValue::IsArithmaticType(larg_kind) && VValue::IsArithmaticType(rarg_kind)) {
        expect_type = VValueKind::kBoolean;
      }
      break;
    case VMEvalGroupType::Logic:
      if (is_binary) {
        if ((larg_kind == VValueKind::kBoolean) && (rarg_kind == VValueKind::kBoolean)) {
          expect_type = VValueKind::kBoolean;
        }
      } else {
        if (larg_kind == VValueKind::kBoolean) {
          expect_type = VValueKind::kBoolean;
        }
      }
      break;
    case VMEvalGroupType::Assign:
      if (larg_kind == rarg_kind) {
        expect_type = larg_kind;
      }
    default:
      break;
  }

  return expect_type;
}

VMEvalGroupType EvaluateUnit::GetEvaluateGroup(VMOperatorType op_type) {
  switch (op_type) {
    case VMOperatorType::Add:
    case VMOperatorType::Sub:
    case VMOperatorType::Mul:
    case VMOperatorType::Div:
      return VMEvalGroupType::Math;
    case VMOperatorType::Equal:
    case VMOperatorType::NotEqual:
      return VMEvalGroupType::NormalComapre;
    case VMOperatorType::Greater:
    case VMOperatorType::Less:
    case VMOperatorType::GreaterEqual:
    case VMOperatorType::LessEqual:
      return VMEvalGroupType::MathCompare;
    case VMOperatorType::And:
    case VMOperatorType::Or:
    case VMOperatorType::Xor:
      return VMEvalGroupType::Logic;
    case VMOperatorType::Assign:
      return VMEvalGroupType::Assign;
    default:
      break;
  }

  return VMEvalGroupType::Unknown;
}

bool EvaluateUnit::IsBinaryOperator(VMOperatorType op_type) {
  return op_type != VMOperatorType::Not;  // now we only have a not unary operator
}

bool EvaluateUnit::DoubeEqual(double a, double b, double ep /*= 0.000001*/) { return fabs(a - b) < ep; }

std::string_view EvaluateUnit::GetOperatorName(VMOperatorType op_type) {
  switch (op_type)
  {
  case gbf::machine::VMOperatorType::Invalid:
    return "invalid";
  case gbf::machine::VMOperatorType::Add:
    return "add";
  case gbf::machine::VMOperatorType::Sub:
    return "sub";
  case gbf::machine::VMOperatorType::Mul:
    return "mul";
  case gbf::machine::VMOperatorType::Div:
    return "div";
  case gbf::machine::VMOperatorType::Equal:
    return "equal";
  case gbf::machine::VMOperatorType::NotEqual:
    return "not_equal";
  case gbf::machine::VMOperatorType::Greater:
    return "greater";
  case gbf::machine::VMOperatorType::Less:
    return "less";
  case gbf::machine::VMOperatorType::GreaterEqual:
    return "greater_equal";
  case gbf::machine::VMOperatorType::LessEqual:
    return "less_equal";
  case gbf::machine::VMOperatorType::And:
    return "and";
  case gbf::machine::VMOperatorType::Or:
    return "or";
  case gbf::machine::VMOperatorType::Xor:
    return "xor";
  case gbf::machine::VMOperatorType::Not:
    return "not";
  case gbf::machine::VMOperatorType::Assign:
    return "assign";
  case gbf::machine::VMOperatorType::Function:
    return "function";
  default:
    return "invalid";
  }
}

void EvaluateUnit::DoMathOperation(VValue& target_val, VMOperatorType op_type, VMEvalGroupType group, const VValue& larg, const VValue& rarg) {
  assert(group == VMEvalGroupType::Math && "VMEvaluateUnit::do_math_operation() run to a error pass here!");
  // assert(target_val->value_type == VMValueType::TInt64 || target_val->value_type == VMValueType::TDouble);
  assert(target_val.IsArithmatic());
  if (target_val.IsDouble()) {
    // double
    double d_lval = 0.0, d_rval = 0.0;

    d_lval = reflection::value_cast<double>(larg);
    d_rval = reflection::value_cast<double>(rarg);

    switch (op_type) {
      case VMOperatorType::Add:
        d_lval += d_rval;
        break;
      case VMOperatorType::Sub:
        d_lval -= d_rval;
        break;
      case VMOperatorType::Mul:
        d_lval *= d_rval;
        break;
      case VMOperatorType::Div:
        assert(!DoubeEqual(d_rval, 0.0) && "VMEvaluateUnit::do_math_operation() divide zero!");
        d_lval /= d_rval;
        break;
      default:
        break;
    }

    target_val = reflection::make_value(d_lval);
  } else {
    // int64
    int64_t i_lval = 0, i_rval = 0;

    i_lval = reflection::value_cast<int64_t>(larg);
    i_rval = reflection::value_cast<int64_t>(rarg);

    switch (op_type) {
      case VMOperatorType::Add:
        i_lval += i_rval;
        break;
      case VMOperatorType::Sub:
        i_lval -= i_rval;
        break;
      case VMOperatorType::Mul:
        i_lval *= i_rval;
        break;
      case VMOperatorType::Div:
        assert(i_rval != 0 && "VMEvaluateUnit::do_math_operation() divide zero!");
        i_lval /= i_rval;
        break;
      default:
        break;
    }

    target_val = reflection::make_value(i_lval);
  }
}

void EvaluateUnit::DoNormalCompareOperation(VValue& target_val, VMOperatorType op_type, VMEvalGroupType group, const VValue& larg, const VValue& rarg) {
  assert(group == VMEvalGroupType::NormalComapre && "VMEvaluateUnit::do_normal_compare_operation() run to a error pass here!");
  // assert(target_val->value_type == VMValueType::TBool);
  assert(target_val.IsBool());

  if (op_type == VMOperatorType::Equal) {
    target_val = reflection::make_value(larg == rarg);
  } else if (op_type == VMOperatorType::NotEqual) {
    target_val = reflection::make_value(!(larg == rarg));
  }
}

void EvaluateUnit::DoMathCompareOperation(VValue& target_val, VMOperatorType op_type, VMEvalGroupType group, const VValue& larg, const VValue& rarg) {
  assert(group == VMEvalGroupType::MathCompare && "VMEvaluateUnit::do_math_compare_operation() run to a error pass here!");
  // assert(target_val->value_type == VMValueType::TBool);
  assert(target_val.IsBool());

  bool has_double = (larg.IsDouble() || rarg.IsDouble());
  bool compare_result = false;
  if (has_double) {
    // double
    double d_lval = 0.0, d_rval = 0.0;

    d_lval = reflection::value_cast<double>(larg);
    d_rval = reflection::value_cast<double>(rarg);

    switch (op_type) {
      case VMOperatorType::Less:
        compare_result = d_lval < d_rval;
        break;
      case VMOperatorType::LessEqual:
        compare_result = d_lval <= d_rval;
        break;
      case VMOperatorType::Greater:
        compare_result = d_lval > d_rval;
        break;
      case VMOperatorType::GreaterEqual:
        compare_result = d_lval >= d_rval;
        break;
      default:
        break;
    }
  } else {
    // int64
    int64_t i_lval = 0, i_rval = 0;

    i_lval = reflection::value_cast<int64_t>(larg);
    i_rval = reflection::value_cast<int64_t>(rarg);

    switch (op_type) {
      case VMOperatorType::Less:
        compare_result = i_lval < i_rval;
        break;
      case VMOperatorType::LessEqual:
        compare_result = i_lval <= i_rval;
        break;
      case VMOperatorType::Greater:
        compare_result = i_lval > i_rval;
        break;
      case VMOperatorType::GreaterEqual:
        compare_result = i_lval >= i_rval;
        break;
      default:
        break;
    }
  }

  target_val = reflection::make_value(compare_result);
}

void EvaluateUnit::DoLogicOperation(VValue& target_val, VMOperatorType op_type, VMEvalGroupType group, const VValue& larg, const VValue& rarg) {
  assert(group == VMEvalGroupType::Logic && "VMEvaluateUnit::do_logic_operation() run to a error pass here!");
  // assert(target_val->value_type == VMValueType::TBool);
  assert(target_val.IsBool());

  bool logic_result = false;
  switch (op_type) {
    case VMOperatorType::And:
      logic_result = reflection::value_cast<bool>(larg) && reflection::value_cast<bool>(rarg);
      break;
    case VMOperatorType::Or:
      logic_result = reflection::value_cast<bool>(larg) || reflection::value_cast<bool>(rarg);
      break;
    case VMOperatorType::Xor:
      logic_result = reflection::value_cast<bool>(larg) ^ reflection::value_cast<bool>(rarg);
      break;
    case VMOperatorType::Not:
      logic_result = !(reflection::value_cast<bool>(larg));
      break;
    default:
      break;
  }

  target_val = reflection::make_value(logic_result);
}

}  // namespace machine
}  // namespace gbf
