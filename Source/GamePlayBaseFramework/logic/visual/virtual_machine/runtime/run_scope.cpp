#include "visual/virtual_machine/runtime/run_scope.hpp"
#include "visual/virtual_machine/runtime/iinstruction.hpp"
#include "visual/virtual_machine/runtime/memory_scope.hpp"

namespace gbf {
namespace machine {

RunScope::RunScope(const VObject& source_object, std::shared_ptr<VCoroutine> coroutine_, std::shared_ptr<NamedMemoryScope> global_mem_scope,
                       std::shared_ptr<MemoryScope> mem_scope) {
  m_source_object = source_object;
  m_coroutine = coroutine_;
  m_global_memory_scope = global_mem_scope;

  m_memory_scope = mem_scope ? mem_scope : std::make_shared<MemoryScope>();
}

RunScope::~RunScope() {}

std::shared_ptr<IInstruction> RunScope::PopInstruction() {
  std::shared_ptr<IInstruction> step;
  if (!(m_instructions_stack.empty())) {
    auto iter = m_instructions_stack.rbegin();
    step = *iter;
    m_instructions_stack.pop_back();
    ////m_finished_steps.push_back(step);
  }

  return step;
}

void RunScope::PushInstruction(std::shared_ptr<IInstruction> instruct) { m_instructions_stack.emplace_back(instruct); }

void RunScope::RemoveInstruction(IInstruction* step_) {
  auto iter = std::find_if(m_instructions_stack.begin(), m_instructions_stack.end(), [step_](const IInstructionPtr& x) { return x.get() == step_; });
  if (iter != m_instructions_stack.end()) {
    m_instructions_stack.erase(iter);
  }
}

bool RunScope::Empty() const noexcept { return m_instructions_stack.empty(); }

}  // namespace machine
}  // namespace gbf
