#pragma once

#include <stdint.h>

#include <string>

#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf
{
	namespace machine
	{
		class VIRTUAL_MACHINE_API IVMStreamReadNode
		{
		public:
			
			virtual IVMStreamReadNode* GetNode(const std::string& prop_name) = 0;

			virtual IVMStreamReadNode* GetArrayElement(size_t index) = 0;

			virtual size_t GetArraySize() = 0;

			virtual bool IsNull() const = 0;

			virtual bool IsArray() const = 0;

			virtual bool IsObject() const = 0;

			virtual bool HasProperty(const std::string& prop_name) = 0;

			virtual std::string GetStringProperty(const std::string& prop_name, std::string default_value = "") = 0;
			virtual bool GetBoolProperty(const std::string& prop_name, bool default_value = false) = 0;
			virtual int GetIntProperty(const std::string& prop_name, int default_value = 0) = 0;
			virtual unsigned GetUintProperty(const std::string& prop_name, unsigned default_value = 0) = 0;
			virtual int64_t GetInt64Property(const std::string& prop_name, int64_t default_value = 0) = 0;
			virtual uint64_t GetUint64Property(const std::string& prop_name, uint64_t default_value = 0) = 0;
			virtual double GetDoubleProperty(const std::string& prop_name, double default_value = 0.0) = 0;

		};
	}
}
