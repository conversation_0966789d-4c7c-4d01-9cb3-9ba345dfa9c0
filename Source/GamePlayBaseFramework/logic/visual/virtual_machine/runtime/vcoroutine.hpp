#pragma once

#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API VCoroutine : public std::enable_shared_from_this<VCoroutine> {
  friend class VScheduler;
 public:
  // Nested Types
  enum class CoroutineState : int {
    Running = 0,
    Idle,   // no instruction on coroutine now
    Yield,  // coroutine is in a yield status
    Wait,   // coroutine is waiting other coroutines
    Exit,   // coroutine is just stopped, can not run again
    Error,  // when has error on it
  };

  enum class StopMode : int {
    StopWhenIdle = 0,
    NoStopMode = 1,
  };

  using URunScopeArray = std::vector<RunScopePtr>;
  ////typedef TGCVector<Instruction*> UVMCroutineStepArray;
  ////using FVMCoroutineFunc = std::function<void(gc::local_ptr<RunScope>)>;

  using CoroutineWeak = std::weak_ptr<VCoroutine>;
  using WeakCoroutineArray = std::vector<CoroutineWeak>;

 public:
  VCoroutine(const VObject& context_object, NamedMemoryScopePtr global_memory_scope, VSchedulerPtr scheduler,
              VCoroutinePtr parent_coroutine);
  virtual ~VCoroutine();

  IInstructionPtr PopInstruction();

  RunScopePtr PushInstruction(IInstructionPtr instruct);

  MemoryScope* GetCurrentMemoryScope() noexcept {
    return GetCurrentMemoryScopeSmartPtr().get();
  }

  MemoryScopePtr GetCurrentMemoryScopeSmartPtr() noexcept;

  void RemoveInstruction(IInstruction* step_);

  void PushRunScope(const VObject& source_object, MemoryScopePtr mem_scope);

  void PopRunScope();

  const VObject& GetContextObject() noexcept { return m_context_object; }

  bool IsOnError() const noexcept { return m_run_state == CoroutineState::Error; }

  void SetIsOnError() noexcept { m_run_state = CoroutineState::Error; }

  CoroutineState DoOneInstruction();

  CoroutineState RunImmediate();

  ////void on_global_event(const ConstString& event_name, size_t out_slot_index, NGValue* param_);

  ////void on_call_function_enter_event(NGValue* param_);

  VCoroutinePtr CreateChildCoroutine();

  NamedMemoryScope* GetGlobalMemoryScope() { return m_global_memory_scope.get(); }

  NamedMemoryScopePtr GetGlobalMemoryScopeSmartPtr() { return m_global_memory_scope; }

  RunScopePtr GetTopScope() const noexcept;

  ////virtual VMEvaluateUnit* get_evaluate_unit() const noexcept = 0;

  void WaitForExit(VCoroutinePtr coroutine_to_exit);

  void YieldForSleep(double sleep_time, PeriodTaskManager* period_task_manager);

  void ResumeYield(bool is_immediate_run);

  void ResumeYieldNextFrame();

  void Release();

  uint32_t GetId() const noexcept { return m_id; }

  ////gc::local_ptr<VMCoroutine> get_root_coroutine() const noexcept { return m_root_coroutine; }

  void AddChildForRoot(VCoroutinePtr child_coroutine);

  VSchedulerPtr GetScheduler() const noexcept { return m_scheduler; }

  void SetEndRunStepStatus(VMRunStepStatus run_status) noexcept { m_end_run_step_status = run_status; }

  VMRunStepStatus GetEndRunStepStatus() const noexcept { return m_end_run_step_status; }

  CoroutineState GetCurrentState() const noexcept { return m_run_state; }

  void OnRunToIdle();
  ////bool is_root_coroutine() const noexcept { return m_parent_coroutine == nullptr; }
 protected:
  ////VMRunScope* new_yield_run_scope(GCObject* source_object, VMMemoryScope* mem_scope);

  void OnExit();
  ////void remove_from_parent();
 protected:
  uint32_t m_id = 0;

  CoroutineState m_run_state = CoroutineState::Yield;

  StopMode m_stop_mode = StopMode::StopWhenIdle;

  // here need not use gc_members
  WeakCoroutineArray m_wait_exit_weak_array;

  WeakCoroutineArray m_child_weak_array;

  WeakCoroutineArray m_all_child_for_root_array;  // use for root coroutine only

  VMRunStepStatus m_end_run_step_status = VMRunStepStatus::Unknown;

  VObject m_context_object;

  ////gc_member_begin();
  NamedMemoryScopePtr           m_global_memory_scope;
  URunScopeArray                m_call_stacks;
  IInstructionPtr               m_last_instruction;

  VCoroutinePtr                 m_parent_coroutine;
  PeriodTaskPtr                 m_delay_period_task;

  VSchedulerPtr                 m_scheduler;
  ////gc_member_end(gc::object_scope);
};

}  // namespace machine
}  // namespace gbf
