#include "visual/virtual_machine/string/cachedstringmanager.h"
#include "visual/virtual_machine/string/cryptotool.h"

namespace gbf {
namespace machine {
static const int kMaxSaveMapStrLength = 40;
//-------------------------------------------------------------------------------------------
CacheStringManager::CacheStringManager() { m_blank_string = add_cache_string(""); }

CacheStringManager::~CacheStringManager() {
  // #if ENABLE_VMCORE_MULTITHREAD_SUPPORT
  // #endif
  remove_cache_string(m_blank_string);

  for (auto iter = m_cache_string_map.begin(); iter != m_cache_string_map.end(); iter++) {
    delete iter->second;
  }
  m_cache_string_map.clear();
}

CachedString* CacheStringManager::add_cache_string(const char* raw_str) {
  size_t len = raw_str == nullptr ? 0 : strlen(raw_str);
  // #if ENABLE_VMCORE_MULTITHREAD_SUPPORT
  std::scoped_lock tmp_lock(m_mutex);
  // #endif

  if (!((raw_str == nullptr || raw_str[0] == '\0') && m_blank_string)) {
    uint64_t hash_code = _hash_string(raw_str, len);
    if (len <= kMaxSaveMapStrLength) {
      auto iter = m_cache_string_map.find(hash_code);
      if (iter != m_cache_string_map.end()) {
        iter->second->add_ref();
        return iter->second;
      }
    }

    CachedString* cache_str = new CachedString(hash_code, raw_str, len);
    cache_str->add_ref();
    if (len <= kMaxSaveMapStrLength) {
      m_cache_string_map.insert(std::make_pair(hash_code, cache_str));
    }

    return cache_str;
  } else {
    m_blank_string->add_ref();
    return m_blank_string;
  }
}

CachedString* CacheStringManager::clone_cache_string(const CachedString* cached_str) {
  CachedString* no_const_str = const_cast<CachedString*>(cached_str);
  // #if ENABLE_VMCORE_MULTITHREAD_SUPPORT
  std::scoped_lock tmp_lock(m_mutex);
  // #endif
  no_const_str->add_ref();
  return no_const_str;
}

void CacheStringManager::remove_cache_string(CachedString* cached_str) {
  // #if ENABLE_VMCORE_MULTITHREAD_SUPPORT
    std::scoped_lock tmp_lock(m_mutex);
  // #endif
  remove_cache_string_unsafe(cached_str);
}

void CacheStringManager::remove_cache_string_unsafe(CachedString* cached_str) {
  CachedString* tmp_str = const_cast<CachedString*>(cached_str);
  tmp_str->remove_ref();
  if (tmp_str->get_ref_count() == 0) {
    if (tmp_str->length() <= kMaxSaveMapStrLength) {
      m_cache_string_map.erase(tmp_str->hash_code());
    }
    delete tmp_str;
  }
}

uint64_t CacheStringManager::_hash_string(const char* raw_str, size_t len) {
  return (uint64_t)CryptoTool::HashString(0, raw_str, len) << 32 | (uint32_t)CryptoTool::HashString(1, raw_str, len);
}
}  // namespace machine
}  // namespace gbf


