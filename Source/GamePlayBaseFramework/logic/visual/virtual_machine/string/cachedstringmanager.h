#pragma once

#include <cstring>
#include <unordered_map>

#include "visual/virtual_machine/string/cachedstring.h"
#include "visual/virtual_machine/vmcore_define.hpp"

////#if ENABLE_VMCORE_MULTITHREAD_SUPPORT
#include <mutex>
////#endif

namespace gbf {
namespace machine {

class VIRTUAL_MACHINE_API CacheStringManager {
 public:
  // custom const char* less operator
  struct char_ptr_lesser {
    bool operator()(char const* a, char const* b) const { return ::strcmp(a, b) < 0; }
  };

  // map has a not changed iterator, so here not used unordered_map(will changed when rehash)
  using StringCachedMap = std::unordered_map<uint64_t, CachedString*>;

 public:
  CacheStringManager();
  ~CacheStringManager();

  CachedString* add_cache_string(const char* raw_str);

  CachedString* clone_cache_string(const CachedString* cached_str);

  CachedString* blank_cache_string() { return m_blank_string; }

  CachedString* blank_cache_string_add_ref() {
    ////#if ENABLE_VMCORE_MULTITHREAD_SUPPORT
   std::scoped_lock tmp_lock(m_mutex);
    ////#endif
    m_blank_string->add_ref();
    return m_blank_string;
  }

  void remove_cache_string(CachedString* cached_str);

  void remove_cache_string_unsafe(CachedString* cached_str);

  static uint64_t _hash_string(const char* raw_str, size_t len);

 private:
  StringCachedMap m_cache_string_map;

  CachedString* m_blank_string = nullptr;

  ////#if ENABLE_VMCORE_MULTITHREAD_SUPPORT
  std::mutex m_mutex;
  ////#endif
};

}  // namespace machine
}  // namespace gbf
