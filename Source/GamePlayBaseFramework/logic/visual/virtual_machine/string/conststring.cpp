#include "visual/virtual_machine/string/conststring.h"

#include <algorithm>

#include "visual/virtual_machine/string/cachedstringmanager.h"
#include "visual/virtual_machine/vmcore_global.hpp"

namespace gbf {
namespace machine {
//-------------------------------------------------------------------------------------------
ConstString::ConstString() : m_cached_str(GCacheStringManager->blank_cache_string_add_ref()) {
  // m_cached_str = GCacheStringManager->blank_cache_string();
  // m_cached_str->add_ref();
}

ConstString::ConstString(const char* raw_str) { m_cached_str = GCacheStringManager->add_cache_string(raw_str); }

ConstString::ConstString(const std::string& str) : ConstString(str.c_str()) {}

ConstString::ConstString(const ConstString& r) { m_cached_str = r.get_cached_str()->clone(); }

ConstString::ConstString(const char* raw_str, size_t offset, size_t count) : ConstString(raw_str, strlen(raw_str), offset, count) {}

ConstString::ConstString(const char* raw_str, size_t raw_len, size_t offset, size_t count) {
  assert(offset <= raw_len);
  size_t needlen = std::min<size_t>(raw_len - offset, count);

  char* tmp_str = (char*)malloc(needlen + 1);
  memcpy(tmp_str, raw_str + offset, needlen);
  tmp_str[needlen] = 0;
  m_cached_str = GCacheStringManager->add_cache_string(tmp_str);
  ::free(tmp_str);
}

ConstString::~ConstString() { free(); }

void ConstString::free() {
  if (m_cached_str) {
    GCacheStringManager->remove_cache_string(m_cached_str);
    m_cached_str = nullptr;
  }
}

ConstString& ConstString::operator=(const std::string& s) { return operator=(s.c_str()); }

ConstString& ConstString::operator=(const char* s) {
  free();
  m_cached_str = GCacheStringManager->add_cache_string(s);
  return *this;
}

ConstString& ConstString::operator=(const ConstString& r) {
  free();
  m_cached_str = r.m_cached_str->clone();
  return *this;
}

////ConstStringArray StringVectorToConstStringArray(const StringVector& strVec)
////{
////	ConstStringArray tmpStrArray;
////	for (const auto& i : strVec)
////	{
////		tmpStrArray.push_back(i);
////	}
////	return tmpStrArray;
////}

////StringVector ConstStringArrayToStringVector(const ConstStringArray& strArray)
////{
////	StringVector tmpStrVec;
////	for (const auto& i : strArray)
////	{
////		tmpStrVec.push_back(i.c_str());
////	}

////	return tmpStrVec;
////}

void ConstString::clear() {
  free();
  m_cached_str = GCacheStringManager->blank_cache_string_add_ref();
  // m_cached_str = GCacheStringManager->blank_cache_string();
  // m_cached_str->add_ref();
}

size_t ConstString::find_first_of(const ConstString& rights, size_t offset) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;
  const char* _Needle = rights.c_str();
  const size_t _Needle_size = rights.length();

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Needle_size != 0 && _Start_at < _Hay_size) {  // room for match, look for it
    const auto _End = _Haystack + _Hay_size;
    for (auto _Match_try = _Haystack + _Start_at; _Match_try < _End; ++_Match_try) {
      if (string_find(_Needle, _Needle_size, *_Match_try)) {
        return (_Match_try - _Haystack);  // found a match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

size_t ConstString::find_first_of(char _Ch, size_t offset /*= 0*/) const noexcept {
  // search [_Haystack, _Haystack + _Hay_size) for _Ch, at/after _Start_at

  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;

  if (_Start_at < _Hay_size) {
    const auto* _Found_at = string_find(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);
    if (_Found_at) {
      return (_Found_at - _Haystack);
    }
  }

  return (static_cast<size_t>(-1));  // (npos) no match
}

size_t ConstString::find_first_not_of(const ConstString& rights, size_t offset) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;
  const char* _Needle = rights.c_str();
  const size_t _Needle_size = rights.length();

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Needle_size != 0 && _Start_at < _Hay_size) {  // room for match, look for it
    const auto _End = _Haystack + _Hay_size;
    for (auto _Match_try = _Haystack + _Start_at; _Match_try < _End; ++_Match_try) {
      if (!string_find(_Needle, _Needle_size, *_Match_try)) {
        return (_Match_try - _Haystack);  // found a match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

size_t ConstString::find_first_not_of(char _Ch, size_t offset /*= 0*/) const noexcept {
  // search [_Haystack, _Haystack + _Hay_size) for _Ch, at/after _Start_at

  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;

  if (_Start_at < _Hay_size) {
    const auto* _Found_at = string_find_not_of(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);
    if (_Found_at) {
      return (_Found_at - _Haystack);
    }
  }

  return (static_cast<size_t>(-1));  // (npos) no match
}

size_t ConstString::find_last_of(const ConstString& rights, size_t offset) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;
  const char* _Needle = rights.c_str();
  const size_t _Needle_size = rights.length();

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Hay_size != 0) {
    for (auto _Match_try = _Haystack + std::min<size_t>(_Start_at, _Hay_size - 1);; --_Match_try) {
      if (string_find(_Needle, _Needle_size, *_Match_try)) {
        return (_Match_try - _Haystack);  // found a match
      }

      if (_Match_try == _Haystack) {
        break;  // at beginning, no more chance for match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

size_t ConstString::find_last_of(char _Ch, size_t offset /*= npos*/) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Hay_size != 0) {
    for (auto _Match_try = _Haystack + std::min<size_t>(_Start_at, _Hay_size - 1);; --_Match_try) {
      if (_Ch == *_Match_try) {
        return (_Match_try - _Haystack);  // found a match
      }

      if (_Match_try == _Haystack) {
        break;  // at beginning, no more chance for match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

size_t ConstString::find_last_not_of(const ConstString& rights, size_t offset) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;
  const char* _Needle = rights.c_str();
  const size_t _Needle_size = rights.length();

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Hay_size != 0) {
    for (auto _Match_try = _Haystack + std::min<size_t>(_Start_at, _Hay_size - 1);; --_Match_try) {
      if (!string_find(_Needle, _Needle_size, *_Match_try)) {
        return (_Match_try - _Haystack);  // found a match
      }

      if (_Match_try == _Haystack) {
        break;  // at beginning, no more chance for match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

size_t ConstString::find_last_not_of(char _Ch, size_t offset /*= npos*/) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  const size_t _Start_at = offset;

  // in [_Haystack, _Haystack + _Hay_size), look for one of [_Needle, _Needle + _Needle_size), at/after _Start_at
  // general algorithm
  if (_Hay_size != 0) {
    for (auto _Match_try = _Haystack + std::min<size_t>(_Start_at, _Hay_size - 1);; --_Match_try) {
      if (_Ch != *_Match_try) {
        return (_Match_try - _Haystack);  // found a match
      }

      if (_Match_try == _Haystack) {
        break;  // at beginning, no more chance for match
      }
    }
  }

  return (static_cast<size_t>(-1));  // no match
}

ConstString ConstString::substr(const size_t _Off /*= 0*/, const size_t _Count /*= npos*/) const noexcept {
  return ConstString(c_str(), length(), _Off, _Count);
}

size_t ConstString::find(const char _Ch, const size_t _Start_at /*= 0*/) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  // search [_Haystack, _Haystack + _Hay_size) for _Ch, at/after _Start_at
  if (_Start_at < _Hay_size) {
    const auto* _Found_at = string_find(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);
    if (_Found_at) {
      return (_Found_at - _Haystack);
    }
  }

  return (static_cast<size_t>(-1));  // (npos) no match
}

size_t ConstString::find(const ConstString& valstr, const size_t _Start_at /*= 0*/) const noexcept {
  const char* _Haystack = c_str();
  const size_t _Hay_size = (size_t)length();
  // search [_Haystack, _Haystack + _Hay_size) for _Ch, at/after _Start_at
  if (_Start_at < _Hay_size) {
    const auto* _Found_at = string_find_str(_Haystack + _Start_at, _Hay_size - _Start_at, valstr.c_str());
    if (_Found_at) {
      return (_Found_at - _Haystack);
    }
  }

  return (static_cast<size_t>(-1));  // (npos) no match
}

ConstString ConstString::operator+(const ConstString& _Right) const {
  char* tmpStr = (char*)malloc(length() + _Right.length() + 1);
  strcpy(tmpStr, c_str());
  strcat(tmpStr, _Right.c_str());
  ConstString ret(tmpStr);
  ::free(tmpStr);
  return ret;
}

ConstString ConstString::operator+(const char* _Right) const {
  char* tmpStr = (char*)malloc(length() + strlen(_Right) + 1);
  strcpy(tmpStr, c_str());
  strcat(tmpStr, _Right);
  ConstString ret(tmpStr);
  ::free(tmpStr);
  return ret;
}

ConstString ConstString::operator+(char _Right) const {
  char* tmpStr = (char*)malloc(length() + 2);
  strcpy(tmpStr, c_str());
  tmpStr[length()] = _Right;
  tmpStr[length() + 1] = '\0';
  ConstString ret(tmpStr);
  ::free(tmpStr);
  return ret;
}

ConstString& ConstString::operator+=(const ConstString& _Right) {
  *this = *this + _Right;
  return *this;
}

ConstString& ConstString::operator+=(const char* _Right) {
  *this = *this + _Right;
  return *this;
}

ConstString& ConstString::operator+=(char _Right) {
  *this = *this + _Right;
  return *this;
}
}  // namespace machine
}  // namespace gbf
