#include "visual/virtual_machine/string/conststringutil.h"
#include <cstdarg>

namespace gbf {
namespace machine {

ConstString ConstStringUtil::trim(const ConstString& str, bool left /*= true*/, bool right /*= true*/) {
  const ConstString delims = " \t\r";
  if (str.length() != 0) {
    size_t left_index = 0;
    size_t right_index = str.length() - 1;

    if (left) left_index = str.find_first_not_of(delims);
    if (right) right_index = str.find_last_not_of(delims);

    return str.substr(left_index, right_index - left_index + 1);
  }

  return str;
}

ConstStringArray ConstStringUtil::split(const ConstString& str, const ConstString& delims /*= "\t\n "*/, unsigned int maxSplits /*= 0*/,
                                        bool preserveDelims /*= false*/) {
  ConstStringArray ret;
  // Pre-allocate some space for performance
  ret.reserve(maxSplits ? maxSplits + 1 : 10);  // 10 is guessed capacity for most case

  unsigned int numSplits = 0;

  // Use STL methods
  size_t start, pos;
  start = 0;
  do {
    pos = str.find_first_of(delims, start);
    if (pos == start) {
      // Do nothing
      start = pos + 1;
    } else if (pos == std::string::npos || (maxSplits && numSplits == maxSplits)) {
      // Copy the rest of the string
      ret.push_back(str.substr(start));
      break;
    } else {
      // Copy up to delimiter
      ret.push_back(str.substr(start, pos - start));

      if (preserveDelims) {
        // Sometimes there could be more than one delimiter in a row.
        // Loop until we don't find any more delims
        size_t delimStart = pos, delimPos;
        delimPos = str.find_first_not_of(delims, delimStart);
        if (delimPos == std::string::npos) {
          // Copy the rest of the string
          ret.push_back(str.substr(delimStart));
        } else {
          ret.push_back(str.substr(delimStart, delimPos - delimStart));
        }
      }

      start = pos + 1;
    }
    // parse up to next real data
    start = str.find_first_not_of(delims, start);
    ++numSplits;

  } while (pos != std::string::npos);

  return std::move(ret);
}

ConstStringArray ConstStringUtil::tokenise(const ConstString& str, const ConstString& singleDelims /*= "\t\n "*/,
                                           const ConstString& doubleDelims /*= "\""*/, unsigned int maxSplits /*= 0*/) {
  ConstStringArray ret;
  // Pre-allocate some space for performance
  ret.reserve(maxSplits ? maxSplits + 1 : 10);  // 10 is guessed capacity for most case

  unsigned int numSplits = 0;
  ConstString delims = singleDelims + doubleDelims;

  // Use STL methods
  size_t start, pos;
  char curDoubleDelim = 0;
  start = 0;
  do {
    if (curDoubleDelim != 0) {
      pos = str.find(curDoubleDelim, start);
    } else {
      pos = str.find_first_of(delims, start);
    }

    if (pos == start) {
      char curDelim = str.at(pos);
      if (doubleDelims.find_first_of(curDelim) != std::string::npos) {
        curDoubleDelim = curDelim;
      }
      // Do nothing
      start = pos + 1;
    } else if (pos == std::string::npos || (maxSplits && numSplits == maxSplits)) {
      if (curDoubleDelim != 0) {
        // Missing closer. Warn or throw exception?
      }
      // Copy the rest of the string
      ret.push_back(str.substr(start));
      break;
    } else {
      if (curDoubleDelim != 0) {
        curDoubleDelim = 0;
      }

      // Copy up to delimiter
      ret.push_back(str.substr(start, pos - start));
      start = pos + 1;
    }
    if (curDoubleDelim == 0) {
      // parse up to next real data
      start = str.find_first_not_of(singleDelims, start);
    }

    ++numSplits;

  } while (start != std::string::npos);

  return std::move(ret);
}

ConstString ConstStringUtil::toLowerCase(const ConstString& str) {
  size_t len = str.length();
  char* temp = (char*)malloc(len + 1);
  temp[len] = '\0';
  const char* raw_str = str.c_str();
  for (size_t i = 0; i < str.length(); i++) {
    temp[i] = (char)tolower(raw_str[i]);
  }
  ConstString ret(temp);
  free(temp);

  return ret;
}

ConstString ConstStringUtil::toUpperCase(const ConstString& str) {
  size_t len = str.length();
  char* temp = (char*)malloc(len + 1);
  temp[len] = '\0';
  const char* raw_str = str.c_str();
  for (size_t i = 0; i < str.length(); i++) {
    temp[i] = (char)toupper(raw_str[i]);
  }
  ConstString ret(temp);
  free(temp);

  return ret;
}

ConstString ConstStringUtil::toTitleCase(const ConstString& str) {
  assert(str.length() > 0);
  size_t len = str.length();
  char* temp = (char*)malloc(len + 1);
  ////temp[len] = '\0';

  const char* raw_str = str.c_str();
  strcpy(temp, raw_str);

  temp[0] = (char)toupper(raw_str[0]);
  for (size_t i = 1; i + 1 < len; i++) {
    if (raw_str[i] == ' ') {
      temp[i + 1] = (char)toupper(raw_str[i + 1]);
    }
  }

  ConstString ret(temp);
  free(temp);
  return ret;
}

bool ConstStringUtil::startsWith(const ConstString& str, const ConstString& pattern, bool lowerCase /*= true*/) {
  size_t thisLen = str.length();
  size_t patternLen = pattern.length();
  if (thisLen < patternLen || patternLen == 0) return false;

  if (lowerCase) {
    for (size_t i = 0; i < patternLen; i++) {
      if (tolower(pattern[i]) != tolower(str[i])) {
        return false;
      }
    }
    return true;
  } else {
    for (size_t i = 0; i < patternLen; i++) {
      if (pattern[i] != str[i]) {
        return false;
      }
    }
    return true;
  }
}

bool ConstStringUtil::endsWith(const ConstString& str, const ConstString& pattern, bool lowerCase /*= true*/) {
  size_t thisLen = str.length();
  size_t patternLen = pattern.length();
  if (thisLen < patternLen || patternLen == 0) return false;
  size_t thisBegin = thisLen - patternLen;
  if (lowerCase) {
    for (size_t i = 0; i < patternLen; i++) {
      if (tolower(pattern[i]) != tolower(str[thisBegin + i])) {
        return false;
      }
    }
    return true;
  } else {
    for (size_t i = 0; i < patternLen; i++) {
      if (pattern[i] != str[thisBegin + i]) {
        return false;
      }
    }
    return true;
  }
}

ConstString ConstStringUtil::standardisePath(const ConstString& path, bool needAddLastSeparator) {
  assert(path.length() > 0);

  size_t len = path.length();
  char* temp = (char*)malloc(len + 2);
  temp[len + 1] = '\0';

  const char* raw_str = path.c_str();
  strcpy(temp, raw_str);

  for (size_t i = 0; i < len; i++) {
    if (raw_str[i] == '\\') {
      temp[i] = '/';
    }
  }

  if (needAddLastSeparator && raw_str[len - 1] != '/') {
    temp[len] = '/';
  }

  ConstString ret(temp);
  free(temp);

  return ret;
}

void ConstStringUtil::splitFilename(const ConstString& qualifiedName, ConstString& outBasename, ConstString& outPath) {
  size_t i = qualifiedName.find_last_of("\\/");

  if (i == ConstString::npos) {
    outPath.clear();
    outBasename = qualifiedName;
  } else {
    outBasename = qualifiedName.substr(i + 1, qualifiedName.length() - i - 1);
    outPath = qualifiedName.substr(0, i + 1);
  }
}

void ConstStringUtil::splitFullFilename(const ConstString& qualifiedName, ConstString& outBasename, ConstString& outExtention, ConstString& outPath) {
  ConstString fullName;
  splitFilename(qualifiedName, fullName, outPath);
  splitBaseFilename(fullName, outBasename, outExtention);
}

void ConstStringUtil::splitBaseFilename(const ConstString& fullName, ConstString& outBasename, ConstString& outExtention) {
  size_t i = fullName.find_last_of(".");
  if (i == ConstString::npos) {
    outExtention.clear();
    outBasename = fullName;
  } else {
    outExtention = fullName.substr(i + 1);
    outBasename = fullName.substr(0, i);
  }
}

ConstString ConstStringUtil::removeTrailingPathSeparator(const ConstString& path) {
  size_t len = path.length();
  if (len > 0) {
    auto sep = path[len - 1];
    if (sep == '/' || sep == '\\') {
      return path.substr(0, len - 1);
    }
  }

  return path;
}

bool ConstStringUtil::match(const ConstString& str, const ConstString& pattern, bool caseSensitive /*= true*/) {
  ConstString tmpStr = str;
  ConstString tmpPattern = pattern;
  if (!caseSensitive) {
    tmpStr = toLowerCase(tmpStr);
    tmpPattern = toLowerCase(tmpPattern);
  }

  size_t strIt = 0;
  size_t patIt = 0;
  size_t lastWildCardIt = tmpPattern.length();
  while (strIt != tmpStr.length() && patIt != tmpPattern.length()) {
    if (tmpPattern[patIt] == '*') {
      lastWildCardIt = patIt;
      // Skip over looking for next character
      ++patIt;
      if (patIt == tmpPattern.length()) {
        // Skip right to the end since * matches the entire rest of the string
        strIt = tmpStr.length();
      } else {
        // scan until we find next pattern character
        while (strIt != tmpStr.length() && tmpStr[strIt] != tmpPattern[patIt]) ++strIt;
      }
    } else {
      if (tmpPattern[patIt] != tmpStr[strIt]) {
        if (lastWildCardIt != tmpPattern.length()) {
          // The last wildcard can match this incorrect sequence
          // rewind pattern to wildcard and keep searching
          patIt = lastWildCardIt;
          lastWildCardIt = tmpPattern.length();
        } else {
          // no wildwards left
          return false;
        }
      } else {
        ++patIt;
        ++strIt;
      }
    }
  }
  // If we reached the end of both the pattern and the string, we succeeded
  if (patIt == tmpPattern.length() && strIt == tmpStr.length()) {
    return true;
  } else {
    return false;
  }
}

ConstString ConstStringUtil::replaceAll(const ConstString& source, const ConstString& replaceWhat, const ConstString& replaceWithWhat) {
  assert(replaceWhat.length() > 0);
  if (source.length() > 0) {
    size_t maxNeedLen = source.length() + 1;
    if (replaceWithWhat.length() > replaceWhat.length()) {
      maxNeedLen += (source.length() / replaceWhat.length()) * (replaceWithWhat.length() - replaceWhat.length());
    }

    const char* srcBuf = source.c_str();
    char* tmpBuf = (char*)malloc(maxNeedLen);

    size_t lastFillPos = 0;

    size_t srcCopyStart = 0;
    size_t curFindPos = 0;
    while (1) {
      curFindPos = source.find(replaceWhat, curFindPos);

      if (curFindPos == std::string::npos) {
        curFindPos = source.length() - 1;

        if (curFindPos != srcCopyStart) {
          const size_t needCopyLen = curFindPos - srcCopyStart + 1;
          memcpy(tmpBuf + lastFillPos, srcBuf + srcCopyStart, needCopyLen);
          lastFillPos += needCopyLen;
          srcCopyStart += needCopyLen;
        }
        break;
      } else {
        size_t needCopyLen = curFindPos - srcCopyStart;
        curFindPos += replaceWhat.length();

        if (needCopyLen > 0) memcpy(tmpBuf + lastFillPos, srcBuf + srcCopyStart, needCopyLen);

        srcCopyStart += replaceWhat.length() + needCopyLen;
        lastFillPos += needCopyLen;

        if (replaceWithWhat.length() > 0) {
          memcpy(tmpBuf + lastFillPos, replaceWithWhat.c_str(), replaceWithWhat.length());
          lastFillPos += replaceWithWhat.length();
        }
      }
    }
    tmpBuf[lastFillPos] = '\0';
    ConstString retStr(tmpBuf);
    free(tmpBuf);

    return retStr;
  } else {
    return ConstString();
  }
}

ConstString ConstStringUtil::format(const char* format, ...) {
  ConstString retstr;

  va_list ap;
  va_start(ap, format);

  char* buf = (char*)malloc(kMaxFormatBufferLength);
  if (buf != nullptr) {
    vsnprintf(buf, kMaxFormatBufferLength, format, ap);
    ////int ret = vsnprintf(buf, kMaxFormatBufferLength, format, ap);
    ////#ifdef CORE_DEBUG_MODE
    ////				// <0 means error; >=MAX means not enough buffer, only partial data was written.
    ////				if (ret < 0 || ret >= kMaxFormatBufferLength)
    ////				{
    ////					printf("StringUtil::format(%s) overflow!!!", format);
    ////					throw RStudio::InvalidParametersException(0, "overflow", "StringUtil::format", __FILE__, __LINE__);
    ////				}
    ////#else
    ////				CORE_UNUSED_VAR(ret);
    ////#endif
    retstr = buf;
    free(buf);
  }
  va_end(ap);

  return retstr;
}

ConstStringArray ConstStringUtil::breakPaths(const ConstString& path_info) {
  ConstString tmp_path_info = replaceAll(path_info, "\\", "/");
  ConstStringArray split_paths;
  if (tmp_path_info.size() > 0) {
    split_paths = ConstStringUtil::split(tmp_path_info, "/");
  }
  return split_paths;
}

ConstString ConstStringUtil::combinePath(const ConstString& path1, const ConstString& path2) {
  ConstStringArray paths1 = std::move(breakPaths(path1));
  ConstStringArray paths2 = std::move(breakPaths(path2));

  for (size_t i = 0; i < paths2.size(); i++) {
    if (paths2[i] == "..") {
      paths1.pop_back();
    } else if (paths2[i] != ".") {
      paths1.push_back(paths2[i]);
    }
  }

  return combineBreakPaths(paths1);
}

ConstString ConstStringUtil::combineBreakPaths(const ConstStringArray& breakPathList) {
  char* tmpBuf = (char*)malloc(1024);
  size_t fillOffset = 0;

  for (size_t i = 0; i < breakPathList.size(); i++) {
    if (i > 0 && fillOffset > 0 && tmpBuf[fillOffset - 1] != '/') {
      tmpBuf[fillOffset] = '/';
      fillOffset++;
    }

    memcpy(tmpBuf + fillOffset, breakPathList[i].c_str(), breakPathList[i].size());
    fillOffset += breakPathList[i].size();
  }

  assert(fillOffset < 1024);
  tmpBuf[fillOffset] = '\0';

  ConstString ret(tmpBuf);
  free(tmpBuf);

  return ret;
}

}  // namespace machine
}  // namespace gbf
