#pragma once

#include <type_traits>
#include "visual/virtual_machine/vmcore_define.hpp"

////#include "memory/gc/gc.hpp"
////
////#include "visual/virtual_machine/string/conststring.h"

#include "rapidjson/document.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamreadnode.h"
#include "visual/virtual_machine/runtime/serialize/ivmstreamwriter.h"
#include "reflection/vtable/object_vtable.hpp"

namespace gbf {
namespace machine {

    class VIRTUAL_MACHINE_API VValueConverter
    {
    public:
        using ID = reflection::TypeId;
        virtual void Serialize(const VValue& value, IVMStreamWriter& writer) = 0;
        virtual gbf::machine::VValue Deserialize(IVMStreamReadNode& from_data) = 0;
        virtual ID GetID() = 0;
    };

    class VIRTUAL_MACHINE_API VValueConverterFactory
    {
    public:
        using ID = VValueConverter::ID;

        static VValueConverter* Get(ID id)
        {
            if (auto itr = sConverters.find(id); itr != sConverters.end())
            {
                return itr->second.get();
            }
            return nullptr;
        }

        template<class T, std::enable_if_t<std::is_pointer_v<T>, int> = 0>
        static VValueConverter* Get()
        {
            using PlainType = std::remove_cv_t<std::remove_reference_t<T>>;
            auto id = reflection::query_meta_class<PlainType>()->id();
            return Get(id);
        }

        static void Register(std::unique_ptr<VValueConverter> converter)
        {
            if (converter == nullptr)
            {
                return;
            }

            auto id = converter->GetID();
            sConverters[id] = std::move(converter);
        }

    private:
        static std::unordered_map<ID, std::unique_ptr<VValueConverter>> sConverters;
    };

    class VIRTUAL_MACHINE_API VValueUtil {
 public:
  static VValue CreateDefaultValueByType(VValueKind kind);

  static void SaveVValueToJsonMap(const VValue& value, IVMStreamWriter& writer);
  static VValue CreateVValueFromJsonMap(IVMStreamReadNode& json_value);

  static VValue CreateVValueFromString(VValueKind kind, const std::string& valstr);

  static VValue CreateVValueFromJsonString(const std::string& jsonstr);  

  static std::string ValueToJsonStr(const machine::VValue& value);

  static VValue PromoteTo(const VValue& src, VValueKind target_kind);

  static VValue PromoteToInteger(const VValue& src);

  static VValue PromoteToReal(const VValue& src);

  static VValue PromoteToString(const VValue& src);

  static const reflection::MetaClass* ValueKindToMetaClass(machine::VValueKind _value_type);

  static reflection::TypeId ValueKindToTypeId(machine::VValueKind _value_type);
};

}  // namespace machine

}  // namespace gbf