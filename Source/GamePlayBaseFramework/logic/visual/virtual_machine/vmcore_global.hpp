#pragma once

#include "visual/virtual_machine/vmcore_define.hpp"

namespace gbf
{
	namespace machine
	{
		class CacheStringManager;
		////class GCManager;
		////class MetaInfoManager;
	}
}

VIRTUAL_MACHINE_API extern gbf::machine::CacheStringManager* GCacheStringManager;
////RSTUDIO_VMCORE_API extern rstudio::vmcore::GCManager* GGCManager;
////RSTUDIO_VMCORE_API extern rstudio::vmcore::MetaInfoManager* GMetaInfoManager;

