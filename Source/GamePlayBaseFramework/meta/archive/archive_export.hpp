#pragma once

#include "platform.hpp"

//----------------------------------------------------------------------------
// Windows Settings
#if GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT

  // If we're not including this from a client build, specify that the stuff
  // should get exported. Otherwise, import it.
  ////#	if !defined(GBF_CORE_STATIC_LIB)
  #if defined(GBF_ARCHIVE_EXPORTS)
    #define GBF_ARCHIVE_API __declspec(dllexport)
  #else
    #define GBF_ARCHIVE_API __declspec(dllimport)
  #endif
////#	else
////#		define GBF_ARCHIVE_API
////#	endif

#else
  // Linux/Apple/iOS/Android Settings
  // Enable GCC symbol visibility
  #if defined(GBF_ARCHIVE_EXPORTS)
    #define GBF_ARCHIVE_API __attribute__((visibility("default")))
  #else
    #define GBF_ARCHIVE_API
  #endif

#endif  // GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WIN32 || GBF_CORE_PLATFORM == GBF_CORE_PLATFORM_WINRT
