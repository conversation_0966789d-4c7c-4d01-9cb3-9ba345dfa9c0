#include "archive/coder/coder_binary.h"

#include "core/error/errors.hpp"
#include "core/utils/string_util.h"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/vtable/array_vtable.hpp"
#include "reflection/meta/array_property.hpp"
#include "reflection/objects/make_array_object.hpp"


#include "archive/protobuf/pb_common.h"
#include "archive/protobuf/pb_decode.h"
#include "archive/protobuf/pb_encode.h"
#include "archive/protobuf/pb_pack_tool.hpp"

#include "archive/protobuf/cxx/cxx_decoder.hpp"
#include "archive/protobuf/cxx/cxx_encoder.hpp"

#include "archive/coder/coder_legacy.h"
#include "reflection/meta/meta_array.hpp"

#include "math/vector3.h"

namespace gbf {
namespace reflection {

//-------------------------------------------------------------------------------------
bool CoderBinary::WriteFieldTagInfo(ByteBufferWriter& buf, const CoderFieldHead& fieldHead, size_t& outHeadWritePos) {
  outHeadWritePos = buf.WritePosition();
  buf.Append((const uint8_t*)&fieldHead, sizeof(fieldHead));
  return true;
}

bool CoderBinary::FixedRightFieldHeadInfo(ByteBufferWriter& buf, CoderFieldHead& fieldHead, size_t headPos) {
  int startFieldContentOffset = headPos + sizeof(fieldHead);

  size_t lastPos = buf.WritePosition();
  fieldHead.dataSize = (int)lastPos - startFieldContentOffset;

  // flush new head info
  buf.WritePosition(headPos);
  buf.Append((const uint8_t*)&fieldHead, sizeof(fieldHead));

  // restore last write postion
  buf.WritePosition(lastPos);

  return true;
}
//-------------------------------------------------------------------------------------

////bool MetaArchiveToolBinary::WriteUserObject(byte_buffer_writer& buf, const UserObject& uo, ArchivePackMode packMode)
////{
////	switch (packMode)
////	{
////	case gbf::reflection::ArchivePackMode::NoCompressMode:
////		return WriteUserObjectLogicMode(buf, uo);
////	case gbf::reflection::ArchivePackMode::PbMode:
////		return WriteUserObjectPbMode(buf, uo);
////	default:
////		GBF_ERROR(NotImplementError("Not support pack mode here!"));
////		break;
////	}
////	return false;
////}

////bool MetaArchiveToolBinary::ReadUserObject(byte_buffer_view& buf, TypeId typeId, ArchivePackMode packMode,
///UserObject& outUo)
////{
////	switch (packMode)
////	{
////	case gbf::reflection::ArchivePackMode::NoCompressMode:
////		return ReadUserObjectLogicMode(buf, typeId, outUo);
////	case gbf::reflection::ArchivePackMode::PbMode:
////		return ReadUserObjectPbMode(buf, typeId, outUo);
////	default:
////		GBF_ERROR(NotImplementError("Not support pack mode here!"));
////		break;
////	}
////	return false;
////}

bool CoderBinary::WriteArgsNoCompressMode(ByteBufferWriter& buf, Args&& args) {
  CoderFieldHead argValueHead;
  argValueHead.metaValueElementType = reflection::ValueKind::kNone;
  for (size_t i = 0; i < args.GetCount(); i++) {
    const auto& val = args[i];
    argValueHead.tag_number = (int)i + 1;
    argValueHead.dataSize = 0;
    reflection::ValueKind kind = val.kind();
    argValueHead.metaValueType = kind;

    size_t headPos = 0;
    WriteFieldTagInfo(buf, argValueHead, headPos);

    switch (argValueHead.metaValueType) {
      case reflection::ValueKind::kNone: {
        // Do nothing here~
      } break;
      case reflection::ValueKind::kBoolean:
      case reflection::ValueKind::kInteger:
      case reflection::ValueKind::kReal:
      case reflection::ValueKind::kString:
      case reflection::ValueKind::kEnum: {
        WriteFieldValueNoCompressMode(buf, val);
      } break;
      case ValueKind::kUser: {
        const auto& uo = value_ref_as<UserObject>(val);
        // We need write id first for user
        std::string className;
        std::string_view fullName = uo.GetClass().name();
        size_t pos = fullName.find("protobuf::");
        if (pos == fullName.npos) {
          className = std::string(fullName);
        } else {
          className = std::string(fullName.substr(8));
        }
        buf.GetLittleEndianStream() << className;
        WriteUserObjectNoCompressMode(buf, uo);
      } break;
      case ValueKind::kArray: {
        const auto& ao = value_ref_as<ArrayObject>(val);
        argValueHead.metaValueElementType = ao.GetElementKind();
        // We need write array meta id for ArrayObject
        buf.GetLittleEndianStream() << (uint64_t)ao.GetMetaId();  // only top level need this
        WriteArrayObjectNoCompressMode(buf, ao, ao.GetMetaId());
      } break;
      default:
        GBF_ERROR(MetaObjectWriteError("WriteFieldValueNoCompressMode(), Can not reach here!"));
    }

    FixedRightFieldHeadInfo(buf, argValueHead, headPos);
  }
  return true;
}

bool CoderBinary::ReadArgsNoCompressMode(ByteBufferView& buf, Args& fillArgs, size_t totalArgs) {
  CoderFieldHead fieldHead;
  while (buf.NotReadSize() > 0) {
    if (buf.NotReadSize() < sizeof(CoderFieldHead)) {
      GBF_ERROR(MetaObjectReadError("Not enough size left for ArchiveFieldHead!"));
    }

    buf.Read((uint8_t*)&fieldHead, sizeof(fieldHead));
    if (buf.NotReadSize() < fieldHead.dataSize) return false;

    ByteBufferView itembuf(buf.ReadPtr(), fieldHead.dataSize);
    buf.ReadSkip(fieldHead.dataSize);

    uint64_t typeId = 0;
    Value val = Value::nothing;
    switch (fieldHead.metaValueType) {
      case reflection::ValueKind::kNone: {
        // Do nothing here~
      } break;
      case reflection::ValueKind::kBoolean:
      case reflection::ValueKind::kInteger:
      case reflection::ValueKind::kReal:
      case reflection::ValueKind::kString:
      case reflection::ValueKind::kEnum: {
        val = ReadValueAsTypeFromBufferNoCompressMode(itembuf, fieldHead.metaValueType, typeId);

      } break;
      case ValueKind::kUser: {
        UserObject uo;
        std::string fullName;
        std::string_view className;
        itembuf.GetLittleEndianStream() >> className;  // read name id first
        size_t pos = className.find("::");
        if (pos == className.npos) {
          fullName = "protobuf::" + std::string(className);
        } else {
          fullName = std::string(className);
        }
        const MetaClass* metaClass = query_meta_class_by_name(fullName);
        if (metaClass == nullptr) {
          std::string errorMsg = StringUtil::Format("can't find metaname. fullName=%s!", fullName.c_str());
          GBF_ERROR(MetaObjectReadError(errorMsg));
          return false;
        }
        ReadUserObjectNoCompressMode(itembuf, metaClass->id(), uo);
        val = gbf::reflection::make_value(uo);
      } break;
      case ValueKind::kArray: {
        ArrayObject ao;
        itembuf.GetLittleEndianStream() >> typeId;  // read meta type id first
        ReadArrayObjectNoCompressMode(itembuf, ao, fieldHead.metaValueElementType, typeId);
        val = gbf::reflection::make_value(ao);
      } break;
      default:
        GBF_ERROR(MetaObjectWriteError("WriteFieldValueNoCompressMode(), Can not reach here!"));
    }

    fillArgs += val;
  }

  return true;
}

bool CoderBinary::WriteArgsLegacyMode(ByteBufferWriter& buf, Args&& args) {
  return CoderLegacy::WriteArgs(buf, std::move(args));
}

bool CoderBinary::ReadArgsLegacyMode(ByteBufferView& buf, Args& fillArgs, size_t totalArgs) {
  return CoderLegacy::ReadArgs(buf, fillArgs, totalArgs);
}

bool CoderBinary::WriteArgsPbMode(ByteBufferWriter& buf, Args&& args) {
  if (args.GetCount() == 0) {
    // No content need write, just return.
    return true;
  }

  if (args.GetCount() > 1) {
    GBF_ERROR(NotImplementError("Pb mode only support 1 argument!"));
    return false;
  }

  if (args[0].kind() != ValueKind::kUser) {
    GBF_ERROR(NotImplementError("Pb mode only support UserObject!"));
    return false;
  }

  const auto& uo = value_ref_as<UserObject>(args[0]);
  buf.GetLittleEndianStream() << (uint64_t)uo.GetClass().id();
  return WriteUserObjectPbMode(buf, uo);
}

bool CoderBinary::ReadArgsPbMode(ByteBufferView& buf, Args& fillArgs, size_t totalArgs) {
  if (totalArgs == 0) {
    // No content need write, just return.
    return true;
  }
  if (totalArgs > 1) {
    GBF_ERROR(NotImplementError("Pb mode only support 1 argument!"));
    return false;
  }

  uint64_t typeId = 0;
  buf.GetLittleEndianStream() >> typeId;
  UserObject uo;
  bool readSuc = ReadUserObjectPbMode(buf, typeId, uo);
  fillArgs += gbf::reflection::make_value(uo);
  return readSuc;
}

bool CoderBinary::WriteArrayObjectNoCompressMode(ByteBufferWriter& buf, const ArrayObject& object,
                                                 uint64_t metaArrayTypeId) {
  CoderArrayObjectHead ahead;
  ahead.metaArrayTypeId = metaArrayTypeId;
  auto arrayCount = object.GetSize();
  ahead.arrayCount = (uint16_t)arrayCount;
  buf.Append((const uint8_t*)&ahead, sizeof(ahead));

  // Iterate over the array elements
  for (size_t j = 0; j < arrayCount; ++j) {
    // Name not need here~~
    WriteFieldValueNoCompressMode(buf, object.GetElement(j));
  }
  return true;
}

bool CoderBinary::ReadArrayObjectNoCompressMode(ByteBufferView& buf, ArrayObject& ao, ValueKind elementKind,
                                                uint64_t metaArrayTypeId) {
  CoderArrayObjectHead ahead;
  buf.Read((uint8_t*)&ahead, sizeof(ahead));

  const auto* meta_arr = gbf::reflection::query_meta_array_by_id(ahead.metaArrayTypeId);
  if (GBF_UNLIKELY(meta_arr == nullptr)) {
    GBF_ERROR(MetaObjectReadError(
        StringUtil::Format("MetaBinaryArchiveTool::ReadArrayObjectImpl() can not find valid meta type! type id:%d", ahead.metaArrayTypeId)));
  }
  ao = gbf::reflection::make_array_object_by_id(ahead.metaArrayTypeId);
  if (GBF_UNLIKELY(!ao)) {
    GBF_ERROR(MetaObjectReadError(
        StringUtil::Format("MetaBinaryArchiveTool::ReadArrayObjectImpl() construct array object failed! type name:%d",
                           ahead.metaArrayTypeId)));
  }
  ao.SetSize(ahead.arrayCount);

  for (int i = 0; i < ahead.arrayCount; i++) {
    auto val = ReadValueAsTypeFromBufferNoCompressMode(buf, elementKind, meta_arr->GetOwnedVtable()->element_type_id_);
    ao.SetElement(i, val);
  }
  return true;
}

bool CoderBinary::WriteUserObjectPbMode(ByteBufferWriter& buf, const UserObject& uo) {
  if (GBF_UNLIKELY(uo.IsEmpty())) {
    // write nothing, just return
    return true;
  }

  const MetaClass& cls = uo.GetClass();
  TypeId typeId = cls.id();

  const auto* metaMessage = protobuf::PbQueryMetaMessage(typeId);
  if (GBF_LIKELY(metaMessage != nullptr)) {
    auto ret = protobuf::LurapbPackTool::PackToBuffer(metaMessage, uo.GetPointer(), buf);
    if (!ret.issuc) {
      GBF_ERROR(MetaObjectReadError(ret.errmsg));
    }
    return ret.issuc;
  } else {
    return protobuf::cxx::Encoder::EncodeUserObject(buf, uo);
  }

  return false;
}

bool CoderBinary::ReadUserObjectPbMode(ByteBufferView& buf, TypeId typeId, UserObject& uo) {
  const auto* metaMessage = protobuf::PbQueryMetaMessage(typeId);
  if (GBF_LIKELY(metaMessage != nullptr)) {
    const MetaClass* cls = (const MetaClass*)(metaMessage->extra_reflection_data);
    uo = cxx::Create(*cls);
    if (GBF_LIKELY(uo.IsValid())) {
      auto ret = protobuf::LurapbPackTool::UnpackFromBuffer(metaMessage, uo.GetPointer(), buf);
      if (ret.issuc) {
        return true;
      }
      GBF_ERROR(MetaObjectReadError(ret.errmsg));
    }

  } else {
    // Try to just use class
    const MetaClass* cls = query_meta_class_by_id(typeId);
    if (GBF_LIKELY(cls != nullptr)) {
      if (protobuf::cxx::Decoder::DecodeUserObject(buf, *cls, uo)) {
        return true;
      }
    }
  }
  return false;
}

bool CoderBinary::WriteUserObjectNoCompressMode(ByteBufferWriter& buf, const UserObject& uo) {
  const auto& metaclass = uo.GetClass();

  std::vector<const Property*> needFields;
  CoderHelper::FilterFields(metaclass, uo, needFields);

  for (auto& needField : needFields) {
    const auto& property = *needField;

    if (property.kind() == ValueKind::kArray) {
      auto const& arrayProperty = dynamic_cast<const reflection::ArrayProperty&>(property);
      auto itemKind = arrayProperty.element_type();

      CoderFieldHead fieldHead;
      fieldHead.dataSize = 0;
      fieldHead.tag_number = property.tag_number();
      fieldHead.metaValueType = property.kind();
      fieldHead.metaValueElementType = ValueKind::kNone;
      size_t headPos = 0;
      fieldHead.metaValueElementType = arrayProperty.element_type();

      const size_t count = arrayProperty.Size(uo);
      // Iterate over the array elements
      if (itemKind == ValueKind::kUser || itemKind == ValueKind::kString) {
        // user & string is dynamic size
        for (size_t j = 0; j < count; ++j) {
          WriteFieldTagInfo(buf, fieldHead, headPos);
          WriteFieldValueNoCompressMode(buf, arrayProperty.Get(uo, j));
          FixedRightFieldHeadInfo(buf, fieldHead, headPos);
        }
      } else {
        // Compress mode use here(only one field here)
        WriteFieldTagInfo(buf, fieldHead, headPos);

        // Just encode one by one
        for (size_t j = 0; j < count; ++j) {
          // Name not need here~~
          WriteFieldValueNoCompressMode(buf, arrayProperty.Get(uo, j));
        }

        FixedRightFieldHeadInfo(buf, fieldHead, headPos);
      }
    } else {
      // Write head first
      CoderFieldHead fieldHead;
      fieldHead.dataSize = 0;
      fieldHead.tag_number = property.tag_number();
      fieldHead.metaValueType = property.kind();
      fieldHead.metaValueElementType = ValueKind::kNone;

      size_t headPos = 0;
      WriteFieldTagInfo(buf, fieldHead, headPos);

      WriteFieldValueNoCompressMode(buf, property.Get(uo));

      FixedRightFieldHeadInfo(buf, fieldHead, headPos);
    }
  }
  return true;
}

bool CoderBinary::ReadUserObjectNoCompressMode(ByteBufferView& buf, TypeId typeId, UserObject& uo) {
  const auto* metaClass = reflection::query_meta_class_by_id(typeId);
  if (metaClass == nullptr) {
    return false;
  }

  uo = cxx::Create(*metaClass);
  if (uo.IsEmpty()) {
    return false;
  }

  CoderFieldHead fieldHead;
  while (buf.NotReadSize() > 0) {
    if (buf.NotReadSize() < sizeof(CoderFieldHead)) {
      GBF_ERROR(MetaObjectReadError("Not enough size left for ArchiveFieldHead!"));
    }

    buf.Read((uint8_t*)&fieldHead, sizeof(fieldHead));
    if (buf.NotReadSize() < fieldHead.dataSize) return false;

    ByteBufferView fieldbuf(buf.ReadPtr(), fieldHead.dataSize);

    bool readFieldSuc = ReadFieldValueNoCompressMode(fieldbuf, fieldHead.metaValueType, fieldHead.metaValueElementType,
                                                     fieldHead.tag_number, *metaClass, uo);
    if (!readFieldSuc) {
      return false;
    }
    buf.ReadSkip(fieldHead.dataSize);
  }

  return true;
}

bool CoderBinary::WriteFieldValueNoCompressMode(ByteBufferWriter& buf, const Value& val) {
  auto kind = val.kind();
  switch (kind) {
    case reflection::ValueKind::kNone: {
      // Do nothing here~
    } break;
    case reflection::ValueKind::kBoolean: {
      buf.GetLittleEndianStream() << value_cast<bool>(val);
    } break;
    case reflection::ValueKind::kInteger: {
      buf.GetLittleEndianStream() << value_cast<int64_t>(val);
    } break;
    case reflection::ValueKind::kReal: {
      buf.GetLittleEndianStream() << value_cast<double>(val);
    } break;
    case reflection::ValueKind::kString: {
      auto sv = value_cast<std::string_view>(val);
      buf.GetLittleEndianStream() << sv;
    } break;
    case reflection::ValueKind::kEnum: {
      buf.GetLittleEndianStream() << value_cast<int>(val);
    } break;
    case ValueKind::kUser: {
      // recurse
      WriteUserObjectNoCompressMode(buf, value_ref_as<UserObject>(val));
    } break;
    case ValueKind::kArray:
    default:
      GBF_ERROR(MetaObjectWriteError("WriteFieldValueNoCompressMode(), Can not reach here!"));
  }
  return true;
}

bool CoderBinary::ReadFieldValueNoCompressMode(ByteBufferView& buf, ValueKind bigType, ValueKind detailType,
                                               uint16_t tagNumber, const MetaClass& metaClass, UserObject& uo) {
  const auto* prop = metaClass.GetPropertyByTag(tagNumber);
  if (prop == nullptr) return false;
  if (prop->kind() != bigType) {
    return false;
  }

  switch (bigType) {
    case gbf::reflection::ValueKind::kNone:
      // Do nothing
      break;
    case gbf::reflection::ValueKind::kBoolean:
    case gbf::reflection::ValueKind::kInteger:
    case gbf::reflection::ValueKind::kReal:
    case gbf::reflection::ValueKind::kString:
    case gbf::reflection::ValueKind::kEnum:
    case gbf::reflection::ValueKind::kUser: {
      auto val = ReadValueAsTypeFromBufferNoCompressMode(buf, bigType, prop->type_index());
      prop->Set(uo, val);
    } break;
    case gbf::reflection::ValueKind::kArray: {
      auto const& arrayProperty = dynamic_cast<const reflection::ArrayProperty&>(*prop);
      if (detailType != arrayProperty.element_type()) return false;

      if (detailType == ValueKind::kUser || detailType == ValueKind::kString) {
        int tmpcount = arrayProperty.Size(uo);
        auto val = ReadValueAsTypeFromBufferNoCompressMode(buf, detailType, arrayProperty.element_type_index());
        arrayProperty.ReSize(uo, tmpcount + 1);
        arrayProperty.Set(uo, tmpcount, val);
      } else {
        while (buf.NotReadSize() > 0) {
          int tmpcount = arrayProperty.Size(uo);
          auto val = ReadValueAsTypeFromBufferNoCompressMode(buf, detailType, arrayProperty.element_type_index());
          arrayProperty.ReSize(uo, tmpcount + 1);
          arrayProperty.Set(uo, tmpcount, val);
        }
      }
    } break;

    case gbf::reflection::ValueKind::kReference:
    default:
      // Can not run to here
      return false;
  }

  return true;
}

Value CoderBinary::ReadValueAsTypeFromBufferNoCompressMode(ByteBufferView& buf, ValueKind valueType,
                                                           TypeId detailTypeId) {
  Value retval = Value::nothing;
  switch (valueType) {
    case gbf::reflection::ValueKind::kNone:
      // Do nothing
      break;
    case gbf::reflection::ValueKind::kBoolean: {
      bool bval = false;
      buf.GetLittleEndianStream() >> bval;
      retval = make_value(bval);
    } break;
    case gbf::reflection::ValueKind::kInteger: {
      int64_t ival = 0;
      buf.GetLittleEndianStream() >> ival;
      retval = make_value(ival);
    } break;
    case gbf::reflection::ValueKind::kReal: {
      double dval = 0.0;
      buf.GetLittleEndianStream() >> dval;
      retval = make_value(dval);
    } break;
    case gbf::reflection::ValueKind::kString: {
      std::string_view sv;
      buf.GetLittleEndianStream() >> sv;
      retval = make_value(sv);
    } break;
    case gbf::reflection::ValueKind::kEnum: {
      int eval = 0;
      buf.GetLittleEndianStream() >> eval;
      retval = make_value(eval);
    } break;
    case gbf::reflection::ValueKind::kUser: {
      UserObject suo;
      ReadUserObjectNoCompressMode(buf, detailTypeId, suo);
      retval = make_value(suo);
    } break;
    case gbf::reflection::ValueKind::kArray:
    case gbf::reflection::ValueKind::kReference:
    default:
      // ToDo: some notify here~
      break;
  }

  return retval;
}

}  // namespace reflection
}  // namespace gbf
