#include "archive/coder/simple_json_tools.h"

#include <vector>
#include "core/error/errors.hpp"
#include "core/imodules/ilog_module.h"
#include "math/colour_value.h"
#include "math/vector2.h"
#include "math/vector3.h"
#include "math/vector4.h"
#include "reflection/objects/make_user_object.hpp"
#include "reflection/objects/make_value.hpp"

namespace gbf {

//-----------------------------------------------------------------------------------------------
static const char kJsonBase64Encoding[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
static const unsigned char kJsonBase64Decoding[] = {
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 62,  255, 255, 255, 63,  52,  53,  54,  55,  56,  57,  58,  59,  60,  61,
    255, 255, 255, 0,   255, 255, 255, 0,   1,   2,   3,   4,   5,   6,   7,   8,   9,   10,  11,  12,  13,  14,  15,  16,  17,  18,  19,  20,  21,
    22,  23,  24,  25,  255, 255, 255, 255, 255, 255, 26,  27,  28,  29,  30,  31,  32,  33,  34,  35,  36,  37,  38,  39,  40,  41,  42,  43,  44,
    45,  46,  47,  48,  49,  50,  51,  255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255,
};
std::string EncodeBase64(const unsigned char* data, std::size_t size) {
  const char PAD = '=';

  std::string ret;
  ret.resize(4 * size / 3 + 3);
  char* out = &ret[0];

  std::size_t chunks = size / 3;
  std::size_t remainder = size % 3;

  for (std::size_t i = 0; i < chunks; i++, data += 3) {
    *out++ = kJsonBase64Encoding[data[0] >> 2];
    *out++ = kJsonBase64Encoding[((data[0] & 0x3) << 4) | (data[1] >> 4)];
    *out++ = kJsonBase64Encoding[((data[1] & 0xf) << 2) | (data[2] >> 6)];
    *out++ = kJsonBase64Encoding[data[2] & 0x3f];
  }

  switch (remainder) {
    case 0:
      break;
    case 1:
      *out++ = kJsonBase64Encoding[data[0] >> 2];
      *out++ = kJsonBase64Encoding[((data[0] & 0x3) << 4)];
      *out++ = PAD;
      *out++ = PAD;
      break;
    case 2:
      *out++ = kJsonBase64Encoding[data[0] >> 2];
      *out++ = kJsonBase64Encoding[((data[0] & 0x3) << 4) | (data[1] >> 4)];
      *out++ = kJsonBase64Encoding[((data[1] & 0xf) << 2)];
      *out++ = PAD;
      break;
  }

  ret.resize(out - &ret[0]);
  return ret;
}

std::vector<unsigned char> DecodeBase64(const std::string& input) {
  typedef std::vector<unsigned char> ret_type;
  if (input.empty()) return ret_type();

  ret_type ret(3 * input.size() / 4 + 1);
  unsigned char* out = &ret[0];

  unsigned value = 0;
  for (std::size_t i = 0; i < input.size(); i++) {
    unsigned char d = kJsonBase64Decoding[static_cast<unsigned>(input[i])];
    if (d == 255) return ret_type();

    value = (value << 6) | d;
    if (i % 4 == 3) {
      *out++ = value >> 16;
      if (i > 0 && input[i - 1] != '=') *out++ = value >> 8;
      if (input[i] != '=') *out++ = value;
    }
  }

  ret.resize(out - &ret[0]);
  return ret;
}
//-----------------------------------------------------------------------------------------------
SimpleJsonTools::JsonDocument::JsonDocument(const ByteBufferPtr& _data) {
  data_stream_ = _data;
  if (data_stream_) {
    assert((data_stream_->Contents()[data_stream_->Size()] == 0) && "Must a 0 terminate buffer here!");
    // Note: in situ parse mode will need the buffer alive
    document_.ParseInsitu((char*)(data_stream_->WritableContents()));
  } else {
    // TODO: make json.doc have an error result.
    ERR_DEF("JsonDocument has not valid stream buffer here!");
  }
}

SimpleJsonTools::JsonDocument::~JsonDocument() {}

void SimpleJsonTools::JsonWriteBinary(reflection::JsonSimpleWriter* writer, const ByteBuffer& buffer) {
  auto base64Str = EncodeBase64((const unsigned char*)buffer.Contents(), buffer.Size());
  writer->String(base64Str.c_str());
}

gbf::ByteBuffer SimpleJsonTools::JsonReadBinary(reflection::JsonValue& val, const char* key) {
  auto valStr = JsonGetString(val, key);
  auto ret = DecodeBase64(valStr);
  std::string_view sv((const char*)&ret[0], ret.size());
  return {sv};
}

void SimpleJsonTools::JsonWrite(reflection::JsonSimpleWriter* writer, const reflection::Value& anyData) {
  writer->StartObject();
  writer->Key("anydata_type");
  writer->Int((int)anyData.kind());
  writer->Key("data");
  switch (anyData.kind()) {
    case reflection::ValueKind::kNone:
      writer->String("NULL");
      break;
    case reflection::ValueKind::kReal: {
      auto data = reflection::value_cast<double>(anyData);
      writer->Double(data);
    } break;
    case reflection::ValueKind::kInteger: {
      writer->Int64(reflection::value_cast<int64_t>(anyData));
    } break;
    case reflection::ValueKind::kBoolean: {
      writer->Bool(reflection::value_cast<bool>(anyData));
    } break;
    case reflection::ValueKind::kString: {
      auto sv = reflection::value_cast<std::string_view>(anyData);
      writer->String(sv.data(), sv.length());
    } break;
    case reflection::ValueKind::kEnum: {
      writer->Int64(reflection::value_cast<int64_t>(anyData));
    } break;
    case reflection::ValueKind::kUser: {
      reflection::CoderJson::WriteUserObjectToJson(anyData.Ref<reflection::UserObject>(), *writer);
    } break;
    default:
      ERR_DEF("Can not support types find in JsonTools::JsonWrite(), type = %d", (int)anyData.kind());
      break;
  };

  writer->EndObject();
}

////void JsonTools::JsonGetBinaryByteBuffer(reflection::JsonValue& val, const char* key, ByteBuffer& buffer) {
////  auto valStr = JsonGetString(val, key);
////  auto ret = DecodeBase64(valStr);
////  buffer.ResetAsBuffer();
////  if (ret.size() > 0) {
////    buffer.Append((const unsigned char*)&ret[0], ret.size());
////  }
////}
////
////void* JsonTools::JsonGetBinary(reflection::JsonValue& val, const char* key, size_t& len) {
////  auto valStr = JsonGetString(val, key);
////  auto ret = DecodeBase64(valStr);
////  len = ret.size();
////  if (len == 0) return nullptr;
////
////  void* data = malloc(sizeof(unsigned char) * len);
////  memcpy(data, &ret[0], len);
////  return data;
////}
////
////void JsonTools::JsonReleaseBinary(void* ptr) { free(ptr); }

const char* SimpleJsonTools::JsonGetString(reflection::JsonValue& val, const char* key, const char* defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsString()) {
    return defaultVal;
  }
  return it->value.GetString();
}

int SimpleJsonTools::JsonGetInt(reflection::JsonValue& val, const char* key, int defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsInt()) {
    return defaultVal;
  }
  return it->value.GetInt();
}

long long SimpleJsonTools::JsonGetInt64(reflection::JsonValue& val, const char* key, long long defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsInt()) {
    return defaultVal;
  }
  return it->value.GetInt64();
}

double SimpleJsonTools::JsonGetDouble(reflection::JsonValue& val, const char* key, double defaultVal) {
  rapidjson::Value::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsNumber()) {
    return defaultVal;
  }
  return it->value.GetDouble();
}

float SimpleJsonTools::JsonGetFloat(reflection::JsonValue& val, const char* key, float defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsNumber()) {
    return defaultVal;
  }
  return static_cast<float>(it->value.GetDouble());
}

bool SimpleJsonTools::JsonGetBool(reflection::JsonValue& val, const char* key, bool defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsBool()) {
    return defaultVal;
  }
  return it->value.GetBool();
}

uint32_t SimpleJsonTools::JsonGetUInt32(reflection::JsonValue& val, const char* key, uint32_t defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsUint()) {
    return defaultVal;
  }
  return it->value.GetUint();
}

uint64_t SimpleJsonTools::JsonGetUInt64(reflection::JsonValue& val, const char* key, uint64_t defaultVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsUint64()) {
    return defaultVal;
  }
  return it->value.GetUint64();
}

reflection::JsonValue* SimpleJsonTools::JsonGetValue(reflection::JsonValue& val, const char* key) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd()) {
    return nullptr;
  }
  return &(it->value);
}

// setters
void SimpleJsonTools::JsonSetString(reflection::JsonValue& val, const char* key, const char* newVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it != val.MemberEnd()) {
    if (it->value.IsString()) {
      (it->value).SetString(rapidjson::StringRef(newVal));
    }
  }
}

void SimpleJsonTools::JsonSetInt(reflection::JsonValue& val, const char* key, int newVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it != val.MemberEnd()) {
    if (it->value.IsInt()) {
      (it->value).SetInt(newVal);
    }
  }
}

void SimpleJsonTools::JsonSetInt64(reflection::JsonValue& val, const char* key, long long newVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it != val.MemberEnd() && it->value.IsInt()) {
    (it->value).SetInt64(newVal);
  }
}

void SimpleJsonTools::JsonSetDouble(reflection::JsonValue& val, const char* key, double newVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it != val.MemberEnd() && it->value.IsNumber()) {
    (it->value).SetDouble(newVal);
  }
}

void SimpleJsonTools::JsonSetBool(reflection::JsonValue& val, const char* key, bool newVal) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it == val.MemberEnd() || !it->value.IsBool()) {
    (it->value).SetBool(newVal);
  }
}

void SimpleJsonTools::JsonGetAnyData(reflection::JsonValue& val, const char* key, reflection::Value& anyData) {
  reflection::JsonValue::MemberIterator it = val.FindMember(key);
  if (it != val.MemberEnd() && it->value.IsObject()) {
    auto type = JsonGetInt(it->value, "anydata_type", (int)reflection::ValueKind::kNone);
    auto data = JsonGetValue(it->value, "data");
    if (data == nullptr) {
      ERR_DEF("JsonTools::JsonGetAnyData() Can not find data in AnyData block !");
      return;
    }

    switch ((reflection::ValueKind)type) {
      case reflection::ValueKind::kNone:
        anyData = reflection::Value::nothing;
        break;
      case reflection::ValueKind::kBoolean:
        anyData = reflection::make_value(JsonGetBool(*data, key, false));
        break;
      case reflection::ValueKind::kInteger:
        anyData = reflection::make_value(JsonGetInt64(*data, key));
        break;
      case reflection::ValueKind::kReal:
        anyData = reflection::make_value(JsonGetDouble(*data, key));
        break;
      case reflection::ValueKind::kString:
        anyData = reflection::make_value(JsonGetString(*data, key, ""));
        break;
      case reflection::ValueKind::kEnum:
        anyData = reflection::make_value(JsonGetInt64(*data, key));
        break;
      case reflection::ValueKind::kUser:
        anyData = reflection::make_value(reflection::CoderJson::ReadUserObjectFromJson(*data));
        break;
      default: {
        ERR_DEF("JsonTools::JsonGetAnyData() can not handle the data type! data type = %d", type);
        ////auto s = data->GetString();
        ////auto buffer = DecodeBase64(s);
        ////anyData.resetByDataAndType((AnyData::AnyDataType)type, &(buffer[0]), buffer.size());
      } break;
    }
  }
}

}  // namespace gbf
