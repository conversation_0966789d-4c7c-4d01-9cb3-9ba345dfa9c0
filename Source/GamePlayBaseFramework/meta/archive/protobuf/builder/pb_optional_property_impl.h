#pragma once

#include "archive/archive_export.hpp"
#include "archive/protobuf/cxx/cxx_type_helper.hpp"
#include "reflection/meta/simple_property.hpp"

namespace gbf {
namespace reflection {
namespace detail {
/**
 * \brief Typed implementation of lurapb optional property
 */
class GBF_ARCHIVE_API PbOptionalPropertyImpl : public SimpleProperty {
 public:
  PbOptionalPropertyImpl(MetaClass* owner, IdRef name, ValueKind valKind, TypeId propertyTypeId, uint32_t tag,
                         protobuf::cxx::FieldDataType dataType, uint32_t dataOffset, uint32_t dataSize,
                         uint32_t offsetForHas);

  ~PbOptionalPropertyImpl();

  bool HasThis(const UserObject& object) const;

  void SetHasThis(const UserObject& object) const;

  void ClearThis(const UserObject& object) const;

 protected:
  /**
   * \see Property::isReadable
   */
  bool IsReadable() const final;

  /**
   * \see Property::isWritable
   */
  bool IsWritable() const final;

  /**
   * \see Property::getValue
   */
  Value GetValue(const UserObject& object) const final;

  /**
   * \see Property::setValue
   */
  void SetValue(const UserObject& object, const Value& value) const final;

  void* ContainerPtrToValuePtrInternal(const UserObject& object, int ArrayIndex = 0) const override
  {
      assert(false);
      return nullptr;
  }

 private:
  protobuf::cxx::FieldDataType pb_data_type_;
  protobuf::cxx::FieldBuildKind build_kind_;
  uint32_t data_offset_;
  uint32_t data_size_;

  uint32_t offset_for_has_;
};

}  // namespace detail
}  // namespace reflection
}  // namespace gbf
