#include "archive/protobuf/cxx/cxx_decoder.hpp"

#include <iostream>
#include "core/error/errors.hpp"
#include "math/mathtool.h"
#include "archive/protobuf/cxx/cxx_callback_helper.hpp"
#include "reflection/objects/array_object.hpp"
#include "reflection/meta/array_property.hpp"
#include "reflection/runtime/cxx_runtime.hpp"
#include "reflection/objects/make_value.hpp"


namespace protobuf {
namespace cxx {

bool Decoder::DecodeVarint(gbf::ByteBufferView& stream, uint64_t& dest) {
  uint8_t byte;
  uint_fast8_t bitpos = 0;
  uint64_t result = 0;

  do {
    if (bitpos >= 64) {
      CallbackHelper::NotifyError("protobuf::cplusplus::DecodeVarint() varint overflow");
      return false;
    }

    if (!stream.Read(&byte, 1)) {
      return false;
    }

    result |= (uint64_t)(byte & 0x7F) << bitpos;
    bitpos = (uint_fast8_t)(bitpos + 7);
  } while (byte & 0x80);

  dest = result;
  return true;
}

bool Decoder::DecodeSvarint(gbf::ByteBufferView& stream, int64_t& dest) {
  uint64_t value = 0;
  if (!DecodeVarint(stream, value)) {
    return false;
  }

  // zigzag~~
  if (value & 1) {
    dest = (int64_t)(~(value >> 1));
  } else {
    dest = (int64_t)(value >> 1);
  }

  return true;
}

static bool DecodeVarint32Eof(gbf::ByteBufferView& stream, uint32_t* dest, bool* eof) {
  uint8_t byte;
  uint32_t result;

  if (!stream.Read(&byte, 1)) {
    if (stream.NotReadSize() == 0) {
      if (eof) {
        *eof = true;
      }
    }

    return false;
  }

  if ((byte & 0x80) == 0) {
    /* Quick case, 1 byte value */
    result = byte;
  } else {
    /* Multibyte case */
    uint_fast8_t bitpos = 7;
    result = byte & 0x7F;

    do {
      if (!stream.Read(&byte, 1)) return false;

      if (bitpos >= 32) {
        /* Note: The varint could have trailing 0x80 bytes, or 0xFF for negative. */
        uint8_t sign_extension = (bitpos < 63) ? 0xFF : 0x01;
        bool valid_extension = ((byte & 0x7F) == 0x00 || ((result >> 31) != 0 && byte == sign_extension));

        if (bitpos >= 64 || !valid_extension) {
          CallbackHelper::NotifyError("protobuf::cplusplus::DecodeVarint32Eof() varint overflow");
          return false;
        }
      } else {
        result |= (uint32_t)(byte & 0x7F) << bitpos;
      }
      bitpos = (uint_fast8_t)(bitpos + 7);
    } while (byte & 0x80);

    if (bitpos == 35 && (byte & 0x70) != 0) {
      /* The last byte was at bitpos=28, so only bottom 4 bits fit. */
      CallbackHelper::NotifyError("protobuf::cplusplus::DecodeVarint32Eof() varint overflow");
      return false;
    }
  }

  *dest = result;
  return true;
}

bool Decoder::DecodeFieldTag(gbf::ByteBufferView& stream, FieldWireType& outWireType, uint32_t& outTag,
                             bool& outEof) {
  uint32_t temp;
  outEof = false;
  outWireType = (FieldWireType)0;
  outTag = 0;

  if (!DecodeVarint32Eof(stream, &temp, &outEof)) {
    CallbackHelper::NotifyError("protobuf::cplusplus::DecodeVarint32Eof() decode fail");
    return false;
  }

  outTag = temp >> 3;
  outWireType = (FieldWireType)(temp & 7);
  return true;
}

bool Decoder::DecodeUserObject(gbf::ByteBufferView& stream, const gbf::reflection::MetaClass& metaClass,
                               gbf::reflection::UserObject& outUo) {
  outUo = gbf::reflection::cxx::Create(metaClass);
  while (stream.NotReadSize() > 0) {
    uint32_t tag = 0;
    FieldWireType wireType = (FieldWireType)0;
    bool eof = false;

    if (!DecodeFieldTag(stream, wireType, tag, eof)) {
      if (eof) {
        break;
      } else {
        return false;
      }
    }

    if (tag == 0) {
      CallbackHelper::NotifyError("zero tag");
      return false;
    }

    const auto* prop = metaClass.GetPropertyByTag(tag);
    if (prop == nullptr) {
      // ToDo: need add log here?
      SkipField(stream, wireType);  // just skip not known tag here
    } else {
      if (!DecodeField(stream, wireType, *prop, outUo)) {
        return false;
      }
    }
  }

  return true;
}

bool Decoder::DecodeString(gbf::ByteBufferView& stream, std::string& outstr) {
  uint64_t len = 0;
  if (!DecodeVarint(stream, len)) {
    return false;
  }

  if (stream.NotReadSize() < len) {
    return false;
  }

  outstr.assign(stream.ReadPtr(), len);
  stream.ReadSkip(len);

  return true;
}

bool Decoder::DecodeBool(gbf::ByteBufferView& stream, bool& outbool) {
  uint64_t val = 0;
  if (!DecodeVarint(stream, val)) {
    return false;
  }

  outbool = (val != 0);
  return true;
}

bool Decoder::DecodeInteger(gbf::ByteBufferView& stream, int64_t& outint) {
  return DecodeSvarint(stream, outint);
}

bool Decoder::DecodeUInteger(gbf::ByteBufferView& stream, uint64_t& outint) {
  return DecodeVarint(stream, outint);
}

bool Decoder::DecodeFixed32(gbf::ByteBufferView& stream, void* pOutData) {
  int32_t val = 0;
  if (stream.NotReadSize() < sizeof(val)) {
    return false;
  }

  stream.GetBigEndianStream() >> val;
  memcpy(pOutData, &val, sizeof(val));

  return true;
}

bool Decoder::DecodeFixed64(gbf::ByteBufferView& stream, void* pOutData) {
  int64_t val = 0;
  if (stream.NotReadSize() < sizeof(val)) {
    return false;
  }

  stream.GetBigEndianStream() >> val;
  memcpy(pOutData, &val, sizeof(val));

  return true;
}

bool Decoder::DecodeEnum(gbf::ByteBufferView& stream, int& outenum) {
  int64_t tmpval = 0;
  if (!DecodeSvarint(stream, tmpval)) {
    return false;
  }
  outenum = (int)tmpval;
  return true;
}

bool Decoder::SeekFieldInfoByTag(gbf::ByteBufferView& stream, uint16_t seekTag, FieldWireType& outWireType,
                                 gbf::ByteBufferView& outFieldStream) {
  while (stream.NotReadSize() > 0) {
    uint32_t tag = 0;
    FieldWireType wireType = (FieldWireType)0;
    bool eof = false;

    if (!DecodeFieldTag(stream, wireType, tag, eof)) {
      if (eof) {
        break;
      } else {
        return false;
      }
    }

    if (tag == 0) {
      CallbackHelper::NotifyError("zero tag");
      return false;
    }

    auto fieldStartPos = stream.ReadPosition();
    SkipField(stream, wireType);  // just skip not known tag here

    if (tag == seekTag) {
      auto streamEndPos = stream.ReadPosition();
      outWireType = wireType;

      outFieldStream = gbf::ByteBufferView(stream.Contents() + fieldStartPos, streamEndPos - fieldStartPos);
      if (wireType == FieldWireType::kString) {
        SkipVarint(outFieldStream);
        outFieldStream = gbf::ByteBufferView(outFieldStream.ReadPtr(), outFieldStream.NotReadSize());
      }

      return true;
    }
  }

  return false;
}

bool Decoder::DecodeSubMessage(gbf::ByteBufferView& stream, const gbf::reflection::MetaClass& subClass,
                               gbf::reflection::UserObject& outSubUo) {
  uint64_t len = 0;
  if (!DecodeVarint(stream, len)) {
    return false;
  }

  if (stream.NotReadSize() < len) {
    CallbackHelper::NotifyError("parent stream too short!");
    return false;
  }

  gbf::ByteBufferView substream(stream.ReadPtr(), len);
  stream.ReadSkip(len);

  return DecodeUserObject(substream, subClass, outSubUo);
}

bool Decoder::SkipField(gbf::ByteBufferView& stream, FieldWireType wireType) {
  switch (wireType) {
    case protobuf::cxx::FieldWireType::kVariant:
      return SkipVarint(stream);
    case protobuf::cxx::FieldWireType::k64Bit:
      return stream.ReadSkip(8);
    case protobuf::cxx::FieldWireType::kString:
      return SkipString(stream);
    case protobuf::cxx::FieldWireType::k32Bit:
      return stream.ReadSkip(4);
    default:
      CallbackHelper::NotifyError("invalid wire type!");
      return false;
  }
}

bool Decoder::SkipVarint(gbf::ByteBufferView& stream) {
  uint8_t byte;
  do {
    if (!stream.Read(&byte, 1)) return false;
  } while (byte & 0x80);
  return true;
}

bool Decoder::SkipString(gbf::ByteBufferView& stream) {
  uint64_t len;
  if (!DecodeVarint(stream, len)) {
    return false;
  }

  return stream.ReadSkip(len);
}

bool Decoder::DecodeField(gbf::ByteBufferView& stream, FieldWireType wireType,
                          const gbf::reflection::Property& prop, const gbf::reflection::UserObject& uo) {
  switch (prop.kind()) {
    case gbf::reflection::ValueKind::kBoolean: {
      if (wireType != FieldWireType::kVariant) {
        CallbackHelper::NotifyError("wrong wire type for bool!");
        return false;
      }

      bool bval = false;
      if (!DecodeBool(stream, bval)) {
        return false;
      }

      prop.Set(uo, gbf::reflection::make_value(bval));
      return true;
    }
    case gbf::reflection::ValueKind::kInteger: {
      if (wireType != FieldWireType::kVariant) {
        CallbackHelper::NotifyError("wrong wire type for integer!");
        return false;
      }

      int64_t ival = 0;
      if (!DecodeInteger(stream, ival)) {
        return false;
      }

      prop.Set(uo, gbf::reflection::make_value(ival));
      return true;
    }
    case gbf::reflection::ValueKind::kEnum: {
      if (wireType != FieldWireType::kVariant) {
        CallbackHelper::NotifyError("wrong wire type for enum!");
        return false;
      }

      int ival = 0;
      if (!DecodeEnum(stream, ival)) {
        return false;
      }

      prop.Set(uo, gbf::reflection::make_value(ival));
      return true;
    }
    case gbf::reflection::ValueKind::kReal: {
      if (wireType == FieldWireType::k64Bit) {
        double dval = 0;
        if (!DecodeFixed64(stream, &dval)) {
          return false;
        }

        prop.Set(uo, gbf::reflection::make_value(dval));
        return true;
      } else if (wireType == FieldWireType::k32Bit) {
        float fval = 0;
        if (!DecodeFixed32(stream, &fval)) {
          return false;
        }

        prop.Set(uo, gbf::reflection::make_value(fval));
        return true;
      } else {
        CallbackHelper::NotifyError("wrong wire type for double!");
        return false;
      }
    }
    case gbf::reflection::ValueKind::kString: {
      if (wireType != FieldWireType::kString) {
        CallbackHelper::NotifyError("wrong wire type for string!");
        return false;
      }

      std::string strval;
      if (!DecodeString(stream, strval)) {
        return false;
      }

      prop.Set(uo, gbf::reflection::make_value(strval));
      return true;
    }
    case gbf::reflection::ValueKind::kUser: {
      auto subId = prop.type_index();
      auto* subMsgClass = gbf::reflection::query_meta_class_by_id(subId);

      gbf::reflection::UserObject subObj;
      if (subMsgClass != nullptr) {
        if (!DecodeSubMessage(stream, *subMsgClass, subObj)) {
          return false;
        }

        prop.Set(uo, gbf::reflection::make_value(subObj));
      } else {
        // ToDo: add warning here?
        // Now just skip unknown sub message here
        if (!SkipString(stream)) {
          return false;
        }
      }

      return true;
    }
    case gbf::reflection::ValueKind::kArray: {
      const auto& ao = gbf::reflection::value_ref_as<const gbf::reflection::ArrayObject>(prop.Get(uo));
      const auto* ap = (gbf::reflection::ArrayProperty*)(&prop);
      return DecodeArrayObject(stream, wireType, *ap, ao);
    }
    case gbf::reflection::ValueKind::kReference:
    case gbf::reflection::ValueKind::kNone:
    default:
      // Not a basic type, just return
      return false;
  }
}

bool Decoder::DecodeArrayObject(gbf::ByteBufferView& stream, FieldWireType wireType,
                                const gbf::reflection::ArrayProperty& aprop,
                                const gbf::reflection::ArrayObject& ao) {
  switch (aprop.element_type()) {
    case gbf::reflection::ValueKind::kBoolean:
    case gbf::reflection::ValueKind::kInteger:
    case gbf::reflection::ValueKind::kReal:
    case gbf::reflection::ValueKind::kEnum: {
      uint64_t len = 0;
      if (!DecodeVarint(stream, len)) {
        return false;
      }

      if (stream.NotReadSize() < len) {
        CallbackHelper::NotifyError("parent stream too short!");
        return false;
      }

      gbf::ByteBufferView substream(stream.ReadPtr(), len);
      stream.ReadSkip(len);

      // decode with substream
      ao.SetSize(0);  // clear array first
      while (substream.NotReadSize() > 0) {
        size_t asize = ao.GetSize();
        ao.SetSize(asize + 1);
        if (ao.GetElementKind() == gbf::reflection::ValueKind::kBoolean) {
          bool bval = false;
          if (!DecodeBool(substream, bval)) {
            return false;
          }
          ao.SetElement(asize, gbf::reflection::make_value(bval));
        } else if (ao.GetElementKind() == gbf::reflection::ValueKind::kInteger) {
          // ToDo: add some tags for int detail info here~~(normal just encode/decode as int64_t)
          int64_t ival = 0;
          if (!DecodeInteger(substream, ival)) {
            return false;
          }
          ao.SetElement(asize, gbf::reflection::make_value(ival));
        } else if (ao.GetElementKind() == gbf::reflection::ValueKind::kReal) {
          // ToDo: some fix here?
          double dval = 0.0;
          if (!DecodeFixed64(substream, &dval)) {
            return false;
          }
          ao.SetElement(asize, gbf::reflection::make_value(dval));
          //////ToDo: some fix here?
          ////if (GBF_LIKELY(aprop.elementTypeIndex() == __type_id<double>()))
          ////{
          ////	//double
          ////	double dval = 0.0;
          ////	if (!DecodeFixed64(substream, &dval))
          ////	{
          ////		return false;
          ////	}
          ////	ao.setElement(asize, dval);
          ////}
          ////else if (aprop.elementTypeIndex() == __type_id<float>())
          ////{
          ////	//float
          ////	float fval = 0.0f;
          ////	if (!DecodeFixed32(substream, &fval))
          ////	{
          ////		return false;
          ////	}
          ////	ao.setElement(asize, fval);
          ////}
          ////else
          ////{
          ////	CallbackHelper::NotifyError("Unknown float type find here!");
          ////	return false;
          ////}
        } else if (ao.GetElementKind() == gbf::reflection::ValueKind::kEnum) {
          int ival = 0;
          if (!DecodeEnum(substream, ival)) {
            return false;
          }
          ao.SetElement(asize, gbf::reflection::make_value(ival));
        }
      }
    } break;
    case gbf::reflection::ValueKind::kString: {
      size_t asize = ao.GetSize();
      ao.SetSize(asize + 1);

      std::string tmpstr;
      if (!DecodeString(stream, tmpstr)) {
        return false;
      }
      ao.SetElement(asize, gbf::reflection::make_value(tmpstr));
    } break;
    case gbf::reflection::ValueKind::kUser: {
      auto subId = aprop.element_type_index();
      const gbf::reflection::MetaClass* subMsgClass = gbf::reflection::query_meta_class_by_id(subId);
      if (subMsgClass != nullptr) {
        size_t asize = ao.GetSize();
        ao.SetSize(asize + 1);

        gbf::reflection::UserObject subUo;
        if (!DecodeSubMessage(stream, *subMsgClass, subUo)) {
          return false;
        }
        ao.SetElement(asize, gbf::reflection::make_value(subUo));
      } else {
        // ToDo: add warning here?
        SkipString(stream);
      }
    } break;
    case gbf::reflection::ValueKind::kArray:  // do not support array in array~~
    default:
      GBF_ERROR(gbf::CanNotRunToHereError());
  }

  return true;
}

}  // namespace cplusplus
}  // namespace protobuf
