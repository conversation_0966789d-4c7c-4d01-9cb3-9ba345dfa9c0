/* pb_common.h: Common support functions for pb_encode.c and pb_decode.c.
 * These functions are rarely needed by applications directly.
 */
#pragma once

#include "core/utils/meta_type_hash.hpp"
#include "archive/protobuf/pb.h"

namespace protobuf {

enum class DispatchType: int {
  kDispatchUnknown = 0,
  kDispatchReq = 1,
  kDispatchRes = 2,
  kDispatchTotal = 3,
};

struct DispatchId {
  DispatchId(DispatchType _disp_type, int32_t _msg_id, int32_t _sub_msg_id) { 
    sdata.detail.disp_type = (int32_t)_disp_type;
    sdata.detail.msg_id = _msg_id;
    sdata.detail.sub_msg_id = _sub_msg_id;
    static_assert(sizeof(sdata) == 8);
  }

  DispatchId(int64_t comp_id) { sdata.combined_id = comp_id; }
  ~DispatchId() = default;

  int64_t GetCombinedId() const { return sdata.combined_id; }

  DispatchType GetDispType() { return (DispatchType)sdata.detail.disp_type; }

  int32_t GetMsgId() { return sdata.detail.msg_id; }

  int32_t GetSubMsgId() { return sdata.detail.sub_msg_id; }
 protected:
  union {
    int64_t combined_id;
    struct {
      int32_t disp_type : 8;
      int32_t msg_id : 24;
      int32_t sub_msg_id : 32;
    } detail;
  } sdata;
};

/* Initialize the field iterator structure to beginning.
 * Returns false if the message type is empty. */
GBF_ARCHIVE_API bool PbFieldIterBegin(PbFieldIter* iter, const PbMsgDesc* desc, void* message);

/* Get a field iterator for extension field. */
GBF_ARCHIVE_API bool PbFieldIterBeginExtension(PbFieldIter* iter, PbExtensionInfo* extension);

/* Same as pb_field_iter_begin(), but for const message pointer.
 * Note that the pointers in pb_field_iter_t will be non-const but shouldn't
 * be written to when using these functions. */
GBF_ARCHIVE_API bool PbFieldIterBeginConst(PbFieldIter* iter, const PbMsgDesc* desc,
                                                      const void* message);
GBF_ARCHIVE_API bool PbFieldIterBeginExtensionConst(PbFieldIter* iter, const PbExtensionInfo* extension);

/* Advance the iterator to the next field.
 * Returns false when the iterator wraps back to the first field. */
GBF_ARCHIVE_API bool PbFieldIterNext(PbFieldIter* iter);

/* Advance the iterator until it points at a field with the given tag.
 * Returns false if no such field exists. */
GBF_ARCHIVE_API bool PbFieldIterFind(PbFieldIter* iter, uint32_t tag);

/* Find a field with type PB_LTYPE_EXTENSION, or return false if not found.
 * There can be only one extension range field per message. */
GBF_ARCHIVE_API bool PbFieldIterFindExtension(PbFieldIter* iter);

/* Validate UTF-8 text string */
GBF_ARCHIVE_API bool PbValidateUtf8(const char* s);

GBF_ARCHIVE_API const PbDetailFeildInfo* PbDetailFieldInfoFind(const PbMsgDesc* desc, uint32_t tag);



GBF_ARCHIVE_API PbMsgDesc* PbCreateMetaMessage(uint64_t type_id, const char* msg_name = "");

GBF_ARCHIVE_API const PbMsgDesc* PbQueryMetaMessageNoDeferInit(uint64_t type_id);

GBF_ARCHIVE_API const PbMsgDesc* PbQueryMetaMessage(uint64_t type_id);

//deprecated here~~
// compatiable with proto tools
inline const PbDetailFeildInfo* pb_detail_field_info_find(const PbMsgDesc* desc, uint32_t tag) {
  return PbDetailFieldInfoFind(desc, tag);
}

inline const PbMsgDesc* pb_query_meta_message(uint64_t type_id)
{
  return PbQueryMetaMessage(type_id);
}

inline const PbMsgDesc* pb_query_meta_message_no_defer_init(uint64_t type_id) {
  return PbQueryMetaMessageNoDeferInit(type_id);
}

//-------------------------------------------------------------------------------------
class GBF_ARCHIVE_API DispatchMessageUtil {
 public:
  static void Register(DispatchType dispatch_type, int32_t msg_id, int32_t sub_msg_id, int64_t meta_id);
  
  static bool QueryMetaId(DispatchType dispatch_type, int32_t msg_id, int32_t sub_msg_id, int64_t& out_meta_id);
  static bool QueryDispatchInfo(int64_t meta_id, DispatchType& out_dispatch_type, int32_t& out_msg_id,
                                        int32_t& out_sub_msg_id);

};

}  // namespace protobuf
