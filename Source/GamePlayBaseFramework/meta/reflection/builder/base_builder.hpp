#pragma once

#include <cassert>
#include <string>
#include "reflection/reflection_fwd.hpp"
#include "reflection/vtable/object_vtable.hpp"
#include "reflection/meta/meta_class.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf {
namespace reflection {

#ifndef _MANAGED
// ObjectBase must define static function MetaClassName
template <typename T>
concept HasMetaClassName = requires { T::MetaClassName(); };
#endif

template <typename T>
class BaseBuilder {
 public:
  /**
   * \brief Construct the builder with a target metaclass to fill
   *
   * \param target Metaclass to build
   */
  BaseBuilder(MetaClass* meta_class): target_(meta_class){}

  template <typename U>  // base
  MetaClass* base() {
    // Retrieve the base metaclass and its name
    const MetaClass& baseClass = *query_meta_class<U>();
    IdReturn baseName = baseClass.name();

    // First make sure that the base class is not already a base of the current class
    for (MetaClass::BaseInfo const& bi : target_->bases_) {
      if (bi.base->name() == baseName) {
        WRN_M(LOG_MODULE_RELFECTION, "Register base class:%s for %s twice.", baseName.c_str(), target_->name().c_str());
        ////PONDER_ERROR(TypeAmbiguity(bi.base->name()));
      }
    }

#ifndef _MANAGED
    if constexpr (HasMetaClassName<U>)
    {
      // A StaticMetaClassName function must be defined to override the base class implementation.
      static_assert(&U::MetaClassName != &T::MetaClassName);
    }
#endif

    // Compute the offset to apply for pointer conversions
    // - Note we do NOT support virtual inheritance here due to the associated problems
    //   with compiler specific class layouts.
    // - Use pointer dummy buffer here as some platforms seem to trap bad memory access even
    //   though not dereferencing the pointer.
    // - U : Base, T : Derived.
    char dummy[8];
    T* asDerived = reinterpret_cast<T*>(dummy);
    U* asBase = static_cast<U*>(asDerived);
    const int offset = static_cast<int>(reinterpret_cast<char*>(asBase) - reinterpret_cast<char*>(asDerived));

    return base(baseClass, offset);
  }

  MetaClass* base(const MetaClass& baseClass, int offset)
  {
      // Add the base metaclass to the bases of the current class
      MetaClass::BaseInfo baseInfos;
      baseInfos.base = &baseClass;
      baseInfos.offset = offset;
      target_->bases_.push_back(baseInfos);

      // Copy all properties of the base class into the current class
      for (auto& [name, prop] : baseClass.properties_table_) {
          target_->AddProperty(name, prop);
      }

      // Copy all functions of the base class into the current class
      for (auto& [name, func] : baseClass.functions_table_) {
          target_->AddFunction(name, func);
      }

      return target_;
  }
 protected:
  MetaClass* target_ = nullptr;   // Target metaclass to fill
};

}  // namespace reflection
}  // namespace gbf
