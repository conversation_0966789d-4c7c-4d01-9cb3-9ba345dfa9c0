
#include "reflection/builder/enum_builder.hpp"
#include "reflection/meta/meta_enum.hpp"

namespace gbf {
namespace reflection {

EnumBuilder::EnumBuilder(MetaEnum& target) : target_(&target) {}

EnumBuilder& EnumBuilder::value(IdRef name, MetaEnum::EnumValue value) {
  ////assert(!target_->HasName(name));
  // assert(!m_target->hasValue(value));
  if (target_->Has<PERSON>ame(name)) {
    target_->enums_.erase(name.data());
  }
  target_->enums_.insert(std::make_pair(name, value)); 

  return *this;
}

}  // namespace reflection
}  // namespace gbf
