
#pragma once

#include "reflection/error/errors.hpp"
#include "reflection/objects/args.hpp"
#include "reflection/meta/constructor.hpp"
#include "reflection/meta/ctor_caller.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/utils/signature_name.hpp"
#include "reflection/objects/make_value.hpp"
#include "reflection/objects/valuechecker.hpp"

namespace gbf {
namespace reflection {
namespace detail {
namespace cxx {
    /**
 * \brief Helper function which converts an argument to a C++ type
 *
 * The main purpose of this function is to convert any BadType error to
 * a BadArgument one.
 *
 * \param args List of arguments
 * \param index Index of the argument to convert
 *
 * \return Value of args[index] converted to T
 *
 * \thrown BadArgument conversion triggered a BadType error
 */
template <typename T>
inline typename std::remove_cv<std::remove_reference_t<T>>::type ConvertArg(const Args& args, size_t index) {
  try {
      return value_cast<typename std::remove_cv<std::remove_reference_t<T>>::type>(args[index]);
  } catch (const BadType&) {
    PONDER_ERROR(BadArgument(args[index].kind(), MapType<T>(), index, "constructor"));
  }
}

//-------------------------------------------------------------------------------------
class CtorCaller : public ICtorCaller {
 public:
  CtorCaller(Constructor* parent_ctor) : ICtorCaller(parent_ctor) {}

  virtual bool Matches(const Args& args) const = 0;
  ////virtual int GetArgNumber() const = 0;

  virtual UserObject CreateStorageExternal(void* ptr, const Args& args) const = 0;
  virtual UserObject CreateStorageRemoteShared(const Args& args) const = 0;
  virtual UserObject CreateStorageGc(const Args& args) const = 0;
};

//-------------------------------------------------------------------------------------

/**
 * \brief Implementation of meta constructors with variable parameters
 */
template <typename T, typename... A>
class TCtorCallerImpl : public CtorCaller {
 public:
  TCtorCallerImpl(Constructor* parent_ctor) : CtorCaller(parent_ctor) {
    signature_name_ = gbf::reflection::TSignatureArgsaName<std::tuple<A...>>::Name();
    sig_arg_array_ = gbf::reflection::TSignatureArgsaName<std::tuple<A...>>::GetArgInfoArray();
    arg_number_ = sizeof...(A);
  }

  ~TCtorCallerImpl() {}

 protected:
  template <typename... As, size_t... Is>
  static inline UserObject CreateWithArgsStorageExternal(void* ptr, const Args& args, std::index_sequence<Is...>) {
    T* raw_ptr = ptr != nullptr ? new (ptr) T(ConvertArg<As>(args, Is)...) : new T(ConvertArg<As>(args, Is)...);
    if constexpr (std::is_base_of_v<RttiBase, T>) {
      raw_ptr->__bind_rtti_info(query_meta_class<T>(), StorageType::StorageRemote, next_reflection_obj_id());
    }
    return make_user_object(raw_ptr, remote_storage_policy{});
  }

  template <typename... As, size_t... Is>
  static inline UserObject CreateWithArgsStorageRemoteShared(const Args& args, std::index_sequence<Is...>) {
    std::shared_ptr<T> ptr = make_shared_with_rtti<T>(ConvertArg<As>(args, Is)...);
    return __box(ptr);
  }

  template <typename... As, size_t... Is>
  static inline UserObject CreateWithArgsStorageGc(const Args& args, std::index_sequence<Is...>) {
    return make_user_object(T(ConvertArg<As>(args, Is)...), gc_storage_policy{});
  }

 public:
  /**
   * \see Constructor::matches
   */
  bool Matches(const Args& args) const override {
    return args.GetCount() == sizeof...(A) && CheckArgs<A...>(args, std::make_index_sequence<sizeof...(A)>());
  }

  int GetArgNumber() const override { return static_cast<int>(arg_number_); }

  /**
   * \see Constructor::create
   */
  UserObject CreateStorageExternal(void* ptr, const Args& args) const override { return CreateWithArgsStorageExternal<A...>(ptr, args, std::make_index_sequence<sizeof...(A)>()); }

  UserObject CreateStorageRemoteShared(const Args& args) const override { return CreateWithArgsStorageRemoteShared<A...>(args, std::make_index_sequence<sizeof...(A)>()); }

  UserObject CreateStorageGc(const Args& args) const override { return CreateWithArgsStorageGc<A...>(args, std::make_index_sequence<sizeof...(A)>()); }
 protected:
  std::string signature_name_;
  SigArgArray sig_arg_array_;
  size_t arg_number_;
};
}  // namespace cxx
}  // namespace detail
}  // namespace reflection
}  // namespace gbf
