
#pragma once

#include "core/imodules/iprofiler_module.h"
#include "reflection/meta/func_caller.hpp"
#include "reflection/meta/function.hpp"
#include "reflection/objects/make_user_object.hpp"
#include "reflection/objects/make_value.hpp"
#include "reflection/objects/valuechecker.hpp"
#include "reflection/traits/data_type_traits.hpp"
#include "reflection/traits/param_traits.hpp"
#include "reflection/traits/function_traits.hpp"
#include "reflection/utils/signature_name.hpp"
#include "reflection/utils/util.hpp"

namespace gbf {
namespace reflection {
namespace detail {
namespace cxx {

//-----------------------------------------------------------------------------
// Handle returning copies

////template <typename R, typename U = void>
////struct TCallReturnCopy;

template <typename R>
struct TCallReturnCopy {
  static inline Value GetValue(R&& o) { 
    ////using UsedDataType = typename ::gbf::reflection::detail::TDataType<R>::Type;
    using UsedTraits = typename ::gbf::reflection::detail::TTypeTraits<std::remove_reference_t<std::remove_cv_t<R>>>;
    if constexpr (UsedTraits::kind == ReferenceKind::kSmartPointer) {
      //Smart pointer here
      static_assert(std::is_base_of_v<RttiBase, typename UsedTraits::DereferencedType>, "Only class inherits from RttiBase can return as shared_ptr!");
      return Value(__box_rtti_object(o.get()));
    }
    else if constexpr (reflection::detail::TIsUserType<R>::value) {
      //user type here
      if constexpr (UsedTraits::kind == ReferenceKind::kPointer) {
        //Only handle rtti object here, it can box from a rawpointer
        if constexpr (std::is_base_of_v<RttiBase, typename UsedTraits::DereferencedType>) {
          return Value(__box_rtti_object(o));
        }
        else {
          //static_assert(UsedTraits::kind != ReferenceKind::kPointer, "Cannot return native pointer here. Use reflection::policy::ReturnInternalRef?");
          SAFE_STATIC_ASSERT_FALSE("Cannot return native pointer here. Use reflection::policy::ReturnInternalRef?");
        }
      }
      else {
        return Value(make_user_object(std::forward<R>(o)));
      }
    }
    else if constexpr (TIsBuiltInNotStringPointerType<R>::value) {
      // built in pointer type here, such as float*, double*, int* ...
      return Value(reinterpret_cast<int64_t>(o));
    }
    else {
      //normal value type here, try to construct by Value.
      return Value(o);
    }
  }
};

//-----------------------------------------------------------------------------
// Handle returning internal references

template <typename R, typename U = void>
struct TCallReturnInternalRef;

template <typename R>
struct TCallReturnInternalRef<R, typename std::enable_if<!reflection::detail::TIsUserType<R>::value &&
                                                         !std::is_same<typename reflection::detail::TDataType<R>::Type, UserObject>::value>::type> {
  static inline Value GetValue(R&& o) { return Value(o); }
};

template <typename R>
struct TCallReturnInternalRef<R, typename std::enable_if<reflection::detail::TIsUserType<R>::value ||
                                                         std::is_same<typename reflection::detail::TDataType<R>::Type, UserObject>::value>::type> {
  static inline Value GetValue(R&& o) { return Value(make_user_object(std::forward<R>(o), remote_storage_policy{})); }
};

//-----------------------------------------------------------------------------
// Choose which returner to use, based on policy
//  - map policy kind to actionable policy type

template <typename Policies_t, typename R>
struct TChooseCallReturner;

template <typename... Ps, typename R>
struct TChooseCallReturner<std::tuple<policy::ReturnCopy, Ps...>, R> {
  using type = TCallReturnCopy<R>;
};

template <typename... Ps, typename R>
struct TChooseCallReturner<std::tuple<policy::ReturnInternalRef, Ps...>, R> {
  using type = TCallReturnInternalRef<R>;
};

template <typename R>
struct TChooseCallReturner<std::tuple<>, R>  // default
{
  using type = TCallReturnCopy<R>;
};

template <typename P, typename... Ps, typename R>
struct TChooseCallReturner<std::tuple<P, Ps...>, R>  // recurse
{
  using type = typename TChooseCallReturner<std::tuple<Ps...>, R>::type;
};

//-----------------------------------------------------------------------------

/*
 * Helper function which converts an argument to a C++ type
 *
 * The main purpose of this function is to convert any BadType error to
 * a BadArgument one.
 */
template <int TFrom, typename TTo>
struct TConvertArg {
  using ReturnType = typename std::remove_reference<TTo>::type;
  static ReturnType Convert(const Args& args, size_t index) {
    try {
      return value_cast<typename std::remove_reference<TTo>::type>(args[index]);
    } catch (const BadType&) {
      PONDER_ERROR(BadArgument(args[index].kind(), MapType<TTo>(), index, "?"));
    }
  }
};

// Specialisation for returning references.
template <typename TTo>
struct TConvertArg<(int)ValueKind::kUser, TTo&> {
  using ReturnType = TTo&;
  static ReturnType Convert(const Args& args, size_t index) {
    auto& uobj = value_ref_as<UserObject>(const_cast<Value&>(args[index]));
    if (uobj.IsEmpty()) PONDER_ERROR(NullObjectError(&uobj.GetClass()));
    return user_object_ref_as<TTo>(uobj);
  }
};

// Specialisation for returning const references.
template <typename TTo>
struct TConvertArg<(int)ValueKind::kUser, const TTo&> {
  using ReturnType = const TTo&;
  static ReturnType Convert(const Args& args, size_t index) {
    auto& uobj = value_ref_as<const UserObject>(args[index]);
    if (uobj.IsEmpty()) PONDER_ERROR(NullObjectError(&uobj.GetClass()));
    return user_object_ref_as<const TTo>(uobj);
  }
};

//-----------------------------------------------------------------------------
// Object function call helper to allow specialisation by return type. Applies policies.

template <typename A>
struct TConvertArgs {
  using Raw = typename reflection::detail::TDataType<A>::Type;
  static constexpr ValueKind kind = reflection::detail::TValueMapper<Raw>::kind;
  using Convertor = TConvertArg<(int)kind, A>;

  static typename Convertor::ReturnType Convert(const Args& args, size_t index) { return Convertor::Convert(args, index); }
};

template <typename R, typename FTraits, typename FPolicies>
class TCallHelper {
 public:
  template <typename F, typename... A, size_t... Is>
  static Value Call(const F& func, const Args& args, std::index_sequence<Is...>) {
    using CallReturner = typename TChooseCallReturner<FPolicies, R>::type;
    return CallReturner::GetValue(func(TConvertArgs<A>::Convert(args, Is)...));
  }
};

// Specialization of CallHelper for functions returning void
template <typename FTraits, typename FPolicies>
class TCallHelper<void, FTraits, FPolicies> {
 public:
  template <typename F, typename... A, size_t... Is>
  static Value Call(const F& func, const Args& args, std::index_sequence<Is...>) {
    func(TConvertArgs<A>::Convert(args, Is)...);
    return Value::nothing;
  }
};

//-----------------------------------------------------------------------------
// Convert traits to callable function wrapper. Generic for all function types.

template <typename R, typename A>
struct TFunctionWrapper;

template <typename R, typename... A>
struct TFunctionWrapper<R, std::tuple<A...>> {
  using Type = typename std::function<R(A...)>;

  template <typename F, typename FTraits, typename FPolicies>
  static Value Call(const F& func, const Args& args) {
    using ArgEnumerator = std::make_index_sequence<sizeof...(A)>;
    return TCallHelper<R, FTraits, FPolicies>::template Call<F, A...>(func, args, ArgEnumerator());
  }
};

template <typename A>
struct TFunctionArgChecker;

template <typename... A>
struct TFunctionArgChecker<std::tuple<A...>> {
  template <typename FTraits>
  static bool CheckArgs(const Args& args) {
    using ArgEnumerator = std::make_index_sequence<sizeof...(A)>;
    if (sizeof...(A) == args.GetCount()) {
      return reflection::detail::CheckArgs<A...>(args, ArgEnumerator());
    } else {
      return false;
    }
  }
};

//-----------------------------------------------------------------------------
// Base for runtime function caller

class FunctionCaller : public IFuncCaller {
 public:
  FunctionCaller(Function* parent_func, std::string_view signature)
      : IFuncCaller(parent_func), signature_name_(signature.data(), signature.length()) {
#if ENABLE_GBF_REFLECTION_FUNCTIONS_PROFILER
    prof_method_info_ =
        GBF_PROFILER_CREATE_METHOD_WITH_SOURCE(full_name(), gbf::ProfilerGroupType::kReflection, "c++ reflection call[c++]");
#endif
  }
  virtual ~FunctionCaller() {}

  virtual Value Execute(const Args& args) const = 0;

  virtual bool CheckArgs(const Args& args) const = 0;

 protected:
  const std::string signature_name_;
#if ENABLE_GBF_REFLECTION_FUNCTIONS_PROFILER
  GBF_PROFILER_METHOD_TYPE prof_method_info_;
#endif
};

// The FunctionImpl class is a template which is specialized according to the
// underlying function prototype.
template <typename F, typename FTraits, typename... P>
class TFunctionCallerImpl final : public FunctionCaller {
 private:
  // Nested types
  using CallTypes = typename FTraits::Details::FunctionCallTypes;
  using DispatchType = TFunctionWrapper<typename FTraits::ExposedType, CallTypes>;
  using ArgumentCheckerType = TFunctionArgChecker<CallTypes>;
  using FPolicies = std::tuple<P...>;

 public:
  TFunctionCallerImpl(Function* parent_func, F function)
      : FunctionCaller(parent_func, gbf::reflection::TSignatureName<F>::Name()), m_function(function) {
    kind_ = FTraits::kind;
    return_type_ = MapType<typename FTraits::ExposedType>();
    return_type_id_ = gbf::MetatypeHash::Hash<typename FTraits::ExposedType>();
    return_base_type_id_ = gbf::MetatypeHash::Hash<typename FTraits::DataType>();

    return_policy_ = ReturnPolicy<typename FTraits::ExposedType, P...>::kind;
    param_info_array_ = FunctionApplyToParams<CallTypes, FunctionMapParamsToValueKind<kNumParams>>::foreach ();
  }

  ~TFunctionCallerImpl(){}

  size_t GetParamCount() const override { return kNumParams; }

  ValueKind GetParamType(size_t index) const override {
    // Make sure that the index is not out of range
    if (index >= kNumParams) PONDER_ERROR(OutOfRange(index, kNumParams));

    return param_info_array_[index].m_valueType;
  }

  std::string_view GetParamTypeName(size_t index) const override {
    // Make sure that the index is not out of range
    if (index >= kNumParams) PONDER_ERROR(OutOfRange(index, kNumParams));

    return param_info_array_[index].m_typename;
  }

  TypeId GetParamTypeIndex(size_t index) const override {
    // Make sure that the index is not out of range
    if (index >= kNumParams) PONDER_ERROR(OutOfRange(index, kNumParams));

    return param_info_array_[index].m_typeid;
  }

  TypeId GetParamBaseTypeIndex(size_t index) const override {
    // Make sure that the index is not out of range
    if (index >= kNumParams) PONDER_ERROR(OutOfRange(index, kNumParams));

    return param_info_array_[index].m_baseTypeId;
  }

  TypeId GetReturnTypeIndex() const override { return return_type_id_; }

  TypeId GetReturnBaseTypeIndex() const override { return return_base_type_id_; }

  Value Execute(const Args& args) const final {
#if ENABLE_GBF_REFLECTION_FUNCTIONS_PROFILER
    GBF_PROFILER_AUTO_SCOPE(prof_method_info_);
#endif
    return DispatchType::template Call<decltype(m_function), FTraits, FPolicies>(m_function, args);
  }

  bool CheckArgs(const Args& args) const final { return ArgumentCheckerType::template CheckArgs<FTraits>(args); }

 private:
  typename DispatchType::Type m_function;  // Object containing the actual function to call
 private:
  static constexpr size_t kNumParams = std::tuple_size_v<CallTypes>;
  std::array<detail::FunctionParamInfo, kNumParams> param_info_array_;
  TypeId return_type_id_;
  TypeId return_base_type_id_;
};

}  // namespace cxx
}  // namespace detail
}  // namespace reflection
}  // namespace gbf
