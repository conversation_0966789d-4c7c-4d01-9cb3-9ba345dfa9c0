/****************************************************************************
**
** This file is part of the <PERSON><PERSON> library, formerly CAMP.
**
** The MIT License (MIT)
**
** Copyright (C) 2009-2014 TEGESO/TEGESOFT and/or its subsidiary(-ies) and mother company.
** Copyright (C) 2015-2020 Nick Trout.
**
** Permission is hereby granted, free of charge, to any person obtaining a copy
** of this software and associated documentation files (the "Software"), to deal
** in the Software without restriction, including without limitation the rights
** to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
** copies of the Software, and to permit persons to whom the Software is
** furnished to do so, subject to the following conditions:
**
** The above copyright notice and this permission notice shall be included in
** all copies or substantial portions of the Software.
**
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
** IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
** FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
** AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
** LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
** OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
** THE SOFTWARE.
**
****************************************************************************/

#pragma once

#include "reflection/type.hpp"
#include "reflection/reflection_fwd.hpp"

namespace cross {
class TypescriptMetaClassUtils;
}
namespace gbf {
namespace reflection {

/**
 * \brief Represents a metaconstructor which is used to create objects instances from metaclasses
 *
 * This class is an interface which has to be derived to implement typed constructors.
 *
 * \sa Property, Function
 */
class GBF_REFLECTION_API Constructor : public Type {
  template<typename T>
  friend class FunctionBuilder;
  template <typename T>
  friend class LuaFunctionBuilder;
  friend class cross::TypescriptMetaClassUtils;
 public:
  Constructor() {
    meta_kind_ = MetaTypeKind::kConstructor;
  }
  /**
   * \brief Destructor
   */
  ~Constructor();

  FORCEINLINE ICtorCaller* GetOneCaller(FuncLanguageType caller_type) const {
    auto& caller_array = reg_ctor_callers_[(int)caller_type];
    if (GBF_LIKELY(!caller_array.empty())) {
      return caller_array[0];
    } else {
      return nullptr;
    }
  }

  FORCEINLINE const CtorCallerArray& GetAllCaller(FuncLanguageType caller_type) const { 
    return reg_ctor_callers_[(int)caller_type]; 
  }

  CtorCallerArray GetMatchedArgNumberCaller(FuncLanguageType caller_type, int arg_number) const;

  void FireBuilderChanged();
 protected:
  void Clear();
  void AddCaller(FuncLanguageType call_type, ICtorCaller* caller);
 protected:
  CtorCallerArray reg_ctor_callers_[(int)FuncLanguageType::Total];
  
};

}  // namespace reflection
}  // namespace gbf
