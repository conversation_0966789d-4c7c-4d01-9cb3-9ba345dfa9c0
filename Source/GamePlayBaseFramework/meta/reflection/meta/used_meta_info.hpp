#pragma once

#include "reflection/reflection_fwd.hpp"
#include "reflection/error/errors.hpp"
////#include "reflection/storage/data_holder.hpp"
////#include "reflection/traits/id_traits.hpp"
////#include "reflection/traits/type_traits.hpp"

namespace gbf {
namespace reflection {

//Use of the UserMetaInfo let UserObject can only hold one pointer for more information
struct UsedMetaInfo {
  const MetaClass*    meta_class = nullptr;
  const ObjectVtable* obj_vtable = nullptr;
  uint64_t            tid = 0;
};

}
}

