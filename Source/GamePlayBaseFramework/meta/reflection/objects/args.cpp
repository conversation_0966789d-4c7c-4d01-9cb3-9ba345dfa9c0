
#include "reflection/objects/args.hpp"
#include "reflection/objects/value.hpp"

namespace gbf {
namespace reflection {

const Args Args::empty;

size_t Args::GetCount() const { return values_.size(); }

const Value& Args::operator[](size_t index) const {
  // Make sure that the index is not out of range
  if (index >= values_.size()) PONDER_ERROR(OutOfRange(index, values_.size()));

  return values_[index];
}

Args Args::operator+(const Value& arg) const {
  Args newArgs(*this);
  newArgs += arg;

  return newArgs;
}

Args& Args::operator+=(const Value& arg) {
  values_.push_back(arg);

  return *this;
}

Args& Args::Insert(size_t index, const Value& v) {
  values_.insert(values_.begin() + index, v);
  return *this;
}

Value Args::Pop() {
  if (values_.size() > 0) {
    Value tmp_val = *(values_.rbegin());
    values_.pop_back();
    return tmp_val;
  } else {
    return Value();
  }
}

void Args::Resize(size_t total) { values_.resize(total); }

}  // namespace reflection
}  // namespace gbf
