#include "reflection/objects/array_object.hpp"
#include "reflection/vtable/array_vtable.hpp"
#include "reflection/meta/meta_array.hpp"
////#include "reflection/objects/value.hpp"

namespace gbf {
namespace reflection {
//-------------------------------------------------------------------------------------
const ArrayObject ArrayObject::nothing;
//-------------------------------------------------------------------------------------
ArrayObject::ArrayObject() {}

ArrayObject::ArrayObject(const ArrayObject& other): array_vtbl_(other.array_vtbl_), meta_array_(other.meta_array_) {
  if(array_vtbl_) {
    array_vtbl_->ctor_from_other_holder_(&data_holder_, &(other.data_holder_));
  }
}

ArrayObject::ArrayObject(ArrayObject&& other) noexcept {
  array_vtbl_ = other.array_vtbl_;
  meta_array_ = other.meta_array_;
  if(array_vtbl_) {
    array_vtbl_->ctor_from_other_holder_(&data_holder_, &(other.data_holder_));
  }
  other.Dispose();
}

ArrayObject::ArrayObject(MetaArray const* meta_array, StorageType storage_type):meta_array_(meta_array) {
  array_vtbl_ = meta_array_->GetStorageVtableByType(storage_type);
  assert(array_vtbl_ && "array vtable must not null here!");
  array_vtbl_->ctor_default_(&data_holder_, nullptr);
}

ArrayObject::ArrayObject(MetaArray const* meta_array, StorageType storage_type, const void* obj): meta_array_(meta_array) {
  array_vtbl_ = meta_array_->GetStorageVtableByType(storage_type);
  assert(array_vtbl_ && "array vtable must not null here!");
  array_vtbl_->ctor_from_other_object_(&data_holder_, obj, nullptr);
}

ArrayObject::~ArrayObject() {
  Dispose();
}

void ArrayObject::Dispose() {
  if (array_vtbl_ != nullptr) {
    array_vtbl_->dtor_(&data_holder_);
  }
  array_vtbl_ = nullptr;
}


gbf::reflection::ArrayObject& ArrayObject::operator=(const ArrayObject& other) {
  Dispose();
  array_vtbl_ = other.array_vtbl_;
  if (array_vtbl_) {
    array_vtbl_->ctor_from_other_holder_(&data_holder_, &(other.data_holder_));
  }
  return *this;
}

gbf::reflection::ArrayObject& ArrayObject::operator=(ArrayObject&& other) noexcept {
  Dispose();
  array_vtbl_ = other.array_vtbl_;
  if (array_vtbl_) {
    array_vtbl_->ctor_from_other_holder_(&data_holder_, &(other.data_holder_));
  }
  other.Dispose();
  return *this;
}

size_t ArrayObject::GetSize() const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    return array_vtbl_->get_size_(array_vtbl_, &data_holder_);
  } else {
    return 0;
  }
}

void ArrayObject::SetSize(size_t size) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    array_vtbl_->set_size_(array_vtbl_, &data_holder_, size);
  }
}

Value ArrayObject::GetElement(size_t index) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    return array_vtbl_->get_element_(array_vtbl_, &data_holder_, index);
  } else {
    return Value::nothing;
  }
}

void ArrayObject::SetElement(size_t index, const Value& value) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    array_vtbl_->set_element_(array_vtbl_, &data_holder_, index, value);
  }
}

void ArrayObject::InsertElement(size_t before, const Value& value) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    array_vtbl_->insert_element_(array_vtbl_, &data_holder_, before, value);
  }
}

void ArrayObject::RemoveElement(size_t index) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    array_vtbl_->remove_element_(array_vtbl_, &data_holder_, index);
  }
}

bool ArrayObject::operator==(const ArrayObject& other) const {
  if (array_vtbl_ && other.array_vtbl_) {
    return GetPointer() == other.GetPointer();
  } else if (!array_vtbl_ && !other.array_vtbl_) {
    return true;  // both are UserObject::nothing
  }

  return false;
}

bool ArrayObject::operator<(const ArrayObject& other) const {
  if (array_vtbl_) {
    if (other.array_vtbl_) {
      return GetPointer() < other.GetPointer();
    }
  }
  return false;
}

void* ArrayObject::GetPointer() const { 
  return array_vtbl_ ? array_vtbl_->to_pointer_(&data_holder_) : nullptr; 
}

gbf::reflection::ValueKind ArrayObject::GetElementKind() const {
  if (array_vtbl_ != nullptr) {
    return array_vtbl_->value_kind_;
  } else {
    return ValueKind::kNone;
  }
}

const uint64_t ArrayObject::GetMetaId() const { return array_vtbl_ != nullptr ? array_vtbl_->id_ : 0; }

void ArrayObject::PushBackElement(const Value& value) const {
  if (GBF_LIKELY(array_vtbl_ != nullptr)) {
    array_vtbl_->push_back_element_(array_vtbl_, &data_holder_, value);
  }
}

ArrayObject ArrayObject::Clone() const {
  if (*this == ArrayObject::nothing) {
    return ArrayObject::nothing;
  } else {
    return ArrayObject{meta_array_, kOwnedStorageType, GetPointer()};
  }
}

ArrayObject ArrayObject::CloneSafe() const noexcept {
  if (*this == ArrayObject::nothing) {
    return ArrayObject::nothing;
  } else {
    return ArrayObject{meta_array_, kOwnedStorageType, GetPointer()};
  }
}

}  // namespace reflection
}  // namespace gbf
