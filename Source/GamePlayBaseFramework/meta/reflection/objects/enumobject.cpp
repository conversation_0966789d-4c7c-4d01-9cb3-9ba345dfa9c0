
#include "reflection/objects/enumobject.hpp"
#include "reflection/meta/meta_enum.hpp"

namespace gbf {
namespace reflection {

long EnumObject::GetValue() const { return value_; }

IdReturn EnumObject::GetName() const { return enum_->GetItemName(value_); }

const MetaEnum& EnumObject::GetEnum() const { return *enum_; }

bool EnumObject::operator==(const EnumObject& other) const {
  return (enum_ == other.enum_) && (value_ == other.value_);
}

bool EnumObject::operator<(const EnumObject& other) const {
  if (enum_ != other.enum_) {
    return enum_ < other.enum_;
  } else {
    return value_ < other.value_;
  }
}

EnumObject EnumObject::Clone() const {
  return EnumObject{ *this };
}

EnumObject EnumObject::CloneSafe() const noexcept {
  return EnumObject{ *this };
}

}  // namespace reflection
}  // namespace gbf
