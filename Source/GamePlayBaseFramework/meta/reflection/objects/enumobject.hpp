#pragma once

#include <string>
#include "reflection/reflection_export.hpp"
#include "reflection/reflection_fwd.hpp"
#include "reflection/vtable/enum_vtable.hpp"


namespace gbf {
namespace reflection {

/**
 * \brief Wrapper to manipulate enumerated values in the Ponder system
 *
 * reflection::EnumObject is an abstract representation of enum values, and supports
 * conversions from strings and integers.
 *
 * \sa UserObject
 */
class GBF_REFLECTION_API EnumObject {
 public:
  /**
   * \brief Construct the enum object from an enumerated value
   *
   * \param value Value to store in the enum object
   */
  template <typename T>
  EnumObject(T value, typename std::enable_if<std::is_enum<T>::value>::type* = 0);

  /**
   * \brief Get the value of the enum object
   *
   * \return Integer value of the enum object
   */
  long GetValue() const;

  /**
   * \brief Get the value of the enum class object
   *
   * \return Enum typed value of the enum class object
   */
  template <typename E>
  E GetValue() const;

  /**
   * \brief Get the name of the enum object
   *
   * \return String containing the name of the enum object
   */
  IdReturn GetName() const;

  /**
   * \brief Retrieve the metaenum of the stored enum object
   *
   * \return Reference to the object's metaenum
   */
  const MetaEnum& GetEnum() const;

  /**
   * \brief Operator == to compare equality between two enum objects
   *
   * Two enum objects are equal if their metaenums and values are both equal
   *
   * \param other Enum object to compare with this
   *
   * \return True if both enum objects are the same, false otherwise
   */
  bool operator==(const EnumObject& other) const;

  /**
   * \brief Operator < to compare two enum objects
   *
   * \param other Enum object to compare with this
   *
   * \return True if this < other
   */
  bool operator<(const EnumObject& other) const;

  EnumObject Clone() const;
  EnumObject CloneSafe() const noexcept;
 private:
  long value_;        ///< Value
  MetaEnum const* enum_;  ///< Metaenum associated to the value
};

}  // namespace reflection
}  // namespace gbf

#include "reflection/objects/enumobject.inl"
