#pragma once

#include <variant>

#include "reflection/reflection_fwd.hpp"

#include "reflection/error/errors.hpp"
#include "reflection/objects/userobject.hpp"
#include "reflection/objects/args.hpp"
#include "reflection/storage/data_holder.hpp"
#include "reflection/traits/type_traits.hpp"
#include "reflection/objects/classcast.hpp"
#include "reflection/objects/value.hpp"
#include "reflection/objects/valueimpl.hpp"
#include "reflection/objects/make_user_object.hpp"

////#include "reflection/objects/objectholder.hpp"
#include "reflection/utils/util.hpp"
#include "memory/gc/gc_make.hpp"



namespace gbf {
namespace reflection {

namespace detail {

// Convert reflection::Value to type
template <typename T, typename E = void>
struct TValueTo {
  static T Convert(const Value& value) { return std::visit(TConvertVisitor<T>(), value.value_); }
};

// Don't need to convert, we're returning a Value
template <>
struct TValueTo<Value> {
  static Value Convert(const Value& value) { return value; }
  static Value Convert(Value&& value) { return std::move(value); }
};

// Convert Values to pointers for basic types
template <typename T>
struct TValueTo<T*,
                typename std::enable_if<!TIsUserType<T>::value>::type>  ////struct ValueTo<T*, typename
                                                                        /// std::enable_if<!hasStaticTypeDecl<T>()>::type>
{
  static T* Convert(const Value& value) { return value_ref_as<detail::BuildInValueRef>(value).GetRef<T>(); }
};

// Convert Values to references for basic types
// template <typename T>
// struct ValueTo<T&, typename std::enable_if<!hasStaticTypeDecl<T>()>::type>
//{
//    static T convert(const Value& value)
//    {
//        return *static_cast<T*>(value.to<UserObject>().pointer());
//    }
//};

}  // namespace detail

template <typename T, typename StoragePolicy>
inline Value make_value(const T& val, StoragePolicy policy) {
  static_assert(std::is_base_of_v<storage_policy, StoragePolicy>, "Found not support storage policy here!");
  Value ret;

  using RealT = std::remove_cv_t<std::remove_reference_t<T>>;

  using PropTraits = typename detail::TTypeTraits<T>;
   using withoutpointerT = std::remove_pointer_t<RealT>;
  if constexpr (std::is_same_v<RealT, Value>) {
    //Handle for Value type here
    ret = val;
  }

  else if constexpr (std::is_pointer_v<T> && !std::is_base_of_v<gbf::reflection::RttiBase, withoutpointerT> && !std::is_same_v<gbf::reflection::RttiBase, withoutpointerT>)
  {
      ret = detail::BuildInValueRef::Make<std::remove_pointer_t<T>>(val);
  }
  else if constexpr (std::is_same_v<RealT, std::shared_ptr<Value>>) {
    //Handle for std::shared_ptr<Value> here
    ret = *val;
  }else if constexpr(PropTraits::kind == ReferenceKind::kSmartPointer) {
    return Value(__box_rtti_object(val.get()));
  } else {
    ////static_assert(reflection::detail::TValueMapper<T>::kind == MapType<T>(), "KIND_TYPE_MUST_BE_SAME_HERE");
    ret.kind_ = reflection::detail::TValueMapper<T>::kind;
    ret.value_ = reflection::detail::TValueMapper<T>::to(val);
  }
  return ret;
}

////template <typename T, typename StoragePolicy>
////inline Value make_value(T* val, StoragePolicy policy) {
////  static_assert(std::is_base_of_v<storage_policy, StoragePolicy> && "Found not support storage policy here!");
////  Value ret;
////  static_assert(reflection::detail::TValueMapper<T>::kind == MapType<T>() && "Must same here!");
////  ret.kind_ = reflection::detail::TValueMapper<T>::kind;
////  ret.value_ = reflection::detail::TValueMapper<T>::to(val);
////
////  return ret;
////}

template <typename T>
inline T value_cast(const Value& val) {
  try {
    return detail::TValueTo<T>::Convert(val);
  } catch (detail::bad_conversion&) {
    PONDER_ERROR(BadType(val.kind(), MapType<T>()));
  }
}

template <typename T>
inline T& value_ref_as(const Value& val) {
  using DataType = typename std::remove_const_t<T>;
  try {
    if constexpr (detail::TIsUserType<T>::value) {
      // Support for user type here
      return user_object_ref_as<DataType>(std::get<UserObject>(val.value_));
    } else {
      // other types
      return std::get<DataType>(const_cast<Value&>(val).value_);
    }
  } catch (std::bad_variant_access&) {
    PONDER_ERROR(BadType(val.kind(), MapType<DataType>()));
  }
}

template <typename T>
inline bool value_can_convert_to(const Value& val) {
  try {
    detail::TValueTo<T>::Convert(val);
    return true;
  } catch (...) {
    return false;
  }
}

}  // namespace reflection
}  // namespace gbf

