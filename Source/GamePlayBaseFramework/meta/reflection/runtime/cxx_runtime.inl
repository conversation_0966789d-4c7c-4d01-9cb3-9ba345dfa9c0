#pragma once


//--------------------------------------------------------------------------------------
// .inl

namespace gbf {
namespace reflection {
namespace cxx {

template <typename... A>
inline UserObject ObjectFactory::CreateStorageExternal(void* external_ptr, A... args) const {
  Args a({args...});
  return ConstructStorageExternal(external_ptr, a);
}

template <typename... A>
inline UserObject ObjectFactory::CreateStorageRemoteShared(A... args) const {
  Args a({args...});
  return ConstructStorageRemoteShared(a);
}

template <typename... A>
inline UserObject ObjectFactory::CreateStorageGc(A... args) const {
  Args a({args...});
  return ConstructStorageGc(a);
}

template <typename... A>
inline Value ObjectCaller::Call(const UserObject& obj, A&&... vargs) {
  if (obj.IsEmpty()) PONDER_ERROR(NullObjectError(&obj.GetClass()));

  Args args(detail::ArgsBuilder<A...>::MakeArgs(std::forward<A>(vargs)...));
  args.Insert(0, make_value(obj));

  auto& all_caller = function_.GetAllCaller(FuncLanguageType::AsCxx);
  if (GBF_UNLIKELY(all_caller.empty())) {
    PONDER_ERROR(OverloadFunctionNotFound(function_.name(), "", args.GetCount()));
  }

  for (auto func : all_caller) {
    auto* onefunc = (reflection::detail::cxx::FunctionCaller*)func;
    if (onefunc->GetParamCount() != args.GetCount()) {
      continue;
    }

    bool check_arg_suc = onefunc->is_just_one() || onefunc->CheckArgs(args);
    if (!check_arg_suc) {
      continue;
    }

    return onefunc->Execute(args);
  }

  PONDER_ERROR(OverloadFunctionNotFound(function_.name(), "", args.GetCount()));
}

template <typename... A>
inline Value FunctionCaller::Call(A... vargs) {
  Args args(detail::ArgsBuilder<A...>::MakeArgs(std::forward<A>(vargs)...));

  auto& all_caller = function_.GetAllCaller(FuncLanguageType::AsCxx);
  if (GBF_UNLIKELY(all_caller.empty())) {
    PONDER_ERROR(OverloadFunctionNotFound(function_.name(), "", args.GetCount()));
  }

  for (auto func : all_caller) {
    auto* onefunc = (reflection::detail::cxx::FunctionCaller*)func;
    if (onefunc->GetParamCount() != args.GetCount()) {
      continue;
    }

    bool check_arg_suc = onefunc->is_just_one() || onefunc->CheckArgs(args);
    if (!check_arg_suc) {
      continue;
    }

    return onefunc->Execute(args);
  }

  PONDER_ERROR(OverloadFunctionNotFound(function_.name(), "", args.GetCount()));
}

inline bool FunctionCaller::Check(const Args& args) {
  auto& all_caller = function_.GetAllCaller(FuncLanguageType::AsCxx);
  if (GBF_UNLIKELY(all_caller.empty())) {
    return false;
  }

  reflection::detail::cxx::FunctionCaller* caller = nullptr;
  for (auto func : all_caller) {
    auto* onefunc = (reflection::detail::cxx::FunctionCaller*)func;
    if (onefunc->CheckArgs(args)) {
      caller = onefunc;
      break;
    }
  }

  return (caller != nullptr);
}

}  // namespace cxx
}  // namespace reflection

}  // namespace gbf
//--------------------------------------------------------------------------------------

// define once in client program to instance this
////#ifdef PONDER_USES_RUNTIME_IMPL
namespace gbf {
namespace reflection {
namespace cxx {

UserObject ObjectFactory::ConstructStorageExternal(void* external_ptr, const Args& args) const {
  // Search an arguments match among the list of available constructors
  auto& ctor = class_.GetCtor();
  auto& all_caller = ctor.GetAllCaller(FuncLanguageType::AsCxx);

  for (auto caller : all_caller) {
    auto* cxx_caller = (reflection::detail::cxx::CtorCaller*)caller;
    if (cxx_caller->GetArgNumber() != args.GetCount()) {
      continue;
    }

    bool is_arg_match = cxx_caller->is_just_one() || cxx_caller->Matches(args);
    if (!is_arg_match) {
      continue;
    }
    //Match found: use the constructor to create the new instance
    return cxx_caller->CreateStorageExternal(external_ptr, args);
  }

  return UserObject::nothing;  // no match found
}

UserObject ObjectFactory::ConstructStorageRemoteShared(const Args& args) const {
  // Search an arguments match among the list of available constructors
  auto& ctor = class_.GetCtor();
  auto& all_caller = ctor.GetAllCaller(FuncLanguageType::AsCxx);

  for (auto caller : all_caller) {
    auto* cxx_caller = (reflection::detail::cxx::CtorCaller*)caller;
    if (cxx_caller->GetArgNumber() != args.GetCount()) {
      continue;
    }

    bool is_arg_match = cxx_caller->is_just_one() || cxx_caller->Matches(args);
    if (!is_arg_match) {
      continue;
    }
    // Match found: use the constructor to create the new instance
    return cxx_caller->CreateStorageRemoteShared(args);
  }

  return UserObject::nothing;  // no match found
}

UserObject ObjectFactory::ConstructStorageGc(const Args& args) const {
  // Search an arguments match among the list of available constructors
  auto& ctor = class_.GetCtor();
  auto& all_caller = ctor.GetAllCaller(FuncLanguageType::AsCxx);

  for (auto caller : all_caller) {
    auto* cxx_caller = (reflection::detail::cxx::CtorCaller*)caller;

    if (cxx_caller->GetArgNumber() != args.GetCount()) {
      continue;
    }

    bool is_arg_match = cxx_caller->is_just_one() || cxx_caller->Matches(args);
    if (!is_arg_match) {
      continue;
    }
    // Match found: use the constructor to create the new instance
    return cxx_caller->CreateStorageGc(args);
  }

  return UserObject::nothing;  // no match found
}

ObjectCaller::ObjectCaller(const Function& f) : function_(f) {}

FunctionCaller::FunctionCaller(const Function& f) : function_(f) {}

//-----------------------------------------------------------------------------------------------

}  // namespace cxx
}  // namespace reflection
}  // namespace gbf

