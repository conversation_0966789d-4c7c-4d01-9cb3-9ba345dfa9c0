/****************************************************************************
**
** This file is part of the <PERSON><PERSON> library, formerly CAMP.
**
** The MIT License (MIT)
**
** Copyright (C) 2009-2014 TEGESO/TEGESOFT and/or its subsidiary(-ies) and mother company.
** Copyright (C) 2015-2020 Nick Trout.
**
** Permission is hereby granted, free of charge, to any person obtaining a copy
** of this software and associated documentation files (the "Software"), to deal
** in the Software without restriction, including without limitation the rights
** to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
** copies of the Software, and to permit persons to whom the Software is
** furnished to do so, subject to the following conditions:
**
** The above copyright notice and this permission notice shall be included in
** all copies or substantial portions of the Software.
**
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
** IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
** FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
** AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
** LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
** OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
** THE SOFTWARE.
**
****************************************************************************/

#pragma once

#include <cstdint>
#include "reflection/config.hpp"
#include "reflection/reflection_fwd.hpp"
#include "reflection/traits/data_type_traits.hpp"
#include "reflection/traits/type_traits_extend.hpp"
#include "reflection/traits/array_traits.hpp"
#include "core/utils/meta_type_hash.hpp"

////#include <typeindex>

/**
 * \namespace ponder
 * \brief Root namespace that encapsulates all of Ponder.
 *
 * \namespace reflection::detail
 * \brief Ponder hidden implementation details.
 *
 * \namespace reflection::policy
 * \brief Ponder policy options.
 */

namespace gbf {
namespace reflection {




// Note: This may have issues with DLLs?
using TypeId = uint64_t;  // Used to uniquely identify a type.

/**
 * \brief Special empty type associated to \c noType
 *
 * This type is mainly used when writing custom \c Value visitors
 */
struct NoType {};

/**
 * \brief Enumeration of abstract value types supported by Ponder Values
 *
 * \sa Value ValueMapper
 */
enum class ValueKind : uint32_t {
  kNone = 0,   ///< No type has been defined yet
  kBoolean,    ///< Boolean type (`bool`)
  kInteger,    ///< Integer types (`unsigned`,`signed`, `char`, `short`, `int`, `long`)
  kReal,       ///< Real types (`float`, `double`)
  kString,     ///< String types (`char*`, `reflection::String`)
  kEnum,       ///< Enumerated types
  kArray,      ///< Array types (`T[]`, `std::vector`, `std::list`)
  kReference,  ///< Reference types (`T*`, `const T*`, `T&`, `const T&`)
  kUser,       ///< User-defined classes
  kUnknown,
  ////kVector3,    ///< gbf::math::vector3
};
#pragma warning(push)
#pragma warning(disable : 4702)
template<typename T>
constexpr ValueKind CastToValueKind()
{
    static_assert(std::is_arithmetic_v<T>);

    if constexpr (std::is_same<T, bool>::value)
    {
        return ValueKind::kBoolean;
    }
    else if (std::is_integral_v<T>)
    {
        return ValueKind::kInteger;
    }
    else if (std::is_floating_point_v<T>)
    {
        return ValueKind::kReal;
    }
    return ValueKind::kNone;
}
#pragma warning(pop)

/**
 * \brief Enumeration of ways to reference an object
 *
 * \sa Value ValueMapper
 */
enum class ReferenceKind {
  kNone,          ///< not an object
  kInstance,      ///< an object instance, e.g. `int`, `T`
  kPointer,       ///< pointer to an object, e.g. `T*`
  kReference,     ///< reference to an object, e.g. T&
  kSmartPointer,  ///< smart pointer reference, e.g. `std::shared_ptr<T>`
  kBuiltinArray,  ///< builtin array, e.g. `T[N]`
};

/**
 * \brief Enumeration of the kinds of function recognised
 *
 * \sa Function
 */
enum class FunctionKind {
  kNone,             ///< not a function
  kFunction,         ///< a function
  kMemberFunction,   ///< function in a class or struct
  kFunctionWrapper,  ///< `std::function<>`
  kBindExpression,   ///< `std::bind()`
  kLambda,           ///< lambda function `[](){}`
  kOverload,         /// for overload function(a container for many other function)
};

/**
 * \brief Enumeration of the kinds of property exposed
 *
 * \sa Property, Function
 */
enum class PropertyKind {
  kFunction,     ///< a function
  kMemberObject  ///< member object in a class or struct
};

/**
 * \brief Enumeration of the kinds of Property accessors use
 *
 * \sa Property
 */
enum class PropertyAccessKind { kSimple, kEnum, kContainer, kUser };

enum class MetaTypeKind {
  kUnknown,
  kConstructor,
  kProperty,
  kFunction,
  kStaticProperty,
  kMetaEnum,
  kMetaArray,
  kMetaMap,
};


/**
 * \brief Base class for all supported types.
 */
class GBF_REFLECTION_API Type {
 public:
  virtual ~Type() {}

  MetaTypeKind GetMetaKind() const {
    return meta_kind_;
  }
 protected:
  MetaTypeKind meta_kind_ = MetaTypeKind::kUnknown;
};

namespace policy {

/**
 * \brief Enumeration of the kinds of return policy
 */
enum class ReturnKind {
  kNoReturn,  // void, returns nothing
  kCopy,
  kInternalRef,
  kMultiple
};

/**
 * \brief Enumeration of the kinds of parameter type
 *
 * Parameters are the types in a function definition. Arguments are what you pass to them.
 */
enum class ParameterKind {
  kPassByValue,      ///< Pass by value, e.g. `foo(T)`
  kPassByReference,  ///< Pass by reference, e.g. `foo(const T*, const U&)`
  //    ReturnObject        ///< Return object, e.g. `foo(T**, T*&)`
};

/**
 * \brief Call return copy policy
 *
 * When added to a function declaration this sets the call return policy to copy. Any
 * values returned by a function are copied. This is the default behaviour.
 */
struct ReturnCopy {
  static constexpr ReturnKind kind = ReturnKind::kCopy;  ///< The policy enum kind.
};

/**
 * \brief Call return internal reference policy
 *
 * When added to a function declaration this sets the call return policy to internal
 * reference. References returned by the function are returned as references values. This
 * is useful for declaring things like singletons, which return values that we do not
 * want to copy.
 */
struct ReturnInternalRef {
  static constexpr ReturnKind kind = ReturnKind::kInternalRef;  ///< The policy enum kind.
};

/**
 * \brief Return multiple values
 *
 * When added to a function declaration this sets the call return policy to support multiple
 * value returning. The function should return a `std::tuple<...>` containing the multiple
 * values. Note that the tuple needs to be declared as a type.
 */
struct ReturnMultiple {
  static constexpr ReturnKind kind = ReturnKind::kMultiple;  ///< The policy enum kind.
};

struct ReturnNone {
  static constexpr ReturnKind kind = ReturnKind::kNoReturn;  ///< The policy enum kind.
};

struct Parameter {};

// struct PassByValue
//{
//    static constexpr ParameterKind kind = ParameterKind::PassByValue; ///< The policy enum kind.
//};
//
// struct PassByReference
//{
//    static constexpr ParameterKind kind = ParameterKind::PassByReference; ///< The policy enum kind.
//};

}  // namespace policy

template <typename T, typename IT>
class TViewIterator {
 private:
  IT m_iter;
  class Holder {
    const T m_value;

   public:
    Holder(IT value) : m_value(value) {}
    T operator*() { return m_value; }
  };

 public:
  using value_type = T;
  using difference_type = std::ptrdiff_t;
  using iterator_category = std::input_iterator_tag;

  explicit TViewIterator(IT value) : m_iter(value) {}
  value_type operator*() const { return *m_iter->second; }
  bool operator==(const TViewIterator& other) const { return m_iter == other.m_iter; }
  bool operator!=(const TViewIterator& other) const { return !(*this == other); }
  Holder operator++(int) {
    Holder ret(m_iter);
    ++m_iter;
    return ret;
  }
  TViewIterator& operator++() {
    ++m_iter;
    return *this;
  }
};

template <typename T, typename IT>
class TView {
 public:
  using Iterator = TViewIterator<T, IT>;

  TView(IT b, IT e) : m_begin(b), m_end(e) {}

  Iterator begin() { return Iterator(m_begin); }
  Iterator end() { return Iterator(m_end); }

 private:
  IT m_begin, m_end;
};


namespace detail{

  // Is T a user type.
template <typename T>
struct TIsUserType {
  using UseDataType = typename ::gbf::reflection::detail::TDataType<T>::Type;
  static constexpr bool value = std::is_class<UseDataType>::value && !std::is_same<UseDataType, Value>::value &&
                                !std::is_same<UseDataType, UserObject>::value && !std::is_same<UseDataType, detail::BuildInValueRef>::value &&
                                !std::is_same<UseDataType, reflection::String>::value && !std::is_same<UseDataType, ArrayObject>::value;
};




////|| is_sequential_container<T>::value				//a sequential container is a user object
////	|| is_associative_container<T>::value;				//a associative container is a user object

// Decide whether the UserObject holder should be ref (true) or copy (false).
template <typename T>
struct TIsUserObjRef {
  static constexpr bool value = std::is_pointer<T>::value || std::is_reference<T>::value;
};

// Calculate TypeId for T
template <typename T>
inline constexpr TypeId CalcTypeId() {
  ////return TypeId(typeid(T));
  return gbf::MetatypeHash::Hash<T>();
}

template <typename T>
inline constexpr IdRef CalcTypeName() {
  ////return TypeId(typeid(T));
  return gbf::MetatypeHash::NamePretty<T>();
}

} // namespace detail


/**
 * \brief Map a C++ type to a Ponder type
 *
 * This function simply returns the mapping defined by ValueMapper (i.e. \c ValueMapper<T>::type).
 *
 * \return Ponder type which T maps to
 */
template <typename T>
constexpr ValueKind MapType() {

  if constexpr(std::is_abstract_v<T>) {
    return ValueKind::kUser;
  } else if constexpr (std::is_same_v<reflection::String, T> || std::is_same_v<const reflection::String, T> ||
                       std::is_same_v<reflection::detail::string_view, T> || std::is_same_v<const char*, T> || std::is_same_v<char*, T>) {
    return ValueKind::kString;
  } else if constexpr(std::is_pointer_v<T> && !detail::TIsUserType<T>::value) {
    //build in reference
    return ValueKind::kReference;
  } else if constexpr(std::is_same_v<detail::BuildInValueRef, T>) {
    return ValueKind::kReference;
  } else if constexpr(std::is_same_v<bool, T>) {
    return ValueKind::kBoolean;
  } else if constexpr (std::is_integral<T>::value && !std::is_const<T>::value) {
    return ValueKind::kInteger;
  } else if constexpr (std::is_floating_point<T>::value && !std::is_const<T>::value) {
    return ValueKind::kReal;
  } else if constexpr (std::is_array_v<T> && std::is_same_v<char, std::remove_all_extents_t<T>>) {
    //char[N]
    return ValueKind::kString;
  } else if constexpr (std::is_array_v<T> && std::is_same_v<const char, std::remove_all_extents_t<T>>) {
    //const char[N]
    return ValueKind::kString;
  } else if constexpr (detail::TArrayTraits<T>::kIsArray) {
    //Do not need filter here for before handle
    ////&&!std::is_same_v<typename detail::TArrayMapper<T>::ElementType, char> &&
    ////    !std::is_same_v<typename detail::TArrayMapper<T>::ElementType, const char>
    return ValueKind::kArray;
  } else if constexpr(std::is_same_v<T, ValueKind>) {
    //special handle for ValueKind, need here???
    return ValueKind::kString;
  } else if constexpr (std::is_enum_v<T>) {
    return ValueKind::kEnum;
  } else if constexpr (std::is_same_v<T, EnumObject>) {
    return ValueKind::kEnum;
  } else if constexpr (std::is_same_v<T, UserObject>) {
    return ValueKind::kUser;
  } else if constexpr (std::is_same_v<T, ArrayObject>) {
    return ValueKind::kArray;
  } else if constexpr (std::is_same_v<void, T>) {
    return ValueKind::kNone;
  } else if constexpr (std::is_same_v<void, NoType>) {
    return ValueKind::kNone;
  } else if constexpr (std::is_reference_v<T>) {
    ////SAFE_STATIC_ASSERT_FALSE("Can not support reference here!");
    return ValueKind::kNone;
  } else if constexpr (std::is_same_v<T, Value>) {
    return ValueKind::kNone;
  } else if constexpr (TIsSharedPtr<T>::value) {
    SAFE_STATIC_ASSERT_FALSE("Can not support smart pointer here!");
    return ValueKind::kNone;
  } else if constexpr (detail::TIsUserType<T>::value){
    return ValueKind::kUser;
  } else {
    SAFE_STATIC_ASSERT_FALSE("Can not support detect kind for this type!");
    return ValueKind::kNone;
  }
}

template <typename T>
struct TIsBuitinNotStringType {
  using UseDataType = std::remove_cv_t<T>;
  static constexpr ValueKind kind = MapType<UseDataType>();
  static constexpr bool value = kind == ValueKind::kNone || kind == ValueKind::kBoolean || kind == ValueKind::kInteger || kind == ValueKind::kReal;
};


template <typename T>
struct TIsBuiltInNotStringPointerType {
  using WithPointerDataType = std::remove_cv_t<T>;
  using WithOutPointerDataType = std::remove_pointer_t<WithPointerDataType>;

  ////static constexpr ValueKind kind = MapType<WithOutPointerDataType>();
  static constexpr ValueKind kind = MapType<WithPointerDataType>();
  static constexpr bool value = !std::is_same_v<WithPointerDataType, WithOutPointerDataType> && (kind != ValueKind::kString) && TIsBuitinNotStringType<WithOutPointerDataType>::value;
};

}  // namespace reflection
}  // namespace gbf
