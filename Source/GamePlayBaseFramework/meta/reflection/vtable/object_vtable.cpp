#include "reflection/vtable/object_vtable.hpp"

#include <unordered_map>
#include <mutex> 
#include "reflection/meta/meta_class.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf {
namespace reflection {


using MetaClassPtr = std::shared_ptr<MetaClass>;

struct ObjectMetaManager {
  std::vector<MetaClassPtr>                                 all_meta_class_array;
  std::unordered_map<std::string, MetaClass const*>         named_meta_class_map;
  std::unordered_map<TypeId, MetaClass const*>              id_meta_class_map;
  std::unordered_map<TypeId, MetaClass const*>              cid_meta_class_map;
};

static ObjectMetaManager s_object_meta_manager;
static std::mutex s_object_meta_manager_mutex;

void detail::link_object_vtable_to_system(
  const ObjectVtable* remote_vtbl,
  const ObjectVtable* remote_shared_vtbl,
  const ObjectVtable* gc_vtbl,
  const ObjectVtable* local_vtbl,
  int offset_to_rtti,
  ObjDynInfo* dyn_info) {
  assert(remote_vtbl && remote_shared_vtbl && gc_vtbl && "Can not has null vtable here!");
  
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex);

  //Change link flag here
  dyn_info->link_to_system = true;

  auto iter = s_object_meta_manager.id_meta_class_map.find(remote_vtbl->id_);
  if(GBF_UNLIKELY(iter == s_object_meta_manager.id_meta_class_map.end())) {
    //need create meta class here
    auto ptr = std::make_shared<MetaClass>(remote_vtbl->id_, remote_vtbl->name_, remote_vtbl, remote_shared_vtbl, gc_vtbl, local_vtbl, offset_to_rtti);
    s_object_meta_manager.all_meta_class_array.push_back(ptr);
    s_object_meta_manager.id_meta_class_map.emplace(std::make_pair(remote_vtbl->id_, ptr.get()));
    s_object_meta_manager.named_meta_class_map.emplace(std::make_pair(std::string(remote_vtbl->name_), ptr.get()));
    dyn_info->meta_class = ptr.get();

    //Call previous meta register function here~~
    // 注意：如果 remote_vtbl->s_pre_meta_register_func() 内部也修改了 s_object_meta_manager 或其他共享数据，
    // 并且它不是设计为在持有锁的情况下调用的，那么将此调用移出锁的范围可能更安全，
    // 或者确保 s_pre_meta_register_func 本身是线程安全的或可重入的。
    // 为简单起见，暂时保留在锁内，但这是一个需要注意的点。
    remote_vtbl->s_pre_meta_register_func();
  } else {
    if (remote_vtbl->name_ != iter->second->name()) [[unlikely]] {
      PONDER_ERROR(SameTypeId(iter->first, iter->second->name(), remote_vtbl->name_));
    }
    dyn_info->meta_class = iter->second;
  }
}

int detail::bind_meta_class_alias_name(MetaClass* meta_class, const std::string& alias_name) {
  if(GBF_UNLIKELY(alias_name.length() == 0)) {
    WRN_M(LOG_MODULE_RELFECTION, "bind_meta_class_alias_name(), Can not use null name here!");
    return 0;
  }

  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex);

  auto iter = s_object_meta_manager.named_meta_class_map.find(alias_name);
  if(GBF_UNLIKELY(iter != s_object_meta_manager.named_meta_class_map.end())) {
    WRN_M(LOG_MODULE_RELFECTION, "bind_meta_class_alias_name(), already has name:%s in system!", alias_name.c_str());
    return 0; 
  }

  s_object_meta_manager.named_meta_class_map.emplace(std::make_pair(alias_name, meta_class));
  meta_class->SetAlias(alias_name); 
  return meta_class->AddAliasCount();
}

bool detail::unbind_meta_class_alias_name(const std::string& alias_name)
{
    if (GBF_UNLIKELY(alias_name.length() == 0)) {
        WRN_M(LOG_MODULE_RELFECTION, "unbind_meta_class_alias_name(), Can not use null name here!");
        return false;
    }

    std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex);
    auto iter = s_object_meta_manager.named_meta_class_map.find(alias_name);
    if (GBF_UNLIKELY(iter == s_object_meta_manager.named_meta_class_map.end())) {
        WRN_M(LOG_MODULE_RELFECTION, "unbind_meta_class_alias_name(), name:%s is not in system!", alias_name.c_str());
        return false;
    }

    s_object_meta_manager.named_meta_class_map.erase(iter);
    return true;
}

GBF_REFLECTION_API MetaClass const* detail::TryGetMetaClassByID(uint64_t id)
{
    std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
    auto iter = s_object_meta_manager.id_meta_class_map.find(id);
    if (iter != s_object_meta_manager.id_meta_class_map.end())
        return iter->second; 

    return nullptr;
}

MetaClass const* query_meta_class_by_name(const std::string& n) {
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
  auto iter = s_object_meta_manager.named_meta_class_map.find(n);
  if(GBF_UNLIKELY(iter == s_object_meta_manager.named_meta_class_map.end())) {
    return nullptr;
  } else {
    return iter->second;
  }
}

MetaClass const* meta_class_by_index(int i)
{
    std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
    if (i < 0 || i >= s_object_meta_manager.all_meta_class_array.size())
    {
        return nullptr;
    }
    else
    {
        return s_object_meta_manager.all_meta_class_array[i].get();
    }
}

size_t meta_class_count()
{
    std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
    return s_object_meta_manager.all_meta_class_array.size();
}

MetaClass const* query_meta_class_by_id(TypeId tid)
{
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex);
  auto iter = s_object_meta_manager.id_meta_class_map.find(tid);
  if(GBF_UNLIKELY(iter == s_object_meta_manager.id_meta_class_map.end())) {
    return nullptr;
  } else {
    return iter->second;
  }
}

MetaClass const* query_meta_class_by_custom_id(TypeId cid) {
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
  auto iter = s_object_meta_manager.cid_meta_class_map.find(cid);
  if (iter != s_object_meta_manager.cid_meta_class_map.end()) [[likely]] {
    return iter->second;
  } else {
    return nullptr;
  }
}

bool regsiter_meta_class_by_custom_id(TypeId cid, MetaClass const* meta) {
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
  return s_object_meta_manager.cid_meta_class_map.emplace(cid, meta).second;
}

void destroy_all_meta_class() {
  std::lock_guard<std::mutex> lock(s_object_meta_manager_mutex); 
  s_object_meta_manager.id_meta_class_map.clear();
  s_object_meta_manager.cid_meta_class_map.clear();
  s_object_meta_manager.named_meta_class_map.clear();
  s_object_meta_manager.all_meta_class_array.clear();
}

uint64_t next_reflection_obj_id() {
  static uint64_t sobj_id_count = 0;

  uint64_t mod_val = (uint64_t)0x1u << RTTI_OBJECT_ID_BITS_COUNT;
  sobj_id_count = (sobj_id_count + 1) % mod_val;
  return sobj_id_count;
}

}  // namespace reflection
}  // namespace gbf
