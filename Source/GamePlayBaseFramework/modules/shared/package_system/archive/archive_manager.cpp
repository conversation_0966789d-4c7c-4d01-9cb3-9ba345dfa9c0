#include "archive/archive_manager.hpp"
#include "archive/archive.hpp"
#include "archive/file_system.hpp"
#include "archive/sqlite_archive.hpp"
#include "archive/zip_archive.hpp"
#include "core/imodules/ilog_module.h"

namespace gbf {

ArchiveManager::ArchiveManager() {
  AddCreator("FileSystem",
             [](std::string_view file_name, bool is_readonly) { return std::make_shared<FileSystemArchive>(file_name, "FileSystem", is_readonly); });

  AddCreator("Sqlite",
             [](std::string_view file_name, bool is_readonly) { return std::make_shared<SqliteArchive>(file_name, "Sqlite", is_readonly); });
  SqliteArchive::InitSqliteConfig();

  AddCreator("Zip",
    [](std::string_view file_name, bool is_readonly) {
      return std::make_shared<ZipArchive>(file_name, "Zip");
    });
}

ArchiveManager::~ArchiveManager() {
  // Unload & delete resources in turn
  for (ArchiveMap::iterator it = mArchives.begin(); it != mArchives.end(); ++it) {
    auto& arch = it->second;
    // Unload
    arch->Unload();
    arch.reset();
  }
  // Empty the list
  mArchives.clear();
}
//-----------------------------------------------------------------------
ArchivePtr ArchiveManager::Load(std::string_view filename, std::string_view archiveType, bool readOnly) {
  ArchiveMap::iterator i = mArchives.find(std::string(filename.data(), filename.length()));
  ArchivePtr arch;

  if (i == mArchives.end()) {
    // Search factories
    auto it = creator_map_.find(std::string(archiveType));
    if (it == creator_map_.end()) {
      // Factory not found
      return arch;
    }

    // call creator~~
    arch = (it->second)(filename, readOnly);
    arch->Load();
    mArchives[std::string(filename)] = arch;
  } else {
    arch = i->second;
  }
  return arch;
}
//-----------------------------------------------------------------------
void ArchiveManager::Unload(Archive* arch) { Unload(arch->get_name()); }
//-----------------------------------------------------------------------
void ArchiveManager::Unload(std::string_view filename) {
  ArchiveMap::iterator i = mArchives.find(std::string(filename));

  if (i != mArchives.end()) {
    i->second->Unload();
    // Find factory to destroy
    auto fit = creator_map_.find(std::string(i->second->get_type()));
    if (fit == creator_map_.end()) {
      // Factory not found

      ERR_M(LOG_MODULE_RESOURCE, "Cannot find an archive factory to deal with archive of type %s in ArchiveManager::Unload()!", i->second->get_type().data());
      return;
    }
    mArchives.erase(i);
  }
}

void ArchiveManager::AddCreator(std::string_view archive_type, ArchiveCreator&& func) { 
  creator_map_[std::string(archive_type)] = func; 
}

}  // namespace gbf
