#include "archive/sqlite_archive.hpp"
#include "core/utils/path_tool.hpp"
#include "core/imodules/ilog_module.h"
#include "core/error/errors.hpp"

namespace gbf {

SqliteArchive::SqliteArchive(std::string_view name, std::string_view archType, bool readOnly) : Archive(name, archType), mReadOnly(readOnly) {
  mName = PathTool::NormalizeFilePath(mName);
}

SqliteArchive::~SqliteArchive() {}

void SqliteArchive::FindFiles(std::string_view pattern, StringVector* simpleList, FileInfoList* detailList) {
  for (const auto& fileName : mFileSet) {
    if (StringUtil::Match(fileName, std::string(pattern))) {
      if (simpleList) {
        simpleList->push_back(fileName);
      }

      if (detailList) {
        FileInfo info;
        std::string dir;
        std::string baseName = fileName;
        size_t dir_pos = fileName.find_last_of('/');
        if (dir_pos != fileName.npos) {
          dir = fileName.substr(0, dir_pos + 1);
          baseName = fileName.substr(dir_pos + 1, fileName.length());
        }

        info.archive = this;
        info.filename = fileName;
        info.basename = baseName;
        info.path = dir;
        detailList->push_back(info);
      }
    }
  }
}

void SqliteArchive::TryOpenSqlite() {
  if (mSqlite == nullptr) {
    Load();
  }
}

bool SqliteArchive::IsCaseSensitive() const { return true; }

void SqliteArchive::Load() {
  mSqlite = Sqlite3Tool::OpenOrCreateDB(mName.c_str(), mReadOnly);
  StringVector fileList;

  Sqlite3Tool::QueryAllFileName(mSqlite, fileList);
  for (const auto& fileName : fileList) {
    if (mFileSet.find(fileName) == mFileSet.end()) mFileSet.insert(fileName);
  }

  Sqlite3Tool::QueryAllRelFileName(mSqlite, fileList);
  for (const auto& fileName : fileList) {
    if (mRelFileSet.find(fileName) == mRelFileSet.end()) mRelFileSet.insert(fileName);
  }
}

void SqliteArchive::Unload() {
  if (mSqlite != nullptr) {
    Sqlite3Tool::CloseDB(mSqlite);
    mSqlite = nullptr;
  }

  mFileSet.clear();
  mRelFileSet.clear();
}

ByteBufferPtr SqliteArchive::ReadFileData(std::string_view filename, bool readOnly /*= true*/) {
  TryOpenSqlite();
  EmbededResourceInfo fileInfo;

  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  if (Sqlite3Tool::QueryEmbededResource(mSqlite, fileName.c_str(), fileInfo)) {
    return fileInfo.databuf;
  }
  return {};
}

DataStreamPtr SqliteArchive::OpenFileStream(std::string_view filename, bool readOnly) {
  GBF_ERROR(NotImplementError("Can not support stream mode in SqliteArchive!"));
}

DataStreamPtr SqliteArchive::CreateWritableFileStream(std::string_view filename) {
  GBF_ERROR(NotImplementError("Can not support stream mode in SqliteArchive!"));
}

void SqliteArchive::RemoveFile(std::string_view filename) {
  TryOpenSqlite();

  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  bool ret;

  ret = Sqlite3Tool::DeleteResFileInfo(mSqlite, fileName.c_str());
  if (ret) mFileSet.erase(fileName);

  ret = Sqlite3Tool::DeleteResConfigInfo(mSqlite, fileName.c_str());
  if (ret) mRelFileSet.erase(fileName);
}

StringVector SqliteArchive::ListFileNames(bool recursive /*= true*/, bool dirs /*= false*/) {
  TryOpenSqlite();

  StringVector ret;
  FindFiles("*", &ret, nullptr);

  return std::move(ret);
}

FileInfoList SqliteArchive::ListFileInfos(bool recursive /*= true*/, bool dirs /*= false*/) {
  TryOpenSqlite();

  FileInfoList ret;
  FindFiles("*", nullptr, &ret);

  return std::move(ret);
}

StringVector SqliteArchive::FindMatchNames(std::string_view pattern, bool recursive /*= true*/, bool dirs /*= false*/) {
  TryOpenSqlite();

  StringVector ret;
  FindFiles(pattern, &ret, nullptr);

  return std::move(ret);
}

bool SqliteArchive::FileExist(std::string_view filename) {
  TryOpenSqlite();

  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  auto it = mFileSet.find(fileName);
  return it != mFileSet.end();
}

bool SqliteArchive::ConfigExist(std::string_view filename) {
  TryOpenSqlite();

  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  auto it = mRelFileSet.find(fileName);
  return it != mRelFileSet.end();
}

time_t SqliteArchive::GetModifiedTime(std::string_view filename) { return 0; }

FileInfoList SqliteArchive::FindMatchInfos(std::string_view pattern, bool recursive /*= true*/, bool dirs /*= false*/) {
  TryOpenSqlite();

  FileInfoList ret;
  FindFiles(pattern, nullptr, &ret);

  return std::move(ret);
}

bool SqliteArchive::ReplaceByEmbeded(const EmbededResourceInfo& info, bool use_compress, bool use_crypto) {
  TryOpenSqlite();

  bool ret = Sqlite3Tool::ReplaceByEmbededResource(mSqlite, info, use_compress, use_crypto);
  if (ret && !FileExist(info.file_name)) mFileSet.insert(info.file_name);
  return ret;
}

void SqliteArchive::QueryVersionInfo(std::string_view filename, ResourceVersionInfo& version) {
  TryOpenSqlite();
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  Sqlite3Tool::QueryResVersionInfo(mSqlite, fileName.c_str(), version);
}

void SqliteArchive::QueryConfigInfo(std::string_view filename, std::string& configInfo) {
  TryOpenSqlite();
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  Sqlite3Tool::QueryResConfigInfo(mSqlite, fileName.c_str(), configInfo);
}

void SqliteArchive::ReplaceConfigInfo(const char* filename, const char* configInfo) {
  TryOpenSqlite();
  std::string fileName = StringUtil::ReplaceAll(std::string(filename), "\\", "/");
  Sqlite3Tool::ReplaceResConfigInfo(mSqlite, fileName.c_str(), configInfo);
}


void SqliteArchive::InitSqliteConfig() {
  ConfigCustomAllocator();
  ConfigLogger();
}

//---------------------------------------------------------
////#define PAGE_CACHED_SIZE 4 * 1024
////#define PAGE_CACHED_COUNT 5
////static void* SqliteCustomMalloc(int size) { return malloc(size); }
////
////static void SqliteCustomFree(void* ptr) { free(ptr); }
////
////static void* SqliteCustomRealloc(void* ptr, int size) { return realloc(ptr, size, Captain::MemoryGroupLabel::General); }
////
////static int SqliteCustomSize(void* ptr) { return 0; }
////
////static int SqliteCustomRoundup(int size) { return (((size) + 7) & ~7); }
////
////static int SqliteCustomInit(void* arg) { return SQLITE_OK; }
////
////static void SqliteCustomShutdown(void* arg) {}
////
void SqliteArchive::ConfigCustomAllocator() {
  ////auto alloc = sqlite3_mem_methods({SqliteCustomMalloc, SqliteCustomFree, SqliteCustomRealloc, SqliteCustomSize, SqliteCustomRoundup,
  ////                                        SqliteCustomInit, SqliteCustomShutdown, nullptr});

  ////sqlite3_config(SQLITE_CONFIG_MALLOC, &alloc);

  ////mPageCachedBuf = malloc(PAGE_CACHED_SIZE * PAGE_CACHED_COUNT);
  ////sqlite3_config(SQLITE_CONFIG_PAGECACHE, mPageCachedBuf, PAGE_CACHED_SIZE, PAGE_CACHED_COUNT);
}


static void SqliteCustomLog(void*, int errCode, const char* msg) { 
  ERR_M(LOG_MODULE_RESOURCE, "Sqlite error(%d): %s", errCode, msg);
}

void SqliteArchive::ConfigLogger() {
  sqlite3_config(SQLITE_CONFIG_LOG, &SqliteCustomLog, nullptr);
}


void SqliteArchive::CollectProfilerStatus() {
  static std::string SqliteMemStr("SqliteMem[current|highest(kb)|count]");

  int sqliteCurMem, sqliteHighMem, mallocCount, mallocHigh;
  sqlite3_status(SQLITE_STATUS_MEMORY_USED, &sqliteCurMem, &sqliteHighMem, false);
  sqlite3_status(SQLITE_STATUS_MALLOC_COUNT, &mallocCount, &mallocHigh, false);
  INF_M(LOG_MODULE_RESOURCE, "SqliteMem[current|highest(kb)|count]: %u|%u|%u", sqliteCurMem / 1024, sqliteHighMem / 1024, mallocCount);
}

}  // namespace gbf
