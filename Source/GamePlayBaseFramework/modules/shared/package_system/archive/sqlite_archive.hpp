#pragma once

#include <set>
#include "archive/archive.hpp"

#include "sqlitecpp/sqlite3/sqlite3.h"
#include "sqlite/sqlite3tool.h"
////#include "String/CapStrPrerequires.h"

namespace gbf {

class SqliteArchive : public Archive {
 public:
  SqliteArchive(std::string_view name, std::string_view archType, bool readOnly);
  ~SqliteArchive();

  bool IsCaseSensitive() const override;

  void Load() override;

  void Unload() override;

  ByteBufferPtr ReadFileData(std::string_view filename, bool readOnly = true) override;

  DataStreamPtr OpenFileStream(std::string_view filename, bool readOnly) override;

  DataStreamPtr CreateWritableFileStream(std::string_view filename) override;

  bool ReplaceByEmbeded(const EmbededResourceInfo& info, bool use_compress, bool use_crypto) override;

  void RemoveFile(std::string_view filename) override;

  StringVector ListFileNames(bool recursive = true, bool dirs = false) override;

  FileInfoList ListFileInfos(bool recursive = true, bool dirs = false) override;

  StringVector FindMatchNames(std::string_view pattern, bool recursive = true, bool dirs = false) override;

  bool FileExist(std::string_view filename) override;

  bool ConfigExist(std::string_view filename) override;

  time_t GetModifiedTime(std::string_view filename) override;

  FileInfoList FindMatchInfos(std::string_view pattern, bool recursive = true, bool dirs = false) override;

  void QueryVersionInfo(std::string_view filename, ResourceVersionInfo& version) override;

  void QueryConfigInfo(std::string_view filename, std::string& configInfo) override;

  void ReplaceConfigInfo(const char* filename, const char* configInfo) override;


  static void CollectProfilerStatus();
  static void InitSqliteConfig();
 protected:
  void FindFiles(std::string_view pattern, StringVector* simpleList, FileInfoList* detailList);
  void TryOpenSqlite();

  static void ConfigCustomAllocator();
  static void ConfigLogger();
 protected:
  sqlite3* mSqlite;
  std::set<std::string> mFileSet;
  std::set<std::string> mRelFileSet;
  bool mReadOnly = false;
};

}  // namespace gbf
