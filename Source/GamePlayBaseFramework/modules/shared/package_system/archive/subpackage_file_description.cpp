#if 0

#include "Resource/CapSubpackageFileDesp.h"
#include "Resource/CapResModule.h"


namespace Captain
{
    SubPackageFile::SubPackageFile()
    {

    }

    SubPackageFile::~SubPackageFile()
    {

    }

    bool SubPackageFile::LoadFile(std::string_view filePath)
    {

        return true;
    }

    int  SubPackageFile::ReadTextFile(std::string_view fileRelative, ByteBuffer& buffer)
    {
        return -1;
    }

    bool SubPackageFile::ReadImageByMipLevel(std::string_view fileRelative, int mipLevel, ByteBuffer& buffer)
    {

        return false;
    }

    // @ return the total file number;
    int  SubPackageFile::GetAllFilesInfoInPackage(FileContainerHeader& header)
    {
        return 0;
    }

    BaseFileInf* SubPackageFile::GetFileInfoFromPackage(std::string_view fileRelative)
    {

        return nullptr;
    }

    void SubPackageFile::WriteToFile(std::string_view filePath)
    {
        
    }

}

#endif
