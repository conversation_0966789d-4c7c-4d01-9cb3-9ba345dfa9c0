#if 0
#pragma once
#include "CapStringTypes.h"
#include "Core/CapByteBuffer.h"
#include "Core/CapCoreMain.h"
#include "Core/CapResModuleDefine.h"
#include "String/CapConstString.h"
#include "Utils/CapDataStream.h"

namespace Captain
{
    // base file information included: file name, file size, file type; 
    struct BaseFileInf
    {
        std::string fileName;
        long    fileSize;
        int FileType; // 0 : text file, 1 : image file
    };

    struct ImgFileInfo : public BaseFileInf
    {
        int width;
        int height;
        int mipIndex;
    };

    struct FileContainerHeader
    {
        unsigned int nTotalFiles;
        TVector<ImgFileInfo> ImageFiles;
        TVector<BaseFileInf> TextFiles;
    };


    class CORE_API   SubPackageFile
    {
    public:
        SubPackageFile();
        ~SubPackageFile();

    public:
        bool LoadFile(std::string_view filePath);

        int  ReadTextFile(std::string_view fileRelative, ByteBuffer& buffer);

        bool ReadImageByMipLevel(std::string_view fileRelative, int mipLevel, ByteBuffer& buffer);

        // @ return the total file number;
        int  GetAllFilesInfoInPackage(FileContainerHeader& header);

        BaseFileInf* GetFileInfoFromPackage(std::string_view fileRelative);

        void WriteToFile(std::string_view filePath);

    protected:
        FileContainerHeader mFileHeader;
    };
}
#endif