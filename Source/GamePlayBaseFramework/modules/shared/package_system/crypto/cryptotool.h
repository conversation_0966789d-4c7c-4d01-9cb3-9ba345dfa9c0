#pragma once

#include <string>
#include "package_system_export.hpp"

namespace gbf {

//[[ CryptoTool only support wow mode crypto and hash algorithm ]]
class GBF_PACKAGE_SYSTEM_API CryptoTool {
 public:
  struct HashInfo {
    unsigned int hash0;
    unsigned int hash1;
    unsigned int hash2;

    inline bool operator<(const HashInfo& a) const {
      if (hash0 < a.hash0) return true;
      if (hash0 > a.hash0) return false;
      if (hash1 < a.hash1) return true;
      if (hash1 > a.hash1) return false;
      return hash2 < a.hash2;
    }

    inline bool operator==(const HashInfo& a) const { return (a.hash0 == hash0) && (a.hash1 == hash1) && (a.hash2 == hash2); }
  };

 public:
  CryptoTool() = delete;
  ~CryptoTool() = delete;

  //[[ type is always use 0,1,2. so we can call 3 times use 3 types to get tree 32bit hash for same content, totally a 96 bit hash value]]
  static unsigned int HashString(unsigned int type, const char* str_in);
  
  //[[ type is always use 0,1,2. so we can call 3 times use 3 types to get tree 32bit hash for same content, totally a 96 bit hash value]] 
  static unsigned int HashString(unsigned int type, const char* str_in, size_t len);

  ////static unsigned int HashString(unsigned int type, const std::string& strIn);

  static HashInfo GetHashInfoFromString(const std::string& str);

  static void EncryptDataAsWow(void* pData, unsigned int dataLen, unsigned int seed);
  static void DecryptDataAsWow(void* pData, unsigned int dataLen, unsigned int seed);

 public:
  static unsigned int* GetCryptTable();
  static unsigned int m_crypt_table[0x500];
};

}  // namespace gbf
