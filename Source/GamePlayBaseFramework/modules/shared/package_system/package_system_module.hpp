#pragma once
#include "platform.hpp"
#include "archive/archive_fwd.hpp"
#include "imod_shared/imodules/ipackage_system_module.h"

namespace gbf {

class PackageSystemModule : public IPackageSystemModule {
 protected:
  // Nested types
  using ResGroupMap = std::map<std::string, ResourceGroupPtr>;
  using ArchiveGroupListMap = std::map<ArchivePtr, std::vector<ResourceGroupPtr>>;

 public:
  PackageSystemModule();
  virtual ~PackageSystemModule();

  // method from IModule
  ModuleCallReturnStatus Init() override;
  ModuleCallReturnStatus Start() override;
  ModuleCallReturnStatus Update() override;
  ModuleCallReturnStatus Stop() override;
  ModuleCallReturnStatus Release() override;
  void Free() override;

 public:
  void SetBundleAssetsPath(std::string_view path) override;
  void SetAppPath(std::string_view path) override;
  void SetRootPath(std::string_view path) override;
  void SetLibPath(std::string_view path) override;
  void SetWorkPath(std::string_view path) override { mWorkPath = path; }

  std::string_view GetBundlePath() override { return mBundlePath; }

  std::string_view GetAppPath() override { return mAppPath; }

  std::string_view GetRootPath() override { return mRootPath; }

  std::string_view GetAssetsPath() override { return mAssetsPath; }

  std::string_view GetLibPath() override { return mLibPath; }

  std::string_view GetWorkPath() override { return mWorkPath; }

  // other public method
  size_t WriteDataToFile(std::string_view relPath, const uint8_t* buffer, size_t count) override;
  ByteBufferPtr ReadDataFromFile(std::string_view relPath) override;

  DataStreamPtr OpenFileStream(std::string_view relPath, bool read_only) override;
  DataStreamPtr CreateWritableFileStream(std::string_view relPath) override;

  void AddResourceLocation(std::string_view relPath, std::string_view locationType, std::string_view groupName, bool isRecursive = false) override;
  void AddResourceLocationSmart(std::string_view relPath, std::string_view groupName, bool isRecursive = false) override;
  void RemoveResourceLocation(std::string_view name, std::string_view groupName) override;

  void CreateResourceGroup(std::string_view groupName, bool isInternal = false) override;
  StringVector GetAllRsourceGroupNames() override;
  StringVector GetCustomResourceGroupNames() override;
  StringVector GetInternalResourceGroupNames() override;

  bool ResourceGroupExists(std::string_view groupName) override;
  bool ResourceLocationExists(std::string_view relPath, std::string_view groupName) override;

  bool IsContainInGroup(std::string_view groupName, std::string_view fileName) override;
  bool IsContainInAnyGroup(std::string_view fileName) override;
  std::string_view QueryGroupByResourceName(std::string_view fileName) override;

  void TraverseMatchedListInGroup(std::string_view groupName, const StringVector& patternList, ResourceLoadFunction&& handler) override;
  void TraverseMatchedInGroup(std::string_view groupName, std::string_view pattern, ResourceLoadFunction&& handler) override;
  void TraverseMatchedInArchive(std::string_view archiveName, std::string_view archiveType, std::string_view pattern, bool recursive,
                                ResourceLoadFunction&& handler) override;

  ByteBufferPtr LoadResource(std::string_view resName, std::string_view groupName, bool searchGroupsIfNotFound = true,
                             bool bOutputLog = true) override;
  ByteBufferPtr LoadSubPackageResource(std::string_view resPackage, std::string_view subFile, std::string_view groupName,
                                       bool searchGroupsIfNotFound = true, bool bOutputLog = true) override;
  jobs::job_ticket_ptr LoadResourceAsync(std::string_view resName, std::string_view groupName, const ResourceLoadFunction& loadFunc,
                                       bool searchGroupsIfNotFound = true) override;

  void RemoveLocalResource(std::string_view location, std::string_view locationType, std::string_view resName) override;
  bool CreateLocalResource(std::string_view location, std::string_view locationType, const EmbededResourceInfo& info, bool need_compress = false,
                           bool need_crypto = false, bool overwrite = true) override;

  ResourceVersionInfo QueryVersionInfo(std::string_view groupName, std::string_view /*notUsedParam*/, std::string_view resName) override;

  void ReplaceConfigInfo(std::string_view location, std::string_view locationType, std::string_view resName, std::string_view configInfo) override;
  std::string QueryConfigInfo(std::string_view resName) override;

  StringVector GetArchiveFileNames(std::string_view location, std::string_view locationType, std::string_view pattern, bool recursive) override;
  StringVector GetGroupFileNames(std::string_view groupName, std::string_view pattern) override;
  StringVector GetGroupLocationNames(std::string_view groupName) override;

  // don't using it easily if not necessary
  void UnloadSqliteArchive(std::string_view location) override;

  void SetWritablePath(std::string_view path) override { mWritablePath = path; }
  std::string_view GetWritablePath() override;

  void SetCachePath(std::string_view path) override;
  std::string_view GetCachePath() override;

  void AddGroupResourceIndex(std::string_view resName, std::string_view location) override;
  void RemoveGroupResourceIndex(std::string_view resName, std::string_view location) override;

  void SetDefaultSearchGroup(std::string_view grpName) override;

 protected:
  ResourceGroupPtr GetResourceGroup(std::string_view groupName);
  ResourceGroupPtr QueryGroupByResourceNameImpl(std::string_view fileName);

  bool IsContainInGroup(ResourceGroup* grp, std::string_view fileName);
  bool ConfigExists(ResourceGroup* grp, std::string_view fileName);

  ArchivePtr GetArchiveByResourceName(std::string_view resName);
  ArchivePtr GetConfigArchive(std::string_view fileName);

  void AddGroupResourceIndex(std::string_view resName, ArchivePtr arch);
  void RemoveGroupResourceIndex(std::string_view resName, ArchivePtr arch);
  void RemoveResourceGroupImpl(ResourceGroupPtr grp);

 protected:
  std::string mBundlePath;
  std::string mAppPath;
  std::string mRootPath;
  std::string mAssetsPath;
  std::string mWritablePath;
  std::string mCachePath;
  std::string mLibPath;
  std::string mWorkPath;
  std::string mDefaultSearchGroup;

  ArchiveManager* mArchiveManager = nullptr;

  ResGroupMap mResGroupMap;
  ResourceGroup* mCurrentGroup;
  ArchiveGroupListMap mArchiveGroupMap;
  ////bool mOpenResourceMonitor = false;
};
}  // namespace gbf
