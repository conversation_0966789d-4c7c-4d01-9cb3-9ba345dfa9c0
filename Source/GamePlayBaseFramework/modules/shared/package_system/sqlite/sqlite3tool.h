#pragma once

#include <string>
#include <vector>
#include "package_system_export.hpp"
#include "imod_shared/imodules/ipackage_system_module.h"
#include "sqlite/sqlite3.h"

namespace gbf {

class GBF_PACKAGE_SYSTEM_API Sqlite3Tool {
 public:
  Sqlite3Tool();

  ~Sqlite3Tool();

  static sqlite3* OpenOrCreateDB(const char* dbName, bool readOnly);
  static void CloseDB(sqlite3* sqliteDb);

  static bool QueryEmbededResource(sqlite3* sqliteDb, const char* fileName, EmbededResourceInfo& out_info);

  static bool QueryTransformedResource(sqlite3* sqliteDb, const char* fileName, TransformedResourceInfo& out_info);

  static bool QueryResVersionInfo(sqlite3* sqliteDb, const char* fileName, ResourceVersionInfo& versionInfo);
  
  static bool QueryResConfigInfo(sqlite3* sqliteDb, const char* fileName, std::string& configInfo);

  static bool QueryAllFileName(sqlite3* sqliteDb, StringVector& rltList);
  static bool QueryAllRelFileName(sqlite3* sqliteDb, StringVector& rltList);

  static bool ReplaceByEmbededResource(sqlite3* sqliteDb, const EmbededResourceInfo& embeded_info, bool need_compress, bool need_crypto);
  static bool ReplaceByTransformedResource(sqlite3* sqliteDb, const TransformedResourceInfo& trans_info);


  static bool DeleteResFileInfo(sqlite3* sqliteDb, const char* fileName);

  static bool ReplaceResConfigInfo(sqlite3* sqliteDb, const char* fileName, const char* configInfo);
  static bool DeleteResConfigInfo(sqlite3* sqliteDb, const char* fileName);

 protected:
  static bool ExcuteQueryNoResult(sqlite3* sqliteDb, const char* sql);
  static bool CreateBasicDBInfo(sqlite3* sqliteDb);

  static unsigned int mCryptSeed;
};

}  // namespace gbf
