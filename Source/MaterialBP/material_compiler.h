#pragma once

#include <fmt/format.h>
#include "CEMetaMacros.h"
#include "MaterialFoward.h"
#include "material_shared.h"
#include "material_value_type.h"
#include "MaterialSceneTextureId.h"
#include "Resource/MaterialDefines.h"
#include "Resource/MaterialParameterCollection.h"
#include "material_parameter_collection_enum.h"
#include "imgui.h"
namespace cross {
class MaterialExpression;
class MaterialExpressionSurfaceShader;
class MaterialExpressionVertexShader;
class ExpressionOutput;
class MaterialExpressionCustom;
class MaterialExpressionFunctionOutput;

struct ShaderCodeChunk
{
    std::string m_Definition;
    std::string m_SymbolName;
    MaterialValueType m_Type;
    bool m_IsInlined;

    ShaderCodeChunk(const char* definition, const char* symbolName, MaterialValueType type, bool isInlined)
        : m_Definition(definition)
        , m_SymbolName(symbolName)
        , m_Type(type)
        , m_IsInlined(isInlined)
    {}
};

enum class MaterialCastFlags : uint32_t
{
    None = 0u,
    ReplicateScalar = (1u << 0),
    AllowTruncate = (1u << 1),
    AllowAppendZeroes = (1u << 2),
    AllowInteger = (1u << 3),

    ValidCast = ReplicateScalar | AllowTruncate,
};
ENUM_CLASS_FLAGS(MaterialCastFlags);

struct MaterialExpressionCustomEntry
{
    MaterialExpressionCustom* Expression;
    std::string Implementation;
    std::string FunctionName;
    std::vector<int32_t> OutputCodeIndicesVec[SF_NumFrequencies];
    std::unordered_map<ExpressionOutput*, int32_t> OutputCodeIndices[SF_NumFrequencies];
};

struct MaterialSampler
{
    std::string Name;
    SamplerState SamplerState;
};

struct CompiledShaderCode
{
    std::string ShaderCode;
};

struct CompiledShaderCodes
{
    bool NeedCompileForward = false;
    bool NeedCompileGPass = false;
    bool NeedCompileShadow = false;
    bool NeedCompileVSMDepth = false;
    bool NeedCompileMeshDecal = false;
    bool NeedCompileClosestHit = false;
    bool NeedCompileAnyHit = false;
    bool NeedCompileIntersection = false;

    CompiledShaderCode ForwardShaderCode;
    CompiledShaderCode GPassShaderCode;
    CompiledShaderCode ShadowShaderCode;
    CompiledShaderCode VSMDepthShaderCode;
    CompiledShaderCode MeshDecalShaderCode;
    CompiledShaderCode ClosestHitShaderCode;
    CompiledShaderCode AnyHitShaderCode;
    CompiledShaderCode IntersectionShaderCode;
};

struct MaterialTemplateFileLocations
{
    std::string_view Forward;
    std::string_view GPass;
    std::string_view Shadow;
    std::string_view VSMDepth;
    std::string_view MeshDecal;
    std::string_view ClosestHit;
    std::string_view AnyHit;
    std::string_view Intersection;
};

class Material_API MaterialCompiler
{
public:
    struct InputParameters
    {
        MaterialDefines defines{};
        MaterialExpressionVertexShader* vertexShaderExpression = nullptr;
        MaterialExpressionSurfaceShader* surfaceShaderExpression = nullptr;
        MaterialExpression* previewExpression = nullptr;
        std::vector<std::string>* VTs = nullptr;
        std::set<std::string>* overflowVTs = nullptr;
        std::function<void()> clearMessage;
        std::function<void(const char*)> addErrorMessage;
    };

    struct CompilationOutput
    {
        bool mBaseColorConnencted{false};
        bool mMetallicConnencted{false};
        bool mSpecularConnencted{false};
        bool mRoughnessConnencted{false};
        bool mEmissiveColorConnencted{false};
        bool mOpacityConnencted{false};
        bool mNormalConnencted{false};
    };

    MaterialCompiler(InputParameters parameters, bool isCompileFunctionPreview = false);

    ~MaterialCompiler();

    bool Translate(CompiledShaderCodes& codes);
    bool Translate_Foliage(CompiledShaderCodes& codes);
    bool Translate_Surface(CompiledShaderCodes& codes);
    bool Translate_PostProcess(CompiledShaderCodes& codes);
    bool Translate_MeshDecal(CompiledShaderCodes& codes);

    auto& GetParameters() { return m_AllUniformExpressionSet.GetParamters(); }
    auto& GetShaderConsts() { return m_AllShaderConstSet.GetShaderConsts(); }
    auto& GetSamplers() { return m_AllSamplers; }
    auto& GetMaterialParameterCollectionUsage() { return m_AllParameterFromMpcSet; }

    auto IsCompileFunctionPreview() const { return m_IsCompileExpressionPreview; }
    auto IsCompileShadowPass() const { return m_IsCompileShadowPass; }
    auto IsCompilePostProcess() const { return m_IsCompilePostProcess; }

    void BeginCompileShaderFrequency(MaterialExpression* expression, ShaderFrequency shaderFrequency);
    void EndCompileShaderFrequency();

    bool IsShaderFrequency(ShaderFrequency shaderFrequency) { return m_ShaderFrequency == shaderFrequency; }

    ShaderFrequency GetShaderFrequency() const { return m_ShaderFrequency; }

    void CallExpression(MaterialExpression* expression);
    int32_t CallExpression(MaterialExpression* expression, ExpressionOutput* output);
    int32_t CallRootExpression(MaterialExpression* expression);
    void CallMaterialFunctionOutputs(const std::vector<MaterialExpression*>& functionOutputs);

    void PushFunction(MaterialFunctionCompileState* functionState);
    MaterialFunctionCompileState* PopFunction();

    // Custom Interpolator
    void AddCustomInterpolator(MaterialCustomInterpolatorType, std::string_view name, int32_t code);
    int32_t FindCustomInterpolator(std::string_view name);
    size_t GetCustomInterpolatorsSize();
    void GatherCustomVertexInterpolators(MaterialExpression* rootExpression);

    // Parameter
    int32_t ScalarParameter(std::string_view parameterName, float value);
    int32_t VectorParameter(std::string_view parameterName, Float4 value);
    int32_t TextureParameter(std::string_view parameterName, MaterialValueType type, std::string_view textureGuid);

    // ShaderConst
    int32_t ShaderConstBool(std::string_view shaderConstName, std::string_view shaderConstDisplayName, bool value);
    int32_t ShaderConstFloat(std::string_view shaderConstName, std::string_view shaderConstDisplayName, float value);

    int32_t GPUSceneData(GPUSceneDataLevel level, MaterialValueType type, std::string_view GPUSceneDataName);
    int32_t ObjectLocalBounds(BoundsOutputIndex outputIndex);

    // Material Attributes
    void PushMaterialAttribute(EMaterialProperty materialAttribute);
    void PopMaterialAttribute();
    EMaterialProperty GetMaterialAttribute();

    // Particle
    int32_t ParticleColor();
    int32_t ParticleUVScale();
    int32_t ParticleAnimatedVelocity();
    int32_t ParticlePosition();
    int32_t ParticleRotation();
    int32_t ParticleSizeScale();

    int32_t Constant(float x);
    int32_t Constant2(float x, float y);
    int32_t Constant3(float x, float y, float z);
    int32_t Constant4(float x, float y, float z, float w);
    int32_t ConstantDouble(double value);

    int32_t ValidCast(int32_t index, MaterialValueType dstType);
    int32_t ForceCast(int32_t index, MaterialValueType dstType, MaterialCastFlags forceCastFlags = MaterialCastFlags::None);
    int32_t BaseTypeCast(int32_t index, MaterialValueType dstType);

    int32_t Sine(int32_t x);
    int32_t Cosine(int32_t x);
    int32_t Tangent(int32_t x);
    int32_t Arcsine(int32_t x);
    int32_t Arccosine(int32_t x);
    int32_t Arctangent(int32_t x);
    int32_t Arctangent2(int32_t y, int32_t x);
    int32_t Floor(int32_t x);
    int32_t Ceil(int32_t x);
    int32_t Round(int32_t x);
    int32_t Truncate(int32_t x);
    int32_t Sign(int32_t x);
    int32_t Frac(int32_t x);
    int32_t Fmod(int32_t a, int32_t b);
    int32_t Abs(int32_t x);

    int32_t Add(int32_t a, int32_t b);
    int32_t Sub(int32_t a, int32_t b);
    int32_t Mul(int32_t a, int32_t b);
    int32_t Div(int32_t a, int32_t b);
    int32_t Dot(int32_t a, int32_t b);
    int32_t Cross(int32_t a, int32_t b);
    int32_t Power(int32_t base, int32_t exponent);
    int32_t Logarithm2(int32_t x);
    int32_t Logarithm10(int32_t x);
    int32_t Exponential(int32_t x);
    int32_t Exponential2(int32_t x);
    int32_t SquareRoot(int32_t x);
    int32_t Length(int32_t x);
    int32_t Normalize(int32_t x);
    int32_t Step(int32_t y, int32_t x);
    int32_t SmoothStep(int32_t x, int32_t y, int32_t a);
    int32_t InvLerp(int32_t x, int32_t y, int32_t a);
    int32_t Lerp(int32_t a, int32_t b, int32_t s);
    int32_t Min(int32_t a, int32_t b);
    int32_t Max(int32_t a, int32_t b);
    int32_t Clamp(int32_t x, int32_t a, int32_t b);
    int32_t Saturate(int32_t x);
    int32_t AppendVector(int32_t a, int32_t b);

    int32_t DDX(int32_t a);
    int32_t DDY(int32_t a);

    int32_t TextureCoordinate(uint32_t coordinateIndex);
    int32_t ScreenUV();
    int32_t PixelPosition();
    int32_t WorldPosition();
    int32_t ObjectPositionWS();
    int32_t TilePosition();
    int32_t WorldGeometryNormal();
    int32_t WorldTangent();
    int32_t ViewProperty(ExposedViewProperty property, bool invProperty = false);
    int32_t Time();
    int32_t VertexID();
    int32_t EyeAdaption();
    int32_t RotateAboutAxis(int32_t NormalizedRotationAxisAndAngleIndex, int32_t PositionOnAxisIndex, int32_t PositionIndex);


    int32_t MaterialParameterCollection(const std::string& mpcPath, const MpcProperty& property);

    int32_t TerrainColor();

    int32_t TerrainCoords(int32_t terrainCoords);

    int32_t StaticTerrainLayerWeight(uint32_t layerIndex);

    int32_t Transform(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A, TransformElementType elementType);

    int32_t TextureSample(SamplerState samplerState, int32_t texture, int32_t coordinate, int32_t level, int32_t bias, int32_t ddx, int32_t ddy);

    int32_t CustomExpression(MaterialExpressionCustom* expressionCustom, ExpressionOutput* output, std::vector<int32_t> compiledInputs);

    int32_t Sobol(int32_t Cell, int32_t Index, int32_t Seed);

    int32_t ComponentMask(int32_t vector, bool r, bool g, bool b, bool a);

    int32_t SceneTextureLookup(int32_t ViewportUV, SceneTextureId InSceneTextureId, bool bFiltered, bool bLinear = true);
    int32_t GetSceneTextureViewSize(SceneTextureId SceneTextureId, bool InvProperty);

    int32_t Comment(Float4 color);

    int32_t DynamicBranch(int32_t condition, int32_t a, int32_t b);
    int32_t If(int32_t a, int32_t b, int32_t aGreaterThanB, int32_t aEqualsB, int32_t aLessThanB);

    int32_t StaticBool(bool Value);

    int32_t PixelDepth();

    int32_t PixelLinearDepth();

    int32_t SceneDepth();

    int32_t SceneLinearDepth();


    int32_t SkyAtmosphereLightDirection(UInt32 lightIndex);
    int32_t SkyAtmosphereLightDiskLuminance(UInt32 lightIndex, int32_t diskAngularDiameterOverride);
    int32_t SkyAtmosphereLightIlluminanceOnGround(UInt32 lightIndex);
    int32_t SkyAtmosphereViewLuminance();

    int32_t VertexColor();

    MaterialValueType GetParameterType(int32_t index) const;
    bool GetStaticBoolValue(int32_t boolIndex, bool& bSucceed);

    // Error
    int32_t Error(const char* text);

    template<typename... Types>
    int32_t Errorf(fmt::format_string<Types...> formatString, Types&&... args)
    {
        auto formattedString = fmt::format(formatString, std::forward<Types>(args)...);
        std::cout << formattedString;
        return Error(formattedString.data());
    }

    int FindVtIndex(const std::string& vtParameterName);

    CompilationOutput GetCompilationOutput() const { return m_CompilationOutput; }

private:
    static constexpr const char* ForwardPassName = "forward";
    static constexpr const char* GPassName = "gpass";
    static constexpr const char* ShadowPassName = "shadow_all";
    static constexpr const char* VSMPassName = "VSMDepth";
    static constexpr const char* MeshDecalPassName = "decal_gpass";
    static constexpr const char* ClosestHitPassName = "closesthit";
    static constexpr const char* AnyHitPassName = "anyhit";
    static constexpr const char* IntersectionPassName = "intersection";

    static constexpr const char* Macro_VertexNeedNormal = "VERTEX_NEED_NORMAL";
    static constexpr const char* Macro_VertexNeedTangent = "VERTEX_NEED_TANGENT";
    static constexpr const char* Macro_VertexNeedVertexColor = "VERTEX_NEED_VERTEX_COLOR";
    static constexpr const char* Macro_NumMaterialTexcoords = "NUM_MATERIAL_TEXCOORDS";
    static constexpr const char* Macro_VertexNeedClipDistance = "VERTEX_CLIP_DISTANCE";

    static constexpr const char* Macro_TerrainLand = "TERRAIN_LAND";
    static constexpr const char* Macro_TerrainOcean = "TERRAIN_OCEAN";
    static constexpr const char* Macro_TerrainWeightBlend = "TERRAIN_WEIGHT_BLEND";

    static const char* GetTerrainMacro(TerrainMode terrainMode);

private:
    int32_t TransformByMatrix(std::string_view matrixName, std::string_view invMatrixName, int32_t A, TransformElementType elementType);

    int32_t TransformPosition(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A);

    int32_t TransformNonePosition(MaterialCommonBasis SourceCoordBasis, MaterialCommonBasis DestCoordBasis, int32_t A, TransformElementType elementType);

    void ResetCompileState();

    struct TranslateParams
    {
        struct UsageInfo
        {
            MaterialUsage Usage;
            std::vector<std::string> Keywords;
        };

        std::vector<UsageInfo> usages;
        std::vector<std::string_view> keywords;
        std::vector<std::string_view> enables;
        std::vector<std::string_view> defines;
    };

    std::string TranslatePass_Surface(std::string_view passName, std::string_view templateFileName, const TranslateParams& params);
    std::string TranslatePass_PostProcess(const std::string& passName, const std::string& templateFileName, const TranslateParams& params);
    std::string TranslatePass_RayTracing(const std::string& passName, std::string_view templateFileName, const TranslateParams& params);
    
    bool TranslateDefaultImpl(CompiledShaderCodes& codes, MaterialTemplateFileLocations const& templateFileLocation);

    void CallExpressionSurfaceData(MaterialExpressionSurfaceShader* surfaceDataExpression);

    // AddCodeChunk
    int32_t AddCodeChunkInner(MaterialValueType type, const char* formattedCode, bool isInlined);

    template<typename... Types>
    int32_t AddCodeChunk(MaterialValueType type, fmt::format_string<Types...> formatString, Types&&... args)
    {
        auto formattedCode = fmt::format(formatString, std::forward<Types>(args)...);
        return AddCodeChunkInner(type, formattedCode.data(), false);
    }

    template<typename... Types>
    int32_t AddInlinedCodeChunk(MaterialValueType type, fmt::format_string<Types...> formatString, Types&&... args)
    {
        auto formattedCode = fmt::format(formatString, std::forward<Types>(args)...);
        return AddCodeChunkInner(type, formattedCode.data(), true);
    }

    // CastValue
    std::string CastValue(std::string_view code, MaterialValueType srcType, MaterialValueType dstType, MaterialCastFlags flags);
    std::string CoerceValue(std::string_view code, MaterialValueType srcType, MaterialValueType dstType);
    std::string CoerceParameter(int32_t index, MaterialValueType dstType);

    void AssignShaderFrequencyScope(ShaderFrequency shaderFrequency);

    std::string GetParameterCode(int32_t index, const char* defaultCode = 0);

    std::string CreateSymbolName(const char* symbolNameHint);

    const char* HLSLTypeString(MaterialValueType Type) const;

    MaterialValueType GetArithmeticResultType(MaterialValueType typeA, MaterialValueType typeB);
    MaterialValueType GetArithmeticResultType(int32_t a, int32_t b);

    MaterialParameterType ToParameterType(MaterialValueType valueType);

private:
    InputParameters m_InputParams;
    CompilationOutput m_CompilationOutput;

    ShaderFrequency m_ShaderFrequency;
    int32_t m_NextSymbolIndex;

    bool m_IsCompileExpressionPreview = false;
    bool m_IsCompileShadowPass = false;
    bool m_IsCompilePostProcess = false;
    UInt32 m_NumVirtualTextureFeedbackRequests = 0;

    // ShaderCode
    std::vector<ShaderCodeChunk> m_SharedPropertyCodeChunks[SF_NumFrequencies];
    std::vector<ShaderCodeChunk>* m_CurrentScopeChunks;

    // FunctionStack
    std::vector<MaterialFunctionCompileState*> m_FunctionStacks[SF_NumFrequencies];
    std::vector<MaterialFunctionCompileState*> m_StoreInterpolatorFunctionStacks;

    // CustomInterpolators
    MaterialCustomInterpolatorSet m_CustomInterpolatorSet;
    std::vector<int32_t> m_ExternalCustomInterpolatorCodeIndices;

    // UniformParameters
    UniformExpressionSet m_AllUniformExpressionSet;

    // MaterialParameters
    resource::MaterialParameterCollectionUsageInfo m_AllParameterFromMpcSet;

    // ShaderConsts
    MaterialCompilerShaderConstSet m_ShaderConstSet;
    MaterialCompilerShaderConstSet m_AllShaderConstSet;

    // GPUSceneData
    std::unordered_set<std::tuple<std::string, MaterialValueType>> m_GPUSceneObjectDataSet;
    std::unordered_set<std::tuple<std::string, MaterialValueType>> m_GPUScenePrimitiveDataSet;

    // CustomExpressions
    std::vector<MaterialExpressionCustomEntry> m_ExpressionCustomEntries;

    // Sampler
    std::vector<MaterialSampler> m_Samplers;
    std::vector<MaterialSampler> m_AllSamplers;

    // Scene Textures
    std::unordered_map<SceneTextureId, bool> m_SceneTextures;
    // @param bTextureLookup true: texture, false:no texture lookup, usually to get the size
    void UseSceneTextureId(SceneTextureId SceneTextureId, bool bTextureLookup);

    // Virtual Textures
    int FindOrAddVtIndex(const std::string& vtParameterName);
    void ClearVtIndex();
    bool HasVT() const;

    // Macro
    MaterialCompileMacroSet m_MacroSet;

    // Stack of current compiling material attributes
    std::vector<EMaterialProperty> m_MaterialAttributesStack;
};


// Helper class to handle MaterialAttribute changes on the compiler stack
class ScopedMaterialCompilerAttribute
{
public:
    ScopedMaterialCompilerAttribute(MaterialCompiler* InCompiler, const EMaterialProperty& InAttribute)
        : Compiler(InCompiler)
        , Attribute(InAttribute)
    {
        //check(Compiler);
        Compiler->PushMaterialAttribute(Attribute);
    }

    ~ScopedMaterialCompilerAttribute() { Compiler->PopMaterialAttribute(); }

private:
    MaterialCompiler* Compiler;
    EMaterialProperty Attribute;
};
}   // namespace cross