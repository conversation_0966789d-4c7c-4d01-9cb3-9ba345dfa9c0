#include <assert.h>
#include "material_expression_abs.h"
#include "material_expression_add.h"
#include "material_expression_append_vector.h"
#include "material_expression_arccosine.h"
#include "material_expression_arcsine.h"
#include "material_expression_arctangent.h"
#include "material_expression_arctangent2.h"
#include "material_expression_bump_offset.h"
#include "material_expression_cast.h"
#include "material_expression_camera_positionws.h"
#include "material_expression_camera_tileposition.h"
#include "material_expression_camera_vectorws.h"
#include "material_expression_ceil.h"
#include "material_expression_ce_to_ue_world_position.h"
#include "material_expression_clamp.h"
#include "material_expression_comment.h"
#include "material_expression_constant.h"
#include "material_expression_constant2vector.h"
#include "material_expression_constant3vector.h"
#include "material_expression_constant4vector.h"
#include "material_expression_cosine.h"
#include "material_expression_cross_product.h"
#include "material_expression_custom.h"
#include "material_expression_custom_interpolator.h"
#include "material_expression_ddx.h"
#include "material_expression_ddy.h"
#include "material_expression_depth_fade.h"
#include "material_expression_divide.h"
#include "material_expression_dot_product.h"
#include "material_expression_exponential.h"
#include "material_expression_exponential2.h"
#include "material_expression_floor.h"
#include "material_expression_fmod.h"
#include "material_expression_frac.h"
#include "material_expression_function_call.h"
#include "material_expression_function_input.h"
#include "material_expression_function_output.h"
#include "material_expression_if.h"
#include "material_expression_lerp.h"
#include "material_expression_logarithm10.h"
#include "material_expression_logarithm2.h"
#include "material_expression_luminance.h"
#include "material_expression_max.h"
#include "material_expression_min.h"
#include "material_expression_multiply.h"
#include "material_expression_object_local_bounds.h"
#include "material_expression_one_minus.h"
#include "material_expression_pixel_depth.h"
#include "material_expression_power.h"
#include "material_expression_registry.h"
#include "material_expression_rotate_about_axis.h"
#include "material_expression_round.h"
#include "material_expression_saturate.h"
#include "material_expression_scalar_parameter.h"
#include "material_expression_screen_uv.h"
#include "material_expression_surface_shader.h"
#include "material_expression_shader_const_bool.h"
#include "material_expression_sign.h"
#include "material_expression_sine.h"
#include "material_expression_square_root.h"
#include "material_expression_static_bool.h"
#include "material_expression_static_switch.h"
#include "material_expression_step.h"
#include "material_expression_subtract.h"
#include "material_expression_tangent.h"
#include "material_expression_texture_coordinate.h"
#include "material_expression_texture_object.h"
#include "material_expression_texture_parameter.h"
#include "material_expression_texture_sample.h"
#include "material_expression_time.h"
#include "material_expression_truncate.h"
#include "material_expression_vector_parameter.h"
#include "material_expression_vertex_id.h"
#include "material_expression_world_geometry_normal.h"
#include "material_expression_world_position.h"
#include "material_expression_gpuscene_data.h"
#include "material_expression_material_parameter_collection.h"
#include "material_expression_terrain_color.h"
#include "material_expression_terrain_layer_coords.h"
#include "material_expression_terrain_layer_weight.h"
#include "material_expression_terrain_layer_blend.h"
#include "material_expression_component_mask.h"
#include "material_expression_scene_texture.h"
#include "material_expression_transform.h"
#include "material_expression_world_tangent.h"
#include "material_expression_normalize.h"
#include "material_expression_vertex_shader.h"
#include "material_expression_texture_sample_parameter.h"
#include "material_expression_panner.h"
#include "reflection/meta/meta_class.hpp"
#include "reflection/meta/user_property.hpp"
#include "material_expression_particle_data.h"
#include "material_expression_sky_atmosphere_light_direction.h"
#include "material_expression_sky_atmosphere_light_disk_luminance.h"
#include "material_expression_sky_atmosphere_light_illuminance_on_ground.h"
#include "material_expression_sky_atmosphere_view_luminance.h"
#include "material_expression_tile_position.h"
#include "material_expression_rerouter.h"
#include "material_expression_named_reroute.h"
#include "material_expression_vertex_color.h"
#include "material_expression_distance.h"
#include "material_expression_object_position_ws.h"
#include "material_expression_eye_adaption.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "Resource/ResourceManager.h"
#include "Resource/TextureResourceInfo.h"
#include "Resource/Texture/Texture.h"
#include "Resource/Texture/TextureCube.h"
#include "Resource/Material.h"
#include "AssetPipeline/Import/TextureImporter/TextureImportSetting.h"
#include "material_expression_sobol.h"
#include "material_expression_desaturation.h"
#include "material_expression_pixel_normal_ws.h"
#include "material_attribute_definition.h"
#include "material_expression_make_material_attributes.h"
#include "material_expression_break_material_attributes.h"
#include "material_expression_get_material_attributes.h"
#include "material_expression_set_material_attributes.h"
#include "material_expression_blend_material_attributes.h"
#include "material_expression_constant_bias_scale.h"
#include "material_expression_constant_double.h"
#include "material_expression_sphere_mask.h"
#include "material_expression_shadow_replace.h"
#include "material_expression_fresnel.h"
#include "material_expression_screen_position.h"
#include "material_expression_view_property.h"
#pragma warning(push)
#pragma warning(disable : 4701)
#pragma warning(disable : 4703)
namespace cross {
void MaterialExpression::InitializePinsData()
{
    using namespace gbf::reflection;

    // m_A -> A
    auto MemberNameToDisplayName = [](std::string_view name) { return name.substr(2, name.size() - 2); };

    std::unordered_map<std::string, ExpressionPin*> pinMap;

    // fill m_Inputs and m_Outputs
    auto* metaClass = __rtti_meta();
    for (int i = 0; i < metaClass->GetPropertyCount(); i++)
    {
        auto& propertyInfo = metaClass->GetProperty(i);
        if (propertyInfo.kind() == ValueKind::kUser)
        {
            auto* userPropertyInfo = static_cast<const UserProperty*>(&propertyInfo);
            if (userPropertyInfo->GetClass().IsTypeMatch(query_meta_class<ExpressionInput>()->id()) || userPropertyInfo->GetClass().IsTypeMatch(query_meta_class<ExpressionAttributesInput>()->id()))
            {
                // ExpressionInput
                auto value = userPropertyInfo->Get(make_user_object(this, remote_storage_policy{}));
                ExpressionInput* input = &value.Ref<ExpressionInput>();

                // DisplayName
                const auto& metaData = propertyInfo.get_meta_data();
                if (metaData.has_key("DisplayName"))
                {
                    input->m_Name = metaData.get_value("DisplayName");
                }
                else
                {
                    input->m_Name = MemberNameToDisplayName(userPropertyInfo->name());
                }

                // add to vector
                m_Inputs.push_back(input);

                pinMap[userPropertyInfo->name()] = input;
            }
            else if (userPropertyInfo->GetClass().IsTypeMatch(query_meta_class<ExpressionOutput>()->id()))
            {
                // ExpressionOutput
                const auto& metaData = propertyInfo.get_meta_data();
                auto value = userPropertyInfo->Get(make_user_object(this, remote_storage_policy{}));
                ExpressionOutput* output = &value.Ref<ExpressionOutput>();
                if (metaData.has_key("DisplayName"))
                {
                    output->m_Name = metaData.get_value("DisplayName");
                }
                else
                {
                    output->m_Name = MemberNameToDisplayName(userPropertyInfo->name());
                }
                output->m_ParentExpression = this;

                // RedirectTo
                if (metaData.has_key("RedirectTo"))
                {
                    auto* redirectToPin = static_cast<ExpressionOutput*>(pinMap[metaData.get_value("RedirectTo")]);
                    output->m_ExpressionOutputRedirectTo = redirectToPin;
                }

                // ColorMask
                if (metaData.has_key("ColorMask"))
                {
                    auto colorMask = metaData.get_value("ColorMask");
                    output->m_ColorMask = ColorMask::None;
                    if (colorMask.find("R") != std::string::npos)
                    {
                        output->m_ColorMask |= ColorMask::R;
                    }
                    if (colorMask.find("G") != std::string::npos)
                    {
                        output->m_ColorMask |= ColorMask::G;
                    }
                    if (colorMask.find("B") != std::string::npos)
                    {
                        output->m_ColorMask |= ColorMask::B;
                    }
                    if (colorMask.find("A") != std::string::npos)
                    {
                        output->m_ColorMask |= ColorMask::A;
                    }
                }

                // add to vector
                m_Outputs.push_back(output);

                pinMap[userPropertyInfo->name()] = output;
            }
        }
        else
        {
            // link property
            auto& metaData = propertyInfo.get_meta_data();
            if (metaData.has_key("OverrideInputProperty"))
            {
                auto* expressionPin = pinMap[metaData.get_value("OverrideInputProperty")];
                expressionPin->m_BindedPropertyName = propertyInfo.name();
                expressionPin->m_GetBindedPropertyValue = [&propertyInfo, this]() -> Value { return propertyInfo.Get(make_user_object(this, remote_storage_policy{})); };
            }
        }
    }
}

int32_t ExpressionInput::Compile(MaterialCompiler& compiler)
{
    if (m_LinkedExpressionOutput)
    {
        return m_LinkedExpressionOutput->Compile(compiler);
    }
    else
    {
        std::string outputerror = fmt::format("Expression: {}, Property Name is: {}, Missing Input", this->m_Name, this->m_BindedPropertyName);
        return compiler.Error(outputerror.c_str());
    }
}

int32_t ExpressionInput::CompileAttributes(MaterialCompiler& compiler, EMaterialProperty attribute, const float* PConstant)
{
    auto attributeDefinition = MaterialAttributeMap::GetMaterialAttributeDefinition(attribute);
    if (!attributeDefinition)
    {
        //return compiler.Error("Invalid attribute");
        return CODE_CHUNK_INDEX_NONE;
    }

    int32_t ret = CODE_CHUNK_INDEX_NONE;

    if (m_LinkedExpressionOutput)
    {
        ret = ExpressionInput::Compile(compiler);
    }
    switch (attributeDefinition->m_ValueType)
    {
    case MaterialValueType::MCT_Float1:
        ret = ret == CODE_CHUNK_INDEX_NONE ? (PConstant ? compiler.Constant(PConstant[0]) : compiler.Constant(attributeDefinition->m_DefaultValue.x)) : ret;
        ret = compiler.ForceCast(ret, MaterialValueType::MCT_Float1);
        break;
    case MaterialValueType::MCT_Float2:
        ret = ret == CODE_CHUNK_INDEX_NONE ? (PConstant ? compiler.Constant2(PConstant[0], PConstant[1]) :
            compiler.Constant2(attributeDefinition->m_DefaultValue.x, attributeDefinition->m_DefaultValue.y)) : ret;
        ret = compiler.ForceCast(ret, MaterialValueType::MCT_Float2);
        break;
    case MaterialValueType::MCT_Float3:
        ret = ret == CODE_CHUNK_INDEX_NONE ? (PConstant ? compiler.Constant3(PConstant[0], PConstant[1], PConstant[2]) :
            compiler.Constant3(attributeDefinition->m_DefaultValue.x, attributeDefinition->m_DefaultValue.y, attributeDefinition->m_DefaultValue.z)) : ret;
        ret = compiler.ForceCast(ret, MaterialValueType::MCT_Float3);
        break;
    case MaterialValueType::MCT_Float4:
        ret = ret == CODE_CHUNK_INDEX_NONE ? (PConstant ? compiler.Constant4(PConstant[0], PConstant[1], PConstant[2], PConstant[3]) :
            compiler.Constant4(attributeDefinition->m_DefaultValue.x, attributeDefinition->m_DefaultValue.y, attributeDefinition->m_DefaultValue.z, attributeDefinition->m_DefaultValue.w)) : ret;
        ret = compiler.ForceCast(ret, MaterialValueType::MCT_Float4);
        break;
    default:
        ret = compiler.Error("Invalid attribute type");
        break;
    }

    return ret;
}

void ExpressionInput::AdditionalSerialize(cross::SerializeNode& node, SerializeContext& context) const
{
    int32_t linkedExpressionId = -1;
    int32_t linkedExpressionOutputIndex = -1;
    int32_t linkedExpressionOutputFunctionInoutId = -1;

    if (m_LinkedExpressionOutput)
    {
        linkedExpressionId = m_LinkedExpressionOutput->m_ParentExpression->m_Id;
        linkedExpressionOutputIndex = m_LinkedExpressionOutput->m_ParentExpression->GetExpressionOutputIndex(m_LinkedExpressionOutput);

        if (auto* functionCall = dynamic_cast<MaterialExpressionFunctionCall*>(m_LinkedExpressionOutput->m_ParentExpression); functionCall)
        {
            SInt32 functionInoutId = functionCall->GetFunctionInoutId(m_LinkedExpressionOutput);
            if (functionInoutId != 0)
            {
                linkedExpressionOutputFunctionInoutId = functionInoutId;
            }
        }
    }

    node["LinkedExpressionId"] = linkedExpressionId;
    node["LinkedExpressionOutputIndex"] = linkedExpressionOutputIndex;
    node["LinkedExpressionOutputFunctionInoutId"] = linkedExpressionOutputFunctionInoutId;
}

int32_t ExpressionAttributesInput::CompileWithDefault(MaterialCompiler& compiler, EMaterialProperty attribute)
{
    ScopedMaterialCompilerAttribute scopedMaterialCompilerAttribute(&compiler, attribute);
    int32_t result = ExpressionInput::CompileAttributes(compiler, attribute);

    return result;
}

int32_t ExpressionOutput::Compile(MaterialCompiler& compiler)
{
    int result = compiler.CallExpression(m_ParentExpression, m_ExpressionOutputRedirectTo ? m_ExpressionOutputRedirectTo : this);

    if (m_ColorMask == ColorMask::All)
    {
        return result;
    }
    else
    {
        return compiler.ComponentMask(result, EnumHasAnyFlags(m_ColorMask, ColorMask::R), EnumHasAnyFlags(m_ColorMask, ColorMask::G), EnumHasAnyFlags(m_ColorMask, ColorMask::B), EnumHasAnyFlags(m_ColorMask, ColorMask::A));
    }
}

int32_t MaterialExpression::GetExpressionInputIndex(ExpressionInput* input) const
{
    auto inputPins = const_cast<MaterialExpression*>(this)->GetInputPins();
    for (int index = 0; index < inputPins.size(); index++)
    {
        auto* inputPin = inputPins[index];
        if (input == inputPin)
        {
            return index;
        }
    }

    return -1;
}

int32_t MaterialExpression::GetExpressionOutputIndex(ExpressionOutput* output) const
{
    auto outputPins = const_cast<MaterialExpression*>(this)->GetOutputPins();
    for (int index = 0; index < outputPins.size(); index++)
    {
        auto* outputPin = outputPins[index];
        if (output == outputPin)
        {
            return index;
        }
    }

    return -1;
}

void MaterialExpression::AdditionalSerialize(cross::SerializeNode& node, SerializeContext& context) const
{
    using namespace gbf::reflection;
    auto* metaClass = __rtti_meta();
    node["Class"] = metaClass->name();
}

void MaterialExpression::Deserialize(const cross::DeserializeNode& node)
{
    using namespace gbf::reflection;

    auto* metaClass = __rtti_meta();

    for (int i = 0; i < metaClass->GetPropertyCount(); i++)
    {
        auto& propertyInfo = metaClass->GetProperty(i);
        if (node.HasMember(propertyInfo.name()))
        {
            if (propertyInfo.kind() == ValueKind::kBoolean)
            {
                // bool
                bool value = node[propertyInfo.name()].AsBoolean();
                propertyInfo.Set(make_user_object(this, remote_storage_policy{}), value);
            }
            else if (propertyInfo.kind() == ValueKind::kReal)
            {
                // float
                float value = node[propertyInfo.name()].AsFloat();
                propertyInfo.Set(make_user_object(this, remote_storage_policy{}), value);
            }
            else if (propertyInfo.kind() == ValueKind::kInteger)
            {
                // int
                int64_t value = node[propertyInfo.name()].AsInt64();
                propertyInfo.Set(make_user_object(this, remote_storage_policy{}), value);
            }
            else if (propertyInfo.kind() == ValueKind::kString)
            {
                // string
                std::string value = node[propertyInfo.name()].AsString();
                propertyInfo.Set(make_user_object(this, remote_storage_policy{}), value);
            }
            else if (propertyInfo.kind() == ValueKind::kUser)
            {
                auto* userPropertyInfo = static_cast<const UserProperty*>(&propertyInfo);
                if (&userPropertyInfo->GetClass() == query_meta_class<Float4>())
                {
                    // Float4
                    auto arrayNode = node[propertyInfo.name()];
                    assert(arrayNode.IsArray());

                    Float4 value;
                    value.x = arrayNode[0].AsFloat();
                    value.y = arrayNode[1].AsFloat();
                    value.z = arrayNode[2].AsFloat();
                    value.w = arrayNode[3].AsFloat();
                    propertyInfo.Set(make_user_object(this, remote_storage_policy{}), make_value(value));
                }
                else if (&userPropertyInfo->GetClass() == query_meta_class<Float3>())
                {
                    // Float3
                    auto arrayNode = node[propertyInfo.name()];
                    assert(arrayNode.IsArray());

                    Float3 value;
                    value.x = arrayNode[0].AsFloat();
                    value.y = arrayNode[1].AsFloat();
                    value.z = arrayNode[2].AsFloat();
                    propertyInfo.Set(make_user_object(this, remote_storage_policy{}), make_value(value));
                }
                else if (&userPropertyInfo->GetClass() == query_meta_class<Float2>())
                {
                    // Float2
                    auto arrayNode = node[propertyInfo.name()];
                    assert(arrayNode.IsArray());

                    Float2 value;
                    value.x = arrayNode[0].AsFloat();
                    value.y = arrayNode[1].AsFloat();
                    propertyInfo.Set(make_user_object(this, remote_storage_policy{}), make_value(value));
                }
            }
        }
    }

    // temp
    if (auto* expression = dynamic_cast<MaterialExpressionParameter*>(this))
    {
        if (expression->m_ParameterName.empty())
        {
            expression->m_ParameterName = expression->m_Name;
        }

        if (expression->m_Group.empty())
        {
            expression->m_Group = "Default";
        }
    }
}
void MaterialExpression::OnPropertyChange(IMaterialEditor* editor)
{
    DoHandlePropertyChange(editor);

    editor->UpdateExpressionsState(this);
}
void MaterialExpression::InitPassState(resource::Fx* fx, const std::string& passName)
{
    bool isForwardPass = passName == "forward";
    if (isForwardPass)
    {
        fx->SetRenderGroup(passName, 3500);
        fx->SetDepthStencilState(passName,
                                 NGIDepthStencilStateDesc{
                                     true,
                                     false,
                                     NGIComparisonOp::GreaterEqual,
                                     false,
                                 });
    }
    else
    {
        fx->SetRenderGroup(passName, 2000);
    }
}

void MaterialExpression::RegisterExpressions()
{
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionAdd>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionAbs>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionAppendVector>({"Math", "Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionArccosine>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionArcsine>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionArctangent>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionArctangent2>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionBumpOffset>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCast>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCameraPositionWS>({"Vectors", "Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCameraTilePosition>({"Vectors", "Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionViewProperty>({"Vectors", "Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCameraVectorWS>({"Vectors"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCeil>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCEToUEWorldPosition>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionClamp>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionComment>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstant>({"Constants"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstantBiasScale>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstant2Vector>({"Constants"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstant3Vector>({"Constants"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstant4Vector>({"Constants"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionConstantDouble>({"Constants"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCosine>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCrossProduct>({"Math", "Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionComponentMask>({"Math", "Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCustom>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionCustomInterpolator>({"CustomInterpolators"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDDX>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDDY>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDepthFade>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDesaturation>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDistance>({"Distance"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDivide>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionDotProduct>({"Math", "Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionExponential>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionExponential2>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionEyeAdaption>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFloor>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFmod>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFrac>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFresnel>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFunctionCall>({"Functions"}, false);
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFunctionInput>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionFunctionOutput>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionIf>({"Control"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionLerp>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionLogarithm2>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionLogarithm10>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionLuminance>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionMaterialParameterCollection>({"Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionMax>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionMin>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionMultiply>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionNamedRerouteDeclaration>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionNamedRerouteUsage>({"NamedReroutes"}, false);
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionNormalize>({"Math", "Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionObjectLocalBounds>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionObjectPositionWS>({"ObjectPositionWs"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionOneMinus>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionPanner>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionPixelDepth>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionPixelLinearDepth>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionPixelNormalWS>({"PixelNormalWS"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionPower>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionRerouter>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionRotateAboutAxis>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionRound>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSaturate>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionScalarParameter>({"Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSceneDepth>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSceneLinearDepth>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSceneTexture>({"Texture"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionScreenPosition>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionScreenUV>({"Texturing"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionShaderConstBool>({"Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionShadowReplace>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSign>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSine>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSobol>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSphereMask>({"Utility"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSquareRoot>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionStaticSwitch>({"Control"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionStaticBool>({"Control"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionStep>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSubtract>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTangent>({"Math"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTerrainColor>({"Terrain"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTerrainLayerCoords>({ "Terrain" });
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTerrainLayerWeight>({"Terrain"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTerrainLayerBlend>({"Terrain"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTextureSampleParameter>({"Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTextureCoordinate>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTextureObject>({"Texture"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTextureParameter>({"Texture", "Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTextureSample>({"Texture"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTilePosition>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTime>({"Time"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTruncate>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionVectorParameter>({"Parameters"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionVertexID>({"Vertex"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionWorldGeometryNormal>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionWorldPosition>({"Coordinates"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionWorldTangent>({"Coordinates", "Vectors"});

    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionBlendMaterialAttributes>({"MaterialAttributes"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionBreakMaterialAttributes>({"MaterialAttributes"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGetMaterialAttributes>({"MaterialAttributes"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionMakeMaterialAttributes>({"MaterialAttributes"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSetMaterialAttributes>({"MaterialAttributes"});

    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataFloat1>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataFloat2>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataFloat3>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataFloat4>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataUInt1>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataUInt2>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataUInt3>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataUInt4>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataSInt1>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataSInt2>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataSInt3>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataSInt4>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionGPUSceneDataBool>({"Custom"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticleColor>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticleUVScale>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticleAnimatedVelocity>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticlePosition>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticleRotation>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionParticleSizeScale>({"Particle"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionTransform>({"Vector Ops"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSkyAtmosphereLightDirection>({"Sky"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSkyAtmosphereLightDiskLuminance>({"Sky"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSkyAtmosphereLightIlluminanceOnGround>({"Sky"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSkyAtmosphereViewLuminance>({"Sky"});
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionVertexColor>({"Constants" });

    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionVertexShader>({""}, false);
    MaterialExpressionRegistry::RegisterExpression<MaterialExpressionSurfaceShader>({""}, false);
}

// Expression::Compile Implementation
int32_t MaterialExpressionExponential::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_Input.m_LinkedExpressionOutput ? m_Input.Compile(compiler) : compiler.Constant(m_ConstA);
    return compiler.Exponential(arg1);
}

int32_t MaterialExpressionExponential2::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_Input.m_LinkedExpressionOutput ? m_Input.Compile(compiler) : compiler.Constant(m_ConstA);
    return compiler.Exponential2(arg1);
}

int32_t MaterialExpressionCast::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t arg = m_Input.Compile(compiler);

    MaterialValueType type;
    switch (m_DstType)
    {
    case MaterialTypeCast::Float:
        type = MaterialValueType::MCT_Float;
        break;
    case MaterialTypeCast::UInt:
        type = MaterialValueType::MCT_UInt;
        break;
    case MaterialTypeCast::SInt:
        type = MaterialValueType::MCT_SInt;
        break;
    }
    return compiler.BaseTypeCast(arg, type);
}

int32_t MaterialExpressionCameraPositionWS::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ViewProperty(ExposedViewProperty:: WorldSpaceCameraPosition);
}

int32_t MaterialExpressionViewProperty::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    // To make sure any material that were correctly handling BufferUV != ViewportUV, we just lie to material
    // to make it believe ViewSize == BufferSize, so they are still compatible with SceneTextureLookup().
    if (m_Property == ExposedViewProperty::ViewPortOffset)
    {
        return compiler.Constant2(0.0f, 0.0f);
    }
    if (m_Property == ExposedViewProperty::ViewPropertyMax)
    {
        return compiler.Errorf("Invalid ViewProperty: ViewPropertyMax");
    }

    return compiler.ViewProperty(m_Property, outputPin == &m_InvPropertyOutput);
}

int32_t MaterialExpressionRerouter::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin) 
{
    return m_Input.Compile(compiler);
}

int32_t MaterialExpressionNamedRerouteDeclaration::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return m_Input.Compile(compiler);
}

void MaterialExpressionNamedRerouteDeclaration::OnPropertyChange(IMaterialEditor* editor)
{
    editor->MakeNamedRerouteNameUnique(this);
}

int32_t MaterialExpressionNamedRerouteUsage::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_Declaration)
    {
        return compiler.Errorf("Invalid named reroute variable");
    }
    return m_Declaration->m_Input.Compile(compiler);
}


int32_t MaterialExpressionCameraTilePosition::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ViewProperty(ExposedViewProperty::TileCameraPosition);
}

int32_t MaterialExpressionScreenPosition::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (outputPin == &m_ViewportUV)
    {
        return compiler.ScreenUV();
    }
    else
    {
        return compiler.PixelPosition();
    }
}

int32_t MaterialExpressionCameraVectorWS::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ViewProperty(ExposedViewProperty::CameraVectorWS);
}

int32_t MaterialExpressionClamp::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t argValue = m_Value.Compile(compiler);
    int32_t arg1 = m_Min.m_LinkedExpressionOutput ? m_Min.Compile(compiler) : compiler.Constant(m_ConstMin);
    int32_t arg2 = m_Max.m_LinkedExpressionOutput ? m_Max.Compile(compiler) : compiler.Constant(m_ConstMax);

    return compiler.Clamp(argValue, arg1, arg2);
}

int32_t MaterialExpressionComment::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Comment(m_Color);
}

int32_t MaterialExpressionAbs::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Abs(arg);
}
int32_t MaterialExpressionPixelNormalWS::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = compiler.WorldGeometryNormal();

    return arg;
}

int32_t MaterialExpressionAdd::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Add(arg1, arg2);
}

int32_t MaterialExpressionAppendVector::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_A.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing AppendVector input A");
    }
    else if (!m_B.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing AppendVector input B");
    }
    else
    {
        int32_t arg1 = m_A.Compile(compiler);
        int32_t arg2 = m_B.Compile(compiler);

        return compiler.AppendVector(arg1, arg2);
    }
}

int32_t MaterialExpressionArccosine::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Arccosine(arg);
}

int32_t MaterialExpressionArcsine::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Arcsine(arg);
}

int32_t MaterialExpressionArctangent::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Arctangent(arg);
}

int32_t MaterialExpressionArctangent2::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t argY = m_Y.Compile(compiler);
    int32_t argX = m_X.Compile(compiler);

    return compiler.Arctangent2(argY, argX);
}

int32_t MaterialExpressionBumpOffset::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
	if(!m_Height.m_LinkedExpressionOutput)
	{
		return compiler.Errorf("Missing Height input");
	}

	return compiler.Add(
            compiler.Mul(
                compiler.ComponentMask(compiler.Transform(MaterialCommonBasis::World, MaterialCommonBasis::Tangent, compiler.ViewProperty(ExposedViewProperty::CameraVectorWS), TransformElementType::Vector), 1, 1, 0, 0),
				compiler.Add(
					compiler.Mul(m_HeightRatioInput.m_LinkedExpressionOutput ? compiler.ForceCast(m_HeightRatioInput.Compile(compiler), MaterialValueType::MCT_Float1) : compiler.Constant(m_HeightRatio),
                                                               compiler.ForceCast(m_Height.Compile(compiler), MaterialValueType::MCT_Float1)
						),
                                                  m_HeightRatioInput.m_LinkedExpressionOutput ? compiler.Mul(compiler.Constant(-m_ReferencePlane), compiler.ForceCast(m_HeightRatioInput.Compile(compiler), MaterialValueType::MCT_Float1))
                                                                                              : compiler.Constant(-m_ReferencePlane * m_HeightRatio)
					)
				),
			m_Coordinate.m_LinkedExpressionOutput ? m_Coordinate.Compile(compiler) : compiler.TextureCoordinate(m_ConstCoordinate)
			);
}

int32_t MaterialExpressionCeil::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Ceil(arg);
}

int32_t MaterialExpressionCEToUEWorldPosition::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t worldPos = m_Input.Compile(compiler);
    int32_t result;
    if (m_CEToUE) // (x, -z, y)
    {
        result = compiler.AppendVector(
                compiler.AppendVector(
                    compiler.ComponentMask(worldPos, true, false, false, false), 
                    compiler.Sub(compiler.Constant(0.0f), compiler.ComponentMask(worldPos, false, false, true, false))
                  ), 
                compiler.ComponentMask(worldPos, false, true, false, false)
                );
    }
    else // (x, z, -y)
    {
        result = compiler.AppendVector(
                compiler.AppendVector(
                    compiler.ComponentMask(worldPos, true, false, false, false), 
                    compiler.ComponentMask(worldPos, false, false, true, false)
                ),
                compiler.Sub(compiler.Constant(0.0f), compiler.ComponentMask(worldPos, false, true, false, false))
                );
    }
    return result;
}

int32_t MaterialExpressionConstant::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Constant(m_Const);
}

int32_t MaterialExpressionConstantBiasScale::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_Input.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing ConstantBiasScale input");
    }
    return compiler.Mul(compiler.Add(compiler.Constant(m_Bias), m_Input.Compile(compiler)), compiler.Constant(m_Scale));
}

static int32_t CompileHelperLength(MaterialCompiler& compiler, int32_t A, int32_t B)
{
    int32_t Delta = compiler.Sub(A, B);
    if (compiler.GetParameterType(A) == MaterialValueType::MCT_Float && compiler.GetParameterType(B) == MaterialValueType::MCT_Float)
    {
        // optimized
        return compiler.Abs(Delta);
    }
    return compiler.Length(Delta);
}

int32_t MaterialExpressionSphereMask::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_A.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing SphereMask input A");
    }
    else if (!m_B.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing SphereMask input B");
    }
    else
    {
        int32_t Arg1 = m_A.Compile(compiler);
        int32_t Arg2 = m_B.Compile(compiler);
        int32_t Distance = CompileHelperLength(compiler, Arg1, Arg2);
        int32_t ArgInvRadius;
        if (m_Radius.m_LinkedExpressionOutput)
        {
            ArgInvRadius = compiler.Div(compiler.Constant(1.0f), compiler.Max(compiler.Constant(0.00001f), m_Radius.Compile(compiler)));
        }
        else
        {
            ArgInvRadius = compiler.Constant(1.0f / std::max(m_AttenuationRadius, 0.00001f));
        }

        int32_t NormalizedDistance = compiler.Mul(Distance, ArgInvRadius);
        int32_t ArgInvHardness;

        if (m_Hardness.m_LinkedExpressionOutput)
        {
            int32_t Softness = compiler.Sub(compiler.Constant(1.0f), m_Hardness.Compile(compiler));
            ArgInvHardness = compiler.Div(compiler.Constant(1.0f), compiler.Max(compiler.Constant(0.00001f), Softness));
        }
        else
        {
            ArgInvHardness = compiler.Div(compiler.Constant(1.0f), compiler.Max(compiler.Sub(compiler.Constant(1.0f), compiler.Mul(compiler.Constant(m_HardnessPercent), compiler.Constant(0.01f))), compiler.Constant(0.00001f)));
        }
        int32_t NegativeDistance = compiler.Sub(compiler.Constant(1.0f), NormalizedDistance);
        int32_t MaskUnclamped = compiler.Mul(NegativeDistance, ArgInvHardness);
        return compiler.Saturate(MaskUnclamped);
    }
}

int32_t MaterialExpressionConstant2Vector::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Constant2(m_X, m_Y);
}

int32_t MaterialExpressionConstant3Vector::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Constant3(m_Value.x, m_Value.y, m_Value.z);
}

int32_t MaterialExpressionConstant4Vector::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Constant4(m_Value.x, m_Value.y, m_Value.z, m_Value.w);
}

int32_t MaterialExpressionConstantDouble::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.ConstantDouble(m_Value);
}

int32_t MaterialExpressionCosine::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Cosine(arg);
}

int32_t MaterialExpressionCrossProduct::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.Compile(compiler);
    int32_t arg2 = m_B.Compile(compiler);

    return compiler.Cross(arg1, arg2);
}

void MaterialExpressionCustom::OnPropertyChange(IMaterialEditor* editor)
{
    m_Inputs.clear();
    for (auto& customInput : m_CustomInputs)
    {
        customInput.Input.m_Name = customInput.Name;
        m_Inputs.push_back(&customInput.Input);
    }

    m_Outputs.resize(1);   // one for m_Output
    for (auto& customOutput : m_AdditionalCustomOutputs)
    {
        customOutput.Output.m_ParentExpression = this;
        customOutput.Output.m_Name = customOutput.Name;
        m_Outputs.push_back(&customOutput.Output);
    }

    // some links may be invalid, clear them
    editor->ClearInvalidLinks();

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);

    // the change of this expression may have an impact to the final result, notify MaterialEditor to refresh shader
    editor->UpdateExpressionsState(this);
}

int32_t MaterialExpressionSobol::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t CellInput = m_Cell.m_LinkedExpressionOutput ? m_Cell.Compile(compiler) : compiler.Constant2(0.f, 0.f);
    int32_t IndexInput = m_Cell.m_LinkedExpressionOutput ? m_Index.Compile(compiler) : compiler.Constant(m_ConstIndex);
    int32_t SeedInput = m_Seed.m_LinkedExpressionOutput ? m_Seed.Compile(compiler) : compiler.Constant2(m_ConstSeed.x, m_ConstSeed.y);

    return compiler.Sobol(CellInput, IndexInput, SeedInput);
}

int32_t MaterialExpressionDesaturation::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_Input.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing Desaturation input");
    }
    int32_t Color = compiler.ForceCast(m_Input.Compile(compiler), MaterialValueType::MCT_Float3, MaterialCastFlags::ReplicateScalar);
    int32_t Grey = compiler.Dot(Color, compiler.Constant3(m_LuminanceFactors.x, m_LuminanceFactors.y, m_LuminanceFactors.z));
    if (m_Fraction.m_LinkedExpressionOutput)
    {
        return compiler.Lerp(Color, Grey, m_Fraction.Compile(compiler));
    }
    else
    {
        return Grey;
    }
}

int32_t MaterialExpressionMakeMaterialAttributes::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t result = CODE_CHUNK_INDEX_NONE;
    EMaterialProperty property = compiler.GetMaterialAttribute();

    switch (property)
    {
    case cross::EMaterialProperty::BaseColor:
        result = m_BaseColor.CompileAttributes(compiler, EMaterialProperty::BaseColor);
        break;
    case cross::EMaterialProperty::Metallic:
        result = m_Metallic.CompileAttributes(compiler, EMaterialProperty::Metallic);
        break;
    case cross::EMaterialProperty::Specular:
        result = m_Specular.CompileAttributes(compiler, EMaterialProperty::Specular);
        break;
    case cross::EMaterialProperty::Roughness:
        result = m_Roughness.CompileAttributes(compiler, EMaterialProperty::Roughness);
        break;
    case cross::EMaterialProperty::Opacity:
        result = m_Opacity.CompileAttributes(compiler, EMaterialProperty::Opacity);
        break;
    case cross::EMaterialProperty::OpacityMask:
        result = m_OpacityMask.CompileAttributes(compiler, EMaterialProperty::OpacityMask);
        break;
    case cross::EMaterialProperty::Normal:
        result = m_Normal.CompileAttributes(compiler, EMaterialProperty::Normal);
        break;
    case cross::EMaterialProperty::AmbientOcclusion:
        result = m_AmbientOcclusion.CompileAttributes(compiler, EMaterialProperty::AmbientOcclusion);
        break;
    case cross::EMaterialProperty::EmissiveColor:
        result = m_EmissiveColor.CompileAttributes(compiler, EMaterialProperty::EmissiveColor);
        break;
    case cross::EMaterialProperty::SubsurfaceColor:
        result = m_SubsurfaceColor.CompileAttributes(compiler, EMaterialProperty::SubsurfaceColor);
        break;
    case cross::EMaterialProperty::WorldPositionOffset:
        result = m_WorldPositionOffset.CompileAttributes(compiler, EMaterialProperty::WorldPositionOffset);
        break;
    default:
        break;
    }

    return result;
}

int32_t MaterialExpressionSetMaterialAttributes::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    EMaterialProperty property = compiler.GetMaterialAttribute();
    if (!MaterialAttributeMap::IsSupportedAttribute(property))
    {
        return compiler.Error("SetMaterialAttributes: unsupportted attributes");
    }

    // Verify setup
    const int32_t NumInputPins = static_cast<int32_t>(m_AttributesInputs.size());
    for (int32_t i = 0; i < NumInputPins; ++i)
    {
        for (int j = i + 1; j < NumInputPins; ++j)
        {
            if (m_AttributesInputs[i].Attribute == m_AttributesInputs[j].Attribute)
            {
                return compiler.Errorf("SetMaterialAttributes: Duplicate attribute types.");
            }
        }
    }

    // Compile attribute
    int32_t result = CODE_CHUNK_INDEX_NONE;
    int32_t pinIndex;
    ExpressionInput* attributeInput = nullptr;
    for (pinIndex = 0; pinIndex < NumInputPins; ++pinIndex)
    {
        if (m_AttributesInputs[pinIndex].Attribute == property)
        {
            attributeInput = &m_AttributesInputs[pinIndex].Input;
            break;
        }
    }
    if (attributeInput)
    {
        result = attributeInput->CompileAttributes(compiler, property);
    }
    else // Use MaterialAttributes pin
    {
        result = m_MaterialAttributes.CompileAttributes(compiler, property);
    }

    return result;
}

void MaterialExpressionSetMaterialAttributes::OnPropertyChange(IMaterialEditor* editor)
{
    m_Inputs.resize(1);
    for (auto& customInput : m_AttributesInputs)
    {
        customInput.Input.m_Name = MaterialAttributeMap::GetMaterialAttributeName(customInput.Attribute);
        customInput.Input.m_Enable = MaterialAttributeMap::IsSupportedAttribute(customInput.Attribute);
        m_Inputs.push_back(&customInput.Input);
    }

    // some links may be invalid, clear them
    editor->ClearInvalidLinks();

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);

    // the change of this expression may have an impact to the final result, notify MaterialEditor to refresh shader
    editor->UpdateExpressionsState(this);
}

MaterialExpressionBreakMaterialAttributes::MaterialExpressionBreakMaterialAttributes()
{
    m_OutputMap[&m_BaseColor] = EMaterialProperty::BaseColor;
    m_OutputMap[&m_Metallic] = EMaterialProperty::Metallic;
    m_OutputMap[&m_Specular] = EMaterialProperty::Specular;
    m_OutputMap[&m_Roughness] = EMaterialProperty::Roughness;
    m_OutputMap[&m_Opacity] = EMaterialProperty::Opacity;
    m_OutputMap[&m_OpacityMask] = EMaterialProperty::OpacityMask;
    m_OutputMap[&m_Normal] = EMaterialProperty::Normal;
    m_OutputMap[&m_AmbientOcclusion] = EMaterialProperty::AmbientOcclusion;
    m_OutputMap[&m_EmissiveColor] = EMaterialProperty::EmissiveColor;
    m_OutputMap[&m_SubsurfaceColor] = EMaterialProperty::SubsurfaceColor;
    m_OutputMap[&m_WorldPositionOffset] = EMaterialProperty::WorldPositionOffset;
    m_OutputMap[&m_ShadingModel] = EMaterialProperty::ShadingModel;
}

int32_t MaterialExpressionBreakMaterialAttributes::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!m_MaterialAttributes.m_LinkedExpressionOutput)
    {
        return compiler.Error("Missing BreakMaterialAttributes input");
    }

    EMaterialProperty property = GetPinProperty(outputPin);
    if (property == EMaterialProperty::None)
    {
        return compiler.Error("BreakMaterialAttributes: error on output pin");
    }

    return m_MaterialAttributes.CompileWithDefault(compiler, property);
}

int32_t MaterialExpressionGetMaterialAttributes::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    const int32_t NumOutputPins = static_cast<int32_t>(m_AttributesOutputs.size());
    for (int32_t i = 0; i < NumOutputPins; ++i)
    {
        for (int j = i + 1; j < NumOutputPins; ++j)
        {
            if (m_AttributesOutputs[i].Attribute == m_AttributesOutputs[j].Attribute)
            {
                return compiler.Errorf("GetMaterialAttributes: Duplicate attribute types.");
            }
        }
    }

    auto property = GetPinAttribute(outputPin);
    EMaterialProperty attribute = property ? property->m_Attribute : compiler.GetMaterialAttribute();
    if (!MaterialAttributeMap::IsSupportedAttribute(attribute))
    {
        return compiler.Errorf("GetMaterialAttributes: unsupportted attributes {}", MaterialAttributeMap::GetMaterialAttributeName(attribute));
    }
    return m_MaterialAttributesInput.CompileWithDefault(compiler, attribute);
}


std::shared_ptr<MaterialAttribute> MaterialExpressionGetMaterialAttributes::GetPinAttribute(ExpressionOutput* output) const
{
    for (const auto& it : m_AttributesOutputs)
    {
        if (&it.Output == output)
        {
            return MaterialAttributeMap::GetMaterialAttributeDefinition(it.Attribute);
        }
    }

    return nullptr;
}

void MaterialExpressionGetMaterialAttributes::OnPropertyChange(IMaterialEditor* editor)
{
    m_Outputs.resize(1);   // one for m_Output
    for (auto& customOutput : m_AttributesOutputs)
    {
        customOutput.Output.m_ParentExpression = this;
        customOutput.Output.m_Name = MaterialAttributeMap::GetMaterialAttributeName(customOutput.Attribute);
        customOutput.Output.m_Enable = MaterialAttributeMap::IsSupportedAttribute(customOutput.Attribute);
        m_Outputs.push_back(&customOutput.Output);
    }

    // some links may be invalid, clear them
    editor->ClearInvalidLinks();

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);

    // the change of this expression may have an impact to the final result, notify MaterialEditor to refresh shader
    editor->UpdateExpressionsState(this);
}

int32_t MaterialExpressionBlendMaterialAttributes::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t result = CODE_CHUNK_INDEX_NONE;
    EMaterialProperty property = compiler.GetMaterialAttribute();
    int32_t resultA = m_A.CompileWithDefault(compiler, property);
    int32_t resultB = m_B.CompileWithDefault(compiler, property);
    int32_t resultAlpha = m_Alpha.Compile(compiler);
    return compiler.Lerp(resultA, resultB, resultAlpha);
}


int32_t MaterialExpressionCustom::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    std::vector<int32_t> compiledInputs;

    for (int i = 0; i < m_CustomInputs.size(); i++)
    {
        if (m_CustomInputs[i].Name.empty())
        {
            compiledInputs.push_back(CODE_CHUNK_INDEX_NONE);
        }
        else
        {
            if (!m_CustomInputs[i].Input.m_LinkedExpressionOutput)
            {
                return compiler.Errorf("Custom material {} missing input {} ({})", m_Name, i + 1, m_CustomInputs[i].Name);
            }

            int32_t inputCode = m_CustomInputs[i].Input.Compile(compiler);
            if (inputCode == CODE_CHUNK_INDEX_NONE)
            {
                return CODE_CHUNK_INDEX_NONE;
            }

            compiledInputs.push_back(inputCode);
        }
    }

    return compiler.CustomExpression(this, outputPin, compiledInputs);
}

int32_t MaterialExpressionCustomInterpolator::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (!compiler.IsShaderFrequency(SF_Surface))
    {
        return compiler.Errorf("Current shader state don't support CustomInterpolator");
    }
    if (compiler.IsCompileFunctionPreview())
    {
        if (m_Input.m_LinkedExpressionOutput)
        {
            int32_t preCompileRet = m_Input.Compile(compiler);
            if (preCompileRet == CODE_CHUNK_INDEX_NONE)
            {
                return compiler.Errorf("Custom Vertex Interpolator get none input");
            }
            return preCompileRet;
        }
        else
        {
            return compiler.Errorf("Custom Vertex Interpolator missing input");
        }
    }

    return compiler.FindCustomInterpolator(m_Name);
}

void MaterialExpressionCustom::Deserialize(const cross::DeserializeNode& node)
{
    using namespace gbf::reflection;

    // m_Code
    m_Code = node["m_Code"].AsString();

    // m_CustomInputs
    {
        const auto& inputsNode = node["m_CustomInputs"];
        if (!inputsNode.IsNull())
        {
            for (int i = 0; i < inputsNode.Size(); i++)
            {
                const auto& inputNode = inputsNode[i];
                std::string name = inputNode["Name"].AsString();

                CustomExpressionInput customInput;
                customInput.Name = name;
                customInput.Input.m_Name = name;
                m_CustomInputs.push_back(std::move(customInput));

                m_Inputs.push_back(&m_CustomInputs.back().Input);
            }
        }
    }

    // m_OutputType
    m_OutputType = static_cast<CustomMaterialOutputType>(node["m_OutputType"].AsUInt32());

    // m_AdditionalOutputs
    {
        const auto& additionalCustomOutputsNode = node["m_AdditionalCustomOutputs"];
        if (!additionalCustomOutputsNode.IsNull())
        {
            for (int i = 0; i < additionalCustomOutputsNode.Size(); i++)
            {
                const auto& outputNode = additionalCustomOutputsNode[i];
                std::string name = outputNode["Name"].AsString();

                CustomExpressionOutput customOutput;
                customOutput.Name = name;
                customOutput.Output.m_Name = name;
                customOutput.Output.m_ParentExpression = this;
                customOutput.OutputType = static_cast<CustomMaterialOutputType>(outputNode["OutputType"].AsUInt32());
                m_AdditionalCustomOutputs.push_back(std::move(customOutput));

                m_Outputs.push_back(&m_AdditionalCustomOutputs.back().Output);
            }
        }
    }
}

void MaterialExpressionCustom::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    using namespace gbf::reflection;

    for (auto& customInput : m_CustomInputs)
    {
        customInput.Input.m_Name = customInput.Name;
        m_Inputs.push_back(&customInput.Input);
    }

    for (auto& customOutput : m_AdditionalCustomOutputs)
    {
        customOutput.Output.m_ParentExpression = this;
        customOutput.Output.m_Name = customOutput.Name;
        m_Outputs.push_back(&customOutput.Output);
    }
}

void MaterialExpressionSetMaterialAttributes::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    using namespace gbf::reflection;

    for (auto& attributeInput : m_AttributesInputs)
    {
        attributeInput.Input.m_Name = MaterialAttributeMap::GetMaterialAttributeName(attributeInput.Attribute);
        attributeInput.Input.m_Enable = MaterialAttributeMap::IsSupportedAttribute(attributeInput.Attribute);
        m_Inputs.push_back(&attributeInput.Input);
    }
}

void MaterialExpressionGetMaterialAttributes::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    using namespace gbf::reflection;

    for (auto& attributeOutput : m_AttributesOutputs)
    {
        attributeOutput.Output.m_ParentExpression = this;
        attributeOutput.Output.m_Name = MaterialAttributeMap::GetMaterialAttributeName(attributeOutput.Attribute);
        attributeOutput.Output.m_Enable = MaterialAttributeMap::IsSupportedAttribute(attributeOutput.Attribute);
        m_Outputs.push_back(&attributeOutput.Output);
    }
}

int32_t MaterialExpressionDDX::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.DDX(arg);
}

int32_t MaterialExpressionDDY::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.DDY(arg);
}

int32_t MaterialExpressionDepthFade::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t opacityArg = m_Opacity.m_LinkedExpressionOutput ? m_Opacity.Compile(compiler) : compiler.Constant(m_OpacityDefault);
    int32_t fadeDistanceArg = m_FadeDistance.m_LinkedExpressionOutput ? m_FadeDistance.Compile(compiler) : compiler.Constant(m_FadeDistanceDefault);

    int32_t pixelDepth = compiler.PixelLinearDepth();
    int32_t fadeArg = compiler.Saturate(compiler.Div(compiler.Sub(compiler.SceneTextureLookup(CODE_CHUNK_INDEX_NONE, SceneTextureId::SceneDepth, false), pixelDepth), fadeDistanceArg));

    return compiler.Mul(opacityArg, fadeArg);
}

int32_t MaterialExpressionDivide::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Div(arg1, arg2);
}

int32_t MaterialExpressionDotProduct::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.Compile(compiler);
    int32_t arg2 = m_B.Compile(compiler);

    return compiler.Dot(arg1, arg2);
}

int32_t MaterialExpressionFloor::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Floor(arg);
}

int32_t MaterialExpressionFmod::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.Compile(compiler);
    int32_t arg2 = m_B.Compile(compiler);

    return compiler.Fmod(arg1, arg2);
}

int32_t MaterialExpressionFrac::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Frac(arg);
}

int32_t MaterialExpressionFunctionCall::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (!m_MaterialFunctionEditorData.MaterialFunction)
    {
        return compiler.Errorf("Missing Material Function");
    }

    assert(m_SharedCompileState);
    compiler.PushFunction(m_SharedCompileState);

    const int32_t result = FindExpressionFunctionOutput(output)->Compile(compiler, nullptr);

    compiler.PopFunction();

    return result;
}

void MaterialExpressionFunctionCall::OnPropertyChange(IMaterialEditor* editor)
{
    MaterialExpression::OnPropertyChange(editor);

    // some links may be invalid, clear them
    editor->ClearInvalidLinks();

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);

    // the change of this expression may have an impact to the final result, notify MaterialEditor to refresh shader
    editor->UpdateExpressionsState(this);

    // initialize named reroutes
    editor->InitializeNamedReroutes();
}

void MaterialExpressionFunctionCall::DoHandlePropertyChange(IMaterialEditor* editor)
{
    RefreshPinsAndEditorData(editor);

    // call OnPropertyChange to notify expression loading something (texture...)
    for (auto expression : m_MaterialFunctionEditorData.Expressions)
    {
        expression->DoHandlePropertyChange(editor);
    }
}

void MaterialExpressionFunctionCall::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    RefreshPinsAndEditorData();
}

void MaterialExpressionFunctionCall::Deserialize(const cross::DeserializeNode& node)
{
    using namespace gbf::reflection;

    m_MaterialFunction = node["m_MaterialFunction"].AsString();
    m_Name = "Invalid MaterialFunction";

    RefreshPinsAndEditorData();
}

void MaterialExpressionFunctionCall::InitializeNamedReroutes()
{
    std::unordered_map<std::string, MaterialExpressionNamedRerouteDeclaration*> declarationMap;

    // Get all NamedRerouteDeclaration
    for (auto& expression : m_MaterialFunctionEditorData.Expressions)
    {
        if (auto declaration = std::dynamic_pointer_cast<MaterialExpressionNamedRerouteDeclaration>(expression))
        {
            declarationMap[declaration->m_VariableGuid] = declaration.get();
        }
    }

    for (auto& expression : m_MaterialFunctionEditorData.Expressions)
    {
        if (auto usage = std::dynamic_pointer_cast<MaterialExpressionNamedRerouteUsage>(expression))
        {
            auto it = declarationMap.find(usage->m_DeclarationGuid);
            if (it != declarationMap.end())
            {
                usage->m_Declaration = it->second;
            }
            else
            {
                usage->m_Declaration = nullptr;
                LOG_ERROR("NamedRerouteUsage with GUID {} has no matching Declaration.", usage->m_DeclarationGuid);
            }
        }
    }

    for (auto& expression : m_MaterialFunctionEditorData.Expressions)
    {
        if (auto functionCall = std::dynamic_pointer_cast<MaterialExpressionFunctionCall>(expression))
        {
            functionCall->InitializeNamedReroutes();
        }
    }
}

std::vector<std::shared_ptr<MaterialExpression>> MaterialExpression::DeserializeExpressions(const cross::DeserializeNode& node)
{
    using namespace gbf::reflection;

    std::vector<std::shared_ptr<MaterialExpression>> expressions;
    std::unordered_map<int32_t, MaterialExpression*> expressionIdMap;

    if (node.IsArray())
    {
        // Create Expressions
        for (int i = 0; i < node.Size(); i++)
        {
            const auto& expressionNode = node.At(i);

            if (expressionNode.HasMember("Id"))
            {
                // deprecated!!!
                int32_t id = expressionNode["Id"].AsInt32();
                std::string className = expressionNode["Class"].AsString();
                int32_t editorPositionX = expressionNode["m_EditorPositionX"].AsInt32();
                int32_t editorPositionY = expressionNode["m_EditorPositionY"].AsInt32();

                if (className == "cross::MaterialExpressionSetSurfaceData")
                {
                    className = "cross::MaterialExpressionSurfaceShader";
                }

                auto expression = MaterialExpressionRegistry::CreateExpression(className);
                expression->m_EditorPositionX = editorPositionX;
                expression->m_EditorPositionY = editorPositionY;
                expressions.push_back(expression);
                expression->Deserialize(expressionNode);
                expressionIdMap[id] = expression.get();
            }
            else
            {
                std::string className = expressionNode["Class"].AsString();

                if (className == "cross::MaterialExpressionSetSurfaceData")
                {
                    className = "cross::MaterialExpressionSurfaceShader";
                }
                auto expression = MaterialExpressionRegistry::CreateExpression(className);
                
                SerializeContext context{};
                expression->Deserialize(expressionNode, context);
                expressions.push_back(expression);
                expressionIdMap[expression->m_Id] = expression.get();
            }
        }

        // Reconstruct Links

        auto LinkInputPinToOutputPin = [&expressionIdMap](DeserializeNode& node, ExpressionInput* input) {
            SInt32 linkedExpressionId = node["LinkedExpressionId"].AsInt32();
            SInt32 linkedExpressionOutputIndex = node["LinkedExpressionOutputIndex"].AsInt32();
            SInt32 linkedExpressionFunctionInoutId = -1;
            if (node.HasMember("LinkedExpressionOutputFunctionInoutId"))
            {
                linkedExpressionFunctionInoutId = node["LinkedExpressionOutputFunctionInoutId"].AsInt32();
            }

            if (linkedExpressionId != -1)
            {
                assert(linkedExpressionOutputIndex != -1);

                auto* targetExpression = expressionIdMap[linkedExpressionId];
                if (targetExpression && targetExpression->GetOutputPins().size() > linkedExpressionOutputIndex)
                {
                    if (linkedExpressionFunctionInoutId != -1)
                    {
                        auto* targetExpressionFunctionCall = dynamic_cast<MaterialExpressionFunctionCall*>(targetExpression);
                        Assert(targetExpressionFunctionCall);

                        input->m_LinkedExpressionOutput = targetExpressionFunctionCall->GetOutputByFuncInoutId(linkedExpressionFunctionInoutId);
                    }
                    else
                    {
                        input->m_LinkedExpressionOutput = targetExpression->GetOutputPins()[linkedExpressionOutputIndex];
                    }
                }
            }
        };

        for (int i = 0; i < node.Size(); i++)
        {
            auto expressionNode = node.At(i);
            auto* expression = expressions[i].get();
            auto* metaClass = expression->__rtti_meta();

            // for each ExpressionInput
            for (int i2 = 0; i2 < metaClass->GetPropertyCount(); i2++)
            {
                auto& propertyInfo = metaClass->GetProperty(i2);
                if (propertyInfo.kind() == ValueKind::kUser)
                {
                    auto* userPropertyInfo = static_cast<const UserProperty*>(&propertyInfo);
                    if (&userPropertyInfo->GetClass() == query_meta_class<ExpressionInput>() || &userPropertyInfo->GetClass() == query_meta_class<ExpressionAttributesInput>())
                    {
                        if (std::string propertyName = userPropertyInfo->name(); expressionNode.HasMember(propertyName))
                        {
                            auto value = userPropertyInfo->Get(make_user_object(expression, remote_storage_policy{}));
                            ExpressionInput* input = &value.Ref<ExpressionInput>();

                            auto expressionInputNode = expressionNode[propertyName];
                            LinkInputPinToOutputPin(expressionInputNode, input);
                        }
                    }
                }
            }

            // Handle CustomInput in MaterialExpressionCustom
            if (metaClass->IsTypeMatch(query_meta_class<MaterialExpressionCustom>()->id()))
            {
                for (int i3 = 0; i3 < expression->m_Inputs.size(); i3++)
                {
                    ExpressionInput* input = expression->m_Inputs[i3];

                    auto expressionInputNode = expressionNode["m_CustomInputs"][i3]["Input"];
                    LinkInputPinToOutputPin(expressionInputNode, input);
                }
            }

            // Handle FunctionIput in MaterialExpressionFuncionCall
            if (metaClass->IsTypeMatch(query_meta_class<MaterialExpressionFunctionCall>()->id()))
            {
                for (int i4 = 0; i4 < expression->m_Inputs.size(); i4++)
                {
                    ExpressionInput* input = expression->m_Inputs[i4];
                    if (expressionNode.HasMember("m_FunctionInputs"))
                    {
                        if (i4 < expressionNode["m_FunctionInputs"].Size())
                        {
                            auto expressionInputNode = expressionNode["m_FunctionInputs"][i4]["Input"];
                            LinkInputPinToOutputPin(expressionInputNode, input);
                        }
                    }
                }
            }

            // Handle FunctionIput in MaterialExpressionVertexShader
            if (metaClass->IsTypeMatch(query_meta_class<MaterialExpressionVertexShader>()->id()))
            {
                for (int i5 = 1; i5 < expression->m_Inputs.size(); i5++)
                {
                    ExpressionInput* input = expression->m_Inputs[i5];
                    if (expressionNode.HasMember("m_CustomInterpolators"))
                    {
                        if (i5 - 1 < expressionNode["m_CustomInterpolators"].Size())
                        {
                            if (expressionNode["m_CustomInterpolators"][i5 - 1].HasMember("Input"))
                            {
                                auto expressionInputNode = expressionNode["m_CustomInterpolators"][i5 - 1]["Input"];
                                LinkInputPinToOutputPin(expressionInputNode, input);
                            }
                        }
                    }
                }
            }

            // Handle Input and Outupt in Set Attributes Expression
            if (metaClass->IsTypeMatch(query_meta_class<MaterialExpressionSetMaterialAttributes>()->id()))
            {
                for (int i6 = 1; i6 < expression->m_Inputs.size(); i6++)
                {
                    ExpressionInput* input = expression->m_Inputs[i6];
                    if (expressionNode.HasMember("m_AttributesInputs"))
                    {
                        if (i6 - 1 < expressionNode["m_AttributesInputs"].Size())
                        {
                            if (expressionNode["m_AttributesInputs"][i6 - 1].HasMember("Input"))
                            {
                                auto expressionInputNode = expressionNode["m_AttributesInputs"][i6 - 1]["Input"];
                                LinkInputPinToOutputPin(expressionInputNode, input);
                            }
                        }
                    }
                }
            }

            // Handle TerrainLayerBlendInput in MaterialExpressionTerrainLayerBlend
            if (metaClass->IsTypeMatch(query_meta_class<MaterialExpressionTerrainLayerBlend>()->id()))
            {
                for (int i7 = 0; i7 < expression->m_Inputs.size(); i7++)
                {
                    ExpressionInput* input = expression->m_Inputs[i7];
                    auto index = i7 / 2;
                    if (i7 & 1)
                    {
                        auto expressionInputNode = expressionNode["m_Layers"][index]["m_HeightInput"];
                        LinkInputPinToOutputPin(expressionInputNode, input);
                    }
                    else
                    {
                        auto expressionInputNode = expressionNode["m_Layers"][index]["m_LayerInput"];
                        LinkInputPinToOutputPin(expressionInputNode, input);
                    }
                }
            }
        }
    }

    return expressions;
}
void MaterialExpressionFunctionCall::RefreshPinsAndEditorData(IMaterialEditor* editor)
{
    auto GetNameFromPath = [](std::string path) {
        auto name = std::filesystem::path(path).filename().string();
        name = name.substr(0, name.find('.'));
        return name;
    };

    if (editor && loadParameters && !m_MaterialFunction.empty())
    {
        RemoveFunctionParameters(editor);
    }
    m_MaterialFunctionEditorData.Clear();

    if (!m_MaterialFunction.empty())
    {
        if (auto materialFunctionResource = TypeCast<resource::MaterialFunction>(gAssetStreamingManager->LoadSynchronously(m_MaterialFunction)); materialFunctionResource)
        {
            auto& defines = materialFunctionResource->GetMaterialFunctionDefines();
            if (defines.ExposeToLibrary)
            {
                m_MaterialFunctionEditorData.MaterialFunction = materialFunctionResource;
                m_MaterialFunctionEditorData.Expressions = MaterialExpression::DeserializeExpressions(DeserializeNode::ParseFromJson(materialFunctionResource->GetExpressionsString()));
                if (editor)
                {
                    GetFunctionParameters(editor);
                }

                m_Name = GetNameFromPath(materialFunctionResource->GetName());

                // Save previous link info
                std::unordered_map<SInt32, ExpressionOutput*> previousInputPinLink;
                std::unordered_map<SInt32, std::vector<ExpressionInput*>> previousOutputPinLinks;
                if (m_PrevMaterialFunction == m_MaterialFunction)
                {
                    for (auto& functionInput : m_FunctionInputs)
                    {
                        if (functionInput.FuncInoutId != -1 && functionInput.Input.m_LinkedExpressionOutput)
                        {
                            previousInputPinLink[functionInput.FuncInoutId] = functionInput.Input.m_LinkedExpressionOutput;
                            functionInput.Input.m_LinkedExpressionOutput = nullptr;
                        }
                    }

                    if (editor)
                    {
                        for (auto& functionOutput : m_FunctionOutputs)
                        {
                            if (functionOutput.FuncInoutId != -1)
                            {
                                previousOutputPinLinks[functionOutput.FuncInoutId] = editor->GetLinkedInputPins(&functionOutput.Output);
                            }
                        }
                    }
                }

                // refresh pins data
                uint32_t inputPinCount = 0, outputPinCount = 0;
                for (auto expression : m_MaterialFunctionEditorData.Expressions)
                {
                    if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()))
                    {
                        functionInput->SetInMaterialFunctionCall();
                        inputPinCount++;
                    }
                    else if (dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
                    {
                        outputPinCount++;
                    }
                }

                // Use resize to remain previous links
                m_FunctionInputs.resize(inputPinCount);
                m_FunctionOutputs.resize(outputPinCount);

                m_Inputs.resize(inputPinCount);
                m_Outputs.resize(outputPinCount);

                std::vector<UInt32> inputSortIndices;
                inputSortIndices.resize(inputPinCount);
                std::vector<UInt32> outputSortIndices;
                outputSortIndices.resize(outputPinCount);

                // Sort
                {
                    std::vector<std::pair<float, UInt32>> inputSortPairs;
                    inputSortPairs.resize(inputPinCount);
                    std::vector<std::pair<float, UInt32>> outputSortPairs;
                    outputSortPairs.resize(outputPinCount);

                    UInt32 inputPinIndex = 0, outputPinIndex = 0;
                    for (auto expression : m_MaterialFunctionEditorData.Expressions)
                    {
                        if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()))
                        {
                            inputSortPairs[inputPinIndex] = {functionInput->m_SortPriority, inputPinIndex};
                            inputPinIndex++;
                        }
                        else if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
                        {
                            outputSortPairs[outputPinIndex] = {functionOutput->m_SortPriority, outputPinIndex};
                            outputPinIndex++;
                        }
                    }

                    std::sort(inputSortPairs.begin(), inputSortPairs.end(), [](const std::pair<float, UInt32>& a, const std::pair<float, UInt32>& b) { return a.first < b.first; });
                    std::sort(outputSortPairs.begin(), outputSortPairs.end(), [](const std::pair<float, UInt32>& a, const std::pair<float, UInt32>& b) { return a.first < b.first; });

                    int i = 0;
                    for (auto [_, index] : inputSortPairs)
                    {
                        inputSortIndices[index] = i++;
                    }
                    i = 0;
                    for (auto [_, index] : outputSortPairs)
                    {
                        outputSortIndices[index] = i++;
                    }
                }

                UInt32 inputPinIndex = 0, outputPinIndex = 0;
                for (auto expression : m_MaterialFunctionEditorData.Expressions)
                {
                    if (auto* functionInput = dynamic_cast<MaterialExpressionFunctionInput*>(expression.get()))
                    {
                        FunctionExpressionInput& input = m_FunctionInputs[inputSortIndices[inputPinIndex]];
                        input.InputExpression = functionInput;
                        input.Input.m_Name = functionInput->m_InputName;
                        input.FuncInoutId = functionInput->m_FuncInoutId;

                        if (auto iter = previousInputPinLink.find(input.FuncInoutId); iter != previousInputPinLink.end())
                        {
                            input.Input.m_LinkedExpressionOutput = iter->second;
                        }

                        functionInput->m_FunctionExpressionInput = &input;

                        m_Inputs[inputSortIndices[inputPinIndex]] = &input.Input;

                        inputPinIndex++;
                    }
                    else if (auto* functionOutput = dynamic_cast<MaterialExpressionFunctionOutput*>(expression.get()))
                    {
                        FunctionExpressionOutput& output = m_FunctionOutputs[outputSortIndices[outputPinIndex]];
                        output.OutputExpression = functionOutput;
                        output.Output.m_ParentExpression = this;
                        output.Output.m_Name = functionOutput->m_OutputName;
                        output.FuncInoutId = functionOutput->m_FuncInoutId;

                        if (auto iter = previousOutputPinLinks.find(output.FuncInoutId); iter != previousOutputPinLinks.end())
                        {
                            for (ExpressionInput* input : iter->second)
                            {
                                input->m_LinkedExpressionOutput = &output.Output;
                            }
                        }

                        m_Outputs[outputSortIndices[outputPinIndex]] = &output.Output;

                        outputPinIndex++;
                    }
                }

                m_PrevMaterialFunction = m_MaterialFunction;

                return;
            }
        }
    }

    m_Inputs.clear();
    m_Outputs.clear();
    m_FunctionInputs.clear();
    m_FunctionOutputs.clear();
    m_PrevMaterialFunction.clear();
}

void MaterialExpressionFunctionCall::GetFunctionParameters(IMaterialEditor* editor)
{
    for (auto expression : m_MaterialFunctionEditorData.Expressions)
    {
        if (auto expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
        {
            editor->AddParameter(expressionParameter);
        }
        else if (auto expressionFuncCall = std::dynamic_pointer_cast<MaterialExpressionFunctionCall>(expression))
        {
            expressionFuncCall->GetFunctionParameters(editor);
        }
    }
    loadParameters = true;
}

void MaterialExpressionFunctionCall::RemoveFunctionParameters(IMaterialEditor* editor)
{
    for (auto expression : m_MaterialFunctionEditorData.Expressions)
    {
        if (auto expressionParameter = dynamic_cast<MaterialExpressionParameter*>(expression.get()))
        {
            editor->RemoveParameter(expressionParameter);
        }
        else if (auto expressionFuncCall = std::dynamic_pointer_cast<MaterialExpressionFunctionCall>(expression))
        {
            expressionFuncCall->RemoveFunctionParameters(editor);
        }
    }
}

ExpressionOutput* MaterialExpressionFunctionCall::GetOutputByFuncInoutId(SInt32 id)
{
    for (auto& functionOutput : m_FunctionOutputs)
    {
        if (functionOutput.FuncInoutId == id)
        {
            return &functionOutput.Output;
        }
    }

    assert(false);
    return nullptr;
}

SInt32 MaterialExpressionFunctionCall::GetFunctionInoutId(ExpressionOutput* output) const
{
    for (auto& functionOutput : m_FunctionOutputs)
    {
        if (&functionOutput.Output == output)
        {
            return functionOutput.FuncInoutId;
        }
    }

    assert(false);
    return 0;
}

MaterialExpressionFunctionOutput* MaterialExpressionFunctionCall::FindExpressionFunctionOutput(ExpressionOutput* output)
{
    for (auto& functionOutput : m_FunctionOutputs)
    {
        if (&functionOutput.Output == output)
        {
            return functionOutput.OutputExpression;
        }
    }

    return nullptr;
}

int32_t MaterialExpressionFunctionInput::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    const static MaterialValueType FunctionTypeMapping[] {
        MaterialValueType::MCT_Float1,
        MaterialValueType::MCT_Float2,
        MaterialValueType::MCT_Float3,
        MaterialValueType::MCT_Float4,
        MaterialValueType::MCT_Texture2D,
        MaterialValueType::MCT_Texture2DNormal,
        MaterialValueType::MCT_StaticBool,
        MaterialValueType::MCT_Float3,
        MaterialValueType::MCT_Bool,
    };

    int32_t result = CODE_CHUNK_INDEX_NONE;

    if (!m_InMaterialFunctionCall)
    {
        result = CompilePreviewValue(compiler);
    }
    else
    {
        if (m_FunctionExpressionInput && m_FunctionExpressionInput->Input.m_LinkedExpressionOutput)
        {
            MaterialFunctionCompileState* functionState = compiler.PopFunction();

            result = m_FunctionExpressionInput->Input.Compile(compiler);

            compiler.PushFunction(functionState);
        }
        else if (m_UsePreviewAsDefault && m_Preview.m_LinkedExpressionOutput)
        {
            result = m_Preview.Compile(compiler);
        }
        else
        {
            MaterialFunctionCompileState* functionState = compiler.PopFunction();
            switch (m_InputType)
            {
                case FunctionInputType::Float1:
                    result = compiler.Constant(m_PreviewValue.x);
                    break;
                case FunctionInputType::Float2:
                    result = compiler.Constant2(m_PreviewValue.x, m_PreviewValue.y);
                    break;
                case FunctionInputType::Float3:
                    result = compiler.Constant3(m_PreviewValue.x, m_PreviewValue.y, m_PreviewValue.z);
                    break;
                case FunctionInputType::Float4:                      
                    result =  compiler.Constant4(m_PreviewValue.x, m_PreviewValue.y, m_PreviewValue.z, m_PreviewValue.w);
                    break;
            }
            compiler.PushFunction(functionState);
        }
    }

    if (result != CODE_CHUNK_INDEX_NONE)
    {
        MaterialValueType srcType = compiler.GetParameterType(result);
        MaterialValueType dstType = FunctionTypeMapping[ToUnderlying(m_InputType)];
        if (srcType == MaterialValueType::MCT_Bool && dstType == MaterialValueType::MCT_StaticBool)
        {
            return result;
        }
        bool srcIsTexture2D = EnumHasAnyFlags(srcType, MaterialValueType::MCT_Texture2D | MaterialValueType::MCT_Texture2DNormal);
        bool dstIsTexture2D = (m_InputType == FunctionInputType::Texture2D) || (m_InputType == FunctionInputType::Texture2DNormal);
        if (!m_IsUETexture2DInput || !srcIsTexture2D || !dstIsTexture2D)
        {
            result = compiler.ValidCast(result, FunctionTypeMapping[ToUnderlying(m_InputType)]);
        }
    }

    return result;
}

int32_t MaterialExpressionFunctionInput::CompilePreviewValue(MaterialCompiler& compiler)
{
    if (m_Preview.m_LinkedExpressionOutput)
    {
        return m_Preview.Compile(compiler);
    }
    else
    {
        switch (m_InputType)
        {
        case FunctionInputType::Float1:
            return compiler.Constant(m_PreviewValue.x);
        case FunctionInputType::Float2:
            return compiler.Constant2(m_PreviewValue.x, m_PreviewValue.y);
        case FunctionInputType::Float3:
            return compiler.Constant3(m_PreviewValue.x, m_PreviewValue.y, m_PreviewValue.z);
        case FunctionInputType::Float4:
            return compiler.Constant4(m_PreviewValue.x, m_PreviewValue.y, m_PreviewValue.z, m_PreviewValue.w);
        default:
            return compiler.Errorf("Missing Preview connection");
        }
    }
}

MaterialValueType MaterialExpressionFunctionInput::FunctionInputTypeToMaterialValueType(FunctionInputType type)
{
    switch (type)
    {
    case FunctionInputType::Float1:
        return MaterialValueType::MCT_Float;
    case FunctionInputType::Float2:
        return MaterialValueType::MCT_Float2;
    case FunctionInputType::Float3:
        return MaterialValueType::MCT_Float3;
    case FunctionInputType::Float4:
        return MaterialValueType::MCT_Float4;
    case FunctionInputType::Texture2D:
        return MaterialValueType::MCT_Texture2D;
    case FunctionInputType::Texture2DNormal:
        return MaterialValueType::MCT_Texture2DNormal;
    case FunctionInputType::StaticBool:
        return MaterialValueType::MCT_StaticBool;
    case FunctionInputType::MaterialAttributes:
        return MaterialValueType::MCT_MaterialAttributes;
    }

    assert(false);
    return MaterialValueType::MCT_Float;
}

int32_t MaterialExpressionFunctionOutput::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (!m_Output.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing function output '{}'", m_Output.m_Name);
    }

    return m_Output.Compile(compiler);
}

int32_t MaterialExpressionIf::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t a = m_A.Compile(compiler);
    int32_t b = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);
    int32_t aGreaterThanB = m_AGreaterThanB.Compile(compiler);
    int32_t aEqualB = m_AEqualsB.m_LinkedExpressionOutput ? m_AEqualsB.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t aLessThanB = m_ALessThanB.Compile(compiler);

    return compiler.If(a, b, aGreaterThanB, aEqualB, aLessThanB);
}

int32_t MaterialExpressionLerp::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);
    int32_t arg3 = m_Alpha.m_LinkedExpressionOutput ? m_Alpha.Compile(compiler) : compiler.Constant(m_ConstAlpha);

    return compiler.Lerp(arg1, arg2, arg3);
}

int32_t MaterialExpressionLogarithm2::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Logarithm2(arg);
}

int32_t MaterialExpressionLogarithm10::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Logarithm10(arg);
}

int32_t MaterialExpressionLuminance::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    const int32_t luminanceFactorsIndex = compiler.Constant3(m_LuminanceFactors.x, m_LuminanceFactors.y, m_LuminanceFactors.z);
    const int32_t inputIndex = m_Input.Compile(compiler);
    if (inputIndex == INDEX_NONE)
    {
        return INDEX_NONE;
    }

    return compiler.Dot(compiler.ComponentMask(inputIndex, true, true, true, false), luminanceFactorsIndex);
}

void MaterialExpressionLuminance::DoHandlePropertyChange(IMaterialEditor* editor)
{
    switch (m_LuminanceMode)
    {
    case MaterialLuminanceMode::ACEScg:
        m_LuminanceFactors = Float3(0.2722287f, 0.6740818f, 0.0536895f);
        break;

    case MaterialLuminanceMode::Rec709:
        m_LuminanceFactors = Float3(0.2126f, 0.7152f, 0.0722f);
        break;

    case MaterialLuminanceMode::Rec2020:
        m_LuminanceFactors = Float3(0.2627f, 0.6780f, 0.0593f);
        break;
    default:
        break;
    }
}

int32_t MaterialExpressionMax::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Max(arg1, arg2);
}

int32_t MaterialExpressionMin::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Min(arg1, arg2);
}

int32_t MaterialExpressionMultiply::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Mul(arg1, arg2);
}

void MaterialExpressionParameter::OnPropertyChange(IMaterialEditor* editor)
{
    editor->OnParameterChange(this);
    MaterialExpression::OnPropertyChange(editor);
}

int32_t MaterialExpressionPixelDepth::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.PixelDepth();
}

int32_t MaterialExpressionPixelLinearDepth::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.PixelLinearDepth();
}

int32_t MaterialExpressionSceneDepth::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.SceneDepth();
}


int32_t MaterialExpressionSceneLinearDepth::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.SceneLinearDepth();
}

int32_t MaterialExpressionPower::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_Base.Compile(compiler);
    int32_t arg2 = m_Exponent.m_LinkedExpressionOutput ? m_Exponent.Compile(compiler) : compiler.Constant(m_ConstExponent);

    return compiler.Power(arg1, arg2);
}

int32_t MaterialExpressionPanner::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t argTex = m_Coordinate.m_LinkedExpressionOutput ? m_Coordinate.Compile(compiler) : compiler.TextureCoordinate(0);
    int32_t argTime = m_Time.m_LinkedExpressionOutput ? m_Time.Compile(compiler) : compiler.Time();
    int32_t argSpeedVector = m_Speed.m_LinkedExpressionOutput ? compiler.ComponentMask(m_Speed.Compile(compiler), true, true, false, false) : compiler.Constant2(m_SpeedX, m_SpeedY);

    return m_bFractionalPart ? compiler.Add(argTex, compiler.Frac(compiler.Mul(argSpeedVector, argTime))) : compiler.Add(argTex, compiler.Mul(argSpeedVector, argTime));
}

int32_t MaterialExpressionRotateAboutAxis::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (!m_NormalizedRotationAxis.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing RotateAboutAxis input NormalizedRotationAxis");
    }
    else if (!m_RotationAngle.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing RotateAboutAxis input RotationAngle");
    }
    else if (!m_PivotPoint.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing RotateAboutAxis input PivotPoint");
    }
    else if (!m_Position.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing RotateAboutAxis input Position");
    }
    else
    {
        const int32_t AngleIndex = compiler.Mul(m_RotationAngle.Compile(compiler), compiler.Constant(2.0f * 3.1415926535897932f / m_Period));
        const int32_t RotationIndex = compiler.AppendVector(compiler.ForceCast(m_NormalizedRotationAxis.Compile(compiler), MaterialValueType::MCT_Float3), compiler.ForceCast(AngleIndex, MaterialValueType::MCT_Float1));

        return compiler.RotateAboutAxis(RotationIndex, m_PivotPoint.Compile(compiler), m_Position.Compile(compiler));
    }
}


int32_t MaterialExpressionRound::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
    {
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Round(arg);
}

int32_t MaterialExpressionSaturate::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t arg = m_Value.Compile(compiler);

    return compiler.Saturate(arg);
}

int32_t MaterialExpressionScalarParameter::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ScalarParameter(m_ParameterName, m_DefaultValue);
}

int32_t MaterialExpressionScreenUV::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ScreenUV();
}

int32_t MaterialExpressionVertexShader::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    compiler.BeginCompileShaderFrequency(this, SF_WorldPositionOffset);
    {
        m_CompileResult.WorldPositionOffset = m_WorldPositionOffset.CompileWithDefault(compiler, EMaterialProperty::WorldPositionOffset);
    }
    compiler.EndCompileShaderFrequency();

    compiler.BeginCompileShaderFrequency(this, SF_GenerateCustomInterpolators);
    {
        for (auto& ci : m_CustomInterpolators)
        {
            int32_t customInterpolatorArg = ci.Input.m_LinkedExpressionOutput ? ci.Input.Compile(compiler) : compiler.Constant(0);
            compiler.AddCustomInterpolator(ci.Type, ci.Name, customInterpolatorArg);
            customInterpolatorArg = compiler.ForceCast(customInterpolatorArg, GetMaterialValueType(ci.Type));
            //m_CompileResult.CustomInterpolators.push_back(customInterpolatorArg);
        }
    }
    compiler.EndCompileShaderFrequency();

    return CODE_CHUNK_INDEX_NONE;
}

void MaterialExpressionVertexShader::OnPropertyChange(IMaterialEditor* editor)
{
    // one for m_WorldPositionOffset
    m_Inputs.resize(1);

    auto FindSameName = [&](CustomInterpolatorInput* interpolator, const std::string& name) {
        for (auto& t : m_CustomInterpolators)
        {
            if (&t != interpolator && name == t.Name)
            {
                return true;
            }
        }

        return false;
    };

    auto ValidateName = [&](CustomInterpolatorInput* interpolator) {
        if (!interpolator->Name.empty() && !FindSameName(interpolator, interpolator->Name))
            return;

        int index = 0;
        while (true)
        {
            std::string newName = "CustomInterpolator";
            if (index > 0)
                newName += "_" + std::to_string(index);

            if (!FindSameName(interpolator, newName))
            {
                interpolator->Name = newName;
                return;
            }

            index++;
        }
    };

    for (auto& interpolator : m_CustomInterpolators)
    {
        ValidateName(&interpolator);
        interpolator.Input.m_Name = interpolator.Name;
        m_Inputs.push_back(&interpolator.Input);
    }

    // some links may be invalid, clear them
    editor->ClearInvalidLinks();

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);

    // the change of this expression may have an impact to the final result, notify MaterialEditor to refresh shader
    editor->UpdateExpressionsState(this);
}

void MaterialExpressionVertexShader::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    for (auto& interpolator : m_CustomInterpolators)
    {
        interpolator.Input.m_Name = interpolator.Name;
        m_Inputs.push_back(&interpolator.Input);
    }
}

int32_t MaterialExpressionSurfaceShader::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    compiler.BeginCompileShaderFrequency(this, SF_Surface);
    {
        if (compiler.IsCompileShadowPass())
        {
            int32_t opacityMaskArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::OpacityMask) : m_OpacityMask.CompileAttributes(compiler, EMaterialProperty::OpacityMask);
            m_CompileResult.opacityMask = compiler.ForceCast(opacityMaskArg, MaterialValueType::MCT_Float1);
        }
        else
        {
            int32_t baseColorArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::BaseColor) : m_BaseColor.CompileAttributes(compiler, EMaterialProperty::BaseColor, reinterpret_cast<float*>(&m_BaseColorConstant)); 
            int32_t metallicArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::Metallic) : m_Metallic.CompileAttributes(compiler, EMaterialProperty::Metallic, &m_MetallicConstant); 
            int32_t specularArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::Specular) : m_Specular.CompileAttributes(compiler, EMaterialProperty::Specular, &m_SpecularConstant); 
            int32_t roughnessArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::Roughness) : m_Roughness.CompileAttributes(compiler, EMaterialProperty::Roughness, &m_RoughnessConstant); 
            int32_t opacityArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::Opacity) : m_Opacity.CompileAttributes(compiler, EMaterialProperty::Opacity, &m_OpacityConstant); 
            int32_t opacityMaskArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::OpacityMask) : m_OpacityMask.CompileAttributes(compiler, EMaterialProperty::OpacityMask); 
            int32_t normalArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::Normal) : m_Normal.CompileAttributes(compiler, EMaterialProperty::Normal); 
            int32_t ambientOcclusionArg =  m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::AmbientOcclusion) : m_AmbientOcclusion.CompileAttributes(compiler, EMaterialProperty::AmbientOcclusion);
            int32_t emissiveColorArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::EmissiveColor) : m_EmissiveColor.CompileAttributes(compiler, EMaterialProperty::EmissiveColor); 
            int32_t subsurfaceColorArg = m_MaterialAttributes.m_Enable ? m_MaterialAttributes.CompileWithDefault(compiler, EMaterialProperty::SubsurfaceColor) : m_SubsurfaceColor.CompileAttributes(compiler, EMaterialProperty::SubsurfaceColor); 
            int32_t debugColorArg = m_DebugColor.m_LinkedExpressionOutput ? m_DebugColor.Compile(compiler) : compiler.Constant3(0, 0, 0);
            int32_t temporalReactiveArg = m_TemporalReactive.m_LinkedExpressionOutput ? m_TemporalReactive.Compile(compiler) : compiler.Constant2(0, 0);

            m_CompileResult.baseColor = compiler.ForceCast(baseColorArg, MaterialValueType::MCT_Float3);
            m_CompileResult.metallic = compiler.ForceCast(metallicArg, MaterialValueType::MCT_Float1);
            m_CompileResult.specular = compiler.ForceCast(specularArg, MaterialValueType::MCT_Float1);
            m_CompileResult.roughness = compiler.ForceCast(roughnessArg, MaterialValueType::MCT_Float1);
            m_CompileResult.opacity = compiler.ForceCast(opacityArg, MaterialValueType::MCT_Float1);
            m_CompileResult.opacityMask = compiler.ForceCast(opacityMaskArg, MaterialValueType::MCT_Float1);
            m_CompileResult.normal = compiler.ValidCast(normalArg, MaterialValueType::MCT_Float3);
            m_CompileResult.ambientOcclusion = compiler.ForceCast(ambientOcclusionArg, MaterialValueType::MCT_Float1);
            m_CompileResult.emissiveColor = compiler.ForceCast(emissiveColorArg, MaterialValueType::MCT_Float3);
            m_CompileResult.subsurfaceColor = compiler.ForceCast(subsurfaceColorArg, MaterialValueType::MCT_Float3);
            m_CompileResult.debugColor = compiler.ForceCast(debugColorArg, MaterialValueType::MCT_Float3);
            m_CompileResult.temporalReactive = compiler.ForceCast(temporalReactiveArg, MaterialValueType::MCT_Float2);
        }

    }
    compiler.EndCompileShaderFrequency();

    return CODE_CHUNK_INDEX_NONE;
}

void MaterialExpressionSurfaceShader::UpdatePinEnables(const MaterialDefines& defines)
{
    // help functions
    auto EnablePins = [](const std::vector<ExpressionPin*>& pins) {
        for (auto* pin : pins)
        {
            pin->m_Enable = true;
        }
    };

    auto DisablePins = [](const std::vector<ExpressionPin*>& pins) {
        for (auto* pin : pins)
        {
            pin->m_Enable = false;
        }
    };

    // first set all pins disable
    for (auto* pin : GetInputPins())
    {
        pin->m_Enable = false;
    }

    // Just Use MaterialAttributes

    switch (defines.Domain)
    {
    case MaterialDomain::Surface:
    {
        if (defines.UseMaterialAttributes)
        {
            EnablePins({&m_MaterialAttributes});
            switch (defines.BlendMode)
            {
            case MaterialBlendMode::Opaque:
                EnablePins({&m_DebugColor});
                break;
            case MaterialBlendMode::Masked:
                EnablePins({&m_DebugColor});
                break;
            case MaterialBlendMode::Modulate:
                break;
            default:
                EnablePins({&m_TemporalReactive});
                break;
            }

            return;
        }
        // handle BlendMode
        switch (defines.BlendMode)
        {
        case MaterialBlendMode::Opaque:
            EnablePins({&m_DebugColor});
            break;
        case MaterialBlendMode::Masked:
            EnablePins({&m_OpacityMask, &m_DebugColor});
            break;
        case MaterialBlendMode::Modulate:
            break;
        default:
            EnablePins({&m_Opacity, &m_TemporalReactive});
            break;
        }

        // handle ShadingModel
        switch (defines.ShadingModel)
        {
        case MaterialShadingModel::Standard:
            EnablePins({&m_BaseColor, &m_Metallic, &m_Specular, &m_Roughness, &m_Normal, &m_AmbientOcclusion, &m_EmissiveColor});
            break;
        case MaterialShadingModel::Unlit:
            EnablePins({&m_EmissiveColor});
            break;
        default:
            EnablePins({&m_BaseColor, &m_Metallic, &m_Specular, &m_Roughness, &m_Opacity, &m_Normal, &m_AmbientOcclusion, &m_EmissiveColor, &m_SubsurfaceColor});
            break;
        }
        break;
    }
    case MaterialDomain::PostProcess:
    {
        EnablePins({&m_EmissiveColor});
        if (defines.PostProcessEnableOutputAlpha)
        {
            EnablePins({&m_Opacity});
        }
        break;
    }
    case MaterialDomain::MeshDecal:
    {
        if (defines.BlendMode == MaterialBlendMode::Translucent)
        {
            EnablePins({&m_BaseColor, &m_Metallic, &m_Specular, &m_Roughness, &m_EmissiveColor, &m_Opacity, &m_Normal, &m_AmbientOcclusion});
        }
        break;
    }
    default:
    {
        Assert(false);
        break;
    }
    }
}

int32_t MaterialExpressionShaderConstBool::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ShaderConstBool(m_ParameterName, m_Name, m_Value);
}

int32_t MaterialExpressionSign::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Sign(arg);
}

int32_t MaterialExpressionSine::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Sine(arg);
}

int32_t MaterialExpressionSquareRoot::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.SquareRoot(arg);
}

int32_t MaterialExpressionStaticBool::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.StaticBool(m_Value);
}

int32_t MaterialExpressionStaticSwitch::Compile(MaterialCompiler& compiler, ExpressionOutput* outoutPin)
{
    int32_t condition = m_Value.m_LinkedExpressionOutput ? m_Value.Compile(compiler) : CODE_CHUNK_INDEX_NONE;

    if (condition != CODE_CHUNK_INDEX_NONE && compiler.GetParameterType(condition) == MaterialValueType::MCT_Bool)
    {
        return compiler.DynamicBranch(condition, m_True.Compile(compiler), m_False.Compile(compiler));
    }

    bool bValue = m_DefaultValue;
    if (condition != CODE_CHUNK_INDEX_NONE)
    {
        bool bSucceeded;
        bValue = compiler.GetStaticBoolValue(condition, bSucceeded);
        if (!bSucceeded)
        {
            return CODE_CHUNK_INDEX_NONE;
        }
    }

    return bValue ? m_True.Compile(compiler) : m_False.Compile(compiler);
}

int32_t MaterialExpressionStep::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_X.m_LinkedExpressionOutput ? m_X.Compile(compiler) : compiler.Constant(m_ConstX);
    int32_t arg2 = m_Y.m_LinkedExpressionOutput ? m_Y.Compile(compiler) : compiler.Constant(m_ConstY);

    return compiler.Step(arg1, arg2);
}

int32_t MaterialExpressionSubtract::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg1 = m_A.m_LinkedExpressionOutput ? m_A.Compile(compiler) : compiler.Constant(m_ConstA);
    int32_t arg2 = m_B.m_LinkedExpressionOutput ? m_B.Compile(compiler) : compiler.Constant(m_ConstB);

    return compiler.Sub(arg1, arg2);
}

int32_t MaterialExpressionTangent::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Tangent(arg);
}

int32_t MaterialExpressionTextureCoordinate::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    constexpr float smallNumber = 1.e-8f;

    if (abs(m_UTiling - m_VTiling) > smallNumber)
    {
        return compiler.Mul(compiler.TextureCoordinate(m_CoordinateIndex), compiler.Constant2(m_UTiling, m_VTiling));
    }
    else if (abs(1.0f - m_UTiling) > smallNumber)
    {
        return compiler.Mul(compiler.TextureCoordinate(m_CoordinateIndex), compiler.Constant(m_UTiling));
    }
    else
    {
        return compiler.TextureCoordinate(m_CoordinateIndex);
    }
}

int32_t MaterialExpressionTextureObject::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.TextureParameter(m_TextureObjectName, m_TextureType, m_TextureString);
}

void MaterialExpressionTextureObject::DoHandlePropertyChange(IMaterialEditor* editor)
{
    if (!m_TextureString.empty())
    {
        editor->UpdateUITextureAndExpressionTexture(this);
    }
}

int32_t MaterialExpressionTextureParameter::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.TextureParameter(m_ParameterName, m_TextureType, m_TextureString);
}

void MaterialExpressionTextureParameter::DoHandlePropertyChange(IMaterialEditor* editor)
{
    if (!m_TextureString.empty())
    {      
        editor->UpdateUITextureAndExpressionTexture(this);
        editor->UpdateExpressionsState(this);
    }
}

int32_t MaterialExpressionTextureSample::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t texArg;
    if (m_Tex.m_LinkedExpressionOutput)
    {
        texArg = m_Tex.Compile(compiler);
    }
    else
    {
        std::string textureObjectName = "TextureSample_" + std::to_string(m_Id);
        m_DefaultTexture = gResourceMgr.ConvertPathToGuid(m_DefaultTexture);
        texArg = compiler.TextureParameter(textureObjectName, m_DefaultTextureType, m_DefaultTexture);
    }
    int32_t uvArg = m_UV.m_LinkedExpressionOutput ? m_UV.Compile(compiler) : compiler.TextureCoordinate(0);

    int32_t levelArg = m_SamplerState.MipValueMode == TextureMipValueMode::MipLevel ? m_Level.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t biasArg = m_SamplerState.MipValueMode == TextureMipValueMode::MipBias ? m_Bias.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t ddxArg = m_SamplerState.MipValueMode == TextureMipValueMode::Derivative ? m_DDX.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t ddyArg = m_SamplerState.MipValueMode == TextureMipValueMode::Derivative ? m_DDY.Compile(compiler) : CODE_CHUNK_INDEX_NONE;

    return compiler.TextureSample(m_SamplerState, texArg, uvArg, levelArg, biasArg, ddxArg, ddyArg);
}
int32_t MaterialExpressionTextureSampleParameter::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t texArg = compiler.TextureParameter(m_ParameterName, m_TextureType, m_TextureString);
    
    int32_t uvArg = m_UV.m_LinkedExpressionOutput ? m_UV.Compile(compiler) : compiler.TextureCoordinate(0);

    int32_t levelArg = m_SamplerState.MipValueMode == TextureMipValueMode::MipLevel ? m_Level.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t biasArg = m_SamplerState.MipValueMode == TextureMipValueMode::MipBias ? m_Bias.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t ddxArg = m_SamplerState.MipValueMode == TextureMipValueMode::Derivative ? m_DDX.Compile(compiler) : CODE_CHUNK_INDEX_NONE;
    int32_t ddyArg = m_SamplerState.MipValueMode == TextureMipValueMode::Derivative ? m_DDY.Compile(compiler) : CODE_CHUNK_INDEX_NONE;

    return compiler.TextureSample(m_SamplerState, texArg, uvArg, levelArg, biasArg, ddxArg, ddyArg);
}

void MaterialExpressionTextureSampleParameter::DoHandlePropertyChange(IMaterialEditor* editor)
{
    m_Inputs.clear();
    m_Inputs.push_back(&m_UV);
    switch (m_SamplerState.MipValueMode)
    {
    case TextureMipValueMode::None:
        break;
    case TextureMipValueMode::MipLevel:
        m_Inputs.push_back(&m_Level);
        break;
    case TextureMipValueMode::MipBias:
        m_Inputs.push_back(&m_Bias);
        break;
    case TextureMipValueMode::Derivative:
        m_Inputs.push_back(&m_DDX);
        m_Inputs.push_back(&m_DDY);
        break;
    }

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);
    if (!m_TextureString.empty())
    {
        editor->UpdateUITextureAndExpressionTexture(this);
        editor->UpdateExpressionsState(this);
    }
}

void MaterialExpressionTextureSample::DoHandlePropertyChange(IMaterialEditor* editor)
{
    m_Inputs.clear();
    m_Inputs.push_back(&m_UV);
    m_Inputs.push_back(&m_Tex);

    switch (m_SamplerState.MipValueMode)
    {
    case TextureMipValueMode::None:
        break;
    case TextureMipValueMode::MipLevel:
        m_Inputs.push_back(&m_Level);
        break;
    case TextureMipValueMode::MipBias:
        m_Inputs.push_back(&m_Bias);
        break;
    case TextureMipValueMode::Derivative:
        m_Inputs.push_back(&m_DDX);
        m_Inputs.push_back(&m_DDY);
        break;
    }

    // notify MaterialEditor to update the corresponding graph node
    editor->UpdateExpressionAppearance(this);
    if (!m_DefaultTexture.empty())
    {
        editor->UpdateUITextureAndExpressionTexture(this);
        editor->UpdateExpressionsState(this);
    }
}

int32_t MaterialExpressionTime::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Time();
}

int32_t MaterialExpressionEyeAdaption::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.EyeAdaption();
}

int32_t MaterialExpressionTruncate::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t arg = m_Input.Compile(compiler);

    return compiler.Truncate(arg);
}

int32_t MaterialExpressionVectorParameter::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.VectorParameter(m_ParameterName, m_DefaultValue);
}

int32_t MaterialExpressionVertexID::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.VertexID();
}

int32_t MaterialExpressionWorldGeometryNormal::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.WorldGeometryNormal();
}

int32_t MaterialExpressionWorldPosition::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.WorldPosition();
}

int32_t MaterialExpressionComponentMask::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ComponentMask(m_Input.Compile(compiler), m_R, m_G, m_B, m_A);
}

int32_t MaterialExpressionSceneTexture::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    int32_t ViewportUV = INDEX_NONE;

    if (m_Coordinates.m_LinkedExpressionOutput)
    {
        ViewportUV = m_Coordinates.Compile(compiler);
    }

    if (outputPin == &m_Color)
    {
        // Color
        return compiler.SceneTextureLookup(ViewportUV, m_SceneTextureId, m_Filtered);
    }
    else if (outputPin == &m_Size)
    {
        return compiler.GetSceneTextureViewSize(m_SceneTextureId, false);
    }
    else if (outputPin == &m_InvSize)
    {
        return compiler.GetSceneTextureViewSize(m_SceneTextureId, true);
    }

    return compiler.Errorf("Invalid input parameter");
}

int32_t MaterialExpressionTransform::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.Transform(m_Source, m_Destination, m_Input.Compile(compiler), m_ElementType);
}
int32_t MaterialExpressionObjectPositionWS::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    return compiler.ObjectPositionWS();
}
void MaterialExpressionTransform::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    if (node.HasMember("m_IsPosition"))
    {
        m_ElementType = node["m_IsPosition"].AsBoolean() ? TransformElementType::Position : TransformElementType::Normal;
    }
}

int32_t MaterialExpressionWorldTangent::Compile(MaterialCompiler& compiler, ExpressionOutput* outputPin)
{
    if (compiler.IsShaderFrequency(ShaderFrequency::SF_Surface))
    {
        return compiler.WorldTangent();
    }
    else
    {
        return compiler.Error("Can't use World Tangent except in pixel shader");
    }
}

int32_t MaterialExpressionMaterialParameterCollection::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.MaterialParameterCollection(m_MpcFile, m_MpcEnum.GetSelectedProperty());
}

void MaterialExpressionMaterialParameterCollection::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    m_MpcEnum.OnChangeMpcFilePath(m_MpcFile);
}

void MaterialExpressionMaterialParameterCollection::OnPropertyChange(IMaterialEditor* editor)
{
    m_MpcEnum.OnChangeMpcFilePath(m_MpcFile);
    MaterialExpression::OnPropertyChange(editor);
}

int32_t MaterialExpressionTerrainColor::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.TerrainColor();
}

int32_t MaterialExpressionTerrainLayerCoords::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    switch (m_CustomUVType)
    {
    case LCCT_CustomUV0:
        return compiler.TerrainCoords(compiler.TextureCoordinate(0));
    case LCCT_CustomUV1:
        return compiler.TerrainCoords(compiler.TextureCoordinate(1));
    case LCCT_CustomUV2:
        return compiler.TerrainCoords(compiler.TextureCoordinate(2));
    case LCCT_WeightMapUV:
        return compiler.TerrainCoords(compiler.TextureCoordinate(3));
    default:
        break;
    }

    int32_t baseUV = INDEX_NONE;
    switch (m_MappingType)
    {
    case TCMT_Auto:
    case TCMT_XY: baseUV = compiler.TerrainCoords(compiler.TextureCoordinate(0)); break;
    case TCMT_XZ: baseUV = compiler.TerrainCoords(compiler.TextureCoordinate(1)); break;
    case TCMT_YZ:
    {
        int Y = compiler.ComponentMask(compiler.TextureCoordinate(0), 0, 1, 0, 0);
        int Z = compiler.ComponentMask(compiler.TextureCoordinate(1), 0, 1, 0, 0);
        baseUV = compiler.TerrainCoords(compiler.AppendVector(Y, Z));
    }
    break;
    default:
        return INDEX_NONE;
    };

    float scale = (m_MappingScale == 0.0f) ? 1.0f : 1.0f / m_MappingScale;
    const float rotX = cos(m_MappingRotation * PI / 180.0f) * scale;
    const float rotY = sin(m_MappingRotation * PI / 180.0f) * scale;

    int32_t transformedUV = compiler.Add(
        compiler.AppendVector(
            compiler.Dot(baseUV, compiler.Constant2(+rotX, +rotY)),
            compiler.Dot(baseUV, compiler.Constant2(-rotY, +rotX))),
        compiler.Constant2(m_MappingPanU, m_MappingPanV)
    );

    return compiler.TerrainCoords(transformedUV);
}

int32_t MaterialExpressionTerrainLayerWeight::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t baseCode = m_Base.m_LinkedExpressionOutput ? m_Base.Compile(compiler) : compiler.Constant3(m_ConstBase.x, m_ConstBase.y, m_ConstBase.z);
    int32_t weightCode = compiler.StaticTerrainLayerWeight(m_LayerIndex);

    int32_t returnCode = CODE_CHUNK_INDEX_NONE;
    if (weightCode == CODE_CHUNK_INDEX_NONE)
    {
        returnCode = baseCode;
    }
    else
    {
        int32_t layerCode = m_Layer.Compile(compiler);
        returnCode = compiler.Add(baseCode, compiler.Mul(layerCode, weightCode));
    }

    return returnCode;
}

int32_t MaterialExpressionTerrainLayerBlend::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    std::vector<int32_t> weightCodes;
    weightCodes.reserve(m_Layers.size());
    auto needsRenormalize = false;
    int32_t weightSumCode = compiler.Constant(0.f);
    for (auto layerIdx = 0; layerIdx != m_Layers.size(); layerIdx++)
    {
        weightCodes.push_back(CODE_CHUNK_INDEX_NONE);
        auto& layer = m_Layers[layerIdx];
        if (layer.m_BlendType != LB_AlphaBlend)
        {
            int32_t heightCode = layer.m_HeightInput.m_LinkedExpressionOutput ? layer.m_HeightInput.Compile(compiler) : compiler.Constant(layer.m_ConstHeightInput);
            int32_t weightCode = compiler.StaticTerrainLayerWeight(layerIdx);
            if (weightCode != CODE_CHUNK_INDEX_NONE)
            {
                switch (layer.m_BlendType)
                {
                case LB_WeightBlend:
                {
                    weightCodes[layerIdx] = weightCode;
                    weightSumCode = compiler.Add(weightSumCode, weightCode);
                }
                    break;
                case LB_HeightBlend:
                {
                    needsRenormalize = true;
                    int32_t modifiedWeightCode = compiler.Clamp
                    (
                        compiler.Add(compiler.Lerp(compiler.Constant(-1.f), compiler.Constant(1.f), weightCode), heightCode),
                        compiler.Constant(0.0001f),
                        compiler.Constant(1.f)
                    );
                    weightCodes[layerIdx] = modifiedWeightCode;
                    weightSumCode = compiler.Add(weightSumCode, modifiedWeightCode);
                }
                    break;
                default:
                    break;
                }
            }
        }
    }

    int32_t invWeightSumCode = compiler.Div(compiler.Constant(1.f), weightSumCode);
    int32_t returnCode = compiler.Constant(0.f);
    for (auto layerIdx = 0; layerIdx != m_Layers.size(); layerIdx++)
    {
        auto& layer = m_Layers[layerIdx];
        if (weightCodes[layerIdx] != CODE_CHUNK_INDEX_NONE)
        {
            int32_t layerCode = layer.m_LayerInput.m_LinkedExpressionOutput ? layer.m_LayerInput.Compile(compiler) :
                compiler.Constant3(layer.m_ConstLayerInput.x, layer.m_ConstLayerInput.y, layer.m_ConstLayerInput.z);
            int32_t weightCode = weightCodes[layerIdx];
            if (needsRenormalize)
            {
                returnCode = compiler.Add(returnCode, compiler.Mul(compiler.Mul(layerCode, weightCode), invWeightSumCode));
            }
            else
            {
                returnCode = compiler.Add(returnCode, compiler.Mul(layerCode, weightCode));
            }
        }
    }

    for (auto layerIdx = 0; layerIdx != m_Layers.size(); layerIdx++)
    {
        auto& layer = m_Layers[layerIdx];
        if (layer.m_BlendType == LB_AlphaBlend)
        {
            int32_t weightCode = compiler.StaticTerrainLayerWeight(layerIdx);
            if (weightCode != CODE_CHUNK_INDEX_NONE)
            {
                int32_t layerCode = layer.m_LayerInput.m_LinkedExpressionOutput ? layer.m_LayerInput.Compile(compiler) :
                    compiler.Constant3(layer.m_ConstLayerInput.x, layer.m_ConstLayerInput.y, layer.m_ConstLayerInput.z);
                returnCode = compiler.Lerp(returnCode, layerCode, weightCode);
            }
        }
    }

    return returnCode;
}

void MaterialExpressionTerrainLayerBlend::OnPropertyChange(IMaterialEditor* editor)
{
    m_Inputs.clear();
    for (auto& layer : m_Layers)
    {
        layer.m_LayerInput.m_Name = fmt::format("{}_Layer", layer.m_LayerName);
        m_Inputs.push_back(&layer.m_LayerInput);
        layer.m_HeightInput.m_Name = fmt::format("{}_Height", layer.m_LayerName);
        m_Inputs.push_back(&layer.m_HeightInput);
    }

    editor->ClearInvalidLinks();

    editor->UpdateExpressionAppearance(this);

    editor->UpdateExpressionsState(this);
}

void MaterialExpressionTerrainLayerBlend::Deserialize(const cross::DeserializeNode& node)
{
    using namespace gbf::reflection;

    const auto& layersNode = node["m_Layers"];
    if (!layersNode.IsNull())
    {
        for (auto i = 0; i != layersNode.Size(); i++)
        {
            const auto& inputNode = layersNode[i];
            {
                TerrainLayerBlendInput layerInput;
                layerInput.m_LayerName = inputNode["m_LayerName"].AsString();
                layerInput.m_LayerInput.m_Name = fmt::format("{}_Layer", layerInput.m_LayerName);
                m_Layers.push_back(std::move(layerInput));
                m_Inputs.push_back(&m_Layers.back().m_LayerInput);
            }
            {
                TerrainLayerBlendInput heightInput;
                heightInput.m_LayerName = inputNode["m_LayerName"].AsString();
                heightInput.m_HeightInput.m_Name = fmt::format("{}_Height", heightInput.m_LayerName);
                m_Layers.push_back(std::move(heightInput));
                m_Inputs.push_back(&m_Layers.back().m_HeightInput);
            }
        }
    }
}

void MaterialExpressionTerrainLayerBlend::AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context)
{
    using namespace gbf::reflection;

    for (auto& layer : m_Layers)
    {
        layer.m_LayerInput.m_Name = fmt::format("{}_Layer", layer.m_LayerName);
        m_Inputs.push_back(&layer.m_LayerInput);
        layer.m_HeightInput.m_Name = fmt::format("{}_Height", layer.m_LayerName);
        m_Inputs.push_back(&layer.m_HeightInput);
    }
}

int32_t MaterialExpressionNormalize::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (auto input = m_Input.Compile(compiler); input == CODE_CHUNK_INDEX_NONE)
    {
        std::string outputerror = fmt::format("Expression: {}, Missing Normalize input", m_Input.m_Name);
        return compiler.Error(outputerror.c_str());
    }
    else
    {
        return compiler.Normalize(input);
    }
}

int32_t MaterialExpressionObjectLocalBounds::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    BoundsOutputIndex outputIndex;
    if (output == &m_HalfExtent){
        outputIndex = BoundsOutputIndex::BoundsHalfExtent;
    }
    else if (output == &m_FullExtent){
        outputIndex = BoundsOutputIndex::BoundsExtent;
    }
    else if (output == &m_Min){
        outputIndex = BoundsOutputIndex::BoundsMin;
    }
    else if (output == &m_Max){
        outputIndex = BoundsOutputIndex::BoundsMax;
    }
    else{
        return compiler.Errorf("Invalid input parameter");
    }

    return compiler.ObjectLocalBounds(outputIndex);
}

int32_t MaterialExpressionShadowReplace::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (!m_Default.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing input Default");
    }
    else if (!m_Shadow.m_LinkedExpressionOutput)
    {
        return compiler.Errorf("Missing input Shadow");
    }
    else
    {
        int32_t Arg1 = m_Default.Compile(compiler);
        int32_t Arg2 = m_Shadow.Compile(compiler);
        return compiler.IsCompileShadowPass() ? Arg2 : Arg1;
    }
}

int32_t MaterialExpressionFresnel::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    // pow(1 - max(0,Normal dot Camera),Exponent) * (1 - BaseReflectFraction) + BaseReflectFraction
    int32_t NormalArg = m_Normal.m_LinkedExpressionOutput ? m_Normal.Compile(compiler) : compiler.WorldGeometryNormal();
    int32_t DotArg = compiler.Dot(NormalArg, compiler.ViewProperty(ExposedViewProperty::CameraVectorWS));
    int32_t MaxArg = compiler.Max(compiler.Constant(0.0f), DotArg);
    int32_t MinusArg = compiler.Sub(compiler.Constant(1.0f), MaxArg);
    int32_t ExponentArg = m_ExponentIn.m_LinkedExpressionOutput ? m_ExponentIn.Compile(compiler) : compiler.Constant(m_Exponent);
    int32_t AbsBaseArg = compiler.Max(compiler.Abs(MinusArg), compiler.Constant(0.0001f));
    int32_t PowArg = compiler.Power(AbsBaseArg, ExponentArg);
    int32_t BaseReflectFractionArg = m_BaseReflectFractionIn.m_LinkedExpressionOutput ? m_BaseReflectFractionIn.Compile(compiler) : compiler.Constant(m_BaseReflectFraction);
    int32_t ScaleArg = compiler.Mul(PowArg, compiler.Sub(compiler.Constant(1.0f), BaseReflectFractionArg));

    return compiler.Add(ScaleArg, BaseReflectFractionArg);
}

int32_t MaterialExpressionOneMinus::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.Sub(compiler.Constant(1.0f), m_Input.Compile(compiler));
}

int32_t MaterialExpressionSkyAtmosphereLightIlluminanceOnGround::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.SkyAtmosphereLightIlluminanceOnGround(m_LightIndex);
}

int32_t MaterialExpressionSkyAtmosphereViewLuminance::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.SkyAtmosphereViewLuminance();
}

int32_t MaterialExpressionSkyAtmosphereLightDiskLuminance::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    int32_t CosHalfDiskRadiusCodeChunk = INDEX_NONE;
    if (m_DiskAngularDiameterOverride.m_LinkedExpressionOutput)
    {
        // Convert from apex angle (angular diameter) to cosine of the disk radius.
        CosHalfDiskRadiusCodeChunk = compiler.Cosine(compiler.Mul(compiler.Constant(0.5f * 3.1415926535897932f / 180.0f), m_DiskAngularDiameterOverride.Compile(compiler)));
    }
    return compiler.SkyAtmosphereLightDiskLuminance(m_LightIndex, CosHalfDiskRadiusCodeChunk);
}

int32_t MaterialExpressionDistance::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    if (!m_A.m_LinkedExpressionOutput)
    {
        return compiler.Error("Missing input A");
    }
    else if (!m_B.m_LinkedExpressionOutput)
    {
        return compiler.Error("Missing input B");
    }
    int32_t Arg1 = m_A.Compile(compiler);
    int32_t Arg2 = m_B.Compile(compiler);

    int32_t Delta = compiler.Sub(Arg1, Arg2);
    
    return compiler.Length(Delta);
}

int32_t MaterialExpressionSkyAtmosphereLightDirection::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.SkyAtmosphereLightDirection(m_LightIndex);
}

int32_t MaterialExpressionTilePosition::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.TilePosition();
}

int32_t MaterialExpressionVertexColor::Compile(MaterialCompiler& compiler, ExpressionOutput* output)
{
    return compiler.VertexColor();
}

}   // namespace cross
#pragma warning(pop)   // namespace cross