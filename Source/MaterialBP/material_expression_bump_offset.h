#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionBumpOffset : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "BumpOffset";
    }

public:
    CEProperty(Reflect)
	ExpressionInput m_Coordinate;

    CEProperty(Reflect)
	ExpressionInput m_Height;

    CEProperty(Reflect)
	ExpressionInput m_HeightRatioInput;

    CEProperty(Reflect)
    float m_ReferencePlane = 0.0f;   // Height at which no offset is applied.

    CEProperty(Reflect, meta(OverrideInputProperty = m_Coordinate))
    int32_t m_ConstCoordinate;

    CEProperty(Reflect, meta(OverrideInputProperty = m_HeightRatioInput))
    float m_HeightRatio;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross
