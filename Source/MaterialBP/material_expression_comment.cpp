#include"material_expression_comment.h"

void cross ::MaterialExpressionComment::SetCustomAttrInt2(int x, int y, cross::IMaterialEditor* editor)
{
    if (m_Width != x || m_Height != y)
    {
        m_Width = x;
        m_Height = y;
        editor->SetResourceChanged(true);
    }
}

cross::Float2 cross ::MaterialExpressionComment::GetNodeSize() 
{
    return cross::Float2(static_cast<float>(m_Width), static_cast<float>(m_Height));
}

std::string cross ::MaterialExpressionComment::GetCaption() const
{
    return m_CommentTitle;
}

std::string cross ::MaterialExpressionComment::GetMenuName() const
{
    return "Comment";
}

ImColor cross ::MaterialExpressionComment::GetTitleColor() const
{
    return ImColor(m_Color.x, m_Color.y, m_Color.z, m_Color.w);
}
