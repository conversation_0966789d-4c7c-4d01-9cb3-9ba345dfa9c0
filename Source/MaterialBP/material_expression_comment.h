#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionComment : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override;

    virtual std::string GetMenuName() const override;

    virtual ImColor GetTitleColor() const override;

    virtual Float2 GetNodeSize() override;

    virtual void SetCustomAttrInt2(int x, int y, IMaterialEditor* editor) override;

public:
    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Auto", DisplayName = "CommentTitle", ToolTips = "Type"))
    std::string m_CommentTitle = "Comment";

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "Float4AsColor"))
    cross::Float4 m_Color{1, 1, 1, 1};

    CEProperty(Reflect)
    bool mShowBubbleWhenZoomed = false;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    int m_Width = 400;

    CEProperty(Reflect, EditorPropertyInfo(bHide = true))
    int m_Height = 600;
};
}   // namespace cross
