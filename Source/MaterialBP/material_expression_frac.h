#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionFrac : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Frac";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Input;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross