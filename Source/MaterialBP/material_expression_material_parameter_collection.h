#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionMaterialParameterCollection : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        if (!m_MpcEnum.mSelectedProperty.empty())
        {
            return "Material Parameter Collection\n" + m_MpcEnum.GetSelectElementCopy();
        }
        else
        {
            return "Material Parameter Collection\n";
        }
    }

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const cross::DeserializeNode& node, SerializeContext& context);

    virtual void OnPropertyChange(IMaterialEditor * editor) override;

    void OnFilePathChange(IMaterialEditor * editor)
    {
        m_MpcEnum.OnChangeMpcFilePath(m_MpcFile);
        OnPropertyChange(editor);
    };

    void OnSelectedChange(IMaterialEditor * editor)
    {
        OnPropertyChange(editor);
    }

public:
    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "StringAsResource", FileTypeDescriptor = "Material parameter collection#mpc"))
    std::string m_MpcFile = "Mateirla/DefaultMpc.mpc";

    CEMeta(Reflect)
    ExpressionOutput m_Output;

    CEProperty(Reflect, EditorPropertyInfo(PropertyType = "DynamicEnum"))
    MpcDynamicEnum m_MpcEnum;
};
}   // namespace cross