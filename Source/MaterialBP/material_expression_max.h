#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionMax : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Max";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_A;

    CEProperty(Reflect)
    ExpressionInput m_B;

    CEMeta(Reflect)
    ExpressionOutput m_Result;

    CEProperty(Reflect, meta(OverrideInputProperty = m_A))
    float m_ConstA;

    CEProperty(Reflect, meta(OverrideInputProperty = m_B))
    float m_ConstB;
};
}   // namespace cross