#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionObjectLocalBounds : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler& compiler, ExpressionOutput* output) override;

    virtual std::string GetCaption() const override
    {
        return "ObjectLocalBounds";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_HalfExtent;

    CEMeta(Reflect)
    ExpressionOutput m_FullExtent;

    CEMeta(Reflect)
    ExpressionOutput m_Min;

    CEMeta(Reflect)
    ExpressionOutput m_Max;
};
}   // namespace cross