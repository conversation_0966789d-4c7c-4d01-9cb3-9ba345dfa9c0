#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionObjectPositionWS : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "ObjectPositionWS";
    }

public:
    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross