#include "EnginePrefix.h"
#include "material_expression_particle_data.h"
#include "material_compiler.h"

#define DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(type)                                                                      \
    int32_t cross::MaterialExpressionParticle##type::Compile(MaterialCompiler& compiler, ExpressionOutput* output)          \
    {                                                                                                                       \
        return compiler.Particle##type();                                                                                   \
    }

DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(Color)
DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(UVScale)
DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(AnimatedVelocity)
DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(Position)
DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(Rotation)
DEFINE_MATERIAL_EXPRESSION_PARTICLE_DATA(SizeScale)