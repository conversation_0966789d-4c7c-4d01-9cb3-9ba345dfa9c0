#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionSaturate : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return "Saturate";
    }

public:
    CEProperty(Reflect)
    ExpressionInput m_Value;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross