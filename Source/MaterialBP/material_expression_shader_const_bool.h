#pragma once
#include "material_expression_parameter.h"

namespace cross {
class CEMeta(Cli) Material_API MaterialExpressionShaderConstBool : public MaterialExpressionParameter
{
public:
    CE_Virtual_Serialize_Deserialize;

    std::string GetMenuName() const
    { 
        return "BoolParam";
    }

    int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    bool IsTypeMaching(const MaterialParameter* param) const override { return dynamic_cast<const MaterialParameterBool*>(param); }

    std::shared_ptr<MaterialParameter> ToParameter() const override
    {
        return gbf::reflection::make_shared_with_rtti<MaterialParameterBool>();
    }

    void CopyFrom(const MaterialParameter* src) override
    {
        MaterialExpressionParameter::CopyFrom(src);
        auto _src = dynamic_cast<const MaterialParameterBool*>(src);
        m_Value = _src->Value;
    }

    void CopyTo(MaterialParameter* dst) const override
    {
        MaterialExpressionParameter::CopyTo(dst);
        auto _dst = dynamic_cast<MaterialParameterBool*>(dst);
        _dst->Value = m_Value;
    }

    CEProperty(Reflect)
    bool m_Value = true;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross