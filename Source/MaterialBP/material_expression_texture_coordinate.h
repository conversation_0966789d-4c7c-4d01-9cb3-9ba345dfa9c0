#pragma once

#include "material_expression.h"

namespace cross {
class CEMeta(Cli) MaterialExpressionTextureCoordinate : public MaterialExpression
{
public:
    CE_Virtual_Serialize_Deserialize;

    virtual int32_t Compile(MaterialCompiler & compiler, ExpressionOutput * output) override;

    virtual std::string GetCaption() const override
    {
        return fmt::format("TexCoord[{}]", m_CoordinateIndex);
    }

    virtual std::string GetMenuName() const override
    {
        return "Texture Coordinate";
    }

public:
    CEProperty(Reflect)
    int32_t m_CoordinateIndex;

    CEProperty(Reflect)
    float m_UTiling = 1.0f;

    CEProperty(Reflect)
    float m_VTiling = 1.0f;

    CEMeta(Reflect)
    ExpressionOutput m_Result;
};
}   // namespace cross