#include "material_instance_editor.h"

#include "AssetPipeline/Import/AssetImporterManager.h"
#include "AssetPipeline/Import/ShaderImporter/ShaderImporter.h"
#include "AssetPipeline/Interface/AssetPipeline.h"
#include "CrossBase/String/StringCodec.h"
#include "Resource/AssetStreaming.h"
#include "Resource/resourceasset.h"

#include "reflection/meta/meta_class.hpp"
#include "reflection/meta/user_property.hpp"

namespace cross {
MaterialInstanceEditor::MaterialInstanceEditor(const char* resourceGuid)
{
    m_MaterialGuid = resourceGuid;

    m_Material = TypeCast<resource::Material>(gAssetStreamingManager->LoadSynchronously(m_MaterialGuid));
    FillParameters();
}

void MaterialInstanceEditor::OnFrame() {}

void MaterialInstanceEditor::UpdateMaterialInstanceState(resource::Material* material, const MaterialInstanceDefines& newMaterialInstanceDefines)
{
    auto& materialDefines = material->GetMaterialInstanceDefines();

    switch (material->GetFx()->GetMaterialDefines().Domain)
    {
    case MaterialDomain::Foliage:
    case MaterialDomain::PostProcess:
    case MaterialDomain::Surface:
        SyncForSurfaceDomain(material, newMaterialInstanceDefines);
        if (newMaterialInstanceDefines.BlendModeEnable)
        {
            auto oldDefinesOpaque = materialDefines.BlendMode == MaterialBlendMode::Opaque || materialDefines.BlendMode == MaterialBlendMode::Masked;
            auto newDefinesOpaque = newMaterialInstanceDefines.BlendMode == MaterialBlendMode::Opaque || newMaterialInstanceDefines.BlendMode == MaterialBlendMode::Masked;
            if (oldDefinesOpaque != newDefinesOpaque)
            {
                material->NotifyChangeRecursively();
            }
        }
        break;

    case MaterialDomain::MeshDecal:
        SyncForMeshDecalDomain(material, newMaterialInstanceDefines);
        break;

    default:
        Assert(false);
        break;
    }

    materialDefines = newMaterialInstanceDefines;
}

void MaterialInstanceEditor::SyncForSurfaceDomain(resource::Material* material, const MaterialInstanceDefines& newMaterialInstanceDefines)
{
    if (newMaterialInstanceDefines.BlendModeEnable)
    {
        switch (newMaterialInstanceDefines.BlendMode)
        {
        case MaterialBlendMode::Opaque:
        case MaterialBlendMode::Masked:
            material->SetPassEnable("forward", false);
            material->SetPassEnable("gpass", true);
            material->SetPassEnable("shadow_all", true);
            material->SetPassEnable("VSMDepth", true);
            break;
        case MaterialBlendMode::Translucent:
        case MaterialBlendMode::Additive:
        case MaterialBlendMode::Modulate:
        case MaterialBlendMode::AlphaComposite:
            material->SetPassEnable("forward", true);
            material->SetPassEnable("gpass", false);
            material->SetPassEnable("shadow_all", false);
            material->SetPassEnable("VSMDepth", false);

            material->SetDepthStencilState("forward", {.EnableDepth = true, .DepthCompareOp = NGIComparisonOp::GreaterEqual});
            switch (newMaterialInstanceDefines.BlendMode)
            {
            case MaterialBlendMode::Translucent:
            {
                constexpr static NGITargetBlendStateDesc gTranslucentBlend{
                    true,
                    false,
                    BlendFactor::SrcAlpha,
                    BlendFactor::InvSrcAlpha,
                    BlendOp::Add,
                    BlendFactor::Zero,
                    BlendFactor::Zero,
                    BlendOp::Add,
                    LogicOp::NoOp,
                    ColorMask::All,
                };
                material->SetBlendState("forward", {.TargetCount = 1, .TargetBlendState = {gTranslucentBlend}});
                break;
            }
            case MaterialBlendMode::Additive:
            {
                constexpr static NGITargetBlendStateDesc gAdditiveBlend{
                    true,
                    false,
                    BlendFactor::SrcAlpha,
                    BlendFactor::One,
                    BlendOp::Add,
                    BlendFactor::Zero,
                    BlendFactor::Zero,
                    BlendOp::Add,
                    LogicOp::NoOp,
                    ColorMask::All,
                };
                material->SetBlendState("forward", {.TargetCount = 1, .TargetBlendState = {gAdditiveBlend}});
                break;
            }
            case MaterialBlendMode::Modulate:
            {
                constexpr static NGITargetBlendStateDesc gModulateBlend{
                    true,
                    false,
                    BlendFactor::DestColor,
                    BlendFactor::Zero,
                    BlendOp::Add,
                    BlendFactor::Zero,
                    BlendFactor::Zero,
                    BlendOp::Add,
                    LogicOp::NoOp,
                    ColorMask::All,
                };
                material->SetBlendState("forward", {.TargetCount = 1, .TargetBlendState = {gModulateBlend}});
                break;
            }
            case MaterialBlendMode::AlphaComposite:
            {
                constexpr static NGITargetBlendStateDesc gAlphaCompositeBlend{
                    true,
                    false,
                    BlendFactor::One,
                    BlendFactor::InvSrcAlpha,
                    BlendOp::Add,
                    BlendFactor::Zero,
                    BlendFactor::Zero,
                    BlendOp::Add,
                    LogicOp::NoOp,
                    ColorMask::All,
                };
                material->SetBlendState("forward", {.TargetCount = 1, .TargetBlendState = {gAlphaCompositeBlend}});
                break;
            }
            default:
                break;
            }
            break;
        default:
            break;
        }

        material->SetBool(NAME_ID("ALPHA_CLIPPING"), newMaterialInstanceDefines.BlendMode == MaterialBlendMode::Masked);
        material->SetFloat(NAME_ID("MATERIAL_BLEND_MODE"), static_cast<float>(ToUnderlying(newMaterialInstanceDefines.BlendMode)));
    }

    if (newMaterialInstanceDefines.ShadingModelEnable)
    {
        material->SetFloat("MATERIAL_TYPE", static_cast<float>(ToUnderlying(newMaterialInstanceDefines.ShadingModel)));
    }

    if (newMaterialInstanceDefines.TwoSidedEnable)
    {
        if (newMaterialInstanceDefines.TwoSided)
        {
            constexpr static NGIRasterizationStateDesc gNoCulling{
                .FillMode = FillMode::Solid,
                .CullMode = CullMode::None,
                .FaceOrder = FaceOrder::CW,
                .EnableDepthClip = true,
                .RasterMode = RasterizationMode::DefaultRaster,
            };
            material->SetRasterizerState("VSMDepth", gNoCulling);
            material->SetRasterizerState("shadow_all", gNoCulling);
            material->SetRasterizerState("gpass", gNoCulling);
            material->SetRasterizerState("forward", gNoCulling);
            material->SetBool(NAME_ID("DOUBLE_SIDED"), true);
            material->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{-1.0f, -1.0f, -1.0f, -1.0f}.data());
        }
        else
        {
            constexpr static NGIRasterizationStateDesc gBackFaceCulling{
                .FillMode = FillMode::Solid,
                .CullMode = CullMode::Back,
                .FaceOrder = FaceOrder::CW,
                .EnableDepthClip = true,
                .RasterMode = RasterizationMode::DefaultRaster,
            };
            material->SetRasterizerState("VSMDepth", gBackFaceCulling);
            material->SetRasterizerState("shadow_all", gBackFaceCulling);
            material->SetRasterizerState("gpass", gBackFaceCulling);
            material->SetRasterizerState("forward", gBackFaceCulling);
            material->SetBool(NAME_ID("DOUBLE_SIDED"), false);
            material->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{0.0, 0.0, 0.0f, 0.0f}.data());
        }
    }
}

void MaterialInstanceEditor::SyncForMeshDecalDomain(resource::Material* material, const MaterialInstanceDefines& materialInstanceDefines)
{
    //constexpr static NGITargetBlendStateDesc gMeshDecalColorAndNormalBlend{
    //    true,
    //    false,
    //    BlendFactor::SrcAlpha,
    //    BlendFactor::InvSrcAlpha,
    //    BlendOp::Add,
    //    BlendFactor::Zero,
    //    BlendFactor::InvSrcAlpha,
    //    BlendOp::Add,
    //    LogicOp::NoOp,
    //    ColorMask::All,
    //};
    //constexpr static NGITargetBlendStateDesc gMeshDecalMSRBlend{
    //    true,
    //    false,
    //    BlendFactor::Zero,
    //    BlendFactor::One,
    //    BlendOp::Add,
    //    BlendFactor::Zero,
    //    BlendFactor::One,
    //    BlendOp::Add,
    //    LogicOp::NoOp,
    //    ColorMask::All,
    //};
    //constexpr static NGITargetBlendStateDesc gMeshDecalSceneColorBlend{
    //    true,
    //    false,
    //    BlendFactor::SrcAlpha,
    //    BlendFactor::One,
    //    BlendOp::Add,
    //    BlendFactor::One,
    //    BlendFactor::Zero,
    //    BlendOp::Add,
    //    LogicOp::NoOp,
    //    ColorMask::All,
    //};
    //material->SetBlendState("decal_gpass", {.TargetCount = 4, .TargetBlendState = {gMeshDecalColorAndNormalBlend, gMeshDecalColorAndNormalBlend, gMeshDecalMSRBlend, gMeshDecalSceneColorBlend}});

    //constexpr static NGIStencilOperation gMeshDecalOp{
    //    StencilOp::Keep,
    //    StencilOp::Keep,
    //    StencilOp::Replace,
    //    NGIComparisonOp::Always,
    //};
    //constexpr static NGIDepthStencilStateDesc gMeshDecalDepthStencilState{
    //    true,
    //    false,
    //    NGIComparisonOp::GreaterEqual,
    //    true,
    //    0x20,
    //    0x20,
    //    gMeshDecalOp,
    //    gMeshDecalOp,
    //};
    //material->SetDepthStencilState("decal_gpass", gMeshDecalDepthStencilState);

    if (materialInstanceDefines.TwoSidedEnable)
    {
        if (materialInstanceDefines.TwoSided)
        {
            constexpr static NGIRasterizationStateDesc gMeshDecalNoCulling{
                .FillMode = FillMode::Solid,
                .CullMode = CullMode::None,
                .FaceOrder = FaceOrder::CW,
                .EnableDepthClip = true,
                .RasterMode = RasterizationMode::DefaultRaster,
            };
            material->SetRasterizerState("decal_gpass", gMeshDecalNoCulling);
            material->SetBool(NAME_ID("DOUBLE_SIDED"), true);
            material->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{-1.0f, -1.0f, -1.0f, -1.0f}.data());
        }
        else
        {
            constexpr static NGIRasterizationStateDesc gMeshDecalBackFaceCulling{
                .FillMode = FillMode::Solid,
                .CullMode = CullMode::Back,
                .FaceOrder = FaceOrder::CW,
                .EnableDepthClip = true,
                .RasterMode = RasterizationMode::DefaultRaster,
            };
            material->SetRasterizerState("decal_gpass", gMeshDecalBackFaceCulling);
            material->SetBool(NAME_ID("DOUBLE_SIDED"), false);
            material->SetFloat4(NAME_ID("_DoubleSidedConstants"), std::vector<float>{0.0, 0.0, 0.0f, 0.0f}.data());
        }
    }

    material->SetBool(NAME_ID("ALPHA_CLIPPING"), false);
    material->SetInt(NAME_ID("MATERIAL_TYPE"), ToUnderlying(MaterialShadingModel::Standard));
    material->SetInt(NAME_ID("MATERIAL_BLEND_MODE"), ToUnderlying(materialInstanceDefines.BlendMode));
    material->SetBool(NAME_ID("DISABLE_FORWARD_SSR"), true);

    material->SetBool(NAME_ID("ForceDisableExponentialFog"), true);
    material->SetBool(NAME_ID("ForceDisableVolumetricFog"), true);
    material->SetBool(NAME_ID("ForceDisableCloudFog"), true);
    material->SetBool(NAME_ID("CE_ENABLE_SEPERATE_TRANSLUCENCY"), false);
}

void MaterialInstanceEditor::OnPropertyChange()
{
    m_Material->ClearAllProperties();
    std::string newParentMaterialPath = mMaterialInstanceDefines.ParentMaterial;
    if (m_Material->GetMaterialInstanceDefines().ParentMaterial != newParentMaterialPath) // Modify Material Parent
    {
        if (m_Material->SetParentAndSave(newParentMaterialPath)) // Legal Parent Materials
        {
            SetParentModifiedStatus(true);
            OnParentChange();
        }
    }
    else
    {
        UpdateMaterialInstanceState(m_Material.get(), mMaterialInstanceDefines);
        for (auto& parameterGroup : m_Material->GetMaterialInstanceDefines().ParameterGroups)
        {
            for (auto* parameter : parameterGroup.Parameters)
            {
                if (parameter->Enable)
                {
                    SetPropertyFromParameter(parameter->ParameterName, parameter);
                }
            }
        }
    }

    m_Material->Serialize(SerializeNode(), m_Material->GetName());
}

void MaterialInstanceEditor::OnParentChange()
{
    auto m_oldParameters = m_Parameters;
    FillParameters();   // Update m_Parameters
    for (auto& parameters : m_Parameters)
    {
        for (const auto& oldParameters : m_oldParameters)
        {
            if (oldParameters->Enable && oldParameters->DisplayName == parameters->DisplayName && typeid(*oldParameters) == typeid(*parameters))
            {
                SetPropertyFromParameter(parameters.get()->ParameterName, oldParameters.get());
                parameters->Enable = true;
            }
        }
    }
    FillParameters();   // Update Parameter Groups By m_Parameters
}

void MaterialInstanceEditor::SetPropertyFromParameter(std::string name, MaterialParameter* parameter)
{
    if (auto* boolParameter = dynamic_cast<MaterialParameterBool*>(parameter))
    {
        m_Material->SetBool(name, boolParameter->Value);
    }
    else if (auto* scalarParameter = dynamic_cast<MaterialParameterScalar*>(parameter))
    {
        m_Material->SetFloat(name, scalarParameter->Value);
    }
    else if (auto* vectorParameter = dynamic_cast<MaterialParameterVector*>(parameter))
    {
        m_Material->SetFloat4(name, vectorParameter->Value.data());
    }
    else if (auto* textureParameter = dynamic_cast<MaterialParameterTexture*>(parameter))
    {
        TexturePtr texPtr{TYPE_CAST(resource::Texture*, gAssetStreamingManager->LoadSynchronously(textureParameter->Value).get())};
        Assert(texPtr);
        m_Material->SetTexture(name, texPtr);
    }
    else if (auto* textureParameter2 = dynamic_cast<MaterialParameterTextureVirtual*>(parameter))
    {
        TexturePtr texPtr{TYPE_CAST(resource::Texture*, gAssetStreamingManager->LoadSynchronously(textureParameter2->Value).get())};
        Assert(texPtr);
        m_Material->SetTexture(name, texPtr);
    }
    else
    {
        Assert(false);
    }
}

bool MaterialInstanceEditor::OnMaterialChange(const std::string& materialGuid)
{
    if (m_Material->GetFx()->GetGuid_Str() == materialGuid)
    {
        FillParameters();
        return true;
    }

    return false;
}

bool MaterialInstanceEditor::IsDependent(const std::string& materialGuid)
{
    return m_Material->IsDependent(materialGuid);
}

void MaterialInstanceEditor::FillParameters()
{
    auto& defines = m_Material->GetMaterialInstanceDefines();

    defines.ParameterGroups.clear();
    m_Parameters.clear();

    // help functions
    auto GetOrAddParameterGroup = [&](const std::string groupName) {
        for (auto& parameterGroup : defines.ParameterGroups)
        {
            if (parameterGroup.Name == groupName)
            {
                return &parameterGroup;
            }
        }

        MaterialParameterGroup group;
        group.Name = groupName;
        defines.ParameterGroups.push_back(group);
        return &defines.ParameterGroups.back();
    };

    auto IsParameterExist = [&](const std::string& name) {
        for (auto& parameter : m_Parameters)
        {
            if (parameter->ParameterName == name)
            {
                return true;
            }
        }

        return false;
    };

    auto AddScalarParameter = [&](const std::string& groupName, const std::string& parameterName, float valueMin, float valueMax, const std::string& displayName, float sortPriority) {
        if (!IsParameterExist(parameterName))
        {
            auto parameter = gbf::reflection::make_shared_with_rtti<MaterialParameterScalar>();
            parameter->ParameterName = parameterName;
            parameter->DisplayName = displayName;
            parameter->SliderMin = valueMin;
            parameter->SliderMax = valueMax;
            parameter->Enable = m_Material->IsPropertyOverride(parameterName);
            if (auto prop = m_Material->GetProperty(parameterName))
            {
                parameter->Value = std::get<std::vector<float>>(*prop)[0];
            }
            parameter->SortPriority = sortPriority;

            m_Parameters.push_back(std::static_pointer_cast<MaterialParameter>(parameter));
            GetOrAddParameterGroup(groupName)->Parameters.push_back(parameter.get());
        }
    };

    auto AddVectorParameter = [&](const std::string& groupName, const std::string& parameterName, const std::string& displayName, float sortPriority) {
        if (!IsParameterExist(parameterName))
        {
            auto parameter = gbf::reflection::make_shared_with_rtti<MaterialParameterVector>();
            parameter->ParameterName = parameterName;
            parameter->DisplayName = displayName;
            parameter->Enable = m_Material->IsPropertyOverride(parameterName);
            if (auto prop = m_Material->GetProperty(parameterName))
            {
                parameter->Value = Float4(std::get<std::vector<float>>(*prop).data());
            }
            parameter->SortPriority = sortPriority;

            m_Parameters.push_back(std::static_pointer_cast<MaterialParameter>(parameter));
            GetOrAddParameterGroup(groupName)->Parameters.push_back(parameter.get());
        }
    };

    auto AddTextureParameter = [&](const std::string& groupName, const std::string& parameterName, const std::string& displayName, float sortPriority) {
        if (!IsParameterExist(parameterName))
        {
            bool isVirtual = parameterName.find("_VT_") != std::string::npos;
            if (isVirtual)
            {
                auto parameter = gbf::reflection::make_shared_with_rtti<MaterialParameterTextureVirtual>();
                parameter->ParameterName = parameterName;
                parameter->DisplayName = displayName;
                parameter->Enable = m_Material->IsPropertyOverride(parameterName);
                if (auto prop = m_Material->GetProperty(parameterName))
                {
                    parameter->Value = std::get<TexturePtr>(*prop)->GetGuid_Str();
                }
                parameter->SortPriority = sortPriority;
                m_Parameters.push_back(std::static_pointer_cast<MaterialParameter>(parameter));
                GetOrAddParameterGroup(groupName)->Parameters.push_back(parameter.get());
            }
            else
            {
                auto parameter = gbf::reflection::make_shared_with_rtti<MaterialParameterTexture>();
                parameter->ParameterName = parameterName;
                parameter->DisplayName = displayName;
                parameter->Enable = m_Material->IsPropertyOverride(parameterName);
                if (auto prop = m_Material->GetProperty(parameterName))
                {
                    parameter->Value = std::get<TexturePtr>(*prop)->GetGuid_Str();
                }
                parameter->SortPriority = sortPriority;
                m_Parameters.push_back(std::static_pointer_cast<MaterialParameter>(parameter));
                GetOrAddParameterGroup(groupName)->Parameters.push_back(parameter.get());
            }
        }
    };

    auto AddShaderConstBool = [&](const std::string& groupName, const std::string& name, std::string displayName, float sortPriority) {
        if (!IsParameterExist(name))
        {
            auto parameter = gbf::reflection::make_shared_with_rtti<MaterialParameterBool>();
            parameter->ParameterName = name;
            parameter->DisplayName = displayName;
            parameter->Enable = m_Material->IsPropertyOverride(name);
            if (auto prop = m_Material->GetProperty(name))
            {
                parameter->Value = std::get<bool>(*prop);
            }
            parameter->SortPriority = sortPriority;

            m_Parameters.push_back(std::static_pointer_cast<MaterialParameter>(parameter));
            GetOrAddParameterGroup(groupName)->Parameters.push_back(parameter.get());
        }
    };

    //////////////////////////////////////////////////////////////////////////

    auto fx = m_Material->GetFx();

    if (const auto& expressionStr = fx->GetExpressionsString(); !expressionStr.empty())
    {
        const auto& paramInfos = fx->GetParameterInfos();

        if (paramInfos.mScalarParams.empty() && paramInfos.mVectorParams.empty() && paramInfos.mTextureParams.empty() && paramInfos.mBoolParams.empty())
        {
            // deprecated!!!

            auto node = DeserializeNode::ParseFromJson(expressionStr);
            assert(node.IsArray());

            for (int i = 0; i < node.Size(); i++)
            {
                auto expressionNode = node.At(i);

                bool isVisible = expressionNode.HasMember("m_IsVisibleInMaterialInstanceEditor") ? expressionNode["m_IsVisibleInMaterialInstanceEditor"].AsBoolean() : true;
                if (!isVisible)
                {
                    continue;
                }

                std::string className = expressionNode["Class"].AsString();
                std::string groupName = expressionNode.HasMember("m_Group") ? expressionNode["m_Group"].AsString() : "";
                std::string parameterName = expressionNode.HasMember("m_ParameterName") ? expressionNode["m_ParameterName"].AsString() : "";
                std::string displayName = expressionNode.HasMember("m_Name") ? expressionNode["m_Name"].AsString() : "";
                float sortPriority = expressionNode.HasMember("m_SortPriority") ? expressionNode["m_SortPriority"].AsFloat() : 1.0f;

                if (parameterName.empty())
                {
                    parameterName = displayName;
                }

                if (groupName == "")
                {
                    groupName = "Default";
                }

                if (className == "cross::MaterialExpressionScalarParameter")
                {
                    float valueMin = expressionNode.HasMember("m_SliderMin") ? expressionNode["m_SliderMin"].AsFloat() : 0.0f;
                    float valueMax = expressionNode.HasMember("m_SliderMax") ? expressionNode["m_SliderMax"].AsFloat() : 0.0f;

                    AddScalarParameter(groupName, parameterName, valueMin, valueMax, displayName, sortPriority);
                }
                else if (className == "cross::MaterialExpressionVectorParameter")
                {
                    AddVectorParameter(groupName, parameterName, displayName, sortPriority);
                }
                else if (className == "cross::MaterialExpressionTextureParameter" || className == "cross::MaterialExpressionTextureSampleParameter")
                {
                    std::string vtLayerName = expressionNode.HasMember("m_VirtualTextureLayer") ? expressionNode["m_VirtualTextureLayer"].AsString() : "";
                    AddTextureParameter(groupName, parameterName + vtLayerName, displayName + vtLayerName, sortPriority);
                }
                else if (className == "cross::MaterialExpressionShaderConstBool")
                {
                    AddShaderConstBool("Shader Consts", parameterName, displayName, sortPriority);
                }
                else if (className == "cross::MaterialExpressionShaderConstFloat")
                {
                    Assert(false);
                }
            }
        }
        else
        {
            for (auto& [parameterName, param] : paramInfos.mScalarParams)
            {
                AddScalarParameter(param.GroupName, param.ParameterName, param.SliderMin, param.SliderMax, param.DisplayName, param.SortPriority);
            }

            for (auto& [parameterName, param] : paramInfos.mVectorParams)
            {
                AddVectorParameter(param.GroupName, param.ParameterName, param.DisplayName, param.SortPriority);
            }

            for (auto& [parameterName, param] : paramInfos.mBoolParams)
            {
                AddShaderConstBool(param.GroupName, param.ParameterName, param.DisplayName, param.SortPriority);
            }

            for (auto& [parameterName, param] : paramInfos.mTextureParams)
            {
                AddTextureParameter(param.GroupName, param.ParameterName, param.DisplayName, param.SortPriority);
            }
        }
    }

    mMaterialInstanceDefines = m_Material->GetMaterialInstanceDefines();

    for (auto& parameterGroup : mMaterialInstanceDefines.ParameterGroups)
    {
        std::sort(parameterGroup.Parameters.begin(), parameterGroup.Parameters.end(), [](MaterialParameter const* a, MaterialParameter const* b) -> bool {
            return a->SortPriority != b->SortPriority ? a->SortPriority < b->SortPriority : a->DisplayName < b->DisplayName;
        });
    }
}
}   // namespace cross