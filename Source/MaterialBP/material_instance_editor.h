#pragma once

#include "Resource/Resource.h"
#include "MaterialBP/MaterialFoward.h"
#include "Resource/Material.h"
#include "Resource/Texture/Texture.h"

namespace cross::resource {
class Fx;
}

namespace cross {
class Material_API CEMeta(Cli) MaterialInstanceEditor
{
public:
    MaterialInstanceEditor(const char* resourceGuid);

    virtual ~MaterialInstanceEditor() {}

    CEMeta(Cli)
    void OnFrame();

    auto GetMaterialInstance()
    {
        return m_Material;
    }

    CEMeta(Cli)
    MaterialInstanceDefines& GetMaterialInstanceDefines()
    {
        return mMaterialInstanceDefines;
    }

    CEMeta(Cli)
    void OnPropertyChange();

    CEMeta(Cli)
    bool OnMaterialChange(const std::string& materialGuid);

    CEMeta(Cli)
    bool IsDependent(const std::string& materialGuid);

    CEMeta(Cli)
    void OnParentChange();

    CEMeta(Cli)
    bool IsParentChanged() { return mParentModified; }

    CEMeta(Cli)
    void SetParentModifiedStatus(const bool value) { mParentModified = value; }

public:
    static void UpdateMaterialInstanceState(resource::Material * material, const MaterialInstanceDefines& newMaterialInstanceDefines);
    static void SyncForSurfaceDomain(resource::Material * material, const MaterialInstanceDefines& newMaterialInstanceDefines);
    static void SyncForMeshDecalDomain(resource::Material * material, const MaterialInstanceDefines& newMaterialInstanceDefines);

    void FillParameters();

    void SetPropertyFromParameter(std::string name, MaterialParameter * parameter);

private:
    // MaterialInstance(Material)
    MaterialPtr m_Material;
    MaterialInstanceDefines mMaterialInstanceDefines;
    std::string m_MaterialGuid;

    // Parameters
    std::vector<std::shared_ptr<MaterialParameter>> m_Parameters;
    bool mParentModified{false};
};
}   // namespace cross