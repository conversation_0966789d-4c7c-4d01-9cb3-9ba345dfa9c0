#pragma once

#include "Resource/MaterialParameterCollection.h"
#include "MaterialBP/MaterialFoward.h"
namespace cross {

using MpcProperty = std::pair<std::string, std::variant<float, Float4>>;

class Material_API CEMeta(Cli) MpcDynamicEnum : public DynamicEnum
{
public:
    MpcDynamicEnum();

    CEFunction(Cli)
    void OnChangeMpcFilePath(const std::string& mpcFilePath);

    MpcProperty GetSelectedProperty() const;

    std::string GetOriginalMpcFilePath() const;

    inline static const std::string PropertyNone = "None";
    inline static const std::string ScalerPrefix = "[Scaler] ";
    inline static const std::string VectorPrefix = "[Vector] ";

private:
    void InitElements() override;

    std::string mMpcFilePath = "Material/DefaultMpc.mpc";

    MPCPtr mMpcPtr;
};

}   // namespace cross