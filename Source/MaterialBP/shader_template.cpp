#include "shader_template.h"
#include <assert.h>
#include <fstream>
#include <sstream>
#include "CrossBase/FileSystem/PathHelper.h"

namespace cross {
std::string ShaderTemplate::m_TemplateDirectory;

void ShaderTemplate::PushCode(std::string_view codePosition, std::string_view code)
{
    m_CodeMap[std::string(codePosition)] = {true, std::string(code)};
}

void ShaderTemplate::EnableCode(std::string_view codePosition)
{
    assert(m_CodeMap.count(std::string(codePosition)));
    m_CodeMap[std::string(codePosition)].first = true;
}

void ShaderTemplate::DisableCode(std::string_view codePosition)
{
    assert(m_CodeMap.count(std::string(codePosition)));
    m_CodeMap[std::string(codePosition)].first = false;
}

void ShaderTemplate::LoadTemplateContent(std::string_view templateFileName)
{
    InitTemplateDirectory();

    std::string surfaceShaderTemplatePath = m_TemplateDirectory + std::string(templateFileName);
    std::ifstream ifs(surfaceShaderTemplatePath);
    if (ifs.is_open())
    {
        m_TemplateFileContent = std::string(std::istreambuf_iterator<char>(ifs), std::istreambuf_iterator<char>());
        ifs.close();
    }
}

std::string ShaderTemplate::OutputGeneratedCode(std::string_view templateFileName)
{
    LoadTemplateContent(templateFileName);

    std::stringstream ss;

    std::string formatBlank;
    int currPos = 0;
    while (currPos < m_TemplateFileContent.size())
    {
        char c = m_TemplateFileContent[currPos];

        if ((currPos + 2) < m_TemplateFileContent.size() && m_TemplateFileContent.substr(currPos, 3) == "{--")
        {
            currPos += 3;
            int endPos = currPos;

            while ((endPos + 2) < m_TemplateFileContent.size() && m_TemplateFileContent.substr(endPos, 3) != "--}")
            {
                endPos++;
            }

            assert(endPos + 2 < m_TemplateFileContent.size());

            std::string codePositionStr = m_TemplateFileContent.substr(currPos, endPos - currPos);
            assert(m_CodeMap.find(codePositionStr) != m_CodeMap.end());

            // insert formatted code
            const auto& codeToPush = m_CodeMap[codePositionStr].first ? m_CodeMap[codePositionStr].second : "";

            for (int i = 0; i < codeToPush.size(); i++)
            {
                if (i > 0 && codeToPush[i - 1] == '\n')
                {
                    ss << formatBlank;
                }
                ss << codeToPush[i];
            }

            currPos = endPos + 3;
        }
        else
        {
            ss << c;
            currPos++;
        }

        if (c == ' ' || c == '\t')
        {
            formatBlank += c;
        }
        else
        {
            formatBlank.clear();
        }
    }

    return ss.str();
}

void ShaderTemplate::InitTemplateDirectory()
{
    if (m_TemplateDirectory.empty())
    {
        m_TemplateDirectory = PathHelper::GetEngineResourceDirectoryPath() + "\\PipelineResource\\FFSRP\\";
    }
}
}   // namespace cross
