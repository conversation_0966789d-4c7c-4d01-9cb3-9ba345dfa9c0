#pragma once

#include "MaterialBP/MaterialFoward.h"
#include <string>
#include <unordered_map>
namespace cross {
class ShaderTemplate
{
public:
    void PushCode(std::string_view codePosition, std::string_view code);

    void EnableCode(std::string_view codePosition);
    void DisableCode(std::string_view codePosition);

    std::string OutputGeneratedCode(std::string_view templateFileName);

private:
    void LoadTemplateContent(std::string_view templateFileName);

private:
    std::string m_TemplateFileContent;
    std::unordered_map<std::string, std::pair<bool, std::string>> m_CodeMap;

    static std::string m_TemplateDirectory;

    static void InitTemplateDirectory();
};
}   // namespace cross