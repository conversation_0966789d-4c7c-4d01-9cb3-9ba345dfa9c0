#include "EnginePrefix.h"
#include "GLES3.h"
#include "GLES3SwapChain.h"

namespace
{
#if defined(GL_ARB_debug_output)
	void DebugMessageCallback(GLenum source,
		GLenum type,
		GLuint id,
		GLenum severity,
		GLsizei length,
		const GLchar* message,
		const void* userParam)
	{
		LOG_ERROR("GL CALLBACK: {} type = {}, severity = {}, message = {}",
			(type == GL_DEBUG_TYPE_ERROR ? "** GL ERROR **" : ""),
			type, severity, message);
	}
#endif
}

cross::OpenGLES* cross::OpenGLES::Instance()
{
	static OpenGLES instance;
	return &instance;
}

bool cross::OpenGLES::InitGL()
{
	bool result = false;
#if CROSSENGINE_WIN
	result = InitGLESOnWindows();
	#if defined(GL_ARB_debug_output)
		GL_CALL(glEnable(GL_DEBUG_OUTPUT_SYNCHRONOUS));//GL_DEBUG_OUTPUT_SYNCHRONOUS
		GL_CALL(glDebugMessageCallback(DebugMessageCallback, nullptr));
	#endif
#elif CROSSENGINE_ANDROID || CROSSENGINE_OSX
	result = InitGLESFakeContext();
#else
	Assert(false);
	throw std::logic_error("OpenGL NGI is not surpported on this platform");
#endif
    ReadCapability();

#if CROSSENGINE_ANDROID || CROSSENGINE_OSX
    FinishGLESFakeContext();
#endif

	LOG_INFO("New OpenGL ES rhi running.");
	return result;
}

void cross::OpenGLES::CleanUp()
{
    //dango TODO: remember to call cleanup somewhere when shutting down
#if CROSSENGINE_WIN
	SAFE_DELETE(mFakeSwapChain);
	DestroyWindow(mFakeWnd);
	mFakeWnd = NULL;
#endif

}

void cross::OpenGLES::UnbindFakeContext()
{
#if CROSSENGINE_WIN
	mFakeSwapChain->UnbindContext();
#endif
}

void cross::OpenGLES::BindFakeContext()
{
#if CROSSENGINE_WIN
	mFakeSwapChain->BindContext();
#endif
}

void cross::OpenGLES::ReadCapability()
{
    GL_CALL(glGetIntegerv(GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT, &mUniformAlign));
    GL_CALL(glGetIntegerv(GL_MAX_UNIFORM_BLOCK_SIZE, &mMaxUniBlockSize));
	Assert(mUniformAlign && mMaxUniBlockSize);

	const char* glesVersion = (const char*)glGetString(GL_VERSION);
	LOG_INFO("Opengl es version is {}\n", glesVersion);

	const char* glslVersion = (const char*)glGetString(GL_SHADING_LANGUAGE_VERSION);
	LOG_INFO("GLSL version is {}\n", glslVersion);

    const std::string_view extentions = reinterpret_cast<const char*>(glGetString(GL_EXTENSIONS));
	LOG_INFO("GL_EXTENSIONS={}", extentions.data());

	if (extentions.find("GL_KHR_texture_compression_astc_ldr") != std::string_view::npos)
		mSupportASTCLdr = true;

	if (extentions.find("GL_KHR_texture_compression_astc_hdr") != std::string_view::npos)
		mSupportASTCHdr = true;

	if (extentions.find("GL_ARB_framebuffer_sRGB") != std::string_view::npos)
		mSupportFrameBufferSRGB = true;

	if (extentions.find("GL_EXT_texture_sRGB") != std::string_view::npos)
		mSupportTextureSRGB = true;

    if (extentions.find("GL_EXT_texture_cube_map_array") != std::string_view::npos)
        mSupportTextureCubeArray = true;
}

#if CROSSENGINE_WIN
bool cross::OpenGLES::InitGLESOnWindows()
{
    //Windows need to create a fake window to get hdc
	const TCHAR* windowClassName = TEXT("DummyGLWindow");
	const char* title = TEXT("DummyGLWindow");

	WNDCLASSEX wcex;
	wcex.cbSize = sizeof(WNDCLASSEX);
	wcex.style = CS_HREDRAW | CS_VREDRAW;
	wcex.lpfnWndProc = DefWindowProc;
	wcex.cbClsExtra = 0;
	wcex.cbWndExtra = 0;
	wcex.hInstance = NULL;
	wcex.hIcon = nullptr;
	wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
	wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
	wcex.lpszMenuName = nullptr;
	wcex.lpszClassName = windowClassName;
	wcex.hIconSm = nullptr;
	RegisterClassEx(&wcex);

	HWND hWnd = CreateWindowA(windowClassName, title, WS_OVERLAPPEDWINDOW,
		CW_USEDEFAULT, 0, 1, 1, nullptr, nullptr, nullptr, nullptr);

    const NGISwapchainDesc swapchainDesc
	{
		hWnd,
		1,
		1,
		GraphicsFormat::R8G8B8A8_UNorm,
		1,
	};
	mFakeSwapChain = new GLES3Swapchain(swapchainDesc, nullptr);
	return true;
}
#endif

#if CROSSENGINE_ANDROID || CROSSENGINE_OSX
//Create fake surface for capability
bool cross::OpenGLES::InitGLESFakeContext()
{
    mFakeDispaly = EGL_CALL(eglGetDisplay(EGL_DEFAULT_DISPLAY));
    AssertMsg(mFakeDispaly != EGL_NO_DISPLAY, "err: eglGetDisplay Failed!!");

    // Initialize EGL
    {
        EGLint majorVersion;
        EGLint minorVersion;
        EGLBoolean result = EGL_CALL(eglInitialize(mFakeDispaly, &majorVersion, &minorVersion));
        AssertMsg(result, "err: eglInitialize Failed!!");
    }

    //Choose config
    EGLConfig config;
    {
        EGLint numConfigs = 0;
        EGLint attribList[] =
            {
                EGL_RED_SIZE,       8,
                EGL_GREEN_SIZE,     8,
                EGL_BLUE_SIZE,      8,
                EGL_ALPHA_SIZE,     8,
                EGL_SAMPLE_BUFFERS, 0,
                // if EGL_KHR_create_context extension is supported, then we will use
                // EGL_OPENGL_ES3_BIT_KHR instead of EGL_OPENGL_ES2_BIT in the attribute list
                EGL_RENDERABLE_TYPE, EGL_OPENGL_ES3_BIT_KHR,
                EGL_NONE
            };

        EGLBoolean result = EGL_CALL(eglChooseConfig(mFakeDispaly, attribList, &config, 1, &numConfigs));
        Assert(result);
        Assert(numConfigs > 0);
    }

    //create context
    EGLint contextAttribs[] = { EGL_CONTEXT_CLIENT_VERSION, 3, EGL_NONE };
    mFakeContext = EGL_CALL(eglCreateContext(mFakeDispaly, config, nullptr, contextAttribs));
    AssertMsg(EGL_NO_CONTEXT != mFakeContext, "err: eglCreateContext Failed!!");

    //make current
    EGLBoolean result = EGL_CALL(eglMakeCurrent(mFakeDispaly, EGL_NO_SURFACE, EGL_NO_SURFACE, mFakeContext));
    Assert(result);
	return true;
}

void cross::OpenGLES::FinishGLESFakeContext()
{
    EGL_CALL(eglDestroyContext(mFakeDispaly, mFakeContext));
    mFakeContext = nullptr;

    EGL_CALL(eglTerminate(mFakeDispaly));
    mFakeDispaly = nullptr;
}


#endif
