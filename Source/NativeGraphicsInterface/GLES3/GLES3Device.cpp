#include "EnginePrefix.h"
#include "GLES3.h"
#include "GLES3Device.h"

cross::GLES3Device::GLES3Device()
{
	OpenGLES::Instance()->InitGL();
}

cross::NGIFormatCapability cross::GLES3Device::GetFormatCapability(GraphicsFormat format)
{
	GLESTexParam param(format, NGITextureType::Texture2D);
	NGIFormatCapability ret{};
    //dango gl TODO: buffer usage is not sure
	ret.BufferCapability |= NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::VertexBuffer | NGIBufferUsage::IndexBuffer | NGIBufferUsage::IndirectBuffer;

	ret.TextureCapability = NGITextureUsage::CopyDst | NGITextureUsage::CopyDst;
    //SRGB, integer, 32bit integer, unorm and snorm
    if(EnumHasAnyFlags(format, GraphicsFormat::CombinationBit))
    {
		if (EnumHasAnyFlags(format, GraphicsFormat::CompTypeSRGB))
		{
			if (OpenGLES::Instance()->SupportFrameBufferSRGB() && param.isColorRenderable)
				ret.TextureCapability |= NGITextureUsage::RenderTarget;
			if (OpenGLES::Instance()->SupportTextureSRGB() && param.isTextureFilteable)
				ret.TextureCapability |= NGITextureUsage::ShaderResource;
		}
		else
		{
			if (param.isColorRenderable)
				ret.TextureCapability |= NGITextureUsage::RenderTarget;
			if (param.isTextureFilteable)
				ret.TextureCapability |= NGITextureUsage::ShaderResource;
		}
    }
    if(format >= GraphicsFormat::ASTCFirst && format <= GraphicsFormat::ASTCLast)
    {
		if (OpenGLES::Instance()->SupportASTCLdr())
		{
			ret.TextureCapability |= NGITextureUsage::ShaderResource;
		}
    }
	if (format >= GraphicsFormat::ASTCHDRFirst && format <= GraphicsFormat::ASTCHDRLast)
	{
		if (OpenGLES::Instance()->SupportASTCHdr())
		{
			ret.TextureCapability |= NGITextureUsage::ShaderResource;
		}
	}
    if(format >= GraphicsFormat::DepthStencilFirst && format <= GraphicsFormat::DepthStencilLast)
    {
		ret.TextureCapability |= NGITextureUsage::DepthStencil;
    }

	return ret;
}

SizeType cross::GLES3Device::GetTextureDataPlacementAlignment(GraphicsFormat format)
{

	return 4;
}

SizeType cross::GLES3Device::GetTextureDataPitchAlignment(GraphicsFormat format)
{
	return 4;
}
