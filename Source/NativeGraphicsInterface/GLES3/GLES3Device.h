#pragma once
#include "GLES3Include.h"

#include "NativeGraphicsInterface/NGI.h"

#include "NativeGraphicsInterface/GLES3/GLES3Object.h"
#include "NativeGraphicsInterface/GLES3/GLES3SwapChain.h"
#include "NativeGraphicsInterface/GLES3/GLES3Resource.h"

namespace cross {

struct GLES3Device : public NGIDevice
{
    GLES3Device();

    ~GLES3Device() override = default;

    NGIPlatform GetPlatform() override
    {
        return NGIPlatform::OpenGLES3;
    }

    bool NGISupportsMultithreading() override
    {
        return true;
    }

    bool IsSupportMSAA(NGIResolveType depthResolveType , NGIResolveType stencilResolveType) override
    {
#if CROSSENGINE_ANDROID
        return false;
#else
        return true;
#endif
    }

    void WaitIdle() override {}

    NGIFormatCapability GetFormatCapability(GraphicsFormat format) override;

    NGISwapchain* CreateSwapchain(const NGISwapchainDesc& desc) override
    {
        return new GLES3Swapchain{desc, this};
    }

    NGICommandQueue* CreateCommandQueue(const NGICommandQueueDesc& desc) override
    {
        return new GLES3CommandQueue{desc, this};
    }

    NGIFence* CreateFence(UInt64 initialValue) override { return new GLES3Fence{initialValue, this}; }

    NGIFence* CreateTimeLineFence(UInt64 initialValue) override
    {
        return nullptr;
    }

    NGIRenderPass* CreateRenderPass(const NGIRenderPassDesc& desc) override
    {
        return new GLES3RenderPass{desc, this};
    }

    NGIFramebuffer* CreateFramebuffer(const NGIFramebufferDesc& desc) override
    {
        return new GLES3Framebuffer{desc, this};
    }

    NGIPipelineLayout* CreatePipelineLayout(const NGIPipelineLayoutDesc& desc) override
    {
        return new GLES3PipelineLayout{desc, this};
    }

    NGIPipelineStatePool* CreatePipelineStatePool(const NGIPipelineStatePoolDesc& desc) override
    {
        return new GLES3PipelineStatePool{desc, this};
    }

    std::vector<std::tuple<GPUProfiling::GPUProfilingContextInfo, std::vector<GPUProfiling::GPUProfilingItem>>> PopGpuProfileItems() override { return {}; }

    NGIResourceGroupPool* CreateResourceGroupPool(const NGIResourceGroupPoolDesc& desc) override
    {
        return new GLES3ResourceGroupPool{desc, this};
    }

    // resource
    NGIBuffer* CreateBufferImp(const NGIBufferDesc& desc, const char * pDebugName) override
    {
        return new GLES3Buffer{desc, this};
    }

    NGIBufferView* CreateBufferViewImp(NGIBuffer* buffer, const NGIBufferViewDesc& desc) override
    {
        Assert(false);
        return nullptr;
        // return new GLES3BufferView{ buffer, desc, this };
    }

    NGITexture* CreateTextureImp(const NGITextureDesc& desc, const char* pDebugName, bool shared) override
    {
        if (EnumOnlyHasFlags(desc.Usage, NGITextureUsage::RenderTarget | NGITextureUsage::DepthStencil))
        {
            return new GLES3RenderBuffer{desc, this};
        }
        else
        {
            return new GLES3Texture{desc, this};
        }
    }

    NGITextureView* CreateTextureViewImp(NGITexture* texture, const NGITextureViewDesc& desc) override
    {
        return new GLES3TextureView{texture, desc, this};
    }

    NGIStagingBuffer* CreateStagingBufferImp(const NGIBufferDesc& desc) override
    {
        return new GLES3StagingBuffer{desc, this};
    }

    //NGIStagingManager* CreateStagingManager(const NGIStagingManagerDesc& desc) override
    //{
    //    return new NGIStagingManager{desc, this};
    //}

    NGISampler* CreateSampler(const NGISamplerDesc& desc) override
    {
        return new GLES3Sampler{desc, this};
    }

    SizeType GetTextureDataPlacementAlignment(GraphicsFormat format) override;

    SizeType GetTextureDataPitchAlignment(GraphicsFormat format) override;

    GLES3ProgramBinaryCache* GetProgramBinaryCache()
    {
        return &mProgrameBinaryCache;
    }

private:
    GLES3ProgramBinaryCache mProgrameBinaryCache;
};


}   // namespace cross
