#pragma once
#include "NGITransientHeap.h"
#include "Vulkan/VulkanTransientHeap.h"
#include "NGIMath.h"
#include "NGI.h"
#include "CrossBase/BitWise.h"
#include <vector>

namespace cross
{
    static SInt32 GNGITransientAllocatorMinimumHeapSize = 128;
    static SInt32 GNGITransientAllocatorMaximumHeapSize = 512;
    static SInt32 GNGITransientAllocatorBufferCacheSize = 64;
    static SInt32 GNGITransientAllocatorTextureCacheSize = 64;
    static SInt32 GNGITransientAllocatorGarbageCollectLatency = 16;

    class NGITransientHeapCache : public NGIObject, NGIDeviceChild
    {
    public:
        struct Initializer
        {
            // Creates a default initializer using common RHI CVars.
            static Initializer CreateDefault()
            {
                Initializer initializer;
                initializer.mMinimumHeapSize = GNGITransientAllocatorMinimumHeapSize * 1024 * 1024;
                initializer.mMaximumHeapSize = GNGITransientAllocatorMaximumHeapSize * 1024 * 1024;
                initializer.mHeapAlignment = 64 * 1024;
                initializer.mBufferCacheSize = GNGITransientAllocatorBufferCacheSize;
                initializer.mTextureCacheSize = GNGITransientAllocatorTextureCacheSize;
                initializer.mGarbageCollectLatency = GNGITransientAllocatorGarbageCollectLatency;
                auto settingManager = EngineGlobal::GetSettingMgr();
                if (settingManager)
                {
                    settingManager->GetValue("TransientResources.bSupportsAllHeapFlags", initializer.bSupportsAllHeapFlags);
                }
                return initializer;
            }

            static const UInt32 kDefaultResourceCacheSize = 256;

            // The minimum size to use when creating a heap. This is the default but can grow based on allocations.
            UInt64 mMinimumHeapSize = 0;

            // The maximum size of a pool. Allocations above this size will fail.
            UInt64 mMaximumHeapSize = 0;

            // The minimum alignment for resources in the heap.
            UInt32 mHeapAlignment = 0;

            // The latency between the completed fence value and the used fence value to invoke garbage collection of the heap.
            UInt32 mGarbageCollectLatency = 0;

            // Size of the texture cache before elements are evicted.
            UInt32 mTextureCacheSize = kDefaultResourceCacheSize;

            // Size of the buffer cache before elements are evicted.
            UInt32 mBufferCacheSize = kDefaultResourceCacheSize;

            // Whether all heaps should be created with the AllowAll heap flag.
            bool bSupportsAllHeapFlags = true;
        };
        NGITransientHeapCache(NGIDevice* device, Initializer initializer)
            : mInitializer(initializer)
            , NGIDeviceChild{device}
        {
        }
        NGI_API ~NGITransientHeapCache();
        
        NGITransientHeap* CreateHeap(UInt64 size, UInt64 alignment, UInt32 memoryTypeBits, NGITransientHeap::Initializer initializer)
        {
            return new VulkanTransientHeap(GetDevice(), size, alignment, memoryTypeBits, initializer);
        }
        void Forfeit(std::vector<NGITransientHeap*>& forfeitedHeaps)
        {
            //QUICK_SCOPED_CPU_TIMING("NGITransientHeapCache::Forfeit");
            if (forfeitedHeaps.empty())
                return;
            mLiveList.reserve(forfeitedHeaps.size());
            for (SInt32 i = static_cast<SInt32>(forfeitedHeaps.size()) - 1; i >= 0; i--)
            {
                auto heap = forfeitedHeaps[i];
                Assert(heap->IsEmpty());
                heap->mLastUsedGarbageCollectCycle = mGarbageCollectCycle;
                mFreeList.emplace_back(heap);
            }
        }
        NGITransientHeap* Aquire(UInt64 size, UInt64 alignment, UInt32 memoryTypeBits, NGITransientHeapFlags flag)
        {
            //QUICK_SCOPED_CPU_TIMING("NGITransientHeapCache::Aquire");
            for (auto it = mFreeList.rbegin(); it != mFreeList.rend(); it++)
            {
                auto heap = *it;
                if (heap->IsAllocationSupported(size, memoryTypeBits, flag))
                {
                    mFreeList.erase(it.base() - 1);
                    return heap;
                }
            }
            NGITransientHeap::Initializer heapInitializer;
            //create heap according to memory request of the texture/buffer
            heapInitializer.mSize = GetHeapSize(size);
            heapInitializer.mAlignment = mInitializer.mHeapAlignment;
            heapInitializer.mFlags = (mInitializer.bSupportsAllHeapFlags ? NGITransientHeapFlags::AllowAll : flag);
            heapInitializer.mTextureCacheSize = mInitializer.mTextureCacheSize;
            heapInitializer.mBufferCacheSize = mInitializer.mBufferCacheSize;
            auto heap = CreateHeap(size, alignment, memoryTypeBits, heapInitializer);
            Assert(heap);
            mTotalMemoryCapacity += heapInitializer.mSize;
            //add it to the live list when it has been created, 
            //remove it from the live list when it has been deleted
            mLiveList.emplace_back(heap);
            return heap;
        }
        template<typename T>
        void remove_all(std::vector<T>& vec, const T& item) {
            vec.erase(std::remove(vec.begin(), vec.end(), item), vec.end());
        }
        //collect free heap in freelist
        void GarbageCollect()
        {
            auto Remove = [&](NGITransientHeap* heap)
            {
                return heap->GetLastUsedGarbageCollectCycle() + mInitializer.mGarbageCollectLatency <= mGarbageCollectCycle;
            };
            size_t it2 = 0;
            for (size_t it = 0; it < mFreeList.size(); it++)
            {
                auto heap = mFreeList[it];
                Assert(heap->IsEmpty());
                if (!Remove(heap))
                {
                    mFreeList[it2++] = heap;
                }
                else
                {
                    mTotalMemoryCapacity -= heap->GetCapacity();
                    //remove it both from mLiveList and mFreeList
                    remove_all(mLiveList, heap);
                    delete heap;
                }
            }
            mFreeList.resize(it2);
            mGarbageCollectCycle++;
            mStats.Submit(mTotalMemoryCapacity);
        }
        auto GetLiveListSize() const
        {
            return mLiveList.size();
        }
        auto GetFreeListSize() const
        {
            return mFreeList.size();
        }
        UInt64 GetHeapSize(UInt64 requestedHeapSize) const
        {
            Assert(requestedHeapSize <= mInitializer.mMaximumHeapSize);
            return std::clamp(BitCeil(requestedHeapSize), mInitializer.mMinimumHeapSize, mInitializer.mMaximumHeapSize);
        }
    private:
        UInt64 mTotalMemoryCapacity = 0;
        NGITransientMemoryStats mStats;
        std::vector<NGITransientHeap*> mFreeList;
        std::vector<NGITransientHeap*> mLiveList;
        UInt32 mGarbageCollectLatency;
        UInt64 mGarbageCollectCycle;
        Initializer mInitializer;
        friend class NGITransientResourceAllocator;
    };
}