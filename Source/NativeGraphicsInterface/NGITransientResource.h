#pragma once
//#include "Vulkan/VulkanCommon.h"
#include "Vulkan/VulkanResource.h"
#include "NGI.h"
#include "NGIUtils.h"
#include <cassert>
namespace cross
{
    class NGITransientHeap;
    struct NGITransientHeapAllocation
    {
        bool IsValid() const { return mSize != 0; }

        // Transient heap which made the allocation.
        NGITransientHeap* mHeap = nullptr;

        // Size of the allocation made from the allocator (aligned).
        UInt64 mSize = 0;

        // Offset in the transient heap; front of the heap starts at 0.
        UInt64 mOffset = 0;

        // Number of bytes of padding were added to the offset.
        UInt32 mAlignmentPad = 0;
    };
    enum class NGITransientResourceType : UInt8
    {
        Texture,
        Buffer
    };
    class NGITransientResource
    {
    public: 
        NGITransientResource(UInt64 hash, NGITransientResourceType type, std::string_view name = "")
            : mHash{hash}
            , mName{name}
            , mType{type}
        {
        }
        const std::string& GetName() const
        {
            return mName;
        }
        NGITransientHeapAllocation& GetAllocation()
        {
            return mAllocation;
        }
        const NGITransientHeapAllocation& GetAllocation() const
        {
            return mAllocation;
        }
        UInt64 GetHash() 
        {
            return mHash;
        }
        // mAllocation.mHeap is set to null when the resource is forfeited to resouce cache
        bool IsBound()
        {
            return mAllocation.mHeap != nullptr;
        }
        void Discard()
        {
            Assert(IsBound());
            bIsAcquired = false;
        }
        bool IsAcquired()
        {
            return bIsAcquired;
        }
        void Acquire()
        {
            bIsAcquired = true;
        }
        NGIResourceState* GetStates()
        {
            Assert(mStates.size());
            return mStates.data();
        }
    protected:
        bool bIsAcquired{true};
        NGITransientHeapAllocation mAllocation;
        UInt64 mHash;
        std::string mName;
        NGITransientResourceType mType;
        std::vector<NGIResourceState> mStates;
    };
    
    class NGITransientTexture : public NGITransientResource
    {
    public:
        NGITransientTexture(VulkanTexture* tex, UInt64 hash, std::string_view name = "")
            : mNativeTexture{tex}
            , NGITransientResource{hash, NGITransientResourceType::Texture, name}
        {
        }
        ~NGITransientTexture();
        void CreateStates(const NGITextureDesc& desc)
        {
            mStates = std::vector<NGIResourceState>{NGICalcSubresourceCount(desc), NGIResourceState::Undefined};
        }
        VkImage GetImage()
        {
            return reinterpret_cast<VkImage>(mNativeTexture->GetNativeHandle());
        }
        
        bool IsBoundInternal()
        {
            return bIsBoundInternal;
        }
        
        void BindInternal(VmaAllocator allocator, VmaAllocation buffer, UInt64 offset, UInt64 size, std::string_view name = "");
        
        VulkanTexture* mNativeTexture{nullptr};
    private:
        //NGITransientHeap*               mHeap{nullptr};
        bool                            bIsBoundInternal{false};
    };
    
    class NGITransientBuffer : public NGITransientResource
    {
    public:
        NGITransientBuffer(VulkanBuffer* buf, UInt64 hash, std::string_view name = "")
            : mNativeBuffer{buf}
            , NGITransientResource{hash, NGITransientResourceType::Buffer, name}
        {}
        ~NGITransientBuffer();
        void CreateStates(const NGIBufferDesc& desc)
        {
            mStates = std::vector<NGIResourceState>{NGIResourceState::Undefined};
        }
        VkBuffer GetBuffer()
        {
            return reinterpret_cast<VkBuffer>(mNativeBuffer->GetNativeHandle());
        }
        VulkanBuffer* mNativeBuffer{nullptr};                  
    private:
    };
}