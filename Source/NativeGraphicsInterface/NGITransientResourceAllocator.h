#pragma once
#include "NGITransientHeap.h"
#include "NGITransientHeapCache.h"
#include "NGI.h"
#include <vector>
#include <iostream>
#include <functional>
namespace cross
{
//FRHITransientResourceHeapAllocator
class NGITransientResourceAllocator : public NGIObject, public NGIDeviceChild
{
public:
    NGITransientResourceAllocator(NGIDevice* device)
        : NGIDeviceChild{device}
        , mHeapCache(device, NGITransientHeapCache::Initializer::CreateDefault())
    {}
    NGI_API void DeallocateTexture(NGITransientTexture* tex);
    NGI_API void DeallocateBuffer(NGITransientBuffer* buf);
    //NGI_API virtual NGITransientTexture* AllocateTexture(const NGITextureDesc& desc, std::string_view name);
    NGI_API virtual NGITransientTexture* CreateTexture(const NGITextureDesc& desc, std::string_view name)
    {
        return nullptr;
    }
    NGI_API virtual NGITransientBuffer* CreateBuffer(const NGIBufferDesc& desc, std::string_view name)
    {
        return nullptr;
    }
    NGITransientBuffer* CreateBufferInternal(const NGIBufferDesc& desc, UInt64 size, UInt64 alignment, UInt32 memoryTypeBits, std::string_view name, NGITransientHeap::CreateBufferFunction func);
    NGITransientTexture* CreateTextureInternal(const NGITextureDesc& desc, UInt64 size, UInt64 alignment, UInt32 memoryTypeBits, std::string_view name, NGITransientHeap::CreateTextureFunction func);
    NGI_API void Flush();
    NGI_API void GarbageCollect();
    NGI_API ~NGITransientResourceAllocator() = default;
private:
    void DumpJson();
    std::vector<NGITransientHeap*> mHeaps;
    NGITransientHeapCache mHeapCache;
    UInt32 mDeallocationCount{0};
};
}