#pragma once
#include "NGI.h"
#include <map>
#include <unordered_set>

namespace cross {

// Immutable POD representing a shared CPU-GPU staging area.
struct StageBuffer
{
    std::unique_ptr<NGIStagingBuffer> Buffer;
    SizeType capacity;
    mutable uint64_t lastAccessed;
    mutable bool AsyncUsing{false};
};


// Manages a pool of stages, periodically releasing stages that have been unused for a while.
// This class manages two types of host-mappable staging areas: buffer stages and image stages.
struct StageBufferPool : NGIDeviceChild, INGIResourceManager
{
public:
    StageBufferPool(NGIDevice* device)
        : NGIDeviceChild(device)
    {}
    ~StageBufferPool() override;

    // Finds or creates a stage whose capacity is at least the given number of bytes.
    // The stage is automatically released back to the pool after TIME_BEFORE_EVICTION frames.
    StageBuffer const* Allocate(NGIBufferUsage usage, SizeType numBytes);
    StageBuffer const* Allocate_AtAsyncThread(NGIBufferUsage usage, SizeType numBytes);

    // Evicts old unused stages and bumps the current frame number.
    void OnBeginFrame(FrameParam* frameparam) override;

    void EndFrame() override;

    // Destroys all unused stages and asserts that there are no stages currently in use.
    // This should be called while the context's VkDevice is still alive.
    void terminate() noexcept;

private:
    // Use an ordered multimap for quick (capacity => stage) lookups using lower_bound().
    std::multimap<SizeType, StageBuffer const*> mFreeStages;

    // Simple unordered set for stashing a list of in-use stages that can be reclaimed later.
    std::unordered_set<StageBuffer const*> mUsedStages;

    // Simple unordered set for stashing a list of in-use stages in async thread.
    std::unordered_set<StageBuffer const*> mAsyncStages;
    std::mutex mLocker;
};

}   // namespace cross