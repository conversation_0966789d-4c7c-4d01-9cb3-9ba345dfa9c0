#include "Statistics.h"
#include "NGI.h"
#include "CrossBase/TimeDef.h"
namespace cross
{
     ResourceType GPUResourceStatistics::ToResourceType(NGITexture* texture) const
    {
        auto texture_desc = texture->GetDesc();

        if (EnumHasAnyFlags(texture_desc.Usage, NGITextureUsage::RenderTarget | NGITextureUsage::DepthStencil | NGITextureUsage::SubpassInput | NGITextureUsage::UnorderedAccess))
        {
            return ResourceType::RT;
        }

        if (EnumHasAnyFlags(texture_desc.Usage, NGITextureUsage::ShaderResource))
        {
            return ResourceType::Texture;
        }

        if (EnumHasAnyFlags(texture_desc.Usage, NGITextureUsage::CopySrc))
        {
            return ResourceType::Staging;
        }


        return ResourceType::Texture;
    }
     ResourceType GPUResourceStatistics::ToResourceType(NGIBuffer* buffer) const {
        auto buffer_desc = buffer->GetDesc();

        if (EnumHasAnyFlags(buffer_desc.Usage, NGIBufferUsage::CopySrc))
        {
            return ResourceType::Staging;
        }
        
        if (EnumHasAnyFlags(buffer_desc.Usage, NGIBufferUsage::ConstantBuffer))
        {
            return ResourceType::UniformBuffer;
        }

        if (EnumHasAnyFlags(buffer_desc.Usage, NGIBufferUsage::VertexBuffer | NGIBufferUsage::IndexBuffer | NGIBufferUsage::IndirectBuffer))
        {
            return ResourceType::Geometry;
        }

        if (EnumHasAnyFlags(buffer_desc.Usage, NGIBufferUsage::TexelBuffer | NGIBufferUsage::RWTexelBuffer | NGIBufferUsage::StructuredBuffer |
            NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::ByteAddressBuffer | NGIBufferUsage::RWByteAddressBuffer | NGIBufferUsage::TextureBuffer))
        {
            return ResourceType::StructuredBuffer;
        }


        return ResourceType::UniformBuffer;
    }

     EngineMeshStatistics& EngineMeshStatistics::GetInstance()
     {
         static EngineMeshStatistics instance;
         return instance;
     }

     void EngineMeshStatistics::AddMeshStat(const std::string& debugName, const MeshStatistics& stat)
     {
         ReleaseMeshStat(debugName);

         {
             std::scoped_lock lock(mMutex);
             mStats[debugName] = stat;
             mTotalSize += stat.TotalVertexBufferSize + stat.TotalIndexBufferSize;
         }
     }
     
     void EngineMeshStatistics::EnsureMeshStat(const std::string& debugName, float debugSize)
     {
         std::scoped_lock lock(mMutex);

         auto it = mStats.find(debugName);

         if (it == mStats.end())
         {
             LOG_WARN("Mesh stats ensure missing stat for {}", debugName);
             return;
         }

         auto& stat = it->second;
         if (debugSize > 0)
         {
             Assert(debugSize == stat.TotalVertexBufferSize + stat.TotalIndexBufferSize);
         }
     }

     void EngineMeshStatistics::ReleaseMeshStat(const std::string& debugName, float debugSize)
     {
         std::scoped_lock lock(mMutex);
         auto it = mStats.find(debugName);

         if (it == mStats.end())
         {
             return;
         }

         auto& stat = it->second;
         mTotalSize -= stat.TotalVertexBufferSize + stat.TotalIndexBufferSize;

         Assert(mTotalSize >= 0);
         mStats.erase(it);
     }

     void EngineMeshStatistics::SetMeshLOD(UInt32 frameCount, const std::string& debugName, UInt8 lodIndex)
     {
         std::scoped_lock lock(mMutex);
         Assert(frameCount >= mFrameCount);
         if (frameCount > mFrameCount)
         {
             //OutputMeshStat();
             mCurrentFrameLODSize = 0;
             mFrameCount = frameCount;
         }

         auto it = mStats.find(debugName);

         if (it == mStats.end())
         {
             //LOG_WARN("Mesh stats lod selection missing stat for {}", debugName);
             return;
         }

         auto& stat = it->second;

         if (lodIndex >= stat.LODStats.size())
         {
             return;
         }

         auto& lodStat = stat.LODStats[lodIndex];
         if (lodStat.LastSelectedFrameCount < mFrameCount)
         {
             mCurrentFrameLODSize += lodStat.VertexBufferSize + lodStat.IndexBufferSize;
             lodStat.LastSelectedFrameCount = mFrameCount;
         }
     }

     void EngineMeshStatistics::OutputMeshStat()
     {
         // async?
         if (mFrameCount == 0 || mFrameCount % 60 != 0)
         {
             return;
         }

         if (mFirstWrite)
         {
             mFirstWriteTime = time::TimeStamp();
             mFirstWrite = false;
         }

         Assert(mCurrentFrameLODSize <= mTotalSize);
         std::string line = fmt::format("{},{},{}\n", mFrameCount, mCurrentFrameLODSize, mTotalSize);
         std::string fileName = fmt::format("{}/{}/MeshStats/{}.csv", PathHelper::GetCurrentDirectoryPath(), PathHelper::GetSavedPath(), mFirstWriteTime);
         FileHelper::SaveStringToFile(line, fileName.c_str(), std::ios::out | std::ios::app);
     }
}