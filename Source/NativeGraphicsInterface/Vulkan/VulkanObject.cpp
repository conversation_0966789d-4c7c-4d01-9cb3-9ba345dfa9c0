#include "VulkanObject.h"

#include "NGITransientResourceAllocator.h"
#include "NGITransientResourceAllocator.h"
#include "NativeGraphicsInterface/Vulkan/VulkanDevice.h"
#include "NativeGraphicsInterface/Vulkan/VulkanResource.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "NativeGraphicsInterface/NGIDebug.h"
#include "Vulkan/include/vulkan/vulkan.hpp"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "Resource/Shader.h"
#include "VulkanTimeStamp.h"
#include "VulkanAccelStruct.h"
#include "VulkanResource.h"

namespace cross
{
    std::tuple<VkPipelineStageFlags, VkAccessFlags> GetBufferStatePipelineStageAccess(NGIResourceState state)
    {
        // for transient manager, buffer first used, but bind memory may bind another buffer or image
        if (state == NGIResourceState::Undefined)
        {
            return {VK_PIPELINE_STAGE_ALL_COMMANDS_BIT, VK_ACCESS_MEMORY_READ_BIT | VK_ACCESS_MEMORY_WRITE_BIT};
        }

        VkPipelineStageFlags stage = 0;
        VkAccessFlags access = 0;

        UInt32 readStateCount = 0;
        UInt32 writeStateCount = 0;

        if (EnumHasAllFlags(state, NGIResourceState::VertexBuffer))
        {
            stage |= VK_PIPELINE_STAGE_VERTEX_INPUT_BIT;
            access |= VK_ACCESS_VERTEX_ATTRIBUTE_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::IndexBuffer))
        {
            stage |= VK_PIPELINE_STAGE_VERTEX_INPUT_BIT;
            access |= VK_ACCESS_INDEX_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::IndirectArgument))
        {
            stage |= VK_PIPELINE_STAGE_DRAW_INDIRECT_BIT;
            access |= VK_ACCESS_INDIRECT_COMMAND_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::CopyDst))
        {
            stage |= VK_PIPELINE_STAGE_TRANSFER_BIT;
            access |= VK_ACCESS_TRANSFER_WRITE_BIT;
            writeStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::CopySrc))
        {
            stage |= VK_PIPELINE_STAGE_TRANSFER_BIT;
            access |= VK_ACCESS_TRANSFER_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::HostRead))
        {
            stage |= VK_PIPELINE_STAGE_HOST_BIT;
            access |= VK_ACCESS_HOST_READ_BIT;
            readStateCount++;
        }

        if (EnumHasAllFlags(state, NGIResourceState::ConstantBufferBit))
        {
            access |= VK_ACCESS_UNIFORM_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::ShaderResourceBit))
        {
            access |= VK_ACCESS_SHADER_READ_BIT;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::UnorderedAccessBit))
        {
            access |= VK_ACCESS_SHADER_READ_BIT;
            access |= VK_ACCESS_SHADER_WRITE_BIT;
            writeStateCount++;
        }
        
        if (EnumHasAllFlags(state, NGIResourceState::VertexShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_VERTEX_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::HullShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_TESSELLATION_CONTROL_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::DomainShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_TESSELLATION_EVALUATION_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::GeometryShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_GEOMETRY_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::PixelShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::ComputeShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructReadBit))
        {
            access |= VK_ACCESS_ACCELERATION_STRUCTURE_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructWrite))
        {
            stage |= VK_PIPELINE_STAGE_ACCELERATION_STRUCTURE_BUILD_BIT_KHR;
            access |= VK_ACCESS_ACCELERATION_STRUCTURE_WRITE_BIT_KHR;
            writeStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructBuildInputBit) || EnumHasAllFlags(state, NGIResourceState::AccelStructBuildBLASBit))
        {
            access |= VK_ACCESS_ACCELERATION_STRUCTURE_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::RayTracingShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_RAY_TRACING_SHADER_BIT_KHR;
        }

        Assert((writeStateCount == 1 && readStateCount == 0) || (writeStateCount == 0 && readStateCount > 0));

        return {
            stage,
            access,
        };
    }
    /*
    * implmentation with synchronization2 stage flags and access
    */
    std::tuple<VkPipelineStageFlags2KHR, VkAccessFlags2KHR> GetBufferStatePipelineStageAccess2(NGIResourceState state)
    {
        // for transient manager, buffer first used, but bind memory may bind another buffer or image
        if (state == NGIResourceState::Undefined)
        {
            return {VK_PIPELINE_STAGE_2_ALL_COMMANDS_BIT_KHR, VK_ACCESS_2_MEMORY_READ_BIT_KHR | VK_ACCESS_2_MEMORY_WRITE_BIT_KHR};
        }

        VkPipelineStageFlags2KHR stage = 0;
        VkAccessFlagBits2KHR access = 0;

        UInt32 readStateCount = 0;
        UInt32 writeStateCount = 0;

        if (EnumHasAllFlags(state, NGIResourceState::VertexBuffer))
        {
            stage |= VK_PIPELINE_STAGE_2_VERTEX_INPUT_BIT_KHR;
            access |= VK_ACCESS_2_VERTEX_ATTRIBUTE_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::IndexBuffer))
        {
            stage |= VK_PIPELINE_STAGE_2_VERTEX_INPUT_BIT_KHR;
            access |= VK_ACCESS_2_INDEX_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::IndirectArgument))
        {
            stage |= VK_PIPELINE_STAGE_2_DRAW_INDIRECT_BIT_KHR;
            access |= VK_ACCESS_2_INDIRECT_COMMAND_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::CopyDst))
        {
            stage |= VK_PIPELINE_STAGE_2_TRANSFER_BIT_KHR;
            access |= VK_ACCESS_2_TRANSFER_WRITE_BIT_KHR;
            writeStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::CopySrc))
        {
            stage |= VK_PIPELINE_STAGE_2_TRANSFER_BIT_KHR;
            access |= VK_ACCESS_2_TRANSFER_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::HostRead))
        {
            stage |= VK_PIPELINE_STAGE_2_HOST_BIT_KHR;
            access |= VK_ACCESS_2_HOST_READ_BIT_KHR;
            readStateCount++;
        }

        if (EnumHasAllFlags(state, NGIResourceState::ConstantBufferBit))
        {
            stage |= VK_PIPELINE_STAGE_2_ALL_COMMANDS_BIT_KHR;
            access |= VK_ACCESS_2_UNIFORM_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::ShaderResourceBit))
        {
            access |= VK_ACCESS_2_SHADER_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::UnorderedAccessBit))
        {
            access |= VK_ACCESS_2_SHADER_READ_BIT_KHR;
            access |= VK_ACCESS_2_SHADER_WRITE_BIT_KHR;
            writeStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::VertexShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_VERTEX_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::HullShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_TESSELLATION_CONTROL_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::DomainShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_TESSELLATION_EVALUATION_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::GeometryShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_GEOMETRY_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::PixelShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_FRAGMENT_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::ComputeShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_COMPUTE_SHADER_BIT_KHR;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructReadBit))
        {
            access |= VK_ACCESS_2_ACCELERATION_STRUCTURE_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructWrite))
        {
            stage |= VK_PIPELINE_STAGE_2_ACCELERATION_STRUCTURE_BUILD_BIT_KHR;
            access |= VK_ACCESS_2_ACCELERATION_STRUCTURE_WRITE_BIT_KHR;
            writeStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::AccelStructBuildInputBit) || EnumHasAllFlags(state, NGIResourceState::AccelStructBuildBLASBit))
        {
            stage |= VK_PIPELINE_STAGE_2_ACCELERATION_STRUCTURE_BUILD_BIT_KHR;
            access |= VK_ACCESS_2_ACCELERATION_STRUCTURE_READ_BIT_KHR;
            readStateCount++;
        }
        if (EnumHasAllFlags(state, NGIResourceState::RayTracingShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_RAY_TRACING_SHADER_BIT_KHR;
        }

        Assert((writeStateCount == 1 && readStateCount == 0) || (writeStateCount == 0 && readStateCount > 0));

        return {
            stage,
            access,
        };
    }

    std::tuple<VkPipelineStageFlags, VkAccessFlags, VkImageLayout> GetTexturePipelineStageAndAccessAndLayout(NGIResourceState state, VkImageAspectFlagBits aspect, bool hasDepth, bool hasStencil, bool srcState)
    {
        switch (state)
        {
        case NGIResourceState::Undefined:
            // for transient manager, image first used, but bind memory may bind another buffer or image
            Assert(srcState);
            return {
                VK_PIPELINE_STAGE_ALL_COMMANDS_BIT,
                VK_ACCESS_MEMORY_READ_BIT | VK_ACCESS_MEMORY_WRITE_BIT,
                VK_IMAGE_LAYOUT_UNDEFINED,
            };
        case NGIResourceState::Present:
            if (srcState)
            {
                return {
                    VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT,
                    0,
                    VK_IMAGE_LAYOUT_PRESENT_SRC_KHR,
                };
            }
            else
            {
                return {
                    VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT,
                    0,
                    VK_IMAGE_LAYOUT_PRESENT_SRC_KHR,
                };
            }
        default:
            break;
        }

        VkPipelineStageFlags stage = 0;
        VkAccessFlags access = 0;
        VkImageLayout layout = VK_IMAGE_LAYOUT_UNDEFINED;

        UInt32 writeStateCount = 0;
        UInt32 readStateCount = 0;

        switch (aspect)
        {
        case VK_IMAGE_ASPECT_COLOR_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
            {
                stage |= VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
                access |= VK_ACCESS_INPUT_ATTACHMENT_READ_BIT;
                layout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_READ_BIT;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        case VK_IMAGE_ASPECT_DEPTH_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_READ_BIT;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetRead | NGIResourceState::SubpassRead))
            {
                if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
                {
                    stage |= VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
                    access |= VK_ACCESS_INPUT_ATTACHMENT_READ_BIT;
                }
                if (EnumHasAnyFlags(state, NGIResourceState::TargetRead))
                {
                    stage |= VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
                    stage |= VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT;
                    access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT;
                }
                layout = VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
                stage |= VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT;
                access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT;
                access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        case VK_IMAGE_ASPECT_STENCIL_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_READ_BIT;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
                access |= VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetRead | NGIResourceState::SubpassRead))
            {
                if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
                {
                    stage |= VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
                    access |= VK_ACCESS_INPUT_ATTACHMENT_READ_BIT;
                }
                if (EnumHasAnyFlags(state, NGIResourceState::TargetRead))
                {
                    stage |= VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
                    stage |= VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT;
                    access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT;
                }
                layout = VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
                stage |= VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT;
                access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT;
                access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;
                layout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        default:
            Assert(false);
            break;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::NoEarlyTestBit))
        {
            stage &= ~VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
        }

        // TODO(peterwjma): unoptimized code, dst src for copy and resolve could be general or present, we only use transfer for now
        if (EnumHasAnyFlags(state, NGIResourceState::CopyDst | NGIResourceState::ResolveDst))
        {
            stage |= VK_PIPELINE_STAGE_TRANSFER_BIT;
            access |= VK_ACCESS_TRANSFER_WRITE_BIT;
            layout = VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL;
            writeStateCount++;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::CopySrc | NGIResourceState::ResolveSrc))
        {
            stage |= VK_PIPELINE_STAGE_TRANSFER_BIT;
            access |= VK_ACCESS_TRANSFER_READ_BIT;
            layout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;
            readStateCount++;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::ShaderResourceBit))
        {
            access |= VK_ACCESS_SHADER_READ_BIT;
            layout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
            readStateCount++;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::UnorderedAccessBit))
        {
            access |= VK_ACCESS_SHADER_WRITE_BIT;
            access |= VK_ACCESS_SHADER_READ_BIT;
            layout = VK_IMAGE_LAYOUT_GENERAL;
            writeStateCount++;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::VertexShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_VERTEX_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::HullShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_TESSELLATION_CONTROL_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::DomainShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_TESSELLATION_EVALUATION_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::GeometryShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_GEOMETRY_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::PixelShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::ComputeShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::RayTracingShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_RAY_TRACING_SHADER_BIT_KHR;
        }

        Assert((writeStateCount == 1 && readStateCount == 0) || (writeStateCount == 0 && readStateCount > 0));
        if (readStateCount > 1)
        {
            layout = VK_IMAGE_LAYOUT_GENERAL;
        }

        return {
            stage,
            access,
            layout,
        };
    }

    /*
    * implmentation with synchronization2 stage flags and access
    */
    std::tuple<VkPipelineStageFlags2KHR, VkAccessFlags2KHR, VkImageLayout> GetTexturePipelineStageAndAccessAndLayout2(NGIResourceState state, VkImageAspectFlagBits aspect, bool hasDepth, bool hasStencil, bool srcState)
    {
        switch (state)
        {
        case NGIResourceState::Undefined:
            // for transient manager, image first used, but bind memory may bind another buffer or image
            Assert(srcState);
            return {
                VK_PIPELINE_STAGE_2_ALL_COMMANDS_BIT_KHR,
                VK_ACCESS_2_MEMORY_READ_BIT_KHR | VK_ACCESS_2_MEMORY_WRITE_BIT_KHR,
                VK_IMAGE_LAYOUT_UNDEFINED,
            };
        case NGIResourceState::Present:
            if (srcState)
            {
                return {
                    VK_PIPELINE_STAGE_2_TOP_OF_PIPE_BIT_KHR,
                    0,
                    VK_IMAGE_LAYOUT_PRESENT_SRC_KHR,
                };
            }
            else
            {
                return {
                    VK_PIPELINE_STAGE_2_BOTTOM_OF_PIPE_BIT_KHR,
                    0,
                    VK_IMAGE_LAYOUT_PRESENT_SRC_KHR,
                };
            }
        default:
            break;
        }

        VkPipelineStageFlags2KHR stage = 0;
        VkAccessFlags2KHR access = 0;
        VkImageLayout layout = VK_IMAGE_LAYOUT_UNDEFINED;

        UInt32 writeStateCount = 0;
        UInt32 readStateCount = 0;

        switch (aspect)
        {
        case VK_IMAGE_ASPECT_COLOR_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
            {
                stage |= VK_PIPELINE_STAGE_2_FRAGMENT_SHADER_BIT_KHR;
                access |= VK_ACCESS_2_INPUT_ATTACHMENT_READ_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_READ_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_READ_BIT_KHR | VK_ACCESS_2_COLOR_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        case VK_IMAGE_ASPECT_DEPTH_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_READ_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetRead | NGIResourceState::SubpassRead))
            {
                if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
                {
                    stage |= VK_PIPELINE_STAGE_2_FRAGMENT_SHADER_BIT_KHR;
                    access |= VK_ACCESS_2_INPUT_ATTACHMENT_READ_BIT_KHR;
                }
                if (EnumHasAnyFlags(state, NGIResourceState::TargetRead))
                {
                    stage |= VK_PIPELINE_STAGE_2_EARLY_FRAGMENT_TESTS_BIT_KHR;
                    stage |= VK_PIPELINE_STAGE_2_LATE_FRAGMENT_TESTS_BIT_KHR;
                    access |= VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT;
                }
                layout = VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_2_EARLY_FRAGMENT_TESTS_BIT_KHR;
                stage |= VK_PIPELINE_STAGE_2_LATE_FRAGMENT_TESTS_BIT_KHR;
                access |= VK_ACCESS_2_DEPTH_STENCIL_ATTACHMENT_READ_BIT_KHR;
                access |= VK_ACCESS_2_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        case VK_IMAGE_ASPECT_STENCIL_BIT:
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveSrc))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_READ_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetResolveDst))
            {
                stage |= VK_PIPELINE_STAGE_2_COLOR_ATTACHMENT_OUTPUT_BIT_KHR;
                access |= VK_ACCESS_2_COLOR_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetRead | NGIResourceState::SubpassRead))
            {
                if (EnumHasAnyFlags(state, NGIResourceState::SubpassRead))
                {
                    stage |= VK_PIPELINE_STAGE_2_FRAGMENT_SHADER_BIT_KHR;
                    access |= VK_ACCESS_2_INPUT_ATTACHMENT_READ_BIT_KHR;
                }
                if (EnumHasAnyFlags(state, NGIResourceState::TargetRead))
                {
                    stage |= VK_PIPELINE_STAGE_2_EARLY_FRAGMENT_TESTS_BIT_KHR;
                    stage |= VK_PIPELINE_STAGE_2_LATE_FRAGMENT_TESTS_BIT_KHR;
                    access |= VK_ACCESS_2_DEPTH_STENCIL_ATTACHMENT_READ_BIT_KHR;
                }
                layout = VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL;
                readStateCount++;
            }
            if (EnumHasAnyFlags(state, NGIResourceState::TargetReadWrite))
            {
                stage |= VK_PIPELINE_STAGE_2_EARLY_FRAGMENT_TESTS_BIT_KHR;
                stage |= VK_PIPELINE_STAGE_2_LATE_FRAGMENT_TESTS_BIT_KHR;
                access |= VK_ACCESS_2_DEPTH_STENCIL_ATTACHMENT_READ_BIT_KHR;
                access |= VK_ACCESS_2_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT_KHR;
                layout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                writeStateCount++;
            }
            break;
        default:
            Assert(false);
            break;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::NoEarlyTestBit))
        {
            stage &= ~VK_PIPELINE_STAGE_2_EARLY_FRAGMENT_TESTS_BIT_KHR;
        }

        // TODO(peterwjma): unoptimized code, dst src for copy and resolve could be general or present, we only use transfer for now
        if (EnumHasAnyFlags(state, NGIResourceState::CopyDst | NGIResourceState::ResolveDst))
        {
            stage |= VK_PIPELINE_STAGE_2_TRANSFER_BIT_KHR;
            access |= VK_ACCESS_2_TRANSFER_WRITE_BIT_KHR;
            layout = VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL;
            writeStateCount++;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::CopySrc | NGIResourceState::ResolveSrc))
        {
            stage |= VK_PIPELINE_STAGE_2_TRANSFER_BIT_KHR;
            access |= VK_ACCESS_2_TRANSFER_READ_BIT_KHR;
            layout = VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL;
            readStateCount++;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::ShaderResourceBit))
        {
            access |= VK_ACCESS_2_SHADER_READ_BIT_KHR;
            layout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
            readStateCount++;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::UnorderedAccessBit))
        {
            access |= VK_ACCESS_2_SHADER_WRITE_BIT_KHR;
            access |= VK_ACCESS_2_SHADER_READ_BIT_KHR;
            layout = VK_IMAGE_LAYOUT_GENERAL;
            writeStateCount++;
        }

        if (EnumHasAnyFlags(state, NGIResourceState::VertexShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_VERTEX_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::HullShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_TESSELLATION_CONTROL_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::DomainShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_TESSELLATION_EVALUATION_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::GeometryShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_GEOMETRY_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::PixelShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_FRAGMENT_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::ComputeShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_COMPUTE_SHADER_BIT_KHR;
        }
        if (EnumHasAnyFlags(state, NGIResourceState::RayTracingShaderBit))
        {
            stage |= VK_PIPELINE_STAGE_2_RAY_TRACING_SHADER_BIT_KHR;
        }

        Assert((writeStateCount == 1 && readStateCount == 0) || (writeStateCount == 0 && readStateCount > 0));
        if (readStateCount > 1)
        {
            layout = VK_IMAGE_LAYOUT_GENERAL;
        }

        return {
            stage,
            access,
            layout,
        };
    }
    enum class TargetState : UInt8
    {
        None = 0,
        HasTarget = 1,
        TargetWritable = 2,

        Read = HasTarget,
        ReadWrite = HasTarget | TargetWritable,
    };

    ENUM_CLASS_FLAGS(TargetState);

    auto ComposeDepthStencilLayout(TargetState depthState, TargetState stencilState)
    {
        switch (depthState)
        {
        case TargetState::None:
            switch (stencilState)
            {
            case TargetState::None:
                return VK_IMAGE_LAYOUT_UNDEFINED;
            case TargetState::Read:
                return VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL;
            case TargetState::ReadWrite:
                return VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
            default:
                break;
            }
        case TargetState::Read:
            switch (stencilState)
            {
            case TargetState::None:
                return VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL;
            case TargetState::Read:
                return VK_IMAGE_LAYOUT_DEPTH_STENCIL_READ_ONLY_OPTIMAL;
            case TargetState::ReadWrite:
                return VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_STENCIL_ATTACHMENT_OPTIMAL;
            default:
                break;
            }
        case TargetState::ReadWrite:
            switch (stencilState)
            {
            case TargetState::None:
                return VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
            case TargetState::Read:
                return VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_STENCIL_READ_ONLY_OPTIMAL;
            case TargetState::ReadWrite:
                return VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;
            default:
                break;
            }
        default:
            break;
        }
        return VK_IMAGE_LAYOUT_UNDEFINED;
    };

    std::tuple<TargetState, TargetState> DecomposeDepthStencilLayout(VkImageLayout layout)
    {
        switch (layout)
        {
        case VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL:
            return {
                TargetState::ReadWrite,
                TargetState::ReadWrite,
            };
        case VK_IMAGE_LAYOUT_DEPTH_STENCIL_READ_ONLY_OPTIMAL:
            return {
                TargetState::Read,
                TargetState::Read,
            };
        case VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_STENCIL_ATTACHMENT_OPTIMAL:
            return {
                TargetState::Read,
                TargetState::ReadWrite,
            };
        case VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_STENCIL_READ_ONLY_OPTIMAL:
            return {
                TargetState::ReadWrite,
                TargetState::Read,
            };
        case VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL:
            return {
                TargetState::ReadWrite,
                TargetState::None,
            };
        case VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL:
            return {
                TargetState::Read,
                TargetState::None,
            };
        case VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL:
            return {
                TargetState::None,
                TargetState::ReadWrite,
            };
        case VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL:
            return {
                TargetState::None,
                TargetState::Read,
            };
        default:
            return {
                TargetState::None,
                TargetState::None,
            };
        }
    }

    /*
    * 1. target layout + target layout = new target layout
    * 2. none target layout + target layout = error
    * 3. none target layout + none target layout = must the same none target layout
    * 4. undefined + other layout = other layout
    * 5. undefined + undefine = undefined
    */

    VkImageLayout MergeDepthStencilLayout(VkImageLayout a, VkImageLayout b)
    {
        TargetState mergedDepthState{ 0 };
        TargetState mergedStencilState{ 0 };
        VkImageLayout firstNoneTargetLayout = VK_IMAGE_LAYOUT_UNDEFINED;

        for (auto layout : { a, b })
        {
            if (layout != VK_IMAGE_LAYOUT_UNDEFINED)
            {
                if (auto [depthState, stencilState] = DecomposeDepthStencilLayout(layout); (depthState | stencilState) == TargetState::None)
                {
                    if (firstNoneTargetLayout == VK_IMAGE_LAYOUT_UNDEFINED)
                    {
                        firstNoneTargetLayout = layout;
                    }
                    else if (firstNoneTargetLayout != layout)
                    {
                        constexpr static auto msg = "if neither are target layout, then must be the same non target layout";
                        AssertMsg(false, msg);
                        NGI_LOG_ERROR(msg);
                    }
                }
                else
                {
                    mergedDepthState |= depthState;
                    mergedStencilState |= stencilState;
                }
            }
        }

        if constexpr (false)
        {
            if (firstNoneTargetLayout != VK_IMAGE_LAYOUT_UNDEFINED && (mergedDepthState | mergedStencilState) != TargetState::None)
            {
                constexpr static auto msg = "can't merge target layout with none target layout";
                AssertMsg(false, msg);
                NGI_LOG_ERROR(msg);
            }
        }

        // if two layout conflict with each other, choose the none target layout
        if (firstNoneTargetLayout != VK_IMAGE_LAYOUT_UNDEFINED)
        {
            return firstNoneTargetLayout;
        }
        else
        {
            return ComposeDepthStencilLayout(mergedDepthState, mergedStencilState);
        }
    }

    /*
    * 1. target state -> full aspect target state
    * 2. none target state -> unchanged
    */
    VkImageLayout ComplementDepthStencilLayout(VkImageLayout layout)
    {
        auto [depthState, stencilState] = DecomposeDepthStencilLayout(layout);
        if ((depthState != TargetState::None) == (stencilState != TargetState::None))
        {
            // has a full depth/stencil aspect or not a target layout
            return layout;
        }
        else if (depthState != TargetState::None)
        {
            return ComposeDepthStencilLayout(depthState, TargetState::ReadWrite);
        }
        else if (stencilState != TargetState::None)
        {
            return ComposeDepthStencilLayout(TargetState::ReadWrite, stencilState);
        }
        else
        {
            Assert(false);
            return VK_IMAGE_LAYOUT_UNDEFINED;
        }
    }

}   // namespace cross

cross::VulkanResourceGroupLayout::VulkanResourceGroupLayout(const NGIResourceGroupLayoutDesc& desc, VulkanDevice* device)
    : NGIResourceGroupLayout{ desc, device }
{
    using namespace CrossSchema;   // safe

    auto resourcesSortedByIndex = mResources;
    std::sort(resourcesSortedByIndex.begin(), resourcesSortedByIndex.end(), [](auto& r1, auto& r2) { return r1.Index < r2.Index; });

    std::vector<VkDescriptorSetLayoutBinding> bindings;
    bindings.reserve(resourcesSortedByIndex.size());
    std::vector<VkDescriptorBindingFlags> bindingFlags;
    bindings.reserve(resourcesSortedByIndex.size());
    bool hasBindlessResource = false;
    UInt32 dynBufIdx = 0;
    for (auto& resource : resourcesSortedByIndex)
    {
        if (resource.Type == ShaderResourceType::ConstantBuffer || resource.Type == ShaderResourceType::TextureBuffer)
        {
            Assert(resource.ArraySize == 1 || resource.ArraySize == 0);
        }

        UInt32 arraySize;
        if (resource.ArraySize == 0)
        {
            if (!VulkanCapability::Inst().Bindless)
            {
                LOG_FATAL("Bindless resource not supported on current hardware!");
                return;
            }
            
            // bindless resource must be located at the last binding
            Assert(resource.Index == resourcesSortedByIndex.back().Index && resource.Space >= ShaderParamGroup_UIntBuffer && resource.Space <= ShaderParamGroup_Sampler);
            arraySize = GetDevice<VulkanDevice>()->GetPhysicalDevice()->mDescriptorIndexingProps.maxPerStageDescriptorUpdateAfterBindStorageBuffers /
                (ShaderParamGroup_Sampler - ShaderParamGroup_UIntBuffer + 1);

            bindingFlags.emplace_back(VK_DESCRIPTOR_BINDING_VARIABLE_DESCRIPTOR_COUNT_BIT | VK_DESCRIPTOR_BINDING_PARTIALLY_BOUND_BIT | VK_DESCRIPTOR_BINDING_UPDATE_AFTER_BIND_BIT);
            hasBindlessResource = true;
        }
        else
        {
            arraySize = resource.ArraySize;
            bindingFlags.emplace_back(0);
        }

        mDescriptorBindingCount += arraySize;

        VkDescriptorSetLayoutBinding binding
        {
            .binding = resource.Index,
            .descriptorType = MapDescriptorType(resource.Type),
            .descriptorCount = arraySize,
            .stageFlags = MapShaderStageFlags(resource.StageMask),
        };
        bindings.emplace_back(binding);
        mID2DescriptorBinding.emplace(resource.ID, binding);

        if (binding.descriptorType == VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC || binding.descriptorType == VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC)
        {
            mDynamicBufferIndices.emplace(resource.ID, dynBufIdx);
            dynBufIdx += binding.descriptorCount;
        }
    }

    VkDescriptorSetLayoutBindingFlagsCreateInfo bindingFlagInfo
    {
        VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_BINDING_FLAGS_CREATE_INFO,
        nullptr,
        desc.ResourceCount,
        bindingFlags.data(),
    };
    VkDescriptorSetLayoutCreateFlags flags = hasBindlessResource ? VK_DESCRIPTOR_SET_LAYOUT_CREATE_UPDATE_AFTER_BIND_POOL_BIT : 0;
    VkDescriptorSetLayoutCreateInfo createInfo
    {
        VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO,
        &bindingFlagInfo,
        flags,
        desc.ResourceCount,
        bindings.data(),
    };
    
    VK_CHECK(vkCreateDescriptorSetLayout(device->Get(), &createInfo, gVkAllocCallback, &mDescriptorSetLayout));

    NGI_LOG_DEBUG("Create descriptor set layout: {}", VkFormatHandle(mDescriptorSetLayout));
}

cross::VulkanResourceGroupLayout::~VulkanResourceGroupLayout()
{
    NGI_LOG_DEBUG("Destroy descriptor set layout: {}", VkFormatHandle(mDescriptorSetLayout));

    vkDestroyDescriptorSetLayout(GetDevice<VulkanDevice>()->Get(), mDescriptorSetLayout, gVkAllocCallback);
}

cross::VulkanPipelineLayout::VulkanPipelineLayout(const NGIPipelineLayoutDesc& desc, VulkanDevice* pDevice)
    : NGIPipelineLayout{desc, pDevice}
{
    using namespace CrossSchema;   // safe

    std::vector<VkDescriptorSetLayout> descriptorSetLayouts;
    for (UInt32 i = 0; i < mDesc.ResourceGroupCount; ++i)
    {
        auto* vkResourceGroupLayout = rhi_cast<VulkanResourceGroupLayout*>(mDesc.ResourceGroupLayouts[i]);
        descriptorSetLayouts.emplace_back(vkResourceGroupLayout->mDescriptorSetLayout);
    }

    VkPipelineLayoutCreateInfo pipelineLayoutInfo
    {
        VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO,
        nullptr,
        0,
        mDesc.ResourceGroupCount,
        descriptorSetLayouts.data(),
        0,
        nullptr,
    };
    VK_CHECK(vkCreatePipelineLayout(GetDevice<VulkanDevice>()->Get(), &pipelineLayoutInfo, gVkAllocCallback, &mPipelineLayout));

    NGI_LOG_DEBUG("Create pipeline layout: {}", VkFormatHandle(mPipelineLayout));
}

cross::VulkanPipelineLayout::~VulkanPipelineLayout()
{
    NGI_LOG_DEBUG("Destroy pipeline layout: {}", VkFormatHandle(mPipelineLayout));

    vkDestroyPipelineLayout(GetDevice<VulkanDevice>()->Get(), mPipelineLayout, gVkAllocCallback);
}

cross::VulkanResourceGroup::VulkanResourceGroup(VulkanResourceGroupLayout* layout, VulkanResourceGroupPool* pool)
    : NGIResourceGroup{ layout, pool }
{
    Initialize(layout);
}

void cross::VulkanResourceGroup::Initialize(VulkanResourceGroupLayout* layout)
{
    mLayout = layout;
    auto dynamicBufferCount = std::accumulate(layout->mResources.begin(), layout->mResources.end(), 0u, [&](UInt32 sum, auto& resource)
    {
        auto type = MapDescriptorType(resource.Type);
        auto isDynamicBuffer = (type == VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC || type == VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC);
        return isDynamicBuffer ? sum + resource.ArraySize : sum;
    });
    mDynamicOffsets.resize(dynamicBufferCount);
}

cross::VulkanBindlessResourceGroup::VulkanBindlessResourceGroup(VulkanResourceGroupLayout* layout, VulkanResourceGroupPool* pool,
    NGIBindlessResourceType bindlessResourceType, UInt32 maxBindlessResourceCount)
: VulkanResourceGroup{layout, pool}, mMaxBindlessResourceCount(maxBindlessResourceCount)
{
    std::vector<VkDescriptorPoolSize> gPoolSizes;

    if (EnumHasAnyFlags(bindlessResourceType, NGIBindlessResourceType::TexelBuffer))
    {
        gPoolSizes.push_back(VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER, maxBindlessResourceCount});
    }
    if (EnumHasAnyFlags(bindlessResourceType, NGIBindlessResourceType::Texture))
    {
        gPoolSizes.push_back(VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE, maxBindlessResourceCount});
    }
    if (EnumHasAnyFlags(bindlessResourceType, NGIBindlessResourceType::Sampler))
    {
        gPoolSizes.push_back(VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_SAMPLER, maxBindlessResourceCount});
    }
    
    VkDescriptorPoolCreateInfo poolCreateInfo{
        VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO,
        nullptr,
        VK_DESCRIPTOR_POOL_CREATE_UPDATE_AFTER_BIND_BIT,
        1,  // the pool is binded to a single descriptor set
        static_cast<UInt32>(gPoolSizes.size()),
        gPoolSizes.data(),
    };
    VK_CHECK(vkCreateDescriptorPool(mPool->GetDevice<VulkanDevice>()->Get(), &poolCreateInfo, gVkAllocCallback, &mRawVulkanPool));
    
    VkDescriptorSetVariableDescriptorCountAllocateInfo countInfo{};
    countInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_VARIABLE_DESCRIPTOR_COUNT_ALLOCATE_INFO;
    countInfo.descriptorSetCount = 1;
    countInfo.pDescriptorCounts = &mMaxBindlessResourceCount;
    
    VkDescriptorSetAllocateInfo allocateInfo{
        VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO,
        &countInfo,
        mRawVulkanPool,
        1,
        &layout->mDescriptorSetLayout,
    };
    
    VK_CHECK(vkAllocateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), &allocateInfo, &mDescriptorSet));
}

cross::VulkanBindlessResourceGroup::~VulkanBindlessResourceGroup()
{
    vkDestroyDescriptorPool(mPool->GetDevice<VulkanDevice>()->Get(), mRawVulkanPool, gVkAllocCallback);
}

cross::VulkanResourceGroup::~VulkanResourceGroup()
{
}

void cross::VulkanResourceGroup::SetSamplers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGISampler** ppSamplers)
{
    auto* pLayout = rhi_cast<VulkanResourceGroupLayout*>(mLayout);
    Assert(pLayout->mID2DescriptorBinding.count(ID));
    auto& binding = pLayout->mID2DescriptorBinding[ID];

    std::vector<VkDescriptorImageInfo> imageInfos{Num};
    std::transform(ppSamplers, ppSamplers + Num, imageInfos.begin(), [=](NGISampler* pSampler) {
        return VkDescriptorImageInfo{
            rhi_cast<VulkanSampler*>(pSampler)->mSampler,
            {},
            VK_IMAGE_LAYOUT_UNDEFINED,
        };
    });
    VkWriteDescriptorSet write
    {
        VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
        nullptr,
        mDescriptorSet,
        binding.binding,
        ArrayOffset,
        Num,
        binding.descriptorType,
        imageInfos.data(),
        nullptr,
        nullptr,
    };
    vkUpdateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), 1, &write, 0, nullptr);
}

void cross::VulkanResourceGroup::SetConstBuffer(NameID ID, NGIBuffer* pConstBuffer, SizeType Offset, SizeType Range)
{
    auto* pLayout = rhi_cast<VulkanResourceGroupLayout*>(mLayout);
    Assert(pLayout->mID2DescriptorBinding.count(ID));
    auto& binding = pLayout->mID2DescriptorBinding[ID];
    Assert(binding.descriptorType == VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC);

    auto pVkConstBuffer = rhi_cast<VulkanStagingBuffer*>(pConstBuffer);
    VkDescriptorBufferInfo bufferInfo{
        pVkConstBuffer->mBuffer,
        Offset,
        Range,
    };
    VkWriteDescriptorSet write{
        VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
        nullptr,
        mDescriptorSet,
        binding.binding,
        0,
        1,
        binding.descriptorType,
        nullptr,
        &bufferInfo,
        nullptr,
    };
    vkUpdateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), 1, &write, 0, nullptr);
}

void cross::VulkanResourceGroup::SetAccelStruct(NameID ID, NGIAccelStruct* accel)
{
    auto* pLayout = rhi_cast<VulkanResourceGroupLayout*>(mLayout);
    Assert(pLayout->mID2DescriptorBinding.count(ID));
    auto& binding = pLayout->mID2DescriptorBinding[ID];
    
    Assert(binding.descriptorType == VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR);
    
    auto* vkAccelStruct = rhi_cast<VulkanAccelStruct*>(accel);
    
    VkWriteDescriptorSetAccelerationStructureKHR accelInfo{
        .sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET_ACCELERATION_STRUCTURE_KHR,
        .accelerationStructureCount = 1,
        .pAccelerationStructures = &vkAccelStruct->mAccelStruct
    };
    
    VkWriteDescriptorSet write{
        .sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
        .pNext = &accelInfo,
        .dstSet = mDescriptorSet,
        .dstBinding = binding.binding,
        .dstArrayElement = 0,
        .descriptorCount = 1,
        .descriptorType = VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR,
        .pImageInfo = nullptr,
        .pBufferInfo = nullptr,
        .pTexelBufferView = nullptr
    };
    
    vkUpdateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), 1, &write, 0, nullptr);
}

void cross::VulkanResourceGroup::SetTextures(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGITextureView** ppTextureViews)
{
    auto* pLayout = rhi_cast<VulkanResourceGroupLayout*>(mLayout);
    Assert(pLayout->mID2DescriptorBinding.count(ID));
    auto& binding = pLayout->mID2DescriptorBinding[ID];

    auto layout = VK_IMAGE_LAYOUT_UNDEFINED;
    switch (binding.descriptorType)
    {
    case VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE:
    case VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT:
        layout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
        break;
    case VK_DESCRIPTOR_TYPE_STORAGE_IMAGE:
        layout = VK_IMAGE_LAYOUT_GENERAL;
        break;
    default:
        Assert(false);
        break;
    }

    std::vector<VkDescriptorImageInfo> imageInfos{Num};
    std::transform(ppTextureViews, ppTextureViews + Num, imageInfos.begin(), [=](NGITextureView* pTextureView) {
        return VkDescriptorImageInfo{
            {},
            rhi_cast<VulkanTextureView*>(pTextureView)->mView,
            layout,
        };
    });
    VkWriteDescriptorSet write{
        VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
        nullptr,
        mDescriptorSet,
        binding.binding,
        ArrayOffset,
        Num,
        binding.descriptorType,
        imageInfos.data(),
        nullptr,
        nullptr,
    };
    vkUpdateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), 1, &write, 0, nullptr);
}

void cross::VulkanResourceGroup::SetBuffers(NameID ID, UInt32 ArrayOffset, UInt32 Num, NGIBufferView** ppBufferViews)
{
    auto* pLayout = rhi_cast<VulkanResourceGroupLayout*>(mLayout);
    Assert(pLayout->mID2DescriptorBinding.count(ID));
    auto& binding = pLayout->mID2DescriptorBinding[ID];
    Assert(ArrayOffset + Num < binding.descriptorCount);

    std::vector<VkDescriptorBufferInfo> bufferInfos;
    std::vector<VkBufferView> bufferViews;
    switch (binding.descriptorType)
    {
    case VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER:
    case VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER:
        bufferViews.resize(Num);
        std::transform(ppBufferViews, ppBufferViews + Num, bufferViews.begin(), [](NGIBufferView* pBufferView) { 
            Assert(rhi_cast<VulkanBufferView*>(pBufferView)->mView);
            return rhi_cast<VulkanBufferView*>(pBufferView)->mView; });
        break;
    case VK_DESCRIPTOR_TYPE_STORAGE_BUFFER:
    case VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC:
        bufferInfos.resize(Num);
        std::transform(ppBufferViews, ppBufferViews + Num, bufferInfos.begin(), [=](NGIBufferView* pBufferView) {
            auto& desc = pBufferView->GetDesc();
            auto* vkBuffer = rhi_cast<VulkanBufferView*>(pBufferView);
            return VkDescriptorBufferInfo{
                vkBuffer->mBuffer,
                desc.BufferLocation,
                desc.SizeInBytes,
            };
        });
        break;
    default:
        Assert(false);
        break;
    }

    VkWriteDescriptorSet write{
        VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
        nullptr,
        mDescriptorSet,
        binding.binding,
        ArrayOffset,
        Num,
        binding.descriptorType,
        nullptr,
        bufferInfos.empty() ? nullptr : bufferInfos.data(),
        bufferViews.empty() ? nullptr : bufferViews.data(),
    };
    vkUpdateDescriptorSets(mPool->GetDevice<VulkanDevice>()->Get(), 1, &write, 0, nullptr);
}

cross::ResourceBindingKey::ResourceBindingKey(VulkanResourceGroupLayout* vkLayout, UInt32 numBindings, const NGIResourceBinding* bindings, FrameAllocator* alloc)
{
#if _WIN32
    if (alloc)
    {
        mPool = std::move(FrameAllocatorPool(alloc, FRAME_STAGE_RENDER));
        std::pmr::polymorphic_allocator<UInt8> alloccator(&mPool);
        mData = std::move(std::pmr::vector<UInt8>(alloccator));
    }
#endif
    auto& ID2Descriptors = vkLayout->mID2DescriptorBinding;

    mData.resize(sizeof(UInt32) + sizeof(NGIResourceBinding) * numBindings + sizeof(UInt32));
    SimpleStream stream{mData.data(), mData.data() + mData.size()};

    stream << vkLayout->GetUniqueID();
    UInt32 numEffectiveBindings = 0;

    for (UInt32 i = 0; i < numBindings; i++)
    {
        auto& binding = bindings[i];
        if (auto ret = ID2Descriptors.find(binding.ID); ret != ID2Descriptors.end())
        {
            stream << binding;
            ++numEffectiveBindings;
        }
    }
    stream << numEffectiveBindings;

    auto byteSize = stream.mCurrentPos - mData.data();
    auto roundedByteSize = ((byteSize - 1) / 4 + 1) * 4;
    mData.resize(roundedByteSize);
    
    Assert(numEffectiveBindings == vkLayout->mDescriptorBindingCount);
}

bool cross::ResourceBindingKey::Hasher::operator()(const ResourceBindingKey& a, const ResourceBindingKey& b) const
{
    return a.mData == b.mData;
}

size_t cross::ResourceBindingKey::Hasher::operator()(const ResourceBindingKey& desc) const
{
    Assert(desc.mData.size() % 4 == 0);
    return HashState(reinterpret_cast<const UInt32*>(desc.mData.data()), desc.mData.size() / 4);
}

bool cross::DescriptorSet::Hasher::operator()(const DynamicBufferKey& a, const DynamicBufferKey& b) const
{
    return a.ID != b.ID ? a.ID < b.ID : a.Offset < b.Offset;
}

cross::DescriptorSetCreator::~DescriptorSetCreator()
{
    NGI_LOG_DEBUG("Destroy resource group pool with vkDescriptorPool: {}", VkFormatHandle(mPool));
    if (mDevice)
        vkDestroyDescriptorPool(mDevice->Get(), mPool, gVkAllocCallback);
}

void cross::DescriptorSetCreator::Init(VulkanDevice* device)
{
    mDevice = device;
    std::array gPoolSizes{
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_SAMPLER, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC, 4096},
        VkDescriptorPoolSize{VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT, 4096},
    };

    VkDescriptorPoolCreateInfo info{
        VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO,
        nullptr,
        VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT,
        4096,
        static_cast<UInt32>(gPoolSizes.size()),
        gPoolSizes.data(),
    };
    VK_CHECK(vkCreateDescriptorPool(mDevice->Get(), &info, gVkAllocCallback, &mPool));
    mFreeSet = static_cast<int32_t>(info.maxSets);
    NGI_LOG_DEBUG("Create resource group pool with vkDescriptorPool: {}", VkFormatHandle(mPool));
}

cross::DescriptorSet* cross::DescriptorSetCreator::Allocate(UInt32 FrameId, const ResourceBindingKey& resourceBindingKey, VulkanResourceGroupLayout* mLayout, UInt32 numBindings, const NGIResourceBinding* bindings, VulkanResourceGroup* outResourceGroup)
{
    // allocate a new descriptor set
    std::unique_lock writerLock(mPoolMutex);
    DescriptorSet* findDescriptorSet = VulkanResourceGroupPool::FindReadyDescriptorSet(mDescriptorSets, FrameId, resourceBindingKey, mLayout, numBindings, bindings, outResourceGroup);
    if (findDescriptorSet != nullptr)
        return findDescriptorSet;

    if (mFreeSet == 0)
    {
        LOG_ERROR("At frame {}, the descriptor set is full", FrameId);
        return nullptr;
    }
    mFreeSet--;

    auto& dynBufOffsets = outResourceGroup->mDynamicOffsets;

    VkDescriptorSetAllocateInfo allocateInfo{
        VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO,
        nullptr,
        mPool,
        1,
        &mLayout->mDescriptorSetLayout,
    };
    DescriptorSet descriptorSet{};
    descriptorSet.Lifetime = FrameId;
    descriptorSet.Pool = this;
    VK_CHECK(vkAllocateDescriptorSets(mDevice->Get(), &allocateInfo, &descriptorSet.Set));
    Assert(descriptorSet.Set);
    // update descriptor set
    std::vector<VkWriteDescriptorSet> writes;
    std::deque<VkDescriptorImageInfo> imageInfos;
    std::deque<VkDescriptorBufferInfo> bufferInfos;
    std::deque<VkBufferView> bufferViews;
    VkWriteDescriptorSetAccelerationStructureKHR accelInfo{
        VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET_ACCELERATION_STRUCTURE_KHR
    };

    auto& ID2Descriptors = mLayout->mID2DescriptorBinding;

    auto maxUniformBufferRange = mDevice->mPhysicalDeviceInfo->mProps.properties.limits.maxUniformBufferRange;
    auto maxStorageBufferRange = mDevice->mPhysicalDeviceInfo->mProps.properties.limits.maxStorageBufferRange;

    for (UInt32 i = 0; i < numBindings; i++)
    {
        auto& binding = bindings[i];

        if (auto ret = ID2Descriptors.find(binding.ID); ret != ID2Descriptors.end())
        {
            VkWriteDescriptorSet write{
                VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET,
                nullptr,
                descriptorSet.Set,
                ret->second.binding,
                binding.ArrayOffset,
                1,
                ret->second.descriptorType,
            };

            switch (binding.Type)
            {
            case NGIResourceBindingType::Sampler:
            {
                Assert(write.descriptorType == VK_DESCRIPTOR_TYPE_SAMPLER);
                VkDescriptorImageInfo samplerInfo{
                    rhi_cast<VulkanSampler*>(binding.Sampler)->mSampler,
                };
                write.pImageInfo = &imageInfos.emplace_back(samplerInfo);
                break;
            }
            case NGIResourceBindingType::Texture:
            {
                Assert(write.descriptorType == VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE || write.descriptorType == VK_DESCRIPTOR_TYPE_STORAGE_IMAGE || write.descriptorType == VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT);
                auto* vkTextureView = rhi_cast<VulkanTextureView*>(binding.TextureView);
                auto& texViewDesc = vkTextureView->GetDesc();
                VkDescriptorImageInfo imageInfo{
                    VK_NULL_HANDLE,
                    vkTextureView->mView,
                };
                switch (write.descriptorType)
                {
                case VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE:
                {
                    if (EnumHasAnyFlags(binding.Flags, NGIResourceBindingFlags::RenderPassTargetSimultaneously))
                    {
                        switch (texViewDesc.SubRange.Aspect)
                        {
                        case NGITextureAspect::Depth:
                            imageInfo.imageLayout = VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL;
                            break;
                        case NGITextureAspect::Stencil:
                            imageInfo.imageLayout = VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL;
                            break;
                        case NGITextureAspect::Color:
                            imageInfo.imageLayout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
                            break;
                        default:
                            AssertMsg(false, "Only one plane was allowed for subpass input");
                            break;
                        }
                    }
                    else
                    {
                        imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
                    }
                    break;
                }
                case VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT:
                {
                    switch (texViewDesc.SubRange.Aspect)
                    {
                    case NGITextureAspect::Depth:
                        imageInfo.imageLayout = VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL;
                        break;
                    case NGITextureAspect::Stencil:
                        imageInfo.imageLayout = VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL;
                        break;
                    case NGITextureAspect::Color:
                        imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
                        break;
                    default:
                        AssertMsg(false, "Only one plane was allowed for subpass input");
                        break;
                    }
                    break;
                }
                case VK_DESCRIPTOR_TYPE_STORAGE_IMAGE:
                    imageInfo.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
                    break;
                default:
                    Assert(false);
                    break;
                }
                write.pImageInfo = &imageInfos.emplace_back(imageInfo);
                break;
            }
            case NGIResourceBindingType::Buffer:
            {
                auto* vkBufferView = rhi_cast<VulkanBufferView*>(binding.BufferView);
                switch (write.descriptorType)
                {
                case VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER:
                case VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER:
                    Assert(vkBufferView->mView);
                    write.pTexelBufferView = &bufferViews.emplace_back(vkBufferView->mView);
                    break;
                case VK_DESCRIPTOR_TYPE_STORAGE_BUFFER:
                {
                    //Assert(vkBufferView->GetDesc().SizeInBytes);

                    VkDescriptorBufferInfo bufferInfo{
                        vkBufferView->mBuffer,
                        vkBufferView->GetDesc().BufferLocation,
                        vkBufferView->GetDesc().SizeInBytes == 0 ? VK_WHOLE_SIZE : vkBufferView->GetDesc().SizeInBytes,
                    };
                    write.pBufferInfo = &bufferInfos.emplace_back(bufferInfo);
                    break;
                }
                case VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC:
                {
                    Assert(vkBufferView->GetDesc().SizeInBytes);

                    VkDescriptorBufferInfo bufferInfo{
                        vkBufferView->mBuffer,
                        0,
                        vkBufferView->GetDesc().SizeInBytes == 0 ? VK_WHOLE_SIZE : vkBufferView->GetDesc().SizeInBytes,
                    };
                    write.pBufferInfo = &bufferInfos.emplace_back(bufferInfo);

                    Assert(mLayout->mDynamicBufferIndices.count(binding.ID));
                    DescriptorSet::DynamicBufferKey key{binding.ID, binding.ArrayOffset};
                    auto dynBufIdx = mLayout->mDynamicBufferIndices[binding.ID] + binding.ArrayOffset;

                    Assert(dynBufIdx < outResourceGroup->mDynamicOffsets.size());
                    descriptorSet.DynamicBuffers[key] = {dynBufIdx, 0, vkBufferView->GetDesc().SizeInBytes};
                    dynBufOffsets[dynBufIdx] = static_cast<UInt32>(vkBufferView->GetDesc().BufferLocation);
                    break;
                }
                default:
                    Assert(false);
                    break;
                }
                break;
            }
            case NGIResourceBindingType::ConstBuffer:
            {
                Assert(binding.ArrayOffset == 0);

                VkDeviceSize maxBufferRange = 0;
                switch (ret->second.descriptorType)
                {
                case VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC:
                    maxBufferRange = maxUniformBufferRange;
                    break;
                case VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC:
                    maxBufferRange = maxStorageBufferRange;
                    break;
                default:
                    Assert(false);
                    break;
                }

                Assert(binding.ConstBufferView.Range <= maxBufferRange);

                // ubo & ssbo always use zero offset because offset persist in the pDynamicOffsets while all ubo & ssbo are dynamic type
                VkDescriptorBufferInfo bufferWriteInfo;
                bufferWriteInfo.buffer = reinterpret_cast<VkBuffer>(binding.ConstBufferView.ConstBuffer->GetNativeHandle());
                bufferWriteInfo.offset = 0;
                bufferWriteInfo.range = binding.ConstBufferView.Range;
                write.pBufferInfo = &bufferInfos.emplace_back(bufferWriteInfo);

                Assert(mLayout->mDynamicBufferIndices.count(binding.ID));
                DescriptorSet::DynamicBufferKey key{binding.ID, binding.ArrayOffset};
                auto dynBufIdx = mLayout->mDynamicBufferIndices[binding.ID] + binding.ArrayOffset;

                Assert(dynBufIdx < outResourceGroup->mDynamicOffsets.size());
                descriptorSet.DynamicBuffers[key] = {dynBufIdx, 0, binding.ConstBufferView.Range};
                dynBufOffsets[dynBufIdx] = static_cast<UInt32>(binding.ConstBufferView.Offset);

                break;
            }
            case NGIResourceBindingType::AccelStruct:
            {
                Assert(write.descriptorType == VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR);
                auto* vkAccelStruct = rhi_cast<VulkanAccelStruct*>(binding.AccelStruct);

                accelInfo.accelerationStructureCount = 1;
                accelInfo.pAccelerationStructures = &vkAccelStruct->mAccelStruct;
                write.pNext = &accelInfo;
                break;
            }
            case NGIResourceBindingType::Unknown:
                Assert(false);
                break;
            }

            writes.emplace_back(write);
        }
    }

    vkUpdateDescriptorSets(mDevice->Get(), static_cast<UInt32>(writes.size()), writes.data(), 0, nullptr);

    outResourceGroup->mDescriptorSet = descriptorSet.Set;
    return &mDescriptorSets[resourceBindingKey].emplace_back(std::move(descriptorSet));
}

void cross::DescriptorSetCreator::FreeDescriptorSet(VkDevice device, const std::vector<VkDescriptorSet> vkDescriptorSets)
{
    SCOPED_CPU_TIMING(GroupRendering, "DescriptorSetCreator::FreeDescriptorSet");
    mFreeSet += static_cast<int32_t>(vkDescriptorSets.size());
    VK_CHECK(vkFreeDescriptorSets(device, mPool, static_cast<UInt32>(vkDescriptorSets.size()), vkDescriptorSets.data()));
}

cross::VulkanResourceGroupPool::VulkanResourceGroupPool(const NGIResourceGroupPoolDesc& desc, VulkanDevice* device)
    : NGIResourceGroupPool{device}
{
    for (auto& creator : mDescriptorSetCreators)
    {
        creator.Init(device);
    }
    int startNum = 0;
    std::generate_n(mThreadIndexMapping.begin(), mThreadIndexMapping.size(), [&startNum]() { return startNum++; });
}

cross::VulkanResourceGroupPool::~VulkanResourceGroupPool()
{

}

std::tuple<cross::VulkanResourceGroup*, cross::DescriptorSet*> cross::VulkanResourceGroupPool::AllocateImpl(NGIResourceGroupLayout* layout, UInt32 numBindings, NGIResourceBinding* bindings)
{
    Assert(std::is_sorted(bindings, bindings + numBindings));

    auto* vkLayout = rhi_cast<VulkanResourceGroupLayout*>(layout);

    auto resourceGroup = AllocateResourceGroup(vkLayout);

    ResourceBindingKey resourceBindingKey{vkLayout, numBindings, bindings, mFramePool};
    DescriptorSet* descriptorSet = FindReadyDescriptorSet(mReadyDescriptorSetPools, GetFrameId(), resourceBindingKey, vkLayout, numBindings, bindings, resourceGroup);
    if (!descriptorSet)
    {
        uint32_t blockID = mThreadIndexMapping[threading::TaskSystem::GetThreadIndexForTask() % (ACTUAL_NUMBER_POOL)];
        descriptorSet = mDescriptorSetCreators[blockID].Allocate(GetFrameId(), resourceBindingKey, vkLayout, numBindings, bindings, resourceGroup);
    }
    Assert(descriptorSet);
    return {resourceGroup, descriptorSet};
}

cross::NGIResourceGroup* cross::VulkanResourceGroupPool::Allocate(NGIResourceGroupLayout* layout, UInt32 numBindings, NGIResourceBinding* bindings)
{
    auto [resourceGroup, descriptorSet] = AllocateImpl(layout, numBindings, bindings);

    return resourceGroup;
}

cross::NGIResourceGroup* cross::VulkanResourceGroupPool::CreateBindlessResourceGroup(NGIResourceGroupLayout* layout, NGIBindlessResourceType BindlessResourceType, UInt32 maxBindlessResourceCount)
{
    return new VulkanBindlessResourceGroup{ rhi_cast<VulkanResourceGroupLayout*>(layout), this, BindlessResourceType, maxBindlessResourceCount };
}

cross::DescriptorSet* cross::VulkanResourceGroupPool::FindReadyDescriptorSet(const DescriptorSetPoolType& descriptorSetPools, UInt32 frameID, const ResourceBindingKey& resourceBindingKey, VulkanResourceGroupLayout* vkLayout,
                                                                             UInt32 numBindings, const NGIResourceBinding* bindings, VulkanResourceGroup* outResourceGroup)
{
    auto& dynBufOffsets = outResourceGroup->mDynamicOffsets;

    // found descriptor sets with same resource binding
    if (auto poolItr = descriptorSetPools.find(resourceBindingKey); poolItr != descriptorSetPools.end())
    {
        // find descriptor set with suitable base offset and range
        if (!poolItr->second.empty())
        {
            auto setItr = poolItr->second.begin();
            const DescriptorSet& descriptorSet = *setItr;
            for (UInt32 bindingsIndex = 0; bindingsIndex < numBindings; ++bindingsIndex)
            {
                const NGIResourceBinding& binding = bindings[bindingsIndex];
                if (binding.Type == NGIResourceBindingType::ConstBuffer || binding.Type == NGIResourceBindingType::Buffer)
                {
                    if (auto layoutBinding = vkLayout->mID2DescriptorBinding.find(binding.ID); layoutBinding != vkLayout->mID2DescriptorBinding.end())
                    {
                        const auto descriptorType = layoutBinding->second.descriptorType;
                        if (descriptorType == VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC)
                        {
                            DescriptorSet::DynamicBufferKey key{binding.ID, binding.ArrayOffset};
                            Assert(descriptorSet.DynamicBuffers.count(key));
                            auto [dynBufStartIdx, _, baseRange] = descriptorSet.DynamicBuffers.at(key);
                            Assert(baseRange == binding.ConstBufferView.Range);
                            auto dynBufIdx = dynBufStartIdx + binding.ArrayOffset;
                            Assert(dynBufIdx < dynBufOffsets.size());
                            dynBufOffsets[dynBufIdx] = static_cast<UInt32>(binding.ConstBufferView.Offset);
                        }
                        else if (descriptorType == VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC)
                        {
                            DescriptorSet::DynamicBufferKey key{binding.ID, binding.ArrayOffset};
                            Assert(descriptorSet.DynamicBuffers.count(key));
                            auto [dynBufStartIdx, _, baseRange] = descriptorSet.DynamicBuffers.at(key);
                            auto& bufferDesc = binding.BufferView->GetDesc();
                            Assert(baseRange == bufferDesc.SizeInBytes);
                            auto dynBufIdx = dynBufStartIdx + binding.ArrayOffset;
                            Assert(dynBufIdx < dynBufOffsets.size());
                            dynBufOffsets[dynBufIdx] = static_cast<UInt32>(bufferDesc.BufferLocation);
                        }
                    }
                }
            }

            // Lifetime is mutable, every thread will write GetFrameId(), don't need lock.
            setItr->Lifetime = frameID;
            outResourceGroup->mDescriptorSet = setItr->Set;
            return const_cast<DescriptorSet*>(&*setItr);
        }
    }

    return nullptr;
}

cross::VulkanResourceGroup* cross::VulkanResourceGroupPool::AllocateResourceGroup(VulkanResourceGroupLayout* layout)
{
    {
        std::shared_lock readerLock(mResourceGroupsMutex);
        auto nextIndex = mUsedResourceGroupCount.fetch_add(1, std::memory_order_relaxed);
        if (nextIndex < static_cast<UInt32>(mResourceGroups.size()))
        {
            auto result = mResourceGroups[nextIndex].get();
            result->Initialize(layout);
            return result;
        }
    }

    {
        std::unique_lock writerLock(mResourceGroupsMutex);
        auto resourceGroup = std::make_unique<VulkanResourceGroup>(layout, this);
        auto result = mResourceGroups.emplace_back(std::move(resourceGroup)).get();
        mUsedResourceGroupCount.store(static_cast<UInt32>(mResourceGroups.size()), std::memory_order_relaxed);
        return result;
    }
}

void cross::VulkanResourceGroupPool::OnBeginFrame(FrameParam* frameparam)
{
    mUsedResourceGroupCount.store(0, std::memory_order_relaxed);
    mFramePool = cross::EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    SCOPED_CPU_TIMING(GroupRendering, "DescriptorSetPool::BeginFrame");

    // merge DescriptorSet to mReadyDescriptorSetPools
    {
        SCOPED_CPU_TIMING(GroupRendering, "merge DescriptorSet to mReadyDescriptorSetPools");
        for (auto& creator : mDescriptorSetCreators)
        {
            DescriptorSetPoolType descriptorSetPool = creator.ReleaseDescriptorSetPool();
            for (auto& [key, descriptorSetList] : descriptorSetPool)
            {
                mReadyDescriptorSetPools.try_emplace(key, std::list<DescriptorSet>{});
                auto& ready_list = mReadyDescriptorSetPools[key];
                ready_list.splice(ready_list.end(), descriptorSetList);
            }
        }
    }

    std::unordered_map<DescriptorSetCreator*, std::vector<VkDescriptorSet>> freeDescriptorSets;

    {
        SCOPED_CPU_TIMING(GroupRendering, "destroy DescriptorSet out of life");

        // destroy DescriptorSet out of life
        UInt32 frameID = GetFrameId();
        UInt32 maxLifeTime = Align(MAX_DESCRIPTOR_SET_LIFE_TIME, CmdSettings::Inst().gMaxQueuedFrame);
        for (auto similarSetsItr = mReadyDescriptorSetPools.begin(); similarSetsItr != mReadyDescriptorSetPools.end();)
        {
            auto& similarDescriptorSets = similarSetsItr->second;
            // descriptor set with same resouce bindings except dynamic offsets
            for (auto setItr = similarDescriptorSets.begin(); setItr != similarDescriptorSets.end();)
            {
                if (setItr->RefCount == 0)
                {
                    Assert(setItr->Lifetime);
                    if (frameID - setItr->Lifetime > maxLifeTime)
                    {
                        freeDescriptorSets[setItr->Pool].emplace_back(setItr->Set);
                        setItr = similarDescriptorSets.erase(setItr);
                        continue;
                    }
                }
                ++setItr;
            }
            if (similarDescriptorSets.empty())
            {
                similarSetsItr = mReadyDescriptorSetPools.erase(similarSetsItr);
            }
            else
            {
                ++similarSetsItr;
            }
        }
    }

    for (auto& [pool, descriptorSets] : freeDescriptorSets)
    {
        pool->FreeDescriptorSet(GetDevice<VulkanDevice>()->Get(), descriptorSets);
    }


    std::random_device rd;
    std::mt19937 g(rd());

    std::shuffle(mThreadIndexMapping.begin(), mThreadIndexMapping.end(), g);
}

cross::VulkanRenderPass::VulkanRenderPass(const NGIRenderPassDesc& desc, VulkanDevice* device)
    : NGIRenderPass{desc, device}
{
    auto* vkDevice = GetDevice<VulkanDevice>();

    auto sampleCount = MapSampleCount(desc.SampleCount);
    std::array<VkAttachmentDescription2, (MaxSupportedRenderTargets + 1) * 2> attaches;
    VkAttachmentDescriptionStencilLayout stencilAttachLayout{ VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_STENCIL_LAYOUT, };
    VkAttachmentDescriptionStencilLayout stencilResolveAttachLayout{ VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_STENCIL_LAYOUT, };

    auto attachItr = attaches.begin();
    auto resolveAttachItr = attachItr + desc.ColorTargetCount + (desc.HasDepthStencil ? 1 : 0);

    std::array<VkAttachmentReference2, MaxSupportedRenderTargets> colorResolveRefs;
    VkSubpassDescriptionDepthStencilResolve depthStencilResolve{ VK_STRUCTURE_TYPE_SUBPASS_DESCRIPTION_DEPTH_STENCIL_RESOLVE, };
    VkAttachmentReference2 depthStencilResolveRef{ VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2, };
    VkAttachmentReferenceStencilLayout stencilResolveRefLayout{ VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_STENCIL_LAYOUT, };

    for (auto i = 0u; i < desc.ColorTargetCount; i++)
    {
        const auto& colorTarget = desc.ColorTargets[i];
        *attachItr++ = {
            VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_2_KHR,
            nullptr,
            0,
            MapGraphicsFormat(colorTarget.Format),
            sampleCount,
            MapLoadOp(colorTarget.LoadOp),
            MapStoreOp(colorTarget.StoreOp),
            VK_ATTACHMENT_LOAD_OP_DONT_CARE,
            VK_ATTACHMENT_STORE_OP_DONT_CARE,
            VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
            VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
        };
        if (EnumHasAnyFlags(colorTarget.StoreOp, NGIStoreOp::Resolve))
        {
            colorResolveRefs[i] = {
                VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                nullptr,
                static_cast<uint32_t>(std::distance(attaches.begin(), resolveAttachItr)),
                VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
                VK_IMAGE_ASPECT_COLOR_BIT,
            };
            *resolveAttachItr++ = {
                VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_2_KHR,
                nullptr,
                0,
                MapGraphicsFormat(colorTarget.Format),
                VK_SAMPLE_COUNT_1_BIT,
                VK_ATTACHMENT_LOAD_OP_DONT_CARE,
                VK_ATTACHMENT_STORE_OP_STORE,
                VK_ATTACHMENT_LOAD_OP_DONT_CARE,
                VK_ATTACHMENT_STORE_OP_DONT_CARE,
                VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
                VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
            };
        }
        else
        {
            colorResolveRefs[i] = {
                VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                nullptr,
                VK_ATTACHMENT_UNUSED,
                VK_IMAGE_LAYOUT_UNDEFINED,
                0,
            };
        }
    }

    auto resolveDepthStencil = false;

    if (desc.HasDepthStencil)
    {
        auto& attachDesc = *attachItr++;
        attachDesc = {
            VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_2,
            nullptr,
            0,
            MapGraphicsFormat(desc.DepthStencilFormat),
            sampleCount,
            MapLoadOp(desc.DepthLoadOp),
            MapStoreOp(desc.DepthStoreOp),
            MapLoadOp(desc.StencilLoadOp),
            MapStoreOp(desc.StencilStoreOp),
        };

        auto [hasDepth, hasStencil] = FormatHasDepthStencil(desc.DepthStencilFormat);
        if (hasDepth)
        {
            attachDesc.initialLayout = attachDesc.finalLayout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
            if (hasStencil)
            {
                stencilAttachLayout.stencilInitialLayout = stencilAttachLayout.stencilFinalLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                attachDesc.pNext = &stencilAttachLayout;
            }
        }
        else if (hasStencil)
        {
            attachDesc.initialLayout = attachDesc.finalLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
        }
        else
        {
            Assert(false);
        }

        VkImageAspectFlags depthStencilResolveAspect = 0;
        if (EnumHasAnyFlags(desc.DepthStoreOp, NGIStoreOp::Resolve))
        {
            depthStencilResolveAspect |= VK_IMAGE_ASPECT_DEPTH_BIT;
        }
        if (EnumHasAnyFlags(desc.StencilStoreOp, NGIStoreOp::Resolve))
        {
            depthStencilResolveAspect |= VK_IMAGE_ASPECT_STENCIL_BIT;
        }
        if (depthStencilResolveAspect)
        {
            auto& vulkan12Props = device->mPhysicalDeviceInfo->mVulkan12Props;
            if (depthStencilResolve.depthResolveMode && !(vulkan12Props.supportedDepthResolveModes & depthStencilResolve.depthResolveMode))
            {
                LOG_ERROR("Use unsupported depth resolve mode");
            }
            if (depthStencilResolve.stencilResolveMode && !(vulkan12Props.supportedStencilResolveModes & depthStencilResolve.stencilResolveMode))
            {
                LOG_ERROR("Use unsupported stencil resolve mode");
            }

            if (depthStencilResolve.depthResolveMode != depthStencilResolve.stencilResolveMode)
            {
                if (vulkan12Props.independentResolveNone)
                {
                    if (!vulkan12Props.independentResolve)
                    {
                        if (depthStencilResolve.depthResolveMode != VK_RESOLVE_MODE_NONE_KHR && depthStencilResolve.stencilResolveMode != VK_RESOLVE_MODE_NONE_KHR)
                        {
                            LOG_ERROR("DepthResolveMode or StencilResolveMode must be specified as NGIResolveMode::DontResolve or the same resolve Mode");
                        }
                    }
                }
                else
                {
                    LOG_ERROR("Independent resolve was not supported");
                }
            }

            depthStencilResolve = {
                VK_STRUCTURE_TYPE_SUBPASS_DESCRIPTION_DEPTH_STENCIL_RESOLVE,
                nullptr,
                MapResolveMode(desc.DepthResolveType),
                MapResolveMode(desc.StencilResolveType),
                &depthStencilResolveRef,
            };

            depthStencilResolveRef.attachment = static_cast<uint32_t>(std::distance(attaches.begin(), resolveAttachItr));
            depthStencilResolveRef.aspectMask = depthStencilResolveAspect;

            auto& resolveAttach = *resolveAttachItr++;
            resolveAttach = {
                VK_STRUCTURE_TYPE_ATTACHMENT_DESCRIPTION_2,
                nullptr,
                0,
                MapGraphicsFormat(desc.DepthStencilFormat),
                VK_SAMPLE_COUNT_1_BIT,
                VK_ATTACHMENT_LOAD_OP_DONT_CARE,
                EnumHasAnyFlags(desc.DepthStoreOp, NGIStoreOp::Resolve) ? VK_ATTACHMENT_STORE_OP_STORE : VK_ATTACHMENT_STORE_OP_DONT_CARE,
                VK_ATTACHMENT_LOAD_OP_DONT_CARE,
                EnumHasAnyFlags(desc.StencilStoreOp, NGIStoreOp::Resolve) ? VK_ATTACHMENT_STORE_OP_STORE : VK_ATTACHMENT_STORE_OP_DONT_CARE,
            };

            if (hasDepth)
            {
                resolveAttach.initialLayout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                resolveAttach.finalLayout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                depthStencilResolveRef.layout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                if (hasStencil)
                {
                    resolveAttach.pNext = &stencilResolveAttachLayout;
                    stencilResolveAttachLayout.stencilInitialLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                    stencilResolveAttachLayout.stencilFinalLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;

                    depthStencilResolveRef.pNext = &stencilResolveRefLayout;
                    stencilResolveRefLayout.stencilLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                }
            }
            else if (hasStencil)
            {
                resolveAttach.initialLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                resolveAttach.finalLayout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                depthStencilResolveRef.layout = VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
            }
            else
            {
                Assert(false);
            }

            resolveDepthStencil = true;
        }
    }

    struct SubpassAttachRef
    {
        std::array<VkAttachmentReference2, MaxSupportedRenderTargets + 1> inputRefs;
        std::array<VkAttachmentReference2, MaxSupportedRenderTargets> colorRefs;
        VkAttachmentReference2 depthStencilRef{ VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2, };
        VkAttachmentReferenceStencilLayout stencilRefLayout{ VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_STENCIL_LAYOUT, };

        // perserved ref not calcualted yet
        uint32_t preserveRefCount = 0;
        std::array<uint32_t, MaxSupportedRenderTargets + 1> preserveRefs;
    };

    std::array<SubpassAttachRef, MaxSupportedSubpasses> subpassRefs;

    std::array<VkSubpassDescription2KHR, MaxSupportedSubpasses> subpasses;

    std::vector<VkSubpassDependency2KHR> dependencies;

    std::array<UInt32, MaxSupportedRenderTargets + 1> lastWriters{};
    std::fill(lastWriters.begin(), lastWriters.end(), UINT32_MAX);

    // two kinds of barriers
    // 1. RW -> R
    // 2. RW -> RW

    for (UInt32 subpassIdx = 0; subpassIdx < desc.SubpassCount; subpassIdx++)
    {
        const auto& subpass = desc.Subpasses[subpassIdx];
        auto& subpassRef = subpassRefs[subpassIdx];

        // handle input attachments
        auto depthAsInput = false;
        auto stencilAsInput = false;

        for (auto inputAttachIdx = 0u; inputAttachIdx < subpass.InputAttachmentCount; inputAttachIdx++)
        {
            switch (auto inputAttach = subpass.InputAttachments[inputAttachIdx]; inputAttach)
            {
            case NGIRenderPassTargetIndex::TargetDepth:
            {
                depthAsInput = true;
                subpassRef.inputRefs[inputAttachIdx] = {
                    VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                    nullptr,
                    desc.ColorTargetCount,
                    VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL,
                    VK_IMAGE_ASPECT_DEPTH_BIT,
                };

                Assert(lastWriters[desc.ColorTargetCount] != UINT32_MAX);
                dependencies.push_back({
                    VK_STRUCTURE_TYPE_SUBPASS_DEPENDENCY_2_KHR,
                    nullptr,
                    lastWriters[desc.ColorTargetCount],
                    subpassIdx,
                    VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT,
                    VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
                    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT,
                    VK_ACCESS_SHADER_READ_BIT,
                    VK_DEPENDENCY_BY_REGION_BIT,
                });
                break;
            }
            case NGIRenderPassTargetIndex::TargetStencil:
            {
                stencilAsInput = true;
                subpassRef.inputRefs[inputAttachIdx] = {
                    VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                    nullptr,
                    desc.ColorTargetCount,
                    VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL,
                    VK_IMAGE_ASPECT_STENCIL_BIT,
                };

                Assert(lastWriters[desc.ColorTargetCount] != UINT32_MAX);
                dependencies.push_back({
                    VK_STRUCTURE_TYPE_SUBPASS_DEPENDENCY_2_KHR,
                    nullptr,
                    lastWriters[desc.ColorTargetCount],
                    subpassIdx,
                    VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT,
                    VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
                    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT,
                    VK_ACCESS_SHADER_READ_BIT,
                    VK_DEPENDENCY_BY_REGION_BIT,
                });
                break;
            }
            default:
            {
                auto targetIndex = ToUnderlying(inputAttach);
                subpassRef.inputRefs[inputAttachIdx] = {
                    VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                    nullptr,
                    targetIndex,
                    VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL,
                    VK_IMAGE_ASPECT_COLOR_BIT,
                };

                Assert(lastWriters[targetIndex] != UINT32_MAX);
                dependencies.push_back({
                    VK_STRUCTURE_TYPE_SUBPASS_DEPENDENCY_2_KHR,
                    nullptr,
                    lastWriters[targetIndex],
                    subpassIdx,
                    VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT,
                    VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
                    VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT,
                    VK_ACCESS_SHADER_READ_BIT,
                    VK_DEPENDENCY_BY_REGION_BIT,
                });
                break;
            }
            }
        }

        // handle color attachments
        for (UInt32 colorAttachIdx = 0; colorAttachIdx < subpass.ColorAttachmentCount; ++colorAttachIdx)
        {
            auto targetIndex = ToUnderlying(subpass.ColorAttachments[colorAttachIdx]);
            subpassRef.colorRefs[colorAttachIdx] = {
                VK_STRUCTURE_TYPE_ATTACHMENT_REFERENCE_2_KHR,
                nullptr,
                targetIndex,
                VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL,
                VK_IMAGE_ASPECT_COLOR_BIT,
            };

            if (lastWriters[targetIndex] != UINT32_MAX)
            {
                dependencies.push_back({
                    VK_STRUCTURE_TYPE_SUBPASS_DEPENDENCY_2_KHR,
                    nullptr,
                    lastWriters[targetIndex],
                    subpassIdx,
                    VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT,
                    VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT,
                    VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT,
                    VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT,
                    VK_DEPENDENCY_BY_REGION_BIT,
                });
            }
            lastWriters[targetIndex] = subpassIdx;
        }

        // handle depth/stencil attachment
        if (subpass.NeedDepthStencil)
        {
            auto [hasDepth, hasStencil] = FormatHasDepthStencil(desc.DepthStencilFormat);
            auto depthReadOnly = subpass.DepthReadOnly || depthAsInput;
            auto stencilReadOnly = subpass.StencilReadOnly || stencilAsInput;

            subpassRef.depthStencilRef.attachment = desc.ColorTargetCount;
            if (hasDepth)
            {
                subpassRef.depthStencilRef.aspectMask |= VK_IMAGE_ASPECT_DEPTH_BIT;
            }
            if (hasStencil)
            {
                subpassRef.depthStencilRef.aspectMask |= VK_IMAGE_ASPECT_STENCIL_BIT;
            }

            if (hasDepth)
            {
                subpassRef.depthStencilRef.layout = depthReadOnly ? VK_IMAGE_LAYOUT_DEPTH_READ_ONLY_OPTIMAL : VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_OPTIMAL;
                if (hasStencil)
                {
                    subpassRef.depthStencilRef.pNext = &subpassRef.stencilRefLayout;
                    subpassRef.stencilRefLayout.stencilLayout = stencilReadOnly ? VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL : VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
                }
            }
            else if (hasStencil)
            {
                subpassRef.depthStencilRef.layout = stencilReadOnly ? VK_IMAGE_LAYOUT_STENCIL_READ_ONLY_OPTIMAL : VK_IMAGE_LAYOUT_STENCIL_ATTACHMENT_OPTIMAL;
            }
            else
            {
                Assert(false);
            }

            if (lastWriters[desc.ColorTargetCount] != UINT32_MAX)
            {
                dependencies.push_back({
                    VK_STRUCTURE_TYPE_SUBPASS_DEPENDENCY_2_KHR,
                    nullptr,
                    lastWriters[desc.ColorTargetCount],
                    subpassIdx,
                    VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT,
                    VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT | VK_PIPELINE_STAGE_LATE_FRAGMENT_TESTS_BIT,
                    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT,
                    VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_READ_BIT | VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT,
                    VK_DEPENDENCY_BY_REGION_BIT,
                });
            }
            lastWriters[desc.ColorTargetCount] = subpassIdx;
        }

        auto isLastSubpass = subpassIdx == desc.SubpassCount - 1u;

        subpasses[subpassIdx] = {
            VK_STRUCTURE_TYPE_SUBPASS_DESCRIPTION_2_KHR,
            isLastSubpass && resolveDepthStencil ? &depthStencilResolve : nullptr,
            0,
            VK_PIPELINE_BIND_POINT_GRAPHICS,
            0,
            subpass.InputAttachmentCount,
            subpassRef.inputRefs.data(),
            subpass.ColorAttachmentCount,
            subpassRef.colorRefs.data(),
            isLastSubpass ? colorResolveRefs.data() : nullptr,
            subpass.NeedDepthStencil ? &subpassRef.depthStencilRef : nullptr,
            subpassRef.preserveRefCount,
            subpassRef.preserveRefs.data(),
        };
    }

    // merge dependencies
    std::sort(dependencies.begin(), dependencies.end(), [](auto& a, auto& b) { return a.srcSubpass != b.srcSubpass ? a.srcSubpass < b.srcSubpass : a.dstSubpass < b.dstSubpass; });
    auto lastDepItr = dependencies.begin();
    UInt32 depCount = 0;
    for (auto itr = dependencies.begin(); itr != dependencies.end(); ++itr)
    {
        if (itr->srcSubpass == lastDepItr->srcSubpass && itr->dstSubpass == lastDepItr->dstSubpass)
        {
            lastDepItr->srcStageMask |= itr->srcStageMask;
            lastDepItr->dstStageMask |= itr->dstStageMask;
            lastDepItr->srcAccessMask |= itr->srcAccessMask;
            lastDepItr->dstAccessMask |= itr->dstAccessMask;
            depCount = static_cast<UInt32>(std::distance(dependencies.begin(), lastDepItr)) + 1;
        }
        else
        {
            ++lastDepItr;
            *lastDepItr = *itr;
            ++depCount;
        }
    }

    VkRenderPassCreateInfo2KHR createInfo{
        VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO_2_KHR,
        nullptr,
        0,
        static_cast<uint32_t>(std::distance(attaches.begin(), resolveAttachItr)),
        attaches.data(),
        desc.SubpassCount,
        subpasses.data(),
        depCount,
        dependencies.data(),
    };

    VK_CHECK(vkCreateRenderPass2KHR(vkDevice->Get(), &createInfo, gVkAllocCallback, &mRenderPass));

    NGI_LOG_DEBUG("Create render pass: {}", VkFormatHandle(mRenderPass));
}

cross::VulkanRenderPass::~VulkanRenderPass()
{
    NGI_LOG_DEBUG("Destroy render pass: {}", VkFormatHandle(mRenderPass));

    vkDestroyRenderPass(GetDevice<VulkanDevice>()->Get(), mRenderPass, gVkAllocCallback);
}

void cross::VulkanRenderPass::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_RENDER_PASS, mRenderPass, pDebugName);
}

cross::VulkanGraphicsPipelineState::VulkanGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc, VulkanPipelineStatePool* pool)
    : NGIGraphicsPipelineState{desc, pool }
{
    using namespace CrossSchema;   // safe

    QUICK_SCOPED_CPU_TIMING(__FUNCTION__);

    NGI_LOG_DEBUG("Create graphics pipeline with program hash: {}-{}", desc.ProgramGUID.high, desc.ProgramGUID.low);

    std::vector<std::tuple<VkSpecializationInfo, std::vector<VkSpecializationMapEntry>>> constantInfo;
    auto& layoutDesc = desc.PipelineLayout->GetDesc();
    std::bitset<32> constantStageMask;
    if (layoutDesc.ConstantCount)
    {
        UInt32 compactSize = 0;
        for (UInt32 i = 0; i < layoutDesc.ConstantCount; i++)
        {
            auto& constantDesc = layoutDesc.Constants[i];
            constantStageMask |= constantDesc.StageMask;
            compactSize = (std::max)(compactSize, constantDesc.Offset + constantDesc.Size);
        }
        constantInfo.reserve(constantStageMask.count());
        Assert(desc.ConstantData);
        Assert(desc.ConstantDataSize >= compactSize);
    }
    else
    {
        Assert(!desc.ConstantData && !desc.ConstantDataSize);
    }

    std::vector<VkPipelineShaderStageCreateInfo> shaderStageInfos;
    auto AddShaderStage = [&](const NGIShaderCodeDesc& shaderCode, CrossSchema::ShaderStageBit stage) {
        if (shaderCode.Data && shaderCode.ByteSize)
        {
            VkSpecializationInfo* pSpecInfo = nullptr;
            if (auto stageBit = ToUnderlying(stage); layoutDesc.ConstantCount && (constantStageMask.to_ulong() & stageBit))
            {
                std::vector<VkSpecializationMapEntry> entries;
                for (UInt32 i = 0; i < layoutDesc.ConstantCount; i++)
                {
                    auto& constant = layoutDesc.Constants[i];
                    if (constant.StageMask & stageBit)
                    {
                        entries.push_back({
                            constant.Index,
                            constant.Offset,
                            constant.Size,
                        });
                    }
                }
                VkSpecializationInfo specInfo{
                    static_cast<UInt32>(entries.size()),
                    entries.data(),
                    desc.ConstantDataSize,
                    desc.ConstantData,
                };
                pSpecInfo = &std::get<0>(constantInfo.emplace_back(specInfo, std::move(entries)));
            }
            VkPipelineShaderStageCreateInfo shaderInfo{
                VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
                nullptr,
                0,
                MapShaderStage(stage),
                VK_NULL_HANDLE,
                shaderCode.EntryPoint,
                pSpecInfo,
            };
            VkShaderModuleCreateInfo shaderModuleCreateInfo{
                VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
                nullptr,
                0,
                shaderCode.ByteSize,
                reinterpret_cast<const uint32_t*>(shaderCode.Data),
            };
            VK_CHECK(vkCreateShaderModule(GetDevice<VulkanDevice>()->Get(), &shaderModuleCreateInfo, gVkAllocCallback, &shaderInfo.module));

            auto moduleName = fmt::format("{}-{}", desc.Program->FilePath, vk::to_string(static_cast<vk::ShaderStageFlagBits>(shaderInfo.stage)));
            GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_SHADER_MODULE, shaderInfo.module, moduleName.c_str());

            shaderStageInfos.emplace_back(shaderInfo);

#if NGI_ENABLE_GPU_DUMP
            if (GetDevice<VulkanDevice>()->GpuDump())
            {
                GetDevice<VulkanDevice>()->GetGPUCrashTracker().AddShaderBinary(shaderCode, desc.Program->FilePath);
            }
#endif
        }
    };

    AddShaderStage(desc.Program->VertexShader, ShaderStageBit::Vertex);
    AddShaderStage(desc.Program->HullShader, ShaderStageBit::Hull);
    AddShaderStage(desc.Program->DomainShader, ShaderStageBit::Domain);
    AddShaderStage(desc.Program->GeometryShader, ShaderStageBit::Geometry);
    AddShaderStage(desc.Program->PixelShader, ShaderStageBit::Pixel);

    auto& inputLayoutDesc = desc.InputLayout;
    VkVertexInputBindingDescription vertBindings[MaxVertexStreams];
    UInt32 vertBindingCount = inputLayoutDesc->GetStreamCount();
    VkVertexInputAttributeDescription vertAttribs[MaxVertexChannelCount];
    UInt32 vertAttribCount = 0;
    UInt32 nonInstanceVertAttribCount = 0;
    std::vector<UInt32> nonInstanceVertexAtrribs;
    for (UInt8 streamIdx = 0; streamIdx < inputLayoutDesc->GetStreamCount(); ++streamIdx)
    {
        auto& stream = inputLayoutDesc->GetVertexStreamLayout(streamIdx);
        vertBindings[streamIdx] =
        {
            streamIdx,
            stream.GetVertexStride(),
            MapVertexFrequency(stream.GetFrequency()),
        };

        auto vertAttribsBegin = std::begin(desc.Program->VertexAttributes);
        auto vertAttribsEnd = vertAttribsBegin + desc.Program->VertexAttributeCount;

        for (UInt32 channelIdx = 0; channelIdx < stream.GetChannelCount(); ++channelIdx)
        {
            auto& channel = stream.GetChannelLayout(channelIdx);
            if (channel.mFixedLocation != std::numeric_limits<UInt32>::max())
            {
                vertAttribs[vertAttribCount++] = {
                    channel.mFixedLocation,
                    streamIdx,
                    MapVertexFormat(channel.mFormat),
                    channel.mOffset,
                };
            }
            else if (auto ret = std::find_if(vertAttribsBegin, vertAttribsEnd, [&](auto& attrib) { return attrib.ChannelName == channel.mChannelName; }); ret != vertAttribsEnd)
            {
                vertAttribs[vertAttribCount++] = {
                    ret->Location,
                    streamIdx,
                    MapVertexFormat(channel.mFormat),
                    channel.mOffset,
                };
                nonInstanceVertexAtrribs.push_back(vertAttribCount - 1);
                ++nonInstanceVertAttribCount;
            }
        }
    }

    if (nonInstanceVertAttribCount < desc.Program->VertexAttributeCount)
    {
        //LOG_WARN("Vertex attributes declared {} in shader don't match those declared in mesh! {}", desc.Program->VertexAttributeCount, nonInstanceVertAttribCount);

        vertBindings[vertBindingCount] =
        {
            vertBindingCount,
            0,
            VK_VERTEX_INPUT_RATE_VERTEX,
        };

        auto* vertAttribsBegin = vertAttribs;
        auto* vertAttribsEnd = vertAttribs + vertAttribCount;

        for (UInt32 i = 0; i < desc.Program->VertexAttributeCount; ++i)
        {
            auto& shaderAttrib = desc.Program->VertexAttributes[i];
            if (std::none_of(vertAttribsBegin, vertAttribsEnd, [&](auto& attrib) { return shaderAttrib.Location == attrib.location; }))
            {
                VkVertexInputAttributeDescription vertAttrib
                {
                    shaderAttrib.Location,
                    vertBindingCount,
                };
                switch (GetSemantic(shaderAttrib.ChannelName))
                {
                case VertexSemantic::SemanticPosition:
                case VertexSemantic::SemanticPositionT:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, Position));
                    break;
                case VertexSemantic::SemanticNormal:
                    vertAttrib.format = VK_FORMAT_R32G32B32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, Normal));
                    break;
                case VertexSemantic::SemanticTangent:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, Tangent));
                    break;
                case VertexSemantic::SemanticBiNormal:
                    vertAttrib.format = VK_FORMAT_R32G32B32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, Binormal));
                    break;
                case VertexSemantic::SemanticBlendIndex:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_UINT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, BlendIndex));
                    break;
                case VertexSemantic::SemanticBlendWeight:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, BlendWeight));
                    break;
                case VertexSemantic::SemanticColor:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, Color));
                    break;
                case VertexSemantic::SemanticTexCoord:
                    vertAttrib.format = VK_FORMAT_R32G32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, TexCoord));
                    break;
                case VertexSemantic::SemanticQuatTan:
                    vertAttrib.format = VK_FORMAT_R32G32B32A32_SFLOAT;
                    vertAttrib.offset = static_cast<uint32_t>(offsetof(DefaultVertex, QuatTan));
                    break;
                case VertexSemantic::SemanticPSize:
                case VertexSemantic::SemanticTessFactor:
                case VertexSemantic::SemanticInstance:
                default:
                    Assert(false);
                    break;
                }
                vertAttribs[vertAttribCount++] = vertAttrib;
            }
        }
        ++vertBindingCount;
    }

    VkPipelineVertexInputStateCreateInfo vertexInput{
        VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO,
        nullptr,
        0,
        vertBindingCount,
        vertBindings,
        vertAttribCount,
        vertAttribs,
    };

    VkPipelineInputAssemblyStateCreateInfo inputAssembly{
        VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO,
        nullptr,
        0,
        MapPrimitiveTopology(desc.Topology),
        VK_FALSE,
    };
    // change the topology of Tes stage pipeline
    if (desc.Program->HullShader.ByteSize)
    {
        inputAssembly.topology = VK_PRIMITIVE_TOPOLOGY_PATCH_LIST;
    }

    VkPipelineViewportStateCreateInfo viewport{
        VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO,
        nullptr,
        0,
        1,
        nullptr,
        1,
        nullptr,
    };

    // TODO(peterwjma): VK does not support disable depth clip
    auto& rpDesc = desc.RenderPass->GetDesc();
    auto& rsDesc = desc.RasterizationState;

    // Requires a device that supports the VK_EXT_conservative_rasterization extension for conservative rasterization
    VkPipelineRasterizationConservativeStateCreateInfoEXT conservativeRasterStateCI{};
    if (rsDesc.RasterMode == RasterizationMode::ConservativeRaster)
    {
        conservativeRasterStateCI.sType = VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_CONSERVATIVE_STATE_CREATE_INFO_EXT;
        conservativeRasterStateCI.conservativeRasterizationMode = VK_CONSERVATIVE_RASTERIZATION_MODE_OVERESTIMATE_EXT;
        conservativeRasterStateCI.extraPrimitiveOverestimationSize =
            std::clamp(rsDesc.RasterOverestimationSize * 1.0f, 0.0f, GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mConservativeRasterizationProps.maxExtraPrimitiveOverestimationSize);
        // Conservative rasterization state has to be chained into the pipeline rasterization state create info structure
        conservativeRasterStateCI.pNext = nullptr;
    }

    VkPipelineRasterizationStateCreateInfo rasterization{
        VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO,
        rsDesc.RasterMode == RasterizationMode::ConservativeRaster ? static_cast<void*>(&conservativeRasterStateCI) : nullptr,
        0,
        // TODO(peterwjma): control depth clip by clamp was a temporary solotion https://iwiki.woa.com/pages/viewpage.action?pageId=1394687189
        rsDesc.EnableDepthClip ? static_cast<VkBool32>(VK_FALSE) : static_cast<VkBool32>(VK_TRUE),
        VK_FALSE,
        rsDesc.FillMode == FillMode::Solid ? VK_POLYGON_MODE_FILL : VK_POLYGON_MODE_LINE,
        MapCullMode(rsDesc.CullMode),
        rsDesc.FaceOrder == FaceOrder::CCW ? VK_FRONT_FACE_COUNTER_CLOCKWISE : VK_FRONT_FACE_CLOCKWISE,
        MapBool(rsDesc.EnableDepthBias),
        static_cast<float>(rsDesc.DepthBias),
        rsDesc.DepthBiasClamp,
        rsDesc.SlopeScaledDepthBias,
        rsDesc.LineWidth,
    };

    VkPipelineMultisampleStateCreateInfo multisample{
        VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO,
        nullptr,
        0,
        MapSampleCount(rpDesc.SampleCount),
        VK_FALSE,
        0.f,
        nullptr,
        VK_FALSE,
        VK_FALSE,
    };

    // TODO(peterwjma): depth bound test
    // TODO(peterwjma): stencil reference
    auto& dsDesc = desc.DepthStencilState;
    VkPipelineDepthStencilStateCreateInfo depthStencil{
        VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO,
        nullptr,
        0,
        MapBool(dsDesc.EnableDepth),
        MapBool(dsDesc.EnableDepthWrite),
        MapComparisonOp(dsDesc.DepthCompareOp),
        VK_FALSE,
        MapBool(dsDesc.EnableStencil),
        {
            MapStencilOp(dsDesc.FrontFace.StencilFailOp),
            MapStencilOp(dsDesc.FrontFace.StencilPassOp),
            MapStencilOp(dsDesc.FrontFace.StencilDepthFailOp),
            MapComparisonOp(dsDesc.FrontFace.StencilCompareOp),
            dsDesc.StencilReadMask,
            dsDesc.StencilWriteMask,
            0,
        },
        {
            MapStencilOp(dsDesc.BackFace.StencilFailOp),
            MapStencilOp(dsDesc.BackFace.StencilPassOp),
            MapStencilOp(dsDesc.BackFace.StencilDepthFailOp),
            MapComparisonOp(dsDesc.BackFace.StencilCompareOp),
            dsDesc.StencilReadMask,
            dsDesc.StencilWriteMask,
            0,
        },
        0.f,
        0.f,
    };

    // TODO(peterwjma): VK does not support separate logic op
    const auto& blendDesc = desc.BlendState;
    VkPipelineColorBlendAttachmentState blends[MaxSupportedRenderTargets]{};
    std::transform(blendDesc.TargetBlendState, blendDesc.TargetBlendState + blendDesc.TargetCount, blends, [](const NGITargetBlendStateDesc& desc) {
        return VkPipelineColorBlendAttachmentState{
            MapBool(desc.EnableBlend),
            MapBlendFactor(desc.SrcBlend, false),
            MapBlendFactor(desc.DestBlend, false),
            MapBlendOp(desc.BlendOp),
            MapBlendFactor(desc.SrcBlendAlpha, true),
            MapBlendFactor(desc.DestBlendAlpha, true),
            MapBlendOp(desc.BlendOpAlpha),
            MapColorMask(desc.WriteMask),
        };
    });
    // if count of blend targets less than color targets in render pass, just make them equal
    auto& spDesc = rpDesc.Subpasses[desc.Subpass];
    if (blendDesc.TargetCount < spDesc.ColorAttachmentCount)
    {
        NGI_LOG_WARN("{} blend target descs supplied than {} target desc in render pass, will make them equal", blendDesc.TargetCount, spDesc.ColorAttachmentCount);
        std::transform(spDesc.ColorAttachments + blendDesc.TargetCount, spDesc.ColorAttachments + spDesc.ColorAttachmentCount, blends + blendDesc.TargetCount, [&](auto index) {
            return VkPipelineColorBlendAttachmentState{
                VK_FALSE,
                VK_BLEND_FACTOR_ONE,
                VK_BLEND_FACTOR_ZERO,
                VK_BLEND_OP_ADD,
                VK_BLEND_FACTOR_ONE,
                VK_BLEND_FACTOR_ZERO,
                VK_BLEND_OP_ADD,
                VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT | VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT,
            };
        });
    }

    VkPipelineColorBlendStateCreateInfo colorBlend{
        VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO,
        nullptr,
        0,
        VK_FALSE,
        VK_LOGIC_OP_CLEAR,
        spDesc.ColorAttachmentCount,
        blends,
        {
            0.f,
            0.f,
            0.f,
            0.f,
        },
    };

    // TODO(peterwjma): add more dynamic state
    UInt32 dynamicStateCount = 2;
    VkDynamicState dynamicStates[16]
    {
        VK_DYNAMIC_STATE_VIEWPORT,
        VK_DYNAMIC_STATE_SCISSOR,
    };

    if (dsDesc.EnableStencil)
    {
        dynamicStates[dynamicStateCount++] = VK_DYNAMIC_STATE_STENCIL_REFERENCE;
    }

    VkPipelineDynamicStateCreateInfo dynamic{
        VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO,
        nullptr,
        0,
        dynamicStateCount,
        dynamicStates,
    };

    // tes state info
    VkPipelineTessellationStateCreateInfo tessellationState{
        VK_STRUCTURE_TYPE_PIPELINE_TESSELLATION_STATE_CREATE_INFO,
        nullptr,
        0,
        3
    };

    // TODO(peterwjma): pipeline cache
    VkGraphicsPipelineCreateInfo createInfo{
        VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO,
        nullptr,
        0,
        static_cast<UInt32>(shaderStageInfos.size()),
        shaderStageInfos.data(),
        &vertexInput,
        &inputAssembly,
        &tessellationState,
        &viewport,
        &rasterization,
        &multisample,
        &depthStencil,
        &colorBlend,
        &dynamic,
        rhi_cast<VulkanPipelineLayout*>(desc.PipelineLayout)->mPipelineLayout,
        rhi_cast<VulkanRenderPass*>(desc.RenderPass)->mRenderPass,
        desc.Subpass,
        VK_NULL_HANDLE,
        0,
    };
    {
        QUICK_SCOPED_CPU_TIMING("vkCreateGraphicsPipelines");

        VK_CHECK(vkCreateGraphicsPipelines(GetDevice<VulkanDevice>()->Get(), pool->mCache, 1, &createInfo, gVkAllocCallback, &mPipeline));

        GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_PIPELINE, mPipeline, mDesc.Program->FilePath);

        for (auto& shaderInfo : shaderStageInfos)
        {
            vkDestroyShaderModule(GetDevice<VulkanDevice>()->Get(), shaderInfo.module, gVkAllocCallback);
        }
    }
}

cross::VulkanGraphicsPipelineState::~VulkanGraphicsPipelineState()
{
    NGI_LOG_DEBUG("Detroy graphics pipeline");
    vkDestroyPipeline(GetDevice<VulkanDevice>()->Get(), mPipeline, gVkAllocCallback);
}

cross::VulkanPipelineStatePool::VulkanPipelineStatePool(const NGIPipelineStatePoolDesc& desc, VulkanDevice* pDevice)
    : NGIPipelineStatePool{pDevice}
{
    VkPipelineCacheCreateInfo info
    {
        VK_STRUCTURE_TYPE_PIPELINE_CACHE_CREATE_INFO,
        nullptr,
        0,
        static_cast<size_t>(desc.InitialDataSize),
        desc.InitialData,
    };
    VK_CHECK(vkCreatePipelineCache(GetDevice<VulkanDevice>()->Get(), &info, gVkAllocCallback, &mCache));
}

cross::VulkanPipelineStatePool::~VulkanPipelineStatePool()
{
    vkDestroyPipelineCache(GetDevice<VulkanDevice>()->Get(), mCache, gVkAllocCallback);
}

cross::NGIGraphicsPipelineState* cross::VulkanPipelineStatePool::AllocateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc)
{
    {
        std::shared_lock readerLock(mPipelineStatesMutex);
        if (auto itr = mPipelineStates.find(&desc); itr != mPipelineStates.end())
        {
            auto& [pipelineState, lifetime] = *(itr->second);
            lifetime = GetFrameId();
            return pipelineState.get();
        }
    }

    {
        QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
        std::unique_lock writerLock(mPipelineStatesMutex);
        if (auto itr = mPipelineStates.find(&desc); itr != mPipelineStates.end())
        {
            auto& [pipelineState, lifetime] = *(itr->second);
            lifetime = GetFrameId();
            return pipelineState.get();
        }
        else
        {
            // the VulkanGraphicsPipelineState's creation, espesially for vk call is quite expensive at some times
            // need consider to do a async creation;
            auto pipelineState = std::make_unique<VulkanGraphicsPipelineState>(desc, this);
            auto* pipelineStateRaw = pipelineState.get();
            auto* persistentDesc = &pipelineState->GetDesc();
            mPipeStatesLib.push_back(std::forward_as_tuple(std::move(pipelineState), GetFrameId()));
            auto [_, ret] = mPipelineStates.emplace(std::piecewise_construct, std::forward_as_tuple(persistentDesc), std::forward_as_tuple(std::prev(mPipeStatesLib.end())));
            Assert(ret);
            return pipelineStateRaw;
        }
    }
}

cross::NGIComputePipelineState* cross::VulkanPipelineStatePool::CreateComputePipelineState(const NGIComputePipelineStateDesc& desc)
{
    return new VulkanComputePipelineState{desc, this};
}

cross::NGIRayTracingPipelineState* cross::VulkanPipelineStatePool::CreateRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc)
{
    return new VulkanRayTracingPipelineState(desc, this);
}

cross::NGIGraphicsPipelineState* cross::VulkanPipelineStatePool::CreateGraphicsPipelineState(const NGIGraphicsPipelineStateDesc& desc)
{
    return new VulkanGraphicsPipelineState{ desc, this };
}

void cross::VulkanPipelineStatePool::OnBeginFrame(FrameParam* frame)
{
    SCOPED_CPU_TIMING(GroupRendering, "VulkanPipelineStatePool::BeginFrame");

    UInt32 erasedCount = 0;

    std::unique_lock writerLock(mPipelineStatesMutex);
    if (mPipelineStates.size() > MAX_PIPELINESTATECACHE_NUM)
    {
        // refresh order so we can delete the unused;
        mPipeStatesLib.sort([](const PipelineStateEntry& a, const PipelineStateEntry& b) {
            return std::get<1>(a) < std::get<1>(b);
            });


        for (auto itr = mPipeStatesLib.begin(); itr != mPipeStatesLib.end();)
        {
            UInt32 framePassed = GetFrameId() - std::get<1>(*itr);
            if (framePassed <= CmdSettings::Inst().gMaxQueuedFrame || mPipelineStates.size() < MAX_PIPELINESTATECACHE_NUM)
            {
                break;
            }

            if (framePassed > CmdSettings::Inst().gMaxQueuedFrame)
            {
                // in some cases, if we don't add these "if statement",
                // compile would first erase the pipestateslib then erase the pipelineState
                // which cause the asan report memory alert.
                if (mPipelineStates.erase(&std::get<0>(*itr)->GetDesc()))
                {
                    itr = mPipeStatesLib.erase(itr);
                    erasedCount++;
                }
                else
                {
                    itr++;
                    LOG_ERROR("Failed to erase an pso allocated at {} in frame {}", std::get<1>(*itr), GetFrameId());
                }
            }
            else
            {
                itr++;
            }
        }
    }
    //for (auto itr = mPipelineStates.begin(); itr != mPipelineStates.end();)
    //{
    //    auto& [_, lifetime] = itr->second;
    //    if (--lifetime == 0)
    //    {
    //        itr = mPipelineStates.erase(itr);
    //        erasedCount++;
    //    }
    //    else
    //    {
    //        ++itr;
    //    }
    //}
    //LOG_INFO("Erase {} Count {} Total {}", erasedCount, mPipelineStates.size(),mPipeStatesLib.size());
}

SizeType cross::VulkanPipelineStatePool::GetSize()
{
    size_t size;
    VK_CHECK(vkGetPipelineCacheData(GetDevice<VulkanDevice>()->Get(), mCache, &size, nullptr));
    return size;
}

void cross::VulkanPipelineStatePool::GetData(SizeType size, void* pData)
{
    auto nativeSize = static_cast<size_t>(size);
    VK_CHECK(vkGetPipelineCacheData(GetDevice<VulkanDevice>()->Get(), mCache, &nativeSize, pData));
}

cross::VulkanComputePipelineState::VulkanComputePipelineState(const NGIComputePipelineStateDesc& desc, VulkanPipelineStatePool* pool)
    : NGIComputePipelineState{desc, pool }
{
    auto vkDevice = pool->GetDevice<VulkanDevice>();
    NGI_LOG_DEBUG("Create compute pipeline with program hash: {}-{}", desc.ProgramGUID.high, desc.ProgramGUID.low);

    VkShaderModule module{};
    VkShaderModuleCreateInfo moduleCreateInfo{
        VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
        nullptr,
        0,
        desc.Program->ComputeShader.ByteSize,
        reinterpret_cast<const uint32_t*>(desc.Program->ComputeShader.Data),
    };
    VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &module));

    std::vector<VkSpecializationMapEntry> specConstEntries;

    auto& layoutDesc = desc.PipelineLayout->GetDesc();

    for (UInt32 i = 0; i < layoutDesc.ConstantCount; i++)
    {
        auto& constantDesc = layoutDesc.Constants[i];
        if (constantDesc.StageMask & ToUnderlying(CrossSchema::ShaderStageBit::Compute))
        {
            specConstEntries.push_back({
                constantDesc.Index,
                constantDesc.Offset,
                constantDesc.Size,
            });
        }
    }

    VkSpecializationInfo specConstInfo{
        static_cast<UInt32>(specConstEntries.size()),
        specConstEntries.data(),
        desc.ConstantDataSize,
        desc.ConstantData,
    };

    VkComputePipelineCreateInfo pipelineCreateInfo{
        VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO,
        nullptr,
        0,
        {
            VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
            nullptr,
            0,
            VK_SHADER_STAGE_COMPUTE_BIT,
            module,
            desc.Program->ComputeShader.EntryPoint,
            specConstEntries.empty() ? nullptr : &specConstInfo,
        },
        rhi_cast<VulkanPipelineLayout*>(desc.PipelineLayout)->mPipelineLayout,
        VK_NULL_HANDLE,
        0,
    };

    VK_CHECK(vkCreateComputePipelines(vkDevice->Get(), pool->mCache, 1, &pipelineCreateInfo, gVkAllocCallback, &mPipeline));

    vkDestroyShaderModule(vkDevice->Get(), module, gVkAllocCallback);

    NGI_LOG_DEBUG("Create compute pipeline: {}", VkFormatHandle(mPipeline));

#if NGI_ENABLE_GPU_DUMP
    if (GetDevice<VulkanDevice>()->GpuDump())
    {
        GetDevice<VulkanDevice>()->GetGPUCrashTracker().AddShaderBinary(desc.Program->ComputeShader, desc.Program->FilePath);
    }
#endif
}

cross::VulkanComputePipelineState::~VulkanComputePipelineState()
{
    NGI_LOG_DEBUG("Destroy compute pipeline: {}", VkFormatHandle(mPipeline));
    vkDestroyPipeline(GetDevice<VulkanDevice>()->Get(), mPipeline, gVkAllocCallback);
}

cross::VulkanRayTracingPipelineState::VulkanRayTracingPipelineState(const NGIRayTracingPipelineStateDesc& desc, VulkanPipelineStatePool* pool)
    : NGIRayTracingPipelineState{desc, pool}
{
    auto vkDevice = pool->GetDevice<VulkanDevice>();
    NGI_LOG_DEBUG("Create ray tracing pipeline with program hash: {}-{}", desc.ProgramGUID.high, desc.ProgramGUID.low);

    std::vector<VkPipelineShaderStageCreateInfo> shaderStages;
    std::vector<VkShaderModule> shaderModules;

    // -----------------step 1: create pipeline shader stages---------------------
    // step 1.1: create ray gen shader stage
    {
        VkShaderModuleCreateInfo moduleCreateInfo{
            VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
            nullptr,
            0,
            desc.Program->RayGenShader.ByteSize,
            static_cast<const uint32_t*>(desc.Program->RayGenShader.Data),
        };

        VkShaderModule shaderModule;
        VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
        shaderModules.push_back(shaderModule);

        VkPipelineShaderStageCreateInfo stageInfo{
            VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
            nullptr,
            0,
            VK_SHADER_STAGE_RAYGEN_BIT_KHR,
            shaderModule,
            desc.Program->RayGenShader.EntryPoint,
            nullptr
        };
        shaderStages.push_back(stageInfo);
    }

    // step 1.2: create miss shader stages
    UInt32 missShaderCount = static_cast<UInt32>(desc.Program->MissShaders.size());
    for (UInt32 i = 0; i < missShaderCount; ++i) {
        VkShaderModuleCreateInfo moduleCreateInfo{
            VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
            nullptr,
            0,
            desc.Program->MissShaders[i].ByteSize,
            static_cast<const uint32_t*>(desc.Program->MissShaders[i].Data),
        };

        VkShaderModule shaderModule;
        VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
        shaderModules.push_back(shaderModule);

        VkPipelineShaderStageCreateInfo stageInfo{
            VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
            nullptr,
            0,
            VK_SHADER_STAGE_MISS_BIT_KHR,
            shaderModule,
            desc.Program->MissShaders[i].EntryPoint,
            nullptr
        };
        shaderStages.push_back(stageInfo);
    }

    // step 1.3: create hit shader stages
    UInt32 hitGroupCount = static_cast<UInt32>(desc.Program->HitGroups.size());
    for (UInt32 i = 0; i < hitGroupCount; ++i) {
        const auto& hitGroup = desc.Program->HitGroups[i];

        // closest hit shader
        {
            VkShaderModuleCreateInfo moduleCreateInfo{
                VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
                nullptr,
                0,
                hitGroup.ClosestHitShader.ByteSize,
                static_cast<const uint32_t*>(hitGroup.ClosestHitShader.Data),
            };

            VkShaderModule shaderModule;
            VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
            shaderModules.push_back(shaderModule);

            VkPipelineShaderStageCreateInfo stageInfo{
                VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
                nullptr,
                0,
                VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR,
                shaderModule,
                hitGroup.ClosestHitShader.EntryPoint,
                nullptr
            };
            shaderStages.push_back(stageInfo);
        }

        // any hit shader
        if (hitGroup.AnyHitShader.ByteSize > 0) {
            VkShaderModuleCreateInfo moduleCreateInfo{
                VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
                nullptr,
                0,
                hitGroup.AnyHitShader.ByteSize,
                static_cast<const uint32_t*>(hitGroup.AnyHitShader.Data),
            };

            VkShaderModule shaderModule;
            VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
            shaderModules.push_back(shaderModule);

            VkPipelineShaderStageCreateInfo stageInfo{
                VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
                nullptr,
                0,
                VK_SHADER_STAGE_ANY_HIT_BIT_KHR,
                shaderModule,
                hitGroup.AnyHitShader.EntryPoint,
                nullptr
            };
            shaderStages.push_back(stageInfo);
        }

        // intersection shader
        if (hitGroup.IntersectionShader.ByteSize > 0) {
            VkShaderModuleCreateInfo moduleCreateInfo{
                VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
                nullptr,
                0,
                hitGroup.IntersectionShader.ByteSize,
                static_cast<const uint32_t*>(hitGroup.IntersectionShader.Data),
            };

            VkShaderModule shaderModule;
            VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
            shaderModules.push_back(shaderModule);

            VkPipelineShaderStageCreateInfo stageInfo{
                VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
                nullptr,
                0,
                VK_SHADER_STAGE_INTERSECTION_BIT_KHR,
                shaderModule,
                hitGroup.IntersectionShader.EntryPoint,
                nullptr
            };
            shaderStages.push_back(stageInfo);
        }
    }

    // step 1.4: create callable shader stages
    UInt32 callableShaderCount = static_cast<UInt32>(desc.Program->CallableShaders.size());
    for (UInt32 i = 0; i < callableShaderCount; ++i) {
        VkShaderModuleCreateInfo moduleCreateInfo{
            VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO,
            nullptr,
            0,
            desc.Program->CallableShaders[i].ByteSize,
            static_cast<const uint32_t*>(desc.Program->CallableShaders[i].Data),
        };

        VkShaderModule shaderModule;
        VK_CHECK(vkCreateShaderModule(vkDevice->Get(), &moduleCreateInfo, gVkAllocCallback, &shaderModule));
        shaderModules.push_back(shaderModule);

        VkPipelineShaderStageCreateInfo stageInfo{
            VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO,
            nullptr,
            0,
            VK_SHADER_STAGE_CALLABLE_BIT_KHR,
            shaderModule,
            desc.Program->CallableShaders[i].EntryPoint,
            nullptr
        };
        shaderStages.push_back(stageInfo);
    }

    // ----------------------step 2: create shader groups-------------------------
    std::vector<VkRayTracingShaderGroupCreateInfoKHR> shaderGroups;

    {
        VkRayTracingShaderGroupCreateInfoKHR group{
            VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
            nullptr,
            VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
            0,
            VK_SHADER_UNUSED_KHR,
            VK_SHADER_UNUSED_KHR,
            VK_SHADER_UNUSED_KHR,
            nullptr
        };
        shaderGroups.push_back(group);
    }

    UInt32 missShaderOffset = 1;
    for (UInt32 i = 0; i < missShaderCount; ++i) {
        VkRayTracingShaderGroupCreateInfoKHR group{
            VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
            nullptr,
            VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
            missShaderOffset + i,
            VK_SHADER_UNUSED_KHR,
            VK_SHADER_UNUSED_KHR,
            VK_SHADER_UNUSED_KHR,
            nullptr
        };
        shaderGroups.push_back(group);
    }

    UInt32 hitShaderOffset = missShaderOffset + static_cast<UInt32>(desc.Program->MissShaders.size());
    UInt32 shaderIndex = hitShaderOffset;
    for (UInt32 i = 0; i < hitGroupCount; ++i) {
        const auto& srcHitGroup = desc.Program->HitGroups[i];

        VkRayTracingShaderGroupCreateInfoKHR group{
            VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
            nullptr,
            srcHitGroup.IntersectionShader.ByteSize > 0 ?
                VK_RAY_TRACING_SHADER_GROUP_TYPE_PROCEDURAL_HIT_GROUP_KHR :
                VK_RAY_TRACING_SHADER_GROUP_TYPE_TRIANGLES_HIT_GROUP_KHR,
            VK_SHADER_UNUSED_KHR,
            shaderIndex++,
            srcHitGroup.AnyHitShader.ByteSize > 0 ? shaderIndex++ : VK_SHADER_UNUSED_KHR,
            srcHitGroup.IntersectionShader.ByteSize > 0 ? shaderIndex++ : VK_SHADER_UNUSED_KHR,
            nullptr
        };

        shaderGroups.push_back(group);
    }

    if (callableShaderCount > 0) {
        UInt32 callableShaderOffset = shaderIndex;
        for (UInt32 i = 0; i < callableShaderCount; ++i) {
            VkRayTracingShaderGroupCreateInfoKHR group{
                VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
                nullptr,
                VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
                callableShaderOffset + i,
                VK_SHADER_UNUSED_KHR,
                VK_SHADER_UNUSED_KHR,
                VK_SHADER_UNUSED_KHR,
                nullptr
            };
            shaderGroups.push_back(group);
        }
        Assert(callableShaderOffset + callableShaderCount == shaderStages.size());
    }

    // -------------------step 3: create constant specializations-------------------
    VkSpecializationInfo specializationInfo{};
    std::vector<VkSpecializationMapEntry> specializationEntries;

    if (desc.ConstantDataSize > 0 && desc.ConstantData) {
        mConstantData = std::make_unique<UInt8[]>(desc.ConstantDataSize);
        memcpy(mConstantData.get(), desc.ConstantData, desc.ConstantDataSize);

        UInt32 numConstants = desc.ConstantDataSize / sizeof(UInt32);
        specializationEntries.resize(numConstants);

        for (UInt32 i = 0; i < numConstants; ++i) {
            specializationEntries[i].constantID = i;
            specializationEntries[i].offset = i * sizeof(UInt32);
            specializationEntries[i].size = sizeof(UInt32);
        }

        specializationInfo.mapEntryCount = numConstants;
        specializationInfo.pMapEntries = specializationEntries.data();
        specializationInfo.dataSize = desc.ConstantDataSize;
        specializationInfo.pData = mConstantData.get();

        for (auto& stage : shaderStages) {
            stage.pSpecializationInfo = &specializationInfo;
        }
    }

    // ----------------step 4: create ray tracing pipeline--------------
    VkRayTracingPipelineCreateInfoKHR pipelineInfo{VK_STRUCTURE_TYPE_RAY_TRACING_PIPELINE_CREATE_INFO_KHR};
    pipelineInfo.stageCount = static_cast<uint32_t>(shaderStages.size());
    pipelineInfo.pStages = shaderStages.data();
    pipelineInfo.groupCount = static_cast<uint32_t>(shaderGroups.size());
    pipelineInfo.pGroups = shaderGroups.data();
    pipelineInfo.maxPipelineRayRecursionDepth = desc.MaxRecursionDepth;
    pipelineInfo.layout = rhi_cast<VulkanPipelineLayout*>(desc.PipelineLayout)->mPipelineLayout;

    VK_CHECK(vkCreateRayTracingPipelinesKHR(
        vkDevice->Get(),
        VK_NULL_HANDLE,
        pool->mCache,
        1,
        &pipelineInfo,
        gVkAllocCallback,
        &mPipeline));

    // -----------------------step 5: create shader binding table------------------------
    if (mPipeline != VK_NULL_HANDLE) {
        // Store shader group counts
        mRayGenShaderCount = 1;  // Always have one ray gen shader
        mMissShaderCount = missShaderCount;
        mHitGroupCount = hitGroupCount;
        mCallableShaderCount = callableShaderCount;

        // Get ray tracing pipeline properties
        VkPhysicalDeviceRayTracingPipelinePropertiesKHR rtProperties{VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_TRACING_PIPELINE_PROPERTIES_KHR};

        VkPhysicalDeviceProperties2 properties{VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROPERTIES_2};
        properties.pNext = &rtProperties;

        vkGetPhysicalDeviceProperties2(vkDevice->GetPhysicalDevice()->mPhyDev, &properties);

        // Get shader group handles
        UInt32 groupCount = static_cast<UInt32>(shaderGroups.size());
        UInt32 handleSize = rtProperties.shaderGroupHandleSize;
        UInt32 shaderGroupBaseAlignment = rtProperties.shaderGroupBaseAlignment;
        UInt32 sbtSize = groupCount * handleSize;

        // Temporary storage for shader group handles
        std::vector<uint8_t> shaderHandleStorage(sbtSize);

        VkResult result = vkGetRayTracingShaderGroupHandlesKHR(vkDevice->Get(), mPipeline, 0, groupCount, sbtSize, shaderHandleStorage.data());
        if (result == VK_SUCCESS)
        {
            // Create shader binding table
            CreateShaderBindingTable(vkDevice, shaderHandleStorage.data(), handleSize, shaderGroupBaseAlignment);
        }
        else
        {
            NGI_LOG_ERROR("Failed to get ray tracing shader group handles: {}", result);
        }
    }

    // -----------------------step 6: clean up------------------------
    for (auto shaderModule : shaderModules) {
        vkDestroyShaderModule(vkDevice->Get(), shaderModule, gVkAllocCallback);
    }

    NGI_LOG_DEBUG("Create ray tracing pipeline: {}", VkFormatHandle(mPipeline));
}

cross::VulkanRayTracingPipelineState::~VulkanRayTracingPipelineState()
{
    VulkanDevice* device = GetDevice<VulkanDevice>();

    // Clean up pipeline
    NGI_LOG_DEBUG("Destroy ray tracing pipeline: {}", VkFormatHandle(mPipeline));
    if (mPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device->Get(), mPipeline, gVkAllocCallback);
        mPipeline = VK_NULL_HANDLE;
    }
}

cross::VulkanRayTracingPipelineState::ShaderBindingTable::~ShaderBindingTable()
{
    delete Buffer;
}

void cross::VulkanRayTracingPipelineState::CreateShaderBindingTable(
    VulkanDevice* device,
    void* shaderHandleData,
    UInt32 handleSize,
    UInt32 ShaderGroupBaseAlignment)
{
    // Calculate sizes for each SBT section
    VkDeviceSize rayGenSize = ShaderGroupBaseAlignment;
    VkDeviceSize missSize = mMissShaderCount * ShaderGroupBaseAlignment;
    VkDeviceSize hitSize = mHitGroupCount * ShaderGroupBaseAlignment;
    VkDeviceSize callableSize = mCallableShaderCount * ShaderGroupBaseAlignment;
    VkDeviceSize totalSize = rayGenSize + missSize + hitSize + callableSize;

    if (totalSize == 0) {
        NGI_LOG_ERROR("Invalid SBT size: {}", totalSize);
        return;
    }

    // Create SBT buffer
    NGIBufferDesc sbtBufferDesc{
        .Size = totalSize,
        .Usage = NGIBufferUsage::ShaderBindingTableBuffer | NGIBufferUsage::CopyDst,
    };
    mSBT.Buffer = new VulkanStagingBuffer(sbtBufferDesc, device, "ShaderBindingTableStagingBuffer");

    // Copy shader group handles to SBT
    UInt8* dst = reinterpret_cast<UInt8*>(mSBT.Buffer->MapRange(NGIBufferUsage::CopyDst, 0, totalSize));
    UInt8* src = reinterpret_cast<UInt8*>(shaderHandleData);
    
    // Copy RayGen shader group handle
    memcpy(dst, src, handleSize);
    dst += ShaderGroupBaseAlignment;
    src += handleSize;

    // Copy Miss shader group handles
    for (UInt32 i = 0; i < mMissShaderCount; i++)
    {
        memcpy(dst, src, handleSize);
        dst += ShaderGroupBaseAlignment;
        src += handleSize;
    }

    // Copy Hit shader group handles
    for (UInt32 i = 0; i < mHitGroupCount; i++)
    {
        memcpy(dst, src, handleSize);
        dst += ShaderGroupBaseAlignment;
        src += handleSize;
    }

    // Copy Callable shader group handles
    for (UInt32 i = 0; i < mCallableShaderCount; i++)
    {
        memcpy(dst, src, handleSize);
        dst += ShaderGroupBaseAlignment;
        src += handleSize;
    }

    mSBT.Buffer->UnmapRange(0, totalSize);
    
    // Set up SBT regions
    VkDeviceAddress sbtAddress = mSBT.Buffer->GetDeviceAddress();
    Assert(sbtAddress % ShaderGroupBaseAlignment == 0);

    mSBT.RayGenRegion = {
        sbtAddress,
        ShaderGroupBaseAlignment,
        rayGenSize
    };

    mSBT.MissRegion = {
        sbtAddress + rayGenSize,
        ShaderGroupBaseAlignment,
        missSize
    };

    mSBT.HitRegion = {
        sbtAddress + rayGenSize + missSize,
        ShaderGroupBaseAlignment,
        hitSize
    };

    mSBT.CallableRegion = {
        sbtAddress + rayGenSize + missSize + hitSize,
        ShaderGroupBaseAlignment,
        callableSize
    };

    NGI_LOG_DEBUG("Created shader binding table: buffer={}, size={}", VkFormatHandle(mSBT.buffer), totalSize);
}

cross::VulkanCommandList::VulkanCommandList(VkCommandBufferLevel level, VkCommandPool pool, VulkanCommandQueue* queue)
    : NGICommandList{queue->mDevice}
    , mLevel{level}
    , mCommandPool{pool}
    , mQueue{queue}
{
    VkCommandBufferAllocateInfo allocateInfo{
       VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO,
       nullptr,
       mCommandPool,
       mLevel,
       1,
    };
    VkDevice device = mQueue->GetDevice<VulkanDevice>()->Get();
    VK_CHECK(vkAllocateCommandBuffers(device, &allocateInfo, &mCommandList));
    GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnCreateVkCommandBuffer(mQueue, mCommandList);
}

void cross::VulkanCommandList::Begin()
{
    if (mLevel == VK_COMMAND_BUFFER_LEVEL_PRIMARY)
    {
        VkCommandBufferBeginInfo beginInfo{
            VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO,
            nullptr,
            VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT,
            nullptr,
        };
        VK_CHECK(vkBeginCommandBuffer(mCommandList, &beginInfo));
    }
    else
    {
        VkCommandBufferInheritanceInfo inheritanceInfo{
            VK_STRUCTURE_TYPE_COMMAND_BUFFER_INHERITANCE_INFO,
            nullptr,
            mRenderPass,
            mSubpass,
            mFramebuffer,
        };
        VkCommandBufferBeginInfo beginInfo{
            VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO,
            nullptr,
            VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT | VK_COMMAND_BUFFER_USAGE_RENDER_PASS_CONTINUE_BIT,
            &inheritanceInfo,
        };
        VK_CHECK(vkBeginCommandBuffer(mCommandList, &beginInfo));
    }
    GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnBeginCommandBuffer(mQueue, mCommandList);
}

void cross::VulkanCommandList::BeginRenderPass(NGIRenderPass* renderPass, NGIFramebuffer* framebuffer, UInt32 clearValueCount, NGIClearValue* clearValues, UInt32 targetBarrierCount, NGITextureBarrier* targetBarriers, bool useBundle)
{
    if (targetBarrierCount)
    {
        ResourceBarrier(0, nullptr, targetBarrierCount, targetBarriers);
    }

    auto& renderPassDesc = renderPass->GetDesc();
    auto* vkRenderPass = rhi_cast<VulkanRenderPass*>(renderPass);
    auto* vkFramebuffer = rhi_cast<VulkanFramebuffer*>(framebuffer);

    auto subpassContent = useBundle ? VK_SUBPASS_CONTENTS_SECONDARY_COMMAND_BUFFERS : VK_SUBPASS_CONTENTS_INLINE;
    mRenderPass = vkRenderPass->mRenderPass;
    mSubpass = 0;
    mFramebuffer = vkFramebuffer->mFramebuffer;

    VkClearValue vkClearValues[MaxSupportedRenderTargets + 1]{};

    for (UInt32 i = 0; i < renderPassDesc.ColorTargetCount; ++i)
    {
        auto& renderTargetDesc = renderPassDesc.ColorTargets[i];
        if (renderTargetDesc.LoadOp == NGILoadOp::Clear)
        {
            switch (GetFormatCompType(renderTargetDesc.Format))
            {
            case GraphicsFormat::CompTypeUNorm:
            case GraphicsFormat::CompTypeSNorm:
            case GraphicsFormat::CompTypeSRGB:
            case GraphicsFormat::CompTypeSFloat:
                std::copy_n(clearValues[i].colorf, 4, vkClearValues[i].color.float32);
                break;
            case GraphicsFormat::CompTypeUInt:
                std::copy_n(clearValues[i].coloru, 4, vkClearValues[i].color.uint32);
                break;
            case GraphicsFormat::CompTypeSInt:
                std::copy_n(clearValues[i].colori, 4, vkClearValues[i].color.int32);
                break;
            default:
                LOG_ERROR("Error clear color format");
                break;
            }
        }
    }

    if (renderPassDesc.HasDepthStencil && (renderPassDesc.DepthLoadOp == NGILoadOp::Clear || renderPassDesc.StencilLoadOp == NGILoadOp::Clear))
    {
        auto& vkClearValue = vkClearValues[renderPassDesc.ColorTargetCount].depthStencil;
        auto& clearValue = clearValues[renderPassDesc.ColorTargetCount].depthStencil;
        vkClearValue.depth = clearValue.depth;
        vkClearValue.stencil = clearValue.stencil;
    }

    VkRenderPassBeginInfo beginInfo{
        VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO,
        nullptr,
        mRenderPass,
        mFramebuffer,
        {
            {
                0,
                0,
            },
            {
                framebuffer->GetDesc().Width,
                vkFramebuffer->GetDesc().Height,
            },
        },
        clearValueCount,
        vkClearValues,
    };

    vkCmdBeginRenderPass(mCommandList, &beginInfo, subpassContent);
}

void cross::VulkanCommandList::ForkBundleCommandLists(UInt32 num, NGIBundleCommandList* const* ppBundleCmdLists)
{
    std::transform(ppBundleCmdLists, ppBundleCmdLists + num, std::back_inserter(mBundleCommandLists), [&](auto* cmdList) {
        auto vkCmdList = rhi_cast<VulkanCommandList*>(cmdList);
        vkCmdList->mRenderPass = mRenderPass;
        vkCmdList->mSubpass = mSubpass;
        vkCmdList->mFramebuffer = mFramebuffer;
        return vkCmdList->mCommandList;
    });
}

void cross::VulkanCommandList::JoinBundleCommandLists()
{
    if (!mBundleCommandLists.empty())
    {
        vkCmdExecuteCommands(mCommandList, static_cast<uint32_t>(mBundleCommandLists.size()), mBundleCommandLists.data());
        mBundleCommandLists.clear();
    }
}

void cross::VulkanCommandList::SetGraphicsPipelineState(NGIGraphicsPipelineState* pipelineState)
{
    mGraphicsPipeline = rhi_cast<VulkanGraphicsPipelineState*>(pipelineState);
    vkCmdBindPipeline(mCommandList, VK_PIPELINE_BIND_POINT_GRAPHICS, mGraphicsPipeline->mPipeline);
}

void cross::VulkanCommandList::SetGraphicsResourceGroup(UInt32 slot, NGIResourceGroup* resourceGroup)
{
    auto* vkResourceGroup = rhi_cast<VulkanResourceGroup*>(resourceGroup);
    auto* vkPipelineLayout = rhi_cast<VulkanPipelineLayout*>(mGraphicsPipeline->GetDesc().PipelineLayout);
    vkCmdBindDescriptorSets(
        mCommandList, VK_PIPELINE_BIND_POINT_GRAPHICS, vkPipelineLayout->mPipelineLayout, slot, 1, &vkResourceGroup->mDescriptorSet, static_cast<uint32_t>(vkResourceGroup->mDynamicOffsets.size()), vkResourceGroup->mDynamicOffsets.data());
}

void cross::VulkanCommandList::SetVertexBuffers(UInt32 num, NGIBuffer** ppVertexBuffers, UInt64* pOffsets)
{
    Assert(num <= MaxVertexStreams);
    VkBuffer vkVertexBuffers[MaxVertexStreams + 1]{};
    UInt64 vkOffsets[MaxVertexStreams + 1]{};
    for (UInt32 i = 0; i < num; ++i)
    {
        auto* vertexBuffer = ppVertexBuffers[i];
        vkVertexBuffers[i] = reinterpret_cast<VkBuffer>(vertexBuffer->GetNativeHandle());
        vkOffsets[i] = pOffsets ? pOffsets[i] : 0;
    }
    vkVertexBuffers[num] = GetDevice<VulkanDevice>()->GetDefaultVertexBuffer();
    vkOffsets[num] = 0;
    vkCmdBindVertexBuffers(mCommandList, 0, num + 1, vkVertexBuffers, vkOffsets);
}

void cross::VulkanCommandList::SetIndexBuffer(NGIBuffer* pIndexBuffer, UInt64 offset, GraphicsFormat format)
{
    Assert(format == GraphicsFormat::R32_UInt || format == GraphicsFormat::R16_UInt);
    vkCmdBindIndexBuffer(mCommandList, reinterpret_cast<VkBuffer>(pIndexBuffer->GetNativeHandle()), offset, format == GraphicsFormat::R32_UInt ? VK_INDEX_TYPE_UINT32 : VK_INDEX_TYPE_UINT16);
}

void cross::VulkanCommandList::SetViewports(UInt32 num, const NGIViewport* pViewports)
{
    static_assert(sizeof(NGIViewport) == sizeof(VkViewport) && alignof(NGIViewport) == alignof(VkViewport));

    vkCmdSetViewport(mCommandList, 0, num, reinterpret_cast<const VkViewport*>(pViewports));
}

void cross::VulkanCommandList::SetScissors(UInt32 num, const NGIScissor* pScissors)
{
    static_assert(sizeof(NGIScissor) == sizeof(VkRect2D) && alignof(NGIScissor) == alignof(VkRect2D));

    vkCmdSetScissor(mCommandList, 0, num, reinterpret_cast<const VkRect2D*>(pScissors));
}

void cross::VulkanCommandList::DrawInstanced(UInt32 vertexCountPerInstance, UInt32 instanceCount, UInt32 startVertexLocation, UInt32 startInstanceLocation)
{
    vkCmdDraw(mCommandList, vertexCountPerInstance, instanceCount, startVertexLocation, startInstanceLocation);
}

void cross::VulkanCommandList::DrawIndexedInstanced(UInt32 indexCountPerInstance, UInt32 instanceCount, UInt32 startIndexLocation, SInt32 baseVertexLocation, UInt32 startInstanceLocation)
{
    vkCmdDrawIndexed(mCommandList, indexCountPerInstance, instanceCount, startIndexLocation, baseVertexLocation, startInstanceLocation);
}

void cross::VulkanCommandList::DrawIndexedIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride)
{
    vkCmdDrawIndexedIndirect(mCommandList, rhi_cast<VulkanBuffer*>(buffer)->mBuffer, offset, drawCount, stride);
}

void cross::VulkanCommandList::DrawIndexedIndirectCount(NGIBuffer* buffer, UInt32 offset, NGIBuffer* countBuffer, UInt32 countBufferOffset, UInt32 maxDrawCount, UInt32 stride)
{
    vkCmdDrawIndexedIndirectCount(mCommandList, rhi_cast<VulkanBuffer*>(buffer)->mBuffer, offset, reinterpret_cast<VkBuffer>(countBuffer->GetNativeHandle()), countBufferOffset, maxDrawCount, stride);
}

void cross::VulkanCommandList::DrawIndirect(NGIBuffer* buffer, UInt32 offset, UInt32 drawCount, UInt32 stride)
{
    vkCmdDrawIndirect(mCommandList, rhi_cast<VulkanBuffer*>(buffer)->mBuffer, offset, drawCount, stride);
}

void cross::VulkanCommandList::SetStencilRef(UInt32 stencilRef)
{
    vkCmdSetStencilReference(mCommandList, VK_STENCIL_FACE_FRONT_AND_BACK, stencilRef);
}

void cross::VulkanCommandList::NextSubPass(bool useBundle)
{
    Assert(mBundleCommandLists.empty());
    vkCmdNextSubpass(mCommandList, useBundle ? VK_SUBPASS_CONTENTS_SECONDARY_COMMAND_BUFFERS : VK_SUBPASS_CONTENTS_INLINE);
    mSubpass++;
}

void cross::VulkanCommandList::EndRenderPass()
{
    Assert(mBundleCommandLists.empty());
    vkCmdEndRenderPass(mCommandList);
    mRenderPass = VK_NULL_HANDLE;
}

void cross::VulkanCommandList::SetComputePipelineState(NGIComputePipelineState* pipelineState)
{
    mComputePipeline = rhi_cast<VulkanComputePipelineState*>(pipelineState);
    vkCmdBindPipeline(mCommandList, VK_PIPELINE_BIND_POINT_COMPUTE, mComputePipeline->mPipeline);
}

void cross::VulkanCommandList::SetComputeResourceGroup(UInt32 slot, NGIResourceGroup* resourceGroup)
{
    auto* vkResourceGroup = rhi_cast<VulkanResourceGroup*>(resourceGroup);
    auto* vkPipelineLayout = rhi_cast<VulkanPipelineLayout*>(mComputePipeline->GetDesc().PipelineLayout);
    vkCmdBindDescriptorSets(
        mCommandList, VK_PIPELINE_BIND_POINT_COMPUTE, vkPipelineLayout->mPipelineLayout, slot, 1, &vkResourceGroup->mDescriptorSet, static_cast<uint32_t>(vkResourceGroup->mDynamicOffsets.size()), vkResourceGroup->mDynamicOffsets.data());
}

void cross::VulkanCommandList::Dispatch(UInt32 threadGroupCountX, UInt32 threadGroupCountY, UInt32 threadGroupCountZ)
{
    vkCmdDispatch(mCommandList, threadGroupCountX, threadGroupCountY, threadGroupCountZ);
}

void cross::VulkanCommandList::DispatchIndirect(NGIBuffer* argumentBuffer, UInt32 offset)
{
    vkCmdDispatchIndirect(mCommandList, reinterpret_cast<VkBuffer>(argumentBuffer->GetNativeHandle()), offset);
}

void cross::VulkanCommandList::CopyBufferToBuffer(NGIBuffer* pDstBuffer, NGIBuffer* pSrcBuffer, UInt32 RegionCount, const NGICopyBuffer* pRegions)
{
    std::vector<VkBufferCopy> vkRegions{RegionCount};
    std::transform(pRegions, pRegions + RegionCount, vkRegions.begin(), [=](const NGICopyBuffer& region) {
        return VkBufferCopy{
            region.SrcOffset,
            region.DstOffset,
            region.NumBytes != 0 ? region.NumBytes : std::min<VkDeviceSize>(pSrcBuffer->GetSize() - region.SrcOffset, pDstBuffer->GetSize() - region.DstOffset),
        };
    });



    Assert(typeid(*pSrcBuffer) == typeid(VulkanBuffer) || typeid(*pSrcBuffer) == typeid(VulkanStagingBuffer));

    vkCmdCopyBuffer(mCommandList, reinterpret_cast<VkBuffer>(pSrcBuffer->GetNativeHandle()), reinterpret_cast<VkBuffer>(pDstBuffer->GetNativeHandle()), RegionCount, vkRegions.data());
}

void cross::VulkanCommandList::CopyBufferToTexture(NGITexture* dstTexture, NGIBuffer* srcBuffer, UInt32 RegionCount, const NGICopyBufferTexture* pRegions)
{
    auto& desc = dstTexture->GetDesc();
    auto texelBlock = GetFormatTexelBlockProperty(desc.Format);

    std::vector<VkBufferImageCopy> vkRegions{RegionCount};

    std::transform(pRegions, pRegions + RegionCount, vkRegions.begin(), [&](const NGICopyBufferTexture& region) {
        Assert(region.BufferRowPitch % texelBlock.Size == 0);
        auto [mip, layer, plane] = NGIDecomposeSubresource(region.TextureSubresource, desc.MipCount, desc.ArraySize);
        VkImageAspectFlags aspect = FormatHasDepthOrStencil(desc.Format) ? (plane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT) : VK_IMAGE_ASPECT_COLOR_BIT;
        return VkBufferImageCopy{
            region.BufferOffset,
            (region.BufferRowPitch / texelBlock.Size) * texelBlock.Width,
            ((region.TextureExtent.Height - 1) / texelBlock.Height + 1) * texelBlock.Height,
            {
                aspect,
                mip,
                layer,
                1,
            },
            {
                region.TextureOffset.X,
                region.TextureOffset.Y,
                region.TextureOffset.Z,
            },
            {
                region.TextureExtent.Width,
                region.TextureExtent.Height,
                region.TextureExtent.Depth,
            },
        };
    });

    vkCmdCopyBufferToImage(mCommandList, reinterpret_cast<VkBuffer>(srcBuffer->GetNativeHandle()), rhi_cast<VulkanTexture*>(dstTexture)->mTexture, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL, RegionCount, vkRegions.data());
}

void cross::VulkanCommandList::CopyTextureToBuffer(NGIBuffer* dstBuffer, NGITexture* srcTexture, UInt32 regionCount, const NGICopyBufferTexture* regions)
{
    auto& desc = srcTexture->GetDesc();
    auto texelBlock = GetFormatTexelBlockProperty(desc.Format);

    std::vector<VkBufferImageCopy> vkRegions{regionCount};

    std::transform(regions, regions + regionCount, vkRegions.begin(), [&](const NGICopyBufferTexture& region) {
        Assert(region.BufferRowPitch % texelBlock.Size == 0);
        auto [mip, layer, plane] = NGIDecomposeSubresource(region.TextureSubresource, desc.MipCount, desc.ArraySize);
        VkImageAspectFlags aspect = FormatHasDepthOrStencil(desc.Format) ? (plane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT) : VK_IMAGE_ASPECT_COLOR_BIT;
        return VkBufferImageCopy{
            region.BufferOffset,
            (region.BufferRowPitch / texelBlock.Size) * texelBlock.Width,
            ((region.TextureExtent.Height - 1) / texelBlock.Height + 1) * texelBlock.Height,
            {
                aspect,
                mip,
                layer,
                1,
            },
            {
                region.TextureOffset.X,
                region.TextureOffset.Y,
                region.TextureOffset.Z,
            },
            {
                region.TextureExtent.Width,
                region.TextureExtent.Height,
                region.TextureExtent.Depth,
            },
        };
    });

    vkCmdCopyImageToBuffer(mCommandList, rhi_cast<VulkanTexture*>(srcTexture)->mTexture, VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL, reinterpret_cast<VkBuffer>(dstBuffer->GetNativeHandle()), regionCount, vkRegions.data());
}

void cross::VulkanCommandList::CopyTextureToTexture(NGITexture* dstTexture, NGITexture* srcTexture, UInt32 regionCount, const NGICopyTexture* regions)
{
    auto& srcDesc = srcTexture->GetDesc();
    auto& dstDesc = dstTexture->GetDesc();
    std::vector<VkImageCopy> vkRegions{regionCount};

    std::transform(regions, regions + regionCount, vkRegions.begin(), [&](const NGICopyTexture& region) {
        auto [srcMip, srcLayer, srcPlane] = NGIDecomposeSubresource(region.SrcSubresource, srcDesc.MipCount, srcDesc.ArraySize);
        auto [dstMip, dstLayer, dstPlane] = NGIDecomposeSubresource(region.DstSubresource, dstDesc.MipCount, dstDesc.ArraySize);
        VkImageAspectFlags srcAspect = FormatHasDepthOrStencil(srcDesc.Format) ? (srcPlane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT) : VK_IMAGE_ASPECT_COLOR_BIT;
        VkImageAspectFlags dstAspect = FormatHasDepthOrStencil(dstDesc.Format) ? (dstPlane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT) : VK_IMAGE_ASPECT_COLOR_BIT;
        return VkImageCopy{{
                               srcAspect,
                               srcMip,
                               srcLayer,
                               1,
                           },
                           {
                               region.SrcOffset.X,
                               region.SrcOffset.Y,
                               region.SrcOffset.Z,
                           },
                           {
                               dstAspect,
                               dstMip,
                               dstLayer,
                               1,
                           },
                           {
                               region.DstOffset.X,
                               region.DstOffset.Y,
                               region.DstOffset.Z,
                           },
                           {
                               region.Extent.Width,
                               region.Extent.Height,
                               region.Extent.Depth,
                           }};
    });

    static auto Equal = Overloaded
    {
        [] <typename T>(const T & t1, const T & t2)
        {
            return memcmp(&t1, &t2, sizeof(T)) == 0;
        },
        [](const VkImageSubresourceLayers& layer1, const VkImageSubresourceLayers& layer2)
        {
            return layer1.mipLevel == layer2.mipLevel &&
                layer1.baseArrayLayer == layer2.baseArrayLayer &&
                layer1.layerCount == layer2.layerCount;
        },
    };

    // merge aspects
    for (auto mergedItr = vkRegions.end(), itr = vkRegions.begin(); itr != vkRegions.end(); )
    {
        if (mergedItr != vkRegions.end() &&
            Equal(itr->srcSubresource, mergedItr->srcSubresource) &&
            Equal(itr->srcOffset, mergedItr->srcOffset) &&
            Equal(itr->dstSubresource, mergedItr->dstSubresource) &&
            Equal(itr->dstOffset, mergedItr->dstOffset) &&
            Equal(itr->extent, mergedItr->extent))
        {
            Assert(itr->srcSubresource.aspectMask == itr->dstSubresource.aspectMask);
            Assert(mergedItr->srcSubresource.aspectMask == mergedItr->dstSubresource.aspectMask);

            mergedItr->srcSubresource.aspectMask |= itr->srcSubresource.aspectMask;
            mergedItr->dstSubresource.aspectMask |= itr->dstSubresource.aspectMask;
            itr = vkRegions.erase(itr);
        }
        else
        {
            mergedItr = itr++;
        }
    }

    vkCmdCopyImage(mCommandList,
                   reinterpret_cast<VkImage>(srcTexture->GetNativeHandle()),
                   VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
                   reinterpret_cast<VkImage>(dstTexture->GetNativeHandle()),
                   VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
                   static_cast<uint32_t>(vkRegions.size()),
                   vkRegions.data());
}

void cross::VulkanCommandList::ClearTexture(NGITextureView* textureView, const NGIClearValue& clearValue)
{
    auto* vkTexture = rhi_cast<VulkanTexture*>(textureView->GetTexture());
    if (auto textureFormat = vkTexture->GetDesc().Format; FormatHasDepthOrStencil(textureFormat))
    {
        VkClearDepthStencilValue vkClearValue{ clearValue.depthStencil.depth, clearValue.depthStencil.stencil, };

        auto& viewSubRange = textureView->GetDesc().SubRange;
        VkImageSubresourceRange range
        {
            MapTextureAspect(viewSubRange.Aspect),
            viewSubRange.MostDetailedMip,
            viewSubRange.MipLevels,
            viewSubRange.FirstArraySlice,
            viewSubRange.ArraySize,
        };

        Assert((range.aspectMask & (VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT)) == range.aspectMask);

        vkCmdClearDepthStencilImage(mCommandList, vkTexture->mTexture, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL, &vkClearValue, 1, &range);
    }
    else
    {
        VkClearColorValue vkClearColor{};
        switch (GetFormatCompType(textureFormat))
        {
        case GraphicsFormat::CompTypeUNorm:
        case GraphicsFormat::CompTypeSNorm:
        case GraphicsFormat::CompTypeSRGB:
        case GraphicsFormat::CompTypeSFloat:
            std::copy_n(clearValue.colorf, 4, vkClearColor.float32);
            break;
        case GraphicsFormat::CompTypeUInt:
            std::copy_n(clearValue.coloru, 4, vkClearColor.uint32);
            break;
        case GraphicsFormat::CompTypeSInt:
            std::copy_n(clearValue.colori, 4, vkClearColor.int32);
            break;
        default:
            LOG_ERROR("Error clear color format");
            break;
        }

        auto& viewSubRange = textureView->GetDesc().SubRange;
        VkImageSubresourceRange range
        {
            MapTextureAspect(viewSubRange.Aspect),
            viewSubRange.MostDetailedMip,
            viewSubRange.MipLevels,
            viewSubRange.FirstArraySlice,
            viewSubRange.ArraySize,
        };

        Assert(range.aspectMask == VK_IMAGE_ASPECT_COLOR_BIT);

        vkCmdClearColorImage(mCommandList, vkTexture->mTexture, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL, &vkClearColor, 1, &range);
    }
}

void cross::VulkanCommandList::ClearBuffer(NGIBufferView* bufferView, UInt32 clearValue)
{
    auto* vkBufferView = rhi_cast<VulkanBufferView*>(bufferView);
    vkCmdFillBuffer(mCommandList, vkBufferView->mBuffer, vkBufferView->GetDesc().BufferLocation, vkBufferView->GetDesc().SizeInBytes, clearValue);
}

void cross::VulkanCommandList::End()
{
    GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnEndCommandBuffer(mQueue, mCommandList);
    vkEndCommandBuffer(mCommandList);
}

void cross::VulkanCommandList::Reset()
{
    mGraphicsPipeline = nullptr;
    mRenderPass = VK_NULL_HANDLE;
    mSubpass = 0;
    mFramebuffer = VK_NULL_HANDLE;
    Assert(mBundleCommandLists.empty());
    vkResetCommandBuffer(mCommandList, VK_COMMAND_BUFFER_RESET_RELEASE_RESOURCES_BIT);
}

/*
 * TODO(peterwjma), support Queue Family Transfer
 */
void cross::VulkanCommandList::ResourceBarrier1(UInt32 bufferBarrierCount, NGIBufferBarrier* bufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* textureBarriers)
{
    if (mRenderPass != VK_NULL_HANDLE)
    {
        return;
    }

    if (bufferBarrierCount == 0 && textureBarrierCount == 0)
    {
        return;
    }

    std::vector<VkBufferMemoryBarrier> vkBufferBarriers;
    vkBufferBarriers.reserve(bufferBarrierCount);
    std::vector<VkImageMemoryBarrier> vkTextureBarriers;
    vkTextureBarriers.reserve(textureBarrierCount);

    VkPipelineStageFlags globalSrcStage = 0, globalDstStage = 0;

    for (auto i = 0u; i < bufferBarrierCount; i++)
    {
        auto& barrier = bufferBarrieres[i];

        if(!barrier.Buffer) continue;

        auto vkBuffer = reinterpret_cast<VkBuffer>(barrier.Buffer->GetNativeHandle());

#if NGI_DEBUG_SYNCHRONIZATION
        LOG_TRACE("Transit buffer: {}[{}] from {} to {}", VkFormatHandle(vkBuffer), barrier.Buffer->GetDebugName(), ToString(barrier.StateBefore), ToString(barrier.StateAfter));
#endif

        //if (barrier.StateBefore == NGIResourceState::Undefined)
        //{
        //    continue;
        //}

        auto [srcStage, srcAccess] = GetBufferStatePipelineStageAccess(barrier.StateBefore);
        globalSrcStage |= srcStage;
        auto [dstStage, dstAccess] = GetBufferStatePipelineStageAccess(barrier.StateAfter);
        globalDstStage |= dstStage;

        vkBufferBarriers.push_back({
            VK_STRUCTURE_TYPE_BUFFER_MEMORY_BARRIER,
            nullptr,
            srcAccess,
            dstAccess,
            VK_QUEUE_FAMILY_IGNORED,
            VK_QUEUE_FAMILY_IGNORED,
            vkBuffer,
            0,
            VK_WHOLE_SIZE,
        });
    }

    for (auto i = 0u; i < textureBarrierCount; i++)
    {
        auto& barrier = textureBarriers[i];
        auto* vkTexture = rhi_cast<VulkanTexture*>(barrier.Texture);

#if NGI_DEBUG_SYNCHRONIZATION
        LOG_TRACE("Transite texture:{}[{}:{}] from: {} to: {}", VkFormatHandle(vkTexture->mTexture), vkTexture->GetDebugName(), barrier.Subresource, ToString(barrier.StateBefore), ToString(barrier.StateAfter));
#endif

        auto& desc = barrier.Texture->GetDesc();
        auto [hasDepth, hasStencil] = FormatHasDepthStencil(desc.Format);

        VkImageSubresourceRange range{};
        if (barrier.Subresource != NGIAllSubresources)
        {
            auto [mip, layer, plane] = NGIDecomposeSubresource(barrier.Subresource, desc.MipCount, desc.ArraySize);
            if (hasDepth)
            {
                if (hasStencil)
                {
                    range.aspectMask = plane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT;
                }
                else
                {
                    range.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT;
                }
            }
            else
            {
                range.aspectMask = hasStencil ? VK_IMAGE_ASPECT_STENCIL_BIT : VK_IMAGE_ASPECT_COLOR_BIT;
            }
            range.baseMipLevel = mip;
            range.levelCount = 1;
            range.baseArrayLayer = layer;
            range.layerCount = 1;
        }
        else
        {
            if (hasDepth && hasStencil)
            {
                AssertMsg(false, "Multi plane texture don't support barrier with subresource == NGIAllSubresources");
                continue;
            }
            else if (hasDepth)
            {
                range.aspectMask |= VK_IMAGE_ASPECT_DEPTH_BIT;
            }
            else if (hasStencil)
            {
                range.aspectMask |= VK_IMAGE_ASPECT_STENCIL_BIT;
            }
            else
            {
                range.aspectMask |= VK_IMAGE_ASPECT_COLOR_BIT;
            }
            range.levelCount = VK_REMAINING_MIP_LEVELS;
            range.layerCount = VK_REMAINING_ARRAY_LAYERS;
        }

        auto [srcStage, srcAccess, srcLayout] = GetTexturePipelineStageAndAccessAndLayout(barrier.StateBefore, static_cast<VkImageAspectFlagBits>(range.aspectMask), hasDepth, hasStencil, true);
        globalSrcStage |= srcStage;
        auto [dstStage, dstAccess, dstLayout] = GetTexturePipelineStageAndAccessAndLayout(barrier.StateAfter, static_cast<VkImageAspectFlagBits>(range.aspectMask), hasDepth, hasStencil, false);
        globalDstStage |= dstStage;

        vkTextureBarriers.push_back({
            VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER,
            nullptr,
            srcAccess,
            dstAccess,
            barrier.Discard ? VK_IMAGE_LAYOUT_UNDEFINED : srcLayout,
            dstLayout,
            VK_QUEUE_FAMILY_IGNORED,
            VK_QUEUE_FAMILY_IGNORED,
            vkTexture->mTexture,
            range,
        });
    }

    /*
     merge seperate barriers of depth/stencil texture
     1. from depth/stencil target to depth shader resource (RAW)
        1. VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT to VK_IMAGE_LAYOUT_SHADER_READ_ONLY
     2. from depth/stencil target to stencil shader resource (RAW)
     3. from depth/stencil target to depth/stencil target (WAW)

     vulkan1.1 only has 4 layout for depth/stencil, depth only or stencil only texture
     depth stencil optimal
     depth read-only stencil optimal
     depth optimal stencil read-only
     depth stenicl read-only
     render pass treat all depth/stencil texture as a writable layout
     */

    if (GetDevice<VulkanDevice>()->GetVulkanVersion() < VK_API_VERSION_1_2)
    {
        auto* barrier = textureBarriers;
        for (auto itr = vkTextureBarriers.begin(), mergedItr = vkTextureBarriers.end(); itr != vkTextureBarriers.end(); ++barrier)
        {
            if (auto [hasDepth, hasStencil] = FormatHasDepthStencil(barrier->Texture->GetDesc().Format); hasDepth && hasStencil)
            {
                if (mergedItr != vkTextureBarriers.end())
                {
                    if (mergedItr->image == itr->image &&
                        mergedItr->subresourceRange.baseMipLevel == itr->subresourceRange.baseMipLevel &&
                        mergedItr->subresourceRange.levelCount == itr->subresourceRange.levelCount &&
                        mergedItr->subresourceRange.baseArrayLayer == itr->subresourceRange.baseArrayLayer &&
                        mergedItr->subresourceRange.baseMipLevel == itr->subresourceRange.baseMipLevel)
                    {
                        mergedItr->srcAccessMask |= itr->srcAccessMask;
                        mergedItr->dstAccessMask |= itr->dstAccessMask;
                        mergedItr->oldLayout = MergeDepthStencilLayout(mergedItr->oldLayout, itr->oldLayout);
                        mergedItr->newLayout = MergeDepthStencilLayout(mergedItr->newLayout, itr->newLayout);
                        mergedItr->subresourceRange.aspectMask |= itr->subresourceRange.aspectMask;
                        itr = vkTextureBarriers.erase(itr);
                        continue;
                    }
                }
                mergedItr = itr;
                // depth/stencil format must has a full layout
                mergedItr->oldLayout = ComplementDepthStencilLayout(mergedItr->oldLayout);
                mergedItr->newLayout = ComplementDepthStencilLayout(mergedItr->newLayout);
                mergedItr->subresourceRange.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT;
            }
            else
            {
                // although it's a depth only or stencil only texture, also treat it as a depth/stencil layout
                if (hasDepth || hasStencil)
                {
                    itr->oldLayout = ComplementDepthStencilLayout(itr->oldLayout);
                    itr->newLayout = ComplementDepthStencilLayout(itr->newLayout);
                }
                mergedItr = vkTextureBarriers.end();
            }
            ++itr;
        }
    }

    if (!VulkanCapability::Inst().GeometryShader)
    {
        EnumRemoveFlags(globalSrcStage, (UInt32)VK_PIPELINE_STAGE_GEOMETRY_SHADER_BIT);
        EnumRemoveFlags(globalDstStage, (UInt32)VK_PIPELINE_STAGE_GEOMETRY_SHADER_BIT);
    }

    if (!VulkanCapability::Inst().TessellationShader)
    {
        EnumRemoveFlags(globalSrcStage, (UInt32)VK_PIPELINE_STAGE_TESSELLATION_CONTROL_SHADER_BIT | VK_PIPELINE_STAGE_TESSELLATION_EVALUATION_SHADER_BIT);
        EnumRemoveFlags(globalDstStage, (UInt32)VK_PIPELINE_STAGE_TESSELLATION_CONTROL_SHADER_BIT | VK_PIPELINE_STAGE_TESSELLATION_EVALUATION_SHADER_BIT);
    }

#if NGI_DEBUG_SYNCHRONIZATION
    for (auto& barrier : vkTextureBarriers)
    {
        LOG_DEBUG("Transform layout of texture: {}, aspect: {} from {} to {}",
            VkFormatHandle(barrier.image), barrier.subresourceRange.aspectMask, vk::to_string(static_cast<vk::ImageLayout>(barrier.oldLayout)), vk::to_string(static_cast<vk::ImageLayout>(barrier.newLayout)));
    }
#endif

    if (vkBufferBarriers.empty() && vkTextureBarriers.empty())
    {
        return;
    }

    vkCmdPipelineBarrier(mCommandList, globalSrcStage, globalDstStage, 0, 0, nullptr, static_cast<UInt32>(vkBufferBarriers.size()), vkBufferBarriers.data(), static_cast<UInt32>(vkTextureBarriers.size()), vkTextureBarriers.data());
}

void cross::VulkanCommandList::ResourceBarrier2(UInt32 bufferBarrierCount, NGIBufferBarrier* bufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* textureBarriers)
{

    if (mRenderPass != VK_NULL_HANDLE)
    {
        return;
    }

    if (bufferBarrierCount == 0 && textureBarrierCount == 0)
    {
        return;
    }

    std::vector<VkBufferMemoryBarrier2KHR> vkBufferBarriers2;
    vkBufferBarriers2.reserve(bufferBarrierCount);
    std::vector<VkImageMemoryBarrier2KHR> vkTextureBarriers2;
    vkTextureBarriers2.reserve(textureBarrierCount);

    for (auto i = 0u; i < bufferBarrierCount; i++)
    {
        auto& barrier = bufferBarrieres[i];

        if (!barrier.Buffer)
            continue;

        auto vkBuffer = reinterpret_cast<VkBuffer>(barrier.Buffer->GetNativeHandle());

#if NGI_DEBUG_SYNCHRONIZATION
        LOG_TRACE("Transit buffer: {}[{}] from {} to {}", VkFormatHandle(vkBuffer), barrier.Buffer->GetDebugName(), ToString(barrier.StateBefore), ToString(barrier.StateAfter));
#endif

        // if (barrier.StateBefore == NGIResourceState::Undefined)
        //{
        //     continue;
        // }

        auto [srcStage, srcAccess] = GetBufferStatePipelineStageAccess2(barrier.StateBefore);
        auto [dstStage, dstAccess] = GetBufferStatePipelineStageAccess2(barrier.StateAfter);

        vkBufferBarriers2.push_back({VK_STRUCTURE_TYPE_BUFFER_MEMORY_BARRIER_2_KHR, nullptr, srcStage, srcAccess, dstStage, dstAccess, VK_QUEUE_FAMILY_IGNORED, VK_QUEUE_FAMILY_IGNORED, vkBuffer, 0, VK_WHOLE_SIZE});
    }

    for (auto i = 0u; i < textureBarrierCount; i++)
    {
        auto& barrier = textureBarriers[i];
        auto* vkTexture = rhi_cast<VulkanTexture*>(barrier.Texture);

#if NGI_DEBUG_SYNCHRONIZATION
        LOG_TRACE("Transite texture:{}[{}:{}] from: {} to: {}", VkFormatHandle(vkTexture->mTexture), vkTexture->GetDebugName(), barrier.Subresource, ToString(barrier.StateBefore), ToString(barrier.StateAfter));
#endif

        auto& desc = barrier.Texture->GetDesc();
        auto [hasDepth, hasStencil] = FormatHasDepthStencil(desc.Format);

        VkImageSubresourceRange range{};
        if (barrier.Subresource != NGIAllSubresources)
        {
            auto [mip, layer, plane] = NGIDecomposeSubresource(barrier.Subresource, desc.MipCount, desc.ArraySize);
            if (hasDepth)
            {
                if (hasStencil)
                {
                    range.aspectMask = plane == 0 ? VK_IMAGE_ASPECT_DEPTH_BIT : VK_IMAGE_ASPECT_STENCIL_BIT;
                }
                else
                {
                    range.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT;
                }
            }
            else
            {
                range.aspectMask = hasStencil ? VK_IMAGE_ASPECT_STENCIL_BIT : VK_IMAGE_ASPECT_COLOR_BIT;
            }
            range.baseMipLevel = mip;
            range.levelCount = 1;
            range.baseArrayLayer = layer;
            range.layerCount = 1;
        }
        else
        {
            if (hasDepth && hasStencil)
            {
                AssertMsg(false, "Multi plane texture don't support barrier with subresource == NGIAllSubresources");
                continue;
            }
            else if (hasDepth)
            {
                range.aspectMask |= VK_IMAGE_ASPECT_DEPTH_BIT;
            }
            else if (hasStencil)
            {
                range.aspectMask |= VK_IMAGE_ASPECT_STENCIL_BIT;
            }
            else
            {
                range.aspectMask |= VK_IMAGE_ASPECT_COLOR_BIT;
            }
            range.levelCount = VK_REMAINING_MIP_LEVELS;
            range.layerCount = VK_REMAINING_ARRAY_LAYERS;
        }

        auto [srcStage, srcAccess, srcLayout] = GetTexturePipelineStageAndAccessAndLayout2(barrier.StateBefore, static_cast<VkImageAspectFlagBits>(range.aspectMask), hasDepth, hasStencil, true);
        auto [dstStage, dstAccess, dstLayout] = GetTexturePipelineStageAndAccessAndLayout2(barrier.StateAfter, static_cast<VkImageAspectFlagBits>(range.aspectMask), hasDepth, hasStencil, false);

        vkTextureBarriers2.push_back({
            VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER_2_KHR,
            nullptr,
            srcStage,
            srcAccess,
            dstStage,
            dstAccess,
            barrier.Discard ? VK_IMAGE_LAYOUT_UNDEFINED : srcLayout,
            dstLayout,
            VK_QUEUE_FAMILY_IGNORED,
            VK_QUEUE_FAMILY_IGNORED,
            vkTexture->mTexture,
            range,
        });
    }

    /*
     merge seperate barriers of depth/stencil texture
     1. from depth/stencil target to depth shader resource (RAW)
        1. VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT to VK_IMAGE_LAYOUT_SHADER_READ_ONLY
     2. from depth/stencil target to stencil shader resource (RAW)
     3. from depth/stencil target to depth/stencil target (WAW)

     vulkan1.1 only has 4 layout for depth/stencil, depth only or stencil only texture
     depth stencil optimal
     depth read-only stencil optimal
     depth optimal stencil read-only
     depth stenicl read-only
     render pass treat all depth/stencil texture as a writable layout
     */

    if (GetDevice<VulkanDevice>()->GetVulkanVersion() < VK_API_VERSION_1_2)
    {
        auto* barrier = textureBarriers;
        for (auto itr = vkTextureBarriers2.begin(), mergedItr = vkTextureBarriers2.end(); itr != vkTextureBarriers2.end(); ++barrier)
        {
            if (auto [hasDepth, hasStencil] = FormatHasDepthStencil(barrier->Texture->GetDesc().Format); hasDepth && hasStencil)
            {
                if (mergedItr != vkTextureBarriers2.end())
                {
                    if (mergedItr->image == itr->image && mergedItr->subresourceRange.baseMipLevel == itr->subresourceRange.baseMipLevel && mergedItr->subresourceRange.levelCount == itr->subresourceRange.levelCount &&
                        mergedItr->subresourceRange.baseArrayLayer == itr->subresourceRange.baseArrayLayer && mergedItr->subresourceRange.baseMipLevel == itr->subresourceRange.baseMipLevel)
                    {
                        mergedItr->srcAccessMask |= itr->srcAccessMask;
                        mergedItr->dstAccessMask |= itr->dstAccessMask;
                        mergedItr->oldLayout = MergeDepthStencilLayout(mergedItr->oldLayout, itr->oldLayout);
                        mergedItr->newLayout = MergeDepthStencilLayout(mergedItr->newLayout, itr->newLayout);
                        mergedItr->subresourceRange.aspectMask |= itr->subresourceRange.aspectMask;
                        itr = vkTextureBarriers2.erase(itr);
                        continue;
                    }
                }
                mergedItr = itr;
                // depth/stencil format must has a full layout
                mergedItr->oldLayout = ComplementDepthStencilLayout(mergedItr->oldLayout);
                mergedItr->newLayout = ComplementDepthStencilLayout(mergedItr->newLayout);
                mergedItr->subresourceRange.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT;
            }
            else
            {
                // although it's a depth only or stencil only texture, also treat it as a depth/stencil layout
                if (hasDepth || hasStencil)
                {
                    itr->oldLayout = ComplementDepthStencilLayout(itr->oldLayout);
                    itr->newLayout = ComplementDepthStencilLayout(itr->newLayout);
                }
                mergedItr = vkTextureBarriers2.end();
            }
            ++itr;
        }
    }
#if NGI_DEBUG_SYNCHRONIZATION
    for (auto& barrier : vkTextureBarriers)
    {
        LOG_DEBUG("Transform layout of texture: {}, aspect: {} from {} to {}",
                  VkFormatHandle(barrier.image),
                  barrier.subresourceRange.aspectMask,
                  vk::to_string(static_cast<vk::ImageLayout>(barrier.oldLayout)),
                  vk::to_string(static_cast<vk::ImageLayout>(barrier.newLayout)));
    }
#endif

    if (vkBufferBarriers2.empty() && vkTextureBarriers2.empty())
    {
        return;
    }

    VkDependencyInfoKHR vkDependencyInfo{
        VK_STRUCTURE_TYPE_DEPENDENCY_INFO_KHR, nullptr, 0, 0, nullptr, static_cast<UInt32>(vkBufferBarriers2.size()), vkBufferBarriers2.data(), static_cast<UInt32>(vkTextureBarriers2.size()), vkTextureBarriers2.data()};

    vkCmdPipelineBarrier2KHR(mCommandList, &vkDependencyInfo);
}

/*
 * TODO(peterwjma), support Queue Family Transfer
 */
void cross::VulkanCommandList::ResourceBarrier(UInt32 bufferBarrierCount, NGIBufferBarrier* bufferBarrieres, UInt32 textureBarrierCount, NGITextureBarrier* textureBarriers)
{
    if (!VulkanCapability::Inst().Synchronization2)
    {
        ResourceBarrier1(bufferBarrierCount, bufferBarrieres, textureBarrierCount, textureBarriers);
    }
    else
    {
        ResourceBarrier2(bufferBarrierCount, bufferBarrieres, textureBarrierCount, textureBarriers);
    }
}

void cross::VulkanCommandList::MemBarrier(NGIMemoryBarrier* pMemBarrier)
{
    auto [srcStage, srcAccess] = GetBufferStatePipelineStageAccess(pMemBarrier->StateBefore);
    auto [dstStage, dstAccess] = GetBufferStatePipelineStageAccess(pMemBarrier->StateAfter);

    VkMemoryBarrier barrier = {
        .sType = VK_STRUCTURE_TYPE_MEMORY_BARRIER,
        .srcAccessMask = srcAccess,
        .dstAccessMask = dstAccess
    };
    vkCmdPipelineBarrier(mCommandList, srcStage, dstStage, 0, 1, &barrier, 0, nullptr, 0, nullptr);
}

void cross::VulkanCommandList::SetRayTracingPipeline(NGIRayTracingPipelineState* pPipelineState)
{
    mRayTracingPipeline = rhi_cast<VulkanRayTracingPipelineState*>(pPipelineState);
    vkCmdBindPipeline(mCommandList, VK_PIPELINE_BIND_POINT_RAY_TRACING_KHR, mRayTracingPipeline->mPipeline);
}

void cross::VulkanCommandList::SetRayTracingResourceGroup(UInt32 slot, NGIResourceGroup* pResourceGroup)
{
    auto* vkResourceGroup = rhi_cast<VulkanResourceGroup*>(pResourceGroup);
    auto* vkPipelineLayout = rhi_cast<VulkanPipelineLayout*>(mRayTracingPipeline->GetDesc().PipelineLayout);
    vkCmdBindDescriptorSets(mCommandList, VK_PIPELINE_BIND_POINT_RAY_TRACING_KHR,
        vkPipelineLayout->mPipelineLayout, slot, 1, &vkResourceGroup->mDescriptorSet,
        static_cast<uint32_t>(vkResourceGroup->mDynamicOffsets.size()), vkResourceGroup->mDynamicOffsets.data());
}

void cross::VulkanCommandList::BuildBottomLevelAccelStruct(NGIAccelStruct* AS, const NGIGeometryDesc* pGeometries, size_t NumGeometries,
    NGIAccelStructBuildFlag BuildFlags, NGIBuffer* ScratchBuffer)
{
    VulkanAccelStruct* accel = rhi_cast<VulkanAccelStruct*>(AS);

    const bool performUpdate = EnumHasAnyFlags(BuildFlags, NGIAccelStructBuildFlag::PerformUpdate);
    if (performUpdate)
    {
        Assert(accel->mAllowUpdate);
    }

    std::vector<VkAccelerationStructureGeometryKHR> geometries(NumGeometries);
    // std::vector<VkAccelerationStructureTrianglesOpacityMicromapEXT> opacityMicroMaps;  // TODO(scolu): Support OMM
    std::vector<VkAccelerationStructureBuildRangeInfoKHR> buildRanges(NumGeometries);
    std::vector<uint32_t> maxPrimitiveCounts(NumGeometries);

    VkDevice device = GetDevice<VulkanDevice>()->Get();
    std::vector<NGIBufferBarrier> geoBufferBarriers;
    for (size_t i = 0; i < NumGeometries; i++)
    {
        MapVKGeometry(pGeometries[i], geometries[i], maxPrimitiveCounts[i], &buildRanges[i]);
        if (pGeometries[i].GeometryData.Triangle.VertexBuffer)
        {
            geoBufferBarriers.push_back(
                {
                    .Buffer = pGeometries[i].GeometryData.Triangle.VertexBuffer,
                    .StateBefore = NGIResourceState::VertexBuffer,
                    .StateAfter = NGIResourceState::AccelStructBuildInputBit
                }
                );
        }
        if (pGeometries[i].GeometryData.Triangle.IndexBuffer)
        {
            geoBufferBarriers.push_back(
                {
                    .Buffer = pGeometries[i].GeometryData.Triangle.IndexBuffer,
                    .StateBefore = NGIResourceState::IndexBuffer,
                    .StateAfter = NGIResourceState::AccelStructBuildInputBit
                }
            );
        }
    }

    // Construct Acceleration Structure Build Geometry Info
    VkBuildAccelerationStructureFlagsKHR accelBuildFlags = MapVKASBuildFlags(BuildFlags);
    if (accel->mAllowUpdate)
    {
        accelBuildFlags |= VK_BUILD_ACCELERATION_STRUCTURE_ALLOW_UPDATE_BIT_KHR;
    }
    VkAccelerationStructureBuildGeometryInfoKHR buildInfo{
        .sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_GEOMETRY_INFO_KHR,
        .type = VK_ACCELERATION_STRUCTURE_TYPE_BOTTOM_LEVEL_KHR,
        .flags = accelBuildFlags,
        .mode = performUpdate ? VK_BUILD_ACCELERATION_STRUCTURE_MODE_UPDATE_KHR : VK_BUILD_ACCELERATION_STRUCTURE_MODE_BUILD_KHR,
        .srcAccelerationStructure = performUpdate ? accel->mAccelStruct : VK_NULL_HANDLE,
        .dstAccelerationStructure = accel->mAccelStruct,
        .geometryCount = static_cast<UInt32>(geometries.size()),
        .pGeometries = geometries.data(),
    };

    // TODO(scolu): Is necessary? if data buffer is newly created it's redundant?
    NGIBufferBarrier dataBufferBarrier{
        .Buffer = accel->mDataBuffer,
        .StateBefore = performUpdate ?  NGIResourceState::AccelStructBuildInputBit : NGIResourceState::Undefined,
        .StateAfter = NGIResourceState::AccelStructWrite
    };
    ResourceBarrier(1, &dataBufferBarrier, 0, nullptr);
    
    if (accel->mSizeInfo.AccelStructSize > accel->mDataBuffer->GetSize())
    {
        LOG_ERROR("Acceleration structure size exceeds buffer size: required {} bytes, but buffer only has {} bytes. AS name: '{}'",
                  accel->mSizeInfo.AccelStructSize,
                  accel->mDataBuffer->GetSize(),
                  accel->GetDebugName());
        Assert(false);
    }

    size_t alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mAccelerationStructureProps.minAccelerationStructureScratchOffsetAlignment;
    
    // TODO(scolu): scratch buffer manager
    if (!ScratchBuffer)
    {
        size_t scratchBufferSize = performUpdate ? accel->mSizeInfo.UpdateScratchSize : accel->mSizeInfo.BuildScratchSize;
        scratchBufferSize = AlignSize(scratchBufferSize, alignment);

        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        VulkanBuffer* scratchBuffer = new VulkanBuffer(
            scratchBufferDesc,
            GetDevice<VulkanDevice>(),
            "BLASBuildScratchBuffer"
        );
        buildInfo.scratchData.deviceAddress = scratchBuffer->GetDeviceAddress();
    }
    else
    {
        buildInfo.scratchData.deviceAddress = dynamic_cast<VulkanBuffer*>(ScratchBuffer)->GetDeviceAddress();
    }

    // NOTE: Buffer memory alignment is managed by VMA pool,
    //  which ensures proper alignment based on device requirements
    Assert(buildInfo.scratchData.deviceAddress % alignment == 0);

    ResourceBarrier(static_cast<UInt32>(geoBufferBarriers.size()), geoBufferBarriers.data(), 0, nullptr);

    std::array<VkAccelerationStructureBuildGeometryInfoKHR, 1> buildInfos{buildInfo};
    std::array<VkAccelerationStructureBuildRangeInfoKHR*, 1> buildRangeArray{buildRanges.data()};
    vkCmdBuildAccelerationStructuresKHR(
        mCommandList, 
        static_cast<uint32_t>(buildInfos.size()), 
        buildInfos.data(), 
        buildRangeArray.data());

    // Resume vertex/index buffer state
    std::vector<NGIBufferBarrier> geoBufferBarriersAfter(geoBufferBarriers);
    std::ranges::for_each(geoBufferBarriersAfter.begin(), geoBufferBarriersAfter.end(), [](NGIBufferBarrier& barrier) {
        std::swap(barrier.StateBefore, barrier.StateAfter);
    });
    ResourceBarrier(static_cast<UInt32>(geoBufferBarriersAfter.size()), geoBufferBarriersAfter.data(), 0, nullptr);

    NGIBufferBarrier dataBufferBarrierAfter{
        .Buffer = accel->mDataBuffer,
        .StateBefore = NGIResourceState::AccelStructWrite,
        .StateAfter = NGIResourceState::AccelStructBuildInputBit
    };
    ResourceBarrier(1, &dataBufferBarrierAfter, 0, nullptr);
}

void cross::VulkanCommandList::CompactBottomLevelAccelStruct()
{
    // TODO(scolu)
}

void cross::VulkanCommandList::BuildTopLevelAccelStruct(NGIAccelStruct* AS, const NGIInstanceDesc* pInstances, size_t NumInstances,
    NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer)
{
    VulkanAccelStruct* accel = rhi_cast<VulkanAccelStruct*>(AS);
    accel->mInstances.resize(NumInstances);

    for (size_t i = 0; i < NumInstances; i++)
    {
        const NGIInstanceDesc& src = pInstances[i];
        VkAccelerationStructureInstanceKHR& dst = accel->mInstances[i];

        memcpy(dst.transform.matrix, src.Transform, sizeof(float) * 12);
        dst.instanceCustomIndex = src.InstanceID;
        dst.mask = src.InstanceMask;
        dst.instanceShaderBindingTableRecordOffset = src.InstanceContribToHitGroupIndex;
        dst.flags = MapGeometryInstanceFlag(src.Flag);
        if (src.BottomLevelAS)
        {
            auto blas = rhi_cast<VulkanAccelStruct*>(src.BottomLevelAS);
            dst.accelerationStructureReference = blas->mAccelStructDeviceAddress;
        }
        else
        {
            dst.accelerationStructureReference = 0;
        }
    }

    size_t uploadBufferSize = accel->GetTopLevelUploadBufferSize();
    NGIBufferDesc uploadBufferDesc{
        .Size = uploadBufferSize,
        .Usage = NGIBufferUsage::AccelStructBuildInputBuffer
    };
    // TODO(scolu): Manage upload buffer lift cycle
    VulkanStagingBuffer* uploadBuffer = new VulkanStagingBuffer(uploadBufferDesc, GetDevice<VulkanDevice>(), "BuildTLASInstanceUploadBuffer");
    void* cpuVA = uploadBuffer->MapRange(NGIBufferUsage::CopySrc, 0, uploadBufferSize);
    memcpy(cpuVA, accel->mInstances.data(), uploadBufferSize);
    uploadBuffer->UnmapRange(0, uploadBufferSize);

    BuildTopLevelAccelStructInternal(AS, uploadBuffer->GetDeviceAddress(), NumInstances, BuildFlags, ScratchBuffer);
}

void cross::VulkanCommandList::BuildTopLevelAccelStructFromBuffer(NGIAccelStruct* AS, ScratchBufferWrap InstanceBuffer, UInt64 InstanceBufferOffset,
    const NGIInstanceDesc* pInstances, size_t NumInstances, NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer)
{
    VulkanAccelStruct* accel = rhi_cast<VulkanAccelStruct*>(AS);
    accel->mInstances.resize(NumInstances);

    for (size_t i = 0; i < NumInstances; i++)
    {
        const NGIInstanceDesc& src = pInstances[i];
        VkAccelerationStructureInstanceKHR& dst = accel->mInstances[i];

        memcpy(dst.transform.matrix, src.Transform, sizeof(float) * 12);
        dst.instanceCustomIndex = src.InstanceID;
        dst.mask = src.InstanceMask;
        dst.instanceShaderBindingTableRecordOffset = src.InstanceContribToHitGroupIndex;
        dst.flags = MapGeometryInstanceFlag(src.Flag);
        if (src.BottomLevelAS)
        {
            auto blas = rhi_cast<VulkanAccelStruct*>(src.BottomLevelAS);
            dst.accelerationStructureReference = blas->mAccelStructDeviceAddress;
        }
        else
        {
            dst.accelerationStructureReference = 0;
        }
    }

    InstanceBuffer.MemWrite(0, accel->mInstances.data(), accel->GetTopLevelUploadBufferSize());
    
    BuildTopLevelAccelStructInternal(AS, rhi_cast<VulkanBuffer*>(InstanceBuffer.GetNGIBuffer())->GetDeviceAddress() + InstanceBufferOffset, NumInstances, BuildFlags, ScratchBuffer);
}

void cross::VulkanCommandList::BuildTopLevelAccelStructInternal(NGIAccelStruct* AS, VkDeviceAddress InstanceData, size_t NumInstances,
    NGIAccelStructBuildFlag BuildFlags, void* ScratchBuffer)
{
    VulkanAccelStruct* accel = rhi_cast<VulkanAccelStruct*>(AS);

    const bool performUpdate = EnumHasAnyFlags(BuildFlags, NGIAccelStructBuildFlag::PerformUpdate);
    if (performUpdate)
    {
        Assert(accel->mAllowUpdate);
        Assert(accel->mInstances.size() == NumInstances);
    }

    VkDevice device = GetDevice<VulkanDevice>()->Get();

    VkAccelerationStructureGeometryKHR geometry{VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_KHR};
    geometry.geometryType = VK_GEOMETRY_TYPE_INSTANCES_KHR;
    VkAccelerationStructureGeometryInstancesDataKHR instancesData{VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_INSTANCES_DATA_KHR};
    instancesData.arrayOfPointers = VK_FALSE;
    instancesData.data.deviceAddress = InstanceData;
    geometry.geometry.instances = instancesData;

    // Construct Acceleration Structure Build Geometry Info
    VkAccelerationStructureBuildGeometryInfoKHR buildInfo{VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_GEOMETRY_INFO_KHR};
    buildInfo.type = VK_ACCELERATION_STRUCTURE_TYPE_TOP_LEVEL_KHR;
    buildInfo.flags = MapVKASBuildFlags(BuildFlags);
    buildInfo.mode = performUpdate ? VK_BUILD_ACCELERATION_STRUCTURE_MODE_UPDATE_KHR : VK_BUILD_ACCELERATION_STRUCTURE_MODE_BUILD_KHR;
    buildInfo.dstAccelerationStructure = accel->mAccelStruct;
    buildInfo.geometryCount = 1;
    buildInfo.pGeometries = &geometry;

    if (accel->mAllowUpdate)
    {
        buildInfo.flags |= VK_BUILD_ACCELERATION_STRUCTURE_ALLOW_UPDATE_BIT_KHR;
    }

    if (performUpdate)
    {
        buildInfo.srcAccelerationStructure = accel->mAccelStruct;
    }

    std::array<uint32_t, 1> maxPrimitiveCounts{static_cast<uint32_t>(NumInstances)};

    // VkAccelerationStructureBuildSizesInfoKHR buildSizes{VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_SIZES_INFO_KHR};
    // vkGetAccelerationStructureBuildSizesKHR(
    //     device,
    //     VK_ACCELERATION_STRUCTURE_BUILD_TYPE_DEVICE_KHR,
    //     &buildInfo,
    //     maxPrimitiveCounts.data(),
    //     &buildSizes
    // );

    if (accel->mSizeInfo.AccelStructSize > accel->mDataBuffer->GetSize())
    {
        LOG_ERROR("Acceleration structure size exceeds buffer size: required {} bytes, but buffer only has {} bytes. AS name: '{}'",
                  accel->mSizeInfo.AccelStructSize,
                  accel->mDataBuffer->GetSize(),
                  accel->GetDebugName());
        Assert(false);
    }

    size_t alignment = GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mAccelerationStructureProps.minAccelerationStructureScratchOffsetAlignment;
    size_t scratchBufferSize = performUpdate ? accel->mSizeInfo.UpdateScratchSize : accel->mSizeInfo.BuildScratchSize;
    scratchBufferSize = AlignSize(scratchBufferSize, alignment);

    if (!ScratchBuffer)
    {
        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        VulkanBuffer* scratchBuffer = new VulkanBuffer(
            scratchBufferDesc,
            GetDevice<VulkanDevice>(),
            "TLASBuildScratchBuffer"
        );
        buildInfo.scratchData.deviceAddress = scratchBuffer->GetDeviceAddress();
    }
    else
    {
        buildInfo.scratchData.deviceAddress = reinterpret_cast<VulkanBuffer*>(ScratchBuffer)->GetDeviceAddress();
    }

    Assert(buildInfo.scratchData.deviceAddress % alignment == 0);

    std::array<VkAccelerationStructureBuildRangeInfoKHR, 1> buildRange;
    buildRange[0].primitiveCount = static_cast<uint32_t>(NumInstances);
    buildRange[0].primitiveOffset = 0;
    buildRange[0].firstVertex = 0;
    buildRange[0].transformOffset = 0;

    std::array<VkAccelerationStructureBuildGeometryInfoKHR, 1> buildInfos{buildInfo};
    std::array<VkAccelerationStructureBuildRangeInfoKHR*, 1> buildRangeArray{buildRange.data()};

    vkCmdBuildAccelerationStructuresKHR(
        mCommandList,
        static_cast<uint32_t>(buildInfos.size()),
        buildInfos.data(),
        buildRangeArray.data()
    );
}

void cross::VulkanCommandList::BLASBarrier(UInt32 NumBlas, NGIAccelStruct* blases)
{
    std::vector<NGIBufferBarrier> barriers(NumBlas);
    for (UInt32 i = 0; i < NumBlas; i++)
    {
        barriers[i].Buffer = rhi_cast<VulkanAccelStruct*>(&blases[i])->mDataBuffer;
        barriers[i].StateBefore = NGIResourceState::AccelStructWrite;
        barriers[i].StateAfter = NGIResourceState::AccelStructBuildBLASBit | NGIResourceState::ShaderStageBitMask;
    }
    ResourceBarrier(NumBlas, barriers.data(), 0, nullptr);
}

void cross::VulkanCommandList::TLASBarrier(NGIAccelStruct* tlas)
{
    NGIBufferBarrier barrier{
        .Buffer = rhi_cast<VulkanAccelStruct*>(tlas)->mDataBuffer,
        .StateBefore = NGIResourceState::AccelStructWrite,
        .StateAfter = NGIResourceState::AccelStructReadBit | NGIResourceState::ShaderStageBitMask
    };
    ResourceBarrier(1, &barrier, 0, nullptr);
}

void cross::VulkanCommandList::TraceRays(NGIRayTracingPipelineState* pipelineState, uint32_t width, uint32_t height, uint32_t depth)
{
    VulkanRayTracingPipelineState* vulkanPipelineState = rhi_cast<VulkanRayTracingPipelineState*>(pipelineState);

    // Get SBT regions
    VkStridedDeviceAddressRegionKHR rayGenRegion = {};
    VkStridedDeviceAddressRegionKHR missRegion = {};
    VkStridedDeviceAddressRegionKHR hitRegion = {};
    VkStridedDeviceAddressRegionKHR callableRegion = {};

    vulkanPipelineState->GetShaderBindingTableRegions(
        &rayGenRegion, &missRegion, &hitRegion, &callableRegion);
    
    // Execute ray tracing
    vkCmdTraceRaysKHR(
        mCommandList,
        &rayGenRegion,
        &missRegion,
        &hitRegion,
        &callableRegion,
        width,
        height,
        depth
    );
}

void cross::VulkanCommandList::BeginDebugRegion(const char* label, const float* color, bool bAddTimeStamp)
{
    if (VulkanCapability::Inst().DebugUtil)
    {
        VkDebugUtilsLabelEXT labelInfo
        {
            VK_STRUCTURE_TYPE_DEBUG_UTILS_LABEL_EXT,
            nullptr,
            label,
            {
                color ? color[0] : 0,
                color ? color[1] : 0,
                color ? color[2] : 0,
                color ? color[3] : 0,
            },
        };
        vkCmdBeginDebugUtilsLabelEXT(mCommandList, &labelInfo);
    }

    if (bAddTimeStamp)
       GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnBeginDebugRegion(mQueue, mCommandList, label);
}

void cross::VulkanCommandList::EndDebugRegion(bool bAddTimeStamp)
{
    if (VulkanCapability::Inst().DebugUtil)
    {
        vkCmdEndDebugUtilsLabelEXT(mCommandList);
    }

    if (bAddTimeStamp)  
       GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnEndDebugRegion(mQueue, mCommandList);
}

void cross::VulkanCommandList::EndQuery(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index)
{
    // insert time stamp
    VkPipelineStageFlagBits vkQueryType = VK_PIPELINE_STAGE_ALL_COMMANDS_BIT;
    switch (type)
    {
    case cross::NGIQueryHeapType::TimeStamp:
    {
        vkQueryType = VK_PIPELINE_STAGE_ALL_COMMANDS_BIT;
        VulkanQueryHeap* vkQueryHeap = dynamic_cast<VulkanQueryHeap*>(queryHeap);
        vkCmdResetQueryPool(mCommandList, vkQueryHeap->mQueryPool, index, 1);
        vkCmdWriteTimestamp(mCommandList, VK_PIPELINE_STAGE_ALL_COMMANDS_BIT, vkQueryHeap->mQueryPool, index);
    }
    break;
    default:
        break;
    }
}

void cross::VulkanCommandList::ResolveQueryData(NGIQueryHeap* queryHeap, NGIQueryHeapType type, UInt32 index, UInt32 numQueries, NGIBuffer* dstBuffer, UInt64 DestinationBufferOffset)
{
    VulkanQueryHeap* vkQueryHeap = dynamic_cast<VulkanQueryHeap*>(queryHeap);
    vkCmdCopyQueryPoolResults(mCommandList, vkQueryHeap->mQueryPool, index, numQueries, reinterpret_cast<VkBuffer>(dstBuffer->GetNativeHandle()), DestinationBufferOffset, sizeof(UInt64), VK_QUERY_RESULT_64_BIT | VK_QUERY_RESULT_WAIT_BIT);
}

cross::VulkanCommandQueue::CommandPool::~CommandPool()
{
    for (auto& [pool, _] : mFreeCmdLists)
    {
        vkDestroyCommandPool(mQueue->GetDevice<VulkanDevice>()->Get(), pool, gVkAllocCallback);
    }

    Assert(mPendingCmdLists.size() == 0);
}

void cross::VulkanCommandQueue::CommandPool::OnBeginFrame(FrameParam* frameparam)
{
    while (!mPendingCmdLists.empty() && std::get<0>(mPendingCmdLists.front()) == CmdSettings::Inst().gMaxQueuedFrame - 1)
    {
        mFreeCmdLists.emplace_back(std::get<1>(mPendingCmdLists.front()), std::get<2>(mPendingCmdLists.front()));
        mPendingCmdLists.pop_front();
    }

    for (auto& [lifetime, pool, _] : mPendingCmdLists)
    {
        lifetime++;
    }
}

void cross::VulkanCommandQueue::CommandPool::EndFrame()
{
}

void cross::VulkanCommandQueue::CommandPool::Allocate(UInt32 num, void** ppCommandLists)
{
    //NGI_LOG_TRACE("Allocate Command List");

    std::lock_guard lock{mMutex};

    auto count = static_cast<SInt32>(num);
    while (!mFreeCmdLists.empty() && count--)
    {
        auto& [_, pool, cmdList] = mPendingCmdLists.emplace_back(0, std::get<0>(mFreeCmdLists.back()), std::get<1>(mFreeCmdLists.back()));
        cmdList.Reset();
        ppCommandLists[count] = &cmdList;
        mFreeCmdLists.pop_back();
    }

    if (0 < count)
    {
        for (SInt32 i = 0; i < count; i++)
        {
            VkCommandPool commandPool;
            VkCommandPoolCreateInfo commandPoolCreateInfo{
                VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO,
                nullptr,
                VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT,
                mQueue->mFamilyIndex,
            };
            VK_CHECK(vkCreateCommandPool(mQueue->GetDevice<VulkanDevice>()->Get(), &commandPoolCreateInfo, nullptr, &commandPool));

            auto& [_, pool, cmdList] = mPendingCmdLists.emplace_back(0, commandPool, VulkanCommandList{mLevel, commandPool, mQueue});
            cmdList.Reset();
            ppCommandLists[i] = &cmdList;
        }
    }
}

cross::VulkanFenceBase::VulkanFenceBase(VulkanDevice* pDevice) : NGIFence(pDevice)
{
}


cross::VulkanFence::VulkanFence(UInt64 initialValue, VulkanDevice* pDevice)
    : VulkanFenceBase{pDevice}
    , mCurrentValue{initialValue}
{
    NGI_LOG_DEBUG("Create fence");
    VK_CHECK(vkCreateFence(GetDevice<VulkanDevice>()->Get(), &gFenceCreateInfo, gVkAllocCallback, &mFence));
}

cross::VulkanFence::~VulkanFence()
{
    NGI_LOG_DEBUG("Destroy fence");
    vkDestroyFence(GetDevice<VulkanDevice>()->Get(), mFence, gVkAllocCallback);
}

UInt64 cross::VulkanFence::GetCompletedValue()
{
    auto ret = vkGetFenceStatus(GetDevice<VulkanDevice>()->Get(), mFence);
    switch (ret)
    {
    case VK_SUCCESS:
        mCurrentValue = mCompleteValue;
        break;
    case VK_NOT_READY:
        break;
    default:
        VK_CHECK(ret);
        break;
    }

    return mCurrentValue;
}

bool cross::VulkanFence::Wait(UInt64 value, UInt64 timeout)
{
    if (mCurrentValue >= value)
        return true;

    mCompleteValue = value;

    while (true)
    {
        auto ret = vkWaitForFences(GetDevice<VulkanDevice>()->Get(), 1, &mFence, true, timeout);
        switch (ret)
        {
        case VK_SUCCESS:
            VK_CHECK(vkResetFences(GetDevice<VulkanDevice>()->Get(), 1, &mFence));
            mCurrentValue = mCompleteValue;
            return true;
        case VK_TIMEOUT:
            return false;
        default:
            VK_CHECK(ret);
            break;
        }
    }

    LOG_ERROR("Should not run to this");
    return true;
}

void cross::VulkanFence::Signal(VkQueue queue, UInt64 value, UInt64 waitValue)
{
    VK_CHECK(vkResetFences(GetDevice<VulkanDevice>()->Get(), 1, &mFence));
    VK_CHECK(vkQueueSubmit(queue, 0, nullptr, mFence));
}

cross::VulkanTimelineSemaphore::VulkanTimelineSemaphore(UInt64 initialValue, VulkanDevice* pDevice, bool external)
    : VulkanFenceBase(pDevice)
    , mSignalValue(0)
{
    VkSemaphoreTypeCreateInfo timelineCreateInfo;
    timelineCreateInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_TYPE_CREATE_INFO;
    timelineCreateInfo.pNext = NULL;
    timelineCreateInfo.semaphoreType = VK_SEMAPHORE_TYPE_TIMELINE;
    timelineCreateInfo.initialValue = initialValue;

    VkSemaphoreCreateInfo createInfo;
    createInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;
    createInfo.flags = 0;

    VkExportSemaphoreCreateInfoKHR exportSemaInfo = {};
    mExternal = external;
#if !defined(CROSSENGINE_WIN) && !defined(CROSSENGINE_LINUX)
    mExternal = false;
#endif

    // external semaphore, used for cloud game/cross application sync
    if (mExternal)
    {
        exportSemaInfo.sType = VK_STRUCTURE_TYPE_EXPORT_SEMAPHORE_CREATE_INFO_KHR;
        exportSemaInfo.pNext = &timelineCreateInfo;
#ifdef CROSSENGINE_WIN
        exportSemaInfo.handleTypes = VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_OPAQUE_WIN32_BIT_KHR;
#else
        exportSemaInfo.handleTypes = VK_EXTERNAL_SEMAPHORE_HANDLE_TYPE_OPAQUE_FD_BIT;
#endif

        createInfo.pNext = &exportSemaInfo;
    }
    else
    {
        createInfo.pNext = &timelineCreateInfo;
    }


    VK_CHECK(vkCreateSemaphore(pDevice->Get(), &createInfo, NULL, &mTimelineSemaphore));
}
cross::VulkanTimelineSemaphore::~VulkanTimelineSemaphore()
{
    vkDestroySemaphore(GetDevice<VulkanDevice>()->Get(), mTimelineSemaphore, nullptr);
}
UInt64 cross::VulkanTimelineSemaphore::GetCompletedValue()
{
    UInt64 value;
    vkGetSemaphoreCounterValue(GetDevice<VulkanDevice>()->Get(), mTimelineSemaphore, &value);
    return value;
}
bool cross::VulkanTimelineSemaphore::Wait(UInt64 value, UInt64 timeout)
{
    VkSemaphoreWaitInfo waitInfo;
    waitInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_WAIT_INFO;
    waitInfo.pNext = NULL;
    waitInfo.flags = 0;
    waitInfo.semaphoreCount = 1;
    waitInfo.pSemaphores = &mTimelineSemaphore;
    waitInfo.pValues = &value;

    while (true)
    {
        SCOPED_CPU_TIMING(VulkanTimelineSemaphore, "Timeline Semaphore Synchronization");
        auto ret = vkWaitSemaphores(GetDevice<VulkanDevice>()->Get(), &waitInfo, timeout);

        switch (ret)
        {
        case VK_SUCCESS:
            return true;
        case VK_TIMEOUT:
            return false;
        default:
            VK_CHECK(ret);
            break;
        }
    }

    LOG_ERROR("Should not run to this");

    return true;
}

void cross::VulkanTimelineSemaphore::Signal(UInt64 value) {
    DEBUG_LOCK_GUARD(mMutex);
    mSignalValue = value;

    VkSemaphoreSignalInfo signalInfo;
    signalInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_SIGNAL_INFO;
    signalInfo.pNext = NULL;
    signalInfo.semaphore = mTimelineSemaphore;
    signalInfo.value = value;

    vkSignalSemaphore(GetDevice<VulkanDevice>()->Get(), &signalInfo);
}

void cross::VulkanTimelineSemaphore::Signal(VkQueue queue, UInt64 value, UInt64 waitValue)
{
    mSignalValue = value;
    VkTimelineSemaphoreSubmitInfo timelineInfo{};
    timelineInfo.sType = VK_STRUCTURE_TYPE_TIMELINE_SEMAPHORE_SUBMIT_INFO;
    timelineInfo.pNext = NULL;
    timelineInfo.waitSemaphoreValueCount = 1;
    timelineInfo.pWaitSemaphoreValues = &waitValue;
    timelineInfo.signalSemaphoreValueCount = 1;
    timelineInfo.pSignalSemaphoreValues = &value;

    VkPipelineStageFlags waitStage = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;

    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.pNext = &timelineInfo;
    submitInfo.waitSemaphoreCount = 1;
    submitInfo.pWaitSemaphores = &mTimelineSemaphore;
    submitInfo.pWaitDstStageMask = &waitStage;
    submitInfo.signalSemaphoreCount = 1;
    submitInfo.pSignalSemaphores = &mTimelineSemaphore;
    submitInfo.commandBufferCount = 0;
    submitInfo.pCommandBuffers = 0;
    VulkanCommandQueue::QueueSubmit(queue, 1, submitInfo);
    //DEBUG_LOCK_GUARD(mMutex);
    //VK_CHECK(vkQueueSubmit(queue, 1, &submitInfo, VK_NULL_HANDLE));
}

void cross::VulkanTimelineSemaphore::Wait(VkQueue queue, UInt64 value)
{
    VkTimelineSemaphoreSubmitInfo timelineInfo{};
    timelineInfo.sType = VK_STRUCTURE_TYPE_TIMELINE_SEMAPHORE_SUBMIT_INFO;
    timelineInfo.pNext = NULL;
    timelineInfo.waitSemaphoreValueCount = 1;
    timelineInfo.pWaitSemaphoreValues = &value;

    VkPipelineStageFlags waitStage = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;

    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.pNext = &timelineInfo;
    submitInfo.waitSemaphoreCount = 1;
    submitInfo.pWaitSemaphores = &mTimelineSemaphore;
    submitInfo.pWaitDstStageMask = &waitStage;
    VulkanCommandQueue::QueueSubmit(queue, 1, submitInfo);
}

cross::VulkanCommandQueue::VulkanCommandQueue(const NGICommandQueueDesc& desc, VulkanDevice* pDevice)
    : NGICommandQueue{pDevice}
    , mType{desc.type}
{
    // TODO(peterwjma), use queue pool
    NGI_LOG_DEBUG("Vulkan create command queue");

    auto GetDeviceQueue = [this](cross::PhysicalDeviceInfo::QueueInfo& queInfo)-> bool {
        if (!queInfo.freeQueues.empty())
        {
            mFamilyIndex = queInfo.familyIndex;
            mIndex = *queInfo.freeQueues.begin();
            queInfo.freeQueues.erase(mIndex);
            vkGetDeviceQueue(GetDevice<VulkanDevice>()->Get(), queInfo.familyIndex, mIndex, &mCommandQueue);
            return true;
        }
        return false;
    };

    switch (desc.type)
    {
    case NGICommandQueueType::Direct:
        if (!GetDeviceQueue(pDevice->mPhysicalDeviceInfo->mGraphicsQueueInfo))
        {
            LOG_FATAL("Vulkan no more graphics queue");
        }
        break;
    case NGICommandQueueType::Compute:
        if (!GetDeviceQueue(pDevice->mPhysicalDeviceInfo->mComputeQueueInfo))
        {
            LOG_FATAL("Vulkan no more compute queue");
        }
        break;
    case NGICommandQueueType::Copy:
        if (!GetDeviceQueue(pDevice->mPhysicalDeviceInfo->mTransferQueueInfo))
        {
            LOG_FATAL("Vulkan no more transfer queue");
        }
        break;
    default:
        LOG_FATAL("Vulkan get command queue of type other than direct not implemented yet");
        break;
    }
}

cross::VulkanCommandQueue::~VulkanCommandQueue()
{
    NGI_LOG_DEBUG("Vulkan destroy command queue");

    switch (mType)
    {
    case NGICommandQueueType::Direct:
        GetDevice<VulkanDevice>()->mPhysicalDeviceInfo->mGraphicsQueueInfo.freeQueues.emplace(mIndex);
        break;
    default:
        break;
    }
}

void cross::VulkanCommandQueue::AllocateCommandLists(UInt32 num, NGICommandList** ppCommandLists)
{
    mCmdPool.Allocate(num, reinterpret_cast<void**>(ppCommandLists));
}

void cross::VulkanCommandQueue::AllocateBundleCommandLists(UInt32 num, NGIBundleCommandList** ppCommandLists)
{
    mBundleCmdPool.Allocate(num, reinterpret_cast<void**>(ppCommandLists));
}

void cross::VulkanCommandQueue::ExecuteCommandLists(UInt32 num, NGICommandList** ppLists)
{
    SCOPED_CPU_TIMING(NGI, "ExecuteCommandLists");

    std::vector<VkCommandBuffer> mVkCommandBuffers;
    mVkCommandBuffers.reserve(num + 2); // +2 to set begin end commandBuffer to get queue gpu execute time
    std::transform(ppLists, ppLists + num, std::back_inserter(mVkCommandBuffers), [](auto cmdList) { return rhi_cast<VulkanCommandList*>(cmdList)->mCommandList; });

    GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->SetQueueExecuteRegion(this, mVkCommandBuffers);

    VkSubmitInfo submitInfo{
        VK_STRUCTURE_TYPE_SUBMIT_INFO,
        nullptr,
        0,
        nullptr,
        nullptr,
        static_cast<uint32_t>(mVkCommandBuffers.size()),
        mVkCommandBuffers.data(),
        0,
        nullptr,
    };


    VulkanCommandQueue::QueueSubmit(mCommandQueue, 1, submitInfo);
    GetDevice<VulkanDevice>()->GetVulkanTimeStamp()->OnExecuteCommandLists(this, mVkCommandBuffers);
    //DEBUG_LOCK_GUARD(mMutex);
    //VK_CHECK(vkQueueSubmit(mCommandQueue, 1, &submitInfo, VK_NULL_HANDLE));
}

void cross::VulkanCommandQueue::QueueSubmit(VkQueue& queue, UInt32 num, VkSubmitInfo& info)
{
    DEBUG_LOCK_GUARD(VulkanCommandQueue::mSubmitMutex);
   VK_CHECK(vkQueueSubmit(queue, num, &info, VK_NULL_HANDLE));
}

void cross::VulkanCommandQueue::Signal(NGIFence* pFence, UInt64 value)
{
    DEBUG_LOCK_GUARD(mMutex);
    rhi_cast<VulkanFenceBase*>(pFence)->Signal(mCommandQueue, value);
}

void cross::VulkanCommandQueue::Wait(NGIFence* pFence, UInt64 value)
{
    DEBUG_LOCK_GUARD(mMutex);
    rhi_cast<VulkanFenceBase*>(pFence)->Wait(mCommandQueue, value);
}


cross::VulkanFramebuffer::VulkanFramebuffer(const NGIFramebufferDesc& desc, VulkanDevice* pDevice)
    : NGIFramebuffer{desc, pDevice}
{
    auto* vkRenderPass = rhi_cast<VulkanRenderPass*>(desc.RenderPass);
    auto& renderPassDesc = vkRenderPass->GetDesc();

    std::array<VkImageView, (MaxSupportedRenderTargets + 1) * 2> attaches;
    auto attachItr = attaches.begin();
    auto resolveAttachItr = attaches.begin() + renderPassDesc.ColorTargetCount + (renderPassDesc.HasDepthStencil ? 1 : 0);
    for (auto i = 0u; i < renderPassDesc.ColorTargetCount; i++)
    {
        auto& colorTargetDesc = renderPassDesc.ColorTargets[i];
        auto& colorTarget = desc.ColorTargets[i];
        Assert(colorTarget.Target);
        *attachItr++ = rhi_cast<VulkanTextureView*>(colorTarget.Target)->mView;
        if (EnumHasAnyFlags(colorTargetDesc.StoreOp, NGIStoreOp::Resolve))
        {
            Assert(desc.ColorTargets[i].ResolveTarget);
            *resolveAttachItr++ = rhi_cast<VulkanTextureView*>(colorTarget.ResolveTarget)->mView;
        }
    }
    if (renderPassDesc.HasDepthStencil)
    {
        Assert(desc.DepthStencilTarget.Target);
        *attachItr++ = rhi_cast<VulkanTextureView*>(desc.DepthStencilTarget.Target)->mView;
        if (EnumHasAnyFlags(renderPassDesc.DepthStoreOp, NGIStoreOp::Resolve) || EnumHasAnyFlags(renderPassDesc.StencilStoreOp, NGIStoreOp::Resolve))
        {
            Assert(desc.DepthStencilTarget.ResolveTarget);
            *resolveAttachItr++ = rhi_cast<VulkanTextureView*>(desc.DepthStencilTarget.ResolveTarget)->mView;
        }
    }

    VkFramebufferCreateInfo createInfo{
        VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO,
        nullptr,
        0,
        vkRenderPass->mRenderPass,
        static_cast<uint32_t>(std::distance(attaches.begin(), resolveAttachItr)),
        attaches.data(),
        desc.Width,
        desc.Height,
        desc.LayerCount,
    };
    VK_CHECK(vkCreateFramebuffer(GetDevice<VulkanDevice>()->Get(), &createInfo, gVkAllocCallback, &mFramebuffer));

    NGI_LOG_DEBUG("Create framebuffer: {}", VkFormatHandle(mFramebuffer));
}

cross::VulkanFramebuffer::~VulkanFramebuffer()
{
    NGI_LOG_DEBUG("Destroy framebuffer: {}", VkFormatHandle(mFramebuffer));
    QUICK_SCOPED_CPU_TIMING("~VulkanFramebuffer");

    vkDestroyFramebuffer(GetDevice<VulkanDevice>()->Get(), mFramebuffer, gVkAllocCallback);
}

void cross::VulkanFramebuffer::SetDebugName(const char* pDebugName)
{
    NGIObject::SetDebugName(pDebugName);

    GetDevice<VulkanDevice>()->SetDebugName(VK_OBJECT_TYPE_FRAMEBUFFER, mFramebuffer, pDebugName);
}

cross::VulkanQueryHeap::VulkanQueryHeap(const NGIQueryHeapDesc& desc, VulkanDevice* device)
    : NGIQueryHeap{desc, device}
{
    VkQueryPoolCreateInfo info{
        VK_STRUCTURE_TYPE_QUERY_POOL_CREATE_INFO,
        nullptr,
        0,
        MapQueryHeapType(desc.Type),
        desc.Count,
    };
    VK_CHECK(vkCreateQueryPool(GetDevice<VulkanDevice>()->Get(), &info, gVkAllocCallback, &mQueryPool));
}

cross::VulkanQueryHeap::~VulkanQueryHeap()
{
    vkDestroyQueryPool(GetDevice<VulkanDevice>()->Get(), mQueryPool, gVkAllocCallback);
}
