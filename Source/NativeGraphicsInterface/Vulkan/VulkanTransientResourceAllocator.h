#pragma once
#include "NativeGraphicsInterface/NGITransientResourceAllocator.h"

namespace cross 
{
class VulkanTransientResourceAllocator : public NGITransientResourceAllocator
{
public:
    VulkanTransientResourceAllocator(NGIDevice* device)
        : NGITransientResourceAllocator(device)
    {
    }
    NGI_API virtual NGITransientTexture* CreateTexture(const NGITextureDesc& desc, std::string_view name) override;
    NGI_API virtual NGITransientBuffer* CreateBuffer(const NGIBufferDesc& desc, std::string_view name) override;
};
}