#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "NativeGraphicsInterface/NGI.h"

namespace cross::anim {

/** Max number of bone influences that a single skinned vert can have per vertex stream. */
#define MAX_INFLUENCES_PER_STREAM 4
#define INFLUENCE_0               0
#define INFLUENCE_1               1
#define INFLUENCE_2               2
#define INFLUENCE_3               3

class SkeltMeshCPUSkinUtil
{
public:
    /** data for a single skinned skeletal mesh vertex */
    struct FinalSkinVertices
    {
        std::vector<Float3A> Positions;
        std::vector<Float3A> Normals;
        std::vector<Float3A> Tangents;

        void Resize(SInt32 vertexCount)
        {
            Positions.resize(vertexCount);
            Normals.resize(vertexCount);
            Tangents.resize(vertexCount);
        }
    };

    /** Struct for storing skin weight info in vertex buffer */
    struct SkinWeightInfo
    {
        /** Bone Ids Array those subscript mean vertex id, exceed InfulenceBoneCount part should be negative **/
        Short4 const* InfluenceBones{nullptr};

        /** Bone Weights Array those subscript mean vertex id, exceed InfulenceBoneCount part should be negative **/
        Float4 const* InfluenceWeights{nullptr};

        int InfulenceBoneCount = MAX_INFLUENCES_PER_STREAM;
        int InfulenceVertCount{0};
    };

    struct InputVertexSkinningInfo
    {
        // In vertex array
        const Float3* InPositions{nullptr};
        const Float3* InNormals{nullptr};
        const Float4* InTangents{nullptr};
        // cpu skin: upload pre position buffer
        // gpu skin: zero
        Float3* InOutPrePositions{nullptr};

        //  Bytes offsets relative to each vertex's start address in Dest array for 3 components.
        //  Usage:
        //      UInt8* PosAddress = Dest + (VertexIndex + StartIndex) * PerVertexStride + PosOffsets
        //      UInt8* NormalAddress = Dest + (VertexIndex + StartIndex) * PerVertexStride + NormalOffsets
        //      UInt8* TangentAddress = Dest + (VertexIndex + StartIndex) * PerVertexStride + TangentOffsets
        UInt32 PosOffsets{0};
        UInt32 NormalOffsets{0};
        UInt32 TangentOffsets{0};
        UInt32 PrePosOffsets{0};
        UInt32 StartIndex{0};

        // Total bytes count for each vertex
        UInt32 PerVertexStride{0};
    };

    void SkinVertices(StagingBufferWrap bufferWrap, const InputVertexSkinningInfo& inVertexInfo, const SkinWeightInfo& inSkinInfo,
                      FrameVector<SIMDMatrix>* __restrict skinningMatrices,   // BindPoseInv * LocalToRootMat
                      const UInt32 localToRootMatrixCount, bool needProcessNormal = true, bool needProcessTangent = true);

    void SkinVertices(FinalSkinVertices& dest, const InputVertexSkinningInfo& inVertexInfo, const SkinWeightInfo& inSkinInfo, 
                      const Float4x4A* __restrict skinningMatrices, 
                      const UInt32 localToRootMatrixCount, bool needProcessNormal = true, bool needProcessTangent = true);

private:
    /** Index of LOD level's vertices that are currently stored in CachedFinalVertices */
    mutable SInt32 mCachedVertexLOD;

    /** Cached skinned vertices. Only updated/accessed by the rendering thread and exporters */
    mutable FinalSkinVertices mCachedFinalVertices;
};
}   // namespace cross::anim