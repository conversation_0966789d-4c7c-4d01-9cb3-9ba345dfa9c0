#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDRenderRawDrawUnitsPayload.h"
#include "Runtime/UI/Px/Canvas.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderNode/CanvasRenderNode.h"
#include "CrossUI/core/oui.h"
#include "CrossUI/Font.h"

namespace cross {
struct VertexStreamLayout;

struct CanvasComponentR : public ecs::IComponent
{
    std::shared_ptr<CanvasRenderNode> mRenderNode = std::make_shared<CanvasRenderNode>();

    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();
};

struct RenderMesh;
struct Viewport;
struct Scissor;

enum class UIType
{
    UI,
    NanoVG
};

struct UIResidentTexResourses
{
    bool mIsVisible{true};

    bool mShouldUpdate{true};
};

class CanvasSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    CanvasSystemR();

    RENDER_ENGINE_API static CanvasSystemR* CreateInstance();

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    void OnBeginFrame(FrameParam* frameParam) override;

    void OnEndFrame(FrameParam* frameParam) override;

    void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    virtual void Release() override{};

public:
    RENDER_ENGINE_API void SetCanvasFlags(ecs::EntityID entity, bool flag);

    RENDER_ENGINE_API void SetNanoVGRenderData(ecs::EntityID entity, FrameVector<std::vector<NanoVGItem>>* items, std::map<int, NanoVGTexItem> textures, FrameVector<NanoVGVertex>* vertexData, Scissor scissor);

    RENDER_ENGINE_API void SetCanvasRenderData(ecs::EntityID entity, FrameVector<CanvasItemBatch>* itemBatches, FrameArray<CanvasGeometryBatch>* geometryBatches);

    RENDER_ENGINE_API void SetLayer(ecs::EntityID entity, int layer);

    RENDER_ENGINE_API int GetLayer(ecs::EntityID entity);

    RENDER_ENGINE_API void SetVisible(ecs::EntityID entity, bool visible);

    RENDER_ENGINE_API void Render(oui::primitive_buffer* buffer);

    friend class CanvasSystem;

    RenderWorld* GetRenderWorld()
    {
        return mRenderWorld;
    }

    // nanovg rendering
    auto& GetNVGDrawUnits()
    {
        return mNVGDrawUnits;
    }

    std::tuple<UInt64, REDTextureView*, bool> GetValidUITexView(RenderingExecutionDescriptor* red, NGITextureDesc texDesc, NGITextureViewDesc texViewDesc);

    RENDER_ENGINE_API void SetUIMaterial(MaterialR* material, GPUTexture* texture, ui::Font* font, Float2 texture_size);

    void SetCanvasVisibleState(UInt64 key, bool state = true);

    UInt64 mCurrentValidCanvas{0};

private:
    // experimental one drawcall ui
    NGIBuffer* mIndexBuffer;
    Float2 mTextureSize;
    std::vector<float> mTextureData;
    std::size_t mIndexBufferSize;
    std::size_t mTextureBufferSize;

    MaterialR* mUIMaterial{nullptr};
    GPUTexture* mTexture;

    ecs::EntityID mUIEntity;
    VertexStreamLayout mLayout;
    std::unique_ptr<MeshR> mRenderMesh;
    GeometryPacketPtr mGeoPack;
    //std::vector<RenderMesh> mRenderMeshes;
    std::vector<std::pair<Viewport*, Scissor*>> mViewports;

    // nanovg related
    std::map<UInt64, UIResidentTexResourses> mUITexResourcesMap;
    
    // for no entity ui info display

    std::map<int, std::tuple<NGITexture*, NGITextureView*>> mNVGTexViewMap;
    GeometryPacketPtr mNVGGeoPack{nullptr};
    FrameStdVector<REDRawDrawUnit> mNVGDrawUnits;

    Scissor mScissor;
};

}   // namespace cross
