#include "EnginePrefix.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameCounter.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/DecalSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/PrimitiveRenderSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderNode/DecalRenderNode.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"
#include "RenderPipelineSystemR.h"

namespace cross {

ecs::ComponentDesc* cross::DecalComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::DecalComponentR>(false);
}

DecalSystemR* DecalSystemR::CreateInstance()
{
    DecalSystemR* system = new DecalSystemR();
    return system;
}

DecalSystemR::DecalSystemR() {}

DecalSystemR::~DecalSystemR() {}


void DecalSystemR::OnFirstUpdate(FrameParam* frameParam) 
{
    PrepareUnitCubeGeometry();
}

void DecalSystemR::Release()
{
    delete this;
}

static DecalList* SetupDecalList(FrameParam* frameParam, UInt32 ReservedDecalNum) 
{
    if (ReservedDecalNum <= 0)
    {
        // Reset to nullptr if no decals available
        return nullptr;
    }
    return frameParam->GetFrameAllocator()->CreateFrameContainer<FrameVector<RenderDecalComponentHandle>>(FrameStage::FRAME_STAGE_RENDER, ReservedDecalNum);
}

static bool IsValidDecalList(DecalList* inDecalList)
{
    return inDecalList != nullptr && !inDecalList->IsEmpty();
}

void DecalSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
    {
        mRenderWorld->SubscribeRemainedEvent<EntityCreateEvent>(this, true);
        mRenderWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }

    if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const auto& e = TYPE_CAST(const RemainedEventUpdatedEvent&, event);
        const auto& eventData = e.mData;
        if (eventData.mRemainedEventType == EntityCreateEvent::sEventType)
        {
            for (UInt32 eventIndex = eventData.mFirstIndex; eventIndex != eventData.mLastIndex + 1U; eventIndex++)
            {
                const auto& entityLifeCycleEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(eventIndex);
                if (ecs::HasComponentMask<DecalComponentR>(entityLifeCycleEvent.mData.mChangedComponentMask))
                {
                    auto [decalComp, renderNodeComp] = mRenderWorld->GetComponent<DecalComponentR, RenderNodeComponentR>(entityLifeCycleEvent.mData.mEntityID);
                    decalComp.mComponent->mRenderNode = std::make_shared<DecalRenderNode>();
                    renderNodeComp.mComponent->SetRenderNode(decalComp.mComponent->mRenderNode);
                }
            }
        }

        //if (eventData.mRemainedEventType == EntityDestroyEvent::sEventType)
        //{
        //    for (UInt32 eventIndex = eventData.mFirstIndex; eventIndex != eventData.mLastIndex + 1U; eventIndex++)
        //    {
        //        const auto& entityLifeCycleEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
        //        if (ecs::HasComponentMask<DecalComponentR>(entityLifeCycleEvent.mData.mChangedComponentMask))
        //        {
        //            auto gpuScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();
        //            auto decalComp = mRenderWorld->GetComponent<DecalComponentR>(entityLifeCycleEvent.mData.mEntityID);
        //            decalComp.mComponent->mRenderNode->FreeGPUScene(*gpuScene);
        //        }
        //    }
        //}
    }

    RenderSystemBase::NotifyEvent(event, flag);
}
void DecalSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this, frameParam] {
        SCOPED_CPU_TIMING(GroupRendering, "DecalSystemRUpdate");

        //bShouldRenderDecal = false;

        if (!EngineGlobal::GetSettingMgr()->GetDecalSetting().Enable)
        {
            return;
        }

        auto queryResult = mRenderWorld->Query<DecalComponentR>();
        UInt32 EntityNum = queryResult.GetEntityNum();
        
        for (const auto& decalComp : queryResult) {
            if (decalComp.Read()->enable)
            {
                bShouldRenderDecal = true;
            }

            SetupRenderNode(decalComp.Read());
        }

        DrawDecalMeshWireFrame(frameParam->GetFrameAllocator());
    });
}

void DecalSystemR::OnEndFrame(FrameParam* frameParam) {}

void DecalSystemR::SetDecalEnable(ecs::EntityID entity, bool enable) {
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->enable = enable;
    }
}


void DecalSystemR::SetDecalTransform(ecs::EntityID entity, const Float4x4& ModelToWorld, const Float3& TilePosition)
{
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->DecalToWorldMatrix = ModelToWorld;
        writer->TilePosition = TilePosition;

        Float3 scale, translation;
        Quaternion rotate;
        ModelToWorld.Decompose(scale, rotate, translation);
        writer->DecalToWorldMatrixRotationOnly = Float4x4::CreateFromQuaternion(rotate);
        writer->DecalOBB = BoundingOrientedBox{translation, scale, rotate};
    }
}

void DecalSystemR::SetDecalFadingParameters(ecs::EntityID entity, float FadeScreenSize, float FadeStartDelay, float FadeDuration, float FadeInDuration, float FadeInStartDelay) {
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->FadeScreenSize = FadeScreenSize;
        writer->FadeStartDelay = FadeStartDelay;
        writer->FadeDuration = FadeDuration;
        writer->FadeInDuration = FadeInDuration;
        writer->FadeInStartDelay = FadeInStartDelay;
    }
}

void DecalSystemR::SetDecalSortOrder(ecs::EntityID entity, UInt32 order)
{
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->SortOrder = order;
    }
}

void DecalSystemR::DrawDecalMeshWireFrame(FrameAllocator* Allocator)
{
    auto primitiveSystem = mRenderWorld->GetRenderSystem<PrimitiveRenderSystemR>();
    auto* InvalidEntities = Allocator->CreateFrameContainer<FrameVector<ecs::EntityID>>(FRAME_STAGE_RENDER, 2);

    BoundingOrientedBox obb = {};
    for (auto& [decalEntity, primitive] : mShowingDecalMeshWireFramePrimitives)
    {
        if (!mRenderWorld->IsEntityAlive(decalEntity) || !mRenderWorld->HasComponent<DecalComponentR>(decalEntity))
        {
            // Invalid
            InvalidEntities->EmplaceBack(decalEntity);
            continue;
        }
        
        auto decalCompH = mRenderWorld->GetComponent<DecalComponentR>(decalEntity).Read();
        Float4x4 RelativeMatrix = decalCompH->DecalToWorldMatrix;

        // Generate BBX primitive in tile space
        PrimitiveGenerator::GenerateOrientedBoxFrame(&primitive, obb, &RelativeMatrix, 0xFF00FF00);

        // Set tile position
        primitive.SetTilePosition(decalCompH->TilePosition);

        // Batch primitive
        primitiveSystem->BatchPrimitive(&primitive, primitiveSystem->GetPresetMaterial(true, false, false), false);
    }

    for (UInt32 i = 0; i < InvalidEntities->GetSize(); ++i)
    {
        mShowingDecalMeshWireFramePrimitives.erase(InvalidEntities->At(i));
    }
}

void DecalSystemR::SetDecalParentID(ecs::EntityID entity, ecs::EntityID ParentEntity)
{
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->ParentEntityID = ParentEntity;
    }
}

void DecalSystemR::SetDecalWireFrameShow(ecs::EntityID entity, bool isShow)
{
    if (isShow)
    {
        if (mShowingDecalMeshWireFramePrimitives.find(entity) == mShowingDecalMeshWireFramePrimitives.end())
        {
            mShowingDecalMeshWireFramePrimitives.emplace(std::pair<ecs::EntityID, PrimitiveData>(entity, {}));
        }
    }
    else
    {
        mShowingDecalMeshWireFramePrimitives.erase(entity);
    }
}

static NGIRasterizationStateDesc GetRasterizationState()
{
    NGIRasterizationStateDesc result;
    result.FillMode = FillMode::Solid;
    result.CullMode = CullMode::Front;
    result.FaceOrder = FaceOrder::CW;

    // DepthClamp was always enabled, DepthClip was ignored by now
    result.EnableDepthClip = true;
    result.EnableAntialiasedLine = true;
    result.EnableDepthBias = false;

    result.RasterMode = RasterizationMode::DefaultRaster;
    return result;
}

static NGIBlendStateDesc GetBlendState() {
    NGIBlendStateDesc result;

    result.EnableAlphaToCoverage = false;
    result.EnableIndependentBlend = false;
    result.TargetCount = 4;

    NGITargetBlendStateDesc item;
    item.EnableBlend = true;
    item.EnableLogicOp = false;
    item.SrcBlend = BlendFactor::SrcAlpha;
    item.DestBlend = BlendFactor::InvSrcAlpha; // 1 - SrcAlpha
    item.BlendOp = NGIBlendOp::Add;

    item.SrcBlendAlpha = BlendFactor::Zero;
    item.DestBlendAlpha = BlendFactor::InvSrcAlpha;   // 1 - SrcAlpha;
    item.BlendOpAlpha = NGIBlendOp::Add;
    item.LogicOp = LogicOp::Clear;
    item.WriteMask = ColorMask::All;

    for (auto& state : result.TargetBlendState)
    {
        state = item;
    }

    NGITargetBlendStateDesc itemEmissive = item;
    itemEmissive.SrcBlend = BlendFactor::One;
    itemEmissive.DestBlend = BlendFactor::One;
    result.TargetBlendState[3] = itemEmissive;
    return result;
}

static REDPassFlagBit GetSubPassFlagBit() {
    return REDPassFlagBit::NeedDepth | REDPassFlagBit::DepthReadOnly | REDPassFlagBit::NeedStencil;
}

// Should be the same as GetSubPassFlagBit()
static NGIDepthStencilStateDesc GetDepthStencilState(bool ReverseZ) {
    NGIDepthStencilStateDesc result;
    result.EnableDepth = true; // NeedDepth
    result.EnableDepthWrite = false; // DepthReadOnly
    result.DepthCompareOp = !ReverseZ ? NGIComparisonOp::GreaterEqual : NGIComparisonOp::LessEqual;

    const NGIStencilOperation Stencil = {StencilOp::Keep, StencilOp::Keep, StencilOp::Replace, NGIComparisonOp::Equal};
    result.EnableStencil = true; // NeedStencil
    result.StencilReadMask = STENCIL_RECEIVE_DECAL_BIT_MASK;    // Read stencil bit to render decal
    result.StencilWriteMask = STENCIL_APPLY_DBUFFER_DECAL_BIT_MASK; // mark stencil bit for apply DBuffer data
    result.FrontFace = Stencil;
    result.BackFace = Stencil;
    return result;
}

static NGIDynamicStateDesc GetDynamicState() {
    NGIDynamicStateDesc result;
    result.StencilReference = STENCIL_RECEIVE_DECAL_BIT_MASK | STENCIL_APPLY_DBUFFER_DECAL_BIT_MASK;
    return result;
}

static bool IsValidDecalMaterial(MaterialR* inMaterial) {
    if (inMaterial == nullptr)
    {
        return false;
    }
    auto fx = inMaterial->GetFx();
    return fx->HasPass(DECAL_FX_PASS_NAME) && fx->GetProperty(DECAL_SHADER_CONSTANT_0) != nullptr;
}

bool DecalSystemR::ShouldRenderDecal() const
{
    return bShouldRenderDecal;
}

static REDColorTargetDesc AllocateColorTargetDesc(RenderingExecutionDescriptor* RED, REDTextureView* inGBufferView, bool IsNormal = false, bool IsEmissive = false) {
    constexpr float normal = 128.0f / 255.0f;
    return {RED->AllocateTextureView(inGBufferView->mTexture,
                                     NGITextureViewDesc{NGITextureUsage::RenderTarget,
                                                        inGBufferView->mDesc.Format,
                                                        NGITextureType::Texture2D,
                                                        {
                                                            NGITextureAspect::Color,
                                                            0,
                                                            1,
                                                            0,
                                                            1,
                                                        }}),
            IsEmissive ? NGILoadOp::Load : NGILoadOp::Clear,
            NGIStoreOp::Store,
            IsNormal ? NGIClearValue{{normal, normal, normal, 1}} : NGIClearValue{{0, 0, 0, 1}}};
}

PropertySet DecalSystemR::SetupPropertySet(const RenderDecalComponentReader& InDecalCompH)
{
    auto worldMatrix = InDecalCompH->DecalToWorldMatrix;
    auto worldMatrixRotateOnly = InDecalCompH->DecalToWorldMatrixRotationOnly;
    auto tilePosition = InDecalCompH->TilePosition;
    PropertySet DecalObjectCtx(nullptr);
    DecalObjectCtx.SetProperty(BuiltInProperty::ce_World, worldMatrix);
    DecalObjectCtx.SetProperty(BuiltInProperty::ce_TilePosition, tilePosition);
    auto invWorldMat = worldMatrix.Inverted();
    DecalObjectCtx.SetProperty(BuiltInProperty::ce_InvWorld, invWorldMat);
    DecalObjectCtx.SetProperty(BuiltInProperty::ce_InvTransposeWorld, invWorldMat.Transpose());
    DecalObjectCtx.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, invWorldMat.Transpose().Inverted());
    DecalObjectCtx.SetProperty(NAME_ID("ce_WorldOnlyRotation"), worldMatrixRotateOnly);

    // setup parent tile-position
    Float3 parentTile = Float3::Zero();
    if (mRenderWorld->IsEntityAlive(InDecalCompH->ParentEntityID))
    {
        if (auto tileComp = mRenderWorld->GetComponent<TilePositionComponentR>(InDecalCompH->ParentEntityID); tileComp.IsValid())
        {
            parentTile = mRenderWorld->GetRenderSystem<TransformSystemR>()->GetTilePosition(tileComp.Read());
        }
    }
    DecalObjectCtx.SetProperty(NAME_ID("ce_ParentTilePosition"), parentTile);

    return DecalObjectCtx;
}

void DecalSystemR::SetupRenderNode(const RenderDecalComponentReader& InDecalCompH) {
    auto& renderNode = InDecalCompH->mRenderNode;
    if (!IsValidDecalMaterial(InDecalCompH->DecalMaterial))
    {
        renderNode->SetEnabled(false);
        return;
    }

    renderNode->mPriority = AssemblePriority(InDecalCompH);
    renderNode->mGeometry = &mUnitCubeGeometry;
    renderNode->mMaterial = InDecalCompH->DecalMaterial;
    renderNode->mProperties = SetupPropertySet(InDecalCompH);

    renderNode->mDecalWorldTransform.PreRelativeMatrix = renderNode->mDecalWorldTransform.RelativeMatrix;
    renderNode->mDecalWorldTransform.PreTilePosition = renderNode->mDecalWorldTransform.TilePosition;
    renderNode->mDecalWorldTransform.RelativeMatrix = InDecalCompH->DecalToWorldMatrix;
    renderNode->mDecalWorldTransform.TilePosition = InDecalCompH->TilePosition;

    renderNode->mMaterial->SetDepthStencilState(DECAL_FX_PASS_NAME, GetDepthStencilState(true));
    renderNode->mMaterial->SetRasterizationState(DECAL_FX_PASS_NAME, GetRasterizationState());
    renderNode->mMaterial->SetBlendState(DECAL_FX_PASS_NAME, GetBlendState());
    renderNode->mMaterial->SetDynamicState(DECAL_FX_PASS_NAME, GetDynamicState());

    renderNode->SetEnabled(InDecalCompH->enable);
}

UInt32 DecalSystemR::AssemblePriority(const RenderDecalComponentReader& InDecalCompH)
{
    if (!IsValidDecalMaterial(InDecalCompH->DecalMaterial))
    {
        return 0;
    }

    auto ExtractWriteNormal = [](MaterialR* inMaterial) -> bool {
        auto bWriteNormal = inMaterial->GetBool(DECAL_SHADER_CONSTANT_1);
        if (!bWriteNormal.has_value())
        {
            return false;
        }
        return bWriteNormal.value();
    };

    bool bWriteNormal = ExtractWriteNormal(InDecalCompH->DecalMaterial);
    UInt8 materialAddress = static_cast<UInt8>(reinterpret_cast<UInt64>(InDecalCompH->DecalMaterial));
    UInt8 EntityID = static_cast<UInt8>(InDecalCompH.GetEntityID().GetHandle());

    UInt32 Result = (InDecalCompH->SortOrder << 17) 
        + ((bWriteNormal ? 1 : 0) << 16) + (materialAddress << 8) + EntityID;
    return Result;

}

void DecalSystemR::PrepareUnitCubeGeometry() {
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* renderPrimitives = rendererSystem->GetRenderPrimitives();
    auto vb = renderPrimitives->GetUnitCube();
    auto ib = renderPrimitives->GetUnitCubeIndex();
    auto ibCount = renderPrimitives->GetUnitCubeIndexCount();

    VertexStreamLayout streamLayout{};
    streamLayout.AddVertexChannelLayout({VertexChannel::Position0, VertexFormat::Float4, 0});
    streamLayout.AddVertexChannelLayout({VertexChannel::Normal0, VertexFormat::Float4, sizeof(float) * 4});
    mDecalMeshInputLayoutDesc.AddVertexStreamLayout(streamLayout);

    // delete mUnitCubeGeoPack at ~mUnitCubeGeometry() ... ReleaseGeometryPacket()
    mUnitCubeGeoPack = new GeometryPacket();
    mUnitCubeGeoPack->AddVertexStream(vb, static_cast<UInt32>(vb->GetDesc().Size), 0, mDecalMeshInputLayoutDesc.GetVertexStreamLayout(0));
    mUnitCubeGeoPack->SetIndexStream(ib, static_cast<UInt32>(ib->GetDesc().Size), ibCount);
    mUnitCubeGeometry.SetData(mUnitCubeGeoPack, 0, 0, ibCount, 0, ibCount / 3, PrimitiveTopology::TriangleList);
}

void DecalSystemR::RenderDecalBuffers(REDTextureView* depthStencilView, REDTextureView* depthViewAfterMsaa, const RenderCamera* camera, const std::array<REDTextureView*, 3>& DBufferViews, REDTextureView* sceneColorView)
{
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    RenderingExecutionDescriptor* RED = rendererSystem->GetRenderingExecutionDescriptor();

    REDColorTargetDesc colorTargetDesc[4] = {
        AllocateColorTargetDesc(RED, DBufferViews[0]), 
        AllocateColorTargetDesc(RED, DBufferViews[1], true),
        AllocateColorTargetDesc(RED, DBufferViews[2]), 
        AllocateColorTargetDesc(RED, sceneColorView, false, true)
    };

    NGIRenderPassTargetIndex colorTargetIndex[4] = {NGIRenderPassTargetIndex::Target0, NGIRenderPassTargetIndex::Target1, NGIRenderPassTargetIndex::Target2, NGIRenderPassTargetIndex::Target3};

    REDDepthStencilTargetDesc depthStencilTargetDesc{depthStencilView, NGILoadOp::Load, NGIStoreOp::Store, NGILoadOp::Load, NGIStoreOp::Store};

    auto* renderPrimitives = rendererSystem->GetRenderPrimitives();

    RED->BeginRenderPass(fmt::format("Render Decals to DBuffer"), 4, &colorTargetDesc[0], &depthStencilTargetDesc, true);   // register : space(0) parameters

    auto* subPass = RED->AllocateSubRenderPass("Decal Draw", 0, nullptr, 4, &colorTargetIndex[0], GetSubPassFlagBit());

    subPass->SetProperty(NAME_ID("_SceneDepth"), depthViewAfterMsaa, NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);

    auto* cullingResult = RED->Cull(REDCullingDesc{mRenderWorld, const_cast<RenderCamera*>(camera)});
    auto drawUnitList = cullingResult->GenerateDrawUnitList(REDDrawUnitsDesc{DECAL_FX_PASS_NAME, gRenderGroupOpaque, gRenderGroupUI - 1});

    subPass->OnCulling([=](REDPass* pass) {
        if (drawUnitList->GetDefaultObjectIndexBufferView())
            subPass->SetProperty(NAME_ID("_ObjectIndexBuffer"), drawUnitList->GetDefaultObjectIndexBufferView());
    });

    subPass->RenderDrawUnits({drawUnitList});
    RED->EndRenderPass();
}

void DecalSystemR::SetDecalMaterial(ecs::EntityID entity, MaterialR* material)
{
    auto decalComponent = mRenderWorld->GetComponent<DecalComponentR>(entity);
    if (!decalComponent.IsValid())
        return;
    {
        auto writer = decalComponent.Write();
        writer->dirty = true;
        writer->DecalMaterial = material;
    }
}
}   // namespace cross
