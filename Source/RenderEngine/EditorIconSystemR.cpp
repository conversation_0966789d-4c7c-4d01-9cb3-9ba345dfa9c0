#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "ECS/Develop/Framework.h"

#include "RenderEngine/EditorIconSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderWindowR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderFactory.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/VisibilitySystemR.h"
#include "RenderEngine/RenderPropertySystemR.h"

namespace cross
{
    void IconPrimitiveBatch::InitializeData(FrameAllocator* curFrameAllocator, UInt32 capacity)
    {
        mBatchDatas = curFrameAllocator->CreateFrameContainer<FrameVector<BatchData>>(FRAME_STAGE_RENDER, capacity);
    }

    EditorIconSystemR* EditorIconSystemR::CreateInstance()
    {
        return new EditorIconSystemR();
    }

    void EditorIconSystemR::Release()
    {
        delete this;
    }

    void EditorIconSystemR::OnBeginFrame(FrameParam* frameParam)
    {
        mCurFrameAllocator = frameParam->GetFrameAllocator();
        mIconPrimitiveBatches = mCurFrameAllocator->CreateFrameContainer<FrameVector<IconPrimitiveBatch>>(FRAME_STAGE_RENDER, 4);
    }

    void EditorIconSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
    {
        CreateTaskFunction(FrameTickStage::Update, {}, [this]
            {
                SCOPED_CPU_TIMING(GroupRendering, "EditorIconSystemRUpdate");
                RenderBatches(mIconPrimitiveBatches);
            });
    }

    void EditorIconSystemR::OnEndFrame(FrameParam* frameParam)
    {
        mCurFrameAllocator = nullptr;
        mIconPrimitiveBatches = nullptr;
    }

    void EditorIconSystemR::BatchPrimitive(ecs::EntityID entity, FixedSizeFrameDataLot* vertexData, FixedSizeFrameDataLot* indexData, 
        VertexStreamLayout layout, PrimitiveTopology topology, UInt32 primitiveCount, bool isIndex32, MaterialR* renderMaterial)
    {
        // move primitive data into current frame
        PrimitiveData* tPrimitive = reinterpret_cast<PrimitiveData*>(mCurFrameAllocator->Allocate(sizeof(PrimitiveData), FRAME_STAGE_RENDER));
        if (isIndex32)
        {
            new (tPrimitive) PrimitiveData(
                vertexData->GetData(),
                reinterpret_cast<UInt32*>(indexData->GetData()),
                vertexData->GetSize() / layout.GetVertexStride(),
                indexData->GetSize() / sizeof(UInt32),
                primitiveCount,
                layout,
                topology);
        }
        else
        {
            new (tPrimitive) PrimitiveData(
                vertexData->GetData(),
                reinterpret_cast<UInt16*>(indexData->GetData()),
                vertexData->GetSize() / layout.GetVertexStride(),
                indexData->GetSize() / sizeof(UInt16),
                primitiveCount,
                layout,
                topology);
        }

#ifdef CE_USE_DOUBLE_TRANSFORM
        if (mRenderWorld->HasComponent<TilePositionComponentR>(entity))
        {
            auto TileComp = mRenderWorld->GetComponent<TilePositionComponentR>(entity);
            auto TransfromSys = mRenderWorld->GetRenderSystem<TransformSystemR>();
            Float3 Tile = TransfromSys->GetTilePosition(TileComp.Read());
            tPrimitive->SetTilePosition(Tile);
        }
#endif

        auto it = mIconPrimitiveBatches->begin();
        for (; it != mIconPrimitiveBatches->end(); it++)
        {
            if (it->mEntity == entity)
            {
                auto& batchData = it->mBatchDatas->EmplaceBack();
                batchData.mPrimitive = tPrimitive;
                batchData.mRenderMaterial = renderMaterial;
                batchData.mVertexLayout = layout;
                break;
            }
        }
        if (it == mIconPrimitiveBatches->end())
        {
            auto& batch = mIconPrimitiveBatches->EmplaceBack();
            batch.InitializeData(mCurFrameAllocator, 4);
            batch.mEntity = entity;

            // Create render entity for each gameworld entity
            if (mRenderEntityMap.find(entity) == mRenderEntityMap.end())
            {
                ecs::EntityID renderEntity = mRenderWorld->CreateEntityID();
                mRenderWorld->CreateComponents<TransformComponentR, TilePositionComponentR, RenderNodeComponentR, RenderPropertyComponentR>(renderEntity);
                auto renderPropertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
                renderPropertySystem->SetCullingProperty(renderEntity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
                mRenderEntityMap.emplace(entity, renderEntity);
            }
            batch.mRenderEntity = mRenderEntityMap[entity];

            auto& batchData = batch.mBatchDatas->EmplaceBack();
            batchData.mPrimitive = tPrimitive;
            batchData.mRenderMaterial = renderMaterial;
            batchData.mVertexLayout = layout;
        }
    }

    EditorIconSystemR::EditorIconSystemR()
    {
        mRenderMesh = std::make_unique<MeshR>(nullptr);
    }

    void EditorIconSystemR::RenderBatches(FrameVector<IconPrimitiveBatch>* batches)
    {
        auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
        auto renderSystem = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();

        // Flush all render meshes
        tRenderMeshes.clear();
        for (auto it = mRenderEntityMap.begin(); it != mRenderEntityMap.end(); ++it)
        {
            auto renderNodeH = mRenderWorld->GetComponent<RenderNodeComponentR>(it->second);
            renderNodeSystem->SetRenderMeshes(renderNodeH, tRenderMeshes);
        }

        // Pack geometry data
        UInt32 totalBatchCount = 0;
        for (auto it = batches->begin(); it != batches->end(); ++it)
        {
            // Copy entity transform data
            transformSystem->CopyTransformData(it->mEntity, it->mRenderEntity);

            UInt32 batchCount = it->mBatchDatas->GetSize();
            while (mGeoPacks.size() < totalBatchCount + batchCount)
            {
                mGeoPacks.emplace_back(RenderFactory::Instance().CreateGeometryPacket());
            }
            for (UInt32 i = totalBatchCount; i < totalBatchCount + batchCount; ++i)
            {
                auto& batchData = it->mBatchDatas->At(i - totalBatchCount);
                auto& geoPack = mGeoPacks.at(i);
                geoPack->Clear();   

                UInt32 vertexSizeInByte = batchData.mPrimitive->GetVertexCount() * batchData.mPrimitive->GetVertexLayout().GetVertexStride();
                auto vertexBufferNGIWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::VertexBuffer, vertexSizeInByte);
                vertexBufferNGIWrap.MemWrite(0, batchData.mPrimitive->GetVertexData(), vertexSizeInByte);

                UInt32 indexSizeInByte = batchData.mPrimitive->GetIndexCount() * (batchData.mPrimitive->IsIndex32() ? sizeof(UInt32) : sizeof(UInt16));
                auto indexBufferNGIWrap = renderSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::IndexBuffer, indexSizeInByte);
                indexBufferNGIWrap.MemWrite(0, batchData.mPrimitive->GetIndexArray(), indexSizeInByte);

                geoPack->AddVertexStream(vertexBufferNGIWrap.GetNGIBuffer(), vertexSizeInByte, UInt32(vertexBufferNGIWrap.GetNGIOffset()), batchData.mVertexLayout);
                geoPack->SetIndexStream(indexBufferNGIWrap.GetNGIBuffer(), indexSizeInByte, batchData.mPrimitive->GetIndexCount(), UInt32(indexBufferNGIWrap.GetNGIOffset()));
            }
            totalBatchCount += batchCount;
        }

        // Pack render mesh and set to render node
        mRenderMesh->ClearAndResize(totalBatchCount);
        totalBatchCount = 0;
        for (auto it = batches->begin(); it != batches->end(); ++it)
        {
            UInt32 batchCount = it->mBatchDatas->GetSize();
            tRenderMeshes.clear();
            // Use render entity proxy to draw the primitive
            auto renderNodeH = mRenderWorld->GetComponent<RenderNodeComponentR>(it->mRenderEntity);
            for (UInt32 i = 0; i < batchCount; ++i)
            {
                auto& batchData = it->mBatchDatas->At(i);
                mRenderMesh->GetRenderGeometry(totalBatchCount + i).SetData(
                    mGeoPacks.at(totalBatchCount + i).get(),
                    batchData.mPrimitive->GetVertexCount(),
                    0,
                    batchData.mPrimitive->GetIndexCount(),
                    0,
                    batchData.mPrimitive->GetPrimitiveCount(),
                    batchData.mPrimitive->GetPrimitiveTopology());
                RenderMesh renderMesh{};
                renderMesh.mLODCount = 1;
                renderMesh.mLODGeometries[0] = &(mRenderMesh->GetRenderGeometry(totalBatchCount + i));
                renderMesh.mDefaultMaterial = batchData.mRenderMaterial;
                tRenderMeshes.emplace_back(renderMesh);
            }
            renderNodeSystem->SetRenderMeshes(renderNodeH, tRenderMeshes);
            totalBatchCount += batchCount;
        }
    }
}