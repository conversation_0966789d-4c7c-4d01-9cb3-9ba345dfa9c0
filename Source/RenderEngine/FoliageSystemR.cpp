#include "EnginePrefix.h"
#include "FoliageSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/OctahedralCommon.h"
#include "RenderWorld.h"
#include "RenderNodeSystemR.h"
#include "RendererSystemR.h"
#include "TransformSystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine.h"
#include "RenderFactory.h"
#include "RenderPropertySystemR.h"
#include "RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderPipeline/Effects/FoliageGpuDriven.h"
#include "memory/FastMemcpy_Avx.h"
#include "RenderEngine/WorldSystemR.h"
#include "RenderMesh.h"
#include "Resource/MeshAssetDataResource.h"
namespace cross
{
    FoliageSystemR* FoliageSystemR::CreateInstance()
    {
        auto instance = new FoliageSystemR;
        return instance;
    }

    void FoliageSystemR::Release()
    {
        delete this;
    }

    FoliageSystemR::~FoliageSystemR() 
    {
    }

    void FoliageSystemR::ProcessEntityDestroyUseStageBuffer() 
    {      
        // collect destroy foliage entity
        UInt32 oldsize = static_cast<UInt32>(mDestroyedFoliages.size());
        UInt32 DestroyCountThisFrame = 0;
        UInt32 remainedEntityLifeCycleEventCount = mRenderWorld->GetRemainedEventCount<EntityDestroyEvent>(true);
        for (UInt32 i = 0; i < remainedEntityLifeCycleEventCount; ++i)
        {
            const EntityDestroyEvent& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
            if (ecs::HasComponentMask<FoliageComponentR>(lifeEvent.mData.mChangedComponentMask))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                for (auto& _pair : mClusterOffsetMap)
                {
                    auto& info = _pair.second;
                    if (info.entityID == entityID)
                    {
                        mDestroyedFoliages.insert(entityID);
                        DestroyCountThisFrame++;
                        break;
                    }
                }
            }
        }
        
        switch (mDestroyStage) 
        {
        case DestroryStage::Invalid:
            if (mDestroyedFoliages.size() > 0)
            {
                size_t countupload = 0;
                if (countupload == 0)
                {
                    mDestroyStage = DestroryStage::CollectDestroyStart; 

                    LOG_INFO("DestroyState Transition: Start Destrory");
                }
            }
            return;
        case DestroryStage::CollectDestroyStart:
            if (oldsize > 0 && DestroyCountThisFrame == 0)
            {
                mDestroyStage = DestroryStage::CollectDestroyFinish;
                LOG_INFO("DestroyState Transition: Finish Destroy Collection");
            }
            return;
        case DestroryStage::CollectDestroyFinish:
            if (oldsize > 0 && DestroyCountThisFrame > 0)
            {
                mDestroyStage = DestroryStage::CollectDestroyStart;
            }
            else if (oldsize > 0 && DestroyCountThisFrame == 0 && remainedEntityLifeCycleEventCount == 0)
            {

                mDestroyStage = DestroryStage::ReconstructUpload;
                LOG_INFO("DestroyState Transition: Start Reconstruction");
            }
            return;
        case DestroryStage::ReconstructUpload:
            if (DestroyCountThisFrame > 0)
            {
                mDestroyStage = DestroryStage::CollectDestroyStart;
            }
            else
            {
                if (mDestroyedFoliages.size() > 0 && mChangeList.GetCount() == 0 && mInstanceBufferUpdateList.size() == 0 && remainedEntityLifeCycleEventCount == 0)
                {
                    for (auto& id : mDestroyedFoliages)
                    {
                        for (auto it = mClusterOffsetMap.begin(); it != mClusterOffsetMap.end();)
                        {
                            if (it->second.entityID == id)
                                it = mClusterOffsetMap.erase(it);
                            else
                                ++it;
                        }
                    }
                   auto instanceCountremain = CalculateInstancebufferChangeCount(mDestroyedFoliages);
                   float ratio = (float)instanceCountremain / (float)mFoliageStartCluster;
                    constexpr float threshold = 0.3f;
                    bool iseditor = EngineGlobal::Inst().GetSettingMgr()->GetAppStartUpType() == AppStartUpType::AppStartUpTypeCrossEditor;
                    if (ratio < threshold || iseditor)
                    {
                        mFoliageClusterOffset = 0;
                        mFoliageIndirectOffset = 0;
                        mFoliageStartCluster = 0;
                        mFoliageEntityOffset = 0;

                        bool hasFoliage = false;
                        UInt32 foliageremain = 0;
                        size_t instanceCountRemain = 0;
                        for (auto& _pair : mClusterOffsetMap)
                        {
                            auto& info = _pair.second;
                            if (info.instanceDataType == InstanceDataType::Foliage && mDestroyedFoliages.find(info.entityID) == mDestroyedFoliages.end())
                            {
                                info.instanceCount = 0;   // force update
                                auto foliageHandle = mRenderWorld->GetComponent<FoliageComponentR>(info.entityID);
                                instanceCountRemain += foliageHandle.Read()->mInstanceCount;
                                UpdateChangedEntity(info.entityID, InstanceBufferState());
                                foliageremain++;
                                hasFoliage = true;
                            }
                        }

                        if (!hasFoliage)
                        {
                            mMergedGeometryPacket.geoPack.reset();
                        }

                        FoliageGpuDriven::TryShrinkBuffer(
                            FoliageGpuDriven::mFoliageInstanceBuffer, mFoliageClusterOffset, NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                        mDestroyedFoliages.clear();
                        LOG_INFO("DestroyState Transition: Destroy Reconstruction Finished, Reconstructed FoliageSize, {} Foliages with {} Instances", foliageremain, instanceCountRemain);
                        mDestroyStage = DestroryStage::Invalid;
                    }
                }                  
            }
            return;
        }
    }

    void FoliageSystemR::ProcessDestroyEntity()
    {
          // collect destroy foliage entity
          UInt32 remainedEntityLifeCycleEventCount = mRenderWorld->GetRemainedEventCount<EntityDestroyEvent>(true);
          for (UInt32 i = 0; i < remainedEntityLifeCycleEventCount; ++i)
          {
              const EntityDestroyEvent& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
              if (ecs::HasComponentMask<FoliageComponentR>(lifeEvent.mData.mChangedComponentMask))
              {
                  auto& entityID = lifeEvent.mData.mEntityID;
                  for (auto& _pair : mClusterOffsetMap)
                  {
                      auto& info = _pair.second;
                      if (info.entityID == entityID)
                      {
                          mDestroyedFoliages.insert(entityID);
                          break;
                      }
                  }
              }
          }
        if (mDestroyedFoliages.size() > 0 && mInstanceBufferUpdateList.size() == 0)
        {
            // reconstruct foliage instance data
            mFoliageClusterOffset = 0;
            mFoliageIndirectOffset = 0;
            mFoliageStartCluster = 0;
            mFoliageEntityOffset = 0;

            bool hasFoliage = false;

            for (auto& id : mDestroyedFoliages)
            {
                for (auto it = mClusterOffsetMap.begin(); it != mClusterOffsetMap.end();)
                {
                    if (it->second.entityID == id)
                        it = mClusterOffsetMap.erase(it);
                    else
                        ++it;
                }
            }

            for (auto& _pair : mClusterOffsetMap)
            {
                auto& info = _pair.second;
                if (info.instanceDataType == InstanceDataType::Foliage && mDestroyedFoliages.find(info.entityID) == mDestroyedFoliages.end())
                {
                    info.instanceCount = 0;   // force update
                    UpdateChangedEntity(info.entityID, InstanceBufferState());
                    hasFoliage = true;
                }
            }

            if (!hasFoliage)
            {
                mMergedGeometryPacket.geoPack.reset();
            }

            FoliageGpuDriven::TryShrinkBuffer(FoliageGpuDriven::mFoliageInstanceBuffer, mFoliageClusterOffset, NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
            mDestroyedFoliages.clear();
        }
    }

    void FoliageSystemR::OnBeginFrame(FrameParam* frameParam)
    {
        mChangeList.BeginFrame(frameParam, FRAME_STAGE_RENDER);
    }

    void FoliageSystemR::OnEndFrame(FrameParam* frameParam)
    {
    }

    void FoliageSystemR::SetInstanceDataDirty(ecs::EntityID entityID) 
    {
        auto [foliageHandle, renderNodeHandle] = mRenderWorld->GetComponent<FoliageComponentR, RenderNodeComponentR>(entityID);
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        auto* key = mRenderWorld->GetRenderSystem<RenderNodeSystemR>()->GetRenderNode(renderNodeHandle.Write());
    
        if (mRenderWorld->IsEntityAliveInner(entityID))
        {
            auto instanceData = GetInstanceData(foliageHandle.Read());
            auto instanceCount = GetInstanceCount(foliageHandle.Read());
            if (instanceData != nullptr)
            {
                bool instanceCountChange = mClusterOffsetMap.find(key) == mClusterOffsetMap.end() || mClusterOffsetMap[key].instanceCount != (UInt32)instanceCount;
                if (!instanceCountChange)
                {
                    mClusterOffsetMap[key].dataDirty = true;
                    UpdateInstanceBuffer(key, instanceData);
                }
            }
            UpdateEntityBuffer(key);
        }
    }

    void FoliageSystemR::CopyInstanceBufferToStageBuffer(ClusterKey key, const InstanceBufferState& state, std::shared_ptr<DataContainerR> dataContainer, FoliageGenerationType type, UInt32 InstanceCount) 
    {
        {
            {
                SCOPED_CPU_TIMING(GroupRendering, "FoliageSystemR::UpdateInstanceBufferAsync");
                auto* transformSystemR = TYPE_CAST(const TransformSystemR*, mRenderWorld->GetRenderSystem<TransformSystemR>());
                if (type == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE)
                {
                    // UInt32 stride = sizeof(FoliageInstanceData);
                    UInt32 stride = sizeof(FoliageInstanceCompactData);
                    UInt32 instanceBufferSize = InstanceCount * stride;
                    void* destination;

                    auto [stagingBuffer, offset, _destination] = state.mBuffer->Raw();
                    destination = _destination;

                    auto localInstanceData = dataContainer->mLocalInstanceData.data();

                    {
                            {
                                for (UInt32 i = 0; i < InstanceCount; ++i)
                                {
                                    auto& foliageInstanceCompactData = localInstanceData[i];
                                    foliageInstanceCompactData.entityIndex = state.mEntityOffset ;
#if _WINDOWS
                                    memcpy_fast(static_cast<UInt8*>(destination) + i * stride, &foliageInstanceCompactData, sizeof(FoliageInstanceCompactData));
#else
                                    memcpy(static_cast<UInt8*>(destination) + i * stride, &foliageInstanceCompactData, sizeof(FoliageInstanceCompactData));
#endif
                                }
                            }
                    }

                    stagingBuffer->UnmapRange(offset, instanceBufferSize);
                }
            }
        }


    }

    void FoliageSystemR::UpdateEntityBuffer(ClusterKey key) 
    {
        if (mClusterOffsetMap.find(key) != mClusterOffsetMap.end())
        {
            DrawInstanceInfo& info = mClusterOffsetMap[key];
            if (!mRenderWorld->IsEntityAlive(info.entityID))
            {
                return;
            }
            if (info.instanceDataType == InstanceDataType::Foliage)
            {
                if (auto [foliageHandle, renderNodeComp] = mRenderWorld->GetComponent<FoliageComponentR, RenderNodeComponentR>(info.entityID); foliageHandle.IsValid() && renderNodeComp.IsValid())
                {
                    UInt32 entitySize = static_cast<UInt32>(sizeof(FoliageEntityData));
                    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                    
                    auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, entitySize);

                    FoliageEntityData entityInfoData = {};

                    //if (!IsPCGFoliage(foliageHandle.Read()))
                    {
                        auto renderNodeWriter = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                        auto& worldTransform = renderNodeWriter->GetRenderNode()->GetWorldTransform();
                        auto& relativeMatrix = worldTransform.RelativeMatrix;
                        auto& preRelativeMatrix = worldTransform.PreRelativeMatrix;
                        entityInfoData.world = relativeMatrix;
                        entityInfoData.preWorld = preRelativeMatrix;
                        entityInfoData.invTransposeWorld = relativeMatrix.Inverted().Transpose();
                        entityInfoData.tilePosition = worldTransform.TilePosition;
                        entityInfoData.preTilePosition = worldTransform.PreTilePosition;
                    }

                    stagingBufferWrap.MemWrite(0, &entityInfoData, entitySize);

                    NGICopyBuffer region{
                        stagingBufferWrap.GetNGIOffset(),
                        static_cast<SizeType>(entitySize * info.entityIndex),
                        entitySize,
                    };

                    rendererSystem->UpdateBuffer(FoliageGpuDriven::mFoliageEntityBuffer.get(),
                                                 stagingBufferWrap.GetNGIBuffer(),
                                                 region,
                                                 NGIResourceState::Undefined,
                                                 NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                }
            }
        }
    }

    void FoliageSystemR::PrepareInstancebufferDataAsyncByChangelist(const FrameChangeList<ecs::EntityID>& changelist) 
    {
        auto changeCount = changelist.GetCount();
        FFSRenderPipelineSetting* pipelineSetting = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
        for (auto i = 0U; i != changeCount; i++)
        {
            const auto entityID = mChangeList.GetData(i);
            if (!mRenderWorld->IsEntityAliveInner(entityID) || !GetEnable(entityID))
                continue;
            auto [foliageHandle, renderNodeHandle] = mRenderWorld->GetComponent<FoliageComponentR, RenderNodeComponentR>(entityID);
            const FoliageComponentReader& foliageComp = foliageHandle.Read();
            auto lightCount = foliageComp->mDataContainer->mInstanceDataPointLight.size() + foliageComp->mDataContainer->mInstanceDataSpotLight.size();
            if (foliageComp->mInstanceCount == 0 && lightCount == 0)
            {
                // diable previous
                auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
                auto* key = renderNodeSystem->GetRenderNode(renderNodeHandle.Write());
                if (mClusterOffsetMap.find(key) != mClusterOffsetMap.end())
                {
                    tRenderMeshes.clear();
                    renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes, RenderNodeType::Foliage);
                    mClusterOffsetMap[key].instanceCount = 0;
                }
                continue;
            }
            auto foliageType = foliageComp->mFoliageGenerationType;
            if ((foliageType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE) && pipelineSetting->EnableFoliageDrawing)
            {
                auto instancecount = GetInstanceCount(foliageHandle.Read());
                UInt32 stride = sizeof(FoliageInstanceCompactData);
                UInt32 instanceBufferSize = instancecount * stride;
                auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
                auto* meshRenderNode = static_cast<NormalRenderNode*>(renderNodeSystem->GetRenderNode(renderNodeHandle.Write()));
                bool instanceCountChange = mClusterOffsetMap.find(meshRenderNode) == mClusterOffsetMap.end() || mClusterOffsetMap[meshRenderNode].instanceCount != GetInstanceCount(foliageHandle.Read());
                std::scoped_lock lock(mMtx);
                {
                    if (mInstanceBufferUpdateList.find(entityID) == mInstanceBufferUpdateList.end())
                    {
                        auto statetoadd = InstanceBufferState();
                        statetoadd.mBuffer = std::make_shared<AsyncUsedStageBuffer>(rendererSystem->GetScratchBuffer()->AllocateStaging_Async(NGIBufferUsage::CopySrc, instanceBufferSize));
                        if (instanceCountChange)
                        {
                            statetoadd.mEntityOffset = mFoliageEntityOffset++;
                        }
                        else
                        {
                            statetoadd.mEntityOffset = mClusterOffsetMap[meshRenderNode].entityIndex;
                        }
                        mInstanceBufferUpdateList[entityID] = statetoadd; 
                        mInstanceBufferUpdateList[entityID].mTask = threading::Async([type = foliageType,
                                                                                      entityID = entityID,
                                                                                      instanceCount = instancecount,
                                                                                      renderWorld = mRenderWorld,
                                                                                      this, 
                                                                                      state = mInstanceBufferUpdateList[entityID], 
                                                                                      meshRenderNode = meshRenderNode, 
                                                                                      datacontainer = foliageHandle.Read()->mDataContainer](auto) {
                            if (renderWorld->IsEntityAliveInner(entityID))
                            {
                                CopyInstanceBufferToStageBuffer(meshRenderNode, state, datacontainer, type, instanceCount);
                            }
                            });
                    }
                }
            }
            else if (foliageType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT)
            {
                ResetLightInstanceData();
            }
        }
    }

    void FoliageSystemR::UpdateChangedFoliageEntity() 
    {
        {
            for (auto it = mInstanceBufferUpdateList.begin(); it != mInstanceBufferUpdateList.end();)
            {
                if (it->second.mTask && it->second.mTask->Complete())
                {
                    //LOG_INFO("Upload Foliage Instance Buffer Data for Entity: {}", it->first);
                    if (mRenderWorld->IsEntityAliveInner(it->first))
                    {
                        UpdateChangedEntity(it->first, it->second, true, false);
                    }
                    else
                    {
                        LOG_ERROR("Upload Foliage Is Destroyed: {}", it->first);
                    }
                    {
                        std::scoped_lock lock(mMtx);
                        it = mInstanceBufferUpdateList.erase(it);
                    }
                }
                else
                {
                    it++;
                }
            }
        }
    }

    void FoliageSystemR::UpdateInstanceBuffer(ClusterKey key, const UInt8* instanceDataNew)
    {
        SCOPED_CPU_TIMING(GroupRendering, "FoliageSystemR::UpdateInstanceBuffer");
        if (mClusterOffsetMap.find(key) != mClusterOffsetMap.end())
        {
            DrawInstanceInfo& info = mClusterOffsetMap[key];
            if (!mRenderWorld->IsEntityAliveInner(info.entityID))
            {
                return;
            }
            if (info.dataDirty)
            {
                auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                auto* transformSystemR = TYPE_CAST(const TransformSystemR*, mRenderWorld->GetRenderSystem<TransformSystemR>());
                if (info.instanceDataType == InstanceDataType::Foliage)
                {
                    //UInt32 stride = sizeof(FoliageInstanceData);
                    UInt32 stride = sizeof(FoliageInstanceCompactData);
                    UInt32 instanceBufferSize = info.instanceCount * stride;
                    
                    auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, instanceBufferSize);
                    

                    auto [foliageHandle, tilePositionComponent] = mRenderWorld->GetComponent<FoliageComponentR, TilePositionComponentR>(info.entityID);
                    const auto* instanceData = GetInstanceData(foliageHandle.Read());
                    const auto* preInstanceData = GetPreInstanceData(foliageHandle.Read());
                    auto& tilePosition = transformSystemR->GetTilePosition(tilePositionComponent.Read());

                    auto localInstanceData = foliageHandle.Write()->mDataContainer->mLocalInstanceData.data();

                    const auto numWorkers = threading::TaskSystem::GetNumWorkerThreadsForTask();
                    UInt32 batchSize = info.instanceCount / static_cast<UInt32>(numWorkers) + 1;

                  
                    {
                        cross::threading::ParallelFor(static_cast<SInt32>(numWorkers), [&](auto taskIndex) {
                            SCOPED_CPU_TIMING(GroupRendering, "FoliageSystemR::UpdateInstanceBuffer_For");
                            //if (localInstanceData)
                            {
                                auto* pFoliageInstanceCompactData = &localInstanceData[taskIndex * batchSize];
                                for (UInt32 i = 0; i < batchSize && (taskIndex * batchSize + i) < info.instanceCount; ++i)
                                {
                                    auto& foliageInstanceCompactData = pFoliageInstanceCompactData[i];
                                    foliageInstanceCompactData.entityIndex = info.entityIndex;
                                    stagingBufferWrap.MemWrite((taskIndex * batchSize + i) * stride, &foliageInstanceCompactData, sizeof(FoliageInstanceCompactData));
                                }
                            }
                        });
                    }

                    NGICopyBuffer region{
                        stagingBufferWrap.GetNGIOffset(),
                        static_cast<SizeType>(info.dataOffset),
                        instanceBufferSize,
                    };

                    rendererSystem->UpdateBuffer(FoliageGpuDriven::mFoliageInstanceBuffer.get(),
                                                 stagingBufferWrap.GetNGIBuffer(),
                                                 region,
                                                 NGIResourceState::Undefined,
                                                 NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                    info.dataDirty = false;
                }
                else
                {
                    if (info.firstUpload)
                    {
                        const auto instanceBufferSize = info.instanceCount * sizeof(LightInstanceData);

                        auto stagingBufferWrap = rendererSystem->GetScratchBuffer()->AllocateStaging(NGIBufferUsage::CopySrc, instanceBufferSize);
                        auto foliageHandle = mRenderWorld->GetComponent<FoliageComponentR>(info.entityID);
                        stagingBufferWrap.MemWrite(0, instanceDataNew, instanceBufferSize);

                        NGICopyBuffer region{
                            stagingBufferWrap.GetNGIOffset(),
                            static_cast<SizeType>(info.dataOffset),
                            instanceBufferSize,
                        };

                        rendererSystem->UpdateBuffer(FoliageGpuDriven::mLightInstanceBuffer.get(),
                                                     stagingBufferWrap.GetNGIBuffer(),
                                                     region,
                                                     NGIResourceState::Undefined,
                                                     NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                        info.firstUpload = false;
                        info.dataDirty = false;
                    }
                }
            }
        }
    }

    void FoliageSystemR::UpdateInstanceBufferUseStageBuffer(ClusterKey key, const InstanceBufferState& state, UInt32 instanceBufferSize) 
    {
        if (mClusterOffsetMap.find(key) != mClusterOffsetMap.end())
        {
            DrawInstanceInfo& info = mClusterOffsetMap[key];
            auto [buffer, offset, dstData] = state.mBuffer->Raw();
            if (info.dataDirty)
            {
                auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
                if (info.instanceDataType == InstanceDataType::Foliage)
                {
                    NGICopyBuffer region{
                        offset,
                        static_cast<SizeType>(info.dataOffset),
                        instanceBufferSize,
                    };

                    rendererSystem->UpdateBuffer(FoliageGpuDriven::mFoliageInstanceBuffer.get(),
                                                 buffer,
                                                 region,
                                                 NGIResourceState::Undefined,
                                                 NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                    info.dataDirty = false;
                }
                else
                {
                    if (info.firstUpload)
                    {
                        NGICopyBuffer region{
                            offset,
                            static_cast<SizeType>(info.dataOffset),
                            instanceBufferSize,
                        };

                        rendererSystem->UpdateBuffer(FoliageGpuDriven::mLightInstanceBuffer.get(),
                                                     buffer,
                                                     region,
                                                     NGIResourceState::Undefined,
                                                     NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource | NGIResourceState::VertexShaderShaderResource);
                        info.firstUpload = false;
                        info.dataDirty = false;
                    }
                }
            }
        }
    }

    void FoliageSystemR::UpdateChangedEntity(ecs::EntityID entityID, const InstanceBufferState& state, bool useStageBuffer, bool forceReupdate)
    {
        tRenderMeshes.clear();
        tLODSettings.clear();
        bool visible = GetEnable(entityID);
        if (!mRenderWorld->IsEntityAlive(entityID))
            return;
        auto frameNumber = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
        bool enableGPUDriven = false;
        FFSRenderPipelineSetting* pipelineSetting = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
        if (pipelineSetting)
        {
            enableGPUDriven = pipelineSetting->mFoliageGpuDrivenSettings.enable;
        }
        auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();

        auto [foliageHandle, renderNodeHandle] = mRenderWorld->GetComponent<FoliageComponentR, RenderNodeComponentR>(entityID);

        // use rendernode as key
        auto* meshRenderNode = static_cast<NormalRenderNode*>(renderNodeSystem->GetRenderNode(renderNodeHandle.Write()));

        if (visible)
        {
            RenderMesh renderMesh{};
            MeshR* sharedGeometryList;

            Assert(GetSectionCount(foliageHandle.Read()));
            const auto& firstSection = GetSection(foliageHandle.Read(), 0);
            auto meshAssetData = firstSection.mMeshAsset->GetAssetData();

            sharedGeometryList = static_cast<MeshR*>(meshAssetData->GetRenderMesh());
            Assert(sharedGeometryList);

            if (sharedGeometryList->IsGeometryEmpty() && sharedGeometryList->TryUpdate(frameNumber))
            {
                if (sharedGeometryList->GetState() == MeshR::State::Uninitialized || sharedGeometryList->GetState() == MeshR::State::NeedReinitialized)
                {
                    sharedGeometryList->BuildStaticMesh();
                }
            }

            const auto lodCount = std::min(resource::MAX_MESH_LOD_NUM, meshAssetData->GetLodCount());
            const auto effectiveLoDCount = lodCount;
            // const auto effectiveLoDCount = std::min(lodCount, static_cast<UInt8>(GetSectionCount(foliageHandle.Read())));
            //  Assert(GetSectionCount(foliageHandle.Read()) == lodCount);
            auto submeshCount = meshAssetData->GetMeshPartCount(0);
            UInt32 indirectCount = (effectiveLoDCount > 1 ? 2 : 1) * submeshCount;

            renderMesh.mLODCount = effectiveLoDCount;

            // negative offset is not ready
            {
                renderMesh.mInstanceData = GetInstanceData(foliageHandle.Read());
                renderMesh.mPreInstanceData = GetPreInstanceData(foliageHandle.Read());
                renderMesh.mInstanceCount = GetInstanceCount(foliageHandle.Read());
            }

            if (renderMesh.mInstanceCount > 0)
            {
                bool instanceCountChange = mClusterOffsetMap.find(meshRenderNode) == mClusterOffsetMap.end() || mClusterOffsetMap[meshRenderNode].instanceCount != renderMesh.mInstanceCount;
                if (instanceCountChange || forceReupdate)
                {
                    UInt32 instanceSize = static_cast<UInt32>(renderMesh.mInstanceCount * sizeof(FoliageInstanceCompactData));
                    UInt32 entitySize = static_cast<UInt32>(sizeof(FoliageEntityData));

                    FoliageGpuDriven::PreAllocateBuffer(FoliageGpuDriven::mFoliageInstanceBuffer,
                                                        NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::VertexBuffer | NGIBufferUsage::RWStructuredBuffer | NGIBufferUsage::StructuredBuffer,
                                                        mFoliageClusterOffset,
                                                        instanceSize,
                                                        NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);

                    FoliageGpuDriven::PreAllocateBuffer(FoliageGpuDriven::mFoliageEntityBuffer,
                                                        NGIBufferUsage::CopyDst | NGIBufferUsage::StructuredBuffer,
                                                        useStageBuffer ? state.mEntityOffset * entitySize : mFoliageEntityOffset * entitySize,
                                                        entitySize,
                                                        NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);

                    auto offset = useStageBuffer ? state.mEntityOffset : mFoliageEntityOffset++;
                    DrawInstanceInfo info = {
                        mFoliageClusterOffset, 
                        mFoliageStartCluster, 
                        (UInt32)renderMesh.mInstanceCount, 
                        0, 
                        InstanceDataType::Foliage, 
                        true, 
                        false, 
                        entityID, 
                        renderMesh.mInstanceData, 
                        offset,
                        GetMaxRandomCulling(foliageHandle.Read()),
                    };
                    mClusterOffsetMap[meshRenderNode] = std::move(info);

                    mFoliageClusterOffset += instanceSize;
                    mFoliageStartCluster += static_cast<UInt32>(renderMesh.mInstanceCount);
                }
                else
                {
                    mClusterOffsetMap[meshRenderNode].maxRandomCulling = GetMaxRandomCulling(foliageHandle.Read());
                    mClusterOffsetMap[meshRenderNode].dataDirty = true;
                }
                if (useStageBuffer)
                {
                    UInt32 stride = sizeof(FoliageInstanceCompactData);
                    UInt32 instanceBufferSize = static_cast<UInt32>(renderMesh.mInstanceCount * stride);
                    UpdateInstanceBufferUseStageBuffer(meshRenderNode, state, instanceBufferSize);
                }
                else
                {
                    UpdateInstanceBuffer(meshRenderNode, renderMesh.mInstanceData);
                }
                UpdateEntityBuffer(meshRenderNode);

                renderMesh.mLODMaterials.resize(renderMesh.mLODCount - 1);

                auto submeshProperties = GetSubmeshProperty(foliageHandle.Read());
                UInt32 meshPartStartIndex, subSectionCount;
                meshAssetData->GetMeshLodInfo(0, meshPartStartIndex, subSectionCount);
                const auto effectiveSubSectionCount = subSectionCount;
                for (auto subSectionIndex = 0U; subSectionIndex != effectiveSubSectionCount; subSectionIndex++)
                {
                    if (subSectionIndex < submeshProperties.size())
                    {
                        if (!submeshProperties[subSectionIndex].visible)
                            continue;
                    }
                    RenderMesh subRenderMesh = renderMesh;
                    for (auto lodIndex = 0U, _lodCount = static_cast<UInt32>(renderMesh.mLODCount); lodIndex != _lodCount; lodIndex++)
                    {
                        const auto& section = GetSection(foliageHandle.Read(), lodIndex);
                        section.mMeshAsset->GetAssetData()->GetMeshLodInfo(lodIndex, meshPartStartIndex, subSectionCount);

                        auto geoIndex = meshPartStartIndex + std::min<UInt32>(subSectionIndex, subSectionCount -1);
                        subRenderMesh.mLODGeometries[lodIndex] = geoIndex < sharedGeometryList->GetGeometryCount() ? &sharedGeometryList->GetRenderGeometry(geoIndex) : nullptr;
                        auto boundingBox = meshAssetData->GetBoundingBox();
                        Float3 extent, center;
                        boundingBox.GetCenter(&center);
                        boundingBox.GetExtent(&extent);
                        float radius = extent.Length();
                        subRenderMesh.mBoundingSphere = Float4(center.x, center.y, center.z, radius);
                        if (!section.mSubSectionMaterials.empty() && subSectionIndex < section.mSubSectionMaterials.size())
                        {
                            if (lodIndex == 0)
                            {
                                subRenderMesh.mDefaultMaterial = section.mSubSectionMaterials[subSectionIndex];
                            }
                            else
                            {
                                subRenderMesh.mLODMaterials[lodIndex - 1] = section.mSubSectionMaterials[subSectionIndex];
                            }
                        }
                        else
                        {
                            if (lodIndex == 0)
                            {
                                subRenderMesh.mDefaultMaterial = section.mDefaultMaterial;
                            }
                            else
                            {
                                subRenderMesh.mLODMaterials[lodIndex - 1] = section.mDefaultMaterial;
                            }
                        }
                    }
                    tRenderMeshes.push_back(subRenderMesh);
                    tLODSettings.push_back(firstSection.mMeshAsset->GetLODSetting());
                }
            }
        }

        renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes, RenderNodeType::Foliage);
        renderNodeSystem->SetObjectVar(renderNodeHandle, "USE_FOLIAGE_INSTANCE", true);
        renderNodeSystem->SetObjectVar(renderNodeHandle, "FOLIAGE_RENDERING", true);
        renderNodeSystem->SetObjectVar(renderNodeHandle, "FOLIAGE_VSM", true);
        meshRenderNode->SetLODSettings(tLODSettings);
        meshRenderNode->mScreenSizeScale = GetScreenSizeScale(foliageHandle.Read());
        meshRenderNode->SetLODBias(static_cast<UInt8>(EngineGlobal::GetSettingMgr()->GetFoliageLoDBias()));
        meshRenderNode->SetLODSelected(enableGPUDriven ? 0 : -1);
    }

    void FoliageSystemR::OnFirstUpdate(FrameParam* frameParam)
    {
        auto* renderPropertySystem = TYPE_CAST(RenderPropertySystemR*, mRenderWorld->GetRenderSystem<RenderPropertySystemR>());
        renderPropertySystem->SetCullingProperty(mPointLightEntity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
        renderPropertySystem->SetCullingProperty(mSpotLightEntity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
    }

    void FoliageSystemR::NotifyEvent(const SystemEventBase& event, UInt32& flag)
    {
        RenderSystemBase::NotifyEvent(event, flag);

        if (event.mEventType == OnSystemAddToRenderWorldEvent::sEventType)
        {
            mPointLightEntity = mRenderWorld->CreateEntityID();
            mSpotLightEntity = mRenderWorld->CreateEntityID();
            mRenderWorld->CreateComponents<TransformComponentR, TilePositionComponentR, AABBComponentR, RenderNodeComponentR, RenderPropertyComponentR>(mPointLightEntity);
            mRenderWorld->CreateComponents<TransformComponentR, TilePositionComponentR, AABBComponentR, RenderNodeComponentR, RenderPropertyComponentR>(mSpotLightEntity);
        }
    }

    void FoliageSystemR::OnRenderPipelineSettingChange()
    {
        if (!EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->GPUDrivenLight)
        {
            ResetLightInstanceData();
        }
        if (!EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->EnableFoliageDrawing)
        {
            auto AllFoliages = mRenderWorld->Query<FoliageComponentR>();
            auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
            for (const auto& Ent : AllFoliages)
            {
                auto entityID = Ent.GetEntityID();
                for (auto& _pair : mClusterOffsetMap)
                {
                    auto& info = _pair.second;
                    if (info.entityID == entityID)
                    {
                        mDestroyedFoliages.insert(entityID);
                        break;
                    }
                }

                tRenderMeshes.resize(0);
                auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(entityID);
                renderNodeSystem->SetRenderMeshes(renderNodeHandle, std::vector<cross::RenderMesh>());
            }
        }
    }

    void FoliageSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
    {
        CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this, frameNumber = frameParam->GetFrameCount()] {
            SCOPED_CPU_TIMING(GroupRendering, "FoliageSystemRUpdate");
            auto changeCount = mChangeList.GetCount();
            FFSRenderPipelineSetting* pipelineSetting = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());

            bool enableGPUDriven = false;
            if (pipelineSetting)
            {
                enableGPUDriven = pipelineSetting->mFoliageGpuDrivenSettings.enable;
            }
            if (enableGPUDriven)
            {
                if (changeCount > 0 && pipelineSetting->EnableFoliageDrawing)
                {
                    // collect meshAssetData and combine to GeometryPacket
                    std::vector<MeshR*> renderGeometryLists;
                    std::vector<MeshAssetDataResourcePtr> meshAssets;
                    for (auto i = 0U; i != changeCount; i++)
                    {
                        auto entityID = mChangeList.GetData(i);
                        if (!mRenderWorld->IsEntityAlive(entityID))
                            continue;
                        auto [foliageHandle, renderNodeHandle] = mRenderWorld->GetComponent<FoliageComponentR, RenderNodeComponentR>(entityID);
                        if (foliageHandle.Read()->mInstanceCount == 0 || IsPCGFoliage(foliageHandle.Read()))
                        {
                            LOG_INFO("Foliage Instance Count 0.");
                            continue;
                        }
                        {
                            const FoliageComponentReader& foliageComp = foliageHandle.Read();
                            auto foliageType = foliageComp->mFoliageGenerationType;
                            if (foliageType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE)
                            {
                                Assert(GetSectionCount(foliageComp));
                                const auto& section = GetSection(foliageComp, 0);
                                auto meshAssetData = section.mMeshAsset->GetAssetData();
                                // Is this logic the same as before?
                                auto renderMesh = static_cast<MeshR*>(meshAssetData->GetRenderMesh());
                                if (auto state = renderMesh->GetState(); state == MeshR::State::Uninitialized || state == MeshR::State::NeedReinitialized)
                                {
                                    renderMesh->SetState(MeshR::State::Uninitialized);
                                    renderGeometryLists.push_back(renderMesh);
                                    meshAssets.push_back(section.mMeshAsset);
                                }
                            }
                        }
                    }

                    if (meshAssets.size() > 0)
                    {
                        std::scoped_lock lock(mBuildMutex);
                        IVertexStreamLayoutPolicy* packStreamLayoutPolicy = RenderFactory::Instance().GetVertexStreamLayoutPolicy(STREAM_LAYOUT_POLICY_STATIC);
                        if (packStreamLayoutPolicy)
                        {
                            packStreamLayoutPolicy->AssembleMergedGpuResource(renderGeometryLists, meshAssets, mMergedGeometryPacket);
                        }
                    }
                }
                //1.Collect changes , update instancebuffer data async
                PrepareInstancebufferDataAsyncByChangelist(mChangeList);
                //2. update changed data to gpu
                UpdateChangedFoliageEntity();
                //3. Collect destroy entities, and reconstruct the instance buffer data when upload ready
                ProcessEntityDestroyUseStageBuffer();
                // update light list
                UpdateLightList();
            }

        });
    }

    void FoliageSystemR::ResetLightInstanceData()
    {
        mLightUploadSet.clear();
        mLightUpdate = true;
        mLightClusterOffset = 0;
        mLightIndirectOffset = 0;
        mLightStartCluster = 0;

        tRenderMeshes.resize(0);
        auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
        auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(mPointLightEntity);
        renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes);
        mClusterOffsetMap.erase(renderNodeSystem->GetRenderNode(renderNodeHandle.Read()));

        renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(mSpotLightEntity);
        renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes);
        mClusterOffsetMap.erase(renderNodeSystem->GetRenderNode(renderNodeHandle.Read()));
    }

    void FoliageSystemR::SetWorldMatrix(ecs::EntityID entity, const Float4x4& worldMatrix)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        {
            auto writer = foliageComponent.Write();
            writer->mWorldMatrix = worldMatrix;
        }
    }

    void FoliageSystemR::SetLoDSections(ecs::EntityID entity, const std::vector<FoliageComponentR::LoDSection>& sections)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        {
            auto writer = foliageComponent.Write();
            writer->mLoDSections = sections;
        }
    }

    void FoliageSystemR::SetSubmeshPropertys(ecs::EntityID entity, const std::vector<FoliageComponentR::SubmeshProperty>& submeshProperties)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        {
            auto writer = foliageComponent.Write();
            writer->mSubmeshProperty = submeshProperties;
        }
    }

    void FoliageSystemR::SetInstanceDataCount(ecs::EntityID entity, UInt64 count)
    {
        QUICK_SCOPED_CPU_TIMING("FoliageSystemR::SetInstanceData");
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        if (!IsPCGFoliage(ecs::GrantReadAccess(writer)))
        {
            auto instanceDataSize = count * sizeof(Float4x4);
            // writer->mLocalInstanceData = std::move(data);
            const UInt8* preInstanceData = writer->mDataContainer->mInstanceData.data();
            //writer->mInstanceData.resize(instanceDataSize);
            //writer->mPreInstanceData.resize(instanceDataSize);
            writer->mInstanceCount = count;
            auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(writer.GetEntityID());
            auto key = mRenderWorld->GetRenderSystem<RenderNodeSystemR>()->GetRenderNode(renderNodeHandle.Read());
            if (preInstanceData != writer->mDataContainer->mInstanceData.data() && mClusterOffsetMap.find(key) != mClusterOffsetMap.end())
            {
                mClusterOffsetMap.erase(key);
            }
        }
    }

    void FoliageSystemR::SetPCGReservedCapacity(ecs::EntityID entity, UInt64 count)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        writer->mPCGReservedCapacity = count;
        auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(writer.GetEntityID());
        auto key = mRenderWorld->GetRenderSystem<RenderNodeSystemR>()->GetRenderNode(renderNodeHandle.Read());
        if (writer->mFoliageGenerationType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_PCG)
        {
            if (mClusterOffsetMap.find(key) != mClusterOffsetMap.end() && mClusterOffsetMap[key].instanceCount != count)
            {
                mClusterOffsetMap.erase(key);
            }
        }
    }

    void FoliageSystemR::SetInstanceDataLight(ecs::EntityID entity, const std::vector<LightInstanceData>& pointData, const std::vector<LightInstanceData>& spotData)
    {
        QUICK_SCOPED_CPU_TIMING(__FUNCTION__);
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        {
            auto writer = foliageComponent.Write();
            writer->mDataContainer->mInstanceDataPointLight = pointData;
            writer->mDataContainer->mInstanceDataSpotLight = spotData;
        }
    }
    void FoliageSystemR::UpdateLightList()
    {
        SCOPED_CPU_TIMING(GroupRendering, "FoliageSystemR::UpdateLightList");
        // update light list
        FFSRenderPipelineSetting* pipelineSetting = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
        bool enableGPUDrivenLight = false;
        if (pipelineSetting)
        {
            enableGPUDrivenLight = pipelineSetting->GPUDrivenLight;
        }
        if (enableGPUDrivenLight)
        {
            // collect destroy light list
            mDestroyLightLists.clear();
            UInt32 remainedEntityLifeCycleEventCount = mRenderWorld->GetRemainedEventCount<EntityDestroyEvent>(true);
            for (UInt32 i = 0; i < remainedEntityLifeCycleEventCount; ++i)
            {
                const EntityDestroyEvent& lifeEvent = mRenderWorld->GetRemainedEvent<EntityDestroyEvent>(i);
                auto& entityID = lifeEvent.mData.mEntityID;

                if (ecs::HasComponentMask<FoliageComponentR>(lifeEvent.mData.mChangedComponentMask) && mLightUploadSet.find(entityID) != mLightUploadSet.end())
                {
                    mDestroyLightLists.push_back(entityID); 
                }
            }

            if (mDestroyLightLists.size() > 0)
            {
                ResetLightInstanceData();
            }

            mLightLists.clear();

            auto propertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
            const auto& propChangeList = propertySystem->GetChangeListCullingData();
            {
                auto changeListSize = propChangeList.GetCount();
                for (UInt32 changeListIndex = 0; changeListIndex < changeListSize; ++changeListIndex)
                {
                    const auto& entityID = propChangeList.GetData(changeListIndex).first;
                    if (mRenderWorld->IsEntityAlive(entityID))
                    {
                        auto foliage = mRenderWorld->GetComponent<FoliageComponentR>(entityID);
                        if (foliage.IsValid())
                        {
                            auto foliageType = foliage.Read()->mFoliageGenerationType;
                            if (foliageType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT)
                            {
                                ResetLightInstanceData();
                            }
                        }
                    }
                }
            }

            if (mLightUpdate)
            {
                auto foliageComps = mRenderWorld->Query<FoliageComponentR, TransformComponentR>();
                for (auto [foliage, transform] : foliageComps)
                {
                    auto foliageType = foliage.Read()->mFoliageGenerationType;
                    auto enable = foliage.Read()->mEnable;
                    if (foliageType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT && enable)
                    {
                        mLightLists.push_back(foliage.GetEntityID());
                    }
                }
                UpdateLightInstanceData();
            }

            if (mDestroyLightLists.size() > 0)
            {
                FoliageGpuDriven::TryShrinkBuffer(FoliageGpuDriven::mLightInstanceBuffer, mLightClusterOffset, NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);
            }
        }
        else
        {
            if (mGPULight)
            {
                ResetLightInstanceData();
            }
        }
        mGPULight = enableGPUDrivenLight;
    }

    void FoliageSystemR::UpdateLightInstanceData()
    {
        if (mLightLists.size() > 0)
        {
            auto* transformSystemR = TYPE_CAST(const TransformSystemR*, mRenderWorld->GetRenderSystem<TransformSystemR>());
            auto propertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();

            mPointLightInstanceData.clear();
            mSpotLightInstanceData.clear();
            mPointLightEntityData.clear();
            mSpotLightEntityData.clear();
            for (UInt32 lightIdx = 0; lightIdx < mLightLists.size(); ++lightIdx)
            {
                auto& lightEntityID = mLightLists[lightIdx];
                if (mRenderWorld->HasComponent<FoliageComponentR>(lightEntityID))
                {
                    auto [foliageHandle, transformComponent, renderProp] = mRenderWorld->GetComponent<FoliageComponentR, TransformComponentR, RenderPropertyComponentR>(lightEntityID);
                    const FoliageComponentReader& foliageComp = foliageHandle.Read();
                    bool isHide = propertySystem->IsHide(renderProp.Read());
                    if (auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(lightEntityID); renderNodeComp.IsValid() && !isHide)
                    {
                        auto renderNodeWriter = renderNodeComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2);
                        auto& relativeMatrix = renderNodeWriter->mRenderNode->mWorldTransform.RelativeMatrix;
                        auto& worldRot = transformSystemR->GetWorldRotation(transformComponent.Read());

                        if (foliageComp->mDataContainer->mInstanceDataPointLight.size() > 0)
                        {
                            for (UInt32 i = 0; i < foliageComp->mDataContainer->mInstanceDataPointLight.size(); ++i)
                            {
                                auto& data = foliageComp->mDataContainer->mInstanceDataPointLight[i];
                                LightInstanceData instance = data;
                                Float4 pos(data.singleLightDirPos);
                                float range = data.singleLightDirPos.w;
                                pos.w = 1;
                                pos = pos * relativeMatrix;
                                Float3 worldPos = pos.XYZ();
                                instance.singleLightDirPos = Float4(worldPos, range);
                                instance.singleLightTilePosition = Float4(renderNodeWriter->mRenderNode->mWorldTransform.TilePosition, 0);
                                instance.world = (Float4x4::CreateScale(data.singleLightDirPos.w) * Float4x4::CreateTranslation(worldPos));
                                mPointLightInstanceData.push_back(std::move(instance));
                            }
                            mPointLightEntityData.insert(mPointLightEntityData.end(), lightEntityID);
                        }
                        if (foliageComp->mDataContainer->mInstanceDataSpotLight.size() > 0)
                        {
                            for (UInt32 i = 0; i < foliageComp->mDataContainer->mInstanceDataSpotLight.size(); ++i)
                            {
                                auto& data = foliageComp->mDataContainer->mInstanceDataSpotLight[i];
                                LightInstanceData instance = data;
                                Float4 pos(data.singleLightDirPos);
                                pos.w = 1;
                                pos = pos * relativeMatrix;
                                Float3 worldPos = pos.XYZ();
                                instance.singleLightDirPos = Float4(worldPos, data.singleLightDirPos.w);
                                instance.singleLightTilePosition = Float4(renderNodeWriter->mRenderNode->mWorldTransform.TilePosition, instance.singleLightTilePosition.w);
                                auto rotation = QuaternionA::EulerToQuaternion(instance.singleLightSpotDirection.XYZ()) * worldRot;
                                auto dir = rotation.Float3Rotate(Float3(0, 0, -1));
                                auto rotMat = Float4x4::CalculationRotationMatrix(Float3(0, 0, -1), dir);
                                auto spotRotEuler = QuaternionA::QuaternionToEuler(rotation);
                                instance.singleLightSpotDirection = Float4(spotRotEuler, instance.singleLightSpotDirection.w);

                                instance.world = (Float4x4::CreateScale(data.singleLightDirPos.w * 1.3f + data.singleLightTilePosition.w) * rotMat * Float4x4::CreateTranslation(worldPos));
                                mSpotLightInstanceData.push_back(std::move(instance));
                            }
                            mSpotLightEntityData.insert(mSpotLightEntityData.end(), lightEntityID);
                        }
                    }
                }
            }

            FFSRenderPipelineSetting* pipelineSetting = dynamic_cast<FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
            if (pipelineSetting)
            {
                auto& settings = pipelineSetting->mFoliageGpuDrivenSettings;
                auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
                auto* renderPropertySystem = TYPE_CAST(RenderPropertySystemR*, mRenderWorld->GetRenderSystem<RenderPropertySystemR>());

                auto prepareRenderNode = [this, renderNodeSystem, renderPropertySystem](ecs::EntityID entityID, MeshAssetDataResourcePtr meshRes, MeshAssetData* meshAssetData, MaterialR* defaultMaterial,
                    std::vector<LightInstanceData>& instanceData, std::unordered_set<ecs::EntityID>& entityData) {
                    bool visible = instanceData.size() > 0;
                    auto cullingProp = visible ? CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE : CullingProperty::CULLING_PROPERTY_NONE;
                    renderPropertySystem->SetCullingProperty(entityID, cullingProp);
                    renderPropertySystem->SetHide(entityID, !visible);
                    renderPropertySystem->SetHideInGame(entityID, !visible);
                    auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(entityID);
                    if (visible && meshRes)
                    {
                        MeshR* sharedGeometryList = static_cast<MeshR*>(meshAssetData->GetRenderMesh());
                        tRenderMeshes.clear();
                        tLODSettings.clear();
                        RenderMesh renderMesh;
                        renderMesh.mEnableCameraMask = false;
                        renderMesh.mLODCount = meshAssetData->GetLodCount();
                        //renderMesh.mLODGeometries[0] = &(sharedGeometryList->GetRenderGeometry(0));
                        renderMesh.mLODMaterials.clear();
                        renderMesh.mDefaultMaterial = defaultMaterial;
                        auto boundingBox = meshAssetData->GetBoundingBox();
                        Float3 extent, center;
                        boundingBox.GetCenter(&center);
                        boundingBox.GetExtent(&extent);
                        float radius = extent.Length();
                        renderMesh.mBoundingSphere = Float4(center.x, center.y, center.z, radius);

                        tLODSettings.push_back(meshRes->GetLODSetting());
                        for (UInt8 lodIndex = 0; lodIndex < renderMesh.mLODCount; ++lodIndex)
                        {
                            renderMesh.mLODGeometries[lodIndex] = &(sharedGeometryList->GetRenderGeometry(lodIndex));
                        }

                        auto* meshRenderNode = static_cast<NormalRenderNode*>(renderNodeSystem->GetRenderNode(renderNodeHandle.Write()));
                        renderMesh.mInstanceData = reinterpret_cast<UInt8*>(instanceData.data());
                        renderMesh.mInstanceCount = instanceData.size();

                        bool instanceCountChange = mClusterOffsetMap.find(meshRenderNode) == mClusterOffsetMap.end() || mClusterOffsetMap[meshRenderNode].instanceCount != (UInt32)renderMesh.mInstanceCount;
                        
                        if (instanceCountChange)
                        {
                            for (auto i = 0; i < instanceData.size(); ++i)
                            {
                                UInt32 instanceID = mLightStartCluster + i;
                                instanceData[i].instanceID = instanceID;
                            }
                            mLightUploadSet.insert(entityData.begin(), entityData.end());
                            UInt32 instanceSize = static_cast<UInt32>(renderMesh.mInstanceCount * sizeof(LightInstanceData));

                            FoliageGpuDriven::PreAllocateBuffer(FoliageGpuDriven::mLightInstanceBuffer,
                                                                NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::VertexBuffer | NGIBufferUsage::RWStructuredBuffer,
                                                                mLightClusterOffset,
                                                                instanceSize,
                                                                NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);

                            UInt32 indirectSize = static_cast<UInt32>(renderMesh.mInstanceCount * sizeof(CompactDrawCMD));
                            FoliageGpuDriven::PreAllocateBuffer(FoliageGpuDriven::mFoliageIndirectBuffer,
                                                                NGIBufferUsage::CopySrc | NGIBufferUsage::CopyDst | NGIBufferUsage::IndirectBuffer | NGIBufferUsage::RWStructuredBuffer,
                                                                mLightIndirectOffset,
                                                                indirectSize,
                                                                NGIResourceState::ComputeShaderShaderResource | NGIResourceState::IndirectArgument,
                                                                false);

                            DrawInstanceInfo info = {mLightClusterOffset, mLightStartCluster, (UInt32)renderMesh.mInstanceCount, 0, InstanceDataType::Light, true, mLightUpdate, entityID, renderMesh.mInstanceData};
                            mClusterOffsetMap[meshRenderNode] = std::move(info);
                            mLightClusterOffset += instanceSize;
                            mLightIndirectOffset += indirectSize;
                            mLightStartCluster += static_cast<UInt32>(renderMesh.mInstanceCount);
                        }
                        else
                        {
                            mClusterOffsetMap[meshRenderNode].dataDirty = true;
                        }

                        UpdateInstanceBuffer(meshRenderNode, renderMesh.mInstanceData);
                        tRenderMeshes.push_back(std::move(renderMesh));

                        renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes, RenderNodeType::Foliage);
                        meshRenderNode->SetLODSettings(tLODSettings);
                    }
                };

                prepareRenderNode(mPointLightEntity, settings.PointLightMeshRes, settings.PointLightMeshR, settings.PointLightMaterialR, mPointLightInstanceData, mPointLightEntityData);

                prepareRenderNode(mSpotLightEntity, settings.SpotLightMeshRes, settings.SpotLightMeshR, settings.SpotLightMaterialR, mSpotLightInstanceData, mSpotLightEntityData);

            }
        }
        mLightUpdate = false;
    }

    void FoliageSystemR::SetWorldBound(ecs::EntityID entity, const BoundingBox& worldBound)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        writer->mWorldBound = worldBound;
    }


    size_t FoliageSystemR::CalculateInstancebufferChangeCount(std::set<ecs::EntityID>& destroylist)
    {
        auto offsetmap = mClusterOffsetMap;
        for (auto& id : mDestroyedFoliages)
        {
            for (auto it = offsetmap.begin(); it != offsetmap.end();)
            {
                if (it->second.entityID == id)
                    it = offsetmap.erase(it);
                else
                    ++it;
            }
        }
        size_t instanceCountRemain = 0;
        for (auto& _pair : offsetmap)
        {
            auto& info = _pair.second;
            if (info.instanceDataType == InstanceDataType::Foliage && destroylist.find(info.entityID) == destroylist.end())
            {
                info.instanceCount = 0;   // force update
                auto foliageHandle = mRenderWorld->GetComponent<FoliageComponentR>(info.entityID);
                instanceCountRemain += foliageHandle.Read()->mInstanceCount;
            }
        }
        return instanceCountRemain;
    }

    void FoliageSystemR::SetEnable(ecs::EntityID entity, bool enable) 
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        writer->mEnable = enable;
    }

    bool FoliageSystemR::GetEnable(ecs::EntityID entity) const 
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto reader = foliageComponent.Read();
        return reader->mEnable;
    }

    void FoliageSystemR::SetScreenSizeScale(ecs::EntityID entity, float screenSizeScale)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        writer->mScreenSizeScale = screenSizeScale;
    }

    void FoliageSystemR::SetFoliageGenerationType(ecs::EntityID entity, FoliageGenerationType generationType)
    {
        auto foliageComponent = mRenderWorld->GetComponent<FoliageComponentR>(entity);
        auto writer = foliageComponent.Write();
        writer->mFoliageGenerationType = generationType;
        if (generationType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_PCG) 
        {
            // no culling for this foliage because we don't know its actual bounding box
            auto renderPropertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
            renderPropertySystem->SetCullingProperty(entity, CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE);
        }
        else if (generationType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_INSTANCE) 
        {
            // reset culling property
            auto renderPropertySystem = mRenderWorld->GetRenderSystem<RenderPropertySystemR>();
            renderPropertySystem->SetCullingProperty(entity, CullingProperty::CULLING_PROPERTY_CULLABLE);
        }
        else if(generationType == FoliageGenerationType::FOLIAGE_GENERATION_TYPE_LIGHT)
        {
            tRenderMeshes.clear();
            auto renderNodeSystem = mRenderWorld->GetRenderSystem<RenderNodeSystemR>();
            auto renderNodeHandle = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
            renderNodeSystem->SetRenderMeshes(renderNodeHandle, tRenderMeshes, RenderNodeType::Foliage);
        }
    }

    void FoliageSystemR::OnPendingToDestroy() 
    {
        auto foliages = mRenderWorld->Query<FoliageComponentR>();
        mDestroyedFoliages.clear();

        for (auto& t : mInstanceBufferUpdateList)
        {
            t.second.mTask->WaitForCompletion();
        }
        
        UInt32 TotalSize = 0;

        mInstanceBufferUpdateList.clear();
        mClusterOffsetMap.clear();
       
        mFoliageClusterOffset = 0;
        mFoliageIndirectOffset = 0;
        mFoliageStartCluster = 0;
        mFoliageEntityOffset = 0;
        mLightClusterOffset = 0;
        mLightIndirectOffset = 0;
        mLightStartCluster = 0;
        mLightUpdate = true;
        for (auto& world : EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<WorldSystemR>()->GetRenderWorldList())
        {
            auto foliagesys = world->GetRenderSystem<FoliageSystemR>();
            TotalSize += foliagesys->mFoliageClusterOffset;
        }
        FoliageGpuDriven::TryShrinkBuffer(FoliageGpuDriven::mFoliageInstanceBuffer, TotalSize, NGIResourceState::ComputeShaderShaderResource | NGIResourceState::PixelShaderShaderResource);
        for (auto& world : EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<WorldSystemR>()->GetRenderWorldList())
        {
            auto foliagesys = world->GetRenderSystem<FoliageSystemR>();
            if (this != foliagesys)
            {
                for (auto& _pair : foliagesys->mClusterOffsetMap)
                {
                    auto& info = _pair.second;
                    if (info.instanceDataType == InstanceDataType::Foliage && world->IsEntityAliveInner(info.entityID))
                    {
                        info.instanceCount = 0;   // force update
                        foliagesys->UpdateChangedEntity(info.entityID, InstanceBufferState());
                    }
                }
            }
        }
    }

    unsigned int FoliageSystemR::GetLightCount()
    {
        return mLightStartCluster;
    }
}
