#include "EnginePrefix.h"
#include "RenderEngine/HierachicalInstancedStaticModelSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "ECS/Develop/Framework.h"
#include "RenderEngine/MeshBuildSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/RenderNode/HierarchicalInstancedStaticModelRenderNode.h"

namespace cross {
ecs::ComponentDesc* HierachicalInstancedStaticModelComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::HierachicalInstancedStaticModelComponentR>(false);
}

HierachicalInstancedStaticModelSystemR* HierachicalInstancedStaticModelSystemR::CreateInstance()
{
    return new HierachicalInstancedStaticModelSystemR();
}

void HierachicalInstancedStaticModelSystemR::Release()
{
    delete this;
}

void HierachicalInstancedStaticModelSystemR::OnBeginFrame(FrameParam* frameParam)
{
    mChangeList.GetContainer().clear();
}

void HierachicalInstancedStaticModelSystemR::OnEndFrame(FrameParam* frameParam) {}

void HierachicalInstancedStaticModelSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        SCOPED_CPU_TIMING(GroupRendering, "HierachicalInstancedStaticModelUpdateR");

        auto* ISMSys = mRenderWorld->GetRenderSystem<InstancedStaticModelSystemR>();
        auto* GPUScene = mRenderWorld->GetRenderSystem<RenderPipelineSystemR>()->GetWorldRenderPipeline()->GetGPUScene();

        // Pass InstancedRenderNode to RenderNodeComponent
        for (UInt32 eventIndex = 0, eventCount = mRenderWorld->GetRemainedEventCount<EntityCreateEvent>(true); eventIndex < eventCount; ++eventIndex)
        {
            auto& lifeEvent = mRenderWorld->GetRemainedEvent<EntityCreateEvent>(eventIndex);
            if (ecs::HasComponentMask<HierachicalInstancedStaticModelComponentR>(lifeEvent.mData.mChangedComponentMask))
            {
                auto& entityID = lifeEvent.mData.mEntityID;
                if (mRenderWorld->IsEntityAlive(entityID))
                {
                    auto [ISMComp, renderNodeComp] = mRenderWorld->GetComponent<InstancedStaticModelComponentR, RenderNodeComponentR>(entityID);
                    if (EnumHasAnyFlags(lifeEvent.mData.mEventFlag, EntityLifeCycleEventFlag::CreateComponent))
                    {
                        ISMComp.Write()->mRenderNode.reset(new HierarchicalInstancedStaticModelRenderNode());
                        
                        renderNodeComp.mComponent->SetRenderNode(ISMComp.Write()->mRenderNode);
                    }
                }
            }
        }

        // Generate RenderNode
        for (auto iter = ISMSys->GetChangeList().GetContainer().begin(); iter != ISMSys->GetChangeList().GetContainer().end(); ++iter)
        {
            ecs::EntityID entity = (*iter);
            if (mRenderWorld->HasComponent<HierachicalInstancedStaticModelComponentR>(entity))
            {
                Assert(mRenderWorld->HasComponent<InstancedStaticModelComponentR>(entity));

                if (!mRenderWorld->IsEntityAlive(entity)) [[unlikely]]
                {
                    continue;
                }

                auto [modelComp, hierachicalModelComp, renderNodeComp] = mRenderWorld->GetComponent<InstancedStaticModelComponentR, HierachicalInstancedStaticModelComponentR, RenderNodeComponentR>(entity);
                if (!modelComp.IsValid()) [[unlikely]]
                {
                    continue;
                }

                auto modelCompReader = modelComp.Read();

                if (!modelCompReader->mVisible)
                {
                    continue;
                }

                MeshR* renderMesh = modelCompReader->mRenderMesh;
                if (!renderMesh)
                {
                    continue;   // Not built
                }

                if (renderMesh->GetState() != MeshR::State::Initialized)
                {
                    continue;
                }

                if (!modelCompReader->mInstanceDataResource)
                {
                    continue;
                }

                ModelRenderNode::RenderModel renderModel{};

                auto ConvertIndividualModelToRenderModel = [&] {
                    auto meshAssetData = modelCompReader->mAsset->GetAssetData();

                    renderModel.mReceiveDecals = modelCompReader->mReceiveDecals;

                    auto modelLODCount = modelCompReader->mLODModelProperties.size();
                    renderModel.mLODModels.resize(modelLODCount);
                    BoundingBox renderModelBoundingBox{BoundingBox::Flags::MergeIdentity};

                    // Use Lod0 Bounding Box
                    BoundingBox renderModelLod0BoundingBox{BoundingBox::Flags::MergeIdentity};
                    {
                        auto const& singleLODModelProperty = modelCompReader->mLODModelProperties[0];
                        auto& singleLODRenderModel = renderModel.mLODModels[0u];

                        auto subModelCount = singleLODModelProperty.mSubModelProperties.size();
                        singleLODRenderModel.mSubModels.resize(subModelCount);
                        for (auto subMeshIndex = 0u; subMeshIndex < subModelCount; subMeshIndex++)
                        {
                            auto const& subModelProperty = singleLODModelProperty.mSubModelProperties[subMeshIndex];

                            if (!subModelProperty.mVisible)
                                continue;

                            auto& subModel = singleLODRenderModel.mSubModels[subMeshIndex];

                            UInt32 subMeshStartIndex = 0;
                            UInt32 subMeshCount = 0;
                            meshAssetData->GetMeshLodInfo(0, subMeshStartIndex, subMeshCount);
                            subModel.mGeometry = subMeshIndex < subMeshCount ? &(renderMesh->GetRenderGeometry(subMeshStartIndex + subMeshIndex)) : nullptr;
                            subModel.mMaterial = subModelProperty.mMaterial;
                            subModel.mBoundingBox = meshAssetData->GetMeshPartBoundingBox(subMeshStartIndex + subMeshIndex);

                            BoundingBox::CreateMerged(renderModelLod0BoundingBox, renderModelLod0BoundingBox, subModel.mBoundingBox);
                        }
                    }
                    for (auto lodIndex = 1u; lodIndex < modelLODCount; lodIndex++)
                    {
                        auto const& singleLODModelProperty = modelCompReader->mLODModelProperties[lodIndex];
                        auto& singleLODRenderModel = renderModel.mLODModels[lodIndex];

                        auto subModelCount = singleLODModelProperty.mSubModelProperties.size();
                        singleLODRenderModel.mSubModels.resize(subModelCount);
                        for (auto subMeshIndex = 0u; subMeshIndex < subModelCount; subMeshIndex++)
                        {
                            auto const& subModelProperty = singleLODModelProperty.mSubModelProperties[subMeshIndex];

                            if (!subModelProperty.mVisible)
                                continue;

                            auto& subModel = singleLODRenderModel.mSubModels[subMeshIndex];

                            UInt32 subMeshStartIndex = 0;
                            UInt32 subMeshCount = 0;
                            meshAssetData->GetMeshLodInfo(lodIndex, subMeshStartIndex, subMeshCount);
                            subModel.mGeometry = subMeshIndex < subMeshCount ? &(renderMesh->GetRenderGeometry(subMeshStartIndex + subMeshIndex)) : nullptr;
                            subModel.mMaterial = subModelProperty.mMaterial;
                            if (modelCompReader->mUseLod0Bbox)
                            {
                                subModel.mBoundingBox = std::move(renderModelLod0BoundingBox);
                            }
                            else
                            {
                                subModel.mBoundingBox = meshAssetData->GetMeshPartBoundingBox(subMeshStartIndex + subMeshIndex);
                                BoundingBox::CreateMerged(renderModelBoundingBox, renderModelBoundingBox, subModel.mBoundingBox);
                            }
                        }
                    }

                    renderModel.mBoundingBox = std::move(modelCompReader->mUseLod0Bbox ? renderModelLod0BoundingBox : renderModelBoundingBox);
                };

                ConvertIndividualModelToRenderModel();

                auto* renderNode = static_cast<HierarchicalInstancedStaticModelRenderNode*>(modelComp.Write()->mRenderNode.get());

                std::vector<const resource::MeshAssetLODSetting*> LODSettings;
                LODSettings.emplace_back(modelComp.Write()->mAsset->GetLODSetting());
                renderNode->SetLODSettings(LODSettings);
                renderNode->FreeGPUScene(*GPUScene);
                renderNode->SetMaxInstancesPerLeaf(hierachicalModelComp.Read()->mMaxInstancesPerLeaf);
                renderNode->SetInternalNodeBranchingFactor(hierachicalModelComp.Read()->mInternalNodeBranchingFactor);
                renderNode->SetDensityLODCount(hierachicalModelComp.Read()->mDensityLODCount);
                renderNode->SetDensityLodDistanceScalar(hierachicalModelComp.Read()->mDensityLodDistanceScalar);
                renderNode->SetRenderModelInstanced(std::move(renderModel), modelCompReader->mInstanceDataResource);
                renderNode->SetGlobalScale(modelCompReader->mGlobalScale);
                renderNode->SetDistnaceCulling(modelCompReader->mDistanceCulling);
                if (modelCompReader->mNeedRebuildClusterTree)
                {
                    renderNode->BuildTree();
                    modelComp.Write()->mNeedRebuildClusterTree = false;
                }

                renderNode->UpdateNodeInstanceCountCache();

                renderNode->AllocateGPUScene(*GPUScene);
                GPUScene->SetGPUSceneDirty(entity);
            }
        }
    });
}
}   // namespace cross
