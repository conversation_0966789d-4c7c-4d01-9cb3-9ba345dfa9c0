#pragma once

#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "Resource/MeshAssetDataResource.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "RenderEngine/InstancedStaticModelSystemR.h"

namespace cross {
struct ComponentDesc;

struct HierachicalInstancedStaticModelComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

    int mMaxInstancesPerLeaf = 16;

    int mInternalNodeBranchingFactor = 16;

    int mDensityLODCount = 1;

    float mDensityLodDistanceScalar = 8;

public:
    bool m_Test;
};

using RenderHierachicalInstancedStaticModelComponentHandle = ecs::ComponentHandle<HierachicalInstancedStaticModelComponentR>;
using RenderHierachicalInstancedStaticModelComponentReader = ecs::ScopedComponentRead<HierachicalInstancedStaticModelComponentR>;
using RenderHierachicalInstancedStaticModelComponentWriter = ecs::ScopedComponentWrite<HierachicalInstancedStaticModelComponentR>;

class HierachicalInstancedStaticModelSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static HierachicalInstancedStaticModelSystemR* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    void SetMaxInstancesPerLeaf(ecs::EntityID entity, int value, bool needRebuild)
    {
        auto [hierachicalmodelH, modelH] = mRenderWorld->GetComponent<HierachicalInstancedStaticModelComponentR, InstancedStaticModelComponentR>(entity);
        hierachicalmodelH.Write()->mMaxInstancesPerLeaf = value;
        if (needRebuild)
        {
            modelH.Write()->mNeedRebuildClusterTree = true;
        }

        SetModelDirty(entity);
    }

    void SetInternalNodeBranchingFactor(ecs::EntityID entity, int value, bool needRebuild)
    {
        auto [hierachicalmodelH, modelH] = mRenderWorld->GetComponent<HierachicalInstancedStaticModelComponentR, InstancedStaticModelComponentR>(entity);
        hierachicalmodelH.Write()->mInternalNodeBranchingFactor = value;
        if (needRebuild)
        {
            modelH.Write()->mNeedRebuildClusterTree = true;
        }

        SetModelDirty(entity);
    }

    void SetDensityLODCount(ecs::EntityID entity, int count, bool needRebuild)
    {
        auto [hierachicalmodelH, modelH] = mRenderWorld->GetComponent<HierachicalInstancedStaticModelComponentR, InstancedStaticModelComponentR>(entity);
        hierachicalmodelH.Write()->mDensityLODCount = count;
        if (needRebuild)
        {
            modelH.Write()->mNeedRebuildClusterTree = true;
        }

        SetModelDirty(entity);
    }

    void SetDensityLodDistanceScalar(ecs::EntityID entity, float value)
    {
        auto [hierachicalmodelH, modelH] = mRenderWorld->GetComponent<HierachicalInstancedStaticModelComponentR, InstancedStaticModelComponentR>(entity);
        hierachicalmodelH.Write()->mDensityLodDistanceScalar = value;
        SetModelDirty(entity);
    }

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    void SetModelDirty(ecs::EntityID entity)
    {
        auto* ISMSys = mRenderWorld->GetRenderSystem<InstancedStaticModelSystemR>();
        ISMSys->SetModelDirty(entity);
    }

protected:
    InstancedStaticModelChangeList<ecs::EntityID> mChangeList;
};

}   // namespace cross
