
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/LightProbeCache.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderFactory.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderContext.h"
#include <limits>

#if CROSSENGINE_EDITOR
#include "Resource/resourceasset.h"
#include "Resource/Texture/Texture.h"
#ifdef LIGHTMAP_BAKE_SYSTEM_ENABLE
#include "Editor/LightmapBaker/GPUBakingCore/PrecomputedVolumetricLightmap.h"
#include "Editor/LightmapBaker/TLBSDataProtocol/SerializeUtils.h"
#endif
#endif

namespace cross {

cross::LightProbeNode LightProbeNode::sZeroNode;

LightProbeCache::LightProbeCache()
    : mBoxMin(FLT_MAX, FLT_MAX, FLT_MAX)
    , mBoxMax(FLT_MIN, FLT_MIN, FLT_MIN)
{
    #if defined(DEBUG_OCTREE)
    TestOctreeClass::TestMain();
    #endif
}

LightProbeCache::~LightProbeCache()
{
    delete mOctreeForRendering;
}

bool LightProbeCache::AddLightProbe(LightProbeNode&& node)
{
    auto& pos = node.mPosition;
    const float& radius = node.mRadius;
    mBoxMin.x = std::min<float>(mBoxMin.x, pos.x - radius);
    mBoxMin.y = std::min<float>(mBoxMin.y, pos.y - radius);
    mBoxMin.z = std::min<float>(mBoxMin.z, pos.z - radius);

    mBoxMax.x = std::max<float>(mBoxMax.x, pos.x + radius);
    mBoxMax.y = std::max<float>(mBoxMax.y, pos.y + radius);
    mBoxMax.z = std::max<float>(mBoxMax.z, pos.z + radius);

    mLightProbeNodes.emplace_back(node);
    return true;
}

bool LightProbeCache::BuildOctree()
{
    auto center = (mBoxMin + mBoxMax) / 2;
    auto extent = (mBoxMax - mBoxMin) / 2;
    float extentMax = std::max<float>(std::max<float>(extent.x, extent.y), extent.z);
    mOctreeForRendering = new TLightVolumeOctree(Float4A(center.x, center.y, center.z, 0), extentMax + 1);

    for (const auto& element : mLightProbeNodes)
    {
        mOctreeForRendering->AddElement(element);
    }
    
    return true;
}

bool LightProbeCache::InterpolateIncidentRadiancePoint(const Float3A& worldPos, LightProbeNode& outNode)
{
    // panlele: need octree opt!!!
    //Float3A testPos = GPUBaking::Float3UE2CE(GPUBaking::float4(-384.f, 896.f, 85.333f, 1.0f));
    #if defined(DEBUG_OCTREE)
    std::vector<Float4> vDebugTestOctree;
    std::vector<Float4> vDebugTest;
    #endif

    float accumulatedWeight = 0;
    TBoxCenterAndExtent BoundingBox(Float4A(worldPos.x, worldPos.y, worldPos.z, 0));
    mOctreeForRendering->FindElementsWithBoundsTest(BoundingBox, [&](const LightProbeNode& volumeSample){
        const float distanceSquared = (volumeSample.mPosition - worldPos).LengthSquared();
        const float radiusSquared = volumeSample.mRadius * volumeSample.mRadius;
        if (distanceSquared < radiusSquared)
        {
            const float invRadiusSquared = 1.0f / radiusSquared;
            const float sampleWeight = (1.0f - distanceSquared * invRadiusSquared) * invRadiusSquared;
            accumulatedWeight += sampleWeight;
            LightProbeNode::MulAdd(sampleWeight, volumeSample, outNode, outNode);
            #if defined(DEBUG_OCTREE)
            vDebugTestOctree.push_back({volumeSample.mPosition.x, volumeSample.mPosition.y, volumeSample.mPosition.z, volumeSample.mRadius});
            #endif
        }
    });

    #if defined(DEBUG_OCTREE)
    {
        float accumulatedWeightTest = 0;
        LightProbeNode outNodeTest;
        for (auto i = 0; i < mLightProbeNodes.size(); i++)
        {
            const auto& volumeSample = mLightProbeNodes[i];
            const float distanceSquared = (volumeSample.mPosition - worldPos).LengthSquared();
            const float radiusSquared = volumeSample.mRadius * volumeSample.mRadius;
            if (distanceSquared < radiusSquared)
            {
                const float invRadiusSquared = 1.0f / radiusSquared;
                const float sampleWeight = (1.0f - distanceSquared * invRadiusSquared) * invRadiusSquared;
                accumulatedWeightTest += sampleWeight;
                LightProbeNode::MulAdd(sampleWeight, volumeSample, outNodeTest, outNodeTest);
                vDebugTest.push_back({volumeSample.mPosition.x, volumeSample.mPosition.y, volumeSample.mPosition.z, volumeSample.mRadius});
            }
        }
        TestOctreeClass::IsSameVector(vDebugTest, vDebugTestOctree);
    }
    #endif

    if (accumulatedWeight > 0)
    {
        LightProbeNode::MulAdd(1.0f / (accumulatedWeight), outNode, LightProbeNode::sZeroNode, outNode);
        outNode.SHMul(1.0f / MathUtils::MathPi);
    }
    //outNode.mDirectionalLightShadowing = outNode.mSHSamplePacked2.w;
    return true;
}

bool LightProbeCache::SerializeCacheData(std::string filename)
{
    SerializeNode root;
    root["mVersion"] = 1;
    root["mBoxMin"] = mBoxMin.Serialize();
    root["mBoxMax"] = mBoxMax.Serialize();
    root["LightProbeNodesSize"] = static_cast<int>(mLightProbeNodes.size());

    SerializeNode serializeNodes;
    for (auto i = 0; i < mLightProbeNodes.size(); ++i)
    {
        SerializeNode serializeNode;
        auto& node = mLightProbeNodes[i];
        serializeNode["mIndex"] = i;
        serializeNode["mPosition"] = node.mPosition.Serialize();
        serializeNode["mRadius"] = node.mRadius;
        serializeNode["mDirectionalLightShadowing"] = node.mDirectionalLightShadowing;
        SerializeNode shSample;
        shSample.PushBack(node.mSHSamplePacked0[0].Serialize());
        shSample.PushBack(node.mSHSamplePacked0[1].Serialize());
        shSample.PushBack(node.mSHSamplePacked0[2].Serialize());
        shSample.PushBack(node.mSHSamplePacked1[0].Serialize());
        shSample.PushBack(node.mSHSamplePacked1[1].Serialize());
        shSample.PushBack(node.mSHSamplePacked1[2].Serialize());
        shSample.PushBack(node.mSHSamplePacked2.Serialize());
        serializeNode["mSHSamplePacked"] = std::move(shSample);
        serializeNode["mLighting"] = node.mLighting.Serialize();
#if ENABLE_PRT_API
        serializeNode["mStaticLighting"] = node.mStaticLighting.Serialize();
        serializeNode["mTransferMatrix"] = node.mTransferMatrix.Serialize();
#endif
        serializeNode["mSkyBentNormal"] = node.mSkyBentNormal.Serialize();
        serializeNodes.PushBack(std::move(serializeNode));
    }
    root["LightProbeNodes"] = std::move(serializeNodes);

    auto* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    auto strData = root.FormatToJson();
    fileSystem->Save(filename, strData.data(), strData.size());
    return true;
}

float LightProbeCache::CalcWeightOfCacheVolume(const Float3A& worldPos)
{
    auto boxCenter = (mBoxMin + mBoxMax) / 2;
    if (worldPos.x >= mBoxMin.x && worldPos.y >= mBoxMin.y && worldPos.z >= mBoxMin.z && worldPos.x <= mBoxMax.x && worldPos.y <= mBoxMax.y && worldPos.z <= mBoxMax.z)
    {
        auto diff = worldPos - boxCenter;
        return diff.Length();
    }
    return -1;
}

bool LightProbeCache::DeserializeCacheData(std::string filename)
{
    auto* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
    auto file = fileSystem->Open(filename);
    std::string fileContent;
    fileContent.resize(file->GetSize());
    file->Read(fileContent.data(), fileContent.size());

    bool result = false;
    SerializeNode root = SerializeNode::ParseFromJson(fileContent, &result);
    Assert(result);

    mBoxMin.Deserialize(root["mBoxMin"]);
    mBoxMax.Deserialize(root["mBoxMax"]);

    auto serNodes = root["LightProbeNodes"];
    auto nodesSize = root["LightProbeNodesSize"].AsUInt32();
    for (UInt32 i = 0; i < nodesSize; i++)
    {
        auto serNode = serNodes[i];
        LightProbeNode node;
        node.mPosition.Deserialize(serNode["mPosition"]);
        node.mRadius = serNode["mRadius"].AsFloat();
        node.mDirectionalLightShadowing = serNode["mDirectionalLightShadowing"].AsFloat();
        if (serNode.HasMember("mSkyBentNormal"))
        {
            node.mSkyBentNormal.Deserialize(serNode["mSkyBentNormal"]);
        }
        auto shSample = serNode["mSHSamplePacked"];
        Assert(shSample.Size() == 7);
        node.mSHSamplePacked0[0].Deserialize(shSample[0]);
        node.mSHSamplePacked0[1].Deserialize(shSample[1]);
        node.mSHSamplePacked0[2].Deserialize(shSample[2]);
        node.mSHSamplePacked1[0].Deserialize(shSample[0 + 3]);
        node.mSHSamplePacked1[1].Deserialize(shSample[1 + 3]);
        node.mSHSamplePacked1[2].Deserialize(shSample[2 + 3]);
        node.mSHSamplePacked2.Deserialize(shSample[0 + 6]);
        if (serNode.HasMember("mLighting"))
        {
            node.mLighting.Deserialize(serNode["mLighting"]);
        }
#if ENABLE_PRT_API
        if (serNode.HasMember("mStaticLighting"))
        {
            node.mStaticLighting.Deserialize(serNode["mStaticLighting"]);
        }
        if (serNode.HasMember("mTransferMatrix"))
        {
            node.mTransferMatrix.Deserialize(serNode["mTransferMatrix"]);
        }
#endif
        AddLightProbe(std::move(node));
    }
    BuildOctree();
    return true;
}

cross::Float3 VolumetricLightmapData::Float3UE2CE(const cross::Float3& v)
{
    static cross::Float4x4A ueTransformInv(1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, 0, 0, 0, 1);

    auto ret = cross::Float4A(v.x, v.y, v.z, 1.0f) * ueTransformInv;
    return cross::Float3(ret.x, ret.y, ret.z);
}

cross::Float3 VolumetricLightmapData::Float3Abs(const cross::Float3& box)
{
    return {fabsf(box.x), fabsf(box.y), fabsf(box.z)};
}

SerializeNode VolumetricLightmapData::Serialize() const
{
    SerializeNode node = {"mEnable"_k = mEnable};
    if (mEnable)
    {
        node["mIndirectionTexFile"] = mIndirectionTexFile;
        node["mAmbientVector"] = mAmbientVector;
        node["mSHCoefficients"] = {mSHCoefficients[0], mSHCoefficients[1], mSHCoefficients[2], mSHCoefficients[3], mSHCoefficients[4], mSHCoefficients[5]};
        node["mSkyBentNormal"] = mSkyBentNormalTexFile;
        Float3 center, extent;
        mVolumeBoundsUE4.GetCenter(&center);
        mVolumeBoundsUE4.GetExtent(&extent);
        node["mVolumeCenter"] = center.Serialize();
        node["mVolumeExtent"] = extent.Serialize();
        node["mBrickSize"] = mBrickSize;
    }
    return node;
}

void VolumetricLightmapData::Deserialize(const DeserializeNode& json)
{
    mEnable = json["mEnable"].AsBoolean();
    if (mEnable)
    {
        mIndirectionTexFile = json["mIndirectionTexFile"].AsString();
        mAmbientVector = json["mAmbientVector"].AsString();
        mSHCoefficients.resize(json["mSHCoefficients"].Size());
        Assert(mSHCoefficients.size() == 6);
        for (auto i = 0; i < mSHCoefficients.size(); i++)
        {
            mSHCoefficients[i] = json["mSHCoefficients"][i].AsString();
        }
        mSkyBentNormalTexFile = json.HasMember("mSkyBentNormal") ? json["mSkyBentNormal"].AsString() : "";
        Float3 center, extent;
        center.Deserialize(json["mVolumeCenter"]);
        extent.Deserialize(json["mVolumeExtent"]);
        mBrickSize = json["mBrickSize"].AsInt32();

        SetVolumeBoundsUE4(BoundingBox(center, extent));
    }
}

#if CROSSENGINE_EDITOR && CROSSENGINE_WIN
struct VolumetricSample
{
    Float3 IndirectionTexturePositionOffset;
    ColorRGBAf AmbientColor;
    ColorRGBA32 SkyBentNormal;
};

struct VolumetricLightmapBrick
{
    Int3 IndirectionTexturePosition;
    SInt32 TreeDepth;
    std::vector<VolumetricSample> SampleVector;
};

UInt8* GetPixelData(resource::TextureResourceDataProvider* data, Int3 coord)
{
    if (coord.x < 0 || coord.y < 0 || coord.z < 0)
    {
        return nullptr;
    }
    UInt32 width, height, depth;
    data->GetImageSize(0, width, height, depth);
    auto pixelSize = GetPixelByteSize(data->GetTextureFormat());
    UInt8* pixelData = data->GetImageData(0) + ((coord.z * height + coord.y) * width + coord.x) * pixelSize;
    return pixelData;
}

bool IsSameBrick(const std::vector<VolumetricLightmapBrick>& bricks, const Int3& coordOrig) 
{
    for (const auto& brick : bricks)
    {
        if (brick.IndirectionTexturePosition.x == coordOrig.x
            && brick.IndirectionTexturePosition.y == coordOrig.y
            && brick.IndirectionTexturePosition.z == coordOrig.z)
        {
            return true;
        }
    }
    return false;
}

void VolumetricLightmapData::GetPositionAndSH(std::vector<Float3>& vOutPosition, std::vector<ColorRGBAf>& vOutAmbient, std::vector<Float4>& vOutSkyBentNormal) const
{
#ifdef LIGHTMAP_BAKE_SYSTEM_ENABLE
    Float3 volumeMin, volumeMax, volumeSize;
    mVolumeBoundsUE4.GetMinMax(&volumeMin, &volumeMax);
    volumeSize = volumeMax - volumeMin;
    Int3 IndirectionTextureDimensions(mIndirectionTex->GetWidth(), mIndirectionTex->GetHeight(), mIndirectionTex->GetDepth());
    GPUBaking::int3 i3IndirectionTextureDimensions(IndirectionTextureDimensions.x, IndirectionTextureDimensions.y, IndirectionTextureDimensions.z);

    float TargetDetailCellSize = volumeSize.x/i3IndirectionTextureDimensions.x/mBrickSize;

    ResourcePtr indirectTexRes = gResourceMgr.LoadRawResouceForEditorDebug(mIndirectionTexFile.c_str());
    resource::TextureResourceDataProvider* idxTexData = TYPE_CAST(resource::Texture*, indirectTexRes.get())->GetTextureData();

    ResourcePtr ambientTexRes = gResourceMgr.LoadRawResouceForEditorDebug(mAmbientVector.c_str());
    resource::TextureResourceDataProvider* ambientTexData = TYPE_CAST(resource::Texture*, ambientTexRes.get())->GetTextureData();
    UInt32 ambWidth, ambHeight, ambDepth;
    ambientTexData->GetImageSize(0, ambWidth, ambHeight, ambDepth);
    GPUBaking::int3 BrickDataDimensions(ambWidth, ambHeight, ambDepth);

    resource::TextureResourceDataProvider* skyBentNormalTexData = nullptr;
    if (mSkyBentNormalTexFile.size() > 0)
    {
        ResourcePtr skyBentNormalTexRes = gResourceMgr.LoadRawResouceForEditorDebug(mSkyBentNormalTexFile.c_str());
        skyBentNormalTexData = TYPE_CAST(resource::Texture*, skyBentNormalTexRes.get())->GetTextureData();
    }
    
    Assert(mBrickSize == 4);
    const SInt32 BrickSizeLog2 = GPUBaking::FloorLog2(mBrickSize);
    std::vector<std::vector<VolumetricLightmapBrick>> BricksByDepth; 
    BricksByDepth.resize(3);
    int MaxRefinementLevels = 3;

    for (auto z = 0; z < IndirectionTextureDimensions.z; z += 1)
    {
        for (auto y = 0; y < IndirectionTextureDimensions.y; y += 1)
        {
            for (auto x = 0; x < IndirectionTextureDimensions.x; x += 1)
            {
                //for each brick size step
                Int3 idxCoord(x, y, z);
                UInt8* IndirectionVoxelPtr = GetPixelData(idxTexData, idxCoord);
                Int3 BrickLayoutPosition(*IndirectionVoxelPtr, *(IndirectionVoxelPtr + 1), *(IndirectionVoxelPtr + 2));
                SInt32 NumBottomLevelBricks = *(IndirectionVoxelPtr + 3);

                SInt32 DetailCellsPerCurrentLevelBrick = NumBottomLevelBricks * mBrickSize;
                SInt32 TreeDepth = MaxRefinementLevels - GPUBaking::FloorLog2(DetailCellsPerCurrentLevelBrick) / BrickSizeLog2;

                Int3 IndirectionTexturePositionOrig(idxCoord.x / NumBottomLevelBricks * NumBottomLevelBricks, idxCoord.y / NumBottomLevelBricks * NumBottomLevelBricks, idxCoord.z / NumBottomLevelBricks * NumBottomLevelBricks);
                if (IsSameBrick(BricksByDepth[TreeDepth], IndirectionTexturePositionOrig))
                {
                    continue;
                }

                auto& bricks = BricksByDepth[TreeDepth];
                bricks.push_back(VolumetricLightmapBrick());
                auto& brick = bricks.back();

                brick.TreeDepth = TreeDepth;
                brick.IndirectionTexturePosition = IndirectionTexturePositionOrig;

                for (auto zi = 0; zi < mBrickSize; zi++)
                {
                    for (auto yi = 0; yi < mBrickSize; yi++)
                    {
                        for (auto xi = 0; xi < mBrickSize; xi++)
                        {
                            Float3 IndirectionTexturePositionOffset(1.0f * xi, 1.0f * yi, 1.0f * zi);
                            IndirectionTexturePositionOffset = IndirectionTexturePositionOffset / static_cast<float>(mBrickSize) * static_cast<float>(NumBottomLevelBricks);
                            VolumetricSample sampleInfo;
                            sampleInfo.IndirectionTexturePositionOffset = IndirectionTexturePositionOffset;

                            GPUBaking::int3 IndirectionBrickOffset;
                            SInt32 IndirectionBrickSize;
                            GPUBaking::float3 IndirectionDataSourceCoordinate(IndirectionTexturePositionOffset.x + IndirectionTexturePositionOrig.x,
                                                                              IndirectionTexturePositionOffset.y + IndirectionTexturePositionOrig.y,
                                                                              IndirectionTexturePositionOffset.z + IndirectionTexturePositionOrig.z);
                            GPUBaking::SampleIndirectionTexture(IndirectionDataSourceCoordinate, i3IndirectionTextureDimensions, idxTexData->GetImageData(0), IndirectionBrickOffset, IndirectionBrickSize);
                            GPUBaking::float3 BrickTextureCoordinate = GPUBaking::ComputeBrickTextureCoordinate(IndirectionDataSourceCoordinate, IndirectionBrickOffset, IndirectionBrickSize, mBrickSize);
                            GPUBaking::FFloat3Packed sampleAmbientColor = GPUBaking::NearestVolumeLookup<GPUBaking::FFloat3Packed>(BrickTextureCoordinate, BrickDataDimensions, reinterpret_cast<const GPUBaking::FFloat3Packed*>(ambientTexData->GetImageData(0)));

                            SInt32 PaddedBrickSize = mBrickSize + 1;
                            GPUBaking::float3 BrickDataPosition = 
                                GPUBaking::float3(static_cast<float>(BrickLayoutPosition.x), static_cast<float>(BrickLayoutPosition.y), static_cast<float>(BrickLayoutPosition.z)) * static_cast<float>(PaddedBrickSize) 
                                + GPUBaking::float3(static_cast<float>(xi), static_cast<float>(yi), static_cast<float>(zi));
                            GPUBaking::FFloat3Packed ambientColor =
                                GPUBaking::NearestVolumeLookup<GPUBaking::FFloat3Packed>(BrickDataPosition, BrickDataDimensions, reinterpret_cast<const GPUBaking::FFloat3Packed*>(ambientTexData->GetImageData(0)));
                            ColorRGBA32 skyBentNormal;
                            if (skyBentNormalTexData)
                            {
                                skyBentNormal = GPUBaking::NearestVolumeLookup<ColorRGBA32>(BrickDataPosition, BrickDataDimensions, reinterpret_cast<const ColorRGBA32*>(skyBentNormalTexData->GetImageData(0)));
                            }

                            GPUBaking::FLinearColor ambientLinearColor = ambientColor.ToLinearColor();
                            Assert(ambientLinearColor == ambientColor.ToLinearColor());
                            sampleInfo.AmbientColor.r = ambientLinearColor.R;
                            sampleInfo.AmbientColor.g = ambientLinearColor.G;
                            sampleInfo.AmbientColor.b = ambientLinearColor.B;
                            sampleInfo.AmbientColor.a = ambientLinearColor.A;
                            sampleInfo.SkyBentNormal = skyBentNormal;
                            brick.SampleVector.emplace_back(std::move(sampleInfo));
                        }
                    }
                }
            }
        }
    }

    std::set<std::tuple<float, float, float>> posSets;
    for (const auto& bricks : BricksByDepth) 
    {
        for (const VolumetricLightmapBrick& brick : bricks)
        {
            float brickDistance = (TargetDetailCellSize * mBrickSize);
            Float3 positionStart(static_cast<float>(brick.IndirectionTexturePosition.x), static_cast<float>(brick.IndirectionTexturePosition.y), static_cast<float>(brick.IndirectionTexturePosition.z));
            positionStart *= brickDistance;
            positionStart += volumeMin;
            for (const VolumetricSample& sample : brick.SampleVector)
            {
                Float3 position = sample.IndirectionTexturePositionOffset* brickDistance + positionStart;
                auto posVec = std::make_tuple(position.x, position.y, position.z);
                if (posSets.find(posVec) == posSets.end())
                {
                    posSets.insert(posVec);
                    vOutPosition.push_back(position);
                    vOutAmbient.push_back(sample.AmbientColor);
                    vOutSkyBentNormal.push_back(Float4(sample.SkyBentNormal.r/255.f, sample.SkyBentNormal.g/255.f, sample.SkyBentNormal.b/255.f, sample.SkyBentNormal.a/255.f));
                }
            }
        }
    }

    for (auto i = 0; i < vOutPosition.size(); i++)
    {
        vOutPosition[i] = Float3UE2CE(vOutPosition[i]);
    }
    for (auto i = 0; i < vOutSkyBentNormal.size(); i++)
    {
        Float3 bentNormal(vOutSkyBentNormal[i].x, vOutSkyBentNormal[i].y, vOutSkyBentNormal[i].z);
        bentNormal = bentNormal * 2 - Float3(1, 1, 1);
        bentNormal = Float3UE2CE(bentNormal);
        vOutSkyBentNormal[i].x = bentNormal.x;
        vOutSkyBentNormal[i].y = bentNormal.y;
        vOutSkyBentNormal[i].z = bentNormal.z;
    }
    return;
#endif
}
#endif

}   // namespace cross
