
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "RenderEngine/LightProbeVolumeSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderFactory.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/PRT/Relighting.h"
#include "RenderEngine/SkyLightSystemR.h"
#include "RenderEngine/SkyAtmosphereSystemR.h"

namespace cross {
ecs::ComponentDesc* LightProbeVolumeComponentR::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetRenderComponentDesc<cross::LightProbeVolumeComponentR>(false);
}
LightProbeVolumeSystemR::LightProbeVolumeSystemR() {}

LightProbeVolumeSystemR::~LightProbeVolumeSystemR() {}

void LightProbeVolumeSystemR::OnEndFrame(FrameParam* frameParam)
{
    mVolumeDirty = false;
}

LightProbeVolumeSystemR* LightProbeVolumeSystemR::CreateInstance()
{
    LightProbeVolumeSystemR* system = new LightProbeVolumeSystemR();
    return system;
}

void LightProbeVolumeSystemR::Release()
{
    delete this;
}

bool LightProbeVolumeSystemR::QueryLightProbeNode(const cross::Float3A& worldPos, LightProbeNode& outBlendNode)
{
    auto entities = mRenderWorld->Query<LightProbeVolumeComponentR>();
    float selectedWeight = -1;
    LightProbeVolumeComponentH selectedComp;
    for (auto lpVolumeComp : entities)
    {
        if (!lpVolumeComp.Read()->mCacheData || !lpVolumeComp.Read()->mEnable)
        {
            continue;
        }
        if (lpVolumeComp.Read()->mUseVolumetricLightMap)
        {
            continue;
        }
        float weight = lpVolumeComp.Read()->mCacheData->CalcWeightOfCacheVolume(worldPos);
        if (selectedWeight < 0)
        {
            selectedWeight = weight;
            selectedComp = lpVolumeComp;
        }
        else
        {
            if (weight >= 0 && weight < selectedWeight)
            {
                selectedWeight = weight;
                selectedComp = lpVolumeComp;
            }
        }
    }
    if (selectedComp.IsValid())
    {
        selectedComp.Read()->mCacheData->InterpolateIncidentRadiancePoint(worldPos, outBlendNode);
        return true;
    }
    return false;
}

const VolumetricLightmapData* LightProbeVolumeSystemR::QueryVLMData(const cross::Float3A& worldPos, float& VLMReflectionProbeIntensity, float& VLMReflectionProbeAOIntensity)
{
    auto entities = mRenderWorld->Query<LightProbeVolumeComponentR>();
    for (auto lpVolumeComp : entities)
    {
        auto lpVolumeCompR = lpVolumeComp.Read();
        if (!lpVolumeCompR->mEnable)
        {
            continue;
        }
        if (!lpVolumeCompR->mUseVolumetricLightMap)
        {
            continue;
        }
        if (lpVolumeCompR->mVLMData && lpVolumeCompR->mVLMData->mEnable && lpVolumeCompR->mVLMData->InVolumeBound(worldPos)
            && lpVolumeCompR->mVLMData->mAmbientVectorTex)
        {
            VLMReflectionProbeIntensity = lpVolumeCompR->mVLMReflectionProbeIntensity;
            VLMReflectionProbeAOIntensity = lpVolumeCompR->mVLMReflectionProbeAOIntensity;
            return lpVolumeCompR->mVLMData.get();
        }
    }
    return nullptr;
}

void LightProbeVolumeSystemR::SetLightProbeVolumeCacheFile(ecs::EntityID entity, const std::string& filename)
{
    auto comp = mRenderWorld->GetComponent<LightProbeVolumeComponentR>(entity).Write();
    if (comp->mCacheFile != filename)
    {
        comp->mCacheData = std::make_unique<LightProbeCache>();
        comp->mCacheData->DeserializeCacheData(filename);
        comp->mCacheFile = filename;
    }
    mVolumeDirty = true;
}

bool LightProbeVolumeSystemR::SetVolumetricLightMap(ecs::EntityID entityID, const TexturePtr& indirectionTex, const TexturePtr& ambientVector, 
    const std::vector<TexturePtr>& shCoefficients, const TexturePtr& skyBentNormal, const BoundingBox& volumeBounds, int brickSize)
{
    auto comp = mRenderWorld->GetComponent<LightProbeVolumeComponentR>(entityID).Write();
    comp->mVLMData = std::make_unique<VolumetricLightmapData>();
    comp->mVLMData->mIndirectionTex = indirectionTex;
    comp->mVLMData->mAmbientVectorTex = ambientVector;
    comp->mVLMData->mSHCoefficientsTex = shCoefficients;
    comp->mVLMData->mSkyBentNormalTex = skyBentNormal;
    comp->mVLMData->SetVolumeBoundsUE4(volumeBounds);
    comp->mVLMData->mBrickSize = brickSize;
    if (indirectionTex)
    {
        comp->mVLMData->mEnable = true;
    }
    else
    {
        comp->mVLMData->mEnable = false;
    }
    mVolumeDirty = true;
    return true;
}

void LightProbeVolumeSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    auto* lightSystem = mRenderWorld->GetRenderSystem<LightSystemR>();
    auto* transformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
    auto* skyLightSys = mRenderWorld->GetRenderSystem<SkyLightSystemR>();
    auto* skySys = mRenderWorld->GetRenderSystem<SkyAtmosphereSystemR>();

    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [this, lightSystem, transformSystem, skyLightSys, skySys]() { 
        SCOPED_CPU_TIMING(GroupRendering, "LightProbeVolumeRUpdate");
        SHUtils::FSHVectorRGB3 envSH;

        auto lights = mRenderWorld->Query<LightComponentR, TransformComponentR>();
        for (auto [lightComp, transformComp] : lights)
        {
            auto lightCompR = lightComp.Read();
            auto transformCompR = transformComp.Read();
            if (!lightSystem->IsLightEnableForRuntime(lightCompR))
            {
                continue;
            }
            Float3 color = lightSystem->GetLightColor(lightCompR) * lightSystem->GetLightIntensity(lightCompR);
            auto lightType = lightSystem->GetLightType(lightCompR);
            float prtIntensity = lightSystem->GetLightPrtIntensity(lightCompR);
            if (lightType == LightType::Directional)
            {
                Float4 lightDir = transformSystem->GetWorldRotation(transformCompR).Float4Rotate(Float4(0, 0, 1, 0));
                Float3 tranmittance = lightSystem->GetLightTransmittance(lightCompR);
                color *= tranmittance;
                SHUtils::SHEvalDirectionalLight(3, Float3(lightDir.x, -lightDir.z, lightDir.y) * -1.f, color * prtIntensity, &envSH.R.V[0], &envSH.G.V[0], &envSH.B.V[0]);
            }
        }

        std::array<Float4, 3> skySH;
        memset(skySH.data(), 0, sizeof(skySH));
        skyLightSys->AddSkyDiffuseSH(skySH);
        for (auto i = 0; i < 4; i++)
        {
            envSH.R.V[i] += skySH[0][i];
            envSH.G.V[i] += skySH[1][i];
            envSH.B.V[i] += skySH[2][i];
        }

        std::vector<LightProbeCache*> ilcCache;
        auto entities = mRenderWorld->Query<LightProbeVolumeComponentR>();
        for (auto lpVolumeComp : entities)
        {
            auto lpVolumeCompWriter = lpVolumeComp.Write();
            if (lpVolumeCompWriter->mCacheData && lpVolumeCompWriter->mEnable)
            {
                ilcCache.push_back(lpVolumeCompWriter->mCacheData.get());
                ilcCache.back()->bNeedsRelighting = true;
            }
        }

        if (ilcCache.size())
        {
            IndirectLightingCacheRelightingTask ilcRelightingTask(100000, ilcCache, envSH, {}, {});
            ilcRelightingTask.Run();
            SetVolumeDirty(true);
        }
    });
}

}   // namespace cross
