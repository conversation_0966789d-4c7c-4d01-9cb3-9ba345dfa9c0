#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderNode/ModelRenderNode.h"
#include "Resource/MeshAssetDataResource.h"
#include "RenderEngine/MeshBlendShapeUtil.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDCulling.h"
#include "memory/allocator/range_allocator.hpp"

namespace cross {

struct ComponentDesc;
class MaterialR;
class MeshR;

enum class SkinLevel
{
    All = 0,
    CurLod,
};

enum class SkinningStatus
{
    Invalid,
    NotSkinned,
    Skinned
};

template<typename T>
struct ModelChangeList
{
public:
    ModelChangeList()
        : mResource(16 * 1024 * 1024, 16 * 1024 * 1024)
    {
        mQueue = std::pmr::set<T>(&mResource);
    }
    void EmplaceChangeData(const T& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    void EmplaceChangeData(T&& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    void EmplaceChangeData(T& ID)
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        mQueue.insert(ID);
    }
    bool HasChangeData(const T& ID) {
        std::shared_lock<std::shared_mutex> lock(mMtx);
        return mQueue.count(ID) > 0;
    }
    T PopFirstChangeData()
    {
        std::unique_lock<std::shared_mutex> lock(mMtx);
        T ret = *mQueue.begin();
        mQueue.erase(mQueue.begin());
        return ret;
    }
    UInt32 GetCount() const
    {
        std::shared_lock<std::shared_mutex> lock(mMtx);
        return static_cast<UInt32>(mQueue.size());
    }
    auto& GetContainer() 
    { 
        return mQueue;
    }

private:
    std::pmr::set<T> mQueue;
    mutable std::shared_mutex mMtx;
    gbf::allocator::RangeAllocatorResource mResource;
};

struct ModelComponentR : ecs::IComponent
{
    //struct SubModelProperty
    //{
    //    MaterialR* mMaterial{nullptr};
    //    std::vector<MaterialR*> mLODMaterials;   // 0-LOD1, 1-LOD2...
    //    UInt32 mRenderEffect{0};
    //    bool mVisible{true};

    //    // Blend shape related for this submodel
    //    bool mHasBlendShape{false};
    //    bool mBlendShapeDirty{false};
    //    std::vector<ChannelWeightData> mLODChannelWeights;
    //};

    struct IndividualModel
    {
        struct SingleLODModelProperty
        {
            struct SubModelProperty
            {
                MaterialR* mMaterial{nullptr};
                UInt32 mRenderEffect{0};
                bool mVisible{true};

                // For blend shape
                bool mHasBlendShape{false};
                bool mBlendShapeDirty{false};
                ChannelWeightData mChannelWeight{};
            };

            std::vector<SubModelProperty> mSubModelProperties{};
        };

        MeshAssetDataResourcePtr mAsset{nullptr};
        //std::vector<SubModelProperty> mSubModelProperties;

        std::vector<SingleLODModelProperty> mLODModelProperties{1}; // default has LOD 0
        
        // SharedGeometry from MeshAssetData, used for static mesh and GPUSkinned skeletal mesh without blend shape
        MeshR* mSharedRenderMesh{nullptr};
        
        // Unique for each IndividualModel, used for CPUSkinned skeletal mesh and mesh with blend shape
        bool mRenderMeshDirty{true};
        std::shared_ptr<MeshR> mPrivateRenderMesh{nullptr};

        // For additive vertex semantics
        UInt32 mAdditiveVertexSemantics = 0;

        // For skeletal mesh pose
        FrameVector<SIMDMatrix>* mCompletePose{nullptr};
        bool mVisible{true};
        bool mReceiveDecals{true};
        bool mIsStaticBuilt{false};

        // For blend shape related for this model
        bool mHasBlendShape{false};
        bool mBlendShapeDirty{false};
        ModelBlendShapeDataPtr mBlendShapeVertexPNTData{nullptr};
        SkinLevel mSKinLevel{SkinLevel::All};
        SkinningStatus mSkinningStatus = SkinningStatus::Invalid;

        bool mStellarMeshEnable{false};
    };

    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

    std::shared_ptr<ModelRenderNode> mRenderNode;

    std::unique_ptr<EntityDistanceCulling> mDistanceCulling = std::make_unique<EntityDistanceCulling>();

    bool mAsyncResourceAssemble = false;

protected:
    MeshBatchInfo mBatchInfo{};

    IndividualModel mMainModel{};

    //[[deprecated]]
    std::vector<IndividualModel> mChangeableModels{};
    bool mIsStatic{};

    friend class ModelSystemR;
    friend class RayTracingScene;
};

using PrimaryModelCompH     = THandle<ModelComponentR::IndividualModel>;
//using SecondaryModelCompH   = THandle<ModelComponentR::SubModelProperty>;

//////////////////////////////////////////////////////////////////////////
// RenderMeshSystem2
using RenderModelComponentHandle = ecs::ComponentHandle<ModelComponentR>;
using RenderModelComponentReader = ecs::ScopedComponentRead<ModelComponentR>;
using RenderModelComponentWriter = ecs::ScopedComponentWrite<ModelComponentR>;
struct RenderMesh;
struct ModelToBuild
{
public:
    ecs::EntityID mEntity;
    const ModelComponentR::IndividualModel* mModel;
};
class ModelSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)
public:
    RENDER_ENGINE_API static ModelSystemR* CreateInstance();

    virtual void Release() override;
public:
    const MeshAssetData* GetMeshAssetData(const RenderModelComponentReader& reader, UInt32 modelIndex) const;

    const MeshAssetDataResourcePtr& GetModelAsset(const RenderModelComponentReader& reader, UInt32 modelIndex) const
    {
        return GetModel(reader, modelIndex).mAsset;
    }
    bool GetRenderMeshDirty(const RenderModelComponentReader& reader, UInt32 modelIndex) const
    {
        return GetModel(reader, modelIndex).mRenderMeshDirty;
    }
    SkinLevel GetModelSkinLevel(const RenderModelComponentReader& reader, UInt32 modelIndex) const
    {
        return GetModel(reader, modelIndex).mSKinLevel;
    }

    bool IsAllSkinningTasksFinished(const RenderModelComponentReader& reader) const
    {
        auto modelCount = GetModelCount(reader);
        for (UInt32 index = 0; index < modelCount; index++)
        {
            if (GetModel(reader, index).mSkinningStatus == SkinningStatus::Invalid)
                continue;
            if (GetModel(reader, index).mSkinningStatus == SkinningStatus::NotSkinned)
                return false;
        }
        return true;
    }

    bool HasModelBeenSkinned(const RenderModelComponentReader& reader, UInt32 modelIndex) const
    {
        Assert(GetModel(reader, modelIndex).mSkinningStatus != SkinningStatus::Invalid);
        return GetModel(reader, modelIndex).mSkinningStatus == SkinningStatus::Skinned;
    }
    
    BoundingBox GetSubModelBoundingBox(const RenderModelComponentReader& reader, UInt32 subModelIndex, UInt32 lodIndex = 0) const 
    { 
        return reader->mRenderNode->GetSubModelBoundingBox(subModelIndex, lodIndex); 
    }

    SInt32 GetSubModelID(const RenderModelComponentReader& reader, UInt32 modelIndex) const 
    { 
        return reader->mRenderNode->GetSubModelObjectGUID(modelIndex); 
    } 
    
    MeshAssetDataResourcePtr& GetModelAsset(const RenderModelComponentWriter& writer, UInt32 modelIndex)
    {
        return GetModel(writer, modelIndex).mAsset;
    }
    void SetRenderMeshDirty(const RenderModelComponentWriter& writer, UInt32 modelIndex, bool dirty)
    {
        GetModel(writer, modelIndex).mRenderMeshDirty = dirty;
        GetModel(writer, modelIndex).mSkinningStatus = dirty ? SkinningStatus::NotSkinned : GetModel(writer, modelIndex).mSkinningStatus;
    }
    void SetModelSkinLevel(const RenderModelComponentWriter& writer, UInt32 modelIndex, SkinLevel skinlevel)
    {
        GetModel(writer, modelIndex).mSKinLevel = skinlevel;
    }

    void SetModelSkinned(const RenderModelComponentWriter& writer, UInt32 modelIndex)
    {
        Assert(GetModel(writer, modelIndex).mSkinningStatus != SkinningStatus::Invalid);
        GetModel(writer, modelIndex).mSkinningStatus = SkinningStatus::Skinned;
    }

    std::shared_ptr<MeshR> GetModelPrivateRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex);
    MeshR* GetModelSharedRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex) const;
    void SetModelSharedRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex, MeshR* renderMesh);
    void ClearModelPrivateRenderMesh(const RenderModelComponentWriter& writer, UInt32 modelIndex);

    void SetModelStaticBuilt(const RenderModelComponentWriter& writer, bool isBuilt, UInt32 modelIndex)
    {
        GetModel(writer, modelIndex).mIsStaticBuilt = isBuilt;
    }

    bool IsModelStaticBuilt(const RenderModelComponentReader& reader, UInt32 modelIndex) const
    {
        return GetModel(reader, modelIndex).mIsStaticBuilt;
    }

    UInt32 GetModelCount(const RenderModelComponentReader& reader) const
    {
        return (UInt32)reader->mChangeableModels.size() + 1;
    }

    UInt32 GetSubModelCount(const RenderModelComponentReader& modelH, UInt32 modelIndex) const
    {
        return (UInt32)GetModel(modelH, modelIndex).mLODModelProperties[0].mSubModelProperties.size();
    }

    RENDER_ENGINE_API void SetModelAsset(ecs::EntityID entity, MeshAssetDataResourcePtr asset, UInt32 modelIndex);

    [[deprecated("deprecated")]]
    RENDER_ENGINE_API void RebuildModelModelAsset(ecs::EntityID entity);

    RENDER_ENGINE_API void SetModelMaterial(ecs::EntityID entity, MaterialR* material, SInt32 subModelIndex, UInt32 modelIndex);

    RENDER_ENGINE_API void SetModelLodMaterial(ecs::EntityID entity, MaterialR* material, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex);

    MaterialR* GetModelMaterial(const RenderModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex);

    MaterialR* GetModelLodMaterial(const RenderModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex);

    RENDER_ENGINE_API void SetModelEnityDistanceCulling(ecs::EntityID entity, const EntityDistanceCulling& entityCulling);
    RENDER_ENGINE_API EntityDistanceCulling GetModelEnityDistanceCulling(ecs::EntityID entity);


    inline const MeshBatchInfo& GetBatchInfo(const RenderModelComponentReader& reader) const
    {
        return reader->mBatchInfo;
    }

    RENDER_ENGINE_API void SetBatchInfo(ecs::EntityID entity, MeshBatchInfo batchInfo);

    RENDER_ENGINE_API void SetModelVisibility(ecs::EntityID entity, bool isVisible, UInt32 modelIndex);
    bool GetModelVisibility(ecs::EntityID entity, UInt32 modelIndex);

    RENDER_ENGINE_API void SetModelReceiveDecals(ecs::EntityID entity, bool value, UInt32 modelIndex);

    RENDER_ENGINE_API void SetModelSubVisibility(ecs::EntityID entity, bool isVisible, SInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex);

    RENDER_ENGINE_API void SetModelDirty(ecs::EntityID entity, bool isRemain = false);

    RENDER_ENGINE_API void SetModelSkinLevelByEntityID(ecs::EntityID entity, UInt32 modelIndex, SkinLevel skinlevel);

    RENDER_ENGINE_API void SetIsStatic(ecs::EntityID entity, bool value);

    RENDER_ENGINE_API void RemoveModel(ecs::EntityID entity, UInt32 modelIndex);

    // Method for mesh builder
    RenderModelComponentHandle GetModelHandle(ecs::EntityID entity);

    UInt8 GetModelLODIndex(const RenderModelComponentReader& modelH, UInt32 modelIndex) const;

public:
    // For skinned mesh
    bool IsSkeletalModel(const RenderModelComponentReader& modelH) const;
    bool IsSkeletalModel(const RenderModelComponentReader& reader, UInt32 modelIndex) const;

    RENDER_ENGINE_API void SetSkeltModelPose(ecs::EntityID entity, UInt32 modelIndex, FrameVector<SIMDMatrix>* modelPosePtr);
    FrameVector<SIMDMatrix>* GetSkeltModelPose(ecs::EntityID entity, UInt32 modelIndex) const;

    void SetRenderGeometryAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic);
    bool IsRenderGeometryAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic);
    void RemoveRenderGeomertyAdditiveVertexChannel(ecs::EntityID entity, UInt32 modelIndex, VertexSemantic inSematic);

public:
    // For model blend shape
    bool IsSubModelHasBlendShape(ecs::EntityID entity, UInt32 modelIndex, UInt32 subModelIndex) const;
    bool GetSubModelBlendShapeDirty(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const;
    void SetSubModelBlendShapeDirty(const RenderModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex, bool dirty);

    RENDER_ENGINE_API bool SetSubModelBlendShapeChannelWeight(ecs::EntityID entity, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float weight, UInt32 lodIndex = 0);
    bool GetSubModelBlendShapeChannelWeight(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float& outWeight, UInt32 lodIndex = 0) const;

    bool IsModelHasBlendShape(const RenderModelComponentReader& modelH, UInt32 modelIndex) const;
    bool GetModelBlendShapeDirty(const RenderModelComponentReader& modelH, UInt32 modelIndex) const;
    void SetModelBlendShapeDirty(const RenderModelComponentWriter& modelH, UInt32 modelIndex, bool dirty);

    std::vector<std::reference_wrapper<const ChannelWeightData>> GetSubModelBlendShapeLODChannelWeights(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const;
    const ChannelWeightData& GetSubModelBlendShapeChannelWeights(const RenderModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex = 0) const;
    BlendShapeVertexData* GetModelBlendShapeVertexData(const RenderModelComponentWriter& modelH, UInt32 modelIndex);
    const BlendShapeVertexData* GetModelBlendShapeVertexData(const RenderModelComponentReader& modelH, UInt32 modelIndex) const;
    inline const ModelComponentR::IndividualModel& GetModel(const RenderModelComponentReader& reader, UInt32 index) const
    {
        if (index == 0)
            return reader->mMainModel;
        Assert(index <= reader->mChangeableModels.size());
        return reader->mChangeableModels[index - 1];
    }

    inline ModelComponentR::IndividualModel& GetModel(const RenderModelComponentWriter& writer, UInt32 index) const
    {
        if (index == 0)
            return writer->mMainModel;
        Assert(index <= writer->mChangeableModels.size());
        return writer->mChangeableModels[index - 1];
    }

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    virtual void OnBeginFrame(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    RENDER_ENGINE_API virtual void OnPendingToDestroy() override;

    MeshR* GetRenderMeshInternal(const ModelComponentR::IndividualModel& model);

    friend class RayTracingScene;

private:
    std::shared_ptr<ModelRenderNode> CreateModelRenderNode(ecs::EntityID entityID, ecs::ComponentBitMask componentBitMask);

protected:
    std::vector<const resource::MeshAssetLODSetting*> tLODSettings;
    ModelChangeList<ecs::EntityID> mChangeList;
    ModelChangeList<ecs::EntityID> mRemainChangeList;
};

}   // namespace cross
