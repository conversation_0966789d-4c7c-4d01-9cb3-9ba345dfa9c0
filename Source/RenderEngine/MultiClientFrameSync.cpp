#include "EnginePrefix.h"
#include "MultiClientFrameSync.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "CrossBase/Threading/PresentThread.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"

#if CROSSENGINE_WIN
#include <dxgi.h>
#include <atlbase.h>
#endif

namespace
{
    cross::MultiClientFrameSync* gMultiClientFrameSync = nullptr;
}

void cross::MultiClientFrameSync::Init(MultiClientFrameSync* inst)
{
    gMultiClientFrameSync = inst;
}

cross::MultiClientFrameSync* cross::MultiClientFrameSync::Inst()
{
    return gMultiClientFrameSync;
}

cross::MultiClientFrameSync::MultiClientFrameSync(SInt64 presentDelay)
    : mPresentDelay{ presentDelay }
{
#if CROSSENGINE_WIN
    CComPtr<IDXGIFactory> factory;
    if (FAILED(CreateDXGIFactory(__uuidof(IDXGIFactory), reinterpret_cast<void**>(&factory))))
    {
        LOG_ERROR("Fail to create dxgi factory");
        return;
    }

    std::vector<CComPtr<IDXGIAdapter>> adapters;

    CComPtr<IDXGIAdapter> adapter;
    for (UINT i = 0; factory->EnumAdapters(i, &adapter) != DXGI_ERROR_NOT_FOUND; ++i)
    {
        adapters.emplace_back(std::move(adapter));
    }

    if (adapters.empty())
    {
        LOG_ERROR("Fail to find GPU");
        return;
    }

    std::sort(adapters.begin(), adapters.end(), [](auto& adp1, auto& adp2) 
    {
        DXGI_ADAPTER_DESC desc1;
        adp1->GetDesc(&desc1);
        DXGI_ADAPTER_DESC desc2;
        adp2->GetDesc(&desc2);
        return desc1.DedicatedVideoMemory > desc2.DedicatedVideoMemory;
    });

    adapter = std::move(adapters.front());
    CComPtr<IDXGIOutput> output;

    auto monIdx = EngineGlobal::GetSettingMgr()->GetMonitorIndex();
    auto nMon = GetSystemMetrics(SM_CMONITORS);

    std::vector<MONITORINFOEX> monInfos;
    monInfos.reserve(nMon);
    auto MonitorEnumProc = [](__in  HMONITOR hMonitor, __in  HDC hdcMonitor, __in  LPRECT lprcMonitor, __in  LPARAM dwData)->BOOL
    {
        auto mis = reinterpret_cast<std::vector<MONITORINFOEX>*>(dwData);
        MONITORINFOEX mi{ sizeof(MONITORINFOEX) };
        GetMonitorInfo(hMonitor, &mi);
        mis->emplace_back(mi);
        return TRUE;
    };
    EnumDisplayMonitors(NULL, NULL, MonitorEnumProc, reinterpret_cast<LPARAM>(&monInfos));

    if (nMon > 1 && monIdx != -1)
    {
        monIdx = std::min(monIdx, nMon);
    }
    else
    {
        auto ret = std::find_if(monInfos.begin(), monInfos.end(), [](auto& mi) -> bool { return mi.dwFlags & MONITORINFOF_PRIMARY; });
        AssertMsg(ret != monInfos.end(), "Can't find primary monitor");
        monIdx = static_cast<SInt32>(std::distance(monInfos.begin(), ret));
    }

    if (FAILED(adapter->EnumOutputs(monIdx, &output)))
    {
        DXGI_ADAPTER_DESC desc;
        adapter->GetDesc(&desc);
        std::string descStr;
        if (auto len = WideCharToMultiByte(CP_ACP, 0, desc.Description, -1, nullptr, 0, nullptr, nullptr); len != 0)
        {
            descStr.resize(len);
            WideCharToMultiByte(CP_ACP, 0, desc.Description, -1, descStr.data(), len, nullptr, nullptr);
        }

        LOG_ERROR("Fail to find monitor with index: {} on GPU: {}", monIdx, descStr);
        return;
    }

    mWaitForVBlank = [output = std::move(output)]{ output->WaitForVBlank(); };
#else
    mWaitForVBlank = [] {};
#endif

    mWaitForVBlank();
    mLastVSyncTime = Clock::now();
}

UInt64 cross::MultiClientFrameSync::BeginFrame()
{
    auto curVSync = GetCurrentVSyncID();

    DispatchRenderingCommandWithToken([=, curVSync = curVSync]
        {
            threading::DispatchPresentCommand([=, curVSync = curVSync]
                {
                    mStartVSyncID = curVSync;
                });
        });

    return curVSync.count();
}

void cross::MultiClientFrameSync::Synchronize()
{
    auto curVSyncID = GetCurrentVSyncID();
    auto frameTime = (curVSyncID - mStartVSyncID).count();

    for (; frameTime < mPresentDelay; ++frameTime)
    {
        mWaitForVBlank();
        mLastVSyncTime = Clock::now();
        mLastVSyncID = curVSyncID + VSync{ 1 };
    }
}

cross::MultiClientFrameSync::VSync cross::MultiClientFrameSync::GetCurrentVSyncID()
{
    auto now = Clock::now();
    std::lock_guard guard{ mMutex };
    auto deltaVSync = std::chrono::duration_cast<VSync>(now - mLastVSyncTime);
    return mLastVSyncID + deltaVSync;
}
