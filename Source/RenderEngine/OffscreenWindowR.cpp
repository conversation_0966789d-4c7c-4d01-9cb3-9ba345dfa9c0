#include "EnginePrefix.h"
#include "OffscreenWindowR.h"

cross::OffscreenWindowR::OffscreenWindowR(RenderTextureR* renderTexture)
    : mRenderTexture{ renderTexture }
{
}

std::tuple<UInt32, UInt32> cross::OffscreenWindowR::GetSize()
{
    auto& info = mRenderTexture->GetInfo();
    return { info.Width, info.Height, };
}

bool cross::OffscreenWindowR::Acquire(NGIFence* toSingalFence)
{
    return true;
}

cross::REDTexture* cross::OffscreenWindowR::PrepareBackbuffer(NGICommandList* cmdList)
{
    return mRenderTexture->GetREDTexture();
}

bool cross::OffscreenWindowR::Present()
{
    return true;
}

void cross::OffscreenWindowR::Resize(UInt32 width, UInt32 height, NGIScreenMode screenMode)
{
    AssertMsg(false, "Can't Resize Offscreen Window");
}