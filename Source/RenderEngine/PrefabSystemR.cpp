#include "EnginePrefix.h"
#include "PrefabSystemR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderPipeline/PrefabProxyRenderPipeline.h"

namespace cross
{
    void PrefabSystemR::CapturePrefabProxy(ecs::EntityID entityID, RenderTextureR* renderTexture, class PrefabProxyRenderPipeline* pipeline)
    {
        auto targetView = renderTexture->GetREDTextureView();
        auto cameraSystem = mRenderWorld->GetRenderSystem<CameraSystemR>();
        auto cameraComponent = mRenderWorld->GetComponent<CameraComponentR>(entityID);
        auto renderCamera = cameraSystem->GetRenderCamera(cameraComponent.Read());
        auto workingPipeline = pipeline->GetWorkingRenderPipeline();
        workingPipeline->SetCamera(entityID);
        workingPipeline->SetRenderCamera(renderCamera);
        workingPipeline->SetTargetView(targetView);
    }
}
