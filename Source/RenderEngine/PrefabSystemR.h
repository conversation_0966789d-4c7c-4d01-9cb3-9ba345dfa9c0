#pragma once

#include "ECS/Develop/Framework.h"
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "RenderEngine/Texture/RenderTextureR.h"


namespace cross
{
    class PrefabSystemR : public RenderSystemBase
    {
        CEMetaInternal(Reflect);
    public:
        RENDER_ENGINE_API static PrefabSystemR* CreateInstance()
        {
            return new PrefabSystemR();
        }

        RENDER_ENGINE_API virtual void Release() override
        {
            delete this;
        }

        RENDER_ENGINE_API virtual void OnFirstUpdate(FrameParam* frameParam) override {}

        RENDER_ENGINE_API virtual void OnBeginFrame(FrameParam* frameParam) override {}

        RENDER_ENGINE_API virtual void OnEndFrame(FrameParam* frameParam) override {}

        RENDER_ENGINE_API void CapturePrefabProxy(ecs::EntityID entityID, RenderTextureR* renderTexture, class PrefabProxyRenderPipeline* pipeline);

    private:
    };
}
