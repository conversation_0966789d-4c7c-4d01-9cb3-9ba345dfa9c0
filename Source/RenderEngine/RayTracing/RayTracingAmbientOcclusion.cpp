#include "RayTracingAmbientOcclusion.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"

namespace cross
{
    void RTAOSettings::Initialize()
    {
        LOAD_RENDER_PIPELINE_COMPUTE_SHADER(RTAOComputeShader);
        LOAD_RENDER_PIPELINE_RAYTRACING_SHADER(RayTracingPipelineShader);
    }

	bool RTAOPass::ExecuteImp(const GameContext& gameContext, REDTextureView* depthStencilView, std::array<REDTextureView*, 4> gBufferViews, REDTextureView*& aoView)
    {
        auto worldType = gameContext.mRenderWorld->GetRenderWorldType();
        auto* renderCamera = gameContext.mRenderCamera;
        auto* red = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor();
        UInt32 targetWidth = static_cast<UInt32>(depthStencilView->mTexture->mDesc.Width);
        UInt32 targetHeight = static_cast<UInt32>(depthStencilView->mTexture->mDesc.Height);
        Int2 ViewportSize = {static_cast<SInt32>(targetWidth), static_cast<SInt32>(targetHeight)};
        Float2 ViewportPixelSize = {1.f / targetWidth, 1.f / targetHeight};
        constexpr GraphicsFormat aoTermViewFormat = GraphicsFormat::R16G16B16A16_UNorm;
        auto* ffsSetting = dynamic_cast<const FFSRenderPipelineSetting*>(gameContext.mRenderPipeline->GetSetting());
        if (ffsSetting->EnableRayTracing && mSetting.enable &&
            (worldType == ToUnderlying(WorldTypeTag::DefaultWorld) ||
                worldType == ToUnderlying(WorldTypeTag::PIEWorld) ||
                worldType == ToUnderlying(WorldTypeTag::PreviewWorld)))
        {
            red->BeginRegion("RTAO");
            auto worldRenderPipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline)->GetWorldRenderPipeline();
            auto* rtaoTexView = IRenderPipeline::CreateTextureView2D("RTAO View", targetWidth, targetHeight, aoTermViewFormat, NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopyDst, 1);

            auto* rtaoPass = red->AllocatePass("RTAO");

            rtaoPass->SetProperty(NAME_ID("_GBuffer0"), gBufferViews[0]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer1"), gBufferViews[1]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer2"), gBufferViews[2]);
            rtaoPass->SetProperty(NAME_ID("_GBuffer3"), gBufferViews[3]);
            REDTextureView* depthView = red->AllocateTextureView(
                depthStencilView->mTexture,
                NGITextureViewDesc{NGITextureUsage::ShaderResource, depthStencilView->mDesc.Format, NGITextureType::Texture2D, NGITextureSubRange{NGITextureAspect::Depth, 0, depthStencilView->mDesc.SubRange.MipLevels, 0, 1}});
            rtaoPass->SetProperty(NAME_ID("_DepthTexture"), depthView);
            rtaoPass->SetProperty(NAME_ID("_outFinalAOTerm"), rtaoTexView);
            worldRenderPipeline->GetRayTracingScene()->SetRayTracingResources(rtaoPass);

            rtaoPass->SetProperty(NAME_ID("ce_InvViewProjMatrix"), renderCamera->GetInvertProjMatrix() * renderCamera->GetInvertViewMatrix());
            rtaoPass->SetProperty(NAME_ID("ce_ViewMatrix"), renderCamera->GetViewMatrix());
            rtaoPass->SetProperty(NAME_ID("_ScreenSizeAndInvSize"), Float4(static_cast<float>(ViewportSize.x), static_cast<float>(ViewportSize.y), ViewportPixelSize.x, ViewportPixelSize.y));
            rtaoPass->SetProperty(NAME_ID("ce_CameraTilePosition"), renderCamera->GetTilePosition<false>());

            UInt3 groupThreadSize;
            mSetting.RTAOComputeShaderR->GetThreadGroupSize("RTAO", groupThreadSize.x, groupThreadSize.y, groupThreadSize.z);


            rtaoPass->Dispatch(mSetting.RTAOComputeShaderR, "RTAO", (targetWidth + groupThreadSize.x - 1) / groupThreadSize.x,
             (targetHeight + groupThreadSize.y - 1) / groupThreadSize.y, 1);

            // TODO(chopperlin) : we will use screenspacedenoiser to do this

            // auto rtPipelinePass = red->AllocatePass("RayTracingPipeline", true);
            // worldRenderPipeline->GetRayTracingScene()->SetRayTracingResources(rtPipelinePass);
            // rtPipelinePass->SetProperty("OutColor", rtaoTexView);
            // rtPipelinePass->DispatchRays(mSetting.RayTracingPipelineShaderR, targetWidth, targetHeight, 1);

            aoView = IRenderPipeline::CreateTextureView2D(rtaoTexView->GetTexture(), aoTermViewFormat, NGITextureUsage::ShaderResource);

            red->EndRegion();
        }
        return true;
    }



}

