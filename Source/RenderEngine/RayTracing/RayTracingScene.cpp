#include "RayTracingScene.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/MaterialManager.h"
#include "Resource/Shader.h"
#include "RenderEngine/ModelSystemR.h"
#include <ranges>
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "CameraSystemR.h"
#include "Profiling/Profiling.h"

namespace cross {

RayTracingScene::~RayTracingScene()
{
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
    if (mSingleTriangleBLAS)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleBLAS));
    }
    if (mSingleTriangleVertexBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleVertexBuffer));
    }
    if (mSingleTriangleIndexBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleIndexBuffer));
    }
}

void RayTracingScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red, GPUScene* gpuScene)
{
    mRenderWorld = renderWorld;
    mRED = red;
    mGPUScene = gpuScene;
    mFFSRenderPipelineSetting = dynamic_cast<const FFSRenderPipelineSetting*>(EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting());
}

void RayTracingScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    mFFSRenderPipelineSetting = renderPipelineSetting;
}

void RayTracingScene::Update()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::Update");

    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
    
    // Step 1: Set up prerequisites
    {
        mRendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        mModelSystem = mRenderWorld->GetRenderSystem<ModelSystemR>();
        mTransformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        mEntityLifeCycleRenderDataSystem = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();
        mMainCameraTilePosition = Float3(0.f, 0.f, 0.f);
        auto mainCamera = mRenderWorld->GetRenderSystem<CameraSystemR>()->GetMainCamera();
        if (mainCamera != ecs::EntityID::InvalidHandle())
        {
            mMainCameraTilePosition = mTransformSystem->GetTilePosition(mainCamera);
        }
        mCmd = mRendererSystem->GetAccelStructCmd();
    }
    
    // Step 2: Handle events
    {
        // Step 2.1: Handle entity create
        for (const auto& data :  mEntityLifeCycleRenderDataSystem->GetRenderEntityCreateList())
        {
            // TODO(scolu): cull sky sphere since it has huge impact on performance
            AddUpdateEntity(data.entity, RayTracingInstanceChangeType::Create);
        }
        
        // Step 2.2: Handle entity change
        // for (const auto& data : mEntityLifeCycleRenderDataSystem->GetRenderEntityChangeList())
        // {
        //     bool isCreateEvent = data.type == RenderEntityChangeData::ChangeType::All;
        //     AddUpdateEntity(data.entity, RayTracingInstanceChangeType::Skeleton);
        // }
    }
    
    BuildBLASes();
    
    if (mIsSceneDirty)
    {
        RefreshIndex();
        UpdateSubInstanceData();
        UpdateHitGroupInfo();
        mIsSceneDirty = false;
        mIsMaterialDirty = false;
    }

    BuildTLAS();  // TODO(scolu): Compact BLASes
    
    mFrameUpdateEntitySet.clear();
}

void RayTracingScene::PostUpdate()
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
}

void RayTracingScene::RemoveEntity(ecs::EntityID entity)
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
    
    std::lock_guard locker(mMutex);
    mRayTracingInstances.erase(std::remove_if(mRayTracingInstances.begin(), mRayTracingInstances.end(),
        [&entity](const RayTracingInstance& instance) {
            return instance.Entity == entity;
        }),
        mRayTracingInstances.end());
    
    mRayTracingInstancesSet.erase(entity);
    mIsSceneDirty = true;
}

void RayTracingScene::SetRayTracingResources(REDPass* pass) const
{
    pass->SetProperty(BuiltInProperty::ce_AccelerationStructure, GetTopLevelAccelStruct());
    pass->SetProperty(BuiltInProperty::ce_SubInstanceData, mSubInstanceBufferView.get());
}

std::shared_ptr<REDRayTracingPipelineWrapper> RayTracingScene::RegisterRayTracingPipeline(RayTracingShaderR* rayGen, std::vector<RayTracingShaderR*> missShaders)
{
    auto wrapper = std::make_shared<REDRayTracingPipelineWrapper>();
    wrapper->RecompileCount = rayGen->GetRecompileCounter();
    mRayTracingPipelineStates.emplace_back(wrapper);
    
    wrapper->ProgramDesc = std::make_shared<resource::RayTracingProgramDesc>();
    *wrapper->ProgramDesc = *rayGen->GetProgramDesc();

    wrapper->ProgramDesc->ProgramDesc.MissShaders.clear();
    for (auto& missShader : missShaders)
    {
        wrapper->ProgramDesc->ProgramDesc.MissShaders.insert(wrapper->ProgramDesc->ProgramDesc.MissShaders.end(),
            missShader->GetProgramDesc()->ProgramDesc.MissShaders.begin(), missShader->GetProgramDesc()->ProgramDesc.MissShaders.end());
    }

    SetSceneDirty();
    
    return wrapper;
}

std::shared_ptr<REDRayTracingPipelineWrapper> RayTracingScene::RegisterRayTracingPipeline(RayTracingShaderR* rayGen)
{
    return RegisterRayTracingPipeline(rayGen, {mFFSRenderPipelineSetting->DefaultHitGroupAndMissShaderR});
}

void RayTracingScene::UnregisterRayTracingPipeline(const std::shared_ptr<REDRayTracingPipelineWrapper>& wrapper)
{
    for (auto it = mRayTracingPipelineStates.begin(); it != mRayTracingPipelineStates.end();)
    {
        if (it->get() == wrapper.get())
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(it->get()->PipelineState));
            it = mRayTracingPipelineStates.erase(it);
        }
        else
        {
            ++it;
        }
    }
}

void RayTracingScene::RefreshIndex()
{
    UInt32 instanceID = 0, subInstanceID = 0;
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        if (!renderNode->GetAccelStruct())
        {
            continue;
        }

        if (renderNode->GetSubMeshCount() > 0)
        {
            instance.InstanceID = instanceID++;
            instance.SubInstanceID = subInstanceID;
            subInstanceID += renderNode->GetSubMeshCount();
        }

        mIsMaterialDirty |= !renderNode->IsMaterialExist(this);
    }
}

void RayTracingScene::SetRayTracingSceneDirty(ecs::EntityID entity, RayTracingInstanceChangeType changeType)
{
    AddUpdateEntity(entity, changeType);
}

bool RayTracingScene::IsEntityValidForRayTracing(ecs::EntityID entity) const
{
    if (!mRenderWorld->IsEntityAlive(entity) ||
        !mRenderWorld->HasComponent<RenderNodeComponentR>(entity) ||
        !mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->IsValidForRayTracing() ||
        mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->GetSubMeshCount() == 0)
    {
        return false;
    }
    
    return true;
}

UInt32 RayTracingScene::GetSubInstanceCount() const
{
    UInt32 subInstanceCount = 0;
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        // TODO(scolu)
        if (!renderNode->GetAccelStruct())
        {
            continue;
        }
        
        subInstanceCount += renderNode->GetSubMeshCount();
    }
    return subInstanceCount;
}

void RayTracingScene::CreateBLASPlaceHolder()
{
    if (!mSingleTriangleBLAS)
    {
        NGIBufferDesc vertexBufferDesc{
            .Size = sizeof(Float3) * 3,
            .Usage = NGIBufferUsage::VertexBuffer
        };
        if (mSingleTriangleVertexBuffer)
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleVertexBuffer));
        }
        mSingleTriangleVertexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(vertexBufferDesc, "SingleTriangleVertexBuffer"));

        NGIBufferDesc indexBufferDesc{
            .Size = sizeof(UInt32) * 3,
            .Usage = NGIBufferUsage::IndexBuffer
        };
        if (mSingleTriangleIndexBuffer)
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleIndexBuffer));
        }
        mSingleTriangleIndexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(indexBufferDesc, "SingleTriangleIndexBuffer"));
        
        NGIAccelStructDesc blasDesc;
        blasDesc.IsTopLevel = false;
        blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
        blasDesc.DebugName = "SingleTriangleBLAS";

        NGIGeometryDesc geoDesc{};
        geoDesc.GeometryType = NGIGeometryType::Triangle;
        NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
        
        triangles.IndexBuffer = mSingleTriangleIndexBuffer.get();
        triangles.IndexFormat = IndexFormat_UInt32;
        triangles.IndexOffset = 0;
        triangles.IndexCount = 3;
        triangles.VertexBuffer = mSingleTriangleVertexBuffer.get();
        triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
        triangles.VertexOffset = 0;
        triangles.VertexStride = 12;
        triangles.VertexCount = 3;
        geoDesc.UseTransform = false;
        geoDesc.Flag = NGIGeometryFlag::Opaque;

        blasDesc.BottomLevelGeometries.push_back(geoDesc);

        mSingleTriangleBLAS.reset(GetNGIDevicePtr()->CreateAccelStruct(blasDesc));
        
        UInt64 scratchBufferSize = mSingleTriangleBLAS->mSizeInfo.BuildScratchSize;
        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        auto [scratchBuffer, scratchBufferState] =
            mRendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLASBuildScratchBuffer", true, false);
        mCmd->BuildBottomLevelAccelStruct(mSingleTriangleBLAS.get(),
                                          blasDesc.BottomLevelGeometries.data(),
                                          blasDesc.BottomLevelGeometries.size(),
                                          blasDesc.BuildFlag,
                                          scratchBuffer);
    }
}

void RayTracingScene::UpdateSubInstanceData()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::UpdateSubInstanceData");
    
    if (mSubInstanceBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
    }

    UInt32 subInstanceCount = GetSubInstanceCount();
    // TODO(scolu): Copy scatter data rather than copy all sub instance data
    mSubInstanceData.clear();
    mSubInstanceData.reserve(subInstanceCount);
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        // TODO(scolu)
        if (!renderNode->GetAccelStruct())
        {
            continue;
        }
        
        auto subInstanceDataVec = renderNode->GetSubInstanceData();
        mSubInstanceData.insert(mSubInstanceData.end(),
            std::make_move_iterator(subInstanceDataVec.begin()), std::make_move_iterator(subInstanceDataVec.end()));
    }
    if (subInstanceCount == 0)
    {
        mSubInstanceData.push_back(SubInstanceData());
    }
    Assert(subInstanceCount == 0 || subInstanceCount == mSubInstanceData.size());
    
    // Upload SubInstanceBuffer
    {
        UInt32 subInstanceBufferSize = std::max(static_cast<UInt32>(mSubInstanceData.size()), 1u) * sizeof(SubInstanceData);
        NGIBufferDesc subInstanceBufferDesc{
            .Size = subInstanceBufferSize,
            .Usage = NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst,
        };
        mSubInstanceBuffer.reset(GetNGIDevicePtr()->CreateBuffer(subInstanceBufferDesc, "SubInstanceData Buffer"));
        
        auto* sb = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetScratchBuffer();
        auto stagingBufferWrap = sb->AllocateStaging(NGIBufferUsage::CopySrc, subInstanceBufferSize);
        stagingBufferWrap.MemWrite(0, mSubInstanceData.data(), subInstanceBufferSize);

        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            subInstanceBufferSize
        };
        mRendererSystem->UpdateBuffer(mSubInstanceBuffer.get(), stagingBufferWrap.GetNGIBuffer(), region, 
            NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        NGIBufferViewDesc subInstanceBufferViewDesc{
            .Usage = NGIBufferUsage::StructuredBuffer,
            .BufferLocation = 0,
            .SizeInBytes = subInstanceBufferSize,
            .StructureByteStride = sizeof(SubInstanceData)
        };
        mSubInstanceBufferView.reset(GetNGIDevicePtr()->CreateBufferView(mSubInstanceBuffer.get(), subInstanceBufferViewDesc));
    }
}

void RayTracingScene::UpdateHitGroupInfo()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::UpdateHitGroupInfo");
    
    UInt32 subInstanceCount = GetSubInstanceCount();
    if (mIsMaterialDirty)
    {
        mHitGroupIndexMap.clear();
        mUniqueHitGroups.clear();
    }
    mHitGroupIndices.clear();
    mHitGroupIndices.reserve(subInstanceCount);
    
    UInt32 hitGroupIndex = 1;
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        // TODO(scolu)
        if (!renderNode->GetAccelStruct())
        {
            continue;
        }
        
        auto hitGroupInfos = renderNode->GetHitGroupInfos();
        for (auto& hitGroup : hitGroupInfos)
        {
            if (mIsMaterialDirty)  // Update Cached Materials if Dirty
            {
                if (!mHitGroupIndexMap.contains(hitGroup.Material))
                {
                    if (hitGroup.ClosestHitShader)
                    {
                        Assert(hitGroupIndex == mUniqueHitGroups.size() + 1);
                        mUniqueHitGroups.push_back(hitGroup);
                        mHitGroupIndices.push_back(hitGroupIndex);
                        mHitGroupIndexMap[hitGroup.Material] = {
                            hitGroupIndex++
                        };
                    }
                    else
                    {
                        mHitGroupIndices.push_back(0);
                        mHitGroupIndexMap[hitGroup.Material] = {
                            0
                        };
                    }
                }
                else
                {
                    mHitGroupIndices.push_back(mHitGroupIndexMap[hitGroup.Material]);
                }
            }
            else  // Only Update HitGroupIndices
            {
                Assert(mHitGroupIndexMap.contains(hitGroup.Material));
                mHitGroupIndices.push_back(mHitGroupIndexMap[hitGroup.Material]);
            }
        }
    }

    if (mIsMaterialDirty)
    {
        mHitGroupDesc.clear();
        mHitGroupDesc.reserve(mHitGroupIndexMap.size() + 1);
        // Add default hit group and miss shader
        mHitGroupDesc.push_back(mFFSRenderPipelineSetting->DefaultHitGroupAndMissShaderR->GetProgramDesc()->ProgramDesc.HitGroups[0]);
        for (auto& uniqueHitGroup : mUniqueHitGroups)
        {
            auto closestHitShader = uniqueHitGroup.ClosestHitShader;
            auto anyHitShader = uniqueHitGroup.AnyHitShader;
            NGIShaderCodeDesc anyHitShaderCode = anyHitShader ? anyHitShader->GetAnyHitShaderCode() : NGIShaderCodeDesc();
            NGIShaderCodeDesc intersectionShaderCode{};
            
            mHitGroupDesc.emplace_back(closestHitShader->GetClosestHitShaderCode(), anyHitShaderCode, intersectionShaderCode);
        }
    }

    ConstructRTPipeline();
}

void RayTracingScene::ConstructRTPipeline()
{
    auto rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    
    for (auto& rtPipelineWrapper : mRayTracingPipelineStates)
    {
        rtPipelineWrapper->ProgramDesc->ProgramDesc.HitGroups = mHitGroupDesc;
        
        rtPipelineWrapper->PerObjectBufferView = mGPUScene->GetPrimitiveSceneDataBufferSRV();
        rtPipelineWrapper->PerPrimitiveBufferView = mGPUScene->GetObjectSceneDataBufferSRV();

        bool isLayoutChanged = false;
        auto MergeShaderLayouts = [&isLayoutChanged](auto& dstLayout, const auto& srcLayout)
        {
            auto MergeConstantBufferLayout = [&isLayoutChanged](resource::ShaderBufferLayout& dst, const resource::ShaderBufferLayout& src)
            {
                for (auto& member : src.Members)
                {
                    auto it = std::find_if(dst.Members.begin(), dst.Members.end(), [&](auto& v) { return v.Name == member.Name; });
                    if (it == dst.Members.end())
                    {
                        dst.Members.push_back(member);
                        dst.ByteSize += member.Size;
                        isLayoutChanged = true;
                    }
                }
            };
            // Merge space0 constant buffers
            for (UInt32 space0ConstBufferIndex = 0; space0ConstBufferIndex < srcLayout[ShaderParamGroup_Pass].ConstantBufferLayouts.size(); ++space0ConstBufferIndex)
            {
                MergeConstantBufferLayout(dstLayout[ShaderParamGroup_Pass].ConstantBufferLayouts[space0ConstBufferIndex],
                    srcLayout[ShaderParamGroup_Pass].ConstantBufferLayouts[space0ConstBufferIndex]);
            }

            auto MergeResourceLayout = [&isLayoutChanged](std::vector<resource::ShaderResourceDesc>& dst, const std::vector<resource::ShaderResourceDesc>& src)
            {
                for (auto& resource : src)
                {
                    auto it = std::find_if(dst.begin(), dst.end(), [&](auto& v) { return v.ID == resource.ID; });
                    if (it == dst.end())
                    {
                        dst.push_back(resource);
                        isLayoutChanged = true;
                    }
                }
            };
            // Merge all space resources
            for (UInt32 spaceIndex = 0; spaceIndex < dstLayout.size(); ++spaceIndex)
            {
                MergeResourceLayout(dstLayout[spaceIndex].ResourceLayouts, srcLayout[spaceIndex].ResourceLayouts);
            }
        };

        auto& srcLayout = rtPipelineWrapper->ProgramDesc->ResourceGroupLayouts;
        for (auto& hitGroup : mUniqueHitGroups)
        {
            auto closestHitR = dynamic_cast<RayTracingShaderR*>(hitGroup.ClosestHitShader->GetRenderObject());
            MergeShaderLayouts(srcLayout, closestHitR->GetProgramDesc()->ResourceGroupLayouts);
            if (hitGroup.AnyHitShader)
            {
                auto anyHitR = dynamic_cast<RayTracingShaderR*>(hitGroup.AnyHitShader->GetRenderObject());
                MergeShaderLayouts(srcLayout, anyHitR->GetProgramDesc()->ResourceGroupLayouts);
            }
        }

        // Create PipelineLayout
        NGIPipelineLayoutDesc pipelineLayoutDesc{static_cast<UInt32>(srcLayout.size())};
        for (UInt32 i = 0; i < srcLayout.size(); ++i)
        {
            auto& group = srcLayout[i];
            std::vector<NGIResourceDesc> resDescs;
            resDescs.reserve(group.ResourceLayouts.size() + group.ConstantBufferLayouts.size());
            std::copy(group.ResourceLayouts.begin(), group.ResourceLayouts.end(), std::back_inserter(resDescs));
            std::copy(group.ConstantBufferLayouts.begin(), group.ConstantBufferLayouts.end(), std::back_inserter(resDescs));
            std::sort(resDescs.begin(), resDescs.end());
            NGIResourceGroupLayoutDesc resourceGroupLayoutDesc{
                static_cast<UInt32>(resDescs.size()),
                resDescs.data(),
            };
            pipelineLayoutDesc.ResourceGroupLayouts[i] = gResourceMgr.mCreateRenderObjectMgr->AllocateResourceGroupLayout(resourceGroupLayoutDesc);
        }
        rtPipelineWrapper->PipelineLayout = gResourceMgr.mCreateRenderObjectMgr->AllocatePipelineLayout(pipelineLayoutDesc);
        rtPipelineWrapper->ProgramDesc->PipelineLayout = rtPipelineWrapper->PipelineLayout;

        // Create PipelineState
        NGIRayTracingPipelineStateDesc pipelineDesc{
            .ProgramGUID = {
                rtPipelineWrapper->ProgramDesc->GUID.low,
                rtPipelineWrapper->ProgramDesc->GUID.high
            },
            .Program = &rtPipelineWrapper->ProgramDesc->ProgramDesc,
            .PipelineLayout = rtPipelineWrapper->ProgramDesc->PipelineLayout,
            .MaxRecursionDepth = 1,
            .MaxPayloadSize = 64,  // TODO: As small as possible, not used for Vulkan
            .MaxAttributeSize = 8,
        };

        if (mIsMaterialDirty || isLayoutChanged || !rtPipelineWrapper->PipelineState)
        {
            if (rtPipelineWrapper->PipelineState)
            {
                EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(rtPipelineWrapper->PipelineState));
            }
            {
                std::scoped_lock lock(mRayTracingPipelineStateCreateMutex);
                rtPipelineWrapper->PipelineState.reset(rendererSystem->GetPipelineStatePool()->CreateRayTracingPipelineState(pipelineDesc));
            }
        }
        
        // Create ShaderBindingTable
        UInt32 hitGroupSBTIndexOffset = static_cast<UInt32>(1 + rtPipelineWrapper->ProgramDesc->ProgramDesc.MissShaders.size());  // 1 for ray gen
        rtPipelineWrapper->PipelineState->CreateShaderBindingTable(0, {1}, mHitGroupIndices, hitGroupSBTIndexOffset);
    }
}

void RayTracingScene::AddUpdateEntity(ecs::EntityID entity, RayTracingInstanceChangeType changeType)
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing || !IsEntityValidForRayTracing(entity))
    {
        return;
    }
    
    std::lock_guard locker(mMutex);
    mFrameUpdateEntitySet.insert({entity, changeType});
    SetSceneDirty();
}

void RayTracingScene::BuildTLAS()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildTLAS");

    mCmd->BeginDebugRegion("BuildTLAS");

    UInt32 subInstanceCount = 0;
    std::vector<NGIInstanceDesc> instanceDescs;
    if (!mRayTracingInstances.empty())
    {
        for (auto& instance: mRayTracingInstances)
        {
            if (!IsEntityValidForRayTracing(instance.Entity))
            {
                continue;
            }

            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
            RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

            // TODO(scolu)
            if (!renderNode->GetAccelStruct())
            {
                continue;
            }
            
            NGIInstanceDesc instanceDesc{};
            // Do preview translation for higher precision near camera
            auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(instance.Entity);
            Float4x4 transform = mTransformSystem->GetWorldAbsoMatrixMatrix(transformComp.Read())->Transpose();
            Float3 previewTranslation = Float3(0.f, 0.f, 0.f);
#ifdef CE_USE_DOUBLE_TRANSFORM
            previewTranslation = mMainCameraTilePosition * LENGTH_PER_TILE;
            transform[3] -= previewTranslation.x;
            transform[7] -= previewTranslation.y;
            transform[11] -= previewTranslation.z;
#endif
            memcpy(instanceDesc.Transform, &transform, sizeof(float) * 12);
            instanceDesc.InstanceID = instance.SubInstanceID;
            instanceDesc.InstanceMask = 0xff;
            instanceDesc.InstanceContribToHitGroupIndex = instance.SubInstanceID;
            NGIInstanceFlag flag = renderNode->ShouldInvokeAnyHit() ? NGIInstanceFlag::ForceNoneOpaque : NGIInstanceFlag::ForceOpaque;
            instanceDesc.Flag = flag;
            instanceDesc.BottomLevelAS = renderNode->GetAccelStruct();
            instanceDescs.push_back(instanceDesc);

            Assert(instance.SubInstanceID == subInstanceCount);  // TODO(scolu): Hide submesh will cause assert true
            subInstanceCount += renderNode->GetSubMeshCount();
        }
    }
    else  // Build TLAS with a simple triangle when no valid instances are available
    {
        CreateBLASPlaceHolder();
        
        NGIInstanceDesc instanceDesc{};
        instanceDesc.InstanceID = 0;
        instanceDesc.InstanceMask = 0xff;
        instanceDesc.InstanceContribToHitGroupIndex = 0;
        instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
        instanceDesc.BottomLevelAS = mSingleTriangleBLAS.get();
        instanceDescs.push_back(instanceDesc);
    }
    Assert(subInstanceCount == GetSubInstanceCount());

    NGIAccelStructBuildFlag buildFlags = NGIAccelStructBuildFlag::PreferFastTrace;

    // UInt64 scratchBufferSize;
    // if (!mAccelStruct || mAccelStruct->GetTopLevelMaxInstanceCount() != instanceDescs.size())
    // {
    //     DestroyTLAS();
    //     CreateTLAS();
    //     scratchBufferSize = mAccelStruct->mSizeInfo.BuildScratchSize;
    // }
    // else
    // {
    //     buildFlags |= NGIAccelStructBuildFlag::PerformUpdate;
    //     scratchBufferSize = mAccelStruct->mSizeInfo.UpdateScratchSize;
    // }

    DestroyTLAS();
    CreateTLAS();
    UInt64 scratchBufferSize = mAccelStruct->mSizeInfo.BuildScratchSize;
    
    NGIBufferDesc scratchBufferDesc{
        .Size = scratchBufferSize,
        .Usage = NGIBufferUsage::RayTracingScratchBuffer
    };
    auto [scratchBuffer, scratchBufferState] =
        mRendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc,
                                                                 "TLAS Build Scratch Buffer",
                                                                 true,
                                                                 false);

    UInt32 uploadBufferSize = mAccelStruct->GetTopLevelUploadBufferSize();
    ScratchBufferWrap uploadInstanceBuffer = mRendererSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::AccelStructBuildInputBuffer, uploadBufferSize);

    // Wait for blas build finish
    NGIMemoryBarrier barrier{.StateBefore = NGIResourceState::AccelStructWrite,
        .StateAfter = NGIResourceState::AccelStructBuildBLAS | NGIResourceState::ShaderStageBitMask};
    mCmd->MemBarrier(&barrier);

    mCmd->BuildTopLevelAccelStructFromBuffer(mAccelStruct.get(),
                                            uploadInstanceBuffer,
                                            uploadInstanceBuffer.GetNGIOffset(),
                                            instanceDescs.data(),
                                            instanceDescs.size(),
                                            buildFlags,
                                            scratchBuffer);
    mCmd->TLASBarrier(mAccelStruct.get());

    mCmd->EndDebugRegion();
}

void RayTracingScene::CreateTLAS()
{
    NGIAccelStructDesc tlasDesc;
    tlasDesc.IsTopLevel = true;
    tlasDesc.TopLevelMaxInstance = std::max(static_cast<UInt32>(mRayTracingInstances.size()), 1u);
    tlasDesc.BuildFlag = NGIAccelStructBuildFlag::AllowUpdate | NGIAccelStructBuildFlag::PreferFastTrace;
    tlasDesc.DebugName = "TLASDataBuffer";
    mAccelStruct.reset(GetNGIDevicePtr()->CreateAccelStruct(tlasDesc));
}

void RayTracingScene::DestroyTLAS()
{
    if (mAccelStruct)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
        mAccelStruct = nullptr;
    }
}

void RayTracingScene::BuildBLASes()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildBLASes");

    mCmd->BeginDebugRegion("BuildBLASes");
    
    for (auto& [entity, changeType] : mFrameUpdateEntitySet)
    {
        if (changeType == RayTracingInstanceChangeType::Create)
        {
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
            else  // for mesh create only build blas for the first time
            {
                continue;
            }
        }
        else if (changeType == RayTracingInstanceChangeType::Mesh || changeType == RayTracingInstanceChangeType::Skeleton)
        {
            // build blas for mesh change and skeleton mesh even if it's already in the list
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
        }
        else if (changeType == RayTracingInstanceChangeType::Material)
        {
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
            else
            {
                continue;
            }
        }
        
        if (!IsEntityValidForRayTracing(entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();
        renderNode->BuildAccelStruct(*this, mRenderWorld, entity);
        Assert(renderNode->GetAccelStruct());
    }

    mCmd->EndDebugRegion();
}

}
