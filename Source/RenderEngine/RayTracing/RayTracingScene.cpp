#include "RayTracingScene.h"
#include "RenderEngine/EntityLifeCycleRenderDataSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/MaterialManager.h"
#include "Resource/Shader.h"
#include "RenderEngine/ModelSystemR.h"
#include <ranges>
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "CameraSystemR.h"
#include "Profiling/Profiling.h"

namespace cross {

RayTracingScene::~RayTracingScene()
{
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
    EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
    if (mSingleTriangleBLAS)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleBLAS));
    }
    if (mSingleTriangleVertexBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleVertexBuffer));
    }
    if (mSingleTriangleIndexBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleIndexBuffer));
    }
}

void RayTracingScene::Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red, GPUScene* gpuScene)
{
    mRenderWorld = renderWorld;
    mRED = red;
    mGPUScene = gpuScene;
}

void RayTracingScene::SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting)
{
    mFFSRenderPipelineSetting = renderPipelineSetting;
}

void RayTracingScene::Update()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::Update");
    
    // Step 1: Set up prerequisites
    {
        mRendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
        mModelSystem = mRenderWorld->GetRenderSystem<ModelSystemR>();
        mTransformSystem = mRenderWorld->GetRenderSystem<TransformSystemR>();
        mEntityLifeCycleRenderDataSystem = mRenderWorld->GetRenderSystem<EntityLifeCycleRenderDataSystemR>();
        mMainCameraTilePosition = Float3(0.f, 0.f, 0.f);
        auto mainCamera = mRenderWorld->GetRenderSystem<CameraSystemR>()->GetMainCamera();
        if (mainCamera != ecs::EntityID::InvalidHandle())
        {
            mMainCameraTilePosition = mTransformSystem->GetTilePosition(mainCamera);
        }
        mCmd = mRendererSystem->GetAccelStructCmd();
    }

    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
    
    // Step 2: Handle events
    {
        // Step 2.1: Handle entity create
        for (const auto& data :  mEntityLifeCycleRenderDataSystem->GetRenderEntityCreateList())
        {
            // TODO(scolu): cull sky sphere since it has huge impact on performance
            AddUpdateEntity(data.entity, RayTracingInstanceChangeType::Create);
        }
        
        // Step 2.2: Handle entity change
        // for (const auto& data : mEntityLifeCycleRenderDataSystem->GetRenderEntityChangeList())
        // {
        //     bool isCreateEvent = data.type == RenderEntityChangeData::ChangeType::All;
        //     AddUpdateEntity(data.entity, RayTracingInstanceChangeType::Skeleton);
        // }
    }
    
    BuildBLASes();

    if (mIsInstanceDataDirty)
    {
        RefreshIndex();
        UpdateSubInstanceData();
        mIsInstanceDataDirty = false;
    }
    
    // TODO(scolu): Compact BLASes
    BuildTLAS();
    mFrameUpdateEntitySet.clear();
}

void RayTracingScene::PostUpdate()
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
}

void RayTracingScene::RemoveEntity(ecs::EntityID entity)
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing)
    {
        return;
    }
    
    std::lock_guard locker(mMutex);
    mRayTracingInstances.erase(std::remove_if(mRayTracingInstances.begin(), mRayTracingInstances.end(),
        [&entity](const RayTracingInstance& instance) {
            return instance.Entity == entity;
        }),
        mRayTracingInstances.end());
    
    mRayTracingInstancesSet.erase(entity);
    mIsInstanceDataDirty = true;
}

void RayTracingScene::SetRayTracingResources(REDPass* pass) const
{
    pass->SetProperty(BuiltInProperty::ce_AccelerationStructure, GetTopLevelAccelStruct());
    pass->SetProperty(BuiltInProperty::ce_SubInstanceData, mSubInstanceBufferView.get());
    pass->SetProperty(BuiltInProperty::ce_PerMaterial,
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetMaterialGPUScene()->GetMaterialBufferViewForRayTracing());
}

void RayTracingScene::RefreshIndex()
{
    UInt32 instanceID = 0, subInstanceID = 0;
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        if (renderNode->GetSubMeshCount() > 0)
        {
            instance.InstanceID = instanceID++;
            instance.SubInstanceID = subInstanceID;
            subInstanceID += renderNode->GetSubMeshCount();
        }
    }
}

void RayTracingScene::SetRayTracingSceneDirty(ecs::EntityID entity, RayTracingInstanceChangeType changeType)
{
    AddUpdateEntity(entity, changeType);
}

bool RayTracingScene::IsEntityValidForRayTracing(ecs::EntityID entity) const
{
    if (!mRenderWorld->IsEntityAlive(entity) ||
        !mRenderWorld->HasComponent<RenderNodeComponentR>(entity) ||
        !mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->IsValidForRayTracing() ||
        mRenderWorld->GetComponent<RenderNodeComponentR>(entity).Read()->mRenderNode.get()->GetSubMeshCount() == 0)
    {
        return false;
    }
    
    return true;
}

UInt32 RayTracingScene::GetSubInstanceCount() const
{
    UInt32 subInstanceCount = 0;
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        // TODO(scolu)
        if (!renderNode->GetAccelStruct())
        {
            continue;
        }
        
        subInstanceCount += renderNode->GetSubMeshCount();
    }
    return subInstanceCount;
}

void RayTracingScene::CreateBLASPlaceHolder()
{
    if (!mSingleTriangleBLAS)
    {
        NGIBufferDesc vertexBufferDesc{
            .Size = sizeof(Float3) * 3,
            .Usage = NGIBufferUsage::VertexBuffer
        };
        if (mSingleTriangleVertexBuffer)
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleVertexBuffer));
        }
        mSingleTriangleVertexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(vertexBufferDesc, "SingleTriangleVertexBuffer"));

        NGIBufferDesc indexBufferDesc{
            .Size = sizeof(UInt32) * 3,
            .Usage = NGIBufferUsage::IndexBuffer
        };
        if (mSingleTriangleIndexBuffer)
        {
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSingleTriangleIndexBuffer));
        }
        mSingleTriangleIndexBuffer.reset(GetNGIDevicePtr()->CreateBuffer(indexBufferDesc, "SingleTriangleIndexBuffer"));
        
        NGIAccelStructDesc blasDesc;
        blasDesc.IsTopLevel = false;
        blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
        blasDesc.DebugName = "SingleTriangleBLAS";

        NGIGeometryDesc geoDesc{};
        geoDesc.GeometryType = NGIGeometryType::Triangle;
        NGIGeometryTriangle& triangles = geoDesc.GeometryData.Triangle;
        
        triangles.IndexBuffer = mSingleTriangleIndexBuffer.get();
        triangles.IndexFormat = IndexFormat_UInt32;
        triangles.IndexOffset = 0;
        triangles.IndexCount = 3;
        triangles.VertexBuffer = mSingleTriangleVertexBuffer.get();
        triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
        triangles.VertexOffset = 0;
        triangles.VertexStride = 12;
        triangles.VertexCount = 3;
        geoDesc.UseTransform = false;
        geoDesc.Flag = NGIGeometryFlag::Opaque;

        blasDesc.BottomLevelGeometries.push_back(geoDesc);

        mSingleTriangleBLAS.reset(GetNGIDevicePtr()->CreateAccelStruct(blasDesc));
        
        UInt64 scratchBufferSize = mSingleTriangleBLAS->mSizeInfo.BuildScratchSize;
        NGIBufferDesc scratchBufferDesc{
            .Size = scratchBufferSize,
            .Usage = NGIBufferUsage::RayTracingScratchBuffer
        };
        auto [scratchBuffer, scratchBufferState] =
            mRendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLASBuildScratchBuffer", true, false);
        mCmd->BuildBottomLevelAccelStruct(mSingleTriangleBLAS.get(),
                                          blasDesc.BottomLevelGeometries.data(),
                                          blasDesc.BottomLevelGeometries.size(),
                                          blasDesc.BuildFlag,
                                          scratchBuffer);
    }
}

void RayTracingScene::UpdateSubInstanceData()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::UpdateSubInstanceData");
    
    if (mSubInstanceBuffer)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBuffer));
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mSubInstanceBufferView));
    }

    UInt32 subInstanceCount = GetSubInstanceCount();
    // TODO(scolu): Copy scatter data rather than copy all sub instance data
    mSubInstanceData.clear();
    mSubInstanceData.reserve(subInstanceCount);
    for (auto& instance : mRayTracingInstances)
    {
        if (!IsEntityValidForRayTracing(instance.Entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

        // TODO(scolu)
        if (!renderNode->GetAccelStruct())
        {
            continue;
        }
        
        auto subInstanceDataVec = renderNode->GetSubInstanceData();
        mSubInstanceData.insert(mSubInstanceData.end(),
            std::make_move_iterator(subInstanceDataVec.begin()), std::make_move_iterator(subInstanceDataVec.end()));
    }
    if (subInstanceCount == 0)
    {
        mSubInstanceData.push_back(SubInstanceData());
    }
    Assert(subInstanceCount == 0 || subInstanceCount == mSubInstanceData.size());
    
    // Upload SubInstanceBuffer
    {
        UInt32 subInstanceBufferSize = std::max(static_cast<UInt32>(mSubInstanceData.size()), 1u) * sizeof(SubInstanceData);
        NGIBufferDesc subInstanceBufferDesc{
            .Size = subInstanceBufferSize,
            .Usage = NGIBufferUsage::StructuredBuffer | NGIBufferUsage::CopyDst,
        };
        mSubInstanceBuffer.reset(GetNGIDevicePtr()->CreateBuffer(subInstanceBufferDesc, "SubInstanceData Buffer"));
        
        auto* sb = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetScratchBuffer();
        auto stagingBufferWrap = sb->AllocateStaging(NGIBufferUsage::CopySrc, subInstanceBufferSize);
        stagingBufferWrap.MemWrite(0, mSubInstanceData.data(), subInstanceBufferSize);

        NGICopyBuffer region{
            stagingBufferWrap.GetNGIOffset(),
            0,
            subInstanceBufferSize
        };
        mRendererSystem->UpdateBuffer(mSubInstanceBuffer.get(), stagingBufferWrap.GetNGIBuffer(), region, 
            NGIResourceState::Undefined, NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);

        NGIBufferViewDesc subInstanceBufferViewDesc{
            .Usage = NGIBufferUsage::StructuredBuffer,
            .BufferLocation = 0,
            .SizeInBytes = subInstanceBufferSize,
            .StructureByteStride = sizeof(SubInstanceData)
        };
        mSubInstanceBufferView.reset(GetNGIDevicePtr()->CreateBufferView(mSubInstanceBuffer.get(), subInstanceBufferViewDesc));
    }
}

void RayTracingScene::AddUpdateEntity(ecs::EntityID entity, RayTracingInstanceChangeType changeType)
{
    if (!mFFSRenderPipelineSetting->EnableRayTracing || !IsEntityValidForRayTracing(entity))
    {
        return;
    }
    
    std::lock_guard locker(mMutex);
    mFrameUpdateEntitySet.insert({entity, changeType});
    mIsInstanceDataDirty = true;
}

void RayTracingScene::BuildTLAS()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildTLAS");

    mCmd->BeginDebugRegion("BuildTLAS");
    
    std::vector<NGIInstanceDesc> instanceDescs;
    if (!mRayTracingInstances.empty())
    {
        for (auto& instance: mRayTracingInstances)
        {
            if (!IsEntityValidForRayTracing(instance.Entity))
            {
                continue;
            }

            auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(instance.Entity);
            RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();

            // TODO(scolu)
            if (!renderNode->GetAccelStruct())
            {
                continue;
            }
            
            NGIInstanceDesc instanceDesc{};
            // Do preview translation for higher precision near camera
            auto transformComp = mRenderWorld->GetComponent<TransformComponentR>(instance.Entity);
            Float4x4 transform = mTransformSystem->GetWorldAbsoMatrixMatrix(transformComp.Read())->Transpose();
            Float3 previewTranslation = Float3(0.f, 0.f, 0.f);
#ifdef CE_USE_DOUBLE_TRANSFORM
            previewTranslation = mMainCameraTilePosition * LENGTH_PER_TILE;
            transform[3] -= previewTranslation.x;
            transform[7] -= previewTranslation.y;
            transform[11] -= previewTranslation.z;
#endif
            memcpy(instanceDesc.Transform, &transform, sizeof(float) * 12);
            instanceDesc.InstanceID = instance.SubInstanceID;
            instanceDesc.InstanceMask = 0xff;
            instanceDesc.InstanceContribToHitGroupIndex = 0;  // TODO(scolu): set to determine hit group index
            instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
            instanceDesc.BottomLevelAS = renderNode->GetAccelStruct();
            instanceDescs.push_back(instanceDesc);
        }
    }
    else  // Build TLAS with a simple triangle when no valid instances are available
    {
        CreateBLASPlaceHolder();
        
        NGIInstanceDesc instanceDesc{};
        instanceDesc.InstanceID = 0;
        instanceDesc.InstanceMask = 0xff;
        instanceDesc.InstanceContribToHitGroupIndex = 0;
        instanceDesc.Flag = NGIInstanceFlag::ForceOpaque;
        instanceDesc.BottomLevelAS = mSingleTriangleBLAS.get();
        
        instanceDescs.push_back(instanceDesc);
    }

    NGIAccelStructBuildFlag buildFlags = NGIAccelStructBuildFlag::PreferFastTrace | NGIAccelStructBuildFlag::AllowUpdate;

    UInt64 scratchBufferSize;
    if (!mAccelStruct || mAccelStruct->GetTopLevelMaxInstanceCount() != instanceDescs.size())
    {
        DestroyTLAS();
        CreateTLAS();
        scratchBufferSize = mAccelStruct->mSizeInfo.BuildScratchSize;
    }
    else
    {
        buildFlags |= NGIAccelStructBuildFlag::PerformUpdate;
        scratchBufferSize = mAccelStruct->mSizeInfo.UpdateScratchSize;
    }
    
    NGIBufferDesc scratchBufferDesc{
        .Size = scratchBufferSize,
        .Usage = NGIBufferUsage::RayTracingScratchBuffer
    };
    auto [scratchBuffer, scratchBufferState] =
        mRendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc,
                                                                 "TLAS Build Scratch Buffer",
                                                                 true,
                                                                 false);

    UInt32 uploadBufferSize = mAccelStruct->GetTopLevelUploadBufferSize();
    ScratchBufferWrap uploadInstanceBuffer = mRendererSystem->GetScratchBuffer()->AllocateScratch(NGIBufferUsage::AccelStructBuildInputBuffer, uploadBufferSize);

    // Wait for blas build finish
    NGIMemoryBarrier barrier{.StateBefore = NGIResourceState::AccelStructWrite,
        .StateAfter = NGIResourceState::AccelStructBuildBLASBit | NGIResourceState::ShaderStageBitMask};
    mCmd->MemBarrier(&barrier);

    mCmd->BuildTopLevelAccelStructFromBuffer(mAccelStruct.get(),
                                            uploadInstanceBuffer,
                                            uploadInstanceBuffer.GetNGIOffset(),
                                            instanceDescs.data(),
                                            instanceDescs.size(),
                                            buildFlags,
                                            scratchBuffer);
    mCmd->TLASBarrier(mAccelStruct.get());

    mCmd->EndDebugRegion();
}

void RayTracingScene::CreateTLAS()
{
    NGIAccelStructDesc tlasDesc;
    tlasDesc.IsTopLevel = true;
    tlasDesc.TopLevelMaxInstance = std::max(static_cast<UInt32>(mRayTracingInstances.size()), 1u);
    tlasDesc.BuildFlag = NGIAccelStructBuildFlag::AllowUpdate | NGIAccelStructBuildFlag::PreferFastTrace;
    tlasDesc.DebugName = "TLASDataBuffer";
    mAccelStruct.reset(GetNGIDevicePtr()->CreateAccelStruct(tlasDesc));
}

void RayTracingScene::DestroyTLAS()
{
    if (mAccelStruct)
    {
        EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->DestroyNGIObject(std::move(mAccelStruct));
        mAccelStruct = nullptr;
    }
}

void RayTracingScene::BuildBLASes()
{
    QUICK_SCOPED_CPU_TIMING("RayTracingScene::BuildBLASes");

    mCmd->BeginDebugRegion("BuildBLASes");
    
    for (auto& [entity, changeType] : mFrameUpdateEntitySet)
    {
        if (changeType == RayTracingInstanceChangeType::Create)
        {
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
            else  // for mesh create only build blas for the first time
            {
                continue;
            }
        }
        else if (changeType == RayTracingInstanceChangeType::Mesh || changeType == RayTracingInstanceChangeType::Skeleton)
        {
            // build blas for mesh change and skeleton mesh even if it's already in the list
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
        }
        else if (changeType == RayTracingInstanceChangeType::Material)
        {
            if (!mRayTracingInstancesSet.contains(entity))
            {
                mRayTracingInstances.push_back({entity, changeType});
                mRayTracingInstancesSet.insert(entity);
            }
            else
            {
                continue;
            }
        }
        
        if (!IsEntityValidForRayTracing(entity))
        {
            continue;
        }

        auto renderNodeComp = mRenderWorld->GetComponent<RenderNodeComponentR>(entity);
        RenderNode* renderNode = renderNodeComp.Read()->mRenderNode.get();
        renderNode->BuildAccelStruct(*this, mRenderWorld, entity);
        Assert(renderNode->GetAccelStruct());
    }

    mCmd->EndDebugRegion();
}

}
