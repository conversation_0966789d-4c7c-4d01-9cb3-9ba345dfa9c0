#pragma once
#include "Platform/PlatformConfigMacro.h"

#if CROSSENGINE_WIN
#pragma warning(disable : 4251)
#pragma warning(disable : 4275)
#ifdef RenderEngine_EXPORTS
#define RENDER_ENGINE_API __declspec(dllexport)
#else
#define RENDER_ENGINE_API __declspec(dllimport)
#endif
#else
#ifdef __GNUC__
#define RENDER_ENGINE_API __attribute__((visibility("default")))
#else
#define RENDER_ENGINE_API
#endif
#endif