#pragma once
#include "Resource/Resource.h"
#include "Resource/Fx.h"
#include "CrossSchema/ShaderDefines_generated.h"
#include "RenderEngine/Texture/GPUTexture.h"
#include "NativeGraphicsInterface/NGI.h"
#include "NativeGraphicsInterface/NGITransientHeap.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderMaterialVirtualTextureStack.h"
#include <shared_mutex>

namespace cross
{
class MaterialManager;

class RENDER_ENGINE_API MaterialR : public IMaterialR
{
    friend class resource::Material;
    friend class resource::Fx;

public:
    auto& GetPassDataBlock()
    {
        return mPassDataBlockMap;
    }
    
    UInt32 GetMaterialIndex() const override
    {
        return mMaterialIndex;
    }

    void SetMaterialIndex(UInt32 Index) override
    {
        mMaterialIndex = Index;
    }
    
    struct MaterialRenderState
    {
        const resource::Shader::ProgramDesc* mProgram{ nullptr };
        UInt16 mRenderGroup{ 0 };
        const void* mShaderConstants{ nullptr };
        const NGIDepthStencilStateDesc* mDepthStencilState{ nullptr };
        const NGIRasterizationStateDesc* mRaterizationState{ nullptr };
        const NGIBlendStateDesc* mBlendState{ nullptr };
        NGIDynamicStateDesc mDynamicState;
        const PropertySet* mObjCtx{ nullptr };
        NGIResourceGroup* mMtlResourceGroup;

        NGIResourceGroup* GetResourceBinding() const { return mMtlResourceGroup; }
    };
#pragma warning(push)
#pragma warning(disable : 4324)
    struct PassDataBlock
    {
        bool mEnable;
        NGIRasterizationStateDesc mRasterizationState = resource::GetDefaultRasterizationState();
        NGIBlendStateDesc mBlendState = resource::GetDefaultBlendState();
        NGIDepthStencilStateDesc mDepthStencilState = resource::GetDefaultDepthStencilState();
        NGIDynamicStateDesc mDynamicState{};
        CrossSchema::ShaderVersion mShaderVersion;
        UInt16 mRenderGroup;
    };
#pragma warning(pop)
    struct VTConstParameters
    {
        // we  only support 4 layers
        // const int EBALED_VT_LAYER_NUM = VIRTUALTEXTURE_SPACE_MAXLAYERS / 2;
        UInt4 PackedVTUniform;
        // we need a layerremapping between updated pagetable and the layer in shader;
        Float4 LayerRemap;
        Float4 PageTableUniform0;
        Float4 PageTableUniform1;
        Float4 PageTableUniform2;

        void Init(cross::IAllocatedVirtualTexture* allocatedVT);
        void FillParameters(cross::IAllocatedVirtualTexture* allocatedVT, UInt8 vtLayerIndex);
        void FillPropertiesSet(PropertySet& VTProps);
    };

    struct ConstBufferData
    {
        NGIResourceBinding resourceBinding;
        NGIBuffer* buffer;
        resource::ShaderBufferLayout layout;
    };

    MaterialR(resource::MaterialInterface* material);

    ~MaterialR();

    void SetName(const std::string& name) override
    {
        mName = name;
    }

    const std::string GetName() const override
    {
        return mName;
    }

protected:
    virtual UInt8 SetValueProp(NameID const& name, const float* val, size_t len) override;

    virtual UInt8 SetValueProp(NameID const& name, const UInt8* val, size_t len) override;

    virtual UInt8 SetBool(NameID const& name, bool val) override;

    UInt64 FillBuffer(resource::ShaderBufferLayout const* layout, UInt8* buffer) const;
    void FillBuffer(resource::ShaderBufferLayout const* layout, StagingBufferWrap bufferWrap, SizeType bufferWrapOffset = 0);

    ResourceWeakPtr<resource::Fx> mFxPtr;

    std::unordered_map<NameID, GPUTexture*> mTextureProps;

    std::unordered_map<NameID, NGISampler*> mSamplerStates;

    std::unordered_map<NameID, std::vector<UInt8>> mValueProps;

    NGIResourceGroup* GetResourceBinding(resource::Shader::ProgramDesc* program);

    const void* GetShaderConst(resource::Shader::ProgramDesc* program);
    
    void UploadMaterialManager(MaterialManager& materialManager) const;
    
private:
    UInt8 SetValue(NameID const& name, const UInt8 * val, size_t len);

    //Direct NGI Cache
    std::unordered_map<NameID, PassDataBlock> mPassDataBlockMap;

    std::shared_mutex mResourceBlockMutex;
    std::shared_mutex mSpecConstBlockMutex;
    UInt64 mPropertyVersion = 0;
   
    std::unique_ptr<RenderMaterialVirtualTextureStack> mVTStack;

    VTConstParameters mCachedVtParameters;

    std::string mName; // for debug

    resource::MaterialInterface* mMaterial;

    std::map<GPUProtoType::ID, ConstBufferData> mVariantConstBufferCache;
    NGIBuffer* mConstantBuffer = nullptr;
    NGITransientHeapAllocation mConstantBufferAllocation;
    SInt32 mConstantBufferAllocationIndex = -1;
    UInt32 mVariantByteSize = 0;
    bool bMtlCached;
    std::mutex mMtlCacheMutex;
    
    UInt32 mMaterialIndex = 0xffffffff;
    
    NameID inline GetValidPassID(NameID const& passID) const
    {
        return (!passID.IsValid() ? mFxPtr.lock()->GetDefaultPass().name : passID);
    }

    const NGIDepthStencilStateDesc& GetDepthStencilState(NameID const& passID) const;

    const NGIRasterizationStateDesc& GetRasterizationState(NameID const& passID) const;

    const NGIBlendStateDesc& GetBlendState(NameID const& passID) const;

    void CreateVTStack() override;

    //virtual bool IsCacheable() override { return mConstBufferCache.canBeCached; }
    virtual bool IsCached() override { return bMtlCached; }
    virtual void UpdateCache() override;
    //virtual int VarientsByteSize() override;

    friend class MaterialManager;
    
public:
    resource::Fx* GetFx() const;

    void Initialize(FxPtr fxPtr) override;

    void NotifyChange() override;

    void FillShaderKey(resource::ShaderVariationKey& key);

    auto& GetTextureProps()  { return mTextureProps;}

#pragma region Render_Side_Core_Interface

    MaterialRenderState GetMaterialRenderState(NameID const& passID, const PropertySet* ctx = nullptr, const PropertySet* passCtx = nullptr);

    std::vector<NameID> GetPasses() const;

    resource::Shader* GetShader(NameID const& passID) const
    {
        return GetFx()->GetShader(passID);
    }
        
    bool HasPass(NameID const& passID) const
    {
        return GetFx()->HasPass(passID);
    }

    virtual void SetEnable(NameID const& passID, bool val) override;

    bool IsEnable(NameID const& passID) const;

    virtual void SetRenderGroup(NameID const& passID, UInt16 val) override;

    UInt16 GetRenderGroup(NameID const& passID) const;
#pragma endregion

#pragma region Property_Accessor
    std::optional<bool> GetBool(NameID const& name) const;

    std::optional<Float4> GetVector(NameID const& name) const;

    std::optional<float> GetFloat(NameID const& name) const;

    bool GetPropertyRawData(NameID const& name, std::size_t size, void* data) const;

    GPUTexture* GetTexture(NameID const& name);

    virtual void SetTexture(NameID const& name, cross::IGPUTexture*) override;
    void SetTexture(NameID const& name, GPUTexture* texture);
    virtual void SetSamplerState(NameID const& name, const SamplerState& samplerState) override;

    virtual void SetDepthStencilState(NameID const& passID, const NGIDepthStencilStateDesc& depthStencilState) override;
    virtual void SetRasterizationState(NameID const& passID, const NGIRasterizationStateDesc& rasterizerState) override;
    virtual void SetBlendState(NameID const& passID, const NGIBlendStateDesc& blendDesc) override;
    virtual void SetDynamicState(NameID const& passID, const NGIDynamicStateDesc& desc) override;
#pragma endregion

    void SetVtParameters(cross::IAllocatedVirtualTexture* allocatedVT, const std::vector<UInt8>& vtLayerIndices);
};

}
