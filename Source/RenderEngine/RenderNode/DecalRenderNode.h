#pragma once

#include "RenderNode.h"

namespace cross
{
    class DecalRenderNode : public RenderNode
    {
        friend class DecalSystemR;

        RenderNodeType GetType() const override
        {
            return RenderNodeType::Decal;
        }
        virtual void GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const override;

    private:
        UInt32 mPriority;
        RenderGeometry* mGeometry;
        MaterialR* mMaterial;
        PropertySet mProperties = PropertySet(nullptr);
        TRSRenderMatrixType mDecalWorldTransform;
    };
}
