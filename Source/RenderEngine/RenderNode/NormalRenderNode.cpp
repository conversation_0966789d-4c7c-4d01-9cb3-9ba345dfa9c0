#include "NormalRenderNode.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/FoliageSystemR.h"

namespace cross {
void NormalRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    auto* foliageSys = params.renderWorld->GetRenderSystem<FoliageSystemR>();

    auto* fa = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();

    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    Float3 cameraWorldPos{cameraView.mInvertViewMatrix.m30, cameraView.mInvertViewMatrix.m31, cameraView.mInvertViewMatrix.m32};

    Float3 entityBoundingSphereInLODCameraTileSpace = entityData.boundingSphereCenter;
#ifdef CE_USE_DOUBLE_TRANSFORM
    entityBoundingSphereInLODCameraTileSpace += (cameraView.mCameraTilePosition - cameraViewLODSel.mCameraTilePosition) * LENGTH_PER_TILE_F;
#endif
    Float2 outSize;
    CameraUtility::GetScreenSize(outSize, BoundingSphere(entityBoundingSphereInLODCameraTileSpace, entityData.boundingSphereRadius), params.cameraLODSel->GetCameraView(), params.cameraLODSel->GetProjectionMode());
    float screenRadius = outSize.y;

    auto distanceToCamera = Float3::Distance(entityData.worldPosition, cameraWorldPos);

    // for each render node in entity
    for (auto& subMeshRenderNode : mRenderNodes)
    {
        auto GenerateDrawUnitForOneLOD = [&](UInt8 lodIndex, SInt32 cullingGUIDStart, SInt32 objectIndexStart) {
            if (auto* geometry = subMeshRenderNode.GetRenderGeometry(lodIndex); geometry)
            {
                auto* material = subMeshRenderNode.GetMaterial(static_cast<RenderMaterialTag>(lodIndex));

                auto& drawUnitsDesc = params.drawUnitsDesc;
                const auto& passName = drawUnitsDesc.TagName;

                MaterialR* finalMaterial;
                UInt16 renderGroup;
                if (params.IsDrawable(material, finalMaterial, renderGroup))
                {
                    REDDrawUnitFlag flags{};

                    if (subMeshRenderNode.IsReceiveDecals())
                    {
                        flags |= REDDrawUnitFlag::ReceiveDecal;
                    }

                    UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, subMeshRenderNode.GetProperties(), passName, flags, lodIndex);

                    void* custumData = nullptr;

                    if (mType == RenderNodeType::Foliage)
                    {
                        auto* lodInfo = fa->Allocate<FoliageInfo>(1, FRAME_STAGE_RENDER);
                        lodInfo->LODCount = subMeshRenderNode.GetLODCount();
                        lodInfo->LODBias = GetLoDBias();
                        lodInfo->LODSetting = mLodSetting;
                        lodInfo->mFoliageClusterKey = this;
                        lodInfo->BoundingSphere = subMeshRenderNode.GetBoundingSphere();
                        lodInfo->RenderNode = &subMeshRenderNode;
                        lodInfo->LODIndex = lodIndex;
                        lodInfo->ObjectIndexStart = objectIndexStart;
                        custumData = lodInfo;
                    }

                    if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
                    {
                        if (mType != RenderNodeType::Foliage)
                        {
                            collector.AddOpaqueIsolatedDrawUnit(renderGroup, geometry, finalMaterial, flags, lodIndex, 0, nullptr, &subMeshRenderNode.mProperties);
                        }
                        else
                        {
                            auto& drawUnit = collector.AddOpaqueBatchableDrawUnit(renderGroup, 
                                                                                  geometry, 
                                                                                  finalMaterial, 
                                                                                  stateBucketID, 
                                                                                  nullptr, 
                                                                                  nullptr, 
                                                                                  flags, 
                                                                                  custumData);
                            drawUnit.mSingleRun = {mInstanceCount, -1, -1};
                        }
                    }
                    else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI)) ||
                             math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
                    {
                        if (mType != RenderNodeType::Foliage)
                        {
                            collector.AddTransparentIsolatedDrawUnit(distanceToCamera, renderGroup, geometry, finalMaterial, flags, lodIndex, 0, nullptr, &subMeshRenderNode.mProperties);
                        }
                        else
                        {
                            auto& drawUnit =
                                collector.AddTransparentBatchableDrawUnit(distanceToCamera, 
                                                                          renderGroup, 
                                                                          geometry, 
                                                                          finalMaterial, 
                                                                          stateBucketID, 
                                                                          nullptr, 
                                                                          nullptr, 
                                                                          flags, 
                                                                          custumData);
                            drawUnit.mSingleRun = {mInstanceCount, -1, -1};
                        }
                    }
                }
            }
        };

        switch (mType)
        {
        case RenderNodeType::Normal:
        {
            const auto lodIndex = GetLODIndex(&screenRadius, subMeshRenderNode.GetLODCount(), mLodSetting);
            // smaller than culledHeight, do not generate drawUnit;
            if (lodIndex == UINT8_MAX)
            {
                break;
            }

            GenerateDrawUnitForOneLOD(lodIndex, -1, -1);
            break;
        }
        case RenderNodeType::Foliage:
        {
            auto& info = foliageSys->mClusterOffsetMap[this];
            for (UInt8 lodIndex = 0; lodIndex < subMeshRenderNode.GetLODCount(); ++lodIndex)
            {
                GenerateDrawUnitForOneLOD(lodIndex, info.startInstance, info.startInstance);
            }
            break;
        }
        default:
            break;
        }
    }
}

}   // namespace cross