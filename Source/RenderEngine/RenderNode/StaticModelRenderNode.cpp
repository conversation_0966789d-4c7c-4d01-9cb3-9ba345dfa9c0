#include "EnginePrefix.h"
#include "StaticModelRenderNode.h"

#include "MaterialBP/material_shared.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/GPUScene/GPUSceneData.h"
#include "RenderEngine/RenderPropertySystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"
#include "NativeGraphicsInterface/Statistics.h"

#include "Resource/ResourceManager.h"

namespace cross {
void StaticModelRenderNode::AllocateGPUScene(GPUScene& GPUScene)
{
    AllocateCullingData(GPUScene, 1);

    AllocateObjectAndPrimitiveData(GPUScene, 1);
}

void StaticModelRenderNode::GenerateDrawUnits(const GenerateDrawUnitsParams& params, REDDrawUnitCollector& collector) const
{
    SCOPED_CPU_TIMING(GroupRendering, "StaticModel::GenerateDrawUnits");
    auto& entityData = params.entityData;
    auto& cameraView = params.camera->GetCameraView();
    auto& cameraViewLODSel = params.cameraLODSel->GetCameraView();

    // FIXME: Seems like mRenderModel.mBoundingBox cannot match to params.entityData.boundingBox in the camera tile space
    // Float2 radiusAndDistacne = GetBoundingBoxScreenRadiusAndDistance(mRenderModel.mBoundingBox, params);
    Float2 radiusAndDistance = GetEntityScreenRadiusAndDistance(params);
    auto selectedLODIndex = GetLODIndex(&radiusAndDistance.x, static_cast<UInt8>(mRenderModel.mLODModels.size()), mLodSetting);

    // smaller than culledHeight, do not generate drawUnit;
    if (selectedLODIndex == UINT8_MAX)
        return;

    if (selectedLODIndex >= static_cast<UInt8>(mRenderModel.mLODModels.size()))
        return;

    auto* streamingManager = gResourceMgr.mStreamingMgr;
    if (streamingManager->IsStreamingEnabled())
    {
        if (mRenderModel.mReadyForStreaming)
        {
            int desiredLODCount = static_cast<int>(mRenderModel.mLODModels.size() - selectedLODIndex);
            Assert(desiredLODCount <= mRenderModel.mLODModels.size());
            desiredLODCount = std::clamp(desiredLODCount, 0, static_cast<int>(mRenderModel.mLODModels.size()));
            streamingManager->UpdateDesiredLODCount(params.entityData.entity, desiredLODCount);
            int residentLODCount = streamingManager->GetResidentLODCount(params.entityData.entity);
            if (residentLODCount >= 0)
            {
                selectedLODIndex = std::max(static_cast<UInt8>(mRenderModel.mLODModels.size() - residentLODCount), selectedLODIndex);
                selectedLODIndex = std::clamp(selectedLODIndex, static_cast<UInt8>(0), static_cast<UInt8>(mRenderModel.mLODModels.size() - 1));
            }
        }
    }

    // for each render node in entity
    for (auto& subModel : mRenderModel.mLODModels[selectedLODIndex].mSubModels)
    {
        auto distanceToCamera = GetBoundingBoxScreenRadiusAndDistance(subModel.mBoundingBox, params).y;

#ifdef ENABLE_SCENE_OCTREE
        // Skip the invisible SubModel
        if (entityData.invisibleSubModelSet != nullptr && (*entityData.invisibleSubModelSet).find(subModel.mObjectCullingGUIDStart) != (*entityData.invisibleSubModelSet).end())
        {
            continue;
        }
#endif

        if (mDistanceCulling && mDistanceCulling->IsCulled(GetScaledCulledDistance(distanceToCamera)))
        {
            continue;
        }

        auto* geometry = subModel.mGeometry;
        if (!geometry)
        {
            continue;
        }

        auto* material = subModel.mMaterial;
        const auto& passAllocs = subModel.PassAllocs;

        auto& drawUnitsDesc = params.drawUnitsDesc;
        const auto& passName = drawUnitsDesc.TagName;

        MaterialR* finalMaterial;
        UInt16 renderGroup;
        if (params.IsDrawable(material, finalMaterial, renderGroup))
        {
            // UInt32 frameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
            // std::string name = geometry->GetGeometryPacket()->GetVertexStream(0)->GetGpuBuffer()->GetDebugName();
            // EngineMeshStatistics::GetInstance().SetMeshLOD(frameCount, name, selectedLODIndex);

            REDDrawUnitFlag flags{};

            if (mRenderModel.mReceiveDecals)
            {
                flags |= REDDrawUnitFlag::ReceiveDecal;
            }

            if (mNeedReverseCullingFace)
            {
                flags |= REDDrawUnitFlag::ReverseFaceOrder;
            }

            UInt32 stateBucketID = REDCulling::CalculateDrawUnitStateBucketID(geometry, finalMaterial, mObjectProperties, passName, flags, selectedLODIndex);

            const GPUScenePassAlloc* GPUSceneAlloc = nullptr;
            const bool isMaterialVersion2 = params.drawUnitsDesc.OverrideMaterial == nullptr && subModel.GPUSceneAlloc.ObjectAlloc.IndexStart != -1;

            if (isMaterialVersion2)
            {
                GPUSceneAlloc = &subModel.GPUSceneAlloc;
            }
            else
            {
                if (auto passAllocItr = passAllocs.find(passName); passAllocItr != passAllocs.end())
                {
                    GPUSceneAlloc = &passAllocItr->second;
                }
            }

            if (math::InBetween(gRenderGroupOpaque, renderGroup, static_cast<UInt16>(gRenderGroupTransparent - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc.IndexStart != -1 ? GPUSceneAlloc->PrimitiveAlloc.BufferView : nullptr;

                    auto& drawUnit = collector.AddOpaqueBatchableDrawUnit(renderGroup, 
                                                                          geometry, 
                                                                          finalMaterial, 
                                                                          stateBucketID, 
                                                                          GPUSceneAlloc->ObjectAlloc.BufferView, 
                                                                          primitiveBufferView, 
                                                                          flags);
                    drawUnit.mSingleRun = {1, subModel.mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc.IndexStart};
                }
                else
                {
                    collector.AddOpaqueIsolatedDrawUnit(renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
            else if (math::InBetween(gRenderGroupTransparent, renderGroup, static_cast<UInt16>(gRenderGroupUI - 1u)) || math::InBetween(gRenderGroupGizmoWithSceneDepth, renderGroup, static_cast<UInt16>(gRenderGroupOverlay - 1u)))
            {
                if (GPUSceneAlloc)
                {
                    auto* primitiveBufferView = GPUSceneAlloc->PrimitiveAlloc.IndexStart != -1 ? GPUSceneAlloc->PrimitiveAlloc.BufferView : nullptr;

                    auto& drawUnit = collector.AddTransparentBatchableDrawUnit(distanceToCamera, 
                                                              renderGroup, 
                                                              geometry, 
                                                              finalMaterial, 
                                                              stateBucketID, 
                                                              GPUSceneAlloc->ObjectAlloc.BufferView, 
                                                              primitiveBufferView, flags);
                    drawUnit.mSingleRun = {1, subModel.mObjectCullingGUIDStart, GPUSceneAlloc->ObjectAlloc.IndexStart};
                }
                else
                {
                    collector.AddTransparentIsolatedDrawUnit(distanceToCamera, renderGroup, geometry, finalMaterial, flags, selectedLODIndex, stateBucketID);
                }
            }
        }
    }
}

void StaticModelRenderNode::UploadGPUScene(GPUScene& GPUScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    QUICK_SCOPED_CPU_TIMING("StaticModelRenderNode::UploadGPUScene");

    auto* renderPropertySys = renderWorld->GetRenderSystem<RenderPropertySystemR>();
    auto renderPropertyComp = renderWorld->GetComponent<RenderPropertyComponentR>(entity);

    UInt32 currEngineFrameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();

    // Upload PrimitiveCullingData
    {
        CullingProperty cullingProperty = renderPropertySys->GetCullingProperty(renderPropertyComp.Read());
        bool isAlwaysVisible = cullingProperty == CullingProperty::CULLING_PROPERTY_ALWAYS_VISIBLE;
        UInt32 flag = isAlwaysVisible ? 1u : 0u;

        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                auto boundingBox = GetBoundingBox(renderWorld, subModel, entity);
                PrimitiveCullingData cullingData{
                    boundingBox.GetCenter(),
                    currEngineFrameCount,
                    boundingBox.GetExtent(),
                    flag,
                    mWorldTransform.TilePosition,
                };

                void* copyDstBufferPtr = GPUScene.UploadData(sizeof(PrimitiveCullingData), subModel.mPrimitiveCullingGUIDStart * sizeof(PrimitiveCullingData));
                memcpy(copyDstBufferPtr, &cullingData, sizeof(PrimitiveCullingData));
            }
        }
    }

    // Upload ObjectCullingData
    {
        // for each SubModel
        for (auto& singleLODModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : singleLODModel.mSubModels)
            {
                ObjectCullingData cullingData{
                    mWorldTransform.RelativeMatrix,
                    static_cast<UInt32>(subModel.mPrimitiveCullingGUIDStart),
                };

                void* copyDstBufferPtr = GPUScene.UploadData(sizeof(ObjectCullingData), subModel.mObjectCullingGUIDStart * sizeof(ObjectCullingData));
                memcpy(copyDstBufferPtr, &cullingData, sizeof(ObjectCullingData));
            }
        }
    }

    // Upload ObjectData
    {
        auto pool = EngineGlobal::Inst().GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
        PropertySet objProps{pool};

        objProps.SetProperty(BuiltInProperty::ce_World, mWorldTransform.RelativeMatrix);
        objProps.SetProperty(NAME_ID("ce_RootToWorld"), mWorldTransform.RelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_PreWorld, mWorldTransform.PreRelativeMatrix);
        objProps.SetProperty(BuiltInProperty::ce_TilePosition, mWorldTransform.TilePosition);
        objProps.SetProperty(BuiltInProperty::ce_PreTilePosition, mWorldTransform.PreTilePosition);
        objProps.SetProperty(BuiltInProperty::ce_InvWorld, mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(NAME_ID("ce_WorldToRoot"), mWorldTransform.RelativeMatrix.Inverted());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose());
        objProps.SetProperty(BuiltInProperty::ce_InvTransposeInvWorld, mWorldTransform.RelativeMatrix.Inverted().Transpose().Inverted());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsCenter"), mRenderModel.mBoundingBox.GetCenter());
        objProps.SetProperty(NAME_ID("ce_LocalBoundsExtent"), mRenderModel.mBoundingBox.GetExtent());

        auto uploadObjectGPUScene = [&](GPUSceneAlloc& alloc, UInt32 primitiveIndex, UInt32 objectCullingGUID) {
            if (alloc.IndexStart == -1)
            {
                return;
            }
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            objProps.SetProperty(BuiltInProperty::ce_PrimitiveIndex, primitiveIndex);
            objProps.SetProperty(BuiltInProperty::ce_ObjectCullingGUID, objectCullingGUID);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        };

        auto uploadPrimitiveGPUScene = [&GPUScene, &objProps](GPUSceneAlloc& alloc, UInt32 matIndex) {
            if (alloc.IndexStart == -1)
            {
                return;
            }
            auto byteStride = alloc.ProtoType->Layout.ByteSize;
            auto* date = GPUScene.UploadData(byteStride * alloc.IndexCount, byteStride * alloc.IndexStart);
            objProps.SetProperty(BuiltInProperty::ce_MaterialIndex, matIndex);
            objProps.FillBuffer(alloc.ProtoType->Layout, date);
        };

        for (auto& lodModel : mRenderModel.mLODModels)
        {
            for (auto& subModel : lodModel.mSubModels)
            {
                if (subModel.mGeometry && subModel.mMaterial)
                {
                    uploadPrimitiveGPUScene(subModel.GPUSceneAlloc.PrimitiveAlloc, subModel.mMaterial->GetMaterialIndex());
                    for (auto& passAlloc : subModel.PassAllocs)
                    {
                        uploadPrimitiveGPUScene(passAlloc.second.PrimitiveAlloc, subModel.mMaterial->GetMaterialIndex());
                    }

                    uploadObjectGPUScene(subModel.GPUSceneAlloc.ObjectAlloc, subModel.GPUSceneAlloc.PrimitiveAlloc.IndexStart, subModel.mObjectCullingGUIDStart);
                    for (auto& passAlloc : subModel.PassAllocs)
                    {
                        uploadObjectGPUScene(passAlloc.second.ObjectAlloc, passAlloc.second.PrimitiveAlloc.IndexStart, subModel.mObjectCullingGUIDStart);
                    }
                }
            }
        }
    }
}

bool StaticModelRenderNode::IsValidForRayTracing() const
{
    return !mRenderModel.mLODModels.empty() && !mRenderModel.mLODModels[0].mSubModels.empty() && mRenderModel.mLODModels[0].mSubModels[0].mGeometry;
}

void StaticModelRenderNode::BuildAccelStruct(RayTracingScene& RayTracingScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    if (mRenderModel.mLODModels.size() == 0)
    {
        return;
    }
    
    SingleLODModel& targetModel = mRenderModel.mLODModels[0];
    MeshR* parentMesh = targetModel.mParentMesh;
    Assert(parentMesh);

    RendererSystemR* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    NGICommandList* cmd = rendererSystem->GetAccelStructCmd();

    if (parentMesh->GetAccelStruct())
    {
        return;
    }
    
    std::vector<NGIGeometryDesc> geometries(targetModel.mSubModels.size(), NGIGeometryDesc{});

    NGIAccelStructDesc blasDesc;
    blasDesc.DebugName = "";
    blasDesc.IsTopLevel = false;
    blasDesc.BuildFlag = NGIAccelStructBuildFlag::PreferFastTrace;
    for (UInt32 index = 0; index < targetModel.mSubModels.size(); index++)
    {
        auto& geoDesc = geometries[index];
        auto& subModel = targetModel.mSubModels[index];

        geoDesc.GeometryType = NGIGeometryType::Triangle;
        geoDesc.UseTransform = false;
        geoDesc.Flag = NGIGeometryFlag::Opaque;

        auto& triangles = geoDesc.GeometryData.Triangle;
        RenderGeometry* renderGeometry = subModel.mGeometry;
        GeometryPacket* geoPacket = subModel.mGeometry->GetGeometryPacket();
        triangles.IndexBuffer = geoPacket->GetIndexStream()->GetGpuBuffer();
        triangles.IndexFormat = geoPacket->GetIndexFormat();
        triangles.IndexCount = renderGeometry->GetIndexCount();
        UInt32 indexSizeInBytes = triangles.IndexFormat == IndexFormat_UInt16 ? 2 : 4;
        triangles.IndexOffset = geoPacket->GetIndexStream()->GetStreamOffset() + renderGeometry->GetIndexStart() * indexSizeInBytes;
        
        for (UInt8 streamIndex = 0; streamIndex < geoPacket->GetStreamCount(); streamIndex++)
        {
            const VertexStreamLayout& streamLayout = geoPacket->GetInputLayout().GetVertexStreamLayout(streamIndex);
            // One channel per stream for bindless access
            Assert(streamLayout.GetChannelCount() == 1);
            const VertexChannelLayout& channelLayout = streamLayout.GetChannelLayout(0);
            if (channelLayout.mChannelName == VertexChannel::Position0 && channelLayout.mFormat == VertexFormat::Float3)
            {
                triangles.VertexBuffer = geoPacket->GetVertexStream(streamIndex)->GetGpuBuffer();
                triangles.VertexFormat = GraphicsFormat::R32G32B32_SFloat;
                triangles.VertexOffset = renderGeometry->GetVertexStart() * streamLayout.GetVertexStride();
                triangles.VertexStride = streamLayout.GetVertexStride();
                triangles.VertexCount = renderGeometry->GetVertexCount();
                break;
            }
        }
        Assert(triangles.IndexBuffer && triangles.VertexBuffer);
    }
    blasDesc.BottomLevelGeometries = geometries;
    
    NGIAccelStruct* accel = GetNGIDevicePtr()->CreateAccelStruct(blasDesc);
    parentMesh->SetAccelStruct(accel);

    RayTracingScene.GetMemoryTracker().Add(TrackedMemoryUsage::StaticBLAS, accel->mSizeInfo.AccelStructSize);

    UInt64 scratchBufferSize = accel->mSizeInfo.BuildScratchSize;
    NGIBufferDesc scratchBufferDesc{
        .Size = scratchBufferSize,
        .Usage = NGIBufferUsage::RayTracingScratchBuffer
    };
    auto [scratchBuffer, scratchBufferState] =
        rendererSystem->GetTransientResourceManager()->AllocateBuffer(scratchBufferDesc, "BLAS Build Scratch Buffer", true, false);
    cmd->BuildBottomLevelAccelStruct(accel,
        blasDesc.BottomLevelGeometries.data(),
        blasDesc.BottomLevelGeometries.size(),
        blasDesc.BuildFlag,
        scratchBuffer);
}

NGIAccelStruct* StaticModelRenderNode::GetAccelStruct() const
{
    return mRenderModel.mLODModels[0].mParentMesh->GetAccelStruct();
}

std::vector<SubInstanceData> StaticModelRenderNode::GetSubInstanceData()
{
    std::vector<SubInstanceData> ret(mRenderModel.mLODModels[0].mSubModels.size());

    for (UInt32 index = 0; index < ret.size(); index++)
    {
        RenderGeometry* geometry = mRenderModel.mLODModels[0].mSubModels[index].mGeometry;
        const GeometryData& geoData = geometry->GetGeometryData();

        std::memcpy(&ret[index], &geoData, sizeof(UInt32) * 10);
        // ret[index].PosBufferIndex = geoData.PosBufferIndex;
        // ret[index].ColorBufferIndex = geoData.ColorBufferIndex;
        // ret[index].NormalBufferIndex = geoData.NormalBufferIndex;
        // ret[index].TangentBufferIndex = geoData.TangentBufferIndex;
        // ret[index].BinormalBufferIndex = geoData.BinormalBufferIndex;
        // ret[index].UVBufferIndex = geoData.UVBufferIndex;
        // ret[index].UV1BufferIndex = geoData.UV1BufferIndex;
        // ret[index].IndexBufferIndex = geoData.IndexBufferIndex;
        // ret[index].VertexStart = geoData.VertexStart;
        // ret[index].IndexStart = geoData.IndexStart;
        
        ret[index].ObjectIndex = mRenderModel.mLODModels[0].mSubModels[index].mObjectCullingGUIDStart;
        ret[index].MaterialIndex = mRenderModel.mLODModels[0].mSubModels[index].mMaterial->GetMaterialIndex();
    }

    return ret;
}

std::vector<HitGroupInfo> StaticModelRenderNode::GetHitGroupInfos()
{
    std::vector<HitGroupInfo> ret(mRenderModel.mLODModels[0].mSubModels.size());

    for (UInt32 index = 0; index < ret.size(); index++)
    {
        ret[index].Material = mRenderModel.mLODModels[0].mSubModels[index].mMaterial;
        auto fx = mRenderModel.mLODModels[0].mSubModels[index].mMaterial->GetFx();
        if (fx->IsMaterialSupportRayTracing())
        {
            ret[index].ClosestHitShader = mRenderModel.mLODModels[0].mSubModels[index].mMaterial->GetFx()->GetClosestHitShaderPtr();
            ret[index].AnyHitShader = mRenderModel.mLODModels[0].mSubModels[index].mMaterial->GetFx()->GetAnyHitShaderPtr();
        }
    }

    return ret;
}

bool StaticModelRenderNode::IsMaterialExist(RayTracingScene* rayTracingScene) const
{
    auto& targetLodModel = mRenderModel.mLODModels[0].mSubModels;
    for (auto& subModel : targetLodModel)
    {
        if (!rayTracingScene->IsMaterialExist(subModel.mMaterial))
        {
            return false;
        }
    }
    return true;
}

bool StaticModelRenderNode::ShouldInvokeAnyHitShader() const
{
    auto& targetLodModel = mRenderModel.mLODModels[0].mSubModels;
    for (auto& subModel : targetLodModel)
    {
        MaterialBlendMode blendMode =  subModel.mMaterial->GetFx()->GetMaterialDefines().BlendMode;
        if (blendMode == MaterialBlendMode::Masked || blendMode == MaterialBlendMode::Translucent)
        {
            return true;
        }
    }
    return false;
}

void StaticModelRenderNode::FreeStellarMeshScene(StellarMesh::StellarMeshScene& StellarMeshScene, ecs::EntityID entity)
{
    if (!mRenderModel.mStellarMeshEnabled)
        return;

    StellarMeshScene.Remove(entity);
}

void StaticModelRenderNode::UploadStellarMeshScene(StellarMesh::StellarMeshScene& StellarMeshScene, RenderWorld* renderWorld, ecs::EntityID entity)
{
    if (!mRenderModel.mStellarMeshEnabled)
        return;

    // Upload StellarMeshScene Data
    UInt32 flags = mRenderModel.mReceiveDecals ? static_cast<UInt32>(StellarMesh::RenderFlag::bReceiveDecals) : 0u;
    StellarMesh::StellarSceneData data{
        mWorldTransform.RelativeMatrix,
        mRenderModel.mBoundingBox.GetCenter(),
        flags,
        mRenderModel.mBoundingBox.GetExtent(),
        mRenderModel.mMeshConstantID,
    };

    std::vector<MaterialR*> materials{};
    if (static_cast<UInt32>(mRenderModel.mLODModels.size()) > 0)
    {
        for (auto subModel : mRenderModel.mLODModels[0].mSubModels)
        {
            materials.emplace_back(subModel.mMaterial);
        }
    }
    
    //LOG_DEBUG("AddOrUpdate Entity: {}, RenderWorld: {}", entity.GetValue(), renderWorld->GetName().GetString());
    StellarMeshScene.AddOrUpdate(entity, data, materials);
}

// void StaticModelRenderNode::ProcessMaterialForStellarMesh(StellarMesh::StellarMeshMaterialScene& StellarMeshMaterialScene)
// {
//     if (mRenderModel.mLODModels.empty())
//         return;
//     for (auto& subModel : mRenderModel.mLODModels[0].mSubModels)
//     {
//         auto mat = subModel.mMaterial;
//         
//     }
// }

}   // namespace cross