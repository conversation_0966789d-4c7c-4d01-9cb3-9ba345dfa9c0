#include "ApplyDBufferData.h"
#include "CECommon/Common/EngineGlobal.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"

namespace cross {
void ApplyDBufferDataPassSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_MATERIAL(ApplyDBufferDataMtl);
}

PassDesc ApplyDBufferDataPass::GetPassDesc()
{
    return PassDesc("ApplyDBufferDataPass", "Apply DBuffer data to GBuffer.");
}

static REDColorTargetDesc AllocateColorTargetDesc(RenderingExecutionDescriptor* RED, REDTextureView* inGBufferView)
{
    return {RED->AllocateTextureView(inGBufferView->mTexture,
                                     NGITextureViewDesc{NGITextureUsage::RenderTarget,
                                                        inGBufferView->mDesc.Format,
                                                        NGITextureType::Texture2D,
                                                        {
                                                            NGITextureAspect::Color,
                                                            0,
                                                            1,
                                                            0,
                                                            1,
                                                        }}),
            NGILoadOp::Load,
            NGIStoreOp::Store,
            NGIClearValue{{0, 0, 0, 1}}};
}

static NGIDepthStencilStateDesc GetDepthStencilState()
{
    NGIDepthStencilStateDesc result;
    result.EnableDepth = false;
    result.EnableDepthWrite = false;
    result.DepthCompareOp = NGIComparisonOp::Always;

    const NGIStencilOperation Stencil = {StencilOp::Keep, StencilOp::Keep, StencilOp::Keep, NGIComparisonOp::Equal};
    result.EnableStencil = true;
    result.StencilReadMask = STENCIL_APPLY_DBUFFER_DECAL_BIT_MASK;
    result.StencilWriteMask = STENCIL_NONE_BIT_MASK;
    result.FrontFace = Stencil;
    result.BackFace = Stencil;
    return result;
}

bool ApplyDBufferDataPass::ExecuteImp(const GameContext& gameContext, REDTextureView* InDepthStencilView, std::array<REDTextureView*, 3>& InDBufferViews, std::array<REDTextureView*, 3>& InCopiedBufferViews,
                                      std::array<REDTextureView*, 4>& OutGBufferViews)
{
    RenderingExecutionDescriptor* RED = gameContext.mRenderPipeline->RED();

    REDColorTargetDesc colorTargetDesc[3] = {AllocateColorTargetDesc(RED, OutGBufferViews[0]), AllocateColorTargetDesc(RED, OutGBufferViews[1]), AllocateColorTargetDesc(RED, OutGBufferViews[2])};
    NGIRenderPassTargetIndex colorTargetIndex[3] = {NGIRenderPassTargetIndex::Target0, NGIRenderPassTargetIndex::Target1, NGIRenderPassTargetIndex::Target2};

   REDDepthStencilTargetDesc depthStencilTarget{
       InDepthStencilView,
       NGILoadOp::Load,
       NGIStoreOp::Store,
       NGILoadOp::Load,
       NGIStoreOp::Store,
   };

    RED->BeginRenderPass("ApplyDBufferDataPass", 3, colorTargetDesc, &depthStencilTarget);
    auto* subPass = RED->AllocateSubRenderPass("ApplyDBufferDataSubPass", 0, nullptr, 3, colorTargetIndex, REDPassFlagBit::StencilReadOnly | REDPassFlagBit::NeedStencil);

    ApplyDBufferDataPSParam param;
    param.CopiedGBuffer0.mValue = InCopiedBufferViews[0];
    param.CopiedGBuffer1.mValue = InCopiedBufferViews[1];
    param.CopiedGBuffer2.mValue = InCopiedBufferViews[2];
    param.DBuffer0.mValue = InDBufferViews[0];
    param.DBuffer1.mValue = InDBufferViews[1];
    param.DBuffer2.mValue = InDBufferViews[2];
    SetPassParameters(param, subPass);

    NGIDynamicStateDesc ref;
    ref.StencilReference = STENCIL_APPLY_DBUFFER_DECAL_BIT_MASK;

    REDDrawScreenQuad ApplyDBufferScreenQuad{
        mSetting.ApplyDBufferDataMtlR,
        ApplyDBufferDataPassID,
        std::nullopt, std::nullopt, 
        GetDepthStencilState(),
        ref
    };
    subPass->DrawScreenQuad(ApplyDBufferScreenQuad);

    RED->EndRenderPass();
    return true;
}

}   // namespace cross