#pragma once
#include "PassBase.h"

namespace cross {
class CE<PERSON>eta(Editor) RENDER_ENGINE_API BlitLowResSceneColorSettings : public PassSetting
{
public:
    virtual void Initialize() override;
};

class RENDER_ENGINE_API BlitLowResSceneColor
{
public:
    constexpr std::string_view GetPassName()
    {
        return std::string_view("BlitLowResSceneColor");
    }
    void FillInput(const GameContext& gameContext);
    void Execute(const GameContext& gameContext);
    MaterialR* mPostMtl = nullptr;
    bool noCulling = true;
    REDTextureView* mInputColor = nullptr;
    BlitLowResSceneColorSettings mSetting;
};
}   // namespace cross 