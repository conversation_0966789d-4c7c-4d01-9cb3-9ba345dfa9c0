#include "MassiveLightsShadow.h"
#include "RenderEngine/RenderPipeline/RenderPipelineR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "Resource/AssetStreaming.h"
#include "RenderEngine/TerrainSystemR.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "RenderEngine/FoliageSystemR.h"

namespace cross {

#define MAX_LIGHTS_PER_CLUSTER   15

void MassiveLightsShadowSettings::Initialize()
{
    LOAD_RENDER_PIPELINE_COMPUTE_SHADER(MassiveLightsShadowComputeShader);
}

bool MassiveLightsShadow::GetEnable()
{
    return mSetting.enable;
}

bool MassiveLightsShadow::GetCluster()
{
    return mSetting.Cluster;
}

bool MassiveLightsShadow::GetTransmission()
{
    return mSetting.Transmission;
}

bool MassiveLightsShadow::GetCullBack()
{
    return mSetting.CullBack;
}

bool MassiveLightsShadow::GetDebug()
{
    return mSetting.Debug;
}

unsigned int MassiveLightsShadow::GetClusterCount()
{
    return static_cast<UInt32>(mSetting.ClusterCount);
}

float MassiveLightsShadow::GetClusterOffsetX()
{
    return mSetting.ClusterOffsetX;
}

float MassiveLightsShadow::GetClusterOffsetZ()
{
    return mSetting.ClusterOffsetZ;
}

float MassiveLightsShadow::GetClusterSize()
{
    return mSetting.ClusterSize;
}

UInt32 MassiveLightsShadow::GetFrameCountModed()
{
    mFrameCountModed = (mFrameCountModed + 1) % 8; return mFrameCountModed; 
}

float MassiveLightsShadow::GetThresholdScale()
{
    return mSetting.ThresholdScale;
}

float MassiveLightsShadow::GetStepUV()
{
    return mSetting.StepUV;
}

int MassiveLightsShadow::GetMinSteps()
{
    return mSetting.MinSteps;
}

int MassiveLightsShadow::GetMaxSteps()
{
    return mSetting.MaxSteps;
}

float MassiveLightsShadow::GetStepMultiplier() 
{
    return mSetting.StepMultiplier;
}

float MassiveLightsShadow::GetExcludedDistance()
{
    return mSetting.ExcludedDistance;
}

float MassiveLightsShadow::GetFadeInStartDistance()
{
    return mSetting.FadeInStartDistance;
}

float MassiveLightsShadow::GetFadeInEndDistance()
{
    return mSetting.FadeInEndDistance;
}

REDBuffer* MassiveLightsShadow::UpdateCommonContext(const GameContext& gameContext, REDPass* pass, int frameCount)
{
    IRenderPipeline* renderPipeline = gameContext.mRenderPipeline;
    RenderingExecutionDescriptor* RED = renderPipeline->GetRenderingExecutionDescriptor();

    REDBufferView* rayDebugBufferView = nullptr;
    REDBuffer* rayDebugBuffer = mRayDebug.InitRayDebugBuffer(RED, rayDebugBufferView, "MassiveLightsShadow");

    pass->SetProperty(NAME_ID("_DebugRayInfoOutput"), rayDebugBufferView);
    pass->SetProperty(NAME_ID("ENABLE_SSRT_RAY_DEBUG"), mSetting.EnableDebugRay);

    bool enable = GetEnable();
    bool cluster = GetCluster();
    bool transmission = GetTransmission();
    bool cullBack = GetCullBack();
    bool debug = GetDebug();

    unsigned int clusterCount = GetClusterCount();
    float clusterOffsetX = GetClusterOffsetX();
    float clusterOffsetZ = GetClusterOffsetZ();
    float clusterSize = GetClusterSize();
    float inverseClusterSize = 1.0f / clusterSize;
    if (enable == false)
    {
        clusterCount = 0;
    }
    float globalUnitScale = EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->GlobalUnitScale;
    float thresholdScale = GetThresholdScale();
    float stepUV = GetStepUV();
    int minSteps = GetMinSteps();
    int maxSteps = GetMaxSteps();
    float stepMultiplier = GetStepMultiplier();
    float excludedDistance = GetExcludedDistance();
    float fadeInStartDistance = GetFadeInStartDistance();
    float fadeInEndDistance = GetFadeInEndDistance();

    pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_ENABLE"), enable);
    pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_CLUSTER"), cluster);
    pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_TRANSMISSION"), transmission);
    pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_CULL_BACK"), cullBack);
    pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_DEBUG"), debug);

    pass->SetProperty(NAME_ID("_ClusterCount"), clusterCount);
    pass->SetProperty(NAME_ID("_ClusterOffsetX"), clusterOffsetX);
    pass->SetProperty(NAME_ID("_ClusterOffsetZ"), clusterOffsetZ);
    pass->SetProperty(NAME_ID("_ClusterSize"), clusterSize);
    pass->SetProperty(NAME_ID("_InverseClusterSize"), inverseClusterSize);
    pass->SetProperty(NAME_ID("_StateFrameIndexModed"), frameCount % 8);
    pass->SetProperty(NAME_ID("_GlobalUnitScale"), globalUnitScale);
    pass->SetProperty(NAME_ID("_ThresholdScale"), thresholdScale);
    pass->SetProperty(NAME_ID("_StepUV"), stepUV);
    pass->SetProperty(NAME_ID("_MinSteps"), minSteps);
    pass->SetProperty(NAME_ID("_MaxSteps"), maxSteps);
    pass->SetProperty(NAME_ID("_StepMultiplier"), stepMultiplier);
    pass->SetProperty(NAME_ID("_ExcludedDistance"), excludedDistance);
    pass->SetProperty(NAME_ID("_FadeInStartDistance"), fadeInStartDistance);
    pass->SetProperty(NAME_ID("_FadeInEndDistance"), fadeInEndDistance);

    return rayDebugBuffer;
}

void MassiveLightsShadow::AssembleRayDebug(const GameContext& gameContext, REDBuffer* rayDebugBuffer)
{
    IRenderPipeline* renderPipeline = gameContext.mRenderPipeline;
    RenderingExecutionDescriptor* RED = renderPipeline->GetRenderingExecutionDescriptor();
    if (mSetting.EnableDebugRay)
    {
        mRayDebug.AssembleRayDebug(gameContext, RED, rayDebugBuffer, mSetting.EnableDebugRayFreeze, "MassiveLightsShadow");
    }
}

bool MassiveLightsShadow::ExecuteImp(const GameContext& gameContext, REDTextureView* depthOnlyView, REDTextureView* gBuffer1MapView, REDTextureView* gBuffer2MapView,
    NGIBufferView* lightInstanceView, REDBufferView*& clustersBufferView, REDBufferView*& shadowBufferView, UInt32 frameCount, RenderWorld* renderWorld)
{
    IRenderPipeline* renderPipeline = gameContext.mRenderPipeline;
    RenderingExecutionDescriptor* RED = renderPipeline->GetRenderingExecutionDescriptor();

    bool cullBack = mSetting.CullBack;

    UInt32 clusterCount = GetClusterCount();
    if (!mSetting.enable)
    {
        clusterCount = 1;
    }
    UInt32 totalClusterCount = clusterCount * clusterCount;
    UInt32 clusterStructSize = sizeof(Float4) + sizeof(UInt32) + sizeof(UInt32) * MAX_LIGHTS_PER_CLUSTER;
    UInt32 bufferSize = totalClusterCount * clusterStructSize;

    auto* clustersBuffer = RED->AllocateBuffer("MassiveLightsClusters", NGIBufferDesc{ bufferSize, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer });
    auto* clustersBufferViewRW = RED->AllocateBufferView(clustersBuffer, NGIBufferViewDesc{ NGIBufferUsage::RWStructuredBuffer, 0, clustersBuffer->mDesc.Size, GraphicsFormat::Unknown, clusterStructSize });
    clustersBufferView = RED->AllocateBufferView(clustersBuffer, NGIBufferViewDesc{ NGIBufferUsage::StructuredBuffer, 0, clustersBuffer->mDesc.Size, GraphicsFormat::Unknown, clusterStructSize });

    auto gameViewWidth = static_cast<UInt16>(depthOnlyView->mTexture->mDesc.Width);
    auto gameViewHeight = static_cast<UInt16>(depthOnlyView->mTexture->mDesc.Height);
    if (!mSetting.enable)
    {
        gameViewWidth = 1;
        gameViewHeight = 1;
    }

    UInt32 bufferSize1 = gameViewWidth * gameViewHeight * sizeof(UInt32);
    auto* shadowBuffer = RED->AllocateBuffer("MassiveLightsShadow", NGIBufferDesc{ bufferSize1, NGIBufferUsage::StructuredBuffer | NGIBufferUsage::RWStructuredBuffer });
    auto* shadowBufferViewRW = RED->AllocateBufferView(shadowBuffer, NGIBufferViewDesc{ NGIBufferUsage::RWStructuredBuffer, 0, shadowBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32) });
    shadowBufferView = RED->AllocateBufferView(shadowBuffer, NGIBufferViewDesc{ NGIBufferUsage::StructuredBuffer, 0, shadowBuffer->mDesc.Size, GraphicsFormat::Unknown, sizeof(UInt32) });

    if (!mSetting.enable)
        return true;

    auto* foliageSystem = gameContext.mRenderWorld->GetRenderSystem<FoliageSystemR>();
    unsigned int lightCount = foliageSystem->GetLightCount();

    if (GetCluster())
    {
        {
            float clusterOffsetX = GetClusterOffsetX();
            float clusterOffsetZ = GetClusterOffsetZ();
            float clusterSize = GetClusterSize();

            auto* pass = RED->AllocatePass("CalculateMassiveLightsClusters");
            pass->SetProperty(NAME_ID("_MassiveLightsClustersRW"), clustersBufferViewRW);
            pass->SetProperty(NAME_ID("_LightInstanceData"), lightInstanceView);
            pass->SetProperty(NAME_ID("_LightCount"), lightCount);
            pass->SetProperty(NAME_ID("_ClusterCount"), clusterCount);
            pass->SetProperty(NAME_ID("_TotalClusterCount"), totalClusterCount);
            pass->SetProperty(NAME_ID("_ClusterOffsetX"), clusterOffsetX);
            pass->SetProperty(NAME_ID("_ClusterOffsetZ"), clusterOffsetZ);
            pass->SetProperty(NAME_ID("_ClusterSize"), clusterSize);

            pass->Dispatch(mSetting.MassiveLightsShadowComputeShaderR, "CalculateMassiveLightsClusters", totalClusterCount / 64, 1, 1);
        }

        {
            auto* pass = RED->AllocatePass("CalculateMassiveLightsShadow");

            REDBuffer* rayDebugBuffer = UpdateCommonContext(gameContext, pass, GetFrameCountModed());

            pass->SetProperty(NAME_ID("MASSIVE_LIGHTS_SHADOW_CULL_BACK"), cullBack);
            pass->SetProperty(NAME_ID("_MassiveLightsClusters"), clustersBufferView);
            pass->SetProperty(NAME_ID("_MassiveLightsShadowRW"), shadowBufferViewRW);
            pass->SetProperty(NAME_ID("_LightInstanceData"), lightInstanceView);
            pass->SetProperty(NAME_ID("_LightCount"), lightCount);
            pass->SetProperty(NAME_ID("_ClusterCount"), clusterCount);
            pass->SetProperty(NAME_ID("_TotalClusterCount"), totalClusterCount);
            pass->SetProperty(NAME_ID("_DepthMap"), depthOnlyView);
            pass->SetProperty(NAME_ID("_GBuffer1Map"), gBuffer1MapView);
            pass->SetProperty(NAME_ID("_GBuffer2Map"), gBuffer2MapView);
                
            pass->Dispatch(mSetting.MassiveLightsShadowComputeShaderR, "CalculateMassiveLightsShadow", (gameViewWidth + 7) / 8, (gameViewHeight + 7) / 8, 1);

            AssembleRayDebug(gameContext, rayDebugBuffer);
        }
   }

    return true;
}

}   // namespace cross