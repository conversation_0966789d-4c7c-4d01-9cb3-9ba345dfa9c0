#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"

namespace cross
{
    class SSAAResolvePassSetting : public PassSetting
    {
    public:
        SSAAResolvePassSetting() {}
        
        CE_Virtual_Serialize_Deserialize

        RENDER_PIPELINE_RESOURCE(ComputeShader, SSAAResolveShader, "Shader/Features/SSAA/SSAAResolve.compute.nda", "SSAAResolve", "", "");

        virtual void Initialize() override;
    };

    class RENDER_ENGINE_API SSAAResolve : public PassBase<SSAAResolvePassSetting, SSAAResolve>
    {
    public:
        static PassDesc GetPassDesc();
    protected:
        bool ExecuteImp(const GameContext& gameContext, REDTextureView* colorInputView, REDTextureView* colorOutView);

        friend PassBase<SSAAResolvePassSetting, SSAAResolve>;
    };
}

