#include "SeparateTranslucencyBlend.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
namespace cross 
{
void SeparateTranslucencyBlendPass::FillInput(const GameContext& gameContext)
{

    auto ffspipeline = dynamic_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
    if (!ffspipeline)
        return;
    mSetting = ffspipeline->GetSetting()->mSeparateTranslucencyBlendSetting;
}

void SeparateTranslucencyBlendPass::Execute(const GameContext& gameContext)
{
    NGIClearValue clearValue{{0, 0, 0, 0}};
    REDTextureView* colorInView = gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::LastPassColor>();
    REDColorTargetDesc renderTarget{colorInView, NGILoadOp::Load, NGIStoreOp::Store, clearValue};
    NGIRenderPassTargetIndex renderTargetIndex = static_cast<NGIRenderPassTargetIndex>(0);

    gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->BeginRenderPass("FFX_FSR2_BLEND_TRANSLUCENCY", 1, &renderTarget, nullptr);
    auto pass = gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->AllocateSubRenderPass("FFX_FSR2_BLEND_TRANSLUCENCY", 0, nullptr, 1, &renderTargetIndex, REDPassFlagBit{0});
    pass->SetProperty(NAME_ID("FFX_FSR2_OPTION_JITTER_CANCEL"), true);
    pass->SetProperty(NAME_ID("fUVJitterOffset"), gameContext.mRenderPipeline->GetJitterData()->GetOffsetInUVSpace());
    pass->SetProperty(NAME_ID("r_separate_translucency"), gameContext.mRenderPipeline->GetBuiltInTexture<PassSemanticName::SeperateTranslucencyView>(), NGIResourceState::PixelShaderShaderResource);

    REDDrawScreenQuad drawInfo{gameContext.mRenderPipeline->GetPostProcessMtl(), "ffx_fsr2_blend_translucency"};
    auto blendStateDesc = NGIBlendStateDesc{false, false, 1};
    blendStateDesc.TargetBlendState[0] = {true, false, BlendFactor::One, BlendFactor::SrcAlpha, NGIBlendOp::Add, BlendFactor::Zero, BlendFactor::SrcAlpha, NGIBlendOp::Add, static_cast<LogicOp>(0), ColorMask::All};
    drawInfo.BlendStateDesc = blendStateDesc;
    pass->DrawScreenQuad(drawInfo);
    gameContext.mRenderPipeline->GetRenderingExecutionDescriptor()->EndRenderPass();
}
}   // namespace cross
