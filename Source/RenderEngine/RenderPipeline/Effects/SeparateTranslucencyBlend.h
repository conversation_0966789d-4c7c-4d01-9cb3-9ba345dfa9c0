#pragma once
#include "PassBase.h"

 namespace cross {
 class CEMeta(Editor, PartOf(FFSRenderPipelineSetting)) RENDER_ENGINE_API SeparateTranslucencyBlendSetting : public PassSetting
 {
 public:
    SeparateTranslucencyBlendSetting()
    {
        enable = true;
    }
 };

 class RENDER_ENGINE_API SeparateTranslucencyBlendPass
 {
 public:
     constexpr std::string_view GetPassName()
     {
         return std::string_view("SeparateTranslucencyBlend");
     }
     void FillInput(const GameContext& gameContext);
     void Execute(const GameContext& gameContext);     
 private:
     SeparateTranslucencyBlendSetting mSetting = {};
 };
 }   // namespace cross
