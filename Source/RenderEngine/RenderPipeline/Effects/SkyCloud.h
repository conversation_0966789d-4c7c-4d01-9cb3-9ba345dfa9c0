#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/ComputeShaderR.h"
#include "RenderEngine/CloudSetting.h"
namespace cross {

// Return CloudColor and DownsampleDepth for Apply
// mCloudIntersectionTex
struct CloudResources
{
    REDTextureView* mCloudColorTex{nullptr};
    REDTextureView* mDownsampleDepthTex{nullptr};
    REDTextureView* mCloudIntersectionTex{nullptr};

    REDBufferView* mUnderCloudStatBuff{nullptr};

    void Reset()
    {
        mCloudColorTex = mDownsampleDepthTex = mCloudIntersectionTex = nullptr;
        mUnderCloudStatBuff = nullptr;
    }
};

enum CLOUD_PIPE_METHOD
{
    SIMPLE,
    SPATIAL_AND_TEMPORAL
};


enum class CEMeta(Editor) CloudTemporalDown : UInt8
{
    None = 0,
    Level_2x = 1,
    Level_4x = 2
};

enum class CEMeta(Editor) CloudSpatialDown : UInt8
{
    None = 0,
    Level_2x = 1,
    Level_4x = 2,
};


struct CEMeta(Editor) RENDER_ENGINE_API SkyCloudSetting : public PassSetting
{
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    CloudSpatialDown SpatialDown{CloudSpatialDown::Level_2x};
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    CloudTemporalDown TemporalDown{CloudTemporalDown::Level_2x};
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    uint32_t DebugOption{0};
    CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = ""))
    CloudsTransparentOrder CloudTransOrder{CloudsTransparentOrder::RenderAfterTransparent};


    RENDER_PIPELINE_RESOURCE(Material, SkyCloudMateial, "Material/SkyCloudMaterial.nda", "SkyCloudMateial", "Materials use for render SkyCloud", "")

    RENDER_PIPELINE_RESOURCE(Texture, BlueNoise, "Texture/SkyCloud/LDR_RGBA_0.nda", "BlueNoiseTexture", "BlueNoise For Texture", "");

    CE_Virtual_Serialize_Deserialize;
    void Initialize() override;
};


class RENDER_ENGINE_API SkyCloud : public PassBase<SkyCloudSetting, SkyCloud>
{
public:
    SkyCloud(){};
    static PassDesc GetPassDesc();

public:
    RenderWorld* mWorld;
    RenderingExecutionDescriptor* mRED = nullptr;
    MaterialR* mPostMtl;

    void SetScreenSize(uint32_t width, uint32_t height);
    bool ExecuteImp(const GameContext& gameContext, REDTextureView* depthOnlyView, CloudResources& cloudRes, CLOUD_PIPE_METHOD method = CLOUD_PIPE_METHOD::SPATIAL_AND_TEMPORAL);
    bool AssembleFogAndCloudApplyPass(const GameContext& gameContext, REDTextureView* depthOnlyView, REDTextureView* volumetricFogView, REDTextureView*& sceneView, CloudResources& cloudRes, bool isSkyLight);

    void AssembleUnderCloudStatPass(const GameContext& gameContext, REDBufferView*& outUnderCloud);

private:
    void DoTexturesSizeCalculation(const GameContext& gameContext, CLOUD_PIPE_METHOD& method);
    void AssembleCloudIntersectionPass(const GameContext& gameContext, REDTextureView*& cloudIntersectionTex);
    void AssembleDownSampleDepthPass(const GameContext& gameContext, REDTextureView* depthOnlyIn, REDTextureView* cloudIntersectionTex, REDTextureView*& downsampleDepthTex);
    void AssembleCloudRayMarchingPass(const GameContext& gameContext, REDTextureView*, REDTextureView*&, REDTextureView*&);
    void AssembleTemporalReconsPass(const GameContext& gameContext, REDTextureView*, REDTextureView*, REDTextureView*, REDTextureView*);


    void ValidateHistoryTexture(UInt32 width, UInt32 height);
    void AllocateUnderCloudStatBuffer();
    void UpdateCloudShapeRelatedParamsContext(RenderContext& context);
    void UpdateCloudLightsIndices(const GameContext& gameContext, RenderContext& context);

protected:

    REDUniquePtr<cross::REDResidentTexture> mHistoryColorTex;
    REDUniquePtr<cross::REDResidentTextureView> mHistoryColorTexView;


    REDUniquePtr<cross::REDResidentBufferView> mUnderCloudTestBufferView;

    REDUniquePtr<REDResidentBuffer> mUnderCloudBuffer;
    REDUniquePtr<REDResidentBufferView> mUnderCloudBufferUAV;
    REDUniquePtr<REDResidentBufferView> mUnderCloudBufferSRV;

    NGITextureUsage mNGIRTUsage = NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::UnorderedAccess;
    Float4 mFullTexSize;
    Float4 mTempReconsTexSize;
    Float4 mRaymarchingTexSize;
    UInt32 mSpatialDownRatio{1};
    UInt32 mTempDownRatio{1};

    uint32_t mWidth = 0;
    uint32_t mHeight = 0;
    uint64_t mFrameNum;

    friend PassBase<SkyCloudSetting, SkyCloud>;
};
}   // namespace cross