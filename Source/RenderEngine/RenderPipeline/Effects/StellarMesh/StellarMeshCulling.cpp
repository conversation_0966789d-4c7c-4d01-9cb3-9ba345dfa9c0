#include "StellarMeshCulling.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipeline.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/StellarMesh/StellarMeshSceneSetting.h"
#include "RenderEngine/StellarMesh/Util.h"
#include "RenderPipeline/Effects/StellarMesh/StellarMeshDefs.h"

#include <tuple>

namespace cross {

struct StellarMeshCullingPipeline::Impl
{
    Impl() = default;
    ~Impl() = default;

    RenderWorld* mRenderWorld;
    FFSWorldRenderPipeline* mWorldRenderPipeline;
    FFSRenderPipeline* mRenderPipeline;
    FFSRenderPipelineSetting const* mRenderPipelineSetting;
    RenderingExecutionDescriptor* mRED;
    StellarMeshCullingPipelineSetting const* mSetting;

    void Initialize(const GameContext& gameContext)
    {
        mRenderWorld = gameContext.mRenderWorld;
        mRenderPipeline = static_cast<FFSRenderPipeline*>(gameContext.mRenderPipeline);
        mWorldRenderPipeline = static_cast<FFSWorldRenderPipeline*>(mRenderPipeline->GetWorldRenderPipeline());
        mRED = mRenderPipeline->GetRenderingExecutionDescriptor();
        mRenderPipelineSetting = mRenderPipeline->GetSetting();
        mSetting = &(mRenderPipelineSetting->mStellarMeshRenderPipelineSetting.mStellarMeshCullingPipelineSetting);
    }

    void InitArg(StellarMeshCullingContext& cullingContext)
    {
        mRED->BeginRegion("StellarMeshInitArgs");

        auto initArgPass = mRED->AllocatePass("InitArg");

        initArgPass->SetProperty(NAME_ID("slotCounter"), cullingContext.mSlotCounterBufferUAV);
        initArgPass->SetProperty(NAME_ID("queueState"), cullingContext.mQueueStateBufferUAV);

        initArgPass->Dispatch(mSetting->InitArgR, "InitArg", 1, 1, 1);

        mRED->EndRegion();
    }

    void ExecuteImpl(StellarMeshCullingPipeline const& io, StellarMeshCullingContext& cullingContext)
    {
        mRED->BeginRegion("StellarMeshCulling");
        auto const& config = mRenderPipelineSetting->mStellarMeshRenderPipelineSetting.mStellarMeshCullingPipelineSetting;

        auto stellarMeshManager = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetStellarMeshManager();
        auto stellarMeshScene   = mWorldRenderPipeline->GetStellarMeshScene();

        auto instanceCulling = [&] {
            auto instanceCullingPass  = mRED->AllocatePass("InstanceCulling");

            auto totalInstances       = stellarMeshScene->GetStellarMeshSceneCount();
            auto instanceBuffer       = stellarMeshScene->GetStellarSceneBufferView();
            auto meshConstantsBuffer  = stellarMeshManager->GetMeshConstantBufferView();

            instanceCullingPass->SetProperty(NAME_ID("totalInstances")       , totalInstances);
            instanceCullingPass->SetProperty(NAME_ID("instances")            , instanceBuffer);
            instanceCullingPass->SetProperty(NAME_ID("meshConstants")        , meshConstantsBuffer);
            instanceCullingPass->SetProperty(NAME_ID("queueState")           , cullingContext.mQueueStateBufferUAV);
            instanceCullingPass->SetProperty(NAME_ID("candidateClusterQueue"), cullingContext.mCandidateClusterQueueBufferUAV);

            instanceCullingPass->Dispatch(mSetting->InstanceCullingR, "InstanceCulling", GetGroupCountWarpped(totalInstances, 64));
        };

        auto initClusterCullingArg = [&] {
            auto initClusterCullingArgPass = mRED->AllocatePass("InitializeClusterCullingArg");

            initClusterCullingArgPass->SetProperty(NAME_ID("queueState")           , cullingContext.mQueueStateBufferSRV);
            initClusterCullingArgPass->SetProperty(NAME_ID("clusterCullingDrawArg"), cullingContext.mClusterCullingDrawArgBufferUAV);

            initClusterCullingArgPass->Dispatch(mSetting->InitClusterCullingArgR, "InitializeClusterCullingArg", 1, 1, 1);
        };

        auto clusterCulling = [&] {
            auto clusterCullingPass = mRED->AllocatePass("ClusterCulling");

            auto clusterBuffer = stellarMeshManager->GetClusterBufferView();

            clusterCullingPass->SetProperty(NAME_ID("clusters")             , clusterBuffer);
            clusterCullingPass->SetProperty(NAME_ID("queueState")           , cullingContext.mQueueStateBufferSRV);
            clusterCullingPass->SetProperty(NAME_ID("candidateClusterQueue"), cullingContext.mCandidateClusterQueueBufferSRV);
            clusterCullingPass->SetProperty(NAME_ID("visibleCluster")       , io.mOutput.mVisibleClusterBufferUAV);
            clusterCullingPass->SetProperty(NAME_ID("slotCounter")          , cullingContext.mSlotCounterBufferUAV);

            clusterCullingPass->DispatchIndirect(mSetting->ClusterCullingR, "ClusterCulling", cullingContext.mClusterCullingDrawArgBuffer, 0);
        };

        auto calculateRasterizerArg = [&] {
            auto calculateRasterizerArgPass = mRED->AllocatePass("CalculateRasterizerArg");

            calculateRasterizerArgPass->SetProperty(NAME_ID("slotCounter"    ), cullingContext.mSlotCounterBufferSRV);
            calculateRasterizerArgPass->SetProperty(NAME_ID("clusterDrawArgs"), io.mOutput.mClusterDrawArgBufferUAV);

            calculateRasterizerArgPass->Dispatch(mSetting->CalculateRasterizerArgR, "CalculateRasterizerArg", 1, 1, 1);
        };

        instanceCulling();
        initClusterCullingArg();
        clusterCulling();
        calculateRasterizerArg();
        mRED->EndRegion();
    }
};

StellarMeshCullingPipeline::StellarMeshCullingPipeline()
    :pImpl(std::make_unique<Impl>())
{
    
}

StellarMeshCullingPipeline::~StellarMeshCullingPipeline() = default;

void StellarMeshCullingPipeline::InitArg(const GameContext& gameContext, StellarMeshCullingContext& cullingContext)
{
    pImpl->Initialize(gameContext);
    pImpl->InitArg(cullingContext);
}


void StellarMeshCullingPipeline::Execute(const GameContext& gameContext, StellarMeshCullingContext& cullingContext)
{
    pImpl->ExecuteImpl(*this, cullingContext);
}

}   // namespace cross
