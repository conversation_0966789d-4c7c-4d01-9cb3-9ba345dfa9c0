#pragma once

#include "PassBase.h"

namespace cross
{
    class CEMeta(Editor, PartOf(FFSRenderPipelineSetting)) RENDER_ENGINE_API TemporalAntiAliasingSetting : public PassSetting
    {
    public:
        CE_Virtual_Serialize_Deserialize;

        TemporalAntiAliasingSetting()
        {
            enable = false;
        }

        virtual void Initialize() override {}

        CEMeta(Ser<PERSON><PERSON>, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Do not use motion vector, for static scenes only."))
        bool WithoutMotionVector{ false };

        CEMeta(Serialize, Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Enables an extra sharpen pass."))
        bool EnableSharpenPass{ true };

        CEMeta(Serialize, Editor, Reflect, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Base weight for current frame.", bKeyFrame = true))
        float CurrentFrameWeight{ .05f };        
    };
    struct TemporalAntiAliasingInput
    {
        REDTextureView* color = nullptr;
        REDTextureView* depth = nullptr;
        REDTextureView* vbuffer = nullptr;
        REDTextureView* eyeadaptation = nullptr;
        void GenerateInputData(const GameContext& gameContext);
    };
    struct TemporalAntiAliasingOutput
    {
        REDTextureView* outputcolor = nullptr;
        void SetOutputData(const GameContext& gameContext);
    };
    struct CEMeta(PartOf(FFSRenderPipeline)) TemporalAntiAliasingContext
    {
        REDTexture* mHistoryBuffer = nullptr;
        REDTextureView* mHistoryBufferRTView = nullptr;
    };
    struct RENDER_ENGINE_API TemporalAntiAliasing
    {
    public:
        constexpr std::string_view GetPassName() 
        {
            return std::string_view("TAA");
        }
        void FillInput(const GameContext& gameContext);

        void Execute(const GameContext& gameContext);

        REDTextureView* mVelocityBufferView{};

        UInt32 mSceneColorTextureWidth{};
        UInt32 mSceneColorTextureHeight{};

        GraphicsFormat mDepthStencilFormat{};
        MaterialR* mPostProcessMaterial{};
        TemporalAntiAliasingSetting mSetting;
        TemporalAntiAliasingInput mInput;
        TemporalAntiAliasingOutput mOutput;
        TemporalAntiAliasingContext* mContext;
    };
}
