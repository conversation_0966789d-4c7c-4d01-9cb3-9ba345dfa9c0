#pragma once

#include "PassBase.h"

#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
#include "RenderEngine/RenderWorldConst.h"
#include "SubSampleShading.h"

namespace cross {
struct ShadowProperties;
class RenderCamera;
class SmartGIPass;

class TileBasedDefferedLightingSettings : public PassSetting
{
public:
    CE_Virtual_Serialize_Deserialize;

    RENDER_PIPELINE_RESOURCE(Material, DeferredShadingMtl, "Material/TileBasedDeferred.nda", "Deferred Shading Material", "", "");

    RENDER_PIPELINE_RESOURCE(Material, StencilDeferredShadingMtl, "Material/StencilDeferred.nda", "Deferred Shading Material", "", "");

    RENDER_PIPELINE_RESOURCE(Material, StencilDeferredShadingAGAAMtl, "Material/StencilDeferred_AGAA.nda", "Deferred Shading with AGAA Material", "", "");

    CEMeta(Editor, EditorPropertyInfo(PropertyType = "Auto", ToolTips = "Visualization Helper")) ViewModeVisualizeType Visualize = ViewModeVisualizeType::Lit;

    TileBasedDefferedLightingSettings() {}

    void Initialize() override;
};

class RENDER_ENGINE_API TileBasedDefferedLighting : public PassBase<TileBasedDefferedLightingSettings, TileBasedDefferedLighting>
{
public:
    TileBasedDefferedLighting();
    UInt32 mlightGridSizeX;
    UInt32 mlightGridSizeY;
    UInt32 mlightGridSizeZ;
    Float3 mBOS;
    UInt32 mLightGridPixelSizeShift;
    UInt32 mLocalLightsNum;
    std::vector<std::pair<ecs::EntityID, UInt32>> mDirectionalLightList;
    RenderWorld* mWorld;
    ViewModeVisualizeType mVisualize;

protected:
    bool ExecuteImp(const GameContext& gameContext,
                    UInt32 RTWidth,
                    UInt32 RTHeight,
                    const ShadowProperties* shadowData,
                    REDTextureView* ShadowMaskView,
                    bool enableMSAA,
                    SubSampleShadingSetting subSampleShadingSetting,
                    SubSampleShading& SubSpamleShadingData,
                    std::array<REDTextureView*, 4> gBufferView,
                    REDTextureView* depthStencilView,
                    REDTextureView* depthOnlyView,
                    REDTextureView* contactShadowView,
                    REDTextureView* SceneColor,
                    bool contactShadowTransmissionEnable,
                    REDTextureView* depthPyramid,
                    REDTextureView* GPassDepthPyramid,
                    SmartGIPass* smartGIPass,
                    REDBufferView* numCulledLightsGrid,
                    REDBufferView* culledLightDataGrid);

private:
    std::tuple<NGIBlendStateDesc, NGIDepthStencilStateDesc> mDirectionalLightBackLightingStateOverride;

    NGIBufferView* shadowDataView;
    NGIBufferView* shadowMatricesView;
    NGIBufferView* spotShadowMapRangeView;
    NGIBufferView* pointShadowMapRangeView;

    UInt32 mFrameCountModed = 0;
    UInt32 GetFrameCountModed();

    const inline static NameID stencilDeferredLighingBackPassID = "StencilDeferredLightingBack";
    const inline static NameID tileBasedDeferredLighingBackPassID = "TileBasedDeferredLighting";

    friend PassBase<TileBasedDefferedLightingSettings, TileBasedDefferedLighting>;
};
}   // namespace cross