#pragma once
#include "PassBase.h"
#include "Resource/Resource.h"
#include "Resource/Material.h"
#include "RenderEngine/RenderMaterial.h"
namespace cross {
struct UIPassInput
{
    REDTextureView* inputcolor = nullptr;
    void GenerateInputData(const GameContext& gameContext);
};
struct UIPassOutput
{
    REDTextureView* outputcolor = nullptr;
    void SetOutputData(const GameContext& gameContext);
};
struct RENDER_ENGINE_API UIPass
{
public:
    constexpr std::string_view GetPassName()
    {
        return  std::string_view("UI");
    }
    MaterialR* mUIPostMat = nullptr;
    void FillInput(const GameContext& gameContext);
    void Execute(const GameContext& gameContext);
    UIPassInput mInput;
    UIPassOutput mOutput;
};
}   // namespace cross