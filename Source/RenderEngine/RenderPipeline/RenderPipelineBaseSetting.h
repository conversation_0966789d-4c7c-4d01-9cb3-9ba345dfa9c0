#pragma once
#include "RenderEngine/RenderEngineForward.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "RenderEngine/RenderPipeline/Effects/SSAAResolve.h"
#include "RenderEngine/RenderPipeline/Effects/Shadow.h"

namespace cross {
struct RENDER_ENGINE_API RenderPipelineBaseSetting : public RenderPipelineSetting
{
    RenderPipelineBaseSetting() = default;

    virtual RenderPipelineSetting* Clone() const override { return nullptr; };

    RENDER_PIPELINE_VALUE(int, CinematicScale, 1, "CinematicScale", "", "General Settings")

    void Initialize() override;

    CE_Virtual_Serialize_Deserialize;

    CEMeta(Serialize)
    SSAAResolvePassSetting mSSAAResolvePassSetting;

};
}   // namespace cross