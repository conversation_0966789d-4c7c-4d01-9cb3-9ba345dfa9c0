#include "BuiltInWorldRenderPipeline.h"

namespace cross {
void BuiltInWorldRenderPipeline::Assemble(FrameParam* frameParam)
{
    // Assemble RenderPipelines
    for (auto& [type, renderPipeline] : mRenderPipelines)
    {
        if (renderPipeline->IsEnable())
        {
            SCOPED_CPU_TIMING(GroupRendering, "RenderPipelineAssemble");
            renderPipeline->Assemble();
        }
    }
}

void BuiltInWorldRenderPipeline::UpdateSetting(const RenderPipelineSetting* setting)
{
    WorldRenderPipeline::UpdateSetting(setting);
}
}   // namespace cross