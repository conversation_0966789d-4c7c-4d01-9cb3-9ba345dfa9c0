
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/RenderPipelineSystemR.h"
#include "RenderEngine/RenderCullingResultQueue.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/ReflectionProbeSystemR.h"
#include "RenderEngine/ShadowSystemR.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"
#include "RenderEngine/RenderPipeline/WorldRenderPipeline/BuiltInWorldRenderPipeline.h"

// todo: remove this
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/CameraSystemR.h"
#include "RenderEngine/RenderPipeline/FFSRenderPipelineSetting.h"

#include "RenderEngine/FrameSynchronizationSystemR.h"
namespace cross {
void RenderPipelineSystemR::UpdateSetting(const RenderPipelineSetting* setting)
{
    mSetting.reset(setting);

    CreateOrResetWorldRenderPipeline();

    mWorldRenderPipeline->UpdateSetting(mSetting.get());
}

void RenderPipelineSystemR::OnBeginFrame(FrameParam* frameParam)
{
}

void RenderPipelineSystemR::SetDrawUnitStatisticQueue(StatisticQueue* statisticQueue)
{ 
    mStatisticQueue = statisticQueue;
}

REDDrawUnitStatistic& RenderPipelineSystemR::GetDrawUnitStatistic()
{
    auto frameCount = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameCount();
    return mStatisticQueue->operator[](frameCount % mStatisticQueue->size());
}

void RenderPipelineSystemR::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction<threading::ThreadID::RenderingThreadLocal>(FrameTickStage::Update, {}, [=] {             
        if (EngineGlobal::GetSettingMgr()->GetUseSeparatePresentThread())
            EngineGlobal::GetRenderEngine()->GetGlobalSystem<FrameSynchronizationSystemR>()->mAcquireReadyFence->Wait(frameParam->GetFrameCount(), UINT64_MAX);
                    
        mWorldRenderPipeline->Assemble(frameParam); });
}

void RenderPipelineSystemR::AddRenderPipeline(ViewType type, IRenderPipeline* pipeline)
{
    mWorldRenderPipeline->AddRenderPipeline(type, pipeline);
}

void RenderPipelineSystemR::ExtractRenderPipeline(IRenderPipeline* pipeline)
{
    mWorldRenderPipeline->ExtractRenderPipeline(pipeline);
}

void RenderPipelineSystemR::DestroyRenderPipeline(IRenderPipeline* pipeline)
{
    mWorldRenderPipeline->DestroyRenderPipeline(pipeline);
}

void RenderPipelineSystemR::CreateOrResetWorldRenderPipeline()
{
    if (mWorldRenderPipeline == nullptr || mCurrPipelineName != mSetting->UseRenderPipeline)
    {
        if (mSetting->UseRenderPipeline == "UseFFSRP")
        {
            mWorldRenderPipeline.reset(new FFSWorldRenderPipeline());
        }
        else
        {
            mWorldRenderPipeline.reset(new BuiltInWorldRenderPipeline());
        }
    }

    mWorldRenderPipeline->Initialize(mRenderWorld, EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor());

    mCurrPipelineName = mSetting->UseRenderPipeline;
}

bool RenderPipelineSystemR::IsPCGSceneWorld()
{
    const HashString& worldName = mRenderWorld->GetName();
    std::string worldName1 = worldName.GetCString();
    int index = static_cast<int>(worldName1.find("PCGSceneWorld"));
    return (index != -1);
}

}   // namespace cross
