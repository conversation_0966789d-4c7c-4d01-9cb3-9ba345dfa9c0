#pragma once
#include "Runtime/GameWorld/RenderWindowInitInfo.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/IWindowR.h"
#include "CECommon/Common/FrameParam.h"

namespace cross
{

class RENDER_ENGINE_API RenderWindowR : public IWindowR
{
public:
    RenderWindowR(const RenderWindowInitInfo& info);
    ~RenderWindowR();

    bool Acquire(NGIFence* toSingalFence) override;
    REDTexture* PrepareBackbuffer(NGICommandList* cmdList) override;
    bool Present() override;

#if CROSSENGINE_ANDROID
    void ReCreateSurface(NativeWindow nativeWindow) override;
#endif
    void Resize(UInt32 width, UInt32 height, cross::NGIScreenMode screenMode) override;

    bool WaitForPresent(UInt64 presentid, UInt64 timeout);

    std::tuple<UInt32, UInt32> GetSize() override;

    auto GetSwapchain()
    {
        return mSwapchain.get();
    }

protected:
    std::unique_ptr<NGISwapchain> mSwapchain = nullptr;
    UInt32 mUndefinedBackbuffers;
    bool mMinimized = false;
    UInt32 mBackIndex = 0;
   
    static constexpr cross::GraphicsFormat mHDRFormat = GraphicsFormat::A2B10G10R10_UNormPack32;
    static constexpr cross::GraphicsFormat mDefaultFormat = GraphicsFormat::B8G8R8A8_UNorm;
};

}
