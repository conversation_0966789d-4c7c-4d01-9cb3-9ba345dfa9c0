#include "EnginePrefix.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/WorldInternalSystem.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/DataTypes.h"
#include "ECS/EntityIDGenerator.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "RenderEngine/RenderWorld.h"
#include "Runtime/Interface/ECSConfig.h"
#include "Threading/RenderingThread.h"
#include "RenderWorld.h"

DECLARE_CPU_TIMING_GROUP(GroupRenderWorld);

namespace cross {

RenderWorld* RenderWorld::CreateInstance(const char* name, EntityIDGeneratorCorePtr& idCore)
{
    return new RenderWorld(name, idCore);
}

void RenderWorld::Release()
{
    delete this;
}

RenderWorld::RenderWorld(const char* name, EntityIDGeneratorCorePtr& idCore)
    : mName(name)
    , mRuntimeID(idCore->GetWorldID())
    , mFrameTickManager(std::make_unique<FrameTickManager>())
    , mRenderWorldTypeTag(0)
{
    InitStore(idCore);
    mStore->SetMoveEntityEventReceiver(this);
    mEmptyPrototype = EngineGlobal::GetECSFramework().GetPrototypeRegistry().GetEmptyPrototype(true);
}

RenderWorld::~RenderWorld()
{
    for (size_t i = 0; i < mAllSystems.size(); i++)
    {
        if (mAllSystems[i])
        {
            const auto& desc = EngineGlobal::GetCompSystemDescSystem()->GetRenderSystemDescByName(mAllSystems[i]->GetFrameTickName().GetCString());
            mAllSystems[i]->Release();
            mAllSystems[desc->mID] = nullptr;     
        }
    }
    mAllSystems.clear();
    mSystemMap.clear();
    
    mStore->ClearStoreData();
}
void RenderWorld::ClearAllEntities()
{
    for (auto&& blockStore : mStore->GetAllBlockStores())
    {
        auto entitiesInBlock = mStore->Query<ecs::EntityIDComponent>(blockStore.second->GetBlockId());
        UInt32 firstIndex = UINT32_MAX;
        UInt32 lastIndex = UINT32_MAX;
        for (const auto& entityIdComp : entitiesInBlock)
        {
            lastIndex = AddRemainedEvent<EntityDestroyEvent>(EntityLifeCycleEventFlag::DestroyEntity | EntityLifeCycleEventFlag::DestroyComponent, entityIdComp.GetEntityID(), GetPrototype(entityIdComp.GetEntityID())->ComponentMask);
            if (firstIndex == UINT32_MAX)
            {
                firstIndex = lastIndex;
            }
        }
        if (firstIndex != UINT32_MAX)
            DispatchRemainedEventUpdatedEvent<EntityDestroyEvent>(firstIndex, lastIndex);
    }
}
void RenderWorld::AddRenderSystem(RenderSystemBase* system, const char* systemname)
{
    DEBUG_ASSERT(threading::TaskSystem::IsInRenderingThread());

    if (!system)
        return;

    const auto& desc = cross::EngineGlobal::GetCompSystemDescSystem()->GetRenderSystemDescByName(systemname);
    if (desc->mID < mAllSystems.size())
    {
        if (mAllSystems[desc->mID] != nullptr)
        {
            LOG_ERROR("Render system with same id already exist in world: {}", desc->mName.GetCString());
            if (mAllSystems[desc->mID] != system)
            {
                LOG_ERROR("Render system with same id already exist in world, and there are different systems");
            }
            return;
        }
    }
    else
    {
        auto* descSystem = EngineGlobal::GetCompSystemDescSystem();
        mAllSystems.resize(descSystem->GetRenderSystemDescCount());
        DEBUG_ASSERT(desc->mID < static_cast<SInt32>(descSystem->GetRenderSystemDescCount()));
    }
    system->SetSystemUniqueName(systemname);
    mAllSystems[desc->mID] = system;
    mSystemMap[desc->mName.GetHash64()] = system;
    system->SetRenderWorld(this);

    UInt32 eventFlag = 0;
    RenderWorldSystemChangedEvent event(WorldSystemEventFlag::AddSystem, system);
    for (size_t i = 0; i < mAllSystems.size(); i++)
    {
        if (mAllSystems[i] && mAllSystems[i] != system)
        {
            mAllSystems[i]->NotifyEvent(event, eventFlag);
        }
    }
}
void RenderWorld::DispatchDestroyEvent(ecs::EntityID entity)
{
    auto* prototype = mStore->GetPrototype(entity);
    if (!prototype)
        return;
    SetEntityMask(true, entity, ecs::RuntimeMaskType::PendingDestroyMask);
    DispatchRemainedEvent<EntityDestroyEvent>(EntityLifeCycleEventFlag::DestroyComponent | EntityLifeCycleEventFlag::DestroyEntity, entity, prototype->ComponentMask);
}
void RenderWorld::AddSystemDependency(RenderSystemBase* predecessor, RenderSystemBase* successor)
    {
    DEBUG_ASSERT(threading::TaskSystem::IsInGameThread());

    if (successor)
    {
        DispatchRenderingCommandWithToken([this, predecessor, successor] { mFrameTickManager->AddSystemDependency(predecessor, successor); });
    }
}

void RenderWorld::AddSystemDependency(const FrameTickManager::DependencyGroup& predecessors, const FrameTickManager::DependencyGroup& successors)
{
    DEBUG_ASSERT(threading::TaskSystem::IsInGameThread());

    DispatchRenderingCommandWithToken([this, predecessors, successors] { mFrameTickManager->AddSystemDependency(predecessors, successors); });
}

void RenderWorld::AddSystemDependency(RenderSystemBase* predecessor, const FrameTickManager::DependencyGroup& successors)
{
    DEBUG_ASSERT(threading::TaskSystem::IsInGameThread());

    DispatchRenderingCommandWithToken([this, predecessor, successors] { mFrameTickManager->AddSystemDependency(predecessor, successors); });
}

void RenderWorld::AddSystemDependency(const FrameTickManager::DependencyGroup& predecessors, RenderSystemBase* successor)
{
    DEBUG_ASSERT(threading::TaskSystem::IsInGameThread());

    DispatchRenderingCommandWithToken([this, predecessors, successor] { mFrameTickManager->AddSystemDependency(predecessors, successor); });
}

RenderSystemBase* RenderWorld::GetRenderSystem(UInt32 systemID)
{
    if (systemID < mAllSystems.size())
    {
        return mAllSystems[systemID];
    }
    return nullptr;
}

WorldInternalSystem* RenderWorld::GetSystem(const char* systemName) const
{
    StringHash64 hash = HashFunction::HashString64(systemName);
    auto it = mSystemMap.find(hash);
    if (it != mSystemMap.end())
        return it->second;
    return nullptr;
}

void RenderWorld::BeginFrame(FrameParam* frameParam)
{
    Assert(threading::TaskSystem::IsInRenderingThread());
    SCOPED_CPU_TIMING(GroupRenderWorld, "RenderWorldBeginFrame");
    mStore->BeginFrame(frameParam);

    ClearRemainedEvent<EntityCreateEvent>();   // Clear last-frame EntityLifeCycleEvent
    ClearRemainedEvent<EntityPostCreateEvent>();
    ClearRemainedEvent<EntityDestroyEvent>();
    ClearRemainedEvent<EntityPostDestroyEvent>();
    ClearRemainedEvent<EntityMoveEvent>();

}

void RenderWorld::EndFrame(FrameParam* frameParam)
{
    Assert(threading::TaskSystem::IsInRenderingThread());
    SCOPED_CPU_TIMING(GroupRenderWorld, "RenderWorldEndFrame");

    for (size_t i = 0; i < mAllSystems.size(); i++)
    {
        if (mAllSystems[i] && mAllSystems[i]->GetEnable())
        {
            mAllSystems[i]->EndFrame(frameParam);
        }
    }
    mStore->EndFrame(frameParam);
}

void RenderWorld::Tick(FrameParam* frameParam)
{
    Assert(threading::TaskSystem::IsInRenderingThread());
    // auto profiler = dynamic_cast<gbf::IProfilerModule*>(GameFramework()->QueryModule(gbf::kModuleProfilerName));
    // auto FrameIDString = std::string("FRAME_").append(std::to_string(frameParam->GetFrameCount()));
    // profiler->BeginSampleDynamic(FrameIDString.c_str(), "");
    {
        SCOPED_CPU_TIMING(GroupRenderWorld, "RenderWorldTick");

        mFrameTickManager->BuildExecutionGraph(frameParam);
        mFrameTickManager->Tick();
    }
    // profiler->EndSample();
}

void RenderWorld::DoPendingDestroy()
{
    if (mIsPendingToDestroy)
    {
        for (size_t i = 0; i < mAllSystems.size(); i++)
        {
            if (mAllSystems[i])
            {
                mAllSystems[i]->PendingToDestroy();
            }
        }
    }
}

ecs::EntityID RenderWorld::CreateEntityID()
{
    return mStore->CreateEntityID();
}

bool RenderWorld::IsEntityAlive(ecs::EntityID entity) const
{
    return mStore->IsEntityAlive(entity);
}

bool RenderWorld::AddComponentByPrototype(ecs::EntityID entityID, const ecs::PrototypePtr& prototype, UInt64 targetBlockForEmptyEntity, bool dispatchCreateEvent)
{
    ecs::ComponentBitMask mask = mStore->AddComponentByPrototype(entityID, prototype, targetBlockForEmptyEntity);
    if (mask.IsZero())
        return false;

    if (dispatchCreateEvent)
    {
        DispatchRemainedEvent<EntityCreateEvent>(EntityLifeCycleEventFlag::CreateComponent, entityID, mask);
        DispatchRemainedEvent<EntityMoveEvent>(EntityLifeCycleEventFlag::MoveComponent, entityID);
        DispatchRemainedEvent<EntityPostCreateEvent>(EntityLifeCycleEventFlag::PostCreateComponent, entityID, mask);
    }

    return true;
}

bool RenderWorld::DelayRemoveComponentByPrototype(ecs::EntityID entityID, const ecs::PrototypePtr prototypeToRemove)
{
    if (!prototypeToRemove || prototypeToRemove == mEmptyPrototype)
        return false;

    auto* curPrototype = mStore->GetPrototype(entityID);
    if (curPrototype == mEmptyPrototype)
        return false;

    ecs::ComponentBitMask changeMask = curPrototype->ComponentMask & prototypeToRemove->ComponentMask;
    changeMask &= (~mEmptyPrototype->ComponentMask);

    DispatchRemainedEvent<EntityDestroyEvent>(EntityLifeCycleEventFlag::DestroyComponent, entityID, changeMask);
    if (prototypeToRemove && !(prototypeToRemove == mEmptyPrototype))
    {
        mStore->RemoveComponentByPrototype(entityID, prototypeToRemove);
    }
    DispatchRemainedEvent<EntityMoveEvent>(EntityLifeCycleEventFlag::MoveComponent, entityID);

    return true;
}

void RenderWorld::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    if (event.mEventType == EntityMemoryMovedEvent::sEventType)
    {
        // Entity moved because of other entity's prototype transferred
        const EntityMemoryMovedEvent& e = TYPE_CAST(const EntityMemoryMovedEvent&, event);
        DispatchRemainedEvent<EntityMoveEvent>(EntityLifeCycleEventFlag::MoveComponent, e.mData.mEntity);
    }
}

void RenderWorld::DestroyBlockStore(UInt64 blockId)
{
    // TODO(yuanwan): extract same logic with gameworld
    auto entitiesInBlock = mStore->Query<ecs::EntityIDComponent>(blockId);
    UInt32 firstIndex = UINT32_MAX;
    UInt32 lastIndex = UINT32_MAX;
    for (auto&& entityIdComp : entitiesInBlock)
    {
        lastIndex = AddRemainedEvent<EntityDestroyEvent>(EntityLifeCycleEventFlag::DestroyEntity | EntityLifeCycleEventFlag::DestroyComponent, entityIdComp.GetEntityID(), GetPrototype(entityIdComp.GetEntityID())->ComponentMask);
        if (firstIndex == UINT32_MAX)
        {
            firstIndex = lastIndex;
        }
    }
    if (firstIndex != UINT32_MAX)
        DispatchRemainedEventUpdatedEvent<EntityDestroyEvent>(firstIndex, lastIndex);

    mStore->DeleteBlockStore(blockId);
}

void RenderWorld::NotifyShutDownEngine()
{
    for (auto & itr: mAllSystems)
    {
        if (itr)
            itr->NotifyShutDownEngine();
    }

}

void RenderWorld::DestroyEntity(ecs::EntityID entity, bool releaseId)
{
    auto* prototype = mStore->GetPrototype(entity);
    if (!prototype)
        return;
    mStore->RemoveAllComponents(entity);
    if (releaseId)
        mStore->ReleaseEntityID(entity);
    else
        mStore->FinishEntityID(entity);
}

void RenderWorld::DumpSystemDependency()
{
    auto contents = mFrameTickManager->GenerateDependencyGraphviz();
    if (auto file = fopen(("RenderWorld_" + std::to_string(GetName().GetHash32()) + ".dot").c_str(), "w"); file)
    {
        fwrite(contents.c_str(), 1, contents.size(), file);
        fclose(file);
    }
}
}   // namespace cross
