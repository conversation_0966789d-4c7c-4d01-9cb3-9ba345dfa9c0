#pragma once
#include "NativeGraphicsInterface/NGI.h"

namespace cross
{
    //////////////////////////////////////////////////////////////////////////
    //enums
    //////////////////////////////////////////////////////////////////////////
    const UInt32 REFLECTION_PROBE_CAMERA_NUM = 6;
    const UInt16 MAX_BAKE_REFLECTION_PROBE_NUM = 65535;
    const UInt32 MAX_PIPELINE_REFLECTION_PROBE_NUM = 10;
    const UInt32 MAX_PIPELINE_LOCAL_LIGHT_NUM = 1024;
    const UInt32 MAX_CASCADE_SHADOW_COUNT = 4;

    enum CEMeta(Editor, <PERSON><PERSON>, Puerts) CullingProperty : uint8_t
    {
        CULLING_PROPERTY_NONE = 0,
        CULLING_PROPERTY_CULLABLE = 1,
        CULLING_PROPERTY_ALWAYS_VISIBLE = 2,
    };

    enum class CEMeta(Reflect, Editor) DisplayMode
    {
        Default = 0,
        PureTexture,
        PureWhite,
        Wireframe,
    };

    enum class CEMeta(Reflect, Editor) ViewModeVisualizeType
    {
        // Ref to UE's ViewMode
        Lit = 0,
        Unlit,            // Base Color
        Wireframe,
        DetailLighting,
        LightingOnly,

        // Buffer Visualization
        BaseColor,
        Depth,
        WorldNormal,
        DiffuseColor,             // Base Color + Material AO
        MaterialAO,
        Metallic,
        Opacity,
        Roughness,
        Specular,
        SpecularColor,            // Fresnel0
        SubsurfaceColor,          // SSS_SubsurfaceColor
        ShadingModel,              // typeID
        MotionVector,

        AmbientOcclusion,
        BentNormal,
        Reflections,
        SeparateTranslucencyRGB,
        GlobalIllumination,
        SceneColor,               // Opacity Color

        GILighting,
        DebugColor,
        EmissiveColor,
        
        // Advanced TODO
        // Light Complexity 
        // Shader Complexity 
        // Stationary Light Overlap 
        // Lightmap Density 
        // LOD Coloration  -- Useful 
        // HLOD Coloration
    };

    enum MeshBatchFlag : UInt16
    {
        MESH_BATCH_DEFAULT = 0x0001,
        MESH_BATCH_STATIC = 0x0002,
        MESH_BATCH_SKIN = 0x0004,
        MESH_BATCH_DO_NOT_MERGE = 0x0008,
        MESH_BATCH_INSTANCING = 0x0010,
        MESH_BATCH_DYNAMIC_MERGE = 0x0020,
    };

    enum MeshBuildPolicy : UInt8
    {
        MESH_BUILD_POLICY_INVALID = 0,
        MESH_BUILD_POLICY_DEFAULT,

        MESH_BUILD_POLICY_CUSTOM = 64,
    };

    enum class CEMeta(Editor,Puerts) FoliageGenerationType : UInt8
    {
        FOLIAGE_GENERATION_TYPE_INSTANCE,
        FOLIAGE_GENERATION_TYPE_PCG,
        FOLIAGE_GENERATION_TYPE_LIGHT,
    };

    enum FrustumCullingImplementation
    {
        None = 0,
        COMMON,
        SSE2,
        SSE41,
        AVX2,
        AVX512,
    };

    enum class CEMeta(Editor, Cli,Puerts) LightType : UInt8
    {
        Directional = 0,
        Point = 1,
        Spot = 2,
        Rect = 3
    };

    enum class CEMeta(Editor, Cli, Puerts) LightMode : UInt8
    {
        Realtime = 0x01,
        Baked = 0x02,
        Mixed = Realtime | Baked,
    };

    enum class CEMeta(Editor, Cli, Puerts) LightPriority : UInt8
    {
        Major = 0,
        Minor = 10
    };

    enum class CEMeta(Editor, Cli, Puerts) LightShadowType : UInt8
    {
        HardShadow = 0,
        SoftShadowPCF_1X1 = 1,
        SoftShadowPCF_3X3 = 2,
        SoftShadowPCF_5X5 = 3,
        SoftShadowPCSS = 4,
    };

    enum class CEMeta(Editor, Cli, Puerts) RenderingLayer : UInt8
    {
        Nothing = 0,
        Default = 1,
        Layer1 = Default << 1,
        Layer2 = Layer1 << 1,
        Layer3 = Layer2 << 1,
        Layer4 = Layer3 << 1,
        Layer5 = Layer4 << 1,
        Layer6 = Layer5 << 1,
        LayerCloud = Layer6 << 1,
        Everything = 0xff
    };

    enum class CEMeta(Editor, Cli, Puerts) ReflectionProbeType : UInt8
    {
        Baked = 0,
        Custom = 1,//select a reflection map
        Realtime = 2,
    };

    enum class CEMeta(Editor, Cli, Puerts) ReflectionProbeShapeType : UInt8
    {
        Box = 0,
        Sphere = 1,
    };

    enum class CEMeta(Editor, Cli, Puerts) ReflectionProbeRefreshMode : UInt8
    {
        OnAwake = 0,
        EveryFrame = 1,
        ViaScripting = 2,
    };

    enum class CEMeta(Editor, Cli, Puerts) ReflectionProbeTimeSlicing : UInt8
    {
        AllFacesAtOnce = 0,
        IndividualFaces = 1,
        NoTimeSlicing = 2,
    };

    enum class CEMeta(Editor) ReflectionProbeTimeSlicingState : UInt8
    {
        ProcessFace = 0,
        ProcessMipmap = 1,
        ProcessOK = 2,
    };

    enum class CEMeta(Editor, Puerts) PostProcessVolumeType : UInt8
    {
        Global = 0,
        Local = 1,
        LocalLerpOnly = 2,
    };

    enum class CEMeta(Editor) PostProcessSMAAType : UInt8
    {
        Luminance = 0,
        Color = 1,
        Depth = 2,
    };


#pragma warning(push)
#pragma warning(disable : 4324)
    struct alignas(16) LightInfo
    {
        Float4 LightDirPos;
        Float4 LightTilePos;
        Float4 LightAttenuation;
        Float4 LightColor;
        Float4 LightSpotDirection;
        float SourceRadius;
        float SoftSourceRadius;
        float SourceLength;  // not supported yet
        int LightShadowDataIndice;
        int VirtualShadowMapId;
        float SpecularLightIntensity;
    };
#pragma warning(pop)
    //////////////////////////////////////////////////////////////////////////
    //struct
    //////////////////////////////////////////////////////////////////////////

    struct MeshBatchInfo
    {
        MeshBatchInfo() = default;
        MeshBatchInfo(MeshBatchFlag flag, UInt8 batchMethod, MeshBuildPolicy policy) :
            mBatchFlag(flag), mPolicy(policy), mBatchMethod(batchMethod) {}
        inline MeshBatchFlag GetBatchFlag()const { return (MeshBatchFlag)mBatchFlag; }
        inline void SetBatchFlag(MeshBatchFlag batchType) { mBatchFlag = batchType; }
        inline bool HasFlag(MeshBatchFlag flag) { return (mBatchFlag & flag) > 0; }
        inline void SetMeshBuildPolicy(MeshBuildPolicy policy) { mPolicy = policy; }
        inline MeshBuildPolicy GetMeshBuildPolicy() const { return (MeshBuildPolicy)mPolicy; }
        inline UInt8 GetBatchMethod() const { return mBatchMethod; }
        inline void SetBatchMethod(UInt8 batchMethod) { mBatchMethod = batchMethod; }

        UInt16 mBatchFlag{ 0 };
        UInt8 mPolicy{ MESH_BUILD_POLICY_DEFAULT };
        UInt8 mBatchMethod{ 0 };
    };
   
    struct RenderLodSelected
    {
        SInt32 mLodSelected{-1};
    };

    enum class CEMeta(Editor, Cli, Puerts) RenderEffectTag : UInt32
    {
        DefaultEffect = 0x0,
        
        // Editor Effect
        EditorHighlight = 0x01,
        EditorOutline = 0x02,
        EditorWireframe = 0x04, // Engine's Editor Effect keeps 8 bits
        CustomEditorEffect = 0x100, // Project's Editor Effect has 8 bits

        // Game Effect
        Highlight = 0x10000,
        Outline = 0x20000,
        Wireframe = 0x40000, // Engine's Game Effect keeps 8 bits
        CastShadow = 0x80000,
        IsStatic = 0x100000,
        CustomGameEffect = 0x1000000, // Project's Game Effect has 8 bits
    };

    ENUM_CLASS_FLAGS(RenderEffectTag)

    struct CEMeta(Editor, Puerts) RenderEffect
    {
        RenderEffectTag mRuntimeEffectMask = RenderEffectTag::CastShadow | RenderEffectTag::IsStatic;
    };


    // Set fixed tag which would be serialized
    inline constexpr RenderEffectTag FixedRenderEffectMask = RenderEffectTag::CastShadow | RenderEffectTag::IsStatic;
    inline constexpr RenderEffectTag DefaultRenderEffectMask = RenderEffectTag::CastShadow | RenderEffectTag::IsStatic;

    struct RenderSortValue
    {
        UInt64 mValue{ 0 };
    };

    struct Viewport
    {
        int32_t x{ 0 };
        int32_t y{ 0 };
        uint32_t width{ 0 };
        uint32_t height{ 0 };

        Viewport() = default;

        Viewport(int32_t _x, int32_t _y, uint32_t _width, uint32_t _height)
            :x(_x), y(_y), width(_width), height(_height) {}

        void Set(int32_t _x, int32_t _y, uint32_t _width, uint32_t _height)
        {
            x = _x;
            y = _y;
            width = _width;
            height = _height;
        }

        bool operator==(const Viewport& rhs)
        {
            return this->x == rhs.y && this->y == rhs.y && this->width == rhs.width && this->height == rhs.height;
        }

        bool operator!=(const Viewport& rhs)
        {
            return !(*this == rhs);
        }
    };

    struct Scissor
    {
        int32_t x{ 0 };
        int32_t y{ 0 };
        uint32_t width{ 0 };
        uint32_t height{ 0 };

        Scissor() = default;

        Scissor(int32_t _x, int32_t _y, uint32_t _width, uint32_t _height)
            :x(_x), y(_y), width(_width), height(_height) {}

        void Set(int32_t _x, int32_t _y, uint32_t _width, uint32_t _height)
        {
            x = _x;
            y = _y;
            width = _width;
            height = _height;
        }

        bool operator==(const Scissor& rhs)
        {
            return this->x == rhs.y && this->y == rhs.y && this->width == rhs.width && this->height == rhs.height;
        }

        bool operator!=(const Scissor& rhs)
        {
            return !(*this == rhs);
        }
    };

    enum class CEMeta(Editor, Script, Puerts) CameraProjectionMode : UInt8
    {
        Perspective = 0,
        Orthogonal = 1,
    };

    struct RenderCameraFrustum
    {
        //Plane mPlanes[6];
        //SoA form
        std::array<std::array<float, 6>, 4> mPlanes;
        //proj * view matrix
        Float4x4 PVMatrix;
        UInt16 mMask{ 0 };
        UInt16 mUsage{ 0 };
        UInt32 mIndexMask{ 0 };
        UInt32 mVisibleEntityCount{ 0 };
    };

    using RenderMaterialTag = UInt32;

    enum class PrimitiveDepth : UInt8
    {
        SceneDepth,
        SelfDepth
    };

    enum class CullingType : UInt8
    {
        View                 = 1,
        Shadow               = 1 << 1,
        Shadow_SimpleCulling = 1 << 2,
        Voxel                = 1 << 3,
    };

    constexpr UInt8 STENCIL_RECEIVE_DECAL_BIT_ID = 6;
    constexpr UInt8 STENCIL_RECEIVE_DECAL_BIT_MASK = 1 << STENCIL_RECEIVE_DECAL_BIT_ID;
    constexpr UInt8 STENCIL_APPLY_DBUFFER_DECAL_BIT_ID = 5;
    constexpr UInt8 STENCIL_APPLY_DBUFFER_DECAL_BIT_MASK = 1 << STENCIL_APPLY_DBUFFER_DECAL_BIT_ID;
    constexpr UInt8 STENCIL_ALL_BIT_MASK = 0xFF;
    constexpr UInt8 STENCIL_NONE_BIT_MASK = 0x00;

    constexpr UInt16 gRenderGroupOpaque = 2000;
    constexpr UInt16 gRenderGroupAlphaTest = 2500;
    constexpr UInt16 gRenderGroupTransparent = 3000;
    constexpr UInt16 gRenderGroupUI = 4000; // WorldSpaceUI 4000, ScreenSpaceUI 4100
    constexpr UInt16 gRenderGroupGizmoWithSceneDepth = 4500; // EditorIcon 4600
    constexpr UInt16 gRenderGroupGizmoWithGizmoDepth = 4800;
    constexpr UInt16 gRenderGroupOverlay = 5000;
    constexpr UInt16 gRenderGroupAfterFSR = 6000;
    constexpr UInt16 gRenderGroupMaxReceiveDecals = gRenderGroupTransparent - 1;
    }