#include "EnginePrefix.h"
#include "REDCulling.h"
#include "RenderEngine/RenderEngine.h"
// #include "RenderEngine/CullingGatherSystemR.h"
#include "RenderEngine/RenderNodeSystemR.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/TransformSystemR.h"
#include "RenderEngine/FoliageComponentR.h"
#include "RenderEngine/FoliageSystemR.h"
#include "RenderEngine/LightSystemR.h"
#include "RenderEngine/ModelSystemR.h"
#include "RenderEngine/AABBSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
#include "CrossBase/Math/CrossMath.h"
#include "RenderEngine/RenderCamera.h"
#include "RenderEngine/GPUScene/GPUScene.h"
#include "RenderEngine/LightCulling.h"
#if _WIN32
#    include <memory_resource>
#endif
namespace cross {

REDCullingResult::REDCullingResult(const REDCullingResultDesc& desc, REDCulling* culling, UInt64 frameID)
    : mDesc{desc}
    , mCulling{culling}
    , mFrameID(frameID)
{
}

REDDrawUnitList* REDCullingResult::GenerateDrawUnitList(const REDDrawUnitsDesc& desc, REDDrawUnitStatistic* statistic)
{
    std::scoped_lock<std::mutex> lock(mSharedDrawunitMutex);
    REDDrawUnitList* new_instance = nullptr;

    if (mDrawUnitLists.count(desc))
    {
        auto & drawUnitList = mDrawUnitLists[desc];

        // same desc frame, same drawUnit, can reuse
        if (drawUnitList.second == mFrameID)
        {
            return drawUnitList.first;
        }

        // means the drawUnit store in this structure is previous frame ,need delete;
        new_instance = new REDDrawUnitList(*this, desc, statistic, std::move(drawUnitList.first->mReadBackFrames),mCulling->GetAllocator());
        delete drawUnitList.first;
    }
    else
    {
        new_instance = new REDDrawUnitList(*this, desc, statistic, mCulling->GetAllocator());
    }

    mDrawUnitLists[desc] = {new_instance, mFrameID};
    // generate drawUnit
    mDrawUnitLists[desc].first->Dispatch();

    return mDrawUnitLists[desc].first;
}

void REDCullingResult::ReleaseFrameData()
{
    for (auto& itr : mDrawUnitLists)
    {
        itr.second.first->ReleaseFrameData();
    }
    mTask = nullptr;
    mReleased = true;
}

REDCullingResult::~REDCullingResult()
{
    for (auto & itr: mDrawUnitLists)
    {
        delete itr.second.first;
    }

    mDrawUnitLists.clear();
}



bool REDCullingResult::DrawUnitDescHasher::operator()(const REDDrawUnitsDesc& a, const REDDrawUnitsDesc& b) const
{
    return a.TagName == b.TagName && a.RenderGroupLow == b.RenderGroupLow && a.RenderGroupHigh == b.RenderGroupHigh && a.RenderEffectMask == b.RenderEffectMask
    && a.RenderNodeTypeMask == b.RenderNodeTypeMask && a.OverrideMaterial == b.OverrideMaterial && a.LODSelectionCamera == b.LODSelectionCamera;
}

size_t REDCullingResult::DrawUnitDescHasher::operator()(const REDDrawUnitsDesc& desc) const
{
    size_t hash_value = 0;
    hash_combine(hash_value, robin_hood::hash_int(desc.TagName.GetID()));
    hash_combine(hash_value, robin_hood::hash_int(desc.RenderGroupLow));
    hash_combine(hash_value, robin_hood::hash_int(desc.RenderGroupHigh));
    hash_combine(hash_value, robin_hood::hash_int(static_cast<uint32_t>(desc.RenderEffectMask)));
    hash_combine(hash_value, robin_hood::hash_int(static_cast<uint32_t>(desc.RenderNodeTypeMask)));
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.OverrideMaterial)));
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.LODSelectionCamera)));
    return hash_value;
}

template<bool WithAABB, class T>
void CullEntities(T& queryResult, REDCullingResultDesc& mDesc, FrameVector<FrameVector<REDCullingResult::EntityData>*>* mBlockEntityData, UInt32 cullingBlockCount, FrameAllocator* fa, threading::TaskEventArray& cullingTasks)
{
    auto* transSys = mDesc.World->GetRenderSystem<TransformSystemR>();
    auto* aabbSys = mDesc.World->GetRenderSystem<AABBSystemR>();
    auto* propsSys = mDesc.World->GetRenderSystem<RenderPropertySystemR>();
    auto* nodeSys = mDesc.World->GetRenderSystem<RenderNodeSystemR>();

   
     auto nEntity = queryResult.GetEntityNum();

    auto itr = queryResult.begin();
    for (UInt32 blockIndex = 0; blockIndex < cullingBlockCount; blockIndex++)
    {
        auto [entityBegin, entityEnd] = Split2Batches(nEntity, cullingBlockCount, blockIndex);
        auto begin = itr;
        itr += static_cast<UInt32>(entityEnd - entityBegin);
        auto end = itr;

        auto* entityData = mBlockEntityData->EmplaceBack(fa->CreateFrameContainer<FrameVector<REDCullingResult::EntityData>>(FrameStage::FRAME_STAGE_RENDER, 256));

        cullingTasks.Add(threading::Dispatch<threading::ThreadID::TaskThread>(threading::Priority::High, [=](auto) mutable {
            SCOPED_CPU_TIMING(GroupRendering, "Block Entity Culling");

            for (auto itr = begin; itr != end; ++itr)
            {
                //const auto& [transComp, tilePosComp, propsComp, nodeComp] = *itr;
                const auto& transComp = std::get<0>(*itr);
                const auto& tilePosComp = std::get<1>(*itr);
                const auto& propsComp = std::get<2>(*itr);
                const auto& nodeComp = std::get<3>(*itr);


                if (!mDesc.World->IsEntityAlive(transComp.GetEntityID()))
                {
                    continue;
                }

                if (propsSys->IsHide(propsComp.Read()) || propsSys->GetCullingProperty(propsComp.Read()) == CULLING_PROPERTY_NONE)
                {
                    continue;
                }

                if (mDesc.Camera->GetCullingType() == CullingType::Voxel && !propsSys->IsNeedVoxelized(propsComp.Read()))
                {
                    continue;
                }

                // Object type culling: static / dynamic / all
                auto renderNodeReader = nodeComp.Read();
                auto* renderNode = nodeSys->GetRenderNode(renderNodeReader);
                auto& worldTransform = renderNode->GetWorldTransform();
                auto& objMask = mDesc.ObjectMask;
                if ((objMask == REDObjectType::Static && !renderNode->IsStatic()) || (objMask == REDObjectType::Dynamic && renderNode->IsStatic()))
                {
                    continue;
                }

                if (mDesc.OnlyShadowCasters && !renderNode->IsCastShadow())
                {
                    continue;
                }

                // Object camera mask culling:
                auto entityLayerIndex = propsSys->GetLayerIndex(propsComp.Read());
                if (renderNode->GetEnableCameraMask() && !((1u << entityLayerIndex) & mDesc.Camera->GetCameraMask()))
                {
                    continue;
                }

                // Bounding culling:
                BoundingBox boundingBox;
                if  constexpr (WithAABB)
                {
                    auto aabbComp = std::get<4>(*itr);
                    boundingBox = aabbSys->GetWorldAABB(aabbComp.Read());

                    auto cameraTile = mDesc.Camera->GetCameraView().mCameraTilePosition;
                    auto tilePos = transSys->GetTilePosition(tilePosComp.Read());
                    boundingBox.Transform(boundingBox, (tilePos - cameraTile) * LENGTH_PER_TILE_F);
                }

                if (propsSys->GetCullingProperty(propsComp.Read()) == CULLING_PROPERTY_CULLABLE)
                {
                    if constexpr (WithAABB)
                    {
                        if (mDesc.CullingSubjectType == REDCullingSubjectType::Camera)
                        {
                            if (!mDesc.Camera->IsBoundingBoxVisible(boundingBox))
                            {
                                continue;
                            }
                        }
                        else
                        {
                            
                            if (mDesc.CullingBound  && !mDesc.CullingBound->Intersects(boundingBox))
                            {
                                continue;
                            }
                        }
                    }
                    //else
                    //{
                    //    continue;
                    //}
                }

                float entityBoundingSphereRadius = boundingBox.GetExtent().Length();

                Float3 worldPos{
                    worldTransform.RelativeMatrix.m30,
                    worldTransform.RelativeMatrix.m31,
                    worldTransform.RelativeMatrix.m32,
                };
                worldPos += (worldTransform.TilePosition - mDesc.Camera->GetCameraView().mCameraTilePosition) * LENGTH_PER_TILE_F;

                entityData->EmplaceBack(REDCullingResult::EntityData{transComp.GetEntityID(), boundingBox.GetCenter(), entityBoundingSphereRadius, worldPos, boundingBox, renderNode});
            }
        }));
    }
}

void REDCullingResult::Dispatch()
{
    auto queryResult = mDesc.World->Query<TransformComponentR, TilePositionComponentR, RenderPropertyComponentR, RenderNodeComponentR, AABBComponentR>();

    auto queryResult2 = mDesc.World->Query<TransformComponentR, TilePositionComponentR, RenderPropertyComponentR, RenderNodeComponentR>();
    auto queryResultsNoAABB = queryResult2.Exclude<AABBComponentR>();

    FrameAllocator* fa = EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator();
    threading::TaskEventArray cullingTasks;

    constexpr auto BLOCK_SIZE = 1024u;

    auto nEntity = queryResult.GetEntityNum();
    auto cullingBlockCount = math::DivideAndRoundUp(nEntity, BLOCK_SIZE);

    auto nNoAABBEntity = queryResultsNoAABB.GetEntityNum();
    auto NoAABBBlockCount = math::DivideAndRoundUp(nNoAABBEntity, BLOCK_SIZE);

    mBlockEntityData = fa->CreateFrameContainer<FrameVector<FrameVector<EntityData>*>>(FrameStage::FRAME_STAGE_RENDER, cullingBlockCount + NoAABBBlockCount);

    CullEntities<true>(queryResult,mDesc, mBlockEntityData, cullingBlockCount, fa, cullingTasks);


    if (queryResultsNoAABB.GetEntityNum() > 0)
    {
        CullEntities<false>(queryResultsNoAABB, mDesc, mBlockEntityData, NoAABBBlockCount, fa, cullingTasks);
    }

    // gather visible entities
    mTask = threading::Dispatch<threading::ThreadID::TaskThread>(cullingTasks, threading::Priority::High, [=](auto) {
        SCOPED_CPU_TIMING(GroupRendering, "GatherVisibleEntityForEachCamera");

        //auto totalVisibleEntityCount = std::accumulate(mBlockEntityData->begin(), mBlockEntityData->end(), size_t{}, [](auto acc, auto& date) { return date->GetSize() + acc; });
        UInt32 totalVisibleEntityCount = 0;
        UInt32 totalHeavyEntity = 0;
        for (const auto& blockEntityData : *mBlockEntityData)
        {
            totalVisibleEntityCount += blockEntityData->GetSize();
            for (auto& itr : *blockEntityData)
            {
                if (itr.renderNode->IsHeavyRenderNode())
                {
                    totalHeavyEntity += 1;
                }
            }
        }

        mEntityData = fa->CreateFrameContainer<FrameVector<EntityData>>(FrameStage::FRAME_STAGE_RENDER, static_cast<UInt32>(totalVisibleEntityCount));
        mHeavyEntity = fa->CreateFrameContainer<FrameVector<EntityData>>(FrameStage::FRAME_STAGE_RENDER, static_cast<UInt32>(totalHeavyEntity));
        
        int heavy_entity_size = 0;
        for (const auto& blockEntityData : *mBlockEntityData)
        {
            // Use iterator-based loop to safely erase elements
            for (auto it = blockEntityData->begin(); it != blockEntityData->end();)
            {
                if (it->renderNode->IsHeavyRenderNode())
                {
                    //heavy_entity_size += 1;
                    mHeavyEntity->EmplaceBack(*it);
                    it = blockEntityData->Erase(it, it + 1);
                }
                else
                {
                    ++it;
                }
            }
            mEntityData->Append(blockEntityData, 0);
        }
    });
}

UInt32 REDCulling::CalculateDrawUnitStateBucketID(const RenderGeometry* geometry, const MaterialR* material, const PropertySet& properties, NameID passName, REDDrawUnitFlag flag, UInt32 lodIndex)
{
    /*  hashBuffer
            geometry->uuid
            material->uuid
            // macros
            nameid, value
            nameid, value
            // shader const
            NameID, value
            NameID, vluae
            // object resource
            nameid, resource uuid
            nameid, resource uuid
    */
#if _WIN32
    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto pool = rdrSys->GetRenderingExecutionDescriptor()->GetREDFrameAllocator();
    std::pmr::polymorphic_allocator<UInt8> alloc(pool);
    std::pmr::vector<UInt8> hashBuffer{alloc};
#else
    std::vector<UInt8> hashBuffer;
#endif
    hashBuffer.reserve(sizeof(UInt32) * 4);

    auto AppendRawDataToHashBuffer = [&](const void* data, SizeType size) {
        SizeType offset = hashBuffer.size();
        hashBuffer.resize(hashBuffer.size() + size);
        UInt8* dstData = hashBuffer.data() + offset;
        memcpy(dstData, data, size);
    };

    auto AppendNumericPropertyToHashBuffer = [&](const PropertySet::NumericProperty& property) {
        std::visit(Overloaded{[&](const Float4x4& v) { AppendRawDataToHashBuffer(v.data(), sizeof(Float4x4)); },
                              [&](const bool& v) {
                                  UInt32 data = v ? 1u : 0u;
                                  AppendRawDataToHashBuffer(&data, sizeof(UInt32));
                              },
                              [&](const std::vector<UInt8>& v) { AppendRawDataToHashBuffer(v.data(), v.size()); },
                              [&](const auto& v) { AppendRawDataToHashBuffer(&v, sizeof(std::decay_t<decltype(v)>)); }},
                   property);
    };

    auto AppendResourcePropertyToHashBuffer = [&](const PropertySet::ResourceProperty& property) { std::visit(Overloaded{[&](const auto& ptr) { AppendRawDataToHashBuffer(&ptr, sizeof(void*)); }}, property.Value); };

    auto AppendDataToHashBuffer = Overloaded{[&](NameID name) {
                                                 const SizeType& propertyID = name.GetID();
                                                 AppendRawDataToHashBuffer(&propertyID, sizeof(SizeType));
                                             },
                                             [&](const void* ptr) { AppendRawDataToHashBuffer(&ptr, sizeof(void*)); },
                                             AppendNumericPropertyToHashBuffer,
                                             AppendResourcePropertyToHashBuffer};

    auto* shader = material->GetShader(passName);
    auto& variantKeywords = shader->GetMacroIDVec();
    auto& shaderConsts = shader->GetShaderConsts();

    AppendDataToHashBuffer(geometry);
    AppendDataToHashBuffer(material);

    // if (!properties.IsNumericSetEmpty())
    {
        for (auto& keyword : variantKeywords)
        {
            if (auto* property = properties.GetNumericProperty(keyword); property)
            {
                AppendDataToHashBuffer(keyword);
                AppendDataToHashBuffer(*property);
            };
        }

        for (auto& shaderConst : shaderConsts)
        {
            if (auto* property = properties.GetNumericProperty(shaderConst); property)
            {
                AppendDataToHashBuffer(shaderConst);
                AppendDataToHashBuffer(*property);
            };
        }
    }

    properties.VisitProperty([&](auto& name, auto& resourcesProperties) {
        if constexpr (std::is_same_v<std::decay_t<decltype(resourcesProperties)>, std::vector<PropertySet::ResourceProperty>>)
        {
            for (const PropertySet::ResourceProperty& resourceProperty : resourcesProperties)
            {
                AppendDataToHashBuffer(resourceProperty);
            }
        }
    });

    AppendRawDataToHashBuffer(&flag, sizeof(flag));

    AppendRawDataToHashBuffer(&lodIndex, sizeof(lodIndex));

    SizeType hashBufferPadSize = hashBuffer.size() % 4u;
    hashBuffer.insert(hashBuffer.end(), hashBufferPadSize, 0u);

    const UInt32* ptrStart = reinterpret_cast<const UInt32*>(hashBuffer.data());
    const UInt32* ptrEnd = reinterpret_cast<const UInt32*>(hashBuffer.data() + hashBuffer.size());

    UInt64 hashValue64 = HashRange(ptrStart, ptrEnd);
    UInt32 hashValue32 = static_cast<UInt32>((hashValue64 >> 32u) ^ (hashValue64 & 0xFFFFFFFF));

    return hashValue32;
}

REDCullingResult* REDCulling::Dispatch(const REDCullingResultDesc& desc, uint64_t frameID)
{
    std::unique_lock lock(mCullingResultMutex);

    if (auto itr = mCullingResults.find(desc); itr == mCullingResults.end())
    {
        auto cullingResult = mCullingResults.emplace(desc, std::make_shared<REDCullingResult>(desc, this, frameID)).first->second;
        cullingResult->Dispatch();
        return cullingResult.get();
    }
    else
    {
        if (itr->second->mReleased)
        {
            itr->second->Dispatch();
            itr->second->mReleased = false;
        }
        itr->second->mFrameID = frameID;
        return itr->second.get();
    }
}

LightCullingResult* REDCulling::DispatchLightCulling(const LightCullingDesc& desc, uint64_t frameID)
{
    std::unique_lock lock(mLightCullingResultMutex);
    if (auto itr = mLightCullingResults.find(desc); itr == mLightCullingResults.end())
    {
        auto lightCullingResult = &mLightCullingResults.emplace(desc, LightCullingResult{desc, this, frameID}).first->second;
        lightCullingResult->Dispatch();
        return lightCullingResult;
    }
    else
    {
        itr->second.mFrameID = frameID;
        return &itr->second;
    }
}

void REDCulling::WaitForCompletion()
{
    SCOPED_CPU_TIMING(GroupRendering, "WaitForCullingCompletion");

    auto* rdrSys = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* stagingMgr = rdrSys->GetScratchBuffer();
    auto* transientResMgr = rdrSys->GetTransientResourceManager();

    UInt32 visitedDrawUnitListCount = 0;
    for (auto& [_, cullingResult] : mCullingResults)
    {
        for (auto& drawUnitList : cullingResult->mDrawUnitLists)
        {
            if (drawUnitList.second.first->mVisited)
            {
                ++visitedDrawUnitListCount;
            }
        }
    }

    while (visitedDrawUnitListCount)
    {
        auto* drawUnitList = mReadyDrawUnitLists.Pop();
        if (drawUnitList->mVisited)
        {
            --visitedDrawUnitListCount;

            if (drawUnitList->GetModifiableDrawUnits().empty())
            {
                drawUnitList->mObjectIndexOffsetsBuffer = nullptr;
                drawUnitList->mObjectIndexOffsetsBufferOffset = 0;
                drawUnitList->mObjectIndexOffsetsBufferView = nullptr;

                drawUnitList->mObjectIndexBuffer = nullptr;
                drawUnitList->mObjectIndexBufferOffset = 0;
                drawUnitList->mObjectIndexBufferView = nullptr;

                continue;
            }

            // Object Index Offset Buffer:
            {
                auto& drawUnits = drawUnitList->GetModifiableDrawUnits();

                constexpr static NGIBufferUsage gObjectIndexOffsetsBufferUsage = NGIBufferUsage::CopySrc | NGIBufferUsage::VertexBuffer | NGIBufferUsage::TexelBuffer;

                auto objectIndexOffsetsBufferSize = drawUnits.size() * sizeof(UInt32);
                auto objectIndexOffsetsBufferWrap = stagingMgr->AllocateScratch(gObjectIndexOffsetsBufferUsage, objectIndexOffsetsBufferSize);

                size_t objectIndexOffsets = 0;
                for (const auto& drawUnit : drawUnits)
                {
                    static_assert(sizeof(UInt32) == sizeof(drawUnit.mObjectIndexOffset));
                    objectIndexOffsetsBufferWrap.MemWrite(objectIndexOffsets, &drawUnit.mObjectIndexOffset, sizeof(UInt32));
                    objectIndexOffsets += sizeof(UInt32);
                    
                }
                drawUnitList->mObjectIndexOffsetsBuffer = objectIndexOffsetsBufferWrap.GetNGIBuffer();
                drawUnitList->mObjectIndexOffsetsBufferOffset = objectIndexOffsetsBufferWrap.GetNGIOffset();

                NGIBufferViewDesc objectIndexOffsetsBufferViewDesc{gObjectIndexOffsetsBufferUsage, objectIndexOffsetsBufferWrap.GetNGIOffset(), objectIndexOffsetsBufferSize, GraphicsFormat::R32_UInt};
                drawUnitList->mObjectIndexOffsetsBufferView = transientResMgr->AllocateBufferView(objectIndexOffsetsBufferViewDesc, objectIndexOffsetsBufferWrap.GetNGIBuffer());
            }

            // Object Index Buffer:
            {
                auto& objectIndexList = drawUnitList->mObjectIndexList;

                constexpr static NGIBufferUsage gObjectIndexBufferUsage = NGIBufferUsage::CopySrc | NGIBufferUsage::VertexBuffer | NGIBufferUsage::TexelBuffer;

                auto objectIndexBufferDataSize = static_cast<UInt32>(objectIndexList.size() * sizeof(SInt32));
                auto objectIndexBufferSize = std::max(objectIndexBufferDataSize, 4u);
                auto objectIndexBufferWrap = stagingMgr->AllocateScratch(gObjectIndexBufferUsage, objectIndexBufferSize);
                objectIndexBufferWrap.MemWrite(0, objectIndexList.data(), objectIndexBufferDataSize);

                drawUnitList->mObjectIndexBuffer = objectIndexBufferWrap.GetNGIBuffer();
                drawUnitList->mObjectIndexBufferOffset = objectIndexBufferWrap.GetNGIOffset();

                NGIBufferViewDesc objectIndexOffsetsBufferViewDesc{gObjectIndexBufferUsage, objectIndexBufferWrap.GetNGIOffset(), objectIndexBufferSize, GraphicsFormat::R32_SInt};
                drawUnitList->mObjectIndexBufferView = transientResMgr->AllocateBufferView(objectIndexOffsetsBufferViewDesc, objectIndexBufferWrap.GetNGIBuffer());
            }
        }
    }
}

void REDCulling::Clear(UInt64 frameID)
{
    threading::TaskEventArray allTasks;
    for (auto& cullingResult : mCullingResults)
    {
        if (cullingResult.second->mTask)
        {
            allTasks.Add(cullingResult.second->mTask);
        }
        for (auto& drawUnitList : cullingResult.second->mDrawUnitLists)
        {
            if (drawUnitList.second.first->mTask)
            {
                allTasks.Add(drawUnitList.second.first->mTask);
            }
        }
    }
    for (auto& lightCullingResult : mLightCullingResults)
    {
        allTasks.Add(lightCullingResult.second.mTask);
    }
    allTasks.WaitForCompletion();

    if (MULTI_FRAME_CACHE > 1)
    {
        auto itr_erase = mCullingResults.begin();
        while (itr_erase != mCullingResults.end())
        {
            if (frameID > itr_erase->second->mFrameID + std::max(MULTI_FRAME_CACHE, 1u) * CmdSettings::Inst().gMaxQueuedFrame)
            {
                itr_erase = mCullingResults.erase(itr_erase);
            }
            else
            {
                itr_erase++;
            }
        }

        for (auto& itr : mCullingResults)
        {
            itr.second->ReleaseFrameData();
        }
    }
    else
    {
        mCullingResults.clear();
    }


    //mCullingResults.clear();


    mLightCullingResults.clear();
    if (auto size = mReadyDrawUnitLists.GetSize(); size)
    {
        // LOG_INFO("{} DrawUnitList unvisited in last frame", size);
        mReadyDrawUnitLists.Clear();
    }
}

FrameAllocator* REDCulling::GetAllocator()
{
    // TODO, we may need a different frame allocator for multiframe cache;
    // since current single frame allocator is problematic in debug mode
    return MULTI_FRAME_CACHE == 1 ? EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocator() : EngineGlobal::GetFrameParamMgr()->GetCurrentRenderFrameParam()->GetFrameAllocatorWithLifeTime(16);

}

bool REDCulling::Hasher::operator()(const REDCullingResultDesc& a, const REDCullingResultDesc& b) const
{
    return a.World == b.World && a.Camera == b.Camera && a.ObjectMask == b.ObjectMask && a.CullingSubjectType == b.CullingSubjectType && a.CullingBound ==b.CullingBound && a.OnlyShadowCasters == b.OnlyShadowCasters;
}


size_t REDCulling::Hasher::operator()(const REDCullingResultDesc& desc) const
{
    size_t hash_value = 0;
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.World)));
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.Camera)));
    hash_combine(hash_value, robin_hood::hash_int(static_cast<uint32_t>(desc.ObjectMask)));
    hash_combine(hash_value, robin_hood::hash_int(static_cast<uint32_t>(desc.CullingSubjectType)));
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.CullingBound)));
    hash_combine(hash_value, robin_hood::hash_int(desc.OnlyShadowCasters));

    return hash_value;
}


//
bool REDCulling::Hasher::operator()(const LightCullingDesc& a, const LightCullingDesc& b) const
{
    return a.World == b.World && a.Camera == b.Camera;
}

size_t REDCulling::Hasher::operator()(const LightCullingDesc& desc) const
{
    size_t hash_value = 0;
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.World)));
    hash_combine(hash_value, robin_hood::hash_int(reinterpret_cast<uint64_t>(desc.Camera)));
    return hash_value;
}

#define EMPLACE_ISOLATED_DRAWUNIT                                                                                                                                                                                                              \
    REDDrawUnit drawUnit{                                                                                                                                                                                                                      \
        mRenderNode->GetType(),                                                                                                                                                                                                                \
        key,                                                                                                                                                                                                                                   \
        StateBucketID,                                                                                                                                                                                                                         \
        CustumData,                                                                                                                                                                                                                            \
        Flags,                                                                                                                                                                                                                                 \
        Geometry,                                                                                                                                                                                                                              \
        Material,                                                                                                                                                                                                                              \
        ObjectProperties ? ObjectProperties : &mRenderNode->GetObjectProperties(),                                                                                                                                                             \
        LODIndex,                                                                                                                                                                                                                              \
        WorldTransform ? WorldTransform : &mRenderNode->GetWorldTransform(),                                                                                                                                                                   \
    };                                                                                                                                                                                                                                         \
    mDrawUnits.emplace_back(drawUnit)

#define EMPLACE_BATCHABLE_DRAWUNIT                                                                                                                                                                                                             \
    REDDrawUnit drawUnit{                                                                                                                                                                                                                      \
        mRenderNode->GetType(),                                                                                                                                                                                                                \
        key,                                                                                                                                                                                                                                   \
        StateBucketID,                                                                                                                                                                                                                         \
        CustumData,                                                                                                                                                                                                                            \
        Flags,                                                                                                                                                                                                                                 \
        Geometry,                                                                                                                                                                                                                              \
        Material,                                                                                                                                                                                                                              \
        ObjectProperties ? ObjectProperties : &mRenderNode->GetObjectProperties(),                                                                                                                                                             \
        0,                                                                                                                                                                                                                                     \
        nullptr,                                                                                                                                                                                                                               \
        ObjectDataBufferView,                                                                                                                                                                                                                  \
        PrimitiveDataBufferView                                                                                                                                                                                       \
    };                                                                                                                                                                                                                                         \
    return mDrawUnits.emplace_back(drawUnit);

// use add drawunit function to reduce confusing
#define DEFAULT_VALUE(X)

void REDDrawUnitCollector::AddOpaqueIsolatedDrawUnit(ISOLATED_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    key.StateBucketID = StateBucketID;

    EMPLACE_ISOLATED_DRAWUNIT;
}

REDDrawUnit& REDDrawUnitCollector::AddOpaqueBatchableDrawUnit(BATCHABLE_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    key.StateBucketID = StateBucketID;
    EMPLACE_BATCHABLE_DRAWUNIT
}

void REDDrawUnitCollector::AddTransparentIsolatedDrawUnit(float Distance, ISOLATED_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    auto disAsUInt = *reinterpret_cast<UInt32*>(&Distance);
    key.Distance = UINT32_MAX - disAsUInt;
    EMPLACE_ISOLATED_DRAWUNIT;
}

REDDrawUnit& REDDrawUnitCollector::AddTransparentBatchableDrawUnit(float Distance, BATCHABLE_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    auto disAsUInt = *reinterpret_cast<UInt32*>(&Distance);
    key.Distance = UINT32_MAX - disAsUInt;
    EMPLACE_BATCHABLE_DRAWUNIT;
}

void REDDrawUnitCollector::AddCustumPriorityIsolatedDrawUnit(UInt32 CustomPriority, ISOLATED_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    key.CustomPriority = CustomPriority;

    EMPLACE_ISOLATED_DRAWUNIT;
}

REDDrawUnit& REDDrawUnitCollector::AddCustumPriorityBatchableDrawUnit(UInt32 CustomPriority, BATCHABLE_DRAWUNIT_PARAMS)
{
    REDDrawUnitSortKey key{};
    key.RenderGroup = RenderGroup;
    key.CustomPriority = CustomPriority;

    EMPLACE_BATCHABLE_DRAWUNIT;
}

#undef DEFAULT_VALUE

}   // namespace cross