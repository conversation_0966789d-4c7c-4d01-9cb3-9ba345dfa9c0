#include "EnginePrefix.h"
#include "REDVisualizer.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

cross::REDVisualizer::REDVisualizer(RendererSystemR* rendererSys)
{
    mRendererSys = rendererSys;
    mRED = rendererSys->GetRenderingExecutionDescriptor();
    SetName(gREDVisualizerViewTextureName, gREDVisualizerPassName);

    InitializeGui();
    mVisEditor = std::make_unique<VisualizerImpl>(GetGuiContext(), ThumbnailWidth, ResourceNodeHeight, NodeSpacing);
    mVisEditor->mSelectInfo = &mSelectResInfo;
    NGIRenderPassDesc blitPassDesc
    {
        1,
        1,
        { { GraphicsFormat::R8G8B8A8_SRGB, NGILoadOp::Clear, NGIStoreOp::Store, }, },
        false,
    };
    blitPassDesc.SubpassCount = 1;
    blitPassDesc.Subpasses[0] = { 0, {}, 1, { NGIRenderPassTargetIndex::Target0 }, false };
    mBlitRenderPass.reset(GetNGIDevice().CreateRenderPass(blitPassDesc));
}

cross::REDVisualizer::~REDVisualizer()
{
}

void cross::REDVisualizer::BlitSelectResNode(NGICommandList* cmdList)
{
    std::vector<NGITextureBarrier> beforeBarriers;
    std::vector<NGITextureBarrier> afterBarriers;

    if (!EnumHasAllFlags(mSelectResInfo.SourceTextureState, NGIResourceState::PixelShaderShaderResource))
    {
        NGITextureBarrier beforeBarrier
        {
            mSelectResInfo.SourceTexture,
            mSelectResInfo.SourceTextureSubresource,
            mSelectResInfo.SourceTextureState,
            NGIResourceState::PixelShaderShaderResource,
            true,
        };
        beforeBarriers.emplace_back(beforeBarrier);

        NGITextureBarrier afterBarrier
        {
            mSelectResInfo.SourceTexture,
            mSelectResInfo.SourceTextureSubresource,
            NGIResourceState::PixelShaderShaderResource,
            mSelectResInfo.SourceTextureState,
        };
        afterBarriers.emplace_back(afterBarrier);
    }

    NGITextureBarrier thumbnailAfterBarrier
    {
        mSelectResInfo.FullBlit,
        0,
        NGIResourceState::TargetReadWrite,
        NGIResourceState::PixelShaderShaderResource,
    };
    afterBarriers.emplace_back(thumbnailAfterBarrier);

    std::sort(beforeBarriers.begin(), beforeBarriers.end(), [](auto& a, auto& b) { return a.Texture < b.Texture; });
    std::sort(afterBarriers.begin(), afterBarriers.end(), [](auto& a, auto& b) { return a.Texture < b.Texture; });

    cmdList->ResourceBarrier(0, nullptr, static_cast<UInt32>(beforeBarriers.size()), beforeBarriers.data());

    auto* transientResMgr = mRendererSys->GetTransientResourceManager();
    auto* stagingMgr = mRendererSys->GetScratchBuffer();
    auto* renderPrimitive = mRendererSys->GetRenderPrimitives();
    auto* pipelineStatePool = mRendererSys->GetGraphicsPipelineStatePool();
    auto* resourceGroupPool = mRendererSys->GetResourceGroupPool();

    auto fullBlitWidth = mSelectResInfo.FullBlit->GetDesc().Width;
    auto fullBlitHeight = mSelectResInfo.FullBlit->GetDesc().Height;
    NGIViewport viewport{ 0, 0, static_cast<float>(fullBlitWidth), static_cast<float>(fullBlitHeight), 0, 1, };
    NGIScissor scissor{ 0, 0, fullBlitWidth, fullBlitHeight };

    NGIFramebufferDesc framebufferDesc
    {
        mBlitRenderPass.get(),
        mSelectResInfo.FullBlit->GetDesc().Width,
        mSelectResInfo.FullBlit->GetDesc().Height,
        1,
        { mSelectResInfo.FullBlitView },
    };
    auto* framebuffer = transientResMgr->AllocateFramebuffer(framebufferDesc);

    NGIClearValue clearValue{ {0, 0, 0, 1} };

    NGITextureBarrier targetBarrier
    {
        mSelectResInfo.FullBlit,
        0,
        *mSelectResInfo.FullBlitBeforeFrameState,
        NGIResourceState::TargetReadWrite,
        true,
    };

    // thuumbnail texture will finally be in this state
    *mSelectResInfo.FullBlitBeforeFrameState = NGIResourceState::PixelShaderShaderResource;

    cmdList->BeginRenderPass(mBlitRenderPass.get(), framebuffer, 1, &clearValue, 1, &targetBarrier, false);
    auto* fullScreenQuad = mRendererSys->GetFullScreenTriangle();
    cmdList->SetVertexBuffers(1, &fullScreenQuad, nullptr);

    resource::Shader::ProgramDesc* programDesc = nullptr;
    switch (mSelectResInfo.SourceTextureView->GetDesc().SubRange.Aspect)
    {
    case NGITextureAspect::Color:
    {
        resource::ShaderVariationKey key{ mBlitColorShader };
        programDesc = mBlitColorShader->GetProgramDesc(key);
        break;
    }
    case NGITextureAspect::Depth:
    {
        resource::ShaderVariationKey key{ mBlitDepthShader };
        programDesc = mBlitDepthShader->GetProgramDesc(key);
        break;
    }
    case NGITextureAspect::Stencil:
    {
        resource::ShaderVariationKey key{ mBlitStencilShader };
        programDesc = mBlitStencilShader->GetProgramDesc(key);
        break;
    }
    default:
        Assert(false);
        break;
    }

    NGIGraphicsPipelineStateDesc pipelineStateDesc
    {
        mBlitRenderPass.get(),
        0,

        programDesc->GUID,
        &programDesc->GraphicsProgramDesc,
        programDesc->PipelineLayout,

        renderPrimitive->mNoVertexInputLayoutDesc.GetHash().GetHash(),
        &renderPrimitive->mNoVertexInputLayoutDesc,

        PrimitiveTopology::TriangleList,
        {
            FillMode::Solid,
            CullMode::None,
            FaceOrder::CCW,
        },
        {
            false,
            false,
            1,
            {
                {
                    false,
                    false,
                    BlendFactor::One,
                    BlendFactor::Zero,
                    NGIBlendOp::Add,
                    BlendFactor::One,
                    BlendFactor::Zero,
                    NGIBlendOp::Add,
                    LogicOp::And,
                    ColorMask::All,
                }
            },
        },
        {},
    };
    auto* graphicsPipelineState = pipelineStatePool->AllocateGraphicsPipelineState(pipelineStateDesc);
    cmdList->SetGraphicsPipelineState(graphicsPipelineState);

    cmdList->SetViewports(1, &viewport);
    cmdList->SetScissors(1, &scissor);

    auto stagingBufWrap = stagingMgr->AllocateScratch(NGIBufferUsage::ConstantBuffer, sizeof(UInt2));
    UInt2 renderTargetSize{ fullBlitWidth, fullBlitHeight };
    stagingBufWrap.MemWrite(0, &renderTargetSize, sizeof(UInt2));

    NGIResourceBinding bindings[]
    {
        NGIResourceBinding::BindTexture(NAME_ID("_InputTex"), mSelectResInfo.SourceTextureView),
        NGIResourceBinding::BindSampler(NAME_ID("texture_sampler"), renderPrimitive->mDefaultSampler.get()),
        NGIResourceBinding::BindConstBuffer(NAME_ID("params"), stagingBufWrap.GetNGIBuffer(), stagingBufWrap.GetNGIOffset(), sizeof(UInt2)),
    };
    std::sort(std::begin(bindings), std::end(bindings));
    auto* passRG = resourceGroupPool->Allocate(programDesc->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass], 3, bindings);
    cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, passRG);

    cmdList->DrawInstanced(6, 1, 0, 0);
    cmdList->EndRenderPass();

    cmdList->ResourceBarrier(0, nullptr, static_cast<UInt32>(afterBarriers.size()), afterBarriers.data());

    mSelectResInfo.ReadyToBlit = true;
}

void cross::REDVisualizer::BlitThumbnails(NGICommandList* cmdList, const std::vector<REDThumbnailInfo*> infos)
{
    std::vector<NGITextureBarrier> beforeBarriers;
    std::vector<NGITextureBarrier> afterBarriers;

    //UInt32 stagingBufferId = 1;
    for (auto& info : infos)
    {
        if (!EnumHasAllFlags(info->SourceTextureState, NGIResourceState::PixelShaderShaderResource))
        {
            NGITextureBarrier beforeBarrier
            {
                info->SourceTexture,
                info->SourceTextureSubresource,
                info->SourceTextureState,
                NGIResourceState::PixelShaderShaderResource,
            };
            beforeBarriers.emplace_back(beforeBarrier);

            NGITextureBarrier afterBarrier
            {
                info->SourceTexture,
                info->SourceTextureSubresource,
                NGIResourceState::PixelShaderShaderResource,
                info->SourceTextureState,
            };
            afterBarriers.emplace_back(afterBarrier);
        }

        NGITextureBarrier thumbnailAfterBarrier
        {
            info->Thumbnail,
            0,
            NGIResourceState::TargetReadWrite,
            NGIResourceState::PixelShaderShaderResource,
        };
        if (mIsBakeThumbnailData)
        {
            thumbnailAfterBarrier.StateBefore = NGIResourceState::CopySrc;
        }
        afterBarriers.emplace_back(thumbnailAfterBarrier);
    }

    std::sort(beforeBarriers.begin(), beforeBarriers.end(), [](auto& a, auto& b) { return a.Texture < b.Texture; });
    std::sort(afterBarriers.begin(), afterBarriers.end(), [](auto& a, auto& b) { return a.Texture < b.Texture; });

    cmdList->ResourceBarrier(0, nullptr, static_cast<UInt32>(beforeBarriers.size()), beforeBarriers.data());

    auto* transientResMgr = mRendererSys->GetTransientResourceManager();
    auto* stagingMgr = mRendererSys->GetScratchBuffer();
    auto* renderPrimitive = mRendererSys->GetRenderPrimitives();
    auto* pipelineStatePool = mRendererSys->GetGraphicsPipelineStatePool();
    auto* resourceGroupPool = mRendererSys->GetResourceGroupPool();

    NGIViewport viewport{ 0, 0, ThumbnailWidth, ThumbnailWidth, 0, 1, };
    NGIScissor scissor{ 0, 0, ThumbnailWidth, ThumbnailWidth };

    for (auto& info : infos)
    {
        NGIFramebufferDesc framebufferDesc
        {
            mBlitRenderPass.get(),
            info->Thumbnail->GetDesc().Width,
            info->Thumbnail->GetDesc().Height,
            1,
            { info->ThumbnailView },
        };
        auto* framebuffer = transientResMgr->AllocateFramebuffer(framebufferDesc);

        NGIClearValue clearValue{ {0, 0, 0, 1} };

        NGITextureBarrier targetBarrier
        {
            info->Thumbnail,
            0,
            *info->ThumbnailBeforeFrameState,
            NGIResourceState::TargetReadWrite,
            true,
        };

        // thuumbnail texture will finally be in this state
        *info->ThumbnailBeforeFrameState = NGIResourceState::PixelShaderShaderResource;

        cmdList->BeginRenderPass(mBlitRenderPass.get(), framebuffer, 1, &clearValue, 1, &targetBarrier, false);
        auto* fullScreenQuad = mRendererSys->GetFullScreenTriangle();
        cmdList->SetVertexBuffers(1, &fullScreenQuad, nullptr);

        resource::Shader::ProgramDesc* programDesc = nullptr;
        switch (info->SourceTextureView->GetDesc().SubRange.Aspect)
        {
        case NGITextureAspect::Color:
        {
            resource::ShaderVariationKey key{ mBlitColorShader };
            programDesc = mBlitColorShader->GetProgramDesc(key);
            break;
        }
        case NGITextureAspect::Depth:
        {
            resource::ShaderVariationKey key{ mBlitDepthShader };
            programDesc = mBlitDepthShader->GetProgramDesc(key);
            break;
        }
        case NGITextureAspect::Stencil:
        {
            resource::ShaderVariationKey key{ mBlitStencilShader };
            programDesc = mBlitStencilShader->GetProgramDesc(key);
            break;
        }
        default:
            Assert(false);
            break;
        }

        NGIGraphicsPipelineStateDesc pipelineStateDesc
        {
            mBlitRenderPass.get(),
            0,

            programDesc->GUID,
            &programDesc->GraphicsProgramDesc,
            programDesc->PipelineLayout,

            renderPrimitive->mNoVertexInputLayoutDesc.GetHash().GetHash(),
            &renderPrimitive->mNoVertexInputLayoutDesc,

            PrimitiveTopology::TriangleList,
            {
                FillMode::Solid,
                CullMode::None,
                FaceOrder::CCW,
            },
            {
                false,
                false,
                1,
                {
                    {
                        false,
                        false,
                        BlendFactor::One,
                        BlendFactor::Zero,
                        NGIBlendOp::Add,
                        BlendFactor::One,
                        BlendFactor::Zero,
                        NGIBlendOp::Add,
                        LogicOp::And,
                        ColorMask::All,
                    }
                },
            },
            {},
        };
        auto* graphicsPipelineState = pipelineStatePool->AllocateGraphicsPipelineState(pipelineStateDesc);
        cmdList->SetGraphicsPipelineState(graphicsPipelineState);

        cmdList->SetViewports(1, &viewport);
        cmdList->SetScissors(1, &scissor);

        auto stagingBufWrap = stagingMgr->AllocateScratch(NGIBufferUsage::ConstantBuffer, sizeof(UInt2));
        UInt2 renderTargetSize{ ThumbnailWidth, ThumbnailWidth };
        stagingBufWrap.MemWrite(0, &renderTargetSize, sizeof(UInt2));

        NGIResourceBinding bindings[]
        { 
            NGIResourceBinding::BindTexture("_InputTex", info->SourceTextureView),
            NGIResourceBinding::BindSampler("texture_sampler", renderPrimitive->mDefaultSampler.get()),
            NGIResourceBinding::BindConstBuffer("params", stagingBufWrap.GetNGIBuffer(), stagingBufWrap.GetNGIOffset(), sizeof(UInt2)),
        };
        std::sort(std::begin(bindings), std::end(bindings));
        auto* passRG = resourceGroupPool->Allocate(programDesc->PipelineLayout->GetDesc().ResourceGroupLayouts[ShaderParamGroup_Pass], 3, bindings);
        cmdList->SetGraphicsResourceGroup(ShaderParamGroup_Pass, passRG);

        cmdList->DrawInstanced(6, 1, 0, 0);
        cmdList->EndRenderPass();

        if (mIsBakeThumbnailData)
        {
            //Prepare Buffer&&Barriers;
            auto bufferPitch = ThumbnailWidth * 4;
            auto bufferRange = bufferPitch * ThumbnailWidth;
            NGICopyBufferTexture region
            {
                0,
                bufferRange,
                bufferPitch,

                0,
                {
                    0,
                    0,
                    0,
                },
                {
                    ThumbnailWidth,
                    ThumbnailWidth,
                    1,
                },
            };
            NGIBufferDesc stagingBufferDesc{
                bufferRange,
                NGIBufferUsage::CopyDst,
            };
            std::unique_ptr<NGIStagingBuffer> stagingBuffer{ GetNGIDevice().CreateStagingBuffer(stagingBufferDesc) };

            NGITextureBarrier thumbnailCopyBeforeBarrier
            {
                info->Thumbnail,
                0,
                NGIResourceState::TargetReadWrite,
                NGIResourceState::CopySrc,
            };

            cmdList->ResourceBarrier(0, nullptr, 1, &thumbnailCopyBeforeBarrier);

            cmdList->CopyTextureToBuffer(stagingBuffer.get(), info->Thumbnail, 1, &region);

            NGIBufferBarrier bufferCopyAfterBarrier
            {
                stagingBuffer.get(),
                NGIResourceState::CopyDst,
                NGIResourceState::HostRead,
            };

            cmdList->ResourceBarrier(1, &bufferCopyAfterBarrier, 0, nullptr);

            mRendererSys->ReadBackBuffer(std::move(stagingBuffer), info->ThumbnailData, bufferRange);
        }
    }

    cmdList->ResourceBarrier(0, nullptr, static_cast<UInt32>(afterBarriers.size()), afterBarriers.data());
}

void cross::REDVisualizer::PrepareNode()
{
    if (!GetTextureView())
        return;

    auto* transientResMgr = mRendererSys->GetTransientResourceManager();

    auto PrepareForVisImpl = [&](VisualizerImpl* vImpl)
    {
        // pass for resource with extended lifetime
        REDPass importPass{ "Import Pass", true, nullptr };
        REDResourceRangeState stateInImportPass;
        stateInImportPass.mFirstPass = &importPass;
        stateInImportPass.mLastPass = &importPass;
        for (auto& texture : mRED->mTextures)
        {
            for (auto& subres : texture.mSubresources)
            {
                switch (texture.mType)
                {
                case REDResourceType::External:
                    subres.LastWriter = mRED->mProloguePass;
                    break;
                case REDResourceType::ExtendedFromLast:
                    subres.LastWriter = &importPass;
                    break;
                default:
                    subres.LastWriter = nullptr;
                    break;
                }
            }
        }

        for (auto& buffer : mRED->mBuffers)
        {
            switch (buffer.mType)
            {
            case REDResourceType::External:
                buffer.mSubresource.LastWriter = mRED->mProloguePass;
                break;
            case REDResourceType::ExtendedFromLast:
                buffer.mSubresource.LastWriter = &importPass;
                break;
            default:
                buffer.mSubresource.LastWriter = nullptr;
                break;
            }
        }

        uintptr_t globalPinID = 1;

        bool afterVisualizerPass = false;

        vImpl->ClearGraphNode();

        uintptr_t globalNodeID = 1;
        uint32_t globalTexID = 1;

        for (auto* pass : mRED->mPasses)
        {
            if (!pass->IsVisited())
            {
                continue;
            }

            if (pass->GetName() == gREDVisualizerPassName)
            {
                afterVisualizerPass = true;
            }

            VisualizerImpl::PassNode passNode{ pass->GetName(), globalNodeID++ };
            auto rSize = vImpl->mRegionInfos.size();
            if ((rSize == 0) || (pass->mRegion != vImpl->mRegionInfos[rSize - 1].mRefRegion) || (pass->mRegion->Depth == 0))
            {
                std::vector<std::string> names;
                auto parent = pass->mRegion->Parent;
                if (pass->mRegion->Depth > 0) {
                    for (UInt32 nameId = 0; nameId < pass->mRegion->Depth - 1; nameId++)
                    {
                        names.push_back(parent->Name);
                        parent = parent->Parent;
                    }
                }
                vImpl->mRegionInfos.push_back({
                    pass->mRegion->Name,
                    pass->mRegion->Depth,
                    pass->mRegion,
                    std::move(names),
                    static_cast<UInt32>(rSize),
                    static_cast<UInt32>(vImpl->mPassNodes.size()),
                    static_cast<UInt32>(vImpl->mPassNodes.size())
                    });
                passNode.mRegionInfo = &(vImpl->mRegionInfos[rSize]);
            }
            else
            {
                passNode.mRegionInfo = &(vImpl->mRegionInfos[rSize - 1]);
                vImpl->mRegionInfos[rSize - 1].mEndPassId = static_cast<UInt32>(vImpl->mPassNodes.size());
            }

            for (auto& [texture, subres] : pass->mTextures)
            {
                auto name = fmt::format("{}-{}", texture.Texture->mName, texture.Subresource);
                auto nameHash = vImpl->GetStringHash(name.c_str());
                if (subres.State.Read)
                {
                    VisualizerImpl::RangeStateInfo info{ nameHash, *(subres.MergedState), subres.MergedState->mFirstPass, subres.MergedState->mLastPass, true, texture.Texture, nullptr };
                    passNode.ReadPins.push_back({ name, {subres.State, info} });

                    if (auto* lastWriter = texture.Texture->mSubresources[texture.Subresource].LastWriter; lastWriter && lastWriter != pass)
                    {
                        if (auto ret0 = lastWriter->mTextures.find(texture); ret0 != lastWriter->mTextures.end())
                        {
                            passNode.ReadPins.back().StateInLastWriter = { nameHash, *(ret0->second.MergedState), ret0->second.MergedState->mFirstPass, ret0->second.MergedState->mLastPass, true, texture.Texture, nullptr };
                        }
                        else if (lastWriter == &importPass)
                        {
                            passNode.ReadPins.back().StateInLastWriter = { nameHash, stateInImportPass, stateInImportPass.mFirstPass, stateInImportPass.mLastPass, true, texture.Texture, nullptr };
                        }
                    }
                }

                if (subres.State.Write && pass->mName != "Epilogue Pass")
                {
                    VisualizerImpl::RangeStateInfo info{ nameHash, *(subres.MergedState), subres.MergedState->mFirstPass, subres.MergedState->mLastPass, true, texture.Texture, nullptr };

                    node::PinId pinID = globalPinID++;
                    passNode.WritePins.push_back({ name, {subres.State, info}, pinID });

                    if (auto ret = vImpl->mResNodes.find(info); ret == vImpl->mResNodes.end())
                    {
                        VisualizerImpl::ResourceNode node{
                            name,
                            globalNodeID++,
                            {pinID},
                            globalPinID++,
                            globalPinID++,
                        };

                        auto& texDesc = texture.Texture->mDesc;
                        auto cap = GetNGIDevice().GetFormatCapability(texDesc.Format);
                        auto textureSupportShaderResource = EnumHasAnyFlags(texDesc.Usage, NGITextureUsage::ShaderResource);
                        auto textureSupportSeperateView = texDesc.Dimension == NGITextureType::Texture2D || texDesc.SeparateView;
                        auto formatSupportBeShaderResource = EnumHasAnyFlags(cap.TextureCapability, NGITextureUsage::ShaderResource);

                        if (!afterVisualizerPass && textureSupportShaderResource && formatSupportBeShaderResource && textureSupportSeperateView)
                        {
                            auto [mip, array, plane] = NGIDecomposeSubresource(texture.Subresource, texDesc.MipCount, texDesc.ArraySize);
                            auto [hasDepth, hasStencil] = FormatHasDepthStencil(texDesc.Format);
                            NGITextureAspect aspect = NGITextureAspect::Color;
                            if (hasDepth)
                            {
                                if (hasStencil)
                                {
                                    switch (plane)
                                    {
                                    case 0:
                                        aspect = NGITextureAspect::Depth;
                                        break;
                                    case 1:
                                        aspect = NGITextureAspect::Stencil;
                                        break;
                                    default:
                                        Assert(false);
                                        break;
                                    }
                                }
                                else
                                {
                                    aspect = NGITextureAspect::Depth;
                                }
                            }
                            else
                            {
                                if (hasStencil)
                                {
                                    aspect = NGITextureAspect::Stencil;
                                }
                                else
                                {
                                    aspect = NGITextureAspect::Color;
                                }
                            }

                            NGITextureViewDesc sourceViewDesc{
                                NGITextureUsage::ShaderResource,
                                texDesc.Format,
                                NGITextureType::Texture2D,
                                {
                                    aspect,
                                    mip,
                                    1,
                                    array,
                                    1,
                                },
                            };

                            NGITextureDesc thumbnailDesc{
                                GraphicsFormat::R8G8B8A8_SRGB,
                                NGITextureType::Texture2D,
                                1,
                                1,
                                REDVisualizer::ThumbnailWidth,
                                REDVisualizer::ThumbnailWidth,
                                1,
                                1,
                                NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                            };
                            NGITextureViewDesc thumbnailRTVDesc{
                                NGITextureUsage::RenderTarget,
                                GraphicsFormat::R8G8B8A8_SRGB,
                                NGITextureType::Texture2D,
                                {
                                    NGITextureAspect::Color,
                                    0,
                                    1,
                                    0,
                                    1,
                                },
                            };
                            auto [thumbnail, state] = transientResMgr->AllocateTexture(thumbnailDesc, name, false);

                            REDThumbnailInfo thumbnailInfo{
                                texture.Texture->mNativeTexture,
                                texture.Subresource,
                                transientResMgr->AllocateTextureView(sourceViewDesc, texture.Texture->mNativeTexture),
                                subres.MergedState->NGIState,

                                thumbnail,
                                transientResMgr->AllocateTextureView(thumbnailRTVDesc, thumbnail),
                                state,
                            };
                            node.Thumbnail = thumbnail;
                            auto dataSize = REDVisualizer::ThumbnailWidth * REDVisualizer::ThumbnailWidth * 4;
                            if (mIsBakeThumbnailData)
                            {
                                if (!vImpl->mThumbnailDatas.count(info))
                                {
                                    UInt8* data = new UInt8[dataSize];
                                    memset(data, 0, dataSize);
                                    vImpl->mThumbnailDatas.emplace(info, data);
                                }
                                node.ThumbnailID = globalTexID++;
                                node.ThumbnailData = vImpl->mThumbnailDatas[info];
                                thumbnailInfo.ThumbnailData = vImpl->mThumbnailDatas[info];
                            }
                            // node.Thumbnail = ImGui::GetFont()->ContainerAtlas->TexID;
                            mThumbnails.emplace(subres.MergedState, thumbnailInfo);

                            //BlitSelectNodeInfo
                            if (mSelectResInfo.SelectState)
                            {
                                if (pass->GetName() == mSelectResInfo.WritePassName && nameHash == mSelectResInfo.NameHash)
                                {
                                    NGITextureDesc fullBlitDesc{
                                        GraphicsFormat::R8G8B8A8_SRGB,
                                        NGITextureType::Texture2D,
                                        1,
                                        1,
                                        texDesc.Width,
                                        texDesc.Height,
                                        1,
                                        1,
                                        NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                    };
                                    NGITextureViewDesc fullBlitView{
                                        NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                        GraphicsFormat::R8G8B8A8_SRGB,
                                        NGITextureType::Texture2D,
                                        {
                                            NGITextureAspect::Color,
                                            0,
                                            1,
                                            0,
                                            1,
                                        },
                                    };
                                    auto [fullBlit, fState] = transientResMgr->AllocateTexture(fullBlitDesc, name + "_fullBlit", false);
                                    mSelectResInfo.SourceTexture = texture.Texture->mNativeTexture;
                                    mSelectResInfo.SourceTextureSubresource = texture.Subresource;
                                    mSelectResInfo.SourceTextureView = transientResMgr->AllocateTextureView(sourceViewDesc, texture.Texture->mNativeTexture);
                                    mSelectResInfo.SourceTextureState = subres.MergedState->NGIState;
                                    mSelectResInfo.FullBlit = fullBlit;
                                    mSelectResInfo.FullBlitView = transientResMgr->AllocateTextureView(fullBlitView, fullBlit);
                                    mSelectResInfo.FullBlitBeforeFrameState = fState;
                                    mSelectResInfo.PrepareState = true;
                                }
                            }
                        }

                        vImpl->mResNodes.emplace(info, std::move(node));
                    }
                    else
                    {
                        ret->second.WriterIDs.emplace_back(pinID);
                    }

                    texture.Texture->mSubresources[texture.Subresource].LastWriter = pass;
                }
            }

            for (auto& [buffer, subres] : pass->mBuffers)
            {
                auto name = fmt::format("{}-{}", buffer->mName, 0);
                auto nameHash = vImpl->GetStringHash(name.c_str());
                if (subres.State.Read)
                {
                    VisualizerImpl::RangeStateInfo info{ nameHash, *(subres.MergedState), subres.MergedState->mFirstPass, subres.MergedState->mLastPass, false, nullptr, buffer };
                    passNode.ReadPins.push_back({
                        name,
                        {subres.State, info},
                        });

                    if (auto* lastWriter = buffer->mSubresource.LastWriter; lastWriter && lastWriter != pass)
                    {
                        if (auto ret0 = lastWriter->mBuffers.find(buffer); ret0 != lastWriter->mBuffers.end())
                        {
                            passNode.ReadPins.back().StateInLastWriter = { nameHash, *(ret0->second.MergedState), ret0->second.MergedState->mFirstPass, ret0->second.MergedState->mLastPass, false, nullptr, buffer };
                        }
                        else if (lastWriter == &importPass)
                        {
                            passNode.ReadPins.back().StateInLastWriter = { nameHash, stateInImportPass, stateInImportPass.mFirstPass, stateInImportPass.mLastPass, false, nullptr, buffer };
                        }
                    }
                }
                if (subres.State.Write && pass->mName != "Epilogue Pass")
                {
                    node::PinId pinID = globalPinID++;
                    VisualizerImpl::RangeStateInfo info{ nameHash, *(subres.MergedState), subres.MergedState->mFirstPass, subres.MergedState->mLastPass, false, nullptr, buffer };
                    passNode.WritePins.push_back({ name, {subres.State, info}, pinID });

                    if (auto ret = vImpl->mResNodes.find(info); ret == vImpl->mResNodes.end())
                    {
                        VisualizerImpl::ResourceNode node{
                            name,
                            globalNodeID++,
                            {pinID},
                            globalPinID++,
                            globalPinID++,
                        };
                        auto [itr, _] = vImpl->mResNodes.emplace(info, std::move(node));
                    }
                    else
                    {
                        ret->second.ReaderIDs.emplace_back(pinID);
                    }

                    buffer->mSubresource.LastWriter = pass;
                }
            }

            std::sort(passNode.ReadPins.begin(), passNode.ReadPins.end(), [](auto& a, auto& b) { return a.Name < b.Name; });
            std::sort(passNode.WritePins.begin(), passNode.WritePins.end(), [](auto& a, auto& b) { return a.Name < b.Name; });

            for (auto& readPin : passNode.ReadPins)
            {
                readPin.ID = globalPinID++;
                auto nameHash = vImpl->GetStringHash(std::to_string(globalPinID).c_str());
                if (auto ret1 = vImpl->mResNodes.find(readPin.StateInLastWriter); ret1 != vImpl->mResNodes.end())
                {
                    ret1->second.ReaderIDs.emplace_back(readPin.ID);
                }
                else
                {
                    VisualizerImpl::RangeStateInfo rangeinfo{ nameHash, stateInImportPass, stateInImportPass.mFirstPass, stateInImportPass.mLastPass };
                    if (readPin.StateInLastWriter.firstpass == rangeinfo.firstpass && readPin.StateInLastWriter.lastpass == rangeinfo.lastpass && readPin.StateInLastWriter.state.NGIState == rangeinfo.state.NGIState)
                    {
                        vImpl->mImportedResReaders.emplace_back(readPin.ID);
                    }
                }
            }

            vImpl->mPassNodes.emplace_back(std::move(passNode));
        }
        vImpl->BuildRegionBlocks();
    };

    auto PrepareSelectInfoForFreeze = [&](VisualizerImpl* vImpl)
    {
        if (!vImpl->mSelectInfo->SelectState) return;
        bool afterVisualizerPass = false;
        for (auto* pass : mRED->mPasses)
        {
            if (!pass->IsVisited())
            {
                continue;
            }

            if (pass->GetName() == gREDVisualizerPassName)
            {
                afterVisualizerPass = true;
            }

            for (auto& [texture, subres] : pass->mTextures)
            {
                auto name = fmt::format("{}-{}", texture.Texture->mName, texture.Subresource);
                auto nameHash = vImpl->GetStringHash(name.c_str());
                auto& texDesc = texture.Texture->mDesc;

                if (subres.State.Write && pass->mName != "Epilogue Pass")
                {
                    if (pass->GetName() == vImpl->mSelectInfo->WritePassName && nameHash == vImpl->mSelectInfo->NameHash)
                    {
                        auto [mip, array, plane] = NGIDecomposeSubresource(texture.Subresource, texDesc.MipCount, texDesc.ArraySize);
                        auto [hasDepth, hasStencil] = FormatHasDepthStencil(texDesc.Format);
                        NGITextureAspect aspect = NGITextureAspect::Color;
                        if (hasDepth)
                        {
                            if (hasStencil)
                            {
                                switch (plane)
                                {
                                case 0:
                                    aspect = NGITextureAspect::Depth;
                                    break;
                                case 1:
                                    aspect = NGITextureAspect::Stencil;
                                    break;
                                default:
                                    Assert(false);
                                    break;
                                }
                            }
                            else
                            {
                                aspect = NGITextureAspect::Depth;
                            }
                        }
                        else
                        {
                            if (hasStencil)
                            {
                                aspect = NGITextureAspect::Stencil;
                            }
                            else
                            {
                                aspect = NGITextureAspect::Color;
                            }
                        }

                        NGITextureViewDesc sourceViewDesc{
                            NGITextureUsage::ShaderResource,
                            texDesc.Format,
                            NGITextureType::Texture2D,
                            {
                                aspect,
                                mip,
                                1,
                                array,
                                1,
                            },
                        };
                        NGITextureDesc fullBlitDesc
                        {
                            GraphicsFormat::R8G8B8A8_SRGB,
                            NGITextureType::Texture2D,
                            1,
                            1,
                            texDesc.Width,
                            texDesc.Height,
                            1,
                            1,
                            NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                        };
                        NGITextureViewDesc fullBlitRTVDesc{
                            NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                            GraphicsFormat::R8G8B8A8_SRGB,
                            NGITextureType::Texture2D,
                            {
                                NGITextureAspect::Color,
                                0,
                                1,
                                0,
                                1,
                            },
                        };
                        auto [fullBlit, fState] = transientResMgr->AllocateTexture(fullBlitDesc, name + "_fullBlit", false);
                        mSelectResInfo.SourceTexture = texture.Texture->mNativeTexture;
                        mSelectResInfo.SourceTextureSubresource = texture.Subresource;
                        mSelectResInfo.SourceTextureView = transientResMgr->AllocateTextureView(sourceViewDesc, texture.Texture->mNativeTexture);
                        mSelectResInfo.SourceTextureState = subres.MergedState->NGIState;
                        mSelectResInfo.FullBlit = fullBlit;
                        mSelectResInfo.FullBlitView = transientResMgr->AllocateTextureView(fullBlitRTVDesc, fullBlit);
                        mSelectResInfo.FullBlitBeforeFrameState = fState;
                        mSelectResInfo.PrepareState = true;
                    }
                }
            }
        }
    };

    bool isPrepare = false;
    if (GetTextureView() && !mVisEditor->mFreeze)
    {
        PrepareForVisImpl(mVisEditor.get());
        isPrepare = true;
    }
    else if (GetTextureView() && mVisEditor->mFreeze)
    {
        PrepareSelectInfoForFreeze(mVisEditor.get());
        isPrepare = true;
    }

    if (mIsBakeThumbnailData && mVisImguiWS && !mVisImguiWS->mFreeze)
    {
        if (!isPrepare)
        {
            PrepareForVisImpl(mVisImguiWS.get());
        }
        else
        {
            mVisImguiWS->CopyREDRes(mVisEditor.get());
        }
    }
}