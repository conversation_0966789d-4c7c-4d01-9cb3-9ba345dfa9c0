#pragma once
#include "CrossSchema/ShaderAsset_generated.h"
#include "CrossBase/String/NameID.h"
#include "NativeGraphicsInterface/NGI.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDResourceManager.h"
#include "NativeGraphicsInterface/NGITransientResourceManager.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDPass.h"
#include "RenderEngine/RenderContext.h"
#include "RenderEngine/RenderEngineForward.h"
#include "RenderEngine/RenderingExecutionDescriptor/REDImGui/REDVisualizer.h"
#include <vector>
#include <set>
#include <tuple>
#include <iterator>
#include <cstddef>
#include <queue>
#include <array>
#if _WIN32
    #include <memory_resource>
#endif
namespace cross 
{
/*
 * manage memory used by REDResource
 * reserve a chunk of memory(64KB)
 * commit physical page as needed
 * decommit physical page if ref count was zero
 * never free virtual memory address
 */
struct REDAllocator
{
    REDAllocator(UInt32 frameLatency = 3);
    ~REDAllocator();

    std::size_t Allocate(std::size_t size, std::size_t alignment);

    void Deallocate(std::size_t ptr, std::size_t size) noexcept;

    void Tick(UInt64 frameID);

private:
    constexpr static std::size_t gMemoryBlockSize = 64 * 1024;
    constexpr static std::size_t gMemoryPageSize = DefaultMemoryPageSize;
    constexpr static std::size_t gMemoryPageCountPerBlock = gMemoryBlockSize / gMemoryPageSize;

    struct MemoryBlock
    {
        std::size_t Begin;
        std::size_t End;
        UInt32 PageRefCounts[gMemoryPageCountPerBlock];
        UInt32 RefCount;
    };

    std::map<std::size_t, MemoryBlock> mHistoryPages;

    std::size_t mCurrentBlockPtr;
    MemoryBlock mCurrentBlock;
    void AllocateBlock();

    std::vector<std::vector<void*>> mGarbageQueue;
    UInt32 mCurrentFrameIndex = 0;
};

template<class T>
struct REDAllocatorWrapper
{
    using value_type = T;

    REDAllocatorWrapper(REDAllocator& allocator) noexcept : mAllocator{ allocator } {}
    
    template<class U>
    REDAllocatorWrapper(const REDAllocatorWrapper<U>& alloc) noexcept : mAllocator{ alloc.mAllocator } {}

    T* allocate(std::size_t count)
    {
        return reinterpret_cast<T*>(mAllocator.Allocate(sizeof(T) * count, alignof(T)));
    }

    void deallocate(T* address, std::size_t count) noexcept
    {
        mAllocator.Deallocate(reinterpret_cast<std::size_t>(address), sizeof(T) * count);
    }

    REDAllocator& mAllocator;
};

struct RenderingExecutionDescriptor
{
    friend struct REDVisualizer;
    friend class RendererSystemR;
    RenderingExecutionDescriptor(NGIResourceManager* transientResMgr, PersistentResourceManager* persistentResMgr, NGITransientBufferManager* stagingMgr);

    // allocate a render pass
    RENDER_ENGINE_API REDRenderPass* BeginRenderPass(std::string_view name, UInt32 numColorTargets, const REDColorTargetDesc* pColorTargetDescs, const REDDepthStencilTargetDesc* pDepthStencilDesc, bool noCulling = false);

    // for raster-only passes with no render targets bound to the pass
    REDRenderPass* BeginRenderPass(std::string_view name, UInt32 width, UInt32 height, UInt32 layerCount, UInt32 sampleCount, bool noCulling = false);

    RENDER_ENGINE_API REDPass* AllocateSubRenderPass(std::string_view name, UInt32 inputTargetCount, NGIRenderPassTargetIndex* pInputTargetIndices, UInt32 colorTargetCount, NGIRenderPassTargetIndex* pColorTargetIndices,
        REDPassFlagBit flags);

    RENDER_ENGINE_API void EndRenderPass();

    // allocate a compute/copy pass
    virtual RENDER_ENGINE_API REDPass* AllocatePass(std::string_view name, bool noCulling = false);

public:
    REDCullingResult* Cull(const REDCullingDesc& desc);
    REDCullingResult* CullShadowCaster(const REDCullingDesc& desc);
    LightCullingResult* CullVisibleLight(RenderWorld* world, RenderCamera* camera);
    REDCullingResult* CullLocalLightShadowCaster(RenderWorld* world, RenderCamera* camera, BoundingBox* boundingBox, REDObjectType objectMask);

    /* REDCullingResult* Cull(RenderWorld* world, RenderCamera* camera, REDObjectType objectMask = REDObjectType::All, REDObjectBoundingType objectBoundingType = REDObjectBoundingType::AABB);
    REDCullingResult* CullVisibleLight(RenderWorld* world, RenderCamera* camera);
    REDCullingResult* CullLocalLightShadowCaster(RenderWorld* world, RenderCamera* camera, REDObjectType objectMask, REDObjectBoundingType objectBoundingType, REDCullingResult* visibleLightCullingResult);*/

    RENDER_ENGINE_API virtual REDRegion* BeginRegion(std::string_view name);

    RENDER_ENGINE_API void EndRegion();

    void SetProperty(const NameID& name, const void* data, size_t dataSize)
    {
        GetCurrentRegion()->PropertySet.SetProperty(name, data, dataSize);
    }

    template<typename T, typename = typename std::enable_if_t<IsOneOf<T, NUMERIC_PROPERTY_TYPES, ALIGNED_NUMERIC_PROPERTY_TYPES, NGISampler*, NGIBufferView*, NGITextureView*, REDTextureView*, REDBufferView*>>>
    void SetProperty(const NameID& name, T&& value)
    {
        if constexpr (std::is_same_v<char, REDTextureView*> || std::is_same_v<char, REDBufferView*>)
        {
            GetCurrentRegion()->PropertySet.SetProperty(name, 1, &value);
        }
        else
        {
            GetCurrentRegion()->PropertySet.SetProperty(name, std::forward<T>(value));
        }

    }


    // allocate a RED texture from scratch
    virtual REDTexture* AllocateTexture(std::string_view name, const NGITextureDesc& desc);

    // allocate a RED texture with external NGI texture
    // Deprecated!!!
    virtual REDTexture* AllocateTexture(std::string_view name, NGITexture* externalTexture);

    // allocate external texture with same external state
    REDTexture* AllocateTexture(std::string_view name, NGITexture* externalTexture, NGIResourceState externalState);

    // allocate external texture with external state for each subresource
    REDTexture* AllocateTexture(std::string_view name, NGITexture* externalTexture, NGIResourceState* externalState);

    // find REDTexture associated with external NGITexture
    virtual REDTexture* FindExternalTexture(NGITexture* externalTexture);

    virtual REDTextureView* AllocateTextureView(REDTexture* texture, const NGITextureViewDesc& desc);

    virtual REDBuffer* AllocateBuffer(std::string_view name, const NGIBufferDesc& desc);

    // allocate external buffer with same external state
    REDBuffer* AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer, NGIResourceState externalState);

    virtual REDBuffer* FindExternalBuffer(NGIBuffer* externalBuffer);

    // allocate a RED buffer with external NGI buffer
    // Deprecated!!!
    virtual REDBuffer* AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer);

    virtual REDBufferView* AllocateBufferView(REDBuffer* buffer, const NGIBufferViewDesc& desc);

    virtual REDUniquePtr<REDResidentBuffer> CreateBuffer(std::string_view name, const NGIBufferDesc& desc);

    REDUniquePtr<REDResidentBufferView> CreateBufferView(REDResidentBuffer* buffer, const NGIBufferViewDesc& desc);

    virtual REDUniquePtr<REDResidentTexture> CreateTexture(std::string_view name, const NGITextureDesc& desc);

    REDUniquePtr<REDResidentTextureView> CreateTextureView(REDResidentTexture* texture, const NGITextureViewDesc& desc);

    virtual void Destroy(REDResidentObject* object);

    inline static const std::string_view gFlushStatePassName = "__FlushPass__";

    // Force change external REDBuffer to it's external state, or it would be change in epilogue pass. Useful when later be used as NGIBuffer.
    RENDER_ENGINE_API void FlushState(REDBuffer* buffer);

    // Force change REDResidentBuffer new state. Useful when later be used as NGIBuffer.
    RENDER_ENGINE_API void FlushState(REDResidentBuffer* buffer, NGIResourceState newState);

    // Force change external REDTexture to it's external state, or it would be change in epilogue pass. Useful when later be used as NGITexture.
    RENDER_ENGINE_API void FlushState(REDTexture* texture, UInt32 subResource = NGIAllSubresources);

    // Force change REDResidentTexture to new state. Useful when later be used as NGITexture.
    RENDER_ENGINE_API void FlushState(REDResidentTexture* texture, NGIResourceState newState, UInt32 subResource = NGIAllSubresources);

    virtual bool Validate(REDTexture* texture);

    virtual bool Validate(REDBuffer* buffer);

    // can be called in callback before execution
    virtual void QueueBufferUpload(REDBuffer* dstBuffer, UInt32 dstBufferOffset, const void* initialData, UInt32 initialDataSize);

    virtual void QueueBufferUpload(REDBuffer* dstBuffer, cross::NGIStagingBuffer* srcBuffer, NGICopyBuffer region);

    // read buffer of undecided size, full copy by default
    virtual void QueueBufferFeedBackDynamic(REDBuffer* srcBuffer, cross::NGIStagingBuffer* dstBuffer, std::pmr::vector<UInt8>* data);

    void BeginFrame(UInt64 frameID);

    virtual void EndFrame();

    // build dependency between passes, create render pass/framebuffer/transient resource
    RENDER_ENGINE_API void CompileAndExecute(NGICommandList* copyCmdList, NGICommandList* preCmdList, NGICommandList* postCmdList, std::vector<NGICommandList*>& cmdLists, NGICommandQueue* cmdQue);

    template<typename TFunc>
    void VisitValidPayloads(TFunc&& func)
    {
        for (auto* pass : mPasses)
        {
            if (pass->IsVisited())
            {
                if (pass->mRenderPass)
                {
                    for (auto* subpass : pass->mRenderPass->mSubpasses)
                    {
                        for (auto& ex : subpass->mPayloads)
                        {
                            func(subpass, ex);
                        }
                    }
                }
                else
                {
                    for (auto& ex : pass->mPayloads)
                    {
                        func(pass, ex);
                    }
                }
            }
        }
    }

    template<typename TVisitor>
    void VisitValidPasses(TVisitor&& visitor)
    {
        for (auto* pass : mPasses)
        {
            if (pass->IsVisited())
            {
                if (pass->mRenderPass)
                {
                    for (auto* subpass : pass->mRenderPass->mSubpasses)
                    {
                        if (subpass->IsVisited())
                        {
                            visitor(subpass);
                        }
                    }
                }
                else
                {
                    visitor(pass);
                }
            }
        }
    }

    void MarkNeedThumbnail()
    {
        mEnableVisualizer = true;
    }

    void SetVisualizer(REDVisualizer* visualizer) { mVisualizer = visualizer; }

    void ToggleNoDrawWorldMode() 
    {
        mNoDrawWorldMode = !mNoDrawWorldMode;
    }
    const auto GetREDFrameAllocator()
    {
        return &mCurrentAllocator;
    }
    auto FindCommonParent(REDRegion* a, REDRegion* b);

    void IterateToParent(REDRegion* region, REDRegion* parent, NGICommandList* cmdList);

    void MarkDebugRegion(REDRegion* curRegion, REDRegion* nextRegion, NGICommandList* cmdList);

    virtual REDRegion* GetCurrentRegion();

    REDRegion* GetRootRegion();

    virtual void SetCurrentRegion(REDRegion* region);

    virtual REDRenderPass* GetCurrentRenderPass();

    virtual void SetCurrentRenderPass(REDRenderPass* renderPass);

    auto GetFrameId() const
    { return mFrameID;
    }

protected:
    const std::vector<REDPass*>& GetPasses() { return mPasses; }
protected:
    REDCulling mCulling;

    FrameDeque<REDPass> mPassPoolOdd;
    FrameDeque<REDPass> mPassPoolEven;
    FrameDeque<REDPass>* mPassPool = nullptr;
    threading::TaskEventPtr mClearEvent;
    
    std::vector<REDPass*> mPasses;
    std::vector<REDPass*> mFlushStatePasses;

    virtual REDPass* AllocateSubPass(std::string_view name);
    virtual REDPass* AllocateFlushStatePass();

    REDPass* mProloguePass;
    REDPass* mEpiloguePass;

    REDAllocator mAllocator{};

    REDAllocator mBufferAllocator{};

    std::list<REDTexture, REDAllocatorWrapper<REDTexture>> mTextures{ REDAllocatorWrapper<REDTexture>{ mAllocator } };
    std::deque<REDTextureView> mTextureViews;

    std::list<REDBuffer, REDAllocatorWrapper<REDBuffer>> mBuffers{ REDAllocatorWrapper<REDBuffer>{ mBufferAllocator } };
    std::deque<REDBufferView> mBufferViews;


    std::set<REDResidentBuffer*> mResidentBuffers;
    std::set<REDResidentTexture*> mResidentTextures;
    std::vector<REDResidentObject*> mPendingDestroyObjects;

    REDRenderPass* mCurrentRenderPass = nullptr;

    FrameDeque<REDResourceRangeState> mRangeStatePool;
   
    virtual REDResourceRangeState* AllocateResourceRangeState(const REDResourceState& state, REDPass* firstPass, REDPass* lastPass)
    {
        return &mRangeStatePool.emplace_back(state, firstPass, lastPass);
    }

    std::deque<REDRenderPass> mRenderPassPool;

    virtual REDRenderPass* AllocateRenderPassInfo()
    {
        return &mRenderPassPool.emplace_back();
    }

    static void MarkVisitedPass(REDPass* pass);

    UInt64 mFrameID;
    NGIResourceManager* mTransientResMgr;
    PersistentResourceManager* mPersistentResMgr;
    NGITransientBufferManager* mStagingMgr;

    std::map<REDBuffer*, std::vector<std::tuple<NGIBuffer*, NGICopyBuffer>>> mBufferUploads;

    std::map<REDBuffer*, std::vector<std::tuple<NGIStagingBuffer*, std::pmr::vector<UInt8>*>>> mBufferFeedBack;



    // visualizer
    REDVisualizer* mVisualizer;

    bool mEnableVisualizer = false;
    bool mNoDrawWorldMode = false; //Ignore draw commands

    // debug region
    std::deque<REDRegion> mRegionPool;

    FrameAllocatorPool mCurrentAllocator;
    REDRegion* mCurrentRegion = nullptr;
};


// this is a simple implementation of threadSafe RED.
// but it suffers from costly pass lock and pass contructor, make it has little advantage during parrallel assemble
// the biggest problem lies down our assemble and system's, they are twisted with read/write in weird ways.
// long way to successfully parallize all.
struct ThreadSafeRenderingExecutionDescriptor : public RenderingExecutionDescriptor
{
public:
    ThreadSafeRenderingExecutionDescriptor(NGIResourceManager* transientResMgr, PersistentResourceManager* persistentResMgr, NGITransientBufferManager* stagingMgr) :
        RenderingExecutionDescriptor(transientResMgr, persistentResMgr, stagingMgr) {}
protected:
    std::mutex mPassMutex;
    //std::vector<REDPass*> mPasses;
    //std::vector<REDPass*> mFlushStatePasses;

    cross::REDPass * AllocatePass(std::string_view name, bool noCulling);

    cross::REDPass* AllocateSubPass(std::string_view name);

    cross::REDPass* AllocateFlushStatePass();

    cross::REDTexture* AllocateTexture(std::string_view name, const NGITextureDesc& desc);

    cross::REDTexture* AllocateTexture(std::string_view name, NGITexture* externalTexture);

    REDTextureView* AllocateTextureView(REDTexture* texture, const NGITextureViewDesc& desc);

    // find REDTexture associated with external NGITexture
    virtual REDTexture* FindExternalTexture(NGITexture* externalTexture);

    std::mutex mTextureMutex;
    //std::list<REDTexture, REDAllocatorWrapper<REDTexture>> mTextures{ REDAllocatorWrapper<REDTexture>{ mAllocator } };
    std::mutex mTextureViewsMutex;
    //std::deque<REDTextureView> mTextureViews;

    cross::REDBuffer* AllocateBuffer(std::string_view name, const NGIBufferDesc& desc);

    cross::REDBuffer* AllocateBuffer(std::string_view name, NGIBuffer* externalBuffer);

    REDBufferView* AllocateBufferView(REDBuffer* buffer, const NGIBufferViewDesc& desc);

    virtual REDBuffer* FindExternalBuffer(NGIBuffer* externalBuffer);

    std::mutex mBufferMutex;
    //std::list<REDBuffer, REDAllocatorWrapper<REDBuffer>> mBuffers{ REDAllocatorWrapper<REDBuffer>{ mBufferAllocator } };
    std::mutex mBufferViewMutex;
    //std::deque<REDBufferView> mBufferViews;

    cross::REDUniquePtr<cross::REDResidentBuffer> CreateBuffer(std::string_view name, const NGIBufferDesc& desc);

    cross::REDUniquePtr<cross::REDResidentTexture> CreateTexture(std::string_view name, const NGITextureDesc& desc);

    std::mutex mResidentBufferMutex;
    //std::set<REDResidentBuffer*> mResidentBuffers;
    std::mutex mResidentTextureMutex;
    //std::set<REDResidentTexture*> mResidentTextures;

    virtual void Destroy(REDResidentObject* object);

    std::mutex mPendingDestroyObjectMutex;
    //std::vector<REDResidentObject*> mPendingDestroyObjects;

    std::mutex mCurrentRenderPassMutex;
    std::unordered_map<size_t, REDRenderPass*> mThreadRenderPass;
    //REDRenderPass* mCurrentRenderPass = nullptr;

    virtual REDResourceRangeState* AllocateResourceRangeState(const REDResourceState& state, REDPass* firstPass, REDPass* lastPass);

    std::mutex mRangeStatePoolMutex;
    //FrameDeque<REDResourceRangeState> mRangeStatePool;

    virtual REDRenderPass* AllocateRenderPassInfo();

    std::mutex mRenderPassMutex;
    //std::deque<REDRenderPass> mRenderPassPool;

    virtual bool Validate(REDTexture* texture);

    virtual bool Validate(REDBuffer* buffer);

    // can be called in callback before execution
    virtual void QueueBufferUpload(REDBuffer* dstBuffer, UInt32 dstBufferOffset, const void* initialData, UInt32 initialDataSize);

    virtual void QueueBufferUpload(REDBuffer* dstBuffer, cross::NGIStagingBuffer* srcBuffer, NGICopyBuffer region);



    std::mutex mBufferUploadMutex;
    //std::map<REDBuffer*, std::vector<std::tuple<NGIBuffer*, NGICopyBuffer>>> mBufferUploads;

    cross::REDRegion* BeginRegion(std::string_view name);

    std::mutex mRegionMutex;
    //std::deque<REDRegion> mRegionPool;

    std::mutex mCurrentRegionMutex;
    std::unordered_map<size_t, REDRegion*> mThreadRegions;

    virtual REDRegion* GetCurrentRegion();

    virtual void SetCurrentRegion(REDRegion* region);

    virtual REDRenderPass* GetCurrentRenderPass();

    virtual void SetCurrentRenderPass(REDRenderPass* renderPass);

    virtual void EndFrame();
};
}   // namespace cross
