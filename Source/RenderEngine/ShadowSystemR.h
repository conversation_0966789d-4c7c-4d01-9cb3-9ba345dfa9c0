#pragma once

#include <queue>
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/RenderSystemBase.h"
#include "CECommon/Common/RenderPipelineSetting.h"
#include "RenderEngine/ShadowCamera.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "RenderEngine/RenderWorldConst.h"
#include "RenderEngine/VirtualShadowMap/VirtualShadowMapClipmap.h"
#include "RenderEngine/RenderPipeline/Effects/LocalLightShadow.h"

namespace cross {
// One shadow camera component for one shadow-casting light
struct ShadowCameraComponentR : ecs::IComponent
{
    CEFunction(Reflect)

    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

private:
    // CascadeShadowMap - [CameraEntity][CascadeIndex]
    std::unordered_map<ecs::EntityID, std::vector<DirectionalLightShadowCamera>> mDirectionalLightShadowCameras;

    // PunctualLightShadowMap
    std::vector<RenderCamera> mPunctualLightShadowCameras;

    // VirtualShadowMapClipmap - [CameraEntity]
    std::unordered_map<ecs::EntityID, VirtualShadowMapClipmap> mVirtualShadowMapClipMap;

    friend class ShadowSystemR;
};

class ShadowSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect)

    using ShadowCameraH = ecs::ComponentHandle<ShadowCameraComponentR>;
    using ShadowCameraReader = ecs::ScopedComponentRead<ShadowCameraComponentR>;
    using ShadowCameraWriter = ecs::ScopedComponentWrite<ShadowCameraComponentR>;

public:
    RENDER_ENGINE_API static ShadowSystemR* CreateInstance();

    ShadowSystemR();

    virtual void Release() override;

    const std::vector<DirectionalLightShadowCamera>* GetDirectionalShadowCameras(const ShadowCameraReader& comp, ecs::EntityID sceneCamera) const;

    const std::vector<RenderCamera>* GetPunctualLightShadowCameras(const ShadowCameraReader& comp) const
    {
        return &comp->mPunctualLightShadowCameras;
    }

    const VirtualShadowMapClipmap* GetVirtualShadowMapClipmap(const ShadowCameraReader& comp, ecs::EntityID sceneCamera) const;
    VirtualShadowMapClipmap* GetVirtualShadowMapClipmap(const ShadowCameraWriter& comp, ecs::EntityID sceneCamera);

    LocalLightShadowCache* GetLocalLightShadowCache() const
    {
        return mLocalShadowMapCache.get();
    }

    UInt32 GetCascadeCount()
    {
        return mCascadeCount;
    }

    const auto& GetAllShadowLights() const
    {
        return mShadowLights;
    }

    bool IsShadowEnable(ecs::EntityID cameraEntity, ecs::EntityID lightEntity)
    {
        auto& lights = mShadowLightsPerCamera[cameraEntity];
        return lights.find(lightEntity) != lights.end();
    }

    bool IsShadowCascadeNeedUpdate(ecs::EntityID cameraEntity, UInt32 cascadeIndex)
    {
        return mShadowCascadeNeedUpdate[cameraEntity][cascadeIndex];
    }

    RENDER_ENGINE_API void SetShadowCascadeEnable(UInt32 cascadeIndex, bool bEnable);

    const std::array<bool, MAX_CASCADE_SHADOW_COUNT>& GetCascadeControl() const
    {
        return mCascadeControl;
    }

    void SetForceEnableReProjectionShadow(bool value)
    {
        if (mIsForceEnableReprojectionShadow != value)
        {
            mIsNeedDrawAllCascades = true;
        }

        mIsForceEnableReprojectionShadow = value;
    }
    
    RenderPipelineSetting* GetRenderPipelineSetting() const;

    bool IsEnableReprojectionShadow() const { return mIsForceEnableReprojectionShadow || GetRenderPipelineSetting()->EnableReprojectionShadow;
    }

    void SetCSMForceRedrawWhenDirectionChange(bool value)
    {
        mIsForceRedrawWhenDirectionChange = value;
    }

    void SetUpRenderPipelineShadowLights(IRenderPipeline* pipeline, const std::vector<ecs::EntityID>& lightList);

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

    static void CalculateCameraProjMatrix(float fov, float nearPlane, float farPlane, float aspectRatio, Float4x4& projMat);

    CEFunction(Reflect)
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

protected:
    void UpdateShadowCameras();

    void UpdateShadowCamera_DirectionalLight(ecs::EntityID lightEntity);
    void UpdateShadowCamera_PointLight(ecs::EntityID lightEntity);
    void UpdateShadowCamera_SpotLight(ecs::EntityID lightEntity);

    void UpdateShadowCamera_CSM(ecs::EntityID cameraEntity, ecs::EntityID lightEntity);
    void UpdateShadowCamera_VirtualShadowMapClipmap(ecs::EntityID cameraEntity, ecs::EntityID lightEntity);

private:
    void SetupShadowLights();
    void UpdateCSMCascadeDrawList(FrameParam* frameParam);
    
    float GetBoundingSphereScale(UInt32 cascadeIndex);
    void SetupCSMShadowCamera(const RenderCamera& viewCamera, ecs::EntityID lightEntity, UInt32 cascadeIndex, DirectionalLightShadowCamera& outShadowCamera);
    float GetSplitDistance(float shadowNear, float shadowFar, UInt32 cascadeIndex) const;
    void CalculateShadowSplitBounds(const RenderCamera& viewCamera, UInt32 cascadeIndex, DirectionalLightShadowCamera& outShadowCamera);
    void CalculateShadowSphereBoundsAndAccurateBounds(const RenderCamera& viewCamera, UInt32 cascadeIndex, DirectionalLightShadowCamera& outShadowCamera);
    void CalculateShadowCullingVolumn(const Float3* cascadeFrustumVerts, DirectionalLightShadowCamera& outShadowCamera);

private:

    UInt32 mCascadeCount = 4;

    bool mIsNeedDrawAllCascades = true;
    bool mIsForceRedrawWhenDirectionChange;
    bool mIsForceEnableReprojectionShadow;

    std::set<ecs::EntityID> mShadowLights;
    std::set<ecs::EntityID> mCSMLights;
    std::unordered_map<ecs::EntityID, Quaternion> mCSMLightDirections;
    std::unordered_map<ecs::EntityID, std::set<ecs::EntityID>> mShadowLightsPerCamera;

    // [CameraEntity][CascadeIndex]
    std::unordered_map<ecs::EntityID, std::array<bool, MAX_CASCADE_SHADOW_COUNT>> mShadowCascadeNeedUpdate;
    std::array<bool, MAX_CASCADE_SHADOW_COUNT> mCascadeControl = {true, true, true, true};
    // for CSM force redraw
    std::array<float, MAX_CASCADE_SHADOW_COUNT> mShadowBoundingSphereZInCameraSpace;
    std::array<float, MAX_CASCADE_SHADOW_COUNT> mShadowBoundingSphereRadius;
    std::unordered_map<ecs::EntityID, std::array<Float3, MAX_CASCADE_SHADOW_COUNT>> mShadowBoundingCenter;

    std::unique_ptr<LocalLightShadowCache> mLocalShadowMapCache;

    friend class LightSystemR;
};
}   // namespace cross