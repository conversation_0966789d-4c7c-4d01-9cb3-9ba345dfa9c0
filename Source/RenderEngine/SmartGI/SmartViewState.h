#pragma once

// #include "CrossBase/Math/CrossMath.h"
// #include "RenderEngine/RenderingExecutionDescriptor/REDResource.h"

// namespace cross {

// (chopperlin) I don't think this is necessary in CE
// class SurfelLightingState
// {
// public:
// 	REDUniquePtr<REDResidentBuffer> SurfelBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelDataBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelEstimatorDataBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelGridBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelCellBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelAliveBufferCurrent;
// 	REDUniquePtr<REDResidentBuffer> SurfelDeadBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelStatsBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelIndirectBuffer;
// 	REDUniquePtr<REDResidentBuffer> SurfelCoverageMarkBuffer;

// 	// Frame management
// 	UInt64 LastUpdateFrameID = 0;
// 	bool bInitialized = false;

// public:
// 	SurfelLightingState() = default;
	
// 	~SurfelLightingState()
// 	{
// 		ReleaseResource();
// 	}

// 	// Check if buffers are valid for the current frame
// 	bool IsValid() const
// 	{
// 		return bInitialized && 
// 			   SurfelBuffer != nullptr && 
// 			   SurfelDataBuffer != nullptr &&
// 			   SurfelGridBuffer != nullptr;
// 	}

// 	// Mark as updated for current frame
// 	void MarkUpdated(UInt64 frameID)
// 	{
// 		LastUpdateFrameID = frameID;
// 		bInitialized = true;
// 	}

// private:
// 	void ReleaseResource()
// 	{
// 		// REDUniquePtr will automatically handle destruction
// 		SurfelBuffer.reset();
// 		SurfelDataBuffer.reset();
// 		SurfelEstimatorDataBuffer.reset();
// 		SurfelGridBuffer.reset();
// 		SurfelCellBuffer.reset();
// 		SurfelAliveBufferCurrent.reset();
// 		SurfelDeadBuffer.reset();
// 		SurfelStatsBuffer.reset();
// 		SurfelIndirectBuffer.reset();
// 		SurfelCoverageMarkBuffer.reset();
		
// 		bInitialized = false;
// 	}
// };

// } // namespace cross



// #include "RHIGPUReadback.h"
// // Must match shader
// const static int32 SmartMaxVoxelClipmapLevels = 8;
// const static int32 NumVoxelLightingTextures = 1;

// class FSmartGatherCvarState
// {
// public:

// 	FSmartGatherCvarState()
// 	{
// 		TraceMeshSDFs = 1;
// 		MeshSDFTraceDistance = 180.0f;
// 		SurfaceBias = 5.0f;
// 		VoxelTracingMode = 0;
// 	}

// 	int32 TraceMeshSDFs;
// 	float MeshSDFTraceDistance;
// 	float SurfaceBias;
// 	int32 VoxelTracingMode;

// 	inline bool operator==(const FSmartGatherCvarState& Rhs)
// 	{
// 		return TraceMeshSDFs == Rhs.TraceMeshSDFs &&
// 			MeshSDFTraceDistance == Rhs.MeshSDFTraceDistance &&
// 			SurfaceBias == Rhs.SurfaceBias &&
// 			VoxelTracingMode == Rhs.VoxelTracingMode;
// 	}
// };

// class FSmartFinalGatherTemporalState
// {
// public:
// 	FIntRect DiffuseIndirectHistoryViewRect;
// 	FVector4f DiffuseIndirectHistoryScreenPositionScaleBias;
// 	TRefCountPtr<IPooledRenderTarget> DiffuseIndirectHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> BackfaceDiffuseIndirectHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> RoughSpecularIndirectHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> BentNormalHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> NumFramesAccumulatedRT;
// 	TRefCountPtr<IPooledRenderTarget> FastUpdateModeHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> NormalHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> OctahedralSolidAngleTextureRT;
// 	TRefCountPtr<IPooledRenderTarget> PrimIDHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> RoughnessHistoryRT;
// 	FIntRect ProbeHistoryViewRect;
// 	FVector4f ProbeHistoryScreenPositionScaleBias;
// 	TRefCountPtr<IPooledRenderTarget> HistoryScreenProbeSceneDepth;
// 	TRefCountPtr<IPooledRenderTarget> HistoryScreenProbeTranslatedWorldPosition;
// 	TRefCountPtr<IPooledRenderTarget> ProbeHistoryScreenProbeRadiance;
// 	TRefCountPtr<IPooledRenderTarget> ImportanceSamplingHistoryScreenProbeRadiance;
//     FSmartGatherCvarState SmartGatherCvars;

// 	FViewMatrices LastUpdateViewMatrices;
// 	int32 LastUpdateFrameNumber;

// 	FSmartFinalGatherTemporalState()
// 	{
// 		DiffuseIndirectHistoryViewRect = FIntRect(0, 0, 0, 0);
// 		DiffuseIndirectHistoryScreenPositionScaleBias = FVector4f(0, 0, 0, 0);
// 		ProbeHistoryViewRect = FIntRect(0, 0, 0, 0);
// 		ProbeHistoryScreenPositionScaleBias = FVector4f(0, 0, 0, 0);
// 		LastUpdateFrameNumber = 0;
// 	}

// 	void SafeRelease()
// 	{
// 		DiffuseIndirectHistoryRT.SafeRelease();
// 		BackfaceDiffuseIndirectHistoryRT.SafeRelease();
// 		RoughSpecularIndirectHistoryRT.SafeRelease();
// 		BentNormalHistoryRT.SafeRelease();
// 		NumFramesAccumulatedRT.SafeRelease();
// 		FastUpdateModeHistoryRT.SafeRelease();
// 		NormalHistoryRT.SafeRelease();
// 		OctahedralSolidAngleTextureRT.SafeRelease();
// 		HistoryScreenProbeSceneDepth.SafeRelease();
// 		HistoryScreenProbeTranslatedWorldPosition.SafeRelease();
// 		ProbeHistoryScreenProbeRadiance.SafeRelease();
// 		ImportanceSamplingHistoryScreenProbeRadiance.SafeRelease();
// 	}
// };

// class FSmartReflectionTemporalState
// {
// public:
// 	uint32 HistoryFrameIndex;
// 	FIntRect HistoryViewRect;
// 	FVector4f HistoryScreenPositionScaleBias;
// 	FIntPoint HistorySceneTexturesExtent;
// 	FIntPoint HistoryEffectiveResolution;
// 	FIntPoint HistoryOverflowTileOffset;
// 	FIntPoint HistoryOverflowTileCount;
// 	uint32 HistoryStrataMaxBSDFCount;

// 	TRefCountPtr<IPooledRenderTarget> SpecularIndirectHistoryRT1;
// 	TRefCountPtr<IPooledRenderTarget> SpecularIndirectHistoryRT2;
// 	TRefCountPtr<IPooledRenderTarget> NumFramesAccumulatedRT1;
// 	TRefCountPtr<IPooledRenderTarget> NumFramesAccumulatedRT2;
// 	TRefCountPtr<IPooledRenderTarget> ResolveVarianceHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> BSDFTileHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> DepthHistoryRT;
// 	TRefCountPtr<IPooledRenderTarget> NormalHistoryRT;

// 	FSmartReflectionTemporalState()
// 	{
// 		HistoryFrameIndex = 0;
// 		HistoryViewRect = FIntRect(0, 0, 0, 0);
// 		HistoryScreenPositionScaleBias = FVector4f(0, 0, 0, 0);
// 		HistorySceneTexturesExtent = FIntPoint(0, 0);
// 		HistoryEffectiveResolution = FIntPoint(0, 0);
// 		HistoryOverflowTileOffset = FIntPoint(0, 0);
// 		HistoryOverflowTileCount = FIntPoint(0, 0);
// 		HistoryStrataMaxBSDFCount = 0;
// 	}

// 	void SafeRelease()
// 	{
// 		SpecularIndirectHistoryRT1.SafeRelease();	
// 		SpecularIndirectHistoryRT2.SafeRelease();
// 		NumFramesAccumulatedRT1.SafeRelease();
// 		NumFramesAccumulatedRT2.SafeRelease();
// 		ResolveVarianceHistoryRT.SafeRelease();
// 		BSDFTileHistoryRT.SafeRelease();
// 		DepthHistoryRT.SafeRelease();
// 		NormalHistoryRT.SafeRelease();
// 	}

// #if WITH_MGPU
// 	void AddCrossGPUTransfers(uint32 SourceGPUIndex, uint32 DestGPUIndex, TArray<FTransferResourceParams>& OutTransfers)
// 	{
// #define TRANSFER_LUMEN_RESOURCE(NAME) \
// 			if (NAME) OutTransfers.Add(FTransferResourceParams(NAME->GetRHI(), SourceGPUIndex, DestGPUIndex, false, false))

// 		TRANSFER_LUMEN_RESOURCE(SpecularIndirectHistoryRT1);
// 		TRANSFER_LUMEN_RESOURCE(SpecularIndirectHistoryRT2);
// 		TRANSFER_LUMEN_RESOURCE(NumFramesAccumulatedRT1);
// 		TRANSFER_LUMEN_RESOURCE(NumFramesAccumulatedRT2);
// 		TRANSFER_LUMEN_RESOURCE(ResolveVarianceHistoryRT);
// 		TRANSFER_LUMEN_RESOURCE(BSDFTileHistoryRT);
// 		TRANSFER_LUMEN_RESOURCE(DepthHistoryRT);
// 		TRANSFER_LUMEN_RESOURCE(NormalHistoryRT);

// #undef TRANSFER_LUMEN_RESOURCE
// 	}
// #endif  // WITH_MGPU

// 	uint64 GetGPUSizeBytes(bool bLogSizes) const;
// };

// class FSmartVoxelLightingClipmapState
// {
// public:
// 	FIntVector LastPartialUpdateOriginInTiles = FIntVector(0);

// 	FVector Center = FVector(0.0f);
// 	FVector Extent = FVector(0.0f);
// 	FVector VoxelSize = FVector(0.0f);
// 	float VoxelRadius = 0.0f;

// 	TArray<FBox> PrimitiveModifiedBounds;
// 	TArray<FBox> UpdateBounds;
// };

// class FSmartBrickCameraBoxState
// {
// public:
// 	FVector3f LastPartialUpdateOriginInTiles = FVector3f(0);

// 	FVector3f Center = FVector3f(0.0f);
// 	FVector3f Extent = FVector3f(0.0f);
// 	FVector3f VoxelSize = FVector3f(0.0f);
// 	FVector3f BrickResolution;
// 	//float VoxelRadius = 0.0f;
// 	TArray<FBox> PrimitiveModifiedBounds;
// 	TSet<FPrimitiveSceneInfo*> AddAndUpdateOperations;
// 	TSet<FPrimitiveSceneInfo*> PendingRemoveOperations;
// };

// class FSmartRadianceCacheClipmap
// {
// public:
// 	/** World space bounds. */
// 	FVector Center;
// 	float Extent;

// 	FVector ClipmapToWorldCenterBias;
// 	float ClipmapToWorldCenterScale;

// 	FVector WorldPositionToProbeCoordBias;
// 	float WorldPositionToProbeCoordScale;

// 	float ProbeTMin;

// 	/** Offset applied to UVs so that only new or dirty areas of the volume texture have to be updated. */
// 	FVector VolumeUVOffset;

// 	/* Distance between two probes. */
// 	float CellSize;
// };

// class FSmartRadianceCacheState
// {
// public:
// 	FSmartRadianceCacheState()
// 	{}

// 	TArray<FSmartRadianceCacheClipmap> Clipmaps;

// 	float ClipmapWorldExtent = 0.0f;
// 	float ClipmapDistributionBase = 0.0f;

// 	/** Clipmaps of probe indexes, used to lookup the probe index for a world space position. */
// 	TRefCountPtr<IPooledRenderTarget> RadianceProbeIndirectionTexture;

// 	TRefCountPtr<IPooledRenderTarget> RadianceProbeAtlasTexture;
// 	/** Texture containing radiance cache probes, ready for sampling with bilinear border. */
// 	TRefCountPtr<IPooledRenderTarget> FinalRadianceAtlas;
// 	TRefCountPtr<IPooledRenderTarget> FinalIrradianceAtlas;
// 	TRefCountPtr<IPooledRenderTarget> ProbeOcclusionAtlas;

// 	TRefCountPtr<IPooledRenderTarget> DepthProbeAtlasTexture;

// 	TRefCountPtr<FRDGPooledBuffer> ProbeAllocator;
// 	TRefCountPtr<FRDGPooledBuffer> ProbeFreeListAllocator;
// 	TRefCountPtr<FRDGPooledBuffer> ProbeFreeList;
// 	TRefCountPtr<FRDGPooledBuffer> ProbeLastUsedFrame;
// 	TRefCountPtr<FRDGPooledBuffer> ProbeLastTracedFrame;
// 	TRefCountPtr<FRDGPooledBuffer> ProbeWorldOffset;
// 	TRefCountPtr<IPooledRenderTarget> OctahedralSolidAngleTextureRT;

// 	void ReleaseTextures()
// 	{
// 		RadianceProbeIndirectionTexture.SafeRelease();
// 		RadianceProbeAtlasTexture.SafeRelease();
// 		FinalRadianceAtlas.SafeRelease();
// 		FinalIrradianceAtlas.SafeRelease();
// 		ProbeOcclusionAtlas.SafeRelease();
// 		DepthProbeAtlasTexture.SafeRelease();
// 		ProbeAllocator.SafeRelease();
// 		ProbeFreeListAllocator.SafeRelease();
// 		ProbeFreeList.SafeRelease();
// 		ProbeLastUsedFrame.SafeRelease();
// 		ProbeLastTracedFrame.SafeRelease();
// 		ProbeWorldOffset.SafeRelease();
// 	}
// };

// class FSmartGIViewState
// {
// public:

// 	FSmartFinalGatherTemporalState FinalGatherState;
// 	FSmartReflectionTemporalState ReflectionState;
// 	FSmartReflectionTemporalState TranslucentReflectionState;
// 	TRefCountPtr<IPooledRenderTarget> DepthHistoryRT;

// 	// FinalGather 
// 	float SmartFinalGatherQuality = 1.0f;

// 	// Voxel clipmaps
// 	float SmartVoxelLightingQuality = 1.0f;
// 	int32 NumClipmapLevels = 0;
// 	FSmartVoxelLightingClipmapState VoxelLightingClipmapState[SmartMaxVoxelClipmapLevels];

// 	TRefCountPtr<FRDGPooledBuffer> VoxelVisBuffer;
// 	const FScene* VoxelVisBufferCachedScene = nullptr;
// 	FIntVector VoxelGridResolution;

// 	bool bSmartPropagateGlobalLightingChange = false;

// 	// Brick CameraBox
// 	FSmartBrickCameraBoxState BrickCameraBoxState;

// 	// Translucency
// 	TRefCountPtr<IPooledRenderTarget> TranslucencyVolume0;
// 	TRefCountPtr<IPooledRenderTarget> TranslucencyVolume1;
// 	TRefCountPtr<IPooledRenderTarget> HistoryTranslucencyVolume0;
// 	TRefCountPtr<IPooledRenderTarget> HistoryTranslucencyVolume1;

// 	FSmartRadianceCacheState RadianceCacheState;
// 	FSmartRadianceCacheState TranslucencyVolumeRadianceCacheState;

// 	// LightCulling
// 	TRefCountPtr<FRDGPooledBuffer> SmartPackedLights;
// 	TRefCountPtr<FRDGPooledBuffer> NumCulledLightsGrid;
// 	TRefCountPtr<FRDGPooledBuffer> CulledLightDataGrid;
// 	FVector ViewWorldCameraOrigin;
// 	FVector PreViewTranslation;
// 	bool bUseDenseShadowMap;
// 	//int MaxLightNumPerGrids;
// 	float LightGridSize;
// 	FIntVector CulledGridSize;

// 	void SafeRelease()
// 	{
// 		FinalGatherState.SafeRelease();
// 		ReflectionState.SafeRelease();
// 		TranslucentReflectionState.SafeRelease();
// 		DepthHistoryRT.SafeRelease();

// 		VoxelVisBuffer.SafeRelease();

// 		TranslucencyVolume0.SafeRelease();
// 		TranslucencyVolume1.SafeRelease();

// 		SmartPackedLights.SafeRelease();
// 		NumCulledLightsGrid.SafeRelease();
// 		CulledLightDataGrid.SafeRelease();
// 	}
// };

// struct FRDGTextureProxy
// {
// public:
// 	TRefCountPtr<IPooledRenderTarget> ExternalTexture;
// 	FRDGTextureRef Texture;
	
// 	inline bool IsValid() { return ExternalTexture.IsValid(); }

// 	inline FRDGTextureSRVRef GetSRV(FRDGBuilder& GraphBuilder) { return GraphBuilder.CreateSRV(Texture); }
// 	inline FRDGTextureUAVRef GetUAV(FRDGBuilder& GraphBuilder) { return GraphBuilder.CreateUAV(Texture); }
	
// 	void SafeRelease()
// 	{
// 		ExternalTexture.SafeRelease();
// 		Texture = nullptr;
// 	}
	
// 	void Init(FRDGBuilder& GraphBuilder, FIntVector Size, EPixelFormat Format, const TCHAR* Name)
// 	{
// 		if (ExternalTexture.IsValid() && Size == ExternalTexture->GetDesc().GetSize())
// 		{
// 			Texture = GraphBuilder.RegisterExternalTexture(ExternalTexture);
// 		}
// 		else 
// 		{
// 			if(ExternalTexture.IsValid())
// 				ExternalTexture.SafeRelease();
			
// 			FRDGTextureDesc TextureDesc = FRDGTextureDesc::Create3D(Size, Format, FClearValueBinding::Black, TexCreate_ShaderResource | TexCreate_UAV);
// 			Texture = GraphBuilder.CreateTexture(TextureDesc, Name);
			
// 			if(Format == PF_R32_UINT || Format == PF_R16G16_UINT)
// 				AddClearUAVPass(GraphBuilder, GetUAV(GraphBuilder), (uint32)0);
// 			else
// 				AddClearUAVPass(GraphBuilder, GetUAV(GraphBuilder), (FVector4)0.0f);
// 			ExternalTexture = GraphBuilder.ConvertToExternalTexture(Texture);
// 		}	
// 	};
// };

// struct FRDGBufferProxy
// {
// public:
// 	TRefCountPtr<FRDGPooledBuffer> ExternalBuffer;
// 	FRDGBufferRef Buffer;
	
// 	inline bool IsValid() { return ExternalBuffer.IsValid(); }
	
// 	inline FRDGBufferSRVRef GetSRV(FRDGBuilder& GraphBuilder) { return GraphBuilder.CreateSRV(Buffer, PF_R32_UINT); }
// 	inline FRDGBufferUAVRef GetUAV(FRDGBuilder& GraphBuilder) { return GraphBuilder.CreateUAV(Buffer, PF_R32_UINT); }
	
// 	void SafeRelease()
// 	{
// 		ExternalBuffer.SafeRelease();
// 		Buffer = nullptr;
// 	}
	
// 	void Init(FRDGBuilder& GraphBuilder, int32 Size, const TCHAR* Name) 
// 	{
// 		if (ExternalBuffer.IsValid() && Size == ExternalBuffer->Desc.NumElements)
// 		{
// 			Buffer = GraphBuilder.RegisterExternalBuffer(ExternalBuffer);
// 		}
// 		else 
// 		{
// 			if(ExternalBuffer.IsValid())
// 				ExternalBuffer.SafeRelease();
// 			Buffer = GraphBuilder.CreateBuffer(FRDGBufferDesc::CreateBufferDesc(sizeof(uint32), Size), Name);
// 			AddClearUAVPass(GraphBuilder, GetUAV(GraphBuilder), 0);
// 			ExternalBuffer = GraphBuilder.ConvertToExternalBuffer(Buffer);
// 		}	
// 	};
// };

// class FVoxelLightingState
// {
// public:
// 	FRDGTextureProxy LightingTexture;
// 	FRDGTextureProxy AlbedoTexture;
// 	FRDGTextureProxy NormalTexture;
// 	FRDGTextureProxy EmissiveTexture;
// 	FRDGTextureProxy OpacityTexture;
// 	FRDGBufferProxy VoxelVisBuffers[SmartMaxVoxelClipmapLevels];
// 	FRDGBufferProxy VoxelVisBufferAllocator;

// 	int32 ShouldUpdatedFrames[SmartMaxVoxelClipmapLevels];

// 	int NumClipmalLevels;
// 	FIntVector Resolution;
	
// 	bool bContextReset = false;
// 	int32 CustomFrameIndex = 0;
// public:
// 	FVoxelLightingState();

// 	~FVoxelLightingState();

// 	void SetShouldUpdatedFrameNumber(int32 ClipmapIndex,int32 FrameNumber);

// 	bool ShouldUpdateLighting(int32 ClipmapIndex) const;

// 	void SetLightingUpdated(int32 ClipmapIndex);

// private:
// 	void ReleaseResource();	
// };

// #include "SmartDDGICommon.h"
// struct FDDGILightingState
// {
// 	TRefCountPtr<IPooledRenderTarget>   DDGIColorTexturePrev[2];
// 	TRefCountPtr<IPooledRenderTarget>   DDGIDepthTexturePrev[2];
// 	TRefCountPtr<IPooledRenderTarget>   DDGIProbeStateTexturePrev[2];
// 	TRefCountPtr<FRDGPooledBuffer>      DDGIOffsetBuffer[2];
// 	TRefCountPtr<FRDGPooledBuffer>      DDGIRayBuffer[2];       //for Visualize DDGI Trace
// 	//TRefCountPtr<FRDGPooledBuffer>    DDGISparseTilePool[2];  //@todo:fill the sparse struct
// 	FDDGIVolumeDesc                     DDGIVolumeDesc[2];

// public:
// 	~FDDGILightingState()
// 	{
// 		ReleaseResource();
// 	}

// private:
// 	void ReleaseResource()
// 	{
// 		for (int i = 0; i < 2; ++i)
// 		{
// 			DDGIColorTexturePrev[i].SafeRelease();
// 			DDGIDepthTexturePrev[i].SafeRelease();
// 			DDGIOffsetBuffer[i].SafeRelease();
// 			DDGIProbeStateTexturePrev[i].SafeRelease();
// 			DDGIRayBuffer[i].SafeRelease();
// 		}
// 	}
// };

// struct FPhotonMappingState
// {
// 	TRefCountPtr<IPooledRenderTarget>   HashPosBucket[2];
// 	TRefCountPtr<IPooledRenderTarget>   HashDirBucket[2];
// 	TRefCountPtr<IPooledRenderTarget>   HashFluxBucket[2];
// 	TRefCountPtr<FRDGPooledBuffer>      HashPhotonCounter[2];

// 	TRefCountPtr<FRDGPooledBuffer>      PhotonCounter;
// 	FRHIGPUBufferReadback* 				PhotonReadbackBuffer = nullptr;
// 	TRefCountPtr<FRDGPooledBuffer>      DebugBuffer;
// 	FRHIGPUBufferReadback* 				DebugReadbackBuffer = nullptr;

// 	int32 GlobalPhotonCounter = 0;
// 	int32 CausicPhotonCounter = 0;

// 	int32 StartFrameNumber = 0;
// 	bool bCoverageEnough = false;

// 	float GlobalRadius;
// 	float CausticRadius;

// 	TRefCountPtr<IPooledRenderTarget> LastDenoisedRadianceRT[4];
// 	TRefCountPtr<FRDGPooledBuffer> LastVarianceBuffer[4];

// public:
// 	~FPhotonMappingState()
// 	{
// 		ReleaseResource();
// 	}

// public:
// 	void ReleaseResource()
// 	{
// 		DebugBuffer.SafeRelease();
// 		if(DebugReadbackBuffer)
// 		{
// 			delete DebugReadbackBuffer;
// 			DebugReadbackBuffer = nullptr;
// 		}
// 		PhotonCounter.SafeRelease();
// 		if(PhotonReadbackBuffer)
// 		{
// 			delete PhotonReadbackBuffer;
// 			PhotonReadbackBuffer = nullptr;
// 		}
// 		for (int i = 0; i < UE_ARRAY_COUNT(HashPosBucket); ++i)
// 		{
// 			HashPosBucket[i].SafeRelease();
// 			HashDirBucket[i].SafeRelease();
// 			HashFluxBucket[i].SafeRelease();
// 			HashPhotonCounter[i].SafeRelease();
// 		}
// 		for (int i = 0; i < UE_ARRAY_COUNT(LastDenoisedRadianceRT); ++i)
// 		{
// 			LastDenoisedRadianceRT[i].SafeRelease();
// 			LastVarianceBuffer[i].SafeRelease();
// 		}
// 	}
// };

// // BrickGI Begin
// class FSmartBrick
// {
// public:
//     FSmartBrick(FVector3f Position) : BrickPosition(Position)
// 	{
// 		bAllocate = false;
// 		bRemove = false;
//     	PhysicalPageCoord = FIntPoint(-1, -1);
// 	}
// 	FSmartBrick() {}
//     ~FSmartBrick() {}

//     FVector3f BrickPosition;
//     bool bAllocate = false;
// 	bool bRemove = false;
//     FIntPoint PhysicalPageCoord = FIntPoint(-1, -1);
//     TArray<FPrimitiveSceneInfo*> PrimitiveSceneInfos;
// 	// BrickGI2.0
// 	TArray<FIntPoint> PageList;
// };

// class FSmartBrickAtlasAllocator
// {
// public:
// 	void Init(FIntPoint PageAtlasSizeInPages);
// 	void Allocate(FIntPoint& PhysicalPageCoord);
// 	void Free(FIntPoint& PhysicalPageCoord);
// 	bool IsSpaceAvailable() const;
// 	bool Initialized() const { return Inited; }

// 	TArray<FIntPoint> PhysicalPageFreeList;
// 	bool Inited = false;
// };

// class FSmartVisFaceAtlasAllocator
// {
// public:
// 	void Init(FIntPoint PageAtlasSizeInPages);
// 	void Allocate(TArray<FIntPoint>& PageList, int Size);
// 	void Free(FIntPoint& PhysicalPageCoord);
// 	bool IsSpaceAvailable() const;
// 	bool Initialized() { return Inited; }

// 	TArray<FIntPoint> PhysicalPageFreeList;
// 	bool Inited = false;
// };

// class FSmartBrickState
// {
// public:
// 	FRDGTextureProxy BrickOpacityAtlas;
//     FRDGTextureProxy BrickTexture;

// 	FRDGTextureProxy BrickGroupTexture;
// 	FRDGTextureProxy VoxelGroupTexture;

// 	// BrickGI2.0--PageReadBackBuffer
// 	TArray<FRHIGPUBufferReadback*> StreamingRequestPageReadbackBuffers;
// 	uint32 MaxStreamingPageReadbackBuffers = 4;
// 	uint32 PageReadbackBuffersWriteIndex = 0;
// 	uint32 PageReadbackBuffersNumPending = 0;

// 	// Lighting Inject List
// 	TArray<TSharedPtr<FSmartBrick>> BricksWaitForInject;
// 	TArray<TSharedPtr<FSmartBrick>> InitialBricksLightingInject;
// 	int32 LightingInjectIndex = 0;

// 	// BrickGI2.0
// 	FRDGTextureProxy BrickVisFaceNormalAtlas;
// 	FRDGTextureProxy BrickMappingAtlas;
// 	FRDGTextureProxy BrickVisFaceAlbedoAtlas;
// 	FRDGTextureProxy BrickVisFaceLightingAtlas;
// 	FRDGTextureProxy BrickVisFaceEmissiveAtlas;

//     struct FSmartDirtyBrickInfo
// 	{
// 		FVector3f BrickCoordinate;
// 		FIntPoint AtlasBias;
// 		bool IsRemove = false; 
// 	};

// 	TArray<FSmartDirtyBrickInfo> DirtyBricks;
// 	TMap<FVector3f, TSharedPtr<FSmartBrick>> BricksTable;
//     TArray<TSharedPtr<FSmartBrick>> CandidateBricks;
	
//     FSmartBrickAtlasAllocator SmartBrickAllocator;
// 	FSmartVisFaceAtlasAllocator SmartVisFaceAllocator;

// 	FVector3f AtlasResolution;
// 	FVector3f AtlasOrigin;
// 	FVector3f StorageAreaExtent;
// 	FVector3f BrickAtlasCenter;

// public:
// 	FSmartBrickState(){};
//     ~FSmartBrickState()
// 	{
// 		RemoveAllBricks();
// 		ReleaseResource();
// 	}

// 	void CheckResource();
//     void RemoveBrick(TSharedPtr<FSmartBrick>& Brick);
// 	void RemoveAllBricks();
// 	void RecoveryBrick();
// 	void BrickSortByDist(TArray<TSharedPtr<FSmartBrick>>& Bricks, FVector3f Center);
// 	void AllocateBrickInAtlas(TSharedPtr<FSmartBrick>& Brick);

// 	void RecycleAndUpdateBrick(
// 		FRDGBuilder& GraphBuilder,
// 		FViewInfo& View,
// 		FRDGBufferRef& DirtyBricksUploadBuffer
// 	);

// 	void ProcessPageReadBackBuffers(
// 		FRDGBuilder& GraphBuilder,
// 		FViewInfo& View
// 	);

// 	FRDGBufferRef CreateDirtyBricksUploadBuffer(
// 		FRDGBuilder& GraphBuilder,
// 		FViewInfo& View
// 	);

// 	FRDGBufferRef CreateBrickAtlasUpdateBuffer(
// 		FRDGBuilder& GraphBuilder,
// 		FViewInfo& View
// 	);

// private:
// 	// void InitResource();
// 	void ReleaseResource()
// 	{
// 		BrickOpacityAtlas.SafeRelease();
// 		BrickVisFaceNormalAtlas.SafeRelease();
// 		BrickVisFaceAlbedoAtlas.SafeRelease();
// 		BrickVisFaceLightingAtlas.SafeRelease();
// 		BrickVisFaceEmissiveAtlas.SafeRelease();
// 		BrickMappingAtlas.SafeRelease();

// 		BrickTexture.SafeRelease();
// 		for (int32 BufferIndex = 0; BufferIndex < StreamingRequestPageReadbackBuffers.Num(); ++BufferIndex)
// 		{
// 			if (StreamingRequestPageReadbackBuffers[BufferIndex])
// 			{
// 				delete StreamingRequestPageReadbackBuffers[BufferIndex];
// 				StreamingRequestPageReadbackBuffers[BufferIndex] = nullptr;
// 			}
// 		}
// 		BrickGroupTexture.SafeRelease();
// 		VoxelGroupTexture.SafeRelease();
// 	}
// };

// BEGIN_GLOBAL_SHADER_PARAMETER_STRUCT(FSmartBrickParameters, )
// 	SHADER_PARAMETER(FVector3f, BrickPosition)
// 	SHADER_PARAMETER(uint32, Direction)	
// 	SHADER_PARAMETER(FIntVector, BrickResolution)
// 	SHADER_PARAMETER(FIntPoint, BrickToAtlasBias)
// 	SHADER_PARAMETER(FVector3f, BrickSize)
// END_GLOBAL_SHADER_PARAMETER_STRUCT()

// // BrickGI End

// BEGIN_GLOBAL_SHADER_PARAMETER_STRUCT(FSmartClipmapParameters, )
// 	SHADER_PARAMETER(uint32, ClipmapIndex)
// 	SHADER_PARAMETER(uint32, Direction)	
// 	SHADER_PARAMETER(FIntVector, ClipmapGridResolution)
// 	SHADER_PARAMETER(FVector4f, ClipmapWorldToUVScale)
// 	SHADER_PARAMETER(FVector4f, ClipmapWorldToUVBias)
// END_GLOBAL_SHADER_PARAMETER_STRUCT()