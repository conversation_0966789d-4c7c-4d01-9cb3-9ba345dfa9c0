#include <SurfelDefinitionShared.h>

#include "SmartSurfelCommon.h"
#include "SmartSurfelLighting.h"
#include "RenderPipeline/FFSRenderPipeline.h"
#include "RenderPipeline/WorldRenderPipeline/FFSWorldRenderPipeline.h"

namespace cross
{

void SmartSurfelRenderer::SurfelCoverageMark(const GameContext& context)
{
    auto pass = mRED->AllocatePass("SurfelCoverageMark");
	// SRV
    pass->SetProperty(NAME_ID("SurfelGridBuffer"), mSurfelContext.SurfelGridBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);
    pass->SetProperty(NAME_ID("SurfelCellBuffer"), mSurfelContext.SurfelCellBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);

	// UAV
    pass->SetProperty(NAME_ID("SurfelBuffer"), mSurfelContext.SurfelBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelStatsBuffer"), mSurfelContext.SurfelStatsBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelCoverageMarkBuffer"), mSurfelContext.SurfelCoverageMarkBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);

    pass->SetProperty(NAME_ID("GSmartSurfelMinCoverage"), mSurfelSetting.SMART_MIN_SURFEL_COVERAGE);
	pass->SetProperty(NAME_ID("InterestNormalWeight"), mSurfelSetting.InterestNormalWeight);
	pass->SetProperty(NAME_ID("InterestDepthWeight"), mSurfelSetting.InterestDepthWeight);
	pass->SetProperty(NAME_ID("InterestThreshold"), mSurfelSetting.InterestThreshold);

    UInt32 downScale = 1 << mSurfelSetting.GSmartSurfelDownSampling;
    UInt2 viewSize = UInt2(static_cast<UInt32>(mSurfelInputs.mViewParams.mSizeAndInvSize.x / downScale), static_cast<UInt32>(mSurfelInputs.mViewParams.mSizeAndInvSize.y / downScale));
    UInt3 threadSize = ComputeShaderUtils::GetGroupCount(UInt3(viewSize.x, viewSize.y, 1), UInt3(16, 16, 1));
    pass->Dispatch(mSurfelSetting.SmartSurfelCoverageMarkComputeShaderR, "SurfelCoverageMark", threadSize);
}

void SmartSurfelRenderer::SurfelCoverage(const GameContext& context)
{
    auto pass = mRED->AllocatePass("SurfelCoverage");
    // SRV
    pass->SetProperty(NAME_ID("SurfelGridBuffer"), mSurfelContext.SurfelGridBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);
    pass->SetProperty(NAME_ID("SurfelCellBuffer"), mSurfelContext.SurfelCellBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);
    pass->SetProperty(NAME_ID("SurfelDeadBuffer"), mSurfelContext.SurfelDeadBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);
    pass->SetProperty(NAME_ID("SurfelCoverageMarkBuffer"), mSurfelContext.SurfelCoverageMarkBufferViewSRV.get(), NGIResourceState::ComputeShaderShaderResource);


    FFSRenderPipeline* rp = dynamic_cast<FFSRenderPipeline*>(context.mRenderPipeline);
    FFSWorldRenderPipeline* wp = static_cast<FFSWorldRenderPipeline*>(rp->GetWorldRenderPipeline());
    auto* gpuScene = wp->GetGPUScene();

    pass->SetProperty(NAME_ID("_GPUSceneObjectCullingDatas"), gpuScene->GetObjectCullingDataBufferSRV(), NGIResourceState::ComputeShaderShaderResource);

    // UAV
    pass->SetProperty(NAME_ID("SurfelBuffer"), mSurfelContext.SurfelBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelDataBuffer"), mSurfelContext.SurfelDataBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelEstimatorDataBuffer"), mSurfelContext.SurfelEstimatorDataBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelAliveBuffer"), mSurfelContext.SurfelAliveBufferNextViewUAV, NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelStatsBuffer"), mSurfelContext.SurfelStatsBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);

    pass->DispatchIndirect(mSurfelSetting.SmartSurfelCoverageComputeShaderR, "SurfelCoverage", mSurfelContext.SurfelIndirectBuffer.get(), (UInt32)SMART_SURFEL_INDIRECT_OFFSET_GENERATE * sizeof(UInt3));
}

void SmartSurfelRenderer::ResetStates(const GameContext& context)
{
    auto pass = mRED->AllocatePass("SurfelIndirectArgPrepare");
    // UAV
    pass->SetProperty(NAME_ID("SurfelStatsBuffer"), mSurfelContext.SurfelStatsBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelIndirectBuffer"), mSurfelContext.SurfelIndirectBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);

    pass->Dispatch(mSurfelSetting.SmartSurfelStatsComputeShaderR, "SurfelResetStats", UInt3(1, 1, 1));

}

void SmartSurfelRenderer::UpdateGenCount(const GameContext& context)
{
    auto pass = mRED->AllocatePass("UpdateGenCount");
    // UAV
    pass->SetProperty(NAME_ID("SurfelStatsBuffer"), mSurfelContext.SurfelStatsBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);
    pass->SetProperty(NAME_ID("SurfelIndirectBuffer"), mSurfelContext.SurfelIndirectBufferViewUAV.get(), NGIResourceState::ComputeShaderUnorderedAccess);

    pass->Dispatch(mSurfelSetting.SmartSurfelUpdateInBufferComputeShaderR, "SurfelUpdateInBuffer", UInt3(1, 1, 1));
}


	

}