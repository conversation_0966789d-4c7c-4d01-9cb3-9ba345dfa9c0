#pragma once
#ifndef _MANAGED
#include "CrossBase/Platform/PlatformTypes.h"
#include "CECommon/IndexAllocator/ThreadSafeIndexAllocator.h"
#include "RenderEngine/GPUScene/ScatterUploadBuffer.h"
#include "RenderEngine/RenderMaterial.h"
#include "StellarMeshShared.h"
#include <span>
#endif
#include <mutex>
#include <unordered_map>
#include <unordered_set>

namespace cross::StellarMesh
{
using BinType = UInt16;

// Each material used in StellarMesh will be assigned a material slot.
struct MaterialSlot
{
    // The Same MaterialInstance will be assigned the same ShadingBin.
    // TODO: Use bindless material. MaterialInstance with the same parent material will be assigned the same ShadingBin.
    BinType mShadingBin{0xFFFF};

    // Material use different rasterize settings will be assigned different RasterBin.
    // Example: Opaque, Masked, TwoSided, WPO, etc.
    BinType mRasterBin{0xFFFF};

    struct PackedData
    {
        UInt32 BinData{INVALID_INDEX};
    };

    PackedData Pack()
    {
        PackedData data{};
        data.BinData = (static_cast<UInt32>(mShadingBin) << 16u) | static_cast<UInt32>(mRasterBin);
        return data;
    }
};

// In StellarMesh, each instance corresponds to a mesh (NOT SubMesh).
// So there may be many materials in one instance.
// InstanceMaterialData is used to get instance's material offset in MaterialSlotBuffer.
// Note: The value here is only visible in StellarMeshMaterialScene, so we cannot add this to StellarSceneData.
struct InstanceMaterialData
{
    UInt32 mMaterialOffset{INVALID_INDEX};
    UInt32 mMaterialCount{0};
};

struct ShadingBinWarpper
{
    BinType mShadingBin{0xFFFF};
    UInt32 mReferenceCount{0};
};

using MaterialShadingBinTable = std::unordered_map<MaterialR*, ShadingBinWarpper>;

class ShadingBinManager
{
public:
    BinType Register(MaterialR* material);
    void Unregister(MaterialR* material);
    MaterialShadingBinTable& GetMaterialShadingBinTable();

private:
    // material to bin
    MaterialShadingBinTable mMaterialBinTable{};
#ifndef _MANAGED
    ThreadSafeIndexAllocator<false> mBinAllocator{"ShadingBinAllocator"};
#endif
};

class StellarMeshMaterialScene
{
public:
    StellarMeshMaterialScene();
#ifndef _MANAGED
    void Add(ecs::EntityID entityID, std::span<MaterialR*> materials, UInt32 stellarMeshSceneIndex);
    void Remove(ecs::EntityID entityID);

    ScatterBytesUploadBuffer& GetMaterialSlotUploadBuffer();
    ScatterBytesUploadBuffer& GetInstanceMaterialUploadBuffer();
#endif
    UInt32 GetMaterialSlotMaxCount();

    void UpdateUploadBuffer();

//     void Initialize(RenderWorld* renderWorld, RenderingExecutionDescriptor* red);
//     void SetFFSRenderPipelineSetting(const FFSRenderPipelineSetting* renderPipelineSetting);
//
//     void AllocateMaterialSlot(InstanceMaterialData& data);
//
//     void UpdateInstanceMaterialData(InstanceMaterialData& data, UInt32 instanceIndex);
// #ifndef _MANAGED
//     void UpdateMaterialSlotData(std::span<MaterialSlot> data, UInt32 materialSlotIndex);
// #endif
//
private:
    struct MaterialInfo
    {
        MaterialR* mMaterial{nullptr};
        MaterialSlot mSlot;
    };

    struct MaterialInfoInEntity
    {
        std::vector<MaterialInfo> mMaterialInfos{};
        UInt32 mStellarMeshSceneIndex{INVALID_INDEX};   // Instance id. Indicate data offset in StellarMeshSceneBuffer
        UInt32 mMaterialSlotIndex{INVALID_INDEX};       // Material offset. Indicate instance's material offset in MaterialSlotBuffer. This value will be recorded in InstanceMaterialData.
    };
    
    ShadingBinManager mShadingBinManager{};

    std::mutex mMutex{};
    std::unordered_map<ecs::EntityID, MaterialInfoInEntity> mEntityToMaterialInfos{};

//     RenderWorld* mRenderWorld{};
//     RenderingExecutionDescriptor* mRED{};
//     const FFSRenderPipelineSetting* mFFSRenderPipelineSetting{};
//
//     REDUniquePtr<REDResidentBuffer> mMaterialSlotBuffer{};
//     REDUniquePtr<REDResidentBufferView> mMaterialSlotBufferView{};
//
//     REDUniquePtr<REDResidentBuffer> mInstanceMaterialDataBuffer{};
//     REDUniquePtr<REDResidentBufferView> mInstanceMaterialDataBufferView{};

#ifndef _MANAGED
    ThreadSafeIndexAllocator<false> mMaterialSlotAllocator{"MaterialSlotAllocator"};
    ScatterBytesUploadBuffer mMaterialSlotUploadBuffer{};
    ScatterBytesUploadBuffer mInstanceMaterialSlotUploadBuffer{};
#endif
    
    std::unordered_set<ecs::EntityID> mDirtyEntity{};
};

}   // namespace cross::StellarMesh
