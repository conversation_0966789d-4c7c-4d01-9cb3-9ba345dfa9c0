#pragma once
#include "RenderEngine/RenderMaterial.h"

namespace cross::StellarMesh {

constexpr UInt32 INVALID_INDEX = 0xFFFFFFFF;

enum class RenderFlag : UInt32
{
    bReceiveDecals = 1 << 0,
};

struct StellarMeshRasterPipeline
{
    MaterialR* mMaterial;
};

struct StellarMeshRasterPipelines
{
    std::map<MaterialR*, StellarMeshRasterPipeline> PipelineMap;
    std::vector<StellarMeshRasterPipeline*> Pipelines;
};

}   // namespace cross
