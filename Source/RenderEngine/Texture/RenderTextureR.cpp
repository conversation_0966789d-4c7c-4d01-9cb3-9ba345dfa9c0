#include "EnginePrefix.h"
#include "RenderTextureR.h"
#include "CECommon/Common/EngineGlobal.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"
//void RenderTexture::Initialize(std::shared_ptr<TextureDataProvider> data) {
//    using namespace cross;   // safe
//
//    auto info = data->GetTextureInfo();
//    auto format = GetGraphicsFormat(info.Format, info.ColorSpace);
//    Assert(format != GraphicsFormat::Unknown);
//    mTextureInfo = std::move(info);
//    UInt8 mipcount = static_cast<UInt8>(info.MipCount);
//    UInt8 realmipbias = 0;
//    if (info.MipCount > info.MipBias)
//    {
//        mipcount = static_cast<UInt8>(info.MipCount - info.MipBias);
//        realmipbias = info.MipBias;
//    }
//    UInt16 realwidth = static_cast<UInt16>(std::ceil(info.Width / (std::pow(2, realmipbias))));
//    UInt16 realheight = static_cast<UInt16>(std::ceil(info.Height / (std::pow(2, realmipbias))));
//    {
//        NGITextureDesc desc{
//            format,
//            GetNGITextureType(info.Dimension),
//            mipcount,
//            1,
//            realwidth,
//            realheight,
//            (UInt16)(info.Dimension == TextureDimension::Tex3D ? info.Depth : 1),
//            (UInt16)info.ArraySize,
//            NGITextureUsage::RenderTarget | NGITextureUsage::UnorderedAccess,
//        };
//
//        Initialize(desc);
//
//        auto texelBlock = GetFormatTexelBlockProperty(format);
//
//        auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
//
//        for (UInt32 m = realmipbias; m < data->GetImageCount(); m++)
//        {
//            UInt32 Wide, High, Depth;
//            data->GetImageSize(m, Wide, High, Depth);
//
//            UInt32 mipLevel, faceIndex, arrayIndex;
//            data->GetImageLevels(m, mipLevel, faceIndex, arrayIndex);
//            mipLevel = mipLevel - realmipbias;
//            auto* Data = data->GetImageData(m);
//            auto DataByteSize = data->GetImageDataByteSize(m);
//
//            auto ArrayIndex = IsCubeType(info.Dimension) ? 6 * arrayIndex + faceIndex : arrayIndex;
//
//            auto RowUpdateSize = data->GetImagePitchByteSize(m);
//            auto [buffer, offset, dstData] = rendererSystem->GetScratchBuffer()->Allocate(NGIBufferUsage::CopySrc, DataByteSize);
//            memcpy(dstData, Data, DataByteSize);
//            buffer->UnmapRange(offset, DataByteSize);
//
//            auto width = static_cast<UInt32>(std::ceil(mTexture->GetDesc().Width / std::pow(2, mipLevel)));
//            auto height = static_cast<UInt32>(std::ceil(mTexture->GetDesc().Height / std::pow(2, mipLevel)));
//
//            NGICopyBufferTexture region{
//                offset,
//                DataByteSize,
//                RowUpdateSize,
//
//                NGICalcSubresource(mipLevel, ArrayIndex, 0, desc.MipCount, desc.ArraySize),
//                {
//                    0,
//                    0,
//                    0,
//                },
//                {
//                    std::min(Wide, width),
//                    std::min(High, height),
//                    std::max(Depth, 1u),
//                },
//            };
//
//            rendererSystem->UpdateTexture(mTexture.get(), buffer, region, NGIResourceState::Undefined, NGIResourceState::PixelShaderShaderResource);
//        }
//    }
//#if !CROSSENGINE_EDITOR
//    data->OnDataUploaded();
//#endif
//}

cross::RenderTextureR::RenderTextureR(const resource::RenderTextureInfo& info)
    : mInfo{info}
{

    auto lamda = [=]() {
        auto RED = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor();

        NGITextureDesc desc{
            static_cast<GraphicsFormat>(info.Format),
            GetNGITextureType(info.Dimension),
            info.MipCount,
            info.SampleCount,
            info.Width,
            info.Height,
            info.Depth,
            info.LayerCount,
            NGITextureUsage::RenderTarget | NGITextureUsage::UnorderedAccess | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource,
        };

        mTexture = RED->CreateTexture(mInfo.Name, desc);

        NGITextureViewDesc viewDesc{NGITextureUsage::CopySrc | NGITextureUsage::CopyDst | NGITextureUsage::ShaderResource | NGITextureUsage::RenderTarget,
                                    desc.Format,
                                    desc.Dimension,
                                    {
                                        NGITextureAspect::Color,
                                        0,
                                        desc.MipCount,
                                        0,
                                        desc.ArraySize,
                                    }};
        mTextureView = RED->CreateTextureView(mTexture.get(), viewDesc);
    };

    if (threading::TaskSystem::IsInRenderingThread())
    {
        lamda();
    }
    else
    {
        DispatchRenderingCommandWithToken([=] { lamda(); });
    }

}