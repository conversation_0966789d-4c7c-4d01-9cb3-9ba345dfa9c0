#pragma once

#include "VirtualShadowMapCommon.h"

namespace cross {

class VirtualShadowMapArrayCache
{
public:
    VirtualShadowMapArrayCache();

    bool IsValid(RenderingExecutionDescriptor* red) const
    {
        return mIsValid && red->Validate(mFrameData.pageTableBuffer);
    }

    void Reset();

    void CacheFrameData(VirtualShadowMapArrayFrameData& frameData);

public:
    VirtualShadowMapArrayFrameData mFrameData;

private:
    bool mIsValid;
};
}   // namespace cross