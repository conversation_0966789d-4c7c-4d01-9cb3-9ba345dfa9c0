#pragma once

#include "VirtualShadowMapArray.h"

namespace cross {

class VirtualShadowMapClipmap
{
public:
    void Init(RenderWorld* renderWorld, ecs::EntityID cameraEntity, ecs::EntityID lightEntity);

    UInt32 GetLevelCount() const
    {
        return (UInt32)mLevelDatas.size();
    }

    SInt32 GetClipmapLevel(SInt32 clipmapIndex) const
    {
        return mFirstLevel + clipmapIndex;
    }

    VirtualShadowMapProjectionData GetProjectionData(UInt32 clipmapIndex) const;

    VirtualShadowMapViewData GetViewData(UInt32 clipmapIndex) const;

    void SetShadowMapId(SInt32 id)
    {
        for (UInt32 index = 0; index < mLevelDatas.size(); index++)
        {
            mLevelDatas[index].virtualShadowMap.SetId(id + index);
        }
    }

    SInt32 GetShadowMapId() const
    {
        if (mLevelDatas.size() > 0)
        {
            return mLevelDatas[0].virtualShadowMap.GetId();
        }
        return -1;
    }

    VirtualShadowMap* GetVirtualShadowMap(UInt32 clipmapIndex = 0)
    {
        return &mLevelDatas[clipmapIndex].virtualShadowMap;
    }

    // temp
    RenderCamera* GetRenderCamera(UInt32 clipmapIndex)
    {
        return &mLevelDatas[clipmapIndex].renderCamera;
    }

    static UInt32 GetCoarsePageClipmapIndexMask(const VirtualShadowMapSettings& shadowSettings);

private:
    RenderWorld* mRenderWorld;

    Float4x4 mWorldToLightViewRotationMatrix;
    Float3 mWorldOrigin;
    SInt32 mFirstLevel;
    Float3 mLightDirection;
    Float3 mTilePosition;
    float mResolutionLodBias;

    struct LevelData
    {
        VirtualShadowMap virtualShadowMap;
        Float4x4 viewToClip;
        Float3 worldCenter;
        Int2 cornerOffset;

        // temp
        RenderCamera renderCamera;
    };
    std::vector<LevelData> mLevelDatas;
};

}   // namespace cross