// Copyright Epic Games, Inc. All Rights Reserved.

#include "AllocatedVirtualTexture.h"
#include "VirtualTextureMath.h"
#include "VirtualTextureProducer.h"
#include "VirtualTextureSpace.h"
#include "VirtualTextureSystemR.h"

namespace cross
{
    AllocatedVirtualTexture::AllocatedVirtualTexture(VirtualTextureSystemR * inSystem,
                                                       UInt32 inFrame,
                                                       const FAllocatedVTDescription& inDesc,
                                                       VirtualTextureProducer* const* inProducers,
                                                       UInt32 inBlockWidthInTiles,
                                                       UInt32 inBlockHeightInTiles,
                                                       UInt32 inWidthInBlocks,
                                                       UInt32 inHeightInBlocks, UInt32 arrayX, UInt32 arrayY)
        : IAllocatedVirtualTexture(inDesc, inBlockWidthInTiles, inBlockHeightInTiles, inWidthInBlocks, inHeightInBlocks, arrayX, arrayY)
        , mFrameAllocated(inFrame)
        , mSpace(nullptr)
        , mVirtualPageX(~0u)
        , mVirtualPageY(~0u)
    {
        maxLevel = MAX_MAX_LEVEL;
        for (UInt32 LayerIndex = 0u; LayerIndex < description.numTextureLayers; ++LayerIndex)
        {
            VirtualTextureProducer const* producer = inProducers[LayerIndex];
            // Can't have missing entries for null producers if we're not merging duplicate layers
            if (producer || !description.bShareDuplicateLayers)
            {
                const UInt32 uniqueProducerIndex = AddUniqueProducer(inDesc.producerHandle[LayerIndex], producer);
                const UInt32 producerLayerIndex = inDesc.producerLayerIndex[LayerIndex];
                UInt32 producerPhysicalGroupIndex = 0u;
                VTPhysicalSpace* physicalSpace = nullptr;
                if (producer)
                {
                    producerPhysicalGroupIndex = producer->GetPhysicalGroupIndexForTextureLayer(producerLayerIndex);
                    physicalSpace = producer->GetPhysicalSpaceForPhysicalGroup(producerPhysicalGroupIndex);
                }
                const UInt32 uniquePhysicalSpaceIndex = AddUniquePhysicalSpace(physicalSpace, uniqueProducerIndex, producerPhysicalGroupIndex);
                mUniquePageTableLayers[uniquePhysicalSpaceIndex].producerTextureLayerMask |= 1 << producerLayerIndex;
                const UInt8 pageTableLayerLocalIndex = mUniquePageTableLayers[uniquePhysicalSpaceIndex].textureLayerCount++;

                mTextureLayers[LayerIndex].uniquePageTableLayerIndex = static_cast<UInt8>(uniquePhysicalSpaceIndex);
                mTextureLayers[LayerIndex].physicalTextureIndex = pageTableLayerLocalIndex;

                if (producer)
                {
                    mFallbackColorPerTextureLayer[LayerIndex] = producer->GetDescription().layerFallbackColor[producerLayerIndex];
                }
            }
        }

        // Must have at least 1 valid layer/producer
        Assert(mUniqueProducers.size() > 0u);
        maxLevel = std::min(maxLevel, VTMath::CeilLogTwo(std::min(GetWidthInTiles(), GetHeightInTiles())));
        maxLevel = std::min(maxLevel, VIRTUALTEXTURE_LOG2_MAX_PAGETABLE_SIZE - 1u);
        for (SInt32 producerIndex = 0u; producerIndex < mUniqueProducers.size(); ++producerIndex) 
        {
            VTProducerHandle producerHandle = mUniqueProducers[producerIndex].handle;
            VirtualTextureProducer* producer = inSystem->FindProducer(producerHandle);
            
            const UInt8 local_vLevel = static_cast<UInt8>(maxLevel - mUniqueProducers[producerIndex].mipBias);   // no mipbias
            const UInt32 mipScaleFactor = (1u << local_vLevel);
            const UInt32 rootWidthInTiles = VTMath::DivideAndRoundUp(producer->GetWidthInTiles(), mipScaleFactor);
            const UInt32 rootHeightInTiles = VTMath::DivideAndRoundUp(producer->GetHeightInTiles(), mipScaleFactor);

            for (UInt32 tileY = 0u; tileY < rootHeightInTiles; ++tileY)
            {
                for (UInt32 tileX = 0u; tileX < rootWidthInTiles; ++tileX)
                {
                    const UInt32 local_vAddress = VTMath::MortonCode2(tileX) | (VTMath::MortonCode2(tileY) << 1);
                    const VTLocalTile TileToUnlock(producerHandle, local_vAddress, local_vLevel);
                    inSystem->LockTile(TileToUnlock);
                }
            }
        }

        VTSpaceDescription spaceDesc;
        spaceDesc.dimensions = inDesc.dimensions;
        spaceDesc.numPageTableLayers = static_cast<UInt8>(mUniquePageTableLayers.size());
        spaceDesc.tileSize = inDesc.tileSize;
        spaceDesc.tileBorderSize = inDesc.tileBorderSize;
        spaceDesc.bPrivateSpace = inDesc.bPrivateSpace;
        spaceDesc.maxSpaceSize = inDesc.maxSpaceSize > 0 ? inDesc.maxSpaceSize : spaceDesc.maxSpaceSize;
        spaceDesc.indirectionTextureSize = inDesc.indirectionTextureSize;
        spaceDesc.pageTableFormat = EVTPageTableFormat::PageTableFormatUInt32;

        mSpace = inSystem->AcquireSpace(spaceDesc, inDesc.forceSpaceID, this);
        spaceID = mSpace->GetID();
        pageTableFormat = mSpace->GetPageTableFormat();      // use PageTableFormatUInt32
    }
    AllocatedVirtualTexture::~AllocatedVirtualTexture() {}

    void AllocatedVirtualTexture::AssignVirtualAddress(UInt32 vAddress) 
    {
        Assert(virtualAddress == ~0u);  // "Trying to assign vAddress to AllocatedVT, already assigned"
        Assert(vAddress != ~0u);
        virtualAddress = vAddress;
        mVirtualPageX = VTMath::ReverseMortonCode2(vAddress);
        mVirtualPageY = VTMath::ReverseMortonCode2(vAddress >> 1);
    }

    UInt32 AllocatedVirtualTexture::GetNumPageTableTextures() const
    {
        return mSpace->GetNumPageTableTextures();
    }

    NGITexture* AllocatedVirtualTexture::GetPageTableTexture(UInt32 inPageTableIndex) const
    {
        return mSpace->GetPageTableTexture(inPageTableIndex);
    }

    NGITextureView* AllocatedVirtualTexture::GetPageTableTextureSRV(UInt32 inPageTableIndex) const
    {
        return mSpace->GetPageTableTextureSRV(inPageTableIndex);
    }

    UInt32 AllocatedVirtualTexture::GetPhysicalTextureSize(UInt32 inLayerIndex) const
    {
        if (inLayerIndex < description.numTextureLayers)
        {
            const VTPhysicalSpace* physicalSpace = mUniquePageTableLayers[mTextureLayers[inLayerIndex].uniquePageTableLayerIndex].physicalSpace;
            return physicalSpace ? physicalSpace->GetTextureSize() : 0u;
        }
        return 0u;
    }

    NGITexture* AllocatedVirtualTexture::GetPhysicalTexture(UInt32 inLayerIndex) const
    {
        if (inLayerIndex < description.numTextureLayers)
        {
            const VTPhysicalSpace* physicalSpace = mUniquePageTableLayers[mTextureLayers[inLayerIndex].uniquePageTableLayerIndex].physicalSpace;
            return physicalSpace ? physicalSpace->GetPhysicalTexture(mTextureLayers[inLayerIndex].physicalTextureIndex) : nullptr;
        }
        return nullptr;
    }

    NGITextureView* AllocatedVirtualTexture::GetPhysicalTextureSRV(UInt32 inLayerIndex) const
    {
        if (inLayerIndex < description.numTextureLayers)
        {
            const VTPhysicalSpace* physicalSpace = mUniquePageTableLayers[mTextureLayers[inLayerIndex].uniquePageTableLayerIndex].physicalSpace;
            return physicalSpace ? physicalSpace->GetPhysicalTextureSRV(mTextureLayers[inLayerIndex].physicalTextureIndex) : nullptr;
        }
        return nullptr;
    }

    void AllocatedVirtualTexture::GetPackedPageTableUniform(UInt4* outUniform) const 
    {
        const UInt32 vPageX = mVirtualPageX;
        const UInt32 vPageY = mVirtualPageY;
        const UInt32 vArrayX = GetVirtualArrayX();
        const UInt32 vArrayY = GetVirtualArrayY();
        const UInt32 vPageSize = GetVirtualTileSize();
        const UInt32 widthInPages = GetWidthInTiles();
        const UInt32 heightInPages = GetHeightInTiles();
        const UInt32 vPageTableMipBias = VTMath::FloorLog2(vPageSize);

        // Here maxAnisotropy only controls the VT mip level selection
        // We don't need to limit this value based on border size, and we can add this factor in even if HW anisotropic filtering is disabled
        const UInt32 maxAnisotropy = 8;
        const UInt32 maxAnisotropyLog2 = (maxAnisotropy > 0u) ? VTMath::FloorLog2(maxAnisotropy) : 0u;

        // make sure everything fits in the allocated number of bits
        Assert(vPageX < 4096u);
        Assert(vPageY < 4096u);
        Assert(vPageTableMipBias < 16u);
        Assert(maxLevel < 16u);
        Assert(spaceID < 16u);

        outUniform[0].x = BitcastFloatToUInt32(1.0f / static_cast<float>(widthInBlocks));
        outUniform[0].y = BitcastFloatToUInt32(1.0f / static_cast<float>(heightInBlocks));

        outUniform[0].z = BitcastFloatToUInt32(static_cast<float>(widthInPages));
        outUniform[0].w = BitcastFloatToUInt32(static_cast<float>(heightInPages));

        outUniform[1].x = BitcastFloatToUInt32(static_cast<float>(maxAnisotropyLog2));
        outUniform[1].y = vPageX | (vPageY << 12) | (vPageTableMipBias << 24);
        outUniform[1].z = maxLevel;
        outUniform[1].w = (spaceID << 28);

        outUniform[2].x = vArrayX;
        outUniform[2].y = vArrayY;
    }

    void AllocatedVirtualTexture::GetPackedUniform(UInt4* outUniform, UInt32 layerIndex) const 
    {
        const UInt32 physicalTextureSize = GetPhysicalTextureSize(layerIndex);
        if (physicalTextureSize > 0u)
        {
            const UInt32 vPageSize = GetVirtualTileSize();
            const UInt32 pageBorderSize = GetTileBorderSize();
            const float rcpPhysicalTextureSize = 1.0f / static_cast<float>(physicalTextureSize);
            const UInt32 pPageSize = vPageSize + pageBorderSize * 2u;

            // normally we need get the average color of the image/udim images;
            // but it will request re-import all asset, so we use white color instead  (note that normal is wrong);
            // TODO(anyone else but me): layered color
            outUniform->x = 0xffffffff;//*reinterpret_cast<UInt32*>(mFallbackColorPerTextureLayer[layerIndex]);   // fallback color
            outUniform->y = BitcastFloatToUInt32(static_cast<float>(vPageSize * rcpPhysicalTextureSize));
            outUniform->z = BitcastFloatToUInt32(static_cast<float>(pageBorderSize * rcpPhysicalTextureSize));
            // Pack page table format bool as sign bit on page size.
            const bool bPageTableExtraBits = GetPageTableFormat() == EVTPageTableFormat::PageTableFormatUInt32;
            const float PackedSignBit = bPageTableExtraBits ? 1.f : -1.f;
            outUniform->w = BitcastFloatToUInt32(static_cast<float>(pPageSize * rcpPhysicalTextureSize * PackedSignBit));
        }
        else
        {
            outUniform->x = 0u;
            outUniform->y = 0u;
            outUniform->z = 0u;
            outUniform->w = 0u;
        }
    }

    void AllocatedVirtualTexture::UnlockLockedTiles(VirtualTextureSystemR* inSystem) const
    {
        for (SInt32 ProducerIndex = 0u; ProducerIndex < mUniqueProducers.size(); ++ProducerIndex)
        {
            VTProducerHandle producerHandle = mUniqueProducers[ProducerIndex].handle;
            VirtualTextureProducer* producer = inSystem->FindProducer(producerHandle);
            if (producer)
            {             
                const UInt8 local_vLevel = static_cast<UInt8>(maxLevel - mUniqueProducers[ProducerIndex].mipBias);   // no mipbias
                const UInt32 mipScaleFactor = (1u << local_vLevel);
                const UInt32 rootWidthInTiles = VTMath::DivideAndRoundUp(producer->GetWidthInTiles(), mipScaleFactor);
                const UInt32 rootHeightInTiles = VTMath::DivideAndRoundUp(producer->GetHeightInTiles(), mipScaleFactor);

                for (UInt32 tileY = 0u; tileY < rootHeightInTiles; ++tileY)
                {
                    for (UInt32 tileX = 0u; tileX < rootWidthInTiles; ++tileX)
                    {
                        const UInt32 local_vAddress = VTMath::MortonCode2(tileX) | (VTMath::MortonCode2(tileY) << 1);
                        const VTLocalTile TileToUnlock(producerHandle, local_vAddress, local_vLevel);
                        inSystem->UnlockTile(TileToUnlock, producer);
                    }
                }
            }  
        }
    }

    bool AllocatedVirtualTexture::TryMapLockedTiles(VirtualTextureSystemR* inSystem) const
    {
        bool bHasMissingTiles = false;
        for (UInt8 pageTableLayerIndex = 0u; pageTableLayerIndex < static_cast<UInt8>(mUniquePageTableLayers.size()); ++pageTableLayerIndex)
        {
            const PageTableLayerDesc& pageTableLayer = mUniquePageTableLayers[pageTableLayerIndex];
            const ProducerDesc& uniqueProducer = mUniqueProducers[pageTableLayer.uniqueProducerIndex];
            const VirtualTextureProducer* producer = inSystem->FindProducer(uniqueProducer.handle);
            if (!producer)
            {
                continue;
            }

            const UInt32 widthInTiles = producer->GetWidthInTiles();
            const UInt32 heightInTiles = producer->GetHeightInTiles();
            const UInt8 local_vLevel = static_cast<UInt8>(std::min(maxLevel - uniqueProducer.mipBias, producer->GetMaxLevel()));
            const UInt32 mipScaleFactor = (1u << local_vLevel);
            const UInt32 rootWidthInTiles = VTMath::DivideAndRoundUp(widthInTiles, mipScaleFactor);
            const UInt32 rootHeightInTiles = VTMath::DivideAndRoundUp(heightInTiles, mipScaleFactor);

            VTPagePool& pagePool = pageTableLayer.physicalSpace->GetPagePool();
            VTPageMap& pageMap = mSpace->GetPageMapForPageTableLayer(pageTableLayerIndex);

            for (UInt32 tileY = 0u; tileY < rootHeightInTiles; ++tileY)
            {
                for (UInt32 tileX = 0u; tileX < rootWidthInTiles; ++tileX)
                {
                    const UInt32 vAddress = VTMath::MortonCode2(mVirtualPageX + (tileX << maxLevel)) | (VTMath::MortonCode2(mVirtualPageY + (tileY << maxLevel)) << 1);
                    UInt32 pAddress = pageMap.FindPageAddress(static_cast<UInt8>(maxLevel), vAddress);
                    if (pAddress == ~0u)
                    {
                        UInt32 local_vAddress = VTMath::MortonCode2(tileX) | (VTMath::MortonCode2(tileY) << 1);

                        const UInt32 localMipBias = 0u;
                        local_vAddress >>= (localMipBias * description.dimensions);

                        pAddress = pagePool.FindPageAddress(uniqueProducer.handle, pageTableLayer.producerPhysicalGroupIndex, local_vAddress, static_cast<UInt8>(local_vLevel + localMipBias));
                        if (pAddress != ~0u)
                        {
                            pagePool.MapPage(mSpace, pageTableLayer.physicalSpace, static_cast<UInt8>(pageTableLayerIndex), static_cast<UInt8>(maxLevel), 
                                static_cast<UInt8>(maxLevel), vAddress, static_cast<UInt8>(maxLevel + localMipBias), static_cast<UInt16>(pAddress));
                        }
                        else
                        {
                            bHasMissingTiles = true;
                            // Mark page table entry as invalid if we can't map.
                            pageMap.InvalidateUnmappedRootPage(mSpace, pageTableLayer.physicalSpace, pageTableLayerIndex, 
                                static_cast<UInt8>(maxLevel), static_cast<UInt8>(maxLevel), vAddress, static_cast<UInt8>(maxLevel + localMipBias));
                        }
                    }
                }
            }
        }

        return !bHasMissingTiles;
    }

    void AllocatedVirtualTexture::Destroy(VirtualTextureSystemR* inSystem) const
    {
        // physical pool need to evict all page that belong this vt
        {
            UnlockLockedTiles(inSystem);
            const UInt32 widthInTiles = GetWidthInTiles();
            const UInt32 heightInTiles = GetHeightInTiles();

            std::vector<VTPhysicalSpace*> UniquePhysicalSpaces;
            for (SInt32 PageTableIndex = 0u; PageTableIndex < mUniquePageTableLayers.size(); ++PageTableIndex)
            {
                if (mUniquePageTableLayers[PageTableIndex].physicalSpace)
                {
                    UniquePhysicalSpaces.push_back(mUniquePageTableLayers[PageTableIndex].physicalSpace);
                }
            }

            for (VTPhysicalSpace* PhysicalSpace : UniquePhysicalSpaces)
            {
                PhysicalSpace->GetPagePool().UnmapAllPagesForSpace(inSystem, mSpace->GetID(), virtualAddress, widthInTiles, heightInTiles, maxLevel);
            }

            for (SInt32 PageTableIndex = 0u; PageTableIndex < mUniquePageTableLayers.size(); ++PageTableIndex)
            {
                mUniquePageTableLayers[PageTableIndex].physicalSpace->Release();
            }
        }

        mSpace->FreeVirtualTexture(const_cast<AllocatedVirtualTexture*>(this));
        inSystem->ReleaseSpace(mSpace);
    }

    UInt32 AllocatedVirtualTexture::AddUniqueProducer(VTProducerHandle const& inHandle, const VirtualTextureProducer* inProducer)
    {
        for (UInt32 index = 0u; index < static_cast<UInt32>(mUniqueProducers.size()); ++index)
        {
            if (mUniqueProducers[index].handle == inHandle)
            {
                return index;
            }
        }

        ProducerDesc currentDesc;

        UInt32 mipBias = 0u;
        if (inProducer) 
        {
            const VTProducerDescription& producerDesc = inProducer->GetDescription();
            // maybe these values should just be set by producers, rather than also set on AllocatedVT desc
            Assert(producerDesc.dimensions == description.dimensions);
            Assert(producerDesc.tileSize == description.tileSize);
            Assert(producerDesc.tileBorderSize == description.tileBorderSize);

            const UInt32 MipBiasX = VTMath::CeilLogTwo(blockWidthInTiles / producerDesc.blockWidthInTiles);
            const UInt32 MipBiasY = VTMath::CeilLogTwo(blockHeightInTiles / producerDesc.blockHeightInTiles);
            Assert(producerDesc.blockWidthInTiles << MipBiasX == blockWidthInTiles);
            Assert(producerDesc.blockHeightInTiles << MipBiasY == blockHeightInTiles);

            mipBias = std::min(MipBiasX, MipBiasY);
            // Take care not to access a mip level that exceeds the producer's maximum level, 
            // as this may result in requesting an invalid mip in VirtualTextureCommen.hlsl and falling back to a fallback value.
            maxLevel = std::min<UInt32>(maxLevel, producerDesc.maxLevel + mipBias);
        }

        currentDesc.handle = inHandle;
        currentDesc.mipBias = static_cast<UInt8>(mipBias);
        mUniqueProducers.emplace_back(currentDesc);

        return static_cast<UInt32>(mUniqueProducers.size() - 1);
    }
    UInt32 AllocatedVirtualTexture::AddUniquePhysicalSpace(VTPhysicalSpace* inPhysicalSpace, UInt32 inUniqueProducerIndex, UInt32 inProducerPhysicalSpaceIndex)
    {
        if (description.bShareDuplicateLayers)
        {
            for (SInt32 index = 0u; index < mUniquePageTableLayers.size(); ++index)
            {
                if (mUniquePageTableLayers[index].physicalSpace == inPhysicalSpace && mUniquePageTableLayers[index].uniqueProducerIndex == inUniqueProducerIndex &&
                    mUniquePageTableLayers[index].producerPhysicalGroupIndex == inProducerPhysicalSpaceIndex)
                {
                    return index;
                }
            }
        }
        PageTableLayerDesc desc;
        desc.physicalSpace = inPhysicalSpace;
        inPhysicalSpace->AddRef();
        desc.uniqueProducerIndex = static_cast<UInt8>(inUniqueProducerIndex);
        desc.producerPhysicalGroupIndex = static_cast<UInt8>(inProducerPhysicalSpaceIndex);
        desc.producerTextureLayerMask = 0;
        desc.textureLayerCount = 0;
        mUniquePageTableLayers.push_back(desc);

        const UInt32 index = static_cast<UInt32>(mUniquePageTableLayers.size() - 1);
        Assert(index < VIRTUALTEXTURE_SPACE_MAXLAYERS);

        return index;
    }
}