#pragma once
#include "NativeGraphicsInterface/NGI.h"
#include "CrossBase/Math/CrossMath.h"

namespace cross {
    extern UInt32 GetVirtualTextureFeedbackScale();

    extern UInt2 GetVirtualTextureFeedbackBufferSize(UInt2 inSceneTextureExtent);

    extern UInt32 SampleVirtualTextureFeedbackSequence(UInt32 inFrameIndex);

    struct VTFeedbackBufferDesc
    {
        void Init2D(UInt2 inBufferSize, UInt32 inBufferScale);

        UInt2 bufferSize = {0, 0};
        SInt32 numRects = 0;
        SInt32 TotalReadSize = 0;
    };    
}
