#include "VirtualTextureSpace.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "RenderEngine/VirtualTexture/VirtualTextureMath.h"
#include "RenderEngine/RendererSystemR.h"
#include "RenderEngine/RenderEngine.h"
#include "RenderEngine/RenderingExecutionDescriptor/RenderingExecutionDescriptor.h"

namespace cross {
bool VTDebugLog = false;
VTSpace::VTSpace() {}

VTSpace::VTSpace(UInt8 inID, const VTSpaceDescription& inDesc)
    : mDescription(inDesc)
    , mAllocator(inDesc.dimensions)
    , mNumRefs(0u)
    , mID(inID)
    , mCachedPageTableWidth(0u)
    , mCachedPageTableHeight(0u)
    , mCachedNumPageTableLevels(0u)
    , bNeedToAllocatePageTable(true)
    , bForceEntireUpdate(false)
{
    const UInt32 platformMaxTextureSize = 16384;   // by platform metal 16384, vulkan different device, different value, maybe 8192, import texture size
    const UInt32 physicalTileSize = inDesc.tileSize + inDesc.tileBorderSize * 2u;
    const UInt32 maxSizeInTiles = platformMaxTextureSize / physicalTileSize;
    const UInt32 maxNumTiles = maxSizeInTiles * maxSizeInTiles;
    for (UInt32 layerIndex = 0u; layerIndex < inDesc.numPageTableLayers; ++layerIndex)
    {
        mPhysicalPageMap[layerIndex].Initialize(maxNumTiles, layerIndex, inDesc.dimensions);
    }

    mAllocator.Initialize(mDescription.maxSpaceSize);
    InitRenderResource();
}

void VTSpace::ReleaseRenderResource()
{
    for (UInt32 textureIndex = 0u; textureIndex < GetNumPageTableTextures(); ++textureIndex)
    {
        // Page Table
        FTextureEntry& textureEntry = mPageTable[textureIndex];
        textureEntry.texture.reset();
        textureEntry.textureView.reset();
    }
}

UInt32 VTSpace::AllocateVirtualTexture(AllocatedVirtualTexture* virtualTexture)
{
    UInt32 vAddress = mAllocator.Alloc(virtualTexture);
    if (mAllocator.GetAllocatedWidth() > mCachedPageTableWidth || mAllocator.GetAllocatedHeight() > mCachedPageTableHeight)
    {
        bNeedToAllocatePageTable = true;
    }

    return vAddress;
}

void VTSpace::FreeVirtualTexture(AllocatedVirtualTexture* virtualTexture)
{
    mAllocator.Free(virtualTexture);
}

void VTSpace::QueueUpdate(UInt8 layer, UInt8 vLogSize, UInt32 vAddress, UInt8 vLevel, const PhysicalTileLocation& pTileLocation)
{
    PageTableUpdate update;
    update.vAddress = vAddress;
    update.pTileLocation = pTileLocation;
    update.vLevel = vLevel;
    update.vLogSize = vLogSize;
    mPageTableUpdates[layer].push_back(update);
}

void VTSpace::AllocateTextures()
{
    if (bNeedToAllocatePageTable)
    {
        mCachedPageTableWidth = VTMath::Align(mAllocator.GetAllocatedWidth(), VIRTUALTEXTURE_MIN_PAGETABLE_SIZE);
        mCachedPageTableHeight = VTMath::Align(mAllocator.GetAllocatedHeight(), VIRTUALTEXTURE_MIN_PAGETABLE_SIZE);
        mCachedNumPageTableLevels = VTMath::FloorLog2(std::max(mCachedPageTableWidth, mCachedPageTableHeight)) + 1u;

        auto* RED = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>()->GetRenderingExecutionDescriptor();

        for (UInt32 textureIndex = 0u; textureIndex < GetNumPageTableTextures(); ++textureIndex)
        {
            // Page Table
            FTextureEntry& textureEntry = mPageTable[textureIndex];
            const NGITextureDesc desc{GraphicsFormat::R32G32B32A32_UInt,
                                      NGITextureType::Texture2D,
                                      static_cast<UInt16>(mCachedNumPageTableLevels),
                                      1,
                                      mCachedPageTableWidth,
                                      mCachedPageTableHeight,
                                      1,
                                      1,
                                      NGITextureUsage::UnorderedAccess | NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst};
            textureEntry.texture = RED->CreateTexture("PageTableTexture", desc);

            NGITextureViewDesc viewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
                                        desc.Format,
                                        desc.Dimension,
                                        {
                                            NGITextureAspect::Color,
                                            0,
                                            desc.MipCount,
                                            0,
                                            desc.ArraySize,
                                        }};

            textureEntry.textureView = RED->CreateTextureView(textureEntry.texture.get(), viewDesc);
        }
        bNeedToAllocatePageTable = false;
        bForceEntireUpdate = true;
    }
}

bool TEST_CUSTOMIZE_UPDATE = true;

void VTSpace::ApplyUpdates(RenderingExecutionDescriptor* RED, RenderWorld* World, ComputeShaderR* VTCS, VirtualTextureSystemR* system)
{
    std::vector<PageTableUpdate> expandedUpdates[VIRTUALTEXTURE_SPACE_MAXLAYERS][16];
    if (bNeedToAllocatePageTable)
    {
        // Defer updates until next frame if page table needs to be re-allocated
        return;
    }
    for (UInt32 layerIndex = 0u; layerIndex < mDescription.numPageTableLayers; ++layerIndex)
    {
        VTPageMap& pageMap = mPhysicalPageMap[layerIndex];

        if (TEST_CUSTOMIZE_UPDATE)
        {
            if (bForceEntireUpdate)
            {
                pageMap.RefreshEntirePageTableCustomized(system, expandedUpdates[layerIndex]);
                bForceEntireUpdate = false;
            }
            else
            {
                pageMap.RefreshPageTableIncremental(system, expandedUpdates[layerIndex]);
            }
        }
        else
        {
            if (bForceEntireUpdate)
            {
                pageMap.RefreshEntirePageTable(system, expandedUpdates[layerIndex]);
            }
            else
            {
                if (mPageTableUpdates[layerIndex].size())
                {
                    if (VTDebugLog)
                    {
                        LOG_INFO("Befor Expantion  Update {} ", mPageTableUpdates[layerIndex].size());
                    }

                    for (const PageTableUpdate& update : mPageTableUpdates[layerIndex])
                    {
                        VTDebugLog ? update.OutputLog() : 0;
                        pageMap.ExpandPageTableUpdateMasked(system, update, expandedUpdates[layerIndex]);
                        // pageMap.ExpandPageTableUpdatePainters(system, update, expandedUpdates[layerIndex]);
                    }
                }
            }
        }
        mPageTableUpdates[layerIndex].clear();
    }

    UInt32 totalNumUpdates = 0;
    for (UInt32 layerIndex = 0u; layerIndex < mDescription.numPageTableLayers; ++layerIndex)
    {
        for (UInt32 mip = 0; mip < mCachedNumPageTableLevels; mip++)
        {
            totalNumUpdates += static_cast<UInt32>(expandedUpdates[layerIndex][mip].size());
        }
    }
    if (totalNumUpdates == 0u)
    {
        return;
    }

    // copy data to buffer
    auto* rendererSystem = EngineGlobal::GetRenderEngine()->GetGlobalSystem<RendererSystemR>();
    auto* scratchBuffer = rendererSystem->GetScratchBuffer();

    NGIBufferView* updateBufferView = nullptr;

    if (bPageTableCleaned == false || (TEST_CUSTOMIZE_UPDATE && bForceEntireUpdate))
    {
        ClearPageTableValue(RED);
        bPageTableCleaned = true;
    }
    // multi pagetable may have some warning
    for (UInt32 layerIndex = 0u; layerIndex < mDescription.numPageTableLayers; ++layerIndex)
    {
        std::vector<PageTableUpdate> UniqueMips[16];


        for (SInt32 mip = mCachedNumPageTableLevels - 1; mip >= 0; mip--)
        {
            //if (TEST_CUSTOMIZE_UPDATE)
            {
                UniqueMips[mip] = expandedUpdates[layerIndex][mip];
            }

            SizeType numUpdates = UniqueMips[mip].size();
            if (numUpdates)
            {
                if (VTDebugLog)
                {
                    LOG_INFO("Total Update {} in Mip {}", UniqueMips[mip].size(), mip);
                }
                for (auto& t : UniqueMips[mip])
                {
                    //UInt32 x = VTMath::ReverseMortonCode2(t.vAddress);
                    //UInt32 y = VTMath::ReverseMortonCode2(t.vAddress >> 1);
                    VTDebugLog ? t.OutputLog() : 0;
                }

                SizeType uploadSize = numUpdates * sizeof(PageTableUpdate);
                auto stagingBufferWrap = scratchBuffer->AllocateScratch(NGIBufferUsage::TexelBuffer, uploadSize);
                stagingBufferWrap.MemWrite(0, UniqueMips[mip].data(), uploadSize);
                NGICopyBuffer region{
                    stagingBufferWrap.GetNGIOffset(),
                    0,
                    uploadSize,
                };

                NGIBufferViewDesc viewDesc{NGIBufferUsage::TexelBuffer, stagingBufferWrap.GetNGIOffset(), uploadSize, GraphicsFormat::R16G16B16A16_UInt, 0};
                updateBufferView = rendererSystem->GetTransientResourceManager()->AllocateBufferView(viewDesc, stagingBufferWrap.GetNGIBuffer());
            }
            //else
            //{
            //    //continue;
            //}
           

            UniqueMips[mip].clear();

            // update pagetable texture mip data
            if(numUpdates)
            {
                totalNumUpdates = static_cast<UInt32>(numUpdates);
                //---------pass: PageTableUpdate----------
                {
                    auto* pageTableUpdatePass = RED->AllocatePass(("PageTableUpdate_Mip" + std::to_string(mip)));
                    pageTableUpdatePass->SetProperty("UpdateBuffer", numUpdates?updateBufferView:rendererSystem->GetRenderPrimitives()->mDefaultBufferView.get());
                    pageTableUpdatePass->SetProperty("PageTableWidth", static_cast<int>(mCachedPageTableWidth >> mip));
                    pageTableUpdatePass->SetProperty("PageTableHeight", static_cast<int>(mCachedPageTableHeight >> mip));
                    pageTableUpdatePass->SetProperty("NumUpdates", static_cast<int>(totalNumUpdates));
                    pageTableUpdatePass->SetProperty("MipLevel", static_cast<int>(mip));
                    pageTableUpdatePass->SetProperty("LayerIndex", static_cast<int>(layerIndex));

                    NGITextureViewDesc mipViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
                                                   GraphicsFormat::R32G32B32A32_UInt,
                                                   NGITextureType::Texture2D,
                                                   {
                                                       NGITextureAspect::Color,
                                                       static_cast<UInt16>(mip),
                                                       1,
                                                       0,
                                                       1,
                                                   }};

                    auto pageTableViewRED = RED->AllocateTextureView(mPageTable[0].texture.get(), mipViewDesc);
                    //pageTableViewRED->SetExternalState(NGIResourceState::ShaderStageBitMask | NGIResourceState::ShaderResourceBit);
                    pageTableUpdatePass->SetProperty("PageTableTexture", pageTableViewRED);

                    UInt32 x, y, z;
                    VTCS->GetThreadGroupSize("PageTableUpdateCS", x, y, z);

                    pageTableUpdatePass->Dispatch(VTCS, "PageTableUpdateCS", 1u + totalNumUpdates / (x * y * z), 1u, 1u);

                    RED->FlushState(mPageTable[0].texture.get(), NGIResourceState::ShaderResourceBit | NGIResourceState::ShaderStageBitMask);
                }
            }
        }
    }
}

void VTSpace::InitRenderResource()
{
    for (UInt32 textureIndex = 0u; textureIndex < GetNumPageTableTextures(); ++textureIndex)
    {
        FTextureEntry& textureEntry = mPageTable[textureIndex];
        textureEntry.texture = nullptr;
    }
}
void VTSpace::SetPagetableCleanedState(bool cleaned) 
{
    bPageTableCleaned = cleaned;
}
void VTSpace::ClearPageTableValue(RenderingExecutionDescriptor* RED)
{
    for (UInt32 layerIndex = 0u; layerIndex < mDescription.numPageTableLayers; layerIndex += 4)
    {
        //mPageTable[layerIndex / 4].pagetableRedTex = RED->AllocateTexture("PageTableTexture", mPageTable[layerIndex / 4].texture);

       // for (SInt32 mip = mCachedNumPageTableLevels - 1; mip >= 0 ; mip--)
        {
            {
                NGITextureViewDesc mipViewDesc{NGITextureUsage::UnorderedAccess | NGITextureUsage::RenderTarget | NGITextureUsage::ShaderResource | NGITextureUsage::CopySrc | NGITextureUsage::CopyDst,
                                               GraphicsFormat::R32G32B32A32_UInt,
                                               NGITextureType::Texture2D,
                                               {
                                                   NGITextureAspect::Color,
                                                   0,
                                                   static_cast<UInt16>(mCachedNumPageTableLevels - 1),
                                                   0,
                                                   1,
                                               }};
                auto pageTableViewRED = RED->AllocateTextureView(mPageTable[layerIndex / 4].texture.get(), mipViewDesc);

                NGIClearValue clearValue;
                std::fill_n(clearValue.coloru, 4, 0);
                RED->AllocatePass("clearPageTable")->ClearTexture(pageTableViewRED, clearValue);
                bPageTableCleaned = true;
            }
        }
    }
}
}   // namespace cross
