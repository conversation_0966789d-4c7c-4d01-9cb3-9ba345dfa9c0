#pragma once
#include "EnginePrefix.h"
#include "CrossBase/Platform/PlatformTypes.h"
#include "NativeGraphicsInterface/NGI.h"
#include "CECommon/Common/TextureDefines.h"
#include "CrossBase/Math/ColorSpace.h"
#include "RenderEngine/VirtualTexture/VirtualTextureMath.h"
#include "RenderEngine/Texture/GPUTexture.h"

namespace cross {

/** Maximum number of layers that can be allocated in a single VT page table */
#define VIRTUALTEXTURE_SPACE_MAXLAYERS 8

/** Maximum dimension of VT page table texture */
#define VIRTUALTEXTURE_LOG2_MAX_PAGETABLE_SIZE 12u
#define VIRTUALTEXTURE_MAX_PAGETABLE_SIZE      (1u << VIRTUALTEXTURE_LOG2_MAX_PAGETABLE_SIZE)
#define VIRTUALTEXTURE_MIN_PAGETABLE_SIZE      32u


#ifdef _WIN32
#    pragma warning(push)
#    pragma warning(disable : 4200)   // zero-sized struct
#    pragma warning(disable : 4201)   // nameless struct/union
#endif
class VirtualTextureSystemR;



union VTProducerHandle
{
    VTProducerHandle()
        : packedValue(0u)
    {}
    explicit VTProducerHandle(UInt32 inPackedValue)
        : packedValue(inPackedValue)
    {}
    VTProducerHandle(UInt32 inIndex, UInt32 inMagic)
        : index(inIndex)
        , magic(inMagic)
    {}

    inline bool IsValid() const
    {
        return packedValue != 0u;
    }
    inline bool IsNull() const
    {
        return packedValue == 0u;
    }

    UInt32 packedValue;
    struct
    {
        UInt32 index : 22;
        UInt32 magic : 10;
    };
};

inline bool operator==(const VTProducerHandle& lhs, const VTProducerHandle& rhs)
{
    return lhs.packedValue == rhs.packedValue;
}
inline bool operator!=(const VTProducerHandle& lhs, const VTProducerHandle& rhs)
{
    return lhs.packedValue != rhs.packedValue;
}


struct VTLayerProperty
{
    VTLayerProperty(VTProducerHandle inProducer, UInt8 inLayerIndex, GPUTexture * inTexture)
        : mProducerHandle(inProducer)
        , mLayerIndex(inLayerIndex)
        , mGpuTexture(inTexture)
    {}
    VTLayerProperty() = default;
    VTProducerHandle mProducerHandle = VTProducerHandle(0u);
    UInt8 mLayerIndex = static_cast<UInt8>(-1);
    GPUTexture* mGpuTexture = nullptr;
};

enum class EVTProducePageFlags : UInt8
{
    None = 0u,
    SkipPageBorders = (1u << 0),
    ContinuousUpdate = (1u << 1),
};

enum class EVTRequestPagePriority
{
    Normal,
    High,
};

enum class EVTRequestPageStatus
{
    Invalid,
    Saturated,
    Pending,
    Available,
};

struct VTRequestPageResult
{
    VTRequestPageResult(EVTRequestPageStatus inStatus = EVTRequestPageStatus::Invalid, UInt64 inHandle = 0u)
        : handle(inHandle)
        , status(inStatus)
    {}

    /** Opaque handle to the request, must be passed to 'ProducePageData'.  Only valid if status is Pending/Available */
    UInt64 handle;

    /** Status of the request */
    EVTRequestPageStatus status;
};

/**
 * Identifies a VT tile within a given producer
 */
union VTLocalTile
{
    inline VTLocalTile() {}
    inline VTLocalTile(const VTProducerHandle& inProducerHandle, UInt32 inLocal_vAddress, UInt8 inLocal_vLevel)
        : packedProducerHandle(inProducerHandle.packedValue)
        , local_vAddress(inLocal_vAddress)
        , local_vLevel(inLocal_vLevel)
        , pad(0)
    {}

    inline VTProducerHandle GetProducerHandle() const
    {
        return VTProducerHandle(packedProducerHandle);
    }

    UInt32 LocalTileX() const
    {
       return  VTMath::ReverseMortonCode2(static_cast<UInt32>(local_vAddress));
    }

    UInt32 LocalTileY() const
    {
        return  VTMath::ReverseMortonCode2(static_cast<UInt32>(local_vAddress >> 1));
    }

    UInt64 packedValue;
    struct
    {
        UInt32 packedProducerHandle;
        UInt32 local_vAddress : 24;
        UInt32 local_vLevel   : 4;
        UInt32 pad            : 4;
    };
};


inline auto vAdressToXY(UInt32 local_vAddress)
{
    return std::pair<UInt32, UInt32>(VTMath::ReverseMortonCode2(static_cast<UInt32>(local_vAddress)), VTMath::ReverseMortonCode2(static_cast<UInt32>(local_vAddress >> 1)));
}

inline bool operator<(const VTLocalTile& lhs, const VTLocalTile& rhs)
{
    return lhs.local_vAddress < rhs.local_vAddress;
}
inline bool operator==(const VTLocalTile& lhs, const VTLocalTile& rhs)
{
    return lhs.packedValue == rhs.packedValue;
}
inline bool operator!=(const VTLocalTile& lhs, const VTLocalTile& rhs)
{
    return lhs.packedValue != rhs.packedValue;
}

struct VTProducerDescription
{
    bool bPersistentHighestMip = true;
    bool bContinuousUpdate = false;

    UInt32 tileSize = 0u;
    UInt32 tileBorderSize = 0u;

    /**
     * Producers are made up of a number of block, each block has uniform size, and blocks are arranged in a larger grid
     * 'Normal' VTs will typically be a single block, for UDIM textures, blocks will map to individual UDIM texture sheets
     * When multiple producers are allocated together, they will be aligned such that blocks of each layer overlay on top of each other
     * Number of blocks for each layer may be different in this case, this is handled by wrapping blocks for layers with fewer blocks
     */
    UInt32 blockWidthInTiles = 0u;
    UInt32 blockHeightInTiles = 0u;
    UInt32 depthInTiles = 0u;
    UInt16 widthInBlocks = 1u;
    UInt16 heightInBlocks = 1u;
    UInt8 dimensions = 0u;
    UInt8 maxLevel = 0u;
    UInt32 arrayX = 1u;
    UInt32 arrayY = 1u;

    /**
     * Producers will fill a number of texture layers.
     * These texture layers can be distributed across one or more physical groups.
     * Each physical group can contain one or more of the texture layers.
     * Within a physical group the texture layers share the same UV allocation/mapping and can be referenced by a single page table lookup.
     */
    UInt8 numTextureLayers = 0u;
    TextureFormat layerFormat[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {TextureFormat::TextureFormat_None};
    ColorSpace colorSpaces[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {ColorSpace::SRGB};
    UInt8* layerFallbackColor[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {nullptr};
    UInt8 numPhysicalGroups = 0u;
    UInt8 physicalGroupIndex[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {0};
};

struct FAllocatedVTDescription
{
    UInt32 tileSize = 0u;
    UInt32 tileBorderSize = 0u;
    UInt8 dimensions = 0u;
    UInt8 numTextureLayers = 0u;

    UInt32 maxSpaceSize = 0u;
    UInt8 forceSpaceID = 0xff;
    UInt32 indirectionTextureSize = 0u;

    /** Producer for each texture layer. */
    VTProducerHandle producerHandle[VIRTUALTEXTURE_SPACE_MAXLAYERS];
    /** Local layer inside producer for each texture layer. */
    UInt8 producerLayerIndex[VIRTUALTEXTURE_SPACE_MAXLAYERS] = {0u};

    union
    {
        UInt8 packedFlags = 0u;
        struct
        {
            /**
             * Should the AllocatedVT create its own dedicated page table allocation? Can be useful to control total allocation.
             * The system only supports a limited number of unique page tables, so this must be used carefully
             */
            UInt8 bPrivateSpace : 1;

            /**
             * If the AllocatedVT has the same producer mapped to multiple layers, should those be merged into a single page table layer?
             * This can make for more efficient page tables when enabled, but certain code may make assumption that number of layers
             * specified when allocating VT exactly matches the resulting page page
             */
            UInt8 bShareDuplicateLayers : 1;
        };
    };
    inline auto get_hash() const
    {
        size_t hash_value = 0;
        cross::hash_combine(hash_value, robin_hood::hash_int(tileSize));
        cross::hash_combine(hash_value, robin_hood::hash_int(tileBorderSize));
        cross::hash_combine(hash_value, robin_hood::hash_int(dimensions));
        cross::hash_combine(hash_value, robin_hood::hash_int(numTextureLayers));
        cross::hash_combine(hash_value, robin_hood::hash_int(maxSpaceSize));
        cross::hash_combine(hash_value, robin_hood::hash_int(indirectionTextureSize));
        for (UInt32 LayerIndex = 0u; LayerIndex < numTextureLayers; ++LayerIndex)
        {
            cross::hash_combine(hash_value, robin_hood::hash_int(producerHandle[LayerIndex].packedValue));
            cross::hash_combine(hash_value, robin_hood::hash_int(producerLayerIndex[LayerIndex]));
        }
        return hash_value;
    }
    struct hash
    {
        size_t operator()(const FAllocatedVTDescription& x) const
        {
            return x.get_hash();
        }
    };
};

inline bool operator==(const FAllocatedVTDescription& lhs, const FAllocatedVTDescription& rhs)
{
    if (lhs.tileSize != rhs.tileSize || lhs.tileBorderSize != rhs.tileBorderSize || lhs.dimensions != rhs.dimensions || lhs.numTextureLayers != rhs.numTextureLayers || lhs.packedFlags != rhs.packedFlags)
    {
        return false;
    }
    for (UInt32 layerIndex = 0u; layerIndex < lhs.numTextureLayers; ++layerIndex)
    {
        if (lhs.producerHandle[layerIndex] != rhs.producerHandle[layerIndex] || lhs.producerLayerIndex[layerIndex] != rhs.producerLayerIndex[layerIndex])
        {
            return false;
        }
    }
    return true;
}
inline bool operator!=(const FAllocatedVTDescription& lhs, const FAllocatedVTDescription& rhs)
{
    return !operator==(lhs, rhs);
}

inline bool operator<(const FAllocatedVTDescription& lhs, const FAllocatedVTDescription& rhs) 
{
    if (lhs.tileSize < rhs.tileSize)
        return true;
    for (int layerIndex = 0u; layerIndex < lhs.numTextureLayers; ++layerIndex)
    {
        if (lhs.producerHandle[layerIndex].packedValue < rhs.producerHandle[layerIndex].packedValue)
        {
            return true;
        }
    }
    return false;
}

/** Describes a location to write a single layer of a VT tile */
struct VTProduceTargetLayer
{
    /** The texture to write to. */
    NGITexture* texture = nullptr;
    /** The UAV to write to. This may be nullptr if no suitable SRV can be created for the texture format.  */
    NGITextureView* textureSRV = nullptr;
    /** Location within the texture to write */
    UInt2 pPageLocation;
};


class IVirtualTexture
{
public:
    inline IVirtualTexture() {}
    virtual ~IVirtualTexture() {}

    /**
     * Makes a request for the given page data.
     * For data sources that can generate data immediately, it's reasonable for this method to do nothing, and simply return 'Available'
     * Only called from render thread
     * @param producerHandle Handle to this producer, can be used as a UID for this producer for any internal caching mechanisms
     * @param layerMask Mask of requested layers
     * @param vLevel The mipmap level of the data
     * @param vAddress Bit-interleaved x,y page indexes
     * @param Priority Priority of the request, used to drive async IO/task priority needed to generate data for request
     * @return VTRequestPageResult describing the availability of the request
     */
    virtual VTRequestPageResult RequestPageData(const VTProducerHandle& producerHandle, UInt8 layerMask, UInt8 vLevel, UInt64 vAddress, EVTRequestPagePriority priority) = 0;
    virtual bool ProducePageData(EVTProducePageFlags flags, const VTProducerHandle& producerHandle, UInt8 layerMask, UInt8 vLevel, UInt64 vAddress, UInt64 requestHandle, const VTProduceTargetLayer* targetLayers) = 0;
    virtual bool IsTopLevel(UInt32 i) = 0;
    virtual UInt32 GetMipSize(UInt64 vAddress, UInt8 vLevel) = 0;
    virtual UInt32 GetTileSize(UInt8 layerMask, UInt8 vLevel, UInt64 vAddress) = 0;

    virtual UInt32 UnLoadPageData(UInt64 vAddress, UInt8 vLevel) = 0;
};

enum class EVTPageTableFormat : UInt8
{
    PageTableFormatUInt16,
    PageTableFormatUInt32,
};

class IAllocatedVirtualTexture
{
public:
    static const UInt32 layersPerPageTableTexture = 4u;

    inline IAllocatedVirtualTexture(const FAllocatedVTDescription& inDesc, UInt32 inBlockWidthInTiles, UInt32 inBlockHeightInTiles, UInt32 inWidthInBlocks, UInt32 inHeightInBlocks, UInt32 arrayX, UInt32 arrayY)
        : description(inDesc)
        , blockWidthInTiles(inBlockWidthInTiles)
        , blockHeightInTiles(inBlockHeightInTiles)
        , widthInBlocks(inWidthInBlocks)
        , heightInBlocks(inHeightInBlocks)
        , frameDeleted(0u)
        , mNumRefs(0)
        , pageTableFormat(EVTPageTableFormat::PageTableFormatUInt32)
        , spaceID(~0u)
        , maxLevel(0u)
        , virtualAddress(~0u)
        , mVirtualPageX(~0u)
        , mVirtualPageY(~0u)
        , mVirtualArrayX(arrayX)
        , mVirtualArrayY(arrayY)
    {}

    virtual UInt32 GetNumPageTableTextures() const = 0;
    virtual NGITexture* GetPageTableTexture(UInt32 InPageTableIndex) const = 0;
    virtual NGITextureView* GetPageTableTextureSRV(UInt32 InPageTableIndex) const = 0;
    virtual UInt32 GetPhysicalTextureSize(UInt32 InlayerIndex) const = 0;
    virtual NGITexture* GetPhysicalTexture(UInt32 InlayerIndex) const = 0;
    virtual NGITextureView* GetPhysicalTextureSRV(UInt32 InlayerIndex) const = 0;
    virtual UInt8 GetPageTableLayerIndex(UInt32 InLayerIndex) const = 0;

    /** Writes 2x FUIntVector4 */
    virtual void GetPackedPageTableUniform(UInt4* OutUniform) const = 0;
    /** Writes 1x FUIntVector4 */
    virtual void GetPackedUniform(UInt4* OutUniform, UInt32 layerIndex) const = 0;

    inline const FAllocatedVTDescription& GetDescription() const
    {
        return description;
    }
    inline const VTProducerHandle& GetProducerHandle(UInt32 InlayerIndex) const
    {
        return description.producerHandle[InlayerIndex];
    }

    inline UInt32 GetVirtualTileSize() const
    {
        return description.tileSize;
    }
    inline UInt32 GetTileBorderSize() const
    {
        return description.tileBorderSize;
    }
    inline UInt32 GetPhysicalTileSize() const
    {
        return description.tileSize + description.tileBorderSize * 2u;
    }
    inline UInt32 GetNumTextureLayers() const
    {
        return description.numTextureLayers;
    }
    inline UInt8 GetDimensions() const
    {
        return description.dimensions;
    }
    inline UInt32 GetWidthInBlocks() const
    {
        return widthInBlocks;
    }
    inline UInt32 GetHeightInBlocks() const
    {
        return heightInBlocks;
    }
    inline UInt32 GetWidthInTiles() const
    {
        return blockWidthInTiles * widthInBlocks;
    }
    inline UInt32 GetHeightInTiles() const
    {
        return blockHeightInTiles * heightInBlocks;
    }
    inline UInt32 GetDepthInTiles() const
    {
        return depthInTiles;
    }
    inline UInt32 GetWidthInPixels() const
    {
        return GetWidthInTiles() * description.tileSize;
    }
    inline UInt32 GetHeightInPixels() const
    {
        return GetHeightInTiles() * description.tileSize;
    }
    inline UInt32 GetDepthInPixels() const
    {
        return depthInTiles * description.tileSize;
    }
    inline UInt32 GetSpaceID() const
    {
        return spaceID;
    }
    inline UInt32 GetVirtualAddress() const
    {
        return virtualAddress;
    }
    inline UInt32 GetVirtualPageX() const
    {
        return mVirtualPageX;
    }
    inline UInt32 GetVirtualPageY() const
    {
        return mVirtualPageY;
    }
    inline UInt32 GetMaxLevel() const
    {
        return maxLevel;
    }
    inline EVTPageTableFormat GetPageTableFormat() const
    {
        return pageTableFormat;
    }
    inline UInt32 AddRef() 
    {
        return ++mNumRefs;
    }
    inline UInt32 ReleaseRef()
    {
        return --mNumRefs;
    }
    inline UInt32 GetRefs()
    {
        return mNumRefs;
    }

    void SetLayerProperty(const std::map<UInt32, VTLayerProperty> & layerMaps) 
    {
        mLayerPropertyMaps = layerMaps;
    }

    UInt8 GetLayerIndexByName(VTProducerHandle producer) 
    {
        auto it = mLayerPropertyMaps.find(producer.packedValue);
        if (it != mLayerPropertyMaps.end())
        {
            return it->second.mLayerIndex;
        }
        AssertMsg(false, "texture not in allocatedVT layers");
        return 0;
    }

    UInt32 GetVirtualArrayX() const { return mVirtualArrayX; };
    UInt32 GetVirtualArrayY() const { return mVirtualArrayY; };

    void SetName(std::string s)  { mName = s;}

    virtual ~IAllocatedVirtualTexture() {}

    virtual bool TryMapLockedTiles(VirtualTextureSystemR* InSystem) const = 0;
    virtual void Destroy(VirtualTextureSystemR* System) const = 0;

protected:
    friend class VirtualTextureSystemR;
    FAllocatedVTDescription description;
    UInt32 blockWidthInTiles;
    UInt32 blockHeightInTiles;
    UInt32 widthInBlocks;
    UInt32 heightInBlocks;
    UInt32 depthInTiles;
    UInt32 frameDeleted;
    UInt32 mNumRefs;
    // should be set explicitly by derived class constructor
    EVTPageTableFormat pageTableFormat;
    UInt32 spaceID;
    UInt32 maxLevel;
    UInt32 virtualAddress;
    UInt32 mVirtualPageX;
    UInt32 mVirtualPageY;
    UInt32 mVirtualArrayX;
    UInt32 mVirtualArrayY;
    std::map<UInt32, VTLayerProperty> mLayerPropertyMaps;
    std::string mName;
};


struct FProducePageDataPrepareTask
{
    IVirtualTexture* virtualTexture;
    EVTProducePageFlags flags;
    VTProducerHandle producerHandle;
    UInt8 layerMask;
    UInt8 vLevel;
    UInt32 vAddress;
    UInt64 requestHandle;
    VTProduceTargetLayer produceTarget[VIRTUALTEXTURE_SPACE_MAXLAYERS];
};


}
