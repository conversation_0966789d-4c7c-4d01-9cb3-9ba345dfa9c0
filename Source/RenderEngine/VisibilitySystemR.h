#pragma once
//#include "CECommon/Common/RenderSystemBase.h"
//#include "RenderEngine/RenderWorld.h"

namespace cross {
//struct VisibilityComponentR final : ecs::IComponent
//{
//    CEFunction(Reflect) RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();
//
//protected:
//    UInt64 mCameraCulledMask{0xffffffffffffffff};   // & with camera's cullingMaskTag to get visibility on certain camera
//    // bool mVisible;
//
//    friend class VisibilitySystemR;
//};
//
//class VisibilitySystemR : public RenderSystemBase
//{
//    CEMetaInternal(Reflect)
//
//        using VisibilityComponentH = ecs::ComponentHandle<VisibilityComponentR>;
//
//public:
//    RENDER_ENGINE_API static VisibilitySystemR* CreateInstance();
//
//    virtual void Release() override;
//
//    virtual void OnBeginFrame(FrameParam* frameParam) override;
//
//    virtual void OnEndFrame(FrameParam* frameParam) override;
//
//public:
//    inline UInt64 GetCameraMask(VisibilityComponentH vc) const
//    {
//        return vc.Read()->mCameraCulledMask;
//    }
//
//    // inline bool IsVisible(VisibilityComponentH vc) const { return vc->mVisible; }
//
//    inline bool IsVisible(ecs::EntityID entity) const
//    {
//        return mRenderWorld->GetEntityMask(entity, ecs::RuntimeMaskType::VisibilityMask);
//    }
//
//    inline void SetVisible(ecs::EntityID entity, bool isVisible)
//    {
//        if (mRenderWorld->GetEntityMask(entity, ecs::RuntimeMaskType::VisibilityMask) != isVisible)
//        {
//            mRenderWorld->SetEntityMask(isVisible, entity, ecs::RuntimeMaskType::VisibilityMask);
//            if (isVisible)
//                VisibleEntities++;
//        }
//    }
//
//    void SetCameraCulledMask(ecs::EntityID entity, UInt64 cameraMask);
//
//    UInt32 VisibleEntities{0};   // TEMP // TODO(yuanwan): Get rid of this after better parallel method of masked entities
//
//protected:
//    VisibilitySystemR();
//
//    ~VisibilitySystemR();
//};

}   // namespace cross
