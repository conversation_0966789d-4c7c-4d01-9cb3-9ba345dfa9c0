#pragma once
#include "CECommon/Common/RenderSystemBase.h"
#include "CrossBase/Math/CrossMath.h"
#include "RenderEngine/RenderContext.h"

namespace cross {
class MaterialR;
class FrameAllocator;

struct VoxelizeComponentR : ecs::IComponent
{
    CEFunction(Reflect)
    RENDER_ENGINE_API static ecs::ComponentDesc* GetDesc();

    static void DeleteNGITexture(void* ptr)
    {
        //GetNGIDevice().DeleteNGIResource(reinterpret_cast<NGITexture*>(ptr));
    }


private:
    std::unique_ptr<UInt8[]> mAlbedoVoxelData{nullptr};
    std::unique_ptr<UInt8[]> mNormalVoxelData{nullptr};
    std::unique_ptr<UInt8[]> mEmissiveVoxelData{nullptr};

    std::unique_ptr<NGITexture> mVoxelizeAlbedoTex{nullptr};
    std::unique_ptr<NGITexture> mVoxelizeNormalTex{nullptr};
    std::unique_ptr<NGITexture> mVoxelizeEmissiveTex{nullptr};

    std::unique_ptr<NGITexture> mAlbedoTex;
    std::unique_ptr<NGITexture> mNormalTex;
    std::unique_ptr<NGITexture> mEmissiveTex;
    UInt32 mDataSize{0};
    GraphicsFormat mFormat{GraphicsFormat::R8G8B8A8_UNorm};

    UInt32 mWidth{64};
    UInt32 mHeight{64};
    UInt32 mDepth{64 * 6};

    UInt3 mClipmapGridResolution;
    Float3 mVoxelSize;

    friend class VoxelizeSystemR;
};

class VoxelizeSystemR : public RenderSystemBase
{
    CEMetaInternal(Reflect) 
    using VoxelizeComponentH = ecs::ComponentHandle<VoxelizeComponentR>;
    using VoxelizeComponentReader = ecs::ScopedComponentRead<VoxelizeComponentR>;

public:
    RENDER_ENGINE_API static VoxelizeSystemR* CreateInstance();

    RENDER_ENGINE_API virtual void Release() override;

public:
    RENDER_ENGINE_API void SetTextureSize(ecs::EntityID eID, UInt32 width, UInt32 height, UInt32 depth, Float3 voxelSize);
    RENDER_ENGINE_API void GetVoxelizeTexture(ecs::EntityID eID, NGITexture*& albedoTex, NGITexture*& normalTex, NGITexture*& emissiveTex);
    RENDER_ENGINE_API void SetVoxelizeTexData(ecs::EntityID eID, UInt32 dataSize, UInt8 albedoData[], UInt8 normalData[], UInt8 emissiveData[]);
    RENDER_ENGINE_API bool IsEmptyData(ecs::EntityID eID);
    RENDER_ENGINE_API void GetTextureSize(ecs::EntityID eID, UInt32& width, UInt32& height, UInt32& depth);
    RENDER_ENGINE_API bool UpdateVoxelizeTexData(ecs::EntityID eID, NGITexture*& dstAlbedoTex, NGITexture*& dstNormalTex, NGITexture*& dstEmissvieTex);
    RENDER_ENGINE_API void UpdateVoxelizeTexturePassContext(ecs::EntityID eID, RenderContext& rc);

protected:
    VoxelizeSystemR();

    ~VoxelizeSystemR();

    virtual void OnEndFrame(FrameParam* frameParam) override;
};
}   // namespace cross