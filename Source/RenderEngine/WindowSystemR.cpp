#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "RenderEngine/WindowSystemR.h"
namespace cross {

const GlobalSystemDesc& WindowSystemR::GetDesc()
{
    static const GlobalSystemDesc* sDesc{nullptr};
    if (!sDesc)
    {
        auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
        sDesc = descSystem->CreateOrGetGlobalSystemDesc("WindowSystemR", false);
    }
    return *sDesc;
}

void WindowSystemR::OnEndFrame(FrameParam* frameParam)
{
}

void WindowSystemR::DestroyWindow(IWindowR* window)
{
    if (auto itr = std::find(mWindows.begin(), mWindows.end(), window); itr != mWindows.end())
    {
        mWindows.erase(itr);
    }

    if (auto itr = std::find(mPendingWindows.begin(), mPendingWindows.end(), window); itr != mPendingWindows.end())
    {
        mPendingWindows.erase(itr);
    }
}

WindowSystemR* WindowSystemR::CreateInstance()
{
    return new WindowSystemR();
}

void WindowSystemR::OnBeginFrame(FrameParam* framePram)
{
    mWindows.insert(mWindows.end(), mPendingWindows.begin(), mPendingWindows.end());
    mPendingWindows.clear();
}

WindowSystemR::~WindowSystemR()
{
    mAppWindow = nullptr;
}

void WindowSystemR::Release()
{
    delete this;
}

}   // namespace cross
