#pragma once
#include <CEMetaMacros.h>

namespace cross {
    struct AdjustColor
    {
        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Hue", bReadOnly = false, ValueMin = "0.0", ValueMax = "360.0", ValueStep = "0.1"))
        float Hue{0.0};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Saturation", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float Saturation{1.0f};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Brightness", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float Brightness{1.0f};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "BrightnessCurve", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float BrightnessCurve{1.0f};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "Vibrance", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float Vibrance{0.0f};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "MinAlpha", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float MinAlpha{0.0f};

        CEMeta(Editor)
        CECSAttribute(PropertyInfo(PropertyType = "FloatWithTrack", ToolTips = "MaxAlpha", bReadOnly = false, ValueMin = "0.0", ValueMax = "1.0", ValueStep = "0.01"))
        float MaxAlpha{1.0f};
    };
}