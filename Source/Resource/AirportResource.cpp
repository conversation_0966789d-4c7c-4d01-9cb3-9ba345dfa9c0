#include "Resource/AirportResource.h"
#include "Resource/AssetStreaming.h"
#include "CrossBase/Serialization/SerializeTemplateBase.h"
#include "Resource/Material.h"
#include "Resource/ScriptResource.h"
#include "Resource/AsyncResource.h"

#if AIRPORT_SCENE_EDITOR
namespace cross::resource
{
    bool OptionPayload::GetResourceValue(std::string& val) const
    {
        if (auto str = std::get_if<std::string>(&this->Value); str)
        {
            val = str->c_str();
            return true;
        }
        return false;
    }

    bool OptionPayload::GetColorValue(Float4& val) const
    {
        if (auto vec = std::get_if<std::vector<float>>(&this->Value); vec)
        {
            memcpy(&val, vec->data(), vec->size() * sizeof(float));
            return true;
        }
        return false;
    }

    bool OptionPayload::GetVisibilityValue(bool& val) const
    {
        if (auto bVal = std::get_if<bool>(&this->Value); bVal)
        {
            val = *bVal;
            return true;
        }
        return false;
    }

    bool OptionPayload::GetScaleValue(Float3& val) const
    {
        if (auto vec = std::get_if<std::vector<float>>(&this->Value); vec)
        {
            memcpy(&val, vec->data(), vec->size() * sizeof(float));
            return true;
        }
        return false;
    }

    void OptionPayload::SetResourceValue(std::string val)
    {
        this->Value = val;
    }

    void OptionPayload::SetColorValue(Float4 val)
    {
        std::vector<float> vec{val.x, val.y, val.z, val.w};
        this->Value = vec;
    }

    void OptionPayload::SetVisibilityValue(bool val)
    {
        this->Value = val;
    }

    void OptionPayload::SetScaleValue(Float3 val)
    {
        std::vector<float> vec{val.x, val.y, val.z};
        this->Value = vec;
    }

    SerializeNode OptionPayload::Serialize(SerializeContext& context) const
    {
        SerializeNode node;
        this->Serialize(node, context);
        return node;
    }

    void OptionPayload::Serialize(SerializeNode& node, SerializeContext& context) const
    {
        SerializeNode t_Type;
        cross::Serialize(this->Type, t_Type, context);
        node["Type"] = std::move(t_Type);

        SerializeNode t_Key;
        cross::Serialize(this->Key, t_Key, context);
        node["Key"] = std::move(t_Key);

        if (this->Type == OptionPayloadType::Material || this->Type == OptionPayloadType::Texture || this->Type == OptionPayloadType::Script)
        {
            if (auto str = std::get_if<std::string>(&this->Value); str)
                node["Value"] = *str;
            else
                node["Value"] = "";
        }
        else if (this->Type == OptionPayloadType::Color || this->Type == OptionPayloadType::Scale)
        {
            SerializeNode array = SerializeNode::EmptyArray();
            if (auto vec = std::get_if<std::vector<float>>(&this->Value); vec)
                for (auto& f : *vec)
                    array.PushBack(f);
            node["Value"] = std::move(array);
        }
        else if (this->Type == OptionPayloadType::Visibility)
        {
            if (auto val = std::get_if<bool>(&this->Value); val)
                node["Value"] = *val;
            else
                node["Value"] = false;
        }
    }

    bool OptionPayload::Deserialize(const DeserializeNode& in, SerializeContext& context)
    {
        bool ret = true;

        auto t_Type = in.HasMember("Type");
        if (t_Type)
            ret &= cross::Deserialize(*t_Type, this->Type, context);
        else
            ret = false;

        auto t_Key = in.HasMember("Key");
        if (t_Key)
            ret &= cross::Deserialize(*t_Key, this->Key, context);
        else
            ret = false;

        auto t_Value = in.HasMember("Value");
        if (t_Value)
        {
            auto& node = *t_Value;
            if ((this->Type == OptionPayloadType::Material || this->Type == OptionPayloadType::Texture || this->Type == OptionPayloadType::Script) && node.IsString())
            {
                this->Value = node.AsString();
            }
            else if ((this->Type == OptionPayloadType::Color || this->Type == OptionPayloadType::Scale) && node.IsArray())
            {
                std::vector<float> vec;
                vec.reserve(node.Size());
                for (int i = 0; i < node.Size(); i++)
                {
                    Assert(node[i].IsNumber());
                    vec.push_back(node[i].AsFloat());
                }
                this->Value = vec;
            }
            else if (this->Type == OptionPayloadType::Visibility && node.IsBoolean())
            {
                this->Value = node.AsBoolean();
            }
            else
                ret = false;
        }
        else
            ret = false;

        return ret;
    }

    bool OptionPayload::PostDeserialize(const DeserializeNode& in, SerializeContext& context)
    {
        return true;
    }

    bool AirportResource::Serialize(const std::string& path)
    {
        if (!HasAsset())
        {
            CreateAsset(path);
        }

        ClearReference();
        SerializeContext context;
        SerializeNode node = mInfo.Serialize(context);
        gResourceMgr.AddResourceDependencies(node, SharedFromThis());

        return Resource::Serialize(std::move(node), path);
    }

    bool AirportResource::Deserialize(DeserializeNode const& s)
    {
        mInfo = {};

        SerializeContext context;
        mInfo.Deserialize(s, context);

        return true;
    }

    bool AirportResource::PostDeserialize()
    {

        return true;
    }

    void AirportResource::SaveToFile(std::string path)
    {
        auto relPath = EngineGlobal::GetFileSystem()->GetRelativePath(path);
        Serialize(relPath);
    }

    AirportResource& AirportResource::Create(std::string prefabPath)
    {
        auto ptr = gResourceMgr.CreateResource<resource::AirportResource>();
        auto res = TypeCast<resource::AirportResource>(ptr);
        auto prefabPtr = gAssetStreamingManager->LoadSynchronously(prefabPath);
        if (prefabPtr)
        {
            res->mInfo.BasePrefab = prefabPtr->GetGuid_Str();
        }
        return *res;
    }

    AirportResource& AirportResource::Get(std::string path)
    {
        auto relPath = EngineGlobal::GetFileSystem()->GetRelativePath(path);
        auto ptr = gAssetStreamingManager->LoadSynchronously(relPath);
        auto res = TypeCast<AirportResource>(ptr);
        Assert(res);
        return *res;
    }

    AirportResource& AirportResource::Get(Resource* ptr)
    {
        auto res = TypeCast<resource::AirportResource>(ResourcePtr(ptr));
        Assert(res);
        return *res;
    }
}
#endif