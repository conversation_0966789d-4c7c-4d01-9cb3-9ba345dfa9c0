#pragma once
#include "Resource/Resource.h"
#include "CEAnimation/AnimBase.h"

namespace cross::anim 
{
class Resource_API CEMeta(Cli, Script) AnimResourceBase : public Resource
{
public:
    AnimResourceBase() = default;
    virtual ~AnimResourceBase() = default;

    virtual void HoldSeqResRefCount(const ResourcePtr& inSeqResPtr);
    virtual void HoldCmpResRefCount(const ResourcePtr& inCmpResPtr);

protected:
    /* The AnimSequenceRes used in this anim resource, no repeat saved */
    CENameMap<CEName, ResourcePtr> mAnimSeqResPtrMap;
    /* The AnimCompositeRes used in this anim resource, no repeat saved */
    CENameMap<CEName, ResourcePtr> mAnimCmpResPtrMap;
};
}   // namespace cross::anim
