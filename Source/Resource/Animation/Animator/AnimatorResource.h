#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "Resource/ResourceManager.h"
#include "CrossBase/String/UniqueString.h"

namespace cross::anim {

class Parameter;

class Resource_API CEMeta(Cli, Script) AnimatorRes : public Resource
{
public:
    friend class cross::ResourceManager;
    FRIEND_WITH_REFLECTION_MODULE;

protected:
    AnimatorRes() = default;

public:
    ~AnimatorRes();
    static int GetClassIDStatic() { return ClassID(AnimatorRes); }

    virtual bool Serialize(SerializeNode&& s, const std::string& path) override;
    virtual bool Deserialize(DeserializeNode const& s) override;

    bool SetStoryBoardContent(const std::string& file_content);
    bool SetResourceRef(const std::string& resource_ref);

    SerializeNode const& GetStoryBoardContent() const { return mStoryBoardContent; }
    std::vector<std::shared_ptr<Parameter>> const& GetParameters() const { return mParameters; }
    const std::vector<UniqueString>& GetIntendedCurveNames() const
    {
        return mIntendedCurves;
    }

    CEMeta(Cli)
    std::string GetContentAsJson() const;

protected:
    SerializeNode mStoryBoardContent;
    SerializeNode mResourceRef;

    std::vector<std::shared_ptr<Parameter>> mParameters;
    std::vector<UniqueString> mIntendedCurves;
};

}   // namespace cross::anim
