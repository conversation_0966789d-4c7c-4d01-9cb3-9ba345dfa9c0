#include "EnginePrefix.h"
#include "Resource/Animation/Curve/CurveControllerRes.h"
#include <CrossBase/Serialization/SerializeTemplateBase.h>

namespace cross {

static UniqueString PropertyToTypeString(const UniqueString inPropertyName) 
{
    const static std::unordered_map<std::string, std::string> sPropertyToTypeMap = {{"Rotation", "RotEuler"}, {"Translation", "Float3"}, {"Scale", "Float3"}, {"fov", "Float1"}, {"Transform", "Transform"}};

    UniqueString TypeString = "Float1";
    if (auto itr = sPropertyToTypeMap.find(std::string{inPropertyName.GetCString()}); itr != sPropertyToTypeMap.end())
    {
        TypeString = UniqueString{itr->second.c_str()};
    }
    return TypeString;
}

#if CROSSENGINE_EDITOR

bool CurveControllerRes::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!HasAsset())
    {
        CreateAsset(PathHelper::GetRelativePath(path));
    }

    std::string realPath = path;
    if (ResourceManager::Instance().HasGuid(path))
    {
        realPath = ResourceManager::Instance().ConvertGuidToPath(realPath);
    }

    Resource::ClearReference();
    //clear unused dataItems
    for (size_t i = 0; i < this->mCurveData.size(); i++) 
    {
        if (this->mCurveData[i].mDataItems.size() == 0) 
        {
            this->mCurveData.erase(this->mCurveData.begin() + i);
        }
    }

    SerializeContext context;
    SerializeNode t_mCurveData;
    cross::Serialize(this->mCurveData, t_mCurveData, context);

    s["CurveData"] = std::move(t_mCurveData);
    //s["CurveData"] = std::move(mSingleCurveData.Serialize(context));

    return Resource::Serialize(std::move(s), realPath);
}
#endif

bool CurveControllerRes::Deserialize(DeserializeNode const& s)
{
    if (!s.HasMember("CurveData"))
    {
        return false;
    }
    bool ret = true;
    SerializeContext context;
    auto t_curveData = s.HasMember("CurveData");
    if (t_curveData) 
    {
        ret &= cross::Deserialize(*t_curveData, this->mCurveData, context);
    }
    else
    {
        ret = false;
    }

    //return mSingleCurveData.Deserialize(*t_curveData, context);
    return ret;
}

void CurveControllerDataItem::AddiSerialize(SerializeNode& inNode, SerializeContext& context) const
{
    SerializeNode curveNode;
    cross::Serialize(CurveData, curveNode, context);
    inNode["CurveData"] = std::move(curveNode);

    SerializeNode AnimTrackNode;
    cross::Serialize(AnimTrack, AnimTrackNode, context);
    inNode["AnimTrack"] = std::move(AnimTrackNode);

    SerializeNode SubSeqTrackNode;
    cross::Serialize(SubSeqTracks, SubSeqTrackNode, context);
    inNode["SubSeqTracks"] = std::move(SubSeqTrackNode);
}

void CurveControllerDataItem::AddiDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    const auto& curveNode = inNode.HasMember("CurveData");
    if (curveNode && !curveNode->IsNull())
    {
        if (SystemName == "")
        {
            if (Type == ComponentProperty)
            {
                auto cmp_name = ComponentName.GetStringView();
                if (cmp_name.find("Transform") != std::string::npos)
                {
                    SystemName = "cross::TransformSystemG";
                }
                else if (cmp_name.find("Camera") != std::string::npos)
                {
                    SystemName = "cross::CameraSystemG";
                }
                else
                {
                    SystemName = "UnknownSystem";
                }
            }
        }
        
        if (CurveType == "")
        {
            CurveType = Type == ModelMaterial ? PropertyToTypeString(MatParamName) : PropertyToTypeString(PropertyName);
        }

        GenCurveDataObject();

        CurveData->Deserialize(*curveNode, context);
    }

    const auto& AnimTrackNode = inNode.HasMember("AnimTrack");
    if (AnimTrackNode && !AnimTrackNode->IsNull())
    {
        cross::Deserialize(*AnimTrackNode, AnimTrack, context);
    }

    const auto& SubSeqTrackNode = inNode.HasMember("SubSeqTracks");
    if (SubSeqTrackNode && !SubSeqTrackNode->IsNull()) 
    {
        cross::Deserialize(*SubSeqTrackNode, SubSeqTracks, context);
    }

    if (Type == AnimationTrack)
    {
        if (AnimTrack == nullptr)
        {
            AnimTrack = std::make_shared<LevelSeqAnimationTrack>();
        }
    }
    else
    {
        if (CurveData == nullptr)
        {
            CurveData = std::make_shared<Float1Curve>();
        }
    }
}

bool CurveControllerData::CreateCurveDataItem(const UniqueString& ComponentName, const UniqueString& PropertyName, const UniqueString& SystemName, const UniqueString& CurveType)
{
    if (FindCurveDataIndex(ComponentName, PropertyName) != -1)
    {
        return false;
    }

    CurveControllerDataItem newItem;
    newItem.Type = CurveControllerDataItem::ComponentProperty;
    newItem.ComponentName = ComponentName;
    newItem.SystemName = SystemName;
    newItem.PropertyName = PropertyName;
    newItem.CurveType = CurveType;
    newItem.GenCurveDataObject();

    mDataItems.push_back(std::move(newItem));
    return true;
}

bool CurveControllerData::RemoveCurveDataItem(const UniqueString& inCompName, const UniqueString& inPropertyName)
{
    if (SInt32 index = FindCurveDataIndex(inCompName, inPropertyName); index == -1)
    {
        return false;
    }
    else
    {
        mDataItems.erase(mDataItems.begin() + index);
        return true;
    }
}

bool CurveControllerData::GetCurveControllerTrack(const UniqueString& inCompName, const UniqueString& inPropertyName, const UniqueString& inTrackName, FloatCurveTrack& outTrack) const
{
    if (SInt32 index = FindCurveDataIndex(inCompName, inPropertyName); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        if (auto indexTrack = curveDataPtr->FindTrackIndex(inTrackName); indexTrack >= 0)
        {
            outTrack = curveDataPtr->FindTrackByIndex(indexTrack);
            return true;
        }
        return false;
    }
}

bool CurveControllerData::GetCurveControllerList(const UniqueString& inCompName, const UniqueString& inPropertyName, FloatCurveList& outList) const 
{
    if (SInt32 index = FindCurveDataIndex(inCompName, inPropertyName); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        outList = *curveDataPtr;
        return true;
    }
}

bool CurveControllerData::SetCurveControllerTrack(const UniqueString& inCompName, const UniqueString& inPropertyName, const UniqueString& inTrackName, const FloatCurveTrack& inTrack) 
{
    if (SInt32 index = FindCurveDataIndex(inCompName, inPropertyName); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);
        Assert(inTrack.Name == inTrackName);

        FloatCurveListModifier modifier(*curveDataPtr);
        return modifier.SetTrack(inTrack);
    }
}

bool CurveControllerData::SetCurveControllerInfo(const UniqueString& inCompName, const UniqueString& inPropertyName, const FloatCurveListInfo& inInfoData) 
{
    if (SInt32 index = FindCurveDataIndex(inCompName, inPropertyName); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        FloatCurveListModifier modifier(*curveDataPtr);
        modifier.ApplyFloatCurveListInfo(inInfoData);
        return true;
    }
}

void* CurveControllerData::CreateCurveDataItemAsAnimationTrack(const std::string& trackName, SInt32 trackIndex)
{
    auto it = std::find_if(mDataItems.begin(), mDataItems.end(), [trackIndex](const CurveControllerDataItem& dataItem) {
        return dataItem.Type == CurveControllerDataItem::AnimationTrack && dataItem.TrackIndex == trackIndex;
    });
    if (it == mDataItems.end())
    {
        auto & newItem = mDataItems.emplace_back();
        newItem.Type = CurveControllerDataItem::AnimationTrack;
        newItem.TrackIndex = trackIndex;
        newItem.SystemName = UniqueString("cross::AnimatorSystemG");
        newItem.AnimTrack = std::make_unique<LevelSeqAnimationTrack>();
        newItem.AnimTrack->TrackName = trackName;
        return newItem.AnimTrack.get();
    }
    return nullptr;
}
bool CurveControllerData::RemoveAnimationTrack(SInt32 trackIndex) {
    auto it = std::find_if(mDataItems.begin(), mDataItems.end(), [trackIndex](const CurveControllerDataItem& dataItem) {
        return dataItem.Type == CurveControllerDataItem::AnimationTrack && dataItem.TrackIndex == trackIndex;
    });
    if (it != mDataItems.end())
    {
        mDataItems.erase(it);
        return true;
    }
    return false;
}

void* CurveControllerData::CreateCurveDataItemAsSubSeqTrack(const std::string& trackName, SInt32 trackIndex)
{
    auto it = std::find_if(mDataItems.begin(), mDataItems.end(), [trackIndex](const CurveControllerDataItem& dataItem) 
        { return dataItem.Type == CurveControllerDataItem::SubSequncerTrack && dataItem.TrackIndex == trackIndex; });
    if (it == mDataItems.end())
    {
        auto& newItem = mDataItems.emplace_back();
        newItem.Type = CurveControllerDataItem::SubSequncerTrack;
        newItem.TrackIndex = trackIndex;
        auto levelSubSeqTrack = std::make_shared<LevelSubSeqTrack>();
        levelSubSeqTrack->TrackName = trackName;
        newItem.SubSeqTracks.push_back(levelSubSeqTrack);
        return newItem.SubSeqTracks[0].get();
    }
    return it->SubSeqTracks[trackIndex].get();
}
bool CurveControllerData::RemoveSubSeqTrack(SInt32 trackIndex)
{
    auto it = std::find_if(mDataItems.begin(), mDataItems.end(), [trackIndex](const CurveControllerDataItem& dataItem) 
        { return dataItem.Type == CurveControllerDataItem::SubSequncerTrack && dataItem.TrackIndex == trackIndex; });
    if (it != mDataItems.end())
    {
        mDataItems.erase(it);
        return true;
    }
    return false;
}

SInt32 CurveControllerData::FindCurveDataIndex(const UniqueString& inCompName, const UniqueString& inPropertyName) const
{
    for (size_t i = 0; i < mDataItems.size(); i++)
    {
        if (mDataItems[i].Type == CurveControllerDataItem::ComponentProperty && 
            mDataItems[i].ComponentName == inCompName && 
            mDataItems[i].PropertyName == inPropertyName)
        {
            return static_cast<SInt32>(i);
        }
    }
    return -1;
}

bool CurveControllerData::CreateCurveDataItem(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, const UniqueString& CurveType, bool IsParticle)
{
    if (FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle) != -1)
    {
        return false;
    }

    CurveControllerDataItem newItem;
    if (!IsParticle)
    {
        newItem.Type = CurveControllerDataItem::ModelMaterial;
        newItem.SystemName = UniqueString("cross:ModelSystemG");
        newItem.ModelIndex = ModelIndex;
        newItem.SubModelIndex = SubModelIndex;
        newItem.MatParamName = ParamName;
        newItem.CurveType = CurveType;
        newItem.GenCurveDataObject();
    }
    else
    {
        newItem.Type = CurveControllerDataItem::ParticleMaterial;
        newItem.SystemName = UniqueString("cross:ParticleSimulationSystemG");
        newItem.EmitterIndex = ModelIndex;
        newItem.MaterialIndex = SubModelIndex;
        newItem.MatParamName = ParamName;
        newItem.CurveType = CurveType;
        newItem.GenCurveDataObject();
    }

    mDataItems.push_back(std::move(newItem));
    return true;
}

bool CurveControllerData::RemoveCurveDataItem(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, bool IsParticle)
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        mDataItems.erase(mDataItems.begin() + index);
        return true;
    }
}

bool CurveControllerData::GetCurveControllerTrack(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, const UniqueString& inTrackName, FloatCurveTrack& outTrack, bool IsParticle) const
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        if (auto indexTrack = curveDataPtr->FindTrackIndex(inTrackName); indexTrack >= 0)
        {
            outTrack = curveDataPtr->FindTrackByIndex(indexTrack);
            return true;
        }
        return false;
    }
}

bool CurveControllerData::GetCurveControllerList(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, FloatCurveList& outList, bool IsParticle) const
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        outList = *curveDataPtr;
        return true;
    }
}

bool CurveControllerData::SetCurveControllerTrack(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, const UniqueString& inTrackName, const FloatCurveTrack& inTrack, bool IsParticle)
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);
        Assert(inTrack.Name == inTrackName);

        FloatCurveListModifier modifier(*curveDataPtr);
        return modifier.SetTrack(inTrack);
    }
}

bool CurveControllerData::SetCurveControllerInfo(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, const FloatCurveListInfo& inInfoData, bool IsParticle)
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        FloatCurveListModifier modifier(*curveDataPtr);
        modifier.ApplyFloatCurveListInfo(inInfoData);
        return true;
    }
}

SInt32 CurveControllerData::FindCurveDataIndex(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, bool IsParticle) const
{
    for (size_t i = 0; i < mDataItems.size(); i++)
    {
        if (!IsParticle)
        {
            if (mDataItems[i].Type == CurveControllerDataItem::ModelMaterial &&
                mDataItems[i].ModelIndex == ModelIndex &&
                mDataItems[i].SubModelIndex == SubModelIndex &&
                mDataItems[i].MatParamName == ParamName)
            {
                return static_cast<SInt32>(i);
            }
        }
        else
        {
            if (mDataItems[i].Type == CurveControllerDataItem::ParticleMaterial &&
                mDataItems[i].EmitterIndex == ModelIndex &&
                mDataItems[i].MaterialIndex == SubModelIndex &&
                mDataItems[i].MatParamName == ParamName)
            {
                return static_cast<SInt32>(i);
            }
        }
    }
    return -1;
}

bool CurveControllerData::GetMotherMaterial(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, bool& motherMaterial, bool IsParticle) const
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        const CurveControllerDataItem& item = mDataItems[index];
        motherMaterial = item.MotherMaterial;
        return true;
    }
}

bool CurveControllerData::SetMotherMaterial(SInt32 ModelIndex, SInt32 SubModelIndex, const UniqueString& ParamName, bool motherMaterial, bool IsParticle)
{
    if (SInt32 index = FindCurveDataIndex(ModelIndex, SubModelIndex, ParamName, IsParticle); index == -1)
    {
        return false;
    }
    else
    {
        CurveControllerDataItem& item = mDataItems[index];
        item.MotherMaterial = motherMaterial;
        return true;
    }
}

bool CurveControllerData::CreateCurveDataItem(const UniqueString& ValueName, const UniqueString& CurveType)
{
    if (FindCurveDataIndex(ValueName) != -1)
    {
        return false;
    }

    CurveControllerDataItem newItem;
    newItem.Type = CurveControllerDataItem::ScriptValue;
    newItem.ScriptValueName = ValueName;
    newItem.CurveType = CurveType;
    newItem.GenCurveDataObject();

    mDataItems.push_back(std::move(newItem));
    return true;
}

bool CurveControllerData::RemoveCurveDataItem(const UniqueString& ValueName)
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        mDataItems.erase(mDataItems.begin() + index);
        return true;
    }
}

bool CurveControllerData::GetCurveControllerTrack(const UniqueString& ValueName, const UniqueString& inTrackName, FloatCurveTrack& outTrack) const
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        if (auto indexTrack = curveDataPtr->FindTrackIndex(inTrackName); indexTrack >= 0)
        {
            outTrack = curveDataPtr->FindTrackByIndex(indexTrack);
            return true;
        }
        return false;
    }
}

bool CurveControllerData::GetCurveControllerList(const UniqueString& ValueName, FloatCurveList& outList) const
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        outList = *curveDataPtr;
        return true;
    }
}

bool CurveControllerData::SetCurveControllerTrack(const UniqueString& ValueName, const UniqueString& inTrackName, const FloatCurveTrack& inTrack)
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);
        Assert(inTrack.Name == inTrackName);

        FloatCurveListModifier modifier(*curveDataPtr);
        return modifier.SetTrack(inTrack);
    }
}

bool CurveControllerData::SetCurveControllerInfo(const UniqueString& ValueName, const FloatCurveListInfo& inInfoData)
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        FloatCurveListModifier modifier(*curveDataPtr);
        modifier.ApplyFloatCurveListInfo(inInfoData);
        return true;
    }
}

bool CurveControllerData::AddCurveControllerKey(const UniqueString& ValueName, const UniqueString& trackName, FloatCurveKey& key)
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        if (!curveDataPtr->HasTrack(trackName))
        {
            return false;
        }

        FloatCurveTrack& track = curveDataPtr->FindTrackByName(trackName);
        track.Keys.push_back(key);
        auto targetKey = std::find_if(track.Keys.begin(), track.Keys.end(), [&key](const FloatCurveKey& thisKey) { return thisKey.Time == key.Time; });
        if (targetKey == track.Keys.end())
        {
            auto position = std::upper_bound(track.Keys.begin(), track.Keys.end(), key, [](const FloatCurveKey& key1, const FloatCurveKey& key2) { return key1.Time < key2.Time; });
            track.Keys.insert(position, key);
        }
        else
        {
            *targetKey = key;
        }
        return true;
    }
}

void CurveControllerData::SetCurveControllerKeyTime(float inTime) 
{
    for (auto& item : mDataItems) 
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = item.GetCurveData();
        for (auto& data : curveDataPtr->mTracks) 
        {
            for (auto key : data.Keys) 
            {
                key.Time = key.Time + inTime;
            }
        }
    }
}

bool CurveControllerData::RemoveCurveControllerKey(const UniqueString& ValueName, const UniqueString& trackName, FloatCurveKey& key)
{
    SInt32 index = FindCurveDataIndex(ValueName);
    if (index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        if (!curveDataPtr->HasTrack(trackName))
        {
            return false;
        }

        FloatCurveTrack& track = curveDataPtr->FindTrackByName(trackName);
        track.Keys.push_back(key);
        auto targetKey = std::find_if(track.Keys.begin(), track.Keys.end(), [&key](const FloatCurveKey& thisKey) { return thisKey.Time == key.Time; });
        if (targetKey == track.Keys.end())
        {
            return false;
        }
        track.Keys.erase(targetKey);
        return true;
    }
}

SInt32 CurveControllerData::FindCurveDataIndex(const UniqueString& ValueName) const
{
    for (size_t i = 0; i < mDataItems.size(); i++)
    {
        if (mDataItems[i].Type == CurveControllerDataItem::ScriptValue &&
            mDataItems[i].ScriptValueName == ValueName)
        {
            return static_cast<SInt32>(i);
        }
    }
    return -1;
}

bool CurveControllerData::CreateCurveDataItem(const UniqueString& eventTrackName, SInt32 trackIndex, const UniqueString& CurveType)
{
    if (FindCurveDataIndex(eventTrackName, trackIndex) != -1) 
    {
        return false;
    }

    CurveControllerDataItem newItem;
    newItem.Type = CurveControllerDataItem::EventValue;
    newItem.SystemName = UniqueString("cross:ScriptSystemG");
    newItem.EventTrackName = eventTrackName;
    newItem.TrackIndex = trackIndex;
    newItem.CurveType = CurveType;

    newItem.GenCurveDataObject();

    mDataItems.push_back(std::move(newItem));
    return true;
}


bool CurveControllerData::RemoveCurveDataItem(const UniqueString& eventTrackName, SInt32 trackIndex)
{
    if (SInt32 index = FindCurveDataIndex(eventTrackName, trackIndex); index == -1) 
    {
        return false;
    }
    else
    {
        mDataItems.erase(mDataItems.begin() + index);
        return true;
    }
}
bool CurveControllerData::GetCurveControllerTrack(const UniqueString& eventTrackName, SInt32 trackIndex, const UniqueString& inTrackName, FloatCurveTrack& outTrack) const
{
    if (SInt32 index = FindCurveDataIndex(eventTrackName, trackIndex); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        if (auto indexTrack = curveDataPtr->FindTrackIndex(inTrackName); indexTrack >= 0)
        {
            outTrack = curveDataPtr->FindTrackByIndex(indexTrack);
            return true;
        }
        return false;
    }
}

bool CurveControllerData::GetCurveControllerList(const UniqueString& eventTrackName, SInt32 trackIndex, FloatCurveList& outList) const
{
    if (SInt32 index = FindCurveDataIndex(eventTrackName, trackIndex); index == -1)
    {
        return false;
    }
    else
    {
        const std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);

        outList = *curveDataPtr;
        return true;
    }
}
bool CurveControllerData::SetCurveControllerTrack(const UniqueString& eventTrackName, SInt32 trackIndex, const UniqueString& inTrackName, const FloatCurveTrack& inTrack)
{
    if (SInt32 index = FindCurveDataIndex(eventTrackName, trackIndex); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        Assert(curveDataPtr);
        Assert(inTrack.Name == inTrackName);

        FloatCurveListModifier modifier(*curveDataPtr);
        return modifier.SetTrack(inTrack);
    }
}
bool CurveControllerData::SetCurveControllerInfo(const UniqueString& eventTrackName, SInt32 trackIndex, const FloatCurveListInfo& inInfoData)
{
    if (SInt32 index = FindCurveDataIndex(eventTrackName, trackIndex); index == -1)
    {
        return false;
    }
    else
    {
        std::shared_ptr<FloatCurveList> curveDataPtr = mDataItems[index].GetCurveData();
        FloatCurveListModifier modifier(*curveDataPtr);
        modifier.ApplyFloatCurveListInfo(inInfoData);
        return true;
    }
}

SInt32 CurveControllerData::FindCurveDataIndex(const UniqueString& eventTrackName, SInt32 trackIndex) const
{
    for (size_t i = 0; i < mDataItems.size(); i++)
    {
        if (mDataItems[i].Type == CurveControllerDataItem::EventValue && 
            mDataItems[i].EventTrackName == eventTrackName && mDataItems[i].TrackIndex == trackIndex) 
        {
            return static_cast<SInt32>(i);
        }
    }
    return -1;
}

void CurveControllerDataItem::GenCurveDataObject()
{
    //UniqueString TypeString = Type == ModelMaterial ? PropertyToTypeString(MatParamName) : PropertyToTypeString(PropertyName);
    std::string_view TypeString = CurveType.GetStringView();
    if (TypeString == "Float1")
    {
        CurveData = std::make_shared<Float1Curve>();
    }
    else if (TypeString == "Float2")
    {
        CurveData = std::make_shared<Float2Curve>();
    }
    else if (TypeString == "Float3")
    {
        CurveData = std::make_shared<Float3Curve>();
    }
    else if (TypeString == "Double3")
    {
        CurveData = std::make_shared<Double3Curve>();
    }
    else if (TypeString == "Float4")
    {
        CurveData = std::make_shared<Float4Curve>();
    }
    else if (TypeString == "RotEuler")
    {
        CurveData = std::make_shared<RotEulerCurve>();
    }
    else if (TypeString == "Transform")
    {
        CurveData = std::make_shared<TransformCurve>();
    }
    else
    {
        CurveData = std::make_shared<Float1Curve>();
    }
}

}   // namespace cross
