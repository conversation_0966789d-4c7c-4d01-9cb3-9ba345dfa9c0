#include "EnginePrefix.h"
#include "Resource/resourceasset.h"
#include "Resource/Animation/Skeleton/SkeletonResource.h"

namespace cross::skeleton {

SkeletonResource::SkeletonResource()
    : Resource()
{}

SkeletonResource::~SkeletonResource() {}

bool SkeletonResource::Deserialize(FBSerializer const& s)
{
    auto fbskeleton = CrossSchema::GetResourceAsset(s.GetArchive().Data() + s.InitialOffset())->resource_as_ImportRunSkeleton();
    if (fbskeleton->fversion() != 0)
        return false;

    if (fbskeleton->fref_skelt() == nullptr)
        return false;

    auto fbRefSkelt = fbskeleton->fref_skelt();
    if (!mStreamingSkelt.mReferenceSkelt.Deserialize(fbRefSkelt))
        return false;

    // Generate runtime skeleton bone tree
    std::vector<BoneTranslateRetargetingMode>& retargetModes = mStreamingSkelt.mRetargetModes;
    retargetModes.clear();

    const auto refBones = fbRefSkelt->skelteon();
    for (int i = 0, refSize = static_cast<int>(refBones->size()); i < refSize; i++)
    {
        BoneTranslateRetargetingMode retargetMode = static_cast<BoneTranslateRetargetingMode>(static_cast<int>((*refBones)[i]->retarget()));
        retargetModes.push_back(retargetMode);
    }

    // Generate slot groups which single group contain multi slots name
    std::vector<anim::AnimSlotGroup>& slotGroups = mStreamingSkelt.mSlotGroups;
    slotGroups.clear();

    const auto inSlotGroups = fbskeleton->fslots();
    for (int i = 0, slotGroupSize = inSlotGroups->size(); i < slotGroupSize; ++i)
    {
        auto& curGroup = slotGroups.emplace_back();

        curGroup.GroupName = (*inSlotGroups)[i]->name() != nullptr ? CEName{flatbuffers::GetCstring((*inSlotGroups)[i]->name())} : anim::AnimSlotGroup::sDefaultGroupName;

        auto slotsName = (*inSlotGroups)[i]->slot_names();
        for (int slotIndex = 0, slotsSize = slotsName->size(); slotIndex < slotsSize; slotIndex++)
        {
            auto slotName = CEName{flatbuffers::GetCstring((*slotsName)[slotIndex])};
            curGroup.SlotNames.push_back(slotName);
        }
    }

    // Generate twin bone array
    auto& twinBones = mStreamingSkelt.mTwinBones;
    twinBones.clear();

    const auto inTwinBones = fbskeleton->ftwinbones();
    if (inTwinBones != nullptr)
    {
        for (int i = 0, twinBoneSize = inTwinBones->size(); i < twinBoneSize; ++i)
        {
            auto inTwinBone_First = CEName{flatbuffers::GetCstring((*inTwinBones)[i]->bonename())};
            SkBoneHandle firstH = mStreamingSkelt.mReferenceSkelt.FindRawBoneIndex(inTwinBone_First);
            if (firstH == SkBoneHandle::InvalidHandle())
                continue;

            auto inTwinBone_Second = CEName{flatbuffers::GetCstring((*inTwinBones)[i]->twinname())};
            SkBoneHandle secondH = mStreamingSkelt.mReferenceSkelt.FindRawBoneIndex(inTwinBone_Second);
            if (secondH == SkBoneHandle::InvalidHandle())
                continue;

            auto& curTwinBone = twinBones.emplace_back();
            curTwinBone.First = firstH;
            curTwinBone.Second = secondH;
        }
    }

    auto MirrorTwinBoneExist = [&](SkBoneHandle boneIndex, SkBoneHandle mirrorBoneIndex) -> bool 
    {
        auto itrFind = std::find_if(mStreamingSkelt.mTwinBones.begin(), mStreamingSkelt.mTwinBones.end(), [=](auto& elem) 
        {
            if (elem.First == boneIndex || elem.Second == boneIndex || elem.First == mirrorBoneIndex || elem.Second == mirrorBoneIndex)
            {
                return true;
            }

            return false;
        });

        return (itrFind != mStreamingSkelt.mTwinBones.end());
    };

    // Attempt to use default strategy to find mirror bone
    // Strategy: boneName contains str which is "_l" or "_r"
    const SkBoneHandle boneNum = {static_cast<UInt32>(mStreamingSkelt.mReferenceSkelt.GetRawBoneNum())};
    static std::vector<std::tuple<std::string_view, std::string_view>> mirrorPartterns = {{"_l", "_r"}, {" l ", " r "}};

    for (SkBoneHandle boneIdx = {0U}; boneIdx < boneNum; ++boneIdx)
    {
        const auto& curBoneName = mStreamingSkelt.mReferenceSkelt.GetRawBoneName(boneIdx);
        std::string mirrorBoneName{curBoneName.GetCString()};
        bool hasMirroBone = false;
        for (auto& [p1, p2] : mirrorPartterns)
        {
            size_t idx = std::string::npos;
            if (idx = mirrorBoneName.find(p1); idx != std::string::npos)
            {
                mirrorBoneName.replace(idx, p1.size(), p2);
                hasMirroBone = true;
                break;
            }
            else if (idx = mirrorBoneName.find(p2); idx != std::string::npos)
            {
                mirrorBoneName.replace(idx, p2.size(), p1);
                hasMirroBone = true;
                break;
            }
        }

        if (!hasMirroBone)
            continue;

        SkBoneHandle mirrorBoneH = mStreamingSkelt.mReferenceSkelt.FindRawBoneIndex(UniqueString{mirrorBoneName.c_str()});
        if (mirrorBoneH == SkBoneHandle::InvalidHandle())
            continue;

        if (MirrorTwinBoneExist(boneIdx, mirrorBoneH))
            continue;

        mStreamingSkelt.mTwinBones.emplace_back(boneIdx, mirrorBoneH);
    }

    return true;
}

bool SkeletonResource::Serialize(const CrossSchema::ImportRunSkeletonT& inRunSkeletonAsset)
{
    Assert(0);
    return false;
}

bool SkeletonResource::Serialize(const std::string& inPath)
{
    CrossSchema::ImportRunSkeletonT csRunSkelt;

    // convert SkeletonResource's refskeleton into flat buffer style struct
    std::unique_ptr<CrossSchema::ImportRefSkeletonT> csRefSkelt(new CrossSchema::ImportRefSkeletonT);

    auto const& runRefSkelt = GetStreamingSkeleton().GetReferenceSkeleton();
    runRefSkelt.Serialize(csRefSkelt.get());

    for (int i = 0, size = static_cast<int>(runRefSkelt.GetRawBoneNum()); i < size; ++i)
    {
        auto& csBoneNode = csRefSkelt->skelteon[i];
        csBoneNode->retarget = (CrossSchema::ImportBoneTransRetgtMode)GetStreamingSkeleton().mRetargetModes[i];
    }

    csRefSkelt->name = GetName();
    csRunSkelt.fref_skelt = std::move(csRefSkelt);

    // convert slots into flat buffer style struct
    for (const auto& slotGroup : GetStreamingSkeleton().mSlotGroups)
    {
        std::unique_ptr<CrossSchema::ImportSlotGroupT> csSlotGroup(new CrossSchema::ImportSlotGroupT);
        csSlotGroup->name = slotGroup.GroupName.GetCString();

        for (const auto& slot : slotGroup.SlotNames)
            csSlotGroup->slot_names.push_back(slot.GetCString());

        csRunSkelt.fslots.push_back(std::move(csSlotGroup));
    }

    // convert twin bones into flat buffer style struct
    for (const auto& twinBone : GetStreamingSkeleton().mTwinBones)
    {
        std::unique_ptr<CrossSchema::ImportTwinBoneT> csTwinBone(new CrossSchema::ImportTwinBoneT);

        csTwinBone->bonename = runRefSkelt.GetRawBoneName(twinBone.First).GetCString();
        csTwinBone->twinname = runRefSkelt.GetRawBoneName(twinBone.Second).GetCString();
    }

    // serialize into binary happened in the last
    CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, static_cast<int32_t>(GetClassID()), 0, 0);

    flatbuffers::FlatBufferBuilder builder(10240);
    auto loc = CrossSchema::CreateImportRunSkeleton(builder, &csRunSkelt);
    auto name = PathHelper::GetBaseFileName(inPath);
    auto loc2 = CrossSchema::CreateResourceAsset(builder, &header, builder.CreateString(name), CrossSchema::ResourceType::ImportRunSkeleton, loc.Union());
    builder.Finish(loc2);

    char const* fbBufBegin = reinterpret_cast<char const*>(builder.GetBufferPointer());
    char const* fbBufEnd = fbBufBegin + builder.GetSize();
    std::vector<char> fbBufVec(fbBufBegin, fbBufEnd);

    return Resource::Serialize(fbBufVec, inPath);
}

void SkeletonResource::SetBoneRetargetingMode(SkBoneHandle boneIndex, BoneTranslateRetargetingMode retargetingMode)
{
    Assert(IsValidBoneIndex(boneIndex));
    mStreamingSkelt.mRetargetModes[boneIndex] = retargetingMode;
}

BoneTranslateRetargetingMode SkeletonResource::GetBoneRetargetingMode(SkBoneHandle boneIndex) const
{
    Assert(IsValidBoneIndex(boneIndex));
    return mStreamingSkelt.mRetargetModes[boneIndex];
};

const std::vector<StreamingTwinBone>& SkeletonResource::GetMirrorTwinBones() const
{
    return mStreamingSkelt.mTwinBones;
};

CEName SkeletonResource::GetMirrorBoneName(SkBoneHandle boneIndex) const
{
    Assert(IsValidBoneIndex(boneIndex));

    auto itrFind = std::find_if(mStreamingSkelt.mTwinBones.begin(), mStreamingSkelt.mTwinBones.end(), [=](auto& elem) 
    {
        if (elem.First == boneIndex || elem.Second == boneIndex)
        {
            return true;
        }

        return false;
    });

    if (itrFind != mStreamingSkelt.mTwinBones.end())
    {
        auto mirrorBone = (itrFind->First == boneIndex) ? itrFind->Second : itrFind->First;
        return mStreamingSkelt.GetReferenceSkeleton().GetRawBoneName(mirrorBone);
    }

    return "";
}

void SkeletonResource::SetMirrorBoneName(SkBoneHandle boneIndex, const CEName& mirrorBoneName) 
{
    Assert(IsValidBoneIndex(boneIndex));
    SetMirrorBoneInfo(boneIndex, mStreamingSkelt.mReferenceSkelt.FindRawBoneIndex(mirrorBoneName));
}

void SkeletonResource::SetMirrorBoneInfo(SkBoneHandle boneIndex, SkBoneHandle mirrorBoneIdx)
{
    if (!IsValidBoneIndex(boneIndex) || !IsValidBoneIndex(mirrorBoneIdx))
        return;

    if (boneIndex == mirrorBoneIdx)
        return;

    auto itrFind = std::find_if(mStreamingSkelt.mTwinBones.begin(), mStreamingSkelt.mTwinBones.end(), [=](auto& elem) 
    {
        if (elem.First == boneIndex || elem.Second == boneIndex || elem.First == mirrorBoneIdx || elem.Second == mirrorBoneIdx)
        {
            return true;
        }

        return false;
    });

    // if mirror bone info exist, update it
    if (itrFind != mStreamingSkelt.mTwinBones.end())
    {
        itrFind->First = boneIndex;
        itrFind->Second = mirrorBoneIdx;
        return;
    }

    // add new mirror bone record
    mStreamingSkelt.mTwinBones.emplace_back(StreamingTwinBone(boneIndex, mirrorBoneIdx));
}

const std::vector<cross::anim::AnimSlotGroup>& SkeletonResource::GetSlotGroups() const
{
    return mStreamingSkelt.mSlotGroups;
}

void SkeletonResource::SetSlotGroups(std::vector<cross::anim::AnimSlotGroup>& inSlotGroups)
{
    mStreamingSkelt.mSlotGroups = inSlotGroups;
}

}   // namespace cross::skeleton
