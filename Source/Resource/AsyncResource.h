#ifndef ASYNC_RESOURCE_H
#define ASYNC_RESOURCE_H
#include "EnginePrefix.h"
#include "Threading/Task.h"
#include "Resource/resourceforward.h"
#include "CrossBase/ReferenceCountObject.h"
#include "GamePlayBaseFramework/meta/reflection/objects/rtti_base.hpp"
#include "GamePlayBaseFramework/meta/reflection/meta/meta_class.hpp"

namespace cross
{
    class Resource;
    using StdTimePoint = std::chrono::high_resolution_clock::time_point;
    
template<class T>
class ResourceFuturePtr;

template<class T>
class ResourceWeakPtr;

template<class T>
using CE_REAL_PTR_IMPLMENT = ResourceFuturePtr<T>;

template<class T>
using CE_WEAK_PTR_IMPLMENT = ResourceWeakPtr<T>;

#define NEED_CHECK_ASYNC_READY 1
#define NEED_ASYNC_FUTURE_BEFORE 1

template<class T>
class CEMeta(Script) ResourceFuturePtr
{
    template<class U>
    friend class ResourceFuturePtr;

public:
    using type = T;
    ResourceFuturePtr() = default;

    explicit ResourceFuturePtr(T* ptr)
        : mRawPtr(ptr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    template<class U>
    explicit ResourceFuturePtr(U* ptr)
        : mRawPtr(ptr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    ResourceFuturePtr(std::nullptr_t) noexcept
        : mRawPtr(nullptr)
    {
    }

    ResourceFuturePtr(const ResourceFuturePtr& sp) :mRawPtr(sp.mRawPtr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    template<class U, class = std::enable_if_t<std::is_convertible_v<U*, T*>>>
    ResourceFuturePtr(const ResourceFuturePtr<U>& sp)
        : mRawPtr(sp.mRawPtr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }
    
    ResourceFuturePtr(ResourceFuturePtr&& sp) noexcept
        :mRawPtr(nullptr)
    {
        if (this != reinterpret_cast<ResourceFuturePtr*>(&reinterpret_cast<unsigned char&>(sp)))
        {
            Swap(sp);
        }
    }

    template<class U>
    ResourceFuturePtr(ResourceFuturePtr&& sp)
        : mRawPtr(sp.mRawPtr)
    {
        sp.mRawPtr = nullptr;
    }

    ResourceFuturePtr& operator=(std::nullptr_t)
    {
        InternalRelease();
        return *this;
    }

    ResourceFuturePtr& operator=(T* src)
    {
        if (mRawPtr != src)
        {
            ResourceFuturePtr(src).Swap(*this);
        }
        return *this;
    }

    template<class U>
    ResourceFuturePtr& operator=(U* src)
    {
        ResourceFuturePtr(src).Swap(*this);
        return *this;
    }

    ResourceFuturePtr& operator=(const ResourceFuturePtr& src)
    {
        if (mRawPtr != src.mRawPtr)
        {
            ResourceFuturePtr(src).Swap(*this);
        }
        return *this;
    }

    template<class U>
    ResourceFuturePtr& operator=(const ResourceFuturePtr<U>& src)
    {
        ResourceFuturePtr(src).Swap(*this);
        return *this;
    }

    ResourceFuturePtr& operator=(ResourceFuturePtr&& src) noexcept
    {
        ResourceFuturePtr(static_cast<ResourceFuturePtr&&>(src)).Swap(*this);
        return *this;
    }

    template<class U>
    ResourceFuturePtr& operator=(ResourceFuturePtr<U>&& src) noexcept
    {
        ResourceFuturePtr(static_cast<ResourceFuturePtr<U>&&>(src)).Swap(*this);
        return *this;
    }

    ~ResourceFuturePtr()
    {
        InternalRelease();
    }

    static gbf::reflection::TypeId StaticClassID()
    {
        static_assert(std::is_base_of_v<gbf::reflection::RttiBase, T>, "ResourceFuturePtr must be used with RttiBase derived classes");
        return gbf::reflection::query_meta_class<T>()->custom_id();
    }

    struct Hash
    {
        size_t operator()(const ResourceFuturePtr<T>& p) const noexcept { return std::hash<T*>()(p.get()); };
    };

    void reset(T* newObj = nullptr)
    {
        ResourceFuturePtr(newObj).Swap(*this);
    }
    
    auto getFuture() const
    {
        Assert(mRawPtr);
        return mRawPtr->GetAsset()->GetAsyncLoadEvent();
    }
#if 0
    void syncFuture() const
    {
        if(mRawPtr && mRawPtr->IsAsyncLoadValid())
        {
#if defined(NEED_CHECK_ASYNC_READY)
            if(threading::TaskSystem::IsInGameThread() && !mRawPtr->IsAsyncLoadComplete())
            {
                LOG_WARN("--linsh res is not ready, need check it");
                //Assert(0);
            }
#endif
            mRawPtr->WaitForAsyncLoad();
        }
    }
#endif

    CEFunction(ScriptCallable)
    int32_t use_count() const { return mRawPtr ? mRawPtr->GetRefCount() : 0; }
    
    T* operator->() const {
#if defined(NEED_ASYNC_FUTURE_BEFORE)
        //syncFuture();
#endif
        return mRawPtr;
    }
    
    T& operator*() const {
#if defined(NEED_ASYNC_FUTURE_BEFORE)
        //syncFuture();
#endif
        return *mRawPtr;
    }
    bool operator!() const { return mRawPtr == nullptr; }
    operator bool() const { return mRawPtr != nullptr; }
    bool operator== (const ResourceFuturePtr& rhs) const { return mRawPtr == rhs.mRawPtr; }
    bool operator!= (const ResourceFuturePtr& rhs) const { return mRawPtr != rhs.mRawPtr; }
    bool operator== (T* rawPtr) const { return mRawPtr == rawPtr; }
    bool operator!= (Resource* rawPtr) const { return mRawPtr != rawPtr; }
    bool operator< (const ResourceFuturePtr& rhs) const { return mRawPtr < rhs.mRawPtr; }
    
    void wait()
    {
        if(mRawPtr && mRawPtr->GetAsset() && mRawPtr->IsAsyncLoadValid())
        {
            mRawPtr->GetAsset()->WaitForAsyncLoad();
        }
    }
    
    T* getNoSyncFuture() const {
        return mRawPtr;
    }
    
    T* get() const {
#if defined(NEED_ASYNC_FUTURE_BEFORE)
        //syncFuture();
#endif
        
        return mRawPtr;
    }
    
    bool operator()() const
    { 
        return mRawPtr != nullptr; 
    }
    //std::shared_ptr<Resource> get_shard_ptr() const { return std::shared_ptr<Resource>(mRawPtr); }
    ///*explicit*/ operator std::shared_ptr<Resource>() const { return std::shared_ptr<Resource>(mRawPtr);  }

    //cross::TFuture<bool> mLoadFuture;
private:
    void NotifyZeroReference() { delete mRawPtr; }

    void Swap(ResourceFuturePtr& r)
    {
        type* tmp = mRawPtr;
        mRawPtr = r.mRawPtr;
        r.mRawPtr = tmp;
    }

    void Swap(ResourceFuturePtr&& r)
    {
        type* tmp = mRawPtr;
        mRawPtr = r.mRawPtr;
        r.mRawPtr = tmp;
    }

    int32_t InternalRelease()
    {
        int32_t ref = 0;
        if (mRawPtr)
        {
            mRawPtr->DecreaseRefCount();
            ref = mRawPtr->GetRefCount();
            Assert(ref >= 0);
            if (ref == 0)
                NotifyZeroReference();
            mRawPtr = nullptr;
        }
        return ref;
    }

    T* mRawPtr{ nullptr };
};

template<class T>
class ResourceWeakPtr
{
public:
    ResourceWeakPtr() = default;

    ResourceWeakPtr(std::nullptr_t) noexcept
    {
        mSafePtr = nullptr;
    }

    ResourceWeakPtr(const ResourceFuturePtr<T>& other) noexcept
    {
        mSafePtr = other.get();
    }

    ResourceWeakPtr(const ResourceWeakPtr& other) noexcept
    {
        mSafePtr = other.mSafePtr;
    }

    ResourceWeakPtr(ResourceWeakPtr&& other) noexcept
    {
        mSafePtr = std::move(other.mSafePtr);
        other.mSafePtr.reset();
    }

    ResourceWeakPtr& operator=(const ResourceFuturePtr<T>& other) noexcept
    {
        std::scoped_lock lock(mMutex);

        mSafePtr = other.get();
        return *this;
    }

    ResourceWeakPtr& operator=(const ResourceWeakPtr& other) noexcept
    {
        std::scoped_lock lock(mMutex);

        if (mSafePtr.get() != other.mSafePtr.get())
        {
            mSafePtr = other.mSafePtr;
        }
        return *this;
    }

    ResourceWeakPtr& operator=(ResourceWeakPtr&& other) noexcept
    {
        std::scoped_lock lock(mMutex);

        if (mSafePtr.get() != other.mSafePtr.get())
        {
            mSafePtr = std::move(other.mSafePtr);
            other.mSafePtr.reset();
        }
        return *this;
    }

    ResourceWeakPtr& operator=(std::nullptr_t)
    {
        reset();
        return *this;
    }

    virtual ~ResourceWeakPtr() noexcept
    {
        mSafePtr.reset();
    }

    bool operator==(const ResourceFuturePtr<T>& rhs) const noexcept
    {
        return mSafePtr.get() == rhs.get();
    }

    bool operator==(const ResourceWeakPtr& rhs) const noexcept
    {
        return mSafePtr.get() == rhs.mSafePtr.get();
    }

    bool operator==(T* rawPtr) const noexcept
    {
        return mSafePtr.get() == rawPtr;
    }

    struct Hash
    {
        size_t operator()(const ResourceWeakPtr<T>& p) const noexcept { return std::hash<T*>()(p.mSafePtr.get()); };
    };

    void reset() noexcept
    {
        std::scoped_lock lock(mMutex);

        mSafePtr.reset();
    }

    // TODO(hendrikwang): swap is not supported
    // TODO(hendrikwang): use_count is not supported

    bool expired() const noexcept
    {
        std::scoped_lock lock(mMutex);

        if constexpr (std::is_base_of_v<ReferenceCountObject, T>)
        {
            return mSafePtr.empty() || mSafePtr->GetRefCount() == 0;
        }
        else
        {
            return mSafePtr.empty();
        }
    }

    ResourceFuturePtr<T> lock() const noexcept
    {
        // If `lock` is called during the dtor of a ReferenceCountObject, mSafePtr is still valid but the reference count is 0,
        // which may cause duplicate destruction of this ReferenceCountObject (ref count 0 -> 1 -> 0)
        // Use `expired` to examine this situation before wrapping and returning a ResourceFuturePtr
        if (expired())
        {
            return nullptr;
        }
        else
        {
            std::scoped_lock lock(mMutex);
            return ResourceFuturePtr<T>(mSafePtr.get());
        }
    }

private:
    SafePtr<T> mSafePtr;
    mutable std::mutex mMutex;
};

template<typename To, typename From>
ResourceFuturePtr<To> TypeCastNoDefault(ResourceFuturePtr<From> f)
{
    To* to = TYPE_CAST(To*, f.get());
    return ResourceFuturePtr<To>(to);
}

    class Resource_API AsyncTimeState
{
    public:
        AsyncTimeState();
        ~AsyncTimeState();
		enum STATE_CATEGORY
		{
            CATEGORY_INIT = 0,
            CATEGORY_GETRESOURCE,
            CATEGORY_ASYNCGETRESOURCE,
			CATEGORY_GETRESOURCE_ASYNCLOADNDAFILE_ASYNC,
            CATEGORY_GETRESOURCE_ASYNCLOADNDAFILE_ASYNC_DSERIALIZE,
            CATEGORY_UPDATE,
			CATEGORY_NONAME,
			CATEGORY_NUM
		};
        
        
        std::vector<std::string> StateEnumToString =
        {
            "CATEGORY_INIT",
            "CATEGORY_GETRESOURCE",
            "CATEGORY_ASYNCGETRESOURCE",
            "CATEGORY_GETRESOURCE_ASYNCLOADNDAFILE_ASYNC",
            "CATEGORY_GETRESOURCE_ASYNCLOADNDAFILE_ASYNC_DSERIALIZE",
            "CATEGORY_UPDATE",
            "CATEGORY_NONAME"
        };

		struct TimeItem
		{
			STATE_CATEGORY mClassfy = CATEGORY_NONAME;
			std::string    mInfo    = "";
			StdTimePoint   mStart;
			StdTimePoint   mEnd;
			double         mElapsedTimeMs;
		};
    public:
		void AddTimeState(STATE_CATEGORY inCatgery, std::string inInfo, StdTimePoint inStartTimePoint, StdTimePoint inEndTimePoint);
        void OutputState();
        
    private:

        std::array<std::vector<TimeItem>, STATE_CATEGORY::CATEGORY_NUM> mMappedStateItems;
        
        std::array<std::string,STATE_CATEGORY::CATEGORY_NUM> mCateoryIndexToName;
        
        std::mutex*  mMutex = nullptr;
	};

    template<class T>
    void ResourceFuturePtrUnitTest()
    {
        T* raw_ptr = nullptr;
        {
            cross::ResourceFuturePtr<T> test(new T);
            raw_ptr = test.get();
            Assert(test.use_count() == 1);
            LOG_ERROR("GTGT, test {}", test.use_count());

            // copy 
            {
                // copy construct
                cross::ResourceFuturePtr<T> test0(test);
                Assert(test.use_count() == 2);

                cross::ResourceFuturePtr<T> test2;
                // T copy to nullptr
                test2 = test;
                Assert(test.use_count() == 3);

                // copy nullptr
                cross::ResourceFuturePtr<T> test3;
                test2 = test3;
                Assert(test.use_count() == 2);

                T* new_t = new T;
                Assert(new_t->GetRefCount() == 0);

                cross::ResourceFuturePtr<T> new_test(new_t);
                Assert(new_test.use_count() == 1);

                test0 = new_test;
                Assert(new_test.use_count() == 2);
                Assert(test.use_count() == 1);
            }
            Assert(test.use_count() == 1);

            // move
            {
                cross::ResourceFuturePtr<T> test0(std::move(test));
                Assert(test.get() == nullptr);
                Assert(test0.use_count() == 1);

                cross::ResourceFuturePtr<T> test1;
                test1 = std::move(test0);
                Assert(test0.get() == nullptr);
                Assert(test1.use_count() == 1);

                T* new_t = new T;
                Assert(new_t->GetRefCount() == 0);
                cross::ResourceFuturePtr<T> new_test0(new_t);
                cross::ResourceFuturePtr<T> new_test1(new_t);
                Assert(new_test0.use_count() == 2);
                new_test0 = std::move(test1);
                Assert(test1.get() == nullptr);
                Assert(new_test0.use_count() == 1);
                Assert(new_test1.use_count() == 1);

                test = std::move(new_test0);
            }
            Assert(test.use_count() == 1);

            // reset
            {
                cross::ResourceFuturePtr<T> test0;
                test0.reset(test.get());
                Assert(test.use_count() == 2);
                LOG_ERROR("GTGT, test {}", test.use_count());

                test0.reset();
                Assert(test.use_count() == 1);
                LOG_ERROR("GTGT, test {}", test.use_count());
            }
            Assert(test.use_count() == 1);

            // weak
            {
                // default ctor
                cross::ResourceWeakPtr<T> testWeak0;
                Assert(testWeak0.expired());

                // copy ctor from shared
                cross::ResourceWeakPtr<T> testWeak(test);
                Assert(!testWeak.expired());

                // copy operator=
                cross::ResourceWeakPtr<T> testWeak1;
                testWeak1 = test;
                Assert(!testWeak1.expired());

                // copy ctor
                cross::ResourceWeakPtr<T> testWeak2(testWeak1);
                Assert(!testWeak2.expired());

                // move operator=
                cross::ResourceWeakPtr<T> testWeak3;
                testWeak3 = std::move(testWeak1);
                Assert(testWeak1.expired());
                Assert(!testWeak3.expired());

                // move ctor
                cross::ResourceWeakPtr<T> testWeak4(std::move(testWeak2));
                Assert(testWeak2.expired());
                Assert(!testWeak4.expired());

                // reset && expired
                testWeak3.reset();
                testWeak4.reset();
                Assert(testWeak3.expired());
                Assert(testWeak4.expired());

                // lock
                {
                    auto testWeak5 = testWeak4.lock();
                    Assert(testWeak5 == nullptr);
                }
                {
                    auto testWeak6 = testWeak.lock();
                    Assert(testWeak6 != nullptr);
                    Assert(test.use_count() == 2);
                }
                Assert(test.use_count() == 1);
            }

            LOG_ERROR("GTGT, test {}", test.use_count());
        }
        LOG_ERROR("GTGT, raw_ptr {}", (uintptr_t)raw_ptr);
    }
}


#endif//#define ASYNC_RESOURCE_H
