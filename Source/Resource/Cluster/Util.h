#pragma once
#include "CrossBase/Math/CrossMath.h"
#include "CrossBase/Hash/Hash.h"
#include <unordered_map>
#include <unordered_set>
#include <vector>
namespace cross::StellarMesh
{

static FORCEINLINE UInt32 MurmurFinalize32(UInt32 Hash)
{
    Hash ^= Hash >> 16;
    Hash *= 0x85ebca6b;
    Hash ^= Hash >> 13;
    Hash *= 0xc2b2ae35;
    Hash ^= Hash >> 16;
    return Hash;
}
    
static FORCEINLINE UInt64 MurmurFinalize64(UInt64 Hash)
{
    Hash ^= Hash >> 33;
    Hash *= 0xff51afd7ed558ccdull;
    Hash ^= Hash >> 33;
    Hash *= 0xc4ceb9fe1a85ec53ull;
    Hash ^= Hash >> 33;
    return Hash;
}

static FORCEINLINE UInt32 Murmur32(std::initializer_list<UInt32> InitList)
{
    UInt32 Hash = 0;
    for (auto Element : InitList)
    {
        Element *= 0xcc9e2d51;
        Element = (Element << 15) | (Element >> (32 - 15));
        Element *= 0x1b873593;

        Hash ^= Element;
        Hash = (Hash << 13) | (Hash >> (32 - 13));
        Hash = Hash * 5 + 0xe6546b64;
    }

    return MurmurFinalize32(Hash);
}

FORCEINLINE UInt32 HashPosition(const Float3& position)
{
    union
    {
        float f;
        UInt32 i;
    } x;
    union
    {
        float f;
        UInt32 i;
    } y;
    union
    {
        float f;
        UInt32 i;
    } z;

    x.f = position.x;
    y.f = position.y;
    z.f = position.z;

    return Murmur32({position.x == 0.0f ? 0u : x.i, position.y == 0.0f ? 0u : y.i, position.z == 0.0f ? 0u : z.i});
}

// Cycle value in pattern 3:
// 0 -> 1
// 1 -> 2
// 2 -> 0
// 3 -> 4
// 4 -> 5
// 5 -> 3
[[nodiscard]] inline UInt32 Cycle3( UInt32 Value )
{
	UInt32 ValueMod3 = Value % 3;
	UInt32 Value1Mod3 = ( 1 << ValueMod3 ) & 3;
	return Value - ValueMod3 + Value1Mod3;
}

//TODO:: Concurrent Hash Table
template<typename HashType, typename ValueType>
struct HashTable final
{
    void Add(HashType hash, ValueType value) noexcept
    {
        mTable[hash].insert(std::move(value));
    }

    bool Erase(HashType hash, ValueType const& value) noexcept
    {
        if (mTable.contains(hash)) {
            auto result = mTable[hash].erase(value);
            if (mTable[hash].empty())
                mTable.erase(hash);
            return result == 1;
        }
        return false;
    }

    template<typename FuncType>
    void ForEach(HashType hash, FuncType&& fn) const noexcept
    {
        if (mTable.contains(hash))
            for (auto const& value: mTable.at(hash))
                fn(value);
    }

private:
    std::unordered_map<HashType, std::unordered_set<ValueType>> mTable{};
};

// Find edge with opposite direction that shares these 2 verts.
//
//   /\
//  /  \
// o-<<-o
// o->>-o
//  \  /
//   \/
//
// The two triangles are considered adjacent to each other
struct EdgeHashTable final
{
    template<typename GetPosition>
    void AddEdge(UInt32 edgeIndex, GetPosition&& getVertexPosition)
    {
        auto const& position0 = getVertexPosition(edgeIndex);
        auto const& position1 = getVertexPosition(Cycle3(edgeIndex));
        
        //size_t hash0 = std::hash<std::tuple<float, float, float>>{}(std::make_tuple(position0.x, position0.y, position0.z));
        //size_t hash1 = std::hash<std::tuple<float, float, float>>{}(std::make_tuple(position1.x, position1.y, position1.z));
        //size_t hash = std::hash<std::tuple<size_t, size_t>>{}(std::make_tuple(hash0, hash1));

        UInt32 hash0 = HashPosition(position0);
        UInt32 hash1 = HashPosition(position1);
        UInt32 hash = Murmur32({hash0, hash1});

        mHashTable.Add(hash, edgeIndex); 
    }

    template<typename GetPosition, typename FuncType>
    void ForEachMatchingEdge(UInt32 edgeIndex, GetPosition&& getVertexPosition, FuncType&& fn)
    {
        auto const& position0 = getVertexPosition(edgeIndex);
        auto const& position1 = getVertexPosition(Cycle3(edgeIndex));

        //size_t hash0 = std::hash<std::tuple<float, float, float>>{}(std::make_tuple(position0.x, position0.y, position0.z));
        //size_t hash1 = std::hash<std::tuple<float, float, float>>{}(std::make_tuple(position1.x, position1.y, position1.z));
        //size_t hash = std::hash<std::tuple<size_t, size_t>>{}(std::make_tuple(hash1, hash0));

        UInt32 hash0 = HashPosition(position0);
        UInt32 hash1 = HashPosition(position1);
        UInt32 hash = Murmur32({hash1, hash0});

        mHashTable.ForEach(
            hash,
            [&](UInt32 otherEdgeIndex) -> void
            {
                if (position0 == getVertexPosition(Cycle3(otherEdgeIndex)) && position1 == getVertexPosition(otherEdgeIndex))
                {
                    fn(edgeIndex, otherEdgeIndex);
                }
            });
    }

private:
    HashTable<UInt32, UInt32> mHashTable{};   // <hash -> edgeIndex>
};

struct Adjacency final
{
    Adjacency(UInt32 n)
    {
        mDirect.resize(n, -1);
    }

    void Link(SInt32 edgeIndex0, SInt32 edgeIndex1)
    {
        if (edgeIndex0 == edgeIndex1)
            return;

        if( mDirect[edgeIndex0] < 0)
		{
			mDirect[edgeIndex0] = edgeIndex1;
		}
        else
        {
            mExtended[edgeIndex0].emplace_back(edgeIndex1);
        }
    }

    template<typename FuncType>
    void ForEach(SInt32 edgeIndex, FuncType&& fn) const
    {
        auto adjIndex = mDirect[edgeIndex];
        if (adjIndex != -1)
        {
            fn(edgeIndex, adjIndex);
        }

        if (!mExtended.contains(edgeIndex))
            return;

        for (auto it = mExtended.at(edgeIndex).begin(); it != mExtended.at(edgeIndex).end(); ++it)
        {
            fn(edgeIndex, *it);
        }
    }

private:
    std::vector<SInt32> mDirect{};
    std::unordered_map<SInt32, std::vector<SInt32>> mExtended{};
};

struct DisjointSet final
{
    DisjointSet() {}
    DisjointSet(const UInt32 size)
    {
        mParents.resize(size);
        for (UInt32 i = 0; i < size; i++)
        {
            mParents[i] = i;
        }
    }

    void Union(UInt32 x, UInt32 y)
    {
        UInt32 px = mParents[x];
        UInt32 py = mParents[y];
        while (px != py)
        {
            mParents[y] = px;
            if (y == py)
            {
                return;
            }
            y = py;
            py = mParents[y];
        }
    }

    UInt32 FindRoot(UInt32 x)
    {
        if (x == mParents[x])
            return x;
        return mParents[x] = FindRoot(mParents[x]);
    }

private:
    std::vector<UInt32> mParents{};
};

}   // namespace cross::StellarMesh
