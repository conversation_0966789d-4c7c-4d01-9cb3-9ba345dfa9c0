#include "EnginePrefix.h"
#include "FetchResource.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "FileSystem/filesystem.h"
#include <filesystem>

namespace cross { 
    
    namespace resource {
        
        void FetchResource::CheckAndFetch(const std::string& inRelativePath)
        {
#ifdef CROSSENGINE_DEPLOY
            auto* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            if (!fileSystem->HaveFile(inRelativePath)) {
                Fetch(inRelativePath);
            }
#endif
        }
        
        void FetchResource::CheckAndFetchFrom(const std::string& inRelativePath, const std::string& fetchPath)
        {
#ifdef CROSSENGINE_DEPLOY
            auto* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            if (!fileSystem->HaveFile(inRelativePath)) {
                FetchFrom(inRelativePath, fetchPath);
            }

#endif
        }

        void FetchResource::Fetch(const std::string& inRelativePath)
        {
#ifdef CROSSENGINE_DEPLOY
            auto fetchPathNode = EngineGlobal::Inst().GetSettingMgr()->GetSettingOption("FetchPath");
            if (fetchPathNode) {
                std::string fetchPath = fetchPathNode->AsString();
                FetchFrom(inRelativePath, fetchPath);
            }
#endif
        }

        void FetchResource::FetchFrom(const std::string& inRelativePath, const std::string& fetchPath)
        {
#ifdef CROSSENGINE_DEPLOY
            std::string fetchFile = fetchPath + "/" + inRelativePath;
            auto* fileSystem = cross::EngineGlobal::Inst().GetFileSystem();
            auto file = fileSystem->Open(fetchFile);
            if (file) 
            {
                LOG_ERROR("[Fetch] Try To Fetch Missing Resources \"{}\"", inRelativePath);

                std::string savedFile = PathHelper::GetCurrentDirectoryPath() + "/" + inRelativePath;
                
                std::filesystem::path savedPath = savedFile;
                std::filesystem::create_directories(savedPath.remove_filename());

                if (fileSystem->Save(savedFile, file->GetBuffer(), file->GetSize()))
                {
                    LOG_WARN("[Fetch] Succeed Copy \"{}\" to \"{}\"", fetchFile, savedFile);
                }
                else
                {
                    LOG_ERROR("[Fetch] Failed Copy \"{}\" to \"{}\"", fetchFile, savedFile);
                }
            }
            else
            {
                LOG_ERROR("[Fetch] Found Abandoned Resources \"{}\"", inRelativePath);
            }
#endif
        }
    }
    
}
