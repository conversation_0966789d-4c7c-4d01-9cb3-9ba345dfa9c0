#include "Resource/FontResource.h"
#include "Resource/AssetStreaming.h"

namespace cross::resource
{
    bool FontResource::Serialize(const std::string& path)
    {
        if (!HasAsset())
        {
            CreateAsset(path);
        }
        ClearReference();

        SerializeNode node;
        SerializeContext context;
        node[FONT_INFO_KEY] = std::move(mFontInfo.Serialize(context));
        if (mMSDFTexture && mMSDFTexture->HasAsset())
        {
            node[MSDF_TEXTURE_KEY] = mMSDFTexture->GetGuid_Str();
            AddReferenceResource(mMSDFTexture->GetGuid_Str());
        }

        return Resource::Serialize(std::move(node), path);
    }

    bool FontResource::Deserialize(DeserializeNode const& s)
    {
        Clear();
        if (s.HasMember(FONT_INFO_KEY))
        {
            SerializeContext context;
            mFontInfo.Deserialize(s[FONT_INFO_KEY], context);
        }
        if (s.<PERSON>(MSDF_TEXTURE_KEY))
        {
            std::string str = s[MSDF_TEXTURE_KEY].AsString();
            ResourcePtr tex = gAssetStreamingManager->GetResource(str);
            if (tex)
            {
                mMSDFTexture = TypeCast<resource::Texture>(tex);
            }
        }

        return true;
    }

    void FontResource::SetFontInfoByJson(DeserializeNode const& s)
    {
        SerializeContext context;
        mFontInfo.Deserialize(s, context);
    }

    void FontResource::Clear()
    {
        mFontInfo = FontInfo{};
        mMSDFTexture = TexturePtr(nullptr);
    }
}