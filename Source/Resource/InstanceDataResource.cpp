#include "EnginePrefix.h"
#include "InstanceDataResource.h"

#include "InstancedStaticModelSystemR.h"
#include "RenderEngine.h"
#include "CrossBase/Serialization/SerializeNode.h"
#include "Resource/ResourceManager.h"
#include "Resource/IResourceInterface.h"
#include "String/StringCodec.h"
#include <ranges>

namespace cross::resource {

void InstanceMemberData::AdditionalSerialize(SerializeNode& inNode, SerializeContext& context) const
{
    inNode["mData"] = Base64Codec::encode(mData.data(), mData.size());
}

void InstanceMemberData::AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context)
{
    mData = Base64Codec::decode(inNode["mData"].AsString());
}

InstanceDataResource::InstanceDataResource()
{
    const static Float3 one(1, 1, 1);
    const static Float3 zero(0, 0, 0);
    const static std::vector oneData(reinterpret_cast<const UInt8*>(one.data()), reinterpret_cast<const UInt8*>(one.data()) + sizeof(Float3));
    const static std::vector zeroData(reinterpret_cast<const UInt8*>(zero.data()), reinterpret_cast<const UInt8*>(zero.data()) + sizeof(Float3));

    const static InstanceMemberData Translation{"Translation", InstanceMemberType::Float3, zeroData};
    const static InstanceMemberData Rotation{"Rotation", InstanceMemberType::Float3, zeroData};
    const static InstanceMemberData Scale{"Scale", InstanceMemberType::Float3, oneData};

    SetInstanceMemberData(Translation);
    SetInstanceMemberData(Rotation);
    SetInstanceMemberData(Scale);
}

bool InstanceDataResource::Deserialize(DeserializeNode const& componentJson)
{
    SerializeContext context{};
    Deserialize(componentJson, context);
    gResourceMgr.mCreateRenderObjectMgr->NotifyInstanceDataResourceChange(this);
    return true;
}
InstanceDataResource* InstanceDataResource::InstanceDataResource_CreateInstanceDataResource()
{
    auto resourcePtr = gResourceMgr.CreateResourceAs<cross::resource::InstanceDataResource>();
    //resourcePtr->IncreaseRefCount();
    return resourcePtr.get();
}
bool InstanceDataResource::Serialize(SerializeNode&& s, const std::string& path)
{
    if (!HasAsset())
    {
        CreateAsset(path);
    }

    SerializeContext context{};
    Serialize(s, context);
    return Resource::Serialize(std::move(s), path);
}

void InstanceDataResource::ClearAllInstanceDatas()
{
    if (threading::TaskSystem::IsInGameThread())
    {
        threading::FlushRenderingCommands();
    }

    mInstanceMembers.clear();
}

void InstanceDataResource::ClearInstanceMemberData(const std::string& name)
{
    if (threading::TaskSystem::IsInGameThread())
    {
        threading::FlushRenderingCommands();
    }

    std::erase_if(mInstanceMembers, [&](auto& member) { return name == member.mName; });
}

void InstanceDataResource::SetInstanceMemberData(const InstanceMemberData& data)
{
    if (threading::TaskSystem::IsInGameThread())
    {
        threading::FlushRenderingCommands();
    }

    if (auto itr = std::ranges::find(mInstanceMembers, data.mName, &InstanceMemberData::mName); itr == mInstanceMembers.end())
    {
        mInstanceMembers.push_back(data);
    }
    else
    {
        *itr = data;
    }
}

void InstanceDataResource::SetInstanceMemberData(const std::string& name, UInt32 type, const void* data, UInt32 size, UInt32 stride)
{
    if (threading::TaskSystem::IsInGameThread())
    {
        threading::FlushRenderingCommands();
    }

    if (auto ret = std::ranges::find(mInstanceMembers, name, &InstanceMemberData::mName); ret == mInstanceMembers.end())
    {
        mInstanceMembers.push_back({name, static_cast<InstanceMemberType>(type), std::vector(reinterpret_cast<const UInt8*>(data), reinterpret_cast<const UInt8*>(data) + size)});
    }
    else
    {
        auto& instanceMemberData = *ret;
        instanceMemberData.mType = static_cast<InstanceMemberType>(type);
        instanceMemberData.mData.resize(size);
        memcpy(instanceMemberData.mData.data(), data, size);
    }
}

void InstanceDataResource::MarkDirty()
{
    mClusterNodes.clear();
    gResourceMgr.mCreateRenderObjectMgr->NotifyInstanceDataResourceChange(this);
}
InstanceMemberDataInfo InstanceDataResource::EditorGetInstanceMemberData(const std::string& name)
{
    auto ret = std::ranges::find(mInstanceMembers, name, &InstanceMemberData::mName);
    Assert(ret != mInstanceMembers.end());
    auto& data = *ret;

    return {
        .outType = static_cast<UInt32>(data.mType),
        .outData = data.mData.data(),
        .outSize = static_cast<UInt32>(data.mData.size()),
        .outStride = static_cast<UInt32>(data.mData.size() / mInstanceCount),
    };
}

bool InstanceDataResource::HasMember(const std::string& name) const
{
    return std::ranges::find(mInstanceMembers, name, &InstanceMemberData::mName) != mInstanceMembers.end();
}

const InstanceMemberData& InstanceDataResource::GetInstanceMemberData(const std::string& name)
{
    auto ret = std::ranges::find(mInstanceMembers, name, &InstanceMemberData::mName);
    Assert(ret != mInstanceMembers.end());
    return *ret;
}

}   // namespace cross::resource

