#include "LevelSubSeqTrack.h"
#include "Resource/AssetStreaming.h"
#include "CrossBase/Serialization/SerializeTemplateBase.h"


namespace cross {

void LevelSubSeqTrack::AddNewSubSeqSection(float startTime, const std::string& subSequence, float duration, float fps, float palyRate)
{
    LevelSubSeqSection section;
    section.SectionName = PathHelper::GetBaseFileName(subSequence);
    section.SubSeqPath = subSequence;
    section.SectionStart = startTime;
    section.SectionEnd = startTime + duration;
    section.SubSeqFPS = fps;
    section.SubSeqPlayRate = palyRate;
    section.SubSeqDuration = duration;
    AddNewSubSeqSection(section);
}

void LevelSubSeqTrack::AddNewSubSeqSection(LevelSubSeqSection& section)
{
    SubSeqSections.push_back(section);
}

bool LevelSubSeqTrack::UpdateSubSeqSection(LevelSubSeqSection& section, UInt32 index) 
{
    if (index < SubSeqSections.size()) 
    {
        SubSeqSections[index] = section;
        return true;
    }
    return false;
}

void LevelSubSeqTrack::RemoveAnimSectionAt(UInt32 index) 
{
    if (index < SubSeqSections.size()) 
    {
        SubSeqSections.erase(SubSeqSections.begin() + index);
    }
}

void LevelSubSeqTrack::Clear() 
{
    SubSeqSections.clear();
}
std::vector<LevelSubSeqSection> LevelSubSeqTrack::GetSubSeqSectionsAtTime(float keyTime) 
{
    std::vector<LevelSubSeqSection> sections;
    for (auto& section : SubSeqSections) 
    {
        if (section.IsTimeWithinSection(keyTime)) 
        {
            sections.push_back(section);
        }
    }
    return sections;
}
}