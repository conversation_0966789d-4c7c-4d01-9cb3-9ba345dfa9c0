#pragma once

#include "MaterialInterface.h"
#include "Fx.h"

namespace cross { namespace resource {

    class Resource_API CEMeta(Cli, Script) Material : public MaterialInterface
    {
    public:
        FRIEND_WITH_REFLECTION_MODULE;
        friend class cross::ResourceManager;
        friend class Fx;

        using PropertyType = MaterialInterface::PropertyType;
        using StateType = MaterialInterface::StateType;
        using PropertyAccesser = MaterialInterface::PropertyAccesser;
        using PropertyRange = Fx::Property::Range;

        struct Pass
        {
            std::optional<bool> mEnable;
            std::optional<UInt32> mRenderGroup;
            std::optional<StateType> mState;
        };

    public:
        Material() = default;
        // use to create material instance from material
        // NOTE: DO NOT perform refresh or other operations that affect reference count in constructors, 
        // which will cause self-deletion because the reference count is 0 in constructors 
        static MaterialInterfacePtr CreateMaterialInstance(const MaterialPtr& parentPtr);
        static MaterialInterfacePtr CreateMaterialInstance(const FxPtr& fxPtr);
        static MaterialInterfacePtr CreateMaterialInstance(const std::string& path);
        static MaterialPtr CreateMaterialTempInstance(const MaterialPtr& parentPtr);
        static MaterialPtr CreateMaterialTempInstance(const FxPtr& fxPtr);

        virtual ~Material();

        virtual bool RefreshRenderData() override;

        StatementDefaultResource(Material)

        static int GetClassIDStatic()
        {
            return ClassID(Material);
        }

        virtual bool ResetResource() override;

        void CopyFrom(const Material& material);

        CEMeta(Cli)//Editor API
        static Resource* MaterialCreateMaterial(char const* shaderPath);
        CEMeta(Cli)
        static cross::resource::Material* Material_CreateInstance(cross::resource::Material* material)
        {
            return static_cast<cross::resource::Material*>(material->CreateInstance().get());
        }

    protected:
        virtual void PostDestroy() override;
        virtual void DestroyRenderData(FrameParam * frameParam) override;

        void SetToRender(const NameID& propID, const PropertyType& propVal);
        void SetToRender(const NameID& passID, const StateType& states);
        void SetToRender(const NameID& passID, const UInt32 group);
        void SetToRender(const NameID& passID, const bool enable);

        virtual void OnParentDirty(const NameID& propID, const PropertyType& propVal) override;
        virtual void OnParentDirty(const NameID& passID, const StateType& states) override;
        virtual void OnParentDirty(const NameID& passID, UInt32 group) override;
        virtual void OnParentDirty(const NameID& passID, bool enable) override;

    public:
        virtual bool Serialize(SerializeNode && s, const std::string& path) override;
        bool Serialize(SerializeNode && s, const std::string& path, MaterialPtr existMat);
        virtual bool Deserialize(SimpleSerializer const& s) override;   // Deprecated!
        virtual bool Deserialize(const DeserializeNode& s) override;
        virtual bool PostDeserialize() override;
    public:// Editor interface
        CEMeta(Cli)
        int GetPropertyType(const NameID& name);
        CEMeta(Cli)
        int GetPropertyOverrided(const NameID& name);
        CEMeta(Cli)
        float GetPropertyFloat(const NameID& name);
        CEMeta(Cli)
        cross::Float2 GetPropertyFloat2(const NameID& name);
        CEMeta(Cli)
        cross::Float3 GetPropertyFloat3(const NameID& name);
        CEMeta(Cli)
        cross::Float4 GetPropertyFloat4(const NameID& name);
        CEMeta(Cli)
        std::string GetPropertyString(const NameID& name);
        CEMeta(Cli)
        bool GetPropertyBool(const NameID& name);
        CEMeta(Cli)
        int GetRenderState(const NameID& name);
    public:
        virtual Shader* GetShader(const NameID& passId) override;

        virtual PropertyType const* GetProperty(const NameID& name) const override;
        bool IsPropertyOverride(const NameID& name) const;
        virtual bool const* GetPropertyVisible(const NameID& name) const override;
        virtual bool const* GetPropertyIsColor(const NameID& name) const override;

        virtual bool IsPassEnable(const NameID& passID) const override;
        virtual void SetPassEnable(const NameID& passID, bool enable) override;
        virtual StateType const* GetState(const NameID& passID) const override;
        CEMeta(Cli)
        virtual UInt32 GetRenderGroup(NameID const& passID) override;
        virtual void VisitProperty(PropertyAccesser func) const override;
        virtual bool HasAncestor(const std::string& name) override;

        virtual void Refresh(bool resetResource = false) override;
        virtual void RefreshMaterialTree(bool resetResource = false, bool skipSelf = false) override;

        // hot reload
        CEMeta(Cli)
        virtual bool SetParent(const std::string& path) override;
        bool SetParent(MaterialInterfacePtr parent);
        bool SetParentAndSave(const std::string& path);
        bool SetParentAndSave(MaterialInterfacePtr parent);
        void UnlinkParent();
        CEMeta(Cli)
        virtual void SetBool(const NameID& name, bool value) override;
        CEMeta(Cli)
        virtual void SetInt(const NameID& name, int value) override;
        CEMeta(Cli)
        virtual void SetFloat(const NameID& name, float value) override;

        virtual void SetFloat2(const NameID& name, const float value[2]) override;

        virtual void SetFloat3(const NameID& name, const float value[3]) override;

        virtual void SetFloat4(const NameID& name, const float value[4]) override;
        CEMeta(Cli)
        virtual void SetFloatArray(const NameID& name, int length, const float* pValue = nullptr) override;

        CEMeta(Cli)
        void SetTexture(const NameID& name, const char* texturepath);

        virtual void SetTexture(const NameID& name, TexturePtr texture) override;

        virtual void SetTextureProp(const NameID& name, TexturePtr texture) override;
        virtual void SetSamplerState(const NameID& name, const SamplerState& samplerState) override;
        virtual void SetProp(const NameID& name, const PropertyType& propVal) override;

        virtual void SetBlendState(const NameID& passID, const NGIBlendStateDesc& blendDesc) override;
        virtual void SetBlendStateProp(const NameID& passID, const NGIBlendStateDesc& blendDesc) override;
        virtual void SetDepthStencilState(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc) override;
        virtual void SetDepthStencilStateProp(const NameID& passID, const NGIDepthStencilStateDesc& depthStencilDesc) override;
        virtual void SetRasterizerState(const NameID& passID, const NGIRasterizationStateDesc& rasterizerState) override;
        virtual void SetDynamicState(const NameID& passID, const NGIDynamicStateDesc& dynamicState) override;
        CEMeta(Cli)
        virtual void SetRenderGroup(const NameID& passID, UInt32 val) override;
        virtual void SetRenderGroupProp(const NameID& passID, UInt32 val) override;
        CEMeta(Cli)
        virtual void RemoveProperty(const NameID& name) override;
        CEMeta(Cli)
        virtual void ResetRenderGroup(const NameID& name) override;

        void ClearAllProperties();

    public:
        virtual FxPtr GetFx() const override { return mBaseMaterial->GetFx(); }
        virtual MaterialInterfacePtr GetParent() override { return mBaseMaterial; }
        virtual MaterialInterfacePtr CreateInstance() override;
        virtual bool IsDependent(const std::string& materialGuid) override;

        virtual MaterialPtr CreateTempInstance() override;

        virtual std::string GetName() const override;
        virtual const std::string& GetGuid_Str() const override;

        virtual bool HasAsset() const override;


        virtual void EditorVisitProperty(PropertyAccesser func) override;
        CEMeta(Cli)
        virtual std::string EditorGetPropertyString() override;
        CEMeta(Cli)
        virtual std::string EditorGetPassString() override;
        CEMeta(Cli)
        virtual void EditorSetRenderState(const NameID& passID, MaterialRenderState renderState) override;
        virtual MaterialRenderState EditorGetRenderState(const NameID& passID) const override;
        virtual bool EditorGetBlendEnable(const NameID& passID) override;
        CEMeta(Cli)
        virtual bool EditorIsPropertyOverrided(const NameID& propID) override;
        CEMeta(Cli)
        virtual bool EditorIsRenderGroupOverrided(const NameID& passID) override;
        CEMeta(Cli)
        virtual bool EditorIsRenderStateOverrided(const NameID& passID) override;
        CEMeta(Cli)
        virtual void EditorNotitfySubMaterial() override;
        virtual void AfterAssetDelete() override;
        CEMeta(Cli)
        void EditorSetMPCResource(const char* mpcPath);
        CEMeta(Cli)
        void EditorSetFloat2(const NameID& name, Float2 value);
        CEMeta(Cli)
        void EditorSetFloat3(const NameID& name, Float3 value);
        CEMeta(Cli)
        void EditorSetFloat4(const NameID& name, Float4 value);
        CEMeta(Cli)
        std::string EditorGetParentPath();
        CEMeta(Cli)
        bool EditorGetIsOverrideMat();
        CEMeta(Cli)
        std::string EditorGetFxPath();
        CEMeta(Cli)
        std::string EditorGetParameterCollectionPath();
        void NotifyChange();
        virtual void NotifyChangeRecursively() override;

        void SetMpcSource(MPCPtr source);
        const MPCPtr GetMpcSource() const;
        void AddParameterUsage(const std::string& item);
        void DeleteParameterUsage(UInt32 index);
        void ClearParameterUsage();
        void RefreshParameterUsage(UsageChangedType type, const char* name = nullptr, SInt32 index = -1);
        void SetAllParameterUsages(const std::vector<std::string>& usages) { mParameterUsageView.Members = usages; }
        const ParameterCollectionUsages& GetAllParameterUsages() const { return mParameterUsageView; }

        auto& GetMaterialInstanceDefines() { return mDefines; }

        virtual void FillParameterCollectionValue(MPCPtr mpc) override;
        virtual void FillAllParameterCollectionValue() override;
        virtual bool HasMpcError() override;
        virtual void RegistAllMpc() override;
        virtual void UnregistAllMpc() override;

    private:
        void InitStates(const NameID& passID);

        void Clear();

        void UnparentInternal();
        void SetParentInternal(MaterialInterfacePtr parent);

        MaterialInterfacePtr mBaseMaterial;

        std::map<NameID, Material::PropertyType> mPropertyMap;
        std::map<NameID, Pass> mPasses;

        // for material v1.0
        MPCPtr mParameterCollectionSource;
        ParameterCollectionUsages mParameterUsageView;
        std::unordered_map<NameID, Material::PropertyType> mParameterUsageMap;

        MaterialInstanceDefines mDefines{};
    };
}}   // namespace cross::resource