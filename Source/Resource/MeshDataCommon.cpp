#include "MeshDataCommon.h"

namespace cross
{

//MeshStreamData::MeshStreamData()
//{
//    mIndexStream.mIs16BitIndex = false;   // always use 32 bit index
//}
//
//const VertexChannelAssetData* MeshStreamData::GetVertexChannelData(VertexChannel vertexChannel) const
//{
//    for (auto i = 0; i < mVertexChannelData.size(); i++)
//    {
//        if (mVertexChannelData[i].mVertexChannel == vertexChannel)
//            return &mVertexChannelData[i];
//    }
//    return nullptr;
//}
//
//VertexChannelAssetData* MeshStreamData::GetVertexChannelData(VertexChannel vertexChannel)
//{
//    for (auto i = 0; i < mVertexChannelData.size(); i++)
//    {
//        if (mVertexChannelData[i].mVertexChannel == vertexChannel)
//            return &mVertexChannelData[i];
//    }
//
//    return nullptr;
//}
//
//std::vector<UInt32> MeshStreamData::GetIndexData(UInt32 indexStart, UInt32 indexCount) const
//{
//    std::vector<UInt32> outData;
//    UInt16 indexStride = mIndexStream.mIs16BitIndex ? sizeof(UInt16) : sizeof(UInt32);
//    UInt8 const* indexDataStart = mIndexStream.mData.data() + indexStart * indexStride;
//
//    for (UInt32 i = 0; i < indexCount; i++)
//    {
//        UInt8 const* curIndexData = indexDataStart + i * indexStride;
//        if (mIndexStream.mIs16BitIndex)
//        {
//            outData.push_back(*reinterpret_cast<const UInt16*>(curIndexData));
//        }
//        else
//        {
//            outData.push_back(*reinterpret_cast<const UInt32*>(curIndexData));
//        }
//    }
//
//    return outData;
//}
//
//void MeshStreamData::AddIndexData(UInt32 index)
//{
//    UInt16 stride = 4;
//    UInt32 orgSize = static_cast<UInt32>(mIndexStream.mData.size());
//    mIndexStream.mData.resize(orgSize + stride);
//    memcpy(mIndexStream.mData.data() + orgSize, &index, stride);
//    mIndexStream.mCount++;
//}
//
//void MeshStreamData::AddIndexData(std::vector<UInt32> const& indexes)
//{
//    UInt16 stride = 4;
//    UInt32 orgSize = static_cast<UInt32>(mIndexStream.mData.size());
//    mIndexStream.mData.resize(orgSize + stride * indexes.size());
//    memcpy(mIndexStream.mData.data() + orgSize, indexes.data(), stride * indexes.size());
//    mIndexStream.mCount += static_cast<UInt32>(indexes.size());
//}

}
