//*********************************************************
//
// Copyright (c) Microsoft. All rights reserved.
// This code is licensed under the MIT License (MIT).
// THIS CODE IS PROVIDED *AS IS* WITHOUT WARRANTY OF
// ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING ANY
// IMPLIED WARRANTIES OF FITNESS FOR A PARTICULAR
// PURPOSE, MERCHANTABILITY, OR NON-INFRINGEMENT.
//
//*********************************************************
#pragma once

#include <DirectXMath.h>

#include <assert.h>
#include <memory>
#include <vector>
#include <set>
#include <unordered_map>

template <typename T>
struct InlineMeshlet
{
    struct PackedTriangle
    {
        uint32_t i0 : 10;
        uint32_t i1 : 10;
        uint32_t i2 : 10;
        uint32_t spare : 2;
    };

    std::vector<T>              UniqueVertexIndices;
    std::vector<PackedTriangle> PrimitiveIndices;

    DirectX::XMVECTOR BoundingSphere;
    std::vector<uint32_t> TriangleIndices;
    std::set<uint32_t> TriangleIndexSet;
    std::vector<T> ExternalEdges;
    uint32_t NumExternalEdge = 0;

    std::unordered_map<uint32_t, uint32_t> AdjacentMeshlet;
};

struct MeshletGroup
{
    DirectX::XMVECTOR BoundingSphere;
    std::vector<uint32_t> Children;             // meshlet children
    uint32_t MipLevel = 0;                      // hierarchy level
    uint32_t ParentIndex = (uint32_t)-1;        // parent group index
    std::vector<uint32_t> GroupChildren;        // group children
};
    
void Meshletize(
    uint32_t maxVerts, uint32_t maxPrims,
    const uint16_t* indices, uint32_t indexCount,
    const DirectX::XMFLOAT3* positions, uint32_t vertexCount,
    uint16_t vertexStart,
    std::vector<InlineMeshlet<uint16_t>>& output,
    std::vector<MeshletGroup>* meshletGroups,
    uint32_t& handleGroupIndex
);

void Meshletize(
    uint32_t maxVerts, uint32_t maxPrims,
    const uint32_t* indices, uint32_t indexCount,
    const DirectX::XMFLOAT3* positions, uint32_t vertexCount,
    uint32_t vertexStart,
    std::vector<InlineMeshlet<uint32_t>>& output,
    std::vector<MeshletGroup>* meshletGroups,
    uint32_t& handleGroupIndex
);
