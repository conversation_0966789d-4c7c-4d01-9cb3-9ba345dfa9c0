#pragma once
#include "ParticleEmitterResource.h"
#include "Resource/ParticleSystem/ParticleSystemInfo.h"

namespace cross::fx
{

class Resource_API CEMeta(Cli, Script) ParticleSystemResource : public Resource
{
public:
    FRIEND_WITH_REFLECTION_MODULE;
    friend class cross::ResourceManager;
    using ParticleEmitterResources = std::vector<ParticleEmitterResPtr>;
    using ParticleEmitterInfos = std::vector<ParticleEmitterInfo>;

protected:
    ParticleSystemResource() = default;

public:
    virtual ~ParticleSystemResource() = default;

    static int GetClassIDStatic() { return ClassID(ParticleSystemResource); }

    bool Serialize(SerializeNode&& s, const std::string& path) override;

    bool Deserialize(DeserializeNode const& s) override;

    const char* GetEmitterPath(UInt32 index) const;

    const char* GetParticleSystemPath() const;

    const ParticleEmitterInfo& GetEmitterInfo(UInt32 index) const;
    CEMeta(Cli)
    static ParticleSystemResource* FX_CreateParticleSystem();

public:
    inline void SetRandomSeed(UInt32 seed) { mRandomSeed = seed; }

    inline SInt32 GetRandomSeed() const { return mRandomSeed; }

    inline void SetAutoPlay(bool autoPlay) { mAutoPlay = autoPlay; }

    inline float GetAutoPlay() const { return mAutoPlay; }

    inline void AddEmitter(const ParticleEmitterResPtr& emitter) { mEmitterHandles.emplace_back(emitter); }

    inline void AddEmitter(const ParticleEmitterInfo& emitter) { mEmitterInfos.emplace_back(emitter); }

    inline void ClearEmitters() 
    { 
        mEmitterHandles.clear(); 
        mEmitterInfos.clear();
    }

    inline const UInt32 GetEmitterCount() const { return static_cast<UInt32>(mEmitterHandles.size()); }

    inline const ParticleEmitterResources& GetEmitterResources() const { return mEmitterHandles; }

    inline ParticleEmitterResources& GetEmitterResources() { return mEmitterHandles; }

    inline const ParticleEmitterInfos& GetEmitterInfos() const { return mEmitterInfos; }
    
    inline ParticleEmitterInfos& GetEmitterInfos() { return mEmitterInfos; }

private:
    bool mAutoPlay = false;
    UInt32 mRandomSeed = 0u;

    // The copied instance of emitter resource.
    ParticleEmitterResources mEmitterHandles;

    ParticleEmitterInfos mEmitterInfos;
};

}   // namespace cross::fx