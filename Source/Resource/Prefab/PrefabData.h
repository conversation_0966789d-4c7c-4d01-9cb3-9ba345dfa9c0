#pragma once
#include "EnginePrefix.h"
#include "Resource/resourceforward.h"
#include "CrossBase/Template/EnumClassFlags.h"
#include "CrossBase/ReferenceCountObject.h"
#include "Serialization/SerializeNode.h"
#include <unordered_map>
#include <unordered_set>
#include <shared_mutex>

namespace cross {

enum class Resource_API EntityChangedMode : UInt8
{
    ChangedNone = 0b00,
    ChangedOwn = 0b01,
    ChangedChild = 0b10,
    ChangedAll = 0b11
};
ENUM_CLASS_FLAGS(EntityChangedMode)

template<class T>
class RefObjectPtr
{
public:
    RefObjectPtr() = default;

    RefObjectPtr(T* ptr) : mRawPtr(ptr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    RefObjectPtr(const RefObjectPtr& sp)
        : mRawPtr(sp.mRawPtr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    RefObjectPtr(const RefObjectPtr&& sp)
        : mRawPtr(sp.mRawPtr)
    {
        if (mRawPtr)
            mRawPtr->IncreaseRefCount();
    }

    RefObjectPtr& operator=(const RefObjectPtr& src)
    {
        if (this != &src)
        {
            if (mRawPtr)
            {
                mRawPtr->DecreaseRefCount();
                if (mRawPtr->GetRefCount() == 0)
                    NotifyZeroReference();
            }
            mRawPtr = src.mRawPtr;
            if (mRawPtr)
                mRawPtr->IncreaseRefCount();
        }
        return *this;
    }

    ~RefObjectPtr()
    {
        if (mRawPtr)
        {
            mRawPtr->DecreaseRefCount();
            assert(mRawPtr->GetRefCount() >= 0);
            if (mRawPtr->GetRefCount() == 0)
                NotifyZeroReference();
            mRawPtr = nullptr;
        }
    }

    void reset(T* newObj = nullptr)
    {
        if (mRawPtr != newObj)
        {
            if (mRawPtr)
            {
                mRawPtr->DecreaseRefCount();
                assert(mRawPtr->GetRefCount() >= 0);
                if (mRawPtr->GetRefCount() == 0)
                    NotifyZeroReference();
                mRawPtr = nullptr;
            }
            mRawPtr = newObj;
            if (mRawPtr)
                mRawPtr->IncreaseRefCount();
        }
    }

    T* operator->() const { return mRawPtr; }
    T& operator*() const { return *mRawPtr; }
    bool operator!() const { return mRawPtr == nullptr; }
    operator bool() const { return mRawPtr != nullptr; }
    bool operator==(const RefObjectPtr& rhs) const { return mRawPtr == rhs.mRawPtr; }
    bool operator!=(const RefObjectPtr& rhs) const { return mRawPtr != rhs.mRawPtr; }
    bool operator==(T* rawPtr) const { return mRawPtr == rawPtr; }
    bool operator<(const RefObjectPtr& rhs) const { return mRawPtr < rhs.mRawPtr; }
    T* get() const { return mRawPtr; }
    bool operator()() const { return mRawPtr != nullptr; }
protected:
    RefObjectPtr shared_from_this() { return *this; }

private:
    void NotifyZeroReference() { delete mRawPtr; }

    T* mRawPtr{nullptr};
};

class PrefabEntityData;
class PrefabData;
class PrefabInsEntityData;
using PrefabEntityDataPtr = RefObjectPtr<PrefabEntityData>;
using PrefabDataPtr = RefObjectPtr<PrefabData>;
using PrefabInsEntityDataPtr = std::shared_ptr<PrefabInsEntityData>;
using PrefabEntityChangedMap = std::unordered_map<CrossUUID, EntityChangedMode>;
using PrefabEntityMap = std::unordered_set<CrossUUID>;

// this store the data that store data that store in file
class Resource_API PrefabEntityData : public ReferenceCountObject
{
public:
    PrefabEntityData(){};
    virtual ~PrefabEntityData() { mPrefabData = nullptr; }
    // @brief IsInherit
    bool IsInherit();
    virtual void IncreaseRefCount() override;
    virtual void DecreaseRefCount() override;
    virtual void IncreaseRefCount() const override;
    virtual void DecreaseRefCount() const override;

    void ClearData();


public:
    std::string             mPrefabId;
    CrossUUID               mPrefabEUID;
    std::string             mInheritPrefabId;
    CrossUUID               mInheritPrefabEUID;
    std::vector<CrossUUID>  mChildren;
    SerializeNode           mData;

private:
    PrefabData* mPrefabData = nullptr;

    friend class PrefabData;
};

class Resource_API PrefabData : public ReferenceCountObject
{
public:
    static constexpr const char* InheritRootGuid = "00000000000000000000000000000001";
    using PrefabEntityDataMap = std::map<CrossUUID, PrefabEntityDataPtr>;
    using DependMap = std::unordered_map<std::string, std::string>;

public:
    // @brief Destructor
    PrefabData(){};
    virtual ~PrefabData() { Clear(); };
    // @brief IsValid
    bool IsValid() { return GetEntityCount() > 0; };
    // @brief GetEntityCount
    UInt64 GetEntityCount();
    // @brief Push
    void Push(PrefabEntityDataPtr data);
    // @brief Add Inherit
    PrefabEntityDataPtr Add(const std::string& inheritPrefabId, CrossUUID inheritPrefabEuid);
    // @brief Pop
    void Pop(CrossUUID euid);
    // @brief Get
    PrefabEntityDataPtr Get(CrossUUID euid);
    // @brief Get
    std::vector<PrefabEntityDataPtr> Get(const std::string& prefabId, CrossUUID prefabEuid);
    // @brief Clear
    void Clear();
    // @brief RemoveByMap
    void RemoveByMap(const PrefabEntityMap& entityMap);


public:
    std::string         mID = "";
    CrossUUID           mRoot;
    PrefabEntityDataMap mEntityDataMap;
    std::shared_mutex   mReadWriteMutex;
#ifdef USE_PREFAB_EDITOR
    std::unordered_set<std::string> mDependList;
    std::unordered_set<std::string> mReDependList;
#endif
};

// be noted by the difference between PrefabEntityData
// this store the instance entity data (after edit)
class Resource_API PrefabInsEntityData
{
public:
    std::string     mPrefabId;
    CrossUUID       mPrefabEUID;
    SerializeNode   mData;
    bool            mIsDirty{false};
};

class Resource_API PrefabEntityDataPtrHash
{
public:
    size_t operator()(const PrefabEntityDataPtr& p) const { return std::hash<PrefabEntityData*>()(p.get()); }
};
}   // namespace cross