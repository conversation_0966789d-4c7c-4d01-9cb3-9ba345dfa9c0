#include "EnginePrefix.h"
#include "Resource/Texture/Texture.h"
#include "TextureUDIM.h"
#include "Resource/IResourceInterface.h"
#include "CrossBase/Threading/RenderingThread.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Allocator/FrameAllocator.h"
#include "CECommon/Common/FrameTickManager.h"
#include "CECommon/Common/FrameCounter.h"
#include "NativeGraphicsInterface/NGIManager.h"
#include "Resource/ResourceManager.h"
#include "NativeGraphicsInterface/NGIUtils.h"

namespace cross
{
    namespace resource
    {
        bool Texture::PostDeserialize()
        {
            return true;
        }

        std::shared_ptr<TextureResourceDataProvider> Texture::GetTextureDataPtr() {
                return mTextureSource;
        }

        bool Texture::Serialize()
        {
            if (mTextureSource)
            {
                bool needLoadData = mTextureSource->mData.size() <= 0;
                if (needLoadData)
                {
                    mTextureSource->ReleaseFileHandle();
                    mTextureSource->ReloadImageData();
                }
                mTextureSource->CreateTexAssetT();
                if (needLoadData)
                {
                    mTextureSource->mData.clear();
                }
                return Serialize(mTextureSource->mTexAssetT);
            }
            return false;
        }

        bool Texture::Serialize(const CrossSchema::TextureAssetT& inTextureAsset)
        {
            if (inTextureAsset.data.size() <= 0)
                return false;

            flatbuffers::FlatBufferBuilder texturebuilder(1024);
            auto mloc = CrossSchema::CreateTextureAsset(texturebuilder, &inTextureAsset);
            CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, static_cast<int32_t>(GetClassID()), (int32_t)inTextureAsset.data.size(), (int32_t)inTextureAsset.data.size());
            
            const std::string assetFileName = mAsset->GetName();
            const std::string oriAssetFileName = mAsset->GetOriName();

            auto mloc2 = CrossSchema::CreateResourceAsset(texturebuilder, &header, texturebuilder.CreateString(oriAssetFileName), CrossSchema::ResourceType::TextureAsset, mloc.Union());
            FinishResourceAssetBuffer(texturebuilder, mloc2);

            //CreateAsset(assetFileName);
            
            const int bufferSize = sizeof(uint8_t) * texturebuilder.GetSize();
            std::vector<char> fbBufVec;
            fbBufVec.resize(bufferSize);
            std::transform(texturebuilder.GetBufferPointer(), texturebuilder.GetBufferPointer() + texturebuilder.GetSize(), fbBufVec.begin(), [](char v) {return static_cast<uint8_t>(v);});
            auto customNode = GetCustomSerializeNode();
            
            return Resource::Serialize(fbBufVec, assetFileName, std::move(customNode));
        }

        void Texture::AddSelfItem(flatbuffers::FlatBufferBuilder& outBuilder, flatbuffers::Offset<void>& outSelfOffset, CrossSchema::ResourceType& outResType)
        {
            auto texOffset = CreateTextureAsset(outBuilder, &(mTextureSource->mTexAssetT));
            outSelfOffset = texOffset.Union();
            outResType = CrossSchema::ResourceType::TextureAsset;

           auto classID = ClassID(Texture2D);
           CrossSchema::ResourceHeader header(ASSET_MAGIC_NUMBER, 0, classID, (int32_t)mTextureSource->mTexAssetT.data.size(), (int32_t)mTextureSource->mTexAssetT.data.size());
           auto mloc2 = CrossSchema::CreateResourceAsset(outBuilder, &header, outBuilder.CreateString("linsh"), CrossSchema::ResourceType::TextureAsset, texOffset.Union());
           FinishResourceAssetBuffer(outBuilder, mloc2);
        }

        void Texture::DestroyRenderData(cross::FrameParam* frameParam)
        {
            DispatchRenderingCommandWithToken([&] {
                if (mGPUTex)
                    mGPUTex.reset();
            });
        }

        Texture::Texture()
            : mTextureSource(nullptr)
        {}

        Texture::Texture(const TextureInfo& createInfo, const char* pDebugName)
        {
            mTextureInfo = createInfo;

            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
            {
                mGPUTex = gResourceMgr.mCreateRenderObjectMgr->GetGpuTex(createInfo);
                Assert(mGPUTex);
            }

            DispatchRenderingCommandWithToken([this, info = mTextureInfo, name = std::string(pDebugName)] { mGPUTex->Initialize(info, name.c_str()); });

            mWide = createInfo.Width;
            mHigh = createInfo.Height;
            mDepth = createInfo.Depth;

            mTextureSource = std::shared_ptr<TextureResourceDataProvider>(new TextureResourceDataProvider("", {} , 0));
        }

        UInt32 Texture::GetPixelPitch(UInt32 width, TextureFormat textureformat, ColorSpace space)
        {
            const auto format = GetGraphicsFormat(textureformat, space);
            const auto texelBlock = GetFormatTexelBlockProperty(format);

            return ((width - 1) / texelBlock.Width + 1) * texelBlock.Size;
        }

        void Texture::UploadImage(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const UInt8* data, UInt32 dataSize, UInt32 rowPitch /*= 0*/, UInt32 slicePitch /*= 0*/)
        {
            auto* tempData = cross::EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator()->Allocate(dataSize, cross::FrameStage::FRAME_STAGE_GAME_RENDER);
            memcpy(tempData, data, dataSize);
            DispatchRenderingCommandWithToken([=]() { mGPUTex->UploadImage(arrayIndex, faceIndex, mipIndex, reinterpret_cast<UInt8*>(tempData), dataSize, rowPitch, slicePitch); });
        }

        void Texture::UploadImageRect(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const cross::Rect3u& uploadRect, const UInt8* data, UInt32 dataSize, UInt32 rowPitch /*= 0*/, UInt32 slicePitch /*= 0*/)
        {
            auto* tempData = cross::EngineGlobal::Inst().GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator()->Allocate(dataSize, cross::FrameStage::FRAME_STAGE_GAME_RENDER);
            memcpy(tempData, data, dataSize);
            DispatchRenderingCommandWithToken([=]() { mGPUTex->UploadImageRect(arrayIndex, faceIndex, mipIndex, uploadRect, reinterpret_cast<UInt8*>(tempData), dataSize, rowPitch, slicePitch); });
        }


        void Texture::UploadImageEditor(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, UInt8* data, UInt32 dataSize, UInt32 rowPitch, UInt32 slicePitch)
        {
            mEditorData.emplace_back();
            auto& buffer = mEditorData.back();
            buffer.resize(dataSize);
            memcpy(buffer.data(), data, dataSize);
            mEditorCmds.push_back([=, dataPtr = buffer.data()]() { UploadImage(arrayIndex, faceIndex, mipIndex, dataPtr, dataSize, rowPitch, slicePitch); });
            MarkRenderUpdate();
        }

        void Texture::UploadImageRectEditor(UInt32 arrayIndex, UInt32 faceIndex, UInt32 mipIndex, const cross::Rect3u& uploadRect, UInt8* data, UInt32 dataSize, UInt32 rowPitch, UInt32 slicePitch)
        {
            mEditorData.emplace_back();
            auto& buffer = mEditorData.back();
            buffer.resize(dataSize);
            memcpy(buffer.data(), data, dataSize);
            mEditorCmds.push_back([=, dataPtr = buffer.data()]() { UploadImageRect(arrayIndex, faceIndex, mipIndex, uploadRect, dataPtr, dataSize, rowPitch, slicePitch); });
            MarkRenderUpdate();
        }

        void Texture::UpdateRenderData(cross::FrameParam* fp)
        {
            for (auto& exe : mEditorCmds)
            {
                exe();
            }
            mEditorCmds.clear();
            mEditorData.clear();
        }


        TextureResourceDataProvider* Texture::GetTextureData()
        {
            return GetTextureDataPtr().get();
        }



        void cross::resource::Texture::SetEnableVTStreaming(bool enable)
        {
            mTextureInfo.EnableVirtualTextureStreaming = enable;
            if (mTextureSource)
            {
                mTextureSource->SetEnableVTStreaming(enable);
            }
        }

        void Texture::RequestStreaming(UInt32 requestedNumMips)
        {
            QUICK_SCOPED_CPU_TIMING("TextureRequestStreaming");

            Assert(cross::GetNGIDevice().NGISupportsMultithreading());
            Assert(cross::threading::TaskSystem::IsInGameThread());
            //Assert(mTextureInfo.EnableStreaming);
            Assert(!mMipSizes.empty());

            requestedNumMips = cross::Clamp(requestedNumMips, 1U, UInt32(mMipSizes.size()));

            IncreaseRefCount();
        }

        void Texture::AddEvent(cross::threading::TaskEventPtr event)
        {
            if (mInflightEvents.empty())
            {
                cross::PostTickUpdates::Get()->Add([handle = TypeCast<Texture>(SharedFromThis())]
                {
                    handle->ProcessPendingEvents();
                });
            }
            mInflightEvents.push_back(event);
            mEventCounter++;
        }

        void Texture::ProcessPendingEvents()
        {
            Assert(cross::threading::TaskSystem::IsInGameThread());

            QUICK_SCOPED_CPU_TIMING("ProcessPendingEvents");

            mInflightEvents.erase(std::remove_if(mInflightEvents.begin(), mInflightEvents.end(), [this](const auto& event)
            {
                if (event->Complete())
                {
                    const auto& streamingData = event->template GetReturnValue<StreamingData>();
                    if (streamingData.mRange.mDestination)
                    {
                        /*DispatchRenderingCommandWithToken([this, dataProvider = streamingData.mDataProvider, updates = streamingData.mUpdates, range = streamingData.mRange]
                            {
                                QUICK_SCOPED_CPU_TIMING("CompleteTextureStreaming");

                                mGPUTex->FinalizeStreamingData(dataProvider, *updates);

                                {
                                    QUICK_SCOPED_CPU_TIMING("DeleteTemporaries");

                                    delete dataProvider;
                                    delete updates;
                                }

                                cross::PostTickUpdates::Get()->Add([range]
                                {
                                    auto release = [range, frameNumber = cross::frame::GetRenderingFrameNumber()](auto self) -> void
                                    {
                                        if (frameNumber + 2 < cross::frame::GetRenderingFrameNumber())
                                        {
                                            DispatchRenderingCommandWithToken([range]
                                            {
                                                QUICK_SCOPED_CPU_TIMING("FreeRange");

                                                cross::StreamingScratchBuffer::Get()->Free(range);
                                            });
                                        }
                                        else
                                        {
                                            cross::PostTickUpdates::Get()->Add([self]
                                            {
                                                self(self);
                                            });
                                        }
                                    };

                                    release(release);
                                });

                                DecreaseRefCount();
                            });*/
                    }
                    else
                    {
                        {
                          /*  QUICK_SCOPED_CPU_TIMING("DeleteTemporaries");

                            delete streamingData.mDataProvider;
                            delete streamingData.mUpdates;*/
                        }

                        DecreaseRefCount();
                    }

                    return true;
                }

                return false;
            }), mInflightEvents.end());

            if (!mInflightEvents.empty())
            {
                cross::PostTickUpdates::Get()->Add([handle = TypeCast<Texture>(SharedFromThis())]
                { 
                        handle->ProcessPendingEvents();
                });
            }
        }

        bool Texture::TryUpload(StreamingData& streamingData)
        {
            //QUICK_SCOPED_CPU_TIMING("TryUpload");

            //auto range = mGPUTex->PrepareStreamingData(streamingData.mDataProvider, *streamingData.mUpdates);
            //if (range.mDestination)
            //{
            //    {
            //        QUICK_SCOPED_CPU_TIMING("CleanupDataProvider");

            //        {
            //            QUICK_SCOPED_CPU_TIMING("CleanImages");
            //            std::vector<CrossSchema::TextureAssetImage>().swap(static_cast<TextureResourceDataProvider_SpanData*>(streamingData.mDataProvider)->mImages);
            //        }

            //        /*{
            //            QUICK_SCOPED_CPU_TIMING("CleanData");
            //            std::vector<UInt8>().swap(streamingData.mDataProvider->mData);
            //        }*/
            //    }

            //    streamingData.mRange = range;
            //    return true;
            //}

            return false;
        }

        GPUTextureType cross::resource::Texture::GetGPUTextureTypeByInfo(bool enableMerge)
        {
            if (mTextureInfo.EnableVirtualTextureStreaming)
            {
                return mTextureInfo.UDIM ? GPUTextureType::UDIM : (enableMerge ? GPUTextureType::MVT : GPUTextureType::VT);
            }
            return GPUTextureType::Normal;
        }

        bool Texture::CreateGPUResource()
        {
            if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
            {
                cross::IGPUTexture* oldGpuTex = nullptr;
                if (mGPUTex.get() == nullptr || mGPUTex->GetType() != GetGPUTextureTypeByInfo())
                {
                    mGPUTex = gResourceMgr.mCreateRenderObjectMgr->GetGpuTex(mTextureInfo);
                }
                Assert(mGPUTex);

                IncreaseRefCount();
                // threading::Async([this](auto&) {
                // auto task = threading::Dispatch<threading::ThreadID::TaskThread>([this](auto&) {

                {
                    QUICK_SCOPED_CPU_TIMING("CreateGPUResource");

                    if (mTextureInfo.UDIM)
                    {
                        mGPUTex->Initialize(mUDims);
                    }
                    else
                    {
                        // threading::Async([this](auto&) {
                        mGPUTex->Initialize(mTextureSource);
                        //});
                    }
                    if (mGPUTex->GetNGITexture())
                    {
                        mGPUTex->GetNGITexture()->SetDebugName(GetName().c_str());
                    }
                }

                DecreaseRefCount();

                // task->WaitForCompletion();
                mTextureSource->GetImageSize(0, mWide, mHigh, mDepth);
            }
            return true;
        }


        cross::IGPUTexture* Texture::GetTextureR()
        {
            return mGPUTex.get();
         }

        cross::NGITexture* Texture::GetNGITexture() const
        {
            return mGPUTex->GetNGITexture();
        }

        cross::NGITextureView* Texture::GetNGITextureView() const
        {
            return mGPUTex->GetNGITextureView();
        }


        TextureResourceDataProvider::~TextureResourceDataProvider() 
        {
            Reset();
        }

        bool TextureResourceDataProvider::Deserialize(cross::FBSerializer const& s, bool useStream)
        {      
            mOffset = static_cast<UInt32>(s.InitialOffset());


            if (!TextureResource::Deserialize(s, useStream))
            {
                return false;
            }
            if (!mHeader.mImportSet.empty())
            {
                SerializeNode node = SerializeNode::ParseFromJson(mHeader.mImportSet);
                if (node.HasMember("Type"))
                {
                    mResourceInfo.Type = static_cast<TextureType>(node["Type"].AsUInt32());
                }
            }
            if (mResourceInfo.MipCount == 1 && mResourceInfo.Dimension == TextureDimension::Tex2D && !mVTStreaming)
            {
                LOG_ERROR("Texture2D : {} has no mip maps, Please Check", this->GetPath());
            }
            if (useStream || mVTStreaming)
            {
                // in ideal case, we should decode compressed format to color
                // todo
                auto data = StreaminRequestIO(mResourceInfo.MipCount - 1);
                mFallbackColor = *reinterpret_cast<UInt32*>(data.mData.data());
                ReleaseFileHandle();
            }
            return true;
        }

        TextureInfo TextureResourceDataProvider::GetTextureInfo() const
        {
            TextureInfo Info =
            {
                mResourceInfo.Dimension,
                mResourceInfo.Format,
                mResourceInfo.ColorSpace,
                mResourceInfo.Width,
                mResourceInfo.Height,
                mResourceInfo.Depth,
                mResourceInfo.MipCount,
                mResourceInfo.ArraySize,
                1,
                mResourceInfo.MipCount,
                mRequestedNumMips ? cross::Clamp(mRequestedNumMips, 1U, mResourceInfo.MipCount) : mResourceInfo.MipCount,
                mResourceInfo.MipBias,
                mResourceInfo.EnableStreaming,
                mResourceInfo.VirtualTextureStreaming,
            };

            return Info;
        }

        TextureFormat TextureResourceDataProvider::GetTextureFormat() const
        {
            return mResourceInfo.Format;
        }

        UInt32 TextureResourceDataProvider::GetImageCount() const
        {
            return mResourceInfo.MipCount * mResourceInfo.ArraySize;
        }

        UInt32 TextureResourceDataProvider::GetImageIndex(UInt32 mipLevel, UInt32 faceIndex, [[maybe_unused]] UInt32 arrayIndex) const
        {
            return TextureResource::GetImageIndex(mipLevel, faceIndex, arrayIndex);
        }

        void TextureResourceDataProvider::GetImageLevels(UInt32 imageIndex, UInt32& mipLevel, UInt32& faceIndex, UInt32& arrayIndex) const
        {
            TextureResource::GetImageLevels(imageIndex, mipLevel, faceIndex, arrayIndex);
        }

        void TextureResourceDataProvider::GetImageSize(UInt32 imageIndex, UInt32& wide, UInt32& high, UInt32& depth) const
        {
            wide = mImages[imageIndex].width();
            high = mImages[imageIndex].height();
            depth = mImages[imageIndex].depth();
        }

        UInt8* TextureResourceDataProvider::GetImageData(UInt32 imageIndex)
        {
            return TextureResource::GetImageData(imageIndex);
        }

        const UInt8* TextureResourceDataProvider::GetImageData(UInt32 imageIndex) const
        {
            return TextureResource::GetImageData(imageIndex);
        }

        UInt32 TextureResourceDataProvider::GetImageDataByteSize(UInt32 imageIndex) const
        {
            return mImages[imageIndex].databytesize();
        }

        UInt32 TextureResourceDataProvider::GetImagePitchByteSize(UInt32 imageIndex) const
        {
            return mImages[imageIndex].rowpitch();
        }

        void TextureResourceDataProvider::OnDataUploaded()
        {
            if (!mResourceInfo.ResidentInCPU)
            {
                mData.clear();
                auto empty = std::vector<UInt8>(0);
                mData.swap(empty);   // deallocate memory
            }
        }
        bool TextureResourceDataProvider::IsStreamingFileAlive()
        {
            if (mArchive && !mArchive->GetMMapedFile()->IsClosed())
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        StreamingData TextureResourceDataProvider::StreaminRequestIO(UInt32 RequestMip) 
        {
            if (!mSerializer)
            {
                mArchive = std::unique_ptr<MemoryMappingArchive>(reinterpret_cast<MemoryMappingArchive*>(ResourceAssetManager::Instance().MemoryMapFile(mAssetPath.c_str(), filesystem::MapRange::WholeFile)));
                mSerializer = std::make_unique<cross::FBSerializer>(*mArchive, mOffset);
            }
            StreamingData Data;
           
            UInt32 startoffset = mImages[RequestMip].dataoffset();
            UInt32 bytesize = mImages[RequestMip].databytesize();
            auto texturetable = static_cast<const flatbuffers::Table*>(CrossSchema::GetResourceAsset(mSerializer->GetArchive().Data() + mSerializer->InitialOffset())->resource());
            auto fbvector = texturetable->GetPointer<flatbuffers::Vector<UInt8>*>(CrossSchema::TextureAsset::VT_DATA);  
            auto* data = fbvector->data() + startoffset;
            auto size = bytesize;
            Data.mData.assign(data, size);
            Data.mResourceInfo.ColorSpace = mResourceInfo.ColorSpace;
            Data.mResourceInfo.Depth = 1;
            Data.mResourceInfo.ArraySize = 1;
            Data.mResourceInfo.Dimension = mResourceInfo.Dimension;
            Data.mResourceInfo.Format = mResourceInfo.Format;
            Data.mResourceInfo.Width = mImages[RequestMip].width();
            Data.mResourceInfo.Height = mImages[RequestMip].height();
            return Data;
        }

        void TextureResourceDataProvider::ReloadImageData()
        {
            auto archive = reinterpret_cast<MemoryMappingArchive*>(gResourceAssetMgr.MemoryMapFile(mAssetPath.c_str(), filesystem::MapRange::WholeFile));
            FBSerializer serializer(*archive, mOffset);
            auto texturetable = static_cast<const flatbuffers::Table*>(CrossSchema::GetResourceAsset(serializer.GetArchive().Data() + serializer.InitialOffset())->resource());
            serializer.Read(texturetable, mData, CrossSchema::TextureAsset::VT_DATA);
            delete archive;
        }

        void TextureResourceDataProvider::ReleaseFileHandle() 
        {
            if (mSerializer)
            {
                mSerializer.reset();
                mArchive.reset();
            }
        }

        void TextureResourceDataProvider::SetEnableVTStreaming(bool enable) 
        {
            if (mVTStreaming == enable)
                return;
            if (enable)
            {
                if (mData.size() <= 0)
                {
                    ReloadImageData();
                }
                ReleaseFileHandle();
            }
            mVTStreaming = enable;
            mTexAssetT.vtstreaming = enable;
            mResourceInfo.VirtualTextureStreaming = enable;
        }

        void TextureResourceDataProvider::Reset()
        {
            std::unique_lock<std::shared_mutex> lock(mInflightEventMutex);
            mInflightEvents.clear();
            mData.clear();
            if (mSerializer && mArchive)
            {
                mArchive->GetMMapedFile()->Close();
                mArchive.reset();
                mSerializer.reset();
            }
        }

        void TextureResourceDataProvider::Reload(const ResourceMetaHeader& header, UInt32 requestedNumMips)
        {
            Reset();
            mHeader = header;
            mRequestedNumMips = requestedNumMips;
        }
    }//namespace resource
}//namespace cross
