#pragma once

#include "Resource/Texture/Texture.h"
#include "CEMetaMacros.h"

namespace cross
{
    namespace resource
    {
        class CEMeta(Script) Texture3D : public cross::resource::Texture
        {
            FRIEND_WITH_REFLECTION_MODULE;
        protected:
            Resource_API void DestroyRenderData(cross::FrameParam * frameParam) override;
        public:
            Resource_API Texture3D(){};
            Resource_API Texture3D(::TextureFormat format, ColorSpace colorSpace, UInt32 wide, UInt32 high, UInt32 depth, UInt32 mipCount = 0);
            Resource_API bool Deserialize(cross::FBSerializer const& s) override;

            bool PostDeserialize() override;
            static int GetClassIDStatic() { return ClassID(Texture3D); }

            virtual ::TextureDimension GetDimension() const override { return ::TextureDimension::Tex3D; }

            void UploadImage(UInt32 mipIndex, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0);
            void UploadImageRect(UInt32 mipIndex, cross::Rect3u uploadRect, UInt8* data, UInt32 dataSize, UInt32 rowPitch = 0, UInt32 slicePitch = 0);

            //virtual void UploadTexture(int mipLevel = 0) override;

        private:
        };
    
    }//namespace resource
}//namespace cross

