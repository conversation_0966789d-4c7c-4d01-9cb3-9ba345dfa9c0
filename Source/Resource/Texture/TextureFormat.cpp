//#include "EnginePrefix.h"
//#include "TextureFormat.h"
//
//// NOTE: match indices in TexFormat* enum!
//
//const static int s_cnTextureByteTable[TexFormatTotalCount] =
//{
//	0,
//	1,	// TexFormatAlpha8
//	2,	// TexFormatARGB4444
//	3,  // TexFormatRGB24
//	4,	// TexFormatRGBA32
//	4,	// TexFormatARGB32
//	16, // TexFormatARGBFloat
//	2,  // TexFormatRGB565
//	3,  // TexFormatBGR24
//	2,  // TexFormatAlphaLum16
//	0,  // TexFormatDXT1 (Depends on width, height, depth)
//	0,  // TexFormatDXT3 (Depends on width, height, depth)
//	0,  // TexFormatDXT5 (Depends on width, height, depth)
//	2,	// TexFormatRGBA4444
//	4,	// TexFormatBGRA32
//	0,	// TexReserved1
//	0,	// TexReserved2
//	8,	// TexFormatRGBAHalf
//	0,	// TexReserved4
//	0,	// TexReserved5
//};
//
//UInt32 GetBytesFromTextureFormat(TextureFormat inFormat)
//{
//	AssertMsg(inFormat < TexFormatDXT1 || inFormat == TexFormatBGRA32 || inFormat == TexFormatRGBAHalf || inFormat == TexFormatRGBA4444, "Invalid texture format: %d", (int)inFormat);
//	return s_cnTextureByteTable[inFormat];
//}
//
//UInt32 GetMaxBytesPerPixel(TextureFormat inFormat)
//{
//	Assert(GetBytesFromTextureFormat(inFormat) <= (UInt32)s_cnTextureByteTable[TexFormatARGBFloat]);
//	return s_cnTextureByteTable[TexFormatARGBFloat];
//}
//
//int GetRowBytesFromWidthAndFormat(int width, TextureFormat inFormat)
//{
//	AssertMsg(inFormat < TexFormatDXT1 || inFormat == TexFormatBGRA32 || inFormat == TexFormatRGBAHalf || inFormat == TexFormatRGBA4444, "Invalid texture format: %d", (int)inFormat);
//	return GetBytesFromTextureFormat(inFormat) * width;
//}
//
//bool IsValidTextureFormat(TextureFormat format)
//{
//	if ((format >= TexFormatAlpha8 && format <= TexFormatRGBA4444) ||
//		IsCompressedPVRTCTextureFormat(format) ||
//		IsCompressedETCTextureFormat(format) ||
//		IsCompressedETC2TextureFormat(format) ||
//		IsCompressedASTCTextureFormat(format) ||
//		IsCompressedEACTextureFormat(format) ||
//		format == TexFormatBGRA32 || format == TexFormatRGBAHalf)
//		return true;
//	else
//		return false;
//}
//
//int GetTextureSizeAllowedMultiple(TextureFormat format)
//{
//	if (IsCompressedDXTTextureFormat(format) || IsCompressedETCTextureFormat(format)
//		|| IsCompressedETC2TextureFormat(format) || IsCompressedEACTextureFormat(format))
//		return 4;
//	else
//		return 1;
//}
//
//int GetMinimumTextureMipSizeForFormat(TextureFormat format)
//{
//	if (format == TexFormatPVRTC_RGBA2 || format == TexFormatPVRTC_RGB2)
//		return 16;
//	else if (format == TexFormatPVRTC_RGBA4 || format == TexFormatPVRTC_RGB4)
//		return 8;
//	else if (IsCompressedETCTextureFormat(format) || IsCompressedETC2TextureFormat(format) || IsCompressedEACTextureFormat(format))
//		return 4;
//	else if (IsCompressedASTCTextureFormat(format))
//		return 1;
//	else if (IsCompressedDXTTextureFormat(format))
//		return 4;
//	else if (IsCompressedBC45TextureFormat(format))
//		return 4;
//	else if (IsCompressedBC67TextureFormat(format))
//		return 4;
//	else if (IsCompressedCrunchTextureFormat(format))
//		return 4;
//	else
//		return 1;
//}
//
//bool IsAlphaOnlyTextureFormat(TextureFormat format)
//{
//	return format == TexFormatAlpha8;
//}
//
//TextureFormat ConvertToAlphaTextureFormat(TextureFormat format)
//{
//	if (format == TexFormatRGB24 || format == TexFormatBGR24)
//		return TexFormatARGB32;
//	else if (format == TexFormatRGB565)
//		return TexFormatARGB4444;
//	else if (format == TexFormatDXT1)
//		return TexFormatDXT5;
//	else if (format == TexFormatPVRTC_RGB2)
//		return TexFormatPVRTC_RGBA2;
//	else if (format == TexFormatPVRTC_RGB4)
//		return TexFormatPVRTC_RGBA4;
//	else if (format == TexFormatETC_RGB4)
//		return TexFormatARGB4444;
//	else if (format == TexFormatETC2_RGB)
//		return TexFormatETC2_RGBA8;
//	else
//		return format;
//}
//
//bool HasAlphaTextureFormat(TextureFormat format)
//{
//	return format == TexFormatAlpha8 || format == TexFormatARGB4444 || format == TexFormatRGBA4444 || format == TexFormatRGBA32 || format == TexFormatARGB32
//		|| format == TexFormatARGBFloat || format == TexFormatDXT5 || format == TexFormatDXT3 || format == TexFormatBC7
//#if ENABLE_CRUNCH_TEXTURE_COMPRESSION
//		|| format == TexFormatDXT5Crunched
//		|| format == TexFormatETC2_RGBA8Crunched
//#endif
//		|| format == TexFormatRGBAHalf || format == TexFormatRGBAFloat
//		|| format == TexFormatPVRTC_RGBA2 || format == TexFormatPVRTC_RGBA4 || format == TexFormatBGRA32
//		|| format == TexFormatETC2_RGBA1 || format == TexFormatETC2_RGBA8
//		|| (format >= TexFormatASTC_4x4 && format <= TexFormatASTC_12x12) || (format >= TexFormatASTC_HDR_4x4 && format <= TexFormatASTC_HDR_12x12);
//}
////
////bool IsDepthRTFormat(RenderTextureFormat format)
////{
////	return format == RTFormatDepth || format == RTFormatShadowMap;
////}
////
////bool IsHalfRTFormat(RenderTextureFormat format)
////{
////	return format == RTFormatARGBHalf || format == RTFormatRGHalf || format == RTFormatRHalf || IsDepthRTFormat(format);
////}
//
//const char* GetCompressionTypeString(TextureFormat format)
//{
//	switch (format)
//	{
//	case TexFormatDXT1: return "DXT1";
//	case TexFormatDXT3: return "DXT3";
//	case TexFormatDXT5: return "DXT5";
//	case TexFormatBC4:  return "BC4";
//	case TexFormatBC5:  return "BC5";
//	case TexFormatBC6H: return "BC6H";
//	case TexFormatBC7:  return "BC7";
//	case TexFormatASTC_4x4:    return "ASTC 4x4";
//	case TexFormatASTC_5x5:    return "ASTC 5x5";
//	case TexFormatASTC_6x6:    return "ASTC 6x6";
//	case TexFormatASTC_8x8:    return "ASTC 8x8";
//	case TexFormatASTC_10x10:  return "ASTC 10x10";
//	case TexFormatASTC_12x12:  return "ASTC 12x12";
//	case TexFormatETC_RGB4: return "ETC1";
//	case TexFormatETC2_RGB:
//	case TexFormatETC2_RGBA1:
//	case TexFormatETC2_RGBA8: return "ETC2";
//	case TexFormatEAC_R:
//	case TexFormatEAC_R_SIGNED:
//	case TexFormatEAC_RG:
//	case TexFormatEAC_RG_SIGNED: return "EAC";
//	case TexFormatPVRTC_RGB2:
//	case TexFormatPVRTC_RGBA2:
//	case TexFormatPVRTC_RGB4:
//	case TexFormatPVRTC_RGBA4: return "PVRTC";
//	case TexFormatDXT1Crunched:
//	case TexFormatDXT5Crunched:
//	case TexFormatETC_RGB4Crunched:
//	case TexFormatETC2_RGBA8Crunched: return "Crunch";
//	default:
//		return "Uncompressed";
//	}
//}
//
//const char* GetTextureFormatString(TextureFormat format)
//{
//	switch (format)
//	{
//	case TexFormatAlpha8:      return "Alpha 8";
//	case TexFormatR8:          return "R 8";
//	case TexFormatRG16:        return "RG 16";
//	case TexFormatARGB4444:    return "ARGB 16 bit";
//	case TexFormatRGBA4444:    return "RGBA 16 bit";
//	case TexFormatRGB24:       return "RGB 24 bit";
//	case TexFormatRGBA32:      return "RGBA 32 bit";
//	case TexFormatARGB32:      return "ARGB 32 bit";
//	case TexFormatARGBFloat:   return "ARGB float";
//	case TexFormatRGB565:      return "RGB 16 bit";
//	case TexFormatBGR24:       return "BGR 24 bit";
//	case TexFormatR16:  return "R 16 bit";
//	case TexFormatDXT1:        return "RGB Compressed DXT1";
//	case TexFormatDXT3:        return "RGBA Compressed DXT3";
//	case TexFormatDXT5:        return "RGBA Compressed DXT5";
//	case TexFormatBC4:         return "R Compressed BC4";
//	case TexFormatBC5:         return "RG Compressed BC5";
//	case TexFormatBC6H:        return "RGB HDR Compressed BC6H";
//	case TexFormatBC7:         return "RGB(A) Compressed BC7";
//	case TexFormatBGRA32:      return "BGRA 32 bit";
//	case TexFormatRHalf:       return "R Half";
//	case TexFormatRGHalf:      return "RG Half";
//	case TexFormatRGBAHalf:    return "RGBA Half";
//	case TexFormatRFloat:      return "R Float";
//	case TexFormatRGFloat:     return "RG Float";
//	case TexFormatRGBFloat:    return "RGB Float";
//	case TexFormatRGBAFloat:   return "RGBA Float";
//	case TexFormatRGB9e5Float: return "RGB9e5 32 bit Shared Exponent Float";
//
//		// gles
//	case TexFormatPVRTC_RGB2: return "RGB Compressed PVRTC 2 bits";
//	case TexFormatPVRTC_RGBA2: return "RGBA Compressed PVRTC 2 bits";
//	case TexFormatPVRTC_RGB4: return "RGB Compressed PVRTC 4 bits";
//	case TexFormatPVRTC_RGBA4: return "RGBA Compressed PVRTC 4 bits";
//	case TexFormatETC_RGB4: return "RGB Compressed ETC 4 bits";
//
//	case TexFormatETC2_RGB: return "RGB Compressed ETC2 4 bits";
//	case TexFormatETC2_RGBA1: return "RGB + 1-bit Alpha Compressed ETC2 4 bits";
//	case TexFormatETC2_RGBA8: return "RGBA Compressed ETC2 8 bits";
//	case TexFormatEAC_R: return "R Compressed EAC 4 bit";
//	case TexFormatEAC_R_SIGNED: return "Signed R Compressed EAC 4 bit";
//	case TexFormatEAC_RG: return "RG Compressed EAC 8 bit";
//	case TexFormatEAC_RG_SIGNED: return "Signed RG Compressed EAC 8 bit";
//
//#define STR_(x) #x
//#define STR(x) STR_(x)
//#define DO_ASTC(bx, by) \
//        case TexFormatASTC_##bx##x##by : return "RGB(A) Compressed ASTC " STR(bx) "x" STR(by) " block"; \
//        case TexFormatASTC_HDR_##bx##x##by: return "RGB(A) Compressed ASTC HDR " STR(bx) "x" STR(by) " block"
//
//		DO_ASTC(4, 4);
//		DO_ASTC(5, 5);
//		DO_ASTC(6, 6);
//		DO_ASTC(8, 8);
//		DO_ASTC(10, 10);
//		DO_ASTC(12, 12);
//
//#undef DO_ASTC
//
//	case TexFormatDXT1Crunched: return "RGB Crunched DXT1";
//	case TexFormatDXT5Crunched: return "RGBA Crunched DXT5";
//	case TexFormatETC_RGB4Crunched: return "RGB Crunched ETC";
//	case TexFormatETC2_RGBA8Crunched: return "RGBA Crunched ETC2";
//
//		// video
//	case TexFormatYUY2:        return "YUY2 Video Picture Encoding";
//
//	default:
//		return "Unsupported";
//	}
//}
//
//const char* GetTextureColorSpaceString(TextureColorSpace colorSpace)
//{
//	switch (colorSpace)
//	{
//	case TextureLinearSpace: return "Linear";
//	case TextureGammaSpace: return "sRGB";
//	default: return "Unsupported";
//	}
//}
//
//TextureColorSpace ColorSpaceToTextureColorSpace(ColorSpace colorSpace)
//{
//	if (colorSpace == GammaSpace)
//		return TextureGammaSpace;
//	else
//		return TextureLinearSpace;
//}
