#pragma once
#include "CEAnimation/AnimBase.h"

namespace cross::anim {

class Animator;
class AnimGraph;
class AnimStoryBoard;
class AnimStateGraph;
class AnimFsm;
class ParamSymbolTable;

class AnimAssembler
{
public:
    // Can be used to assemble LayerGraph
    static bool AssembleStoryBoard(AnimStoryBoard* outStoryBoard, const DeserializeNode& inStoryBoardJson);

protected:
    static bool AssembleFSM(AnimFsm* outFSM, const DeserializeNode& inFSMJson, const AnimStoryBoard* inOwnerStoryBoard);

    static bool AssembleStateGraph(AnimStateGraph* outStateGraph, const DeserializeNode& inStateGraphJson, const AnimStoryBoard* inOwnerStoryBoard);

private:
    // Assemble a single graph(StoryBoard, LayerGraph, StateGraph) and record GetPoseNode & SavePoseNode for inStoryBoard(may be StoryBoard or LayerGraph)
    static bool AssembleGraphImp(AnimGraph* outGraph, const DeserializeNode& inGraphJson, const AnimStoryBoard* inOwnerStoryBoard);

    // Called after all nodes and links of this graph in Json have been produced.
    // Get real link from handle for each node.
    // Get real source and target node from handle for each link.
    static bool PostAssembleGraph(AnimGraph* outGraph, const DeserializeNode& inNodesJson, const DeserializeNode& inLinksJson);

    // Build CachePoseLink between GetPoseNode and SavePoseNode for this StoryBoard or LayerGraph.
    static void BuildGetSavePoseNodeLink(AnimStoryBoard* outStoryBoard);
};

}   // namespace cross::anim
