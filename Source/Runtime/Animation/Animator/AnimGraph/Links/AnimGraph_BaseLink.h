#pragma once
#include "Resource/BaseClasses/ClassIDs.h"
#include "Resource/Resource.h"
#include "CrossBase/Template/TypeSafeHandle.hpp"
#include "CEAnimation/AnimContexts.h"
#include "CEAnimation/AnimBase.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"

namespace cross::anim {

class AnimGraph;
class AnimFsm_Transition;

using time::TrackUnWrapperH;

/* A basic pose class link to another anim graph node */
class AnimGraph_BaseLink
{
public:
    AnimGraph_BaseLink() = default;
    virtual ~AnimGraph_BaseLink() = default;

    AnimGraph_BaseLink(const AnimGraph* inOwner, const CEName& inLinkName, GraphLinkH inLinkH, GraphNodeH inTargetNodeID, GraphNodeH inSourceNodeID)
        : mOwnerGraph(inOwner)
        , mName(inLinkName)
        , mLinkH(inLinkH)
        , mTargetNodeH(inTargetNodeID)
        , mSourceNodeH(inSourceNodeID)
    {}

    /* Only initialize once */
    virtual void Initialize(const AnimInitContext& inContext);

    virtual void Update(const AnimUpdateContext& inContext);

    virtual void GatherDebugData(GraphNodeDebugData& debugData);

    virtual void Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) {}

    virtual void PostUpdate();

    /* Will be called by StateGraph::Reset() when turn to new state */
    virtual void Reset();

    virtual void GatherSpecificFormulaFunction(AnimFsm_Transition* inTransitionPtr) const;

    const AnimGraph* GetOwnerGraph() const
    {
        return mOwnerGraph;
    }

protected:
    /* 
     * Get real source and target node from handle for this link.
     * Called in AnimAssembler::PostAssembleGraph() after mOwnerGraph has been assembled 
     */
    virtual bool PostAssemble();

    /* Try to establish the linked node pointer. */
    bool AttemptLink();

protected:
    /* Serialized in asset
     *	The source linked node ID, used for debug visualization.
     */
    GraphNodeH mSourceNodeH = GraphNodeH::InvalidHandle();

    /* Serialized in asset
     *	The target linked node ID, used to build the non-serialized pointer map.
     */
    GraphNodeH mTargetNodeH = GraphNodeH::InvalidHandle();

protected:
    /* The non serialized cached target pointer. */
    class AnimGraph_BaseNode* mTargetNode = nullptr;

    /* The non serialized cached source pointer. */
    class AnimGraph_BaseNode* mSourceNode = nullptr;

    /* Flag to prevent reentry when dealing with circular trees. */
    bool bProcessedCurState = false;

protected:
    /* Serialized in asset
     *	The name of this link, used to identify the output of this graph. Filled in by the compiler, propagated from the parent graph.
     */
    CEName mName{""};

    GraphLinkH mLinkH = GraphLinkH::InvalidHandle();

    /* The anim graph who owns this link */
    const AnimGraph* mOwnerGraph = nullptr;

    friend class AnimAssembler;
};
}   // namespace cross::anim
