#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/Animator.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_LocalPoseLink.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraphDefine.h"

namespace cross::anim {

REGISTER_LINK_TYPE(AnimGraph_LocalPoseLink)

void AnimGraph_LocalPoseLink::Evaluate(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext)
{
#if DEBUG_ANIMATION_SYSTEM
    LOG_INFO("AnimGraph_LocalPoseLink: Evaluate {} already in progress", typeid(*this).name());
#endif
    Assert(bProcessedCurState == false && "Evaluate already in progress, circular link for AnimStoryBoard");

    // reentry prevent
    AnimGuardValue<bool>(bProcessedCurState, true);

    // NO reentry prevent for evaluate
    if (mTargetNode == nullptr)
        return;

    auto nextContext = inContext.Move(mTargetNodeH);
    mTargetNode->EvaluateLocalSpace(outPose, nextContext);
}

AnimGraph_BaseLink* AnimGraph_LocalPoseLink::Produce(const AnimGraph* inOwner, GraphLinkH inLinkH, const DeserializeNode& inLinkJson, const CENameMap<CEName, size_t>& inNodeNameToIndexMap)
{
    Assert(inLinkJson.HasMember(ANIMGRAPH_LINK_TARGET));
    Assert(inLinkJson.HasMember(ANIMGRAPH_LINK_SOURCE));
    GraphNodeH targetH, sourceH;

    if (auto itr = inNodeNameToIndexMap.find(CEName{inLinkJson[ANIMGRAPH_LINK_TARGET].AsString().c_str()}); itr != inNodeNameToIndexMap.end())
    {
        targetH = {itr->second};
    }

    if (auto itr = inNodeNameToIndexMap.find(CEName{inLinkJson[ANIMGRAPH_LINK_SOURCE].AsString().c_str()}); itr != inNodeNameToIndexMap.end())
    {
        sourceH = {itr->second};
    }

    CEName linkName{inLinkJson["Name"].AsString().c_str()};

    return new AnimGraph_LocalPoseLink(inOwner, linkName, inLinkH, targetH, sourceH);
}

}   // namespace cross::anim