#pragma once
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_BaseNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Links/AnimGraph_RootPoseLink.h"

namespace cross::anim {

/* Convert pose from Root space into Local space */
class AnimGraph_SpaceRootToLocalNode : public AnimGraph_BaseNode
{
public:
    AnimGraph_SpaceRootToLocalNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
        : AnimGraph_BaseNode(inAnimGraph, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
    {}

    virtual ~AnimGraph_SpaceRootToLocalNode() = default;

    //// ~AnimGraph_BaseNode Interface Begin~
    ////

    virtual void Initialize(const AnimInitContext& inContext) override;

    virtual void Update(const AnimUpdateContext& inContext) override;

    virtual void EvaluateLocalSpace(RootSpacePose& outPose, AnimExtractContext<TrackUnWrapperH>& inContext) override;

    ////
    //// ~AnimGraph_BaseNode Interface End~

public:
    static AnimGraph_BaseNode* Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
        const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap);

protected:
    inline AnimGraph_RootPoseLink* PoseLink()
    {
        Assert(mPoseLinks.size() > 0);
        return TYPE_CAST(AnimGraph_RootPoseLink*, mPoseLinks[0]);
    }

    friend class AnimAssembler;
};

}   // namespace cross::anim