#include "EnginePrefix.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/Parameters/AnimGraph_ParamImplNode.h"
#include "Runtime/Animation/Animator/AnimGraph/Nodes/AnimGraph_NodeUtils.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimGraph.h"

namespace cross::anim {

REGISTER_NODE_TYPE(AnimGraph_ParamImplNode)

AnimGraph_ParamImplNode::AnimGraph_ParamImplNode(AnimGraph const* inAnimGraph, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
    : AnimGraph_ParamBaseNode(inAnimGraph, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap)
{
    Assert(inNodeJson.HasMember(ANIMGRAPH_NODE_IN_PARAMS));
    Assert(inNodeJson[ANIMGRAPH_NODE_IN_PARAMS].Size() == 1);

    // Get param handle this ParamImplNode uses
    auto inParamName = CEName{inNodeJson[ANIMGRAPH_NODE_IN_PARAMS][0].AsString().c_str()};
    Assert(inParamNameToIndexMap.find(inParamName) != inParamNameToIndexMap.end());
    mParamHandle = {inParamNameToIndexMap.at(inParamName)};

    std::string returnTypeStr = "";
    ExtractMember(returnTypeStr, inNodeJson, ANIMGRAPH_NODE_PARAMIMPL_RETURN_TYPE);
    mReturnType = ParamMode::Operate(returnTypeStr);
}

AnimGraph_BaseNode* AnimGraph_ParamImplNode::Produce(const AnimGraph* inOwner, GraphNodeH inNodeH, const DeserializeNode& inNodeJson, 
    const CENameMap<CEName, size_t>& inLinkNameToIndexMap, const CENameMap<CEName, size_t>& inParamNameToIndexMap)
{
    return new AnimGraph_ParamImplNode(inOwner, inNodeH, inNodeJson, inLinkNameToIndexMap, inParamNameToIndexMap);
}

bool AnimGraph_ParamImplNode::PostAssemble(const DeserializeNode& inNodeJson, const std::vector<AnimGraph_ParameterLink*>& inParamLinks)
{
    if (!AnimGraph_BaseNode::PostAssemble(inNodeJson, inParamLinks))
        return false;

    mParameter = mOwnerGraph->GetParameter(mParamHandle);
    if (mParameter == nullptr)
        return false;

    return true;
}
}   // namespace cross::anim