#pragma once

#include "CECommon/Animation/Curve/AnimCurve.h"
#include "Resource/Resource.h"

#include "CEAnimation/AnimBase.h"
#include "CEAnimation/AnimContexts.h"
#include "CEAnimation/Notify/AnimNotifyQueue.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "CEAnimation/Composite/AnimCompositeBase.h"

#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/Animation/Skeleton/SkeletonComponent.h"
#include "Runtime/Animation/Animator/AnimGraph/AnimStoryBoard.h"
#include "Runtime/Animation/Animator/Parameter/ParameterExpression.h"

using namespace cross::skeleton;

namespace cross::anim {

using ExtractContextPtr = AnimExtractContext<TrackUnWrapperH>*;

class AnimFactory;
class IAnimator;

class ENGINE_API Animator : public IAnimator
{
public:
    Animator(GameWorld* inWorld, ecs::EntityID inEntityID, const AnimFactory* inAnimFactory);
    ~Animator();

    /** Called after our anim instance is initialized by skeleton */
    bool Initialize(AnimatorResPtr& inAnimatorRes);

    /** Called before update so we can copy any data we need */
    void PreUpdate(float deltaSeconds);

    /** Update override point */
    void Update(float deltaSeconds, RootSpacePose& outPose);

    /** Called after Update **/
    void PostUpdate();

    GameWorld* GetWorld() const override
    {
        return mGameWorld;
    }
    ecs::EntityID GetOwner() const
    {
        return mOwnerEntity;
    }

    CEName GetStbName() const;

#if CROSSENGINE_EDITOR
    bool GetIsBeingDebugged() const
    {
        return mIsBeingDebugged;
    }

    void SetIsBeingDebugged(bool debug)
    {
        mIsBeingDebugged = debug;
    }

    const StbDebugData& GetStbDebugData() const
    {
        return mSbPtr->GetStbDebugData();
    }
#endif   // CROSSENGINE_EDITOR

    bool GetPlayable() const
    {
        return mPlayable;
    }

    void SetPlayable(bool bPlayable)
    {
        mPlayable = bPlayable;
    }

    const Skeleton* GetSkeleton() const;

    inline AnimFactory const* GetFactory() const
    {
        return mCurFactory;
    }

    inline const std::vector<ParameterPtr>* GetParameters() const
    {
        return &mParameters;
    }

    inline size_t GetParameterIndexByName(const CEName& name)
    {
        if (mNameToParamIndexMap.find(name) == mNameToParamIndexMap.end())
        {
            return static_cast<size_t>(-1);
        }
        return mNameToParamIndexMap[name];
    }

    ParameterPtr GetParameterByName(const CEName& name);

    const CENameMap<CEName, size_t>& GetParamNameToIndexMap() const
    {
        return mNameToParamIndexMap;
    }

    ParamSymbolTable& GetParamSymbolTable()
    {
        return mParamSymbolTable;
    }

    AnimNotifyQueue& GetNotifyQueue()
    {
        return mNotifyQueue;
    }

    AnimCurveData& GetCurveData()
    {
        return mAnimCurveData;
    }

    const std::vector<AnimCmpInstanceBasePtr>& GetActiveAnimInstances() const
    {
        return mAnimInstances;
    }

    void PushNewPlayedAnim(AnimCmpInstanceBasePtr animExec, bool stopOthers = true);

    AnimCmpInstanceBasePtr GetAnimInstanceForID(SInt32 InstanceID) const;

protected:
    /** Called during PreUpdate, if SkelMesh LOD has changed since last update */
    void OnPreUpdateLODChanged(const SInt32 previousLODIndex, const SInt32 newLODIndex);

    /** 1st tick
        Update weight, sync group and so on
    **/
    void UpdateAnimInstance(float deltaSeconds);

    /** 2nd tick
        Blend weight
    **/
    void UpdateGraph(float deltaSeconds);

    /** 3rd tick
        Get and blend pose.
    **/
    void EvaluatePose(RootSpacePose& outPose);

private:
    /* rebuild name to param index map */
    void BuildParamIndexMap();

private:
    /* If mPlayable == false, animator will not be updated and evaluated*/
    bool mPlayable = true;

#if CROSSENGINE_EDITOR
    /* Whether this animator is being debugged, if true, debug info will be shown  */
    bool mIsBeingDebugged = false;
#endif   // CROSSENGINE_EDITOR

    /** The last time passed into PreUpdate() */
    float mCurDeltaSeconds{0.f};

    /** The last dime dilation (gleaned from world settings) */
    float mCurTimeDilation{0.f};

    /** The component to world transform of the component we are running on */
    NodeTransform mComponentTransform;

    /** The transform of the actor we are running on */
    NodeTransform mActorTransform;

    /** Animation Notifies that has been triggered in the latest tick **/
    AnimNotifyQueue mNotifyQueue;

    /**  **/
    RootMotionParams mRootMotionParams;

    // Anim Curves
    AnimCurveData mAnimCurveData;

    /** LODLevel used by RequiredBones */
    SInt32 mLODLevel{0};

    /** Factory for create executable anim instance **/
    AnimFactory const* mCurFactory{nullptr};

    /** Parameters for connect game world and current animator **/
    std::vector<ParameterPtr> mParameters;

    CENameMap<CEName, size_t> mNameToParamIndexMap;

    ParamSymbolTable mParamSymbolTable;

    AnimStoryBoardPtr mSbPtr{nullptr};

    bool mIsAnySlotPlaying;

public:
    bool IsPlayingRootMotion() const;
    RootMotionParams ConsumeRootMotion();

    // temporally added
    Float4x4A GetWorldTransform();

    void SetWorldTransform(const Float3& trans, const Float3& scale, const Quaternion& quat);

    bool IsAnySlotPlaying() const;

private:
    AnimUpdateContext mUpdateContext;
    AnimExtractContext<TrackUnWrapperH> mExtractContext;

    /////// simulate graph members
    //
    // All AnimatrixInstance & AnimCompositeInstance
    std::vector<AnimCmpInstanceBasePtr> mAnimInstances;

    // World
    GameWorld* mGameWorld{nullptr};
    ecs::EntityID mOwnerEntity;
};

}   // namespace cross::anim
// World
