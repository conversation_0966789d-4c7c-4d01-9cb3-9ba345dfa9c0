#pragma once
#include "CEAnimation/Animator/AnimatorParameter.h"
#include "Runtime/Animation/Animator/Parameter/exprtk_parser.hpp"

namespace cross::anim
{
    class ParamExpression;
    class ParamExprParser;

    class Lexer
    {
        using Token = exprtk::lexer::token;
        using TokenType = exprtk::lexer::token::token_type;
        using Analyzer = exprtk::lexer::generator;

    public:
        bool Process(const std::string& exprStr);

    protected:
        Lexer(const std::string& inDependencyStr) : mDependencyStr(inDependencyStr) {}

        bool ScanToken();

    private:
        Analyzer mAnalyzer;
        const std::string mDependencyStr = "";

        friend class ParamExprParser;
    };

    // Add all animator parameter into this table
    class ENGINE_API ParamSymbolTable
    {
        using ExprtkSymbolTable = exprtk::symbol_table<float>;

    public:
        ParamSymbolTable();

        template<class ValueType>
        void GetVariable(const CEName& paramName, ValueType& outValue)
        {
            if (mNumericTypeStore.find(paramName) != mNumericTypeStore.end())
                GetNumeric(paramName, outValue);
            else if (mStringTypeStore.find(paramName) != mStringTypeStore.end())
                GetString(paramName, outValue);
            else if (mVector2TypeStore.find(paramName) != mVector2TypeStore.end() ||
                mVector3TypeStore.find(paramName) != mVector3TypeStore.end() ||
                mVector4TypeStore.find(paramName) != mVector4TypeStore.end())
                GetVector(paramName, outValue);
            else if (mTransformTypeStore.find(paramName) != mTransformTypeStore.end())
                GetTransform(paramName, outValue);
            else
                AssertMsg(false, "Variable is not exist");
        }

        // Add param into table when it not exists or Update param value when it already exists
        bool UpdateTableByParam(const Parameter* inParam);

        // Add custom function into table
        template<class FuncType>
        void AddFunction(const CEName& funcName, FuncType& outValue)
        {
            mTable.add_function(funcName.GetCString(), outValue);
        }

    private:
        template<class ValueType>
        bool AddOrUpdateNumeric(const CEName& paramName, const ValueType& value)
        {
            auto floatValue = static_cast<float>(value);

            // Update
            if (mNumericTypeStore.find(paramName) != mNumericTypeStore.end())
            { 
                mNumericTypeStore[paramName] = floatValue;
                return true;
            }

            // Add
            auto iter = mNumericTypeStore.insert({ paramName , floatValue });
            std::string nameStr(paramName.GetCString());
            return  mTable.add_variable(nameStr, mNumericTypeStore[paramName], false);
        }

        bool AddOrUpdateString(const CEName& paramName, const std::string& value);

        template<class ValueType>
        bool AddOrUpdateVector(const CEName& paramName, const ValueType& value)
        {
            std::string nameStr(paramName.GetCString());

            if constexpr (std::is_same_v<ValueType, Float2>)
            {
                // Update
                if (mVector2TypeStore.find(paramName) != mVector2TypeStore.end())
                {
                    mVector2TypeStore[paramName] = value;
                    return true;
                }

                // Add
                auto iter = mVector2TypeStore.insert({ paramName , value });
                return mTable.add_vector(nameStr, iter.first->second.data(), 2, false);
            }

            if constexpr (std::is_same_v<ValueType, Float3>)
            {
                // Update
                if (mVector3TypeStore.find(paramName) != mVector3TypeStore.end())
                {
                    mVector3TypeStore[paramName] = value;
                    return true;
                }

                // Add
                auto iter = mVector3TypeStore.insert({ paramName , value });
                return mTable.add_vector(nameStr, iter.first->second.data(), 3, false);
            }

            if constexpr (std::is_same_v<ValueType, Float4>)
            {
                // Update
                if (mVector4TypeStore.find(paramName) != mVector4TypeStore.end())
                {
                    mVector4TypeStore[paramName] = value;
                    return true;
                }

                // Add
                auto iter = mVector4TypeStore.insert({ paramName , value });
                return mTable.add_vector(nameStr, iter.first->second.data(), 4, false);
            }
        }

        bool AddOrUpdateTransform(const CEName& paramName, const NodeTransform& value);

        template<class ValueType>
        void GetNumeric(const CEName& paramName, ValueType& outValue)
        {
            if constexpr (std::is_same_v<ValueType, bool>)
                outValue = static_cast<bool>(mNumericTypeStore[paramName]);

            if constexpr (std::is_same_v<ValueType, SInt32>)
                outValue = static_cast<SInt32>(mNumericTypeStore[paramName]);

            if constexpr (std::is_same_v<ValueType, float>)
                outValue = mNumericTypeStore[paramName];
        }

        template<class ValueType>
        void GetString(const CEName& paramName, ValueType& outValue)
        {
            if constexpr (std::is_same_v<ValueType, std::string>)
                outValue = mStringTypeStore[paramName];
        }

        template<class ValueType>
        void GetVector(const CEName& paramName, ValueType& outValue)
        {
            if constexpr (std::is_same_v<ValueType, Float2>)
                outValue = mVector2TypeStore[paramName];

            if constexpr (std::is_same_v<ValueType, Float3>)
                outValue = mVector3TypeStore[paramName];

            if constexpr (std::is_same_v<ValueType, Float4>)
                outValue = mVector4TypeStore[paramName];
        }

        template<class ValueType>
        void GetTransform(const CEName& paramName, ValueType& outValue)
        {
            if constexpr (std::is_same_v<ValueType, NodeTransform>)
                outValue = mTransformTypeStore[paramName];
        }

    private:
        ExprtkSymbolTable mTable;

        CENameMap<CEName, float> mNumericTypeStore;
        CENameMap<CEName, std::string> mStringTypeStore;
        CENameMap<CEName, Float2> mVector2TypeStore;
        CENameMap<CEName, Float3> mVector3TypeStore;
        CENameMap<CEName, Float4> mVector4TypeStore;
        CENameMap<CEName, NodeTransform> mTransformTypeStore;

        friend class ParamExpression;
        friend class ParamExprParser;
    };

    namespace Expression
    {
        enum Type
        {
            None = 0,
            Graph,
            FSM,
            Segment
        };
    }

    // An Expression Instance
    class ParamExpression
    {
        using ExprtkExpression = exprtk::expression<float>;

    public:
        ParamExpression(const std::string& inExprStr, Expression::Type inType) : mExprStr(inExprStr), mExprType(inType) {}
        ParamExpression() = delete;
        ParamExpression(const ParamExpression& other) = delete;
        ParamExpression& operator=(const ParamExpression& other) = delete;

        void ClearRegistration() { mExprInstance.clear_registration(); }
        void RegisterSymbolTable(ParamSymbolTable& inSymbolTable);
        bool Compile();
        // must compile first
        float Evaluate();

        const std::string& GetExprString() const { return mExprStr; }
        Expression::Type GetExprType() const { return mExprType; }

    private:
        const std::string mExprStr = "";
        Expression::Type mExprType = Expression::None;
        ExprtkExpression mExprInstance;

        bool mHasCompiled{ false };

        friend class ParamExprParser;
    };

    // Parser Factory
    class ParamExprParser
    {
        using ExprtkParser = exprtk::parser<float>;
        using ExprtkParserSettings = ExprtkParser::settings_t;

    public:
        using LexerMap = std::unordered_map<Expression::Type, Lexer>;
        static LexerMap sLexerMap;

    public:
        ParamExprParser(const ParamExprParser& other) = delete;
        ParamExprParser& operator=(const ParamExprParser& other) = delete;

        bool CompileExpression(ParamExpression& inExpr);

        static ParamExprParser& GetInstance() noexcept;

    private:
        ParamExprParser();

        void Initialize();

    private:
        ExprtkParser mParser;
        static const std::size_t sDefaultSettings;
    };
}
