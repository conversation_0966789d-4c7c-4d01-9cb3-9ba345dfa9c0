#include "EnginePrefix.h"
#include "Runtime/Animation/Composite/AnimModifiers.h"

namespace cross::anim
{
#if CROSSENGINE_EDITOR

    void AnimTrackModifier::RegisterLengthModifiedCallback(OnAnimAssetLengthModified inLengthModifiedFunc) 
    {
        mOnLengthModifiedFunc = inLengthModifiedFunc;
    }

    void AnimTrackModifier::ApplyToAnimationTrack(struct AnimTrack& outTrack)
    {
        outTrack = mTrack;
    }

    void AnimTrackModifier::RevertFromAnimationTrack(struct AnimTrack const& inTrack) 
    {
        mTrack = inTrack;
    }

    bool AnimTrackModifier::InsertSegment(SInt32 preIndex, AnimSeqPtr segAnimAsset, SlotTrackResSegment const& segAnimRes) 
    {
        if (!segAnimAsset->IsSkeletonAttached(mRunSkeleton))
            return false;

        if (preIndex > mTrack.AnimSegments.size())
            return false;

        // Add a new segment res in the very beginning
        mTrackRes.SegmentsDesc.insert(
            mTrackRes.SegmentsDesc.begin() + preIndex, segAnimRes);

        float preTrackLength = mTrack.GetRunLength();

        // Make a new segment then by above anim res reference, coordinate with runtime
        AnimSegment seg(segAnimAsset.get(), *(mTrackRes.SegmentsDesc.begin() + preIndex), {preTrackLength});
        mTrack.AnimSegments.insert(mTrack.AnimSegments.begin() + preIndex, seg);

        // holding instance smart pointer here
        mAddedAnimSeqs.push_back(segAnimAsset);

        // refresh segments track start pos after add
        RefreshSegmentsPositionInTrack();

        if (mOnLengthModifiedFunc != nullptr && (std::abs)(mTrack.GetRunLength() - preTrackLength) > 0.001f)
            mOnLengthModifiedFunc(&mTrack, preTrackLength);

        return true;
    }

    bool AnimTrackModifier::RemoveSegment(SInt32 index) 
    {
        float preTrackLength = mTrack.GetRunLength();

        Assert(index < mTrack.AnimSegments.size());
        auto& segments = mTrack.AnimSegments;
        segments.erase(segments.begin() + index);

        // refresh segments track start pos after remove
        RefreshSegmentsPositionInTrack();

        if (mOnLengthModifiedFunc != nullptr)
            mOnLengthModifiedFunc(&mTrack, preTrackLength);

        return true;
    }

    void AnimTrackModifier::RefreshSegmentsPositionInTrack() 
    {
        TrackUnWrapperH cursor = {0};

        std::for_each(mTrack.AnimSegments.begin(), mTrack.AnimSegments.end(), [&cursor](auto& elem) 
        { 
            elem.StartPos = cursor;
            cursor = {cursor + elem.GetRunLoopingLength()};
        });
    }

    void AnimTrackModifier::RefreshTrackRes()
    {
        mTrackRes.SlotName = mTrack.SlotName;

        auto& segmentsDesc = mTrackRes.SegmentsDesc;
        segmentsDesc.clear();
        segmentsDesc.reserve(mTrack.AnimSegments.size());
        for (const auto& segment : mTrack.AnimSegments)
        {
            SlotTrackResSegment resSegment{};
            resSegment.ReltvPath = segment.SequencePtr->GetAssetPath().c_str();
            if (PathHelper::GetExtension(resSegment.ReltvPath.GetCString()) != "nda")
                resSegment.ReltvPath = segment.SequencePtr->GetName();

            resSegment.Name = PathHelper::GetBaseFileName(resSegment.ReltvPath.GetCString()).c_str();
            resSegment.StartPos = segment.Reference.StartPos;
            resSegment.EndPos = segment.Reference.EndPos;
            resSegment.PlayRate = segment.Reference.PlayRate;
            resSegment.LoopingCount = segment.Reference.LoopingCount;
            segmentsDesc.push_back(resSegment);
        }
    }
    
    AnimatrixModifier::AnimatrixModifier(AnimatrixPtr inShellPtr, Skeleton const* inSkelt)
    {
        CENameMap<CEName, AnimTrack*> ShellTracks;
        for (auto& [name, track] : inShellPtr->mDefTracks)
            ShellTracks[name] = &track;
        for (auto& [name, track] : inShellPtr->mSecTracks)
            ShellTracks[name] = &track;

        auto& SlotTracks = inShellPtr->mAnimatrixResPtr->mSlotTracks;
        for (auto& slotTrack : SlotTracks)
        {
            if (auto it = ShellTracks.find(slotTrack.SlotName); it != ShellTracks.end())
            {
                std::string Name = slotTrack.SlotName.GetCString();
                ModifierForActivatedTracks.emplace(Name, AnimTrackModifier(*(it->second), slotTrack, inSkelt));
                TrackNames.push_back(Name);

            }
        }
    }
#endif
}