#include "EnginePrefix.h"
#include "Runtime/Animation/Composite/Animatrix.h"
#include "Runtime/Animation/Notify/AnimNotify_JumpToSection.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/Animation/Animator/Animator.h"

namespace cross::anim
{
    JumpBranchMap AnimNotifyJumpSectionByScript::JumpBranchImpls;

    /////////////////////////////////////////////
    // AnimNotifyJumpSectionByScript
    //
    /////////////////////////////////////////////

    NotifyGetResult::Type AnimNotifyJumpSectionByScript::Broadcast(IAnimator const* inAnimator, IExecutableAnim const* inExecAnim, CEName const& inSlotName, IGameWorld* inGameWorld) const
    {
        if (inSlotName == "")
            return NotifyGetResult::None;

        bool jumpSection = false;
        if (JumpBranchImpls.find(inGameWorld->GetName()) != JumpBranchImpls.end())
            jumpSection = JumpBranchImpls[inGameWorld->GetName()](dynamic_cast<const Animator*>(inAnimator), inExecAnim, mScriptCallbackStr.GetCString());
        
        if (jumpSection) 
        {
            auto const* animatrixPtr = TYPE_CAST(AnimatrixInstance const*, inExecAnim);
            Assert(animatrixPtr != nullptr);
        
            auto const* animSlotPtr = animatrixPtr->GetActivatedSlot(inSlotName);
            Assert(animSlotPtr != nullptr);

            auto const* animSecSlotPtr = TYPE_CAST(AnimReferenceSecTrack const*, animSlotPtr);
            Assert(animSecSlotPtr != nullptr);

            return animSecSlotPtr->MoveNext() ? NotifyGetResult::JumpSection : NotifyGetResult::None;
        }

        return NotifyGetResult::None;
    }

    void AnimNotifyJumpSectionByScript::Deserialize(CrossSchema::ImportAnimNotifyEvent const* fbNotifyEventPtr)
    {
        AnimNotifyEvent::Deserialize(fbNotifyEventPtr);

        const auto& fbNotify = *(fbNotifyEventPtr->notify());
        const auto fbNotifyImpl = fbNotify.notify_impl_as_Notify_JumpSectionScriptImpl();
        Assert(fbNotifyImpl != nullptr);

        mScriptCallbackStr = CEName{flatbuffers::GetString(fbNotifyImpl->callback_str()).c_str()};
    }

    void AnimNotifyJumpSectionByScript::Deserialize(const DeserializeNode& node) 
    { 
        AnimNotifyEvent::Deserialize(node);
        mScriptCallbackStr = CEName{node["CallBack"].AsString().c_str()};
    }

#if CROSSENGINE_EDITOR 

    void AnimNotifyJumpSectionByScript::Serialize(std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>& csNotifyEvent) const
    {
        std::unique_ptr<CrossSchema::ImportAnimNotifyT> csNotify(new CrossSchema::ImportAnimNotifyT);

        const auto&& ceName = GetTypeName();
        csNotify->notify_type = ceName.GetCString();

        csNotify->notify_impl.type = CrossSchema::ImportAnimNotifyImpl::Notify_JumpSectionScriptImpl;

        auto implPtr = new CrossSchema::Notify_ScriptImplT();
        implPtr->callback_str = mScriptCallbackStr.GetCString();
        csNotify->notify_impl.value = implPtr;

        AnimNotifyEvent::Serialize(csNotifyEvent);
        csNotifyEvent->notify = std::move(csNotify);
    }

    void AnimNotifyJumpSectionByScript::Serialize(SerializeNode& node) const
    {
        const auto&& ceName = GetTypeName();
        node["Type"] = ceName.GetCString();
        node["CallBack"] = mScriptCallbackStr.GetCString();

        AnimNotifyEvent::Serialize(node);
    }

#endif

    /////////////////////////////////////////////
    // AnimNotifyJumpSectionByFormula
    //
    /////////////////////////////////////////////

    NotifyGetResult::Type AnimNotifyJumpSectionByFormula::Broadcast(IAnimator const* inAnimator, IExecutableAnim const* inExecAnim, CEName const& inSlotName, IGameWorld* inGameWorld) const 
    {
        return NotifyGetResult::None;
    }
 #if CROSSENGINE_EDITOR
    void AnimNotifyJumpSectionByFormula::Serialize(std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>& csNotifyEvent) const
    {
        std::unique_ptr<CrossSchema::ImportAnimNotifyT> csNotify(new CrossSchema::ImportAnimNotifyT);

        const auto&& ceName = GetTypeName();
        csNotify->notify_type = ceName.GetCString();

        AnimNotifyEvent::Serialize(csNotifyEvent);
        csNotifyEvent->notify = std::move(csNotify);
    }

    void AnimNotifyJumpSectionByFormula::Serialize(SerializeNode& node) const
    {
        const auto&& ceName = GetTypeName();
        node["Type"] = ceName.GetCString();

        AnimNotifyEvent::Serialize(node);
    }
#endif
    void AnimNotifyJumpSectionByFormula::Deserialize(const DeserializeNode& node)
    {
        AnimNotifyEvent::Deserialize(node);
    }
}
