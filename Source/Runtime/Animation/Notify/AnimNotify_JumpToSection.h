#pragma once

#include "Runtime/Animation/Animator/Parameter/ParameterExpression.h"
#include "CEAnimation/Notify/AnimNotify.h"
#include "Runtime/Animation/Notify/AnimNotify_Script.h"
#include "CEAnimation/AnimContexts.h"

namespace cross::anim
{
    using JumpBranchMap = CEHashMap<HashString, std::function<bool(Animator const*, anim::IExecutableAnim const* inJudger, std::string const)>>;
    class ENGINE_API AnimNotifyJumpSectionByScript : public AnimNotifyEventRegister<AnimNotifyJumpSectionByScript>
    {
    public:
        //// ~AnimNotifyEvent Interface Begin~
        //// 

        virtual NotifyGetResult::Type Broadcast(IAnimator const* inAnimator, IExecutableAnim const* inExecAnim, CEName const& inSlotName, IGameWorld* inGameWorld) const override;

        virtual bool IsInstantNotify() const override { return true; }
        virtual bool IsBranchingPointNotify() const override { return true; }

        virtual void Deserialize(CrossSchema::ImportAnimNotifyEvent const* fbNotifyEventPtr) override;
        virtual void Deserialize(const DeserializeNode& node) override;

#if CROSSENGINE_EDITOR
        void SetScriptCallbackStr(CEName inCallbackStr)
        {
            mScriptCallbackStr = inCallbackStr;
        }
        CEName GetScriptCallbackStr() const
        {
            return mScriptCallbackStr;
        }

        virtual void Serialize(std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>& csNotifyEvent) const override;
        virtual void Serialize(SerializeNode& node) const override;
#endif

        //// ~AnimNotifyEvent Interface End~
        //// 

         static JumpBranchMap JumpBranchImpls;

    private:
        CEName mScriptCallbackStr = "";
    };

    class ENGINE_API AnimNotifyJumpSectionByFormula : public AnimNotifyEventRegister<AnimNotifyJumpSectionByFormula>
    {
    public:
        //// ~AnimNotifyEvent Interface Begin~
        //// 

        virtual NotifyGetResult::Type Broadcast(IAnimator const* inAnimator, IExecutableAnim const* inExecAnim, CEName const& inSlotName, IGameWorld* inGameWorld) const override;

        virtual bool IsInstantNotify() const override { return true; }

        virtual bool IsBranchingPointNotify() const override { return true; }

#if CROSSENGINE_EDITOR
        virtual void Serialize(std::unique_ptr<CrossSchema::ImportAnimNotifyEventT>& csNotifyEvent) const override;
        virtual void Serialize(SerializeNode& node) const override;
#endif

        virtual void Deserialize(const DeserializeNode& node) override;

        //// ~AnimNotifyEvent Interface End~
        //// 

    private:
        // all animator parameters that this notify uses.
        ParamHs mParamHandles;
        // express string holding here
        std::string mFormula;
    };
}
