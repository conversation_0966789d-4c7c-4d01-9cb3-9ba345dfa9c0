#include "EnginePrefix.h"
#include "Runtime/Animation/Sequence/UniformSampleSeq.h"
#include "CEAnimation/AnimRuntime.h"

namespace cross::anim {
/////////////////////////////////////////////
// UniformSampleSeq
//
/////////////////////////////////////////////
NodeTransform UniformSampleSeq::ExtractRootMotionFromDeltaTime(const RawH& startPos, float deltaTime, bool bAllowLoop) const
{
    RootMotionParams rootMotionParam;

    if (deltaTime != 0.f)
    {
        const bool bPlayBwd = (deltaTime < 0.f);

        RawH prevPos = {std::clamp(startPos.mVal, 0.f, GetRunLength().mVal)};
        RawH curPos = prevPos;
        float desiredDelta = deltaTime;

        do
        {
            // disable looping here. Advance to desired position, or beginning / end of animation
            const AdvanceAnim::Type advanceType = AnimRuntime::AdvanceTime(false, desiredDelta, curPos, GetRunLength());

            rootMotionParam.Accumulate(ExtractRootMotionFromRange(prevPos, curPos));

            // If we've hit the end of the animation, and we're allowed to loop, keep going.
            if ((advanceType == AdvanceAnim::AA_Finished) && bAllowLoop)
            {
                const float actualDelta = curPos - prevPos;
                desiredDelta -= actualDelta;

                prevPos = bPlayBwd ? GetRunLength() : RawH::From(0.f);
                curPos = prevPos;
            }
            else
            {
                break;
            }
        } while (true);
    }

    return rootMotionParam.GetRootMotionTransform();
}

NodeTransform UniformSampleSeq::ExtractRootMotionFromRange(const RawH& startPos, const RawH& endPos) const
{
    const bool bSampleBwd = (startPos > endPos);

    NodeTransform outRootMotionTransform;
    mSampler->Interpolate(outRootMotionTransform, startPos, endPos, (bSampleBwd ? IAnimSampler::SampleDirection::Backward : IAnimSampler::SampleDirection::Forward));

    return outRootMotionTransform;
}

void UniformSampleSeq::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const
{
    mSampler->GetBoneTransform(outBoneTrans, boneIndex, currentTime);
}

void UniformSampleSeq::GetAnimationFullPose(RootSpacePose& outPose, AnimExtractContext<RawH>& extractContext) const
{
    // consider bone requirement later
    mSampler->Interpolate(outPose, extractContext.CurrentTime(), static_cast<IAnimSampler::SampleDirection>(extractContext.PlayDirection));

    // once pose has been extracted, snap root bone to RootLockTransform if we have extracted root motion.
    if (extractContext.CanExtractRootMotion() && HasRootMotion())
    {
        outPose[POSE_BONE_INDEX_ROOT] = mSampler->GetRootLockTransform();
    }
}

/////////////////////////////////////////////
// UniformSampler
//
/////////////////////////////////////////////
UniformSampler::UniformSampler(const UniformSampleSeq* inAnimPtr, const UniformSampleStreamableData* inStreamableDataPtr)
{
    Assert(inAnimPtr != nullptr);
    mAnimPtr = inAnimPtr;

    Assert(inStreamableDataPtr->AclCompressedAnim != nullptr);
    mCompressedTracks = inStreamableDataPtr->AclCompressedAnim;

    // initialize
    Initialize();
}

UniformSampler::~UniformSampler()
{
    acl::deallocate_type_array(AnsiAllocator::Instance(), mCurPoseCache, mAnimPtr->GetReferenceSkeleton().GetRawBoneNum());
}

void UniformSampler::Initialize()
{
    // initialize compress context
    mDecprContextPtr = std::make_unique<DecompressContext>();
    mDecprContextPtr->initialize(*mCompressedTracks);

    // initialize SampleCache
    UInt32 boneNum = static_cast<UInt32>(mAnimPtr->GetReferenceSkeleton().GetRawBoneNum());
    mCurPoseCache = acl::allocate_type_array<DecompressTransform>(AnsiAllocator::Instance(), boneNum);
    mDecprWriterPtr = std::make_unique<MyTrackWriter>(mCurPoseCache, boneNum);

    mTempPoseCache = acl::allocate_type_array<DecompressTransform>(AnsiAllocator::Instance(), boneNum);
    mTempDecprWirterPtr = std::make_unique<MyTrackWriter>(mTempPoseCache, boneNum);

    // update SampleCache to anim first frame
    mCurSampleDirect = SampleDirection::Forward;
    UpdateSampleCache(0.f);

    GetBoneTransform(mLastFrameRootTrans, SK_BONE_INDEX_ROOT);
    mCurFrameRootTrans = mLastFrameRootTrans;

    // initialize RootLockTransform
    switch (mAnimPtr->mStreamingAnimDataPtr->RootLockType)
    {
    case RootMotionRootLock::AnimFirstFrame:
        mRootLockTransform = mCurFrameRootTrans;
        break;
    case RootMotionRootLock::Zero:
        mRootLockTransform = NodeTransform::Identity();
        break;
    default:
    case RootMotionRootLock::RefPose:
        mRootLockTransform = {mAnimPtr->mRefSkeleton.GetRawRefBonePoseScale()[0], mAnimPtr->mRefSkeleton.GetRawRefBonePoseRotate()[0], mAnimPtr->mRefSkeleton.GetRawRefBonePoseTranslate()[0]};
        break;
    }
}

void UniformSampler::Interpolate(RootSpacePose& outPose, const UInt32 curFrameIndex, SampleDirection sampleDirect)
{
    Interpolate(outPose, mAnimPtr->GetTimeAtFrame(curFrameIndex), sampleDirect);
}

void UniformSampler::Interpolate(RootSpacePose& outPose, const RawH& currentTime, SampleDirection sampleDirect)
{
    auto curTime = Clamp<float>(currentTime.mVal, 0.0f, mAnimPtr->GetRunLength());

    InterpolateInternal(curTime, sampleDirect);

    UpdateBoneLclProperty(outPose);
}

void UniformSampler::Interpolate(NodeTransform& outRootMotionTrans, const RawH& previousTime, const RawH& currentTime, SampleDirection sampleDirect)
{
    float lastTime = Clamp<float>(previousTime.mVal, 0.0f, mAnimPtr->GetRunLength());
    float curTime = Clamp<float>(currentTime.mVal, 0.0f, mAnimPtr->GetRunLength());

    InterpolateInternal(lastTime, sampleDirect);
    GetBoneTransform(mLastFrameRootTrans, SK_BONE_INDEX_ROOT);
    mLastFrameRootTrans = mRootLockTransform.Inverse() * mLastFrameRootTrans;

    InterpolateInternal(curTime, sampleDirect);
    GetBoneTransform(mCurFrameRootTrans, SK_BONE_INDEX_ROOT);
    mCurFrameRootTrans = mRootLockTransform.Inverse() * mCurFrameRootTrans;

    outRootMotionTrans = NodeTransform::GetRelativeTransform(mLastFrameRootTrans, mCurFrameRootTrans);
}

void UniformSampler::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex, const float currentTime) const
{
    // grab pose for the time given from compressed animation
    mDecprContextPtr->seek(currentTime, acl::sample_rounding_policy::none);
    mDecprContextPtr->decompress_track(boneIndex, *mTempDecprWirterPtr);

    // grab compressed local matrix
    const auto& boneTrans = mTempPoseCache[boneIndex];

    // SIMDVector4 is actually __m128 == rtm::quatf、vector4f
    outBoneTrans.SetTranslation(MathSIMD::VectorSetW0(boneTrans.translation));
    outBoneTrans.SetRotation(boneTrans.rotation);
    outBoneTrans.SetScale(MathSIMD::VectorSetW0(boneTrans.scale));
}

void UniformSampler::InterpolateInternal(float curTime, SampleDirection sampleDirect)
{
    mCurSampleDirect = sampleDirect;

    if (mCurSampleTime != curTime)
    {
        UpdateSampleCache(curTime);
    }
}

void UniformSampler::UpdateSampleCache(float curTime)
{
    mCurSampleTime = curTime;

    // grab pose for the time given from compressed animation
    mDecprContextPtr->seek(curTime, acl::sample_rounding_policy::none);
    mDecprContextPtr->decompress_tracks(*mDecprWriterPtr);
}

void UniformSampler::UpdateBoneLclProperty(RootSpacePose& outPose) const
{
    if (mAnimPtr->mRunSkeltPtr == nullptr)
    {
        LOG_ERROR("RunSkeltPtr is null");
        return;
    }

    SkBoneHandle seqBonesNum = {static_cast<UInt32>(mAnimPtr->GetReferenceSkeleton().GetRawBoneNum())};

    const auto& runSkRefSkeleton = mAnimPtr->mRunSkeltPtr->GetReferenceSkeleton();
    // Grab runtime skeleton Ref-Pose translate
    const auto& runSkRefSkeltTranslateArray = runSkRefSkeleton.GetRawRefBonePoseTranslate();
    // Grab animation Ref-Pose translate
    const auto& animRefSkeltTranslateArray = mAnimPtr->mRefSkeleton.GetRawRefBonePoseTranslate();

    for (SkBoneHandle seq_bone_index = {0}; seq_bone_index < seqBonesNum; ++seq_bone_index)
    {
        // Retarget animation bone into skeleton bone
        SkBoneHandle skel_bone_index = mAnimPtr->GetSkeletonIndexFromSeqSkeltIndex(seq_bone_index);

        // Not found run_skelt_bone in anim_skelt, continue
        if (skel_bone_index == SK_BONE_INDEX_NONE)
            continue;

        // Retarget skeleton bone into FilteredPose Bone
        PoseBoneHandle pose_bone_index = outPose.GetPoseIndexFromFilteredBoneIndex(skel_bone_index);

        // Not found mapped filter_pose_bone in FilteredPose from run skeleton bone, continue
        if (pose_bone_index == POSE_BONE_INDEX_NONE)
            continue;

        GetBoneTransform(outPose[pose_bone_index], seq_bone_index);

        switch (mAnimPtr->mRunSkeltPtr->GetBoneRetargetingMode(skel_bone_index))
        {
        case BoneTranslateRetargetingMode::AnimScaledBySkelt:
        {
            const float runSkeltBoneLength = runSkRefSkeltTranslateArray[skel_bone_index].XYZ().Length();
            const float animBoneLength = animRefSkeltTranslateArray[seq_bone_index].XYZ().Length();

            if (runSkeltBoneLength > 0.001f)
            {
                outPose[pose_bone_index].SetTranslation(outPose[pose_bone_index].GetTranslation() * animBoneLength / runSkeltBoneLength);
            }
            break;
        }

        default:
            break;
        }
    }
}

void UniformSampler::GetBoneTransform(NodeTransform& outBoneTrans, SkBoneHandle boneIndex) const
{
    // grab compressed local matrix
    const auto& boneTrans = mCurPoseCache[boneIndex];

    // SIMDVector4 is actually __m128 == rtm::quatf、vector4f
    outBoneTrans.SetTranslation(MathSIMD::VectorSetW0(boneTrans.translation));
    outBoneTrans.SetRotation(boneTrans.rotation);
    outBoneTrans.SetScale(MathSIMD::VectorSetW0(boneTrans.scale));
}
}   // namespace cross::anim
