#pragma once

#include "CrossBase/Math/CrossMath.h"
#include "Runtime/Interface/CrossEngine.h"

namespace cross {
class ENGINE_API AudioEngine
{
public:
    // result codes
    using Result = SInt32;

    // primary data types
    using Char = char;
    using Size = SizeType;
    using Float = float;
    using TimeMs = UInt32;
    using Index = SInt32;

    // identifications
    using ID             = SInt32;
    using ObjectID       = UInt64;
    using EventID        = ID;
    using ParamID        = ID;
    using SwitchID       = ID;
    using SwitchValueID  = ID;
    using StateID        = ID;
    using StateValueID   = ID;
    using GeometryID     = ID;
    using ObstacleID     = ID;
    using RoomID         = ID;
    using PortalID       = ID;

    // pointers
    using EventPtr = UInt32;

    // enums
    enum class CurveInterpolation
    {
        Linear            = 0, ///< Linear (Default)
        Log1            = 1, ///< Log1
        Log3            = 2, ///< Log3
        Sine            = 3, ///< Sine
        SCurve            = 4, ///< S Curve
        InvSCurve        = 5, ///< Inversed S Curve
        Exp1            = 6, ///< Exp1
        Exp3            = 7, ///< Exp3
        SineRecip        = 8, ///< Reciprocal of sine curve
        Constant        = 9, ///< Constant ( not valid for fading values )
    };

    // structs
    struct Surface
    {
        ID texture_id;
        Float transmission_loss;
    };

    // callbacks
    typedef std::function<void(EventID const event_id, EventPtr const event_ptr)> EndCallback;
    typedef std::function<void(EventID const event_id, EventPtr const event_ptr, ID const identifier, TimeMs const position, std::string str)> MarkerCallback;

    // consts
    static ObjectID const DEFAULT_LISTENER;

public:
    // constructor and destructor
    AudioEngine() = default;
    virtual ~AudioEngine() = default;
        
    // ID and string convertion
    virtual ID StringToID(Char const * const str) = 0;

    // configuration
    virtual Result LanguageSet(Char const * const language_name) = 0;
    virtual Result RootpathSet(Char const * const rootpath) = 0;

    // processing
    virtual Result Begin() = 0;
    virtual Result End() = 0;

    // bank API
    virtual Result BankLoad(Char const * const bank_name) = 0;
    virtual Result BankUnload(Char const * const bank_name) = 0;

    // object API
    virtual Result ObjectRegister(ObjectID const object_id) = 0;
    virtual Result ObjectUnregister(ObjectID const object_id) = 0;
    virtual Result ObjectSetPose(ObjectID const object_id, Float3 const & position, Float3 const & forward, Float3 const & up) = 0;
    virtual Result ObjectSetPose(ObjectID const object_id, Double3 const & position, Double3 const & forward, Double3 const & up) = 0;
    virtual Result ObjectSetListeners(ObjectID const object_id, ObjectID const * const listeners, Size const num_listeners) = 0;
    virtual Result ObjectAddListener(ObjectID const object_id, ObjectID const listener) = 0;
    virtual Result ObjectRemoveListener(ObjectID const object_id, ObjectID const listener) = 0;
	virtual Result ObjectSetVolume(ObjectID const object_id, ObjectID const listener, Float const volume) const = 0;

    // event API
    virtual Result EventPrepare(EventID const * const event_ids, Size const size) = 0;
    virtual Result EventUnprepare(EventID const* const event_ids, Size const size) = 0;
    virtual Result EventPost(ObjectID const object_id, EventID const event_id, EventPtr* const out_event_ptr
        , EndCallback const & end_callback = nullptr, MarkerCallback const & marker_callback = nullptr, void* const cookie = nullptr) = 0;
    virtual Result EventStop(EventPtr const event_ptr, TimeMs const duration, CurveInterpolation const curve) = 0;
    virtual Result EventStopAll(ObjectID const object_id) = 0;

    // parameter API
    virtual Result ParamSet(ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) = 0;
    virtual Result ParamSet(ObjectID const object_id, ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) = 0;
    virtual Result ParamSet(EventPtr const event_ptr, ParamID const param_id, Float const value, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) = 0;
    virtual Result ParamReset(ObjectID const object_id, ParamID const param_id, TimeMs const duration = 0, CurveInterpolation const curve = CurveInterpolation::Linear) = 0;

    // switch API
    virtual Result SwitchSet(ObjectID const object_id, SwitchID const switch_id, SwitchValueID const switch_value_id) = 0;

    // state API
    virtual Result StateSet(StateID const state_id, StateValueID const state_value_id) = 0;

    // output device API
    virtual Index OutputGetNum() = 0;
    virtual Index OutputGetDefault() = 0;
    virtual Result OutputGetName(Index const output_index, std::string & out_name) = 0;
    virtual Result OutputAdd(Index const output_index, ObjectID const object_id) = 0;
    virtual Result OutputRemove(Index const output_index) = 0;
    virtual Result OutputReplace(Index const output_index_src, Index const output_index_dest) = 0;
	virtual Result OutputSetVolume(Index const output_index, Float const volume) = 0;

    // spatial audio API
    virtual Result SpatialSetOutside(std::string const & reverbAuxBus
        , Float const reverbLevel, Float const auxSendLevelToSelf) = 0;

    virtual Result SpatialGetGeometry(GeometryID const geometry_id) = 0;
    virtual Result SpatialCreateGeometry(GeometryID const geometry_id
        , std::vector<Float3> const & vertexes, std::vector<Short4> const & triangles, std::vector<Surface> const & surfaces
        , bool const enableDiffraction, bool const enableDiffractionOnBoundaryEdges) = 0;
    virtual Result SpatialDestroyGeometry(GeometryID const geometry_id) = 0;

    virtual Result SpatialCreateObstacle(ObstacleID const obstacle_id, GeometryID const geometry_id
        , Double4x4 const & transform
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid) = 0;
    virtual Result SpatialDestroyObstacle(ObstacleID const obstacle_id) = 0;

    virtual Result SpatialCreateRoom(RoomID const room_id, GeometryID const geometry_id
        , Double4x4 const & transform
        , bool const useForReflectionAndDiffraction, bool const bypassPortalSubtraction, bool const isSolid
        , std::string const & reverbAuxBus, Float const reverbLevel, Float const transmissionLoss
        , Float const auxSendLevelToSelf, Float const priority) = 0;
    virtual Result SpatialDestroyRoom(RoomID const room_id) = 0;

    virtual Result SpatialCreatePortal(PortalID const portal_id
        , Float3 const & extent, Double4x4 const & transform) = 0;
    virtual Result SpatialDestroyPortal(PortalID const portal_id) = 0;

    virtual Result SpatialSetListener(ObjectID const object_id) const = 0;
    virtual Result SpatialUnsetListener(ObjectID const object_id) const = 0;
};
}