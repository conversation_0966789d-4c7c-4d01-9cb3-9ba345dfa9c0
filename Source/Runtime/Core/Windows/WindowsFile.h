#if 0
#pragma once

class WindowsFile : public cross::File
{
public:
	WindowsFile();
	virtual ~WindowsFile();

	bool Open(std::string const& path, File::AccessMode accessMode) override;
	void Close() override;
    void Flush() override;

	UInt64 Read(void* buffer, UInt64 size) override;
	bool Write(const void* buffer, UInt64 size) override;

	UInt64 Size() const override;
	bool Resize(UInt64 size) override;

	bool SeekRead(SInt64 offset, File::SeekFrom seekPos = File::SeekFrom::Current) override;
    bool SeekWrite(SInt64 offset, File::SeekFrom seekPos = File::SeekFrom::Current) override;
    bool Eof() const override;
    std::string const& GetFileName() const override;

protected:
	HANDLE OpenFile(File::AccessMode access);
	bool SetFileLength(const HANDLE handle, UInt64 size);
    static constexpr DWORD SeekFrom(File::SeekFrom seekPos) noexcept;
    bool Seek(SInt64 offset, File::SeekFrom seekPos);

protected:
	HANDLE          mFileHandle;
    std::string     mFilePath;
    UInt64          mPosition;
};
#endif
