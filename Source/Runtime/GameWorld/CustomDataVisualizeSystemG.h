#pragma once

#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "Resource/MeshAssetDataResource.h"
#include "Resource/Material.h"

namespace cross {

class MifData {
public:
    MifData(std::string path);
    ~MifData();
    // split string by space ' '
    std::vector<std::string> splitString(std::string s);
    void LoadMifFile(std::string filePath);
    //std::vector<std::vector<std::pair<double, double>>> ModifyData();
    void ModifyData();
    void MergeMifData(MifData* tempData);
    std::vector<std::vector<std::pair<double, double>>> tempData;
private:
    int BigRegionNum = 0;
    std::vector<std::vector<std::vector<std::pair<double, double>>>> GeoData;
};
enum class FileType
{
    Mif,
};
enum class ViewDataType
{
    Solid,
    WireFrame
};
enum class DataType
{
    Line, 
    Polygon,
    //Cube, 
    //Sphere, 
    //Point, 
    //Plane,
    //Capsule,
    //Cylinder,
    //Cone,
    //Circle
};
class CustomDataVisualizeSystemG : public GameSystemBase
{
public : 
    using DataChangeList = FrameChangeList<ecs::EntityID>;

    template<typename Type>
    using DataVector = std::vector<Type, ce_stl_allocator<Type>>;

    CEFunction(Reflect) 
   static CustomDataVisualizeSystemG* CreateInstance();

    virtual void OnFirstUpdate(FrameParam* frameParam) override;

    virtual void Release() override;

    virtual void OnBeginFrame(FrameParam* frameParam) override;

    CEFunction(Reflect) 
    virtual void OnBuildUpdateTasks(FrameParam* frameParam) override;

    virtual void OnEndFrame(FrameParam* frameParam) override;

    virtual RenderSystemBase* GetRenderSystem() override;

    virtual void NotifyAddRenderSystemToRenderWorld() override;

    const DataChangeList& GetChangeList() const
    {
        return mChangeList;
    }

    DataChangeList& GetChangeList()
    {
        return mChangeList;
    }
    void ReadData(std::string path);
    void GetPolygonsFromMIF(std::vector<std::vector<std::pair<double, double>>> mifDatas);
    void drawData();
    CustomDataVisualizeSystemG();
    virtual ~CustomDataVisualizeSystemG();

private:
    Float3 DecodeNormal(UInt16 value) const
    {
        const UInt16 b = value & 0xFF;
        const UInt16 a = value >> 8U;
        float x = 2.f / 255.f * b - 1.f;
        float z = 2.f / 255.f * a - 1.f;
        float y = sqrt(std::clamp(1.f - x * x - z * z, 0.0f, 1.0f));
        return {x, y, z};
    }

    UInt16 EncodeNormal(const Float3& value)
    {
        const auto b = static_cast<UInt16>(std::clamp(127.5f * (value.x + 1.f) + 0.5f, 0.f, 255.f));
        const auto a = static_cast<UInt16>(std::clamp(127.5f * (value.z + 1.f) + 0.5f, 0.f, 255.f));
        return (a << 8U) | b;
    }

#ifdef CROSSENGINE_EDITOR
    Float3 CalculateFlatPosition(const Float4& patchOffsetAndScale, const Float3& worldTranslation, const Float3& worldScale, Float3 patchCoords, float height) const
    {
        patchCoords = patchOffsetAndScale.XYZ() + patchCoords * patchOffsetAndScale.w;
        patchCoords.y = height;
        return worldTranslation + patchCoords * worldScale;
    }

    Float3 CalculateSphericalPosition(const Float2& gridDimRcp, const Float4& patchOffsetAndScale, const Float3& worldTranslation, const Float3& worldScale, Float3 patchCoords, float height) const
    {
        patchCoords = patchOffsetAndScale.XYZ() + patchCoords * patchOffsetAndScale.w;
        const auto longitude = PI * (2.f * patchCoords.x * gridDimRcp.x - 1.f);
        const auto latitude = .5f * PI * (2.f * patchCoords.z * gridDimRcp.y - 1.f);
        return worldTranslation + WGS84CoordinateTo3D_Radian(longitude, latitude, height * worldScale.y, worldScale.x);
    }

    Float3 CalculateWGS84Position(const Float2& gridDimRcp, const Float4& patchOffsetAndScale, const Float3& worldTranslation, const Float3& worldScale, Float3 patchCoords, float height) const
    {
        patchCoords = patchOffsetAndScale.XYZ() + patchCoords * patchOffsetAndScale.w;
        const auto longitude = PI * (2.f * patchCoords.x * gridDimRcp.x - 1.f);
        const auto latitude = .5f * PI * (2.f * patchCoords.z * gridDimRcp.y - 1.f);
        return worldTranslation + WGS84CoordinateTo3D_Radian(longitude, latitude, height * worldScale.y, worldScale.x);
    }
#endif
    bool mIsRenderObjectOwner = false;
    std::vector<std::string> dataPath = {"C:/work/SeaData/data/ocean.mif", "C:/work/SeaData/data/hk.mif"};
    FileType fileType = FileType::Mif;
    ViewDataType viewType = ViewDataType::WireFrame;
    DataType dataType = DataType::Polygon;
    //mif data
    MifData* mifData = nullptr;
    std::vector<std::vector<Float3>> polygons;
    std::vector<Float3> points;
    DataChangeList mChangeList;
};
}   // namespace cross