#include "EnginePrefix.h"
#include "EcotopeMap.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "Resource/AssetStreaming.h"

namespace cross
{
    EcotopeMap::EcotopeMap()
    {
        mWidth = 0;
        mHeight = 0;
    }

    EcotopeMap::~EcotopeMap()
    {
    }

    int EcotopeMap::GetWidth()
    {
        return mWidth;
    }

    int EcotopeMap::GetHeight()
    {
        return mHeight;
    }

    void EcotopeMap::Create(int width, int height)
    {
        mWidth = width;
        mHeight = height;
        int size = mWidth * mHeight;
        mTextureData.resize(size);
        auto Count = mTextureData.size();
        for (int i = 0; i < Count; i++)
        {
            mTextureData[i] = ColorRGBA32(255, 255, 255, 255);
        }
        CreateTexture();
        UpdateTexture();
    }

    void EcotopeMap::Save(const std::string& path)
    {
        int size = mWidth * mHeight;
        int byteSize = size * sizeof(ColorRGBA32);
        FILE* file = fopen(path.c_str(), "wb");
        fwrite(&mWidth, sizeof(mWidth), 1, file);
        fwrite(&mHeight, sizeof(mHeight), 1, file);
        fwrite(mTextureData.data(), byteSize, 1, file);
        fclose(file);
    }

    void EcotopeMap::Load(const std::string& path)
    {
        if (mWidth != 0)
            return;

        if (!EngineGlobal::GetFileSystem()->HaveFile(path))
            return;

        mFilePath = path;

        //auto fileH = EngineGlobal::GetFileSystem()->Open(path, false);
        //fileH->Read(reinterpret_cast<char*>(&mWidth), sizeof(mWidth));
        //fileH->Read(reinterpret_cast<char*>(&mHeight), sizeof(mHeight));
        //int size = mWidth * mHeight;
        //int byteSize = size * sizeof(ColorRGBA32);
        //mTextureData.resize(size);
        //fileH->Read(reinterpret_cast<char*>(mTextureData.data()), size * sizeof(ColorRGBA32));
        //fileH->Close();
        
        FILE* file = fopen(path.c_str(), "rb");
        fread(&mWidth, 1, sizeof(mWidth), file);
        fread(&mHeight, 1, sizeof(mHeight), file);
        int size = mWidth * mHeight;
        int byteSize = size * sizeof(ColorRGBA32);
        mTextureData.resize(size);
        fread(mTextureData.data(), byteSize, 1, file);
        fclose(file);

        CreateTexture();
        UpdateTexture();
    }

    void EcotopeMap::Clear()
    {
        auto Count = mTextureData.size();
        for (int i = 0; i < Count; i++)
        {
            mTextureData[i] = ColorRGBA32(255, 255, 255, 255);
        }
        UpdateTexture();
    }

    ColorRGBA32 EcotopeMap::GetPixel(int i, int j)
    {
        return mTextureData[j * mWidth + i];
    }

    void EcotopeMap::SetPixel(int i, int j, ColorRGBA32 pixel)
    {
        mTextureData[j * mWidth + i] = pixel;
    }

    void EcotopeMap::Brush(float u, float v, float endU, float endV, float density)
    {
        u = MathUtils::Clamp(u, 0.f, 1.f);
        v = MathUtils::Clamp(v, 0.f, 1.f);
        endU = MathUtils::Clamp(endU, 0.f, 1.f);
        endV = MathUtils::Clamp(endV, 0.f, 1.f);

        int X = static_cast<int>(u * mWidth);
        int Y = static_cast<int>(v * mHeight);
        int EndX = static_cast<int>(endU * mWidth);
        int EndY = static_cast<int>(endV * mHeight);

        int Density = static_cast<int>(density * 255);
        UInt8 DensityU8 = static_cast<UInt8>(std::abs(Density));
        ColorRGBA32 DensityC = {DensityU8, DensityU8, DensityU8, 0};      
        bool HasEdit = false;
        
        if (Density > 0)
        {
            for (int y = Y; y < EndY; y++)
            {
                for (int x = X; x < EndX; x++)
                {
                    SetPixel(x, y, GetPixel(x, y) + DensityC);
                    HasEdit = true;
                }
            }
        }
        else if (Density < 0)
        {
            for (int y = Y; y < EndY; y++)
            {
                for (int x = X; x < EndX; x++)
                {
                    SetPixel(x, y, GetPixel(x, y) - DensityC);
                    HasEdit = true;
                }
            }
        }
        
        if (HasEdit)
        {
            UpdateTexture();
        }
    }

    void EcotopeMap::CreateTexture()
    {
        mTexture = (cross::TexturePtr)new resource::Texture2D(TextureFormat::RGBA32, ColorSpace::Linear, mWidth, mHeight, 1);
    }

    void EcotopeMap::UpdateTexture()
    {
        int size = mWidth * mHeight;
        int byteSize = size * sizeof(ColorRGBA32);
        mTexture->UploadImage(0, 0, 0, reinterpret_cast<UInt8*>(mTextureData.data()), byteSize, 0);
        mDirty = true;
    }
    
    TexturePtr EcotopeMap::GetTexture()
    {
        return mTexture;
    }
}
