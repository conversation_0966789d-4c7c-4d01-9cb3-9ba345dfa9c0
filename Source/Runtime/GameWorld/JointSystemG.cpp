#include "EnginePrefix.h"
#include "Runtime/GameWorld/JointSystemG.h"

#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/PhysicsSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/PrimitiveRenderSystemG.h"
#include "RenderEngine/PrimitiveGenerator.h"
#include "Runtime/Animation/Skeleton/SkeletonComponent.h"

namespace cross {
ecs::ComponentDesc* JointComponentG::GetDesc()
{
    return EngineGlobal::GetECSFramework().CreateOrGetGameComponentDesc<cross::JointComponentG>({ false, true, true }, &JointSystemG::SerializeJointComponent, &JointSystemG::DeserializeJointComponent, &JointSystemG::PostDeserializeComponent);
}

JointSystemG* JointSystemG::CreateInstance() { return new JointSystemG(); }

JointSystemG::JointSystemG() {}

JointSystemG::~JointSystemG() {}

void JointSystemG::Release() { delete this; }

void JointSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    CreateTaskFunction(FrameTickStage::Update, {}, [this] {
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor)
        {
            auto jointSys = mGameWorld->GetGameSystem<JointSystemG>();
            auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
            auto result = mGameWorld->Query<WorldTransformComponentG, JointComponentG>();
            for (auto [transComp, jointComp] : result)
            {
                const Float3A& jointTrans = transSys->GetWorldTranslation(transComp.Read());
                for (const JointConnection& connetion : jointSys->GetConnections(jointComp.Read()))
                {
                    if (connetion.target0 || connetion.target1)
                    {
                        auto [physicsActor0, physicsActor1] = GetActors(connetion);
                        auto primitiveSystem = mGameWorld->GetGameSystem<PrimitiveRenderSystemG>();

                        PrimitiveData prim;
                        if (connetion.target0 && physicsActor0)
                        {
                            PrimitiveGenerator::GenerateLine(&prim, jointTrans, physicsActor0->GetTransform().first);
                            primitiveSystem->DrawPrimitive(&prim, Float4x4::Identity(), PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(0, 1, 0)));
                        }
                        if (connetion.target1 && physicsActor1)
                        {
                            PrimitiveGenerator::GenerateLine(&prim, jointTrans, physicsActor1->GetTransform().first);
                            primitiveSystem->DrawPrimitive(&prim, Float4x4::Identity(), PrimitiveRenderSystemG::PrimitiveLook(ColorRGBAf(0, 1, 0)));
                        }
                    }
                }
            }
        }
    });
}

void JointSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);

        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        if (phySys)
        {
            phySys->SubscribeEvent<RigidCreatedEvent>(this, 0);
        }
        auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
        if (transformSys)
        {
            transformSys->SubscribeEvent<TRSChangedEvent>(this, 0);
        }
    }
    else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
        if(e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
        {
            for(UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
            {
                const EntityDestroyEvent& entityLifeCycleEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
                if (ecs::HasComponentMask<JointComponentG>(entityLifeCycleEvent.mData.mChangedComponentMask))
                {
                    auto jointComp = mGameWorld->GetComponent<JointComponentG>(entityLifeCycleEvent.mData.mEntityID);
                    for (JointConnection& connect : jointComp.Write()->mConnections)
                    {
                        DeleteJoint(connect.mPhysicsJoint);
                        connect.mPhysicsJoint = nullptr;
                    }
                    jointComp.Write()->mConnections.clear();
                }
            }
        }
    }
    else if (event.mEventType == RigidCreatedEvent::sEventType)
    {
        const RigidCreatedEvent* rigidCreated = TYPE_CAST(const RigidCreatedEvent*, &event);
        auto result = mGameWorld->Query<JointComponentG>();
        for (auto joint : result)
        {
            for (auto& connect : joint.Write()->mConnections)
            {
                if (connect.target0 == rigidCreated->mData.mEntity || connect.target1 == rigidCreated->mData.mEntity)
                {
                    OnRigidChange(joint.Write(), connect);
                }
            }
        }
    }
    else if (event.mEventType == TRSChangedEvent::sEventType)
    {
        const bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
        if (isEditor)
        {
            ecs::EntityID entityId = TYPE_CAST(const TRSChangedEvent*, &event)->mData.mEntity;
            auto joints = mGameWorld->Query<JointComponentG>();
            for (JointComponentHandle joint : joints)
            {
                std::vector<JointConnection>& connects = joint.Write()->mConnections;
                if (joint.GetEntityID() == entityId)
                {
                    for (JointConnection& conn : connects)
                    {
                        OnTransformChange(joint.Write(), conn);
                    }
                }
                // dango phy TODO: a bug here. only first target can be found
                else
                {
                    for (JointConnection& conn : connects)
                    {
                        if (conn.target0 == entityId || conn.target1 == entityId)
                        {
                            OnTransformChange(joint.Write(), conn);
                        }
                    }
                }
            }
        }
    }
}

void JointSystemG::OnRigidChange(const JointComponentWriter& comp, JointConnection& connect)
{
    auto [phyActor0, phyActor1] = GetActors(connect);

    if (!connect.mPhysicsJoint && 
        (phyActor0 && phyActor1 && (phyActor0->IsDynamicActor() || phyActor1->IsDynamicActor())))
    {
        connect.mPhysicsJoint = CreateJoint(phyActor0, phyActor1, connect.config);
    }
    else if (connect.mPhysicsJoint &&
        (!phyActor0 || !phyActor1 || (!phyActor0->IsDynamicActor() && !phyActor1->IsDynamicActor())))
    {
        DeleteJoint(connect.mPhysicsJoint);
        connect.mPhysicsJoint = nullptr;
    }

    if (connect.mPhysicsJoint)
    {
        connect.mPhysicsJoint->SetActors(phyActor0, phyActor1);
    }
}

void JointSystemG::OnTransformChange(const JointComponentWriter& comp, JointConnection& connect)
{
    const bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
    if (isEditor)
    {
        auto [trans0, trans1] = GetLocalPoseOfActors(ecs::GrantReadAccess(comp), connect);

        connect.config.frameOffset[0] = trans0.translation;
        connect.config.frameOffset[1] = trans1.translation;
        connect.config.frameRotate[0] = trans0.rotation;
        connect.config.frameRotate[1] = trans1.rotation;
        if (connect.mPhysicsJoint)
        {
            connect.mPhysicsJoint->SetLocalPos(trans0, trans1);
        }
    }
    else
    {
        if (connect.mPhysicsJoint)
        {
            Transform trans0{connect.config.frameOffset[0], Float3::One(), connect.config.frameRotate[0]};
            Transform trans1{connect.config.frameOffset[1], Float3::One(), connect.config.frameRotate[1]};
            connect.mPhysicsJoint->SetLocalPos(trans0, trans1);
        }
    }
}

void JointSystemG::OnConfigChange(const JointComponentWriter& comp, JointConnection& connect)
{
    if(connect.mPhysicsJoint)
    {
        connect.mPhysicsJoint->SetConfig(connect.config);
    }
}

SerializeNode JointSystemG::SerializeJointComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<JointComponentG*>(componentPtr);
    SerializeContext context;
    SerializeNode node = comp->Serialize(context);



    // dango phy TODO: Temp Hack: currently EntityID serialization is not finished
    {
        SerializeNode mConnectionsNode = node["mConnections"];
        for (size_t i = 0; i < comp->mConnections.size(); ++i)
        {
            mConnectionsNode[i]["target0"] = comp->mConnections[i].target0 ? serializeWorld->GetEUID(comp->mConnections[i].target0).ToString() : SerializeNode{};
            mConnectionsNode[i]["target1"] = comp->mConnections[i].target1 ? serializeWorld->GetEUID(comp->mConnections[i].target1).ToString() : SerializeNode{};
        }
    }
    //End hack

    return node;
}

void JointSystemG::DeserializeJointComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
{
    auto comp = static_cast<JointComponentG*>(componentPtr);
    SerializeContext context;
    comp->Deserialize(json, context);
}

void JointSystemG::PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
{
    // dango phy TODO: Temp Hack: currently EntityID serialization is not finished
    {
        auto comp = static_cast<JointComponentG*>(componentPtr);
        const DeserializeNode& mConnectionsNode = json["mConnections"];
        for (size_t i = 0; i < comp->mConnections.size(); ++i)
        {
            const auto& target0Node = mConnectionsNode[i]["target0"];
            comp->mConnections[i].target0 = target0Node.IsNull() ? ecs::EntityID::InvalidHandle() : gameWorld->GetEntity(EUID(target0Node.AsString()));
            const auto& target1Node = mConnectionsNode[i]["target1"];
            comp->mConnections[i].target1 = target1Node.IsNull() ? ecs::EntityID::InvalidHandle() : gameWorld->GetEntity(EUID(target1Node.AsString()));
        }
    }
    // End hack





    auto jointSystem = gameWorld->GetGameSystem<JointSystemG>();
    auto physicsSystem = gameWorld->GetGameSystem<PhysicsSystemG>();
    auto jointComp = gameWorld->GetComponent<JointComponentG>(entityId);
    auto jointCompWriter = jointComp.Write();

    for (JointConnection& conn : jointCompWriter->mConnections)
    {
        //Ensure PhysicsActor
        auto phyComp0 = conn.target0 ? gameWorld->GetComponent<PhysicsComponentG>(conn.target0) : PhysicsSystemG::PhysicsComponentHandle::InvalidHandle();
        auto phyComp1 = conn.target1 ? gameWorld->GetComponent<PhysicsComponentG>(conn.target1) : PhysicsSystemG::PhysicsComponentHandle::InvalidHandle();

        if (phyComp0)
        {
            physicsSystem->InitPhysics(phyComp0.Write());
        }
        if (phyComp1)
        {
            physicsSystem->InitPhysics(phyComp1.Write());
        }

        jointSystem->OnRigidChange(jointCompWriter, conn);
        jointSystem->OnTransformChange(jointCompWriter, conn);
    }
}


PhysicsJoint* JointSystemG::CreateJoint(PhysicsActor* target0, PhysicsActor* target1, const PhysicsJointConfig& config)
{
    //const bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
    //if (!isEditor)
    {
        PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
        if (physicsEngine)
        {
            return physicsEngine->CreatePhysicsJoint(target0, target1, config);
        }
        return nullptr;
    }
   // return nullptr;
}

void JointSystemG::DeleteJoint(PhysicsJoint* joint)
{
    PhysicsEngine* physicsEngine = EngineGlobal::GetPhysicsEngine();
    if (physicsEngine)
    {
        physicsEngine->ReleaseJoint(joint);
    }
}

std::pair<Transform, Transform> JointSystemG::GetLocalPoseOfActors(const JointComponentReader& comp, const JointConnection& conn) const
{
    std::pair<Transform, Transform> ret;

    auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();

    auto jointTrans = mGameWorld->GetComponent<WorldTransformComponentG>(comp.GetEntityID());
    const Float3& jointPos = transformSystem->GetWorldTranslation(jointTrans.Read());
    const Quaternion& jointRot = transformSystem->GetWorldRotation(jointTrans.Read());

    auto [physicsActor0, physicsActor1] = GetActors(conn);
    if (physicsActor0)
    {
        auto [offset0, rot0] = physicsActor0->GetTransform();
        ret.first.translation = Float3::Transform(jointPos - offset0, rot0.Inverse());
        ret.first.rotation = jointRot / rot0;
    }

    if (physicsActor1)
    {
        auto [offset1, rot1] = physicsActor1->GetTransform();
        ret.second.translation = Float3::Transform(jointPos - offset1, rot1.Inverse());
        ret.second.rotation = jointRot / rot1;
    }
    
    return ret;
}

std::pair<PhysicsActor*, PhysicsActor*> JointSystemG::GetActors(const JointConnection& connect) const
{
    auto getActor = [this](ecs::EntityID entity, const std::string& boneName) {
        PhysicsActor* phyActor = nullptr;
        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
        if (entity)
        {
            if (!boneName.empty())
            {
                auto skeltComp = mGameWorld->GetComponent<SkeletonComponentG>(entity);
                if (!skeltComp.IsValid())
                {
                    return phyActor;
                }
                const SkeletonPhysicsNode* node = skeltComp.Read()->SkeletonPhy.GetNode(boneName.c_str());
                if (node)
                {
                    phyActor = node->mActor.get();
                }
                else
                {
                    LOG_ERROR("Joint want to link to bone [{}], however this bone have no PhysicsSkelton node bound", boneName.c_str());
                }
            }
            else
            {
                auto phyComp0 = mGameWorld->GetComponent<PhysicsComponentG>(entity);
                phyActor = phyComp0 ? phySys->GetPhysicsActor(phyComp0.Write()) : nullptr;
            }
        }
        return phyActor;
    };

    return {getActor(connect.target0, connect.bone0), getActor(connect.target1, connect.bone1)};
}

///////////////////////////////////////For Editor Export//////////////////////////////////
std::vector<JointConnection>& JointSystemG::GetConnections(cross::GameWorld* world, UInt64 entity)
{
    auto jointSys = world->GetGameSystem<JointSystemG>();
    auto jointComp = world->GetComponent<JointComponentG>(entity);
    return jointSys->GetConnections(jointComp.Write());
}

void JointSystemG::SetConnections(cross::GameWorld* world, UInt64 entity, const std::vector<JointConnection>& value)
{
    auto jointSys = world->GetGameSystem<JointSystemG>();
    auto jointComp = world->GetComponent<JointComponentG>(entity);
    const auto& jointWriter = jointComp.Write();
    jointSys->SetConnections(jointWriter, value);
    for (auto& conn : jointWriter->mConnections)
    {
        jointSys->OnTransformChange(jointWriter, conn);
        jointSys->OnRigidChange(jointWriter, conn);
        jointSys->OnConfigChange(jointWriter, conn);
    }
}
/////////////////////////////////////////End Editor ////////////////////////////////////////


}   // namespace cross
