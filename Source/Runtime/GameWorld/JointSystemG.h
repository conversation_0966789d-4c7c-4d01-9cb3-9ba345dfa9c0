#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/GameSystemBase.h"
#include "PhysicsEngine/PhysicsJoint.h"

namespace cross {
class JointSystemG;

//////////////////////////////////////////////////////////////////////////
struct JointConfig { };

struct JointConnection
{
    PhysicsJoint* mPhysicsJoint = nullptr;

    CEMeta(Serialize, Editor)
    ecs::EntityID target0 = ecs::EntityID::InvalidHandle();
    CEMeta(Serialize, Editor)
    std::string bone0;

    CEMeta(Serialize, Editor)
    ecs::EntityID target1 = ecs::EntityID::InvalidHandle();
    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    std::string bone1;


    CEMeta(<PERSON><PERSON><PERSON>, Editor)
    CECSAttribute(PropertyInfo(PropertyType = "Struct", ToolTips = "config"))
    PhysicsJointConfig config;

    CE_Serialize_Deserialize;
};

struct JointComponentG final : ecs::IComponent
{
    CEFunction(Reflect)
    static ecs::ComponentDesc* GetDesc();

protected:
    CEMeta(Serialize)
    std::vector<JointConnection> mConnections;
    friend class JointSystemG;


    CE_Serialize_Deserialize;
};

//////////////////////////////////////////////////////////////////////////
class JointSystemG final : public GameSystemBase
{
public:
    using JointComponentHandle = ecs::ComponentHandle<JointComponentG>;
    using JointComponentReader = ecs::ScopedComponentRead<JointComponentG>;
    using JointComponentWriter = ecs::ScopedComponentWrite<JointComponentG>;

    CEFunction(Reflect)
    static JointSystemG* CreateInstance();

    virtual void Release() override;

    CEFunction(Reflect)
    void OnBuildUpdateTasks(FrameParam* frameParam) override;

    virtual void NotifyAddRenderSystemToRenderWorld() override {}

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;


    void OnRigidChange(const JointComponentWriter& comp, JointConnection& connect);
    void OnTransformChange(const JointComponentWriter& comp, JointConnection& connect);
    void OnConfigChange(const JointComponentWriter& comp, JointConnection& connect);
public:
    static SerializeNode SerializeJointComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);
    static void DeserializeJointComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);
    static void PostDeserializeComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);
public:
    const std::vector<JointConnection>& GetConnections(const JointComponentReader& handle) const { return handle->mConnections; }
    void SetConnections(const JointComponentWriter& handle, const std::vector<JointConnection>& value) { handle->mConnections = value; }

    std::vector<JointConnection>& GetConnections(const JointComponentWriter& handle) { return handle->mConnections; }

    PhysicsJoint* CreateJoint(PhysicsActor * target0, PhysicsActor* target1, const PhysicsJointConfig& config);
    void DeleteJoint(PhysicsJoint* joint);

public:
    ////////////////////////////For editor cs export/////////////////////////////////
    CEMeta(Editor) static std::vector<JointConnection>& GetConnections(cross::GameWorld* world, UInt64 entity);
    CEMeta(Editor) static void SetConnections(cross::GameWorld* world, UInt64 entity, const std::vector<JointConnection>& value);
    ////////////////////////////////End editor/////////////////////////////////////

protected:
    JointSystemG();
    virtual ~JointSystemG();

    std::pair<Transform, Transform> GetLocalPoseOfActors(const JointComponentReader& comp, const JointConnection& conn) const;
    std::pair<PhysicsActor*, PhysicsActor*> GetActors(const JointConnection& connect) const;

    virtual RenderSystemBase* GetRenderSystem() override
    {
        return nullptr;
    }
};


}
