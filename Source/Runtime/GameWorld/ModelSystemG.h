#pragma once
#include "ECS/Develop/Framework.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Math/CrossMath.h"
#include "CECommon/Common/GameSystemBase.h"
#include "CECommon/Common/FrameStdContainer.h"
#include "Resource/MeshAssetDataResource.h"
#include "RenderEngine/RenderWorldConst.h"
#include "CEAnimation/Transform/AnimPose.h"
#include "RenderEngine/MeshBlendShapeUtil.h"
#include "RenderEngine/ModelSystemR.h"
#include "CECommon/Common/FrameStdContainer.h"

namespace cross {


struct SubModelProperty
{
    MaterialInterfacePtr mMaterial;

    CEProperty()
    std::string mMaterialPath{};

    CEProperty()
    bool mVisible{true};

    // For blend shape
    bool mHasBlendShape{false};
    ChannelWeightData mChannelWeight;

    CE_Serialize_Deserialize;
};

struct SingleLODModelProperty
{
    CEProperty()
    std::vector<SubModelProperty> mSubModelProperties{};

    CE_Serialize_Deserialize;
};

struct IndividualModel
{
    MeshAssetDataResourcePtr mAsset;
    // std::vector<SubModelProperty> mSubModelProperties;

    CEProperty()
    std::string mAssetPath{};

    CEProperty()
    std::vector<SingleLODModelProperty> mSingleLODModelProperties;

    FrameVector<SIMDMatrix>* mCompletePose{nullptr};
    anim::MeshPosePtr mPosePtr{nullptr};

    CEProperty()
    bool mVisible{true};

    CEProperty()
    bool mReceiveDecals{true};

    bool mHasBlendShape{false};
    bool mAutoBlendShapeByAnimation{true};

    CE_Serialize_Deserialize;
};

struct ModelComponentG : ecs::IComponent
{
    //struct SubModelProperty
    //{
    //    MaterialInterfacePtr mMaterial;
    //    std::vector<MaterialInterfacePtr> mLODMaterials; //0-LOD1, 1-LOD2...
    //    bool mVisible{ true };
    //    UInt8 mRenderPriority{ 0 };

    //    // For blend shape
    //    bool mHasBlendShape{false};
    //    std::vector<ChannelWeightData> mLODChannelWeights;
    //};

    CEFunction(Reflect)
    static ENGINE_API ecs::ComponentDesc* GetDesc();

public:
    CEProperty()
    std::vector<IndividualModel> mChangeableModels;

    CEProperty()
    IndividualModel mMainModel;

    MeshBatchInfo mBatchInfo;

    bool mEnableBigModel = true;   // May process big model specially

    CEProperty()
    bool mEnableGPUSkin = true;   // May process big model specially

    CEProperty(Editor, Script) 
    bool mEnabledIntersection = true;

    CEProperty(Editor, Script)
    bool mCacheableForDrawing = false;

    bool mEnable = false;

    CEProperty()
    EntityDistanceCulling mDistanceCulling;

    CE_Serialize_Deserialize;

    CEFunction(AdditionalDeserialize)
    void AdditionalDeserialize(const DeserializeNode& inNode, SerializeContext& context);

    friend class ModelSystemG;
};

struct ModelChangeData
{
    ecs::EntityID mEntity;
    UInt32 mModelIndex;
    MeshAssetDataResourcePtr mResourcePtr;
};

using ModelChangeEvent = SystemEvent<ModelChangeData>;

class ModelSystemR;
class ENGINE_API CEMeta(Cli) ModelSystemG : public GameSystemBase, public SystemEventManager<ModelChangeEvent>
{
    CESystemInternal(ComponentType = ModelComponentG) 
public :
    using ModelComponentHandle = ecs::ComponentHandle<ModelComponentG>;
    DEFINE_COMPONENT_READER_WRITER(ModelComponentG, ModelComponentReader, ModelComponentWriter)

    CEFunction(Reflect)
    static ModelSystemG* CreateInstance();
    virtual void Release() override;
    virtual void OnBeginFrame(FrameParam* frameParam) override;
    virtual void OnFirstUpdate(FrameParam* frameParam) override;
    virtual void OnEndFrame(FrameParam* frameParam) override;
    virtual RenderSystemBase* GetRenderSystem() override;
    virtual void NotifyAddRenderSystemToRenderWorld() override
    {
        mIsRenderObjectOwner = false;
    };

public:
    CEFunction(Editor, Script)
    bool IsSkeletalModel(const ModelComponentReader& modelH) const;

    bool SetModelAsset(const ModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr, UInt32 modelIndex);

    CEFunction(Editor, Script, Cli)
    bool SetModelAssetPath(const ModelComponentWriter& modelH, const std::string& assetpath, UInt32 modelIndex);
    CEFunction(Editor, Script)                                                                                                         // -1 modify all subModels
    bool SetModelMaterialPath(const ModelComponentWriter& modelH, const std::string& assetpath, int subModelIndex, UInt32 lodIndex, UInt32 modelIndex);   // -1 modify all subModels
    void SetModelLodMaterial(const ModelComponentWriter& modelH, MaterialInterfacePtr material, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex);
    CEFunction(Editor, Script)
    bool SetModelLodMaterialPath(const ModelComponentWriter& modelH, const std::string& materialPath, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex);

    CEFunction(Editor)
    void SetModelEnityDistanceCulling(const ModelComponentWriter& modelH, const EntityDistanceCulling& entityCulling);

    CEFunction(Editor)
    EntityDistanceCulling GetModelEnityDistanceCulling(const ModelComponentReader& model);

    int AddModelByAsset(const ModelComponentWriter& modelH, const MeshAssetDataResourcePtr& meshAssetDataResourcePtr);   // return index
    CEFunction(Editor, Script)
    int AddModelByAssetPath(const ModelComponentWriter& modelH, const std::string& assetPath);

    CEFunction(Editor, Script)
    void RemoveModel(const ModelComponentWriter& modelH, UInt32 modelIndex);

    CEFunction(Editor, Script)
    bool IsModelVisible(const ModelComponentReader& modelH, UInt32 modelIndex) const;
    CEFunction(Editor, Script)
    bool IsSubModelVisible(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, UInt32 lodindex) const;
    CEFunction(Editor, Script)
    //param forceUpdate will ignore isVisible check to forceUpdate 
    void SetModelVisibility(const ModelComponentWriter& modelH, bool isVisible, UInt32 modelIndex, bool forceUpdate = false);
    CEFunction(Editor, Script)
    void SetSubModelVisibility(const ModelComponentWriter& modelH, bool isVisible, int subModelIndex, UInt32 lodindex, UInt32 modelIndex);   // -1 modify all subModels

    CEFunction(Editor, Script) void SetModelReceiveDecals(const ModelComponentWriter& modelH, bool value, UInt32 modelIndex);
    CEFunction(Editor, Script) bool GetModelReceiveDecals(const ModelComponentReader& modelH, UInt32 modelIndex) const;

    void SetModelDirty(const ModelComponentWriter& modelH);
    CEFunction(Editor, Script)
    UInt32 GetModelCount(const ModelComponentReader& modelH) const
    {
        return (UInt32)modelH->mChangeableModels.size() + 1;
    }
    CEFunction(Cli, Editor, Script)
    UInt32 GetSubModelCount(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 lodIndex) const;
    
    CEFunction(Cli, Editor, Script)
    UInt32 GetLODCount(const ModelComponentReader& modelH, UInt32 modelIndex) const;
    
    CEFunction(Cli)
    static int Model_GetSubModelCount(cross::resource::MeshAssetDataResource* meshRes, UInt32 lodIndex)
    {
        Assert(meshRes->GetClassID() == ClassID(MeshAssetDataResource));
        return meshRes->GetAssetData()->GetMeshPartCount(lodIndex);
    }
    CEFunction(Cli)
    static cross::Resource* Model_GetMaterialResource(cross::IGameWorld * world, UInt64 entity, SInt32 modelIndex, SInt32 subModelIndex);
    CEFunction(Cli)
    static void Model_SetMaterialInstance(cross::IGameWorld * world, UInt64 entity, cross::resource::MaterialInterface * materialInterface, int subModelIndex, int modelIndex);
    CEFunction(Script)
    void SetModelMaterialFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, float propValue); 
    CEFunction(Script)
    float GetModelMaterialFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName);
    std::vector<float> GetModelMaterialVectorFloat(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName);

    CEFunction(Script)
    void SetModelMaterialBool(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, bool propValue, bool FX = false); 
    CEFunction(Script)
    bool GetModelMaterialBool(const ModelComponentReader& modelH, int subModelIndex, UInt32 modelIndex, const std::string& propName, bool FX = false);

    // only return first LOD's model property
    const SubModelProperty& GetSubModelProperty(const ModelComponentReader& modelH, UInt32 modelIndex, int subModelIndex) const
    {
        return GetModel(modelH, modelIndex).mSingleLODModelProperties[0].mSubModelProperties[subModelIndex];
    }

    int GetModelIndex(const ModelComponentReader& modelH, const std::string& modelName) const;
    int GetSubModelIndex(const ModelComponentReader& modelH, const std::string& subModelName, UInt32 modelIndex) const;
    std::string GetModelName(const ModelComponentReader& modelH, UInt32 modelIndex) const;
    std::string GetSubModelName(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex) const;

    const MeshAssetDataResourcePtr& GetModelAsset(const ModelComponentReader& modelH, UInt32 modelIndex) const
    {
        return GetModel(modelH, modelIndex).mAsset;
    }
    MaterialInterfacePtr GetModelLodMaterial(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 lodIndex, UInt32 modelIndex) const;
    std::vector<MaterialInterfacePtr> GetModelLodMaterialPtr(const ModelComponentReader& modelH, UInt32 subModelIndex, UInt32 modelIndex) const;

    bool SetModelAssetPath(const ModelComponentWriter& modelH, std::string const& assetpath);                            // Set main model
    bool SetModelMaterialPath(const ModelComponentWriter& modelH, const std::string& materialPath, int subModelIndex);   // Set main model

    const auto& GetChangeList() const
    {
        return mModelChangeList;
    }
    auto& GetChangeList()
    {
        return mModelChangeList;
    }
    // Composite bounding boxes of current mesh resources. Go to AABBSystem for cheaper bounding box with transform.
    BoundingBox GetCurrentBoundingBox(const ModelComponentReader& modelH) const;

    void OnMeshChanged(MeshAssetDataResourcePtr meshAsset);

public:
    // For Skinned mesh
    void SetSkeltModelPose(const ModelComponentWriter& modelH, UInt32 modelIndex, const anim::RootSpacePose* skeletonPose);
    CEFunction(Editor, Script)
    void SetSkeltModelGPUSkin(const ModelComponentWriter& modelH, bool enable);
    CEFunction(Editor, Script)
    bool GetSkeltModelGPUSkin(const ModelComponentReader& modelH) const;

    void RefreshSkelModelAABB(const ModelComponentWriter& modelH, UInt32 modelIndex);
    FrameVector<SIMDMatrix>* GetSkeltModelPose(const ModelComponentWriter& modelH, UInt32 modelIndex) const;

    // Intersection
    CEFunction(Editor, Script) 
    void SetIntersection(const ModelComponentWriter& modelH, bool enable);
    CEFunction(Editor, Script) 
    bool GetIntersection(const ModelComponentReader& modelH) const;


    // For model blend shape begin
private:
    bool IsSubModelHasBlendShape(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex) const;
    bool IsSubModelHasBlendShape(const ModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex) const;
    bool SetSubModelBlendShapeChannelWeight(const ModelComponentWriter& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, float weight, UInt32 lodIndex = 0);
    float GetSubModelBlendShapeChannelWeight(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, HashString channelName, UInt32 lodIndex = 0) const;
    const NameMap<HashString, UInt32>& GetSubModelBlendShapeChannelNameMap(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodIndex = 0) const;
public:
    CEFunction(Editor, Script)
    bool IsModelHasBlendShape(const ModelComponentReader& modelH, UInt32 modelIndex) const;
    CEFunction(Editor, Script)
    UInt32 GetModelBlendShapeChannelCount(const ModelComponentReader& modelH, UInt32 modelIndex);
    CEFunction(Editor) 
    void GetModelBlendShapeChannelNames(const ModelComponentReader& modelH, UInt32 modelIndex, std::vector<std::string>& OutNames);
    const NameMap<HashString, ChannelCollectInfo>& GetModelBlendShapeChannelMap(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 lodIndex = 0) const;
    CEFunction(Editor) 
    bool SetModelBlendShapeChannelWeight(const ModelComponentWriter& modelH, UInt32 modelIndex, HashString channelName, float weight, UInt32 lodIndex = 0);
    CEFunction(Editor)
    void GetModelAssetPath(const ModelComponentReader& modelH, UInt32 modelIndex, std::string& Result);
    CEFunction(Editor, Script)
    std::string GetModelMaterialPath(const ModelComponentReader& modelH, UInt32 modelIndex, UInt32 subModelIndex, UInt32 lodindex);
    float GetModelBlendShapeChannelWeight(const ModelComponentReader& modelH, UInt32 modelIndex, HashString channelName, UInt32 lodIndex = 0) const;

    void SetAutoBlendShapeByAnimation(ecs::EntityID entity, bool autoBlendShape);
    bool GetAutoBlendShapeByAnimation(ecs::EntityID entity) const;
    // For model blend shape end

    // For mesh streaming
public:
    CEFunction(Editor, Script, Cli)
    bool IsModelAssetStreamable(const ModelComponentReader& modelH, UInt32 modelIndex) const;
    CEFunction(Editor, Script, Cli)
    void SetModelAssetStreamable(const ModelComponentWriter& modelH, UInt32 modelIndex, bool enabled);

    ModelSystemG();

private:
    bool IsSupportGPUSkin(const ModelComponentReader& reader) const;
    bool IsGPUSkinEnabledInternal(const ModelComponentReader& reader) const;

protected:
    virtual ~ModelSystemG();

    virtual void NotifyEvent(const SystemEventBase& event, UInt32& flag) override;

    MeshBatchInfo GenerateBatchInfo(MeshAssetDataResourcePtr inMeshResource);

    inline const IndividualModel& GetModel(const ModelComponentReader& modelH, UInt32 index = 0) const
    {
        if (index == 0)
            return modelH->mMainModel;

        Assert(index <= modelH->mChangeableModels.size());
        return modelH->mChangeableModels[index - 1];
    }

    inline IndividualModel& GetModel(const ModelComponentWriter& modelH, UInt32 index = 0) const
    {
        if (index == 0)
            return modelH->mMainModel;

        Assert(index <= modelH->mChangeableModels.size());
        return modelH->mChangeableModels[index - 1];
    }

private:
    ModelSystemR* mRenderMeshSystem{nullptr};
    bool mIsRenderObjectOwner{true};
    ModelChangeList<ecs::EntityID> mModelChangeList;
    MaterialInterfacePtr mDefaultMaterial;

public:
    static SerializeNode SerializeModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr);

    static ModelComponentG CreateModelComp(const std::string& modelPath, const std::vector<std::string>& materialPaths, bool isVisible = true, float maxDis = 0.0);

    static void DeserializeModelComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr);

    static void PostDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void UpdateDeserializeModelComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId);

    static void GetResourceModelComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource);
};

}   // namespace cross
