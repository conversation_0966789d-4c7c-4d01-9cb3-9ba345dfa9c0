
#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Runtime/GameWorld/ReflectionProbeCullingSystemG.h"
#include "RenderEngine/ReflectionProbeCullingSystemR.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Threading/RenderingThread.h"

namespace cross
{
ReflectionProbeCullingSystemG* ReflectionProbeCullingSystemG::CreateInstance()
{
    ReflectionProbeCullingSystemG* system = new ReflectionProbeCullingSystemG();
    return system;
}

ReflectionProbeCullingSystemG::ReflectionProbeCullingSystemG()
{
    mRenderSystem = ReflectionProbeCullingSystemR::CreateInstance();
}

ReflectionProbeCullingSystemG::~ReflectionProbeCullingSystemG()
{
    if (mIsRenderObjectOwner && mRenderSystem)
    {
        mRenderSystem->Release();
        mRenderSystem = nullptr;
    }
}

void ReflectionProbeCullingSystemG::Release()
{
    delete this;
}

void ReflectionProbeCullingSystemG::NotifyAddRenderSystemToRenderWorld()
{
    mIsRenderObjectOwner = false;
}

RenderSystemBase* ReflectionProbeCullingSystemG::GetRenderSystem()
{
    return mRenderSystem;
}

}
