//#include "EnginePrefix.h"
//#include "Runtime/GameWorld/SimpleMeshBuilderG.h"
//#include "RenderEngine/SimpleMeshBuilderR.h"
//
//namespace cross
//{
//
//SimpleMeshBuilderG::SimpleMeshBuilderG()
//	:MeshBuilderBaseG("CrossSimpleMeshBuilder")
//{
//	mRenderObject = SimpleMeshBuilderR::CreateInstance();
//}
//
//SimpleMeshBuilderG::~SimpleMeshBuilderG()
//{
//	if (mOwnRenderObject)
//	{
//		mRenderObject->Release();
//	}
//	mRenderObject = nullptr;
//}
//
//cross::MeshBuilderBaseR* SimpleMeshBuilderG::GetRenderObject()
//{
//	return mRenderObject;
//}
//
//}