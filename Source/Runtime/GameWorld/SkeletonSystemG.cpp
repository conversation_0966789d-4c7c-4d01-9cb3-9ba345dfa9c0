#include "EnginePrefix.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/GameWorld.h"


#include "PhysicsSystemG.h"
#include "PhysicsEngine/PhysicsEngine.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/ModelSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/SkBuildSystemG.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "Runtime/Animation/Skeleton/SkeletonComponent.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysics.h"
#include "Runtime/Animation/Skeleton/SkeletonPhysicsResource.h"
#include "Resource/AssetStreaming.h"
#include "Threading/RenderingThread.h"
#include "RenderEngine/SkeletonSystemR.h"

using namespace cross::skeleton;

namespace cross
{
    SkeletonSystemG* SkeletonSystemG::CreateInstance()
    {
        auto sys = new SkeletonSystemG();
        return sys;
    }

    RenderSystemBase* SkeletonSystemG::GetRenderSystem()
    {
        return mRenderObject;
    }

    SkeletonSystemG::SkeletonSystemG()
    {
        mRenderObject = SkeletonSystemR::CreateInstance();
    }

    SkeletonSystemG::~SkeletonSystemG()
    {
        if (mIsRenderObjectOwner && mRenderObject)
        {
            mRenderObject->Release();
            mRenderObject = nullptr;
        }
    }

    void SkeletonSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
    {
        GameSystemBase::NotifyEvent(event, flag);

        if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
        {
            mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
        }
        else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
        {
            const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
            if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
            {
                for (UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
                {
                    const EntityDestroyEvent& entityLifeCycleEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
                    if (ecs::HasComponentMask<SkeletonComponentG>(entityLifeCycleEvent.mData.mChangedComponentMask))
                    {
                        auto skeltComp = mGameWorld->GetComponent<SkeletonComponentG>(entityLifeCycleEvent.mData.mEntityID);
                        skeltComp.Write()->SkeletonPhy.PendingRemove(mGameWorld);
                        skeltComp.Write()->SkeletonPhy.Clear();
                        // TODO(dango): Make sure that this is called on exclusive thread
                    }
                }
            }
        }
    }

    void SkeletonSystemG::InitSkeletonPhysics(const SkeletonCompWriter& skComp)
    {
        skComp->SkeletonPhy.Init(skComp->PosePtr, skComp->RunSkelt);
        skComp->SkeletonPhy.Clear();

        auto* physicsEngine = EngineGlobal::Inst().GetPhysicsEngine();
        auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();

        for (SkBoneHandle iBone = {0}; iBone < skComp->RunSkelt->GetBoneNumOfBoneTree(); ++iBone)
        {
            anim::CEName name = skComp->RunSkelt->GetBoneNameInBoneTree(iBone);
            const NodeTransform& trs = skComp->PosePtr->GetRootSpaceTransform(skComp->PosePtr->GetPoseIndexFromFilteredBoneIndex(iBone));
            skComp->SkeletonPhy.AddBoneNode(mGameWorld, skComp.GetEntityID(), name, iBone, trs.GetTranslation(), trs.GetRotation(), trs.GetScale());
        }
        const bool isEditor = EngineGlobal::GetSettingMgr()->GetAppStartUpType() == cross::AppStartUpType::AppStartUpTypeCrossEditor && mGameWorld->GetWorldType() != WorldTypeTag::PIEWorld;
        for (UInt32 i = 0; i < skComp->SkeletonPhy.GetResource()->GetJoints().size(); ++i)
        {
            const SkeletonJoint& joint = skComp->SkeletonPhy.GetResource()->GetJoints()[i];
            skComp->SkeletonPhy.AddJoint(
                mGameWorld,
                joint.mFromBone,
                skComp->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(joint.mFromBone),
                joint.mToBone,
                skComp->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(joint.mToBone),
                joint.mConfig,
                isEditor, 
                i);
        }
    }

    void SkeletonSystemG::UpdateSkeletonPhysics(const SkeletonCompWriter& handle)
    {
        if (handle->SkeletonPhy.Inited())
        {
            SCOPED_CPU_TIMING(GroupAnimation, "UpdateSkeletonPhysics");

            auto transCompReader = mGameWorld->GetComponent<WorldTransformComponentG>(handle.GetEntityID()).Read();
            auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
            handle->SkeletonPhy.SyncPoseToPhyWorld(handle->PosePtr.get(), NodeTransform{transSys->GetWorldScale(transCompReader), transSys->GetWorldRotation(transCompReader), transSys->GetWorldTranslation(transCompReader)});
            handle->SkeletonPhy.BackupPoseForNextFrame(handle->PosePtr.get());
        }
    }

    void SkeletonSystemG::Release()
    {
        delete this;
    }

    void SkeletonSystemG::NotifyAddRenderSystemToRenderWorld()
    {
        mIsRenderObjectOwner = false;
    }

    void SkeletonSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
    {
        auto elapsedTime = frameParam->GetDeltaTime();
        auto* frameAllocator = dynamic_cast<FrameAllocator*>(frameParam->GetFrameAllocator());
        auto NumThreads = (threading::TaskSystem::Get()->GetNumMasterThreads() + threading::TaskSystem::Get()->GetNumTaskThreads()) * 8;

        std::vector<TaskFunction*> skeltTasks;
        for (SInt32 Index = 0; Index < NumThreads; Index += 1)
        {
            skeltTasks.emplace_back(CreateTaskFunction(FrameTickStage::Update, {}, [this, Index, NumThreads, elapsedTime, frameParam, frameAllocator]() mutable
                {
                    auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();

                    const UInt32 MinBatchSize = 1;
                    auto queryResult = mGameWorld->Query<SkeletonComponentG, ModelComponentG, ecs::EntityIDComponent>();
                    auto EntityNum = queryResult.GetEntityNum();
                    UInt32 EntityPerTask = MinBatchSize;
                    if (EntityNum > MinBatchSize * NumThreads)
                    {
                        EntityPerTask = (UInt32)std::ceil(EntityNum * 1.0f / NumThreads);
                    }

                    UInt32 startIndex = Index * EntityPerTask, endIndex = startIndex + EntityPerTask >= EntityNum ? EntityNum : startIndex + EntityPerTask;
                    for (UInt32 entityIndex = startIndex; entityIndex < endIndex; entityIndex++)
                    {
                        auto&& [skComp, modelComp, entityIDComp] = queryResult[entityIndex];

                        if (!skComp.Read()->mEnable)
                        {
                            continue;
                        }

                        if (skComp.Read()->mEnable && skComp.Read()->PosePtr != nullptr && 
                            skComp.Read()->RunSkelt != nullptr)
                        {
                            // only when skeleton component is enable and pose updated,
                            // should we convert skeleton pose and update skeleton physics.
                            if (skComp.Read()->PoseUpdated)
                            {
                                SCOPED_CPU_TIMING(GroupAnimation, "PreparePoseForRendering");

                                // Blend with physics pose
                                if (skComp.Read()->SkeletonPhy.GetIsSimulating() && skComp.Read()->PhysicsPoseWeight > 0 && skComp.Read()->SkeletonPhy.GetPoseReady())
                                {
                                    SkeletonCompWriter witer = skComp.Write();
                                    Assert(witer->SkeletonPhy.Inited());
                                    anim::RootSpacePose* poseAnim = witer->PosePtr.get();
                                    anim::RootSpacePose* posePhy = witer->SkeletonPhy.GetPose().get();
                                    poseAnim->ConvertAllBoneToLocalSpace();
                                    posePhy->ConvertAllBoneToLocalSpace();
                                    poseAnim->BlendPose(*posePhy, witer->PhysicsPoseWeight, 1.0f - witer->PhysicsPoseWeight); 
                                    poseAnim->NormalizeRotations();
                                    //poseAnim->CopyPose(*posePhy);
                                }

                                skComp.Write(ecs::ComponentAccessFlag::SubComponentAccess2)->PoseUpdated = false;
                            }

                            // map skeleton pose to skeletal mesh pose if is skeletal model
                            if (modelSys->IsSkeletalModel(modelComp.Read()))
                            {
                                SCOPED_CPU_TIMING(GroupAnimation, "SetSkeltModelPose");
                                auto modelCount = modelSys->GetModelCount(modelComp.Read());
                                for (UInt32 modelIdx = 0; modelIdx < modelCount; ++modelIdx)
                                {
                                    modelSys->SetSkeltModelPose(modelComp.Write(), modelIdx, skComp.Read()->PosePtr.get());

                                    // Refresh skeleton model aabb
                                    modelSys->RefreshSkelModelAABB(modelComp.Write(), modelIdx);
                                }
                            }
                        }
                        else
                        {
                            // set skeletal mesh pose to null if skeleton component is disabled
                            if (modelSys->IsSkeletalModel(modelComp.Read()))
                            {
                                auto modelCount = modelSys->GetModelCount(modelComp.Read());
                                for (UInt32 modelIdx = 0; modelIdx < modelCount; ++modelIdx)
                                {
                                    modelSys->SetSkeltModelPose(modelComp.Write(), modelIdx, nullptr);
                                }
                            }
                        }
                    }
                }));
        }

        CreateTaskFunction(FrameTickStage::Update, skeltTasks, [this] 
        {
            SCOPED_CPU_TIMING(GroupPhysics, "Physics_Script_OnCollision");
            auto phySys = mGameWorld->GetGameSystem<PhysicsSystemG>();
            phySys->GetPhysicsScene()->LockWrite();
            auto queryResult = mGameWorld->Query<SkeletonComponentG>();
            for (const auto& skComp : queryResult)
            {
                // WARNING: 
                // its could happen that during last query this component is not enable
                // but enable now, dangerous.
                // myabe not? since we can make sure post deserialization is run before any system
                if (!skComp.Read()->mEnable)
                {
                    continue;
                }
                // update skeleton physics
                UpdateSkeletonPhysics(skComp.Write()); //physx needs single thread write
            }

            phySys->GetPhysicsScene()->UnlockWrite();
        });
    }

    void SkeletonSystemG::NotifyShutDownEngine()
    {
        //if (mRenderObject)
        //{
        //    auto* renderObject = mRenderObject;
        //    DispatchRenderingCommandWithToken([renderObject] {
        //        renderObject->NotifyShutDownEngine();
        //    });
        //}
    }

    void SkeletonSystemG::SetSkeletonCompEnable(const SkeletonCompWriter& handle, bool enable)
    {
        handle->mEnable = enable;

        DispatchRenderingCommandWithToken([renderSystem = mRenderObject, entity = handle.GetEntityID(), enable]
        {
            renderSystem->SetSkeletonCompEnable(entity, enable);
        }); 
    }

    bool SkeletonSystemG::GetSkeletonCompEnable(const SkeletonCompReader& handle) 
    { 
        return handle->mEnable;
    }

    void SkeletonSystemG::SetSkeletonPose(const SkeletonCompWriter& skComp, anim::SkeltPosePtr posePtr)
    {
        if (posePtr)
        {
            skComp->PosePtr = posePtr;
            skComp->PoseUpdated = true;
        }
    }

    const SkeltPosePtr SkeletonSystemG::GetSkeletonPose(const SkeletonCompReader& handle) const
    {
        return handle->PosePtr;
    }

    void SkeletonSystemG::ResetSkPoseToRefPose(const SkeletonCompWriter& skComp) 
    {
        if (skComp->PosePtr)
        {
            skComp->PosePtr->ResetToRefPose();
            skComp->PoseUpdated = true;
        }
    }

    void SkeletonSystemG::ResetRuntimeSkeleton(const SkeletonCompWriter& skComp) 
    {
        skComp->SkeletonAssetPtr.reset();
        skComp->RunSkelt.reset();
        skComp->PosePtr.reset();
        skComp->PoseUpdated = false;
    }

    bool SkeletonSystemG::SetSkeleton(const SkeletonCompWriter& handle, const std::string& skAssetpath)
    {
        // Grab runtime skeleton from resource
        const auto& resPtr = gAssetStreamingManager->LoadSynchronously(skAssetpath);
        if (resPtr == nullptr)
        {
            LOG_ERROR("Skeleton resource loading failed");
            return false;
        }

        auto skResPtr = cross::TypeCast<skeleton::SkeletonResource>(resPtr);
        if (skResPtr.get() == nullptr)
        {
            LOG_ERROR("Skeleton resource casting failed");
            return false;
        }

        handle->RunSkelt = std::make_shared<skeleton::Skeleton>();
        handle->RunSkelt->Initialize(&skResPtr->GetStreamingSkeleton());

        // Check Compatible with models
        auto modelSys = mGameWorld->GetGameSystem<ModelSystemG>();
        auto modelComp = mGameWorld->GetComponent<ModelComponentG>(handle.GetEntityID());
        // If model component exists and is skeletal model
        if (modelComp != modelComp.InvalidHandle() && modelSys->IsSkeletalModel(modelComp.Read()))
        {
            auto reader = modelComp.Read();

            UInt32 modelCount = modelSys->GetModelCount(reader);
            for (UInt32 idx = 0; idx < modelCount; ++idx)
            {
                const auto& meshAsset = modelSys->GetModelAsset(reader, idx);
                if (!handle->RunSkelt->IsCompatibleMesh(meshAsset->GetRefSkeleton()))
                {
                    //Assert(false && "Skeletal Mesh is not Compatible with Skeleton");
                    LOG_ERROR("Skeletal Mesh: {} is not Compatible with Skeleton: {}", meshAsset->GetName(), handle->SkeletonAssetPtr->GetName());
                    return false;
                }
            }
        }

        // Holding skeleton resource if Compatible checking passed
        handle->SkeletonAssetPtr = skResPtr;

        // Initialize skeleton pose
        {
            auto const& runRefSkelt = handle->RunSkelt->GetReferenceSkeleton();
            SkBoneHandle boneNum = { static_cast<UInt32>(runRefSkelt.GetRawBoneNum()) };

            std::vector<SkBoneHandle> allBones(boneNum);
            for (SkBoneHandle curBone = { 0 }; curBone < boneNum; ++curBone)
                allBones[curBone] = curBone;

            handle->PosePtr = std::make_shared<cross::anim::RootSpacePose>();
            handle->PosePtr->InitPose(allBones, handle->RunSkelt.get());
            handle->PoseUpdated = true;
        }

        // Initialize skeleton physics
        if (handle->SkeletonPhy.GetResource())
        {
            InitSkeletonPhysics(handle);
        }
        
        // Dispatch skeleton change event immediately
        {
            SkeletonModifyEvent event;
            event.mData.BindingEntity = handle.GetEntityID();
            DispatchImmediateEvent<SkeletonModifyEvent>(event);
        }

        return true;
    }

    void SkeletonSystemG::ResetSkeletonPhysics(const SkeletonCompWriter& handle)
    {
        handle->SkeletonPhy.Reset();
    }

    bool SkeletonSystemG::SetSkeletonPhysics(const SkeletonCompWriter& handle, const std::string& skPhyPath)
    {
        if (skPhyPath != "")
        {
            SkeltPhyResPtr res = cross::TypeCast<SkeletonPhysicsResource>(gAssetStreamingManager->LoadSynchronously(skPhyPath));
            handle->SkeletonPhy.SetResource(res);

            if (handle->SkeletonAssetPtr)
            {
                InitSkeletonPhysics(handle);
            }
        }
        return true;
    }

    std::string SkeletonSystemG::GetSkeletonPhysics(const SkeletonCompReader& handle)
    {
        const auto& res = handle->SkeletonPhy.GetResource();
        if (res)
        {
            return res->GetName();
        }
        return "";
    }

    void SkeletonSystemG::BeforeSkeletonPhysicsActorFetch(const SkeletonCompWriter& handle)
    {
        handle->SkeletonPhy.BeforeSkeletonPhysicsActorFetch();
    }

    void SkeletonSystemG::OnSkeletonPhysicsActorFetch(const SkeletonCompWriter& handle, skeleton::SkBoneHandle bone, const Float3& pos, const Quaternion& rot)
    {
        auto transSys = mGameWorld->GetGameSystem<TransformSystemG>();
        auto transComp = mGameWorld->GetComponent<WorldTransformComponentG>(handle.GetEntityID());
        const auto& worldPos = transSys->GetWorldTranslation(transComp.Read());
        const auto& worldRot = transSys->GetWorldRotation(transComp.Read());

        handle->SkeletonPhy.OnSkeletonPhysicsActorFetch(bone, worldPos, worldRot, pos, rot);
    }

    void SkeletonSystemG::FinishSkeletonPhysicsActorFetch(const SkeletonCompWriter& handle)
    {
        handle->SkeletonPhy.FinishSkeletonPhysicsActorFetch();
    }

    void SkeletonSystemG::UpdateSkeletonPhysicsData(const SkeletonCompWriter& handle, const cross::skeleton::SkeletonPhysicsResourceData& inValue)
    {
        handle->SkeletonPhy.UpdateSkeletonPhysicsData(inValue);
        handle->SkeletonPhy.Clear();
        if (handle->SkeletonPhy.Inited())
        {
            InitSkeletonPhysics(handle);
        }
    }

    void SkeletonSystemG::StartSimulateSkeletonPhysics(const SkeletonCompWriter& handle, float physicsPoseWeight)
    {
        handle->SkeletonPhy.SetIsSimulating(true);
        handle->PhysicsPoseWeight = physicsPoseWeight;
    }

    void SkeletonSystemG::StopSimulateSkeletonPhysics(const SkeletonCompWriter& handle)
    {
        handle->SkeletonPhy.SetIsSimulating(false);
    }


    std::string SkeletonSystemG::GetSkeletonAssetPath(const SkeletonCompReader& handle)
    {
        if (handle->SkeletonAssetPtr)
        {
            return handle->SkeletonAssetPtr->GetName();
        }
        return std::string("");
    }

    SerializeNode SkeletonSystemG::SerializeSkelteonComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
    {
        SerializeNode localJson;
        auto skeletCompPtr = static_cast<SkeletonComponentG*>(componentPtr);

        localJson["Path"] = skeletCompPtr->SkeletonAssetPtr != nullptr ? skeletCompPtr->SkeletonAssetPtr->GetGuid_Str() : "";
        localJson["PhysicsPath"] = skeletCompPtr->SkeletonPhy.GetResource() ? skeletCompPtr->SkeletonPhy.GetResource()->GetSerialNode() : "";
        return localJson;
    }

    void SkeletonSystemG::DeserializeSkeletonComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
    {
    }

    void SkeletonSystemG::PostDeserializeSkeletonComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
    {
        auto skeletonSys = gameWorld->GetGameSystem<SkeletonSystemG>();
        auto skeletCompPtr = static_cast<SkeletonComponentG*>(componentPtr);
        auto writer = gameWorld->GetComponent<SkeletonComponentG>(entityId).Write();

        auto skeletonPath = json["Path"].AsString();
        if (skeletonPath != "")
        {
            skeletonSys->SetSkeleton(writer, skeletonPath);
        }

        auto phyNode = json.HasMember("PhysicsPath");
        if (phyNode)
        {
            auto phyPath = Resource::GetPathFromSerialNode(*phyNode);
            skeletonSys->SetSkeletonPhysics(writer, phyPath);
        }
        skeletonSys->SetSkeletonCompEnable(writer, true);
    }

    void SkeletonSystemG::UpdateDeserializeSkeletonComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId) {}

    void SkeletonSystemG::GetResourceSkeletonComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr, ResourcePtr resource) {
        auto skeletCompPtr = static_cast<SkeletonComponentG*>(componentPtr);
        if (skeletCompPtr->SkeletonAssetPtr)
        {
            resource->AddReferenceResource(skeletCompPtr->SkeletonAssetPtr->GetGuid_Str());
        }
        if (skeletCompPtr->SkeletonPhy.GetResource())
        {
             resource->AddReferenceResource(skeletCompPtr->SkeletonPhy.GetResource()->GetGuid_Str());
        }
    }

    void SkeletonSystemG::OnFirstUpdate(FrameParam* frameParam)
    {
    }

}
