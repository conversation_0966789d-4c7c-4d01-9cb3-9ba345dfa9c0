#include "EnginePrefix.h"
#include "ECS/Develop/Framework/Types.h"
#include "CrossBase/Template/TypeCast.hpp"
#include "CrossBase/String/UniqueString.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "CECommon/Common/FrameContainer.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/SkeletonSystemG.h"
#include "Runtime/GameWorld/SkeltSocketSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "CEAnimation/Skeleton/Skeleton.h"
#include "Runtime/Animation/Skeleton/SocketComponent.h"
#include "Resource/AssetStreaming.h"
#include "Threading/RenderingThread.h"

namespace cross
{
    SkeltSocketSystemG* SkeltSocketSystemG::CreateInstance()
    {
        return new SkeltSocketSystemG();
    }

    SkeltSocketSystemG::SkeltSocketSystemG()
    {}

    SkeltSocketSystemG::~SkeltSocketSystemG()
    {}

    void SkeltSocketSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
    {
        GameSystemBase::NotifyEvent(event, flag);
        if (event.mEventType == OnSystemAddToGameWorldEvent::sEventType || event.mEventType == GameWorldSystemChangedEvent::sEventType)
        {
            // Subscribe events when add to world
            auto transformSys = mGameWorld->GetGameSystem<TransformSystemG>();
            transformSys->SubscribeRemainedEvent<TransformJointEvent>(this, true);
        }
        else if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
        {
            auto transformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
            auto& remainedEvent = static_cast<const RemainedEventUpdatedEvent&>(event);

            if (remainedEvent.mData.mRemainedEventType == TransformJointEvent::sEventType)
            {
                for (auto index = remainedEvent.mData.mFirstIndex; index <= remainedEvent.mData.mLastIndex; index++)
                {
                    auto& e = transformSystem->GetRemainedEvent<TransformJointEvent>(index);
                    auto& eventData = e.mData;
                    auto socketComp = mGameWorld->GetComponent<SkeltSocketComponentG>(eventData.mChildEntity);
                    if (socketComp.IsValid())
                    {
                        auto boneName = socketComp.Read()->BoneName;
                        auto reltvTrans = socketComp.Read()->ReltvTrans;
                        AttachEntity(socketComp.Write(), eventData.mNewParentEntity, boneName, reltvTrans);
                    }
                }
            }
        }
    }

    void SkeltSocketSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
    {
        auto transSystem = mGameWorld->GetGameSystem<TransformSystemG>();

        SInt32 numThreads = threading::TaskSystem::Get()->GetNumMasterThreads() + threading::TaskSystem::Get()->GetNumTaskThreads();
        const SInt32 minSizeForSingleThread = 10;

        // grab all sockets for execute
        auto queryResult = mGameWorld->Query<SkeltSocketComponentG, LocalTransformComponentG>();
        // grab single executable thread's task number
        const SInt32 entityNum = queryResult.GetEntityNum();
        const SInt32 taskCountForSingleThread = (std::max)((SInt32)std::ceil(entityNum * 1.0f / numThreads), minSizeForSingleThread);

        SInt32 leftEntityNum = entityNum;

        std::vector<ecs::EntityID> entities;
        entities.reserve(entityNum);
        for (const auto& comps : queryResult)
        {
            entities.push_back(std::get<0>(comps).GetEntityID());
        }

        for (SInt32 Index = 0; Index < numThreads; Index += 1)
        {
            CreateTaskFunction(FrameTickStage::Update, {}, [this, Index, numThreads, taskCountForSingleThread, transSystem, entityNum, entities]() mutable 
            {
                // grab start index for current thread
                SInt32 startIndex = Index * taskCountForSingleThread;

                for (SInt32 curIndex = startIndex; curIndex < std::min(startIndex + taskCountForSingleThread, entityNum); ++curIndex)
                {
                    auto entity = entities[curIndex];
                    if (!mGameWorld->IsEntityAlive(entity))
                        continue;

                    auto&& [skeltSocketComp, localTransComp] = mGameWorld->GetComponent<SkeltSocketComponentG, LocalTransformComponentG>(entity);


                    if (skeltSocketComp.Read()->BoneH == SkBoneHandle::InvalidHandle())
                        continue;

                    auto socketingEntity = skeltSocketComp.GetEntityID();
                    if (socketingEntity == ecs::EntityID::InvalidHandle())
                        continue;

                    auto attchedEntity = transSystem->GetEntityParent(socketingEntity);
                    if (attchedEntity == ecs::EntityID::InvalidHandle())
                        continue;

                    {
                        std::scoped_lock lock(mMutex);

                        auto attchedSkeletonComp = mGameWorld->GetComponent<SkeletonComponentG>(attchedEntity);
                        if (attchedSkeletonComp == attchedSkeletonComp.InvalidHandle() || attchedSkeletonComp.Read()->PosePtr == nullptr)
                            continue;

                        PoseBoneHandle curPoseH = attchedSkeletonComp.Write()->PosePtr->GetPoseIndexFromFilteredBoneIndex(skeltSocketComp.Read()->BoneH);
                        Assert(PoseBoneHandle::InvalidHandle() != curPoseH);

                        auto attchedBoneTrans = attchedSkeletonComp.Read()->PosePtr->GetRootSpaceTransform(curPoseH);
                        attchedBoneTrans = skeltSocketComp.Read()->ReltvTrans * attchedBoneTrans;

                        transSystem->SetLocalTranslation(localTransComp.Write(), attchedBoneTrans.GetTranslation());
                        transSystem->SetLocalRotation(localTransComp.Write(), attchedBoneTrans.GetRotation());
                        transSystem->SetLocalScale(localTransComp.Write(), attchedBoneTrans.GetScale());
                    }
                }
            });

            // no more valid task exists, out early
            leftEntityNum -= taskCountForSingleThread;
            if (leftEntityNum <= 0)
                break;
        }
    }

    bool SkeltSocketSystemG::SetSocket(const SocketCompWriter& inHandle, std::string inAttachedBone, Float3 const& inTranslate, Quaternion const& inRotate, Float3 const& inScale)
    {
        auto socketingEntity = inHandle.GetEntityID();
        if (socketingEntity == ecs::EntityID::InvalidHandle())
            return false;

        inHandle->BoneName = CEName(inAttachedBone.c_str());
        inHandle->ReltvTrans.SetTranslation(inTranslate);
        inHandle->ReltvTrans.SetRotation(inRotate);
        inHandle->ReltvTrans.SetScale(inScale);

        auto transSystem = mGameWorld->GetGameSystem<TransformSystemG>();
        // This means if you want to attach an entity with SkeltSocketComponentG to a skeletal mesh,
        // you should make entity be child of skeletal mesh entity.
        auto attchedEntity = transSystem->GetEntityParent(socketingEntity);

        return AttachEntity(inHandle, attchedEntity, inHandle->BoneName, inHandle->ReltvTrans);
    }

    bool SkeltSocketSystemG::AttachEntity(const SocketCompWriter& inHandle, ecs::EntityID attchedEntity, CEName boneName, const NodeTransform& inTranslate)
    {
        inHandle->BoneName = boneName;
        inHandle->ReltvTrans = inTranslate;

        if (attchedEntity == ecs::EntityID::InvalidHandle())
            return false;

        auto attchedSkeleton = mGameWorld->GetComponent<SkeletonComponentG>(attchedEntity);
        if (!attchedSkeleton.IsValid() || attchedSkeleton.Read()->PosePtr == nullptr)
            return false;

        auto attchedBoneH = attchedSkeleton.Read()->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(boneName);
        if (attchedBoneH == SkBoneHandle::InvalidHandle())
            return false;

        inHandle->BoneH = attchedBoneH;
        return true;
    }
    
    SerializeNode SkeltSocketSystemG::SerializeSkeltSocketComponent(ISerializeWorld* serializeWorld, ecs::IComponent* componentPtr)
    {
        SerializeNode localJson;
        auto skeletSocketCompPtr = static_cast<SkeltSocketComponentG*>(componentPtr);

        localJson["Bone"] = skeletSocketCompPtr->BoneName.GetCString();
        localJson["TRS"] = { "mTranslation"_k = skeletSocketCompPtr->ReltvTrans.GetTranslation().Serialize(),
                            "mScale"_k = skeletSocketCompPtr->ReltvTrans.GetScale().Serialize(),
                            "mRotation"_k = skeletSocketCompPtr->ReltvTrans.GetRotation().Serialize() };
        return localJson;
    }

    void SkeltSocketSystemG::DeserializeSkeltSocketComponent(ISerializeWorld* serializeWorld, const DeserializeNode& json, ecs::IComponent* componentPtr)
    {
        if (json.IsNull())
            return;

        auto skeletSocketCompPtr = static_cast<SkeltSocketComponentG*>(componentPtr);
        if (json.IsObject())
        {
            skeletSocketCompPtr->BoneName = UniqueString(json["Bone"].AsString().c_str());

            auto trsJson = json["TRS"];
            if (trsJson.IsObject())
            {
                cross::Float3A temp_t;
                temp_t.Deserialize(trsJson["mTranslation"]);

                cross::QuaternionA temp_r;
                temp_r.Deserialize(trsJson["mRotation"]);

                cross::Float3A temp_s;
                temp_s.Deserialize(trsJson["mScale"]);

                skeletSocketCompPtr->ReltvTrans.SetTranslation(temp_t);
                skeletSocketCompPtr->ReltvTrans.SetRotation(temp_r);
                skeletSocketCompPtr->ReltvTrans.SetScale(temp_s);
            }
        }
    }

    void SkeltSocketSystemG::PostDeserializeSkeltSocketComponent(const DeserializeNode& json, ecs::IComponent* componentPtr, GameWorld* gameWorld, ecs::EntityID entityId)
    {
        auto parentid = gameWorld->GetGameSystem<TransformSystemG>()->GetEntityParent(entityId);
        if (parentid != ecs::EntityID::InvalidHandle()) 
        {
            auto parentskeleton = gameWorld->GetComponent<SkeletonComponentG>(parentid);
            if (parentskeleton.IsValid()) 
            {
                auto skeletSocketCompPtr = static_cast<SkeltSocketComponentG*>(componentPtr);
                if (skeletSocketCompPtr && parentskeleton.mComponent->RunSkelt)
                {
                    skeletSocketCompPtr->BoneH = parentskeleton.mComponent->RunSkelt->GetReferenceSkeleton().FindRawBoneIndex(CEName{skeletSocketCompPtr->BoneName});
                }
            }
        }
    }

    Float3 SkeltSocketSystemG::GetReltvTranslation(const SocketCompReader& inHandle) { return inHandle->ReltvTrans.GetTranslation(); }

    Quaternion SkeltSocketSystemG::GetReltvRotation(const SocketCompReader& inHandle) { return inHandle->ReltvTrans.GetRotation(); }

    Float3 SkeltSocketSystemG::GetReltvScale(const SocketCompReader& inHandle) { return inHandle->ReltvTrans.GetScale(); }
    std::string SkeltSocketSystemG::GetBoneName(const SocketCompReader& inHandle)
    {
        return std::string{inHandle->BoneName.GetCString()};
    }
}
