#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/ComponentSystemDescSystem.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Threading/RenderingThread.h"
#include "Runtime/GameWorld/WorldPartition/EntityHashSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/AABBSystemG.h"
#include "Runtime/GameWorld/TransformSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "Runtime/GameWorld/EntityMetaSystem.h"
#include "Runtime/GameWorld/Prefab/PrefabManager.h"


DECLARE_CPU_TIMING_GROUP(GroupGameWorld);

namespace cross
{
EntityHashSystemG* EntityHashSystemG::CreateInstance()
{
    EntityHashSystemG* system = new EntityHashSystemG();
    return system;
}

EntityHashSystemG::EntityHashSystemG()
{
}

EntityHashSystemG::~EntityHashSystemG()
{
}

void EntityHashSystemG::Release()
{
    delete this;
}

void EntityHashSystemG::NotifyAddRenderSystemToRenderWorld()
{
}

void EntityHashSystemG::OnBuildUpdateTasks(FrameParam* frameParam)
{
    // TODO(yuanwan) Runtime may not need these hashing operation
    CreateTaskFunction<threading::ThreadID::GameThreadLocal>(FrameTickStage::Update, {}, [this]
    {
        SCOPED_CPU_TIMING(GroupGameWorld, "EntityHashUpdate");

        // HashWay should be processed even if there is only one block because it can be put in Preblock
        auto& hashWayChangeList = mTransformSystem->GetHashWayChangeList();
        const UInt32 hashChangeListSize = hashWayChangeList.GetCount();
        for (UInt32 changeListIndex = 0; changeListIndex < hashChangeListSize; ++changeListIndex)
        {
            auto& entity = (hashWayChangeList.GetData(changeListIndex));
            if (mGameWorld->BelongCustomBlock(entity))
                continue;
            auto&& tertiaryRoot = GetTertiaryRoot(entity);
            if (!tertiaryRoot)
                continue;
            tRearrangeTertiarySet.emplace(tertiaryRoot);
        }
        // TODO(wwt) Fixed crash first in runtime
        if (mLoadingSystem->GetWorldBlockManager()->GetWorkingState() == WorldBlockManager::WorkingState::Idle && mLoadingSystem->GetBlockGrid())
        {
            UInt32 transformJointEventCount = mTransformSystem->GetRemainedEventCount<TransformJointEvent>(true);
            for (UInt32 i = 0; i < transformJointEventCount; ++i)
            {
                const auto& eventData = mTransformSystem->GetRemainedEvent<TransformJointEvent>(i).mData;
                // check alive
                if (!mGameWorld->IsEntityAlive(eventData.mChildEntity) || !mGameWorld->IsEntityAlive(eventData.mNewParentEntity))
                    continue;
                // check parent
                if (eventData.mOldParentEntity == eventData.mNewParentEntity)
                    continue;
                // check secondaryRoot
                auto&& tertiaryRoot = GetTertiaryRoot(eventData.mChildEntity);
                if (!tertiaryRoot)
                {
                    auto&& secondaryRoot = GetSecondaryRoot(eventData.mChildEntity);
                    if (secondaryRoot && (secondaryRoot == eventData.mChildEntity || !GetEntityBlock(mLoadingSystem->GetBlockGrid(), eventData.mChildEntity)))
                    {
                        tRearrangeSecondarySet.emplace(eventData.mChildEntity);
                    }
                }
                else
                {
                    auto srcBlock = GetEntityBlock(mLoadingSystem->GetBlockGrid(), eventData.mChildEntity);
                    if (tertiaryRoot == eventData.mChildEntity)
                    {
                        if (!srcBlock || eventData.mOldParentEntity)
                        {
                            tRearrangeTertiarySet.emplace(eventData.mChildEntity);
                        }
                    }
                    else
                    {
                        auto targetBlock = GetEntityBlock(mLoadingSystem->GetBlockGrid(), tertiaryRoot);
                        if (targetBlock && srcBlock != targetBlock)
                        {
                            tMoveToTertiarySet.emplace(eventData.mChildEntity);
                        }
                    }
                }
            }
        }
        for (const auto& entity : tRearrangeSecondarySet)
        {
            RearrageSecondaryRootEntity(entity);
        }
        for (const auto& entity : tRearrangeTertiarySet)
        {
            MoveToTargetBlock(entity, true, nullptr);
            RearrageTertiaryRootEntity(entity);
        }
        for (const auto& entity : tMoveToTertiarySet)
        {
            MoveToTertiaryRootEntity(entity);
            mGameWorld->MoveEntityToRuntimeBlockStore(entity);
        }
        tRearrangeSecondarySet.clear();
        tRearrangeTertiarySet.clear();
        tMoveToTertiarySet.clear();
    });
}

void EntityHashSystemG::NotifyEvent(const SystemEventBase& event, UInt32& flag)
{
    GameSystemBase::NotifyEvent(event, flag);

    if (event.mEventType == RemainedEventUpdatedEvent::sEventType)
    {
        if (mLoadingSystem->GetWorldBlockManager()->GetWorkingState() == WorldBlockManager::WorkingState::Idle && mLoadingSystem->GetBlockGrid())
        {
            const RemainedEventUpdatedEvent& e = static_cast<const RemainedEventUpdatedEvent&>(event);
            if (e.mData.mRemainedEventType == EntityDestroyEvent::sEventType)
            {
                for (UInt32 eventIndex = e.mData.mFirstIndex; eventIndex <= e.mData.mLastIndex; ++eventIndex)
                {
                    const auto& lifeEvent = mGameWorld->GetRemainedEvent<EntityDestroyEvent>(eventIndex);
                    auto& eventData = lifeEvent.mData;
                    // Process deleted entities, entities created are processed in transform ChangeList
                    if ((eventData.mEventFlag & EntityLifeCycleEventFlag::DestroyEntity) == EntityLifeCycleEventFlag::None)
                        continue;
                    ecs::EntityID secondaryRoot = GetSecondaryRoot(eventData.mEntityID);
                    if (secondaryRoot && secondaryRoot == eventData.mEntityID)
                    {
                        MoveToTargetBlock(secondaryRoot, true, nullptr);
                    }
                }
            }
        }
    }
    else if (event.mEventType == GameWorldSystemChangedEvent::sEventType)
    {
        if (!mMetaSystem) mMetaSystem = mGameWorld->GetGameSystem<EntityMetaSystem>();
        if (!mTransformSystem) mTransformSystem = mGameWorld->GetGameSystem<TransformSystemG>();
        if (!mLoadingSystem) mLoadingSystem = mGameWorld->GetGameSystem<WorldLoadingSystemG>();
        if (!mAABBSystem) mAABBSystem = mGameWorld->GetGameSystem<AABBSystemG>();
        mGameWorld->SubscribeRemainedEvent<EntityDestroyEvent>(this, true);
    }
}

void EntityHashSystemG::OnFirstUpdate(FrameParam* frameParam)
{
    mTransformSystem->SubscribeRemainedEvent<TransformJointEvent>(this, false);
}

void EntityHashSystemG::RearrangeWorld()
{
    MoveToTargetBlock(mTransformSystem->GetRootEntity(), true, mLoadingSystem->GetBlockGrid()->GetPreBlock());
    mTransformSystem->TraverseHierarchyBreadth(mTransformSystem->GetRootEntity(), [this](ecs::EntityID entity)
    {
        ecs::EntityID&& secondaryRoot = GetSecondaryRoot(entity);
        if (!secondaryRoot || mGameWorld->BelongCustomBlock(secondaryRoot))
            return;
        if (secondaryRoot == entity)
        {
            mTransformSystem->TraverseHierarchyBreadth(entity, [this](ecs::EntityID entity) { mMetaSystem->SetBelongedBlockInstance(mGameWorld->GetComponent<ecs::EntityMetaComponentG>(entity).Write(), nullptr); });
            RearrageSecondaryRootEntity(secondaryRoot);
        }
    });
}

WorldPartitionBlock* EntityHashSystemG::GetEntityTargetBlock(WorldPartitionBlockGrid* blockGrid, ecs::EntityID entity)
{
    WorldPartitionBlock* targetBlock = nullptr;
    UInt8 blockGettingWay = static_cast<UInt8>(WorldPartitionBlockGrid::BlockGettingWay::Aggressive);
    auto rootTransformCompReader = mGameWorld->GetComponent<WorldTransformComponentG>(entity).Read();
    EntityHashWay hashWay = mTransformSystem->GetEntityHashWay(rootTransformCompReader);
    switch (hashWay)
    {
    case EntityHashWay::PreLoad:
    {
        targetBlock = blockGrid->GetPreBlock();
        break;
    }
    case EntityHashWay::AlwaysLoad:
    {
        targetBlock = blockGrid->GetAlwaysLoadBlock(blockGettingWay);
        break;
    }
    case EntityHashWay::Position:
    case EntityHashWay::BoudingBox:
    {
        // Get BoundingBox of the tree
        BoundingBox box{BoundingBox::Flags::MergeIdentity};
        mTransformSystem->TraverseHierarchyDepth(entity, [this, &box](ecs::EntityID entity)
        {
            auto aabbComp = mGameWorld->GetComponent<AABBComponentG>(entity);
            if (aabbComp.IsValid())
                BoundingBox::CreateMerged(box, box, mAABBSystem->GetWorldAABB(aabbComp.Read()));
            else
                box.Encapsulate(mTransformSystem->GetWorldTranslation(mGameWorld->GetComponent<WorldTransformComponentG>(entity).Read()));
        });
        targetBlock = blockGrid->GetBlockCanHold(box, 0.66f, blockGettingWay); // appropriate percentage
        break;
    }
    default:
        break;
    }
    // Assert(targetBlock && targetBlock->GetBlockInstance());
    return targetBlock;
}

WorldPartitionBlock* EntityHashSystemG::GetEntityBlock(WorldPartitionBlockGrid* blockGrid, ecs::EntityID entity)
{
    if (!mGameWorld->IsEntityAlive(entity))
    {
        return nullptr;
    }
    auto metaSys = mGameWorld->GetGameSystem<EntityMetaSystem>();
    auto metaComp = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(entity);
    auto sourceBlock = metaSys->GetBelongedBlockInstance(metaComp.Read());
    return sourceBlock ? blockGrid->GetBlock(sourceBlock->GetBlockId().GetString()) : nullptr;
}

void EntityHashSystemG::MoveToSecondaryRootEntity(ecs::EntityID root)
{
    auto blockGrid = mLoadingSystem->GetBlockGrid();
    WorldPartitionBlock* targetBlock = blockGrid->GetLevels().size() > 1 ? blockGrid->GetPreBlock() : GetEntityTargetBlock(blockGrid, root);
    if (targetBlock == blockGrid->GetPreBlock())
    {
        MoveToTargetBlock(root, false, nullptr);
        auto metaCom = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(root);
        mMetaSystem->SetBelongedBlockInstance(metaCom.Write(), targetBlock->GetBlockInstance());
        targetBlock->GetBlockInstance()->AddEntity(mGameWorld, root);
    }
    else
    {
        MoveToTargetBlock(root, false, targetBlock);
    }
}

void EntityHashSystemG::MoveToTertiaryRootEntity(ecs::EntityID root)
{
    auto&& secondaryRoot = GetTertiaryRoot(root);
    auto srcBlock = GetEntityBlock(mLoadingSystem->GetBlockGrid(), root);
    auto targetBlock = GetEntityBlock(mLoadingSystem->GetBlockGrid(), secondaryRoot);
    if (srcBlock != targetBlock)
    {
        mTransformSystem->TraverseHierarchyDepth(root, [this, &targetBlock](ecs::EntityID entity) {
            auto metaCom = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(entity);
            mMetaSystem->SetBelongedBlockInstance(metaCom.Write(), targetBlock->GetBlockInstance());
            targetBlock->GetBlockInstance()->AddEntity(mGameWorld, entity);
        });
    }
}

void EntityHashSystemG::RearrageSecondaryRootEntity(ecs::EntityID root) {
    MoveToSecondaryRootEntity(root);
    if (GetEntityBlock(mLoadingSystem->GetBlockGrid(), root) == mLoadingSystem->GetBlockGrid()->GetPreBlock())
    {
        mTransformSystem->TraverseChildren(root, [this](ecs::EntityID child) { RearrageTertiaryRootEntity(child); });
    }
}

void EntityHashSystemG::RearrageTertiaryRootEntity(ecs::EntityID root)
{
    auto metaComp = mGameWorld->GetComponent<ecs::EntityMetaComponentG>(root);
    if (!mMetaSystem->IsSerializable(metaComp.Read()) || mGameWorld->BelongCustomBlock(root))
        return;
    WorldPartitionBlock* targetBlock = GetEntityTargetBlock(mLoadingSystem->GetBlockGrid(), root);
    MoveToTargetBlock(root, false, targetBlock);
}

void EntityHashSystemG::MoveToTargetBlock(ecs::EntityID root, bool onlfSelf, WorldPartitionBlock* targetBlock) 
{
    auto parentBlock = GetEntityBlock(mLoadingSystem->GetBlockGrid(), mTransformSystem->GetEntityParent(root));
    if (targetBlock) 
    {
        targetBlock->GetBlockInstance()->AddEntity(mGameWorld, root);
    }
    mLoadingSystem->GetWorldBlockManager()->MoveToBlockRoot(root, onlfSelf, targetBlock ? targetBlock->GetBlockInstance() : nullptr, parentBlock != targetBlock);
}

ecs::EntityID EntityHashSystemG::GetSecondaryRoot(ecs::EntityID entity)
{
    if (entity == mTransformSystem->GetRootEntity()) 
        return ecs::EntityID();
    ecs::EntityID secondaryRoot = entity;
    ecs::EntityID parent = mTransformSystem->GetEntityParent(secondaryRoot);
    if (!parent) 
        return parent;
    while (parent != mTransformSystem->GetRootEntity())
    {
        secondaryRoot = parent;
        parent = mTransformSystem->GetEntityParent(secondaryRoot);
        if (!parent)
            return ecs::EntityID();
    }
    return secondaryRoot;
}

ecs::EntityID EntityHashSystemG::GetTertiaryRoot(ecs::EntityID entity)
{
    const auto& worldRoot = mTransformSystem->GetRootEntity();
    ecs::EntityID parent = mTransformSystem->GetEntityParent(entity);
    if (!parent)
        return ecs::EntityID();
    ecs::EntityID grandParent = mTransformSystem->GetEntityParent(parent);
    if (!grandParent)
        return ecs::EntityID();
    ecs::EntityID teriaryRoot = entity;
    while (grandParent != worldRoot)
    {
        teriaryRoot = parent;
        parent = mTransformSystem->GetEntityParent(teriaryRoot);
        grandParent = mTransformSystem->GetEntityParent(parent);
        if (!parent)
            return ecs::EntityID();
    }
    return teriaryRoot;
}
}
