#pragma once
#include <algorithm>
#include "Runtime/GameWorld/WorldPartition/WorldPartitionBasis.h"
#include "Runtime/GameWorld/WorldPartition/WorldPartitionBlock.h"

namespace cross
{
    class WorldLoadingSystemG;

    class BlockResourceStateHandle
    {
    };

    enum class CEMeta(Editor) WorldLoadingPolicy
    {
        Streaming = 0,
        All       = 1,
    };

    class WorldPartitionPolicy
    {
    public:
        WorldPartitionPolicy(WorldLoadingSystemG* loadSys)
        {
            mLoadSystem = loadSys;
        }

        ~WorldPartitionPolicy() = default;
    
    public:
        bool PreUpdateBlock();

        void UpdateBlockState(WorldPartitionBlockPtr block);

        void SetPolicy(WorldLoadingPolicy p)
        {
            mPolicy = p;
        }

        auto GetPolicy() { return mPolicy; }

    private:
        //@brief: get all need to be active block
        //@param: activeList is a return value, tell which block need to active
        void GetAllStreamingBlocks(ChangedBlockList& activeList);

        void GetStreamingBlocksAllPolicy(ChangedBlockList& activeList);

        SInt32 Index(const SInt32& index, SInt32 max, SInt32 min = 0) {
            return std::clamp<SInt32>(index, min, max-1);
        }

        //@brief: DistancePolicy
        void GetStreamingBlocksDistancePolicy(ChangedBlockList& activeList);

        void SetStateForBlock(WorldPartitionBlockState state, ChangedBlockList& activeList)
        {
            for (auto it = activeList.begin(); it != activeList.end(); it++)
            {
                (*it)->SetState(state);
            }
        }

    private:
        WorldLoadingSystemG* mLoadSystem = nullptr;
        WorldLoadingPolicy mPolicy  = WorldLoadingPolicy::All;

        //@brief: block need activated is in loading list, you can check the status
        ChangedBlockList mActivatedBlocks;
        ChangedBlockList mLoadedBlocks;
        
        //@brief: tick use, update info if dirty
        ChangedBlockList mToActivateBlocks;
        ChangedBlockList mToUnActiveBlocks;
        ChangedBlockList mToUnloadBlocks;

        friend WorldLoadingSystemG;
    };
}
