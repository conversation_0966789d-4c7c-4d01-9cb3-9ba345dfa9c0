#include "EnginePrefix.h"
#include "CECommon/Common/EngineGlobal.h"
#include "CECommon/Common/FrameParam.h"
#include "CECommon/Common/GlobalSystemDesc.h"
#include "Runtime/Interface/ECSConfig.h"
#include "CECommon/Common/SettingsManager.h"
#include "Runtime/GameWorld/WorldSystemG.h"
#include "Runtime/GameWorld/GameWorld.h"
#include "Runtime/GameWorld/CameraSystemG.h"
#include "Runtime/GameWorld/CanvasSystemG.h"
#include "Runtime/GameWorld/JointSystemG.h"
#include "Runtime/GameWorld/ScriptSystemG.h"
#include "Runtime/GameWorld/WorldPartition/WorldLoadingSystemG.h"
#include "Runtime/GameWorld/AnimatorSystemG.h"
#include "Runtime/GameWorld/LightMapSystemG.h"
#include "Runtime/GameWorld/LightProbeVolumeSystemG.h"
#include "Threading/RenderingThread.h"

#include "RenderEngine/RenderWorld.h"
#include "RenderEngine/WorldSystemR.h"
#include "Resource/ResourceManager.h"
//--------------------------------------
//--------------------------------------
#include "Resource/ResourceSyncSystem.h"
#include "Runtime/Reflection/TypeGet.h"
#include "Runtime/Interface/EngineModuleConfig.h"
#include "reflection/meta/meta_class.hpp"
#include "meta/reflection/builder/class_builder.hpp"
namespace cross
{

    WorldSystemG::CustomAssembleWorldFunction WorldSystemG::CustomAssembleWorldFunc;

    const GlobalSystemDesc& WorldSystemG::GetDesc()
    {
        static const GlobalSystemDesc* sDesc{ nullptr };
        if (!sDesc)
        {
            auto* descSystem = EngineGlobal::GetGlobalSystemDescSystem();
            sDesc = descSystem->CreateOrGetGlobalSystemDesc("GameWorldSystem", true);
        }
        return *sDesc;
    }

    WorldSystemG* WorldSystemG::CreateInstance()
    {
        return new WorldSystemG();
    }

    WorldSystemG::WorldSystemG()
    {
        mRenderObject = WorldSystemR::CreateInstance();
        mEntityPrototypies.resize(sMaxWorldTypeTag);
        mWorldUpdateList.reserve(8);
        mDestroyList.reserve(4);
    }

    WorldSystemG::~WorldSystemG()
    {
        NotifyShutdownEngine();
    }

    void WorldSystemG::CreateSystemsByConfig(IGameWorld* gw, const cross::SerializeNode& Config) 
    {
        if (!Config.IsArray())
        {
            return;
        }
        for (size_t t = 0; t < Config.Size(); t++) 
        {
            std::string system_name = Config[t].AsString();
            {
                auto metacls = gbf::reflection::query_meta_class_by_name(system_name);
                if (metacls)
                {
                    auto& function = metacls->GetFunction("CreateInstance");
                    cross::GameSystemBase* ptr = &gbf::reflection::cxx::CallStatic(function).Ref<cross::GameSystemBase>();
                    gw->AddGameSystem(ptr, system_name.c_str());

                    FrameTickManager::DependencyGroup postdepsgroup{};
                    postdepsgroup.push(ptr);
                    gw->AddSystemDependency({}, postdepsgroup);
                    if (auto renderSystem = ptr->GetRenderSystem(); renderSystem)
                    {
                        FrameTickManager::DependencyGroup postdepsgroupR{};
                        postdepsgroupR.push(renderSystem);
                        if (auto rw = gw->GetRenderWorld(); rw)
                        { 
                            rw->AddSystemDependency({}, postdepsgroupR);
                        }
                    }
                }

            }
        }
        ///Create module systems   
    }

    void WorldSystemG::SetDependenciesByConfig(IGameWorld* gw, const cross::SerializeNode& Config) 
    {
        if (!Config.IsArray())
        {
            return;
        }
        for (size_t t = 0; t < Config.Size(); t++) 
        {
            auto pregroup = Config[t].HasMember("Pre").value();
            auto postgroup = Config[t].HasMember("Post").value();

            FrameTickManager::DependencyGroup predepsgroup{}, predepsgroupR{};
            for (size_t i = 0; i < pregroup.Size(); i++)
            {
                auto desc = cross::EngineGlobal::Inst().GetCompSystemDescSystem()->GetGameSystemDescByName(pregroup[i].AsString().c_str());

                if (desc && gw->GetGameSystem(desc->mID))
                {
                    predepsgroup.push(gw->GetGameSystem(desc->mID));
                    
                    if (auto renderSystem = gw->GetGameSystem(desc->mID)->GetRenderSystem(); renderSystem)
                        predepsgroupR.push(renderSystem);
                }
                else
                {
                    LOG_ERROR("Cannot found system {}", pregroup[i].AsString().c_str());
                }
            }
            FrameTickManager::DependencyGroup postdepsgroup{}, postdepsgroupR{};
            for (size_t i = 0; i < postgroup.Size(); i++)
            {
                auto desc = cross::EngineGlobal::Inst().GetCompSystemDescSystem()->GetGameSystemDescByName(postgroup[i].AsString().c_str());
                if (desc && gw->GetGameSystem(desc->mID))
                {
                    postdepsgroup.push(gw->GetGameSystem(desc->mID));

                    if (auto renderSystem = gw->GetGameSystem(desc->mID)->GetRenderSystem(); renderSystem)
                        postdepsgroupR.push(renderSystem);
                }
                else
                {
                    LOG_ERROR("Cannot found system {}", postgroup[i].AsString().c_str());
                }
            }
            gw->AddSystemDependency(predepsgroup, postdepsgroup);
            if (auto rw = gw->GetRenderWorld(); rw)
            {
                if (predepsgroupR.size() || postdepsgroupR.size())
                    gw->GetRenderWorld()->AddSystemDependency(predepsgroupR, postdepsgroupR);
            }
        }
    }

    void WorldSystemG::Release()
    {
        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            delete mWorldUpdateList[i];
        }
        mWorldUpdateList.clear();
        mWorldLookupTable.clear();

        delete mBuildInWorld;
        mBuildInWorld = nullptr;

        if (mRenderObject)
        {
            if (mIsRenderObjectOwner && mRenderObject)
            {
                mRenderObject->Release();
            }
        }

        delete this;
    }

    void WorldSystemG::OnBeginFrame(FrameParam* frameParam)
    {
        if (!mEnable)
            return;

        auto elapsedTime = frameParam->GetDeltaTime();
        mFrameParam = frameParam;
        mElapsedTime = elapsedTime;

        mStageDetail = RSD_StartFrame;

        if (!mBuildInWorld)
        {
            CreateGameWorldParam param;
            mBuildInWorld = new GameWorld("GlobalWorld", param);
            AssembleGlobalWorld(mBuildInWorld, param);

            auto* renderWorld = mBuildInWorld->GetRenderWorld();
            if (renderWorld)
            {
                mBuildInWorld->NotifyAddRenderSystemToRenderWorld();
                DispatchRenderingCommandWithToken([renderWorldSystem = mRenderObject, renderWorld]
                    {
                        renderWorldSystem->SetGlobalWorld(renderWorld);
                    });
            }
        }

        mBuildInWorld->BeginFrame(frameParam, elapsedTime);

        mStageDetail = RSD_BeforeBeginFrame;

        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            mWorldUpdateList[i]->BeginFrame(frameParam, elapsedTime);
        }

        mStageDetail = RSD_BeforeFirstUpdate;

        for (size_t i = 0; i < mFirstUpdateList.size(); i++)
        {
            auto& curWorld = mFirstUpdateList[i];
            if (curWorld->IsRunCurrentFrame() && !curWorld->IsPendingToDestroy())
            {
                mFirstUpdateList[i]->FirstUpdate(frameParam, elapsedTime);
            }
        }
        mFirstUpdateList.clear();

        mStageDetail = RSD_FinishFirstUpdate;
    }

    void WorldSystemG::OnFirstUpdate(FrameParam* frameParam)
    {
        EngineGlobal::GetSettingMgr()->GetRenderPipelineSetting()->Initialize();
    }

    void WorldSystemG::OnUpdate(FrameParam* fp)
    {
        if (!mEnable)
            return;

        auto elapsedTime = fp->GetDeltaTime();
        mFrameParam = fp;
        mElapsedTime = elapsedTime;

        mStageDetail = RSD_BeforeTick;

        mBuildInWorld->Tick(fp, elapsedTime);

        // trigger commands
        EngineGlobal::Inst().GetTerminalManager()->DispatchDelayEvent<TerminalVarEvent>();

        //tick worlds
        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            mWorldUpdateList[i]->Tick(fp, elapsedTime);
        }

        if (!mDestroyList.empty())
        {
            for (size_t i = 0; i < mDestroyList.size(); i++)
            {
                mDestroyList[i]->ClearAllEntities();
            }
        }
        mStageDetail = RSD_FinishTick;
    }

    void WorldSystemG::OnEndFrame(FrameParam* frameParam)
    {
        if (!mEnable)
            return;

        mStageDetail = RSD_BeforeEndFrame;

        auto elapsedTime = frameParam->GetDeltaTime();

        mBuildInWorld->EndFrame(frameParam, elapsedTime);

        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            mWorldUpdateList[i]->EndFrame(frameParam, elapsedTime);
        }

        mStageDetail = RSD_FinishEndFrame;

        if (!mDestroyList.empty())
        {
            for (size_t i = 0; i < mDestroyList.size(); i++)
            {
                DoDestroyWorld(mDestroyList[i]);
            }
            mDestroyList.clear();
        }
        mFrameParam = nullptr;
        mElapsedTime = 0;
    }

    void WorldSystemG::NotifyShutdownEngine()
    {

        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            mWorldUpdateList[i]->NotifyShutdown();
        }


        //if (mRenderObject)
        //{

        //    {
        //        auto* renderObject = mRenderObject;
        //        DispatchRenderingCommandWithToken([renderObject]
        //            {
        //                renderObject->NotifyShutdownEngine();
        //            });
        //    }

        //    //mRenderObject = nullptr;
        //}
    }

    GameWorld* WorldSystemG::CreateWorld(const char* worldName, const CreateGameWorldParam& createParam)
    {
        Assert(worldName);

        GameWorld* gameWorld = new GameWorld(worldName, createParam);

        if (!CustomAssembleWorldFunc || !CustomAssembleWorldFunc(gameWorld, createParam))
        {
            AssembleDefaultWorld(gameWorld, createParam);
        }
        gameWorld->InitializeSingletonComponents();

        gameWorld->SetEntityPrototypeTable(EngineGlobal::GetECSConfig()->GetWorldEntityTypeMap(createParam.mWorldTypeTag));

        auto* renderWorld = gameWorld->GetRenderWorld();
        if (renderWorld)
        {
            auto* renderWorldSystem = mRenderObject;
            DispatchRenderingCommandWithToken([renderWorldSystem, renderWorld]
                {
                    renderWorldSystem->AddRenderWorld(renderWorld);
                });

            gameWorld->NotifyAddRenderSystemToRenderWorld();
        }

        UInt32 hash = HashFunction::HashString32(worldName);
        Assert(mWorldLookupTable.find(hash) == mWorldLookupTable.end());

        mWorldLookupTable.emplace(gameWorld->GetName().GetHash32(), gameWorld);
        mWorldUpdateList.emplace_back(gameWorld);

        //catch up current stage if we are in the middle of one frame
        if (mStageDetail > RSD_BeforeBeginFrame && mStageDetail < RSD_BeforeEndFrame)
        {
            gameWorld->BeginFrame(mFrameParam, mElapsedTime);

            if (mStageDetail > RSD_BeforeFirstUpdate)
            {
                gameWorld->FirstUpdate(mFrameParam, mElapsedTime);

                if (mStageDetail >= RSD_FinishTick)
                {
                    gameWorld->Tick(mFrameParam, mElapsedTime);
                }
            }
        }
        else
        {
            mFirstUpdateList.emplace_back(gameWorld);
        }
        // dispatch event
        WorldLifeEvent e;
        e.mData.mWorldId = gameWorld->GetRuntimeID();
        e.mData.mEventType = WorldLifeEventType::Create;
        DispatchImmediateEvent(e);
        return gameWorld;
    }

    void WorldSystemG::DestroyWorld(GameWorld* world)
    {
        assert(world);
        mDestroyList.emplace_back(world);
        world->PendingToDestroy();

        for (size_t i = 0; i < mFirstUpdateList.size(); i++)
        {
            if (mFirstUpdateList[i] == world)
            {
                mFirstUpdateList.erase(mFirstUpdateList.begin() + i);
            }
        }
        // dispatch event
        WorldLifeEvent e;
        e.mData.mWorldId = world->GetRuntimeID();
        e.mData.mEventType = WorldLifeEventType::Destroy;
        DispatchImmediateEvent(e);
    }

    void WorldSystemG::DoDestroyWorld(GameWorld* world)
    {
        auto it = mWorldLookupTable.find(world->GetName().GetHash32());
        assert(it != mWorldLookupTable.end());
        mWorldLookupTable.erase(it);
        world->DoPendingDestroy();
        auto* renderWorld = world->GetRenderWorld();
        for (size_t i = 0; i < mWorldUpdateList.size(); i++)
        {
            if (mWorldUpdateList[i] == world)
            {
                mWorldUpdateList.erase(mWorldUpdateList.begin() + i);
                break;
            }
        }
        delete world;

        if (mRenderObject && renderWorld)
        {
            auto* renderWorldSystem = mRenderObject;
            DispatchRenderingCommandWithToken([renderWorldSystem, renderWorld] { renderWorldSystem->DoDestroyRenderWorld(renderWorld); });
        }
        // Avoid Crash Here cause by some render resource;
        threading::FlushRenderingCommands();
        gResourceMgr.GarbageCollect();
        if (EngineGlobal::GetSettingMgr()->GetAppStartUpType() != AppStartUpTypeHeadless)
        {
            auto fp = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam();
            auto syncSystem = static_cast<ResourceSyncSystem*>(EngineGlobal::GetEngine()->GetGlobalSystem<ResourceSyncSystem>());
            syncSystem->SyncToRenderThread(fp, true);
        }
    }

    GameWorld* WorldSystemG::GetWorldByName(const char* name)
    {
        if (!name)
            return nullptr;

        StringHash32 hash = HashFunction::HashString32(name);
        auto it = mWorldLookupTable.find(hash);
        if (it != mWorldLookupTable.end())
        {
            return it->second;
        }

        return nullptr;
    }

    GameWorld* WorldSystemG::GetWorldByID(UInt32 id)
    {
        for (auto& [_, world] : mWorldLookupTable)
        {
            if (world->GetRuntimeID() == id)
                return world;
        }
        return nullptr;
    }

    void WorldSystemG::AssembleGlobalWorld(IGameWorld* gameWorld, const CreateGameWorldParam& createParam)
    {
        if (createParam.mWorldTypeTag != 0)
        {
        }
        else
        {
            auto* scriptSystem = ScriptSystemG::CreateInstance();
            gameWorld->AddGameSystem(scriptSystem, "cross::ScriptSystemG");
        }
    }

    void WorldSystemG::AssembleDefaultWorld(IGameWorld* gameWorld, const CreateGameWorldParam& createParam)
    {
        const std::string worldSystemConfigPath = "EngineResource/Config/WorldSystemConfig.json";
        auto configFile = EngineGlobal::GetSettingMgr()->LoadConfigFile(PathHelper::GetEngineResourceAbsolutePath(worldSystemConfigPath));
        if (!configFile)
        {
#if CROSSENGINE_IOS || CROSSENGINE_ANDROID
            std::string filePathInDocument = PathHelper::GetDocumentDirectoryPath() + worldSystemConfigPath;
            configFile = EngineGlobal::GetSettingMgr()->LoadConfigFile(filePathInDocument);
            if (!configFile)
#endif
            {
                LOG_ERROR("Faile to load system config file!!!");
                Assert(0);
                return;
            }
        }
        auto confignode = configFile->HasMember("WorldSystemConfig");
        if (!confignode)
        {
            LOG_ERROR("Invalid config file!!!");
        }
        // it is reasonable that different world type have different system, and by the original design, it should be configurable,
        // but when time goes, nobody remember this, and nobody can maintain the exploded system list
        // in fact, all the enum go to the same code, same system.
        // so now (2024/7/9) remove it. i'm confident that many such code exits.
        {
            auto syslist = confignode->HasMember("SystemList").value();
            CreateSystemsByConfig(gameWorld, syslist);
            auto dependslist = confignode->HasMember("Dependencies").value();
            SetDependenciesByConfig(gameWorld, dependslist);
            std::for_each(EngineGlobal::Inst().GetEngineModuleConfig()->GetLoadedModulesInfo().begin(), EngineGlobal::Inst().GetEngineModuleConfig()->GetLoadedModulesInfo().end(), [this, gameWorld](auto& moduleinfo) {
                if (moduleinfo.second.mEnable)
                {
                    CreateSystemsByConfig(gameWorld, moduleinfo.second.mGameSystemList);
                    SetDependenciesByConfig(gameWorld, moduleinfo.second.mDependencies);
                }
            });
        }
    }

    void WorldSystemG::NotifyAddRenderSystemToRenderEngine()
    {
        mIsRenderObjectOwner = false;
    }

    GlobalRenderSystemBase* WorldSystemG::GetRenderSystem()
    {
        return mRenderObject;
    }

    void WorldSystemG::RegisterEntityPrototype(UInt8 worldTypeTag, const char* entityTypeName, ecs::PrototypePtr& gameEntityprototype, ecs::PrototypePtr& renderEntityPrototype)
    {
        assert(worldTypeTag < sMaxWorldTypeTag);

        if (!entityTypeName)
        {
            LOG_WARN("Register invalid entity prototype");
            return;
        }

        auto& entityPrototypies = mEntityPrototypies[worldTypeTag];
        StringHash32 hash = HashFunction::HashString32(entityTypeName);
        auto it = entityPrototypies.find(hash);
        if (it != entityPrototypies.end())
        {
            it->second.mGameEntityPrototype = gameEntityprototype;
            it->second.mRenderEntityPrototype = renderEntityPrototype;
        }
        else
        {
            entityPrototypies.emplace(std::piecewise_construct,
                std::forward_as_tuple(hash),
                std::forward_as_tuple(entityTypeName, gameEntityprototype, renderEntityPrototype));
        }
    }

}
