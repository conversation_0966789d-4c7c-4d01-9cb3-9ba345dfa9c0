#include "EnginePrefix.h"
#include "GestureRecognizer.h"

#include "Runtime/Input/Core/PlatformUser.h"

namespace cross
{

// Map a angle with degree representation to [0, 360) range
[[maybe_unused]] float ClampAxis(float angle)
{
    // Returns Angle in the range (-360,360)
    angle = fmod(angle, 360.0f);

    if (angle < 0.0f)
    {
        // Shift to [0,360) range
        angle += 360.0f;
    }

    return angle;
}

void CEGestureRecognizer::DetectGestures(const Float3 (&inTouches)[input::CEKeys::NUM_TOUCH_KEYS], PlatformUser* inUser, float deltaTime)
{
    // How many fingers currently held down?
    SInt32 touchCount = 0;
    for (SInt32 touchIndex = 0; touchIndex < input::CEKeys::NUM_TOUCH_KEYS; touchIndex++)
    {
        if (inTouches[touchIndex].z != 0)
        {
            touchCount++;
        }
    }

    // Further processing is only needed if there were or are active touches.
    if (mPreviousTouchCount != 0 || touchCount != 0)
    {
        // Place new anchor points
        for (SInt32 index = 0; index < ArrayCount(mAnchorPoints); index++)
        {
            if (mPreviousTouchCount < index + 1 && touchCount >= index + 1)
            {
                mAnchorPoints[index] = inTouches[index].GetXY();
            }
        }

        // Handle different types of two finger gestures
        if (touchCount >= 2)
        {
            // If current gesture values do not contain this gesture key, the default value is 0.0f
            bool hasPinchValue = mCurrentGestureValues.contains(input::CEKeys::Gesture_Pinch);
            float& currentAlpha = mCurrentGestureValues[input::CEKeys::Gesture_Pinch];

            // NOTE: Values for pinch input produce very different results for same area on android device
            Float2 currentPinchStartPoint = inTouches[0].GetXY();
            Float2 currentPinchEndPoint = inTouches[1].GetXY();
            const float PinchThreshold = 10000.0f;

            if (!hasPinchValue)
            {
                // If the pinch points are close enough to those from the last pinch, continue with the previous starting distance
                float pinchStartPointDistanceSquared = (mLastPinchStartPoint - currentPinchStartPoint).LengthSq();
                float pinchEndPointDistanceSquared = (mLastPinchEndPoint - currentPinchEndPoint).LengthSq();
                if (MathUtils::IsNearlyZero(mAnchorDistanceSquared) || pinchStartPointDistanceSquared > PinchThreshold || pinchEndPointDistanceSquared > PinchThreshold)
                {
                    // Remember the starting distance
                    SetAnchorDistanceSquared(currentPinchStartPoint, currentPinchEndPoint);
                }

                // Alpha of 1 is initial pinch anchor distance
                mCurrentGestureValues.emplace(input::CEKeys::Gesture_Pinch, 1.0f);
                currentAlpha = mCurrentGestureValues.at(input::CEKeys::Gesture_Pinch);
            }

            // Calculate current alpha
            float newDistanceSquared = (currentPinchStartPoint - currentPinchEndPoint).LengthSq();

            currentAlpha = newDistanceSquared / mAnchorDistanceSquared;
            HandleGestureEvent(inUser, input::CEKeys::Gesture_Pinch, true, false);

            mLastPinchStartPoint = currentPinchStartPoint;
            mLastPinchEndPoint = currentPinchEndPoint;

            // Calculate the angle of the vector between the touch points
            // NOTE: Different from UE: CE use angles at range [-180, 180), UE use angles at range [0, 360)
            float newAngle = std::atan2(inTouches[0].y - inTouches[1].y, inTouches[0].x - inTouches[1].x);
            newAngle = MathUtils::ConvertToDegrees(MathUtils::UnwindRadians(newAngle));

            bool hasRotateValue = mCurrentGestureValues.contains(input::CEKeys::Gesture_Rotate);
            float& currentAngle = mCurrentGestureValues[input::CEKeys::Gesture_Rotate];

            if (!hasRotateValue)
            {
                // Save the starting angle
                mStartAngle = newAngle;

                // Use 0 as the initial angle value; subsequent angles will be relative
                mCurrentGestureValues.emplace(input::CEKeys::Gesture_Rotate, 0.0f);
                HandleGestureEvent(inUser, input::CEKeys::Gesture_Rotate, true, false);
            }
            else
            {
                currentAngle = MathUtils::ConvertToDegrees(MathUtils::UnwindRadians(MathUtils::ConvertToRadians(newAngle - mStartAngle)));

                // Gestures are only processed for IE_Pressed events, so treat this like another "start"
                HandleGestureEvent(inUser, input::CEKeys::Gesture_Rotate, true, false);
            }
        }

        // Handle pinch and rotate release.
        if (mPreviousTouchCount >= 2 && touchCount < 2)
        {
            HandleGestureEvent(inUser, input::CEKeys::Gesture_Pinch, false, true);
            HandleGestureEvent(inUser, input::CEKeys::Gesture_Rotate, false, true);
        }

        if (mPreviousTouchCount == 0 && touchCount == 1)
        {
            // Initialize the flick
            mFlickTime = 0.0f;
        }
        else if (mPreviousTouchCount == 1 && touchCount == 1)
        {
            // Remember the position for when we let go
            mFlickCurrent = Float2(inTouches[0].x, inTouches[0].y);
            mFlickTime += deltaTime;
        }
        // NOTE: Different from UE, we consider Gesture_Flick as a only-one-finger gesture,
        // Touch gestures using more than 1 finger will not trigger flick event
        else if (mPreviousTouchCount == 1 && touchCount == 0)
        {
            // Must be a fast flick
            if (mFlickTime < 0.25f && (mFlickCurrent - mAnchorPoints[0]).LengthSq() > 10000.f)
            {
                // This is the angle from +X in screen space
                // NOTE: Different from UE: CE use angles at range [-180, 180), UE use angles at range [0, 360)
                // In CE, right is 0, up is 90, left is 180, down is -90
                // In UE, right is 0, up is 90, left is 180, down is 270
                float angle = std::atan2(-(mFlickCurrent.y - mAnchorPoints[0].y), mFlickCurrent.x - mAnchorPoints[0].x);
                angle = MathUtils::ConvertToDegrees(MathUtils::UnwindRadians(angle));

                // flicks are one-shot, so we start and end in the same frame
                mCurrentGestureValues.emplace(input::CEKeys::Gesture_Flick, angle);
                HandleGestureEvent(inUser, input::CEKeys::Gesture_Flick, true, true);
            }
        }
    }

    // Remember for the next frame
    mPreviousTouchCount = touchCount;
}

void CEGestureRecognizer::SetAnchorDistanceSquared(const Float2& firstPoint, const Float2& secondPoint)
{
    mAnchorDistanceSquared = (firstPoint - secondPoint).LengthSq();
}

void CEGestureRecognizer::HandleGestureEvent(PlatformUser* inUser, const input::CEKey& inGesture, bool isStarted, bool isEnded)
{
    float value = mCurrentGestureValues.at(inGesture);

    if (true)
    {
        // NOTE: Different from UE: For Gesture_Flick, it only emits event right after the finger leaves the screen
        // Therefore its behaviour is pressed-and-released
        // In UE, this method only send a Pressed event, which make IsPressed always returns true after this gesture is emitted
        // In CE, this method send a Released event right after a Pressed event, which make IsPressed always returns false
        // In both cases, WasJustPressed can always correctly indicate whether a flick gesture is emitted in current frame or not
        if (isStarted || isEnded)
        {
            if (isStarted)
                inUser->HandleGestureEvent(inGesture, input::CEInputEvent::Pressed, value);
            if (isEnded)
                inUser->HandleGestureEvent(inGesture, input::CEInputEvent::Released, value);
        }
        else
            inUser->HandleGestureEvent(inGesture, input::CEInputEvent::Repeat, value);

        // Remove value if the gesture is complete
        if (isEnded)
        {
            mCurrentGestureValues.erase(inGesture);
        }
    }
}

}  // namespace cross
