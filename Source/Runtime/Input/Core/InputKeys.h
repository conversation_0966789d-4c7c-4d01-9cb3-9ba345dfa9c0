#ifndef INPUTKEYS_H
#define INPUTKEYS_H
#pragma once
#include "CrossBase/String/UniqueString.h"
#include "CrossBase/Containers/HashMap/HashMap.hpp"
#include "Runtime/Interface/CrossEngine.h"

#define NEW_INPUT_FRAMEWORK_ENABLE 

namespace cross::input
{
struct ENGINE_API CEKeyConext;

enum class CEPairedAxis : int
{
    Unpaired,
    X,
    Y,
    Z,
};

/** Store a platform predefined key's name & display name for engine **/
struct ENGINE_API CEMeta(Reflect, Puerts) CEKey
{
    CEMeta(Reflect)
    CEKey();

    CEKey(const CEKey& other);

    CEKey& operator=(const CEKey& rhs);
    ~CEKey();

    CEMeta(Reflect)
    CEKey(const UniqueString inName);


    CEMeta(Reflect)
    CEKey(const UniqueString inName, UniqueString inDisplayName);

    CEMeta(ScriptCallable)
    static CEKey CreateCEKeyByStr(const std::string& keyName);
       
    bool IsValid() const;

    CEMeta(Reflect)
    operator UniqueString() const noexcept
    {
        return mName;
    }

    UniqueString GetName() const
    {
        return mName;
    }

    CEKey GetPairedAxisKey() const;

    CEPairedAxis GetPairedAxis() const;

    inline friend bool operator==(const CEKey& lhs, const CEKey& rhs) noexcept
    {
        return lhs.mName == rhs.mName;
    }

    inline friend bool operator<(const CEKey& lhs, const CEKey& rhs)
    {
        return lhs.mName < rhs.mName;
    }

    inline bool operator()(const CEKey& lhs, const CEKey& rhs) const
    {
        return lhs.mName < rhs.mName;
    }

    CEKeyConext const* Context() const;

private:
    /** Identifiable key for engine mapping platform char code **/
    UniqueString mName;
    /** Editor & localize use **/
    UniqueString mDisplayName;
    /** Details info for current platform key **/
    mutable std::shared_ptr<CEKeyConext> mContextPtr = nullptr;

    friend class CEKeys;
};
}

namespace std {
template<>
struct hash<cross::input::CEKey>
{
    size_t operator()(const cross::input::CEKey& v) const
    {
        return v.GetName().GetHash64();
    }
};
}   // namespace std

namespace cross::input 
{
struct CEModifierKey
{
    enum Type
    {
        LeftShift = 0,      
        RightShift,     
        LeftControl,
        RightControl,   
        LeftAlt,        
        RightAlt,      
        CapsLock,  
        LeftCommand,
        RightCommand,

        Count,
    };

    enum Mask
    {
        // win. VK_LSHIFT
        LeftShiftM = 1 << 0,
        // win. VK_RSHIFT
        RightShiftM = 1 << 1,
        // win. VK_LCONTROL
        LeftControlM = 1 << 2,
        // win. VK_RCONTROL
        RightControlM = 1 << 3,
        // win. VK_LMENU
        LeftAltM = 1 << 4,
        // win. VK_RMENU
        RightAltM = 1 << 5,
        // win. VK_CAPITAL
        CapsLockM = 1 << 6,
        // mac.
        LeftCommandM = 1 << 7,
        // mac.
        RightCommandM = 1 << 8,
    };

    typedef UInt16 State;
};

/**
 * ModifierKeysState stores the pressed state of keys that are commonly used as modifiers
 */
class CEModifierKeyStates
{
public:
    /**
     * Constructor.  Events are immutable once constructed.
     *
     * @param  bInIsLeftShiftDown  True if left shift is pressed
     * @param  bInIsRightShiftDown  True if right shift is pressed
     * @param  bInIsLeftControlDown  True if left control is pressed
     * @param  bInIsRightControlDown  True if right control is pressed
     * @param  bInIsLeftAltDown  True if left alt is pressed
     * @param  bInIsRightAltDown  True if right alt is pressed
     */
    CEModifierKeyStates(const bool bInIsLeftShiftDown, const bool bInIsRightShiftDown, const bool bInIsLeftControlDown, const bool bInIsRightControlDown, const bool bInIsLeftAltDown, const bool bInIsRightAltDown,
                       const bool bInIsLeftCommandDown, const bool bInIsRightCommandDown, const bool bInAreCapsLocked)
        : bIsLeftShiftDown(bInIsLeftShiftDown)
        , bIsRightShiftDown(bInIsRightShiftDown)
        , bIsLeftControlDown(bInIsLeftControlDown)
        , bIsRightControlDown(bInIsRightControlDown)
        , bIsLeftAltDown(bInIsLeftAltDown)
        , bIsRightAltDown(bInIsRightAltDown)
        , bIsLeftCommandDown(bInIsLeftCommandDown)
        , bIsRightCommandDown(bInIsRightCommandDown)
        , bAreCapsLocked(bInAreCapsLocked)
    {}

    CEModifierKeyStates()
        : bIsLeftShiftDown(false)
        , bIsRightShiftDown(false)
        , bIsLeftControlDown(false)
        , bIsRightControlDown(false)
        , bIsLeftAltDown(false)
        , bIsRightAltDown(false)
        , bIsLeftCommandDown(false)
        , bIsRightCommandDown(false)
        , bAreCapsLocked(false)
    {}
    /**
     * Returns true if either shift key was down when this event occurred
     *
     * @return  True if shift is pressed
     */
    bool IsShiftDown() const
    {
        return bIsLeftShiftDown || bIsRightShiftDown;
    }

    /**
     * Returns true if left shift key was down when this event occurred
     *
     * @return  True if left shift is pressed
     */
    bool IsLeftShiftDown() const
    {
        return bIsLeftShiftDown;
    }

    /**
     * Returns true if right shift key was down when this event occurred
     *
     * @return  True if right shift is pressed
     */
    bool IsRightShiftDown() const
    {
        return bIsRightShiftDown;
    }

    /**
     * Returns true if either control key was down when this event occurred
     *
     * @return  True if control is pressed
     */
    bool IsControlDown() const
    {
        return bIsLeftControlDown || bIsRightControlDown;
    }

    /**
     * Returns true if left control key was down when this event occurred
     *
     * @return  True if left control is pressed
     */
    bool IsLeftControlDown() const
    {
        return bIsLeftControlDown;
    }

    /**
     * Returns true if right control key was down when this event occurred
     *
     * @return  True if right control is pressed
     */
    bool IsRightControlDown() const
    {
        return bIsRightControlDown;
    }

    /**
     * Returns true if either alt key was down when this event occurred
     *
     * @return  True if alt is pressed
     */
    bool IsAltDown() const
    {
        return bIsLeftAltDown || bIsRightAltDown;
    }

    /**
     * Returns true if left alt key was down when this event occurred
     *
     * @return  True if left alt is pressed
     */
    bool IsLeftAltDown() const
    {
        return bIsLeftAltDown;
    }

    /**
     * Returns true if right alt key was down when this event occurred
     *
     * @return  True if right alt is pressed
     */
    bool IsRightAltDown() const
    {
        return bIsRightAltDown;
    }

    /**
     * Returns true if either command key was down when this event occurred
     *
     * @return  True if command is pressed
     */
    bool IsCommandDown() const
    {
        return bIsLeftCommandDown || bIsRightCommandDown;
    }

    /**
     * Returns true if left command key was down when this event occurred
     *
     * @return  True if left command is pressed
     */
    bool IsLeftCommandDown() const
    {
        return bIsLeftCommandDown;
    }

    /**
     * Returns true if right command key was down when this event occurred
     *
     * @return  True if right command is pressed
     */
    bool IsRightCommandDown() const
    {
        return bIsRightCommandDown;
    }

    /**
     * @return  true if the Caps Lock key has been toggled to the enabled state.
     */
    bool AreCapsLocked() const
    {
        return bAreCapsLocked;
    }

    /**
     * @param ModifierKeys the modifier keys to test to see if they are pressed.  Returns true if no modifiers are specified.
     * @return true if all modifier keys are pressed specified in the modifier keys.
     */
    bool AreModifersDown(CEModifierKey::State checkKeys) const
    {
        bool modifersDown = true;

        if (((checkKeys & CEModifierKey::LeftShiftM) == CEModifierKey::LeftShiftM) ||
            ((checkKeys & CEModifierKey::RightShiftM) == CEModifierKey::RightShiftM))
        {
            modifersDown &= IsShiftDown();
        }

        if (((checkKeys & CEModifierKey::LeftCommandM) == CEModifierKey::LeftCommandM) ||
            ((checkKeys & CEModifierKey::RightCommandM) == CEModifierKey::RightCommandM))
        {
            modifersDown &= IsCommandDown();
        }

        if (((checkKeys & CEModifierKey::LeftControlM) == CEModifierKey::LeftControlM) || 
            ((checkKeys & CEModifierKey::RightControlM) == CEModifierKey::RightControlM))
        {
            modifersDown &= IsControlDown();
        }

        if (((checkKeys & CEModifierKey::LeftAltM) == CEModifierKey::LeftAltM) || 
            ((checkKeys & CEModifierKey::RightAltM) == CEModifierKey::RightAltM))
        {
            modifersDown &= IsAltDown();
        }

        return modifersDown;
    }

private:
    /** True if the left shift key was down when this event occurred. */
    UInt16 bIsLeftShiftDown : 1;

    /** True if the right shift key was down when this event occurred. */
    UInt16 bIsRightShiftDown : 1;

    /** True if the left control key was down when this event occurred. */
    UInt16 bIsLeftControlDown : 1;

    /** True if the right control key was down when this event occurred. */
    UInt16 bIsRightControlDown : 1;

    /** True if the left alt key was down when this event occurred. */
    UInt16 bIsLeftAltDown : 1;

    /** True if the right alt key was down when this event occurred. */
    UInt16 bIsRightAltDown : 1;

    /** True if the left command key was down when this event occurred. */
    UInt16 bIsLeftCommandDown : 1;

    /** True if the right command key was down when this event occurred. */
    UInt16 bIsRightCommandDown : 1;

    /** True if the Caps Lock key has been toggled to the enabled state. */
    UInt16 bAreCapsLocked : 1;
};

namespace CEMouseButton {
    enum ENGINE_API Type
    {
        Left = 0,
        Middle,
        Right,
        Thumb01,
        Thumb02,

        Invalid,
    };
}

namespace CETouchIndex {
    // The number of entries in ETouchIndex must match the number of touch keys defined in EKeys and NUM_TOUCH_KEYS above
    enum ENGINE_API Type
    {
        Dummy  = -1,
        Touch1 = 0,
        Touch2,
        Touch3,
        Touch4,
        Touch5,
        Touch6,
        Touch7,
        Touch8,
        Touch9,
        Touch10,
        /**
         * This entry is special.  MAX_TOUCHES - 1, is used for the cursor so that it's represented
         * as another finger index, but doesn't overlap with touch input indexes.
         */
        CursorPointerIndex,
        MAX_TOUCHES
    };

    class Dummy{};
}   // namespace CETouchIndex

/** Various states of touch inputs. */
namespace CETouchType {
    enum ENGINE_API Type
    {
        Began,
        Moved,
        Stationary,
        ForceChanged,
        FirstMove,
        Ended,

        NumTypes
    };
}

struct ENGINE_API CEKeyConext
{
    enum ENGINE_API CEKeyFlags
    {
        GamepadKey = 1 << 0,
        Touch = 1 << 1,
        MouseButton = 1 << 2,

        Axis1D = 1 << 3,
        Axis2D = 1 << 4,
        ButtonAxis = 1 << 5,

        UpdateAxisWithoutSamples = 1 << 6,
        Deprecated = 1 << 7,
        Gesture = 1 << 8,
        
        Keyboard = 1 << 9,
        NoFlags = Keyboard,

        Axis3D = 1 << 10,
    };

    using KeyFlagHandle = THandle<CEKeyFlags>;

    CEKeyConext(const CEKey& inKey, const KeyFlagHandle inKeyFlags = {0}, const UniqueString inMenuCategory = "")
        : reference(inKey)
        , category(inMenuCategory)
    {
        CommonInit(inKeyFlags);
    }

    inline bool IsGamepadKey() const { return bIsGamepadKey != 0; }
    
    inline bool IsTouch() const { return bIsTouch != 0; }

    inline UInt32 GetTouchIndex() const { return static_cast<UInt32>(touchIndex); }

    inline bool IsMouseButton() const { return bIsMouseButton != 0; }

    inline bool ShouldUpdateAxisWithoutSamples() const { return bShouldUpdateAxisWithoutSamples; }

    inline bool IsAxis1D() const { return axisType == CEInputAxis::Axis1D; }

    inline bool IsAxis2D() const { return axisType == CEInputAxis::Axis2D; }

    inline bool IsAxis3D() const { return axisType == CEInputAxis::Axis3D; }

    inline bool IsAnalog() const { return IsAxis1D() || IsAxis2D() || IsAxis3D(); }

    inline bool IsGesture() const { return bIsGesture != 0; }

    inline bool IsKeyboard() const { return bIsKeyboard != 0; }

    inline bool IsButtonAxis() const { return axisType == CEInputAxis::Button; }

    inline const CEKey& Reference() const { return reference; }

    inline CEPairedAxis GetPairedAxis() const { return PairedAxis; }

    inline const CEKey& GetPairedAxisKey() const { return PairedAxisKey; }

private:
    enum class ENGINE_API CEInputAxis
    {
        None,
        Button,   // Whilst the physical input is an analog axis the FKey uses it to emulate a digital button.
        Axis1D,
        Axis2D,
        Axis3D,
    };

private:
    friend struct CEKey;
    friend class CEKeys;

    void CommonInit(const KeyFlagHandle inKeyFlags);

private:
    const CEKey& reference;
    UniqueString category = "";

    CEPairedAxis PairedAxis = CEPairedAxis::Unpaired;
    CEKey PairedAxisKey = CEKey();

    bool bIsGamepadKey                   : 1;
    bool bIsTouch                        : 1;
    bool bIsMouseButton                  : 1;
    bool bShouldUpdateAxisWithoutSamples : 1;
    bool bIsDeprecated                   : 1;
    bool bIsGesture                      : 1;
    bool bIsKeyboard                     : 1;
 
    SInt32 touchIndex;

    CEInputAxis axisType = CEInputAxis::None;
};

using ContextKeyFlags = CEKeyConext::CEKeyFlags;

class ENGINE_API CEKeysCategory
{
public:
    inline static const UniqueString NAME_KeyboardCategory = "Keyboard";
    inline static const UniqueString NAME_GamepadCategory = "Gamepad";
    inline static const UniqueString NAME_MouseCategory = "Mouse";
    inline static const UniqueString NAME_AndroidCategory = "Android";
    inline static const UniqueString NAME_IOSCategory = "IOS";
};

class ENGINE_API CEMeta(Puerts) CEKeys
{
public:
    CEProperty(ScriptReadOnly)
    inline static const CEKey AnyKey{"AnyKey"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Invalid{"Invalid"};
    
    //////////////////////////////////////////////////////////////////////
    // Mouse
    CEProperty(ScriptReadOnly)
    inline static const CEKey CursorX{"CursorX"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey CursorY{"CursorY"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Mouse2D{"Mouse2D"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey MouseScrollUp{"MouseScrollUp"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey MouseScrollDown{"MouseScrollDown"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey MouseWheelAxis{"MouseWheelAxis"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftMouseButton{"LeftMouseButton"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightMouseButton{"RightMouseButton"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey MiddleMouseButton{"MiddleMouseButton"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey ThumbMouseButton{"ThumbMouseButton"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey ThumbMouseButton2{"ThumbMouseButton2"};
                 
    //////////////////////////////////////////////////////////////////////
    // Keyboard
    CEProperty(ScriptReadOnly)
    inline static const CEKey BackSpace{"BackSpace"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Tab{"Tab"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Enter{"Enter"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Pause{"Pause"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey CapsLock{"CapsLock"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Escape{"Escape"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey SpaceBar{"SpaceBar"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey PageUp{"PageUp"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey PageDown{"PageDown"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey End{"End"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Home{"Home"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Left{"Left"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Up{"Up"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Right{"Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Down{"Down"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Insert{"Insert"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Delete{"Delete"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Zero{"Zero"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey One{"One"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Two{"Two"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Three{"Three"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Four{"Four"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Five{"Five"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Six{"Six"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Seven{"Seven"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Eight{"Eight"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Nine{"Nine"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey A{"A"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey B{"B"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey C{"C"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey D{"D"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey E{"E"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F{"F"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey G{"G"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey H{"H"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey I{"I"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey J{"J"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey K{"K"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey L{"L"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey M{"M"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey N{"N"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey O{"O"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey P{"P"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Q{"Q"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey R{"R"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey S{"S"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey T{"T"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey U{"U"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey V{"V"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey W{"W"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey X{"X"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Y{"Y"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Z{"Z"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadZero{"NumPadZero"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadOne{"NumPadOne"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadTwo{"NumPadTwo"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadThree{"NumPadThree"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadFour{"NumPadFour"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadFive{"NumPadFive"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadSix{"NumPadSix"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadSeven{"NumPadSeven"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadEight{"NumPadEight"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumPadNine{"NumPadNine"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Multiply{"Multiply"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Add{"Add"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Subtract{"Subtract"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Decimal{"Decimal"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Divide{"Divide"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey F1{"F1"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F2{"F2"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F3{"F3"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F4{"F4"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F5{"F5"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F6{"F6"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F7{"F7"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F8{"F8"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F9{"F9"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F10{"F10"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F11{"F11"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey F12{"F12"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey NumLock{"NumLock"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey ScrollLock{"ScrollLock"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftShift{"LeftShift"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightShift{"RightShift"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftControl{"LeftControl"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightControl{"RightControl"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftAlt{"LeftAlt"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightAlt{"RightAlt"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftCommand{"LeftCommand"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightCommand{"RightCommand"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Semicolon{"Semicolon"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Equals{"Equals"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Comma{"Comma"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Underscore{"Underscore"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Hyphen{"Hyphen"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Period{"Period"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Slash{"Slash"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Tilde{"Tilde"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftBracket{"LeftBracket"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Backslash{"Backslash"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightBracket{"RightBracket"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Apostrophe{"Apostrophe"};
                 
    CEProperty(ScriptReadOnly)
    inline static const CEKey Ampersand{"Ampersand"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Asterix{"Asterix"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Caret{"Caret"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Colon{"Colon"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Dollar{"Dollar"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Exclamation{"Exclamation"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey LeftParantheses{"LeftParantheses"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey RightParantheses{"RightParantheses"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Quote{"Quote"};

    //////////////////////////////////////////////////////////////////////
    //Gamepad Keys
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Left2D{"Gamepad_Left2D"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftX{"Gamepad_LeftX"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftY{"Gamepad_LeftY"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Right2D{"Gamepad_Right2D"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightX{"Gamepad_RightX"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightY{"Gamepad_RightY"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftTriggerAxis{"Gamepad_LeftTriggerAxis"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightTriggerAxis{"Gamepad_RightTriggerAxis"};

    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftThumbstick{"Gamepad_LeftThumbstick"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightThumbstick{"Gamepad_RightThumbstick"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Special_Left{"Gamepad_Special_Left"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Special_Left_X{"Gamepad_Special_Left_X"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Special_Left_Y{"Gamepad_Special_Left_Y"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_Special_Right{"Gamepad_Special_Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_FaceButton_Bottom{"Gamepad_FaceButton_Bottom"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_FaceButton_Right{"Gamepad_FaceButton_Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_FaceButton_Left{"Gamepad_FaceButton_Left"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_FaceButton_Top{"Gamepad_FaceButton_Top"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftShoulder{"Gamepad_LeftShoulder"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightShoulder{"Gamepad_RightShoulder"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftTrigger{"Gamepad_LeftTrigger"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightTrigger{"Gamepad_RightTrigger"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_DPad_Up{"Gamepad_DPad_Up"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_DPad_Down{"Gamepad_DPad_Down"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_DPad_Right{"Gamepad_DPad_Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_DPad_Left{"Gamepad_DPad_Left"};

    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftStick_Up{"Gamepad_LeftStick_Up"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftStick_Down{"Gamepad_LeftStick_Down"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftStick_Right{"Gamepad_LeftStick_Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_LeftStick_Left{"Gamepad_LeftStick_Left"};

    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightStick_Up{"Gamepad_RightStick_Up"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightStick_Down{"Gamepad_RightStick_Down"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightStick_Right{"Gamepad_RightStick_Right"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gamepad_RightStick_Left{"Gamepad_RightStick_Left"};

    //////////////////////////////////////////////////////////////////////
    // Fingers
    inline static const UInt32 NUM_TOUCH_KEYS = 11;
    inline static const CEKey TouchKeys[NUM_TOUCH_KEYS];
    //////////////////////////////////////////////////////////////////////
    // Gestures
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gesture_Pinch{"Gesture_Pinch"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gesture_Flick{"Gesture_Flick"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Gesture_Rotate{"Gesture_Rotate"};

    //////////////////////////////////////////////////////////////////////
    // Android
    CEProperty(ScriptReadOnly)
    inline static const CEKey Android_Back{"Android_Back"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Android_Volume_Up{"Android_Volume_Up"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Android_Volume_Down{"Android_Volume_Down"};
    CEProperty(ScriptReadOnly)
    inline static const CEKey Android_Menu{"Android_Menu"};

    //////////////////////////////////////////////////////////////////////
    // IOS


    //////////////////////////////////////////////////////////////////////
    // Oculus Touch Controller
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_X_Click{ "OculusTouch_Left_X_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Y_Click{ "OculusTouch_Left_Y_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_X_Touch{ "OculusTouch_Left_X_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Y_Touch{ "OculusTouch_Left_Y_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Menu_Click{ "OculusTouch_Left_Menu_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Grip_Click{ "OculusTouch_Left_Grip_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Grip_Axis{ "OculusTouch_Left_Grip_Axis" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Trigger_Click{ "OculusTouch_Left_Trigger_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Trigger_Axis{ "OculusTouch_Left_Trigger_Axis" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Trigger_Touch{ "OculusTouch_Left_Trigger_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_X{ "OculusTouch_Left_Thumbstick_X" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Y{ "OculusTouch_Left_Thumbstick_Y" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Click{ "OculusTouch_Left_Thumbstick_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Touch{ "OculusTouch_Left_Thumbstick_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Up{ "OculusTouch_Left_Thumbstick_Up" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Down{ "OculusTouch_Left_Thumbstick_Down" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Left{ "OculusTouch_Left_Thumbstick_Left" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Left_Thumbstick_Right{ "OculusTouch_Left_Thumbstick_Right" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_A_Click{ "OculusTouch_Right_A_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_B_Click{ "OculusTouch_Right_B_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_A_Touch{ "OculusTouch_Right_A_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_B_Touch{ "OculusTouch_Right_B_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_System_Click{ "OculusTouch_Right_System_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Grip_Click{ "OculusTouch_Right_Grip_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Grip_Axis{ "OculusTouch_Right_Grip_Axis" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Trigger_Click{ "OculusTouch_Right_Trigger_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Trigger_Axis{ "OculusTouch_Right_Trigger_Axis" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Trigger_Touch{ "OculusTouch_Right_Trigger_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_X{ "OculusTouch_Right_Thumbstick_X" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Y{ "OculusTouch_Right_Thumbstick_Y" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Click{ "OculusTouch_Right_Thumbstick_Click" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Touch{ "OculusTouch_Right_Thumbstick_Touch" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Up{ "OculusTouch_Right_Thumbstick_Up" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Down{ "OculusTouch_Right_Thumbstick_Down" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Left{ "OculusTouch_Right_Thumbstick_Left" };
    CEProperty(ScriptReadOnly)
    static inline const CEKey OculusTouch_Right_Thumbstick_Right{ "OculusTouch_Right_Thumbstick_Right" };

    //////////////////////////////////////////////////////////////////////
    // Joystick
    CEProperty(ScriptReadOnly)
    static inline const CEKey Joystick_X_0{"Joystick_X_0"};

public:
    static void Initialize(const InitInfo& initInfo);
    static void AddKey(const CEKeyConext& KeyDetails);
    static void AddPairedKey(const CEKeyConext& KeyDetails, CEKey KeyX, CEKey KeyY);
    static std::shared_ptr<CEKeyConext> GetKeyContext(const CEKey& Key);
    static input::CEKey const& TranslateMouseButtonToKey(const input::CEMouseButton::Type Button);
    static input::CEKey const& TranslatePointerIndexToKey(const SInt32 Pointer);
    static input::CEKey const& TranslateNameToKey(UniqueString Name);

    static const CEHashMap<CEKey, std::shared_ptr<CEKeyConext>>& GetInputKeys()
    {
        return InputKeys;
    }

private:
    static bool bInitialized;

    static CEHashMap<CEKey, std::shared_ptr<CEKeyConext>> InputKeys;

    friend struct CEInputKeyManager;
};

struct ENGINE_API CEInputKeyManager
{
public:
    static CEInputKeyManager& Get();

    UInt32 GetCodeFromKey(const CEKey& Key) const
    {
        auto re = GetPlatformCodeFromKey(Key);
        if (re == std::numeric_limits<UInt32>::max())
            return GetCharCodeFromKey(Key);
        return re; 
    }

    CEKey const* GetKeyFromCode(const UInt32 keyCode, const UInt32 charCode) const 
    {
        auto re = GetPlatformKeyFromCode(keyCode);
        if (re == nullptr)
            return GetCharKeyFromCode(charCode);
        return re;
    }

    CEKey const* AnyKey() const;

    /*
     * Retrieves the specified character code mapped to the key.
     */
    inline UInt32 GetPlatformCodeFromKey(const CEKey& Key) const 
    {
        auto result = std::find_if(KeyMapPlatformToEnum.begin(), KeyMapPlatformToEnum.end(), [&Key](auto& elem) 
        {
            return elem.second->Reference() == Key;
        });
        return (result != KeyMapPlatformToEnum.end()) ? result->first : std::numeric_limits<UInt32>::max();
    }

    inline bool IsPlatformKey(const CEKey& Key) const
    {
        return GetPlatformCodeFromKey(Key) != std::numeric_limits<UInt32>::max();
    }

    inline UInt32 GetCharCodeFromKey(const CEKey& Key) const
    {
        auto result = std::find_if(KeyMapCharToEnum.begin(), KeyMapCharToEnum.end(), [&Key](auto& elem) 
        {
            return elem.second->Reference() == Key; 
        });
        return (result != KeyMapCharToEnum.end()) ? result->first : std::numeric_limits<UInt32>::max();
    }

    inline bool IsCharKey(CEKey& Key) const
    {
        return GetCharCodeFromKey(Key) != std::numeric_limits<UInt32>::max();
    }

    /**
     * Retrieves the key mapped to the specified character code.
     * @param KeyCode   The key code to get the name for.
     */
    inline const CEKey* GetPlatformKeyFromCode(const UInt32 KeyCode) const
    {
        if (KeyMapPlatformToEnum.find(KeyCode) != KeyMapPlatformToEnum.end())
            return &KeyMapPlatformToEnum.at(KeyCode)->Reference();
        return nullptr;
    }

    inline const CEKey* GetCharKeyFromCode(const UInt32 KeyCode) const
    {
        if (KeyMapCharToEnum.find(KeyCode) != KeyMapCharToEnum.end())
            return &KeyMapCharToEnum.at(KeyCode)->Reference();
        return nullptr;
    }

    void InitKeyMappings(const InitInfo& initInfo);

private:
    CEInputKeyManager();

    CEHashMap<UInt32, std::shared_ptr<CEKeyConext>> KeyMapPlatformToEnum;
    CEHashMap<UInt32, std::shared_ptr<CEKeyConext>> KeyMapCharToEnum;
};
}

namespace cross {
class PlatformUser;
using UserHandle = THandle<PlatformUser>;

// enum input::CETouchIndex::Type;
using PointerHandle = THandle<input::CETouchIndex::Type>;
}   // namespace cross
#endif