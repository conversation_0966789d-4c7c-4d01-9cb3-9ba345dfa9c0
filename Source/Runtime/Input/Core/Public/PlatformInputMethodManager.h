#pragma once
#include "Configuration/CrossEngineConfig.h"
#include "Runtime/Input/Core/Public/PlatformInterface.h"
#include <memory>

namespace cross
{

// Input method context
// TODO: Leave the interfaces empty for now
class ENGINE_API PlatformInputMethodContext
{
public:
    PlatformInputMethodContext() = default;
    virtual ~PlatformInputMethodContext() = default;
};

// Generic platform input method manager interface
class ENGINE_API PlatformInputMethodManager
{
public:
    PlatformInputMethodManager() = default;
    virtual ~PlatformInputMethodManager() = default;

    virtual void ApplyDefaultInputMethod(const std::shared_ptr<PlatformApplication>& platform);
    virtual void RegisterInputMethod(const std::shared_ptr<PlatformInputMethodContext>& context);
    virtual void UnregisterInputMethod(const std::shared_ptr<PlatformInputMethodContext>& context);
    virtual void ActivateInputMethod(const std::shared_ptr<PlatformInputMethodContext>& context);
    virtual void DeactivateInputMethod(const std::shared_ptr<PlatformInputMethodContext>& context);
    virtual bool IsActiveInputMethod(const std::shared_ptr<PlatformInputMethodContext>& context) const;
};

} // namespace cross
