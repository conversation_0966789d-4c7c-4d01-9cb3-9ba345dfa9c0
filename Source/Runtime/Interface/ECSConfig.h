#pragma once

#include <vector>
#include <unordered_map>
#include "CrossBase/Template/Functional.hpp"
#include "CrossBase/String/UniqueString.h"
#include "ECS/Develop/Framework/Types.h"


namespace cross
{

class IGameWorld;

struct EntityTypeInfo
{
	UniqueString mEntityTypeName; 
	ecs::PrototypePtr mGamePrototype;
	ecs::PrototypePtr mRenderPrototype;
};

using WorldEntityTypeMap = std::unordered_map<StringHash32, EntityTypeInfo>;

class ECSConfig
{
public:
	using CustomSystemPriorityFunction = TFunction<void(ECSConfig&)>;

	static CustomSystemPriorityFunction CustomSystemPriorityFunc;

	ECSConfig();

	template<typename GameComponent, typename... RenderComponent>
	void RegisterGameRenderComponentPair();

	const WorldEntityTypeMap* GetWorldEntityTypeMap(UInt32 worldTypeTag) const;

	bool AddEntityPrototype(const char* name, ecs::PrototypePtr& protptype, UInt32 worldTypeTag);

	ecs::PrototypePtr ConvertToRenderType(ecs::PrototypePtr gamePrototype);
    
    void RegisterComponentPairByConfig(cross::SerializeNode& Config);

private:
	struct EntityTypeGroup
	{
		UInt32 mWorldTypeTag{ 0 };
		WorldEntityTypeMap mEntityPrototypeTable;
	};

	void LoadEntityConfigFromFile();

	WorldEntityTypeMap* GetOrCreateWorldEntityTypeMap(UInt32 worldTypeTag);

	struct ComponentPair
	{
		ComponentPair() = default;
		ComponentPair(ecs::ComponentDesc* gameComponentName) :mGameComponent(gameComponentName){}

		const ecs::ComponentDesc* mGameComponent{ nullptr };
		std::vector<const ecs::ComponentDesc*> mRenderComponents;
	};
	std::unordered_map<StringHash32, ComponentPair> mComponentMappingTable;

	std::unordered_map<UInt64, ecs::PrototypePtr> mRenderPrototypeMappingTable;
	
	static const UInt32 sMaxWorldTypeCount{ 10 };
	std::vector<EntityTypeGroup> mWorldEntityTypeMap;
};

struct NullComponentG: ecs::IComponent
{
	static ecs::ComponentDesc* GetDesc();
};

template<typename GameComponent, typename ...RenderComponents>
inline void ECSConfig::RegisterGameRenderComponentPair()
{
	auto* compDescG = GameComponent::GetDesc();
	if (compDescG)
	{
		Assert(compDescG->IsGameComponent());
	}

	std::array<const ecs::ComponentDesc*, sizeof...(RenderComponents)> renderCompoDesces = { RenderComponents::GetDesc()... };

	if (!compDescG)
		return;

	StringHash32 key = compDescG->Name.GetHash32();
	if (mComponentMappingTable.find(key) != mComponentMappingTable.end())
	{
		return;
	}

	auto result = mComponentMappingTable.try_emplace(key, compDescG).first;
	auto renderCompCount = renderCompoDesces.size();
	if (renderCompCount > 0)
	{
		if (renderCompCount > 1)
		{
			std::sort(renderCompoDesces.begin(), renderCompoDesces.end(), 
				[](const ecs::ComponentDesc* desc0, const ecs::ComponentDesc* desc1)
				{
					return desc0->Name.GetHash32() < desc1->Name.GetHash32();
				}
			);
		}

		auto& renderComponents = result->second.mRenderComponents;
		renderComponents.reserve(renderCompCount);
		renderComponents.insert(renderComponents.begin(), renderCompoDesces.begin(), renderCompoDesces.end());
	}
}

}