#pragma once

#include <mutex>
#include "CrossBase/PlatformDefs.h"
/*
* this file monitors whether engine is run correctly, i.e, not freeze, hang
*/

namespace cross
{
    struct Frame
    {
        UInt32 FrameID = static_cast<UInt32>(-1);
        float  mTime = 0;
        float  mDeltaTime = 0;
    };

    class EngineCrashMonitor
    {
    public:
        EngineCrashMonitor();
        void UpdateFrameID(UInt32 frameId, float time, float deltaTime);

        void Shutdown()
        {
            bTerminate = true;
        }

        bool Enable()
        {
            return mEnable;
        }

        ~EngineCrashMonitor()
        {
            Shutdown();
        }

    protected:
        virtual void ProcessAfterHang();

        virtual void MonitorImp();

        std::mutex mFrameMutex;
        Frame    mUpdatedFrame;

        std::atomic<bool> bTerminate = false;
        float mCheckIntervalSeconds = 10;
        float mExpireThreshold = 600;//10 minitues;
        bool mEnable = false;
    };
}
