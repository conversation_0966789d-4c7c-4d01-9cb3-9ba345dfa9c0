#pragma once

#include "nanovg.h"
#include "Runtime/UI/Px/Canvas.h"

namespace cross::px
{
    // Supporting NanoVG
    enum NVGcreateFlags
    {
        // Flag indicating if geometry based anti-aliasing is used (may not be needed when using MSAA).
        NVG_ANTIALIAS = 1 << 0,
        // Flag indicating if strokes should be drawn using stencil buffer. The rendering will be a little
        // slower, but path overlaps (i.e. self-intersecting or sharp turns) will be drawn just once.
        NVG_STENCIL_STROKES = 1 << 1,
        // Flag indicating that additional debug checks are done.
        NVG_DEBUG = 1 << 2,
    };

    enum StencilSetting
    {
        VKNVG_STENCIL_STROKE_UNDEFINED = 0,
        VKNVG_STENCIL_STROKE_FILL = 1,
        VKNVG_STENCIL_STROKE_DRAW_AA,
        VKNVG_STENCIL_STROKE_CLEAR,
    };

    struct VKNVGCreatePipelineKey
    {
        int stencilStroke;
        bool stencilFill;
        bool stencilTest;
        bool edgeAA;
        bool edgeAAShader;
        NVGcompositeOperationState compositOperation;

        bool operator==(const VKNVGCreatePipelineKey& b) const
        {
            if (compositOperation.dstAlpha == b.compositOperation.dstAlpha && compositOperation.dstRGB == b.compositOperation.dstRGB && compositOperation.srcAlpha == b.compositOperation.srcAlpha &&
                compositOperation.srcRGB == b.compositOperation.srcRGB && edgeAA == b.edgeAA && edgeAAShader == b.edgeAAShader && stencilFill == b.stencilFill && stencilStroke == b.stencilStroke && stencilTest == b.stencilTest)
            {
                return true;
            }
            return false;
        }
    };

    enum VKNVGshaderType
    {
        NSVG_SHADER_FILLGRAD,
        NSVG_SHADER_FILLIMG,
        NSVG_SHADER_SIMPLE,
        NSVG_SHADER_IMG
    };

    enum VKNVGcallType
    {
        VKNVG_NONE = 0,
        VKNVG_FILL,
        VKNVG_CONVEXFILL,
        VKNVG_STROKE,
        VKNVG_TRIANGLES,
    };

    struct VKNVGcall
    {
        int type;
        int image;
        int pathOffset;
        int pathCount;
        int triangleOffset;
        int triangleCount;
        int uniformOffset;
        NVGcompositeOperationState compositOperation;
    };

    struct VKNVGpath
    {
        int fillOffset;
        int fillCount;
        int strokeOffset;
        int strokeCount;
    };

    struct VKNVGfragUniforms
    {
        Float4x4 scissorMat;
        Float4 scissorExt;
        Float4 scissorScale;
        Float4x4 paintMat;
        Float4 extent;
        Float4 radius;
        Float4 feather;
        Float4 innerCol;
        Float4 outerCol;
        Float4 strokeMult;
        int texType;
        int type;
    };

    class NanoVGRenderer
    {
    public:
        NanoVGRenderer(int flags);
        ~NanoVGRenderer();

        int Create();
        int CreateTexture(int type, int w, int h, int imageFlags, const unsigned char* data);
        int DeleteTexture(int image);
        int UpdateTexture(int image, int x, int y, int w, int h, const unsigned char* data);
        int GetTextureSize(int image, int* w, int* h);
        void Viewport(float width, float height, float devicePixelRatio);
        void Cancel();
        void Flush();
        void Fill(NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, float fringe, const float* bounds, const NVGpath* paths, int npaths);
        void Stroke(NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, float fringe, float strokeWidth, const NVGpath* paths, int npaths);
        void Triangles(NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, const NVGvertex* verts, int nverts, float fringe);
        void Delete();

        VertexStreamLayout mVertexStreamLayout;
        Float2 mViewSize;

        // each NanoVGRenderer collects mNanoVGTexItems, mNanoVGItems, mNanoVGVertexDataTotal
        // and packed as NanoVGBatch in drawCanvas func, send to Canvas.cpp, and merge all in update function
        
        // nvg textures, mainly used for font
        std::map<int, NanoVGTexItem> mNanoVGTexItems;
        // nvg items
        std::vector<NanoVGItem> mNanoVGItems;
        // nvg items's vertices
        std::vector<NanoVGVertex> mNanoVGVertexDataTotal;

        constexpr static int fragSize = sizeof(VKNVGfragUniforms);
        int flags{0};
        FxPtr mFx;
        int mTexID{0};
        std::vector<std::pair<VKNVGCreatePipelineKey, MaterialPtr>> mPipelineKeyMaterialPool;

        // Per frame buffers
        std::vector<VKNVGcall> calls;
        std::vector<VKNVGpath> paths;
        std::vector<NVGvertex> verts;
        std::vector<VKNVGfragUniforms> uniforms;

        void AddNanoVGItem(PrimitiveTopology topology, 
                            VKNVGCreatePipelineKey pipelinekey, 
                            PropertySet properties, 
                            UInt32 vertCount,
                            UInt32 vertOffset);
        
        void ClearNanoVGData()
        {
            mNanoVGItems.clear();
            // IMPORTANT : should not clear here!!
            //mNanoVGTexItems.clear();
            mNanoVGVertexDataTotal.clear();
        }

        VKNVGcall* vknvg_allocCall();
        int vknvg_allocPaths(int n);
        int vknvg_allocVerts(int n);
        int vknvg_allocFragUniforms(int n);
        VKNVGfragUniforms* vknvg_fragUniformPtr(int i);
        int vknvg_convertPaint(VKNVGfragUniforms* frag, NVGpaint* paint, NVGscissor* scissor, float width, float fringe, float strokeThr);
        void vknvg_fill(VKNVGcall* call);
        void vknvg_convexFill(VKNVGcall* call);
        void vknvg_stroke(VKNVGcall* call);
        void vknvg_triangles(VKNVGcall* call);
        PropertySet vknvg_setUniforms(int uniformOffset, int image);
        MaterialPtr CreateMaterialFromRenderState(VKNVGCreatePipelineKey pipelinekey);

        static int Create(void* uptr) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Create(); }
        static int CreateTexture(void* uptr, int type, int w, int h, int imageFlags, const unsigned char* data) { return reinterpret_cast<NanoVGRenderer*>(uptr)->CreateTexture(type, w, h, imageFlags, data); }
        static int DeleteTexture(void* uptr, int image) { return reinterpret_cast<NanoVGRenderer*>(uptr)->DeleteTexture(image); }
        static int UpdateTexture(void* uptr, int image, int x, int y, int w, int h, const unsigned char* data) { return reinterpret_cast<NanoVGRenderer*>(uptr)->UpdateTexture(image, x, y, w, h, data); }
        static int GetTextureSize(void* uptr, int image, int* w, int* h) { return reinterpret_cast<NanoVGRenderer*>(uptr)->GetTextureSize(image, w, h); }
        static void Viewport(void* uptr, float width, float height, float devicePixelRatio) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Viewport(width, height, devicePixelRatio); }
        static void Cancel(void* uptr) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Cancel(); }
        static void Flush(void* uptr) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Flush(); }
        static void Fill(void* uptr, NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, float fringe, const float* bounds, const NVGpath* paths, int npaths) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Fill(paint, compositeOperation, scissor, fringe, bounds, paths, npaths); }
        static void Stroke(void* uptr, NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, float fringe, float strokeWidth, const NVGpath* paths, int npaths) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Stroke(paint, compositeOperation, scissor, fringe, strokeWidth, paths, npaths); }
        static void Triangles(void* uptr, NVGpaint* paint, NVGcompositeOperationState compositeOperation, NVGscissor* scissor, const NVGvertex* verts, int nverts, float fringe) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Triangles(paint, compositeOperation, scissor, verts, nverts, fringe); }
        static void Delete(void* uptr) { return reinterpret_cast<NanoVGRenderer*>(uptr)->Delete(); }

    };
}