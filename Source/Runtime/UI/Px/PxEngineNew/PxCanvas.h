#pragma once
#include "ICanvas.h"
#include "nanovg.h"
#include "PxWindowContext.h"

namespace cross::px {
using namespace pixui;

class PxWindowContext;
struct ENGINE_API NVGFont
{
    int fontId;
    pixui::uint decoration;
    pixui::FontStyle style;
    const char* fontFace;
    const char* customName;
    float lineHeight;
    float size;
    float deltaY;
    int mWeight;
    NVGFont(){}
    NVGFont(int id, const char* name, const char* cname, float size, float lh, float deltaY, pixui::uint deco, pixui::FontStyle fs, int weight)
        : fontId(id)
        , fontFace(name)
        , customName(cname)
        , size(size)
        , lineHeight(lh)
        , deltaY(deltaY)
        , decoration(deco)
        , style(fs)
        , mWeight(weight)
    {}
};

// TODO(chopperlin) should improve font loading method
static const std::map<std::string, std::string> FONT_PATH_MAP = {{"PingFangSCRegular", "EngineResource/NanoVG/PingFangSCRegular.ttf"}};

class PxCanvas : public ICanvas
{
public:

    //IMPL_IUNKNOWN_EMPTY(0);
    PxCanvas()
    {
    }

    /******************************************** canvas ********************************************/
    virtual void canvasBeginPaint() override;
    virtual void canvasEndPaint() override;

    virtual void canvasBeginPath() override;
    virtual void canvasClosePath() override;
    virtual void canvasSetStrokeWidth(float size) override;
    virtual void canvasMoveTo(float x, float y) override;
    virtual void canvasLineTo(float x, float y) override;
    virtual void canvasStroke() override;
    virtual void canvasFill() override;
    virtual void canvasDrawImage(PxImageHandle handle, const position& pos, const pixui::matrix& mat, const position& offset, const size& sz) override;

    virtual float canvasTextWidth(PxFontHandle hFont, const char* pszText) override;
    virtual void canvasSave() override;
    virtual void canvasRestore() override;
    virtual void canvasSetFillStyle(float r, float g, float b, float a) override;
    virtual void canvasSetStrokeStyle(float r, float g, float b, float a) override;
    virtual void canvasDrawText(float x, float y, const char* text) override;
    virtual void canvasSetLineCap(int value) override;
    virtual void canvasSetLineJoin(int value) override;
    virtual void canvasSetFont(PxFontHandle fontId) override;
    virtual void canvasRect(float x, float y, float w, float h) override;
    virtual void canvasBezierCurveTo(float x1, float y1, float x2, float y2, float x, float y) override;
    virtual void canvasQuadraticCurveTo(float cx, float cy, float x, float y) override;
    virtual void canvasScale(float x, float y) override;
    virtual void canvasTranslate(float x, float y) override;
    virtual void canvasRotate(float angle) override;
    virtual void canvasSetTransform(float a, float b, float c, float d, float e, float f) override;

    virtual void canvasStrokeRect(float x, float y, float width, float height) override;

    virtual void canvasFillRect(float x, float y, float width, float height) override;

    virtual void canvasClearRect(float x, float y, float width, float height) override;

    virtual void canvasArc(float x, float y, float radius, float startAngle, float endAngle, int anticlockwise) override;
    virtual void canvasArcTo(float fromX, float fromY, float toX, float toY, float radius) override;
    virtual void canvasTextAlign(int align) override;

    // implement addRef and release by Self
    virtual void addRef() override;
    virtual bool release() override;
    virtual bool queryInterface(const PxGUID& interfaceID, void** ppv) override;

public:
    
    float mWidth{0.0f};
    float mHeight{0.0f};
    NVGcontext* mNVGContext{nullptr};
    // manage a NanoVG Font Map
    // PxFontHandle : nvgFontId
    std::map<PxFontHandle, NVGFont> mNVGFontMap;
    
    UInt64 mPxCanvasUniqueHandle{0};
    std::shared_ptr<PxWindowContext> mContext;

private:
    std::atomic<int> mRefCount{0};
};
}   // namespace cross::px