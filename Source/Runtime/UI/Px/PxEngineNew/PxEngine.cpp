#include "EnginePrefix.h"

#include "Runtime/Interface/CrossEngineImp.h"
#include "Resource/ResourceManager.h"
#include "Resource/AssetStreaming.h"
#include "Resource/Resource.h"
#include "Resource/RenderTextureResource.h"

#include "RenderEngine/RenderMaterial.h"

#include "PxEngine.h"
#include "Px/PxUtil.h"

#include "imageio.h"

#include "PxType.h"
#include "IContainer.h"
#include "IWindow.h"

#include "PxWindowContext.h"
#include "PxWindowDrawDelegate.h"
#include "PxWindowEventDelegate.h"
#include "PxWindowResDelegate.h"

namespace cross::px {
using namespace pixui;
class PxLogDelegate : public ILogDelegate
{
public:
    IMPL_IUNKNOWN_EMPTY(0);
    virtual void onLog(IContainer* ctx, IWindow* window, const char* category, ELogLevel loglevel, const char* log) override
    {
        if (!mIsInited)
        {
            // params
            auto settingManager = EngineGlobal::GetSettingMgr();
            if (settingManager)
            {
                settingManager->GetValue("PixUISetting.ShowPixUILog", mIsShowPixUILog);
            }
            mIsInited = true;
        }

        if (!mIsShowPixUILog)
        {
            return;
        }

        switch (loglevel)
        {
        case em_ll_info:
            LOG_INFO("{}: {}", category, log);
            break;
        case em_ll_debug:
            LOG_DEBUG("{}: {}", category, log);
            break;
        case em_ll_warn:
            LOG_WARN("{}: {}", category, log);
            break;
        case em_ll_error:
            LOG_ERROR("{}: {}", category, log);
            break;
        default:
            break;
        }
    }

private:
    bool mIsShowPixUILog{false};
    bool mIsInited{false};
};

bool StartLoaderFile(int32 nViewID, const char* pszUrl, bool bAsync, PxStartOption::PF_OnLoadedCall pfOnLoaded)
{
    // Custom file loading process here, and return true if loading file success, otherwise return false.
    return false;
}

bool StopLoaderFile(int32 nViewID, const char* pszUrl)
{
    // Notify stop loading file.
    return false;
}

template<typename DelegateMap>
typename DelegateMap::iterator RemoveDelegate(DelegateMap& delegateMap, PxViewHandle viewHandle)
{
    if (auto it = delegateMap.find(viewHandle); it != delegateMap.end())
    {
        it->second->release();
        return delegateMap.erase(it);
    }
    return delegateMap.end();
}

PxEngine::PxEngine()
{
    mPxLogDelegate = new PxLogDelegate();

    std::string cachePath = EngineGlobal::Inst().GetFileSystem()->GetWorkDir();
    cachePath.append("\\Data\\Cache");

    // params
    auto settingManager = EngineGlobal::GetSettingMgr();
    if (settingManager)
    {
        settingManager->GetValue("PixUISetting.ForcePaintMode", mForcePaintMode);
        settingManager->GetValue("PixUISetting.WindowAsyncMode", mWindowAsyncMode);
        settingManager->GetValue("PixUISetting.AsyncFrameTime", mAsyncFrameTime);
    }

    PxStartOption startOption;
    startOption.pfStartLoaderFile = StartLoaderFile;
    startOption.pfStopLoaderFile = StopLoaderFile;
    startOption.szWritePath = cachePath.c_str();
    startOption.uFrameTime = mAsyncFrameTime;

    mPxContainer = pxCreateContainer();
    std::string version = mPxContainer->version();
    LOG_INFO("PixUI sdk version: {}", version);

    Assert(mPxContainer != nullptr);
    if (mPxContainer)
    {
        mPxContainer->startup(startOption);
        mPxContainer->setLogDelegate(mPxLogDelegate);
    }
}

PxEngine::~PxEngine()
{
    if (mPxContainer)
    {
        mPxContainer->shutdown();
        mPxContainer->release();
    }
    if (mPxLogDelegate)
    {
        mPxLogDelegate->release();
    }
}

PxEngine& PxEngine::Inst()
{
    static PxEngine engine;
    return engine;
}

void PxEngine::CreateView(int width, int height, ecs::EntityID entity, Canvas* canvas)
{
    PxViewHandle viewHandle = 0;
    if (mPxContainer)
    {
        CreateWindowOption option;
        option.bAsync = mWindowAsyncMode;
        option.szDefaultFontName = canvas->mDefaultFontName.c_str();
        option.unDefaultFontSize = 16;
        IWindow* window = mPxContainer->createWindow(option);
        //window->addRef();
        if (window)
        {
            viewHandle = window->getViewHandle();
            window->resize(width, height);
            window->setScreenSize(width, height);
            mEntityMap[viewHandle] = entity;
            auto ctx = std::make_shared<PxWindowContext>();
            ctx->SetCanvas(canvas);
            ctx->SetEntityID(entity);

            auto* eventDelegate = new PxWindowEventDelegate(ctx);
            auto* drawDelegate = new PxWindowDrawDelegate(ctx);
            auto* resDelegate = new PxWindowResDelegate(ctx);

            window->setDrawDelegate(drawDelegate);
            window->setEventDelegate(eventDelegate);
            window->setResDelegate(resDelegate);

            mWindowContextMap.insert({viewHandle, ctx});
            mIWindowMap[viewHandle] = window;
        }
    }
    canvas->mViewHandle = viewHandle;
    canvas->mWidth = width;
    canvas->mHeight = height;
}

bool PxEngine::HasView(PxViewHandle viewHandle)
{
    return mEntityMap.find(viewHandle) != mEntityMap.end();
}

// if reload new page, should release all the resources related to viewHandle
// TODO(chopperlin) how did we release resources before?
void PxEngine::LoadPage(PxViewHandle viewHandle, const char* url)
{
    std::string path = PathResolve(url);
    if (path.empty() || path.find("http") != std::string::npos || path.find("file:") != std::string::npos)
        return;

    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        std::string absolutePath = PathHelper::GetAbsolutePath(path);
        auto ret = window->loadFromUrl(absolutePath.c_str());
        if (ret != em_ec_ok)
        {
            LOG_ERROR("PxEngine LoadPage {} error code = {}", url, ret);
        }
        else
        {
            LOG_INFO("PxEngine LoadPage {} error code = {}", url, ret);
        }
    }
}

void PxEngine::ResizeWindow(PxViewHandle viewHandle, int width, int height)
{
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        window->resize(width, height);
        window->setScreenSize(width, height);
    }
}

void PxEngine::DestroyView(PxViewHandle viewHandle, bool wait)
{
    if (IWindow* window = FindWindowByViewHandle(viewHandle); !window)
    {
        //window->close();
        window->release();
    }

    mEntityMap.erase(viewHandle);
    mWindowContextMap.erase(viewHandle);
    mIWindowMap.erase(viewHandle);
}

void PxEngine::SendMouseEvent(PxViewHandle viewHandle, const cross::input::PointerEvent& event, cross::input::CEInputEvent::Type flag)
{
    using namespace cross::input;
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        MouseButton button = MB_None;
        if (event.Key == CEKeys::LeftMouseButton)
            button = MB_Left;
        else if (event.Key == CEKeys::RightMouseButton)
            button = MB_Right;
        else if (event.Key == CEKeys::MiddleMouseButton)
            button = MB_Middle;
        else
            button = MB_None;
        window->onMouseEvent(static_cast<int>(event.ScreenSpacePosition.x), static_cast<int>(event.ScreenSpacePosition.y), static_cast<int>(event.WheelDelta), 0, button, flag == cross::input::CEInputEvent::Pressed);
    }
}

void PxEngine::SendTouchEvent(PxViewHandle viewHandle, const cross::input::PointerEvent& event, cross::input::CETouchType::Type flag)
{
    using namespace cross::input;

    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        ETouchPhase phase = em_touch_phase_stationary;
        switch (flag)
        {
        case cross::input::CETouchType::Began:
            phase = em_touch_phase_began;
            break;
        case cross::input::CETouchType::Ended:
            phase = em_touch_phase_ended;
            break;
        case cross::input::CETouchType::Moved:
            phase = em_touch_phase_moved;
            break;
        default:   // Touch cancelled
            phase = em_touch_phase_canceld;
            break;
        }
        window->onTouchEvent(static_cast<int>(event.ScreenSpacePosition.x), static_cast<int>(event.ScreenSpacePosition.y), static_cast<int32>(event.PointerIndex.mVal), phase);
    }
}

void PxEngine::SendKeyEvent(PxViewHandle viewHandle, const cross::input::KeyEvent& event, cross::input::CEInputEvent::Type flag)
{
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        EKeyCode keyCode;
        if (event.CharCode == std::numeric_limits<UInt32>::max())
        {
            keyCode = static_cast<EKeyCode>(event.PlatformCode);
        }
        else
        {
            keyCode = static_cast<EKeyCode>(event.CharCode);
        }
        window->onKeyEvent(keyCode, 0, flag == cross::input::CEInputEvent::Pressed);
    }
}

void PxEngine::SendKeyboardEvent(PxViewHandle viewHandle, const UniqueString& keyString)
{
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        auto keyInput = keyString.GetCString();
        auto ec = window->onKeyboardInput(keyInput, false, false, false);
        LOG_INFO("PIXUI SendKeyboardEvent {} Error Code = {}", keyInput, ec);
    }
}

bool PxEngine::TickView(PxViewHandle viewHandle)
{
    SCOPED_CPU_TIMING(GroupCanvas, "PxEngineTick");
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        return window->tick();
    }
    return false;
}

void PxEngine::PaintView(PxViewHandle viewHandle)
{
    SCOPED_CPU_TIMING(GroupCanvas, "PxEnginePaint");
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        int paintMode = mForcePaintMode ? 1 : 0;
        window->paint(paintMode);
    }
}

void PxEngine::SendMessage(PxViewHandle viewHandle, const char* message)
{
    if (IWindow* window = FindWindowByViewHandle(viewHandle))
    {
        window->postMessage(message);
    }
}

MaterialPtr CreateRectangleMaterial(const CanvasMode mode)
{
    MaterialPtr material;
    if (mode == CanvasMode::ScreenSpace)
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasRectScreenFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    else
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasRectWorldFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    return material;
}

MaterialPtr CreateBorderMaterial(const CanvasMode mode)
{
    MaterialPtr material;
    if (mode == CanvasMode::ScreenSpace)
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasBorderScreenFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    else
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasBorderWorldFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    return material;
}

MaterialPtr CreateTextMaterial(const CanvasMode mode)
{
    MaterialPtr material;
    if (mode == CanvasMode::ScreenSpace)
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasTextScreenFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    else
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasTextWorldFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    return material;
}

MaterialPtr CreateImageMaterial(const CanvasMode mode)
{
    MaterialPtr material;
    if (mode == CanvasMode::ScreenSpace)
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasImageScreenFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    else
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasImageWorldFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    return material;
}

MaterialPtr CreateVideoMaterial(const CanvasMode mode)
{
    MaterialPtr material;
    if (mode == CanvasMode::ScreenSpace)
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasVideoScreenFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    else
    {
        auto fx = TypeCast<resource::Fx>(gAssetStreamingManager->LoadSynchronously("EngineResource/Shader/CanvasVideoWorldFx.nda"));
        material = TypeCast<resource::Material>(resource::Material::CreateMaterialInstance(fx));
    }
    return material;
}

MaterialPtr PxEngine::CreateCanvasMaterial(const CanvasItemType type, const CanvasMode mode)
{
    switch (type)
    {
    case CanvasItemType::Rect:
        return CreateRectangleMaterial(mode);
    case CanvasItemType::Text:
        return CreateTextMaterial(mode);
    case CanvasItemType::Image:
        return CreateImageMaterial(mode);
    case CanvasItemType::Video:
        return CreateVideoMaterial(mode);
    case CanvasItemType::Border:
        return CreateBorderMaterial(mode);
    default:
        //Assert(false);
        break;
    }
    return CreateRectangleMaterial(mode);
}

void PxEngine::SetAudioDataCallback(pixui::AudioDataCallback callbackAudioData, int playAudio)
{
    if (mPxContainer)
    {
        mPxContainer->setAudioDataCallback(callbackAudioData, playAudio);
    }
}

IWindow* PxEngine::FindWindowByViewHandle(PxViewHandle viewHandle)
{
    if (mIWindowMap.find(viewHandle) != mIWindowMap.end())
    {
        return mIWindowMap[viewHandle];
    }
    if (mPxContainer)
    {
        return mPxContainer->findWindow(viewHandle);
    }
    return nullptr;
}

void PxEngine::SetCanvas(PxViewHandle viewHandle, Canvas* canvas)
{
    if (auto it = mWindowContextMap.find(viewHandle); it != mWindowContextMap.end())
    {
        auto ctx = it->second;
        if (ctx->GetCanvas() != canvas)
        {
            ctx->SetCanvas(canvas);
        }
    }
}

void PxEngine::ClearMaterialPool(PxViewHandle viewHandle)
{
    if (auto it = mWindowContextMap.find(viewHandle); it != mWindowContextMap.end())
    {
        auto ctx = it->second;
        ctx->ClearMaterialPool();
    }
}
}   // namespace cross::px