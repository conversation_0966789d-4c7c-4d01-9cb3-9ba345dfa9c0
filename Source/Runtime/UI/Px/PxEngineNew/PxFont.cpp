#include "PxFont.h"

namespace cross::px {
using namespace pixui;
PxFont::PxFont(FontResourcePtr fontRes, int size, int weight, unsigned int decoration, FontStyle italic)
    : mFontResource(fontRes)
    , mSize(size)
    , mWeight(weight)
    , mDecoration(decoration)
    , mFontStyle(italic)
{
    if (mFontResource)
    {
        auto& glyphs = mFontResource->GetFontInfo().glyphs;
        for (auto& glyph : glyphs)
        {
            mGlyphMap.emplace(glyph.unicode, &glyph);
        }

        const auto& kernings = mFontResource->GetFontInfo().kerning;
        for (const auto& kerning : kernings)
        {
            mKerningMap.emplace(KerningHash{kerning.unicode1, kerning.unicode2}.hash(), kerning.advance);
        }

        mEmSize = mFontResource->GetFontInfo().metrics.emSize;
    }
}

void PxFont::SetFontMetrics(pixui::FontMetrics* fontMetrics)
{
    if (mFontResource)
    {
        const auto& metrics = mFontResource->GetFontInfo().metrics;
        fontMetrics->height = static_cast<int16>(mSize * metrics.lineHeight / mEmSize);
        fontMetrics->ascent = static_cast<int16>(mSize * metrics.ascender / mEmSize);
        fontMetrics->descent = static_cast<int16>(mSize * metrics.descender / mEmSize);
    }
}

float PxFont::GetKerning(int unicode1, int unicode2) const
{
    size_t hash = KerningHash{unicode1, unicode2}.hash();
    if (mKerningMap.find(hash) != mKerningMap.end())
    {
        return mSize * mKerningMap.at(hash) / mEmSize;
    }
    return 0;
}

float PxFont::GetCharWidth(int unicode) const
{
    if (mGlyphMap.find(unicode) != mGlyphMap.end())
    {
        return mSize * mGlyphMap.at(unicode)->advance / mEmSize;
    }
    if (mGlyphMap.find(0x20) != mGlyphMap.end())   // 0x20 space
    {
        return mSize * mGlyphMap.at(0x20)->advance / mEmSize;
    }
    return 0;
}

float PxFont::ComputeTextWidth(const char* text) const
{
    std::wstring wText = ConvertUTF8toUTF16(std::string{text});

    float width = 0;
    int prevUnicode = -1;
    for (const auto& wChar : wText)
    {
        int unicode = static_cast<int>(wChar);
        float kerning = GetKerning(prevUnicode, unicode);
        float charWidth = GetCharWidth(unicode);
        width += kerning + charWidth;
        prevUnicode = unicode;
    }

    return width;
}

float PxFont::GetLineHeight() const
{
    if (mFontResource)
    {
        return mSize * mFontResource->GetFontInfo().metrics.lineHeight / mEmSize;
    }
    return static_cast<float>(mSize);
}

float PxFont::GetAscender() const
{
    if (mFontResource)
    {
        return mFontResource->GetFontInfo().metrics.ascender;
    }
    return 1.0;
}

int PxFont::GetSize() const
{
    return mSize;
}

int PxFont::GetWeight() const
{
    return mWeight;
}

float PxFont::GetEmSize() const
{
    return mEmSize;
}

pixui::FontStyle PxFont::GetFontStyle() const
{
    return mFontStyle;
}

unsigned int PxFont::GetUnDecoration() const
{
    return mDecoration;
}

Float2 PxFont::GetTextureSize() const
{
    if (mFontResource)
    {
        const auto& atlas = mFontResource->GetFontInfo().atlas;
        return {static_cast<float>(atlas.width), static_cast<float>(atlas.height)};
    }
    return {0, 0};
}

float PxFont::GetDistanceRange() const
{
    if (mFontResource)
    {
        return static_cast<float>(mFontResource->GetFontInfo().atlas.distanceRange);
    }
    return 1.0;
}

resource::FontGlyph* PxFont::GetFontGlyph(int unicode) const
{
    if (mGlyphMap.find(unicode) != mGlyphMap.end())
    {
        return mGlyphMap.at(unicode);
    }
    if (mGlyphMap.find(0x20) != mGlyphMap.end())   // 0x20 space
    {
        return mGlyphMap.at(0x20);
    }
    return nullptr;
}

TexturePtr PxFont::GetFontTexture() const
{
    if (mFontResource)
    {
        return mFontResource->GetMSDFTexture();
    }
    return TypeCast<resource::Texture>(resource::Texture::GetDefault());
}

}   // namespace cross::px