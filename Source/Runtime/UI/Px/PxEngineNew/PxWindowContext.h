#pragma once
#include "EnginePrefix.h"

#include "PxType.h"
#include "PxFont.h"
#include "Runtime/UI/Px/Canvas.h"
#include "RenderTextureResource.h"
#include "nanovg.h"
#include "NanoVGRenderer.h"
#include "PxCanvas.h"

namespace cross::px {
using namespace pixui;

class NanoVGRenderer;
class PxCanvas;
class PxWindowContext : public std::enable_shared_from_this<PxWindowContext>
{
public:

    Canvas* GetCanvas()
    {
        return mCanvas;
    }
    void SetCanvas(Canvas* canvas)
    {
        mCanvas = canvas;
    }

    ecs::EntityID GetEntityID()
    {
        return mEntityID;
    }
    void SetEntityID(ecs::EntityID entityID)
    {
        mEntityID = entityID;
    }

    Float2 GetCanvasSize() const
    {
        return {static_cast<float>(mCanvas->mWidth), static_cast<float>(mCanvas->mHeight)};
    }

    // font
    std::optional<PxFont*> FindFont(PxFontHandle handle);
    PxFontHandle CreateFont(const char* pszFontName, const int nSize, const int nWeight, const unsigned int unDecoration, const FontStyle emItalic, FontMetrics* pStFontMetrics);
    void DeleteFont(PxFontHandle handle);
    float TextWidth(PxFontHandle hFont, const char* pszText);
    float CharWidth(PxFontHandle hFont, uint32_t unCharCode);

    // image
    PxImageHandle CreateImage(const char* pszName);
    bool InitImageByRawData(PxImageHandle hImage, const int nWidth, const int nHeight, const int nImageFlag, const EPixelFormat emPixelFormat, const uint8* pRawData, size_t nDataSize);

    void UpdateImageByRawData(PxImageHandle hImage, const EPixelFormat emPixel, const uint8* pRawData, size_t nDataSize);
    bool DeleteImage(PxImageHandle hImage);

    bool IsExternalImage(PxImageHandle hImage);
    size GetImageSize(PxImageHandle hImage);

    TexturePtr FindImage(PxImageHandle handle);

    // video
    TexturePtr FindVideo(PxImageHandle handle);
    TexturePtr FindVideoAlpha(PxImageHandle handle);

    void ResetActiveIndex();

    resource::Material* GetMaterial(const CanvasItemType type);
    void ClearMaterialPool();

    ICanvas* CreateCanvas(uint32 width, uint32 height, int32 flags);
    void DeleteCanvas(ICanvas* handle);

    // nanovg
    NVGcontext* CreateNVGContext(UInt64 canvasHandle, int flags);

    std::shared_ptr<PxWindowContext> GetPtr()
    {
        return shared_from_this();
    }

    // 1 PAGE : 1 PxWindowContext, 1 PAGE could contains multiple canvases --> NanoVGRenderers
    // PxCanvas's unique handle, managed by PxWindowContext
    // canvasHandle = std::unique_ptr<PxCanvas>.get()
    // canvasHandle : PxCanvas, the same canvasHandle
    std::unordered_map<UInt64, std::unique_ptr<PxCanvas>> mPxCanvases;
    
    // canvasHandle : NVGRenderer, the NVGRenderer contains different nvgItems, ...
    std::unordered_map<UInt64, std::unique_ptr<NanoVGRenderer>> mNVGRendererMap;
    FxPtr mFx{nullptr};

    std::unordered_map<PxFontHandle, PxFont> mFontMap;

private:
    ecs::EntityID mEntityID = ecs::EntityID::InvalidHandle();
    Canvas* mCanvas = nullptr;

    PxFontHandle mFontHandle = 0;
    PxImageHandle mImageHandle = 0;

    std::unordered_map<PxImageHandle, std::string> mURLMap;
    std::unordered_map<PxImageHandle, TexturePtr> mTextureMap;

    std::unordered_map<PxImageHandle, std::string> mExternalURLMap;
    std::unordered_map<PxImageHandle, TexturePtr> mExternalTextureMap;
    std::unordered_map<PxImageHandle, RenderTextureResourcePtr> mExternalRenderTextureMap;

    std::vector<PxImageHandle> mVideoList;
    std::unordered_map<PxImageHandle, TexturePtr> mVideoTextureMap;
    std::unordered_map<PxImageHandle, TexturePtr> mVideoAlphaTextureMap;

    std::array<int, CanvasItemTypeCount> mActiveIndex;
    std::array<std::vector<MaterialPtr>, CanvasItemTypeCount> mMaterialPool;

};
}   // namespace cross::px