#include "RmlUi_Renderer.h"

#include "../PxUtil.h"
#include "../PxEngineNew/PxEngine.h"
#include "Resource/ResourceManager.h"
#include "Resource/RenderTextureResource.h"

#include <RmlUi/Core/Core.h>
#include <RmlUi/Core/DecorationTypes.h>
#include <RmlUi/Core/FileInterface.h>
#include <RmlUi/Core/Geometry.h>
#include <RmlUi/Core/Log.h>
#include <RmlUi/Core/MeshUtilities.h>
#include <RmlUi/Core/Platform.h>
#include <RmlUi/Core/SystemInterface.h>

#include "ShellFileInterface.h"

#define STB_IMAGE_IMPLEMENTATION
#include <stb_image.h>

#include <algorithm>
#include <string.h>
#include <Px/Canvas.h>

#if _MSC_VER && !__INTEL_COMPILER
#else
Rml::RenderInterface::RenderInterface() {}
Rml::RenderInterface::~RenderInterface() {}
void Rml::RenderInterface::EnableClipMask(bool enable) {}
void Rml::RenderInterface::RenderToClipMask(ClipMaskOperation operation, CompiledGeometryHandle geometry, Vector2f translation) {}
void Rml::RenderInterface::SetTransform(const Matrix4f* transform) {}
Rml::LayerHandle Rml::RenderInterface::PushLayer() { return 0; }
void Rml::RenderInterface::CompositeLayers(LayerHandle source, LayerHandle destination, BlendMode blend_mode, Span<const CompiledFilterHandle> filters) {}
void Rml::RenderInterface::PopLayer() {}
Rml::TextureHandle Rml::RenderInterface::SaveLayerAsTexture() { return 0; }
Rml::CompiledFilterHandle Rml::RenderInterface::SaveLayerAsMaskImage() { return 0; }
Rml::CompiledFilterHandle Rml::RenderInterface::CompileFilter(const String& name, const Dictionary& parameters) { return 0; }
void Rml::RenderInterface::ReleaseFilter(CompiledFilterHandle filter) {}
Rml::CompiledShaderHandle Rml::RenderInterface::CompileShader(const String& name, const Dictionary& parameters) { return 0; }
void Rml::RenderInterface::RenderShader(CompiledShaderHandle shader, CompiledGeometryHandle geometry, Vector2f translation, TextureHandle texture) {}
void Rml::RenderInterface::ReleaseShader(CompiledShaderHandle shader) {}
#endif

namespace cross::rmlui
{
    static const NGIDepthStencilStateDesc ItemDepthStencilState = {
        true,
        false,
        NGIComparisonOp::GreaterEqual,
        true,                               // bool EnableStencil;
        0xff,                               // UInt8 StencilReadMask;
        0xff,                               // UInt8 StencilWriteMask;
        {
            StencilOp::Keep,                // StencilOp StencilFailOp;
            StencilOp::Keep,                // StencilOp StencilDepthFailOp;
            StencilOp::Keep,                // StencilOp StencilPassOp;
            NGIComparisonOp::LessEqual      // NGIComparisonOp StencilCompareOp;
        },
        {
            StencilOp::Keep,                // StencilOp StencilFailOp;
            StencilOp::Keep,                // StencilOp StencilDepthFailOp;
            StencilOp::Keep,                // StencilOp StencilPassOp;
            NGIComparisonOp::LessEqual      // NGIComparisonOp StencilCompareOp;
        }
    };

    RenderInterface_CE::Geometry::Geometry(Rml::Span<const Rml::Vertex> const vertices, Rml::Span<const int> const indices
        , MaterialPtr const material, Float2 const & boundingSize)
        : vertices(vertices), indices(indices), material(material), boundingSize(boundingSize)
    {
    }

    RenderInterface_CE::RenderInterface_CE()
    {
    }

    RenderInterface_CE::~RenderInterface_CE()
    {
#if _MSC_VER && !__INTEL_COMPILER
        if(mTextureNull < std::numeric_limits<Rml::TextureHandle>::max())
        {
            ReleaseTexture(mTextureNull);
            mTextureNull = std::numeric_limits<Rml::TextureHandle>::max();
        }
#endif
    }

    Rml::CompiledGeometryHandle RenderInterface_CE::CompileGeometry(Rml::Span<const Rml::Vertex> vertices, Rml::Span<const int> indices)
    {
#if _MSC_VER && !__INTEL_COMPILER
        MaterialPtr const material = px::PxEngine::Inst().CreateCanvasMaterial(CanvasItemType::Image, mCurrentCanvas->mMode);
        Float2 boundingBoxMin = Float2(std::numeric_limits<float>::infinity(), std::numeric_limits<float>::infinity());
        Float2 boundingBoxMax = Float2(-std::numeric_limits<float>::infinity(), -std::numeric_limits<float>::infinity());
        for(size_t vi = 0 ; vi < vertices.size() ; ++vi)
        {
            Rml::Vertex const * const v = vertices.data() + vi;
            Float2 const f(v->position.x, v->position.y);
            boundingBoxMin = Float2::Min(boundingBoxMin, f);
            boundingBoxMax = Float2::Max(boundingBoxMax, f);
        }
        return reinterpret_cast<Rml::CompiledGeometryHandle>(new Geometry(vertices, indices, material, boundingBoxMax - boundingBoxMin));
#else
        return reinterpret_cast<Rml::CompiledGeometryHandle>(nullptr);
#endif
    }

    cross::CanvasItem * RenderInterface_CE::CreateItem(Canvas * const canvas
        , Geometry const * const geometry, TexturePtr const texture
        , Rml::Vector2f const& translation
        , UInt8 const stencilRef)
    {
#if _MSC_VER && !__INTEL_COMPILER
        // create item
        cross::CanvasItem * const item = canvas->CreateCanvasItem();
        if(item == nullptr)
            return nullptr;
    
        // get allocator
        auto* allocator = EngineGlobal::GetFrameParamMgr()->GetCurrentGameFrameParam()->GetFrameAllocator();

        // setup vertexes
        {
            FrameArray<CanvasVertex>* const vertexData = allocator->CreateFrameContainer<FrameArray<CanvasVertex>>(FRAME_STAGE_GAME_RENDER, static_cast<UInt32>(geometry->vertices.size()));
            Float3 const trans = Float3(translation.x, translation.y, 0);
            for(std::uint32_t vi = 0 ; vi < geometry->vertices.size() ; ++vi)
            {
                Rml::Vertex const * const vs = geometry->vertices.data() + vi;
                vertexData->EmplaceBack(CanvasVertex{Float3(vs->position.x, vs->position.y, 0) + trans, Float2(vs->tex_coord.x, vs->tex_coord.y)});
            }
            item->mVertexData = vertexData;
            item->mVertexCount = static_cast<UInt32>(geometry->vertices.size());
        }

        // setup indices
        {
            auto* indexData = allocator->CreateFrameContainer<FrameArray<UInt16>>(FRAME_STAGE_GAME_RENDER, static_cast<UInt32>(geometry->indices.size()));
            for(std::uint32_t ii = 0 ; ii < geometry->indices.size() ; ii += 3)
            {    // flip the triangle
                indexData->EmplaceBack(UInt16(*(geometry->indices.data() + ii + 0)));
                indexData->EmplaceBack(UInt16(*(geometry->indices.data() + ii + 2)));
                indexData->EmplaceBack(UInt16(*(geometry->indices.data() + ii + 1)));
            }
            item->mIndexData = indexData;
            item->mIndexCount = static_cast<UInt32>(geometry->indices.size());
        }

        // setup primitives
        {
            item->mLayout.AddVertexChannelLayout(VertexChannel::Position0, VertexFormat::Float3, 0);
            item->mLayout.AddVertexChannelLayout(VertexChannel::TexCoord0, VertexFormat::Float2, 12);
            item->mPrimitiveCount = static_cast<UInt32>(geometry->indices.size()) / 3; // triangle list
            item->mPrimitiveTopology = cross::PrimitiveTopology::TriangleList;
        }

        // setup others
        {
            item->mCanvasSize = cross::Float2(static_cast<float>(canvas->mWidth), static_cast<float>(canvas->mHeight));
            item->mType = CanvasItemType::Image;
            item->mItemSize = geometry->boundingSize;
            item->mBorderRadiusX = Float4{0, 0, 0, 0};
            item->mBorderRadiusY = Float4{0, 0, 0, 0};
            Rml::ColourbPremultiplied const & color = geometry->vertices.size() > 0 ? geometry->vertices.begin()->colour : Rml::ColourbPremultiplied();
            item->mColor = {color.red / 255.0f, color.green / 255.0f, color.blue / 255.0f, color.alpha / 255.0f}; //HERE using the first vertex's color?
        }
        
        // setup material
        {
            item->mMaterial = geometry->material.get();
            item->mMaterial->SetBool("WORLD_SPACE", canvas->mMode == CanvasMode::WorldSpace);
            item->mMaterial->SetBool("NEED_MASK", false);
            item->mMaterial->SetBool("HAS_ROTATE", false);
            //if (mTempRotate.y != 0 && mRotateOrigin != Float2(0, 0))
            //{
            //    item->mMaterial->SetFloat2("rotate_origin", mRotateOrigin.data());
            //    item->mMaterial->SetFloat("rotate_perspective", mTempPerspective);
            //    item->mMaterial->SetFloat3("rotate_deg", mTempRotate.data());
            //}
            item->mMaterial->SetBool("HAS_BLUR", false);
            //if (mTempBlur != 0)
            //{
            //    item->mMaterial->SetFloat("blur_radius", static_cast<float>(mTempBlur));
            //    mTempBlur = 0;
            //}
            item->mMaterial->SetFloat2("canvas_size", item->mCanvasSize.data());
            item->mMaterial->SetFloat2("item_size", item->mItemSize.data());
            item->mMaterial->SetFloat4("item_color", item->mColor.data());
            item->mMaterial->SetFloat4("border_radius_x", item->mBorderRadiusX.data());
            item->mMaterial->SetFloat4("border_radius_y", item->mBorderRadiusY.data());

            resource::Material::StateType const * const materialState = item->mMaterial->GetState("UI");
            cross::NGIDepthStencilStateDesc const& depthStencilState = materialState->DepthStencilStateDesc;

            NGIDepthStencilStateDesc newDepthStencilState = ItemDepthStencilState;
            newDepthStencilState.EnableDepth = depthStencilState.EnableDepth;
            newDepthStencilState.EnableDepthWrite = depthStencilState.EnableDepthWrite;
            newDepthStencilState.DepthCompareOp = depthStencilState.DepthCompareOp;

            item->mMaterial->SetDepthStencilState("UI", newDepthStencilState);
            item->mMaterial->SetDynamicState("UI", NGIDynamicStateDesc{stencilRef});

            item->mMaterial->SetTexture("color_texture", texture);
            Float4 const imageRect{0, 0, static_cast<float>(texture->GetWidth()), static_cast<float>(texture->GetHeight())};
            item->mMaterial->SetFloat4("image_rect", imageRect.data());
        }

        return item;
#else
        return canvas->CreateCanvasItem();
#endif
    }

    void RenderInterface_CE::RenderGeometry(Rml::CompiledGeometryHandle handle, Rml::Vector2f translation, Rml::TextureHandle texture)
    {
#if _MSC_VER && !__INTEL_COMPILER
        Geometry const * const geometry = reinterpret_cast<Geometry const *>(handle);
        if(geometry == nullptr)
            return;
        if(texture >= mTextures.size())
        {
            // load null texture if needed
            Rml::Vector2i loaded_null_texture_dimensions(0);
            if(mTextureNull >= mTextures.size())
                mTextureNull = GenerateTextureWhite();
            if(mTextureNull >= mTextures.size())
                return;
            cross::CanvasItem const * const item = CreateItem(mCurrentCanvas, geometry, mTextures.at(mTextureNull), translation, mStencilRef);
        }
        else
        {
            cross::CanvasItem const * const item = CreateItem(mCurrentCanvas, geometry, mTextures.at(texture), translation, mStencilRef);
        }
#endif
    }

    void RenderInterface_CE::ReleaseGeometry(Rml::CompiledGeometryHandle handle)
    {
#if _MSC_VER && !__INTEL_COMPILER
        Geometry const * const geometry = reinterpret_cast<Geometry const *>(handle);
        if(nullptr != geometry)
            delete geometry;
#endif
    }

    /// Flip vertical axis of the rectangle, and move its origin to the vertically opposite side of the viewport.
    /// @note Changes coordinate system from RmlUi to OpenGL, or equivalently in reverse.
    /// @note The Rectangle::Top and Rectangle::Bottom members will have reverse meaning in the returned rectangle.
    static Rml::Rectanglei VerticallyFlipped(Rml::Rectanglei rect, int viewportHeight)
    {
        RMLUI_ASSERT(rect.Valid());
        Rml::Rectanglei flippedRect = rect;
        flippedRect.p0.y = viewportHeight - rect.p1.y;
        flippedRect.p1.y = viewportHeight - rect.p0.y;
        return flippedRect;
    }

    static void SetScissor(Rml::Rectanglei region, bool vertically_flip)
    {
        /*if (region.Valid() != scissor_state.Valid())
        {
            if (region.Valid())
                glEnable(GL_SCISSOR_TEST);
            else
                glDisable(GL_SCISSOR_TEST);
        }

        if (region.Valid() && vertically_flip)
            region = VerticallyFlipped(region, viewport_height);

        if (region.Valid() && region != scissor_state)
        {
            // Some render APIs don't like offscreen positions (WebGL in particular), so clamp them to the viewport.
            const int x = Rml::Math::Clamp(region.Left(), 0, viewport_width);
            const int y = Rml::Math::Clamp(viewport_height - region.Bottom(), 0, viewport_height);

            glScissor(x, y, region.Width(), region.Height());
        }

        Gfx::CheckGLError("SetScissorRegion");
        scissor_state = region;*/
    }

    void RenderInterface_CE::EnableScissorRegion(bool enable)
    {
        // Assume enable is immediately followed by a SetScissorRegion() call, and ignore it here.
        if (!enable)
            SetScissor(Rml::Rectanglei::MakeInvalid(), false);
    }

    void RenderInterface_CE::SetScissorRegion(Rml::Rectanglei region)
    {
        SetScissor(region, false);
    }

    void RenderInterface_CE::EnableClipMask(bool enable)
    {
    }

    void RenderInterface_CE::RenderToClipMask(Rml::ClipMaskOperation operation, Rml::CompiledGeometryHandle geometry, Rml::Vector2f translation)
    {
    }

    Rml::TextureHandle RenderInterface_CE::LoadTexture(Rml::Vector2i& textureDimensions, const Rml::String& source)
    {
#if _MSC_VER && !__INTEL_COMPILER
        // resolve
        std::string uri = cross::px::PathResolve(source.c_str());

        // is online image?
        if(uri.rfind("http://", 0) == 0 || uri.rfind("https://", 0) == 0)
        {
            Rml::Log::Message(Rml::Log::LT_ERROR, "only local texture supported (%s).", source.c_str());
            return std::numeric_limits<Rml::TextureHandle>::max();
        }

        // remove prefix
        std::string realpath = source;
        if (uri.rfind("file:///", 0) == 0)
            realpath = uri.substr(8);

        if (realpath.empty())
        {
            Rml::Log::Message(Rml::Log::LT_ERROR, "unexpected empty texture path");
            return std::numeric_limits<Rml::TextureHandle>::max();
        }

        // parse the path
        std::string const ext = cross::PathHelper::GetExtension(realpath);
        std::string const basename = cross::PathHelper::GetBaseFileName(realpath, false);

        // nda texture
        if (ext == "nda")
        {
            TexturePtr texture = TypeCastNoDefault<cross::resource::Texture>(gAssetStreamingManager->GetResource(realpath));
            if (texture)
            {
                textureDimensions.x = texture->GetWidth();
                textureDimensions.y = texture->GetHeight();
                mTextures.push_back(texture);
                return mTextures.size() - 1;
            }
        }
        // render texture
        else if (ext == "renderTexture")
        {
            auto renderTexture = TypeCastNoDefault<cross::resource::RenderTextureResource>(gAssetStreamingManager->GetResource(realpath));
            if (renderTexture)
            {
                cross::TexturePtr texture = TypeCast<cross::resource::Texture>(renderTexture);
                textureDimensions.x = texture->GetWidth();
                textureDimensions.y = texture->GetHeight();
                mTextures.push_back(texture);
                return mTextures.size() - 1;
            }
        }
        // local image
        else
        {
            // open the file
            ShellFileInterface * fileInterface = dynamic_cast<ShellFileInterface*>(Rml::GetFileInterface());
            FILE * const file = (FILE*)fileInterface->OpenConst(realpath);
            if(file == nullptr)
            {
                Rml::Log::Message(Rml::Log::LT_ERROR, "failed to open image file %s", realpath.c_str());
                return std::numeric_limits<Rml::TextureHandle>::max();
            }

            // load the texture using stb
            unsigned char* data = nullptr;
            int width = 0, height = 0, num_channel = 0;
            data = stbi_load_from_file(file, &width, &height, &num_channel, 0);
            if(nullptr == data)
            {
                Rml::Log::Message(Rml::Log::LT_ERROR, "failed to load image file %s.", realpath.c_str());
                return std::numeric_limits<Rml::TextureHandle>::max();
            }

            // close the file
            fileInterface->Close((Rml::FileHandle)file);

            // check number of channels
            if(4 != num_channel)
            {
                Rml::Log::Message(Rml::Log::LT_ERROR, "only 32bit RGBA images are supported (%s).", realpath.c_str());
                return std::numeric_limits<Rml::TextureHandle>::max();
            }

            // setup the dimensions
            textureDimensions.x = width;
            textureDimensions.y = height;

            // copy to a temperary buffer
            const size_t imageSize = width * height * 4; // We always make 32bit textures
            Rml::TextureHandle texture = GenerateTexture({data, imageSize}, textureDimensions);

            // free data
            free(data);
            return texture;
        }
        return std::numeric_limits<Rml::TextureHandle>::max();
#else
        return std::numeric_limits<Rml::TextureHandle>::max();
#endif
    }

    Rml::TextureHandle RenderInterface_CE::GenerateTextureWhite()
    {
        unsigned char const data[] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
            , 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
            , 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF
            , 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};
        int const width = 4, height = 4, numChannel = 4;
        Rml::Vector2i const textureDimensions(width, height);
        size_t const imageSize = width * height * numChannel; // We always make 32bit textures
        Rml::TextureHandle const texture = GenerateTexture({data, imageSize}, textureDimensions);
        return texture;
    }

    Rml::TextureHandle RenderInterface_CE::GenerateTexture(Rml::Span<const Rml::byte> sourceData, Rml::Vector2i sourceDimensions)
    {
        std::string const name = "[Rml RenderInterface Generated Texture] " + std::to_string(mGeneratedTextureIndex);
        ++mGeneratedTextureIndex;
        RMLUI_ASSERT(sourceData.data() && sourceData.size() == size_t(sourceDimensions.x * sourceDimensions.y * 4));
        cross::Texture2DPtr const texture = gResourceMgr.CreateResourceAs<cross::resource::Texture2D>(TextureFormat::RGBA32, cross::ColorSpace::Linear
            , sourceDimensions.x, sourceDimensions.y, 1, name.c_str());
        texture->UploadImage(0, sourceData.data(), static_cast<UInt32>(sourceData.size()), sourceDimensions.x * 4);
        mTextures.push_back(texture);
        return mTextures.size() - 1;
    }

    void RenderInterface_CE::ReleaseTexture(Rml::TextureHandle textureHandle)
    {
        size_t const tex = textureHandle;
        gResourceMgr.DeleteResource(mTextures[tex]->GetName().c_str());
    }

    void RenderInterface_CE::SetTransform(const Rml::Matrix4f* newTransform)
    {
    }

    Rml::CompiledFilterHandle RenderInterface_CE::CompileFilter(const Rml::String& name, const Rml::Dictionary& parameters)
    {
        return 0;
    }

    void RenderInterface_CE::ReleaseFilter(Rml::CompiledFilterHandle filter)
    {
    }

    Rml::CompiledShaderHandle RenderInterface_CE::CompileShader(const Rml::String& name, const Rml::Dictionary& parameters)
    {
        return 0;
    }

    void RenderInterface_CE::RenderShader(Rml::CompiledShaderHandle shaderHandle, Rml::CompiledGeometryHandle geometryHandle,
        Rml::Vector2f translation, Rml::TextureHandle /*texture*/)
    {
    }

    void RenderInterface_CE::ReleaseShader(Rml::CompiledShaderHandle shaderHandle)
    {
    }

    Rml::LayerHandle RenderInterface_CE::PushLayer()
    {
        return 0;
    }

    void RenderInterface_CE::CompositeLayers(Rml::LayerHandle sourceHandle, Rml::LayerHandle destinationHandle, Rml::BlendMode blendMode,
        Rml::Span<const Rml::CompiledFilterHandle> filters)
    {
    }

    void RenderInterface_CE::PopLayer()
    {
    }

    Rml::TextureHandle RenderInterface_CE::SaveLayerAsTexture()
    {
        return 0;
    }

    Rml::CompiledFilterHandle RenderInterface_CE::SaveLayerAsMaskImage()
    {
        return 0;
    }

    void RenderInterface_CE::SetCurrentCanvas(cross::Canvas * const canvas)
    {
        mCurrentCanvas = canvas;
    }
}
