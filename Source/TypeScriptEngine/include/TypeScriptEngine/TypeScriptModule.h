#pragma once
#include "TypeScriptEnginForward.h"
// for Register c++ class
#include "Binding.hpp"
#include "TypeScriptObject.h"

namespace PUERTS_NAMESPACE {
class FJsEnv;
}

namespace cross {
class TYPE_SCRIPT_ENGINE_API TypeScriptModule final
{
public:
    TypeScriptModule(TypeScriptModule const& other) = delete;
    TypeScriptModule(TypeScriptModule&& other) = delete;
    TypeScriptModule& operator=(const TypeScriptModule& other) = delete;
    TypeScriptModule& operator=(TypeScriptModule&& other) = delete;

    TypeScriptModule();
    ~TypeScriptModule();
    static TypeScriptModule* Instance();

    // shutdown v8
    void Destroy();

    // create v8 virtual machine
    void Start(bool PIE = false);

    // destroy v8 virtual machine
    void Stop();

    /**
     *
     * @param model_name javaScript file name "hello.mjs" which is in "Contents/JavaScript/"
     * @param BindObjectTypeId get BindObjectTypeId by PUERTS_NAMESPACE::DynamicTypeId<T>::get
     * @param BindObject GameObject pointer
     * @return TypeScriptObject
     */
    TypeScriptObject BindScriptObject(std::string_view model_name, const ClassTypeId* BindObjectTypeId, ClassObject* BindObject, std::string_view scriptEditorFieldsJson);

    /**
     *
     * @param BindObjectTypeId get BindObjectTypeId by PUERTS_NAMESPACE::DynamicTypeId<T>::get
     * @param BindObject BindObject GameObject pointer
     * @return TypeScriptObject
     */
    TypeScriptObject ConvertToTypeScriptObject(const ClassTypeId* BindObjectTypeId, ClassObject* BindObject);
    bool MixinTypescript(TypeScriptObject tsObject, const std::string& typescriptClass);

    bool ReloadScriptModule(std::string_view model_name, const std::string& script_content);
    void Tick(float DeltaTime, float DurationTime);

    // only suggest typescript that engine  have some idle  time  for gc, no gc gurrantted;
    void SuggestGarbageCollect(float time_budget_in_seconds);

#pragma region CEPropertyFunction
    std::string GetScriptEditorFieldsJson(const TypeScriptObject& typeScriptObject);
    bool SetScriptDefaultValue(const TypeScriptObject& typeScriptObject, const std::string& scriptDefaultValueJson);
    void GetReferenceResource(const TypeScriptObject& typeScriptObject, const std::string& resourceGuid);
#pragma endregion CEPropertyFunction

    std::string GenerateTypeScriptDeclaration(bool buildIn);
    std::string GenerateTypeScriptEnumImpl(bool buildIn);

    struct TypescriptMetaClassDefine
    {
        // using malloc & free, don't using new & delete
        gbf::reflection::MetaClass* MetaClassStorage;
        std::unique_ptr<gbf::reflection::ObjectVtable> remote_vtbl;
        std::unique_ptr<gbf::reflection::ObjectVtable> remote_shared_vtbl;
        std::unique_ptr<gbf::reflection::ObjectVtable> gc_vtbl;
        std::unique_ptr<gbf::reflection::ObjectVtable> local_vtbl;
    };
private:
    void ScanAndLoadTypescriptFile();
    void RegisterTypescriptMetaClass();
    void UnregisterTypescriptMetaClass();
    void DestroyTypescriptMetaClassStorage();
    std::unique_ptr<PUERTS_NAMESPACE::FJsEnv> mJsEnv;
    struct TypeScriptModuleParam;
    std::unique_ptr<TypeScriptModuleParam> mParam;
    
    // key is typescript object name
    std::unordered_map<std::string, TypescriptMetaClassDefine> mTypescriptMetaClassStorage;

};

}   // namespace cross