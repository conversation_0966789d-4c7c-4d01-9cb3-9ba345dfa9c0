#pragma once
#include <TypeInfo.hpp>
#include <set>
namespace PUERTS_NAMESPACE
{
template <typename T>
struct ScriptTypeName<std::set<T>>
{
    static constexpr auto value()
    {
        auto t_value = ScriptTypeName<T>::value();
        return internal::Literal("CSet<") + t_value + internal::Literal(">");
    }
};

template <typename T>
struct is_objecttype<std::set<T>> : public std::true_type
{
};

namespace v8_impl
{
template <typename T>
class StdSetExtension
{
    using SetType = std::set<T>;

public:
    static int32_t Num(const SetType& data)
    {
        return data.size();
    }

    static bool IsValidIndex(SetType& data, int32_t index)
    {
        return index < data.size() && index >= 0;
    }

    static T Get(SetType& data, int32_t index)
    {
        auto it = data.begin();
        std::advance(it, index);
        return *it;
    }

    static const T& GetRef(SetType& data, int32_t index)
    {
        auto it = data.begin();
        std::advance(it, index);
        return *it;
    }

    static int32_t FindIndex(const SetType& data, const T& value)
    {
        auto it = data.find(value);
        if (it != data.end())
        {
            return std::distance(data.begin(), it);
        }
        return -1;
    }

    static void RemoveAt(SetType& data, int32_t index)
    {
        auto it = data.begin();
        std::advance(it, index);
        data.erase(it);
    }

    static int32_t GetMaxIndex(const SetType& data)
    {
        return data.size() - 1;
    }

    static bool Empty(const SetType& data)
    {
        return data.empty();
    }

    static bool Contains(const SetType& data, const T& value)
    {
        return data.find(value) != data.end();
    }

    static void Add(SetType& data, const T& value)
    {
        data.insert(value);
    }

};
                                                                                              
template <typename T>                                                                                   
struct Converter<std::set<T>*>                                                                        
{
    using CLS = std::set<T>;

    static v8::Local<v8::Value> toScript(v8::Local<v8::Context> context, CLS* value)          
    {                                                                                         
        if (value != nullptr)                                                                 
        {                                                  
            return ::PUERTS_NAMESPACE::DataTransfer::FindOrAddCData(                          
                context->GetIsolate(), context, DynamicTypeId<CLS>::get(value), value, true); 
        }                                                                                     
        else                                                                                  
        {                                                                                     
            return v8::Undefined(context->GetIsolate());                                      
        }                                                                                     
    }

    static CLS* toCpp(v8::Local<v8::Context> context, const v8::Local<v8::Value>& value)      
    {
        return ::PUERTS_NAMESPACE::DataTransfer::GetPointerFast<CLS>(value.As<v8::Object>());
    }

    static bool accept(v8::Local<v8::Context> context, const v8::Local<v8::Value>& value)     
    {                                                                                         
        return ::PUERTS_NAMESPACE::DataTransfer::IsInstanceOf(                                
            context->GetIsolate(), StaticTypeId<CLS>::get(), value.As<v8::Object>());         
    }                                                                                         
};

}}

#define RegisterSetWrapper(SET_TYPE, REGISTER_TYPE)                                                                                                                                                                                              \
    {                                                                                                                                                                                                                                          \
        using DataType =  SET_TYPE::value_type;                                                                                                                                                       \
        using ExtensionType = puerts::v8_impl::StdSetExtension<DataType>;                                                                                                                                                                       \
        auto classBuilder = ::PUERTS_NAMESPACE::DefineClass<SET_TYPE>(REGISTER_TYPE);                                                                                                                                                        \
        if (FindCppTypeClassByName(classBuilder.className_) == nullptr)                                                                                                                                                                                \
        {                                                                                                                                                                                                                                              \
			classBuilder.template Constructor<>()                                                                                                                                                                                                       \
            .Method("Num", MakeExtension(&ExtensionType::Num))                                                                                                                                                  \
            .Method("Add", MakeExtension(&ExtensionType::Add))                                                                                                                                   \
            .Method("Get", MakeExtension(&ExtensionType::Get))                                                                                                                                   \
            .Method("GetRef", MakeExtension(&ExtensionType::GetRef))                                                                                                                                   \
            .Method("Contains", MakeExtension(&ExtensionType::Contains))                                                                                                                                   \
            .Method("FindIndex", MakeExtension(&ExtensionType::FindIndex))                                                                                                                                               \
            .Method("RemoveAt", MakeExtension(&ExtensionType::RemoveAt))                                                                                                                                         \
            .Method("GetMaxIndex", MakeExtension(&ExtensionType::GetMaxIndex))                                                                                                                                                  \
            .Method("IsValidIndex", MakeExtension(&ExtensionType::IsValidIndex))                                                                                                                                                  \
            .Method("Empty", MakeExtension(&ExtensionType::Empty))                                                                                                                                               \
            .Register();                                                                                                                                                                                                                       \
        }                                                                                                                                                                                                                                              \
        ::PUERTS_NAMESPACE::ContainerRegistry::Instance().RegisterClassConstructor<SET_TYPE>(); \
     }
