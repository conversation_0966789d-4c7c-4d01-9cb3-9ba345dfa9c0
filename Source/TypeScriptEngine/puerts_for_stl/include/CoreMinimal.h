#pragma once
#if _WIN32
#pragma warning(disable : 4100)
#pragma warning(disable : 4251)
#pragma warning(disable : 4267)
#pragma warning(disable : 4127)
#pragma warning(disable : 4244)
#pragma warning(disable : 4840)
#pragma warning(disable : 4714)
#pragma warning(disable : 4244)
#endif
#include <string>
#include <vector>
#include <unordered_map>
#include <filesystem>

#if CROSS_ENGINE_ADD_ON
// for nodejs addon
#define Assert
#else
// for cross engine
#include "Log.h"
#endif
using ClassTypeId = void;
using ClassObject = void;
#define PUERTS_TEXT(x) (x)
#define UTF8_TO_TCHAR(x) (x)
#define TCHAR_TO_UTF8(x) (x)
#if WIN32 && JSENV_DLL
#    ifdef JSENV_EXPORT
#        define JSENV_API __declspec(dllexport)
#    else
#        define JSENV_API __declspec(dllimport)
#    endif
#else
#    ifdef __GNUC__
#        define JSENV_API __attribute__((visibility("default")))
#    else
#        define JSENV_API
#    endif
#endif

#define DECLARE_LOG_CATEGORY_EXTERN(...)

#if _WIN32
#    define MSVC_PRAGMA(Pragma) __pragma(Pragma)
#else
#    define MSVC_PRAGMA(Pragma)
#endif