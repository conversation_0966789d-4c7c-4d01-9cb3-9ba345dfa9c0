/*
 * <PERSON><PERSON> is pleased to support the open source community by making <PERSON>uer<PERSON> available.
 * Copyright (C) 2020 THL A29 Limited, a Tencent company.  All rights reserved.
 * Puerts is licensed under the BSD 3-Clause License, except for the third-party components listed in the file 'LICENSE' which may
 * be subject to their corresponding license terms. This file is subject to the terms and conditions defined in file 'LICENSE',
 * which is part of this source code package.
 */

#pragma once

#include <vector>

#include "CoreMinimal.h"

#pragma warning(push, 0)
#include "libplatform/libplatform.h"
#include "v8.h"
#pragma warning(pop)

#include "NamespaceDef.h"
#include "DataTransfer.h"
//#include "UECompatible.h"
#include <fmt/format.h>
namespace PUERTS_NAMESPACE
{
enum ArgType
{
    EArgInt32,
    EArgNumber,
    EArgString,
    EArgExternal,
    EArgFunction,
    EArgObject
};

class JSENV_API FV8Utils
{
public:
    PUERTS_FORCEINLINE static void ThrowException(v8::Isolate* Isolate, const std::string& Message)
    {
        ThrowException(Isolate, Message.c_str());
    }

    PUERTS_FORCEINLINE static void ThrowException(v8::Isolate* Isolate, const char* Message)
    {
        auto ExceptionStr = v8::String::NewFromUtf8(Isolate, Message, v8::NewStringType::kNormal).ToLocalChecked();
        Isolate->ThrowException(v8::Exception::Error(ExceptionStr));
    }

    PUERTS_FORCEINLINE static void* GetPointer(v8::Local<v8::Context>& Context, v8::Local<v8::Value> Value, int Index = 0)
    {
        if (Value.IsEmpty() || !Value->IsObject() || Value->IsUndefined() || Value->IsNull())
        {
            return nullptr;
        }
        auto Object = Value->ToObject(Context).ToLocalChecked();
        return GetPointerFast<void>(Object, Index);
    }

    PUERTS_FORCEINLINE static void* GetPointer(v8::Local<v8::Object> Object, int Index = 0)
    {
        if (Object.IsEmpty() || Object->IsUndefined() || Object->IsNull())
        {
            return nullptr;
        }
        return GetPointerFast<void>(Object, Index);
    }

    template <typename T>
    PUERTS_FORCEINLINE static T* GetPointerFast(v8::Local<v8::Object> Object, int Index = 0)
    {
        return DataTransfer::GetPointerFast<T>(Object, Index);
    }
#if USING_IN_UNREAL_ENGINE
    PUERTS_FORCEINLINE static UObject* GetUObject(v8::Local<v8::Context>& Context, v8::Local<v8::Value> Value, int Index = 0)
    {
        auto UEObject = reinterpret_cast<UObject*>(GetPointer(Context, Value, Index));
        return (!UEObject || (UEObject != RELEASED_UOBJECT && UEObject->IsValidLowLevelFast() && !UEObjectIsPendingKill(UEObject)))
                   ? UEObject
                   : RELEASED_UOBJECT;
    }

    PUERTS_FORCEINLINE static UObject* GetUObject(v8::Local<v8::Object> Object, int Index = 0)
    {
        auto UEObject = reinterpret_cast<UObject*>(GetPointer(Object, Index));
        return (!UEObject || (UEObject != RELEASED_UOBJECT && UEObject->IsValidLowLevelFast() && !UEObjectIsPendingKill(UEObject)))
                   ? UEObject
                   : RELEASED_UOBJECT;
    }
#endif

    PUERTS_FORCEINLINE static bool IsReleasedPtr(void* Ptr)
    {
        return RELEASED_UOBJECT_MEMBER == Ptr;
    }

    PUERTS_FORCEINLINE static v8::Local<v8::String> InternalString(v8::Isolate* Isolate, const std::string& String)
    {
        return ToV8String(Isolate, String);
    }

    PUERTS_FORCEINLINE static v8::Local<v8::String> InternalString(v8::Isolate* Isolate, const char* String)
    {
        return v8::String::NewFromUtf8(Isolate, String, v8::NewStringType::kNormal).ToLocalChecked();
    }

    static std::string ToFString(v8::Isolate* Isolate, v8::Local<v8::Value> Value);

    PUERTS_FORCEINLINE static std::string ToFName(v8::Isolate* Isolate, v8::Local<v8::Value> Value)
    {
        return std::string(*(v8::String::Utf8Value(Isolate, Value)));
    }

    PUERTS_FORCEINLINE static v8::Local<v8::String> ToV8String(v8::Isolate* Isolate, const std::string& String)
    {
        return ToV8String(Isolate, String.c_str());
    }
#if USING_IN_UNREAL_ENGINE
    PUERTS_FORCEINLINE static v8::Local<v8::String> ToV8String(v8::Isolate* Isolate, const std::string& String)
    {
        const FNameEntry* Entry = String.GetComparisonNameEntry();
        std::string Out;
        if (String.GetNumber() == NAME_NO_NUMBER_INTERNAL)
        {
            Out.Empty(Entry->GetNameLength());
            Entry->AppendNameToString(Out);
        }
        else
        {
            Out.Empty(Entry->GetNameLength() + 6);
            Entry->AppendNameToString(Out);

            Out += PUERTS_TEXT('_');
            Out.AppendInt(NAME_INTERNAL_TO_EXTERNAL(String.GetNumber()));
        }

        return ToV8String(Isolate, Out);
    }

    PUERTS_FORCEINLINE static v8::Local<v8::String> ToV8String(v8::Isolate* Isolate, const FText& String)
    {
        return ToV8String(Isolate, String.ToString());
    }

    static v8::Local<v8::String> ToV8String(v8::Isolate* Isolate, const char* String);
#endif

    static v8::Local<v8::String> ToV8String(v8::Isolate* Isolate, const char* String);

    static v8::Local<v8::String> ToV8StringFromFileContent(v8::Isolate* Isolate, const std::vector<uint8_t>& FileContent)
    {
        const uint8_t* Buffer = FileContent.data();
        auto Size = FileContent.size();

        // TODO should we support UTF-16 ?
        {
            if (Size >= 3 && Buffer[0] == 0xef && Buffer[1] == 0xbb && Buffer[2] == 0xbf)
            {
                // Skip over UTF-8 BOM if there is one
                Buffer += 3;
                Size -= 3;
            }
            return v8::String::NewFromUtf8(Isolate, (const char*) Buffer, v8::NewStringType::kNormal, Size).ToLocalChecked();
        }
    }

    template <typename T>
    PUERTS_FORCEINLINE static T* IsolateData(v8::Isolate* Isolate)
    {
        return DataTransfer::IsolateData<T>(Isolate);
    }

    PUERTS_FORCEINLINE static bool CheckArgumentLength(const v8::FunctionCallbackInfo<v8::Value>& Info, int32_t Length)
    {
        if (Info.Length() < Length)
        {
            ThrowException(Info.GetIsolate(),
                fmt::format(PUERTS_TEXT("Bad parameters, the function expect {}, but  {} provided."), Length, Info.Length()));
            return false;
        }
        return true;
    }

    PUERTS_FORCEINLINE static std::string TryCatchToString(v8::Isolate* Isolate, v8::TryCatch* TryCatch)
    {
        v8::Isolate::Scope IsolateScope(Isolate);
        v8::HandleScope HandleScope(Isolate);
        v8::String::Utf8Value Exception(Isolate, TryCatch->Exception());
        std::string ExceptionStr((*Exception));
        v8::Local<v8::Message> Message = TryCatch->Message();
        if (Message.IsEmpty())
        {
            // 如果没有提供更详细的信息，直接输出Exception
            return ExceptionStr;
        }
        else
        {
            v8::Local<v8::Context> Context(Isolate->GetCurrentContext());

            // 输出 (filename):(line number): (message).
            v8::String::Utf8Value FileName(Isolate, Message->GetScriptResourceName());
            int LineNum = Message->GetLineNumber(Context).FromJust();
            std::string FileNameStr(*FileName);
            std::string LineNumStr = fmt::format("{}", LineNum);
            std::string FinalReport = fmt::format("{}:{}:{}\n", FileNameStr, LineNumStr, ExceptionStr);

            // 输出调用栈信息
            v8::Local<v8::Value> StackTrace;
            if (TryCatch->StackTrace(Context).ToLocal(&StackTrace))
            {
                v8::String::Utf8Value StackTraceVal(Isolate, StackTrace);
                std::string StackTraceStr(*StackTraceVal);
                FinalReport += "\n" + StackTraceStr;
            }
            return FinalReport;
        }
    }

    PUERTS_FORCEINLINE static bool CheckArgument(const v8::FunctionCallbackInfo<v8::Value>& Info, const std::vector<ArgType>& TypesExpect)
    {
        if (Info.Length() < TypesExpect.size())
        {
            ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters, the function expect {}, but  {} provided."),
                                                  TypesExpect.size(), Info.Length()));
            return false;
        }

        for (int i = 0; i < TypesExpect.size(); ++i)
        {
            switch (TypesExpect[i])
            {
                case EArgInt32:
                    if (!Info[i]->IsInt32())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect a int32_t."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                case EArgNumber:
                    if (!Info[i]->IsNumber())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect a int32_t."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                case EArgString:
                    if (!Info[i]->IsString())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect a string."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                case EArgExternal:
                    if (!Info[i]->IsExternal())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect an external."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                case EArgFunction:
                    if (!Info[i]->IsFunction())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect a function."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                case EArgObject:
                    if (!Info[i]->IsObject())
                    {
                        ThrowException(Info.GetIsolate(), fmt::format(PUERTS_TEXT("Bad parameters #{}, expect a object."), i));
                        return false;
                    }
                    else
                    {
                        break;
                    }
                default:
                    break;
            }
        }

        return true;
    }
};
}    // namespace PUERTS_NAMESPACE

#define CHECK_V8_ARGS_LEN(Length)                     \
    if (!FV8Utils::CheckArgumentLength(Info, Length)) \
    {                                                 \
        return;                                       \
    }

#define CHECK_V8_ARGS(...)                                 \
    static std::vector<ArgType> ArgExpect = {__VA_ARGS__}; \
    if (!FV8Utils::CheckArgument(Info, ArgExpect))         \
    {                                                      \
        return;                                            \
    }
