#include "TypescriptMetaClass.h"

#include "reflection/builder/base_builder.hpp"

namespace cross {

inline void PostTypescriptMetaClass(gbf::reflection::RttiBase* rttiPtr, const gbf::reflection::MetaClass* typescript_meta_class)
{
    // bing typescript meta class
    rttiPtr->__rtti_meta_class_ = typescript_meta_class;

    // Call MixinTypescript
    auto user_object = gbf::reflection::make_user_object(rttiPtr, gbf::reflection::remote_storage_policy{});
    gbf::reflection::cxx::Call(typescript_meta_class->GetFunction("MixinTypescript"), user_object, typescript_meta_class->alias());
}

class TypescriptCtorCallerImpl : public gbf::reflection::detail::cxx::CtorCaller
{
public:
    TypescriptCtorCallerImpl(gbf::reflection::Constructor* parent_ctor, CtorCaller* cppCtorCaller, gbf::reflection::MetaClass* typescriptMetaClass)
        :CtorCaller(parent_ctor),
        mTypescriptMetaClass(typescriptMetaClass),
        mCppCtorCaller(*cppCtorCaller)
    {
        is_just_one_ = cppCtorCaller->is_just_one();
    }
    virtual int GetArgNumber() const override
    {
        return mCppCtorCaller.GetArgNumber();
    }

    virtual bool Matches(const gbf::reflection::Args& args) const override
    {
        return mCppCtorCaller.Matches(args);
    }

    virtual gbf::reflection::UserObject CreateStorageExternal(void* ptr, const gbf::reflection::Args& args) const
    {
        gbf::reflection::UserObject user_object = mCppCtorCaller.CreateStorageExternal(ptr, args);
        TypescriptCtorImpl(user_object);
        return user_object;
    }
    virtual gbf::reflection::UserObject CreateStorageRemoteShared(const gbf::reflection::Args& args) const
    {
        gbf::reflection::UserObject user_object = mCppCtorCaller.CreateStorageRemoteShared(args);
        TypescriptCtorImpl(user_object);
        return user_object;
    }
    virtual gbf::reflection::UserObject CreateStorageGc(const gbf::reflection::Args& args) const
    {
        gbf::reflection::UserObject user_object = mCppCtorCaller.CreateStorageGc(args);
        TypescriptCtorImpl(user_object);
        return user_object;
    }
private:
    void TypescriptCtorImpl(const gbf::reflection::UserObject& user_object) const
    {
        gbf::reflection::RttiBase* rttiPtr = static_cast<gbf::reflection::RttiBase*>(user_object.GetPointer());
        PostTypescriptMetaClass(rttiPtr, mTypescriptMetaClass);
    }

    gbf::reflection::MetaClass* mTypescriptMetaClass;
    CtorCaller& mCppCtorCaller;
};

template<class StoragePolicy>
std::unique_ptr<gbf::reflection::ObjectVtable> CreateObjectVtable(const gbf::reflection::MetaClass* cppMetaClass)
{
    const gbf::reflection::ObjectVtable* cppvtbl = cppMetaClass->GetStorageVtable<StoragePolicy>();
    std::unique_ptr<gbf::reflection::ObjectVtable> vtbl(new gbf::reflection::ObjectVtable());
    *vtbl.get() = *cppvtbl;

    vtbl->can_copy_construct = false;
    vtbl->can_default_construct = true;
    vtbl->can_move_construct_noexcept = false;
    vtbl->ctor_default_ = +[](gbf::reflection::DataHolder* target, const void* extra_meta_info) {
        const gbf::reflection::MetaClass* typescript_meta_class = reinterpret_cast<const gbf::reflection::MetaClass*>(extra_meta_info);
        const gbf::reflection::ObjectVtable* cppvtbl = typescript_meta_class->GetBase(0).GetStorageVtable<StoragePolicy>();
        cppvtbl->ctor_default_(target, extra_meta_info);
        PostTypescriptMetaClass(reinterpret_cast<gbf::reflection::RttiBase*>(static_cast<char*>(cppvtbl->to_pointer_(target)) + typescript_meta_class->GetOffsetToRttiObj()), typescript_meta_class);
    };
    return vtbl;
}

void TypescriptMetaClassUtils::CreateTypescriptMetaClass(const std::string& typescriptClassName, TypeScriptModule::TypescriptMetaClassDefine& tsClassDefine, const gbf::reflection::MetaClass* cppMetaClass)
{
    tsClassDefine.remote_vtbl = std::move(CreateObjectVtable<gbf::reflection::remote_storage_policy>(cppMetaClass));
    tsClassDefine.remote_shared_vtbl = std::move(CreateObjectVtable<gbf::reflection::remote_shared_storage_policy>(cppMetaClass));
    tsClassDefine.gc_vtbl = std::move(CreateObjectVtable<gbf::reflection::gc_storage_policy>(cppMetaClass));
    tsClassDefine.local_vtbl = std::move(CreateObjectVtable<gbf::reflection::local_storage_policy>(cppMetaClass));

    new (tsClassDefine.MetaClassStorage)gbf::reflection::MetaClass(tsClassDefine.remote_vtbl->id_, tsClassDefine.remote_vtbl->name_,
        tsClassDefine.remote_vtbl.get(),
        tsClassDefine.remote_shared_vtbl.get(),
        tsClassDefine.gc_vtbl.get(),
        tsClassDefine.local_vtbl.get(),
        cppMetaClass->GetOffsetToRttiObj());

    gbf::reflection::BaseBuilder<gbf::reflection::RttiBase> base_builder(tsClassDefine.MetaClassStorage);
    base_builder.base(*cppMetaClass, 0);

    gbf::reflection::Constructor& tsCtor = const_cast<gbf::reflection::Constructor&>(tsClassDefine.MetaClassStorage->GetCtor());

    const auto& cppCtor = cppMetaClass->GetCtor();
    auto& cppAllCaller = cppCtor.GetAllCaller(gbf::reflection::FuncLanguageType::AsCxx);
    for (gbf::reflection::ICtorCaller* cppCaller : cppAllCaller)
    {
        tsCtor.AddCaller(gbf::reflection::FuncLanguageType::AsCxx, new TypescriptCtorCallerImpl(&tsCtor, dynamic_cast<gbf::reflection::detail::cxx::CtorCaller*>(cppCaller), tsClassDefine.MetaClassStorage));
    }
}

void TypescriptMetaClassUtils::ResetTypescriptMetaClassDefine(TypeScriptModule::TypescriptMetaClassDefine& tsClassDefine)
{
    memset(tsClassDefine.MetaClassStorage, 0, sizeof(gbf::reflection::MetaClass));
    tsClassDefine.remote_vtbl.reset();
    tsClassDefine.remote_shared_vtbl.reset();
    tsClassDefine.gc_vtbl.reset();
    tsClassDefine.local_vtbl.reset();
}

void TypescriptMetaClassUtils::DestructorTypescriptMetaClassDefine(TypeScriptModule::TypescriptMetaClassDefine& tsClassDefine)
{
    tsClassDefine.MetaClassStorage->~MetaClass();
    ResetTypescriptMetaClassDefine(tsClassDefine);
}

}